using Extric.Towbook.Configuration;
using Extric.Towbook.Configuration.Logging;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using System;

namespace FleetnetConnectivityService
{
    class Program
    {

        [STAThread]
        static void Main(string[] args)
        {
            Console.WriteLine("Process id: " + System.Diagnostics.Process.GetCurrentProcess().Id);

            System.Net.ServicePointManager.SecurityProtocol =
                System.Net.SecurityProtocolType.Tls12 |
                System.Net.SecurityProtocolType.Tls11 |
                System.Net.SecurityProtocolType.Tls;

            Console.WriteLine("FleetnetConnectivityService is running as console application");
            CreateHostBuilder(args).Build().Run();
        }

        public static IHostBuilder CreateHostBuilder(string[] args) =>
            Host.CreateDefaultBuilder(args)
                .ConfigureServices((_, services) =>
                    {
                        services.ConfigureCore();
                        services.AddHostedService(serviceProvider => new FleetnetConnectivityService(args, serviceProvider.GetService<IHostApplicationLifetime>()));
                    }
                )
                .ConfigureAppConfiguration(configurationBuilder => {
                    configurationBuilder.AddUserSecrets<Program>();
                })
                .ConfigureLogglyWithNLog()
                .UseConsoleLifetime();
    }
}
