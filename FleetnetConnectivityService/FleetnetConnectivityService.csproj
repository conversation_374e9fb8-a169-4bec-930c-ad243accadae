<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <NoWin32Manifest>true</NoWin32Manifest>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    <DockerfileContext>..</DockerfileContext>
    <ContainerDevelopmentMode>Regular</ContainerDevelopmentMode>
    <UserSecretsId>9d92ffcc-16da-450a-b1cb-617b1549ccc9</UserSecretsId>
  </PropertyGroup>
  <ItemGroup>
    <None Remove="appsettings.json" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="appsettings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <None Update="NLog.config" CopyToOutputDirectory="Always" />
  </ItemGroup>
  <ItemGroup>
    <None Include="../openssl.cnf">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>
  <ItemGroup>
	<PackageReference Include="Microsoft.Extensions.Hosting" Version="8.0.0" />
	<PackageReference Include="Microsoft.Extensions.Hosting.Abstractions" Version="8.0.0" />
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.21.0" />
    <PackageReference Include="System.ServiceProcess.ServiceController" Version="8.0.0" />
    <PackageReference Include="System.Data.DataSetExtensions" Version="4.5.0" />
    <PackageReference Include="System.IO.Compression" Version="4.3.0" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Extric.Towbook.Integration.MotorClubs\Extric.Towbook.Integration.MotorClubs.csproj" />
  </ItemGroup>
</Project>