using NLog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Threading;
using Extric.Towbook;

using Extric.Towbook.Integrations.MotorClubs.Fleetnet;
using Extric.Towbook.Utility;
using Microsoft.Extensions.Hosting;

namespace FleetnetConnectivityService
{
    partial class FleetnetConnectivityService : IHostedService
    {
        private static Logger logger = LogManager.GetCurrentClassLogger();

        private readonly IHostApplicationLifetime _appLifetime;
        private readonly string[] _args;

        private static int runningThreadCount = 0;
        //private static int interval = 240000;
        private static int interval = 1000 * 60 * 4;

        private int maxThreads = 10;
        private SuperQueue<FleetnetProvider> sp = new SuperQueue<FleetnetProvider>(50, new Action<FleetnetProvider>(ProcessProvider));
        private System.Timers.Timer executionTimer = new System.Timers.Timer();

        public FleetnetConnectivityService(string[] args, IHostApplicationLifetime appLifetime)
        {
            _args = args;
            _appLifetime = appLifetime;
            _appLifetime.ApplicationStopped.Register(OnStopped);
        }
        public Task StartAsync(CancellationToken cancellationToken)
        {
            logger.Info("FleetnetConnectivityService Start Async ...");
            OnStart(_args);
            return Task.CompletedTask;
        }

        public Task StopAsync(CancellationToken cancellationToken)
        {
            logger.Info("FleetnetConnectivityService Stop Async ...");
            OnStop();
            return Task.CompletedTask;
        }

        protected void OnStart(string[] args)
        {
            //EventLog.WriteEntry("Application", "Starting", EventLogEntryType.Information);

            logger.Log(LogLevel.Info, "Service Started on {0}", Environment.MachineName);

            for (int i = 0; i < args.Length; i++)
                if (args[i] == "/threads" || args[i] == "/t")
                    maxThreads = Convert.ToInt32(args[i + 1]);

            executionTimer.AutoReset = false;
            executionTimer.Interval = interval;
            executionTimer.Elapsed += executionTimer_Elapsed;
            executionTimer.Start();
            executionTimer_Elapsed(null, null);
        }

        protected void OnStop()
        {
            logger.Log(LogLevel.Info, "Service Stopping on {0}", Environment.MachineName);

            executionTimer.Enabled = false;

            while (runningThreadCount > 0)
            {
                Thread.Sleep(1000);
            }
        }

        void executionTimer_Elapsed(object sender, System.Timers.ElapsedEventArgs e)
        {
            runningThreadCount += 1;
            try
            {
                logger.LogEvent("[app] -> Fetching online providers...");
                IEnumerable<FleetnetProvider> providers = FleetnetProvider
                    .GetByLoginStatus(FleetnetProviderLoginStatus.LoggedIn);

                if (providers.Any())
                {
                    logger.LogEvent("[app] -> " + providers.Count() + " providers found logged in... enqueuing...");
                    foreach (var p in providers)
                        sp.EnqueueTask(p);
                    logger.LogEvent("[app] -> " + providers.Count() + " added to queue...");
                }
                else
                    logger.LogEvent("[app] -> No providers found...");
            }
            catch (Exception ex)
            {
                logger.LogEvent("ERROR" + ex.ToJson());
            }
            finally
            {
                runningThreadCount -= 1;
                executionTimer.Start();
            }
        }

        public static void ProcessProvider(FleetnetProvider prov)
        {
            string errorMsg = null;

            runningThreadCount += 1;
            try
            {
                Console.WriteLine(DateTime.Now.ToJson() + ":" + prov.ToJson());

                FleetnetRestClient.GetProduction().OnlineNotification(prov.ProviderId, "Web");
            }
            catch (Exception ex)
            {
                logger.LogEvent("[thread] -> ERROR processing Provider: " + prov.ToJson() + " - message: " + ex.Message);
                logger.LogEvent(ex.ToJson());

                errorMsg = ex.Message + ", " + ex.ToJson();

                prov.IsLoggedIn = false;
                prov.Save();

                prov.UpdateLoginStatus(FleetnetProviderLoginStatus.LoggedOut);

                Console.WriteLine(ex.ToJson());
            }
            finally
            {
                logger.LogEvent("[thread] -> Row: " + prov.ToJson() + " processed!");
                runningThreadCount -= 1;
            }
        }

        private void OnStopped()
        {
            logger.Info("FleetnetConnectivityService stopped successfully");
            LogManager.Shutdown();
            Console.WriteLine("FleetnetConnectivityService Stopped finished");
        }

    }
}
