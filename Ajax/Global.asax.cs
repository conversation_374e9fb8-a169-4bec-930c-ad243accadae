using System;
using System.Net.Mail;
using Extric.Towbook;
using Extric.Towbook.Utility;
using Extric.Towbook.Web;
using Extric.Towbook.WebShared;
using Microsoft.AspNetCore.Routing;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.Extensions;
using Microsoft.Extensions.Primitives;

using HttpContext = Extric.Towbook.Web.HttpContext;
namespace Ajax
{
    public class MvcApplication //: HttpApplication
    {
        // Statics values to Landing Page
        public static readonly string isFromLandingPage = "IS_FROM_LANDING_PAGE";
        public static readonly string driverIsCreated = "DRIVER_IS_CREATED";
        public static readonly string truckIsCreated = "TRUCK_IS_CREATED";
        public static readonly string pricingIsCreated = "PRICING_IS_CREATED";
        public static readonly string userIsCreated = "USER_IS_CREATED";
        public static readonly string isLandingPageDisplayed = "IS_LANDING_PAGE_DISPLAYED";

        private const string _seLockString = "<meta name=\"googlebot\" content=\"noindex,noarchive,nofollow\" />\n<meta name=\"msnbot\" content=\"noindex,nofollow\" />\n<meta name=\"robots\" content=\"noindex,nofollow\" />";

        //public void Application_BeginRequest(object sender, EventArgs e)
        //{

        //    HttpContext.Current.Response.Headers.Add("X-Twbk-Location", UriHelper.GetEncodedUrl(HttpContext.Current.Request));

        //    if (WebGlobal.CurrentUser != null)
        //    {
        //        HttpContext.Current.Response.AddHeader("X-Towbook-Username", WebGlobal.CurrentUser.Username);
        //        HttpContext.Current.Response.AddHeader("X-Towbook-Company-Id", WebGlobal.CurrentUser.CompanyId.ToString());
        //        HttpContext.Current.Response.AddHeader("X-Towbook-Company-Type", WebGlobal.CurrentUser.Company?.Type.ToString());
        //        HttpContext.Current.Response.AddHeader("X-Towbook-User-Type", WebGlobal.CurrentUser.Type.ToString());
        //    }
        //TODO: Fix for merge
        //    var isLocal = (Request.Url.Host.ToLowerInvariant() == "localhost" ||
        //        Request.Url.Host.StartsWith("192.168") ||
        //        Request.Url.Host.StartsWith("ws5.dal09") ||
        //        Request.Url.Host.Contains(".test") ||
        //        Request.IsLocal);
        //    //AntiForgeryConfig.RequireSsl = !isLocal;

        //    if (HttpContext.Current.Request.Headers["X-Requested-With"] == null &&
        //        HttpContext.Current.Request.Headers["X-Towbook-Internal"] == null &&
        //        HttpContext.Current.Request.QueryString["ajax"] == null &&
        //        HttpContext.Current.Request.RequestType == "GET")
        //    {
        //        if (HttpContext.Current.Request.RawUrl.ToLowerInvariant().StartsWith("/ajax/settings/"))
        //        {
        //            var extras = "";
        //            foreach (string key in HttpContext.Current.Request.QueryString.AllKeys)
        //            {
        //                if (key != "u")
        //                    extras += "&" + key + "=" + HttpContext.Current.Request.QueryString[key];
        //            }

        //            if (extras.Length > 0)
        //                extras = "?" + extras.Substring(1);

        //            HttpContext.Current.Response.Redirect(HttpContext.Current.Request.Path.Value.ToLowerInvariant().Replace("/ajax/settings/", "/settings/") + extras);
        //        }
        //    }

        //}


        //public static void RegisterGlobalFilters(GlobalFilterCollection filters)
        //{
        //    filters.Add(new HandleErrorAttribute());
        //}

        public static void RegisterRoutes(RouteCollection routes)
        { 
            //routes.IgnoreRoute("{resource}.axd/{*pathInfo}");

            //routes.MapRoute(
            //    "Default", // Route name
            //    "{controller}/{action}/{id}", // URL with parameters
            //    new { controller = "Home", action = "Index", id = UrlParameter.Optional }, // Parameter defaults
            //    new string[] {"Ajax.Controllers"}
            //);
        }

        public static void Application_Start()
        {

            //Extric.Towbook.Web.HttpContextFactory.Instance = new HttpContextAspNet();

            // default to TLS 1.2 
            System.Net.ServicePointManager.SecurityProtocol =
                System.Net.SecurityProtocolType.Tls12 |
                System.Net.SecurityProtocolType.Tls11 |
                System.Net.SecurityProtocolType.Tls;

            //AreaRegistration.RegisterAllAreas();

            //RegisterGlobalFilters(GlobalFilters.Filters);

            //RegisterRoutes(RouteTable.Routes);
            RegisterRoutes(null);


            RegisterKeys();
        }

        public static void RegisterKeys()
        {
            var tb = Extric.Towbook.Integration.Provider.Towbook;

            tb.RegisterKey(Extric.Towbook.Integration.KeyType.Company, "Setup_Drivers");
            tb.RegisterKey(Extric.Towbook.Integration.KeyType.Company, "Setup_Trucks");
            tb.RegisterKey(Extric.Towbook.Integration.KeyType.Company, "Setup_Accounts");
            tb.RegisterKey(Extric.Towbook.Integration.KeyType.Company, "Setup_RateItems");
            tb.RegisterKey(Extric.Towbook.Integration.KeyType.Company, "Setup_Users");
        }

        //void Application_Error(object sender, EventArgs e)
        //{
        //    var lei = new LogEventInfo();

        //    var errorId = Guid.NewGuid();

        //    var exceptionMessage = Server.GetLastError().InnerException != null ? Server.GetLastError().InnerException.Message : Server.GetLastError().Message;

        //    if (Request.HttpMethod != null)
        //        lei.Properties["method"] = Request.HttpMethod;

        //    if (Request.RawUrl != null)
        //        lei.Properties["url"] = Request.RawUrl;

        //    lei.Properties["requestingIp"] = WebGlobal.GetRequestingIp();
        //    lei.Properties["errorId"] = errorId;
        //    lei.Properties["exception"] = Server.GetLastError().InnerException ?? Server.GetLastError();

        //    Nlog.LogExceptionEvent(exceptionMessage,
        //        Server.GetLastError().InnerException,
        //        WebGlobal.CurrentUser, 
        //        lei);

        //    var msg = new MailMessage();

        //    msg.From = new MailAddress("<EMAIL>",
        //        "Towbook" + " " + Environment.MachineName);
        //    msg.To.Add("<EMAIL>");
        //    msg.Priority = MailPriority.High;
        //    msg.Subject = "Towbook Web App Error (Ajax Project)";

        //    msg.IsBodyHtml = false;
        //    msg.Body = "Towbook encountered an error during operation; the user was redirected to an error notice page. \r\n\r\n";

        //    try
        //    {
        //        if (WebGlobal.CurrentUser != null)
        //        {
        //            msg.Body += "Customer Details\r\n";
        //            msg.Body += "---------------\r\n";
        //            msg.Body += "User account: " + WebGlobal.CurrentUser.Username + "(Id: " + WebGlobal.CurrentUser.Id + ")\r\n";
        //            msg.Body += "Company: " + WebGlobal.CurrentUser.Company.Name + "(Id: " + WebGlobal.CurrentUser.Company.Id + ")\r\n";
        //            msg.Body += "Phone: " + Core.FormatPhone(WebGlobal.CurrentUser.Company.Phone) + "(" + WebGlobal.CurrentUser.Company.State + ")\r\n";
        //            msg.Body += "\r\n\r\n";
        //        }
        //    }
        //    catch { }

        //    try
        //    {

        //        msg.Body += "Exception Details\n";
        //        msg.Body += "-----------------\n";
        //        msg.Body += Server.GetLastError().Message + "\n";
        //        msg.Body += Server.GetLastError().StackTrace + "\n";
        //        if (Server.GetLastError().InnerException != null)
        //        {
        //            msg.Body += Server.GetLastError().InnerException.Message + "\n";
        //            msg.Body += "Exception was thrown by " + Server.GetLastError().InnerException.Source + "\n\n";
        //            msg.Body += Server.GetLastError().InnerException.StackTrace.ToString() + "\r\n";
        //        }
        //        else
        //        {
        //            msg.Body += "Exception was thrown by " + Server.GetLastError().Source + "\n\n";
        //            msg.Body += Server.GetLastError().StackTrace.ToString() + "\r\n";
        //        }
        //    }
        //    catch { }

        //    msg.Body += "\r\n";

        //    try
        //    {

        //        msg.Body += "Related Details\r\n";
        //        msg.Body += "---------------\r\n";
        //        msg.Body += "ErrorID: " + errorId + "\r\n";
        //        msg.Body += "Path: " + Request.PhysicalPath.ToString() + "\r\n";
        //        msg.Body += "Ip Address: " + WebGlobal.GetRequestingIp() + "\r\n";

        //        if (Request.ServerVariables["QUERY_STRING"].ToString().Length > 0)
        //            msg.Body += "Query String: " + Request.ServerVariables["QUERY_STRING"].ToString() + "\r\n";

        //        if (Request.ServerVariables["HTTP_REFERER"] != null)
        //            msg.Body += "Referring URL: " + Request.ServerVariables["HTTP_REFERER"].ToString() + "\r\n";

        //        if (Request.ServerVariables["HTTP_USER_AGENT"] != null)
        //            msg.Body += "User Agent: " + Request.ServerVariables["HTTP_USER_AGENT"].ToString() + "\r\n";

        //        msg.Body += "Post Variables: \r\n";

        //        foreach (string key in Request.Form.Keys)
        //        {
        //            msg.Body += key + "=" + Request.Form[key] + "\r\n";
        //        }
        //    }
        //    catch
        //    {

        //    }


        //    #region attempt to associate error with account via note
        //    /*
        //     // 7/24/22 - Stop saving these to CompanyNotes. It's ok to write these to a database that correlates it to a CompanyId/UserID,, but not CompanyNotes.

        //    try
        //    {
        //        if (WebGlobal.CurrentUser != null)
        //        {
        //            Extric.Towbook.Company.Note n = new Extric.Towbook.Company.Note();
        //            n.CompanyId = WebGlobal.CurrentUser.Company.Id;
        //            n.User = WebGlobal.CurrentUser;
        //            n.Content = "[ERROR REPORT]\n" +
        //                msg.Body.Substring(msg.Body.IndexOf("Exception Details"));
        //            n.Save();
        //        }
        //    }
        //    catch
        //    {
        //        msg.Body += "\r\nWARNING: FAILED TO ATTACH ERROR INFO TO COMPANY HISTORY TABLE.";
        //    }
        //    #endregion

        //    try
        //    {
        //        using (SmtpClient sc = new SmtpClient())
        //        {
        //            sc.Send(msg);
        //        }
        //    }
        //    catch (Exception exa)
        //    {
        //        Response.Write(exa.ToString());
        //    }
        //    Response.Write("<html>");
        //    Response.Write("<head><title>Towbook: Application Error</title>");
        //    Response.Write("<style> * { font-family: verdana; font-size: 11px; line-height: 150% } strong,h1 { font-size: 17px; color: navy; display: block; font-family: segoe ui light, 'Open Sans', verdana; } </style>");
        //    Response.Write(_seLockString);
        //    Response.Write("</head>");
        //    Response.Write("<body>");
        //    Response.Write("<h1>Towbook Unexpected Error</h1>");

        //    if (WebGlobal.CurrentUser != null)
        //    {
        //        if (WebGlobal.CurrentUser.Type == Extric.Towbook.User.TypeEnum.SystemAdministrator ||
        //            WebGlobal.CurrentUser.Company.Id == 1 || WebGlobal.CurrentUser.CompanyId == 2)
        //        {
        //            Response.Write("Detailed Exception:<br />" +
        //                "<pre>" + Server.GetLastError().ToString() + "</pre><br />");
        //        }
        //    }

        //    Response.Write("<p style=\"font-weight: bold; font-family: consolas, courier\">" + 
        //        HttpUtility.HtmlEncode(exceptionMessage).Replace("\n", "<br />\n") + 
        //        "<br />Error ID: " + errorId + "</p>");
        //    Response.Write("<p><strong style=\"color: navy; font-size: 11px; border-top: dotted 1px navy; padding-top: 10px\">The action you're trying to take has resulted in an unexpected problem. We apologize for any inconvenience this is causing you.</strong><br />");
        //    Response.Write("Towbook has been notified of this issue along with your information so that we can contact you if necessary.<br /><br />");
        //    Response.Write("If you need immediate assistance, please feel free to call us at " + Core.SupportPhoneNumber + ".</p>");
        //    Response.Write("</body></html>");
        //    Response.End();
        //}*/

        //protected void Application_End()
        //{
        //    Nlog.Log(LogLevel.Info, "Application_End in progress...");
        //    Extric.Towbook.Integration.MotorClubs.Services.ServiceBusHelper.Cleanup();
        //    Extric.Towbook.EventNotifications.ENServiceBusHelper.Cleanup();
        //}
    }
}