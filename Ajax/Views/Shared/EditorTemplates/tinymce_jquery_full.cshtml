@*
Don't forget to reference the JQuery Library here, inside your view or layout.
<script src="@Url.Content("~/Scripts/jquery-x.x.x.min.js")" type="text/javascript"></script>
*@

<script src="@Url.Content("~/Scripts/tinymce/jquery.tinymce.js")" type="text/javascript"></script>

<script type="text/javascript">

    (function(){ 

      $(function() { 
      
          $('#@ViewData.TemplateInfo.GetFullHtmlFieldName(string.Empty)').tinymce({

            // Location of TinyMCE script
            script_url: '@Url.Content("~/Scripts/tinymce/tiny_mce.js")',
            theme: "advanced",
            
            height: "500",
            width: "790",
            verify_html : false,
            plugins : "pagebreak,style,layer,table,save,advhr,advimage,advlink,emotions,iespell,inlinepopups,insertdatetime,preview,media,searchreplace,print,contextmenu,paste,directionality,fullscreen,noneditable,visualchars,nonbreaking,xhtmlxtras,template,wordcount,advlist,autosave",
            
            // Theme options
            theme_advanced_buttons1 : "save,newdocument,|,bold,italic,underline,strikethrough,|,justifyleft,justifycenter,justifyright,justifyfull,styleselect,formatselect,fontselect,fontsizeselect",
            theme_advanced_buttons2 : "cut,copy,paste,pastetext,pasteword,|,search,replace,|,bullist,numlist,|,outdent,indent,blockquote,|,undo,redo,|,link,unlink,anchor,image,cleanup,help,code,|,insertdate,inserttime,preview,|,forecolor,backcolor",
            theme_advanced_buttons3 : "tablecontrols,|,hr,removeformat,visualaid,|,sub,sup,|,charmap,emotions,iespell,media,advhr,|,print,|,ltr,rtl,|,fullscreen",
            theme_advanced_buttons4 : "insertlayer,moveforward,movebackward,absolute,|,styleprops,|,cite,abbr,acronym,del,ins,attribs,|,visualchars,nonbreaking,template,pagebreak,restoredraft,codehighlighting,netadvimage",
            theme_advanced_toolbar_location : "top",
            theme_advanced_toolbar_align : "left",
            theme_advanced_statusbar_location : "bottom",
            theme_advanced_resizing : false,
            
            // Example content CSS (should be your site CSS)
            content_css : "@Url.Content("~/Scripts/tinymce/css/content.css")",
            convert_urls : false,
            
            // Drop lists for link/image/media/template dialogs
            template_external_list_url : "lists/template_list.js",
            external_link_list_url : "lists/link_list.js",
            external_image_list_url : "lists/image_list.js",
            media_external_list_url : "lists/media_list.js"

        });

      });

    })();

</script>

@Html.TextArea(string.Empty, /* Name suffix */
    ViewData.TemplateInfo.FormattedModelValue /* Initial value */
)