using System.Threading.Tasks;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;
using System;
using System.Net;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http.Extensions;
using Microsoft.Extensions.Primitives;
using Extric.Towbook.WebShared;

namespace Ajax
{
    public class BeginRequestMiddleware
    {
        //private readonly string _devKey = "f3666d70c70945a6997016271e73d22b";
        //private readonly string _productionKey = "1fd189ca61f84acfabb3c9bc3f19b96c";
        //private readonly string _defaultPath = "receivers/sykes/";

        private readonly RequestDelegate _next;

        public BeginRequestMiddleware(RequestDelegate next)
        {
            _next = next;
        }

        public async Task InvokeAsync(HttpContext context, IWebHostEnvironment environment)
        {
            this.BeginInvoke(context);
            await this._next.Invoke(context);
            this.EndInvoke(context);
        }



        private void BeginInvoke(HttpContext context)
        {
            NewRelic.Api.Agent.NewRelic.DisableBrowserMonitoring();

            var location = context.Request.PathBase + context.Request.Path + context.Request.QueryString;
            context.Response.Headers["X-Twbk-Location"] = location;

            if (location == "/health")
                return;

            if (StringValues.IsNullOrEmpty(context.Request.Headers["X-Requested-With"]) &&
                StringValues.IsNullOrEmpty(context.Request.Headers["X-Towbook-Internal"]) &&
                StringValues.IsNullOrEmpty(context.Request.Query["ajax"]) &&
                StringValues.IsNullOrEmpty(context.Request.Query["ajax"]) &&
                HttpMethods.IsGet(context.Request.Method))
            {
                //if (UriHelper.GetEncodedUrl(context.Request).ToLowerInvariant().StartsWith("/ajax/settings/"))
                if (UriHelper.GetEncodedPathAndQuery(context.Request).ToLowerInvariant().StartsWith("/ajax/settings/"))
                {
                    var extras = "";
                    foreach (string key in context.Request.Query.Keys)
                    {
                        if (key != "u")
                            extras += "&" + key + "=" + context.Request.Query[key];
                    }

                    if (extras.Length > 0)
                        extras = "?" + extras.Substring(1);

                    context.Response.Redirect(context.Request.Path.Value.ToLowerInvariant().Replace("/ajax/settings/", "/settings/") + extras);
                }
            }
        }

        private void EndInvoke(HttpContext context)
        {
            //// Do custom work after controller execution
        }


        public static async Task WriteInvalidToken(HttpContext context, bool missing = false)
        {
            context.Response.StatusCode = 401;
            string statusDescription;
            if (missing)
                statusDescription = "Bearer Token is required.";
            else
                statusDescription = "Invalid Token. Please ensure your token is valid.";
            //context.Response.StatusDescription = statusDescription;
            context.Response.ContentType = "text/json";
            await context.Response.WriteAsync(JsonConvert.SerializeObject(
                new
                {
                    error = statusDescription
                }));
            return;
        }
        
    }

    public static class BeginRequestMiddlewareExtensions
    {
        public static IApplicationBuilder UseBeginRequest(
            this IApplicationBuilder builder)
        {
            return builder.UseMiddleware<BeginRequestMiddleware>();
        }
    }
}
