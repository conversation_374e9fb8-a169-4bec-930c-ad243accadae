using Extric.Towbook.Company;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Ajax.Areas.Settings.Models
{
    public class CompanyRequirementsModel
    {
        public bool RequireAccountToCreateCall { get; set; }

        public SignatureSettingsModel SignatureSettings { get; set; }

        public CompanyRequirementsModel()
        {
            SignatureSettings = new SignatureSettingsModel();
        }
    }

    public class SignatureSettingsModel
    {
        public bool RequireSignatureForStickerApproval { get; set; }
        public bool RequireSignatureForPermitApproval { get; set; }
        public bool RequireSignatureForTowApproval { get; set; }
        public bool RequireSignatureForDriverClear { get; set; }

        public ElectronicConsentDisclaimer Disclaimer { get; set; }
    }
}