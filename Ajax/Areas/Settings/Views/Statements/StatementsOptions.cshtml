@model Extric.Towbook.API.Models.StatementOptionModel
@using Extric.Towbook.Utility
@{
    ViewBag.Title = "Statement Options";
}


<style type="text/css">
  .box-options ul li {
    display: block;
    padding: 0 10px
  }

  .box-options ul li input {
    display: inline-block;
  }

  .box-options ul li label {
    display: inline-block;
    padding: 5px 10px;
  }

  .box-options ul li .ui-combobox {
    padding-top: 7px
  }

  .box-options ul li .additional {
    color: #777;
    font-style: italic;
  }

  textarea::-webkit-input-placeholder {
    color: #999;
  }
  /* WebKit, Blink, Edge */
  textarea:-moz-placeholder {
    color: #999;
    opacity: 1;
  }
  /* Mozilla Firefox 4 to 18 */
  textarea::-moz-placeholder {
    color: #999;
    opacity: 1;
  }
  /* Mozilla Firefox 19+ */
  textarea:-ms-input-placeholder {
    color: #999;
  }
  /* Internet Explorer 10-11 */
  textarea::-ms-input-placeholder {
    color: #999;
  }
  /* Microsoft Edge */
  
  p .fa-spin, .fa-check { display: none; margin-left: 10px; font-size: 20px }
  p > span { display: none; font-size: 1em;}

  p.default, p.saving, p.saved { line-height: 30px;}
  p.saving .fa-spin, p.saving span:first-of-type {display: inline-block; }
  p.saved .fa-check, p.saved span:last-of-type { display: inline-block; }

  #disclaimer { min-width: 300px; }
</style>

<div class="Overview">
    Customize what displays on your printed Statements.
</div>

<div class="box-options-wrapper">
  <div class="box-options">
    <label class="row">Show/Hide Details from Statement</label>
    <ul id="statementOptions"></ul>
  </div>


  

    <div class="box-options">
      <label class="row">Other Statement Options</label>
      <ul>
        @if (Extric.Towbook.WebShared.WebGlobal.CurrentUser.Company.HasFeature(Extric.Towbook.Generated.Features.AdvancedBilling))
        {
          <li>
            <label>Default Due Date</label>
            <select id="x-due-date-default" data-width="200px" title="select one"></select>
          </li>
        }
          <li>
            <label for="disclaimer">Disclaimer</label>
            <textarea id="disclaimer" class="tb-textarea" data-max-length="8000" placeholder="@(ViewBag.DefaultDisclaimer)" rows="20">@Extric.Towbook.Core.HtmlEncode(this.Model.Disclaimer)</textarea>
          </li>
        </ul>
    </div>

  @if (Extric.Towbook.WebShared.WebGlobal.CurrentUser.Company.HasFeature(Extric.Towbook.Generated.Features.AdvancedBilling))
  {
    <div class="box-options">
      <label class="row">Default email options</label>
      <input type="hidden" id="x-email-id" value="@(this.Model.EmailOption?.Id ?? 0)" />
      <ul>
        <li>
          <label for="x-email-subject">Subject</label>
          <input id="x-email-subject" type="text" style="width: 860px;" placeholder="Enter default subject" value="@Extric.Towbook.Core.HtmlEncode(this.Model.EmailOption?.Subject ?? string.Empty)"/>
        </li>
        <li>
          <label for="x-email-message">Message</label>
          <textarea id="x-email-message" rows="10" placeholder="Enter default message to be included">@Extric.Towbook.Core.HtmlEncode(this.Model.EmailOption?.Message ?? string.Empty)</textarea>
        </li>
      </ul>
    </div>
  }

</div>


<p class="default">
    <a class="button" id="statementOptionsSave" href="" style="min-width:230px">Save</a>
    <i class="fa fa-circle-notch fa-spin fa-3x fa-fw"></i>
    <span class="sr-only">Saving...</span>
    <i class="fa fa-check"></i>
    <span>Saved!</span>
</p>



<script type='text/x-jQuery-tmpl' id='t-option'>
    <li><input type="checkbox" id="${id}" value="1" {{if selected}}checked{{/if}} /><label for="${id}">${name}</label></li>
</script>

<script type='text/x-jQuery-tmpl' id='t-option'>
    <li>${name}</li>
</script>



<script>
  var model =@Html.Raw(this.Model.ToJson());
  var dueDateOptions = @Html.Raw(ViewBag.DueDateOptionsJson);

  function getData() {
    var data = {};
    $('#statementOptions input').each(function (i, o) {
      data[$(o).attr('id')] = $(o).is(":checked");
    });

    data.disclaimer = $('#disclaimer').getVal();
    data.dueDateOption = {
      id: $('#x-due-date-default').getVal()
    };

    data.emailOption = {
      id: $('#x-email-id').getVal() || 0,
      subject: $('#x-email-subject').getVal() || '',
      message: $('#x-email-message').getVal() || ''
    };

    return data;
  }

    $(function () {
        var target = $('#statementOptions');

        towbook.compileTemplate('t-option', '#t-option');

        $('#x-due-date-default').appendOptions(dueDateOptions, false, true, "id", "name", "not specified").combobox();

        @if(this.Model.DueDateOption?.Id >= 0)
        {
          <text>
          var ddo = towbook.get(dueDateOptions, @this.Model.DueDateOption.Id);
          if (ddo)
            $('#x-due-date-default').combobox('quiet', ddo.name);
          </text>
        }
        else
        {
          <text>
          $('#x-due-date-default').setVal(30);
          </text>
        }

        var options = [];

        $.each(model, function(i, o) {
            if (i.indexOf('show') != 0)
              return;

            console.log('i: ', i)
            switch(i){
                case 'showInvoiceItems':
                    name = 'Show Invoice Items';
                    break;
                case 'showReason':
                    name = 'Show Reason';
                    break;
                case 'showToFrom':
                    name = 'Show To From address';
                    break;
                case 'showDriver':
                    name = 'Show Driver';
                    break;
                case 'showOdometer':
                    name = 'Show Odometer';
                    break;
                case 'showVin':
                    name = 'Show VIN';
                    break;
                case 'showInvoiceNumber':
                    name = 'Show Invoice Number';
                    break;
                case 'showVinAsLastEight':
                    name = 'Format Vin as Last Eight';
                    break;
                case 'showLatest':
                    name = 'Include Payments made after the statement date';
                    break;
                case 'showCallNumber':
                    name = 'Show Call Number';
                    break;
                case 'showInvoiceLinks':
                    name = 'Include links to see invoice';
                    break;
                case 'showAccountContact':
                    name = 'Show Account Contact';
                    break;
                case 'showCompletionDate':
                    name = 'Use Completion Date (create date will be used by default)';
                    break;
                case 'showPlateNumber':
                    name = 'Show Plate Number';
                    break;
                case 'showUnitNumber':
                    name = 'Show Unit Number';
                    break;
                case 'showTruck':
                    name = 'Show Truck';
                    break;
                case 'showBillingNotes':
                    name = 'Show Billing Notes';
                    break;
                default:
                    name = i;
                    break;
            }

            options.push({ id: i, name: name, selected: o});
        });

        $(options).each(function(i,o) {
            $(target).append(towbook.applyTemplate('t-option', o));
        });

        $('#statementOptionsSave').on('click', function () {
            $('p.default').removeClass('saved').addClass('saving');

            var statementOptions = getData();
            console.log(statementOptions);

            $('#save').attr('disabled', 'disabled').val('Saving...');

            $.ajax({
                url: "/api/statementOptions/0",
                data: JSON.stringify(statementOptions),
              type: 'PUT',
              contentType: "application/json; charset=utf-8",
            }).done(function (data) {
                console.log("Saved!", data);
                $('#save').removeAttr('disabled');
                $('p.default').removeClass('saving').addClass('saved');
            }).error(function (xhr, status, error) {
                alert("Error" + status + ": " + error);
                $('#save').removeAttr('disabled');
            });

            return false;
        });
    });

</script>