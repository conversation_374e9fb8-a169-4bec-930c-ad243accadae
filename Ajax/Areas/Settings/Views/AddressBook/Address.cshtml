@using Extric.Towbook.Web
@model Extric.Towbook.API.Models.AddressBookEntryModel
@{
    Layout = "../Shared/Dialog.cshtml";

    if (Model.Id == 0)
    {
        ViewBag.Title = "Add an Address Book Entry";
    }
    else
    {
        ViewBag.Title = "Modify Address Book Entry";
    }
}

<style>
    #Name, #Address { width: 100% }
</style>
@if (Model.Id == 0) { }

<div class="form">
  <input type="hidden" name="ajax" value="@(HttpContext.Current.Request.Query["ajax"] == "1" ? "1" : "0")" />
    @Html.AntiForgeryToken()
    @Html.HiddenFor(model => model.Id)

    <dl>
        <dt>@Html.LabelFor(model => model.Name, "Enter the Name of the Contact or Company") @Html.ValidationMessageFor(model => model.Name)</dt>
        <dd>@Html.EditorFor(model => model.Name)</dd>
    </dl>
    <dl>
        <dt>@Html.LabelFor(model => model.Address, "Street Address")</dt>
        <dd>@Html.EditorFor(model => model.Address)</dd>
    </dl>

    <dl style="display: inline-block">
        <dt>@Html.LabelFor(model => model.City)</dt>
        <dd>@Html.EditorFor(model => model.City)</dd>
    </dl>
    <dl style="display: inline-block">
        <dt>@Html.LabelFor(model => model.State)</dt>
        <dd>@Html.EditorFor(model => model.State)</dd>
    </dl>
    <dl style="display: inline-block">
        <dt>@Html.LabelFor(model => model.Zip)</dt>
        <dd>@Html.EditorFor(model => model.Zip)</dd>
    </dl>
    <br/>
    <dl style="display: inline-block">
        <dt>@Html.LabelFor(model => model.Phone, "Phone Number")</dt>
        <dd>@Html.EditorFor(model => model.Phone)</dd>
    </dl>
    <dl style="display: inline-block">
        <dt>@Html.LabelFor(model => model.Email, "Email Address")</dt>
        <dd>@Html.EditorFor(model => model.Email)</dd>
    </dl>
    <dl>
        <dt>@Html.LabelFor(model => model.Notes, "Notes")</dt>
        <dd>@Html.TextAreaFor(model => model.Notes, new { rows=5 })</dd>
    </dl>
    <dl>
        <dt>@Html.LabelFor(model => model.IsProblemCustomer, "Problem Customer")</dt>
        <dd>@Html.EditorFor(model => model.IsProblemCustomer)<span style="padding-left: 5px;">Mark as a problem customer.  Display the notes when using this contact.</span></dd>
    </dl>
</div>

@section buttons  { 
    <ul >
        <li><input type="submit" value="Save" class="button" id="saveAddress" /></li>
        <li><a href="/ajax/settings/AddressBook/@Model.Id" class="button" rel="dialog-cancel">Cancel</a></li>
        @if (Model.Id > 0)
        {
            <li class="right"><input type="button" value="Delete" class="button delete" onclick="deleteThis()"/></li>
        }
    </ul>
}

<script type="text/javascript">
    function deleteThis() {
        if (confirm('Are you sure you want to delete this address book entry?')) {
            $('#dialog-form').get(0).setAttribute('action', '/ajax/Settings/AddressBook/@Model.Id/delete');
            $('#dialog-form').submit();
        }
    }

    // After the initial warning, the user could still save a problem customer
    // with no phone number and an error would be thrown. This provides addtional edit checks.
    $(document).ready(function () {
        $('#saveAddress').on('click', function (e) {
            if ($('#Phone').getVal() == "" && $('#IsProblemCustomer').prop('checked')) {
                warnAboutProblemCustomer();
                $('#Phone').flashRow();
                e.preventDefault();
            }
        });
    });

    $('#IsProblemCustomer').change(function () {
      if ($(this).prop('checked')) {
        if ($('#Phone').getVal() == "") {
          warnAboutProblemCustomer();
          $(this).prop('checked', '');
          return false;
        }
      }
    });

    $('#Phone').on('blur', function () {  
      if ($(this).getVal() == "" && $('#IsProblemCustomer').prop('checked')) {
        warnAboutProblemCustomer();
        $('#Phone').flashRow();
      }
    });

    $('#Phone').change(function () {
      var str = towbook.formatPhoneNumber($(this).val());
      $(this).val(str);
    });

    function warnAboutProblemCustomer() {
        swal({
            type: 'info',
            text: 'You cannot mark this contact as a problem customer unless you provide a phone number. The phone number is necessary to search by contact.'
        })
    }

    $.validator.unobtrusive.parse('#dialog-form');

  $(function () {
    $('#Phone').trigger('change');
  });

</script>
