@model Extric.Towbook.API.Models.AttributeModel
@using Extric.Towbook;
@using (Html.BeginForm("Details", "CustomFields", FormMethod.Post, new { id = "tbForm" }))
{
  @Html.AntiForgeryToken()
  <style type="text/css" >
    #Type, #x-selection-box { width: 20px }
    .tbox { width: 100%;  }
    .box-options-wrapper { display: flex; flex-wrap: wrap; justify-content: flex-start; flex-direction: row;}
    fieldset.box-options { padding: 10px; }
    .box-options dd label { display:inline-flex; padding: 5px; }
    .add-option { width: 120px; cursor: pointer; color: #2b75be; padding: 7px;}
    #x-selection-warn { display: none; }
    .new.selection #x-selection-warn { display: block; padding: 10px; background-color: #f0f0f0; border-top: 1px solid #c8c8c8; }
    #x-selection-box-edit { display: none; }
    .new.selection #x-selection-box-container { border: 2px solid #c8c8c8; border-radius: 8px; overflow: hidden; }

    .edit.selection #x-selection-box-edit { display: table; }

    .delete-option { font-size: 18px; cursor: pointer; color: #c8c8c8; }
    .delete-option:hover { color: darkred; }

    li.option-row { display: flex; column-gap: 10px; }
    li.option-row span { display: inline-flex; margin: auto; }
    span.option-label { flex-basis: 25%; }
    span.option-value { flex-basis: 70%; }
    span.delete-option { flex-basis: 5%; }
    .field-validation-error { padding-left: 10px; }
  </style>
  <div class="container @Html.Raw(Model.Id == 0 ? "new" : "edit") @Html.Raw(Model.Type.ToString().ToLowerInvariant())">
    <div class="box-options-wrapper">
      <fieldset class="box-options">
        <dl>
            <dt>@Html.Label("Custom Field Name: (Example: Motor Club Call Number)") @Html.ValidationMessageFor(model => Model.Name)</dt>
            <dd>@Html.TextBoxFor(model => model.Name, new { @class = "tbox", @maxlength="50" })</dd>
        </dl>
        <dl>
            <dt>@Html.Label("This field will be used to store") @Html.ValidationMessageFor(model => Model.Type)</dt>
            <dd >
                @Html.RadioButtonFor(model => model.Type, Extric.Towbook.AttributeType.String, new { @onclick = "onClickFieldTypeRadioButton()" }) @Html.Label("Text/Any value") <br />
                @Html.RadioButtonFor(model => model.Type, Extric.Towbook.AttributeType.Number, new { @onclick = "onClickFieldTypeRadioButton()" }) @Html.Label("Numbers only") <br />
                @Html.RadioButtonFor(model => model.Type, Extric.Towbook.AttributeType.Boolean, new { @onclick = "onClickFieldTypeRadioButton()" }) @Html.Label("Yes/No") <br />
                @Html.RadioButtonFor(model => model.Type, Extric.Towbook.AttributeType.Date, new { @onclick = "onClickFieldTypeRadioButton()" }) @Html.Label("Date only") <br />
                @Html.RadioButtonFor(model => model.Type, Extric.Towbook.AttributeType.Time, new { @onclick = "onClickFieldTypeRadioButton()" }) @Html.Label("Time only") <br />
                @Html.RadioButtonFor(model => model.Type, Extric.Towbook.AttributeType.DateTime, new { @onclick = "onClickFieldTypeRadioButton()" }) @Html.Label("Date & Time") <br />
                <div id="x-selection-box-container">
                @Html.RadioButtonFor(model => model.Type, Extric.Towbook.AttributeType.Selection, new { @id = "x-selection-box", @onclick = "onClickFieldTypeRadioButton(event)" })@Html.Label("Selection Box") 
                <div id="x-selection-warn"><i class="fa fa-info-circle"></i>&nbsp; Save your new custom field in order to create any custom selection choices.</div>
                </div>
            </dd>
        </dl>
      </fieldset>
    
    
      <fieldset class="box-options" id="x-selection-box-edit">
        <dl>
          <dt><label>Options for <span style="font-weight: 600; line-height: 30px; padding-left: 5px;">@Model.Name</span></label></dt>
          <dd id="x-selection-options">
            <ul></ul>
          </dd>
        </dl>
        <dl>
          <dd>
            <div class="add-option" onclick="addOption()"><i class="fa fa-plus"></i>&nbsp; Add Option</div>
          </dd>
        </dl>
      </fieldset>
    
      <fieldset class="box-options">
          <dl>
              <dt>@Html.Label("Choose where this field will be displayed")</dt>
              <dd>
                  @Html.CheckBoxFor(model => model.PrintOnTowInvoice) @Html.Label("Print on Tow Invoice")<br />
                  @Html.CheckBoxFor(model => model.PrintOnImpoundInvoice) @Html.Label("Print on Impound Invoice")<br />
                  @Html.CheckBoxFor(model => model.PrintOnStatement) @Html.Label("Print on Statement")<br />
              </dd>
          </dl>
          <dl>
              <dd>
                  @Html.HiddenFor(model => model.Id)
                  @Html.Raw("Warning: Updating this will update it on existing records/invoices.")
              </dd>
          </dl>
          @if (ViewBag.HasImpoundOnlyFeature)
          {
          <dl>
              <dt>@Html.Label("Choose where this field is available")</dt>
              <dd>
                  @Html.RadioButtonFor(model => model.ImpoundsOnly, false) @Html.Label("All Calls")<br />
                  @Html.RadioButtonFor(model => model.ImpoundsOnly, true) @Html.Label("Impounds Only")<br />
              </dd>
          </dl>
          }
      </fieldset>
    </div>
  </div>

    <ul class="formNavigation">
        <li><input type="submit" value="Save Changes" class="button" /></li>
        <li><a href="/ajax/settings/Dispatching/CustomFields/List" class="button">Cancel</a></li>
        @if (Model.Id > 0)
        {
            if (Model.Deleted)
            {
                <li class="right"><input type="button" value="Undelete Custom Field" class="button" id="undeleteBtn" /></li>
            }
            else
            {
                <li class="right"><input type="button" value="Delete Custom Field" class="button delete" id="deleteBtn"/></li>
            }
        }
    </ul>

    <script type="text/x-jQuery-tmpl" id="t-selectionOption">
      <li class="option-row" data-id="${id}" data-attribute-id="${attributeId}">
        <span class="option-label">Option ${order}</span>
        <span class="option-value"><input type="text" {{if id > 0}}disabled{{/if}} value="${option}" /></span>
        <span class="delete-option" title="delete this option" onclick="deleteOption(${id})">{{if id > 0}}<i class="fa fa-trash"></i>{{/if}}</span>
      </li>
    </script>

    <script type="text/javascript">
      var selectionOptions = JSON.parse(@Html.Raw(Json.Serialize(ViewBag.SelectionOptionsJson)));


        $(function () {
            $("#undeleteBtn").on("click", function (e) {
                e.stopPropagation();
                e.preventDefault();
                var button = e.currentTarget;
                var id = @Model.Id;
                button.innerText = 'Undeleting';
                button.disabled = true;
                $.ajax({
                  url: '/api/attributes/' + id + '/undelete',
                  type: 'POST',
                }).done(function () {
                    tload(null, null, '/ajax/settings/dispatchingpreferences');
                }).fail(function () {
                  button.innerText = 'Failed';
                  swal({
                    type: 'error',
                    text: 'We ran into an error undeleting this field. Please try again later or contact our support staff.',
                  });
                });
                return false;
            });
        });

        $(function () {
            $("#deleteBtn").on("click", function (e) {
                e.stopPropagation();
                e.preventDefault();
                var button = e.currentTarget;
                var id = @Model.Id;
                button.innerText = 'Deleting';
                button.disabled = true;
                $.ajax({
                  url: '/api/attributes/' + id,
                  type: 'DELETE',
                }).done(function () {
                    tload(null, null, '/ajax/settings/dispatchingpreferences');
                }).fail(function () {
                  button.innerText = 'Failed';
                  swal({
                    type: 'error',
                    text: 'We ran into an error deleting this field. Please try again later or contact our support staff.',
                  });
                });
                return false;
            });
        });

      function onClickFieldTypeRadioButton(e) {

        if (e && e.srcElement && e.srcElement.id == 'x-selection-box') {
          $('.container').addClass('selection');
        } else {
          $('.container').removeClass('selection');
        }
      }

      $('#Name').on('blur', function () {
        var val = _enc($(this).getVal());
        $('#x-selection-box-edit > dl:nth-child(1) > dt > label > span').text(val);
      });

      function buildSelectionOptions() {
        if (!selectionOptions)
          return;

        var order = 1;
        selectionOptions.map(item => item.order = order++);

        $('#x-selection-options').find('li.option-row').remove();

        selectionOptions.map(item => {
          var row = towbook.applyTemplate('t-selectionOption', item);
          $('#x-selection-options').find('ul').append(row);
        });
      }

      function addOption() {

        const data = {
          id: 0,
          attributeId: @Html.Raw(Model.Id),
          option: ''
        };

        swal({
          title: "Enter Option Name",
          input: "text",
          inputPlaceholder: "Option Name",
          showCancelButton: true,
          allowEscapeKey: false,
          allowOutsideClick: false,
          cancelButtonText: "Cancel",
          cancelButtonColor: "#DD6B55",
          inputValidator: function (value) {
            return new Promise((resolve, reject) => {
              if (value == "")
                reject("Please enter a value");
              else if (value.length > 100)
              {
                reject("Too many characters. The maximum number of characters is 100.")
              }
              else
                resolve();
            })
          }
        }).then(function (value) {
          console.log('option value:', value);

          data.option = value;

          swal({
            title: 'Saving...',
            text: 'Please wait',
            allowOutsideClick: false,
            allowEscapeKey: false,
            onOpen: function () {
              swal.showLoading()
            }
          });

          $.post('/api/attributes/' + data.attributeId + '/selectionoptions', data)
            .done(function (response) {
              selectionOptions.push(response);

              buildSelectionOptions();

              swal.close();
            }).fail(function (xhr, status, error) {
              onError(xhr, status, error);
            });

        }).catch(function () {
          return;
        });
      }

      function deleteOption(id) {
        console.log("Deleted id " + id + " ...")
        swal({
          title: "Delete this option?",
          text: "Deleting this option is a permanent action. You are able to create others, but this one will not be retrievable.",
          type: 'warning',
          showCancelButton: true,
          allowEscapeKey: false,
          allowOutsideClick: false
        }).then(function () {
          $.ajax({
            url: "/api/attributes/@Html.Raw(Model.Id)/selectionOptions/" + id, type: 'DELETE'
          }).done(function (data) {

            selectionOptions = selectionOptions.filter(f => f.id != id);
            buildSelectionOptions();

            swal.close();
          }).fail(function (xhr, status, error) {
            onError(xhr, status, error);
          });
        }).catch(function () { return; });
      }

      function onError(xhr, status, error) {
        console.log(xhr, status, error);

        message = xhr.responseText || "An error occurred. \r\n " + status.toUpperCase() + " " + error + "\r\n";

        swal({ type: "warning", text: message, title: "Oops, something went wrong." });
      }

      towbook.compileTemplate('t-selectionOption', $("#t-selectionOption"));

      buildSelectionOptions();
    </script>
        
}
