@using Extric.Towbook.WebShared
@{
    ViewBag.Title = "Dispatching Invoice Display Options";

}


<style type="text/css">
    .box-options ul li {
        display: block;
        padding: 0 10px
    }

        .box-options ul li input {
            display: inline-block;
        }

        .box-options ul li label {
            display: inline-block;
            padding: 5px 10px;
        }

        .box-options ul li .ui-combobox {
            padding-top: 7px
        }

        .box-options ul li .additional {
            color: #777;
            font-style: italic;
        }

    div.save .fa-spin, .fa-check, .fa-exclamation {
        display: none;
        margin-left: 10px;
        font-size: 20px
    }

    div.save > span {
        display: none;
        font-size: 1em;
    }

    div.save {
        line-height: 30px;
    }

    div.saving .fa-spin, div.saving .fa-spin + span {
        display: inline-block;
    }

    div.saved .fa-check, div.saved .fa-check + span {
        display: inline-block;
    }

    div.error .fa-exclamation, div.error .fa-exclamation + span {
        display: inline-block;
    }

    textarea::-webkit-input-placeholder {
        color: #999;
    }
    /* WebKit, Blink, Edge */
    textarea:-moz-placeholder {
        color: #999;
        opacity: 1;
    }
    /* Mozilla Firefox 4 to 18 */
    textarea::-moz-placeholder {
        color: #999;
        opacity: 1;
    }
    /* Mozilla Firefox 19+ */
    textarea:-ms-input-placeholder {
        color: #999;
    }
    /* Internet Explorer 10-11 */
    textarea::-ms-input-placeholder {
        color: #999;
    }
    /* Microsoft Edge */
</style>

<div class="Overview">
    Customize what displays on your printed invoices.
</div>

<div class="box-options-wrapper">
    <div class="box-options">
        <label class="row">Show/Hide Date/Times from invoice</label>
        <ul id="invoiceTimes"></ul>
    </div>

    <div class="box-options">
        <label class="row">Show/Hide Details from invoice</label>
        <ul id="invoiceOptions"></ul>
    </div>

    <div class="box-options">
      <label class="row">Invoice Numbering</label>
      <ul id="invoiceNumbering"></ul>
      <br />
      <label class="row">Email Options</label>
      <ul id="invoiceEmail"></ul>
      @if(WebGlobal.CurrentUser.Company.HasFeature(Extric.Towbook.Generated.Features.AdvancedBilling)) 
      { 
        <text>
      <br />
      
      <div style="display: block; padding: 0px 10px;">
        <label class="row">Subject</label>
        <div style="display: block; padding: 0px 0px 0px 10px;">
          <input id="x-email-subject" type="text" placeholder="Enter default subject" value="@Extric.Towbook.Core.HtmlEncode(ViewBag.EmailSubject ?? string.Empty)" style="width: 100%; padding-left: 10px;" />
        </div>
        <label class="row">Message</label>
        <div style="display: block; padding: 0px 0px 0px 10px;">
          <textarea id="x-email-message" rows="8" placeholder="Enter default message to be included">@Extric.Towbook.Core.HtmlEncode(ViewBag.EmailMessage ?? string.Empty)</textarea>
        </div>
        <input type="hidden" id="x-email-id" value="@(ViewBag.EmailOptionId ?? 0)" />
      </div>
      
        </text>
      }
    </div>

</div>


<div class="save">
    <input type="button" class="button save-button" value="Save Options">
    <i class="fa fa-circle-notch fa-spin fa-3x fa-fw"></i>
    <span class="sr-only">Saving...</span>
    <i class="fa fa-check"></i>
    <span>Saved!</span>
    <i class="fa fa-exclamation"></i>
    <span>Error</span>
</div>



<script type='text/x-jQuery-tmpl' id='t-option'>
    <li><input type="checkbox" id="${id}" value="1" {{if selected}} checked{{/if}} /><label for="${id}">${name}</label></li>
</script>
<script type='text/x-jQuery-tmpl' id='t-option'>
    <li>${name}</li>
</script>
<script id="t-select" type="text/x-jquery-tmpl">
    <li>
        <label for="${id}" style="margin-right:5px">${friendlyName}</label>
        <select id="${id}" name="${name}" style="width:290px">
            {{tmpl(options, { selectedId: selectedValue }) "#t-selectOption"}}
        </select>
    </li>
</script>
<script id="t-selectOption" type="text/x-jquery-tmpl">
    <option {{if id === $item.selectedId}} selected="selected" {{/if}} value="${id}">${value}</option>
</script>
<script>
    var _orig = {};
    var model = {};
    var options = [];
    var hasDestinationArrivalStatus = @(ViewBag.HasDestinationArrivalStatus == true ? "true" : "false");
    var isInOntario = @(WebGlobal.CurrentUser.Company.Country == Extric.Towbook.Company.Company.CompanyCountry.Canada && WebGlobal.CurrentUser.Company.State == "ON" ? "true" : "false");
    function getSettings() {
        $.ajax({
            url: '/api/invoiceoptions',
            type: 'GET',
            beforeSend: function () {
                setLoadingMessage($('#ajaxContent'), "loading");
            }
        }).done(function (data) {
            console.log("invoice options", data);
            init(data);
            data.disclaimers = [];
            _orig = data;
            model = getData();

          if (isInOntario)
            $('#showCompanyEmail').closest('li').hide();
        }).error(function (xhr, status, error) {
            handleError(xhr, status, error);
        }).always(function (data) {
            unsetLoadingMessage();
        });
    }
    function handleError(xhr, status, error) {
        console.log(xhr, status, error);
        swal({ title: "Please try again", text: "Server returned status of " + xhr.status, type: "error" })
            .then(function () {
                $('.save-button').removeAttr('disabled').val('Save Options');
            });
        $('div.save').removeClass('saving').removeClass('saved').addClass('error');
    }
    function init(data) {
        var target = $('#invoiceOptions').html('');
        var timeTarget = $('#invoiceTimes').html('');
        var inTarget = $('#invoiceNumbering').html('');
        var emailTarget = $("#invoiceEmail").html('');
        options = [];
        $.each(data, function (i, o) {
            var name, order, group;
            if (i == 'dispatchEntryInvoiceOptionsId' ||
                i == 'companyId' ||
                i == 'accountId' ||
                i == 'showPriority')
                return;
            switch (i) {
                default: return;
                case 'hideAccountAddress': order = 16; name = 'Show Account Address'; o = !o; group = "details"; break;
                case 'hideAccountContact': order = 17; name = 'Show Account Contact'; o = !o; group = "details"; break;
                case 'hideCallNumber': order = 10; name = 'Show Call Number'; o = !o; group = "details"; break;
                case 'showCompletionDateTime': order = 7; name = 'Show Completion Time'; group = "times"; break;
                case 'showDispatchedDateTime': order = 2; name = "Show Dispatch Time"; group = "times"; break;
                case 'showDriver': order = 15; name = 'Show Driver'; group = "details"; group = "details"; break;
                case 'showEnrouteDateTime': order = 3; name = "Show Enroute Time"; group = "times"; break;
                case 'hideInvoiceNumber': order = 9; name = 'Show Invoice Number'; o = !o; group = "details"; break;
                case 'hideNotes': order = 8; name = 'Show Notes'; o = !o; group = "details"; break;
                case 'showOnSceneDateTime': order = 4; name = "Show On Scene Time"; group = "times"; break;
                case 'hidePrintDate': order = 18; name = 'Show Print Date'; o = !o; group = "times"; break;
                case 'hideReason': order = 11; name = 'Show Reason'; o = !o; group = "details"; break;
                case 'hideTowDestination': order = 13; name = 'Show Tow Destination'; o = !o; group = "details"; break;
                case 'hidePickupLocation': order = 12; name = 'Show Tow Source'; o = !o; group = "details"; break;
                case 'showTowingDateTime': order = 5; name = "Show Towing Time"; group = "times"; break;
                case 'showTruck': order = 14; name = 'Show Truck'; group = "details"; break;
                case 'showDispatcher': order = 15; name = 'Show Dispatcher'; group = "details"; break;
                case 'showDestinationArrivalDateTime':
                    if (hasDestinationArrivalStatus) { order = 6; name = "Show Destination Arrival Time"; group = "times"; }
                    else return;
                    break;
                case 'windowEnvelopeFormat': order = 30; name = 'Format invoice to fit in a window envelope'; group = "details"; break;
                case 'includeDriverSignature': order = 31; name = "Include Driver Signature"; break;
                case 'defaultInvoiceNumber': order = 40; name = 'Default Invoice Number'; group = "numbering"; break;
                case 'defaultInvoiceNumberLocked': order = 41; name = 'Lock Default Invoice Number from being modified'; group = "numbering"; break;
                //case 'defaultInvoiceNumberPrefix': order = 42; name = 'Default Invoice Number Prefix'; group = "numbering"; break;
                case 'hideCreationTime': order = 1; name = "Hide Creation Time"; group = "times"; break;
                case 'showCompanyEmail': order = 19; name = "Show Company Email"; group = "details"; break;
            }
            options.push({ id: i, name, selected: o, order: order, group: group });
        });
        // Sort by order, then by name
        options.sort(function (a, b) {
            if (a.order != b.order)
                return a.order - b.order;
            return a.name.localeCompare(b.name);
        });
        $(options).each(function (i, o) {
            if (o.id == "defaultInvoiceNumber") {
                // TODO: add select box with options of:
                // none, call number, year - call number, year - month - callnumber
                var m = new moment();
                $(inTarget).append(towbook.applyTemplate('select', {
                    id: 'defaultInvoiceNumber',
                    name: 'defaultInvoiceNumber',
                    friendlyName: o.name,
                    options: [
                        { id: 0, value: 'Empty' },
                        { id: 1, value: 'Same as Call Number (123)' },
                        { id: 2, value: 'Year-Call Number (' + m.format("YY") + '-123)' },
                        { id: 3, value: 'Year-Month-Day-Call Number (' + m.format("YY-MMDD") + '-123)' },
                        { id: 4, value: 'Year-Month-Call Number (' + m.format("YY-MM") + '-123)' },

                        { id: 5, value: 'Invoice Number restarting at 1 every January 1st. (1)' },
                        { id: 6, value: 'Year-Invoice Number (' + m.format("YY") + '-1)' },
                        { id: 7, value: 'Year-Month-Day-Invoice Number (' + m.format("YY-MMDD") + '-1)' },
                        { id: 8, value: 'Year-Month-Invoice Number (' + m.format("YY-MM") + '-1)' },
                    ],
                    selectedValue: o.selected
                }));
            } else {
                if (o.group == "times")
                    $(timeTarget).append(towbook.applyTemplate('option', o));
                else if (o.group == "numbering")
                    $(inTarget).append(towbook.applyTemplate('option', o));
                else
                    $(target).append(towbook.applyTemplate('option', o));
            }
        });
        $("#invoiceEmail").append(towbook.applyTemplate('select', {
            id: 'defaultEmailEventPreference',
            name: 'defaultInvoiceNumber',
            friendlyName: "Email Invoice preference",
            options: [
                { id: 0, value: 'Call is Completed' },
                { id: 1, value: 'Call is Marked Audited' },
            ],
            selectedValue: data.emailInvoiceEventPreference || 0
        }));
        $('#defaultEmailEventPreference').closest('li').append('<div class="additional">Note: only applies to accounts that enable automatic emails</div>');

        if (data.emailOption && data.emailOption.id > 0) {
          $('#x-email-id').setVal(data.emailOption.id);
          $('#x-email-subject').setVal(data.emailOption.subject);
          $('#x-email-message').setVal(data.emailOption.message);
        }

        $('select').combobox();
        $('.save-button').removeAttr('disabled').val('Save Options');
        $('div.save').removeClass('saving').removeClass('saved').removeClass('error');
    }
    function getData() {
        var data = {};
        $('#ajaxContent input').each(function (i, o) {
          var propName = $(o).attr('id');
          if (propName !== undefined) {
              data[propName] = $(o).is(":checked");
            }
        });
        $('#ajaxContent select').each(function (i, o) {
            if ($(o).attr('id') !== undefined)
                data[$(o).attr('id')] = $(o).val();
        });
        data.id = model.id || 0;
        data.hideAccountContact = !data.hideAccountContact;
        data.hideAccountAddress = !data.hideAccountAddress;
        data.hideInvoiceNumber = !data.hideInvoiceNumber;
        data.hideNotes = !data.hideNotes;
        data.hidePickupLocation = !data.hidePickupLocation;
        data.hideTowDestination = !data.hideTowDestination;
        data.hideReason = !data.hideReason;
        data.hideCallNumber = !data.hideCallNumber;
        data.hidePrintDate = !data.hidePrintDate;

        // Email
        data.emailInvoiceEventPreference = $('#defaultEmailEventPreference').val();
        if (data.emailInvoiceEventPreference == "0")
            data.emailInvoiceEventPreference = null;

        data.emailOption = {};
        data.emailOption.id = $('#x-email-id').getVal();
        data.emailOption.subject = $('#x-email-subject').getVal();
        data.emailOption.message = $('#x-email-message').getVal();

        return data;
    }
    function saveSettings() {
        var data = getData();
        $('div.save').removeClass('saving').removeClass('saved').removeClass('error');
        if (JSON.stringify(getData()) === JSON.stringify(model)) {
            $('div.x-no-save-msg').remove();
            $('<div class="x-no-save-msg" style="display: inline-block; padding-left: 10px;">No changes were found.</div>').appendTo($('div.save'));
            setTimeout(function () {
                $('div.x-no-save-msg').fadeOut();
            }, 2000);
            console.log("!!! No changes detected. No save needed.");
            return;
        }
        console.log("SAVING DATA", data);
        var saveType = 'PUT';
        $.ajax({
            url: "/api/invoiceOptions/" + data.id,
            data: JSON.stringify(data),
            dataType: 'json',
            type: saveType,
            contentType: "application/json; charset=utf-8",
            beforeSend: function () {
                $('.save-button').attr('disabled', 'disabled').val('Saving...');
                $('div.save').addClass('saving').removeClass('saved').removeClass('error');
            }
        }).done(function (data) {
            if (data)
                init(data);

            $('div.save').removeClass('saving').addClass('saved');
        }).error(function (xhr, status, error) {
            handleError(xhr, status, error);
        }).always(function () {
            $('.save-button').removeAttr('disabled').val("Save Options");
        });
    }
    $(function () {
        towbook.compileTemplate('option', '#t-option');
        towbook.compileTemplate('select', '#t-select');
        $('.save-button').on('click', function () {
            saveSettings();
            return false;
        });
        $('.block-cancel').on('click', function () {
            $('p.default').removeClass('saving').removeClass('saved');
        });
        $("#addReason").on('click', function() {
            showRuleEditor(null);
        });
        getSettings();
    });
</script>