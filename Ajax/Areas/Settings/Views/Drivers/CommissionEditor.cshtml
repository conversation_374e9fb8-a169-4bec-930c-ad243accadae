@model Extric.Towbook.API.Models.CommissionRateModel
@{
    int driverId = ViewBag.driverId;
}
           
@using (Html.BeginForm("Commissions", "Drivers", FormMethod.Post, new { id = "tbForm" }))
{ 
    @Html.AntiForgeryToken()
    <strong>What type of commission is this?</strong><br />
    
    @Html.Raw("- To pay a percentage (most common), enter a percentage, such as: 5.25% (most common)")<br />
    @Html.Raw("- To pay a flat rate, enter a dollar amount like: <strong>$5</strong>")<br />
    @Html.HiddenFor(model => model.Id)
    @Html.HiddenFor(model => model.RateItemId)
    @Html.Hidden("driverId", driverId)

    <style type="text/css">
            #types dl
            {
                display: table-row;
                padding: 0px;
            }
            #types dl dd
            {
                display: table-cell;
                padding: 0px;
            }
            #types dl dt
            {
                display: table-cell;
                width: 90px;
                padding: 0;
            }
            #types dl dd input
            {
                margin: 1px;
                display: inline;
            }
            
            #CategoryId
            {
                width: 490px;
            }

        </style>

    <fieldset>
    <dl>
        <dt><h2 style="border-bottom: dotted 1px #333; width: 490px"><span title="Specify the default pricing for this rate item. Once you have set up the rate here, you can adjust it per account under each individual account">Specify the price for each type of job this can be billed to</span></h2></dt>
    </dl>
    <dl>
      <dd id="types">
        <dl>
            <dt style="width: 340px; display: block">@Html.Label("Pay this commission for any of the fields below that are empty")</dt>
            <dd>@Html.TextBoxFor(model => model.CommissionValue, new { @onkeypress = "return CheckKeyCode(this, event, false, true);" })</dd>
        </dl>
        @for (int i = 0; i < Model.ExtendedRates.Count; i++)
        {                       
            <dl>
                <dt>@Html.Label(Model.ExtendedRates[i].Name) @Html.HiddenFor(model => Model.ExtendedRates[i].BodyTypeId)</dt>
                <dd>@Html.TextBoxFor(model => Model.ExtendedRates[i].Value, new { @style = "display: inline", @class = "tbox" })</dd>
            </dl>
        } 
        </dd>
    </dl>
  </fieldset>

    
    <ul class="formNavigation bottomStick">
      <li><input type="submit" value="Save" class="button"  /></li>
      <li><a href="/ajax/settings/Drivers/@driverId/Details" class="button">Cancel</a></li>
    </ul>
    
}