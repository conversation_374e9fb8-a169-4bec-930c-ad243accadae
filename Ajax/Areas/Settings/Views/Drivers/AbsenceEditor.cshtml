@model Extric.Towbook.API.Drivers.Controllers.AbsencesController.ScheduleAbsenceModel
@inject Microsoft.AspNetCore.Http.IHttpContextAccessor HttpContextAccessor
@using Ajax;
@using Extric.Towbook;
@using Extric.Towbook.WebShared;
@{
    Layout = "../Shared/Dialog.cshtml";
    ViewBag.Title = "Driver Absence";
}
<input type="hidden" name="ajax" value="@(Context.Request.Query["ajax"] == "1" ? "1" : "0")" />
@Html.HiddenFor(model => Model.Id)
@Html.HiddenFor(model => Model.DriverId)
@Html.HiddenFor(model => Model.OwnerUserId)

Please enter the absence info below.<br />

<dl style="display:inline-block">
    <dt>@Html.LabelFor(model => Model.StartDate, "Date/Time of Absence")</dt>
    <dd>@Html.EditorFor(model => Model.StartDate, "DateTimePicker")</dd>
</dl>

<dl style="display:inline-block">
    <dt>@Html.LabelFor(model => Model.EndDate, "Date/Time End")</dt>
    <dd>@Html.EditorFor(model => Model.EndDate, "DateTimePicker")</dd>
</dl>

<dl>
    <dt><strong>Reason</strong></dt>
    <dd>
        @Html.LabelFor(model => model.Type) @Html.ValidationMessageFor(model => model.Type)
        @Html.DropDownListFor(model => model.Type, MvcExtensions.ToSelectIntList(Model.Type), new { style = "width: 100px", id = "absenceType" })
    </dd>
</dl>


@section buttons {
    <ul>
        <li><input type="button" value="Save Changes" id="saveChanges" class="button" /></li>
        <li><a href="/ajax/settings/drivers/@Model.DriverId/details" rel="dialog-cancel" class="button">Cancel</a></li>
        @if (Model.Id > 0)
        {
            <li class="right"><input type="button" value="Delete Entry" id="deleteEntry" class="button delete" /></li>
        }
    </ul>
}

<script type="text/javascript">

    $(function(){

        $("#saveChanges").click(function(){
            var entryId = @Model.Id;

            $.ajax({
                url: '/api/drivers/' + @Model.DriverId + '/absences' + (entryId == 0 ? '' : ('/' + entryId)), 
                type: entryId == 0 ? 'POST' : 'PUT',
                data: JSON.stringify({
                    "id": entryId,
                    "driverId": @Model.DriverId,
                    "startDate": $("#StartDate").val(),
                    "endDate": $("#EndDate").val(),
                    "createDate": null,
                    "owner": null,
                    "type": $("#absenceType").val()
                }),
                dataType: 'json',
                contentType: 'application/json; charset=utf-8',
            }).done(function (data) {
                parent.finishAbsenceRequest(data);
            }).error(function (xhr, status, error) {
                $("#error").text("The absence you entered is invalid.");
            });
        });

        if ($("#deleteEntry").length > 0){
            $("#deleteEntry").click(function(){
                if (confirm('Are you sure you want to delete this absence entry?')) {

                    var id = @Model.Id;
                    var driverId = @Model.DriverId;

                    $.ajax({
                        url: '/api/drivers/' + driverId + '/absences/' + id,
                        type: 'DELETE',
                    }).done(function (data) {
                        parent.finishAbsenceRequest({ id: id, deleted: true });
                    }).error(function (xhr, status, error) {
                        alert("error: " + status + ", " + error);
                    });
                }
            });
        }

        $('#StartDate_Date').val('@Model.StartDate.Value.ToShortDate(Extric.Towbook.WebShared.WebGlobal.CurrentUser.Company)')
        $('#StartDate_Time').val('@Html.Raw(Model.StartDate.Value.ToShortTowbookTimeString())')
        $('#StartDate_Date').data('towbookDate', '@Model.StartDate.Value.ToShortDateString()')

        $('#EndDate_Date').val('@Model.EndDate.Value.ToShortDate(Extric.Towbook.WebShared.WebGlobal.CurrentUser.Company)')
        $('#EndDate_Time').val('@Html.Raw(Model.EndDate.Value.ToShortTowbookTimeString())')
        $('#EndDate_Date').data('towbookDate', '@Model.EndDate.Value.ToShortDateString()')
      });

</script>
