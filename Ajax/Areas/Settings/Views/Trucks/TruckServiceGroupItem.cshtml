@model Extric.Towbook.API.Models.TruckServiceGroupItemModel
@if (Model.TruckServiceGroupItemId > 0)
    {
        <h2>Modify Scheduled Maintenance Item</h2>
    }
    else {
        <h2>Enter a New Maintenance Item</h2>
    }

@using (Html.BeginForm("Details", "TruckServiceGroup", FormMethod.Post, new { id = "tbForm", @class="towbook-dialog" }))
{
    
    @Html.Label("Enter a short description of what this maintenance is (example: Oil Change)") <br /> 
    @Html.EditorFor(model => model.Name, new { @style="border: solid 1px #afafaf; padding: 5px; width: 400px; margin-top: 5px"})   
    
    <div>
        <strong>How often is this service performed?</strong><br />
        @Html.RadioButtonFor(model => model.CountAtZero, true ) @Html.Label("When odometer reaches specified value") <br />
        @Html.RadioButtonFor(model => model.CountAtZero, false ) @Html.Label("Everytime odometer increases by specified value (starting from current odometer reading)") 
    </div>

    <div style="float: left">
        Miles/Kilometers: <br />
        @Html.EditorFor(model => model.ReoccurMiles, new { @style = "border: solid 1px #afafaf; padding: 5px; margin-top: 5px; width: 185px" })
    </div>
    <div style="float: left">
        Or this number of days:<br />
        @Html.EditorFor(model => model.ReoccurDays, new { @style = "border: solid 1px #afafaf; padding: 5px; margin-top: 5px; width: 185px" })
    </div>

    @Html.HiddenFor(model => model.TruckServiceGroupItemId)
    @Html.HiddenFor(model => model.ServiceGroupId)

    <ul class="formNavigation">
        @if (Model.TruckServiceGroupItemId > 0)
        {
            <li><input type="button" value="Save Changes" class="button" onclick="submitThis(false)"/></li>
        }
        else
        {
            <li><input type="button" value="Create Item" class="button" onclick="submitThis(true)"/></li>
        }
        
        <li><a href="/ajax/settings/Trucks/MaintenanceServiceSchedule/Index" rel="dialog-cancel" class="button">Cancel</a></li>
        @if (Model.TruckServiceGroupItemId > 0)
        {
            <li class="right"><input type="button" value="Delete" class="button delete" onclick="deleteThis()"/></li>
        }
    </ul>
    
    <script type="text/javascript">
        function submitThis(create) {
            var name = $('#Name').val();
            var reoccurDays = $('#ReoccurDays').val();
            var reoccurMiles = $('#ReoccurMiles').val();
            var countAtZero = ($('#CountAtZero').val() === "True" ? 1 : 0);

            console.log("AC: ready to sumbit");

            if(name == "" || (reoccurDays == "0" && reoccurMiles == "0"))
            {
                alert("Please specify a short description and enter either a value for miles/kilometers or the number of days.");
                return;
            }

            var saveUrl = '';
            var saveType = 'POST';

            if (create != 1) {
                saveUrl = '/api/truckMaintenance/' + @Model.TruckServiceGroupItemId;
                saveType = 'PUT';
            }
            else {
                saveUrl = '/api/truckMaintenance/';
                saveType = 'POST';
            }

            $.ajax({
                url: "/api/truckMaintenance/@Model.TruckServiceGroupItemId",
                data: JSON.stringify({
                    "serviceGroupId": @Model.ServiceGroupId, 
                    "name": name,
                    "reoccurDays": reoccurDays,
                    "reoccurMiles": reoccurMiles,
                    "countAtZero": countAtZero,
                    "truckServiceGroupItemId": (create == true ? -1 : @Model.TruckServiceGroupItemId)
                }),
                dataType: 'json',
                type: saveType,
                contentType: "application/json; charset=utf-8",
            }).done(function (data) {
                console.log("AC submitted as " + (create == true ? "POST" :  "PUT") + ": ", data);
                closeLightbox();
                if(create == true)
                    finishItemCreateRequest(data)
                else
                    finishItemUpdateRequest(data)
            }).error(function (xhr, status, error) {
                $(loader).hide();
                $(SendMessageForm).show();
                alert("Error" + status + ": " + error);
            });
        }

        function deleteThis() {
            if (confirm('Are you sure you want to delete this schedule item?')) {
                //$('#tbForm').get(0).setAttribute('action', '/ajax/settings/Trucks/TruckServiceGroup/delete');
                //$('#tbForm').submit();

                $.ajax({
                    url: "/ajax/settings/Trucks/TruckServiceGroup/delete?ajax=1",
                    data: JSON.stringify({ "TruckServiceGroupItemId": @Model.TruckServiceGroupItemId }),
                    type: "POST",
                    contentType: "application/json; charset=utf-8",
                }).done(function (data) {
                    closeLightbox();
                    finishItemDeleteRequest({ "serviceGroupId": @Model.ServiceGroupId, "truckServiceGroupItemId": @Model.TruckServiceGroupItemId });
                }).error(function (xhr, status, error) {
                    $(loader).hide();
                    $(SendMessageForm).show();
                    alert("Error" + status + ": " + error);
                });
            }

        }

        

    </script>
}