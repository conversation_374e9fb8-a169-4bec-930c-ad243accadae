@model Extric.Towbook.API.Models.TruckMaintenanceItemModel
@{
    Layout = "../Shared/Dialog.cshtml";
    ViewBag.Title = "Record Maintenance"; 
}

<div class="form">
    <input type="hidden" name="ajax" value="@(Context.Request.Query["ajax"] == "1" ? "1" : "0")" />
    <dl>
        <dt>@Html.LabelFor(model => Model.Title) @Html.ValidationMessageFor(model => Model.Title)</dt>
        <dd>@Html.EditorFor(model => Model.Title)</dd>
    </dl>
    <dl>
        <dt>@Html.LabelFor(model => Model.Description)</dt>
        <dd>@Html.TextAreaFor(model => Model.Description)</dd>
    </dl>
    <dl>
        <dt>@Html.Label("Scheduled Date")</dt>
        <dd>
            @Html.EditorFor(model => Model.ScheduledDate, "datePicker") 
        </dd>
    </dl>
    <dl>
        <dt>Amount ($)</dt>
        <dd>
            @Html.TextBoxFor(model => Model.Cost, new { @class = "NumbersDecOnly tbox" })
        </dd>
    </dl>
         
    @Html.HiddenFor(model => Model.TruckMaintenanceItemId)
    @Html.HiddenFor(model => Model.TruckId)
    @Html.HiddenFor(model => Model.UserId) 
</div>

@section buttons {
<ul>
    <li><input id="maintenanceSave" type="submit" value="Save Changes" class="button" /></li>
    <li><a href="/ajax/settings/Trucks/TrucksMain/@Model.TruckId/details" class="button" rel="dialog-cancel">Cancel</a></li>
    @if (Model.TruckMaintenanceItemId > 0)
    {
        <li class="right"><input id="maintenanceDelete" type="button" value="Delete" class="button delete" /></li>
    }
</ul>
}
    
<script type="text/javascript">
    $(function () {

        $('#maintenanceSave').on('click', function () {
            if (!$('#dialog-form').valid())
                return false;

            $('#dialog-form').submit();

            // todo: change to use API?
        });

        $('#maintenanceDelete').on('click', function () {
            if (confirm('Are you sure you want to delete this maintenence entry?')) {
                $('#dialog-form').get(0).setAttribute('action', '/ajax/settings/Trucks/Maintenance/delete');
                $('#dialog-form').submit();
            }
        });
    });
</script>