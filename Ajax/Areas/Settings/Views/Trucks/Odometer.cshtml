@using Extric.Towbook;
@using Extric.Towbook.WebShared
@model Extric.Towbook.API.Models.TruckOdometerReadingModel
@{ 
    Layout = "../Shared/Dialog.cshtml";
    ViewBag.Title = "Record Odometer"; 
}
<input type="hidden" name="ajax" value="@(Context.Request.Query["ajax"] == "1" ? "1" : "0")" />
@Html.HiddenFor(model => Model.Id)
@Html.HiddenFor(model => Model.TruckId)
@Html.HiddenFor(model => Model.UserId) 

Please enter the trucks odometer reading below.

<dl>
    <dt>@Html.LabelFor(model => Model.Odometer) <span id="error"></span></dt>
    <dd>@Html.TextBoxFor(model => Model.Odometer, new { @class = "NumbersOnly" })</dd>
</dl>
<dl>
    <dt>@Html.LabelFor(model => Model.Date, "Date/Time of Odometer Reading")</dt>
    <dd>@Html.EditorFor(model => Model.Date, "DateTimePicker")</dd>
</dl>

@section buttons {
    <ul>
      <li><input type="button" value="Save Changes" id="saveChanges" class="button" /></li>
      <li><a href="/ajax/settings/Trucks/TrucksMain/@Model.TruckId/details" rel="dialog-cancel" class="button">Cancel</a></li>
    @if (Model.Id > 0)
    {
        <li class="right"><input type="button" value="Delete Entry" id="deleteEntry" class="button delete" onclick="deleteThis()"/></li>
    }
    </ul>
}
    
<script type="text/javascript">

    $(function(){

        $("#saveChanges").click(function(){
            var entryId = @Model.Id;

            $.ajax({
                url: '/api/trucks/' + @Model.TruckId + '/odometer' + (entryId == 0 ? '' : ('/' + entryId)),
                type: entryId == 0 ? 'POST' : 'PUT',
                data: JSON.stringify({
                    "id": entryId,
                    "truckId": @Model.TruckId,
                    "odometer": $("#Odometer").val(),
                  "date": $("#Date_Date").val() + ' ' + $("#Date_Time").val().toUpperCase(),
                    "createDate": null,
                    "owner": null
                }),
                dataType: 'json',
                contentType: 'application/json; charset=utf-8',
            }).done(function (data) {
                data.user = { fullName: data.owner.fullName };
                parent.finishOdometerRequest(data);
            }).error(function (xhr, status, error) {
                $("#error").text("The odometer value you entered is invalid.");
            });
        });

        if ($("#deleteEntry").length > 0){
            $("#deleteEntry").click(function(){
                if (confirm('Are you sure you want to delete this odometer entry?')) {

                    var id = @Model.Id;
                    var truckId = @Model.TruckId;

                    $.ajax({
                        url: '/api/trucks/' + truckId + '/odometer/' + id,
                        type: 'DELETE',
                    }).done(function (data) {
                        parent.finishOdometerRequest({ id: id, deleted: true });
                    }).error(function (xhr, status, error) {
                        alert("error: " + status + ", " + error);
                    });
                }
            });
        }
      
        $('#Date_Date').val('@Model.Date.ToShortDate(Extric.Towbook.WebShared.WebGlobal.CurrentUser.Company)')
        $('#Date_Time').val('@Html.Raw(Model.Date.ToShortTowbookTimeString())')
        $('#Date_Date').data('towbookDate', '@Model.Date.ToShortDateString()')
      
      });

</script> 
