@using Extric.Towbook.WebShared;
@model Extric.Towbook.API.Models.TruckExpenseItemModel
@{
    Layout = "../../Shared/Dialog.cshtml";
    ViewBag.Title = Model.Id == 0 ? "Record Expense" : "Modify Expense";
}

<style>
    #Title, #Details { width: 100% }
    .towbook-dialog-footer { left: inherit }
    .field-validation-error { color: red; }
</style>
@if (Model.Id == 0) { }

<div class="form">
    <input type="hidden" name="ajax" value="@(Context.Request.Query["ajax"] == "1" ? "1" : "0")" />

    @Html.HiddenFor(model => Model.Id)

    <dl id="expense-truck">
        <dt>
            @Html.LabelFor(model => model.CategoryId, "Truck")
            @Html.ValidationMessageFor(model => model.TruckId)
        </dt>
        <dd>@Html.DropDownListFor(model => model.TruckId, ViewBag.Trucks as SelectList)</dd>
    </dl>
    @if (Model.TruckId > 0)
    {
        <style>
            #expense-truck {
                display: none
            }
        </style>
    }

    <dl>
        <dt>
            @Html.LabelFor(model => model.CategoryId, "Category")
            @Html.ValidationMessageFor(model => model.CategoryId)
        </dt>
        <dd>@Html.DropDownListFor(model => model.CategoryId, ViewBag.Categories as SelectList)</dd>
    </dl>

    <dl>
        <dt>@Html.LabelFor(mode => Model.Title, "Title (example: Fuel)") @Html.ValidationMessageFor(model => Model.Title)</dt>
        <dd>@Html.EditorFor(mode => Model.Title)</dd>
    </dl>
    <dl>
        <dt>@Html.LabelFor(model => model.Details)</dt>
        <dd>@Html.EditorFor(model => model.Details)</dd>
    </dl>
    <dl style="display: inline-block">
        <dt>@Html.Label("Amount/Cost") @Html.ValidationMessageFor(model => Model.Amount)</dt>
        <dd>@Html.TextBoxFor(model => Model.Amount, new { @class = "NumbersDecOnly tbox" })</dd>
    </dl>
    <dl style="display: inline-block">
        <dt>@Html.LabelFor(model => model.Date)</dt>
        <dd>@Html.TextBoxFor(model => model.Date, new { id="x-expense-date", @class= "towbook-dt-date" })</dd>
    </dl>
    <dl style="display: inline-block">
        <dt>@Html.Label("Payment Method") @Html.ValidationMessageFor(model => Model.PaymentReferenceNumber)</dt>
        <dd>@Html.TextBoxFor(model => Model.PaymentReferenceNumber, new { @class = "tbox" })</dd>
    </dl>
    <dl style="display: inline-block">
        <dt>@Html.Label("Units/Gallons") @Html.ValidationMessageFor(model => Model.Units)</dt>
        <dd>@Html.TextBoxFor(model => Model.Units, new { @class = "NumbersDecOnly tbox" })</dd>
    </dl>
    <dl style="display: inline-block">
        <dt>@Html.Label("Odometer") @Html.ValidationMessageFor(model => Model.Odometer)</dt>
        <dd>@Html.TextBoxFor(model => Model.Odometer, new { @class = "NumbersOnly tbox" })</dd>
    </dl>

    @if (Model.Id < 1)
    {
        <dl>
            <dt>Attach Receipt/Documentation</dt>
            <dd><input type="file" name="file" id="file" /></dd>
        </dl>
    }
</div>

@section buttons  { 
    <ul >
        <li><input type="submit" value="Save" class="button" id="saveExpense" /></li>
        <li><a href="/ajax/settings/Trucks/TrucksMain/@Model.TruckId/details" class="button" rel="dialog-cancel">Cancel</a></li>
        @if (Model.Id > 0)
        {
            <li class="right"><input data-href="/api/trucks/@Model.TruckId/expenses/@Model.Id" data-id="@Model.Id" type="button" value="Delete Expense" class="button delete" id="deleteExpense" /></li>
        }
    </ul>
}
    
<script type="text/javascript">
    $(function () {
        $('#deleteExpense').on('click', function () {
            if (confirm('Are you sure you want to delete this Truck Expense?')) {

                var url = $(this).data('href');
                var expenseId = $(this).data('id');

                $.ajax({
                    url: url,
                    type: 'DELETE',
                    data: "",
                    contentType: "application/json; charset=utf-8"
                }).done(function (response) {
                    parent.finishExpenseRequest({ id: expenseId, deleted: true });
                }).error(function (xhr, status, error) {
                    alert("error: " + status + ", " + error);
                });
            }
        });

        $('#saveExpense').on('click', function (event) {
            event.preventDefault();
            var form = {};

            $('.form input, .form textarea').each(function (e, i) {
                console.log($(i).attr('id') + "=" + $(i).val());
                if ($(i).attr('id') != undefined) {
                    var key = $(i).attr('id');
                    key = key.charAt(0).toLowerCase() + key.slice(1);

                    var value = $(i).val();

                    if (key == "x-expense-date") {
                        // append noon as the time in the value property to avoid datetimeoffset during the POST
                        // that changes the date to a previous day.
                        $(i).val($(i).getVal() + " 12:00 PM");
                        key = "Date";
                    }

                    form[key] = value;
                }
            });

            console.log("ready to save truck expense" + form);

            if (!$('#dialog-form').valid())
                return false;

            $('#dialog-form').submit();
        });

        $.validator.unobtrusive.parse('#dialog-form');

        $('#x-expense-date').tbDatepicker();
        $('#x-expense-date').setVal('@Model.Date.ToShortDate(WebGlobal.CurrentUser.Company)');
        $('#x-expense-date').data('towbookDate', '@Model.Date.ToShortDateString()');
    });
</script>