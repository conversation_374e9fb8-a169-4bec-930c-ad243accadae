@using Extric.Towbook.WebShared;
@model Ajax.Areas.Settings.Models.LandingPageModel
@{
    Layout = null;
    var companies = await WebGlobal.GetCompaniesAsync();
}

<script src="@Url.Content("~/Scripts/LandingPage/LandingPage.js")"></script>
<link rel="stylesheet" href="@Url.Content("~/Styles/LandingPage/LandingPage.css")" />

<style>
    .boxit {
        margin: 10px 10px 10px 0;
        padding-top: 13px;
        background: white;
    }
    .boxit li {
        padding: 0 20px 13px 20px;
    }
    .boxit li .fa,
    .boxit li .fas { 
        padding: 0 10px;
    }
    .boxit li .copyContainer {
        padding: 8px 10px 8px 4px;
        background-color: #2B75BE;
        color: white;
        font-size: 16px;
        transition: background-color 0.5s ease;
    }
    .boxit li .copyContainer:hover {
        cursor: pointer;
        background-color: #23629E;
    }
    .boxit li .shareUrl-input {
        width: 250px;
    }
    .boxit li .copyContainer label {
        color: white;
        font-size: 16px;
        font-family: <PERSON><PERSON><PERSON>, <PERSON><PERSON>;
        background-color: none;
        cursor: pointer;
    }
</style>

@if (Model.LandingPageWillBeDisplayed == true)
{
    <div class="Overview">Welcome to Towbook!</div>

    <div>Click on one of the steps below to get started.</div>

    <table class="list">
        <thead>
            <tr>
                <td style="width: 80%">Steps</td>
                <td style="width: 20%">Status</td>
            </tr>
        </thead>
        <tbody>
            <tr style="cursor: pointer;">
                <td class="CellLeft">
                    <a href="/ajax/settings/company/" onclick="tload(getMenuElement(this))">Step 1. Company Profile</a>
                </td>
                <td>@Model.IsCompanyProfileCompleted</td>
            </tr>
            <tr style="cursor: pointer;">
                <td class="CellLeft">
                    <a href="/ajax/settings/drivers/" onclick="tload(getMenuElement(this))">Step 2. Add your Drivers</a>
                </td>
                <td>@Model.IsDriversConfigurationCompleted</td>
            </tr>
            <tr style="cursor: pointer;">
                <td class="CellLeft">
                    <a href="/ajax/settings/trucks/" onclick="tload(getMenuElement(this))">Step 3. Add your Trucks and Service Vehicles</a>
                </td>
                <td>@Model.IsTrucksConfigurationCompleted</td>
            </tr>
            <tr style="cursor: pointer;">
                <td class="CellLeft">
                    <a href="/Accounts/AccountEditor.aspx" onclick="tload(getMenuElement(this))">Step 4. Add your Accounts</a>
                </td>
                <td>@Model.IsAccountsConfigurationCompleted</td>
            </tr>
            <tr style="cursor: pointer;">
                <td class="CellLeft">
                    <a href="/ajax/settings/rateitems/" onclick="tload(getMenuElement(this))">Step 5. Setup your Pricing for cash and account calls</a>
                </td>
                <td>@Model.IsRateItemsConfigurationCompleted</td>
            </tr>
            <tr style="cursor: pointer;">
                <td class="CellLeft">
                    <a href="/ajax/settings/users" onclick="tload(getMenuElement(this))">Step 6. Add Users - give your employees access to Towbook</a>
                </td>
                <td>@Model.IsUsersConfigurationCompleted</td>
            </tr>
        </tbody>
    </table>
}
else
{
    <div>
        To adjust various settings for your company, utilize the different settings tabs on the left!
        <h2>Items needing your attention</h2>

        Towbook automatically alerts you when we detect items that might need your attention.
        <br /><br />

        <strong>Currently, there are no items requiring your attention.</strong>
        <ul id="alerts">
            <li>
                <strong>You only have one login created for Towbook.</strong><br />
                Towbook lets you create each of your employees their own user account -- for no additional charge.
            </li>
            <li>
                <strong>You don't have any trucks entered</strong><br />
                To get the most benefit out of Towbook, we highly recommend entering your trucks into Towbook.
            </li>
            <li>
                <strong>You don't have any drivers entered</strong><br />
                By setting up your company's drivers, you can take advantage of text messaging, commissions tracking, and more.

            </li>
            <li>
                <strong>You haven't created an impound lot.</strong><br />
                Creating an impound lot allows you to start the process of storing vehicles through Towbook!
            </li>
        </ul>
        <br />
        <br />

        <label>Incoming Email Address@(companies.Length > 1 ? "es" : ""): </label>
        <ul class="boxit">

            @foreach (var c in companies)
            {
                var x = Extric.Towbook.Utility.SqlMapper.QuerySP<dynamic>("internalGetPrimaryEmailByCompanyId", new { CompanyId = c.Id }).FirstOrDefault();
                if (x != null)
                {
                    var forwards = Extric.Towbook.Integrations.Email.EmailAddressForwarder.GetByEmailAddress((int)x.EmailAddressId).Where(f => f.Email != "<EMAIL>");
                    
                    <li>
                        @(c.Name) &nbsp;&nbsp;&nbsp;<a href="mailto:@x.EmailAddress">@x.EmailAddress</a>
                        @if (forwards.Any())
                        {
                            <i class='fa fa-long-arrow-right'></i><span>Forwarded To</span><i class='fa fa-long-arrow-right'></i>
                            @Html.Raw(@String.Join(" , &nbsp;", forwards.Select(o => "<a href='mailto: " + o.Email + "'>" + o.Email + "</a>")))
                        }
                    </li>
                }
            }
        </ul>
        <br />

        <!-- Not showing fax numbers yet, though this code runs fine -->
        @*
        <label>Fax Number@(companies.Length > 1 ? "s" : ""): </label>
        <ul class="boxit">

            @foreach (var c in companies)
            {
                var zx = Extric.Towbook.Utility.SqlMapper.QuerySP<dynamic>("internalGetPrimaryFaxByCompanyId", new { CompanyId = c.Id }).FirstOrDefault();
                if (zx != null)
                {
                    <li>
                        @(c.Name) &nbsp;&nbsp;&nbsp;@(Extric.Towbook.Core.FormatPhone(zx.FaxNumber))
                    </li>
                }
            }
        </ul>
        *@

        
        <br />
        <label>Refer Friends, Get Rewarded:</label>
        <ul class="boxit">
            <li>
                <p>Share your referral link</p>
                <div>
                    <!-- COPY INPUT -->
                    <input class="shareUrl-input" type="text" readonly="readonly" value="@Model.ReferralLink" /><!--
                 --><span class="copyContainer"><i class="fas fa-clipboard"></i><label>Copy</label></span>
                    <p>Refer a friend and you'll get a free month on Towbook for every company you send our way when they sign up for a Towbook subscription!</p>
                </div>
            </li>
        </ul>
        
    </div>
}
@if (Model.displayCongratulationMessage == true)
{
    <div class="Overview">
        Congratulations! Your Basic Setup is Complete<br>
        <a class="button" href="/default.aspx" class="tip">Dashboard</a>
    </div>
}
<style>
    #alerts {
        margin-top: 10px;
        display: none;
    }

        #alerts li {
            margin-bottom: 10px;
        }
</style>
<script type="text/javascript">
$(function () {
    var $temp = $("<input>");
    var $url = "@Model.ReferralLink";

    $('.copyContainer').on('click', function () {
        $("body").append($temp);
        $temp.val($url).select();
        document.execCommand("copy");
        $temp.remove();

        $(".copyContainer").find('label:eq(0)').text("Copied!");
        $(".copyContainer").css({ "background-color": "#216321" });
        setTimeout(function () {
            $(".copyContainer").find('label:eq(0)').text("Copy");
            $(".copyContainer").css({ "background-color": "#2B75BE" });
        }, 3000);
    })


});
</script>
