<style>
    #ch > h1:first-child {
        display: none;
    }

    #ch {
        background: var(--slate-2);
        padding: 0;
    }

    label {
        color: inherit !important;
    }

    #ch > #ajaxContent {
        margin-left: 0;
        margin-right: 0;
    }
</style>

<link rel="stylesheet" href="/dist/style.css" />

<script>
    // Create a new script element
    var script = document.createElement('script');
    // Set the source attribute to the URL of the script
    // TODO: use cachebreaking key from file
    script.src = '/dist/login-restriction-settings.js?_=1709674104';
    // Set the type attribute to "module"
    script.type = 'module';
    // Set the defer attribute
    script.defer = true;
    // Append the script element to the document's head
    document.head.appendChild(script);
</script>

<script defer>
    window.forceRender?.["login-restriction-settings"]?.();
</script>
<div id="login-restriction-settings"></div>
