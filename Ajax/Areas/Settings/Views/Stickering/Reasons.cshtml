@model Extric.Towbook.API.Stickering.Models.ReasonModel
@using Extric.Towbook.Utility
@{
    ViewBag.Title = "Sticker Reasons";
}

<style>
    h3 {
        font-size: 20px;
    }

    .section-header {
        padding: 20px 0;
        font-size: 20px;
        color: #A2A2A2;
        font-family: 'Segoe UI', "Open Sans", ff-meta-web-pro, calibri, arial;
    }

    #reasonBody tr td {
        line-height: 26px;
    }

    #reasonBody tr td:last-of-type {
        text-align: right;
    }

    #reasonBody tr.inactive > td {
        color: #ccc;
    }

    #reasonBody tr.inactive td:first-of-type span {
        text-transform: uppercase;
        font-weight: bold;
        color: #aaa;
        padding-left: 10px;
    }

    #reasonBody tr .fa-lock {
        visibility: hidden;
    }

    #reasonBody tr .fa-edit {
        visibility: hidden;
    }

    #reasonBody tr:hover .fa-edit {
        visibility: hidden;
    }

    #reasonBody tr.default.active:hover .fa-edit,
    #reasonBody tr.default.inactive:hover .fa-edit {
        display: inline-block;
        visibility: visible;
        color: #ccc;
    }

    #reasonBody tr.default.active:hover .fa-edit:hover,
    #reasonBody tr.default.inactive:hover .fa-edit:hover {
        color: #3982d3;
        cursor: pointer;
    }

    #reasonBody tr.default.inactive:hover .fa-info-circle {
        visibility: hidden;
    }

    #reasonBody tr td .fa {
        font-size: 14px;
        line-height: 18px;
    }

    #reasonBody tr td .fa > span {
        padding-left: 3px;
    }

    #reasonBody tr.default {
        cursor: pointer;
    }

    #reasonBody tr.internal:hover td {
        background-color: white;
        cursor: initial;
    }

    #addReason {
        padding: 20px;
    }

    .x-text-input  {
        padding: 10px;
    }

    .saveButton {
        padding: 5px;
        margin-left: 5px;
    }

    .cancel-button {
        border-width: 0px;
    }

    .blockUI.blockOverlay {
        background-color: white !important;
    }
</style>

<div class="Overview">
    Manage sticker reasons for the company. You will need to associate them with a private property account that has stickering enabled before you can apply them to a sticker.
</div>

<div>
    <a class="button addNewType" onclick="return false;">Add new reason</a>
    <span style="padding-left: 10px;"><input id="hideInactive" type="checkbox" checked="checked"/>Hide inactive reasons</span>
</div>

<table id="reasonTable" class="list" style="width: 100%;">
    <thead>
        <tr><th>Name</th><th>Description</th><th style="width: 7%"></th></tr>
    </thead>
    <tbody id="reasonBody"></tbody>
</table>

<script type="text/x-jQuery-tmpl" id="t-popup">
    <div id="addReason" data-id="${id}">
        <label for="name">Reason Name <span style="font-size: .8em;">(max 32 characters)</span></label>
        <input class="x-name x-text-input" name="name" type="text" placeholder="name" value="${name}" style="width: 100%;" maxlength="32"/>
        <label for="description">Description</label>
        <input class="x-description x-text-input" name="description" type="text" placeholder="description" value="${description}" style="width: 100%;" />
        {{if (id > 0)}}
        <br /><br />
        <input class="x-active" type="checkbox" {{if (deleted)}} checked="checked" {{/if}} /> Make this reason inactive (do not use anymore)
        {{/if}}
        <div style="position: absolute; bottom: 15px; left: 15px; right: 15px; text-align: right;"><input type="button" value="Cancel" onclick="pup.close();" id="cancelBtn" class="cancel-button" /><input type="button" value="Save" class="saveButton button" /></div>
    </div>
</script>

<script type="text/javascript">
    var reasons = [];
    var model = @Html.Raw(Model.ToJson());
    var companyId = @ViewBag.Company.Id;

    towbook.compileTemplate('t-popup', $("#t-popup"));

    var pup = new Popup({
        title: 'Add new reason',
        width: 600,
        height: 400,
        modal: false
    });

    var loadData = function () {
        setLoadingMessage($('#ajaxContent'), "loading");

        $.get('/api/stickering/reasons/' + companyId + '/?includeAll=true').done(function (response) {
            reasons = response;
            refreshList();

            unsetLoadingMessage();
        }).error(function (xhr, status, error) {
            handleError(xhr, status, error);
        });
    }

    var handleError = function (xhr, status, error) {
        console.log(xhr, status, error);
        swal({ title: "Please try again", text: "Server returned status of " + xhr.status, type: "error" })
            .then(function () {
                $('#optionSave').removeAttr('disabled');
                $('#optionSave').val('Save Options');
            });

        $('div.save').removeClass('saving').removeClass('saved').addClass('error');
    }

    var refreshList = function () {
        var encode = function encode(html) { return document.createElement('div').appendChild(document.createTextNode(html)).parentNode.innerHTML };

        var content = $('#reasonBody');

        content.find('tr').remove();

        $.each(reasons, function (i, type) {
            $('<tr class="' + (type.companyId ? 'default' : 'internal') + (type.deleted ? ' inactive' : ' active') + '" data-id="' + type.id + '">' +
                '<td>' + encode(type.name) + '</td>' +
                '<td>' + encode(type.description == null ? '' : type.description ) + '</td>' +
                '<td><i class="fa far fa-edit"><span>modify</span></i></td>' +
                '</tr>').appendTo(content);
        });

        $('#reasonBody').find('tr').off('click').on('click', function () {
            if ($(this).hasClass('internal'))
                return true;

            showPopup($(this).data('id'));
        });

        hideInactive($('#hideInactive').is(':checked'));
    }

    var showPopup = function(id) {
        pup.show();

        var type = reasons.filter(function (f) { return f.id == id })[0] || [];

        var content = $(towbook.applyTemplate('t-popup', { id: type.id, name: type.name, description: type.description, deleted: type.deleted }));

        if (type.id > 0)
            pup.setTitle('Modify reason');
        else
            pup.setTitle("Add a new reason");

        $(content).find('.saveButton').on('click', function () {
            var id = $('#addReason').data("id");
            var name = $('#addReason').find('.x-name').val();
            var description = $('#addReason').find('.x-description').val();
            var activeObj = $('#addReason').find('.x-active').first();

            if (name == "") {
                pup.close();
                return;
            }

            var m = model;
            m.id = id;
            m.companyId = companyId;
            m.name = name;
            m.description = description;
            m.deleted = activeObj.length == 0 || activeObj.is(":checked") ? true : false;

            $.when(saveReason(m)).then(function (data) {
                var type = towbook.get(reasons, data.id, "id");
                if (type == null) {
                    reasons.push({ id: data.id, name: data.name, description: data.description, companyId: @ViewBag.Company.Id, deleted: data.companyId == null });

                    // sort alphabetical
                    reasons = reasons.sort(function (a, b) { return a.name - b.name; });
                }
                else
                    $.extend(type, data);

                pup.close();
                refreshList();

                // indicate newly created or updated table row
                $('#reasonTable').find('tr[data-id="' + data.id + '"]').flashRow();

            }).fail(function (xhr, status, error) {
                handleError(xhr, status, error);
            });
        });

        pup.setContent(content);
    }

    var saveReason = function (param) {
        var deferred = new $.Deferred();

        var type = "POST";
        var url = '/api/stickering/reasons';
        if (param.id > 0) {
            type = "PUT";
            url += '/' + param.id;
        }
        else
            param.id = 0;

        $.ajax({
            url: url,
            data: JSON.stringify(param),
            type: type,
            contentType: "application/json; charset=utf-8"
        }).done(function (data) {
            deferred.resolve(data);
        }).fail(function (xhr, status, error) {
            deferred.reject(xhr, status, error);
        });
        return deferred.promise();
    };

    var hideInactive = function (hide) {
        if (hide)
            $('#reasonTable').find('tr.inactive').hide();
        else
            $('#reasonTable').find('tr.inactive').show();
    };

    $(function () {
        loadData();

        $('.addNewType').on('click', function () {
            showPopup();
        });

        $('#hideInactive').change(function (e) {
            hideInactive($(this).is(":checked"));
        });
    });

</script>