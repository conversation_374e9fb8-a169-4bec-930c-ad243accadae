@model Extric.Towbook.API.Models.StorageRateModel
@inject Microsoft.AspNetCore.Antiforgery.IAntiforgery Xsrf

<script>
    @functions{
        public string TokenHeaderValue()
        {
            return Xsrf.GetAndStoreTokens(Context).RequestToken;
        }
    }
    function getHeaders() {
       return { 'RequestVerificationToken': '@TokenHeaderValue()' };
    }
</script>

@using (Html.BeginForm("UpdateStorage", "RateItems", FormMethod.Post, new { id = "tbForm" }))
{   
    <style type="text/css">
        #StorageGracePeriodHours { width: 180px; }
        #storageRateDetailsAjaxTarget label { font-weight: normal }
        #storageRateDetailsAjaxTarget { margin-top: 20px }
    </style>
    
    @Html.ValidationSummary(true)  
    <div class="Overview">Setup and adjust storage calculation preferences, for your entire company or specific accounts.</div>
    
    <label for="ddlAccounts">Apply to:</label>
    @Html.DropDownListFor(model => model.AccountName, new SelectList(Model.Accounts, "Id", "Company"), new { id = "ddlAccounts", data_width = "100%" })

    <div id="storageRateDetailsAjaxTarget">
        @await Html.PartialAsync("StorageRateGracePeriodDetails")
    </div>
    
    
    <div id="formNavigationHolder"></div>
    <ul class="formNavigation bottomStick stickingBottom">
        <li>
            <input id="storageRatesSaveBtn" type="submit" value="Save Changes" class="button" /></li>
    </ul>
}
@*This is used when we save data into the db*@
@Html.Hidden("hfUpdateStorageUrl", Url.Action("UpdateStorage", "RateItems"))
@*This is used when we select different accounts*@
@Html.Hidden("hfStorageRateDetailsUrl", Url.Action("GetAccountStorageRateConfiguration", "RateItems", new { accountId = Model.AccountId }))

<script type="text/javascript">
$(document).ready(function () {
    $('#ddlAccounts').combobox({ selected: accountChange });


    $('#StorageGracePeriodHours').combobox({ selected: function () { onGracePeriodChange(); } });

    $("input[name='ChargeStart'][type='radio']").change(function () {
        console.log("AC: inside chance event");
        syncOptions();
    });


    $('#storageRatesSaveBtn').bind('click', function () {
        var updateUrl = $("#hfUpdateStorageUrl").val();
        var maxCharge = $("#MaximumCharge").val();
        var maxInitial = $("#MaxInitial").val();
        var data = {
            Id: $("#hfStorageRateId").val(),
            AccountId: $("#ddlAccounts").val(),
            MaximumCharge: maxCharge == "" ? 0 : parseFloat(maxCharge),
            MaxInitial: maxInitial == "" ? 0 : maxInitial,
            StorageGracePeriodHours: $("#StorageGracePeriodHours").val(),
            Surchage_Rate: $("#Surchage_Rate").val(),
            Fuel_Surchage: $("input[name=Fuel_Surchage]:checked").val(),
            ChargeStart: $("input[name=ChargeStart]:checked").val(),
            FreeSaturdays: $("input[name=FreeSaturdays]:checked").val(),
            FreeSundays: $("input[name=FreeSundays]:checked").val(),
            ForceChargeAtMidnight: $("input[name=ForceChargeAtMidnight]:checked").val()
        };

        console.log(data);

        $.ajax({
            type: "POST",
            url: updateUrl,
            data: JSON.stringify(data),
            dataType: 'json',
            contentType: "application/json; charset=utf-8",
            headers: getHeaders(),
        }).success(function (response) {
            console.log("AC: successful save of storage items", response);

            $.growlUI('', 'Changes saved successfully.');
        }).error(function (xhr, status, error) {
            swal('Please try again', 'There was a problem trying to save your storage settings. [status code ' + xhr.status + ' ]', 'error');
        });

        return false;
    });

    syncOptions();
});

function syncOptions() {
    var v = $('input[name=ChargeStart]:checked').val()
    var gp = parseInt($('#StorageGracePeriodHours').val());

    if (isNaN(gp))
      gp = 0;

    $('#divForceChargeAtMidnight').find('label').html("Automatically charge the first day of storage at Midnight (override the grace period)")

    if (v == "Midnight") {
        if (gp > 0)
            $('#divForceChargeAtMidnight').css('visibility', 'visible');
        else
            $('#divForceChargeAtMidnight').css('visibility', 'hidden');


      $('#immediateIgnoreGrace').hide();
    }
    else
    {

        if (v == "Immediate") {
            if (gp == 0)
                gp = "Immediately";

            $('#divForceChargeAtMidnight').css('visibility', 'hidden');

            $('#StorageGracePeriodHours').appendOptions(gracePeriodOptions, true, true, "value", "text", null, null, true).combobox('quiet', gp);
        }
        else if (v == "ImmediateIgnoreGrace" || v == "Each24HoursIgnoreGrace") {
            if (gp > 24)
                gp = 24;

            $('#divForceChargeAtMidnight').find('label').html("If Midnight occurs during the grace period, charge the a day of storage at Midnight (after the grace period ends)")
            $('#divForceChargeAtMidnight').css('visibility', 'visible');

            $('#StorageGracePeriodHours').appendOptions(gracePeriodOptions.filter(function (f) { return f.value != 48 }), true, true, "value", "text", null, null, true).combobox('quiet', gp);
        }

    }

    if (gp > 0) {
        $('label[for=scs1]').html("Every 24 hours after the grace period has finished");
        $('#immediateIgnoreGrace').show();
    }
    else {
        $('label[for=scs1]').html("Every 24 hours");
        $('#immediateIgnoreGrace').hide();
    }
}

function RebindWaypoints() {
    //Kill the Waypoints registered when loading the page
    $('.TabContainer').waypoint('destroy');
    //Restarting the Waypoints to register the new objects and avoid bug "Uncaught RangeError: Maximum call stack size exceeded" 
    $('#storageRateDetailsAjaxTarget div.TabContainer').waypoint(function (direction) {
        var active_section = $(this);
        if (direction === "up")
            active_section = active_section.prev();

        if (active_section.hasClass("topStick"))
            return;

        $('.TabContainer').waypoint('disable');
        var link = $('li[data-target="' + active_section.attr("id") + '"]');
        $('.TabList li').removeClass("selected");
        link.addClass('selected');
        $('.TabContainer').waypoint('enable');
    }, { offset: '100px', continuous: false });
}

function RebindSlidingPanel() {
    //This code is for fix the divs that cannot be hidden nor shown
    $("#storageRateDetailsAjaxTarget div.TabContainer > h1").click(function () {
        var e = $(this).parent().find('.Padded');

        if (e.is(":visible")) {
            e.slideUp();
        }
        else {

            // slide up any of the other visible panels
            $(".TabContainer .Padded:visible").slideUp(500);

            e.slideDown(500);
        }
    });
}

function accountChange() {
    var accountId = $("#ddlAccounts").val();

    $("#hfAccountId").val(accountId);

    var detailsUrl = $("#hfStorageRateDetailsUrl").val();
    var accIdIndex = detailsUrl.indexOf("=");
    var newURL = detailsUrl.substring(0, accIdIndex + 1) + accountId;
    console.log(newURL);

    $.ajax({
        url: newURL,
        contentType: "application/json; charset=utf-8",
        headers: {
            "X-Twbk-Location": "/ajax/settings/rateitems/storage",
        },
        statusCode: {
            200: function (response) {
                $('#storageRateDetailsAjaxTarget').html(response);
                $("#StorageGracePeriodHours").combobox({ selected: function () { onGracePeriodChange(); } });
                RebindSlidingPanel();
                RebindWaypoints();

                syncOptions(false);
            }
        }
    }).error(function (xhr, status, error) {
        console.log("Error: Storage -> AccountChange", xhr, status, error);
        swal('Please try again', 'There was a problem trying to load the account storage settings. [status code ' + xhr.status + ' ]', 'error');
    });
}

function onGracePeriodChange() {
  console.log("AC: inside select of grace period");

  if ($('#StorageGracePeriodHours').val() == 0) {
    if ($('input[name=ChargeStart]:checked').val() == "ImmediateIgnoreGrace" ||
      $('input[name=ChargeStart]:checked').val() == "Each24HoursIgnoreGrace")
      $('#scs1').attr('checked', 'checked');
  }

  syncOptions();
}

</script>