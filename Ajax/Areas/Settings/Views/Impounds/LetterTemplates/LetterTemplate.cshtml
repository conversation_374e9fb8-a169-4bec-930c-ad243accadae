@model Extric.Towbook.API.Models.LetterTemplateModel
<style>

    textarea {
        height: 400px;
    }

    .internal {
        padding: 20px;
        border: 1px solid #D3D3D3;
        background: #efefef;
    }


</style>
@using (Html.BeginForm("Details", "LetterTemplates", FormMethod.Post, new { id = "tbForm" }))
{
    @Html.AntiForgeryToken();
    @Html.HiddenFor(model => model.Id)

    if (Model.No10EnvelopeFormatted)
    {
        @Html.HiddenFor(model => model.No10EnvelopeFormatted)
    }

    @Html.LabelFor(model => model.Title) @Html.ValidationMessageFor(model => model.Title)
    @Html.TextBoxFor(model => model.Title, new { @style = "width: 100%" })<br /><br />
            
    @Html.LabelFor(model => model.Contents) <span id="contents_val_msg" style="display:none" class="field-validation-error" data-valmsg-for="Contents" data-valmsg-replace="true">The Contents field is required.</span>
    @Html.EditorFor(model => model.Contents)

    
    if (Model.Id > 1 && !Model.IsStateLetter)
    {
        <br/>@Html.Label("Options")<br/>

        if (Model.No10EnvelopeFormatted)
        {
            @Html.CheckBoxFor(model => model.No10EnvelopeFormatted)
            @Html.LabelFor(model => model.No10EnvelopeFormatted)<br />
        }
    }

    
    /*
    if (WebGlobal.ImpersonationUser != null && WebGlobal.ImpersonationUser.Type == Extric.Towbook.User.TypeEnum.SystemAdministrator)
    {
        if (Model.Id < 1 || Model.No10EnvelopeFormatted == false) {

            <br /><div class="internal">System Admin Options (customers do not see these options)<br />

            @if (Model.Id > 0 && Model.No10EnvelopeFormatted == false)
            {
                @Html.CheckBoxFor(model => model.No10EnvelopeFormatted)
                @Html.LabelFor(model => model.No10EnvelopeFormatted)
            }

            @if (Model.Id < 1)
            {
                @Html.CheckBox("letter1991", new { @class = "copyLetter", @data_id = "1991" })
                @Html.Label("Make this a copy of the Generic Letter (1991)")
            }
            </div>

            <br />
            <br />
            <br />
            <br />
        }
    }*/

    <div id="formNavigationHolder"></div>
    <ul class="formNavigation bottomStick">
        <li><input id="buttontosave" type="submit" value="Save Changes" class="button" /></li>
        <li><a href="/ajax/settings/impounds/lettertemplates/list" class="button">Cancel</a></li>
        @if (Model.Id > 0)
        {
            <li class="right"><input type="button" value="Delete Template" class="button delete" onclick="deleteThis()" style="width: 150px"/></li>
        }
    </ul>
}

<script type="text/javascript">

    function deleteThis() {
        if (confirm('Are you sure you want to delete this Template?')) {
            $('#tbForm').get(0).setAttribute('action', '/ajax/settings/Impounds/LetterTemplates/delete');
            $('#tbForm').submit();
        }

    }

    $("#buttontosave").click(function () {
        var content = tinyMCE.activeEditor.getContent();
        if (content == "" || content == null) {
            $("#contents_val_msg").show();
            $("#tbForm").valid();
            return false;
        } else {
            $("#contents_val_msg").hide();
        }

    });

    $('.copyLetter').on('click', function (e) {
        var id = $(this).attr('data-id');

        $.post("/ajax/settings/impounds/lettertemplates/copy?id=" + id, JSON.stringify({ id: id }), function (response) {
            if (response.result == "Redirect")
                window.location = "/ajax/settings/impounds/lettertemplates/" + response.id + "/details";
        });
    });

</script>