@using Extric.Towbook.WebShared
@using Extric.Towbook.Utility
@using Extric.Towbook.Company
@model Extric.Towbook.Impounds.InvoiceOptions
@{
    ViewBag.Title = "Invoice Display Options";

    var disclaimer = ViewBag.DefaultDisclaimer != null ? ViewBag.DefaultDisclaimer.Disclaimer : "";
    var invoiceDisclaimer = (InvoiceDisclaimer.GetDefaultByCompanyId(WebGlobal.CurrentUser.CompanyId) ?? new InvoiceDisclaimer()).Disclaimer;
    var defaultDisclaimer = !string.IsNullOrWhiteSpace(invoiceDisclaimer) ? invoiceDisclaimer :
        WebGlobal.CurrentUser.Company.Name +
        " appreciates your business; if you have any questions regarding this invoice, please contact us at " +
        WebGlobal.CurrentUser.Company.Phone;
}

<style type="text/css">
    p .fa-spin, .fa-check { display: none; margin-left: 10px; font-size: 20px }
    p > span { display: none; font-size: 1em;}

    p.default, p.saving, p.saved { line-height: 30px;}
    p.saving .fa-spin, p.saving span:first-of-type {display: inline-block; }
    p.saved .fa-check, p.saved span:last-of-type { display: inline-block; }

    textarea::-webkit-input-placeholder { color: #999; } /* WebKit, Blink, Edge */
    textarea:-moz-placeholder { color: #999; opacity: 1; } /* Mozilla Firefox 4 to 18 */
    textarea::-moz-placeholder { color: #999; opacity: 1; } /* Mozilla Firefox 19+ */
    textarea:-ms-input-placeholder { color: #999; } /* Internet Explorer 10-11 */
    textarea::-ms-input-placeholder { color: #999; } /* Microsoft Edge */
</style>

<div class="Overview">
    Customize what displays on your printed invoices.
</div>

<ul id="invoiceOptions"></ul>


<h3>Disclaimers</h3>

<label>Default Invoice Disclaimer</label>
<textarea id="defaultDisclaimer" class="tb-textarea" data-max-length="8000" style="overflow: auto; height: 140px" placeholder="@defaultDisclaimer">@disclaimer</textarea>
<ul id="reasonDisclaimers"></ul>


<p id="saved-container" class="default">
    <a class="button" id="invoiceOptionsSave" href="" style="min-width:230px">Save Options</a>
    <i class="fa fa-circle-notch fa-spin fa-3x fa-fw"></i>
    <span class="sr-only">Saving...</span>
    <i class="fa fa-check"></i>
    <span>Saved!</span>
</p>


<script type='text/x-jQuery-tmpl' id='t-option'>
    <li {{if hide}}style="display:none;"{{/if}}>
        <input class="invoice-option-item" type="checkbox" id="${id}" value="1" {{if selected}} checked{{/if}} />
        <label for="${id}">${name}</label>
    </li>
</script>

<script>
    var model =@Html.Raw(this.Model.ToJson());
    var isInTexas = @(WebGlobal.CurrentUser.Company.State == "TX" ? "true" : "false");
    var isInOntario = @(WebGlobal.CurrentUser.Company.Country == Company.CompanyCountry.Canada && WebGlobal.CurrentUser.Company.State == "ON" ? "true" : "false");

    $(function () {
        var disclaimerId = '@(ViewBag.DefaultDisclaimer != null ? ViewBag.DefaultDisclaimer.Id : "")';
        var hasDestinationArrivalStatus = @(ViewBag.HasDestinationArrivalStatus == true ? "true" : "false");
        var target = $('#invoiceOptions');

        towbook.compileTemplate('option', '#t-option');
        $(target).append("<li><strong>Show/Hide Details from invoice</strong></li>");

        var options = [];

        $.each(model, function(i, o) {
            var name, order;
            var hide = false;
            if (i == 'impoundInvoiceOptionsId' ||
                i == 'companyId' ||
                i == 'accountId' ||
                i == 'showPriority' ||
                i == 'hideCompletionTime')
                return;
            switch (i) {
                default: name = i; break;
                case 'showCreateDate': order = 1; name = 'Show Create Date'; break;
                case 'showDispatchTime': order = 2; name = 'Show Dispatch Time'; break;
                case 'showEnrouteTime': order = 3; name = 'Show Enroute Time'; break;
                case 'showArrivalTime': order = 4; name = 'Show Arrival Time'; break;
                case 'showTowingTime': order = 5; name = 'Show Towing Time'; break;
                case 'showDestinationArrivalTime':
                    if (hasDestinationArrivalStatus) { order = 6; name = "Show Destination Arrival Time"; }
                    else return;
                    break;
                case 'showCompletionTime': order = 7; name = 'Show Completion Time'; break;
                case 'showCallNumber': order = 8; name = "Show Call Number"; break;
                case 'hideDrivable': order = 9; name = 'Show Drivable'; o = !o; break;
                case 'showDriver': order = 10; name = 'Show Driver'; break;
                case 'hideDriverLicense': order = 11; name = 'Show Driver License'; o = !o; break;
                case 'hideImpoundDate': order = 12; name = 'Show Impound Date'; o = !o; break;
                case 'showInvoiceNumber': order = 13; name = "Show Invoice Number"; break;
                case 'hideKeys': order = 14; name = 'Show Keys'; o = !o; break;
                case 'showNotes': order = 15; name = 'Show Notes'; break;
                case 'showPoliceHold': order = 16; name = 'Show Police Hold'; break;
                case 'showReleaseNotes': order = 17; name = 'Show Release Notes'; break;
                case 'hidePrintDate': order = 18; name = 'Show Print Date'; o = !o; break;
                case 'hideReason': order = 19; name = 'Show Reason'; o = !o; break;
                case 'hideReferenceNumber': order = 20; name = 'Show Reference Number'; o = !o; break;
                case 'hideStockNumber': order = 21; name = 'Show Stock Number'; o = !o; break;
                case 'hideTowDestination': order = 22; name = 'Show Tow Destination'; o = !o; break;
                case 'hideTowSource': order = 23; name = 'Show Tow Source'; o = !o; break;
                case 'showTruck': order = 24; name = 'Show Truck'; break;
                case 'hideAccountContact': order = 25; name = 'Show Account Contact'; o = !o; break;
                case 'showCallContact': order = 26; name = 'Show Call Contact'; break;
                case 'showWindowEnvelope': order = 30; name = 'Format invoice to fit in a window envelope'; break;
                case 'includeDriverSignature': order = 31; name = "Include driver signature"; break;
                case 'showDispatcher': order = 32; name = "Show Dispatcher"; break;
                case 'hideRateItemCategoryNames':
                    if (isInTexas) hide = true;
                    order = 33;
                    name = "Don't group charges by category";
                    break;
                case 'showCompanyEmail':
                  if (isInOntario) hide = true;
                  order = 34;
                  name = "Show Company Email";
                  break;
            }

            options.push({ id: i, name, selected: o, order, hide});
        });

        // Sort by order, then by name
        options.sort(function (a, b) {
            if (a.order != b.order)
                return a.order - b.order;
            return a.name.localeCompare(b.name);
        });

        $(options).each(function(i,o) {
            $(target).append(towbook.applyTemplate('option', o));
        });

        $('.invoice-option-item').on('click', function () {
            $('#saved-container').removeClass('saved');
        });


        $('#invoiceOptionsSave').on('click', function () {
            var invoiceOptions = {};

            $('p.default').removeClass('saved').addClass('saving');

            $('#invoiceOptions input').each(function(i, o) {
                invoiceOptions[$(o).attr('id')] = $(o).is(":checked"); });


            invoiceOptions.hideReferenceNumber = !invoiceOptions.hideReferenceNumber;
            invoiceOptions.hideTowSource = !invoiceOptions.hideTowSource;
            invoiceOptions.hideTowDestination = !invoiceOptions.hideTowDestination;
            invoiceOptions.hidePrintDate = !invoiceOptions.hidePrintDate;
            invoiceOptions.hideReason = !invoiceOptions.hideReason;
            invoiceOptions.hideDrivable = !invoiceOptions.hideDrivable;
            invoiceOptions.hideDriverLicense = !invoiceOptions.hideDriverLicense;
            invoiceOptions.hideKeys = !invoiceOptions.hideKeys;
            invoiceOptions.hideStockNumber = !invoiceOptions.hideStockNumber;
            invoiceOptions.hideImpoundDate = !invoiceOptions.hideImpoundDate;
            invoiceOptions.hideCompletionTime = !invoiceOptions.hideCompletionTime;
            invoiceOptions.hideAccountContact = !invoiceOptions.hideAccountContact;

            console.log(invoiceOptions);

            $('#invoiceOptionsSave').attr('disabled', 'disabled').val('Saving...');

            var saveType = 'PUT';

            $.ajax({
                url: "/api/impoundInvoiceOptions/" + model.impoundInvoiceOptionsId,
                data: JSON.stringify(invoiceOptions),
                dataType: 'json',
                type: saveType,
                contentType: "application/json; charset=utf-8",
            }).done(function (data) {
                var invoiceDisclaimer = { disclaimer: $('#defaultDisclaimer').val() };

                if (!disclaimerId) {
                    invoiceDisclaimer.impound = 1;
                }

                $.ajax({
                    url: "/api/invoiceDisclaimers/" + disclaimerId,
                    data: JSON.stringify(invoiceDisclaimer),
                dataType: 'json',
                type: (disclaimerId ? "PUT" : "POST"),
                contentType: "application/json; charset=utf-8",
                }).done(function (data) {
                    disclaimerId = data.id;

                    $('#save').removeAttr('disabled');
                    $('p.default').removeClass('saving').addClass('saved');
                });
            }).error(function (xhr, status, error) {
                alert("Error" + status + ": " + error);
                $('#save').removeAttr('disabled');
            });

            return false;
        });


        towbook.t
    });

</script>