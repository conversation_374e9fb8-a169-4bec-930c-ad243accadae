@{
    ViewBag.Title = "AccessDenied";
}
@{
    Layout = null;
}

<div class="Overview">
    <p>
        You don't have access to all the companies this user is in. You need to access to  
        @if(ViewBag.CompanyNames.Count > 1)
        {
            <text>the companies</text>
            for(var i =0; i< ViewBag.CompanyNames.Count; i++)
            {
                if(i < ViewBag.CompanyNames.Count - 1)
                {
                    <b>@ViewBag.CompanyNames[i], </b>
                }
                else
                {
                    <text> and <b>@ViewBag.CompanyNames[i]</b></text>
                }
            }
        } else {
            <text>the company <b>@ViewBag.CompanyNames[0]</b></text>

        }
        in order to edit this user. 
    </p>
    <p>
        If you think you should already have access, please call Towbook Support at (810) 320-5063 or email us at 
        <a href="mailto:<EMAIL>"><EMAIL></a>.
    </p>
</div>
