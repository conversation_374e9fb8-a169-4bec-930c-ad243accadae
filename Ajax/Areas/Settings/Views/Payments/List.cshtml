@model List<Extric.Towbook.API.Models.PaymentTypeModel>
@{
	ViewBag.Title = "Payment Types";
}


<style>
	h3 {
		font-size: 20px;
	}

	.section-header {
		padding: 20px 0;
		font-size: 20px;
		color: #A2A2A2;
		font-family: 'Segoe UI', "Open Sans", ff-meta-web-pro, calibri, arial;
	}

	#paymentList tr td:last-of-type {
		text-align: right;
	}

	#paymentList tr.inactive td:first-of-type {
		color: #ccc;
	}

	#paymentList tr.inactive td:first-of-type span {
		text-transform: uppercase;
		font-weight: bold;
		color: #aaa;
		padding-left: 10px;
	}

	#paymentList tr .fa-lock {
		display: none;
	}

	#paymentList tr.internal .fa-lock,
	#paymentList tr.inactive .fa-info {
		display: inline-block;
		color: #ccc;
	}

	#paymentList tr td .fa {
		font-size: 14px;
		line-height: 18px;
	}

	#paymentList tr td .fa > span {
		padding-left: 6px;
	}

	#paymentList tr.internal .fa-info {
		display: none;
	}

	#paymentList tr.active .fa-info {
		display: none;
	}

	#paymentList tr.default {
		cursor: pointer;
	}

	#paymentList tr.internal:hover td {
		background-color: white;
		cursor: initial;
	}
	
	#paymentList tr.default:hover td .fa-edit {
		display: inline-block;
	}

	#addPaymentType {
		padding: 20px;
	}

	.x-name {
		padding: 10px;
	}

	.saveButton {
		padding: 5px;
        margin-left: 5px;
	}

    .cancel-button {
        border-width: 0px;
    }
</style>

<div class="Overview">
	<text>
		Manage what payment type choices will be available to your team.
	</text>
</div>

<div><a class="button addNewType" onclick="return false;">Add payment type</a></div>


<div class="section-header">Payment Types</div>
<table id="paymentList" class="list paymentItem" style="width: 100%;">
	<thead>
		<tr><th>Name</th><th style="width: 10%"></th></tr>
	</thead>
	<tbody></tbody>
</table>



<script type="text/x-jQuery-tmpl" id="t-popup">
	<div id="addPaymentType" data-id="${id}">
		<label for="name">Payment Name</label>
		<input class="x-name" name="name" type="text" placeholder="name" value="${name}" style="width: 100%;" />
		{{if (id > 0)}}
		<br/><br/>
		<input class="x-active" type="checkbox" {{if (!active)}}checked="checked"{{/if}} /> Make this payment type inactive
		{{/if}}
		<div style="position: absolute; bottom: 15px; left: 15px; right: 15px; text-align: right;"><input type="button" value="Cancel" onclick="pup.close();" id="cancelBtn" class="cancel-button" /><input type="button" value="Save" class="saveButton button" /></div>
	</div>
</script>


<script type="text/javascript">
	var paymentTypes = [];

	towbook.compileTemplate('t-popup', $("#t-popup"));

	var pup = new Popup({
		title: 'Add new payment type',
		width: 600,
		height: 400,
		modal: false
	});

	$('.addNewType').on('click', function () {
		showPopup();
	});

	function refreshList() {
		var content = $('#paymentList').find('tbody');

		content.find('tr').remove();

		$.each(paymentTypes, function (i, type) {
			$('<tr class="' + (type.internal == true ? 'internal' : 'default') + (type.active ? ' active' : ' inactive') + '" data-id="' + type.id + '">' +
				'<td>' + type.name + '</td>' +
				'<td><i class="fa fa-lock"><span>REQUIRED</span></i><i class="fa fa-info"><span>INACTIVE</span></i></td>' +
				'</tr>').appendTo(content);
		});


		$('#paymentList').find('tr').off('click').on('click', function () {
            if ($(this).hasClass('internal'))
                return true;

            showPopup($(this).data('id'));
		});
	}

	function showPopup(id) {
		pup.show();

		var type = paymentTypes.filter(function (f) { return f.id == id })[0] || [];

		var content = $(towbook.applyTemplate('t-popup', { id: type.id, name: type.name, active: type.active }));

		if (type.id > 0)
			pup.setTitle("Modify the payment type");
		else
			pup.setTitle("Add a new payment type");

		$(content).find('.saveButton').on('click', function () {
			var id = $('#addPaymentType').data("id");
			var name = $('#addPaymentType').find('.x-name').val();
			var activeObj = $('#addPaymentType').find('.x-active').first();

			if (name == "") {
				pup.close();
				return;
			}


            $.when(
                save({ id: id, name: name, internal: false, active: activeObj.length == 0 || activeObj.is(":checked") ? false : true })
            ).then(function (data) {
				data.name = _enc(data.name);
                var type = towbook.get(paymentTypes, data.id, "id");
                if (type == null)
                    paymentTypes.push({ id: data.id, name: data.name, internal: data.internal, active: data.active });
                else
                    $.extend(type, data);

                pup.close();
                refreshList();
            }).fail(function (xhr) {
                    swal({ title: "Something went wrong", text: "Error: " + xhr.responseText });
                    console.log(xhr);
            });
		});

		pup.setContent(content);
	}

	function save(param) {
		var deferred = new $.Deferred();

		var type = "POST";
		var url = '/api/paymentTypes/';
		if (param.id > 0) {
			type = "PUT";
			url = '/api/paymentTypes/' + param.id;
		}
		else
			param = { name: param.name };

        $.ajax({
            url: url,
            data: JSON.stringify(param),
            type: type,
            contentType: "application/json; charset=utf-8"
        }).done(function (data) {
            deferred.resolve(data);
        }).fail(function (xhr) { 
            deferred.reject(xhr);
        });
		return deferred.promise();
	}

	@foreach (Extric.Towbook.API.Models.PaymentTypeModel type in @Model)
	{
	<text>paymentTypes.push({ id: @type.Id, name: "@type.Name", internal: @(type.Internal == true ? "true" : "false"), active: @(type.Active == true ? "true" : "false") });</text>
	}

	refreshList();

</script>

