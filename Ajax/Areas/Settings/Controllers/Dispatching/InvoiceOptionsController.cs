using System;
using System.Collections.Generic;
using System.Linq;
using Extric.Towbook.WebShared;
using System.Collections.ObjectModel;
using Ajax.Areas.Settings.Models;
using Extric.Towbook.Dispatch;
using Extric.Towbook.API.Models;
using Extric.Towbook.Company;
using Extric.Towbook.Integration;
using Extric.Towbook;
using System.Linq;
using System.Collections.ObjectModel;
using Microsoft.AspNetCore.Mvc;

namespace Ajax.Areas.Settings.Controllers.Dispatching
{
    [Area("Settings")]
    public class InvoiceOptionsController : Controller
    {
        [HttpGet]
        public ActionResult Index()
        {
            return List();
        }

        [HttpGet]
        public PartialViewResult List()
        {
            if (WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.Manager)
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            Response.Headers.Add("X-Twbk-Title", "Dispatching Invoice Display Options");

            var api = new Extric.Towbook.API.Integration.Controllers.CompanyController();

            //API returns type of object, so we need to cast it to Collection<CompanyKeyValue>
            var companyKeyValues = api.Get() as Collection<CompanyKeyValue>;
            bool hasDestinationArrivalStatus = companyKeyValues != null && companyKeyValues.Any(ckv => ckv.Value == "EnableDestinationArrivalStatus");

            ViewBag.HasDestinationArrivalStatus = hasDestinationArrivalStatus;

            return PartialView("../Dispatch/InvoiceOptions");
        }

    }
}
