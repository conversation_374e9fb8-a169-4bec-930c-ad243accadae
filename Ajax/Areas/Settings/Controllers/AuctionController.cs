using Extric.Towbook.Auctions.Joyride;
using Extric.Towbook.WebShared;
using Microsoft.AspNetCore.Mvc;

namespace Ajax.Areas.Settings.Controllers
{
    public class AuctionSettingsModel
    {
        public string Provider { get; set; }
    }

    [Area("Settings")]
    public class AuctionController : Controller
    {
        // GET: Settings/Auction
        [HttpGet]
        public ActionResult Index()
        {

            var model = new AuctionSettingsModel();

            var jc = JoyrideConnection.GetByCompanyId(WebGlobal.CurrentUser.CompanyId);
            if (jc != null)
                model.Provider = "Joyride";

            return View(model);
        }
    }
}