using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.AspNetCore.Mvc;
using Extric.Towbook.WebShared;
using Extric.Towbook.Company;
using Extric.Towbook.Integration;
using Extric.Towbook.API.Models;
using Extric.Towbook.Utility;
using Extric.Towbook;
using System.Collections.ObjectModel;
using Ajax.Filters;
using System.Threading.Tasks;

namespace Ajax.Areas.Settings.Controllers
{
    [Area("Settings")]
    public class ReleaseOptionsController : Controller
    {
        [HttpGet]
        public ActionResult Index()
        {
            return List();
        }

        [HttpGet]
        public PartialViewResult List()
        {
            if (WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.Manager)
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            Response.Headers.Add("X-Twbk-Title", "Release Options");

            var schedule = AfterHoursReleaseFeeModel.GetScheduleByCompany(WebGlobal.CurrentUser.Company, true);

            this.ViewBag.AfterHoursReleaseFee = schedule.ToJson();

            return PartialView("../impounds/requirements/index");
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<JsonResult> Schedule([FromBody] List<ReleaseFeeItem> model)
        {
            if (WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.Manager)
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            var tzo = WebGlobal.CurrentUser.Company.TimezoneOffset;
            var items = model.ToCollection();

            foreach (var item in items)
            {
                foreach (var t in item.Times)
                {
                    // need to translate times to server time.  Store schedule in EST.
                    t.Start = t.Start.Add(TimeSpan.FromMinutes(-(long)tzo * 60));
                    t.End = t.End.Add(TimeSpan.FromMinutes(-(long)tzo * 60));
                }
            }

            var ckv = await CompanyKeyValue.GetFirstValueOrNewAsync(WebGlobal.CurrentUser.CompanyId, Provider.Towbook.ProviderId, "AfterHourReleaseFeeScheduleJson");
            ckv.Value = items.ToJson();
            ckv.Save();

            return Json("success");
        }

    }
}
