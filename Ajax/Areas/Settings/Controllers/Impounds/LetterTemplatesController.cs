using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using Extric.Towbook;
using Extric.Towbook.API.Models;
using Extric.Towbook.Utility;
using Extric.Towbook.WebShared;
using Ajax.Areas.Settings.Models;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace Ajax.Areas.Settings.Controllers
{
    [Area("Settings")]
    public class LetterTemplatesController : Controller
    {
        [HttpGet]
        public async Task<PartialViewResult> List()
        {
            if (WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.Manager)
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            Response.Headers.Add("X-Twbk-Title", "Letter Templates");
            var model = LetterTemplateModel.MapDomainObjectListToModelList(await LetterTemplate.GetByCompanyAsync(WebGlobal.CurrentUser.Company));

            Collection<CompanyLetterTemplate> letterOptions = CompanyLetterTemplate.GetByCompanyId(WebGlobal.CurrentUser.CompanyId);

            var outsideLetters = letterOptions.Where(a => !model.Select(s => s.Id).Contains(a.LetterTemplateId));
            if (outsideLetters?.Count() > 0)
            {
                // add to the list of letter templates
                foreach (var outsideLetter in outsideLetters)
                {
                    var letterTemplate = await LetterTemplate.GetByIdAsync(outsideLetter.LetterTemplateId, false);
                    if (letterTemplate != null)
                    {
                        letterTemplate.IsSharedLetter = true;
                        letterTemplate.IsHidden = outsideLetter.Hidden;
                        model.Add(LetterTemplateModel.MapDomainObjectToModel(letterTemplate));
                    }
                }
            }

            return PartialView("../impounds/lettertemplates/List", model.OrderBy(o => o.Id).ToList());
        }

        // GET: /Settings/ImpoundLetterTemplates/Details/5
        /// <summary>
        /// Returns a read-only view of the LetterTemplate with supplied ID
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<PartialViewResult> Details(int id)
        {
            if (WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.Manager)
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            Response.Headers.Add("X-Twbk-Title", "Modify Template");

            int[] companyIds = new int[] { WebGlobal.CurrentUser.CompanyId }.Union((await WebGlobal.GetCompaniesAsync()).Select(s => s.Id)).ToArray();
            var lt = await LetterTemplate.GetByIdAsync(companyIds, id);

            if (lt == null || !(await WebGlobal.CurrentUser.HasAccessToCompanyAsync(lt.CompanyId)))
                throw new Exception("Invalid ID");

            var model = LetterTemplateModel.MapDomainObjectToModel(lt);

            return PartialView("../impounds/LetterTemplates/LetterTemplate", model);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<ActionResult> Copy(int id)
        {
            if (WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.Manager)
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            int[] companyIds = new int[] { WebGlobal.CurrentUser.CompanyId }.Union((await WebGlobal.GetCompaniesAsync()).Select(s => s.Id)).ToArray();
            var lt = await LetterTemplate.GetByIdAsync(companyIds, id);

            if (lt == null || !(await WebGlobal.CurrentUser.HasAccessToCompanyAsync(lt.CompanyId)))
                throw new Exception("Invalid ID");

            lt.Id = 0;
            lt.CompanyId = WebGlobal.CurrentUser.CompanyId;
            var newId = await lt.SaveAsync();

            return Json(new { result = "Redirect", url = Url.Action("Details", "LetterTemplates"), id = newId });
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<ActionResult> Details(LetterTemplateModel model)
        {
            if (WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.Manager)
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            if (ModelState.IsValid)
            {
                LetterTemplate lt = new LetterTemplate();

                if (model.Id > 0)
                {
                    int[] companyIds = new int[] { WebGlobal.CurrentUser.CompanyId }.Union((await WebGlobal.GetCompaniesAsync()).Select(s => s.Id)).ToArray();
                    lt = await LetterTemplate.GetByIdAsync(companyIds, model.Id);
                    if (lt == null || !(await WebGlobal.CurrentUser.HasAccessToCompanyAsync(lt.CompanyId)))
                        throw new Exception("Invalid ID");

                    lt = LetterTemplateModel.MapModelToDomainObject(model, lt);
                }
                else
                {
                    LetterTemplateModel.MapModelToDomainObject(model, lt);
                    lt.CompanyId = WebGlobal.CurrentUser.CompanyId;
                }

                var id = await lt.SaveAsync();

                // Save company letter template options
                if (id > 0)
                {
                    var clt = (await CompanyLetterTemplate.GetByIdAsync(id, lt.CompanyId)).FirstOrDefault();
                    if (model.IsHidden != lt.IsHidden)
                    {
                        if (clt == null)
                        {
                            clt = new CompanyLetterTemplate()
                            {
                                LetterTemplateId = lt.Id,
                                CompanyId = lt.CompanyId,
                                Hidden = model.IsHidden,
                                No10EnvelopeFormatted = lt.No10EnvelopeFormatted
                            };
                        }

                        clt.Save();
                    }
                }

                return RedirectToAction("List");
            }
            else
            {
                if (model.Id > 0)
                {
                    return await Details(model.Id);
                }
                return New();
            }
        }

        [HttpGet]
        public PartialViewResult New()
        {
            if (WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.Manager)
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            Response.Headers.Add("X-Twbk-Title", "Add New Letter Template");

            var model = LetterTemplateModel.MapDomainObjectToModel(new LetterTemplate());
            
            return PartialView("../Impounds/LetterTemplates/LetterTemplate", model);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<ActionResult> Delete(LetterTemplateModel model)
        {
            if (WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.Manager)
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            var lt = LetterTemplate.GetById((await WebGlobal.GetCompaniesAsync()).Select(s => s.Id).ToArray(), model.Id);

            if (lt != null)
            {
                await lt.DeleteAsync();
            }

            return RedirectToAction("List");
        }
    }
}
