using System;
using System.Collections.Generic;
using System.Linq;
using Extric.Towbook.Integration;
using QBI = Extric.Towbook.Integrations.Quickbooks;
using Extric.Towbook.WebShared;
using Extric.Towbook.Utility;
using Extric.Towbook.Integrations.Quickbooks.Model;
using Newtonsoft.Json;
using System.Net;
using System.IO;
using Stripe;
using System.Configuration;
using Extric.Towbook;
using Microsoft.AspNetCore.Mvc;

namespace Ajax.Areas.Settings.Controllers
{
    [Area("Settings")]
    public class StripeController : Controller
    {

        //
        // GET: /Settings/Stripe/

        private string FormatDate(DateTime? date, string nullText = "Never")
        {
            if (date.HasValue)
                return date.Value.ToShortDateString() + " " + date.Value.ToShortTowbookTimeString();
            else
                return nullText;
        }

        [HttpGet]
        public ActionResult Index(string scope, string code, string error, string description)
        {
            if (WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.Manager)
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            User user = WebGlobal.CurrentUser;
            var key = $"stripe:auth:{WebGlobal.CurrentUser.CompanyId}:{code}";

            ViewBag.Error = error;
            ViewBag.Description = description;
            ViewBag.HasError = !string.IsNullOrEmpty(error);

            Response.Headers.Add("X-Twbk-Title", "Stripe Connect Setup");

            if (Core.GetRedisValue(key) == null)
            {
                if (string.IsNullOrEmpty(scope) && string.IsNullOrEmpty(code))
                {
                    var connectSession = Extric.Towbook.Stripe.ConnectSession.GetByCompanyId(user.CompanyId);
                    if (connectSession != null)
                    {
                        ViewBag.Connected = true;
                        ViewBag.StripeUserId = connectSession.StripeUserId;
                        ViewBag.CreateDate = WebGlobal.OffsetDateTime(connectSession.CreateDate).ToShortDateString();

                        var u = Extric.Towbook.User.GetById(connectSession.OwnerUserId);
                        ViewBag.OwnerUser = u.FullName;
                    }
                    else
                    {
                        ViewBag.Connected = false;
                    }
                }
                else
                {
                    try
                    {
                        var secretKey = Core.GetAppSetting("Stripe:SecretKey");
                        var stripeOAuthTokenService = new OAuthTokenService(new StripeClient(apiKey: secretKey));
                        var stripeOAuthTokenCreateOptions = new OAuthTokenCreateOptions()
                        {
                            ClientSecret = secretKey,
                            Code = code,
                            GrantType = "authorization_code"
                        };

                        OAuthToken stripeOAuthToken = stripeOAuthTokenService.Create(stripeOAuthTokenCreateOptions);

                        var connectSession = new Extric.Towbook.Stripe.ConnectSession()
                        {
                            CompanyId = user.CompanyId,
                            AccountId = null,
                            StripeUserId = stripeOAuthToken.StripeUserId,
                            AccessToken = stripeOAuthToken.AccessToken,
                            PublishableKey = stripeOAuthToken.StripePublishableKey,
                            RefreshToken = stripeOAuthToken.RefreshToken
                        };

                        connectSession.Save(user);

                        ViewBag.Connected = true;
                        ViewBag.StripeUserId = stripeOAuthToken.StripeUserId;
                        ViewBag.CreateDate = WebGlobal.OffsetDateTime(connectSession.CreateDate).ToShortDateString();
                        ViewBag.OwnerUser = user.FullName;

                        Core.SetRedisValue(key, code, TimeSpan.FromMinutes(15));
                    }
                    catch (StripeException e)
                    {
                        Response.StatusCode = (int)System.Net.HttpStatusCode.InternalServerError;
                        //Response.StatusDescription = e.StripeError.Error + " " + e.StripeError.ErrorDescription + " ";

                        throw new TowbookException("Stripe Error", e);
                    }
                }
            }

            return View("Stripe", user.Company);
        }

        [HttpGet]
        public ActionResult Revoke()
        {
            if (WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.Manager)
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            Extric.Towbook.User user = WebGlobal.CurrentUser;

            var connectSession = Extric.Towbook.Stripe.ConnectSession.GetByCompanyId(user.CompanyId);
            if (connectSession != null)
            {
                var key = Core.GetAppSetting("Stripe:SecretKey");
                var clientId = Core.GetAppSetting("Stripe:ClientId");
                var stripeOAuthTokenService = new OAuthTokenService(new StripeClient(apiKey: key));

                try
                {
                    var response = stripeOAuthTokenService.Deauthorize(new OAuthDeauthorizeOptions() { ClientId = clientId, StripeUserId = connectSession.StripeUserId });

                    connectSession.Revoke(user);
                }
                catch (StripeException e)
                {
                    Response.StatusCode = (int)System.Net.HttpStatusCode.InternalServerError;
                    //Response.StatusDescription = e.StripeError.Error + " " + e.StripeError.ErrorDescription + " ";

                    throw new TowbookException("Stripe Error", e);
                }
            }

            return Index("", "", "", "");
        }
    }

}
