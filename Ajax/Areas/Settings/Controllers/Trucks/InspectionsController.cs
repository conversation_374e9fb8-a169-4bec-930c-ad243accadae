using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using Extric.Towbook;
using Extric.Towbook.API.PreTripInspections.Models;
using Extric.Towbook.Integration;
using Extric.Towbook.PreTripInspections;
using Extric.Towbook.Utility;
using Extric.Towbook.WebShared;
using Microsoft.AspNetCore.Mvc;

namespace Ajax.Areas.Settings.Controllers.Trucks
{
    [Area("Settings")]
    public class InspectionsController : Controller
    {
        // GET: Settings/Inspection
        public async Task<IActionResult> Index()
        {
            if (WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.Manager)
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            Response.Headers.Add("X-Twbk-Title", "Equipment Inspections");

            List<Truck> trucks = new List<Truck>();

            var userCompanies = await WebGlobal.GetCompaniesAsync();
            foreach (var company in userCompanies)
                trucks.AddRange(await Truck.GetByCompanyAsync(company));


            var pretripsItems = PreTripInspectionItemType.GetAllItemsByCompany(userCompanies.Select(s => s.Id).ToArray(), WebGlobal.CurrentUser.PrimaryCompanyId)
                                        .Select(i => PreTripInspectionItemTypeModel.Map(i)).ToCollection();

            var truckTypes = EnumExtensionMethods.Enum<Truck.TruckType>.GetAllValuesAsIEnumerable()
                                .Select(d => new EnumExtensionMethods.EnumTypeDefinitionModel(d))
                                .ToCollection();

            var itemTypeDetails = PreTripInspectionItemTypeDetail.GetByCompanyIds(userCompanies.Select(s => s.Id).ToArray());

            var customInspections = itemTypeDetails.Select(s => new PreTripInspectionItemTypeModel()
                                        {
                                            Id = -1,
                                            Name = s.Name,
                                            DetailId = s.Id,
                                            TruckType = (int)Truck.TruckType.Unspecified
            });

            foreach (var item in pretripsItems)
                item.DetailId = itemTypeDetails.FirstOrDefault(w => w.Id == item.DetailId)?.Id;

            ViewBag.TruckTypes = truckTypes
                                    .Where(w => trucks.Select(a => (int)a.Type).Contains(w.Id) &&
                                                !pretripsItems.Any(b => b.TruckType == w.Id))
                                    .ToCollection();

            ViewBag.Inspections = pretripsItems
                                    .Where(p => trucks.Select(a => (int)a.Type).Contains(p.TruckType) || (int)p.TruckType == 0 /* unspecified */)
                                    .GroupBy(g => g.TruckType)
                                    .Select(s =>
                                    {
                                        var truck = truckTypes.FirstOrDefault(f => f.Id == s.Key);
                                        var item = pretripsItems.FirstOrDefault(f => f.Id == s.Key);
                                        
                                        var name = truck?.Name ?? "Truck";
                                        if (s.Key == 0)
                                            name = "Standard Pre/Post Trip";

                                        name += " Inspections";

                                        return new PreTripInspectionItemTypeModel()
                                        {
                                            Id = s.Key,
                                            Name = name,
                                            DetailId = -1,
                                            TruckType = item?.TruckType ?? truck?.Id ?? 0
                                        };
                                    })
                                    .Union(customInspections)
                                    .OrderByDescending(o => o.Name.StartsWith("Standard Pre/Post Trip"))
                                    .ThenBy(b => b.Name);

            ViewBag.Details = itemTypeDetails
                .Select(s => new PreTripInspectionItemTypeDetailModel() 
                { 
                    DetailId = s.Id,
                    Name = s.Name
                });

            ViewBag.PreTripInspectionsFF = await WebGlobal.CurrentUser.Company.HasFeatureAsync(Extric.Towbook.Generated.Features.PreTripInspections);
            var doNotDefaultInspectionItemsToPass =
                    WebGlobal.CurrentUser.Company.HasAccessToBaseFeature("inspectionDefault") &&
                    CompanyKeyValue.GetFirstValueOrNull(WebGlobal.CurrentUser.CompanyId, Provider.Towbook.ProviderId, "DoNotDefaultInspectionItemsToPass") == "1";
            ViewBag.doNotDefaultInspectionItemsToPass = ViewBag.PreTripInspectionsFF ? doNotDefaultInspectionItemsToPass : false;

            return View("../Trucks/InspectionList");
        }

        [HttpGet]
        public async Task<ViewResult> Details(int? truckTypeId, int? detailId = null)
        {
            if (WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.Manager)
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            var inspectionTitle = truckTypeId.GetValueOrDefault() > 0 ? 
                new EnumExtensionMethods.EnumTypeDefinitionModel((Truck.TruckType)truckTypeId.Value).Name + " Inspection Questions" : 
                "Standard Equipment Inspection Questions";

            var companyIds = (await WebGlobal.GetCompaniesAsync()).Select(s => s.Id).ToArray();
            Collection<PreTripInspectionItemType> items = new Collection<PreTripInspectionItemType>();

            this.ViewBag.DetailId = 0;
            var categories = PreTripInspectionItemCategory.GetByCompany(companyIds)
                                        .Select(c => PreTripInspectionItemCategoryModel.Map(c)).ToCollection();

            // custom inspection (truckless)
            if (detailId > 0)
            {
                var detail = await PreTripInspectionItemTypeDetail.GetByIdAsync(detailId.Value);
                if (detail == null || !companyIds.Contains(detail.CompanyId))
                    throw new TowbookException("You don't have access to perform this action.");

                items = (await PreTripInspectionItemType.GetByItemTypeDetailAsync(WebGlobal.CurrentUser.PrimaryCompanyId, detailId.Value))
                    .ToCollection();

                inspectionTitle = detail.Name + " Inspection Questions";

                this.ViewBag.DetailId = detail.Id;
                categories = categories
                        .Where(w => w.Id == 8 /* Inspection Items */)
                        .ToCollection();
            }
            // by truck type
            else if (truckTypeId > 0)
            {
                items = PreTripInspectionItemType.GetOrCreateByTruckType(
                            companyIds,
                            WebGlobal.CurrentUser.PrimaryCompanyId,
                            truckTypeId.GetValueOrDefault())
                    .Where(w => w.PreTripInspectionItemTypeDetailId == null)
                    .ToCollection();

                var truckTypeInclusionCategories = PreTripInspectionItemCategoryInclusion.GetByTruckTypeIds(new[] { truckTypeId.Value });

                categories = categories
                    .Where(w => truckTypeInclusionCategories.Any(a => a.CategoryId == w.Id))
                    .ToCollection();
            }
            // standard
            else
            {
                items = PreTripInspectionItemType.GetByCompany(
                        companyIds, 
                        WebGlobal.CurrentUser.PrimaryCompanyId, 
                        null)
                    .Where(w => w.PreTripInspectionItemTypeDetailId == null)
                    .ToCollection();
            }

            Response.Headers.Add("X-Twbk-Title", inspectionTitle);

            this.ViewBag.InspectionTitle = inspectionTitle;
            this.ViewBag.Items = items
                                    .Select(i => PreTripInspectionItemTypeModel.Map(i))
                                    .ToCollection();

            this.ViewBag.Categories = categories;
            this.ViewBag.TruckTypeId = truckTypeId.GetValueOrDefault();
            

            return View("../Trucks/Inspection");
        }
    }
}
