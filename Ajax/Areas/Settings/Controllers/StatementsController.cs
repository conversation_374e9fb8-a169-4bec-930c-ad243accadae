using Extric.Towbook.API.Models;
using Extric.Towbook.Company;
using Extric.Towbook.WebShared;
using System;
using System.Collections.Generic;
using System.Linq;
using Extric.Towbook.Utility;
using Extric.Towbook;
using System.Collections.ObjectModel;
using Extric.Towbook.Accounts;
using Microsoft.AspNetCore.Mvc;

namespace Ajax.Areas.Settings.Controllers
{
    [Area("Settings")]
    public class StatementsController : Controller
    {
        public ViewResult Index()
        {
            return List();
        }

        [HttpGet]
        public ViewResult List()
        {
            if (WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.Manager)
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            Response.Headers.Add("X-Twbk-Title", "Statement Settings");

            var d = StatementDisclaimer.GetByCompanyId(WebGlobal.CurrentUser.CompanyId, null);
            var eo = StatementEmailOption.GetByCompanyId(WebGlobal.CurrentUser.CompanyId);

            var model = StatementOptionModel.Map(StatementOption.GetByCompanyId(WebGlobal.CurrentUser.CompanyId), d?.Disclaimer, eo);

            ViewBag.DueDateOptionsJson = EnumExtensionMethods.Enum<DueDateDefaultType>.GetAllValuesAsIEnumerable()
                .Where(w => w != DueDateDefaultType.Unspecified)
                .Select(s => new EnumExtensionMethods.EnumTypeDefinitionModel(s))
                .ToJson();

            ViewBag.DefaultDisclaimer = $"{WebGlobal.CurrentUser.Company.Name} appreciates your business. If you have any questions regarding this statement, please contact us at {WebGlobal.CurrentUser.Company.Phone}.";

            return View("StatementsOptions", model);
        }
    }
}
