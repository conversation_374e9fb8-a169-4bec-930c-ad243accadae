using System;
using System.Collections.Generic;
using System.Linq;
using Extric.Towbook.Integration;
using QBI = Extric.Towbook.Integrations.Quickbooks;
using Extric.Towbook.WebShared;
using Extric.Towbook.Utility;
using Extric.Towbook.Integrations.Quickbooks.Model;
using Extric.Towbook.Integrations.Quickbooks;
using Extric.Towbook.API.Models.Calls;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Primitives;
using Extric.Towbook;
using System.Threading.Tasks;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json;

namespace Ajax.Areas.Settings.Controllers;

[Area("Settings")]
public class QuickbooksController : Controller
{
    //
    // GET: /Settings/Quickbooks/

    private string FormatDate(DateTime? date, string nullText = "Never")
    {
        if (date.HasValue)
            return date.Value.ToShortDate() + " " + date.Value.ToShortTowbookTimeString();
        else
            return nullText;
    }

    [HttpGet]
    public async Task<ActionResult> Index()
    {
        if (WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.Manager &&
            WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.Accountant)
            throw new TowbookException("Your user account doesn't have access to perform this action.");

        Extric.Towbook.User user = WebGlobal.CurrentUser;
        ViewBag.QuickBooksConnectorType = "None";

        if (await user.Company.IsConnectedToAccountingProviderAsync())
        {
            var ags = await Extric.Towbook.Agent.Session.GetByCompanyIdAsync(WebGlobal.CurrentUser.CompanyId);
            if (ags != null)
            {
                var qss = Extric.Towbook.Agent.Sync.QuickbooksSyncService.Get(ags.Id);
                var ds = new Extric.Towbook.Agent.QuickBooks.DataService();

                var accounts = ds.GetAccounts(ags.Id);

                ViewBag.RevenueAccounts = accounts.Where(o => o.Type == 8 || o.Type == 18)
                    .Select(o => new { Name = o.Name, Id = o.FullName })
                    .OrderBy(o => o.Name)
                    .Distinct()
                    .ToCollection();

                if (WebGlobal.CurrentUser.Company.Country == Extric.Towbook.Company.Company.CompanyCountry.USA)
                {
                    ViewBag.Classes = ds.GetClasses(ags.Id);
                }

                ViewBag.QuickBooksConnectorType = "QuickBooks Desktop";
                ViewBag.IncomeAccountId = CompanyKeyValue.GetFirstValueOrNull(WebGlobal.CurrentUser.CompanyId,
                    Provider.QuickBooks.ProviderId, "AgentDefaultRevenueAccountId");

                Response.Headers["X-Twbk-Title"] = "QuickBooks Desktop Configuration";
            }
            else
            {
                bool valid = true;

                if (await QuickbooksUtility.VerifyConnector(WebGlobal.CurrentUser.CompanyId))
                {
                    var qbc = await QuickbooksUtility.GetConnector(WebGlobal.CurrentUser.CompanyId);

                    if (qbc != null)
                    {
                        int statusCode = 0;

                        string response = WebGlobal.GetResponseFromUrl(
                            $"/api/integration/accounting/providers/quickbooks/accounts", out statusCode, true, false);

                        if (statusCode != 200)
                        {
                            throw new Exception("statusCode: " + statusCode + "... " +
                                response);
                        }

                        ViewBag.RevenueAccounts = JsonConvert.DeserializeObject<IEnumerable<Account>>(response)
                            .Where(o =>
                                o.Type == TbAccountTypeEnum.Income ||
                                o.Type == TbAccountTypeEnum.OtherIncome)
                            .ToList();

                        if (ViewBag.RevenueAccounts == null)
                            valid = false;

                        ViewBag.Classes = qbc.GetClasses();
                        ViewBag.IncomeAccountId = CompanyKeyValue.GetFirstValueOrNull(WebGlobal.CurrentUser.CompanyId,
                           Provider.QuickBooks.ProviderId, "IncomeAccountRef");
                        ViewBag.QuickBooksConnectorType = "QuickBooks Online";

                        ViewBag.TaxCodes = qbc.GetSalesTaxCodes();
                        ViewBag.TaxAgencies = (qbc as QuickBooksOnlineConnector).GetNativeTaxAgencies();

                        Response.Headers["X-Twbk-Title"] = "QuickBooks Online Configuration";
                    }
                    else
                    {
                        valid = false;
                    }
                }
                else
                {
                    valid = false;

                }

                if (!valid)
                {
                    var keys = CompanyKey.GetByProviderId(Provider.QuickBooks.ProviderId)
                        .Where(o => new[] { "AccessToken", "RefreshToken", "RefreshTokenExpires",
                            "RealmId", "DataSource" }.Contains(o.Name)).Select(o => o.Id);

                    var ckv = CompanyKeyValue.GetByCompany(WebGlobal.CurrentUser.CompanyId,
                        Provider.QuickBooks.ProviderId)
                        .Where(o => keys.Contains(o.KeyId));

                    foreach (var v in ckv)
                    {
                        if (keys.Where(o => o == v.KeyId).Any())
                            v.Delete();
                    }

                    Response.Headers["X-Twbk-Title"] = "QuickBooks Setup";
                }
            }
        }
        else
        {
            Response.Headers["X-Twbk-Title"] = "QuickBooks Setup";
        }


        #region Integration Statistics / Status

        // Invoices

        ViewData.Add("LastSuccessInvoiceDateTime", FormatDate(ProcessQueueHelper.GetLastSuccessInvoicePost(user.CompanyId)));
        ViewData.Add("SuccessInvoicePostCount", ProcessQueueHelper.GetSuccessInvoiceCount(user.CompanyId));

        // Payments
        ViewData.Add("LastSuccessPaymentDateTime", FormatDate(ProcessQueueHelper.GetLasSuccessPaymentPost(user.CompanyId)));
        ViewData.Add("SuccessPaymentPostCount", ProcessQueueHelper.GetSuccessPaymentCount(user.CompanyId));

        // RateItem
        var companyRateItemSendDate = CompanyKeyValue.GetFirstValueOrNull(user.CompanyId, Provider.QuickBooks.ProviderId, "ItemRegistrationDateTime");
        ViewData.Add("LastRateItemRegistrationDateTime", companyRateItemSendDate ?? "Never");


        // Accounts
        var accountsCount = ProcessQueueHelper.GetLinkedAndUnlinkedAccountsCount(user.CompanyId);

        foreach (var k in accountsCount.Keys)
        {
            ViewData.Add(k, accountsCount[k]);
        }

        #endregion

        ViewBag.TaxableTaxCodeId = (string)(CompanyKeyValue.GetFirstValueOrNull(user.CompanyId, Provider.QuickBooks.ProviderId, "TaxableTaxCodeId") ?? "");
        ViewBag.NonTaxableTaxCodeId = (string)(CompanyKeyValue.GetFirstValueOrNull(user.CompanyId, Provider.QuickBooks.ProviderId, "NonTaxableTaxCodeId") ?? "");
        ViewBag.DefaultTaxAgencyId = (string)(CompanyKeyValue.GetFirstValueOrNull(user.CompanyId, Provider.QuickBooks.ProviderId, "DefaultTaxAgency") ?? "");

        return View("Quickbooks", user.Company);
    }


    [HttpPost]
    public async Task<ActionResult> Index(IFormCollection collection)
    {
        if (WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.Manager &&
            WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.Accountant)
            throw new TowbookException("Your user account doesn't have access to perform this action.");

        const string allowAccountlessInvoicesKeyName = "AllowAccountlessInvoices";
        var companyId = WebGlobal.CurrentUser.CompanyId;

        var allowAccountlessInvoicesKey = Provider.QuickBooks.GetKey(KeyType.Company, allowAccountlessInvoicesKeyName);

        var allowAccountlessInvoices = CompanyKeyValue
            .GetByCompany(companyId, Provider.QuickBooks.ProviderId, allowAccountlessInvoicesKeyName)
            .FirstOrDefault();

        var scheduleOption = ScheduleConfigurationHelper.ScheduleOption.Manual;

        // Obtain the settings parameter
        var allowValue = collection[allowAccountlessInvoicesKeyName].Contains("true");

        if (!Enum.TryParse(collection["AccoutingProcessSchedule"], true, out scheduleOption))
        {
            return await Index();
        }

        ScheduleConfigurationHelper.Save(companyId, scheduleOption);

        var ags = await Extric.Towbook.Agent.Session.GetByCompanyIdAsync(WebGlobal.CurrentUser.CompanyId);

        if (ags != null)
        {
            #region Default Accounts - TB Agent
            if (!StringValues.IsNullOrEmpty(collection["IncomeAccountRef"]))
            {
                var agentIncomeAccount = Provider.QuickBooks.GetKey(KeyType.Company, "AgentDefaultRevenueAccountId");

                var aiaValue = (await CompanyKeyValue.GetByCompanyIdAsync(WebGlobal.CurrentUser.CompanyId,
                    Provider.QuickBooks.ProviderId,
                    "AgentDefaultRevenueAccountId")).FirstOrDefault() ?? new CompanyKeyValue(WebGlobal.CurrentUser.CompanyId, agentIncomeAccount.Id, "");

                aiaValue.Value = collection["IncomeAccountRef"];
                aiaValue.Save();
            }
            #endregion

            allowAccountlessInvoices = allowAccountlessInvoices ?? new CompanyKeyValue(companyId, allowAccountlessInvoicesKey.Id, "");

            allowAccountlessInvoices.Value = allowValue.ToString();
            allowAccountlessInvoices.Save();

            return Redirect("/settings?u=/ajax/Settings/Quickbooks");
        }
        else
        {
            #region Default Accounts - QBOnline
            var incomeAccountRefK = Provider.QuickBooks.GetKey(KeyType.Company, "IncomeAccountRef");
            var incomeAccountRefKV = CompanyKeyValue.GetByCompany(companyId).Where(w => w.KeyId == incomeAccountRefK.Id).FirstOrDefault();

            if (incomeAccountRefKV == null)
            {
                incomeAccountRefKV = new CompanyKeyValue(companyId, incomeAccountRefK.Id, "");
            }

            if (collection.ContainsKey("IncomeAccountRef"))
            {
                incomeAccountRefKV.Value = collection["IncomeAccountRef"];
                incomeAccountRefKV.Save();
            }



            if (WebGlobal.CurrentUser.Company.Country == Extric.Towbook.Company.Company.CompanyCountry.Canada)
            {
                if (collection.ContainsKey("TaxableTaxCodeId"))
                {
                    var ckv = await CompanyKeyValue.GetFirstValueOrNewAsync(companyId, Provider.QuickBooks.ProviderId, "TaxableTaxCodeId");
                    ckv.Value = collection["TaxableTaxCodeId"];
                    ckv.Save();
                }

                if (collection.ContainsKey("NonTaxableTaxCodeId"))
                {
                    var ckv = await CompanyKeyValue.GetFirstValueOrNewAsync(companyId, Provider.QuickBooks.ProviderId, "NonTaxableTaxCodeId");
                    ckv.Value = collection["NonTaxableTaxCodeId"];
                    ckv.Save();
                }

                if (collection.ContainsKey("DefaultTaxAgencyId"))
                {
                    var ckv = await CompanyKeyValue.GetFirstValueOrNewAsync(companyId, Provider.QuickBooks.ProviderId, "DefaultTaxAgency");
                    ckv.Value = collection["DefaultTaxAgencyId"];
                    ckv.Save();
                }
            }

            #endregion
        }


        #region Default Item Class
        var defaultClassId = CompanyKeyValue.GetByCompany(companyId, Provider.QuickBooks.ProviderId, "DefaultClass").FirstOrDefault();

        if (defaultClassId == null)
        {
            defaultClassId = new CompanyKeyValue(companyId,
                Provider.QuickBooks.GetKey(KeyType.Company, "DefaultClass").Id,
                "");
        }

        if (collection.ContainsKey("DefaultClass"))
        {
            defaultClassId.Value = collection["DefaultClass"];
            defaultClassId.Save();
        }

        #endregion

        #region Allow Accountless Invoices
        const string customCustomerKeyName = "CustomAccountCustomer";


        //Get the current configuration Key
        //Get the current configuration Value
        if (allowAccountlessInvoices == null)
        {
            allowAccountlessInvoices = new CompanyKeyValue(companyId, allowAccountlessInvoicesKey.Id, "");

            //Check if there's a custom Customer to use for Invoices that don't have an Account associated
            var customCustomerK = Provider.QuickBooks.GetKey(KeyType.Company, customCustomerKeyName);
            var customAccountCustomer = CompanyKeyValue.GetByCompany(companyId, Provider.QuickBooks.ProviderId, customCustomerKeyName).FirstOrDefault();

            if (customAccountCustomer == null)
            {
                //The Custom value doesn't exist, register the Custom customer in QB and the resulting Id in the KeyValue store
                QBI.Model.Customer customer = new QBI.Model.Customer()
                {
                    Id = null,
                    FirstName = "Towbook-Retail",
                    LastName = "Customers",
                    OpenBalanceAmount = 0
                };

                //Save the customer
                var resultCustomer = await QBI.CustomerHelper.AddCustomerAsync(WebGlobal.CurrentUser.CompanyId, customer);

                //Register the result in the KeyValue store
                customAccountCustomer = new CompanyKeyValue()
                {
                    CompanyId = companyId,
                    KeyId = customCustomerK.Id,
                    Value = resultCustomer.Id
                };

                customAccountCustomer.Save();
            }
        }

        //Update the configurations Value, according to the settings parameter
        #endregion


        return Redirect("/settings?u=/ajax/Settings/Quickbooks");
    }

    [HttpGet]
    public ActionResult AccountMatcher()
    {
        if (WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.Manager &&
            WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.Accountant)
            throw new TowbookException("Your user account doesn't have access to perform this action.");

        Response.Headers["X-Twbk-Title"] = "Quickbooks Setup: Associate Customers with Towbook";
        return View();
    }
}
