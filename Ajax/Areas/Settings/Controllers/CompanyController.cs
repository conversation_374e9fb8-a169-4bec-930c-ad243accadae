using System;
using System.Linq;
using Ajax.Areas.Settings.Models;
using Extric.Towbook.WebShared;
using c = Extric.Towbook.Company;
using Extric.Towbook.API.Models;
using Extric.Towbook.Licenses;
using Extric.Towbook.Utility;
using Extric.Towbook.Web;
using Extric.Towbook;
using System.IO;
using System.Text;
using Extric.Towbook.Integration;
using Extric.Towbook.Company;
using Ajax.Filters;

using Async = System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace Ajax.Areas.Settings.Controllers
{
    [Area("Settings")]
    public class CompanyController : Controller
    {
        public static readonly string[] ReservedSubdomains = 
            { "api", "www", "app", "smtp", "get", "post", "apps", "auth", "admin", "http", "https" ,
              "blog", "beta", "dev", "whois", "mail", "user", "url", "update", "test", "ssl", 
              "sql", "sms", "session", "script", "root", "redirect", "query", "public", "proxy", 
              "private", "portal", "pop", "ping", "php", "payment", "oauth", "ns", "null", "news", 
              "mx", "master", "map", "maps", "mail", "log", "legal", "job", "jobs", "info", "html",
              "host", "git", "github", "ftp", "form", "file", "files", "download", "dist", "dev",
              "porn", "fuck", "shit", "ass", "booty", "penis", "vagina", "oral", "sex" };

        //
        // GET: /Company/Company/

        public PartialViewResult CanadaSpecfiic()
        {
            return PartialView("CanadaSpecific", 
                Extric.Towbook.Company.Countries.Canada.GetByCompany(WebGlobal.CurrentUser.CompanyId));
        }

        [HttpPost]
        public PartialViewResult CanadaSpecfiic(Extric.Towbook.Company.Countries.Canada c)
        {
            c.Save();
            return CanadaSpecfiic();
        }

        [HttpGet]
        public async Task<IActionResult> Index()
        {
            if (WebGlobal.CurrentUser.Type == Extric.Towbook.User.TypeEnum.Accountant && WebGlobal.CurrentUser.CanAccessClosedPeriodSettings())
                return RedirectToAction("Index", "ClosedPeriod/");

            if (WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.Manager)
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            Response.Headers.Add("X-Twbk-Title", "Company Profile");

            ViewBag.Timezones = Timezone.Timezones;

            //Response.Cache.SetNoStore();
            
            var companyLicenseKeysFiltered = CompanyLicenseKey.GetByCompany(WebGlobal.CurrentUser.Company);
            var allCompanyLicenseKeyValues = CompanyLicenseKeyValue.GetByCompanyId(WebGlobal.CurrentUser.CompanyId);
            var licenseData = CompanyLicenseValues.GetAllMatchedKeyValues(companyLicenseKeysFiltered, allCompanyLicenseKeyValues);
            var vehicleLookup = new VehicleLookupModel();

            if (await WebGlobal.CurrentUser.Company.HasFeatureAsync(Extric.Towbook.Generated.Features.VehicleLookup))
            {
                var hostnames = WebsiteHostname.GetByCompanyId(WebGlobal.CurrentUser.CompanyId).ToCollection();
                string suggestedHostname = $"{WebGlobal.CurrentUser.CompanyId}";

                var x = SqlMapper.QuerySP<dynamic>("internalGetPrimaryEmailByCompanyId", new { WebGlobal.CurrentUser.CompanyId }).FirstOrDefault();
                if (x != null && !string.IsNullOrEmpty(x.EmailAddress))
                    suggestedHostname = x.EmailAddress.Replace("@towbook.net", "");

                if (hostnames.Count() == 0)
                    hostnames.Add(new WebsiteHostname() { Hostname = string.Empty, RedirectToHostnameId = null });

                var website = Website.GetById(hostnames.FirstOrDefault()?.WebsiteId ?? 0);

                var notice = CompanyKeyValue.GetFirstValueOrNull(WebGlobal.CurrentUser.CompanyId, Provider.Towbook.ProviderId, "VehicleLookupGeneralNotice");

                var includePrice = CompanyKeyValue.GetFirstValueOrNull(WebGlobal.CurrentUser.CompanyId, Provider.Towbook.ProviderId, "VehicleLookupIncludePrice") == "1";

                vehicleLookup = new VehicleLookupModel()
                {
                    Id = website?.WebsiteId ?? 0,
                    Enabled = website != null && website.IsDeleted.GetValueOrDefault() == false,
                    IncludePrice = includePrice,
                    Hostnames = hostnames?.Take(1).Select(s => HostnameModel.Map(s)).ToCollection(),
                    SuggestedHostname = suggestedHostname,
                    Notes = notice
                };
            }

            var model = new ValidatingCompanySettingsModel()
            {
                Company = CompanyModel.Map(WebGlobal.CurrentUser.Company),
                Licenses = licenseData,
                VehicleLookup = vehicleLookup
            };

            var sharedCompany = SharedCompany.GetByCompanyId(WebGlobal.CurrentUser.CompanyId).FirstOrDefault(company => company.SharedCompanyId == WebGlobal.CurrentUser.CompanyId);
            if (sharedCompany != null)
            {
                model.IsSharedCompany = true;
                model.Nickname = sharedCompany.Nickname;
            }

            bool? overrideTimezoneUseDST = null;
            if (model.Company.Country == Company.CompanyCountry.Australia.ToString())
            {
                if (model.Company.State != null && model.Company.State.ToLowerInvariant().StartsWith("vic"))
                {
                    var tz = System.TimeZoneInfo.FindSystemTimeZoneById("AUS Eastern Standard Time");
                    overrideTimezoneUseDST = tz.IsDaylightSavingTime(System.DateTime.Now);
                }
            }

            model.Company.TimezoneOffset = Timezone.Timezones.Select(s =>
            {
                if (overrideTimezoneUseDST != null)
                    s.DaylightSavings = overrideTimezoneUseDST.Value;

                return s;
            }).FirstOrDefault(o =>
                o.Offset == model.Company.TimezoneOffset &&
                o.DaylightSavings == WebGlobal.CurrentUser.Company.TimezoneUseDST)?.Id ?? 5; // default to eastern time zone.
            
            return View("Company", model);
        }

        public ActionResult News()
        {
            if (WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.Manager)
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            Response.Headers.Add("X-Twbk-Title", "Company News/Announcements");

            var model = new CompanySettingsModel()
            {
                Company = WebGlobal.CurrentUser.Company
            };

            return View("News", model);
        }


        [HttpPost]
        public async Async.Task<ActionResult> News(Ajax.Areas.Settings.Models.CompanySettingsModel model)
        {
            if (WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.Manager)
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            var realCompany = await Company.GetByIdAsync(WebGlobal.CurrentUser.CompanyId);

            realCompany.News = model.Company.News;

            await realCompany.Save();

            return Redirect("/settings/");
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task UpdateCompanyLicenseValues([FromBody] ValidatingCompanySettingsModel postCompanyLicense)
        {
            if (WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.Manager)
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            if (!ModelState.IsValid)
                return;
            
            var sharedCompany = SharedCompany.GetByCompanyId(WebGlobal.CurrentUser.CompanyId).FirstOrDefault(company => company.SharedCompanyId == WebGlobal.CurrentUser.CompanyId);
            if (sharedCompany != null && postCompanyLicense.IsSharedCompany)
            {
                sharedCompany.Nickname = postCompanyLicense.Nickname;
                await sharedCompany.Save();
            }

            var  realCompany = await Company.GetByIdAsync(WebGlobal.CurrentUser.CompanyId);

            realCompany.Name = postCompanyLicense.Company.Name;
            realCompany.Address = postCompanyLicense.Company.Address;
            realCompany.City = postCompanyLicense.Company.City;
            realCompany.State = postCompanyLicense.Company.State;
            realCompany.Zip = postCompanyLicense.Company.Zip;

            var zone = Timezone.Timezones.FirstOrDefault(o => o.Id == postCompanyLicense.Company.TimezoneOffset);
            if (zone != null)
            {
                realCompany.TimezoneOffset = zone.Offset;
                realCompany.TimezoneUseDST = zone.DaylightSavings;
            }

            Enum.TryParse(postCompanyLicense.Company.Country, out Company.CompanyCountry parsedCountry);
            realCompany.Country = parsedCountry;
                
            realCompany.Email = postCompanyLicense.Company.Email;
            realCompany.Fax = postCompanyLicense.Company.Fax;
            realCompany.Phone = postCompanyLicense.Company.Phone;

            // Try to resolve the address waypoints
            var address = realCompany.GetComposedAddress();
    
            var location = await GeocodeWithGoogle(address);
            
            if (location != null)
            {
                realCompany.Latitude = location.Latitude;
                realCompany.Longitude = location.Longitude;
            }

            await realCompany.Save();

            #region Licenses Save to database

            if (postCompanyLicense.Licenses != null)
            {
                foreach (var x in postCompanyLicense.Licenses)
                {
                    CompanyLicenseKeyValue clkv = new CompanyLicenseKeyValue();

                    if (x.KeyId > 0)
                    {
                        var c = CompanyLicenseKeyValue.GetByCompanyId(WebGlobal.CurrentUser.CompanyId, x.KeyId);

                        if (c != null)
                            clkv = c;
                    }

                    clkv.CompanyId = realCompany.Id;
                    clkv.KeyId = x.KeyId;
                    clkv.Value = x.Value;

                    if (string.IsNullOrWhiteSpace(clkv.Value))
                        clkv.Delete(WebGlobal.CurrentUser);
                    else
                        clkv.Save(WebGlobal.CurrentUser);
                }
            }
            #endregion

            #region Website Lookup Feature
            if(await WebGlobal.CurrentUser.Company.HasFeatureAsync(Extric.Towbook.Generated.Features.VehicleLookup) && 
               postCompanyLicense.VehicleLookup != null)
            {
                var hostname = postCompanyLicense.VehicleLookup.Hostnames.FirstOrDefault();
                if (hostname?.Hostname != null) {

                    hostname.Hostname = hostname.Hostname.ToLowerInvariant().Trim();
                        
                    var sub = hostname.Hostname.Split('.')[0];
                    if (string.IsNullOrEmpty(sub) || ReservedSubdomains.Where(w => w.StartsWith(sub)).Any())
                        throw new TowbookException($"The hostname is invalid.");

                    var website = Website.GetById(hostname.WebsiteId) 
                                  ?? new Website() { CompanyId = WebGlobal.CurrentUser.CompanyId };

                    if (postCompanyLicense.VehicleLookup.Enabled)
                        website.IsDeleted = null;
                    else
                        website.IsDeleted = true;

                    website.Save();

                    if (postCompanyLicense.VehicleLookup.Enabled)
                    {
                        var websiteHostname = WebsiteHostname.GetById(hostname.Id) ??
                                              new WebsiteHostname() { WebsiteId = website.WebsiteId };

                        if (hostname.RedirectToHostnameId.GetValueOrDefault() == 0)
                            websiteHostname.RedirectToHostnameId = null;

                        if (hostname.Hostname.Contains(".towbook.net"))
                            websiteHostname.Hostname = hostname.Hostname;
                        else
                            websiteHostname.Hostname = hostname.Hostname + ".towbook.net";

                        websiteHostname.Save();
                    }
                }
                
                var ckvNotice = await CompanyKeyValue.GetFirstValueOrNewAsync(WebGlobal.CurrentUser.CompanyId, Provider.Towbook.ProviderId, "VehicleLookupGeneralNotice");
                if (!string.IsNullOrEmpty(postCompanyLicense.VehicleLookup.Notes))
                {
                    ckvNotice.Value = Core.HtmlEncode(postCompanyLicense.VehicleLookup.Notes);
                    ckvNotice.Save();
                }
                else if (ckvNotice.Id > 0)
                {
                    ckvNotice.Delete();
                }

                var ckvPrice = await CompanyKeyValue.GetFirstValueOrNewAsync(WebGlobal.CurrentUser.CompanyId, Provider.Towbook.ProviderId, "VehicleLookupIncludePrice");
                ckvPrice.Value = postCompanyLicense.VehicleLookup.IncludePrice ? "1" : "0";
                ckvPrice.Save();
            }
            #endregion

        }


        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> RefreshLicenseValues(c.Company comp)
        {
            if (WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.Manager)
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            var companyLicenseKeysFiltered = CompanyLicenseKey.GetByCompany(comp);
            var allCompanyLicenseKeyValues = CompanyLicenseKeyValue.GetByCompanyId(comp.Id);
            var licenseData = CompanyLicenseValues.GetAllMatchedKeyValues(companyLicenseKeysFiltered, allCompanyLicenseKeyValues);
            VehicleLookupModel vehicleLookup = null;

            if (await WebGlobal.CurrentUser.Company.HasFeatureAsync(Extric.Towbook.Generated.Features.VehicleLookup))
            {
                var hostnames = WebsiteHostname.GetByCompanyId(WebGlobal.CurrentUser.CompanyId).ToCollection();
                string suggestedHostname = $"{WebGlobal.CurrentUser.CompanyId}";

                var x = SqlMapper.QuerySP<dynamic>("internalGetPrimaryEmailByCompanyId", new { WebGlobal.CurrentUser.CompanyId }).FirstOrDefault();
                if (x != null && !string.IsNullOrEmpty(x.EmailAddress))
                    suggestedHostname = x.EmailAddress.Replace("@towbook.net", "");

                var website = Website.GetById(hostnames.FirstOrDefault()?.WebsiteId ?? 0);
                
                var notice = CompanyKeyValue.GetFirstValueOrNull(WebGlobal.CurrentUser.CompanyId, Provider.Towbook.ProviderId, "VehicleLookupGeneralNotice");

                var includePrice = CompanyKeyValue.GetFirstValueOrNull(WebGlobal.CurrentUser.CompanyId, Provider.Towbook.ProviderId, "VehicleLookupIncludePrice") == "1";

                vehicleLookup = new VehicleLookupModel()
                {
                    Id = website?.WebsiteId ?? 0,
                    Enabled = website != null && website.IsDeleted.GetValueOrDefault() == false,
                    IncludePrice = includePrice,
                    Hostnames = hostnames?.Take(1).Select(s => HostnameModel.Map(s)).ToCollection(),
                    SuggestedHostname = suggestedHostname,
                    Notes = notice
                };
            }

            var model = new CompanySettingsModel()
            {
                Company = null,
                Licenses = licenseData,
                VehicleLookup = vehicleLookup
            };

            return View("CompanyLicenseDetails", model);
        }          


        [HttpPost]
        public async Task<JsonResult> HostnameLookup(string name)
        {
            if (WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.Manager)
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            if (string.IsNullOrEmpty(name))
                AjaxUtility.ThrowIfPropertyMissing("name");

            var hostname = name;
            object returnObj = null;

            await AjaxUtility.ThrowIfFeatureNotAssignedAsync(Extric.Towbook.Generated.Features.VehicleLookup);

            var sub = name.ToLowerInvariant().Split('.')[0];
            if(string.IsNullOrEmpty(sub) || ReservedSubdomains.Where(w => w.StartsWith(sub)).Any())
                return Json(new { Content = hostname, Valid = false });

            var wh = WebsiteHostname.GetByHostname(name);
            if (wh != null)
            {
                var website = Website.GetById(wh.WebsiteId);
                if(website != null && website.CompanyId == WebGlobal.CurrentUser.CompanyId)
                    returnObj = new { Content = hostname, Valid = true };
                else
                    returnObj = new { Content = hostname, Valid = false };
            }
            else
                returnObj = new { Content = hostname, Valid = true };

            return Json(returnObj);
        }



        private async Task<LocationModel> GeocodeWithGoogle(string address) => await GeocodeHelper.Geocode(address);
    }
}
