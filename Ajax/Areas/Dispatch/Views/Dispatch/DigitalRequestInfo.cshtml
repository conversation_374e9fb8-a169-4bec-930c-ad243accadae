@model Extric.Towbook.Dispatch.CallModels.CallModel
@{
    ViewBag.Title = "Request Information";
}
<script type="text/javascript">
    $(function () {
        $('#updateStatusBtn').click(function () {
            $('#pane-change-status input').attr('disabled', 'disabled');
            $(this).attr('value', 'Updating...');

           $.ajax({
                url: '/api/calls/@ViewBag.Id/requestInfo',
                type: 'POST',
                contentType: "application/json; charset=utf-8",
                data: JSON.stringify({
                    comments: $('#comments').val(),
                })
            }).done(function (data) {
                towbook.views.dispatch.clearDetailView();
            }).error(function (xhr, status, error) {
                alert("error: " + status + ", " + error);
            });
        });

        $('#cancelBtn').click(function () {
            towbook.views.dispatch.clearDetailView();
        });
    });
</script>
<style>
    label {
        display: block;
        margin-top: 20px
    }
</style>

<h2>Digital Dispatch Request Information for Call #@ViewBag.CallNumber</h2>

@if (ViewBag.CallRequest == null)
{
    <strong>This call was not created from a Digital Dispatch.</strong>
    <br />
    <br />
    <strong>Because of this, Towbook can't send this request digitally. Please call the motor club directly.</strong>
}
else
{

    <div style="padding-left: 10px;">
        <div>@(ViewBag.Call.Account.Company)</div>
        <div><text>Purchase Order #</text> @(ViewBag.Call.PurchaseOrderNumber)</div>

        <label for="comments">Please enter your question:</label>
        <textarea id="comments" maxlength="100"></textarea><br /><br />

        When the motor club responds, the response will be added to the notes of this call.
    </div>
}
@section buttons {
    @if (ViewBag.CallRequest != null)
    {
        <input type="submit" class="standard-button" value="Send" id="updateStatusBtn" />
    }
    <input type="button" class="standard-button" value="Cancel" id="cancelBtn" />
}