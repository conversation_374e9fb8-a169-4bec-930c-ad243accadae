@model Extric.Towbook.Dispatch.CallModels.CallModel
@using Extric.Towbook.Utility

<style>
    .towbook-combobox-item * {
        font: 12px "Open Sans", Arial, sans-serif !important;
    }

    .towbook-combobox-item .ui-corner-all {
        padding: 6px 5px 6px 8px !important;
    }

    .driver-item {
        margin: -6px -5px -6px -8px;
        padding: 6px 5px 6px 8px;
        font-weight: bold !important;
    }

    .driver-item .checked-in {
        font: normal normal normal 14px/1 "Font Awesome 5 Pro" !important;
        color: #4ec900;
        margin-right: 4px !important;
    }

    .driver-item .truck {
        display: inline-block;
        margin-left: 8px;
        color: #777;
    }

    .driver-item .status {
        display: inline-block;
        float: right;
        margin: 3px;
        width: 10px;
        height: 10px;
        border-radius: 50%;
        color: transparent;
    }

    .driver-item .status:hover {
        color: #fff;
        padding: 4px 8px;
        width: auto;
        height: auto;
        border-radius: 30px;
        border: solid 2px #fff;
        margin: -6px -6px 0px -6px;
    }

    .driver-item .estimate {
        margin: 1px 5px 0 10px;
        color: #777
    }
</style>

<div class="status-select">
    <div class="input-wrap">
        <label for="status">Change status to:</label><br />
        <select id="status"></select>
    </div>
</div>

<div class="driver-select">
    <div id="z-multipleDrivers" class="input-wrap"></div>

    <div id="z-singleDriver" class="input-wrap">
        <label for="driver">Driver to dispatch this call to: </label><br />
        <select id="driver" title="choose a driver"></select>
    </div>

    <div id="z-singleTruck" class="input-wrap">
        <label for="truck">Truck to dispatch to this call:</label><br />
        <select id="truck" title="choose a truck"></select>
    </div>

    <div id="z-dispatchMessage" class="input-wrap">
        <label for="driverMessage">Send message to the driver's mobile phone: </label><br />
        <textarea id="driverMessage">@ViewBag.Message</textarea>
    </div>
</div>

@section buttons {
    <input type="button" class="standard-button" id="updateBtn" value="Update" title="Update Status" />
    <input type="button" class="standard-button" id="cancelBtn" value="Cancel" title="Cancel" />
}

    <script type="text/javascript">

    function secondsToHms(d) {
        d = Number(d);
        if (d >= 3583)
            return (d / 3600).toFixed(2) + " hrs";
        else
            return (d / 60).toFixed(0) + " mins";
    }

    function sortCallsByStatus(calls) {
        if (calls && calls.length > 1) {

            // Sort by status (descending), then by estimatedTimeSeconds
            calls.sort(function (a, b) {
                if (a.status == 7 && b.status == 5) return 1;
                if (a.status == 5 && b.status == 7) return -1;
                if (a.status < b.status) return 1;
                if (a.status > b.status) return -1;
                if (a.status == b.status) {
                    if (a.estimatedTimeSeconds < b.estimatedTimeSeconds) return -1;
                    if (a.estimatedTimeSeconds > b.estimatedTimeSeconds) return 1;
                }
                return 0;
            });
        }
        return calls;
    }

    function mouseoverDriverMarker(id) {
        // var marker = dispatchingMarkers.find(function (m) { return m.id == 'driver-' + id });
        var marker = tbmap.findDriver(id);
        if (marker)
            marker.addClass('highlight');
    }

    function mouseoutDriverMarker(id) {
        // var marker = dispatchingMarkers.find(function (m) { return m.id == 'driver-' + id });
        var marker = tbmap.findDriver(id);
        if (marker)
            marker.removeClass('highlight');
    }

    function finishInit() {
        var currentStatusId = @ViewBag.Call.Status.Id;
        var dispatchStatusId = 1;
        var cancelledStatusId = @ViewBag.CancelledStatusId;
        var completedStatusId = @ViewBag.CompletedStatusId;
        var statuses = JSON.parse('@Html.Raw(System.Web.HttpUtility.JavaScriptStringEncode(ViewBag.StatusesJson))');
        var callId = @ViewBag.Id;
        var companyId = @ViewBag.Call.CompanyId;
        var assignedDrivers = JSON.parse('@Html.Raw(System.Web.HttpUtility.JavaScriptStringEncode(ViewBag.AssignedDrivers))');
        var callNumber = @ViewBag.CallNumber;
        var nearest = JSON.parse('@Html.Raw(System.Web.HttpUtility.JavaScriptStringEncode(ViewBag.NearestDrivers))');
        var assignedTrucks = JSON.parse('@Html.Raw(System.Web.HttpUtility.JavaScriptStringEncode(ViewBag.AssignedTrucks))');
        var usersCheckedIn =  JSON.parse('@Html.Raw(System.Web.HttpUtility.JavaScriptStringEncode(ViewBag.UsersCheckedIn))'); 

        // Clone the drivers list
        var newDrivers = JSON.parse(JSON.stringify(towbook.drivers));

        // Filter only drivers belonging to this call's company, and that are active (don't have an endDate)
        var drivers = $.grep(towbook.filterCompanies(newDrivers, companyId), function (item) {
            return item.endDate == null;
        });

        // For drivers that are also 'nearest' drivers, get their distance/time from the call
        drivers = $.map(drivers, function(o) {
            o.truckId = null;
            o.latitude = 0;
            o.longitude = 0;
            o.estimatedDistanceMiles = null;
            o.estimatedTimeSeconds = null;
            o.calls = [];

            var f = towbook.get(nearest, o.id, "driverId");
            $.extend(o, f);
            o.calls = sortCallsByStatus(o.calls);

            var u = towbook.get(usersCheckedIn, o.linkedUserId);
            o.checkedIn = (u && u.checkedIn) || false;

            return o;
        });

        // Sorting Rules:
        //
        // checked in ?
        //   # calls assigned (asc)
        //     proximity to call (driver's curr loc to pickup loc)
        // else
        //   reporting location ?
        //     # calls assigned (asc)
        //       proximity to call (driver's curr loc to pickup loc)
        //   else
        //     # calls assigned (desc)

        drivers.sort(function (a, b) {
            var ax = a.checkedIn ? 0 : 1;
            var bx = b.checkedIn ? 0 : 1;
            if (ax == bx) {
                if (ax == 0) {
                    ax = a.calls.length || 0;
                    bx = b.calls.length || 0;
                    if (ax == bx) {
                        ax = a.estimatedDistanceMiles ? 0 : 1;
                        bx = b.estimatedDistanceMiles ? 0 : 1;
                        if (ax == bx) {
                            if (a.estimatedDistanceMiles) {
                                return a.estimatedDistanceMiles - b.estimatedDistanceMiles;
                            } else {
                                return a.name.localeCompare(b.name);
                            }
                        } else {
                            return ax - bx;
                        }
                    } else {
                        return ax - bx;
                    }
                } else {
                    ax = a.latitude && a.longitude ? 0 : 1;
                    bx = b.latitude && b.longitude ? 0 : 1;
                    if (ax == bx) {
                        if (ax == 0) {
                            ax = a.calls.length || 0;
                            bx = b.calls.length || 0;
                            if (ax == bx) {
                                ax = a.estimatedDistanceMiles ? 0 : 1;
                                bx = b.estimatedDistanceMiles ? 0 : 1;
                                if (ax == bx) {
                                    if (a.estimatedDistanceMiles) {
                                        return a.estimatedDistanceMiles - b.estimatedDistanceMiles;
                                    } else {
                                        return a.name.localeCompare(b.name);
                                    }
                                } else {
                                    return ax - bx;
                                }
                            } else {
                                return ax - bx;
                            }
                        } else {
                            ax = a.calls.length || 0;
                            bx = b.calls.length || 0;
                            return bx - ax;
                        }
                    } else {
                        return ax - bx;
                    }
                }
            } else {
                return ax - bx;
            }
        });

        //// Make a marker for each driver
        //$.forEach(drivers, function (o) {
        //    var driver = towbook.get(vm.drivers, o.id, 'driverId');
        //    if (driver) {
        //        var statusId = driver.highestStatus && driver.highestStatus.statusId;
        //        var callNumber = driver.highestStatus && driver.highestStatus.callNumber;
        //        var position = driver.location;
        //        if (position) {
        //            var marker = new CustomMarker(tbmap.map, {
        //                data: driver,
        //                type: 'driver',
        //                id: 'driver-' + driver.id,
        //                position: new google.maps.LatLng(position.latitude, position.longitude),
        //                classes: ['driver-marker'],
        //                status: getCallStatusText(statusId).replace(' ', '').toLowerCase(),
        //                text: driver.initials,
        //                info: {
        //                    eta: {
        //                        num: driver.initials,
        //                        units: '',
        //                    },
        //                    title: driver.name,
        //                    status: getCallStatusText(statusId),
        //                    callNumber: callNumber,
        //                }
        //            });

        //            marker.addListener('click', function () {
        //                $('#driver').setVal(driver.driverId);
        //            }.bind(tbmap));
        //            marker.refresh();

        //            dispatchingMarkers.push(marker);

        //            // If we're moving the driver markers to their future positions
        //            if (dispatchingMarkersMove) {

        //                // If the driver is assigned to a call
        //                if (driver.highestStatus && driver.highestStatus.callId) {
        //                    var currCall = towbook.get(vm.calls, driver.highestStatus.callId);
        //                    if (currCall) {

        //                        // Get the call's final destination
        //                        var waypoint;
        //                        if (currCall.isTow()) {
        //                            waypoint = currCall.waypoints.find(function (w) { return w.title == 'Destination' });
        //                        } else {
        //                            waypoint = currCall.waypoints.find(function (w) { return w.title == 'Pickup' });
        //                        }
        //                        if (waypoint && waypoint.latitude && waypoint.longitude) {
        //                            var startLat = position.latitude;
        //                            var startLng = position.longitude;
        //                            var endLat = waypoint.latitude;
        //                            var endLng = waypoint.longitude;

        //                            // If the destination is not where the driver is currently
        //                            if (endLat != startLat || endLng != startLng) {

        //                                // Smoothly animate the driver marker to the new position
        //                                animate({
        //                                    duration: 1000, // milliseconds
        //                                    easing: 'easeInOutCubic',
        //                                    step: function (y) {
        //                                        let lat = startLat + y * (endLat - startLat);
        //                                        let lng = startLng + y * (endLng - startLng);
        //                                        marker.setPosition(new google.maps.LatLng(lat, lng));
        //                                    }
        //                                });
        //                            }
        //                        }
        //                    }
        //                }
        //            }
        //        }
        //    }
        //});

        //// For each call
        //$.forEach(vm.calls, function (call) {

        //    var isCurrCall = (call.id === callId);

        //    // That is the call being dispatched, or is active
        //    if (isCurrCall || (call.status.id > 0 && (call.status.id < 5 || call.status.id == 7))) {

        //        // That is the call being dispatched, or in the same company
        //        // (Only able to dispatch to drivers within the same company)
        //        if (isCurrCall || (call.companyId == companyId || (call.companies && call.companies.indexOf(companyId) > -1))) {

        //            // If its the call being dispatched, get its current location
        //            // Else get its final destination
        //            var waypoint;
        //            if (isCurrCall) {
        //                waypoint = getCurrentWaypoint(call);
        //            } else {
        //                if (call.isTow()) {
        //                    waypoint = call.waypoints.find(function (w) { return w.title == 'Destination' });
        //                } else {
        //                    waypoint = call.waypoints.find(function (w) { return w.title == 'Pickup' });
        //                }
        //            }
        //            if (waypoint && waypoint.latitude && waypoint.longitude) {

        //                // Create a marker for it
        //                var marker = new CustomMarker(tbmap.map, {
        //                    data: call,
        //                    type: 'call',
        //                    id: 'call-' + call.id,
        //                    position: new google.maps.LatLng(waypoint.latitude, waypoint.longitude),
        //                    classes: ['call-marker'],
        //                    status: getCallStatusText(call.status.id).replace(' ', '').toLowerCase(),
        //                    text: call.callNumber,
        //                    info: {
        //                        eta: {
        //                            num: getDriverName(call).initials,
        //                            units: '',
        //                        },
        //                        title: getVehicle(call),
        //                        status: getCallStatusText(call.status.id),
        //                        callNumber: call.callNumber,
        //                    }
        //                });

        //                if (isCurrCall) {
        //                    if ((call.destination || '').length < 3 && !call.impound) {
        //                        marker.classes.push('service');
        //                    }
        //                    if (call.priority == 1) {
        //                        marker.classes.push('emergency');
        //                    }
        //                    marker.classes.push('raised');

        //                } else {
        //                    marker.classes.push('destination');

        //                    if (call.isTow()) {
        //                        var a = new google.maps.LatLng(call.waypoints[0].latitude, call.waypoints[0].longitude);
        //                        var b = new google.maps.LatLng(call.waypoints[1].latitude, call.waypoints[1].longitude);
        //                        marker.setRoute(a, b);
        //                    }
        //                }

        //                if (showingCallNumbers) {
        //                    marker.classes.push('show-call-num');
        //                }

        //                marker.addListener('click', function () {
        //                    var drivers = marker.data.drivers.filter(function (d) { return d.driver && d.driver.id }) || [];
        //                    if (drivers.length > 0) {
        //                        $('#driver').setVal(drivers[0].driver.id);
        //                    }
        //                }.bind(tbmap));

        //                dispatchingMarkers.push(marker);
        //            }
        //        }
        //    }
        //});

        // Filter only trucks belonging to this call's company
        var trucks = towbook.filterCompanies(towbook.trucks, companyId).map(t => {
            t.name = _enc(t.name);
            return t;
        });

        // Load statuses, drivers, trucks into dropdowns
        $('#status').appendOptions(statuses, false, false, 'id', 'name', null, null, true);
        $('#truck').appendOptions(trucks).setVal(assignedTrucks[0] || 0);
        $('#driver').appendOptions(drivers, true, false, 'id', 'name', null, null, true).setVal(assignedDrivers[0] || 0);

        // Set status to 'overrideStatusId' if it exists, else set to next status
        if (typeof (overrideStatusId) !== "undefined")
            nextSelectedId = overrideStatusId;
        else
            nextSelectedId = getNextStatusId(currentStatusId);

        $("#status").val(nextSelectedId);

        if (nextSelectedId == dispatchStatusId) {
            $('#z-dispatchMessage').css('display', 'block');
            multiDriverSwitch();
        }
        else
        {
            $('#z-dispatchMessage').css('display', 'none');

            if(towbook.isEmpty(assignedDrivers) || assignedDrivers[0] == 0)
                multiDriverSwitch();
        }

        $('#status').combobox({selected: onStatusChange});
        $('#driver').combobox({
            source: $.map($('#driver').children('option'), function (option) {
                var driver = towbook.get(drivers, $(option).val());
                var driverVm = towbook.get(vm.drivers, $(option).val(), 'driverId');
                if (driver && driverVm) {
                    var highestStatus = driver.calls && driver.calls.length && driver.calls[0];
                    var currCallId = highestStatus && highestStatus.callId;
                    var label = `<div class="driver-item" 
                        onmouseover="mouseoverDriverMarker('${driverVm.id}')"
                        onmouseout="mouseoutDriverMarker('${driverVm.id}')">
                        ${driver.checkedIn ? '<i class="fa fa-check checked-in" title="Checked in"></i>' : ''}${_enc(driver.name)}
                        <div class="truck"> ${_enc(driverVm.truckName) || ''}</div>`;
                    if (driver.calls) {
                        for (var i = driver.calls.length - 1; i >= 0; i--) {
                            var call = driver.calls[i];
                            if (call.status < 5 || call.status == 7)
                                label += ('<div class="status ' + getCallStatusText(call.status).replace(' ', '').toLowerCase() + '">' + call.callNumber + '</div>');
                        }
                    }
                    if (driver.estimatedDistanceMiles || driver.estimatedTimeSeconds) {
                        label += ('<div class="estimate">' + driver.estimatedDistanceMiles.toFixed(1) + ' mi, ' +
                            secondsToHms(driver.estimatedTimeSeconds) + ' from current location</div>');
                    }
                    driver.calls.forEach(function (call) {
                        // If this call is the currently assigned call, and it has a distance estimate, show it
                        if (call.callId == currCallId && (call.estimatedDistanceMiles || call.estimatedTimeSeconds)) {
                            label += ('<div class="estimate">' + call.estimatedDistanceMiles.toFixed(1) + ' mi, ' +
                                secondsToHms(call.estimatedTimeSeconds) + ' from end of current job</div>');
                        }
                    });
                    label += '</div>';
                    return {
                        label: label,
                        value: $(option).text(),
                        option: option,
                    };
                }
            }),
            selected: function() {
                if(towbook.driverTruckDefaults.length == 0)
                    return;

                var driverId = $(this).getVal();
                var driverTruck = towbook.get(towbook.driverTruckDefaults, driverId, "driverId");
                if(driverTruck != null)
                    $('#truck').setVal(driverTruck.truckId);
            }
        });

        $('#truck').combobox();

        $('#cancelBtn').click(function cancelBtn_clicked() {
            towbook.views.dispatch.clearDetailView();
        });

        $('#updateBtn').click(doUpdate);

        /** functions */

        function multiDriverSwitch() {
            if (assignedDrivers.length > 1) {

                var multi = $('#z-multipleDrivers');
                multi.html('');
                multi.append('<label>Dispatch the call to the following drivers:</label>');

                $.each(assignedDrivers, function (index, obj) {
                    var driver = towbook.get(towbook.drivers, obj);
                    if (driver != null)
                        multi.append(`<div class='driver-name'>${_enc(driver.name)}</div>`);
                });

                $('#z-multipleDrivers').show();
                $('#z-singleDriver').hide();
                $('#z-singleTruck').hide();
            } else {
                $('#z-multipleDrivers').hide();
                $('#z-singleDriver').show();
                $('#z-singleTruck').show();
            }
        }

        function getNextStatusId(statusId) {
            var i;

            for (i = 0; i < statuses.length; i++) {
                if (statusId == statuses[i].id) {
                    break;
                }
            }

            if (i + 1 < statuses.length) {
                return statuses[i + 1].id;
            }

            return dispatchStatusId;
        }

        function onStatusChange(obj, selected) {
            if (dispatchStatusId == selected.item.value) {
                $('#z-dispatchMessage').css('display', 'block');
                multiDriverSwitch();
            } else {
                $('#z-dispatchMessage').css('display', 'none');

                if (!towbook.isEmpty(assignedDrivers) && assignedDrivers[0] != 0) {
                    $('#z-multipleDrivers').hide();
                    $('#z-singleDriver').hide();
                    $('#z-singleTruck').hide();
                }
            }
        }

        function doUpdate () {
            var targetStatusId = $('#status').val();

            if (completedStatusId == targetStatusId) {
                loadAjax('/ajax/dispatch/' + callId + '/complete');
                return false;
            }

            var xdata = {
                id: callId,
                status: {id: targetStatusId}
            };

            // if the target status is a Dispatch
            var driverId = $('#driver').val();
            var truckId = $('#truck').val();

            if (assignedDrivers.length < 2 &&
                (driverId > 0 || truckId > 0))
            {
                xdata.assets = [
                    {
                        id: @ViewBag.Call.Assets[0].Id,
                        drivers: [
                            {
                                driver: { id: driverId},
                                truck: { id: truckId == null ? 0 : truckId}
                            }
                        ]
                    }];
            }

            function updateStatusNow(){
                $.ajax({
                    url: '/api/calls/' + callId,
                    contentType: "application/json; charset=utf-8",
                    type: 'PUT',
                    data:  JSON.stringify(xdata),
                    headers: { "X-Prevent-Auto-Dispatch": 1 }
                }).done(function (resp) {
                    towbook.views.dispatch.clearDetailView("Success");
                });
            }

            var driverList = null;
            var message = "";
            if (targetStatusId == dispatchStatusId) {

                if (assignedDrivers != null && assignedDrivers.length > 1) {
                    driverList = assignedDrivers;
                }

                var firstDriver = parseInt($('#driver').val() || driverList[0] || 0);
                if (firstDriver) {

                    $('#updateBtn').attr('value', 'Updating...');
                    $('#updateBtn, #cancelBtn').prop('disabled', 'disabled');

                    $.ajax({
                        url: '/api/drivers/' + firstDriver + '/sendMessage',
                        contentType: "application/json; charset=utf-8",
                        type: 'POST',
                        data: JSON.stringify({
                            callId: callId,
                            id: $('#driver').val(),
                            message: $('#driverMessage').val().substring(0, 320),
                            drivers: driverList
                        })
                    }).done(function (resp2) {
                        updateStatusNow();
                    }).error(function (xhr, status, error) {

                        var jsonResponse = JSON.parse(xhr.responseText);
                        if (jsonResponse["type"] == "tomtom") {
                            var tbMessage = "Warning: Towbook encountered a TomTom error (" + jsonResponse["code"] + ").";

                            if (jsonResponse["code"] == "9500")
                                tbMessage += "\r\rTowbook could not dispatch the call to the TomTom unit because the pickup address or destination address is causing an error. You will either need to fix the address and try again or update the call to 'dispatched' and notify the driver separately.";

                            if (!confirm(tbMessage + "\n\nWould you like to still update the call status to Dispatched?")) {
                                $('#updateBtn').attr('value', 'Update');
                                $('#updateBtn, #cancelBtn').prop('disabled', '');
                            }
                            else
                                updateStatusNow();
                        }
                        else {
                            alert(status.toUpperCase() + ": " + error + " [status code " + xhr.status + "]\n\nSorry, we could not process your request.  If you continue to get this error, please contact TOWBOOK for help.");
                        }
                    });
                } else {
                    alert('Please select a driver to dispatch to');
                }
            }
            else
            {
                updateStatusNow();
            }
        }
    }

    </script>