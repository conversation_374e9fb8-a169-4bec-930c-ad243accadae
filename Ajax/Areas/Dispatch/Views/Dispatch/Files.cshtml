@model Extric.Towbook.API.Models.CompanyFileModel
@using Extric.Towbook
@{
    ViewBag.Title = "Files";
}
<style type="text/css">@@import url(/ui/js/plupload/jquery.plupload.queue/css/jquery.plupload.queue.css);</style>

<script type="text/javascript" src="/ui/js/plupload/plupload.full.js"></script>
<script type="text/javascript" src="/ui/js/plupload/jquery.plupload.queue/jquery.plupload.queue-d2.js"></script>

<script type='text/x-jQuery-tmpl' id='tpl-file'>
    <tr>
        <td><a href="${url}">${filename}</a></td>
        <td>${towbook.formatDate(createDate)} ${towbook.formatAMPM(createDate)}</td>
        <td>${ownerUserName}</td>
        <td>${description}</td>
        <td style="cursor:pointer" data-id="${id}">(delete)</td>
    </tr>
</script>

<script type="text/javascript">
    $(function () {

        window.initUploadQueue = function () {
            $("#uploader").pluploadQueue({
                // General settings
                runtimes: 'gears,flash,silverlight,browserplus,html5',
                url: '/api/files?callId=@ViewBag.Id&description=none',
                max_file_size: '10mb',
                unique_names: true,

                // Resize images on clientside if we can; this causes Bad Things to happen on iOS/Android (Cuts off the photo)
                // resize: { width: 960, height: 720, quality: 90 },
                urlstream_upload: true,
                multiple_queues: true,
                strings: [
                    {
                        key: "Add files",
                        value: "Browse..."
                    },
                    {
                        key: 'Start upload',
                        value: 'Upload'
                    },
                    {
                        key: "Add your files to the upload queue and click the Start Upload button to upload them. ",
                        value: "Add your fiels to the upload queue and click the Start Upload button to upload them. "
                    }],
                // Specify what files to browse for
                /*filters: [{
                    title: "files",
                    extensions: "pdf,doc,docx,xls,xlsx,txt,jpg,png,bmp,gif,zip"
                }
                ],*/

                // Flash settings
                flash_swf_url: '/ui/js/plupload/plupload.flash.swf',

                // Silverlight settings
                silverlight_xap_url: '/ui/js/plupload/js/plupload.silverlight.xap',
                cancelUpload: function (x) {
                    console.log(x);
                },
                onStart: function() { 
                    
                },
                onProgress: function (m) {
                    
                },
                onComplete: function () {
                    $('#uploadFiles').css('display', 'none');
                    loadFiles();

                    $('#viewFiles').show();
                }
            });

        };

        if (typeof ($.template['file']) == 'undefined')   //If template doesn't exist yet, compile it
            $("#tpl-file").template('file');

        var userList = @Html.Raw(ViewBag.Users);

        $('#btnUpload').on('click', function() { 
            $('#viewFiles').hide();
            $('#uploadFiles').show();
            initUploadQueue();
            $('#title').html('Upload Files for Call #@ViewBag.CallNumber'); 
        });

        $('#btnClose').on('click', function() {
            towbook.views.dispatch.clearDetailView();
        });

        $(document).on('click', '.cancel', function () {
            $('#viewFiles').show();
            $('#uploadFiles').hide();
            $('#title').html('Call #@ViewBag.CallNumber Files'); 
        });

        var deleteFile = function(obj) {
            if(obj == null)
                return false;
            
            if (confirm("Are you sure you want to permanently delete this file from this call?")) {
                var id = $(obj).attr('data-id');

                $.ajax({
                    url: '/api/files/' + id + '/delete',
                    type: 'DELETE',
                }).done(function (data) {
                    $(obj).closest('tr').remove();

                    if($('#fileList').find('tbody tr').length == 0)
                    {
                        $('#fileList thead').hide();
                        $('#listTitle').show();
                        $('#btnUpload').attr('value', "Upload new files");
                    }
                }).error(function (xhr, status, error) {
                    alert("error: " + status + ", " + error);
                });
                
                return true;
            }
            
            return false;
        };

        var loadFiles = function () {
            $('#fileList tbody').html('');

            $.ajax({
                url: '/api/files/get?callId=@ViewBag.Id',
                type: 'GET',
            }).done(function (data) {
                
                if(data.length == 0)
                {
                    $('#fileList thead').hide();
                    $('#listTitle').show();
                    $('#btnUpload').attr('value', "Upload new files");

                }
                else
                {
                    $('#listTitle').hide();
                    $('#fileList thead').show();
                    $('#btnUpload').attr('value', "Upload more files");

                    var htmlList;
                    for (i in data) {
                        $('#fileList').find('tbody').append($.tmpl('file', data[i]));
                    }

                    //$('#fileList').find('tbody').append(htmlList);

                    $('#fileList tbody').find('tr').find('td:last').on('click', function () { deleteFile(this) });

                }

                $('#title').html('Call #@ViewBag.CallNumber Files');
                $('#listHolder').show();
                $('#viewFiles').show();

                return;


            }).error(function (xhr, status, error) {
                alert("error: " + status + ", " + error);
            });

            return;

            @{
                var list = await new Extric.Towbook.API.Controllers.FilesController().Get(null, null, null, @ViewBag.Id);
                
                if( list.Count > 0)
                {
                    @: $('#listTitle').hide();
                    @: $('#fileList thead').show();
                    @: $('#btnUpload').attr('value', "Upload more files");

                    string fileList = string.Empty;
            
                    foreach(var cfm in list)
                    {
                        string filename = cfm.Filename;
                        string createDate = cfm.CreateDate.ToString("d") + " " + ((DateTime)cfm.CreateDate).ToShortTowbookTimeString();
                        int id = cfm.Id;
                        string description = cfm.Description;
                        Extric.Towbook.User user = new Extric.Towbook.User();
                        string userName = cfm.OwnerUserName;
                        string url = cfm.Url;
                        @: fileList += '<tr><td><a href="@url">@filename</a></td><td>@createDate</td><td>@userName</td><td>@description</td><td style="cursor:pointer" data-id="@id">(delete)</td></tr>';
                    }
                    
                    @: $('#fileList').find('tbody').append(fileList);

                    @: $('#fileList tbody').find('tr').find('td:last').on('click', function () { deleteFile(this) });

                } else {
                    //Response.Write("There are no files to display for this call. To upload a file, click Upload File(s) at the top of this page.");
                    
                    @: $('#fileList thead').hide();
                    @: $('#listTitle').show();
                    @: $('#btnUpload').attr('value', "Upload new files");
                }
                
                @: $('#title').html('Files of Call #@ViewBag.CallNumber');
                @: $('#listHolder').show();
                @: $('#viewFiles').show();
              
            }


        };

        loadFiles();
    });

</script>

<style>

    #uploadFiles { display:none }
    #viewFiles { display: none } 

    #imageViewer { display: none; text-align: center; padding-top: 25px }
    #imageViewer img { pointer-events: none; }
    #imageViewer #imageDetails { display: none; position: absolute; background-color: white; background-color: rgba(0,0,0, 0.7); color: white; padding: 10px; height: 55px; } 
    #listHolder { display: none } 

    #fileList { padding: 0; margin: 0 }
    #fileList td { padding: 5px}  

    #btnUpload {margin-bottom: 10px }

    .plupload_filelist_footer > .plupload_file_name {
        margin-top: 40px;
        margin-left: -8px;
        margin-bottom: 10px;
    }

    @@media screen and (max-width: 480px) {
        input.standard-button { width: 100% }
    }

</style>

<h2 id="title">Files for Call #@ViewBag.CallNumber</h2>

<div id="viewFiles">
    <input type="button" class="standard-button" id="btnUpload" value="Upload files" />
    <input type="button" class="standard-button" id="btnClose" value="Close" />

    <div id="listHolder">
      <span id="listTitle">There are no files associated with this call.</span>
      <table id="fileList">
          <thead>
              <tr>
                  <td>File</td>
                  <td>Date</td>
                  <td>User</td>
                  <td>Description</td>
                  <td>Delete</td>
              </tr>
          </thead>
          <tbody></tbody>
      </table>
    </div>

    <div id="imageViewer">
        <div id="imageDetails">
            <div style="float:right; padding-right: 5px"><input type="button" class="standard-button" id="btnDelete" value="Delete file" /></div>
            <div style="float:left; padding-left: 5px">
                <ul class="x-details"></ul>
            </div>
        </div>
        <img src="about:blank" id="file" />
    </div>

</div>

<div id="uploadFiles">

<div id="uploader">Your browser doesn't support Flash, Silverlight, Gears, BrowserPlus or HTML 5. Please upgrade your browser and try again. Please feel free to contact Towbook for assistance.</div>
    <div style="clear: both"></div>
</div>
