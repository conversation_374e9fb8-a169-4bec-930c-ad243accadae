@model Extric.Towbook.Dispatch.CallModels.CallModel
@{
    ViewBag.Title = "Delete";
}
<script type="text/javascript">
    $(function () {
        $('#updateStatusBtn').click(function () {
            if ($('#digitalcancel-other-reason').is(':visible') && $('#otherReason').val() == '') {
                alert('Please enter a reason for cancellation in the Other Reaosn field.');
                $('#digitalcancel-other-reason').focus();
                return;
            }

            $('#pane-change-status input').attr('disabled', 'disabled');
            $(this).attr('value', 'Updating...');

           $.ajax({
                url: '/api/calls/@ViewBag.Id/digitalCancel',
                type: 'POST',
                contentType: "application/json; charset=utf-8",
                data: JSON.stringify({
                    reasonId: $('#goa-reason').val(),
                    customerContacted: $('#customerContacted').val() == "true",
                    comments: $('#comments').val(),
                    otherReason: $('#otherReason').val()
                })
            }).done(function (data) {
                towbook.views.dispatch.clearDetailView();
            }).error(function (xhr, status, error) {
                alert("error: " + status + ", " + error);
            });
        });

        $('#goa-reason').change(function () {
            if ($(this).val() == 2371) {
                $('#digitalcancel-other-reason').show();
            } else {
                $('#digitalcancel-other-reason').hide();
            }
        });

        $('#cancelBtn').click(function () {
            towbook.views.dispatch.clearDetailView();
        });
    });
</script>
<style>
    label {
        display: block;
        margin-top: 20px
    }
</style>

<h2>Digital Dispatch Cancel Call #@ViewBag.CallNumber</h2>

@if (ViewBag.CallRequest == null)
{
    <strong>This call was not created from a Digital Dispatch.</strong>
    <br />
    <br />
    <strong>Because of this, this call cannot have the cancellation communicated digitally. </strong><br /><br />
    <a href="#" onclick="loadAjax('/ajax/dispatch/@ViewBag.Id/cancel')">Manually cancel call</a>
}
else
{

<div style="padding-left: 10px;">
    <div>@(ViewBag.Call.Account.Company)</div>
    <div><text>Purchase Order #</text> @(ViewBag.Call.PurchaseOrderNumber)</div>

    <label for="goa-reason">Reason for Cancellation:</label>
    <select id="goa-reason">
        @foreach (var reason in ViewBag.Reasons)
        {
            <option value="@reason.MasterAccountReasonId">@reason.Name</option>
        }
    </select>
    @if (ViewBag.Call.Account.MasterAccountId != Extric.Towbook.Accounts.MasterAccountTypes.Swoop &&
        ViewBag.Call.Account.MasterAccountId != Extric.Towbook.Accounts.MasterAccountTypes.AaaAcg)
    {
        <label>Did you contact the customer?</label>
        <select id="customerContacted">
            <option value="false">No</option>
            <option value="true">Yes</option>
        </select>

        <label for="comments">Comments:</label>
        <textarea id="comments"></textarea>
    }
    @if (ViewBag.Call.Account.MasterAccountId == Extric.Towbook.Accounts.MasterAccountTypes.AaaAcg)
    {
        <div id="digitalcancel-other-reason" style="display:none">
            <label for="otherReason">Enter Other Reason:</label>
            <input type="text" id="otherReason" />
        </div>
    }
</div>
}
@section buttons {
    @if (ViewBag.CallRequest != null)
    {
        <input type="submit" class="standard-button" value="Submit" id="updateStatusBtn" />
    }
    <input type="button" class="standard-button" value="Cancel" id="cancelBtn" />
}