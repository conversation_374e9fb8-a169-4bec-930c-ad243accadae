@using Microsoft.Extensions.Primitives
<!DOCTYPE html>
<html>
@if (StringValues.IsNullOrEmpty(Context.Request.Query["_"])) {
        <head>
            <title></title>
            <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1" />
            <script src="//ajax.googleapis.com/ajax/libs/jquery/1.7.2/jquery.min.js" type="text/javascript"></script>
            <script src="//ajax.googleapis.com/ajax/libs/jqueryui/1.8.14/jquery-ui.min.js" type="text/javascript"></script>
            <script src="/ui/js/jquery.tmpl.min.js"></script>
            <script src="/UI/js/jquery.tipTip.js" type="text/javascript"></script> 

    <script src="/UI/js/jquery.tipsy.js" type="text/javascript"></script>
            <script src="//d3dy5gmtp8yhk7.cloudfront.net/1.12/pusher.min.js" type="text/javascript"></script>

            <script src="/ui/js/towbook/towbook.js"></script>
            <script src="/ui/js/towbook.js"></script>

            <script src="/ui/js/jquery.tmpl.min.js"></script>
            <script src="/ui/js/jquery.mousewheel.js"></script>
            <script src="/ui/js/mwheelIntent.js"></script>
            <script src="/ui/js/accounting.js"></script>
            
            

            <script src="/ui/js/jquery.jscrollpane.min.js"></script>
            <script src="/ui/js/jquery.timepicker.min.js"></script>

            <script src="/ui/js/timepicker/datepair.js"></script>
            <script src="/UI/JS/jquery.blockUI.js"></script>    

            <script type="text/javascript" src="/UI/js/swal2/sweetalert2.min.js"></script>
            <link rel="stylesheet" type="text/css" href="/UI/js/swal2/sweetalert2.css" />
    
            <link rel="stylesheet" href="/UI/css/jquery-ui.css" />
            <link rel="stylesheet" href="/UI/css/theme/jquery-ui-1.8.21.custom.css" />
            <link rel="stylesheet" href="/UI/css/tiptip.css" />
            <link rel="stylesheet" href="https://pro.fontawesome.com/releases/v5.1.0/css/all.css" integrity="sha384-87DrmpqHRiY8hPLIr7ByqhPIywuSsjuQAfMXAE0sMUpY3BM7nXjf+mLIUSvhDArs" crossorigin="anonymous">
            <link rel="stylesheet" href="/UI/css/towbook.css" />
            <link rel="stylesheet" href="/UI/css/application-forms.css" />
            <link rel="stylesheet" href="/dispatch/default.css" />

            <style>
                body { margin: 0 auto; max-width: 800px; background-color: white; background-image: none }
                #wrapper, .ajax-view {
                    background-color: white;
                    padding: 10px
                }
                .ajax-view>h2 {margin-top: 0 }
            </style>

            <script>
                $(function () {

                    if (towbook.geo.states.length == 0) {
                        towbook.log("!!! api/config doesn't seem to be loaded... loading it.");

                        $.when($.ajax({ url: "/api/config" })).done(function (config) {
                            $.extend(towbook, config);
                            towbook.formatData();
                            if (typeof (finishInit) == "function") {
                                finishInit();
                            }

                        }).fail(function (x, y, z) {
                            console.log(x);
                            console.log(y);
                            console.log(z);

                            alert("There was an error loading one of the Towbook components. Please try refreshing the page.\r\n\r\n" + x.responseText);
                        });
                    }
                    else {
                        finishInit();
                    }

                    if (typeof (loadAjax) != "function") {
                        window.loadAjax = function (href) { window.location.href = href; }
                    }


                    $.extend(towbook.views, {
                        dispatch: {
                            clearDetailView: function () {
                            if (parent && parent.closeLightbox && typeof(parent.closeLightbox) == 'function')
                              parent.closeLightbox();
                            }
                        }
                    });

                });
            </script>
        </head>
} 
        <body>

<div class="ajax-view">
        @RenderBody()
</div>

@if (IsSectionDefined("buttons")) { 
<div class="navigation-row">
    @RenderSection("buttons", false)
</div>
}

            


@if (!StringValues.IsNullOrEmpty(Context.Request.Query["_"])) {      
    <script>
        $(function () {
            if (typeof (finishInit) == "function") {
                finishInit();
            }
            else {
                towbook.log("finishInit is NOT a function.");
            }
        });
    </script>
}



</body>

</html>

