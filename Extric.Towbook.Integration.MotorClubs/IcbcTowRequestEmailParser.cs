using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Extric.Towbook.Utility;
using Extric.Towbook.Vehicle;
using HtmlAgilityPack;

namespace Extric.Towbook.Integration.MotorClubs
{
    public class IcbcTowRequestEmailParser: IMotorClubRequest
    {
        public string RequestId { get; set; }
        public DateTime? RequestDate { get; set; }
        public string BenefitAmount { get; set; }
        public string CustomerId { get; set; }
        public string CustomerName { get; set; }
        public string CustomerPhone { get; set; }
        public string DestinationAddress { get; set; }
        public string DestinationName { get; set; }
        public string DestinationCrossStreet { get; set; }
        public string ETA { get; set; }
        public decimal PayoutFees { get; set; }
        public string PickupAddress { get; set; }
        public string PickupCrossStreet { get; set; }
        public string PickupType { get; set; }
        public string PurchaseOrderNumber { get; set; }
        public string ServiceNeeded { get; set; }
        public string TowingLocationId { get; set; }
        public string TowingProviderId { get; set; }
        public string VehicleClass { get; set; }
        public string VehicleColor { get; set; }
        public string VehicleLicense { get; set; }
        public string VehicleLicenseState { get; set; }
        public string VehicleMake { get; set; }
        public string VehicleModel { get; set; }
        public string VehicleNotes { get; set; }
        public string VehicleType { get; set; }
        public string VehicleVIN { get; set; }
        public string VehicleYear { get; set; }
        public string CustomerAddress { get; set; }
        public string CustomerCity { get; set; }
        public string CustomerState { get; set; }
        public string CustomerZip { get; set; }

        public IcbcTowRequestEmailParser()
        {
        }

        public static IcbcTowRequestEmailParser FromHtml(string html)
        {
            

            #region  Emails come with "<div>.</div>" tags. We need to remove these "bullets" from the text.
            var doc = new HtmlDocument();
            doc.LoadHtml(html);

            // Select all div elements that contain only "."
            var divs = doc.DocumentNode
                          .SelectNodes("//div")?
                          .Where(div => div.InnerText.Trim() == ".")
                          .ToList();

            // Remove them from the document
            if (divs != null)
            {
                foreach (var div in divs)
                {
                    div.Remove();
                }
            }
            #endregion

            // Get the cleaned HTML
            string cleanedHtml = doc.DocumentNode.OuterHtml;

            var text = new HtmlToText().ConvertHtml(cleanedHtml ?? string.Empty);

            return FromText(text);
        }

        private static IcbcTowRequestEmailParser FromText(string data)
        {
            var result = new IcbcTowRequestEmailParser();

            Dictionary<string, string> values = new Dictionary<string, string>();

            var claimInformation = ExtractText(data, "Claim information", "Vehicle information", false);
            var vehicleInformation = ExtractText(data, "Vehicle information", "Tow information", false);
            var towCompanyInformation = ExtractText(data, "Tow information", "Current location", false);
            
            var pickUpInformation = "Current location" + Environment.NewLine + ExtractText(data, "Current location", "Destination", false);
            
            var dropOffInformation = "Destination" + Environment.NewLine + ExtractText(data, "Destination", "Drop off instructions", false);
            if(string.IsNullOrEmpty(dropOffInformation))
                dropOffInformation = "Destination" + Environment.NewLine + ExtractText(data, "Destination", "Thank you", false);

            values.AddRangeNewOnly(GetTableRowValues(claimInformation));
            values.AddRangeNewOnly(GetTableRowValues(vehicleInformation));
            values.AddRangeNewOnly(GetTableRowValues(towCompanyInformation));
            values.AddRangeNewOnly(GetTableRowValues(pickUpInformation));
            values.AddRangeNewOnly(GetTableRowValues(dropOffInformation));

            foreach (var val in values)
            {
                if (val.Key.ToLowerInvariant().Equals("icbc claim number"))
                    result.PurchaseOrderNumber = val.Value;

                if (val.Key.ToLowerInvariant().Equals("date of loss"))
                    result.VehicleNotes += "Date of loss: " + val.Value + "\n";

                if (val.Key.ToLowerInvariant().StartsWith("licence plate"))
                    result.VehicleLicense = val.Value;

                if (val.Key.ToLowerInvariant().StartsWith("vehicle description"))
                {
                    string year = string.Empty;
                    string color = string.Empty;
                    string make = string.Empty;
                    string model = string.Empty;

                    VehicleUtility.FindVehicleInfo(val.Value.Replace("/", " "), out year, out make, out model, out color);

                    result.VehicleColor = color;
                    result.VehicleMake = make;
                    result.VehicleModel = model;
                    result.VehicleYear = year;
                }

                if (val.Key.ToLowerInvariant().StartsWith("vehicle identification number"))
                    result.VehicleVIN = val.Value;

                if (val.Key.ToLowerInvariant().Equals("current location"))
                    result.PickupAddress = val.Value;

                if (val.Key.ToLowerInvariant().Equals("destination"))
                    result.DestinationAddress = val.Value;

                if (val.Key.ToLowerInvariant().StartsWith("tow company") && val.Key.ToLowerInvariant().EndsWith("name"))
                    result.VehicleNotes += "Tow Company's Name: " + val.Value + "\n";
            }

            var pickUpInstructions = ExtractText(data, "Pick up instructions", "Destination", false);
            if (!string.IsNullOrEmpty(pickUpInstructions))
            {
                result.VehicleNotes += "Pick up instructions: " + pickUpInstructions + "\n";
            }

            var dropOffInstructions = ExtractText(data, "Drop off instructions", "Thank you", false);
            if(!string.IsNullOrEmpty(dropOffInstructions))
            {
                result.VehicleNotes += "Drop off instructions: " + dropOffInstructions + "\n";
            }

            return result;
        }

        private Dictionary<string,string> GetTowFromInfo(string text)
        {
            var ret = new Dictionary<string, string>();
            var rows = text.Split('\n');

            if (rows.Length >= 2)
            {
                var pickupLocation = rows[1]
                    .Replace("ICBC Claims Facility - ", "")
                    .Replace("Tow Yard - ", "")
                    .Replace("At Insured's Address - ", "")
                    .Replace("Repair Facility - ", "");

                ret.Add("TOW FROM", pickupLocation);

                foreach (var row in rows.AsEnumerable().Skip(2))
                    this.VehicleNotes += row + "\n";
            }
            else
                ret.Add("TOW FROM", text);

            return ret;
        }

        private Dictionary<string, string> GetTowLocationInfo(string text)
        {
            var ret = new Dictionary<string, string>();
            var rows = text.Split('\n');

            if (rows.Length >= 1)
            {
                ret.Add("TOW TO", rows[0]);

                foreach (var row in rows.AsEnumerable().Skip(1))
                    this.VehicleNotes += row + "\n";
            }
            else
                ret.Add("TOW TO", text);

            return ret;
        }

        private static Dictionary<string, string> GetTableRowValues(string data, char separator = '\n')
        {
            var ret = new Dictionary<string, string>();

            if (string.IsNullOrEmpty(data))
                return ret;

            var rows = data.Split(separator);
            if(rows.Length >= 2)
            {
                foreach (var group in rows.Batch(2))
                {
                    if (group.Count() <= 1)
                        continue;

                    string pattern = @"\s{4,}"; // 4 spaces

                    var keyRow = System.Text.RegularExpressions.Regex.Split(group.ElementAt(0), pattern);
                    var valueRow = System.Text.RegularExpressions.Regex.Split(group.ElementAt(1), pattern);

                    if (keyRow != null && valueRow != null)
                    {
                        List<string> alignedValues = new List<string>(valueRow);

                        while (alignedValues.Count < keyRow.Length)
                            alignedValues.Insert(alignedValues.Count - 1, string.Empty);

                        for (int i = 0; i < keyRow.Length; i++)
                            ret[keyRow[i].Trim()] = alignedValues[i].Trim();
                    }
                }
            }

            return ret;
        }

        private string ExtractDateTime(string data, string key)
        {
            int keyIdx = data.IndexOf(key);
            if (keyIdx == -1)
            {
                // couldn't find the key... don't return an exception - this will cause fields that are missing to cause the whole parser to crash.
                return "";
            }

            int endLineIdx = data.IndexOf("\n", keyIdx);

            string wholeLine = data.Substring(keyIdx + key.Length, endLineIdx - (keyIdx + key.Length));
            
            return wholeLine.Remove(0, 1).Trim().Replace(" / ", " ");
        }

        public static string ExtractText(string text, string key, string nextKey = "\n", bool clean = true, int startIndex = 0)
        {
            var data = text.Replace("\r", "");

            int keyIdx = data.IndexOf(key, startIndex);
            if (keyIdx == -1)
            {
                // couldn't find the key... don't return an exception - this will cause fields that are missing to cause the whole parser to crash.
                return "";
            }

            int endLineIdx = data.IndexOf(nextKey, keyIdx);
            if (endLineIdx == -1)
            {
                endLineIdx = data.IndexOf(nextKey);

                // stop out of index exception
                if (keyIdx > endLineIdx)
                    return "";
            }

            string[] parts = data.Substring(keyIdx + key.Length, endLineIdx - (keyIdx + key.Length)).Split('\n');
            if (parts.Length == 1)
            {
                if (clean)
                    return parts[0].Replace("\n", "").Trim();
                else
                    return parts[0].Trim();
            }
            else if (parts.Length > 1)
            {
                StringBuilder ret = new StringBuilder();
                foreach (var part in parts)
                {
                    if (part.Trim() == Environment.NewLine || part.Trim() == string.Empty)
                        continue;

                    ret.AppendLine(part.Trim());
                }

                if (clean)
                    return ret.ToString().Replace("\r", "").Replace("\n", "");
                else
                    return ret.ToString().Replace("\r", "");
            }
            else
                return string.Empty;
        }

        public System.Threading.Tasks.Task PostSave(Extric.Towbook.Dispatch.Entry entry)
        {
            return System.Threading.Tasks.Task.CompletedTask;
        }
    }
}
