using System.Collections.Generic;
using Extric.Towbook.Integration.MotorClubs.Queue;
using Async = System.Threading.Tasks;

namespace Extric.Towbook.Integration.MotorClubs.Services
{
    /// <summary>
    /// Exposes a service class for you to use to request a MotorClubAccountVerification be processed via the Service Bus Queue.
    /// </summary>
    public class AuthenticationVerificationService
    {
        public const string QueueName = "Mcb-AuthenticationVerificationQueueV2";
        public const string MessagePrefix = "AuthenticationVerificationService/";
        /// <summary>
        /// Submits a request to the backend queue to submit a job to the motor club.
        /// </summary>
        /// <param name="accountId"></param>
        /// <param name="callId"></param>
        public async Async.Task RequestVerificationOfUserCredentials(AuthenticationQueueItem avq)
        {
            avq.Save();

            AuthenticationQueueItem.UpdateStatus(avq, Queue.AuthenticationQueueItemStatus.Waiting);

            var props = new Dictionary<string, object>();
            
            props.Add("id", avq.Id);
            props.Add("accountId", avq.AccountId);
            props.Add("companyId", avq.CompanyId);
            props.Add("requestCreated", avq.CreateDate);

            await ServiceBusHelper.SendMessageAsync(QueueName, MessagePrefix + avq.Id, avq, "Motor Club Verify Authentication Credentials", props);
        }
    }
}
