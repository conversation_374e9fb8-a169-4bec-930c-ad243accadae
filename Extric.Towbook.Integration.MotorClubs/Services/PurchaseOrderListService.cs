using System;
using System.Collections.Generic;
using System.Linq;
using Extric.Towbook.Accounts;
using Extric.Towbook.Integration.MotorClubs.Queue;
using Async = System.Threading.Tasks;

namespace Extric.Towbook.Integration.MotorClubs.Services
{
    public class PurchaseOrderListService
    {
        public const string PurchaseOrdersQueueName = "Mcb-PurchaseOrdersQueue";
        public const string MessagePrefix = "PurchaseOrderListService/";

        /// <summary>
        /// Submits a request to the backend queue to retrieve the list of purchase orders from the MC websites.
        /// </summary>
        public async Async.Task RequestPurchaseOrderListUpdate(int accountId, DateTime? scheduledDate = null, bool force = false)
        {
            var account = await Account.GetByIdAsync(accountId);
            if (account == null)
                throw new MotorClubException("Account not found.");

            await RequestPurchaseOrderListUpdate(account, scheduledDate, force);
        }

        /// <summary>
        /// Submits a request to the backend queue to retrieve the list of purchase orders from the MC websites.
        /// </summary>
        public async Async.Task RequestPurchaseOrderListUpdate(Account account, DateTime? scheduledDate = null, bool force = false)
        {
            var masterAcc = await MasterAccount.GetByIdAsync(account.MasterAccountId);
            if (masterAcc == null)
                throw new MotorClubException("Account does not have a valid master account id associated");

            if (!force)
            {
                // If PO matching is not enabled for this account, exit
                var enablePoMatching = AccountKeyValue.GetByAccount(account.CompanyId, account.Id, Provider.Towbook.ProviderId, "EnablePoMatching").FirstOrDefault();
                if (enablePoMatching == null || enablePoMatching.Value != "1")
                    return;

                // If its currently in process, exit
                var q = PurchaseOrderListQueueItem.GetInProcess(account.Id);
                if (q != null)
                    return;
            }

            if (scheduledDate == null)
                scheduledDate = DateTime.Now;

            var iqi = new PurchaseOrderListQueueItem()
            {
                CompanyId = account.CompanyId,
                AccountId = account.Id,
                Status = Queue.QueueItemStatus.Waiting,
                ScheduleDate = scheduledDate.Value,
                Force = force,
            };
            iqi.Save();

            Dictionary<string, object> x = new Dictionary<string, object>();

            x.Add("id", iqi.Id);
            x.Add("companyId", iqi.CompanyId);
            x.Add("requestCreated", iqi.CreateDate);

            if (force)
                x.Add("force", true);

            await ServiceBusHelper.SendMessageAsync(PurchaseOrdersQueueName, MessagePrefix + iqi.Id, iqi, "Motor Club Purchase Order List Retrieval", x, scheduledDate);
        }
    }
}
