using System;
using System.Collections.Generic;
using System.Diagnostics;
using Extric.Towbook.Integration.MotorClubs.Queue;
using Async = System.Threading.Tasks;

namespace Extric.Towbook.Integration.MotorClubs.Services
{
    /// <summary>
    /// Exposes a class for you to communicate with the backend queue (Service Bus) that the backend workers receive job requests from.
    /// 
    /// Use this class to request a backend worker instance to sync payments from a motor club for the specified accountId. 
    /// 
    /// Calls to this class are extremely fast as the only work done is to place the request in the Service Bus Queue, which will be picked up by 
    /// any running backend workers.
    /// </summary>
    public class PaymentSyncService
    {
        public const string QueueName = "Mcb-PaymentQueue";
        public const string MessagePrefix = "PaymentSyncService/";

        /// <summary>
        /// Submits a request for payments to be sync'ed from the motor club for the specified accountId.
        /// </summary>
        /// <param name="accountId"></param>
        public async Async.Task RequestPaymentSync(PaymentQueueItem pqi)
        {
            pqi.Save();
            PaymentQueueItem.UpdateStatus(pqi, PaymentQueueItemStatus.Waiting);

            var x = Stopwatch.StartNew();

            Dictionary<string, object> props = new Dictionary<string, object>();

            props.Add("id", pqi.Id);
            props.Add("accountId", pqi.AccountId);
            props.Add("companyId", pqi.CompanyId);
            props.Add("requestCreated", pqi.CreateDate);

            await ServiceBusHelper.SendMessageAsync("Mcb-PaymentQueue", "PaymentSyncService/" + pqi.Id, pqi, "Motor Club Request Payment Sync Submission", props);

            Console.WriteLine("zz>" + x.ElapsedMilliseconds + "ms elapsed while adding to paymentsync queue");

        }
    }
}
