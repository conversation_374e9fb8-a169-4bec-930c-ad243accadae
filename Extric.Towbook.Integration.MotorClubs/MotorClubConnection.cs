using Extric.Towbook.Utility;
using HtmlAgilityPack;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using System.Text.RegularExpressions;
using NLog;
using System.IO.Compression;
using System.Globalization;

namespace Extric.Towbook.Integration.MotorClubs
{
    public abstract class MotorClubConnection
    {
        protected string viewState = string.Empty;
        protected string eventValidation = string.Empty;
        protected const string MSG_ORIGINAL_FORMAT_CHANGED = "Couldn't parse table rows, maybe the original format was changed";
        protected DateTime Expiry { get; } = DateTime.Now.AddMinutes(30);

        protected CookieContainer cookieContainer;
        
        public string Username { get; protected set; }
        public string Password { get; protected set; }
        public MotorClubTaskType Type { get; protected set; }
        protected virtual string LoginUrl { get; set; }

        public bool LogHttpTraffic { get; set; } = false;
        public static Logger logger = LogManager.GetCurrentClassLogger();
        public CookieContainer GetCookies()
        {
            return cookieContainer;
        }
         
        protected MotorClubConnection(string username, string password)
        {
            this.Username = username;
            this.Password = password;

            this.cookieContainer = new CookieContainer();
        }

        protected MotorClubConnection(string username, string password, MotorClubTaskType type)
        {
            this.Username = username;
            this.Password = password;
            this.Type = type;

            this.cookieContainer = new CookieContainer();
        }

        protected HtmlDocument Get(LogInfo logInfo, string url)
        {
            var log = new AutomatedHttpLog();
            var html = WebRequestHelper.GetHtml(url, null, ref this.cookieContainer, ref log);
            LogHttpSession(log, logInfo);

            return html;
        }

        protected HtmlDocument Post(LogInfo logInfo, string url, ref Dictionary<string, string> formData, bool testMode = false, bool includeDisabled = true)
        {
            var log = new AutomatedHttpLog();
            var html = WebRequestHelper.GetHtml(url, formData, ref this.cookieContainer, ref log, testMode);
            LogHttpSession(log, logInfo);

            if (!testMode) // if testMode == true, you won't get a response so there won't be any formData in the html
            {
                formData = GetFormFields(html, "", "", includeDisabled);
            }

            return html;
        }

        protected HtmlDocument PostMultipart(LogInfo logInfo, string url, ref List<KeyValuePair<string, string>> formData, List<KeyValuePair<string, WebRequestHelper.FormFieldFile>> files = null, bool testMode = false, bool includeDisabled = true)
        {
            var log = new AutomatedHttpLog();
            var html = WebRequestHelper.GetHtmlMultipart(url, formData, files, ref this.cookieContainer, ref log, testMode);
            LogHttpSession(log, logInfo);

            if (!testMode) // if testMode == true, you won't get a response so there won't be any formData in the html
            {
                formData = GetFormFields(html, "", "", includeDisabled).ToList();
            }

            return html;
        }

        public static Dictionary<string, string> GetFormFields(HtmlDocument html, string formId = "", string formName = "", bool includeDisabled = true)
        {
            return new FormElementCollection(html, formId, formName, includeDisabled);
        }

        public static Dictionary<string, string> UpdateFormFields(Dictionary<string, string> f1, HtmlDocument html)
        {
            var f2 = GetFormFields(html);

            foreach (var d in f2)
                f1[d.Key] = d.Value;

            return f1;
        }

        public void LogHttpSession(AutomatedHttpLog log, LogInfo logInfo = null)
        {
            if (LogHttpTraffic)
            {
                // Get rid of VIEWSTATE and EVENTVALIDATION prior to logging
                var requestBody = log.RequestBody ?? "";
                var responseBody = log.ResponseBody ?? "";
                requestBody = Regex.Replace(requestBody, "(?<=(__VIEWSTATE)=)[^\\&]*", "", RegexOptions.IgnoreCase);
                requestBody = Regex.Replace(requestBody, "(?<=(__EVENTVALIDATION)=)[^\\&]*", "", RegexOptions.IgnoreCase);
                responseBody = Regex.Replace(responseBody, "(?<=<input[^>]*id=\"(__VIEWSTATE)\"[^>]*value=\")[^\"]*", "", RegexOptions.IgnoreCase);
                responseBody = Regex.Replace(responseBody, "(?<=<input[^>]*id=\"(__EVENTVALIDATION)\"[^>]*value=\")[^\"]*", "", RegexOptions.IgnoreCase);

                var properties = new Dictionary<object, object>
                {
                    ["RequestTime"] = log.RequestTime,
                    ["RequestEnd"] = log.RequestEnd,
                    ["RequestUrl"] = log.RequestUrl,
                    ["MachineIp"] = log.MachineIp,
                    ["RequestHeaders"] = log.RequestHeaders,
                    ["ResponseHeaders"] = log.ResponseHeaders,
                    ["RequestBody"] = Compress(requestBody),
                    ["ResponseBody"] = Compress(responseBody),
                    ["ResponseCode"] = log.ResponseCode,
                    ["ResponseCodeDescription"] = log.ResponseCodeDescription,
                };

                if (logInfo == null)
                    logger.LogEvent("HTTP Session", 0, LogLevel.Debug, properties);
                else
                    logInfo.AddEvent("HTTP Session", LogLevel.Debug, properties);
            }
        }

        public void LogEvent(LogInfo logInfo, string message, LogLevel logLevel, Dictionary<object, object> properties = null)
        {
            logger.LogEvent(logInfo.Prefix(message), logInfo.CompanyId, logLevel, logInfo.GetProperties(properties));
        }

        public string PrettyJSON(string message)
        {
            var output = "";

            bool insideString = false;
            int nesting = 0;
            char previousChar = ' ';

            for (int i = 0; i < message.Length; i++)
            {
                char c = message[i];
                var nextChar = (i < message.Length - 1 ? message[i + 1] : ' ');

                if (insideString && previousChar == '"' && (c == ',' || c == ':' || c == '}' || c == ']'))
                {
                    insideString = false;
                }

                if (!insideString)
                {
                    if (c == '"')
                    {
                        insideString = true;
                        output += c;
                    }
                    else if (c == ',')
                    {
                        if (previousChar == '}' && nextChar == '{')
                        {
                            output += c;
                        }
                        else if (nesting > 0)
                        {
                            output += ",\n";
                            output += Tabs(nesting);
                        }
                        else
                        {
                            output += ",\n";
                        }
                    }
                    else if (c == ' ' && previousChar == ',')
                    {
                        // Ignore this space
                    }
                    else if (previousChar != '\\')
                    {
                        if (c == '{')
                        {
                            output += "{\n";
                            output += Tabs(++nesting);
                        }
                        else if (c == '}')
                        {
                            output += "\n";
                            output += Tabs(--nesting);
                            output += "}";
                        }
                        else if (c == '[' && nesting == 0)
                        {
                            output += c;
                        }
                        else output += c;
                    }
                    else output += c;
                }
                else output += c;

                previousChar = c;
            }

            return output;
        }

        private string Tabs(int chars, string tabChar = "  ")
        {
            var s = "";
            for (int i = 0; i < chars; i++)
            {
                s += tabChar;
            }
            return s;
        }

        private static string Compress(string text)
        {
            byte[] buffer = Encoding.UTF8.GetBytes(text);
            MemoryStream ms = new MemoryStream();
            using (GZipStream zip = new GZipStream(ms, CompressionMode.Compress, true))
            {
                zip.Write(buffer, 0, buffer.Length);
            }

            ms.Position = 0;
            MemoryStream outStream = new MemoryStream();

            byte[] compressed = new byte[ms.Length];
            ms.Read(compressed, 0, compressed.Length);

            byte[] gzBuffer = new byte[compressed.Length + 4];
            System.Buffer.BlockCopy(compressed, 0, gzBuffer, 4, compressed.Length);
            System.Buffer.BlockCopy(BitConverter.GetBytes(buffer.Length), 0, gzBuffer, 0, 4);
            return Convert.ToBase64String(gzBuffer);
        }

        public static bool GetBool(string str)
        {
            bool val;
            bool.TryParse(str ?? "", out val);
            return val;
        }

        public static bool? GetNullableBool(string str, bool coerceNulls = false)
        {
            bool val;

            if (bool.TryParse(str ?? "", out val))
                return val;
            else if (coerceNulls)
                return false;
            else
                return null;
        }

        public static int GetInt(string str, IFormatProvider formatProvider = null)
        {
            if (formatProvider == null)
                formatProvider = CultureInfo.GetCultureInfo("en-US");

            int val;
            int.TryParse(str ?? "", NumberStyles.Any, formatProvider, out val);
            return val;
        }

        public static int? GetNullableInt(string str, bool coerceNulls = false)
        {
            var formatProvider = CultureInfo.GetCultureInfo("en-US");
            int val;

            if (int.TryParse(str ?? "", NumberStyles.Any, formatProvider, out val))
                return val;
            else if (coerceNulls)
                return 0;
            else
                return null;
        }

        public static decimal GetDecimal(string str, IFormatProvider formatProvider = null)
        {
            if (formatProvider == null)
                formatProvider = CultureInfo.GetCultureInfo("en-US");

            decimal val;
            decimal.TryParse(str ?? "", NumberStyles.Any, formatProvider, out val);
            return val;
        }

        public static decimal? GetNullableDecimal(string str, bool coerceNulls = false)
        {
            var formatProvider = CultureInfo.GetCultureInfo("en-US");
            decimal val;

            if (decimal.TryParse(str ?? "", NumberStyles.Any, formatProvider, out val))
                return val;
            else if (coerceNulls)
                return 0m;
            else
                return null;
        }

        public static DateTime GetDateTime(string str, IFormatProvider formatProvider = null)
        {
            if (formatProvider == null)
                formatProvider = CultureInfo.GetCultureInfo("en-US");

            DateTime val;
            DateTime.TryParse(str ?? "", formatProvider, DateTimeStyles.None, out val);  // Returns DateTime.MinValue if failed
            return val;
        }

        public static DateTime? GetNullableDateTime(string str, bool coerceNulls = false)
        {
            var formatProvider = CultureInfo.GetCultureInfo("en-US");
            DateTime val;

            if (DateTime.TryParse(str ?? "", formatProvider, DateTimeStyles.None, out val))
                return val;
            else if (coerceNulls)
                return DateTime.MinValue;
            else
                return null;
        }
    }

    public class LogInfo
    {
        public int ServiceType { get; set; }
        public string MessageId { get; set; }
        public int CompanyId { get; set; }
        public int AccountId { get; set; }
        public bool TestMode { get; set; }
        public string MotorClubName { get; set; }
        public string PoNumber { get; set; }
        public int CallNumber { get; set; }
        public int DispatchEntryId { get; set; }
        public bool FlushRequested { get; set; }
        public string SecondaryPrefix { get; set; }

        public List<LogInfoEvent> Events { get; set; } = new List<LogInfoEvent>();

        private int _index = 0;

        public LogInfo(string messageId, int companyId, bool testMode, string motorClubName = "Motor Club")
        {
            MessageId = messageId;
            CompanyId = companyId;
            TestMode = testMode;
            MotorClubName = motorClubName;
        }

        public string Prefix(string message)
        {
            var secondaryPrefix = (string.IsNullOrWhiteSpace(SecondaryPrefix) ? "" : "-" + SecondaryPrefix);

            return $"{MessageId}-{_index++:00}{secondaryPrefix}: {message}";
        }

        public Dictionary<object, object> GetProperties(Dictionary<object, object> appendTo = null)
        {
            var properties = appendTo ?? new Dictionary<object, object>();

            //if (!properties.ContainsKey("serviceType"))
            //    properties.Add("serviceType", ServiceType);

            //if (!properties.ContainsKey("companyId"))
            //    properties.Add("companyId", CompanyId);

            //if (!properties.ContainsKey("accountId") && AccountId > 0)
            //    properties.Add("accountId", AccountId);

            //if (!properties.ContainsKey("dispatchEntryId") && DispatchEntryId > 0)
            //    properties.Add("dispatchEntryId", DispatchEntryId);

            if (!properties.ContainsKey("masterAccountName") && !string.IsNullOrWhiteSpace(MotorClubName))
                properties.Add("masterAccountName", MotorClubName);

            //if (!properties.ContainsKey("poNumber") && !string.IsNullOrWhiteSpace(PoNumber))
            //    properties.Add("poNumber", PoNumber);

            //if (!properties.ContainsKey("callNumber") && CallNumber > 0)
            //    properties.Add("callNumber", CallNumber);

            //if (!properties.ContainsKey("testMode"))
            //    properties.Add("testMode", TestMode);

            return properties;
        }

        public void AddEvent(string message, LogLevel level, Dictionary<object, object> properties = null)
        {
            Events.Add(new LogInfoEvent()
            {
                Message = Prefix(message),
                Level = level,
                Properties = properties
            });
        }

        public void FlushEvents(Logger logger)
        {
            foreach (var ev in Events)
            {
                logger.LogEvent(ev.Message, CompanyId, ev.Level, GetProperties(ev.Properties));
            }

            Events.Clear();
            FlushRequested = false;
        }
    }

    public class LogInfoEvent
    {
        public string Message { get; set; }
        public LogLevel Level { get; set; }
        public Dictionary<object, object> Properties { get; set; }
    }
}
