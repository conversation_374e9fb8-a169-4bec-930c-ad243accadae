using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Extric.Towbook.Integration.MotorClubs.Billing.Agero
{
    public class Check
    {
        public string CheckNumber { get; set; }
        public int VendorId { get; set; }
        public DateTime CheckDate { get; set; }
        public decimal Amount { get; set; }
        public int Claims { get; set; }
        public CheckStatus Status { get; set; }
        public string Comments { get; set; }
        public List<PurchaseOrder> PurchaseOrders { get; set; }
    }

    public enum CheckStatus
    {
        None = 0,
        Pending = 1,
        Paid = 2
    }
}
