using Extric.Towbook.Utility;
using HtmlAgilityPack;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Text.RegularExpressions;
using Newtonsoft.Json;
using NLog;
using System.Diagnostics;
using System.Threading;

namespace Extric.Towbook.Integration.MotorClubs.Billing.Quest
{
    public class QuestConnection : MotorClubConnection
    {
        public string ProviderId { get; set; }
        private ConnectionKeyModel _connKey;
        private const int maxSessionsPerLogin = 4;
        private static readonly Guid machineId = Guid.NewGuid();

        protected override string LoginUrl
        {
            get
            {
                return @"https://secure.questsoftware.com/qts/VenLogin.jsp";
            }
            set
            {
                base.LoginUrl = value;
            }
        }

        private QuestConnection(string username, string password, ConnectionKeyModel connKey)
            : base(username, password)
        {
            _connKey = connKey;
        }

        private static ConcurrentDictionary<string, QuestConnection> connections
                 = new ConcurrentDictionary<string, QuestConnection>();

        private static ConcurrentDictionary<string, ExpiringPurchaseOrderList> availablePOs
                 = new ConcurrentDictionary<string, ExpiringPurchaseOrderList>();

        /// <summary>
        /// Login to Quest
        /// </summary>
        /// <param name="logInfo"></param>
        /// <param name="username"></param>
        /// <param name="password"></param>
        /// <param name="providerId"></param>
        /// <returns></returns>
        public static QuestConnection Login(LogInfo logInfo, string username, string password, string providerId)
        {
            if (string.IsNullOrWhiteSpace(username))
                throw new InvalidLoginException("", "Username is empty.");

            if (string.IsNullOrWhiteSpace(password))
                throw new InvalidLoginException("", "Password is empty.");

            if (string.IsNullOrWhiteSpace(providerId))
                throw new InvalidLoginException("", "ProviderId is empty and is required for Quest to login.");

            var connKey = GetConnectionKey(logInfo, username, password, providerId);
            var conn = new QuestConnection(username, password, connKey);

            try
            {
                var cacheKey = connKey.ToString();

                if (connections.TryGetValue(cacheKey, out conn))
                {
                    if (conn.Expiry > DateTime.Now)
                    {
                        conn._connKey = connKey;
                        return conn;
                    }
                    else
                        connections.TryRemove(cacheKey, out conn);
                }

                var tries = 0;
                do
                {
                    try
                    {
                        tries++;

                        conn = new QuestConnection(username, password, connKey);
                        conn.ProviderId = providerId;
                        conn.LogHttpTraffic = true;

                        logInfo.AddEvent($"Logging into Quest, connKey: {connKey}", LogLevel.Info);

                        var html = conn.Get(logInfo, conn.LoginUrl);
                        var node = html.DocumentNode.SelectSingleNode("//input[@id='venNumber']");
                        if (node == null)
                            throw new InvalidLoginException("", "Unable to access Quest login page");

                        var formData = new Dictionary<string, string>
                        {
                            {"venNumber", conn.ProviderId},
                            {"turUserName", conn.Username},
                            {"turPassword", conn.Password},
                        };

                        html = conn.Post(logInfo, conn.LoginUrl.Replace(".jsp", "") + "?action=venLogin", ref formData);

                        if (!html.DocumentNode.InnerHtml.Contains("VenMenu.jsp"))
                            throw new InvalidLoginException();

                        connections.TryAdd(cacheKey, conn);

                        return conn;
                    }
                    catch (InvalidLoginException)
                    {
                        throw;
                    }
                    catch (Exception e)
                    {
                        logInfo.AddEvent($"Retrying login, attempt #{tries} failed because of: {e.Message}", LogLevel.Warn);
                    }
                }
                while (tries < 4);

                throw new Exception("Error while attempting to connect to Quest.  Please try resubmitting the invoice.");
            }
            catch
            {
                conn.ReleaseLock(logInfo);
                throw;
            }
        }

        public List<PurchaseOrderItem> GetAvailablePOs(LogInfo logInfo)
        {
            ExpiringPurchaseOrderList pos = null;

            string key = ProviderId + ":" + Username + ":" + Password;

            if (availablePOs.TryGetValue(key, out pos))
            {
                if (pos.Expiry > DateTime.Now)
                    return pos;
                else
                    availablePOs.TryRemove(key, out pos);
            }

            pos = new ExpiringPurchaseOrderList();

            // Load the "Enter Invoices" page
            var url = "https://secure.questsoftware.com/qts/VenSO?action=viewAllNew";
            var html = Get(logInfo, url);

            // If there are no po's listed, return empty list
            if (html.DocumentNode.InnerHtml.Contains("There are no records currently in the search list"))
                return pos;

            // Get the PO's on it
            var rows = html.DocumentNode.SelectNodes("//table[@id='vendorServiceOrderList'] //tr");
            if (rows != null && rows.Count > 0)
            {
                // For each row
                foreach (HtmlNode row in rows)
                {
                    // Get its cells
                    var cells = row.SelectNodes("td");
                    if (cells != null && cells.Count > 0)
                    {
                        // Construct a PO from the info in this row
                        var po = new PurchaseOrderItem();

                        // Get values
                        po.PurchaseOrderNumber = HtmlToText.GetString(cells[0]);
                        po.MemberNumber = HtmlToText.GetString(cells[1]);
                        po.MemberName = HtmlToText.GetString(cells[2]);
                        po.DateOfService = HtmlToText.GetString(cells[3]);
                        po.Status = HtmlToText.GetString(cells[4]);

                        pos.Add(po);
                    }
                }
            }

            availablePOs.TryAdd(key, pos);

            return pos;
        }

        public PurchaseOrder GetPurchaseOrder(LogInfo logInfo, string poNumber, out HtmlDocument html)
        {
            var po = new PurchaseOrder();
            var processed = 0;
            do
            {
                processed++;

                // Fix to enable find input elements on nested divs inside the form element.
                // and ALSO to not drop the </option> end tags... 
                HtmlNode.ElementsFlags.Remove("form");
                HtmlNode.ElementsFlags.Remove("option");
                HtmlNode.ElementsFlags.Remove("tr");
                HtmlNode.ElementsFlags.Remove("td");

                // Get the PO
                var url = $"https://secure.questsoftware.com/qts/ServiceOrderHandler?action=venLoadSO&sorNumber={poNumber}&invStatus=New";
                html = Get(logInfo, url);

                if (html.DocumentNode.InnerHtml.Contains("Not able to load ISC #") ||
                    html.DocumentNode.InnerHtml.Contains("Unrecoverable Error in Handler"))
                {
                    return null;
                }

                // Get the 'edit' page
                url = $"https://secure.questsoftware.com/qts/VenInvoice?action=createInv&sorNumber={poNumber}";
                html = Get(logInfo, url);

                // Ensure we don't get redirected to main menu because it's already been submitted
                if (html.DocumentNode.InnerHtml.Contains("The invoicing for this Service Order has already been submitted."))
                {
                    throw new CancelledException($"Quest says 'Already been submitted'");
                }

                // Ensure it has form elements on it
                var node = html.DocumentNode.SelectSingleNode("//input[@id='sorNumber']");
                if (node == null)
                    return null;

                var form = GetFormFields(html);

                // General
                po.PurchaseOrderNumber = GetInt(HtmlToText.GetString(form["sorNumber"]));
                po.SorPolicyNumber = HtmlToText.GetString(form["sorPolicyNumber"]);
                po.SorPolFirstName = HtmlToText.GetString(form["sorPolFirstName"]);
                po.SorPolLastName = HtmlToText.GetString(form["sorPolLastName"]);
                po.SorCallStart = HtmlToText.GetString(form["sorCallStart"]);
                po.VIN = HtmlToText.GetString(form["invVehVin"]);
                po.InvoiceNumber = HtmlToText.GetString(form["invVenInvoice"]);
                po.InvoiceTax = GetDecimal(HtmlToText.GetString(form["invTax"]));

                // Available charges
                po.AvailableCharges.Clear();
                html.DocumentNode.SelectNodes("//select[@id='lineServiceNum']/option")?.ToList().ForEach(n =>
                {
                    po.AvailableCharges.Add(new InvoiceCharge()
                    {
                        ServiceNum = GetInt(n.Attributes["value"].Value),
                        Name = n.InnerText.Trim(),
                    });
                });

                // Invoice charges
                var removed = 0;
                int removeLineIndex = -1;
                do
                {
                    // If we found a line needing to be removed
                    if (removeLineIndex > -1)
                    {
                        // Remove it
                        html = Post(logInfo, "https://secure.questsoftware.com/qts/VenInvoice?action=removeLineItem&sequence=" + removeLineIndex, ref form);
                        removed++;
                        removeLineIndex = -1;
                    }

                    po.InvoiceCharges = GetInvoiceCharges(html, po, ref removeLineIndex);
                }
                while (removeLineIndex > -1 && removed < 6);

                // If we removed some line(s)
                if (removed > 0)
                {
                    // Save the invoice
                    html = Post(logInfo, "https://secure.questsoftware.com/qts/VenInvoice?action=saveInv", ref form);
                    processed++;
                }
            }
            while (processed == 2);

            return po;
        }

        public decimal SubmitPurchaseOrder(LogInfo logInfo, PurchaseOrder po, HtmlDocument html)
        {
            var formData = GetFormFields(html);

            // Add charges
            foreach (var charge in po.InvoiceCharges.Where(c => !c.PrePopulated))
            {
                formData["lineServiceNum"] = $"{charge.ServiceNum}";
                formData["linQty"] = $"{charge.Quantity:0.00}";
                formData["linAmount"] = $"{charge.Amount:0.00}";

                html = Post(logInfo, "https://secure.questsoftware.com/qts/VenInvoice?action=addLineItem", ref formData);

                if (charge.Name.Contains("MTV"))
                {
                    // If there is Included miles now on the invoice, and they are > towbook's free miles, deduct from Towbook's total
                    var removeLineIndex = -1;
                    var charges = GetInvoiceCharges(html, po, ref removeLineIndex);
                    var chg = charges.FirstOrDefault(c => c.Name.Contains("Included") && c.Name.Contains("MTV"));
                    if (chg != null)
                    {
                        var diff1 = Math.Abs(chg.Amount ?? 0) - charge.TowbookFreeMilesTotal;
                        if (diff1 > 0)
                            po.ExpectedTotal -= diff1;
                    }
                }

                if (charge.Name.Contains("Loaded Mileage"))
                {
                    // If there is Included miles now on the invoice, and they are > towbook's free miles, deduct from Towbook's total
                    var removeLineIndex = -1;
                    var charges = GetInvoiceCharges(html, po, ref removeLineIndex);
                    var chg = charges.FirstOrDefault(c => c.Name.Contains("Included") && c.Name.Contains("Loaded Mileage"));
                    if (chg != null)
                    {
                        var diff1 = Math.Abs(chg.Amount ?? 0) - charge.TowbookFreeMilesTotal;
                        if (diff1 > 0)
                            po.ExpectedTotal -= diff1;
                    }
                }
            }

            // Add tax, vin, call #
            formData["invVehVin"] = $"{po.VIN}";
            formData["invVenInvoice"] = $"{po.InvoiceNumber}";
            formData["invTax"] = $"{po.InvoiceTax:0.00}";

            // Recalculate Amount
            html = Post(logInfo, "https://secure.questsoftware.com/qts/VenInvoice?action=recalcLineItems", ref formData);

            // Try to parse the total Quest calculated for this PO
            var questTotal = CalcInvoiceTotal(html);
            if (questTotal == 0m) 
                throw new BillingException("Unable to review the invoice prior to submission.  Please try submitting manually.");

            // If the total in Towbook > total in Quest (and greater than .05 to offset for Quest rounding mileage DOWN)
            var diff = po.ExpectedTotal - questTotal;
            if (diff > 0.05m)
            {
                // Insert the difference as an add'l amount
                formData["lineServiceNum"] = "1004";
                formData["linQty"] = $"{(diff):0.00}";
                formData["linAmount"] = "1.00";

                html = Post(logInfo, "https://secure.questsoftware.com/qts/VenInvoice?action=addLineItem", ref formData);

                // Try to parse the total Quest calculated for this PO
                questTotal = CalcInvoiceTotal(html);
                if (questTotal == 0m) 
                    throw new BillingException("Unable to review the invoice prior to submission.  Please try submitting manually.");
            }

            // If Quest's total is not between 90% and 150% of Towbook's total, throw exception
            if (questTotal < po.ExpectedTotal * .9m)
            {
                // If it's because we've exceeded the member limit
                var node = html.DocumentNode.SelectSingleNode("//td[contains(text(),'Mbr Limit')]");
                if (node != null)
                    throw new UserErrorException($"Quest will only pay {questTotal:c} for this invoice due to member limits.  Either adjust the amount in Towbook and resubmit it, or bill it manually.");
                else
                    throw new BillingException($"Quest total of {questTotal:0.00} is significantly less than the expected total of {po.ExpectedTotal:0.00}");
            }
            else if (questTotal > po.ExpectedTotal * 1.5m)
            {
                throw new BillingException($"Quest total of {questTotal:0.00} is significantly more than the expected total of {po.ExpectedTotal:0.00}");
            }
            else
            {
                // Log totals
                logInfo.AddEvent($"Quest -- Total: \t{questTotal:0.00}\t Expected: \t{po.ExpectedTotal:0.00}\t Towbook: \t{po.TowbookTotal:0.00}", LogLevel.Info);

                // If totals are too divergent, request logs be flushed at end of processing
                if (Math.Abs(questTotal - po.ExpectedTotal) > 5 ||
                    Math.Abs(questTotal - po.TowbookTotal) > 10)
                {
                    logInfo.FlushRequested = true;
                }
            }

            // Submit the PO
            if (logInfo.TestMode)
            {
                throw new BillingException("TestMode successful; final submission bypassed");
            }
            else
            {
                html = Post(logInfo, "https://secure.questsoftware.com/qts/VenInvoice?action=updateInv", ref formData);

                // If there are lines that Quest is prompting us to auto-correct (run no more than 5 times)
                var autoCorrectCount = 0;
                HtmlNode node = null;
                do
                {
                    node = html.DocumentNode.SelectSingleNode("//input[@value='Auto Correct']");
                    if (node != null)
                    {
                        var indices = node.Attributes["onClick"].Value.Replace("autoCorrectLineItem(", "").Replace(");", "").Split(',');
                        if (indices.Length == 2)
                        {
                            var linSeq = indices[0];
                            var errNum = indices[1];
                            html = Post(logInfo, "https://secure.questsoftware.com/qts/VenInvoice?action=autoCorrectLineItem&linSeq=" + linSeq + "&errNum=" + errNum, ref formData);
                            autoCorrectCount++;
                        }
                    }
                }
                while (node != null && autoCorrectCount < 5);

                // If we auto-corrected anything
                if (autoCorrectCount > 0)
                {
                    // Try to parse the total Quest calculated for this PO
                    // just so we have a record of the final amount being submitted after auto-correcting
                    questTotal = CalcInvoiceTotal(html);

                    // Resubmit the PO
                    html = Post(logInfo, "https://secure.questsoftware.com/qts/VenInvoice?action=updateInv", ref formData);
                }

                // If the page doesn't show a success message
                if (!html.DocumentNode.InnerHtml.Contains("has been Submitted"))
                {
                    throw new BillingException("Quest wouldn't accept the submission because of unknown errors.");
                }
            }

            return questTotal;
        }

        public List<PaymentStatement> GetPaymentStatements(LogInfo logInfo, string poNumber, DateTime startDate, DateTime endDate, bool retrieveDetails = false)
        {
            var payments = new List<PaymentStatement>();
            var url = @"https://secure.questsoftware.com/qts/StatementHandler";
            var html = Get(logInfo, url + "?action=viewPayments");
            var form = GetFormFields(html);

            form["searchSONumber"] = poNumber;
            form["startDateD1"] = startDate.Month.ToString();
            form["startDateD2"] = startDate.Day.ToString();
            form["startDateD3"] = startDate.Year.ToString();
            form["startDate"] = startDate.ToString("MM/dd/yyyy");
            form["endDateD1"] = endDate.Month.ToString();
            form["endDateD2"] = endDate.Day.ToString();
            form["endDateD3"] = endDate.Year.ToString();
            form["endDate"] = endDate.ToString("MM/dd/yyyy");

            html = Post(logInfo, url, ref form);

            var table = html.DocumentNode.SelectSingleNode("//table[@id='paymentlistresults']");
            if (table != null)
            {
                var rows = table.SelectNodes("tr[@class='paymentsearchjsp']");
                if (rows != null && rows.Count >= 1)
                {
                    foreach (HtmlNode row in rows)
                    {
                        var cols = row.SelectNodes("td");

                        var paymentItem = new PaymentStatement()
                        {
                            Type = cols[0].InnerText,
                            Number = cols[1].InnerText,
                            Date = GetDateTime(cols[2].InnerText),
                            Amount = Convert.ToDecimal(cols[3].InnerText.Replace("$", "").Trim())
                        };

                        if (retrieveDetails)
                            paymentItem.PaymentDetails = GetPaymentDetails(logInfo, paymentItem.Number, paymentItem.Type);

                        payments.Add(paymentItem);
                    }
                }
            }

            return payments;
        }

        public List<PaymentStatementDetail> GetPaymentDetails(LogInfo logInfo, string number, string type, int payeeCount = 1)
        {
            var paymentDetails = new List<PaymentStatementDetail>();

            var url = String.Format(
                @"https://secure.questsoftware.com/qts/StatementHandler?action={0}&payNum={1}&venNum={2}&payType={3}&payeeCount={4}",
                "loadStatement",
                number,
                ProviderId,
                type,
                payeeCount
            );

            var html = Get(logInfo, url);
            var tables = html.DocumentNode.SelectNodes("//table/tr/td/table");
            if (tables.Count > 1)
            {
                var rows = tables[1].SelectNodes("tr");

                for (var i = 2; i < rows.Count - 2; i++)
                {
                    var cols = rows[i].SelectNodes("td");
                    var detail = new PaymentStatementDetail()
                    {
                        InsuredName = cols[0].InnerText,
                        PolicyNumber = cols[1].InnerText,
                        PurchaseOrderNumber = cols[2].InnerText,
                        VendorInv = cols[3].InnerText,
                        InvoicedAmt = Convert.ToDecimal(cols[4].InnerText.Replace("$", "")),
                        AdjustmentAmt = Convert.ToDecimal(cols[5].InnerText.Replace("$", "")),
                        PaymentAmt = Convert.ToDecimal(cols[6].InnerText.Replace("$", ""))
                    };

                    paymentDetails.Add(detail);
                }
            }

            return paymentDetails;
        }

        public void ReleaseLock(LogInfo logInfo)
        {
            logInfo.AddEvent($"Disposing LockId: {_connKey.LockId.Value}, connKey: {_connKey}", LogLevel.Info);
            DistributedLock.ForceUnlock(_connKey.LockId.Value);
            logInfo.SecondaryPrefix = "";
        }

        #region Private Methods

        private class ConnectionKeyModel
        {
            public Guid Machine;
            public string UserName;
            public string Password;
            public string ProviderId;
            public int SessionNumber;
            public Guid? Code;
            public long? LockId;

            public override string ToString()
            {
                var x = new
                {
                    m = Machine,
                    u = UserName,
                    pv = ProviderId,
                    s = SessionNumber,
                };

                return x.ToJson();
            }
        }

        private static ConnectionKeyModel GetConnectionKey(LogInfo logInfo, string username, string password, string providerId)
        {
            var connKey = new ConnectionKeyModel() { UserName = username, Password = password, ProviderId = providerId, Machine = machineId };
            var s = Stopwatch.StartNew();

            // Try 900 times
            for (int x = 0; x < 900; x++)
            {
                // To get a lock on one of the available sessions
                for (int i = 0; i < maxSessionsPerLogin; i++)
                {
                    connKey.SessionNumber = i;

                    var lockRow = DistributedLock.GetLock("QuestConnection", connKey.ToString());
                    if (lockRow == null) continue;

                    if (lockRow.Code != Guid.Empty)
                    {
                        s.Stop();
                        connKey.Code = lockRow.Code;
                        connKey.LockId = lockRow.DistributedLockId;
                        logInfo.SecondaryPrefix = $"S{connKey.SessionNumber}-T{Thread.CurrentThread.ManagedThreadId:00}";

                        logInfo.AddEvent($"Acquired LockId: {connKey.LockId.Value} after {s.Elapsed.TotalSeconds:0.0}s, connKey: {connKey}", LogLevel.Info);
                        return connKey;
                    }
                    else if (lockRow.CreateDate < DateTime.Now.ToUniversalTime().AddSeconds(-360))
                    {
                        // Release expired locks
                        logInfo.AddEvent($"Releasing Expired LockId: {lockRow.DistributedLockId}, connKey: {lockRow.Value}", LogLevel.Info);
                        DistributedLock.ForceUnlock(lockRow.DistributedLockId);
                    }
                }

                // Sleep before trying again
                Thread.Sleep(200);
            }

            s.Stop();
            throw new Exception($"Couldn't acquire lock after {s.Elapsed.TotalSeconds:0.0}s");
        }

        private static List<InvoiceCharge> GetInvoiceCharges(HtmlDocument html, PurchaseOrder po, ref int removeLineIndex)
        {
            var charges = new List<InvoiceCharge>();

            var nodes = html.DocumentNode.SelectNodes("//td[@class='editgriddata']");
            if (nodes != null && nodes.Count > 0)
            {
                var charge = new InvoiceCharge()
                {
                    PrePopulated = true
                };

                // For each of the editgriddata nodes
                for (int i = 0; i < nodes.Count; i++)
                {
                    // If we hit the editgriddata containing 'lineServiceNum'
                    // we're past the section containing invoice charges, so exit loop
                    var node = nodes[i].SelectSingleNode("select[@id='lineServiceNum']");
                    if (node != null)
                    {
                        break;
                    }

                    // Every three nodes add another charge
                    node = nodes[i].SelectSingleNode("input");
                    if (node != null)
                    {
                        // First node contains name
                        if (i % 3 == 0)
                        {
                            charge.Name = node.Attributes["value"].Value;

                            // Try to get its service num from the list of available charges
                            charge.ServiceNum = po.GetChargeByName(charge.Name)?.ServiceNum;
                        }

                        // Second node contains quantity
                        if (i % 3 == 1)
                        {
                            charge.Quantity = GetNullableDecimal(node.Attributes["value"].Value, true);
                        }

                        // Third node contains amount
                        if (i % 3 == 2)
                        {
                            charge.Amount = GetNullableDecimal(node.Attributes["value"].Value, true);

                            // If this row has a Remove button on it, and its a row we want to remove
                            var removeTd = nodes[i].NextSibling?.NextSibling?.SelectSingleNode("input[@value='Remove']");
                            if (removeTd != null && (charge.Name.Contains("MTV") || charge.Name.Contains("Loaded Mileage") || charge.Name.Contains("Out Of Pocket")))
                            {
                                // Get the line number to remove
                                removeLineIndex = GetInt(removeTd.Attributes["onclick"].Value.Replace("removeLineItem(", "").Replace(");", ""));
                            }

                            charges.Add(charge);
                            charge = new InvoiceCharge()
                            {
                                PrePopulated = true
                            };
                        }
                    }
                }
            }

            return charges;
        }

        private decimal CalcInvoiceTotal(HtmlDocument html)
        {
            var total = 0m;

            var node = html.DocumentNode.SelectSingleNode("//td[@class='editgriddata']//input[@class='editgrid readonly']");
            if (node != null)
            {
                total = GetDecimal(node.Attributes["value"].Value);
            }

            return total;
        }

        #endregion
    }

    public class ExpiringPurchaseOrderList : List<PurchaseOrderItem>
    {
        public DateTime Expiry { get; } = DateTime.Now.AddMinutes(30);
    }
}