using Extric.Towbook.Utility;
using HtmlAgilityPack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
namespace Extric.Towbook.Integration.MotorClubs.Billing.Axis
{
    public class AAAConnection: AxisConnection
    {
        protected override string LoginUrl
        {
            get
            {
                return @"https://contractor.colorado.aaa.com/master/axislink.asp";
            }
            set
            {
                base.LoginUrl = value;
            }
        }

        protected override string GetDetailsUrl
        {
            get
            {
                return @"https://contractor.colorado.aaa.com/master/axislink.asp";
            }
            set
            {
                base.GetDetailsUrl = value;
            }
        }

        private AAAConnection(string username, string pass)
            : base(username, pass)
        {
            
        }

        public static AAAConnection Login(string username, string pass, bool isCanadian = false)
        {
            AAAConnection result = new AAAConnection(username, pass);
            return result.BaseLogin(result.LoginUrl, username, pass) as AAAConnection;
        }

        public override List<AxisCall> GetCalls()
        {
            string url = @"HTTPS://contractor.colorado.aaa.com:443/master/axislink.asp";
            
            List<AxisCall> result = new List<AxisCall>();

            HtmlDocument html = WebRequestHelper.GetHtml(url, new Dictionary<string, string>
            {
                {"TransportURL", url},
                {"BrowserID", this.browserId},
                {"WFApplic", this.wfApplic},
                {"WFTemplate", "GARAGE.CALL.CRITERIA*"},
                {"WFToken", this.wfToken},
                {"GARAGE", this.garage}
            }, ref this.cookieContainer);

            HtmlNodeCollection rows = html.DocumentNode.SelectNodes("//tr[contains(@class, 'ERS_CALL_STATE')]");
            if (rows != null)
            { 
                foreach (HtmlNode row in rows)
                {
                    AxisCall newCall = new AxisCall();

                    newCall.Id = row.ChildNodes[1].InnerText.Replace("&nbsp;", "");
                    newCall.RequestId = row.ChildNodes[1].InnerText.Replace("&nbsp;", "");
                    newCall.RequestDate = DateTime.Now;// ToDo: row.ChildNodes[3].InnerText.Replace("&nbsp;", "");
                    newCall.Garage = row.ChildNodes[4].InnerText.Replace("&nbsp;", "");
                    newCall.Driver = row.ChildNodes[5].InnerText.Replace("&nbsp;", "");
                    newCall.CallState = row.ChildNodes[6].InnerText.Replace("&nbsp;", "");
                    newCall.Status = row.ChildNodes[7].InnerText.Replace("&nbsp;", "");
                    newCall.PickupAddress = row.ChildNodes[8].InnerText.Replace("&nbsp;", "");
                    newCall.Grid = row.ChildNodes[9].InnerText.Replace("&nbsp;", "");
                    newCall.CHG = row.ChildNodes[10].InnerText.Replace("&nbsp;", "");
                    newCall.PickupCrossStreet = row.ChildNodes[11].InnerText.Replace("&nbsp;", "");
                    newCall.Problem = row.ChildNodes[12].InnerText.Replace("&nbsp;", "");
                    newCall.HandlingCode = row.ChildNodes[13].InnerText.Replace("&nbsp;", "");
                    newCall.ClearReason = row.ChildNodes[14].InnerText.Replace("&nbsp;", "");
                    newCall.TotalCost = row.ChildNodes[15].InnerText.Replace("&nbsp;", "");
                    newCall.Key = row.ChildNodes[1].ChildNodes[1].ChildNodes[1].Attributes["name"].Value;
                
                    result.Add(newCall);
                }
            }
            return result;
        }
    }
}
