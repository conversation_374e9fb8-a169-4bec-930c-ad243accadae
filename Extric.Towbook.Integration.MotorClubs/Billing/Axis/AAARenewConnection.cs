using Extric.Towbook.Utility;
using HtmlAgilityPack;
using Microsoft.AspNetCore.WebUtilities;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web;

namespace Extric.Towbook.Integration.MotorClubs.Billing.Axis
{
    public class AAARenewConnection : AxisConnection
    {
        protected string workbook;
        protected string wfpresbckey;

        protected override string LoginUrl
        {
            get
            {
                return @"https://rbpers.newland.ca/jsp_forms/axislink/contractorweb.jsp";
            }
            set
            {
                base.LoginUrl = value;
            }
        }

        protected override string GetDetailsUrl
        {
            get
            {
                return @"https://rbpers.newland.ca/jsp_forms/axislink/contractorweb.jsp";
            }
            set
            {
                base.GetDetailsUrl = value;
            }
        }

        public AAARenewConnection(string username, string password) : base (username, password)
        {

        }

        public static AAARenewConnection Login(string username, string password)
        {
            AAARenewConnection result = new AAARenewConnection(username, password);
            AutomatedHttpLog myLog = new AutomatedHttpLog();
            //return result.BaseLogin(result.LoginUrl, username, password) as AAARenewConnection;

            var url = result.LoginUrl;
            var html = WebRequestHelper.GetHtml(url, null, ref result.cookieContainer);

            if (html.DocumentNode.SelectSingleNode("//input[@id='WFAPPLIC']") != null)
                result.wfApplic = html.DocumentNode.SelectSingleNode("//input[@id='WFAPPLIC']").Attributes["value"].Value;
            if (html.DocumentNode.SelectSingleNode("//input[@id='WFTEMPLATE']") != null)
                result.wfTemplate = html.DocumentNode.SelectSingleNode("//input[@id='WFTEMPLATE']").Attributes["value"].Value;
            if (html.DocumentNode.SelectSingleNode("//input[@id='WORKBOOK']") != null)
                result.workbook = html.DocumentNode.SelectSingleNode("//input[@id='WORKBOOK']").Attributes["value"].Value;
            if (html.DocumentNode.SelectSingleNode("//input[@id='WFVERSION']") != null)
                result.wfVersion = html.DocumentNode.SelectSingleNode("//input[@id='WFVERSION']").Attributes["value"].Value;

            if (html.DocumentNode.SelectSingleNode("//input[@id='wftoken']") != null)
                result.wfToken = html.DocumentNode.SelectSingleNode("//input[@id='wftoken']").Attributes["value"].Value;
            if (html.DocumentNode.SelectSingleNode("//input[@id='transporturl']") != null)
                result.transportUrl = html.DocumentNode.SelectSingleNode("//input[@id='transporturl']").Attributes["value"].Value;
            if (html.DocumentNode.SelectSingleNode("//input[@id='browserid']") != null)
                result.browserId = html.DocumentNode.SelectSingleNode("//input[@id='browserid']").Attributes["value"].Value;

            if (html.DocumentNode.SelectSingleNode("//input[@id='wfpresbckey']") != null)
                result.wfpresbckey = html.DocumentNode.SelectSingleNode("//input[@id='wfpresbckey']").Attributes["value"].Value;
            
            // POST
            html = WebRequestHelper.GetHtml(url, new Dictionary<string, string>
            {
                {"WFApplic", result.wfApplic},
                {"WFTemplate", result.wfTemplate},
                {"Mode", "Entry"},
                {"WFToken", result.wfToken},
                {"User", username},
                {"Password", password},
                {"Login", "Login"}
            }, ref result.cookieContainer);
            
            return result;
        }

        public override List<AxisCall> GetCalls()
        {
            string url = @"https://rbpers.newland.ca/jsp_forms/axislink/contractorweb.jsp";
            AutomatedHttpLog myLog = new AutomatedHttpLog();
            List<AxisCall> result = new List<AxisCall>();

            //HtmlDocument html = WebRequestHelper.GetHtml(url, new Dictionary<string, string>
            //{
            //    {"TransportURL", url},
            //    {"BrowserID", this.browserId},
            //    {"WFApplic", "ERS"},
            //    {"WFTemplate", "GARAGE.CALL.CRITERIA*"},
            //    {"WFToken", this.wfToken},
            //    {"GARAGE", this.garage},
            //    {"PASSWORD", this.Password},
            //    {"MODEL.PREFIX", "CWP"},
            //    {"WFVersion", this.wfVersion},
            //    {"WFPRESBCKEY", ""},
            //    {"Active", "Y"},
            //}, ref this.cookieContainer);

            // GET 
            NameValueCollection queryString = HttpUtility.ParseQueryString(String.Empty);

            wfApplic = "ERS";
            wfVersion = "CWP";
            wfTemplate = "GARAGE.CALL.CRITERIA";

            queryString["WFAPPLIC"] = "ERS"; //result.wfApplic;
            queryString["WFTEMPLATE"] = "GARAGE.CALL.CRITERIA"; //result.wfTemplate;
            queryString["BROWSERID"] = this.browserId;
            queryString["MODEL.PREFIX"] = "CWP";
            queryString["WFPRESBCKEY"] = this.wfpresbckey;
            queryString["WFTOKEN"] = this.wfToken;
            queryString["WFNEWTOKEN"] = "NO";
            queryString["GARAGE"] = this.Username;
            queryString["MODE"] = "";
            queryString["PAGEURL"] = "";
            queryString["PASSWORD"] = this.Password;
            queryString["WFVERSION"] = "CWP";

            url += "?" + queryString.ToString();
            HtmlDocument html = WebRequestHelper.GetHtml(url, null, ref this.cookieContainer, ref myLog);

            HtmlNodeCollection rows = html.DocumentNode.SelectNodes("//tr[contains(@class, 'ERS_CALL_STATE')]");
            if (rows != null)
            {
                foreach (HtmlNode row in rows)
                {
                    AxisCall newCall = new AxisCall();

                    newCall.Id = row.ChildNodes[1].InnerText.Replace("&nbsp;", "").Replace("\r\n", "").Trim();
                    newCall.RequestId = row.ChildNodes[1].InnerText.Replace("&nbsp;", "").Replace("\r\n", "").Trim();

                    if (!string.IsNullOrEmpty(row.ChildNodes[3].InnerText.Replace("&nbsp", " ").Trim()))
                    {
                        // has format: 21 FEB 17
                        var dateParts = row.ChildNodes[3].InnerText.Replace("&nbsp", " ").Trim().Split(' ');
                        var dateText = getMonthNum(dateParts[1]) + "/" + dateParts[0] + "/" + "20" + dateParts[2]; // 02/21/2017
                        
                        newCall.RequestDate = Convert.ToDateTime(dateText);
                    }
                        

                    newCall.Garage = row.ChildNodes[4].InnerText.Replace("&nbsp;", "").Replace("\r\n", "").Trim();
                    newCall.Driver = row.ChildNodes[5].InnerText.Replace("&nbsp;", "").Replace("\r\n", "").Trim();
                    newCall.CallState = row.ChildNodes[6].InnerText.Replace("&nbsp;", "").Replace("\r\n", "").Trim();
                    newCall.Status = row.ChildNodes[7].InnerText.Replace("&nbsp;", "").Replace("\r\n", "").Trim();
                    newCall.PickupAddress = row.ChildNodes[8].InnerText.Replace("&nbsp;", "").Replace("\r\n", "").Trim();
                    newCall.CHG = row.ChildNodes[9].InnerText.Replace("&nbsp;", "").Replace("\r\n", "").Trim();
                    newCall.PickupCrossStreet = row.ChildNodes[10].InnerText.Replace("&nbsp;", "").Replace("\r\n", "").Trim();
                    newCall.Problem = row.ChildNodes[11].InnerText.Replace("&nbsp;", "").Replace("\r\n", "").Trim();
                    newCall.HandlingCode = row.ChildNodes[13].InnerText.Replace("&nbsp;", "").Replace("\r\n", "").Trim();
                    newCall.TotalCost = row.ChildNodes[14].InnerText.Replace("&nbsp;", "").Replace("\r\n", "").Trim();

                    newCall.Key = row.ChildNodes[1].ChildNodes[1].ChildNodes[1].Attributes["name"].Value;

                    result.Add(newCall);
                }
            }

            return result;
        }

        private string getMonthNum(string code)
        {
            switch (code.ToLowerInvariant())
            {
                case "jan": return "01";
                case "feb": return "02";
                case "mar": return "03";
                case "apr": return "04";
                case "may": return "05";
                case "jun": return "06";
                case "jul": return "07";
                case "aug": return "08";
                case "sep": return "09";
                case "oct": return "10";
                case "nov": return "11";
                case "dec": return "12";
                default: return code;
            }
        }
    }
}
