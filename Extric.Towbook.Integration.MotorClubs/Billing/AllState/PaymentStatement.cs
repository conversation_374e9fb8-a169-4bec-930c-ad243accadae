using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Extric.Towbook.Integration.MotorClubs.Billing.Allstate
{
    public class PaymentStatement
    {
        public string Number { get; set; }
        public DateTime Date { get; set; }
        public PaymentStatementStatus Status { get; set; }
        public decimal AmountPaid { get; set; }
        public PaymentStatementType Type { get; set; }
        public List<PaymentStatementDetail> PaymentDetails { get; set; } = new List<PaymentStatementDetail>();

        public static PaymentStatementType GetType(string paymentStatementTypeCode)
        {
            switch (paymentStatementTypeCode)
            {
                case "EFT":
                    return PaymentStatementType.EFT;
                case "Check":
                    return PaymentStatementType.Check;
                case "None":
                default:
                    return PaymentStatementType.None;
            }
        }

        public static string GetTypeCode(PaymentStatementType paymentStatementType)
        {
            switch (paymentStatementType)
            {
                case PaymentStatementType.EFT:
                    return "EFT";
                case PaymentStatementType.Check:
                    return "Check";
                case PaymentStatementType.None:
                default:
                    return "None";
            }
        }

        public static PaymentStatementStatus GetStatus(string paymentStatementStatusCode)
        {
            switch (paymentStatementStatusCode)
            {                
                case "Pending":
                    return PaymentStatementStatus.Pending;
                case "Issued":
                    return PaymentStatementStatus.Issued;
                case "None":
                default:
                    return PaymentStatementStatus.None;
            }
        }

        public static string GetStatusCode(PaymentStatementStatus paymentStatementStatus)
        {
            switch (paymentStatementStatus)
            {
                case PaymentStatementStatus.Issued:
                    return "Issued";
                case PaymentStatementStatus.Pending:
                    return "Pending";
                case PaymentStatementStatus.None:
                default:
                    return "None";
            }
        }
    }

    public enum PaymentStatementStatus
    {
        None = 0,
        Issued = 1,
        Pending = 2
    }

    public enum PaymentStatementType
    {
        None = 0,
        EFT = 1,
        Check = 2
    }

    public class PaymentStatementDetail
    {
        public string Id { get; set; }
        public string ServiceType { get; set; }
        public DateTime ServiceDate { get; set; }
        public string MemberId { get; set; }
        public string MemberName { get; set; }
        public decimal AmountPaid { get; set; }
        public decimal PrevAmountPaid { get; set; }
    }
}
