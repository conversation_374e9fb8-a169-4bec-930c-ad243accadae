using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;

namespace Extric.Towbook.Integration.MotorClubs.Billing.RoadAmerica
{
    public class Payment
    {
        [JsonProperty(PropertyName = "Number")]
        public string PurchaseOrderNumber { get; set; }
        public decimal Approved { get; set; }
        public string CheckNumber { get; set; }
        public decimal? Claim { get; set; }
        public DateTime Date { get; set; }
        public string IsACH { get; set; }
        public DateTime? PaymentDate { get; set; }
        public string Status { get; set; }
    }
}
