using Extric.Towbook.Dispatch;
using System;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace Extric.Towbook.Integration.MotorClubs
{
    public class AStarEmailParser : IMotorClubRequest
    {
        public string RequestId { get; set; }
        public DateTime? RequestDate { get; set; }
        public string BenefitAmount { get; set; }
        public string CustomerId { get; set; }
        public string CustomerName { get; set; }
        public string CustomerPhone { get; set; }
        public string DestinationAddress { get; set; }
        public string DestinationCrossStreet { get; private set; }
        public string DestinationName { get; set; }
        public string ETA { get; set; }
        public decimal PayoutFees { get; set; }
        public string PickupAddress { get; set; }
        public string PickupCrossStreet { get; private set; }
        public string PickupType { get; set; }
        public decimal PickupLatitude { get; set; }
        public decimal PickupLongitude { get; set; }
        public string PurchaseOrderNumber { get; set; }
        public string ServiceNeeded { get; set; }
        public string TowingLocationId { get; set; }
        public string TowingProviderId { get; set; }
        public string VehicleClass { get; set; }
        public string VehicleColor { get; set; }
        public string VehicleLicense { get; set; }
        public string VehicleLicenseState { get; set; }
        public string VehicleMake { get; set; }
        public string VehicleModel { get; set; }
        public string VehicleNotes { get; set; }
        public string VehicleType { get; set; }
        public string VehicleVIN { get; set; }
        public string VehicleYear { get; set; }
        public string CustomerAddress { get; set; }
        public string CustomerCity { get; set; }
        public string CustomerState { get; set; }
        public string CustomerZip { get; set; }
        public string VehicleOdometer { get; set; }

        private static string Subject { get; set; }

        public async Task PostSave(Extric.Towbook.Dispatch.Entry entry)
        {
            if (entry.AccountId > 1)
            {
                var account = Accounts.Account.GetById(entry.AccountId);

                if (account != null && account.Companies.Contains(entry.CompanyId))
                {
                    var akv = AccountKeyValue.GetFirstValueOrNull(account.CompanyId, account.Id, Integration.Provider.Towbook.ProviderId, "AccountPhysicalAddressAsDefaultLocation");

                    if (akv == "1")
                    {
                        entry.TowSource = ((!string.IsNullOrWhiteSpace(account.Address) ? account.Address + ", " : "") + account.City + "  " + account.State + "  " + account.Zip).Trim();
                        await entry.Save();
                    }
                }
            }
        }

        public static AStarEmailParser FromHtml(string html, string subject)
        {
            Subject = subject ?? string.Empty;

            if (subject.IndexOf("FW:") != -1)
                Subject = ExtractText(subject, "FW:", "-EOF-").Trim();

            return FromText(new HtmlToText().ConvertHtml(html ?? string.Empty));
        }

        private static AStarEmailParser FromText(string text)
        {
            AStarEmailParser result = new AStarEmailParser();

            result.PurchaseOrderNumber = ExtractText(text, "Vendor ticket #", "The ticket details are as follows");
            result.RequestId = ExtractText(text, "Internal Ticket #", "Vendor ticket #");
            result.TowingProviderId = ExtractStoreNumber(ExtractText(text, "Pep Boys Store #", "Internal Ticket #")); ;

            result.PickupAddress = ExtractText(text, "Description:", "Additional Notes");
            result.VehicleNotes = ExtractText(text, "Additional Notes:", "Status");

            result.ServiceNeeded = ExtractText(text, "Type");

            // vehicle
            result.VehicleLicense = ExtractText(text, "License Plate #").ToUpperInvariant();
            result.VehicleLicenseState = ExtractText(text, "License Plate State").ToUpperInvariant();
            result.VehicleVIN = ExtractText(text, "VIN").ToUpperInvariant();
            result.VehicleYear = ExtractText(text, "Year");
            result.VehicleMake = ExtractText(text, "Make");
            result.VehicleModel = ExtractText(text, "Model");

            // notes
            var abandonmentDate = ExtractText(text, "Abandoned since");
            if (!string.IsNullOrEmpty(abandonmentDate))
                result.VehicleNotes += "\r\n\r\nAbandoned since " + abandonmentDate;

            var keyLocation = ExtractText(text, "Where are the keys secured?");
            if(!string.IsNullOrEmpty(keyLocation))
                result.VehicleNotes += "\r\n\r\nWhere are the keys secured? " + keyLocation;

            var additionalNotes = ExtractText(text, "Additional comments captured are:", "Thank you!");
            if(!string.IsNullOrEmpty(additionalNotes))
                result.VehicleNotes += "\r\n\r\n" + additionalNotes;


            var swoText = ExtractText(text, "SWO", "Customer's Last Name");
            if (!string.IsNullOrEmpty(swoText))
                result.VehicleNotes += "\r\n\r\n" + swoText;

            // customer info
            var customerName = ExtractText(text, "Customer's First Name") + " " + ExtractText(text, "Customer's Last Name");
            var customerPhone = ExtractText(text, "Customer Phone");
            var customerAddress = ExtractText(text, "Customer's Street Address");
            var customerCity = ExtractText(text, "Customer's City");
            var customerState = ExtractText(text, "Customer's State");
            var customerZip = ExtractText(text, "Customer's Zip");

            result.VehicleNotes += "\r\n\r\nCustomer Info: " + (string.IsNullOrEmpty(customerName) ? "" : customerName) +
                (string.IsNullOrEmpty(customerPhone) ? "" : " " + customerPhone) +
                (string.IsNullOrEmpty(customerAddress) ? "" : " " + customerAddress) +
                (string.IsNullOrEmpty(customerCity) ? "" : " " + customerCity) +
                (string.IsNullOrEmpty(customerState) ? "" : " " + customerState) +
                (string.IsNullOrEmpty(customerZip) ? "" : " " + customerZip);

            // service writer
            var serviceWriter = ExtractText(text, "Service Writer");
            if(!string.IsNullOrEmpty(serviceWriter))
                result.VehicleNotes += "\r\n\r\nService Writer: " + serviceWriter;


            return result;
        }

        public static string ExtractStoreNumber(string text)
        {
            if (string.IsNullOrEmpty(text))
                return string.Empty;

            // all digits, return with trimming "0" at start
            if (text.All(Char.IsDigit))
                return text.TrimStart('0');

            if (text.Length > 4 && text.Where(Char.IsDigit).Count() >= 4)
                return string.Join("", text.Where(Char.IsDigit).Take(4)).TrimStart('0');

            return text;
        }

        public static string ExtractText(string text, string key, string nextKey = "\n")
        {
            // return if text is invalid
            if (string.IsNullOrEmpty(text))
                return "";

            var idx = text.IndexOf(key + ":");
            // try to find without ":" at the end
            if (idx == -1)
                idx = text.IndexOf(key);
            // Must have the primary index at this point, return if not
            if (idx == -1)
                return "";

            // Get the secondary index, try user provided key first
            var idxNext = text.IndexOf(nextKey, idx);
            // optionally, check for EOF key and return text to the end of the string.
            if (nextKey == "-EOF-")
                idxNext = text.Length;
            // if user didn't provide a key, try to the end of the current line
            if (string.IsNullOrEmpty(nextKey))
                idxNext = text.IndexOf("\n", idx);
            // Another try at a different delimiter
            if (idxNext == -1)
                idxNext = text.IndexOf("\0", idx);
            // Must have a secondary index at this point, return if not
            if (idxNext == -1)
                return "";

            if (idx + key.Length == idxNext)
                return "";

            var ret = text.Substring(idx + key.Length + 1, idxNext - (idx + key.Length + 1)).Trim();

            if (ret.ToLowerInvariant() == "n/a")
                ret = string.Empty;

            // remove all cases of special escape character |
            return Regex.Replace(ret, @"\|", "");
        }

        private static string CleanText(string text)
        {
            if (string.IsNullOrEmpty(text))
                return string.Empty;

            // replace double spaces with one space.
            text = string.Join(" ", text.Split(new char[] { ' ' }, StringSplitOptions.RemoveEmptyEntries));

            return  text
                .Replace("\r\n\r\n", "\r\n")
                .Replace(" \r\n", "\r\n")
                .Replace("\r\n ", "\r\n")
                .Replace("\r\n\r\n", "\r\n");
        }
    }
}
