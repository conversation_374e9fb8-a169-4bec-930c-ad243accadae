using Extric.Towbook.Dispatch;
using HtmlAgilityPack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Extric.Towbook.Integration.MotorClubs
{
    public class AaInsuranceReturnTowEmailParser: IMotorClubRequest
    {
        public string RequestId { get; set; }
        public DateTime? RequestDate { get; set; }
        public string BenefitAmount { get; set; }
        public string CustomerId { get; set; }
        public string CustomerName { get; set; }
        public string CustomerPhone { get; set; }
        public string DestinationAddress { get; set; }
        public string DestinationName { get; set; }
        public string DestinationCrossStreet { get; set; }
        public string ETA { get; set; }
        public decimal PayoutFees { get; set; }
        public string PickupAddress { get; set; }
        public string PickupCrossStreet { get; set; }
        public string PickupType { get; set; }
        public string PurchaseOrderNumber { get; set; }
        public string ServiceNeeded { get; set; }
        public string TowingLocationId { get; set; }
        public string TowingProviderId { get; set; }
        public string VehicleClass { get; set; }
        public string VehicleColor { get; set; }
        public string VehicleLicense { get; set; }
        public string VehicleLicenseState { get; set; }
        public string VehicleMake { get; set; }
        public string VehicleModel { get; set; }
        public string VehicleNotes { get; set; }
        public string VehicleType { get; set; }
        public string VehicleVIN { get; set; }
        public string VehicleYear { get; set; }
        public string CustomerAddress { get; set; }
        public string CustomerCity { get; set; }
        public string CustomerState { get; set; }
        public string CustomerZip { get; set; }

        private string data = string.Empty;
        public AaInsuranceReturnTowEmailParser(string data)
        {
            this.data = data;
        }

        public static AaInsuranceReturnTowEmailParser FromText(string data)
        {
            AaInsuranceReturnTowEmailParser result = new AaInsuranceReturnTowEmailParser(data);

            result.PurchaseOrderNumber = ExtractText(result.data, "IAA Return Tow -").Trim();

            var batchNo = ExtractText(result.data, "Batch No:").Replace("\r\n", "").Trim();
            var stockNo = ExtractText(result.data, "Stock No:", "Batch No:").Trim();


            // Stock # and Batch # to notes
            if(!string.IsNullOrEmpty(stockNo))
                result.VehicleNotes = "Stock No: " + stockNo;
            if (!string.IsNullOrEmpty(batchNo))
                result.VehicleNotes += "\nBatch #: " + batchNo;

            result.VehicleNotes += "\nTow Notes: " + ExtractText(result.data, "Tow Notes", "Outbound Inventory").Replace("\n\n", "").Trim();

            var addresses = GetPickupAndDestinationAddresses(ExtractText(result.data, "From :", "Tower:"));
            if (addresses.ContainsKey("From"))
                result.PickupAddress = addresses["From"];

            if (addresses.ContainsKey("To"))
                result.DestinationAddress = addresses["To"];

            // Contact
            result.VehicleNotes += "\nProvider: " + ExtractText(result.data, "Provider", "Owner").Trim();
            result.VehicleNotes += "\nContact: " + ExtractText(result.data, "Owner").Trim();
            result.VehicleNotes += "\nClaim: " + ExtractText(result.data, "Claim", "Adjuster").Trim();
            result.VehicleNotes += "\nAdjuster: " + ExtractText(result.data, "Adjuster").Trim();

            // Zone
            result.TowingProviderId = ExtractText(result.data, "Tow Zone", "Mileage");

            // Mileage
            result.VehicleNotes += "\n\nMileage: " + ExtractText(result.data, "Mileage", "Keys");

            // Vehicle
            string[] vehicleParts = ExtractText(result.data, "Vehicle", "Weight").Split(' ');
            result.VehicleYear = vehicleParts.Count() > 0 ? vehicleParts[0] : "";
            result.VehicleMake = vehicleParts.Count() > 1 ? vehicleParts[1] : "";
            result.VehicleModel = vehicleParts.Count() > 2 ? vehicleParts[2] : "";
            result.VehicleColor = ExtractText(result.data, "Color:");
            result.VehicleVIN = ExtractText(result.data, "VIN", "Plate");
            result.VehicleLicense = ExtractText(result.data, "Plate", "Color");


            result.VehicleNotes += "\n\nDamage: " + ExtractText(result.data, "Damage: ", "Primary").Replace("\n", "")
                + "\nPrimary: " + ExtractText(result.data, "Primary", "Secondary").Replace("\r\n", "")
                + "\nSecondary: " + ExtractText(result.data, "Secondary").Replace("\r\n", "")
                + "\nTowable: " + ExtractText(result.data, "Towable", "Truck").Replace("\r\n", "");

            return result;
        }

        private string ExtractDateTime(string key)
        {
            int keyIdx = this.data.IndexOf(key);
            if (keyIdx == -1)
            {
                // couldn't find the key... don't return an exception - this will cause fields that are missing to cause the whole parser to crash.
                return "";
            }

            int endLineIdx = this.data.IndexOf("\n", keyIdx);

            string wholeLine = this.data.Substring(keyIdx + key.Length, endLineIdx - (keyIdx + key.Length));
            
            return wholeLine.Remove(0, 1).Trim().Replace(" / ", " ");
        }

        public static string ExtractText(string data, string key, string nextKey = "\n", int startIndex = 0)
        {
            int keyIdx = data.IndexOf(key, startIndex);
            if (keyIdx == -1)
            {
                // couldn't find the key... don't return an exception - this will cause fields that are missing to cause the whole parser to crash.
                return "";
            }

            int endLineIdx = data.IndexOf(nextKey, keyIdx);
            if (endLineIdx == -1)
            {
                endLineIdx = data.IndexOf(nextKey);

                // stop out of index exception
                if (keyIdx > endLineIdx)
                    return "";
            }

            string[] parts = data.Substring(keyIdx + key.Length, endLineIdx - (keyIdx + key.Length)).Split(':');
            if (parts.Length == 1)
                return parts[0].Replace("\r", "");
            else if (parts.Length > 1)
            {
                string ret = string.Empty;
                foreach (var part in parts)
                    ret += part.Trim().Replace("\r", "");
                return ret;
            }
            else
                return string.Empty;
        }

        /// <summary>
        /// Translate From and To addresses in the form of....
        ///  From : Eugene AUX Lot                                                 To : Ramsey-Waite Company
        ///         90801 Highway 99 N,                                                 4258 Franklin Blvd,
        ///         Eugene, OR 97402                                                    Eugene, OR 97403
        ///         (541) 689-4000                                                      (541) 726-7625
        ///         Lane                                                                Lane
        /// </summary>
        /// <param name="data">The string that contains the From and To addresses</param>
        /// <param name="separator">Each next line will determine rows.  Columns will determine From or To text.</param>
        /// <returns>A Dictionary with keys "From" and "To"</returns>
        private static Dictionary<string, string> GetPickupAndDestinationAddresses(string data)
        {
            var ret = new Dictionary<string, string>();
            var fromStr = new StringBuilder();
            var toStr = new StringBuilder();

            if (string.IsNullOrEmpty(data))
                return ret;

            var rows = data.Replace(" To", "").Split('\n');
            if (rows.Length >= 2)
            {
                foreach (var row in rows)
                {
                    string pattern = @"\s{30,}"; // 30 spaces

                    var cols = System.Text.RegularExpressions.Regex.Split(row, pattern);
                    if(cols.Length == 2)
                    {
                        var str1 = cols[0].Trim();
                        var str2 = cols[1].Trim();

                        if (Core.IsPhoneValidStandard(Core.FormatPhoneWithNumbersOnly(str1)))
                            continue;

                       fromStr.Append(" " + str1);
                       toStr.Append(" " + str2);
                    }
                }
            }

            ret.Add("From", fromStr.ToString().Trim());
            ret.Add("To", toStr.ToString().Trim());

            return ret;
        }

        public System.Threading.Tasks.Task PostSave(Entry entry)
        {
            return System.Threading.Tasks.Task.CompletedTask;
        }

    }
}
