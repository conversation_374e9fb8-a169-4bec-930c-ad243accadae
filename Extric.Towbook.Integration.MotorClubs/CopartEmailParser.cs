using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace Extric.Towbook.Integration.MotorClubs
{
    public class CopartEmailParser : IMotorClubRequest
    {
        public string RequestId { get; set; }
        public DateTime? RequestDate { get; set; }
        public string BenefitAmount { get; set; }
        public string CustomerId { get; set; }
        public string CustomerName { get; set; }
        public string CustomerPhone { get; set; }
        public string DestinationAddress { get; set; }
        public string DestinationName { get; set; }
        public string DestinationCrossStreet { get; set; }
        public string ETA { get; set; }
        public decimal PayoutFees { get; set; }
        public string PickupAddress { get; set; }
        public string PickupCrossStreet { get; set; }
        public string PickupType { get; set; }
        public string PurchaseOrderNumber { get; set; }
        public string ServiceNeeded { get; set; }
        public string TowingLocationId { get; set; }
        public string TowingProviderId { get; set; }
        public string VehicleClass { get; set; }
        public string VehicleColor { get; set; }
        public string VehicleLicense { get; set; }
        public string VehicleLicenseState { get; set; }
        public string VehicleMake { get; set; }
        public string VehicleModel { get; set; }
        public string VehicleNotes { get; set; }
        public string VehicleType { get; set; }
        public string VehicleVIN { get; set; }
        public string VehicleYear { get; set; }
        public string CustomerAddress { get; set; }
        public string CustomerCity { get; set; }
        public string CustomerState { get; set; }
        public string CustomerZip { get; set; }

        private string data = string.Empty;

        public CopartEmailParser(string data)
        {
            this.data = data;
        }

        public static CopartEmailParser FromText(string data)
        {
            CopartEmailParser result = new CopartEmailParser(data);

            string vehicleParts = result.ExtractText("Desc", "Damage");
            var vehicleSplit = vehicleParts.Split(' ');
            var vehicleModel = vehicleParts.Remove(0, 2).Trim();

            var addressParts = result.ExtractAddressInfo();
            
            result.CustomerName = result.ExtractText("Owner");
            result.CustomerPhone = result.ExtractText("Owner Phone");

            string lotNumber = result.ExtractText("Lot #");

            result.PurchaseOrderNumber = lotNumber.Trim();
            
            var lossType = result.ExtractText("Loss Type", "Claim #");
            if(string.IsNullOrEmpty(lossType))
                lossType = result.ExtractText("Loss type", "Claim #");
            if (string.IsNullOrEmpty(lossType))
                lossType = result.ExtractText("Loss type", "Claim#");

            result.ServiceNeeded = lossType;

            result.TowingProviderId = result.ExtractText("Trip Zone", "Advance Limit");
            if (result.TowingProviderId == "")
                result.TowingProviderId = result.ExtractFirstValueAfterKey("Trip Zone:");

            result.RequestDate = DateTime.Now; // Convert.ToDateTime(result.ExtractText("Loss Date"));
            result.VehicleYear = vehicleSplit[0];
            result.VehicleColor = vehicleSplit[vehicleSplit.Length - 1];
            result.VehicleModel = vehicleModel.Remove(vehicleModel.LastIndexOf(' '), result.VehicleColor.Length);
            result.VehicleLicense = result.ExtractText("License#", "Tow");

            var vin = result.ExtractText("Vin #", "Vehicle Type");
            if (string.IsNullOrEmpty(vin))
                vin = result.ExtractText("VIN#", "Vehicle Type");

            result.VehicleVIN = vin;


            var vehicleType = result.ExtractText("Vehicle Type", "Driver");
            if(string.IsNullOrEmpty(vehicleType))
                vehicleType = result.ExtractText("Vehicle Type", "Vendor");

            result.VehicleType = vehicleType;


            result.PickupAddress = addressParts["PickupAddress"];
            result.DestinationAddress = addressParts["DestinationAddress"];

            string compare = @"COPART - [0-9]{1,3}";
            Regex regex = new Regex(compare);
            if (addressParts.ContainsKey("PickupNameAndPhone"))
            {
                var match = regex.Match(addressParts["PickupNameAndPhone"]);
                if (match.Success)
                {
                    result.TowingLocationId = match.Value.Replace("COPART -", "").Trim();
                }

                result.VehicleNotes = addressParts["PickupNameAndPhone"] + "\r\n";
            }

            if (addressParts.ContainsKey("DestinationNameAndPhone"))
            {
                var match = regex.Match(addressParts["DestinationNameAndPhone"]);
                if (match.Success)
                {
                    result.TowingLocationId = match.Value.Replace("COPART -", "").Trim();
                }

                result.VehicleNotes += addressParts["DestinationNameAndPhone"] + "\r\n";
            }

            result.VehicleNotes += result.ExtractVehicleNotes();

            return result;
        }

        private Dictionary<string, string> ExtractAddressInfo()
        {
            Dictionary<string, string> result = new Dictionary<string, string>();

            bool includeColon = true;

            int startIdx = this.data.IndexOf("From:");

            if (startIdx == -1)
            {
                startIdx = this.data.IndexOf("From");
                if (startIdx > 0)
                    includeColon = false;
            }

            if (startIdx == -1)
                return result;

            int endIdx = this.data.IndexOf("Cross St.", startIdx);

            if (endIdx == -1)
                endIdx = this.data.IndexOf("Cross St", startIdx);

            // Make sure we don't go out of bounds on the substring call
            if (startIdx < (endIdx - startIdx))
                return result;

            var segment = this.data.Substring(startIdx, endIdx - startIdx);
            string[] lines = RuntimeInformation.IsOSPlatform(OSPlatform.Windows)? segment.Split('\r'): segment.Split('\n');

            if(lines.Any() && string.IsNullOrEmpty(lines.First().Replace("To", "").Replace("From", "").Replace(":", "").Trim()))
            {
                // remove titles "To" and "From"
                lines = lines.Skip(1).ToArray();
            }

            string pickupNameAndPhone = "";
            string pickupAddress = "";
            string destinationNameAndPhone = "";
            string destinationAddress = "";

            if(lines.Count() == 2)
            {
                // Simply the address in 2 lines.  Added this as a just in case they send only the address.  From what
                // I can see Copart emails always seem to have a name, street address, city/state/zip, and phone number
                pickupAddress = lines[0].Substring(0, lines[0].Length / 2).Replace((includeColon ? "From:" : "From"), "").Trim() 
                    + " " 
                    + lines[1].Substring(0, lines[1].Length / 2).Trim();

                destinationAddress = lines[0].Substring(lines[0].Length / 2, lines[0].Length - lines[0].Length / 2).Replace("To:", "").Trim()
                    + " "
                    + lines[1].Substring(lines[1].Length / 2, lines[1].Length - lines[1].Length / 2).Trim();
            }
            else if (lines.Count() == 3)
            {
                // We don't know if the first line is the name of the business or contact or it's the street address.
                // So, we'll need to parse the three lines as the pickupAddress just to be safe.
                pickupAddress = lines[0].Substring(0, lines[0].Length / 2).Replace((includeColon ? "From:" : "From"), "").Trim() 
                    + " " 
                    + lines[1].Substring(0, lines[1].Length / 2).Trim() 
                    + " " 
                    + lines[2].Substring(0, lines[2].Length / 2).Trim();

                destinationAddress = lines[0].Substring(lines[0].Length / 2, lines[0].Length - lines[0].Length / 2).Replace("To:", "").Trim()
                    + " "
                    + lines[1].Substring(lines[1].Length / 2, lines[1].Length - lines[1].Length / 2).Trim()
                    + " "
                    + lines[2].Substring(lines[2].Length / 2, lines[2].Length - lines[2].Length / 2).Trim();
            }
            else if(lines.Count() > 3)
            {
                // Assume the default of name, street address, city/state/zip, and phone number
                pickupNameAndPhone = "Pickup From: " 
                    + lines[0].Substring(0, lines[0].Length / 2).Replace((includeColon ? "From:" : "From"), "").Trim() 
                    + " " 
                    + lines[3].Substring(0, lines[3].Length / 2).Trim();

                pickupAddress = lines[1].Substring(0, lines[1].Length / 2).Trim()
                    + " "
                    + lines[2].Substring(0, lines[2].Length / 2).Trim();

                destinationNameAndPhone = "Deliver To: "
                    + lines[0].Substring(lines[0].Length / 2, lines[0].Length - lines[0].Length / 2).Replace("To:", "").Trim()
                    + " "
                    + lines[3].Substring(lines[3].Length / 2, lines[3].Length - lines[3].Length / 2).Trim();

                destinationAddress = lines[1].Substring(lines[1].Length / 2, lines[1].Length - lines[1].Length / 2).Trim()
                    + " "
                    + lines[2].Substring(lines[2].Length / 2, lines[2].Length - lines[2].Length / 2).Trim();
            }

            result.Add("PickupAddress", pickupAddress);
            result.Add("DestinationAddress", destinationAddress);

            if (pickupNameAndPhone.Trim() != "Pickup From:")
                result.Add("PickupNameAndPhone", pickupNameAndPhone);

            if (destinationNameAndPhone.Trim() != "Deliver To:")
                result.Add("DestinationNameAndPhone", destinationNameAndPhone);

            return result;
        }

        private string ExtractVehicleNotes()
        {
            StringBuilder result = new StringBuilder();

            int startIdx = this.data.IndexOf("Lot Notes");
            int endIdx = this.data.Length;
            string[] lines = null;
            while (startIdx != -1)
            {
                endIdx = this.data.IndexOf("PICKUP ORDER", startIdx);
                if (endIdx == -1)
                    endIdx = this.data.IndexOf("Advance Charges Review", startIdx);
                if (endIdx == -1)
                    break;
                var segment = this.data.Substring(startIdx, endIdx - startIdx);
                lines = RuntimeInformation.IsOSPlatform(OSPlatform.Windows) ? segment.Split('\r') : segment.Split('\n');
                for (int i = 0; i < lines.Length - 2; i++ )
                {
                    if (lines[i].Length > 10)
                        result.AppendLine(lines[i].Substring(10).Trim());
                    else
                        continue;
                }

                startIdx = this.data.IndexOf("Lot Notes", endIdx);
            }

            return result.ToString();
        }

        private string ExtractText(string key, string nextKey = "\n")
        {
            int keyIdx = this.data.IndexOf(key);
            if (keyIdx == -1)
            {
                // couldn't find the key... don't return an exception - this will cause fields that are missing to cause the whole parser to crash.
                return "";
            }

            int endLineIdx = this.data.IndexOf(nextKey, keyIdx);
            if (endLineIdx == -1)
                return "";

            string[] parts = this.data.Substring(keyIdx + key.Length, endLineIdx - (keyIdx + key.Length)).Split(new char[]{':'}, StringSplitOptions.RemoveEmptyEntries);
            if (parts.Length == 1)
                return parts[0].Replace("\r", "").Trim();
            else if (parts.Length == 2)
                return parts[1].Trim().Replace("\r", "").Trim();
            else
                return string.Empty;
        }

        private string ExtractFirstValueAfterKey(string key)
        {
            var ret = "";
            int keyIdx = this.data.IndexOf(key);
            if (keyIdx == -1 || (keyIdx + key.Length) > this.data.Length)
                return ret;

            // We found the key ... get rid of the key now
            ret = this.data.Substring(keyIdx + key.Length);

            int firstCharIdx = ret.TakeWhile(char.IsWhiteSpace).Count();
            int lastCharIdx = firstCharIdx + ret.Substring(firstCharIdx).TakeWhile(c => !char.IsWhiteSpace(c)).Count();

            if (firstCharIdx == -1 || lastCharIdx == -1)
                return "";

            if (firstCharIdx > lastCharIdx || lastCharIdx > ret.Length)
                return "";

            return ret.Substring(firstCharIdx, lastCharIdx - firstCharIdx);
        }

        public System.Threading.Tasks.Task PostSave(Extric.Towbook.Dispatch.Entry entry)
        {
            return System.Threading.Tasks.Task.CompletedTask;
        }
    }
}
