using System.Collections.Generic;

namespace Extric.Towbook.Integration.MotorClubs.Dispatch.D3.Model
{
    /// <summary>
    /// Represents the result returned by D3 for a /user/login request.
    /// </summary>
    public class D3LoginResult
    {
        public string LoginName { get; set; }
        public object Password { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string Role { get; set; }
        public string TrueUserRole { get; set; }
        public string Status { get; set; }
        public string Session { get; set; }
        public object TermId { get; set; }
        public string OpAddress { get; set; }
        public object ActionRequired { get; set; }
        public object LogoutReason { get; set; }
        public object SppToken { get; set; }
        public object Club { get; set; }
        public object Root { get; set; }
        public object Host { get; set; }
        public object Group { get; set; }
        public string Email { get; set; }
        public object Facility { get; set; }
        public string Spmg { get; set; }
        public object Aar { get; set; }
        public bool Reset_di_prof { get; set; }
        public object TruckId { get; set; }
        public bool Truck { get; set; }
        public string LastUsedRole { get; set; }
        public string LastUsedLocation { get; set; }
        public List<string> AssignedRoles { get; set; }
        public List<string> AssignedLocations { get; set; }
    }

}
