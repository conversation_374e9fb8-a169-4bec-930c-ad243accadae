using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Extric.Towbook.Integration.MotorClubs.Dispatch.D3.Model
{
    public class SkillOption
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string Profiles { get; set; }
        public bool Active { get; set; }
    }

    public class Chronology
    {
        public string CalendarType { get; set; }
        public string Id { get; set; }
    }

    public class Time
    {
        public int Nano { get; set; }
        public int Hour { get; set; }
        public int Minute { get; set; }
        public int Second { get; set; }
        public int Year { get; set; }
        public string Month { get; set; }
        public int DayOfMonth { get; set; }
        public string DayOfWeek { get; set; }
        public int DayOfYear { get; set; }
        public int MonthValue { get; set; }
        public Chronology Chronology { get; set; }
    }

    public class DispatcherStatusHistory
    {
        public string StatusCode { get; set; }
        public object ReasonCode { get; set; }
        public Time Time { get; set; }
    }

    public class D3ConfigurationUserDI
    {
        public object SessionId { get; set; }
        public string LoginName { get; set; }
        public string Module { get; set; }
        public string UserRole { get; set; }
        public object TruckId { get; set; }
        public object DriverId { get; set; }
        public List<string> Facilities { get; set; }
        public object Timestamp { get; set; }
        public bool MotdRead { get; set; }
        public List<object> FavoriteCalls { get; set; }
        public string EmployeeId { get; set; }
        public object LdapEmployeeId { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string TrueUserRole { get; set; }
        public string RootSpmg { get; set; }
        public object Facility { get; set; }
        public string LastUsedRole { get; set; }
        public string LastUsedLocation { get; set; }
        public List<object> LocationOptions { get; set; }
        public List<object> DispatchQueues { get; set; }
        public List<SkillOption> SkillOptions { get; set; }
        public List<object> Profiles { get; set; }
        public bool DiQueuePrompt { get; set; }
        public List<object> UserSettings { get; set; }
        public object UserId { get; set; }
        public string AssociationCode { get; set; }
        public string ClubCode { get; set; }
        public string DefaultStateCd { get; set; }
        public bool UseCanada { get; set; }
        public object AssignedQueueLevels { get; set; }
        public List<int> ActiveSkills { get; set; }
        public List<object> FacilityPointsOfContact { get; set; }
        public List<object> ActiveCallsWithoutChanges { get; set; }
        public List<DispatcherStatusHistory> DispatcherStatusHistory { get; set; }
        public List<string> AccessControls { get; set; }
        public object CallToLookup { get; set; }
        public bool Ldap { get; set; }
    }
}
