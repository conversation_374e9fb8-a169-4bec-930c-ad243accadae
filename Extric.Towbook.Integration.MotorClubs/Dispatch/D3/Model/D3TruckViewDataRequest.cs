using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Extric.Towbook.Integration.MotorClubs.Dispatch.D3.Model
{
    public class D3TruckViewDataRequest
    {
        public string TabId { get; set; }
        public IEnumerable<string> VisibleFacilities { get; set; }
        public bool MdtTrucksOnly { get; set; }
        public bool ActiveTrucksOnly { get; set; }
        public bool LoadedTrucksOnly { get; set; }
        public string SelectedFacility { get; set; }
        public string SelectedTruck { get; set; }
        public List<object> CallSortColumns { get; set; }
        public List<object> TruckSortColumns { get; set; }
    }
}
