using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Extric.Towbook.Integration.MotorClubs.Dispatch.D3.Model
{
    public class VisibleFacility
    {
        public string FacilityId { get; set; }
        public string FacilityName { get; set; }
        public int CallCount { get; set; }
        public int UnassignedCallCount { get; set; }
        public int ActiveTruckCount { get; set; }
        public int AllTruckCount { get; set; }
        public string FacilityPhone { get; set; }
        public object DispatchMethod { get; set; }
    }
    

    public class SelectedFacilityTruck
    {
        public string TruckId { get; set; }
        public string Terminal { get; set; }
        public string TruckType { get; set; }
        public bool BatteryIndicator { get; set; }
        public object DriverStatus { get; set; }
        public string TruckStatus { get; set; }
        public object DriverNote { get; set; }
        public string Facility { get; set; }
        public string CommunicationType { get; set; }
        public object CustomColumns { get; set; }
    }

    public class Cell
    {
        public string Name { get; set; }
        public string Display { get; set; }
        public int ColumnId { get; set; }
    }

    public class SelectedTruckCall
    {
        public string CallDate { get; set; }
        public string CallId { get; set; }
        public string HiddenData { get; set; }
        public List<Cell> Cells { get; set; }

        public string UrlKey
        {
            get
            {
                if (!(string.IsNullOrWhiteSpace(CallDate) || string.IsNullOrWhiteSpace(CallId)))
                {
                    return CallDate.Replace("-", "") + "-" + CallId;
                }
                else
                {
                    return null;
                }
            }
        }
    }

    public class D3TruckViewDataResponse
    {
        public string SelectedFacility { get; set; }
        public string SelectedTruck { get; set; }
        public List<VisibleFacility> VisibleFacilities { get; set; }
        public List<SelectedFacilityTruck> SelectedFacilityTrucks { get; set; }
        public List<SelectedTruckCall> SelectedTruckCalls { get; set; }
        public long LastUpdateCacheKey { get; set; }
        public long LastChangedCallId { get; set; }
    }

}
