using System.Collections.Generic;

namespace Extric.Towbook.Integration.MotorClubs.Model
{
    public sealed class PhotoPayload
    {
        public int DispatchEntryId { get; set; }
        public int CallRequestId { get; }
        public int PhotoId { get; }
        public decimal Latitude { get; }
        public decimal Longitude { get; }
        public string DriverName { get; }

        public PhotoPayload(int callRequestId, int photoId, decimal latitude, decimal longitude, string driverName,
            int dispatchEntryId)
        {
            CallRequestId = callRequestId;
            PhotoId = photoId;
            Latitude = latitude;
            Longitude = longitude;
            DriverName = driverName;
            DispatchEntryId = dispatchEntryId;
        }

        public override bool Equals(object obj)
        {
            return obj is PhotoPayload other &&
                   CallRequestId == other.CallRequestId &&
                   PhotoId == other.PhotoId &&
                   Latitude == other.Latitude &&
                   Longitude == other.Longitude &&
                   DriverName == other.DriverName &&
                   DispatchEntryId == other.DispatchEntryId;
        }

        public override int GetHashCode()
        {
            var hashCode = 445915648;
            hashCode = hashCode * -1521134295 + CallRequestId.GetHashCode();
            hashCode = hashCode * -1521134295 + PhotoId.GetHashCode();
            hashCode = hashCode * -1521134295 + Latitude.GetHashCode();
            hashCode = hashCode * -1521134295 + Longitude.GetHashCode();
            hashCode = hashCode * -1521134295 + DispatchEntryId.GetHashCode();
            hashCode = hashCode * -1521134295 + EqualityComparer<string>.Default.GetHashCode(DriverName);
            return hashCode;
        }
    }
}
