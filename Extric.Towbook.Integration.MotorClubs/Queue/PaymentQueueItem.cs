using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Extric.Towbook.Utility;

namespace Extric.Towbook.Integration.MotorClubs.Queue
{
    public enum PaymentQueueItemStatus : byte
    {
        None = 0,

        /// <summary>
        /// Queue item is being initialized
        /// </summary>
        Initializing = 1,

        /// <summary>
        /// Entered into the queue/default state, hasn't been touched yet.
        /// </summary>
        Waiting = 2,

        /// <summary>
        /// Grabbed from the queue by a processing agent, about to work on -- no work has started on it yet though.
        /// </summary>
        AboutToStart = 3,

        /// <summary>
        /// Agent has started processing the queue item.
        /// </summary>
        InProgress = 4,

        /// <summary>
        /// Processing has completed and the item can be removed from the queue / no further action should be taken.
        /// </summary>
        Completed = 5,

        /// <summary>
        /// Something went wrong and the item was not finished successfully.
        /// </summay>
        Error = 6,
    }


    public class PaymentQueueItem
    {
        public int Id { get; set; }
        public PaymentQueueItemStatus Status { get; set; }

        public int CompanyId { get; set; }
        public DateTime CreateDate { get; set; }

        /// <summary>
        /// What account should this queue item be associated with? Agero? CC? RoadAmerica? Geico? Allstate? Quest?
        /// </summary>
        public int AccountId { get; set; }

        /// <summary>
        /// Who submitted the invoice/call.
        /// </summary>
        public int OwnerUserId { get; set; }

        public PaymentQueueItem()
        {

        }

        /// <summary>
        /// Update the status on a queue item.
        /// </summary>
        /// <param name="item"></param>
        /// <param name="status"></param>
        public static void UpdateStatus(PaymentQueueItem item, PaymentQueueItemStatus status)
        {
            SqlMapper.ExecuteSP("MCDispatch.MotorClubBillingPaymentQueueUpdateStatusById",
                new
                {
                    @QueueItemId = item.Id,
                    @StatusId = status
                });
        }

        /// <summary>
        /// Retrieve the specified queue item.
        /// </summary>
        /// <returns></returns>
        public static PaymentQueueItem GetById(int id)
        {
            var r = SqlMapper.QuerySP("MCBilling.MotorClubBillingPaymentQueueGetById", new { @QueueItemId = id });

            if (r != null)
            {
                return Map(r.FirstOrDefault());
            }
            else
            {
                return null;
            }
        }

        public static PaymentQueueItem Map(dynamic o)
        {
            return new PaymentQueueItem()
            {
                Id = o.QueueItemId,
                Status = (PaymentQueueItemStatus)o.StatusId,
                CompanyId = o.CompanyId,
                CreateDate = o.CreateDate ?? DateTime.MinValue,
                AccountId = o.AccountId
            };
        }

        public void Save()
        {
            if (this.Id == 0)
            {
                var q = SqlMapper.QuerySP<dynamic>("MCBilling.MotorClubBillingPaymentQueueInsert",
                    new
                    {
                        @StatusId = PaymentQueueItemStatus.Initializing,
                        @CompanyId = this.CompanyId,
                        @AccountId = this.AccountId,
                        @OwnerUserId = this.OwnerUserId
                    }).FirstOrDefault();

                if (q != null)
                {
                    this.Id = q.Id;
                    this.CreateDate = q.CreateDate;
                }
                else
                {
                    throw new TowbookException("MotorClubBillingPaymentQueueInsert didn't return a result.");
                }
                    
            }
            else
            {
                SqlMapper.ExecuteSP("MCBilling.MotorClubBillingPaymentQueueUpdateById",
                    new
                    {
                        @QueueItemId = this.Id,
                        @CompanyId = this.CompanyId,
                        @AccountId = this.AccountId,
                        @OwnerUserId = this.OwnerUserId
                    });
            }
        }
    }
}
