using System;
using Extric.Towbook.Dispatch;
using System.Threading.Tasks;
using Async = System.Threading.Tasks;

namespace Extric.Towbook.Integration.MotorClubs
{
    public interface IMotorClubRequest
    {
        string RequestId { get; }
        DateTime? RequestDate { get; set;  }

        string BenefitAmount { get; }
        string CustomerId { get; }
        string CustomerName { get; }
        string CustomerPhone { get; }
        string DestinationAddress { get; }
        string DestinationName { get; }
        string DestinationCrossStreet { get; }

        string ETA { get; }
        decimal PayoutFees { get; }
        string PickupAddress { get; }
        string PickupCrossStreet { get; }
        string PickupType { get; }
        string PurchaseOrderNumber { get; }
        string ServiceNeeded { get; }
        string TowingLocationId { get; }
        string TowingProviderId { get; }
        string VehicleClass { get; }
        string VehicleColor { get; }
        string VehicleLicense { get; }
        string VehicleLicenseState { get; set; }
        string VehicleMake { get; set;  }
        string VehicleModel { get; }
        string VehicleNotes { get; }
        string VehicleType { get; }
        string VehicleVIN { get; }
        string VehicleYear { get; }

        string CustomerAddress { get; }
        string CustomerCity { get; }
        string CustomerState { get; }
        string CustomerZip { get;  }

        Async.Task PostSave(Entry entry);
    }
}
