using Extric.Towbook.Dispatch;
using HtmlAgilityPack;
using System;
using System.Collections.Generic;
using System.Linq;
using Extric.Towbook.Utility;
using System.Text.RegularExpressions;
using System.Globalization;

namespace Extric.Towbook.Integration.MotorClubs
{
    /// <summary>
    /// Parses Pinnacle Dispatch emails (formerly known as Coach-Net)
    /// </summary>
    public class CoachNetEmailParser: IMotorClubRequest
    {
        private HtmlDocument doc = new HtmlDocument();

        public string RequestId { get; set; }
        public string PurchaseOrderNumber { get; set; }
        public DateTime? RequestDate { get; set; }
        public string BenefitAmount { get; set; }
        public string CustomerId { get; set; }
        public string CustomerName { get; set; }
        public string CustomerPhone { get; set; }
        public string DestinationAddress { get; set; }
        public string DestinationCrossStreet { get; set; }
        public string DestinationName { get; set; }
        public string ETA { get; set; }
        public decimal PayoutFees { get; set; }
        public string PickupAddress { get; set; }
        public string PickupCrossStreet { get; set; }
        public string PickupType { get; set; }
        public string ServiceNeeded { get; set; }
        public string TowingLocationId { get; set; }
        public string TowingProviderId { get; set; }
        public string VehicleClass { get; set; }
        public string VehicleColor { get; set; }
        public string VehicleLicense { get; set; }
        public string VehicleLicenseState { get; set; }
        public string VehicleMake { get; set; }
        public string VehicleModel { get; set; }
        public string VehicleNotes { get; set; }
        public string VehicleType { get; set; }
        public string VehicleVIN { get; set; }
        public string VehicleYear { get; set; }
        public string CustomerAddress { get; set; }
        public string CustomerCity { get; set; }
        public string CustomerState { get; set; }
        public string CustomerZip { get; set; }

        public static CoachNetEmailParser FromText(string html)
        {
            CoachNetEmailParser result = new CoachNetEmailParser();

            result.doc.LoadHtml(html);


            string poKey = "PO # ";

            result.PurchaseOrderNumber = result.ExtractValue(poKey);
            result.RequestDate = result.ExtractDate();
            result.TowingProviderId = result.ExtractProvider();

            result.ServiceNeeded = result.ExtractValue("SERVICE");
            result.ETA = result.ExtractValuesFromLine(new string[] { "ETA", "Safe", "Member Pay" })["ETA"];
            result.CustomerName = result.ExtractValue("Name");


            result.RequestDate = result.ExtractDate();
            result.CustomerId = result.ExtractValueFromText("Member #");
            result.CustomerPhone = result.ExtractValuesFromLine(new string[] {"Telephone#", "Alternate"})["Telephone#"];

            var loc = result.ExtractValue("Loc");

            result.PickupAddress = result.ExtractValue("Cross") + 
                (!string.IsNullOrWhiteSpace(loc) ? " (" + loc + ")" : "");

            Dictionary<string, string> cityZip = result.ExtractValuesFromLine(new string[] { "City", "Zip" });
            result.CustomerCity = cityZip["City"];
            result.CustomerZip = cityZip["Zip"];

            result.DestinationName = result.ExtractValue("Dest");
            result.DestinationAddress = result.ExtractValue("Address");

            Dictionary<string, string> vehicle1 = result.ExtractValuesFromLine(new string[] { "Year", "Make", "Model" });
            Dictionary<string, string> vehicle2 = result.ExtractPlateAndVin();
            
            
            result.VehicleYear = vehicle1["Year"];
            result.VehicleMake = vehicle1.Get("Make");
            result.VehicleModel = vehicle1.Get("Model");
            result.VehicleLicense = vehicle2.Get("License");
            result.VehicleLicenseState = vehicle2.Get("State");
            result.VehicleVIN = vehicle2["Vin#"];
            result.VehicleColor = result.ExtractValueFromText("Color");
            result.VehicleNotes = result.ExtractValue("Desc");
            result.VehicleClass = result.ExtractValueFromText("Class:");
            
            return result;
        }

        private Dictionary<string, string> ExtractPlateAndVin()
        {
            string key = "License";
            HtmlNodeCollection tds = doc.DocumentNode.SelectNodes(string.Format("//td[normalize-space(.)='{0}:']/ancestor::tr/td[2]", key));

            if (tds == null)
                return null;

            string plateAndVin = Fix(tds[0].InnerText);

            if (plateAndVin.Contains("Vin#:"))
            {
                string[] pnl = plateAndVin.Split(new string[] { "Vin#:" }, StringSplitOptions.None);

                string plate = null;
                string state = null;
                string vin = pnl[1];

                if (pnl[0].Contains('/'))
                {
                    string[] plateState = pnl[0].Split('/');
                    if (plateState.Length == 2)
                    {
                        plate = Fix(plateState[1]);
                        state = Fix(plateState[0]);
                    }
                }


                return new Dictionary<string, string> { 
                    { "License", plate }, 
                    { "State", state }, 
                    { "Vin#", vin } 
                };
            }
            return null;

        }
        public override string ToString()
        {
            return this.ToJson();
        }

        public static string Fix(string input)
        {
            if (input == null)
                return null;
            return Regex.Replace(input.Replace("\n", " ").Replace("\r", "").Replace("&nbsp;", " ").Trim(), @"\s+", " ");
        }

        private DateTime? ExtractDate()
        {
            string key = "FROM"; 
            HtmlNodeCollection tds = doc.DocumentNode.SelectNodes(string.Format("//td[normalize-space(.)='{0}:']/ancestor::tr/td[3]", key));

            if (tds == null)
                return null;

            string rawDate = Fix(tds[0].InnerText);

            if (rawDate != null)
            {
                string[] rawDateSplit = rawDate.Split('-');
                // date comes from coachnet in central time always.. convert it to eastern
                return DateTime.ParseExact(rawDate, "H:mm:ss-MM/dd/yyyy", CultureInfo.InvariantCulture, DateTimeStyles.AllowWhiteSpaces).AddHours(1);
            }

            return null;
        }

        private String ExtractProvider()
        {
            string key = "COMPANY";
            HtmlNodeCollection tds = doc.DocumentNode.SelectNodes(string.Format("//td[normalize-space(.)='{0}:']/ancestor::tr/td[3]", key));

            if (tds == null)
                return null;

            return Fix(tds[0].InnerText).Replace("Vendor ID:", "").Trim();
        }

        private string ExtractValue(string key)
        {
            HtmlNodeCollection tds = doc.DocumentNode.SelectNodes(string.Format("//td[normalize-space(.)='{0}:']/ancestor::tr/td[2]", key));

            if (tds == null)
                return string.Empty;
                
            return Fix(tds[0].InnerText);
        }

        private Dictionary<string, string> ExtractValuesFromLine(string[] keys)
        {
            HtmlNodeCollection tds = doc.DocumentNode.SelectNodes(string.Format("//td[normalize-space(.)='{0}:']/ancestor::tr/td[2]", keys[0]));

            if (tds == null)
            {
                throw new Exception("Error parsing multiple keys..." + keys[0]);
            }

            string partialResult = tds[0].InnerText;
            Dictionary<string, string> result = new Dictionary<string, string>();

            int idx = 0;
            int idxPlusOne = 0;

            result.Add(keys[0], Fix(partialResult.Substring(0, partialResult.IndexOf(keys[1]))));

            for (int i = 1; i < keys.Length; i++ )
            {
                idx = partialResult.IndexOf(keys[i]);

                if (i != keys.Length - 1)
                    idxPlusOne = partialResult.IndexOf(keys[i+1]);
                else
                    idxPlusOne = partialResult.Length;

                result.Add(keys[i], Fix(partialResult.Substring(idx, idxPlusOne - idx).Split(':')[1]));
            }

            return result;
        }

        private string ExtractValueFromText(string key)
        {
            int idx = doc.DocumentNode.OuterHtml.IndexOf(key);
            
            if (idx == -1)
                throw new Exception("Error parsing key " + key);

            int idxPlusOne = doc.DocumentNode.OuterHtml.IndexOf("<", idx);

            return Fix(doc.DocumentNode.OuterHtml.Substring(idx, idxPlusOne - idx).Split(':')[1]);
        }

        public System.Threading.Tasks.Task PostSave(Extric.Towbook.Dispatch.Entry entry)
        {
            return System.Threading.Tasks.Task.CompletedTask;
        }
    }
}
