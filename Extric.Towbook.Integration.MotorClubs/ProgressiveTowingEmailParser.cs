using Extric.Towbook.Dispatch;
using HtmlAgilityPack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Extric.Towbook.Integration.MotorClubs
{
    public class ProgressiveTowingEmailParser: IMotorClubRequest
    {
        public string RequestId { get; set; }
        public DateTime? RequestDate { get; set; }
        public string BenefitAmount { get; set; }
        public string CustomerId { get; set; }
        public string CustomerName { get; set; }
        public string CustomerPhone { get; set; }
        public string DestinationAddress { get; set; }
        public string DestinationName { get; set; }
        public string DestinationCrossStreet { get; set; }
        public string ETA { get; set; }
        public decimal PayoutFees { get; set; }
        public string PickupAddress { get; set; }
        public string PickupCrossStreet { get; set; }
        public string PickupType { get; set; }
        public string PurchaseOrderNumber { get; set; }
        public string ServiceNeeded { get; set; }
        public string TowingLocationId { get; set; }
        public string TowingProviderId { get; set; }
        public string VehicleClass { get; set; }
        public string VehicleColor { get; set; }
        public string VehicleLicense { get; set; }
        public string VehicleLicenseState { get; set; }
        public string VehicleMake { get; set; }
        public string VehicleModel { get; set; }
        public string VehicleNotes { get; set; }
        public string VehicleType { get; set; }
        public string VehicleVIN { get; set; }
        public string VehicleYear { get; set; }
        public string CustomerAddress { get; set; }
        public string CustomerCity { get; set; }
        public string CustomerState { get; set; }
        public string CustomerZip { get; set; }

        public System.Threading.Tasks.Task PostSave(Entry entry)
        {
            return System.Threading.Tasks.Task.CompletedTask;
        }

        static public ProgressiveTowingEmailParser FromHtml(string html)
        {
            ProgressiveTowingEmailParser result = new ProgressiveTowingEmailParser();
            HtmlDocument doc = new HtmlDocument();
            doc.LoadHtml(html);

            string claimNumber = doc.DocumentNode.SelectNodes("//span[starts-with(.,'Claim #')]")[0].NextSibling.InnerText;
            string requestDate = doc.DocumentNode.SelectNodes("//span[starts-with(.,'Date of loss')]")[0].InnerText.Split(':')[1];
            string vehicleLocation = doc.DocumentNode.SelectNodes("//span[starts-with(.,'VEHICLE LOCATION')]")[0].NextSibling.InnerText;
            string vehicleMakeModel = doc.DocumentNode.SelectNodes("//span[starts-with(.,'MAKE AND MODEL')]")[0].NextSibling.InnerText;
            string damages = doc.DocumentNode.SelectNodes("//span[starts-with(.,'DMGS')]")[0].InnerText.Split(':')[1];
            string vinPlate = doc.DocumentNode.SelectNodes("//span[starts-with(.,'VIN AND PLATE')]")[0].NextSibling.InnerText;
            string vehicleColor = doc.DocumentNode.SelectNodes("//span[starts-with(.,'COLOR')]")[0].NextSibling.InnerText;
            string contact = doc.DocumentNode.SelectNodes("//span[starts-with(.,'CONTACT PERSON')]")[0].NextSibling.InnerText;
            string specialConcerns = doc.DocumentNode.SelectNodes("//span[starts-with(.,'SPECIAL CONCERNS')]")[0].NextSibling.InnerText;
            string towTo = "";

            var node = doc.DocumentNode.SelectNodes("//span[starts-with(.,'Tow to')]")[0].ParentNode.NextSibling;
            while (node != null && node.InnerText != "&nbsp;")
            {
                towTo += node.InnerText.Replace("\r", "").Replace("\n", "") + "\r\n";
                node = node.NextSibling;
            }

            result.RequestDate = Convert.ToDateTime(requestDate);
            result.RequestId = claimNumber;
            result.PickupAddress = vehicleLocation;

            string[] vehicleParts = vehicleMakeModel.Split(' ');
            result.VehicleYear = vehicleParts[0];
            result.VehicleMake = vehicleParts[1];
            result.VehicleModel = string.Join(" ", vehicleParts.Skip(2));
            result.ServiceNeeded = damages;

            string[] vinPlateParts = vinPlate.Split(',');
            result.VehicleVIN = vinPlateParts[0];
            result.VehicleLicense = vinPlateParts[1];

            result.VehicleColor = vehicleColor;

            string[] customerParts = contact.Split(',');
            result.CustomerName = customerParts[1];
            result.CustomerPhone = customerParts[2];

            result.VehicleNotes = specialConcerns;
            result.DestinationAddress = towTo;

            return result;
        }

        static public string CleanHtml(string value)
        {
            return value
                .Replace("&nbsp;", "");
        }
    }
}
