using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Security.Principal;
using System.Threading.Tasks;
using Extric.Towbook.Utility;
using Extric.Towbook.Web;
using Microsoft.AspNetCore.Http.Extensions;
using Microsoft.Extensions.Primitives;
using Microsoft.Net.Http.Headers;
using NLog;

namespace Extric.Towbook.WebShared
{
    public class WebGlobal
    {
        private static readonly Logger logger = LogManager.GetCurrentClassLogger();

        public static readonly int numberOfLoginForLandingPage = 145;
        public static readonly string isFromLandingPage = "IS_FROM_LANDING_PAGE";
        public static readonly string accountIsCreated = "ACCOUNT_IS_CREATED";

        public static readonly string defaultAppDomain = "http://app.towbook.com";

        private static readonly HttpClient httpClient = new(new SocketsHttpHandler
        {
            PooledConnectionLifetime = TimeSpan.FromMinutes(5),
            PooledConnectionIdleTimeout = TimeSpan.FromSeconds(30),
            UseCookies = false
        });

        private static readonly HttpClient httpClientNoRedirects = new(new SocketsHttpHandler
        {
            PooledConnectionLifetime = TimeSpan.FromMinutes(5),
            PooledConnectionIdleTimeout = TimeSpan.FromSeconds(30),
            AllowAutoRedirect = false,
            UseCookies = false
        });


        public static User ImpersonationUser
        {
            get
            {
                //var cookieName = System.Web.Security.FormsAuthentication.FormsCookieName
                //var cookieName = ".XTBAUTHSEC";
                if (HttpContext.Current.Request?.Cookies["X-Impersonation-Session"] != null)
                {
                    //var ticket = System.Web.Security.FormsAuthentication.Decrypt(httpContext.Current.Request.Cookies[cookieName].Value);
                    //if (ticket?.UserData != null)
                    //{
                    //    var user = User.GetById(Convert.ToInt32(ticket.UserData));
                    //    return user;
                    //}
                }
                return null;
            }
        }

        public static Microsoft.AspNetCore.Http.HttpContext httpContext
        {
            get
            {
                return Web.HttpContext.Current;
            }
            set
            {

            }
        }

        public static string CookieDomain
        {
            get
            {
                return Core.GetAppSetting("Towbook:CookieDomain") ?? "towbook.com";
            }
        }

        public static User CurrentUser
        {
            get
            {
                if (HttpContext.Current == null)
                    return null;

                User user = HttpContext.Current.Items["ServiceGlobal.CurrentUser"] as User;

                if (user == null || user.Deleted || user.Disabled)
                {
                    if (HttpContext.Current.User != null && HttpContext.Current.User is GenericPrincipal)
                    {
                        var username = ((GenericPrincipal)HttpContext.Current.User).Identity.Name;
                        var u = User.GetByUsername(username);
                        if (u != null)
                            return u;
                    }
                }
                if (user != null)
                {
                    user = User.GetById(user.Id);

                    if (user.Disabled || user.Deleted)
                        user = null;
                }

                return user;
            }
            set
            {
                throw new NotImplementedException();
            }
        }

        public static async Task<User> CurrentUserAsync()
        {
            if (HttpContext.Current == null)
                return null;

            User user = HttpContext.Current.Items["ServiceGlobal.CurrentUser"] as User;

            if (user == null || user.Deleted || user.Disabled)
            {
                if (HttpContext.Current.User != null && HttpContext.Current.User is GenericPrincipal)
                {
                    var username = ((GenericPrincipal)HttpContext.Current.User).Identity.Name;
                    var u = await User.GetByUsernameAsync(username);
                    if (u != null)
                        return u;
                }
            }
            if (user != null)
            {
                user = await User.GetByIdAsync(user.Id);

                if (user.Disabled || user.Deleted)
                    user = null;
            }

            return user;
        }

        public static Company.Company[] Companies
        {
            get
            {
                if (CurrentUser == null)
                    return new Company.Company[0];

                var x = Company.CompanyUser.GetByUserId(CurrentUser.Id);

                if (x == null || x.Length == 0)
                {
                    return new Company.Company[] { CurrentUser.Company };
                }

                if (HttpContext.Current == null)
                    return Company.Company.GetByIds(x.Select(o => o.CompanyId).ToArray()).ToArray();

                if (HttpContext.Current.Items["wg:companies"] == null)
                {
                    HttpContext.Current.Items["wg:companies"] = Company.Company.GetByIds(x.Select(o => o.CompanyId).ToArray()).ToArray();
                }

                return HttpContext.Current.Items["wg:companies"] as Company.Company[];
            }
        }

        public static string CurrentCompany
        {
            get
            {
                if (CurrentUser.Type == User.TypeEnum.AccountUser)
                    return Accounts.Account.GetById(CurrentUser.AccountId).Company;
                else
                    return CurrentUser.Company.Name;
            }
        }

        public static bool IsMultiCompanyUser
        {
            get
            {
                return Company.CompanyUser.GetByUserId(CurrentUser.Id).Any();
            }
        }

        public static int? StringToNullableInt(string number)
        {
            int intNumber;
            return int.TryParse(number, out intNumber) ? (int?)intNumber : null;
        }

        public static DateTime? OffsetDateTime(DateTime? dt)
        {
            if (dt == null)
                return null;
            else
                return OffsetDateTime(dt.Value);
        }

        public static DateTime OffsetDateTime(DateTime dt, Company.Company company = null)
        {
            if (dt == DateTime.MinValue) return dt;

            if (company == null)
            {
                if (CurrentUser != null)
                    company = CurrentUser.Company;
                else
                    return dt;
            }

            if (company.TimezoneUseDST)
            {
                if (company != null && dt != DateTime.MinValue)
                    return dt.AddHours(company.TimezoneOffset);
                else
                    return dt;
            }
            else
            {
                int extraOffset = 0;
                // take into account daylight savings time not being enabled...
                if (dt.IsDaylightSavingTime())
                {
                    extraOffset = -1;
                }
                if (company != null && dt != DateTime.MinValue)
                    return dt.AddHours(company.TimezoneOffset).AddHours(extraOffset);
                else
                    return dt.AddHours(extraOffset);
            }
        }

        public static DateTime OffsetDateTime(DateTime dateTime, bool reverse, Company.Company company = null)
        {
            return OffsetDateTimeNullable(dateTime, reverse, company).Value;
        }
        public static DateTime? OffsetDateTimeNullable(DateTime? dateTime, bool reverse, Company.Company company = null)
        {
            if (company == null)
            {
                if (CurrentUser != null)
                    company = CurrentUser.Company;
                else
                    return dateTime;
            }

            if (dateTime == null)
                return null;

            var dt = dateTime.Value;
            if (company.TimezoneUseDST)
            {
                if (company != null && dt != DateTime.MinValue)
                    return dt.AddHours(-company.TimezoneOffset);
                else
                    return dt;
            }
            else
            {

                int extraOffset = 0;
                // take into account daylight savings time not being enabled...
                if (dt.IsDaylightSavingTime())
                {
                    extraOffset = 1;
                }
                if (company != null && dt != DateTime.MinValue)
                    return dt.AddHours(-company.TimezoneOffset).AddHours(extraOffset);
                else
                    return dt.AddHours(extraOffset);
            }
        }

        public static string FormatDateTime(object dt) => FormatDateTime(dt, null);


        public static string FormatDateTime(object dt, Company.Company company)
        {
            DateTime dt2 = (DateTime)dt;

            if (dt2 > DateTime.MinValue)
            {
                return (dt2.ToShortDate(company) + " @ " + dt2.ToShortTowbookTimeString());
            }
            else
            {
                return "(never logged in)";
            }
        }

        public static string FormatDisabled(object val)
        {
            bool valx = (bool)val;

            if (valx == true)
            {
                return "<br /><br /><span style=\"margin-top: 10px; font-weight: bold; padding-left: 34px; color: red\">(This user is disabled; to re-enable it for use, click here and check Enable this user)</span>";
            }
            else
            {
                return "";
            }
        }

        public static string[] GetAuthorizedServers()
        {
            return new string[]
            {
                "198.23.126.23", // ws0
                "198.23.126.28", // ws1
                "198.23.126.21",  // ws2
                "198.23.126.24",  // ws3
                "198.23.126.22", // ws4
                "198.23.126.26", // ws5
                "198.23.126.30", //ws8
                "169.55.3.237", //ws9
                "169.55.228.53", //ws10
                "*************", //ws11
                "************", // www2
                "************", // www4
                "*************", // st clair
                "*************", // lb1 
                "***************",
                "************", // ws7
                "************" // ws6
            };
        }


        public static string GetDomain()
        {
            var esUrl = HttpContext.Current.Request.EnvironmentSpecificRequestUrl();

            if (esUrl.Host.ToLowerInvariant().EndsWith("appdomain.cloud"))
            {
                return GetAppDomain();
            }

            return esUrl.Scheme + Uri.SchemeDelimiter +
                    esUrl.Host +
                    (esUrl.IsDefaultPort ? "" : ":" + esUrl.Port);
        }

        public static string GetAppDomain()
        {
            var configValue = Core.GetAppSetting("appDomain");
            return configValue != null ? configValue.ToString() : defaultAppDomain;
        }


        public static string GetResponseFromUrl(string url, bool addDomain = false)
        {
            int none = -255;
            return GetResponseFromUrl(url, out none, addDomain, false);
        }
        public static string GetResponseFromUrl(string url, Dictionary<string, string> addHeaders)
        {
            int none = -255;
            return GetResponseFromUrl(url, out none, false, false, null, addHeaders);
        }

        /// <summary>
        /// Returns the content of a request, using the current domains cookies. Typically should only be using with localhost, or *.towbook.com.
        /// </summary>
        /// <param name="url">URL to request... example: http://app.towbook.com/dispatch/invoice.aspx?id=1243</param>
        /// <param name="statusCode">Status Code that was returned by the request</param>
        /// <returns>string of the response</returns>
        public static string GetResponseFromUrl(string url, out int statusCode, bool addDomain = false, bool returnErrorAsEmptyString = true, string cookieDomain = null, Dictionary<string, string> addHeaders = null)
        {
            var logEvent = new LogEventInfo();
            logEvent.Message = "GetResponseFromUrl";
            logEvent.Level = LogLevel.Info;
            if (WebGlobal.CurrentUser != null)
            {
                logEvent.Properties["companyId"] = WebGlobal.CurrentUser.CompanyId;
                logEvent.Properties["username"] = WebGlobal.CurrentUser.Username;
            }
            logEvent.Properties["type"] = "http_request";

            var Request = HttpContext.Current?.Request;

            if (addDomain || url.StartsWith("/"))
            {
                if (Request != null)
                {
                    string domain = GetDomain();

                    url = domain + "/" + url.TrimStart('/');
                }
            }

            logEvent.Properties["url"] = url;

            try
            {
                using (var request = new HttpRequestMessage(HttpMethod.Get, url))
                {
                    if (addHeaders != null)
                    {
                        foreach (var x in addHeaders.Keys)
                        {
                            request.Headers.Add(x, addHeaders[x]);
                        }
                    }
                    request.Headers.Add("X-Api-Token", AuthenticationToken.GetByUserId(WebGlobal.CurrentUser.Id,
                        Platform.ClientVersion.GetByGitHash("internal-api-server", Platform.ClientVersionType.Internal).Id).Token);


                    var cookieContainer = new CookieCollection();

                    if (HttpContext.Current != null)
                    {
                        foreach (string key in HttpContext.Current.Request.Cookies.Keys)
                        {
                            cookieContainer.Add(new Cookie(key,
                                HttpContext.Current.Request.Cookies[key],
                                "/",
                                string.IsNullOrWhiteSpace(cookieDomain) ? HttpContext.Current.Request.EnvironmentSpecificRequestUrl().Host : cookieDomain));
                        }
                    }
                    request.SetCookie(cookieContainer);

                    try
                    {
                        using (var response = httpClient.Send(request))
                        {
                            statusCode = (int)response.StatusCode;


                            if (response.StatusCode != HttpStatusCode.OK)
                            {
                                if (returnErrorAsEmptyString)
                                {
                                    return "";
                                }
                            }
                            logEvent.Properties["responseCode"] = statusCode;
                            

                            using var s = response.Content.ReadAsStream();
                            using StreamReader sr = new(s, System.Text.Encoding.GetEncoding("utf-8"));

                            
                            var respBody = sr.ReadToEnd();
                            
                            // log the first 256 bytes of the response.
                            logEvent.Properties["response"] = JsonExtensions.Truncate(respBody, 256);

                            return respBody;
                        }
                    }
                    catch (WebException we)
                    {
                        var response = we.Response as HttpWebResponse;

                        if (response != null)
                        {
                            statusCode = (int)response.StatusCode;
                            logEvent.Properties["responseCode"] = statusCode;
                            if (returnErrorAsEmptyString)
                            {
                                return "";
                            }
                            else
                            {
                                using (var s = response.GetResponseStream())
                                {
                                    using (StreamReader sr = new StreamReader(s, System.Text.Encoding.GetEncoding("utf-8")))
                                    {
                                        return sr.ReadToEnd();
                                    }
                                }
                            }
                        }
                        else
                        {
                            statusCode = 437;
                            logEvent.Properties["responseCode"] = statusCode;
                            return "";
                        }
                    }
                }
            }
            catch (Exception e)
            {
                logEvent.Exception = e;
                logEvent.Message = "GetResponseFromUrl failed: " + e.Message;
                logEvent.Properties["exception"] = e;
                logEvent.Level = LogLevel.Error;
                
                throw;
            }
            finally
            {
                logger.Log(logEvent);
            }
        }

        public static async Task<HttpResponseMessage> GetResponseFromUrlAsync(string url, bool addDomain = false, bool returnErrorAsEmptyString = true, string cookieDomain = null, Dictionary<string, string> addHeaders = null)
        {
            if (addDomain || url.StartsWith("/"))
            {
                string domain = GetDomain();
                url = domain + "/" + url.TrimStart('/');
            }

            using (var request = new HttpRequestMessage(HttpMethod.Get, url))
            {
                request.Headers.Add("X-Api-Token", AuthenticationToken.GetByUserId(WebGlobal.CurrentUser.Id,
                    Platform.ClientVersion.GetByGitHash("internal-api-server", Platform.ClientVersionType.Internal).Id).Token);

                if (addHeaders != null)
                {
                    foreach (var x in addHeaders.Keys)
                    {
                        request.Headers.Add(x, addHeaders[x]);
                    }
                }

                var cookieContainer = new CookieCollection();
                if (HttpContext.Current != null)
                {
                    foreach (string key in HttpContext.Current.Request.Cookies.Keys)
                    {
                        cookieContainer.Add(new Cookie(key,
                            HttpContext.Current.Request.Cookies[key],
                            "/",
                            string.IsNullOrWhiteSpace(cookieDomain) ? HttpContext.Current.Request.EnvironmentSpecificRequestUrl().Host : cookieDomain));
                    }
                }
                request.SetCookie(cookieContainer);

                try
                {
                    using (var responseMessage = await httpClient.SendAsync(request))
                    {

                        if (responseMessage.StatusCode != HttpStatusCode.OK)
                        {
                            if (returnErrorAsEmptyString)
                            {
                                return new HttpResponseMessage() { StatusCode = HttpStatusCode.OK, Content = new StringContent("") };
                            }
                            else
                            {
                                /*    throw new Exception(
                                        String.Format("Server error (HTTP {0}: {1}).",
                                            response.StatusCode,
                                            response.StatusDescription));*/
                            }
                        }

                        using (var s = await responseMessage.Content.ReadAsStreamAsync())
                        {
                            using (var sr = new StreamReader(s, System.Text.Encoding.GetEncoding("utf-8")))
                            {
                                return new HttpResponseMessage()
                                {
                                    Content = new StringContent(await sr.ReadToEndAsync()),
                                    StatusCode = responseMessage.StatusCode
                                };
                            }
                        }
                    }
                }
                catch (WebException we)
                {
                    var response = we.Response as HttpWebResponse;

                    if (response != null)
                    {
                        if (returnErrorAsEmptyString)
                        {
                            return new HttpResponseMessage() { Content = new StringContent(""), StatusCode = response.StatusCode };
                        }
                        else
                        {
                            using (var s = response.GetResponseStream())
                            {
                                using (var sr = new StreamReader(s, System.Text.Encoding.GetEncoding("utf-8")))
                                {
                                    return new HttpResponseMessage()
                                    {
                                        Content = new StringContent(await sr.ReadToEndAsync()),
                                        StatusCode = response.StatusCode
                                    };
                                }
                            }
                        }
                    }
                    else
                    {
                        return new HttpResponseMessage() { Content = new StringContent(""), StatusCode = (HttpStatusCode)437 };
                    }
                }
            }
        }

        public static async Task<Stream> GetResponseFromUrlAsStreamAsync(string url, bool addDomain = false, string cookieDomain = null, Dictionary<string, string> addHeaders = null)
        {
            if (addDomain || url.StartsWith("/"))
            {
                string domain = GetDomain();
                url = domain + "/" + url.TrimStart('/');
            }

            using (var request = new HttpRequestMessage(HttpMethod.Get, url))
            {
                request.Headers.Add("X-Api-Token", AuthenticationToken.GetByUserId(WebGlobal.CurrentUser.Id,
                Platform.ClientVersion.GetByGitHash("internal-api-server", Platform.ClientVersionType.Internal).Id).Token);

                if (addHeaders != null)
                {
                    foreach (var x in addHeaders.Keys)
                    {
                        request.Headers.Add(x, addHeaders[x]);
                    }
                }

                var cookieContainer = new CookieCollection();
                if (HttpContext.Current != null)
                {
                    foreach (string key in HttpContext.Current.Request.Cookies.Keys)
                    {
                        cookieContainer.Add(new Cookie(key,
                            HttpContext.Current.Request.Cookies[key],
                            "/",
                            string.IsNullOrWhiteSpace(cookieDomain) ? HttpContext.Current.Request.EnvironmentSpecificRequestUrl().Host : cookieDomain));
                    }
                }
                request.SetCookie(cookieContainer);

                int statusCode;

                try
                {
                    using (var response = await httpClient.SendAsync(request))
                    {
                        statusCode = (int)response.StatusCode;

                        if (response.StatusCode != HttpStatusCode.OK)
                        {
                            throw new Exception(
                                String.Format("Server error (HTTP {0}: {1}).",
                                    response.StatusCode,
                                    response.StatusCode.ToString()));
                        }

                        using (var s = response.Content.ReadAsStream())
                        {
                            var memoryStream = new MemoryStream();
                            s.CopyTo(memoryStream);
                            memoryStream.Position = 0;
                            return memoryStream;
                        }
                    }
                }
                catch (WebException we)
                {
                    var response = we.Response as HttpWebResponse;
                    if (response != null)
                    {
                        statusCode = (int)response.StatusCode;

                        throw new Exception(
                            String.Format("Server error (HTTP {0}: {1}).",
                                response.StatusCode,
                                response.StatusDescription));
                    }
                    else
                    {
                        statusCode = 437;
                        using (StreamReader sr = new StreamReader("", System.Text.Encoding.GetEncoding("utf-8")))
                        {
                            return sr.BaseStream;
                        }
                    }
                }
            }
        }

        public static Stream GetResponseFromUrlAsStream(string url, out int statusCode, bool addDomain = false, string cookieDomain = null, Dictionary<string, string> addHeaders = null)
        {
            if (addDomain || url.StartsWith("/"))
            {
                string domain = GetDomain();
                url = domain + "/" + url.TrimStart('/');
            }

            using (var request = new HttpRequestMessage(HttpMethod.Get, url))
            {
                request.Headers.Add("X-Api-Token", AuthenticationToken.GetByUserId(WebGlobal.CurrentUser.Id,
                Platform.ClientVersion.GetByGitHash("internal-api-server", Platform.ClientVersionType.Internal).Id).Token);

                if (addHeaders != null)
                {
                    foreach (var x in addHeaders.Keys)
                    {
                        request.Headers.Add(x, addHeaders[x]);
                    }
                }

                var cookieContainer = new CookieCollection();
                if (HttpContext.Current != null)
                {
                    foreach (string key in HttpContext.Current.Request.Cookies.Keys)
                    {
                        cookieContainer.Add(new Cookie(key,
                            HttpContext.Current.Request.Cookies[key],
                            "/",
                            string.IsNullOrWhiteSpace(cookieDomain) ? HttpContext.Current.Request.EnvironmentSpecificRequestUrl().Host : cookieDomain));
                    }
                }
                request.SetCookie(cookieContainer);
                HttpResponseMessage response = null;
                try
                {
                    response = httpClientNoRedirects.Send(request);
                    {
                        statusCode = (int)response.StatusCode;


                        // Manually check if the response is a redirection
                        if (response.StatusCode == System.Net.HttpStatusCode.Redirect ||
                            response.StatusCode == System.Net.HttpStatusCode.MovedPermanently ||
                            response.StatusCode == System.Net.HttpStatusCode.Found ||
                            response.StatusCode == System.Net.HttpStatusCode.SeeOther ||
                            response.StatusCode == System.Net.HttpStatusCode.TemporaryRedirect)
                        {
                            // Get the URL to redirect to
                            Uri redirectUrl = response.Headers.Location;

                            request.RequestUri = redirectUrl;
                            return GetResponseFromUrlAsStream(redirectUrl.ToString(), out statusCode, false, null, null);

                            // Decide if you want to follow the redirect and make a new request
                            response = httpClientNoRedirects.Send(request);
                        }

                        if (response.StatusCode != HttpStatusCode.OK)
                        {
                            throw new Exception(
                                String.Format("Server error (HTTP {0}: {1}).",
                                    response.StatusCode,
                                    response.StatusCode.ToString()));
                        }

                        using (var s = response.Content.ReadAsStream())
                        {
                            var memoryStream = new MemoryStream();
                            s.CopyTo(memoryStream);
                            memoryStream.Position = 0;
                            return memoryStream;
                        }
                    }
                }
                catch (WebException we)
                {
                    using (var response2 = we.Response as HttpWebResponse)
                    {
                        if (response2 != null)
                        {
                            statusCode = (int)response2.StatusCode;

                            throw new Exception(
                                String.Format("Server error (HTTP {0}: {1}).",
                                    response2.StatusCode,
                                    response2.StatusDescription));
                        }
                        else
                        {
                            statusCode = 437;
                            using (StreamReader sr = new StreamReader("", System.Text.Encoding.GetEncoding("utf-8")))
                            {
                                return sr.BaseStream;
                            }
                        }
                    }
                }
                finally
                {
                    if (response != null)
                        response.Dispose();
                }
            }
        }

        /// <summary>
        /// Formats a phone number into a format of (xxx) yyy-zzzz... this is only valid in North America... for other countries,
        /// we need to modify this to respect the locale.
        /// </summary>
        /// <param name="phone">input phone number, may contain -, ., (, ), or spaces.</param>
        /// <returns>Cleanly formatted phone number</returns>
        public static string FormatPhone(string phone)
        {
            return Core.FormatPhone(phone);
        }

        public static void OverrideCompanyForThisRequestOnly(int p, User user)
        {
            if (user.HasAccessToCompany(p))
            {
                HttpContext.Current.Items["ServiceGlobal.CompanyTemp"] = p;
            }
            else
                throw new TowbookException("Access Denied");
        }

        /// <summary>
        /// Get a list of companies that the current user has access to.
        /// </summary>
        /// <returns></returns>
        public static async Task<Company.Company[]> GetCompaniesAsync()
        {
            var currentUser = await CurrentUserAsync()
;            if (currentUser == null)
                return new Company.Company[0];

            var x = await Company.CompanyUser.GetByUserIdAsync(currentUser.Id);

            if (x == null || x.Length == 0)
            {
                return new Company.Company[] { currentUser.Company };
            }

            if (HttpContext.Current == null)
                return (await Company.Company.GetByIdsAsync(x.Select(o => o.CompanyId).ToArray()))
                    .ToArray();

            if (HttpContext.Current.Items["wg:companies"] == null)
            {
                HttpContext.Current.Items["wg:companies"] = (await Company.Company.GetByIdsAsync(x.Select(o => o.CompanyId).ToArray()))
                    .ToArray();
            }

            return HttpContext.Current.Items["wg:companies"] as Company.Company[];
        }

        const string HeaderFrontdoorId = "X-Azure-FDID";
        const string HeaderFrontdoorClientIp = "X-Azure-ClientIP";
        const string HeaderFrontdoorHost = "X-Forwarded-Host";

        const string FrontdoorId = "1306f249-1995-4ffc-8833-0ce6eae5bd3f";

        public static string GetRequestingIp()
        {
            var req = HttpContext.Current.Request;
            var connection = HttpContext.Current.Connection;

            if (req != null && connection != null)
            {

                var userHostAddress = connection.RemoteIpAddress.ToString();

                if (req.Headers[HeaderFrontdoorId] == FrontdoorId)
                {
                    //TODO: compare if it's null
                    if (req.Headers[HeaderFrontdoorClientIp] != "")
                        return req.Headers[HeaderFrontdoorClientIp];
                }

                if (userHostAddress.StartsWith("10.") ||
                    userHostAddress.StartsWith("192.") ||
                    userHostAddress.StartsWith("172.") ||
                    userHostAddress == "*************" ||
                    userHostAddress == "*************")
                {
                    string ip = req.Headers["X-Forwarded-For"]; // load balancer
                    if (!string.IsNullOrWhiteSpace(req.Headers["X-Twbk-Client-IP"]))
                    {
                        return req.Headers["X-Twbk-Client-IP"];
                    }
                    if (string.IsNullOrWhiteSpace(ip))
                    {
                        return userHostAddress;
                    }

                    return ip;
                }
                else
                {
                    return userHostAddress;
                }
            }
            else
            {
                return "0.0.0.0";
            }
        }

        public static string GetHostName()
        {
            var esUrl = HttpContext.Current.Request.EnvironmentSpecificRequestUrl();

            return esUrl.Host; 
        }

    }

    public static class WebGlobalExtensions
    {
        public static CookieCollection GetCookie(this HttpRequestMessage httpRequestMessage)
        {
            Uri requestUri = httpRequestMessage.RequestUri;
            var cookieContainer = new CookieContainer();
            if (httpRequestMessage.Headers.TryGetValues(HeaderNames.Cookie, out var cookieValueList))
            {
                foreach (var value in cookieValueList)
                {
                    cookieContainer.SetCookies(requestUri, value);
                }
            }

            return cookieContainer.GetCookies(requestUri);
        }

        /// <summary>
        /// when using this, make sure to set UseCookies = false on the HttpClient. 
        /// </summary>
        /// <param name="httpRequestMessage"></param>
        /// <param name="cookieCollection"></param>
        public static void SetCookie(this HttpRequestMessage httpRequestMessage,
            CookieCollection cookieCollection = null)
        {
            if (cookieCollection is null)
            {
                return;
            }

            httpRequestMessage.Headers.Remove(HeaderNames.Cookie);

            foreach (Cookie cookie in cookieCollection)
            {
                if (cookie is not null)
                {
                    httpRequestMessage.Headers.Add(HeaderNames.Cookie, cookie.ToString());
                }
            }
        }


        private static Uri EnvironmentSpecificRequestUrl(Uri uri, Uri referrerUri, IDictionary<string, StringValues> headers, bool ignorePortNumber = true)
        {
            UriBuilder hostUrl = new UriBuilder();
            string hostHeader = headers["Host"];

            if (hostHeader.Contains(":"))
            {
                hostUrl.Host = hostHeader.Split(':')[0];

                if (!ignorePortNumber)
                    hostUrl.Port = Convert.ToInt32(hostHeader.Split(':')[1]);
                else
                    hostUrl.Port = -1;
            }
            else
            {
                hostUrl.Host = hostHeader;
                hostUrl.Port = -1;
            }

            Uri url = uri;
            UriBuilder uriBuilder = new UriBuilder(url);

            if (String.Equals(hostUrl.Host, "localhost", StringComparison.OrdinalIgnoreCase) || hostUrl.Host == "127.0.0.1")
            {

                // Do nothing
                // When we're running the application from localhost (e.g. debugging from Visual Studio), we'll keep everything as it is.
                // We're not using request.IsLocal because it returns true as long as the request sender and receiver are in same machine.
                // What we want is to only ignore the requests to 'localhost' or the loopback IP '127.0.0.1'.
                if (referrerUri == null)
                {
                    return uriBuilder.Uri;
                }
                else
                // if using a nginx reverse proxy for localhost development use the referrerUri
                {
                    return referrerUri;
                }
            }

            // When the application is run behind a load-balancer (or forward proxy), request.IsSecureConnection returns 'true' or 'false'
            // based on the request from the load-balancer to the web server (e.g. IIS) and not the actual request to the load-balancer.
            // The same is also applied to request.Url.Scheme (or uriBuilder.Scheme, as in our case).
            bool isSecureConnection = (headers.ContainsKey("X-Forwarded-Proto") && String.Equals(headers["X-Forwarded-Proto"], "https", StringComparison.OrdinalIgnoreCase)) || uriBuilder.Scheme == "https";

            if (isSecureConnection)
            {
                uriBuilder.Port = hostUrl.Port == -1 ? 443 : hostUrl.Port;
                uriBuilder.Scheme = "https";
            }
            else
            {
                uriBuilder.Port = hostUrl.Port == -1 ? 80 : hostUrl.Port;
                uriBuilder.Scheme = "http";
            }

            uriBuilder.Host = hostUrl.Host;

            if (uriBuilder.Host.ToLowerInvariant().StartsWith("beta."))
                uriBuilder.Host = uriBuilder.Host.Substring(5);

            return uriBuilder.Uri;
        }


        public static Uri EnvironmentSpecificRequestUrl(this Microsoft.AspNetCore.Http.HttpRequest request)
        {
            return EnvironmentSpecificRequestUrl(new Uri(request.GetEncodedUrl()), request.Headers.ContainsKey("referer") ? new Uri(request.Headers["referer"]): null, request.Headers);
        }


        /// <summary>
        /// Use this instead of DateTime.ToShortDateString() - it will use the current users country to format it accordingly.
        /// </summary>
        /// <param name="dt"></param>
        /// <returns></returns>
        //public static string ToShortDate(this DateTime dt) => dt.ToString("d",
        //    WebGlobal.CurrentUser?.Company.CultureInfo ?? new CultureInfo("en-US"));
        public static string ToShortDate(this DateTime dt, Company.Company company) => dt.ToString("d",
            company?.Country != Company.Company.CompanyCountry.Canada ? company?.CultureInfo ?? new CultureInfo("en-US") : new CultureInfo("en-US"));

        public static string ToShortDate(this DateTime? dt, Company.Company company) => dt != null ? dt.Value.ToShortDate(company) : null;

        public static string ToMoney(this decimal money) => money.ToString("C",
            WebGlobal.CurrentUser?.Company.CultureInfo ?? new CultureInfo("en-US"));

        public static string ToMoney(this decimal money, Company.Company company) => money.ToString("C",
            company?.CultureInfo ?? new CultureInfo("en-US"));

    }

}
