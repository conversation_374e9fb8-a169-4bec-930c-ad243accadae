<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
  </PropertyGroup>

  <ItemGroup>
	<PackageReference Include="ITfoxtec.Identity.Saml2" Version="4.11.3" />
	<PackageReference Include="Microsoft.AspNet.WebApi.Client " Version="5.2.7" />  
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.21.0" />
    <PackageReference Include="NLog" Version="5.3.3" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.7.1" />
    <!--<PackageReference Include="System.Runtime.Caching" Version="7.0.0" />-->
	<PackageReference Include="System.Runtime.Caching" Version="8.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Extric.Towbook\Extric.Towbook.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Update="Microsoft.AspNet.WebApi.Client" Version="6.0.0" />
  </ItemGroup>
	
</Project>
