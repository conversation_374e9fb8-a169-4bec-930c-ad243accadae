using System;
using System.Collections.Generic;
using System.IO;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Extric.Towbook.Configuration;
using Newtonsoft.Json;
using NLog;
using Extric.Towbook.Utility;

namespace Extric.Towbook.WebShared.Net5;

public sealed class PdfClient : PdfClientBase
{
    public static void Init()
    {
        Init(new(new SocketsHttpHandler
        {
            PooledConnectionLifetime = TimeSpan.FromMinutes(5),
            PooledConnectionIdleTimeout = TimeSpan.FromSeconds(30),
            UseCookies = false
        }));
    }
}
