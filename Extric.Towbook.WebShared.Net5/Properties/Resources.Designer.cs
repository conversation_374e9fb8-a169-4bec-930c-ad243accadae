using System.Globalization;
using System.Reflection;
using System.Resources;

namespace Extric.Towbook.Web
{
    internal static class Resources
    {
        private static readonly ResourceManager _resourceManager = new ResourceManager("Extric.Towbook.WebShared.Net5.Resources", typeof(Resources).GetTypeInfo().Assembly);

        //
        // Summary:
        //     The request is invalid.
        internal static string HttpError_BadRequest => GetString("HttpError_BadRequest");

        //
        // Summary:
        //     An error has occurred.
        internal static string HttpError_GenericError => GetString("HttpError_GenericError");

        //
        // Summary:
        //     The model state is valid.
        internal static string HttpError_ValidModelState => GetString("HttpError_ValidModelState");

        //
        // Summary:
        //     Could not find a formatter matching the media type '{0}' that can write an instance
        //     of '{1}'.
        internal static string HttpRequestMessage_CouldNotFindMatchingFormatter => GetString("HttpRequestMessage_CouldNotFindMatchingFormatter");

        //
        // Summary:
        //     The {0} instance is not properly initialized. Use {1} to create an {0} for the
        //     current request.
        internal static string HttpRequestMessage_MustHaveHttpContext => GetString("HttpRequestMessage_MustHaveHttpContext");

        //
        // Summary:
        //     The {0} only supports writing objects of type {1}.
        internal static string HttpResponseMessageFormatter_UnsupportedType => GetString("HttpResponseMessageFormatter_UnsupportedType");

        //
        // Summary:
        //     The key is invalid JQuery syntax because it is missing a closing bracket.
        internal static string JQuerySyntaxMissingClosingBracket => GetString("JQuerySyntaxMissingClosingBracket");

        //
        // Summary:
        //     The number of keys in a NameValueCollection has exceeded the limit of '{0}'.
        //     You can adjust it by modifying the MaxHttpCollectionKeys property on the '{1}'
        //     class.
        internal static string MaxHttpCollectionKeyLimitReached => GetString("MaxHttpCollectionKeyLimitReached");

        //
        // Summary:
        //     Processing of the HTTP request resulted in an exception. Please see the HTTP
        //     response returned by the 'Response' property of this exception for details.
        internal static string HttpResponseExceptionMessage => GetString("HttpResponseExceptionMessage");

        //
        // Summary:
        //     Failed to generate a URL using route '{0}'.
        internal static string CreatedAtRoute_RouteFailed => GetString("CreatedAtRoute_RouteFailed");

        //
        // Summary:
        //     URL
        internal static string BindingSource_URL => GetString("BindingSource_URL");

        //
        // Summary:
        //     The request is invalid.
        internal static string FormatHttpError_BadRequest()
        {
            return GetString("HttpError_BadRequest");
        }

        //
        // Summary:
        //     An error has occurred.
        internal static string FormatHttpError_GenericError()
        {
            return GetString("HttpError_GenericError");
        }

        //
        // Summary:
        //     The model state is valid.
        internal static string FormatHttpError_ValidModelState()
        {
            return GetString("HttpError_ValidModelState");
        }

        //
        // Summary:
        //     Could not find a formatter matching the media type '{0}' that can write an instance
        //     of '{1}'.
        internal static string FormatHttpRequestMessage_CouldNotFindMatchingFormatter(object p0, object p1)
        {
            return string.Format(CultureInfo.CurrentCulture, GetString("HttpRequestMessage_CouldNotFindMatchingFormatter"), p0, p1);
        }

        //
        // Summary:
        //     The {0} instance is not properly initialized. Use {1} to create an {0} for the
        //     current request.
        internal static string FormatHttpRequestMessage_MustHaveHttpContext(object p0, object p1)
        {
            return string.Format(CultureInfo.CurrentCulture, GetString("HttpRequestMessage_MustHaveHttpContext"), p0, p1);
        }

        //
        // Summary:
        //     The {0} only supports writing objects of type {1}.
        internal static string FormatHttpResponseMessageFormatter_UnsupportedType(object p0, object p1)
        {
            return string.Format(CultureInfo.CurrentCulture, GetString("HttpResponseMessageFormatter_UnsupportedType"), p0, p1);
        }

        //
        // Summary:
        //     The key is invalid JQuery syntax because it is missing a closing bracket.
        internal static string FormatJQuerySyntaxMissingClosingBracket()
        {
            return GetString("JQuerySyntaxMissingClosingBracket");
        }

        //
        // Summary:
        //     The number of keys in a NameValueCollection has exceeded the limit of '{0}'.
        //     You can adjust it by modifying the MaxHttpCollectionKeys property on the '{1}'
        //     class.
        internal static string FormatMaxHttpCollectionKeyLimitReached(object p0, object p1)
        {
            return string.Format(CultureInfo.CurrentCulture, GetString("MaxHttpCollectionKeyLimitReached"), p0, p1);
        }

        //
        // Summary:
        //     Processing of the HTTP request resulted in an exception. Please see the HTTP
        //     response returned by the 'Response' property of this exception for details.
        internal static string FormatHttpResponseExceptionMessage()
        {
            return GetString("HttpResponseExceptionMessage");
        }

        //
        // Summary:
        //     Failed to generate a URL using route '{0}'.
        internal static string FormatCreatedAtRoute_RouteFailed(object p0)
        {
            return string.Format(CultureInfo.CurrentCulture, GetString("CreatedAtRoute_RouteFailed"), p0);
        }

        //
        // Summary:
        //     URL
        internal static string FormatBindingSource_URL()
        {
            return GetString("BindingSource_URL");
        }

        private static string GetString(string name, params string[] formatterNames)
        {
            string text = _resourceManager.GetString(name);
            if (formatterNames != null)
            {
                for (int i = 0; i < formatterNames.Length; i++)
                {
                    text = text.Replace("{" + formatterNames[i] + "}", "{" + i + "}");
                }
            }

            return text;
        }
    }
}
