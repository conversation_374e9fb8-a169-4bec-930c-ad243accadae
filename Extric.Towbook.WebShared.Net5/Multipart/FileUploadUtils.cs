using System.IO;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;

namespace Extric.Towbook.WebShared.Multipart
{
    public class FileUploadUtils
    {
        public static void saveAs(IFormFile file, string fn)
        {
            using (FileStream fileStream = new FileStream(fn, FileMode.Create, FileAccess.Write))
            {
                byte[] bytes = GetByteArrayFromImageAsync(file);
                fileStream.Write(bytes, 0, bytes.Length);
            }
        }

        public static async Task<object> saveAsAsync(IFormFile file, string fn)
        {
            using (Stream fileStream = new FileStream(fn, FileMode.Create))
            {
                await file.CopyToAsync(fileStream);
            }
            return file;
        }


        private static byte[] GetByteArrayFromImageAsync(IFormFile file)
        {
            using (var target = new MemoryStream())
            {
                file.CopyTo(target);
                return target.ToArray();
            }
        }


    }
}
