using System;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System.Configuration;

using Extric.Towbook;
using Extric.Towbook.Utility;
using Extric.Towbook.Integrations.Networkfleet;
using System.Collections.Generic;

namespace Extric.Towbook.Integrations.Networkfleet.Tests
{
    [TestClass]
    public class NetworkfleetTests
    {
        private static string login;
        private static string password;
 
        [ClassInitialize]
        public static void InitTestSuite(TestContext testContext)
        {
            NetworkfleetTests.login = ConfigurationManager.AppSettings["Networkfleet.login"];
            NetworkfleetTests.password = ConfigurationManager.AppSettings["Networkfleet.password"];
        }

        [TestMethod]
        [ExpectedException(typeof(InvalidLoginException), "When the login has failed an InvalidLoginException should be thrown")]
        public void CreateFailureTest()
        {
             var service = NetworkfleetService.Create("invalid_user", "");
        }

        [TestMethod]
        public void CreateTest()
        {
            var service = NetworkfleetService.Create(NetworkfleetTests.login, NetworkfleetTests.password);

            Assert.IsInstanceOfType(service, typeof(NetworkfleetService));
        }
        
        [TestMethod]
        public void GetVehiclesTest()
        {
            var service = NetworkfleetService.Create(NetworkfleetTests.login, NetworkfleetTests.password);
            var vehicles = service.GetVehicles();

            Assert.IsInstanceOfType(vehicles, typeof(List<Vehicle>));

            foreach(Vehicle vehicle in vehicles)
            {
                Console.WriteLine("Vehicle: {0}", vehicle.ToJson());

                Assert.IsInstanceOfType(vehicle.Id, typeof(long));
                Assert.IsInstanceOfType(vehicle.Vin, typeof(Composed));
                Assert.IsInstanceOfType(vehicle.Label, typeof(string));
                Assert.IsInstanceOfType(vehicle.Color, typeof(string));
                Assert.IsInstanceOfType(vehicle.Make, typeof(string));
                Assert.IsInstanceOfType(vehicle.Model, typeof(string));

                if (vehicle.DriverId != null)
                    Assert.IsInstanceOfType(vehicle.DriverId, typeof(Composed));

                Assert.IsInstanceOfType(vehicle.DeviceSerialNumber, typeof(Composed));
                Assert.IsInstanceOfType(vehicle.Year, typeof(int));
                
                Assert.IsInstanceOfType(vehicle.Odometer, typeof(Odometer));
                Assert.IsInstanceOfType(vehicle.Odometer.Timestamp, typeof(DateTime));
                Assert.IsInstanceOfType(vehicle.Odometer.Units, typeof(string));
                Assert.IsInstanceOfType(vehicle.Odometer.Value, typeof(double));

                if (vehicle.EngineRunTime != null)
                {
                    Assert.IsInstanceOfType(vehicle.EngineRunTime, typeof(EngineRunTime));
                    Assert.IsInstanceOfType(vehicle.EngineRunTime.Timestamp, typeof(DateTime));
                    Assert.IsInstanceOfType(vehicle.EngineRunTime.IsTracked, typeof(string));
                    Assert.IsInstanceOfType(vehicle.EngineRunTime.Value, typeof(string));
                }

                Assert.IsInstanceOfType(vehicle.LicensePlate, typeof(LicensePlate));
                Assert.IsInstanceOfType(vehicle.LicensePlate.State, typeof(string));
                Assert.IsInstanceOfType(vehicle.LicensePlate.Country, typeof(string));
                Assert.IsInstanceOfType(vehicle.LicensePlate.Value, typeof(string));

                Assert.IsInstanceOfType(vehicle.TrackableItemType, typeof(string));
                Assert.IsInstanceOfType(vehicle.FuelType, typeof(string));
                Assert.IsInstanceOfType(vehicle.CreatedTimestamp, typeof(Timestamp));
                Assert.IsInstanceOfType(vehicle.ModifiedTimestamp, typeof(Timestamp));                
            }
        }

        [TestMethod]
        public void GetVehiclesWithFiltersTest()
        {
            int index = 0;
            int limit = 5;

            var service = NetworkfleetService.Create(NetworkfleetTests.login, NetworkfleetTests.password);
            var vehicles = service.GetVehicles(index, limit);

            Assert.IsInstanceOfType(vehicles, typeof(List<Vehicle>));
            Assert.AreEqual(vehicles.Count, limit);
        }

        [TestMethod]
        [ExpectedException(typeof(Exception), "An exception should be thrown")]
        public void GetVehiclesWithFiltersExceptionTest()
        {
            int index = 0;
            int limit = -1; // invalid value for limit, it should be greater than 0

            var service = NetworkfleetService.Create(NetworkfleetTests.login, NetworkfleetTests.password);
            var vehicles = service.GetVehicles(index, limit);
        }

        [TestMethod]
        public void GetVehicleTest()
        {
            long vehicleId = 436575;
            var service = NetworkfleetService.Create(NetworkfleetTests.login, NetworkfleetTests.password);

            var res = service.GetVehicle(vehicleId);
        }

        [TestMethod]
        public void GetVehicleOdometerTest()
        {
            var service = NetworkfleetService.Create(NetworkfleetTests.login, NetworkfleetTests.password);
            var vehicleId = 436575;
            var odometer = service.GetVehicleOdometer(vehicleId);

            Assert.IsInstanceOfType(odometer, typeof(Odometer));
        }

        [TestMethod]
        public void GetVehicleEngineRuntimeTest()
        {
            var service = NetworkfleetService.Create(NetworkfleetTests.login, NetworkfleetTests.password);
            var vehicleId = 436575;
            var engineRuntime = service.GetVehicleEngineRuntime(vehicleId);

            Assert.IsInstanceOfType(engineRuntime, typeof(EngineRunTime));
        }


        [TestMethod]
        public void GetLocationsTest()
        {
            var service = NetworkfleetService.Create(NetworkfleetTests.login, NetworkfleetTests.password);
            var locations = service.GetLocations();

            Assert.IsInstanceOfType(locations, typeof(List<Location>));

            foreach (var location in locations)
            {
                Console.WriteLine("LOC: {0}", location.ToJson());
            }
        }

        [TestMethod]
        public void GetLocationsWithFiltersTest()
        {
            var service = NetworkfleetService.Create(NetworkfleetTests.login, NetworkfleetTests.password);
            int index = 0;
            int limit = 5;

            var locations = service.GetLocations(index, limit);

            Assert.IsInstanceOfType(locations, typeof(List<Location>));
            Assert.AreEqual(locations.Count, limit);
        }

        [TestMethod]
        public void GetVehicleLocationsTest()
        {
            var service = NetworkfleetService.Create(NetworkfleetTests.login, NetworkfleetTests.password);
            var vehicleId = 436575;
            int index = 0;
            int limit = 5;

            var locations = service.GetVehicleLocations(vehicleId);
            Assert.IsInstanceOfType(locations, typeof(List<Location>));

            locations = service.GetVehicleLocations(vehicleId, index, limit);
            Assert.AreEqual(locations.Count, limit);
        }

        [TestMethod]
        public void GetSensorsTest()
        {
            var service = NetworkfleetService.Create(NetworkfleetTests.login, NetworkfleetTests.password);
            var sensors = service.GetSensors();

            Assert.IsInstanceOfType(sensors, typeof(List<Sensor>));

            Console.WriteLine("Console.count = {0}", sensors.Count);

            foreach (var sensor in sensors)
            {
                Console.WriteLine("Sensor: {0}", sensor.ToJson());
            }
        }

        [TestMethod]
        public void GetSensorTest()
        {
            var service = NetworkfleetService.Create(NetworkfleetTests.login, NetworkfleetTests.password);
            var id = 78781;
            var sensor = service.GetSensor(id);

            Assert.IsInstanceOfType(sensor, typeof(Sensor));
        }

        //[TestMethod]
        //public void CreateSensorTest()
        //{
        //    var service = NetworkfleetService.Create(NetworkfleetTests.login, NetworkfleetTests.password);
        //    var sensor = new Sensor();
        //    sensor.Name = "test sensor #1";
        //    sensor.Description = "sample description";

        //    var result = service.CreateSensor(sensor);

        //    Console.WriteLine("SensorID: {0}", result.ToString());
        //}

        [TestMethod]
        public void GetDeviceAlertsTest()
        {
            var service = NetworkfleetService.Create(NetworkfleetTests.login, NetworkfleetTests.password);
            var deviceAlerts = service.GetDeviceAlerts();

            Assert.IsInstanceOfType(deviceAlerts, typeof(List<DeviceAlert>));
            Console.WriteLine("Found {0} device alerts", deviceAlerts.Count);
        }

        [TestMethod]
        public void GetVehicleDeviceAlertsTest()
        {
            var service = NetworkfleetService.Create(NetworkfleetTests.login, NetworkfleetTests.password);
            var vehicleId = 436575;
            var deviceAlerts = service.GetVehicleDeviceAlerts(vehicleId);

            Assert.IsInstanceOfType(deviceAlerts, typeof(List<DeviceAlert>));
            Console.WriteLine("Found {0} device alerts for vehicleId: {1}", deviceAlerts.Count, vehicleId);
        }
    }
}
