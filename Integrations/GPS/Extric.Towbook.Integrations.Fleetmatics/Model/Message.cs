using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace Extric.Towbook.Integrations.Fleetmatics.Model
{
    public enum MessageStatus
    {
        None = 0,
        Success = 1,
        Failed = 2,
        Format_Ok = 3,
        Partial_Success = 4
    }

    public class Message
    {
        //public int Id { get; set; }

        //The Text of the message to be sent
        public string Body { get; set; }

        public List<int> VehicleIds { get; set; }

        public IVehicle Vehicle { get; set; }

        //A valid address of the Stop
        public string Address { get; set; }

        public DateTime LastModified { get; private set; }

        public MessageStatus Status { get; set; }

        public string ErrorDescription { get; private set; }

        public Message()
        {}

        public Message(XElement s)
        {
            //Id = Convert.ToInt32(s.Attribute("ivehicleid").Value);

            this.Body = s.Element("address").Value;
            
            this.VehicleIds = new List<int>();

            var vehicleElements = s.Element("vehicle");
            if (vehicleElements != null)
            {
                var vehicles = s.Element("vehicle").Elements();
                foreach (var v in vehicles)
                {
                    this.VehicleIds.Add(Convert.ToInt32(v.Value));
                }
            }

            this.Vehicle = new Model.Vehicle();
            //{
            //    Data = new VehicleData()
            //};

            var vehicleExternalIdElement = s.Element("van_external_id");
            if (vehicleExternalIdElement != null)
            {
                this.Vehicle.Id = vehicleExternalIdElement.Value;
            }

            var latitudElement = s.Element("lat");
            if (latitudElement != null)
            {
                this.Vehicle.Latitude = Convert.ToDecimal(latitudElement.Value);
            }

            var longitudeElement = s.Element("lng");
            if (longitudeElement != null)
            {
                this.Vehicle.Longitude = Convert.ToDecimal(longitudeElement.Value);
            }

            var addressElement = s.Element("address");
            if (addressElement != null)
            {
                this.Address = addressElement.Value;
            }

            var lastModifiedElement = s.Element("last_modified");
            if (lastModifiedElement != null && !String.IsNullOrEmpty(lastModifiedElement.Value))
            {
                this.LastModified = DateTime.Parse(lastModifiedElement.Value);
            }

            var messageStatus = MessageStatus.None;
            var statusElement = s.Element("status");
            if (statusElement == null)
            {
                statusElement = s.Element("message_status");

                if (Enum.TryParse<MessageStatus>(s.Element("status").Value, true, out messageStatus))
                {
                    this.Status = messageStatus;
                }
            }
            else
            {
                if (Enum.TryParse<MessageStatus>(statusElement.Value, true, out messageStatus))
                {
                    this.Status = messageStatus;
                }
            }

            var errorDescElement = s.Element("error_desc");
            if (errorDescElement != null)
            {
                this.ErrorDescription = errorDescElement.Value;
            }
        }
    }
}
