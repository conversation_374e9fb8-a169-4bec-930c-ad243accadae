using fm = Extric.Towbook.Integrations.Fleetmatics.Model;
using Extric.Towbook.Utility;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;
using Extric.Towbook.WebShared;

namespace Extric.Towbook.Integrations.Fleetmatics
{
    public class GarminHelper
    {
        public const string rootName = "Fleetmatics";
        public static string baseUrl = Core.GetAppSetting<string>("baseFleetmaticsURL"); //http://www.fleetmatics-usa.com/

        public static string BuildUrlSecureSegment(string userId, Guid guid, DateTime dateTime)
        {
            //TODO> ask how to get the encryption Key
            AesManaged aes = new AesManaged();
            aes.KeySize = 256;
            aes.GenerateKey();
            aes.GenerateIV();

            EncryptionHelper helper = new EncryptionHelper();
            helper.Key = aes.Key;
            helper.IV = aes.IV;

            var encUserId = helper.EncryptStringToBytes_Aes(userId);

            var timestamp = dateTime.ToString("yyyyMMddHHmmssffff");
            var encTimeStamp = EncryptionHelper.EncryptWithSha1(guid, timestamp);

            return string.Format("?bt={0}&cs={1}&ts={2}", encUserId, encTimeStamp, timestamp);
        }

        #region Send Stop Service
        public static XDocument SendStopRequest(List<fm.Stop> stops) //TODO: define structures
        {
            var xDoc = new XDocument(
                new XElement(rootName,
                    new XElement("stopRequest",
                        new XElement("optimize", false),
                        new XElement("stops"))
                ));

            var stopElements = xDoc.Element(rootName).Element("stopRequest").Element("stops");

            foreach (var s in stops)
            {
                stopElements.Add(new XElement("stop",
                    new XElement("poi_external_id"), //Don't know what this is - OPTIONAL
                    new XElement("van_external_id", s.Vehicle.Id), //Vehicle ID - REQUIRED
                    new XElement("lat", s.Vehicle.Latitude), // Latitud - OPTIONAL, but Required if Address is not set
                    new XElement("lng", s.Vehicle.Longitude), // Longitude - OPTIONAL, but Required if Address is not set
                    new XElement("address", s.Address), //Address of the stop - OPTIONAL, but required if Lat and Lng are not set
                    new XElement("desc", s.Description)) //The description of the stop - REQUIRED
                    );
            }

            int companyId = WebGlobal.CurrentUser.CompanyId;
            //TODO: Create the appropiate keys
            //Get the User Id/Pass from the CompanyKeyValue store            
            var userId = VehicleExtensions.GetUserIdValue(companyId);
            var guid = VehicleExtensions.GetGuidValue(companyId);

            //Build URL
            /*
             * Sample URL:
             * http://www.fleetmaticsusa.com/GarminAPI.svc/api/stop/sendstops?bt={EcryptedUserId}&cs={SHA(Guid+TimeStamp)}&ts={timestamp}
             */

            var url = baseUrl + "GarminAPI.svc/api/stop/sendstops";
            url += BuildUrlSecureSegment(userId.Value, Guid.Parse(guid.Value), DateTime.Now);

            return FleetmaticsXHRHelper.SendStops(url, xDoc);
        }

        public static XDocument CancelStopRequest(fm.Stop stop)
        {
            var xDoc = new XDocument(
                new XElement(rootName,
                    new XElement("stopRequest",
                        new XElement("stops")) //REQUIRED
                ));

            var stops = xDoc.Element(rootName).Element("stopRequest").Element("stops");

            //foreach (var s in stops)
            foreach (var s in Enumerable.Range(1, 5))
            {
                stops.Add(new XElement("stop", //REQUIRED
                    new XElement("stop_external_id", stop.Id)) //REQUIRED
                    );
            }

            //Build URL
            /*
             * Sample URL:
             * http://www.fleetmaticsusa.com/GarminAPI.svc/api/stop/cancelstops?bt={EcryptedUserId}&cs={SHA(Guid+TimeStamp)}&ts={timestamp}
             */

            var url = baseUrl + "GarminAPI.svc/api/stop/cancelstops";
            url += BuildUrlSecureSegment("", Guid.NewGuid(), DateTime.Now);

            return FleetmaticsXHRHelper.SendStops(url, xDoc);
        }
        #endregion

        #region Send Message Service
        public static XDocument SendMessageRequest(List<fm.Message> messsages, bool skipFailed = true) //TODO: define structures
        {
            var xDoc = new XDocument(
                new XElement(rootName,
                    new XElement("messageRequest",
                        new XElement("include_sender", false), //REQUIRED - boolean
                        new XElement("skip_failed", skipFailed), //REQUIRED - boolean
                        new XElement("messages")) //REQUIRED
                ));

            var messageElements = xDoc.Element(rootName).Element("messageRequest").Element("messages");

            //foreach (var m in Messages)
            foreach (var m in messsages) //TODO: send parameter
            {
                messageElements.Add(new XElement("message",
                    new XElement("body", m.Body), //REQUIRED
                    new XElement("vehicle")) //REQUIRED
                    );

                var vehicles = messageElements.Element("vehicle");

                foreach (var v in m.VehicleIds)
                {
                    vehicles.Add(new XElement("external_id", v)); //REQUIRED
                }
            }

            //Build URL
            /*
             * Sample URL:
             * http://www.fleetmaticsusa.com/GarminAPI.svc/api/message/sendmessages?bt={EcryptedUserId}&cs={SHA(Guid+TimeStamp)}&ts={timestamp}
             */

            var url = baseUrl + "GarminAPI.svc/api/message/sendmessages";
            url += BuildUrlSecureSegment("", Guid.NewGuid(), DateTime.Now);

            return FleetmaticsXHRHelper.SendStops(url, xDoc);
        }
        #endregion
    }
}
