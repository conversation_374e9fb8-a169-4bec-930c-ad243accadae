using System;
using System.Text;
using System.Threading.Tasks;
using Extric.Towbook.Integration;

namespace Extric.Towbook.Integrations.Fleetmatics
{
    public class TokenGenerator
    {
        public const string KEY_USERNAME = "FletmaticsLogin";
        public const string KEY_PASSWORD = "FletmaticsPassword";
        public const string KEY_COMPANY_PROVIDER = "CompanyGpsProvider";

        private const int EXPIRED_TIME = 19;
        private const string REDIS_KEY_FORMAT = "fleetmatics:token:{0}";
        private const string AUTHORIZATION_FORMAT = "Atmosphere atmosphere_app_id={0}, Bearer {1}";

        private readonly string _username;
        private readonly string _password;
        private readonly string _appId;
        private string _token;
        private FleetmaticsRestClient _restClient;

        public string Username { get => _username; } 

        private string RedisKey { get => string.Format(REDIS_KEY_FORMAT, GetBasicAuthentication()); }

        private FleetmaticsRestClient RestClient => (_restClient != null)
            ? _restClient : _restClient = FleetmaticsRestClient.Create(this);

        public TokenGenerator(string username, string password)
        {
            if (string.IsNullOrWhiteSpace(username))
                throw new FleetmaticsException("Fleetmatics Username isn't set");

            if (string.IsNullOrWhiteSpace(password))
                throw new FleetmaticsException("Fleetmatics Password isn't set");

            _username = username;
            _password = password;
            _appId = "fleetmatics-p-us-xDCNYtT540sksG3Wl8tKlGGry13cx5jYPzRwkfHu";
        }

        public async Task<string> Token()
        {
            _token = Core.GetRedisValue(RedisKey);
            if (string.IsNullOrWhiteSpace(_token))
            {
                _token = await RestClient.GetToken();
                Core.SetRedisValue(RedisKey, _token, TimeSpan.FromMinutes(EXPIRED_TIME));
            }

            return _token;

        }

        public async Task<string> GetAuthentication()
        {
            var authenticationString = string.Format(AUTHORIZATION_FORMAT, _appId, await Token());
            return authenticationString;
        }

        public string GetBasicAuthentication()
        {
            var byteArray = Encoding.ASCII.GetBytes(string.Format("{0}:{1}", _username, _password));
            return Convert.ToBase64String(byteArray);
        }

        public static TokenGenerator GetInstance(int companyId)
        {
            var username = CompanyKeyValue.GetFirstValueOrNull(companyId, Provider.Fleetmatics.ProviderId, KEY_USERNAME);
            var password = CompanyKeyValue.GetFirstValueOrNull(companyId, Provider.Fleetmatics.ProviderId, KEY_PASSWORD);

            if (username == null)
                throw new FleetmaticsException("Fleetmatics Username isn't set");

            if (password == null)
                throw new FleetmaticsException("Fleetmatics Password isn't set");


            return new TokenGenerator(username, password);
        }

        public static void ClearCredentialsFromDatabase(int companyId)
        {
            var userLogin = CompanyKeyValue.GetFirstValueOrNew(companyId, Provider.Fleetmatics.ProviderId, KEY_USERNAME);
            var password = CompanyKeyValue.GetFirstValueOrNew(companyId, Provider.Fleetmatics.ProviderId, KEY_PASSWORD);
            var gpsc = CompanyKeyValue.GetFirstValueOrNew(companyId, Provider.Towbook.ProviderId, KEY_COMPANY_PROVIDER);

            userLogin.Delete();
            password.Delete();
            gpsc.Delete();
        }
    }
}
