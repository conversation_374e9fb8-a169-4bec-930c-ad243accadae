using System;
using System.Net;

namespace Extric.Towbook.Integrations.Fleetmatics
{
    [Serializable]
    public class FleetmaticsException : Exception
    {
        public FleetmaticsException() { }
        public FleetmaticsException(string message) : base(message) { }
        public FleetmaticsException(string message, Exception inner) : base(message, inner) { }
        public FleetmaticsException(string errorMessage, HttpStatusCode errorCode)
            : base("Error Code: " + errorCode + ", " + errorMessage)
        {
            this.ErrorCode = errorCode;
            this.ErrorMessage = errorMessage;
        }

        public HttpStatusCode ErrorCode { get; private set; }

        public string ErrorMessage { get; private set; }

        protected FleetmaticsException(
          System.Runtime.Serialization.SerializationInfo info,
          System.Runtime.Serialization.StreamingContext context)
            : base(info, context) { }
    }
}
