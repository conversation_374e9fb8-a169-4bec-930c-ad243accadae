using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using Extric.Towbook.Integrations.TomTom.Model;
using Glav.CacheAdapter.Core.DependencyInjection;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Extric.Towbook.Integration;
using Extric.Towbook.Utility;
using System.Diagnostics;
using NLog;
using System.Text.RegularExpressions;
using Glav.CacheAdapter.Helpers;
using System.Configuration;
using System.Net;

namespace Extric.Towbook.Integrations.TomTom
{
    public class TomTomUtility
    {
        private static readonly Logger logger = LogManager.GetCurrentClassLogger();

        private string username;
        private string password;
        private string account;

        public TomTomUtility(string username, string password, string account)
        {
            if (String.IsNullOrWhiteSpace(username))
                throw new TomTomException("TomTom Username isn't set");

            if (String.IsNullOrWhiteSpace(password))
                throw new TomTomException("TomTom Password isn't set");

            if (String.IsNullOrWhiteSpace(account))
                throw new TomTomException("TomTom Account isn't set");

            this.username = WebUtility.UrlEncode(username);
            this.password = WebUtility.UrlEncode(password);
            this.account = WebUtility.UrlEncode(account);
        }

        public static void ClearCredentialsFromDatabase(int companyId)
        {
            var an = CompanyKeyValue.GetFirstValueOrNew(companyId, Provider.TomTom.ProviderId, "AccountName");
            var u = CompanyKeyValue.GetFirstValueOrNew(companyId, Provider.TomTom.ProviderId, "Username");
            var p = CompanyKeyValue.GetFirstValueOrNew(companyId, Provider.TomTom.ProviderId, "Password");
            var gpsc = CompanyKeyValue.GetFirstValueOrNew(companyId, Provider.Towbook.ProviderId, "CompanyGpsProvider");

            an.Delete();
            u.Delete();
            p.Delete();
            gpsc.Delete();
        }

        public static string ConvertToBase(int num, int nbase)
        {
            string chars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ";

            // check if we can convert to another base
            if (nbase < 2 || nbase > chars.Length)
                return "";

            int r;
            string newNumber = "";

            // in r we have the offset of the char that was converted to the new base
            while (num >= nbase)
            {
                r = num % nbase;
                newNumber = chars[r] + newNumber;
                num = num / nbase;
            }
            // the last number to convert
            newNumber = chars[num] + newNumber;

            return newNumber;
        }

        public const string API_KEY = "75CAD9F6-ACA9-11E2-AB30-27F0ADFC9456";

        private static string CreateQueryString(dynamic o)
        {
            if (o == null)
                return string.Empty;
            var jObj = (JObject)JsonConvert.DeserializeObject(JsonConvert.SerializeObject(o));

            var query = String.Join("&",
                            jObj.Children().Cast<JProperty>()
                            .Select(jp => jp.Name + "=" + WebUtility.UrlEncode(jp.Value.ToString())));

            return query;
        }

        private string GetUrl(Method method)
        {
            return
                String.Format("https://csv.webfleet.com/extern?account={0}&username={1}&password={2}&action={3}&apikey={4}&msgclass=0&lang=en&outputformat=json&useISO8601=true&",
                account,
                username,
                password, method, API_KEY);
        }
        private static readonly HttpClient sharedHttpClient = new HttpClient();

        private string Get(Method method, dynamic parameters = null)
        {
            var s = Stopwatch.StartNew();

            string url = GetUrl(method) + CreateQueryString(parameters);
            string x = sharedHttpClient.GetStringAsync(url).Result;

            s.Stop();
            long elapsed = s.ElapsedMilliseconds;

            var logEvent = new LogEventInfo();

            try
            {
                if (x[0] == '{')
                {
                    var error = JsonConvert.DeserializeObject<dynamic>(x);
                    if (error.errorMsg != null ||
                        error.errorCode != null)
                    {
                        throw new TomTomException(
                            (string.IsNullOrWhiteSpace(error.errorMsg?.ToString()) ? "NO_ERROR_DESCRIPTION" : error.errorMsg.ToString()) + "," +
                            JsonExtensions.ToJson(parameters),
                            (error.errorCode?.ToString() ?? "NO_ERROR_CODE"));
                    }
                }

                if (x[0] != '{' && x[0] != '[' && x.Contains(","))
                {
                    string[] errors = x.Split(',');
                    if (errors.Length == 2)
                    {
                        logEvent.Level = LogLevel.Error;
                        throw new TomTomException(errors[1], errors[0]);
                    }
                }

                return x;
            }
            finally
            {
                logEvent.LoggerName = logger.Name;
                logEvent.Message = "TomTom Event: " + method.ToString();

                logEvent.Level = LogLevel.Info;
                logEvent.TimeStamp = DateTime.Now;
                logEvent.Properties["tomtomMethod"] = method.ToString();
                logEvent.Properties["tomtomUsername"] =  username;
                logEvent.Properties["tomtomAccount"] = account;
                logEvent.Properties["request"] = url;

                logEvent.Properties["response"] = x;
                
                logEvent.Properties.Add("responseTime", elapsed);
                logger.Log(logEvent);
            }
        }

        private List<T> GetAsList<T>(Method method, dynamic parameters = null)
        {
            string json = "";

            try
            {
                json = Get(method, parameters);

                return JsonConvert.DeserializeObject<List<T>>(json);
            }
            catch (JsonException e)
            {
                throw new Exception("Error parsing JSON..." + json, e);
            }
        }

        public IEnumerable<dynamic> GetOrders(RangePattern pattern)
        {
            return GetAsList<dynamic>(Method.showOrderReportExtern, new { range_pattern = pattern.ToStringAPI() });
        }

        public IEnumerable<dynamic> GetIOEvents(RangePattern pattern)
        {
            return GetAsList<dynamic>(Method.showIOReportExtern, new { range_pattern = pattern.ToStringAPI() });
        }

        public void DeleteOrder(string id)
        {
            Get(Method.deleteOrderExtern, new { orderid = id });
        }

        public IEnumerable<Unit> GetUnits()
        {
            string keyName = "tt_units_" + this.account;

            var cacheValue = Core.GetRedisValueAsByteArray(keyName);

            if (cacheValue == null)
            {
                var units = GetAsList<dynamic>(Method.showObjectReportExtern, null).Select(o =>
                new Unit()
                {
                    Description = o.description,
                    Id = o.objectuid,
                    Type = o.objecttype,
                    PosTextShort = o.postext_short,
                    PosText = o.postext,
                    CurrentDriverId = o.driveruid,
                    Latitude = o.latitude_mdeg / 1000000.0m,
                    Longitude = o.longitude_mdeg / 1000000.0m,
                    Name = o.objectname,
                    PosDateTime = o.pos_time,
                    Number = o.objectno
                });

                Core.SetRedisValue(keyName, units.Serialize(), TimeSpan.FromSeconds(20));
                return units;
            }
            else
            {
                return cacheValue.Deserialize<List<Unit>>();
            }
        }

        public IEnumerable<Model.Driver> GetDrivers()
        {
            // Caches items for 10 seconds to make sure we dont go over the 6 requests/60 seconds quota.
            return AppServices.Cache.Get("tt_drivers_" + this.account, TimeSpan.FromSeconds(10), () =>
            {
                return new CacheCollection<Model.Driver>(
                    GetAsList<dynamic>(Method.showDriverReportExtern, null).Select(o =>
                new Model.Driver()
                {
                    Id = o.driveruid,
                    Number = o.driverno,
                    Name = o.name1,
                    State = o.state,
                    Zip = o.zip,
                    City = o.city,
                    Street = o.street,
                    MobilePhone = o.telmobile,
                    PrivatePhone = o.telprivate,
                    Email = o.email,
                    Description = o.description,
                    Company = o.company,
                    Code = o.code,
                    ObjectNum = o.objectno,
                    SignonTime = o.signontime,
                    SignonRole = o.signonrole,
                    CurrentWorkstate = o.current_workstate,
                    CardId = o.dt_cardid,
                    CurrentWorkingTimestart = o.current_workingtimestart,
                    CurrentWorkingTimeend = o.current_workingtimeend,
                    ManualAssignment = o.manualassignment,
                    CurrentUnitId = o.objectuid,
                    Pin = o.pin,
                }));
            }).Items;
        }

        /// <summary>
        /// Gets the current queued messages and marks them as acknowledged/removes them from the queue.
        /// </summary>
        /// <returns></returns>
        public IEnumerable<QueueMessage> GetQueuedMessages()
        {
            string url = GetUrl(Method.popQueueMessagesExtern);

            try
            {
                var json = GetAsList<QueueMessage>(Method.popQueueMessagesExtern);


                Get(Method.ackQueueMessagesExtern);

                return json;
            }
            catch (TomTomException tte)
            {
                if (tte.ErrorCode == "WFCQ_E0022")
                {
                    Get(Method.createQueueExtern, new { msgclass = "0" });

                    return GetQueuedMessages();
                }
                throw;
            }
        }

        /// <summary>
        /// Returns a list of geocoded results for the specified address.
        /// </summary>
        /// <param name="address">Address (Address, city state zip preferably)</param>
        /// <returns></returns>
        public List<GeocodeResult> Geocode(string address)
        {
            try
            {
                return GetAsList<dynamic>(
                    Method.geocodeAddress,
                    new { freetext = address })
                    .Select<dynamic, GeocodeResult>(o => GeocodeResult.Map(o)).ToList();
            }
            catch (TomTomException t)
            {
                if (t.ErrorCode == "9517" ||
                    t.ErrorCode == "10000")
                    return new List<GeocodeResult>();
                throw;
            }
        }

        public object UpdateCall(string objectuid, OrderType orderType, long longitude, long latitude, string orderId, string notes, string street, string contactName = null, string phone = null)
        {

            street = street.Replace(", USA", "");

            if (notes != null && notes.Length > 500)
                notes = notes.Substring(0, 500);

            phone = Core.FormatPhone(phone);

            // if returned string isnt 14 characters it isnt valid.
            if (!Core.IsPhoneValidStandard(phone))
                phone = "";

            dynamic src = new
                 {
                     objectuid = objectuid,
                     orderid = orderId,
                     latitude = latitude,
                     longitude = longitude,
                     ordertext = notes,
                     ordertype = orderType,
                     street = street,
                     orderautomations = "2,3,5,6",
                     contact = contactName,
                     contacttel = phone
                 };

            var y = Get(Method.updateOrderExtern, src);

            return y;
        }

        /// <summary>
        /// Resigns an order to the specified objectuid.
        /// </summary>
        /// <param name="orderId"></param>
        /// <param name="objectuid"></param>
        /// <returns></returns>
        public object ReassignOrder(string orderId, string objectuid)
        {
            dynamic src = new
            {
                orderid = orderId,
                objectuid = objectuid,
            };

            var y = Get(Method.reassignOrderExtern, src);

            return y;
        }


        public object AddUpdateDriver(Model.Driver d)
        {
            if (string.IsNullOrWhiteSpace(d.Id))
            {
                dynamic drv = new
                {
                    driverno = d.Number,
                    name = d.Name,
                    company = d.Company,
                    code = d.Code,
                    description = d.Description,
                    country = d.Country,
                    zip = d.Zip,
                    city = d.City,
                    street = d.Street,
                    telmobile = d.MobilePhone,
                    telprivate = d.PrivatePhone,
                    pin = d.Pin,
                    email = d.Email,
                };
                return Get(Method.insertDriverExtern, drv);
            }
            else
            {
                dynamic drv = new
                {
                    driverno = d.Number,
                    name = d.Name,
                    company = d.Company,
                    code = d.Code,
                    description = d.Description,
                    country = d.Country,
                    zip = d.Zip,
                    city = d.City,
                    street = d.Street,
                    telmobile = d.MobilePhone,
                    telprivate = d.PrivatePhone,
                    pin = d.Pin,
                    email = d.Email,
                    driverno_old = d.Number,
                };
                return Get(Method.updateDriverExtern, drv);
            }
        }

        /// <summary>
        /// Strips any text between (), like "201 N Riverside E6 (Extric LLC)" would be returned as "201 N Riverside E6".
        /// </summary>
        /// <param name="s">the text to strip ()'ed text from.</param>
        /// <returns></returns>
        private string RemoveParenthesedText(string s)
        {
            if (String.IsNullOrWhiteSpace(s))
                return s;

            var input = s;
            var output = Regex.Replace(input, @" ?\(.*?\)", string.Empty);

            return output;
        }

        public object SendCall(string objectuid, OrderType orderType, long longitude, long latitude, string orderId, string notes, string street, string contactName = null, string phone = null)
        {
            if (objectuid == null || objectuid == "null")
            {
                // no object specified.. don't bother sending it..  probably should throw an exception.
                return null;
            }

            street = street.Replace(", USA", "");
            street = Core.StripLastFourFromNineDigitZipInAddress(street);

            if (notes != null && notes.Length > 400)
                notes = notes.Substring(0, 400);

            phone = Core.FormatPhone(phone);

            // if returned string isnt 14 characters it isnt valid.
            if (!Core.IsPhoneValidStandard(phone))
                phone = "";

            dynamic src = new
            {
                objectuid = objectuid,
                orderid = orderId,
                latitude = latitude,
                longitude = longitude,
                ordertext = notes,
                ordertype = orderType,
                street = RemoveParenthesedText(street),
                orderautomations = "2,3,5,6",
                contact = contactName,
                contacttel = phone
            };
            
            var y = Get(Method.sendDestinationOrderExtern, src);

            if (y != "[]")
            {
                var error = JsonConvert.DeserializeObject<dynamic>(y);
                if (error.errorMsg != null)
                {
                    throw new TomTomException(error.errorMsg.ToString(), error.errorCode.ToString());
                }
            }

            return y;

        }

        /// <summary>
        /// Sends a message to the specified TomTom unit
        /// </summary>
        /// <param name="objectuid">Unique Id of the TomTom unit</param>
        /// <param name="message">Message to send to the unit</param>
        public void SendMessage(string objectuid, string message)
        {
            Get(Method.sendTextMessageExtern, new { objectuid = objectuid, messagetext = message });
        }

        public void ClearOrders(string objectuid)
        {
            Get(Method.clearOrdersExtern, new { objectuid = objectuid });
        }

        public static TomTomUtility GetInstance(User user)
        {
            if (user == null)
            {
                throw new TomTomException("User object provided is not set to an instance of a valid user for TomTom.");
            }

            return GetInstance(user.CompanyId);
        }

        public static TomTomUtility GetInstance(int companyId)
        {
            if (Core.GetAppSetting<string>("Towbook:Gps:TomTom:Disable") == "1")
                return null;

            try
            {
                var username = CompanyKeyValue.GetFirstValueOrNull(companyId, Provider.TomTom.ProviderId, "Username");
                var password = CompanyKeyValue.GetFirstValueOrNull(companyId, Provider.TomTom.ProviderId, "Password");
                var account = CompanyKeyValue.GetFirstValueOrNull(companyId, Provider.TomTom.ProviderId, "AccountName");

                if (!String.IsNullOrWhiteSpace(username) &&
                    !String.IsNullOrWhiteSpace(password) &&
                    !String.IsNullOrWhiteSpace(account))
                {
                    return new TomTomUtility(username,
                       password,
                       account);
                }
                else
                    return null;
            }
            catch
            {
                return null;
            }
        }

        public IEnumerable<ClosestUnit> GetClosestUnits(decimal latitude, decimal longitude)
        {
            return GetAsList<ClosestUnit>(Method.showNearestVehicles, new
            {
                latitude = Math.Round(latitude * 1000000, 0),
                longitude = Math.Round(longitude * 1000000, 0),
            });
        }
    }

    public class ClosestUnit
    {
        public string objectno;
        public string objectuid;
        public string objectstate;
        public long latitude;
        public long longitude;
        public long lineardistance;
        public long routedistance;
        public long routetime;
    }

    public class QueueMessage
    {
        public string msg_text { get; set; }
        public string orderno { get; set; }
        public OrderState? order_state { get; set; }
        public OrderType order_type { get; set; }

        public string msgid { get; set; }
        public string msg_time { get; set; }
        public MessageClass msg_class { get; set; }
        public MessageType MessageType
        {
            get
            {
                MessageType c = (MessageType)Convert.ToInt32(msg_type.Substring(msg_type.Length - 3));

                return c;
            }
        }
        public string msg_type { get; set; }
        public string objectno { get; set; }
        public string odometer { get; set; }
        public int pos_latitude { get; set; }
        public int pos_longitude { get; set; }
        public string pos_text { get; set; }
        public string pos_params { get; set; }
        public string pos_time { get; set; }
        public string speed { get; set; }
        public string course { get; set; }
        public string direction { get; set; }
        public string status { get; set; }

        public string driverno { get; set; }
        public string objectuid { get; set; }

        public override string ToString()
        {
            return "order=" + orderno + ", State=" + (order_state?.ToString() ?? "") + ", Type={" + MessageType + "}";

        }
    }

    public class GeocodeResult
    {
        public string AdditionalInformation { get; set; }
        public string StreetNumber { get; set; }
        public string Street { get; set; }
        public string City { get; set; }
        public string Zip { get; set; }
        public string Country { get; set; }
        public string FormattedLatitude { get; set; }
        public string FormattedLongitude { get; set; }
        public long Latitude { get; set; }
        public long Longitude { get; set; }
        public string PosText { get; set; }
        public string Raw { get; set; }

        public static GeocodeResult Map(dynamic o)
        {
            return new GeocodeResult()
            {
                AdditionalInformation = o.additional_information,
                City = o.addrcity,
                Country = o.addrcountry,
                Street = o.addrstreet,
                StreetNumber = o.addrstreetnumber,
                Zip = o.addrzip,
                FormattedLatitude = o.formatted_latitude,
                FormattedLongitude = o.formatted_longitude,
                Latitude = Convert.ToInt64(o.latitude.ToString()),
                Longitude = Convert.ToInt64(o.longitude.ToString()),
                PosText = o.postext
            };
        }
    }

}


