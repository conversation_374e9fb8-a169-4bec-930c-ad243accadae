<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>netstandard2.0</TargetFramework>
		<GenerateAssemblyInfo>false</GenerateAssemblyInfo>
	</PropertyGroup>

	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
		<AllowUnsafeBlocks>true</AllowUnsafeBlocks>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.AspNet.WebApi.Client" Version="6.0.0" />
		<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
		<PackageReference Include="protobuf-net" Version="3.2.30" />
		<PackageReference Include="protobuf-net.Core" Version="3.2.30" />
		<PackageReference Include="RestSharp" Version="111.4.1" />
		<PackageReference Include="System.Buffers" Version="4.5.1" />
		<!--<PackageReference Include="System.Collections.Immutable" Version="7.0.0" />-->
		<PackageReference Include="System.Collections.Immutable" Version="8.0.0" />
		<PackageReference Include="System.Memory" Version="4.5.5" />
		<PackageReference Include="System.Numerics.Vectors" Version="4.5.0" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\..\..\Extric.Towbook\Extric.Towbook.csproj" />
	</ItemGroup>

</Project>