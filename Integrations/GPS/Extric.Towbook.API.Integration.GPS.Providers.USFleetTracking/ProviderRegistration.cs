using Extric.Towbook.Integration;
using Microsoft.AspNetCore.Routing;

namespace Extric.Towbook.API.Integration.GPS.Providers.USFleetTracking
{
    public class ProviderRegistration
    {
        public static void RegisterRoutes(RouteCollection routes)
        {
            /* string root = "integration/gps/providers/usfleettracking/";
            List<Route> rl = new List<Route>();

            rl.Add(routes.MapHttpRoute(
                name: "Integration_USFleetTracking_Get",
                routeTemplate: root + "{controller}/{id}/{action}",
                defaults: new { id = RouteParameter.Optional, action = "Get" },
                constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "GET" }) }));

            rl.Add(routes.MapHttpRoute(
                name: "Integration_USFleetTracking_Put",
                routeTemplate: root + "{controller}/{id}/{action}",
                defaults: new { id = RouteParameter.Optional, action = "Put" },
                constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "PUT" }) }));


            rl.Add(routes.MapHttpRoute(
                name: "Integration_USFleetTracking_Post",
                routeTemplate: root + "{controller}/{id}",
                defaults: new { id = RouteParameter.Optional, action = "Post" },
                constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "POST" }) }));


            rl.Add(routes.MapHttpRoute(
                name: "Integration_USFleetTracking_Delete",
                routeTemplate: root + "{controller}/{id}",
                defaults: new { id = RouteParameter.Optional, action = "Delete" },
                constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "DELETE" }) }));

            rl.Add(routes.MapHttpRoute(
                name: "Integration_USFleetTracking",
                routeTemplate: root + "{controller}/{id}",
                defaults: new { action = "Get", id = RouteParameter.Optional }));

            foreach (var r in rl)
            {
                if (r.DataTokens == null)
                    r.DataTokens = new RouteValueDictionary();

                r.DataTokens["Namespaces"] = new string[] { "Extric.Towbook.API.Integration.GPS.Providers.USFleetTracking" };
            } */

            var p = Extric.Towbook.Integration.Provider.USFleetTracking;

            p.RegisterKey(KeyType.Company, "Username");
            p.RegisterKey(KeyType.Company, "Password");
            p.RegisterKey(KeyType.Company, "AccountName");
            p.RegisterKey(KeyType.Driver, "DriverId");
            p.RegisterKey(KeyType.Truck, "TruckId");
        }
    }
}