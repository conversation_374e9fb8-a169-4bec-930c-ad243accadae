using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Web.Http;
using Extric.Towbook.Integration;
using Extric.Towbook.Integrations.TomTom;
using Extric.Towbook.Integrations.TomTom.Model;
using Extric.Towbook.WebShared;
using Microsoft.AspNetCore.Mvc;

namespace Extric.Towbook.API.Integration.GPS.Providers.TomTom.Controllers
{
    [Route("integration/gps/providers/tomtom/vehicles")]
    public class TomTomVehiclesController : ControllerBase
    {
        private TomTomUtility GetTomTomUtility()
        {
            return TomTomUtility.GetInstance(WebGlobal.CurrentUser.CompanyId);
        }

        [Route("")]
        [HttpGet]
        public IEnumerable<Unit> Get()
        {
            var ttu = GetTomTomUtility();

            if (ttu != null)
            {
                try
                {
                    return ttu.GetUnits();
                }
                catch (TomTomException te)
                {
                    if (te.ErrorCode == "1101")
                    {
                        TomTomUtility.ClearCredentialsFromDatabase(WebGlobal.CurrentUser.CompanyId);
                    }
                    return Array.Empty<Unit>();
                }
            }
            else
                return Array.Empty<Unit>();
        }

        [Route("")]
        [HttpPost]
        public async Task Post([FromQuery] int truckId, [FromQuery] string vehicleId)
        {
            TruckKeyValue tkv = new TruckKeyValue();
            tkv.TruckId = truckId;
            tkv.Value = vehicleId;
            tkv.KeyId = TruckKey.GetByProviderId(Provider.TomTom.ProviderId, "TruckId").Id;
            await tkv.SaveAsync();
        }
    }
}
