using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Extric.Towbook.Integration;
using Extric.Towbook.Integrations.TomTom;
using Extric.Towbook.WebShared;
using Microsoft.AspNetCore.Mvc;

namespace Extric.Towbook.API.Integration.GPS.Providers.TomTom.Controllers
{
    [Route("integration/gps/providers/tomtom/drivers")]
    public class TomTomDriversController : ControllerBase
    {
        /*
         *  Extric.Towbook.API.Integration.GPS.Providers.TomTom
         *  name: "Integration_TomTom_Get",
            routeTemplate: root + "{controller}/{id}/{action}",
            defaults: new { id = RouteParameter.Optional, action = "Get" },
            constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "GET" }) })
         */

        /// <summary>
        /// Retrieves a list of TomTom drivers for the current company.
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("")]
        public IEnumerable<Integrations.TomTom.Model.Driver> Get()
        {
            var kvs = GetKeys();

            try
            {
                return GetTomTomUtility()?.GetDrivers().Select(o =>
                {
                    // Set the Towbook driverId
                    o.DriverId = kvs?.FirstOrDefault(k => k.Value == o.Id)?.DriverId ?? 0;
                    return o;
                });
            }
            catch (TomTomException te)
            {
                if (te.ErrorCode == "1101")
                {
                    TomTomUtility.ClearCredentialsFromDatabase(WebGlobal.CurrentUser.CompanyId);
                }
                return Array.Empty<Integrations.TomTom.Model.Driver>();
            }
        }

        /// <summary>
        /// Get GPS Drivers for Company.
        /// Following the Get() pattern if TomTom instance is null, null is retured.
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("gpsDrivers")]
        public IEnumerable<Integrations.TomTom.Model.Driver> GpsDrivers()
        {
            try
            {
                return GetTomTomUtility()?.GetDrivers();
            }
            catch (TomTomException te)
            {
                if (te.ErrorCode == "1101")
                {
                    TomTomUtility.ClearCredentialsFromDatabase(WebGlobal.CurrentUser.CompanyId);
                }

                return Array.Empty<Integrations.TomTom.Model.Driver>();
            }
        }

        /// <summary>
        /// Link a Towbook driver to a TomTom driver
        /// </summary>
        /// <returns></returns>
        [Route("")]
        [HttpPost]
        public async Task<DriverKeyValue> Post([FromQuery] int driverId, [FromQuery] string gpsId)
        {
            // If driverId == -1, then we're being told to add the TomTom driver to Towbook
            if (driverId == -1)
            {
                // Get the driver from TomTom
                var gpsDriver = GetTomTomUtility()?.GetDrivers().FirstOrDefault(d => d.Id == gpsId);
                if (gpsDriver == null)
                    return null; 
                else
                {
                    // Add it to Towbook
                    var driver = new Driver()
                    {
                        CompanyId = WebGlobal.CurrentUser.CompanyId,
                        Name = gpsDriver.Name ?? "",
                        MobilePhone = gpsDriver.MobilePhone ?? "",
                        Email = gpsDriver.Email ?? "",
                        Active = true,
                    };
                    await driver.Save();
                    driverId = driver.Id;
                }
            }

            // If gpsId == -1, then we're being told to add the Towbook driver to TomTom
            if (gpsId == "-1")
            {
                // Get the driver from Towbook
                var driver = await Driver.GetByIdAsync(driverId);
                if (driver == null)
                    return null;
                else
                {
                    // Add it to TomTom
                    var ttu = GetTomTomUtility();
                    if (ttu == null)
                        return null;

                    ttu.AddUpdateDriver(new Integrations.TomTom.Model.Driver()
                    {
                        Number = driverId.ToString(),
                        Name = driver.Name,
                        Code = driverId.ToString(),
                        Description = "Imported from Towbook.",
                        Country = "US",
                        MobilePhone = driver.MobilePhone ?? "",
                        Pin = "1234",
                        Email = driver.Email ?? "",
                    });

                    gpsId = ttu.GetDrivers().FirstOrDefault(d => d.Number == driverId.ToString())?.Id;
                }
            }

            var kv = GetKeys(driverId).FirstOrDefault() ?? new DriverKeyValue()
            {
                DriverId = driverId,
                KeyId = DriverKey.GetByProviderId(Provider.TomTom.ProviderId, "DriverId").Id,
            };

            kv.Value = gpsId;
            kv.Save();

            return kv;
        }

        [Route("{id}")]
        [HttpDelete]
        public IEnumerable<DriverKeyValue> Delete(int id)
        {
            var data = GetKeys(id);

            foreach (var akv in data)
            {
                akv.Delete();
            }

            return data;
        }

        private IEnumerable<DriverKeyValue> GetKeys(int? id = null)
        {
            if (id == null)
                return DriverKeyValue.GetByCompany(WebGlobal.CurrentUser.CompanyId).Where(w => w.KeyId == Provider.TomTom.GetKey(KeyType.Driver, "DriverId").Id);
            else
                return DriverKeyValue.GetByDriver(WebGlobal.CurrentUser.CompanyId, id.Value).Where(w => w.KeyId == Provider.TomTom.GetKey(KeyType.Driver, "DriverId").Id);;
        }

        private TomTomUtility GetTomTomUtility()
        {
            return TomTomUtility.GetInstance(WebGlobal.CurrentUser.CompanyId);
        }
    }
}
