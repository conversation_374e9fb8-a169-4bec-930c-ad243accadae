using Extric.Towbook.Integrations.TomTom;
using Extric.Towbook.WebShared;
using Microsoft.AspNetCore.Mvc;

namespace Extric.Towbook.API.Integration.GPS.Providers.TomTom.Controllers
{
    [Route("integration/gps/providers/tomtom/logout")]
    public class TomTomLogoutController : ControllerBase
    {
        [Route("")]
        [HttpPost]
        public dynamic Post(TomTomLoginModel model)
        {
            int companyId = WebGlobal.CurrentUser.CompanyId;

            TomTomUtility.ClearCredentialsFromDatabase(companyId);

            return "Data saved.";
        }
    }
}
