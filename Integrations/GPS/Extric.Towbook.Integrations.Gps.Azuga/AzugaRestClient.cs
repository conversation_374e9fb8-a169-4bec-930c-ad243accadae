using System;
using System.Collections.Generic;
using RestSharp;
using Newtonsoft.Json;
using System.Linq;
using Extric.Towbook.Configuration;
using System.Threading.Tasks;

namespace Extric.Towbook.Integrations.Gps.Azuga
{
    public class AzugaRestClient
    {
        private readonly RestClient _restClient;
        private string apiKey;


        /// <summary>
        /// Login to the Azuga service.
        /// </summary>
        /// <param name="apiKey">Azuga API Key</param>
        public static AzugaRestClient Create(string apiKey)
        {
            var instance = new AzugaRestClient();

            if (string.IsNullOrWhiteSpace(apiKey))
                throw new ArgumentException("api key must be specified", nameof(apiKey));

            instance.apiKey = Base64Encode(apiKey);
            return instance;
        }

        public AzugaRestClient()
        {
            var options = new RestClientOptions(url);
            _restClient = new RestClient(AppServicesHelper.HttpClient, options);
        }

        private static string Base64Encode(string plainText)
        {
            var plainTextBytes = System.Text.Encoding.UTF8.GetBytes(plainText);
            return System.Convert.ToBase64String(plainTextBytes);
        }



        private const string url = "https://api.azuga.com/azuga-ws/v1";

        /// <summary>
        ///  To return the last location of the vehicles associated with your account.
        /// </summary>
        public async Task<List<VehicleLocation>> GetVehicles()
        {
            var request = new RestRequest("/live/location.json", Method.Get);
            request.AddHeader("Authorization", apiKey);

            var response = await _restClient.ExecuteAsync(request);

            if ((int)response.StatusCode == 429)
            {
                throw new TowbookException(response.Content);
            }
            
            var result = JsonConvert.DeserializeObject<Rootobject>(response.Content);
            if (result.liveMapResponseVO.Code == 200)
            {
                if (result.liveMapResponseVO.Reason == "No result found")
                    return new List<VehicleLocation>();
                else
                    return result.liveMapResponseVO.Locations.ToList();
            }

            return null;
        }


        public class Rootobject
        {
            public Livemapresponsevo liveMapResponseVO { get; set; }
        }

        public sealed class Livemapresponsevo
        {
            public int Code { get; set; }
            public string Message { get; set; }
            public object MessageCode { get; set; }
            public string Reason { get; set; }
            public string GeneratedAt { get; set; }
            public int CurrentPage { get; set; }
            public int TotalPages { get; set; }
            public VehicleLocation[] Locations { get; set; }
        }

        public sealed class VehicleLocation
        {
            public string VehicleId { get; set; }
            public string VehicleName { get; set; }
            public string Date { get; set; }
            
            public string Event { get; set; }
            public string Speed { get; set; }
            public string StopTime { get; set; }
            public string IdleTime { get; set; }
            public string GroupId { get; set; }
            public string GroupName { get; set; }
            public string PhoneNumber { get; set; }
            public object Reserved1 { get; set; }
            public object Reserved2 { get; set; }
            public object Reserved3 { get; set; }
            public object Reserved4 { get; set; }
            public User user { get; set; }
            public decimal Latitude { get; set; }
            public decimal Longitude { get; set; }
            public decimal LastKnownLat { get; set; }
            public decimal LastKnownLon { get; set; }
            public string LastKnownAddress { get; set; }
            public long LastKnownTime { get; set; }
            public string realLKL_address { get; set; }
            public long realLKL_time { get; set; }
            public bool StoredLocation { get; set; }
            public string Location { get; set; }

            public DateTime Timestamp
            {
                get => DateTime.ParseExact(Date, "MMM-d-yyyy, hh:mm tt \\U\\T\\C", null);
            }
        }

        public sealed class User
        {
            public string userId { get; set; }
            public string firstName { get; set; }
            public string lastName { get; set; }
            public string userName { get; set; }
            public string fullName { get; set; }
            public bool notInGroup { get; set; }
            public bool deleted { get; set; }
            public object employeeId { get; set; }
            public bool emailVerification { get; set; }
        }
    }
}
