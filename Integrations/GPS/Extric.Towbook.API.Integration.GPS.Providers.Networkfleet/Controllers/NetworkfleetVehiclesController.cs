using Extric.Towbook.Integration;
using Extric.Towbook.Integrations.Networkfleet;
using Extric.Towbook.WebShared;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using NetworkFleetVehicle = Extric.Towbook.Integrations.Networkfleet.Vehicle;

namespace Extric.Towbook.API.Integration.GPS.Providers.Networkfleet.Controllers
{
    [Route("integration/gps/providers/networkfleet/Vehicles")]
    public class NetworkfleetVehiclesController : ControllerBase
    {
        //rl.Add(routes.MapHttpRoute(
        //    name: "Integration_Networkfleet",
        //    routeTemplate: root + "{controller}/{id}",
        //    defaults: new { action = "Get", id = RouteParameter.Optional }));
        [HttpGet]
        [Route("")]
        public IEnumerable<NetworkFleetVehicle> Get()
        {
            var companyId = WebGlobal.CurrentUser.CompanyId;
            var username = CompanyKeyValue.GetFirstValueOrNull(companyId, Provider.Networkfleet.ProviderId, "Username");
            var password = CompanyKeyValue.GetFirstValueOrNull(companyId, Provider.Networkfleet.ProviderId, "Password");

            if (username != null)
            {
                var service = NetworkfleetService.Create(username, password);
                return service.GetVehicles();
            }
            else
                return Array.Empty<NetworkFleetVehicle>();
        }

        //rl.Add(routes.MapHttpRoute(
        //    name: "Integration_Networkfleet_Post",
        //    routeTemplate: root + "{controller}/{id}",
        //    defaults: new { id = RouteParameter.Optional, action = "Post" },
        //    constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "POST" }) }));
        [HttpPost]
        [Route("")]
        public async Task Post([FromQuery] int truckId, [FromQuery] string vehicleId)
        {
            await Common.LinkTruckToGpsUnitAsync(truckId, vehicleId.ToString());
        }
    }
}
