<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<RootNamespace>Extric.Towbook.API.Integration.GPS.Providers.Networkfleet</RootNamespace>
		<AssemblyName>Extric.Towbook.API.Integration.GPS.Providers.Networkfleet</AssemblyName>
		<OutputType>Library</OutputType>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.CSharp" Version="4.7.0" />
		<PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.21.0" />
		<PackageReference Include="NLog" Version="5.3.3" />
		<PackageReference Include="Swashbuckle.AspNetCore" Version="6.7.1" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\..\..\Extric.Towbook.WebShared.Net5\Extric.Towbook.WebShared.Net5.csproj" />
		<ProjectReference Include="..\Extric.Towbook.Integrations.Networkfleet\Extric.Towbook.Integrations.Networkfleet.csproj" />
	</ItemGroup>

</Project>