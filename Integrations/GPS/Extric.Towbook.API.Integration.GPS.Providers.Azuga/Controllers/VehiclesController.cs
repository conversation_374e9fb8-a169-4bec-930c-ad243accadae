using System.Collections.Generic;
﻿using System;
using Extric.Towbook.Integration;
using Extric.Towbook.Integrations.Gps.Azuga;
using Extric.Towbook.WebShared;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace Extric.Towbook.API.Integration.GPS.Providers.Azuga.Controllers
{
    [Route("integration/gps/providers/azuga/vehicles")]
    public class VehiclesController : ControllerBase
    {

        [HttpGet]
        [Route("")]
        public async Task<IEnumerable<AzugaRestClient.VehicleLocation>> Get()
        {
            var companyId = WebGlobal.CurrentUser.CompanyId;
            var apiKey = CompanyKeyValue.GetFirstValueOrNull(companyId, Provider.Azuga.ProviderId, "ApiKey");
            
            if (apiKey != null)
            {
                var service = AzugaRestClient.Create(apiKey);
                return await service.GetVehicles();
            }
            else
                return Array.Empty<AzugaRestClient.VehicleLocation>();
        }

        [HttpPost]
        [Route("")]
        public async Task Post([FromQuery] int truckId, [FromQuery] string vehicleId)
        {
            TruckKeyValue tkv = new TruckKeyValue();
            tkv.TruckId = truckId;
            tkv.Value = vehicleId;            

            tkv.KeyId = TruckKey.GetByProviderId(Provider.Azuga.ProviderId, "TruckId").Id;

            await tkv.SaveAsync();
        }
    }
}
