<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>netstandard2.0</TargetFramework>
		<GenerateAssemblyInfo>false</GenerateAssemblyInfo>
	</PropertyGroup>

	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
		<AllowUnsafeBlocks>true</AllowUnsafeBlocks>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\..\..\Extric.Towbook\Extric.Towbook.csproj" />
	</ItemGroup>
</Project>