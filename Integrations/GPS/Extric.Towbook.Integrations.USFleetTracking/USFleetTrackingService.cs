using System;
using System.Collections.Generic;
using System.Linq;

using Extric.Towbook;
using USFT.RestClient.v1;

namespace Extric.Towbook.Integrations.USFleetTracking
{
    public class USFleetTrackingService : UsftClient
    {
        

        public USFleetTrackingService(string UserName, string PrivateKeyOrPassword, AuthenticationMode AuthMode = AuthenticationMode.USFT) : 
            base(UserName, PrivateKeyOrPassword, AuthMode)
        {
        }

        public static UsftClient Create(string username, string password)
        {
            UsftClient instance = null;

            if (instance == null)
            {
                try
                {
                    instance = new UsftClient(username, password, UsftClient.AuthenticationMode.Basic);
                    instance.TestConnection();
                }
                catch (Exception e)
                {
                    throw new InvalidLoginException(e.Message);
                }
            }

            return instance;
        }
    }
}