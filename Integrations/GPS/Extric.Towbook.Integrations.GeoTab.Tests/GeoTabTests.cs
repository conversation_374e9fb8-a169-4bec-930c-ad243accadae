using Extric.Towbook.Utility;
using System;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using ObjectModel = Geotab.Checkmate.ObjectModel;
using System.Collections.Generic;

namespace Extric.Towbook.Integrations.GeoTab.Tests
{
    [TestClass]
    public class GeoTabTests
    {
        private static string account;
        private static string login;
        private static string password;

        [ClassInitialize]
        public static void InitTestSuite(TestContext testContext)
        {
            GeoTabTests.account = "Safetow";
            GeoTabTests.login = "<EMAIL>";
            GeoTabTests.password = "towbooks";
        }

        [TestMethod]
        [ExpectedException(typeof(InvalidLoginException), "When the login has failed an InvalidLoginException should be thrown")]
        public void LoginFailureTest()
        {
            var service = GeoTabConnection.Login(account, login, "");
        }

        [TestMethod]
        public void LoginTest()
        {
            var service = GeoTabConnection.Login(account, login, password);

            Assert.IsInstanceOfType(service, typeof(GeoTabConnection));
        }

        [TestMethod]
        public async System.Threading.Tasks.Task GetAllVehiclesTest()
        {
            var connection = await GeoTabConnection.Login(account, login, password);
            var vehicles = await connection.GetVehicles();

            Assert.IsInstanceOfType(vehicles, typeof(List<ObjectModel.Device>));

            Console.WriteLine(vehicles.ToJson(true));
        }
    }
}
