using Extric.Towbook.Integration;
using Extric.Towbook.Integrations.Fleetmatics;
using Microsoft.AspNetCore.Routing;

namespace Extric.Towbook.API.Integration.GPS.Providers.Fleetmatics
{
    public class ProviderRegistration
    {
        public ProviderRegistration()
        {
        }

        public static void RegisterRoutes(RouteCollection routes)
        {
            /* string root = "integration/accounting/providers/fleetmatics/";
            List<Route> rl = new List<Route>();

            rl.Add(routes.MapHttpRoute(
                name: "Integration_Fleetmatics_Get",
                routeTemplate: root + "{controller}/{id}/{action}",
                defaults: new { id = RouteParameter.Optional, action = "Get" },
                constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "GET" }) }));

            rl.Add(routes.MapHttpRoute(
                name: "Integration_Fleetmatics_Put",
                routeTemplate: root + "{controller}/{id}/{action}",
                defaults: new { id = RouteParameter.Optional, action = "Put" },
                constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "PUT" }) }));


            rl.Add(routes.MapHttpRoute(
                name: "Integration_Fleetmatics_Post",
                routeTemplate: root + "{controller}/{id}",
                defaults: new { id = RouteParameter.Optional, action = "Post" },
                constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "POST" }) }));


            rl.Add(routes.MapHttpRoute(
                name: "Integration_Fleetmatics_Delete",
                routeTemplate: root + "{controller}/{id}",
                defaults: new { id = RouteParameter.Optional, action = "Delete" },
                constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "DELETE" }) }));



            rl.Add(routes.MapHttpRoute(
                name: "Integration_Fleetmatics",
                routeTemplate: root + "{controller}/{id}",
                defaults: new { action = "Get", id = RouteParameter.Optional }));

            foreach (var r in rl)
            {
                if (r.DataTokens == null)
                    r.DataTokens = new RouteValueDictionary();

                r.DataTokens["Namespaces"] = new string[] { "Extric.Towbook.API.Integration.GPS.Providers.Fleetmatics" };
            } */

            var p = Extric.Towbook.Integration.Provider.Fleetmatics;

            // oauth/connection keys (set when quickbooks is connected to towbook in the OAuthResponseController)
            p.RegisterKey(KeyType.Company, TokenGenerator.KEY_USERNAME);
            p.RegisterKey(KeyType.Company, TokenGenerator.KEY_PASSWORD);
            p.RegisterKey(KeyType.Company, TokenGenerator.KEY_COMPANY_PROVIDER);
            p.RegisterKey(KeyType.Truck, "TruckId");
        }
    }
}
