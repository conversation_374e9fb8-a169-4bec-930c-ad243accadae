using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Extric.Towbook.Integrations.Networkfleet
{
    class PagedSensorResult
    {
        public string Type { get; set; }
        public int Index { get; set; }
        public int Limit { get; set; }
        public int Count { get; set; }
        public int Total { get; set; }
        [JsonProperty("sensor")]
        public List<Sensor> Sensors { get; set; }
    }
}
