using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Serialization;

namespace Extric.Towbook.Integrations.Networkfleet
{
    public class Vehicle
    {
        [JsonProperty("@id")]
        public long Id { get; set; }
        public Composed Vin { get; set; }
        public string Label { get; set; }
        public string Color { get; set; }
        public string Make { get; set; }
        public string Model { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public Composed DriverId { get; set;}

        public string Trim { get; set; }
        public Composed DeviceSerialNumber { get; set; }
        public int Year { get; set; }
        public Odometer Odometer { get; set; }
        public EngineRunTime EngineRunTime { get; set; }
        public LicensePlate LicensePlate { get; set; }
        public string TrackableItemType { get; set; }
        public string FuelType { get; set; }
        public Timestamp CreatedTimestamp { get; set; }
        public Timestamp ModifiedTimestamp { get; set; }
    }
}
