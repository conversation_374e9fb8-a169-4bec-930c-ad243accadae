using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Serialization;

namespace Extric.Towbook.Integrations.Networkfleet
{
    public class Odometer
    {
        [JsonProperty("@type")]
        public string Type { get; set; } = "Odometer";

        [JsonProperty("@units")]
        public string Units { get; set; }

        [JsonProperty("@timestamp")]
        public DateTime Timestamp { get; set; }

        public double Value;
    }
}
