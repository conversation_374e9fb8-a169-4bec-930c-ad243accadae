using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Extric.Towbook.Integration;
using System.Xml.Linq;
using Microsoft.AspNetCore.Routing;

namespace Extric.Towbook.API.Integration.Data.Providers.AutoDataDirect
{
    public class ProviderRegistration
    {
        public ProviderRegistration()
        {
        }

        public static void RegisterRoutes(RouteCollection routes)
        {
            /* string root = "integration/data/providers/autodatadirect/";
            List<Route> rl = new List<Route>();

            rl.Add(routes.MapHttpRoute(
                name: "Integration_Forms_Valide_Get",
                routeTemplate: root + "forms/validate",
                defaults: new { controller = "Forms", action = "validate" },
                constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "GET" }) }));

            rl.Add(routes.MapHttpRoute(
                name: "Integration_Autodata_Get",
                routeTemplate: root + "{controller}/{id}/{action}",
                defaults: new { id = RouteParameter.Optional, action = "Get" },
                constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "GET" }) }));

            rl.Add(routes.MapHttpRoute(
                name: "Integration_Autodata_Put",
                routeTemplate: root + "{controller}/{id}/{action}",
                defaults: new { id = RouteParameter.Optional, action = "Put" },
                constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "PUT" }) }));


            rl.Add(routes.MapHttpRoute(
                name: "Integration_Autodata_Post",
                routeTemplate: root + "{controller}/{id}",
                defaults: new { id = RouteParameter.Optional, action = "Post" },
                constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "POST" }) }));


            rl.Add(routes.MapHttpRoute(
                name: "Integration_Autodata_Delete",
                routeTemplate: root + "{controller}/{id}",
                defaults: new { id = RouteParameter.Optional, action = "Delete" },
                constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "DELETE" }) }));



            rl.Add(routes.MapHttpRoute(
                name: "Integration_Autodata",
                routeTemplate: root + "{controller}/{id}",
                defaults: new { action = "Get", id = RouteParameter.Optional }));

            foreach (var r in rl)
            {
                if (r.DataTokens == null)
                    r.DataTokens = new RouteValueDictionary();
                r.DataTokens["Namespaces"] = new string[] { "Extric.Towbook.API.Integration.Data.Providers.AutoDataDirect" };
            } */


            var p = Extric.Towbook.Integration.Provider.Autodata;

            p.RegisterKey(KeyType.Company, "AutodataId");
            p.RegisterKey(KeyType.Company, "AutodataPin");
            p.RegisterKey(KeyType.DispatchEntry, "referenceId");
            p.RegisterKey(KeyType.DispatchEntry, "recordUuid");
        }
    }
}
