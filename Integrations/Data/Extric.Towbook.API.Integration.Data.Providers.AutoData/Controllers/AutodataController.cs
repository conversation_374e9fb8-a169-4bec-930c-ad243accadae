using System;
using System.Linq;
using System.Xml.Linq;
using Extric.Towbook.Integration;
using Extric.Towbook.Integrations.AutoData.Model;
using Extric.Towbook.Utility;
using Extric.Towbook.WebShared;
using Microsoft.AspNetCore.Mvc;

namespace Extric.Towbook.API.Integration.Data.Providers.AutoDataDirect.Controllers
{
    [Route("integration/data/providers/autodatadirect/autodata")]
    public class AutodataController : ControllerBase
    {
        // GET api/<controller>
        //public UploadRequest Get()
        [HttpGet]
        [Route("")]
        public UploadRequest Get()
        {
            var head = new Head("towbook-b2b", "Twb#295Zk3Q123", "TESTING", "1.3.12");

            //XElement resulthead = head.GenerateXML();

            var jsirecord = new JsiRecord("INSURANCE", "12",
                "P999996", 1111, "ALL-WAYS TOWING", *********, "<EMAIL>", "1230 Plunk Street", "Hollywood", "FL", 33023,
                "3D4GG47B09T547295", "", "", 0, "", "", "",
                "", "",
                "2013-03-19",
                "SCRAP", "", "",
                "Company Sample 1", "", "", "",
                "Company Sample 2", "", "", "", "", "", "", 0,
                "Company Sample 3", "", "", "",
                "", "", "");

            return new UploadRequest(head, jsirecord);
        }

        // GET api/<controller>/5
        [HttpGet]
        [Route("{id}")]
        public string Get(int id)
        {
            return "value";
        }

        // POST api/<controller>
        [HttpPost]
        [Route("")]
        public string[] Post([FromBody]string autodata)
        {
            var s = autodata.IndexOf("<impoundId>");
            var e = autodata.IndexOf("</impoundId>");
            var impoundId = autodata.Substring(s + "<impoundId>".Length, e - (s + "<impoundId>".Length));
            var dispatchEntryId = Impounds.Impound.GetById(Convert.ToInt32(impoundId)).DispatchEntry.Id;

            //Get User-name and Password from KeyValue store
            var loginName = CompanyKeyValue.GetByCompanyId(WebGlobal.CurrentUser.CompanyId, Provider.Autodata.ProviderId, "AutodataId").FirstOrDefault();

            var password = CompanyKeyValue.GetByCompanyId(WebGlobal.CurrentUser.CompanyId, Provider.Autodata.ProviderId, "AutodataPin").FirstOrDefault();

            var uploadRequest = new UploadRequest(XDocument.Parse(autodata.ToString()));
            uploadRequest.Header.LoginName = loginName.Value;
            uploadRequest.Header.Password = password.Value;

            //TODO: move url to web.config app key
            var response = XHRHelper.SendXMLRequest("https://apps.add123.com/JSIUploadB2B/serve.xml", new XDocument(uploadRequest.GenerateXML()));

            

            //if (response.ToString() == "ACCEPTED")
            var resp = response.Element("jsi-status-response").Element("jsi-record-status").Element("status").Value;
            string[] respon = new string[4];
            if (resp == "ACCEPTED" || resp == "SENT" || resp == "QUEUED")
            {
                var referenceID = DispatchEntryKeyValue.GetByCompanyId(WebGlobal.CurrentUser.CompanyId, Provider.Autodata.ProviderId, "referenceId").FirstOrDefault();

                if (referenceID == null)
                {
                    referenceID = new DispatchEntryKeyValue()
                    {
                        //CompanyId = WebGlobal.CurrentUser.CompanyId,
                        DispatchEntryId = dispatchEntryId,
                        KeyId = DispatchEntryKey.GetByProviderId(Provider.Autodata.ProviderId).Where(w => w.Name == "referenceId").First().Id,
                    };
                }

                referenceID.Value = "Value"; //TODO: get the correct response value
                referenceID.Save();

                var recordUuid = DispatchEntryKeyValue.GetByCompanyId(WebGlobal.CurrentUser.CompanyId, Provider.Autodata.ProviderId, "recordUuid").FirstOrDefault();

                if (referenceID == null)
                {
                    referenceID = new DispatchEntryKeyValue()
                    {
                        //CompanyId = WebGlobal.CurrentUser.CompanyId,
                        DispatchEntryId = dispatchEntryId,
                        KeyId = DispatchEntryKey.GetByProviderId(Provider.Autodata.ProviderId).Where(w => w.Name == "recordUuid").First().Id,
                    };
                }

                referenceID.Value = "Value"; //TODO: get the correct response value
                referenceID.Save();
                respon[0] = "1";
                respon[1] = response.Element("jsi-status-response").Element("jsi-record-status").Element("status").Value;
                
            }
            else
            {
                respon[0] = "0";
                respon[1] = response.Element("jsi-status-response").Element("jsi-record-status").Element("status").Value;
                respon[2] = response.Element("jsi-status-response").Element("jsi-record-status").Element("field-in-error").Element("name").Value;
                respon[3] = response.Element("jsi-status-response").Element("jsi-record-status").Element("field-in-error").Element("reason").Value;
            }
            return respon;

        }

        // PUT api/<controller>/5
        [HttpPut]
        [Route("{id}")]
        public void Put(int id, [FromBody]string value)
        {
        }

        // DELETE api/<controller>/5
        [HttpDelete]
        [Route("{id}")]
        public void Delete(int id)
        {
        }
    }
}