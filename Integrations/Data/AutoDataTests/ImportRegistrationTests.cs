using Extric.Towbook.AutoDataDirect;
using Extric.Towbook.Dispatch;
using System.Collections.Generic;
using System.Linq;
using System.Xml.Linq;
using Xunit;

namespace Extric.Towbook.Integrations.AutoData.Tests
{
    public class ImportRegistrationTests
    {
        [Fact]
        public void SinglePayloadTest()
        {
            XElement xmlData = XElement.Load(@"../../../xml/SinglePayloadResponseTestData.xml");

            var responseModel = RecordResponse.FromXml(xmlData.ToString());

            Assert.NotNull(responseModel);
            Assert.NotNull(responseModel.Payloads);
            Assert.True(responseModel.Payloads.Count() == 1);

            var interestedEntities = responseModel.Payloads
                .SelectMany(s => s.Vehicle?.InterestedParties)?.ToList();

            Assert.NotNull(interestedEntities);
            Assert.True(interestedEntities.Count() == 2);
        }

        [Fact]
        public void MultiplePayloadsTest()
        {
            XElement xmlData = XElement.Load(@"../../../xml/MultiplePayloadResponseTestData.xml");

            var responseModel = RecordResponse.FromXml(xmlData.ToString());

            Assert.NotNull(responseModel);
            Assert.NotNull(responseModel.Payloads);
            Assert.True(responseModel.Payloads.Count() == 2);

            var interestedEntities = responseModel.Payloads
                .SelectMany(s => s.Vehicle?.InterestedParties)?.ToList();

            Assert.NotNull(interestedEntities);
            Assert.True(interestedEntities.Count() == 4);
        }

        [Fact]
        public void NmvtisRecordTest1()
        {
            var xmlData = XElement.Load(@"../../../xml/NMVTISPPI-VinPointerTestData1.xml");
            var responseModel = RecordResponse.FromXml(xmlData.ToString());

            var ppi = responseModel.Payloads.FirstOrDefault()?.NmvtisPpiRecord;

            Assert.Equal("3916", ppi.referenceid);

            var titlingJurisdition = ppi.VinPointerRecords?.FirstOrDefault()?.TitlingJurisdiction;

            Assert.Equal("AL", titlingJurisdition);
        }

        [Fact]
        public void NmvtisRecordTest2()
        {
            var xmlData = XElement.Load(@"../../../xml/NMVTISPPI-VinPointerTestData2.xml");
            var responseModel = RecordResponse.FromXml(xmlData.ToString());

            var ppi = responseModel.Payloads.FirstOrDefault()?.NmvtisPpiRecord;

            Assert.Equal("365941", ppi.referenceid);

            var titlingJurisdition = ppi.VinPointerRecords?.FirstOrDefault()?.TitlingJurisdiction;

            Assert.Equal("FL", titlingJurisdition);
        }

        [Fact]
        public void NmvtisRecordTest3()
        {
            var xmlData = XElement.Load(@"../../../xml/NMVTISPPI-VinPointerTestData3.xml");
            var responseModel = RecordResponse.FromXml(xmlData.ToString());

            var ppi = responseModel.Payloads.FirstOrDefault()?.NmvtisPpiRecord;

            Assert.Equal("54597", ppi.referenceid);

            var titlingJurisdition = ppi.VinPointerRecords?.FirstOrDefault()?.TitlingJurisdiction;

            Assert.Equal("FL", titlingJurisdition);
        }

        [Fact]
        public void NmvtisRecordVehicleBrandCodeNameTest()
        {
            var xmlData = XElement.Load(@"../../../xml/NMVTISPPI-VehicleBrandNameTestData.xml");
            var responseModel = RecordResponse.FromXml(xmlData.ToString());

            var ppi = responseModel.Payloads.FirstOrDefault()?.NmvtisPpiRecord;

            Assert.Equal("nmvtis", ppi.jurisdictioncode);
            Assert.Equal("225568", ppi.referenceid);

            var brandItem = ppi.vehiclebrands?.Brands?.FirstOrDefault();

            Assert.Equal("Former Rental", brandItem?.BrandCodeName);
        }

        [Fact]
        public void NmvtisRecordActiveStolenRecordTest()
        {
            var xmlData = XElement.Load(@"../../../xml/NMVTISPPI-VehicleActiveStolenRecordTestData.xml");
            var responseModel = RecordResponse.FromXml(xmlData.ToString());

            var ppi = responseModel.Payloads.FirstOrDefault()?.NmvtisPpiRecord;

            Assert.Equal("nmvtis", ppi.jurisdictioncode);
            Assert.Equal("467", ppi.referenceid);

            var sr = ppi.StolenRecords?.FirstOrDefault();

            Assert.Equal("MI", sr.State);
            Assert.Equal("2023-05-23", sr.IncidentDate.ToString("yyyy-MM-dd"));
            Assert.Equal("Active", sr.Status);
        }

        [Fact]
        public void StolenRecordDateTimeTest()
        {
            var dates = new string[] {
                "2020-12-31 00:00:00.0 EST",
                "2023-01-08 00:00:00.0 EST",
                "2023-05-23 01:00:00.0 EDT",
                "2020-12-31 00:00:00.0 CST",
                "2020-12-31 00:00:00.0 CDT"
            };

            var expected = new string[]
            {
                "2020-12-31 00:00:00",
                "2023-01-08 00:00:00",
                "2023-05-23 01:00:00",
                "2020-12-31 00:00:00",
                "2020-12-31 00:00:00"
            };

            var actual = new List<string>();

            foreach (var d in dates)
            {
                var r = new nmvtisppirecordStolenRecord()
                {
                    DateString = d
                };

                actual.Add(r.DateString);
            }

            Assert.Equal(expected.Length, actual.Count());

            for (int i = 0; i < expected.Length; i++)
            {
                Assert.Equal(actual[i], expected[i]);
            }
        }

        [Fact]
        public void MultipleAddressRegistrantTest()
        {
            var xmlData = XElement.Load(@"../../../xml/KitchenSink.xml");
            var responseModel = RecordResponse.FromXml(xmlData.ToString());

            var registrants = responseModel.Payloads?.SelectMany(s => s.Vehicle.InterestedParties);

            Assert.NotNull(registrants);
            Assert.Equal(7, registrants.Count());

            var expected = new List<KeyValuePair<string, string>>()
            {
                new KeyValuePair<string, string>("Lienholder 1", "10000 STIRLING RD SUITE 5"),
                new KeyValuePair<string, string>("Lienholder 1", "1000 FAKE RD"),
                new KeyValuePair<string, string>("Lienholder 2", "12123 SILVER RD FLOOR 5"),
                new KeyValuePair<string, string>("Lienholder 2", "1000 FAKE RD"),
                new KeyValuePair<string, string>("Owner 1", "11123 WILLOW GROVE"),
                new KeyValuePair<string, string>("Owner 2", "305 JEHU RD"),
                new KeyValuePair<string, string>("Owner 2", "123 PINE AVE"),
                new KeyValuePair<string, string>("Registrant 1", "11123 WILLOW GROVE"),
                new KeyValuePair<string, string>("Registrant 1", "305 JEHU RD"),
                new KeyValuePair<string, string>("Registrant 2", "305 JEHU RD"),
                new KeyValuePair<string, string>("Registrant 2", "11123 WILLOW GROVE LN"),
                new KeyValuePair<string, string>("Insurance Company", "5123 SUMMERBROOK DR, BOX L31A"),
            };

            var contacts = AutoDataHelper.MapVehicleRegistrantsToEntryContacts(registrants).ToList();

            Assert.Equal(12, contacts.Count());

            // match to four contact types
            Assert.Equal(4, contacts.GroupBy(g => g.Type).Count());

            // match to four contacts grouped by name
            Assert.Equal(4, contacts.GroupBy(g => g.Name).Count());

            // match each contact to expected result
            foreach (var c in contacts)
            {
                var count = expected.Where(f => f.Key == c.Notes && f.Value == c.Address).Count();
                Assert.Equal(1, count);
            }
        }

        [Fact]
        public void InactiveLienholderTest()
        {
            var xmlData = XElement.Load(@"../../../xml/OHVIN-InactiveLienholderTestData.xml");
            var responseModel = RecordResponse.FromXml(xmlData.ToString());

            Assert.Equal("f08f6ad5-697b-40f9-8f55-d3b7ef5977ab", responseModel.TransactionUuid);

            var registrants = responseModel.Payloads?.SelectMany(s => s.Vehicle.InterestedParties);

            Assert.NotNull(registrants);
            Assert.Equal(3, registrants.Count());

            var firstLienholder = registrants.FirstOrDefault(f => f.EntityTitle == "Lienholder 1");

            Assert.NotNull(firstLienholder);
            Assert.Equal("INACTIVE", firstLienholder.LienStatus);

            // translate to entry contacts (do not save)
            var contacts = AutoDataHelper.MapVehicleRegistrantsToEntryContacts(registrants).ToList();

            Assert.Equal(2, contacts.Count());

            Assert.True(contacts.Count(f => f.Type == ContactType.Owner) == 1);
            Assert.True(contacts.Count(f => f.Type == ContactType.Registrant) == 1);

        }
    }
}