<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>netstandard2.0</TargetFramework>
		<GenerateAssemblyInfo>false</GenerateAssemblyInfo>
	</PropertyGroup>

	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
		<AllowUnsafeBlocks>true</AllowUnsafeBlocks>
	</PropertyGroup>

	<ItemGroup>
	  <Compile Remove="Properties\**" />
	  <EmbeddedResource Remove="Properties\**" />
	  <None Remove="Properties\**" />
	</ItemGroup>

	<ItemGroup>
	  <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
	  <PackageReference Include="RestSharp" Version="111.4.1" />
	</ItemGroup>

	<ItemGroup>
	  <ProjectReference Include="..\..\..\Extric.Towbook\Extric.Towbook.csproj" />
	</ItemGroup>

</Project>