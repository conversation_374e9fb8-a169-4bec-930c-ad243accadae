using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;

namespace Extric.Towbook.Integrations.AutoDataDirect.Model
{
    public class FormsApiResponseModel
    {
        [JsonProperty("transId")]
        public string TransactionUuid { get; set; }

        /// <summary>
        /// Towbook property to carry the date when the request was packaged as an xml file.
        /// </summary>
        public DateTime CreateDate { get; set; }
        /// <summary>
        /// Towbook property to carry the person who submitted the forms-api request.
        /// </summary>
        public int? OwnerUserId { get; set; }
    }

    
}
