using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace Extric.Towbook.Integrations.AutoData.Model
{
    public class AmRecReq
    {
        public int RecordUuid { get; set; }

        public int ReferenceId { get; set; }

        public string AmendmentReason { get; set; }

        public AmRecReq(int recorduuid, int referenceid, string amendmentreason) 
        {
            this.RecordUuid = recorduuid;
            this.ReferenceId = referenceid;
            this.AmendmentReason = amendmentreason;
        }

        public AmRecReq(XElement amrecreq) 
        { 
            this.RecordUuid = Convert.ToInt32(amrecreq.Element("record-uuid").Value);
            this.ReferenceId = Convert.ToInt32(amrecreq.Element("reference-id").Value);
            this.AmendmentReason = amrecreq.Element("amendment-reason").Value;
        }

        public XElement GenerateXML() 
        {
            XElement result = new XElement("amend-record",
                new XElement("record-uuid", this.RecordUuid),
                new XElement("reference-id", this.ReferenceId),
                new XElement("amendment-reason", this.AmendmentReason));
            return result;
        }
    }

}
