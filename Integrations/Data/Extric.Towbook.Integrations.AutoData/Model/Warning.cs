using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace Extric.Towbook.Integrations.AutoData.Model
{
    public class Warning
    {
        public string Type { get; set; }

        public string Reason { get; set; }
        
        public Warning()
        {
        }

        public Warning(XElement warnings)
        {
            this.Type = warnings.Element("type").Value;
            this.Reason = warnings.Element("reason").Value;
        }

    }
}
