using Extric.Towbook.Integration;
using Microsoft.AspNetCore.Routing;

namespace Extric.Towbook.API.Integration.Accounting.Providers.QuickBooks
{
    public class ProviderRegistration
    {
        public ProviderRegistration()
        {
        }

        public static void RegisterRoutes(RouteCollection routes)
        {
            /* string root = "integration/accounting/providers/quickbooks/";
            List<Route> rl = new List<Route>();

            rl.Add(routes.MapHttpRoute(
                name: "Integration_Quickbooks_Get",
                routeTemplate: root + "{controller}/{id}/{action}",
                defaults: new { id = RouteParameter.Optional, action = "Get" },
                constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "GET" }) }));

            rl.Add(routes.MapHttpRoute(
                name: "Integration_Quickbooks_Put",
                routeTemplate: root + "{controller}/{id}/{action}",
                defaults: new { id = RouteParameter.Optional, action = "Put" },
                constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "PUT" }) }));


            rl.Add(routes.MapHttpRoute(
                name: "Integration_Quickbooks_Post",
                routeTemplate: root + "{controller}/{id}",
                defaults: new { id = RouteParameter.Optional, action = "Post" },
                constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "POST" }) }));


            rl.Add(routes.MapHttpRoute(
                name: "Integration_Quickbooks_Delete",
                routeTemplate: root + "{controller}/{id}",
                defaults: new { id = RouteParameter.Optional, action = "Delete" },
                constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "DELETE" }) }));

            rl.Add(routes.MapHttpRoute(
                name: "Integration_Quickbooks",
                routeTemplate: root + "{controller}/{id}",
                defaults: new { action = "Get", id = RouteParameter.Optional }));

            foreach (var r in rl)
            {
                if (r.DataTokens == null)
                    r.DataTokens = new RouteValueDictionary();

                r.DataTokens["Namespaces"] = new string[] { "Extric.Towbook.API.Integration.Accounting.Providers.QuickBooks" };
            } */

            var p = Extric.Towbook.Integration.Provider.QuickBooks;

            //KeyType AgentInvoiceId
            p.RegisterKey(KeyType.Account, "Name");
            p.RegisterKey(KeyType.DispatchEntry, "AgentInvoiceId");
            //KeyType.DispatchEntry, "InvoiceId"
            p.RegisterKey(KeyType.Account, "CustomerId");
            p.RegisterKey(KeyType.DispatchEntry, "InvoiceId");
            p.RegisterKey(KeyType.Payment, "PaymentId");
            p.RegisterKey(KeyType.Payment, "AgentPaymentId");
            p.RegisterKey(KeyType.PaymentMethod, "PaymentTypeId");

            // oauth/connection keys (set when quickbooks is connected to towbook in the OAuthResponseController)

            p.RegisterKey(KeyType.Company, "Token", "OAuth Connection Keys");
            p.RegisterKey(KeyType.Company, "SecretToken", "OAuth Connection Keys");
            p.RegisterKey(KeyType.Company, "RealmId", "OAuth Connection Keys");
            p.RegisterKey(KeyType.Company, "DataSource", "OAuth Connection Keys");
            p.RegisterKey(KeyType.Company, "ItemRegistrationDateTime", "Date/Time of last time items were registered in Quickbooks from Towbook");

            p.RegisterKey(KeyType.Company, "AccessToken", "OAuth2 Access Token");
            p.RegisterKey(KeyType.Company, "RefreshToken", "OAuth2 Refresh Token");
            p.RegisterKey(KeyType.Company, "RefreshTokenExpires", "Refresh Token Expiration");

            // TODO: SalesTaxId/SalesTaxName, should be part of key's for TaxRate (Extric.Towbook.TaxRate) but they don't exist yet.
            p.RegisterKey(KeyType.Company, "SalesTaxId");
            p.RegisterKey(KeyType.Company, "SalesTaxName");

            p.RegisterKey(KeyType.RateItem, "ItemId");

            //KeyType AgentItemId
            p.RegisterKey(KeyType.RateItem, "AgentItemId");

            p.RegisterKey(KeyType.Company, "AllowAccountlessInvoices",
                "Key to determine if it's possible to send accountless invoices to QB");

            p.RegisterKey(KeyType.Company, "InvoicePaymentSchedule",
                "Key to save the schedule configuration for sending Invoices and Payments to QB");

            p.RegisterKey(KeyType.Company, "IncomeAccountRef",
                "Key to save the IncomeAccountRef for the Creation of RateItems");

            p.RegisterKey(KeyType.Company, "COGSAccountRef",
                "Key to save the COGSAccountRef for the Creation of RateItems");

            p.RegisterKey(KeyType.Company, "AssetAccountRef",
                "Holds the AssetAccountRef for the creation of Items/RateItems");

            p.RegisterKey(KeyType.Company, "UseInvoiceNumberInsteadOfCallNumber",
                "If set to 1, then the InvoiceNumber will be set to the Entry.InvoiceNumber, instead of \"T\" + Entry.CallNumber.");

            p.RegisterKey(KeyType.Company, "CustomInvoiceNumberPrefix",
                "Prefix the invoice number/DocNumber with this value. Useful for multi-company setups and to prevent number collisions");

            p.RegisterKey(KeyType.Company, "DefaultClass",
                "Holds the Class ID for the default class to assign to line items on invoices.");

            p.RegisterKey(KeyType.Company, "DefaultTaxAgency",
                "Holds the TaxAgency ID for the default tax agencyto assign when creating new TaxCodes/TaxRates via TaxService.");


            p.RegisterKey(KeyType.Company, "CustomRateItem");

            p.RegisterKey(KeyType.Company, "PushPayments");

            p.RegisterKey(KeyType.Company, "PreventItemCreation",
                "Set to 1 to tell Towbook to never create new Items");

            p.RegisterKey(KeyType.TaxRate, "TaxCodeId", "QB TaxCodeId");
            p.RegisterKey(KeyType.TaxRate, "TaxRateId", "QB TaxRateId");

            
            

            p.RegisterKey(KeyType.Company, "TaxableTaxCodeId");
            p.RegisterKey(KeyType.Company, "NonTaxableTaxCodeId");


            p.RegisterKey(KeyType.Company, "DepositAccountRef");

            p.RegisterKey(KeyType.Company, "BlockPushingCallNotes");

            p.RegisterKey(KeyType.Company, "PaymentReferenceNumbers", "1=Default/Push Invoice Number, 2=Push Reference Number");

        }
    }
}
