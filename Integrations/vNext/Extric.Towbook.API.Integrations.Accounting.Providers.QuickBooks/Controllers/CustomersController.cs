using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using Extric.Towbook.Accounts;
using Extric.Towbook.Integration;
using Extric.Towbook.Integrations.Quickbooks;
using Extric.Towbook.Utility;
using Extric.Towbook.Web;
using Extric.Towbook.WebShared;
using Microsoft.AspNetCore.Mvc;
using QBI = Extric.Towbook.Integrations.Quickbooks;
using QBM = Extric.Towbook.Integrations.Quickbooks.Model;

namespace Extric.Towbook.API.Integration.Accounting.Providers.QuickBooks.Controllers
{
    [Route("integration/accounting/providers/quickbooks/customers")]
    public class CustomersController : ControllerBase
    {

        [HttpGet]
        [Route("")]
        public async Task<object> GetSolver([FromQuery] string customerId) =>
         customerId != null ? await GetCustomerByIdAsync(customerId) : await GetListAsync();

        /// <summary>
        /// Gets a customer by the QuickBooks customerId.
        /// </summary>
        /// <param name="customerId"></param>
        /// <returns></returns>
        private async Task<QBM.ICustomer> GetCustomerByIdAsync([FromQuery] string customerId)
        {
            var objQBConnecter = await QBI.QuickbooksUtility.GetConnector(WebGlobal.CurrentUser.CompanyId);
            if (objQBConnecter != null)
            {
                var customer = objQBConnecter.GetCustomerById(customerId);
                if (customer != null)
                    return customer;
                else
                    throw new Extric.Towbook.Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound)
                    {
                        Content = new StringContent($"The customerId you tried to access cannot be found.")
                    });
            }
            else
            {
                throw new Extric.Towbook.Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest)
                {
                    Content = new StringContent($"QuickBooks isn't connected to your Towbook company.")
                });
            }
        }

        /// <summary>
        /// Returns all quickbooks customers.
        /// </summary>
        /// <returns></returns>
        private async Task<IEnumerable<QBM.ICustomer>> GetListAsync()
        {
            var objQBConnecter = await QBI.QuickbooksUtility.GetConnector(WebGlobal.CurrentUser.CompanyId);

            if (objQBConnecter != null)
            {
                var customers = objQBConnecter.GetCustomers().OrderBy(o => o.Company).ToArray();

                return customers;
            }
            else
            {
                var ags = await Agent.Session.GetByCompanyIdAsync(WebGlobal.CurrentUser.CompanyId);

                if (ags != null)
                {
                    var ds = new Agent.QuickBooks.DataService();

                    return ds.GetCustomers(ags.Id).Select(o => new QBM.Customer()
                    {
                        Id = o.ObjectId,
                        Company = o.FullName ?? o.Name,
                        Phone = Core.FormatPhone(o.Phone),
                        Email = new QBM.Email[1]
                        {
                            new QBM.Email() { Address = o.Email }
                        },
                        AddressList = new QBM.Address[1]
                        {
                            new QBM.Address()
                        }
                    });
                }

                return null;
            }
        }

        /// <summary>
        /// Associates a Towbook Account with a Quickbooks Customer. 
        /// If accountId is set to "create", an account will be created. If qbCustomerId is set to "create", a QB customer will be created.
        /// Both cannot be set to create at the same time.
        /// </summary>
        /// <param name="accountId">Towbook Account.Id, represents the Towbook account you want to associate. If an account doesn't exist for the QB Customer you want to associate pass the value "create" (without quotes)</param>
        /// <param name="qbCustomerId">Quickbooks Customer Id, in the form of id:domain. If it doesn't exist, you can pass "create" as the value and the method will create it in QB.</param>
        /// <returns>Dynamic object representing the association created: accountId, accountName, qbCustomerId, qbCustomerName, and linkId.</returns>
        [HttpPost]
        [Route("")]
        public async Task<object> Post([FromQuery]string accountId, [FromQuery]string qbCustomerId = null)
        {
            int tbAccountId = -1;

            QBM.ICustomer customer = null;
            Account account = null;

            if (accountId.ToLowerInvariant() == "create" &&
                qbCustomerId.ToLowerInvariant() == "create")
            {
                return new HttpResponseMessage(HttpStatusCode.InternalServerError)
                {
                    Content = new StringContent("Both accountId and qbCustomerId are set to create. Either an existing account or customer must be specified.")
                };
            }

            if (accountId.ToLowerInvariant() == "create" && qbCustomerId.ToLowerInvariant() != "create")
            {
                customer = (await QuickbooksUtility.GetConnector(WebGlobal.CurrentUser.CompanyId))?.GetCustomerById(qbCustomerId);
            }

            var ags = await Agent.Session.GetByCompanyIdAsync(WebGlobal.CurrentUser.CompanyId);

            if (ags != null)
            {
                return await DoAgentCustomerLink(accountId, qbCustomerId, ags);
            }

            var realmId = CompanyKeyValue.GetFirstValueOrNull(WebGlobal.CurrentUser.CompanyId,
                Provider.QuickBooks.ProviderId, "RealmId");

            if (accountId.ToLowerInvariant() == "create")
            {
                if (customer == null)
                {
                    return new HttpResponseMessage(HttpStatusCode.InternalServerError)
                    {
                        Content = new StringContent("Cannot create Towbook account. The referenced Quickbooks Customer doesn't refer to a valid Customer object.")
                    };
                }

                var existingAccountKV = AccountKeyValue.GetByCompany(WebGlobal.CurrentUser.CompanyId, Provider.QuickBooks.ProviderId, new string[] { "CustomerId" })
                    .FirstOrDefault(o => AccountLink.FindByRealmId(realmId, o.Value)?.CustomerId == customer.Id);

                var al = AccountLink.FindByRealmId(realmId, existingAccountKV?.Value);

                if (al != null)
                {
                    return new HttpResponseMessage(HttpStatusCode.InternalServerError)
                    {
                        Content = new StringContent(new
                        {
                            Error = "Towbook doesn't allow you to create new Towbook Accounts for QB Customers that are already associated with another Towbook Account.",
                            RequestedQbCustomerId = qbCustomerId,
                            ExistingAccountId = existingAccountKV.AccountId,
                            ExistingLinkId = existingAccountKV.Id
                        }.ToJson())
                    };
                }

                account = new Account();
                account.CompanyId = WebGlobal.CurrentUser.CompanyId;
                account.Company = customer.Company;

                if (customer.AddressList.Length > 0)
                {
                    if (customer.AddressList[0].Line2 != null)
                        account.Address = customer.AddressList[0].Line2;
                    else
                        account.Address = customer.AddressList[0].Line1;

                    account.City = customer.AddressList[0].City;
                    account.State = customer.AddressList[0].State;
                    account.Zip = customer.AddressList[0].PostalCode;
                }

                account.Phone = customer.Phone;
                account.Email = customer.EmailAddress;
                account.FullName = customer.FirstName + " " + customer.LastName;
                account.Status = AccountStatus.Active;

                await account.Save();
                tbAccountId = account.Id;
            }
            else
            {
                tbAccountId = Convert.ToInt32(accountId);
                account = await Account.GetByIdAsync(tbAccountId);
            }
            
            if (account == null || !WebGlobal.CurrentUser.HasAccessToCompany(account.CompanyId))
            {
                return new HttpResponseMessage(HttpStatusCode.NotFound)
                {
                    Content = new StringContent("Either the Towbook Account doesn't belong to your company or it doesn't exist.")
                };
            }

            if (qbCustomerId.ToLowerInvariant() == "create")
            {
                // before we create the customer, let's make sure this towbook account doesn't already have an association:

                var existingCustomerKV = AccountKeyValue.GetByAccount(account.CompanyId, account.Id, Provider.QuickBooks.ProviderId, "CustomerId").FirstOrDefault();

                var al2 = AccountLink.FindByRealmId(realmId, existingCustomerKV?.Value);

                if (al2?.CustomerId != null)
                {
                    var temp = (await QuickbooksUtility.GetConnector(WebGlobal.CurrentUser.CompanyId))
                        .GetCustomerById(al2.CustomerId);

                    if (temp != null)
                    {
                        return new HttpResponseMessage(HttpStatusCode.InternalServerError)
                        {
                            Content = new StringContent(new
                            {
                                Error = "Towbook doesn't let you create new Quickbooks Customers for Accounts that are already associated with another Quickbooks Customer.",
                                RequestedQbCustomerId = al2.CustomerId,
                                ExistingQbCustomerId = existingCustomerKV.Value,
                                RequestedAccountId = account.Id,
                                ExistingLinkId = existingCustomerKV.Id
                            }.ToJson())
                        };
                    }
                    else
                    {
                        existingCustomerKV.Value = AccountLink.Parse(existingCustomerKV.Value, realmId).Where(o => o?.RealmId != realmId).ToJson();
                        existingCustomerKV.Save();
                    }
                }

                var newCustomer = new QBM.Customer()
                {
                    Id = null,
                    Company = account.Company,
                    EmailAddress = account.Email,
                    Phone = account.Phone
                };

                if (!string.IsNullOrWhiteSpace(account.FullName))
                {
                    if (account.FullName.IndexOf(" ") > 0)
                    {
                        newCustomer.FirstName = account.FullName.Substring(0, account.FullName.IndexOf(" ")).Trim();
                        newCustomer.LastName = account.FullName.Substring(account.FullName.IndexOf(" ") + 1).Trim();
                    }

                    if (newCustomer.LastName.Length > 25)
                        newCustomer.LastName = newCustomer.LastName.Substring(0, 25);

                    if (newCustomer.FirstName.Length > 25)
                        newCustomer.FirstName = newCustomer.FirstName.Substring(0, 25);
                }

                if (!string.IsNullOrWhiteSpace(account.Address))
                {
                    newCustomer.AddressList = new QBM.Address[1];
                    newCustomer.AddressList[0] = new QBM.Address()
                    {
                        Line1 = account.Address,
                        City = account.City,
                        State = account.State,
                        PostalCode = account.Zip,
                        Default = true,
                        Tag = new string[] { "Billing" }
                    };
                }
                customer = await QBI.CustomerHelper.AddCustomerAsync(WebGlobal.CurrentUser.CompanyId, newCustomer);

                
               Extric.Towbook.Web.HttpContext.Current.Items["QBCustomers_" + WebGlobal.CurrentUser.CompanyId] = null;
            }
            else
            {
                if (customer == null)
                    customer = (await QuickbooksUtility.GetConnector(WebGlobal.CurrentUser.CompanyId)).GetCustomerById(qbCustomerId);

                if (customer == null)
                {
                    return new HttpResponseMessage(HttpStatusCode.InternalServerError)
                    {
                        Content = new StringContent(new
                        {
                            Error = "The customer requested doesn't exist.",
                            RequestedQbCustomerId = qbCustomerId
                        }.ToJson())
                    };
                }
            }

            AccountKeyValue result1 = null;
            if (qbCustomerId.ToLowerInvariant() != "create" &&
                accountId.ToLower() != "create")
            {
                // check if link already exists. 

                result1 = AccountKeyValue.GetByAccount(account.CompanyId, account.Id, Provider.QuickBooks.ProviderId, "CustomerId").FirstOrDefault();

                var r2 = AccountLink.FindByRealmId(realmId, result1?.Value);

                if (r2?.CustomerId == qbCustomerId)
                {
                    return new HttpResponseMessage(HttpStatusCode.InternalServerError)
                    {
                        Content = new StringContent(new
                        {
                            Error = "The association you tried to create already exists.",
                            RequestedQbCustomerId = qbCustomerId,
                            RequestedAccountId = account.Id,
                            ExistingLinkId = result1.Id
                        }.ToJson())
                    };
                }

                if (r2 != null)
                {
                    var iq = await QuickbooksUtility.GetConnector(WebGlobal.CurrentUser.CompanyId);

                    var q = iq.GetCustomerById(r2.CustomerId);

                    if (q == null)
                    {
                        result1.Value = AccountLink.Parse(result1.Value, realmId).Where(o => o.RealmId != realmId).ToJson();
                        result1.Save();
                    }
                    else
                    {
                        if (q.Company.Contains("deleted"))
                        {
                            result1.Value = AccountLink.Parse(result1.Value, realmId).Where(o => o.RealmId != realmId).ToJson();
                            result1.Save();
                        }
                        else
                        {
                            return new HttpResponseMessage(HttpStatusCode.InternalServerError)
                            {
                                Content = new StringContent(new
                                {
                                    Error = "The Towbook Account you're trying to associate, is already associated with another QuickBooks Customer...",
                                    RequestedQbCustomerId = qbCustomerId,
                                    RequestedAccountId = account.Id,
                                    ExistingQbCustomerId = r2.CustomerId,
                                    ExistingLinkId = result1.Id,
                                    FoundCustomer = q
                                }.ToJson())
                            };
                        }
                    }
                }
            }
            var newAssoc = new AccountLink { RealmId = realmId, CustomerId = customer.Id };

            var result = AccountKeyValue.GetByAccount(account.CompanyId, account.Id, Provider.QuickBooks.ProviderId, "CustomerId").FirstOrDefault();

            var array = Array.Empty<AccountLink>();

            if (result != null)
                array = AccountLink.Parse(result.Value, realmId);

            array = array.Union(new[] { newAssoc }).ToArray();

            if (result1 == null)
                result1 = new AccountKeyValue()
                {
                    AccountId = account.Id,
                    KeyId = Provider.QuickBooks.GetKey(KeyType.Account, "CustomerId").Id,
                };

            result1.Value = array.ToJson();
            result1.Save();

            return new
            {
                LinkId = result1.Id,
                AccountId = account.Id,
                AccountName = account.Company,
                QbCustomerId = customer.Id,
                QbCustomerName = customer.Company
            };
        }

        private async Task<object> DoAgentCustomerLink(string accountId, string qbCustomerId, Agent.Session ags)
        {
            var ds = new Agent.QuickBooks.DataService();

            var qbCustomer = ds.GetCustomerById(ags.Id, qbCustomerId);
            Account account = null;

            if (accountId == "create")
            {
                account = new Account();
                account.CompanyId = WebGlobal.CurrentUser.CompanyId;
                account.Company = qbCustomer.Company ?? qbCustomer.Name;
                account.Phone = qbCustomer.Phone;
                account.Email = qbCustomer.Email;
                account.FullName = qbCustomer.FirstName + " " + qbCustomer.LastName;
                account.Status = AccountStatus.Active;

                await account.Save();
            }
            else
            {
                account = await Account.GetByIdAsync(Convert.ToInt32(accountId));
            }

            if (qbCustomerId == "create")
            {
                var newCustomer = new Agent.QuickBooks.Customer();
                newCustomer.Name = account.Company;
                newCustomer.Company = account.Company;
                newCustomer.Email = account.Email;
                newCustomer.Phone = Core.FormatPhone(account.Phone);
                newCustomer.IsActive = true;

                var qss = Agent.Sync.QuickbooksSyncService.Get(ags.Id);
                if (qss != null)
                {
                    await qss.Push(newCustomer);
                    qbCustomer = newCustomer;
                }
            }

            if (qbCustomer != null && account != null)
            {
                var r = new AccountKeyValue()
                {
                    AccountId = account.Id,
                    KeyId = Provider.QuickBooks.GetKey(KeyType.Account, "Name").Id,
                    Value = qbCustomer.FullName ?? qbCustomer.Name
                };

                r.Save();

                return new
                {
                    LinkId = r.Id,
                    AccountId = account.Id,
                    AccountName = account.Company,
                    QbCustomerId = qbCustomer.Id,
                    QbCustomerName = qbCustomer.FullName ?? qbCustomer.Name
                };
            }

            return null;
        }
    }
}
