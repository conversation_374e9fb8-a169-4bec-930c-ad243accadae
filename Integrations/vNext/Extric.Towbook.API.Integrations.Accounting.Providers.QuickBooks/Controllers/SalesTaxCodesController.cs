using Extric.Towbook.Integrations.Quickbooks;
using Extric.Towbook.WebShared;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using System.Threading.Tasks;

namespace Extric.Towbook.API.Integration.Accounting.Providers.QuickBooks.Controllers
{
    [Route("integration/accounting/providers/quickbooks/salestaxcodes")]
    public class SalesTaxCodesController : ControllerBase
    {
        [HttpGet]
        [Route("")]
        public async Task<object> Get()
        {
            QuickBooksOnlineConnector qbc = await QuickbooksUtility.GetConnector(WebGlobal.CurrentUser.CompanyId) as QuickBooksOnlineConnector;

            if (qbc != null)
            {
                return JArray.Parse(qbc.GetNativeSalesTaxCodes());
            }
            else
            {
                return null;
            }
        }
    }
}
