using Microsoft.AspNetCore.Mvc;

namespace Extric.Towbook.API.Integration.Accounting.Providers.QuickBooks.Controllers
{
    [Route("integration/accounting/providers/quickbooks/test")]
    public class QuickBooksTestController : ControllerBase
    {
        [HttpGet]
        [Route("")]
        public string Get()
        {
            return "Succesfully processed request for Extric.Towbook.API.Integration.Accounting.Providers.QuickBooks";
        }
    }
}
