<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <RootNamespace>Extric.Towbook.API.Integration.Accounting.Providers.QuickBooks</RootNamespace>
    <AssemblyName>Extric.Towbook.API.Integration.Accounting.Providers.QuickBooks</AssemblyName>
    <OutputType>Library</OutputType>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.CSharp" Version="4.7.0" />
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.21.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\..\Extric.Towbook.WebShared.Net5\Extric.Towbook.WebShared.Net5.csproj">
      <CopyLocalSatelliteAssemblies>false</CopyLocalSatelliteAssemblies>
      <Private>false</Private>
    </ProjectReference>
    <ProjectReference Include="..\Extric.Towbook.Integrations.Quickbooks\Extric.Towbook.Integrations.Quickbooks.csproj">
      <CopyLocalSatelliteAssemblies>false</CopyLocalSatelliteAssemblies>
      <Private>false</Private>
    </ProjectReference>
  </ItemGroup>

</Project>