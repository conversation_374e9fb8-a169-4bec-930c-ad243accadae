using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Extric.Towbook.Integrations.Quickbooks.Model
{
    public interface IPaymentMethod
    {
        string PaymentMethodId { get; set; }
        string SyncToken { get; set; }
        MetaData MetaData { get; set; }
        string ExternalKey { get; set; }
        bool Synchronized { get; set; }
        string ObjectState { get; set; }
        string Name { get; set; }
        bool Active { get; set; }
        string Type { get; set; }
    }
}
