using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Extric.Towbook.Integrations.Quickbooks.Model
{
    /// <summary>
    /// Interface for Payment that holds payment related properties.
    /// </summary>
    public interface IPayment
    {
        string PaymentId { get; set; }
        string SyncToken { get; set; }
        MetaData MetaData { get; set; }
        PaymentHeader Header { get; set; }
        PaymentLine[] Lines { get; set; }

        string ReferenceNumber { get; set; }
    }
}
