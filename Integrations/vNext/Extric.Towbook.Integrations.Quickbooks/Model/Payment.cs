namespace Extric.Towbook.Integrations.Quickbooks.Model
{

    /// <summary>
    /// Class that implements IPayment interface.
    /// </summary>
    public class Payment : IPayment
    {
        public string PaymentId { get; set; }
        public string SyncToken { get; set; }
        public MetaData MetaData { get; set; }
        public PaymentHeader Header { get; set; }
        public PaymentLine[] Lines { get; set; }

        public string ReferenceNumber { get; set;  }

        public Payment()
        {
            SyncToken = string.Empty;
        }

        public Payment(string paymentId, string syncToken, MetaData metaData, PaymentHeader paymentHeader, PaymentLine[] paymentLines)
        {
            PaymentId = paymentId;
            SyncToken = syncToken;
            MetaData = metaData;
            Header = paymentHeader;
            Lines = paymentLines;
        }
    }
}