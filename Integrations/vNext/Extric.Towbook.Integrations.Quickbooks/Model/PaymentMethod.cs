using System.Diagnostics;

namespace Extric.Towbook.Integrations.Quickbooks.Model
{
    [DebuggerDisplay("Id={PaymentMethodId}, Name={Name}, Active={Active}")]
    public class PaymentMethod : IPaymentMethod
    {
        public string PaymentMethodId { get; set; }
        public string SyncToken { get; set; }
        public MetaData MetaData { get; set; }
        public string ExternalKey { get; set; }
        public bool Synchronized { get; set; }
        public string ObjectState { get; set; }
        public string Name { get; set; }
        public bool Active { get; set; }
        public string Type { get; set; }

        public PaymentMethod()
        {
            SyncToken = string.Empty;
        }
        public override string ToString()
        {
            return Name; 
        }


        public PaymentMethod(string paymentMethodId, string syncToken, MetaData metaData, string externalKey, bool synchronized, string objectState, string name, bool active, string type)
        {
            PaymentMethodId = paymentMethodId;
            SyncToken = syncToken;
            this.MetaData = metaData;
            ExternalKey = externalKey;
            Synchronized = synchronized;
            ObjectState = objectState;
            Name = name;
            Active = active;
            Type = type;
        }
    }
}
