using Extric.Towbook.Utility;
using Microsoft.AspNetCore.Mvc;
using NLog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Extric.Towbook.API.Integration.Gateway.ApiModels;

namespace Extric.Towbook.API.Integration.Gateway.AutoRescueApi
{

    [Route("receivers/autorescue/dispatch/{id}/accept")]
    public partial class DispatchAcceptController : ControllerBase
    {
        private static readonly Logger logger = LogManager.GetCurrentClassLogger();

        [HttpPost]
        public async Task<object> Post(string id, DispatchAcceptModel model)
        {
            if (model == null)
                throw new Extric.Towbook.Web.HttpResponseException(HttpStatusCode.BadRequest);

            var json = JsonExtensions.ToJson(model);
            var log = this.NewLogEvent("DispatchAccept", json);

            try
            {
                log.Properties["dispatchId"] = id;
                log.Properties["queueItemId"] = await this.SendToBackendService(GatewayEventType.Accept,
                    json,
                    id);
            }
            catch (Exception e)
            {
                log.Properties["exception"] = e;
            }
            finally
            {
                logger.Log(log);
            }

            return new HttpResponseMessage(HttpStatusCode.Accepted);
        }
    }
}
/*



POST
{ baseUrl}/ dispatch /{
    dispatchId]/ accept
{
        "eta": 45,
  "reasonId": 123 
}
*/