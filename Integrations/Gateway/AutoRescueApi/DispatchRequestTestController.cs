using Extric.Towbook.Utility;
using Microsoft.AspNetCore.Mvc;
using NLog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Extric.Towbook.API.Integration.Gateway.ApiModels;

namespace Extric.Towbook.API.Integration.Gateway.AutoRescueApi
{
    [Route("receivers/autorescue/dispatch/requesttest")]
    public class DispatchRequestTestController : ControllerBase
    {
        private static readonly Logger logger = LogManager.GetCurrentClassLogger();

        public class RequestTestModel
        {
            public string ConnectionId { get; set; }
            public decimal Latitude { get; set; }
            public decimal Longitude{ get; set; }
            public string Type { get; set; }

            public string Make { get; set; }
            public string Model { get; set; }
            public string Year { get; set; }
            public string Vin { get; set; }
            public string VehicleType { get; set; }
            public string LicensePlate { get; set; }
            public string Color { get; set; }

            public string Odometer { get; set; }
            public string Notes { get; set; }
        }

        [HttpPost]
        [Route("")]
        public async Task<object> Post(RequestTestModel requestTestModel)
        {
            var log = this.NewLogEvent("DispatchRequestTest", requestTestModel.ToJson());

            try
            {
                var arc = new AutoRescueClient(this.GetEnvironment(), this.GetRequestingIp());

                var gc = await GeocodeHelper.Geocode(requestTestModel.Latitude + "," + requestTestModel.Longitude);

                Guid.TryParse(requestTestModel.ConnectionId, out Guid n);

                var conn = GatewayConnection.GetById(n);
                if (conn.EnvironmentId != this.GetEnvironmentId())
                    conn = null;

                if (conn == null)
                {
                    log.Level = LogLevel.Error;
                    log.Message = "Invalid ConnectionId";

                    throw new Extric.Towbook.Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest)
                    {
                        Content = new StringContent("Invalid ConnectionId")
                    });
                }

                string[] actions = new[] { "ACCEPT", "REFUSE", "REQUEST_PHONE_CALL" };

                if (conn.Provider != "Quest" && conn.Provider != "NSD")
                    actions = new[] { "ACCEPT", "REFUSE" };

                var job = new DispatchModel()
                {
                    Actions = actions,
                    ContractorId = conn.ContractorId,
                    DispatchId = Guid.NewGuid().ToString("N"),
                    LocationId = conn.LocationId,
                    Provider = conn.Provider,
                    Job = new Job()
                    {
                        Customer = new Customer()
                        {
                            CustomerPhone = Core.FormatPhoneWithDashesOnly("**********"),
                            CustomerName = "Bruce Wayne",
                            CustomerPhoneType = "Mobile",
                            MemberName = "Bruce Wayne"
                        },
                        IncidentAddress = new IncidentAddress()
                        {
                            Address = gc.Address,
                            City = gc.City,
                            State = gc.State,
                            Zip = gc.Zip,
                            Landmark = "None",
                            PersonInCar = "Yes",
                            SafeLocation = "Yes",
                            Latitude = gc.Latitude,
                            Longitude = gc.Longitude
                        },
                        MaxETA = 90,
                        JobId = DateTime.Now.Ticks.ToString(),
                        Price = "99.95",
                        Type = requestTestModel.Type ?? "Jump Start",
                        Vehicle = new Vehicle()
                        {
                            Color = requestTestModel.Color ?? "Black",
                            Make = requestTestModel.Make ?? "Acura",
                            Model = requestTestModel.Model ?? "NSX",
                            Odometer = requestTestModel.Odometer ?? "4000",
                            VehicleType = requestTestModel.VehicleType ?? "Coupe",
                            LicensePlate = requestTestModel.LicensePlate ?? "XDAN",
                            Year = requestTestModel.Year ?? "2019",
                            Vin = requestTestModel.Vin ?? "19UNC1B09KY000181",
                            AdditionalInformation = "hybrid"
                        },
                        Timestamp = DateTime.Now,
                        RequiredAcknowledgeTimeInSeconds = 90,
                        Notes = requestTestModel.Notes ?? "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.\n" +
                            " Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat."
                    }
                };

                arc.SendOffer(job);

            }
            finally
            {
                logger.Log(log);
            }
            return new HttpResponseMessage(HttpStatusCode.Created);
        }
    }
}


/*
 * 
POST {baseUrl}/dispatch/{dispatchId]/accept 
{
  "eta": 45,
  "reasonId?": 123 
}
*/