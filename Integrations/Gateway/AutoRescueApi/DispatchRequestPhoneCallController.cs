using Microsoft.AspNetCore.Mvc;
using NLog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Extric.Towbook.API.Integration.Gateway.ApiModels;

namespace Extric.Towbook.API.Integration.Gateway.AutoRescueApi
{
    [Route("receivers/autorescue/dispatch/{id}/requestphonecall")]
    public class DispatchRequestPhoneCallController : ControllerBase
    {

        private static readonly Logger logger = LogManager.GetCurrentClassLogger();


        [HttpPost]
        [Route("")]
        public async Task<object> Post(string id)
        {
            var log = this.NewLogEvent("DispatchRequestPhoneCall");

            try
            {
                log.Properties["dispatchId"] = id;
                log.Properties["queueItemId"] = await this.SendToBackendService(GatewayEventType.RequestPhoneCall,
                    "{}",
                    id);
            }
            catch (Exception e)
            {
                log.Properties["exception"] = e;
            }
            finally
            {
                logger.Log(log);
            }

            return new HttpResponseMessage(HttpStatusCode.Accepted);
        }



    }
}


/*
 * 
POST {baseUrl}/dispatch/{dispatchId]/accept 
{
  "eta": 45,
  "reasonId?": 123 
}
*/