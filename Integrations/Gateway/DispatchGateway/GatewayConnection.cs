using Extric.Towbook.Utility;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Extric.Towbook.API.Integration.Gateway
{
    [Table("MCDispatch.GatewayConnections")]
    public class GatewayConnection
    {
        [JsonIgnore]
        [Key("ConnectionId")]
        public int ConnectionId { get; set; }

        [JsonIgnore]
        public Guid ConnectionGuid { get; set; }

        [JsonIgnore]
        public int EnvironmentId { get; set; }

        [Ignore(true)]
        public string Id
        {
            get { return ConnectionGuid.ToString("N"); }
            set { }
        }

        public string Provider { get; set; }
        public string ContractorId { get; set; }
        public string LocationId { get; set; }
        public string TaxId { get; set; }
        public DateTime Timestamp { get; set; }
        public bool LoggedIn { get; set; }
        public string Status { get; set; }

        public static GatewayConnection GetById(Guid id)
        {
            return SqlMapper.Query<GatewayConnection>(
                "SELECT * FROM MCDispatch.GatewayConnections WHERE ConnectionGuid=@Id AND IsDeleted=0",
                new
                {
                    Id = id
                }).FirstOrDefault();
        }
        public static GatewayConnection GetByContractorId(string contractorId)
        {
            return SqlMapper.Query<GatewayConnection>(
                "SELECT * FROM MCDispatch.GatewayConnections WHERE ContractorId=@Id AND IsDeleted=0",
                new
                {
                    Id = contractorId
                }).FirstOrDefault();
        }

        public static IEnumerable<GatewayConnection> GetByEnvironmentId(int environmentId)
        {
            return SqlMapper.Query<GatewayConnection>("SELECT * FROM MCDispatch.GatewayConnections" +
                " WHERE IsDeleted=0 AND EnvironmentId=@EnvironmentId",
                new
                {
                    EnvironmentId = environmentId
                }).ToCollection();
        }

        public void Save()
        {
            if (Timestamp == DateTime.MinValue)
                Timestamp = DateTime.Now;

            using (var c = Core.GetConnection())
            {
                var sm = c.Execute("INSERT INTO MCDispatch.GatewayConnections(ConnectionGuid, Provider, ContractorId,LocationId,Timestamp,LoggedIn,Status,EnvironmentId)" +
                    "VALUES (@ConnectionGuid, @Provider, @ContractorId,@LocationId,@Timestamp,@LoggedIn,@Status, @EnvironmentId)",
                    this);
            }
        }

        public void Delete()
        {
            using (var c = Core.GetConnection())
            {
                c.Execute("UPDATE  MCDispatch.GatewayConnections SET IsDeleted=1 WHERE ConnectionGuid=@Guid AND EnvironmentId=@EnvironmentId",
                    new
                    {
                        Guid = ConnectionGuid,
                        EnvironmentId = EnvironmentId
                    });
            }
        }
    }
}
