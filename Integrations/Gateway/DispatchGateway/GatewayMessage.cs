using Extric.Towbook.Utility;
using System;

namespace Extric.Towbook.API.Integration.Gateway
{
    [Table("MCDispatch.GatewayMessages")]
    public class GatewayMessage
    {
        [Key]
        public long GatewayMessageId { get; set; }
        public int EnvironmentId { get; set; }
        public int MessageType { get; set; }
        public string DispatchId { get; set; }
        public string JsonData { get; set; }
        public DateTime CreateDate { get; set; }

        public GatewayMessage Save()
        {
            CreateDate = DateTime.Now;
            if (this.GatewayMessageId == 0)
                this.GatewayMessageId = SqlMapper.Insert(this);
            else
                SqlMapper.Update(this);

            return this;
        }
    }
}
