<?xml version="1.0" encoding="utf-8"?>
<configuration>
	<appSettings>
		<add key="Core.Database.ConnectionString" value="Data Source=**********;Initial Catalog=Towbook;Integrated Security=False;Trusted_Connection=false;User Id=anthony.carroll;Password=********************************;MultipleActiveResultSets=True"/>
		<add key="Core.Cache.Redis.Servers" value="127.0.0.1"/>
		<add key="Core.Cache.Redis.SentinelServiceName" value="!disable"/>
		<add key="ApiUrl" value="http://localhost:8087"/>
		<add key="WebAppUrl" value="http://localhost"/>
	</appSettings>
	<runtime>
		<assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
			<dependentAssembly>
				<assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral"/>
				<bindingRedirect oldVersion="0.0.0.0-********" newVersion="********"/>
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.ValueTuple" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral"/>
				<bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******"/>
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Memory" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral"/>
				<bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******"/>
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Buffers" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral"/>
				<bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******"/>
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Numerics.Vectors" publicKeyToken="b03f5f7f11d50a3a" culture="neutral"/>
				<bindingRedirect oldVersion="0.0.0.0-4.1.4.0" newVersion="4.1.4.0"/>
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Configuration.ConfigurationManager" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral"/>
				<bindingRedirect oldVersion="0.0.0.0-5.0.0.0" newVersion="5.0.0.0"/>
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Runtime.CompilerServices.Unsafe" publicKeyToken="b03f5f7f11d50a3a" culture="neutral"/>
				<bindingRedirect oldVersion="0.0.0.0-5.0.0.0" newVersion="5.0.0.0"/>
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Threading.Tasks.Extensions" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral"/>
				<bindingRedirect oldVersion="0.0.0.0-4.2.0.1" newVersion="4.2.0.1"/>
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Extensions.Primitives" publicKeyToken="adb9793829ddae60" culture="neutral"/>
				<bindingRedirect oldVersion="0.0.0.0-5.0.0.0" newVersion="5.0.0.0"/>
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Extensions.DependencyInjection.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral"/>
				<bindingRedirect oldVersion="0.0.0.0-3.1.8.0" newVersion="3.1.8.0"/>
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Extensions.Caching.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral"/>
				<bindingRedirect oldVersion="0.0.0.0-3.1.8.0" newVersion="3.1.8.0"/>
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Extensions.Options" publicKeyToken="adb9793829ddae60" culture="neutral"/>
				<bindingRedirect oldVersion="0.0.0.0-3.1.8.0" newVersion="3.1.8.0"/>
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Extensions.Logging.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral"/>
				<bindingRedirect oldVersion="0.0.0.0-3.1.8.0" newVersion="3.1.8.0"/>
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.IdentityModel.Tokens" publicKeyToken="31bf3856ad364e35" culture="neutral"/>
				<bindingRedirect oldVersion="0.0.0.0-6.8.0.0" newVersion="6.8.0.0"/>
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.IdentityModel.Logging" publicKeyToken="31bf3856ad364e35" culture="neutral"/>
				<bindingRedirect oldVersion="0.0.0.0-6.8.0.0" newVersion="6.8.0.0"/>
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.IdentityModel.Tokens.Jwt" publicKeyToken="31bf3856ad364e35" culture="neutral"/>
				<bindingRedirect oldVersion="0.0.0.0-5.6.0.0" newVersion="5.6.0.0"/>
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Bcl.AsyncInterfaces" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral"/>
				<bindingRedirect oldVersion="0.0.0.0-7.0.0.0" newVersion="7.0.0.0"/>
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Collections.Immutable" publicKeyToken="b03f5f7f11d50a3a" culture="neutral"/>
				<bindingRedirect oldVersion="0.0.0.0-5.0.0.0" newVersion="5.0.0.0"/>
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Diagnostics.DiagnosticSource" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral"/>
				<bindingRedirect oldVersion="0.0.0.0-5.0.0.1" newVersion="5.0.0.1"/>
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Azure.Core" publicKeyToken="92742159e12e44c8" culture="neutral"/>
				<bindingRedirect oldVersion="0.0.0.0-1.9.0.0" newVersion="1.9.0.0"/>
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Text.Encodings.Web" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral"/>
				<bindingRedirect oldVersion="0.0.0.0-7.0.0.0" newVersion="7.0.0.0"/>
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Text.Json" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral"/>
				<bindingRedirect oldVersion="0.0.0.0-5.0.0.1" newVersion="5.0.0.1"/>
			</dependentAssembly>
		</assemblyBinding>
	</runtime>
	<startup>
		<supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.8"/>
	</startup>
</configuration>