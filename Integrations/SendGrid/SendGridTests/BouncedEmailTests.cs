using Extric.Towbook.API.Integration.SendGrid.Models;
using Extric.Towbook.API.Integration.SendGrid;
using Extric.Towbook.Utility;
using System.Collections.Generic;
using System;
using Xunit;
using System.Threading.Tasks;

namespace Extric.Towbook.API.Integration.SendGrid.Tests
{
    public class BouncedEmailTests
    {
        [Fact]
        /// <summary>
        /// Mimic an Unclassified bounce delivery event (see log order)
        /// From real logs:
        /* 
           [{
        		"email": "<EMAIL>",
        		"event": "processed",
        		"send_at": 0,
        		"sg_event_id": "cHJvY2Vzc2VkLTE0MjIzNy13YUE3VVNWNFF3YVhDeFVlbHRuTWt3LTA",
        		"sg_message_id": "waA7USV4QwaXCxUeltnMkw.filterdrecv-7b586f6447-9tm2t-1-63A0C029-A3.0",
        		"smtp-id": "<waA7USV4QwaXCxUeltnMkw@geopod-ismtpd-3-1>",
        		"timestamp": 1671479337

            },
        	{
        		"attempt": "0",
        		"email": "<EMAIL>",
        		"event": "deferred",
        		"response": "unable to get mx info: failed to get IPs from PTR record: lookup <nil>: unrecognized address",
        		"sg_event_id": "ZGVmZXJyZWQtMC0xNDIyMzctd2FBN1VTVjRRd2FYQ3hVZWx0bk1rdy0w",
        		"sg_message_id": "waA7USV4QwaXCxUeltnMkw.filterdrecv-7b586f6447-9tm2t-1-63A0C029-A3.0",
        		"smtp-id": "<waA7USV4QwaXCxUeltnMkw@geopod-ismtpd-3-1>",
        		"timestamp": 1671479337,
        		"tls": 0
        	}
        ]

        [{
        	"attempt": "1",
        	"email": "<EMAIL>",
        	"event": "deferred",
        	"response": "unable to get mx info: failed to get IPs from PTR record: lookup <nil>: unrecognized address",
        	"sg_event_id": "ZGVmZXJyZWQtMS0xNDIyMzctd2FBN1VTVjRRd2FYQ3hVZWx0bk1rdy0w",
        	"sg_message_id": "waA7USV4QwaXCxUeltnMkw.filterdrecv-7b586f6447-9tm2t-1-63A0C029-A3.0",
        	"smtp-id": "<waA7USV4QwaXCxUeltnMkw@geopod-ismtpd-3-1>",
        	"timestamp": 1671479367,
        	"tls": 0
        }]

        [{
        	"attempt": "2",
        	"email": "<EMAIL>",
        	"event": "deferred",
        	"response": "unable to get mx info: failed to get IPs from PTR record: lookup <nil>: unrecognized address",
        	"sg_event_id": "ZGVmZXJyZWQtMi0xNDIyMzctd2FBN1VTVjRRd2FYQ3hVZWx0bk1rdy0w",
        	"sg_message_id": "waA7USV4QwaXCxUeltnMkw.filterdrecv-7b586f6447-9tm2t-1-63A0C029-A3.0",
        	"smtp-id": "<waA7USV4QwaXCxUeltnMkw@geopod-ismtpd-3-1>",
        	"timestamp": 1671479427,
        	"tls": 0
        }]

        [{
        	"bounce_classification": "Unclassified",
        	"email": "<EMAIL>",
        	"event": "bounce",
        	"reason": "unable to get mx info: failed to get IPs from PTR record: lookup <nil>: unrecognized address",
        	"sg_event_id": "Ym91bmNlLTQtMTQyMjM3LXdhQTdVU1Y0UXdhWEN4VWVsdG5Na3ctMA",
        	"sg_message_id": "waA7USV4QwaXCxUeltnMkw.filterdrecv-7b586f6447-9tm2t-1-63A0C029-A3.0",
        	"smtp-id": "<waA7USV4QwaXCxUeltnMkw@geopod-ismtpd-3-1>",
        	"timestamp": 1671479638,
        	"tls": 0,
        	"type": "blocked"
        }]*/
        /// </summary>
        public async Task UnclassifiedBounceEmailTest()
        {
            var companyId = 10577;
            var callId = *********;
            var userId = 43210;
            var emailAddress = "<EMAIL>";

            var events = new List<Event>();

            // create sent email transaction
            var et = UnitTestHelper.CreateSentEmail(companyId, emailAddress, userId, EmailType.DispatchEntry, callId, null);

            // create status event of created
            var etes = await UnitTestHelper.UpdateOrCreateEventStatus(et, EventStatusType.Created);

            Assert.Equal(et.Id, etes.EmailTransactionId);
            Assert.Equal(EventStatusType.Created, etes.Status);
            Assert.Equal(callId, etes.DispatchEntryId);

            var smtpId = Guid.NewGuid().ToString("N");

            // Add double event -> Processed and deferred

            // Set to processed
            // then immediately moved to deferred
            var ev1 = new DeliveredEvent()
            {
                Email = emailAddress,
                EventType = EventType.Processed,
                Timestamp = DateTime.Now,
                SendGridEventId = "SendGridUnitTest-" + Guid.NewGuid().ToString("N"),
                SmtpId = smtpId,
                UniqueArgs = new Dictionary<string, object>()
                {
                    { "callId", callId },
                    { "callNumber", 1234 },
                    { "emailTransactionIds", new string[] {etes.EmailTransactionId.ToString()} },
                    { "category",  new string[] { "SendGridUnitTest", "UnclassifiedBounceEmailTest", $"user{userId}", $"company_{companyId}" } }
                }
            };

            var ev2 = new DeferredEvent()
            {
                Email = emailAddress,
                EventType = EventType.Deferred,
                Attempt = 0,
                Response = "unable to get mx info: failed to get IPs from PTR record: lookup <nil>: unrecognized address",
                SendGridEventId = "SendGridUnitTest-" + Guid.NewGuid().ToString("N"),
                Timestamp = DateTime.Now,
                SmtpId = smtpId,
                UniqueArgs = new Dictionary<string, object>()
                {
                    { "callId", callId },
                    { "callNumber", 1234 },
                    { "emailTransactionIds", new string[] {etes.EmailTransactionId.ToString()} },
                    { "category",  new string[] { "SendGridUnitTest", "UnclassifiedBounceEmailTest", $"user{userId}", $"company_{companyId}" } }
                }
            };

            events.Add(ev1);
            events.Add(ev2);

            var response = UnitTestHelper.ApiPost(events.ToJson());

            Assert.NotNull(response);
            Assert.Equal(System.Net.HttpStatusCode.OK, response.StatusCode);

            // confirm status change (note deferred is considered "processing")
            etes = EmailTransactionEventStatus.GetByEmailTransactionId(etes.EmailTransactionId);
            Assert.Equal(EventStatusType.Processing, etes.Status);

            // first deferred attempt
            events.Clear();
            ev2.Attempt = 1;
            events.Add(ev2);

            response = UnitTestHelper.ApiPost(events.ToJson());

            Assert.NotNull(response);
            Assert.Equal(System.Net.HttpStatusCode.OK, response.StatusCode);

            // second deferred attempt
            events.Clear();
            ev2.Attempt = 2;
            events.Add(ev2);

            response = UnitTestHelper.ApiPost(events.ToJson());

            Assert.NotNull(response);
            Assert.Equal(System.Net.HttpStatusCode.OK, response.StatusCode);

            // confirm status has NOT changed (still deferred/processing)
            etes = EmailTransactionEventStatus.GetByEmailTransactionId(etes.EmailTransactionId);
            Assert.Equal(EventStatusType.Processing, etes.Status);

            events.Clear();

            // final bounce event
            var ev3 = new BounceEvent()
            {
                Email = emailAddress,
                EventType = EventType.Bounce,
                BounceType = BounceEventType.Blocked,
                BounceClassification = "Unclassified",
                Reason = "unable to get mx info: failed to get IPs from PTR record: lookup <nil>: unrecognized address",
                SendGridEventId = "SendGridUnitTest-" + Guid.NewGuid().ToString("N"),
                SmtpId = smtpId,
                Timestamp = DateTime.Now,
                UniqueArgs = new Dictionary<string, object>()
                {
                    { "callId", callId },
                    { "callNumber", 1234 },
                    { "emailTransactionIds", new string[] {etes.EmailTransactionId.ToString()} },
                    { "category",  new string[] { "SendGridUnitTest", "UnclassifiedBounceEmailTest", $"user{userId}", $"company_{companyId}" } }
                }
            };

            events.Add(ev3);

            response = UnitTestHelper.ApiPost(events.ToJson());

            Assert.NotNull(response);
            Assert.Equal(System.Net.HttpStatusCode.OK, response.StatusCode);

            // confirm status has NOT changed to undeliered
            etes = EmailTransactionEventStatus.GetByEmailTransactionId(etes.EmailTransactionId);
            Assert.Equal(EventStatusType.Undeliverable, etes.Status);
        }



        /// <summary>
        /// Mimic an Unclassified bounce delivery event (see log order)
        /// From real logs:
        /*
        [{
		        "category": ["new_user_confirmation", "company_30796", "user151640"],
		        "email": "<EMAIL>",
		        "event": "processed",
		        "send_at": 0,
		        "sg_event_id": "cHJvY2Vzc2VkLTE0MjIzNy1wMlRTTGxHTVJlU2JMNS1Ka3N0UWp3LTA",
		        "sg_message_id": "p2TSLlGMReSbL5-JkstQjw.filterdrecv-7f5bf548fc-nm6q4-1-63A0BB38-9.0",
		        "smtp-id": "<p2TSLlGMReSbL5-JkstQjw@geopod-ismtpd-1-1>",
		        "timestamp": **********
	        },
	        {
		        "bounce_classification": "Invalid Address",
		        "category": ["new_user_confirmation", "company_30796", "user151640"],
		        "email": "<EMAIL>",
		        "event": "bounce",
		        "ip": "************",
		        "reason": "550 5.1.1 The email account that you tried to reach does not exist. Please try double-checking the recipient's email address for typos or unnecessary spaces. Learn more at https://support.google.com/mail/?p=NoSuchUser f10-20020a170902ce8a00b0018938988ea9si12129611plg.520 - gsmtp",
		        "sg_event_id": "Ym91bmNlLTAtMTQyMjM3LXAyVFNMbEdNUmVTYkw1LUprc3RRanctMA",
		        "sg_message_id": "p2TSLlGMReSbL5-JkstQjw.filterdrecv-7f5bf548fc-nm6q4-1-63A0BB38-9.0",
		        "smtp-id": "<p2TSLlGMReSbL5-JkstQjw@geopod-ismtpd-1-1>",
		        "status": "5.1.1",
		        "timestamp": **********,
		        "tls": 1,
		        "type": "bounce"
	        }]
        */
        /// </summary>
        [Fact]
        public async Task InvalidAddressBounceEmailTest()
        {
            var companyId = 10577;
            var callId = *********;
            var userId = 43210;
            var emailAddress = "<EMAIL>";

            var events = new List<Event>();

            // create sent email transaction
            var et = UnitTestHelper.CreateSentEmail(companyId, emailAddress, userId, EmailType.DispatchEntry, callId, null);

            // create status event of created
            var etes = await UnitTestHelper.UpdateOrCreateEventStatus(et, EventStatusType.Created);

            Assert.Equal(et.Id, etes.EmailTransactionId);
            Assert.Equal(EventStatusType.Created, etes.Status);
            Assert.Equal(callId, etes.DispatchEntryId);

            var smtpId = Guid.NewGuid().ToString("N");

            // Set to processed
            // then immediately moved to bounced
            var ev1 = new DeliveredEvent()
            {
                Email = emailAddress,
                EventType = EventType.Processed,
                Timestamp = DateTime.Now,
                SendGridEventId = "SendGridUnitTest-" + Guid.NewGuid().ToString("N"),
                SmtpId = smtpId,
                UniqueArgs = new Dictionary<string, object>()
                {
                    { "callId", callId },
                    { "callNumber", 1234 },
                    { "emailTransactionIds", new string[] {etes.EmailTransactionId.ToString()} },
                    { "category",  new string[] { "SendGridUnitTest", "InvalidAddressBounceEmailTest", $"user{userId}", $"company_{companyId}" } }
                }
            };

            var ev2 = new BounceEvent()
            {
                Email = emailAddress,
                EventType = EventType.Bounce,
                Reason = "550 5.1.1 The email account that you tried to reach does not exist. Please try double-checking the recipient's email address for typos or unnecessary spaces. Learn more at https://support.google.com/mail/?p=NoSuchUser f10-20020a170902ce8a00b0018938988ea9si12129611plg.520 - gsmtp",
                SendGridEventId = "SendGridUnitTest-" + Guid.NewGuid().ToString("N"),
                Timestamp = DateTime.Now,
                SmtpId = smtpId,
                Status = "5.1.1",
                BounceType = BounceEventType.Bounce,
                BounceClassification = "Invalid Address",
                UniqueArgs = new Dictionary<string, object>()
                {
                    { "callId", callId },
                    { "callNumber", 1234 },
                    { "emailTransactionIds", new string[] {etes.EmailTransactionId.ToString()} },
                    { "category",  new string[] { "SendGridUnitTest", "InvalidAddressBounceEmailTest", $"user{userId}", $"company_{companyId}" } }
                }
            };

            events.Add(ev1);
            events.Add(ev2);

            var response = UnitTestHelper.ApiPost(events.ToJson());

            Assert.NotNull(response);
            Assert.Equal(System.Net.HttpStatusCode.OK, response.StatusCode);

            // confirm status change (note deferred is considered "processing")
            etes = EmailTransactionEventStatus.GetByEmailTransactionId(etes.EmailTransactionId);
            Assert.Equal(EventStatusType.Undeliverable, etes.Status);
        }
    }
}