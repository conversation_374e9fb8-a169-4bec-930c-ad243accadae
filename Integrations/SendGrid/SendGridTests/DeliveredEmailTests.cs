using Extric.Towbook.API.Integration.SendGrid.Models;
using Extric.Towbook.Utility;
using System;
using System.Collections.Generic;
using System.Diagnostics.Eventing.Reader;
using System.Linq;
using System.Net.Http.Headers;
using System.Threading.Tasks;
using Xunit;


namespace Extric.Towbook.API.Integration.SendGrid.Tests
{
    public class DeliveredEmailTests
    {
        [Fact]
        public async Task SendDispatchEntryInvoiceEmailTest()
        {
            var companyId = 10577;
            var callId = *********;
            var userId = 43210;
            var emailAddress = "<EMAIL>";

            var events = new List<Event>();

            // create sent email transaction
            var et = UnitTestHelper.CreateSentEmail(companyId, emailAddress, userId, EmailType.DispatchEntry, callId, null);

            // create status event of created
            var etes = await UnitTestHelper.UpdateOrCreateEventStatus(et, EventStatusType.Created);

            Assert.Equal(et.Id, etes.EmailTransactionId);
            Assert.Equal(EventStatusType.Created, etes.Status);
            Assert.Equal(callId, etes.DispatchEntryId);

            // setup delivered event
            var smtpId = Guid.NewGuid().ToString("N");
            var ev1 = new DeliveredEvent()
            {
                Email = emailAddress,
                EventType = EventType.Processed,
                Timestamp = DateTime.Now,
                SendGridEventId = "SendGridUnitTest-" + Guid.NewGuid().ToString("N"),
                SmtpId = smtpId,
                UniqueArgs = new Dictionary<string, object>()
                {
                    { "callId", callId },
                    { "callNumber", 1234 },
                    { "emailTransactionIds", new string[] {etes.EmailTransactionId.ToString()} },
                    { "category",  new string[] { "SendGridUnitTest", "SendDispatchEntryInvoiceEmailTest", $"user{userId}", $"company_{companyId}" } }
                }
            };

            events.Add(ev1);

            // POST "processed" payload to SendGrid webhook controller
            var response = UnitTestHelper.ApiPost(events.ToJson());

            Assert.NotNull(response);
            Assert.Equal(System.Net.HttpStatusCode.OK, response.StatusCode);

            // confirm status change
            etes = EmailTransactionEventStatus.GetByEmailTransactionId(etes.EmailTransactionId);
            Assert.Equal(EventStatusType.Processing, etes.Status);

            // update event to completed
            events.Clear();

            ev1.EventType = EventType.Delivered;

            events.Add(ev1);

            // POST "delivered" payload to SendGrid webhook controller
            response = UnitTestHelper.ApiPost(events.ToJson());

            Assert.NotNull(response);
            Assert.Equal(System.Net.HttpStatusCode.OK, response.StatusCode);

            // confirm status change
            etes = EmailTransactionEventStatus.GetByEmailTransactionId(etes.EmailTransactionId);
            Assert.Equal(EventStatusType.Completed, etes.Status);
        }

        [Fact]
        public async Task SendMultipleDispatchEntryInvoiceEmailTest()
        {
            var companyId = 10577;
            var callIds = new int[] { ********, ******** };
            var accountId = 311903;
            var userId = 43210;
            var emailAddress = "<EMAIL>";

            var events = new List<Event>();

            // create sent email transaction
            var et = new Utility.EmailTransaction()
            {
                CompanyId = companyId,
                StatementId = (int?)null,
                Type = EmailType.Account,
                EmailAddress = emailAddress,
                UserId = userId,
                Status = EventStatusType.Created,
                CreateDate = DateTime.Now,
                ReferenceId = accountId
            };

            // add callIds
            et.DispatchEntryIds = callIds.ToArray();

            et.Save();

            var et2 = et;

            et2.EmailAddress = "<EMAIL>";
            et2.Save();


            // create status event of created
            var etes = await UnitTestHelper.UpdateOrCreateEventStatus(et, EventStatusType.Created);
            var etes2 = await UnitTestHelper.UpdateOrCreateEventStatus(et2, EventStatusType.Created);

            Assert.Equal(et.Id, etes.EmailTransactionId);
            Assert.Equal(EventStatusType.Created, etes.Status);
            Assert.Equal(callIds.First(), etes.DispatchEntryId);

            Assert.Equal(et2.Id, etes2.EmailTransactionId);

            var uniqueArgs = new Dictionary<string, object>();

            uniqueArgs["callId"] = callIds.First().ToString();
            uniqueArgs["callIds"] = string.Join("-", callIds);
            uniqueArgs["emailTransactionIds"] = string.Join(",", new int[] { etes.EmailTransactionId, etes2.EmailTransactionId });
            uniqueArgs["category"] = string.Join(",", new[] { "SendGridUnitTest", "SendDispatchEntryInvoiceEmailTest", $"user{userId}", $"company_{companyId}" });

            // setup delivered event
            var smtpId = Guid.NewGuid().ToString("N");
            
            var ev1 = new DeliveredEvent()
            {
                Email = emailAddress,
                EventType = EventType.Processed,
                Timestamp = DateTime.Now,
                SendGridEventId = "SendGridUnitTest-" + Guid.NewGuid().ToString("N"),
                SmtpId = smtpId,
                UniqueArgs = uniqueArgs
            };

            events.Add(ev1);

            // POST "processed" payload to SendGrid webhook controller
            var response = UnitTestHelper.ApiPost(events.ToJson());

            Assert.NotNull(response);
            Assert.Equal(System.Net.HttpStatusCode.OK, response.StatusCode);

            // confirm status change
            etes = EmailTransactionEventStatus.GetByEmailTransactionId(etes.EmailTransactionId);
            etes2 = EmailTransactionEventStatus.GetByEmailTransactionId(etes2.EmailTransactionId);
            Assert.Equal(EventStatusType.Processing, etes.Status);
            Assert.Equal(EventStatusType.Processing, etes2.Status);

            // update event to completed
            events.Clear();

            ev1.EventType = EventType.Delivered;

            events.Add(ev1);

            // POST "delivered" payload to SendGrid webhook controller
            response = UnitTestHelper.ApiPost(events.ToJson());

            Assert.NotNull(response);
            Assert.Equal(System.Net.HttpStatusCode.OK, response.StatusCode);

            // confirm status change
            etes = EmailTransactionEventStatus.GetByEmailTransactionId(etes.EmailTransactionId);
            etes2 = EmailTransactionEventStatus.GetByEmailTransactionId(etes2.EmailTransactionId);
            Assert.Equal(EventStatusType.Completed, etes.Status);
            Assert.Equal(EventStatusType.Completed, etes2.Status);
        }

        /// <summary>
        /// Test the successful delivery of a statement email.
        /// The test extends further to test successful delivery to two recipients.
        /// </summary>
        [Fact]
        public async Task SendStatementEmailTest()
        {
            var companyId = 10577;
            var statementId = 2653147;
            var userId = 43210;
            var emailAddress1 = "<EMAIL>";
            var emailAddress2 = "<EMAIL>";

            var events = new List<Event>();

            // create sent email transaction
            var et1 = UnitTestHelper.CreateSentEmail(companyId, emailAddress1, userId, EmailType.Statement, null, statementId);
            var et2 = UnitTestHelper.CreateSentEmail(companyId, emailAddress2, userId, EmailType.Statement, null, statementId);

            // create status event of created
            var etes1 = await UnitTestHelper.UpdateOrCreateEventStatus(et1, EventStatusType.Created);
            var etes2 = await UnitTestHelper.UpdateOrCreateEventStatus(et2, EventStatusType.Created);

            Assert.Equal(et1.Id, etes1.EmailTransactionId);
            Assert.Equal(EventStatusType.Created, etes1.Status);
            Assert.Equal(statementId, etes1.StatementId);

            Assert.Equal(et2.Id, etes2.EmailTransactionId);
            Assert.Equal(EventStatusType.Created, etes2.Status);
            Assert.Equal(statementId, etes2.StatementId);


            var smtpId1 = Guid.NewGuid().ToString("N");
            var smtpId2 = Guid.NewGuid().ToString("N");

            // Set first recipient to processed
            // Keep second recipient to created
            var ev1 = new DeliveredEvent()
            {
                Email = emailAddress1,
                EventType = EventType.Processed,
                Timestamp = DateTime.Now,
                SendGridEventId = "SendGridUnitTest-" + Guid.NewGuid().ToString("N"),
                SmtpId = smtpId1,
                UniqueArgs = new Dictionary<string, object>()
                {
                    { "statementId", statementId },
                    { "statementNumber", 1234 },
                    { "emailTransactionIds", new string[] {etes1.EmailTransactionId.ToString()} },
                    { "category",  new string[] { "SendGridUnitTest", "SendStatementEmailTest", $"user{userId}", $"company_{companyId}" } }
                }
            };

            events.Add(ev1);

            var response = UnitTestHelper.ApiPost(events.ToJson());

            Assert.NotNull(response);
            Assert.Equal(System.Net.HttpStatusCode.OK, response.StatusCode);

            // confirm first receipient status change
            etes1 = EmailTransactionEventStatus.GetByEmailTransactionId(etes1.EmailTransactionId);
            Assert.Equal(EventStatusType.Processing, etes1.Status);

            // confirm second receipient status has NO change
            etes2 = EmailTransactionEventStatus.GetByEmailTransactionId(etes2.EmailTransactionId);
            Assert.Equal(EventStatusType.Created, etes2.Status);

            events.Clear();

            // Set first recipient to delivered
            ev1.EventType = EventType.Delivered;

            // Set second recipient to processed
            var ev2 = new DeliveredEvent()
            {
                Email = emailAddress2,
                EventType = EventType.Processed,
                Timestamp = DateTime.Now,
                SendGridEventId = "SendGridUnitTest-" + Guid.NewGuid().ToString("N"),
                SmtpId = smtpId2,
                UniqueArgs = new Dictionary<string, object>()
                {
                    { "staementId", statementId },
                    { "statementNumber", 1234 },
                    { "emailTransactionIds", new string[] {etes2.EmailTransactionId.ToString()} },
                    { "category",  new string[] { "SendGridUnitTest", "SendStatementEmailTest", $"user{userId}", $"company_{companyId}" } }
                }
            };

            events.Add(ev1);
            events.Add(ev2);

            response = UnitTestHelper.ApiPost(events.ToJson());

            Assert.NotNull(response);
            Assert.Equal(System.Net.HttpStatusCode.OK, response.StatusCode);

            // confirm first receipient status change
            etes1 = EmailTransactionEventStatus.GetByEmailTransactionId(etes1.EmailTransactionId);
            Assert.Equal(EventStatusType.Completed, etes1.Status);

            // confirm second receipient status change
            etes2 = EmailTransactionEventStatus.GetByEmailTransactionId(etes2.EmailTransactionId);
            Assert.Equal(EventStatusType.Processing, etes2.Status);

            events.Clear();

            // Set second recipient to delivered
            ev2.EventType = EventType.Delivered;

            events.Add(ev2);

            response = UnitTestHelper.ApiPost(events.ToJson());

            Assert.NotNull(response);
            Assert.Equal(System.Net.HttpStatusCode.OK, response.StatusCode);

            // confirm first receipient status has NO change
            etes1 = EmailTransactionEventStatus.GetByEmailTransactionId(etes1.EmailTransactionId);
            Assert.Equal(EventStatusType.Completed, etes1.Status);

            // confirm second receipient status change
            etes2 = EmailTransactionEventStatus.GetByEmailTransactionId(etes2.EmailTransactionId);
            Assert.Equal(EventStatusType.Completed, etes2.Status);
        }
    }
}
