using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace Extric.Towbook.API.Integration.SendGrid.Models
{
    public class Category
    {
        public string[] Value { get; }
        private JsonToken _jsonToken;

        public Category(string[] value, JsonToken jsonToken)
        {
            Value = value;
            _jsonToken = jsonToken;
        }

        [JsonIgnore]
        public bool IsArray => _jsonToken == JsonToken.StartArray;
    }
}