INSERT INTO MasterAccountReasons (MasterAccountId, Type, Code, Name)
VALUES
 (31, 2, '39','Refuse - do not agree to terms'),
 (31, 2, '36','Equipment not available'),
 (31, 2, '38','Restricted roadway'),
 (31, 2, '915','Insufficient amount for distance'),
 (31, 2, '916','Insufficient amount for weather'),
 (31, 2, '917','Insufficient amount for service type')

GO

CREATE or alter procedure [MCDispatch].[OONAllstateProvidersGetByGeolocationWithCompanyId] (                            
 @CompanyId int,
 @SLAT AS FLOAT,                            
 @SLON AS FLOAT                            
) AS                 
set transaction isolation level read uncommitted                   
--declare @slat float= 48.826030            
--declare @slon float = -122.499674            
       --drop table if exists  #allstateRadiusCalls                 
select top 3  r.Latitude, r.Longitude, r.Distance, c.Name, c.Address, C.Phone, C.Fax, C.City, C.State, c.Zip  , c.CompanyId                      
into #allstateRadiusCalls                      
      From(                            
SELECT TOP 10 UserId, Latitude, Longitude, SQRT(                            
    POWER(69.1 * (Latitude - @SLAT), 2) +                            
    POWER(69.1 * (@SLON - Longitude) * COS(Latitude / 57.3), 2)) AS Distance                            
FROM UserLocationsCurrent with (nolock) where timestamp > dateadd(hour,-1,getdate()) ORDER BY 4 ) r                             
inner join users u with (nolock) on u.userid=r.UserId                
inner join companies c with (nolock) on c.companyid=u.CompanyId and             
c.CompanyId=@CompanyId
and c.companyid not in (select companyid from MCDispatch.AllstateContractors with (nolock) where MasterAccountId=31 and ContractorId not like '0%' and isdeleted=0)                
where r.distance < 55                       
                        
select top 3 y.*, a.AccountId from(                
SELECT top 10  c.Latitude, c.Longitude, SQRT(                            
    POWER(69.1 * (c.Latitude - @SLAT), 2) +                            
    POWER(69.1 * (@SLON - c.Longitude) * COS(c.Latitude / 57.3), 2)) AS Distance, c.Name, c.Address, C.Phone, C.Fax, C.City, C.State, c.Zip  , c.CompanyId                          
FROM companies c                             
where c.companyid=@CompanyId and  SQRT(                            
    POWER(69.1 * (c.Latitude - @SLAT), 2) +                            
    POWER(69.1 * (@SLON - c.Longitude) * COS(c.Latitude / 57.3), 2)) < 55                            
 and c.companyid not in (Select companyid from #allstateRadiusCalls)                      
 and c.companyid not in (select companyid from MCDispatch.AllstateContractors with (nolock) where MasterAccountId=31 and ContractorId not like '0%' and isdeleted=0)                
union all                      
select * From #allstateRadiusCalls   where companyid is not null) y                 
left outer join accounts a on a.CompanyId=y.CompanyId and a.MasterAccountId=31            
inner join MCDispatch.AllstateContractors ac on ac.AccountId=a.AccountId and a.MasterAccountId=31        
where a.CompanyId=@CompanyId