using System;
using Microsoft.AspNetCore.Mvc;

namespace Extric.Towbook.API.Integration.MotorClubs.AaaAce
{
    [Route("receivers/aaa/ace")]
    public class AaaController : ControllerBase
    {
        [HttpGet]
        [Route("")]
        public object Get()
        {
            return "";
        }

        [HttpPost]
        [Route("")]
        public object Post()
        {
            return "AAA Controller POST " + Guid.NewGuid().ToString("n") + " - " + DateTime.Now.ToString();
        }
    }
}
