using System.IO;
using System.Text;
using System.Threading.Tasks;
using Extric.Towbook.Integration.MotorClubs.Services;
using Extric.Towbook.Integrations.MotorClubs.Aaa;
using Extric.Towbook.WebShared;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using NLog;

namespace Extric.Towbook.API.Integration.MotorClubs.AaaNortheast
{
    [Route("receivers/aaa/northeast")]
    public class DispatchRefusedController : ControllerBase
    {
        private static readonly Logger logger = LogManager.GetCurrentClassLogger();

        // POST /dispatch
        [HttpPost]
        [Route("refused")]
        public async Task<ObjectResult> Post()
        {
            var rawJson = await Request.Body.ReadAsStringAsync();

            var log = this.NewLogEvent("RefusedPost", rawJson);

            if (this.Request.Headers.HasAuthorization())
                log.Properties.Add("Authorization", this.Request.Headers.AuthorizationParameter());

            try
            {
                var dispatch = JsonConvert.DeserializeObject<IncomingRefusedModel>(rawJson);

                var contractor = this.GetContractorById(dispatch.ContractorId);

                log.Properties["queueItemId"] = await this.SendToBackendService(DigitalDispatchService.CallEventType.Rejected,
                    contractor,
                    rawJson,
                    dispatch.DispatchId);
            }
            finally
            {
                logger.Log(log);
            }

            return Accepted();
        }
    }
}
