using System;
using System.IO;
using System.Text;
using System.Threading.Tasks;
using Extric.Towbook.Integration.MotorClubs.Services;
using Extric.Towbook.Integrations.MotorClubs.Aaa.Ace;
using Extric.Towbook.WebShared;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Routing;
using Newtonsoft.Json;
using NLog;

namespace Extric.Towbook.API.Integration.MotorClubs.AaaNortheast
{
    [Route("receivers/aaa/northeast")]
    public class ClearedController : ControllerBase
    {
        private static readonly Logger logger = LogManager.GetCurrentClassLogger();

        // POST /dispatch 
        [HttpPost]
        [Route("cleared")]
        public async Task<ObjectResult> Post()
        {
            var rawJson = await Request.Body.ReadAsStringAsync();

            var log = this.NewLogEvent("ClearedPost", rawJson);

            if (this.Request.Headers.HasAuthorization())
                log.Properties.Add("Authorization", this.Request.Headers.AuthorizationParameter());

            long queueItemId = 0;
            try
            {
                var dispatch = JsonConvert.DeserializeObject<DispatchModel>(rawJson);
                var contractor = this.GetContractorById(dispatch.ContractorId);

                log.Properties["contractorId"] = dispatch.ContractorId;
                if (contractor == null)
                {
                    log.Message = "Unknown ContractorId";
                    log.Level = LogLevel.Error;
                }
                else
                {
                    log.Properties["queueItemId"] = queueItemId = await this.SendToBackendService(
                        DigitalDispatchService.CallEventType.Import,
                        contractor,
                        rawJson,
                        dispatch.DispatchId);
                }
            }
            catch (Exception e)
            {
                log.Properties["exception"] = e;
                log.Level = LogLevel.Error;

                throw;
            }
            finally
            {
                logger.Log(log);
            }
            return this.AcceptedWithId(queueItemId.ToString());
        }
    }
}
