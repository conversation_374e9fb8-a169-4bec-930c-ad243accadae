using System;
using Microsoft.AspNetCore.Mvc;

namespace Extric.Towbook.API.Integration.MotorClubs.Aaa
{
    [Route("receivers/aaa")]
    [Route("receivers/aaa/aaa")]
    public class AaaController : ControllerBase
    {
        [Route("")]
        [HttpGet]
        public object Get()
        {
            return "";
        }

        [Route("{id}")]
        [Route("{id}/get")]
        [HttpGet]
        public object Get(string id) 
        {
            return "";
        }

        [Route("")]
        [HttpPost]
        public object Post()
        {
            return "AAA Controller POST " + Guid.NewGuid().ToString("n") + " - " + DateTime.Now.ToString();
        }

        [Route("{id}")]
        [Route("{id}/put")]
        [HttpPut]
        public object Put(string id)
        {
            return "AAA Controller PUT " + Guid.NewGuid().ToString("n") + " - " + DateTime.Now.ToString();
        }

        [Route("{id}")]
        [Route("{id}/delete")]
        [HttpDelete]
        public object Delete(string id)
        {
            return "AAA Controller DELETE " + Guid.NewGuid().ToString("n") + " - " + DateTime.Now.ToString();
        }

    }
}
