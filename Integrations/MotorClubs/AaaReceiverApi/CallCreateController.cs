using Extric.Towbook.Integration.MotorClubs.Services;
using Extric.Towbook.Integrations.MotorClubs.Aaa.National;
using Newtonsoft.Json;
using NLog;
using System;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using Extric.Towbook.WebShared;
using Microsoft.AspNetCore.Mvc;

namespace Extric.Towbook.API.Integration.MotorClubs.Aaa
{
    [Route("receivers/aaa/callCreate")]
    public class CallCreateController : ControllerBase
    {
        private static readonly Logger logger = LogManager.GetCurrentClassLogger();


        [HttpPost]
        [Route("")]
        public async Task<ObjectResult> Post()
        {
            var rawJson = await Request.Body.ReadAsStringAsync();

            var log = new LogEventInfo();

            log.Level = LogLevel.Info;
            log.Message = "CallCreate";
            log.Properties["masterAccountName"] = "AaaNational";
            log.Properties["json"] = rawJson;

            if (this.Request.Headers.HasAuthorization())
                log.Properties.Add("Authorization", this.Request.Headers.AuthorizationParameter());

            try
            {
                var ccp = JsonConvert.DeserializeObject<CallCreatePayload>(rawJson);

                log.Properties["contractorId"] = ccp.Calls.First().Service.Facility.Id;

                var contractor = this.GetContractorById(ccp.Calls.First().Service.Facility.Id);
                if (contractor == null)
                    throw new Exception("Unknown ContractorId");

                log.Properties["queueItemId"] = await this.SendToBackendService(
                    DigitalDispatchService.CallEventType.Received,
                    contractor,
                    rawJson,
                    ccp.Calls.First().CallKey);

                var resp = StatusCode((int)HttpStatusCode.OK, new AaaResponseModel<ResponsePayload>()
                {
                    StatusCode = "200",
                    StatusDescription = "Received",
                    SourceSystem = new AaaSystem(),
                    Header = new AaaResponseModel<ResponsePayload>.HeaderModel()
                    {
                        Id = ccp.Id,
                        Status = "OK",
                        SubType = ccp.SubType,
                        Type = ccp.RequestEventType,
                        Version = ccp.Version,
                    },
                    ResponsePayload = new ResponsePayload()
                    {
                        CallKey = ccp.Calls.First().CallKey,
                        MemberID = ccp.Calls.First().Customer.Memberships.First().Id,
                        Pta = ccp.Calls.First().Service.Status.Pta,
                        PtaInMinutes = 0,
                    }
                });

                Response.Headers["X-Twbk-Id"] = log.Properties["queueItemId"].ToString();

                return resp;
            }
            catch(Exception y)
            {
                log.Properties["exception"] = y;
                log.Level = LogLevel.Error;

                throw;
            }
            finally
            {
                logger.Log(log);
            }
        
        }
    }


}
