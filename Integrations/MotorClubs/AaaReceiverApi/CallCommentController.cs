using Microsoft.AspNetCore.Mvc;
using Extric.Towbook.Accounts;
using Extric.Towbook.Integration.MotorClubs.Services;
using Extric.Towbook.Integrations.MotorClubs.Aaa;
using Extric.Towbook.Integrations.MotorClubs.Aaa.National;
using Newtonsoft.Json;
using NLog;
using System;
using System.Linq;
using System.Net.Http;
using System.Net;
using System.Threading.Tasks;
using Extric.Towbook.Web;
using Extric.Towbook.WebShared;
using System.IO;

namespace Extric.Towbook.API.Integration.MotorClubs.Aaa
{
    [Route("receivers/aaa/callComment")]
    public class CallCommentController : ControllerBase
    {
        private static readonly Logger logger = LogManager.GetCurrentClassLogger();

        public sealed class CallCommentPayload
        {
            public int Id { get; set; }
            public string RequestEventType { get; set; }
            public string SubType { get; set; }
            public string Version { get; set; }
            public SystemPayload SourceSystem { get; set; }
            public SystemPayload TargetSystem { get; set; }
            public long CreateDate { get; set; }
            public CallCommentCall[] Calls { get; set; }

        }

        public sealed class SystemPayload
        {
            public string SystemId { get; set; }
            public SystemOrganizationPayload SystemOrganization { get; set; }

            public sealed class SystemOrganizationPayload
            {
                public string Name { get; set; }
                public string Code { get; set; }
            }
        }

        public sealed class CallCommentCall
        {
            public string Version { get; set; }
            public Comment[] Comments { get; set; }
            public string CallKey { get; set; }
            public string CallDate { get; set; }
            public string CallId { get; set; }
        }

        public sealed class Comment
        {
            public SystemPayload System { get; set; }
            public ContactPayload Contact { get; set; }

            // "2022-02-08T19:54:29Z[UTC]
            public string CommentDate { get; set; }
            public string CommentType { get; set; }
            public string Visibility { get; set; }
            public string Text { get; set; }
            public class ContactPayload
            {
                public string UserId { get; set; }
                public string ContactType { get; set; }
            }
        }

        [HttpPost]
        [Route("")]
        public async Task<ObjectResult> Post()
        {
            var rawJson = await Request.Body.ReadAsStringAsync();

            var log = new LogEventInfo();

            log.Level = LogLevel.Info;
            log.Message = "CallComment";
            log.Properties.Add("masterAccountName", "AaaNational");
            log.Properties.Add("json", rawJson);

            if (this.Request.Headers.HasAuthorization())
                log.Properties.Add("Authorization", this.Request.Headers.AuthorizationParameter());

            try
            {
                var ccp = JsonConvert.DeserializeObject<CallCommentPayload>(rawJson);

                var ad = AaaDispatch.GetByMasterAccountDispatchId(ccp.Calls.First().CallKey, MasterAccountTypes.AaaNational);
                
                if (ad == null)
                    throw new Exception("Unknown CallKey");

                var contractor = AaaContractor.GetById(ad.AaaContractorId);
                if (contractor == null)
                    throw new Exception("Unknown ContractorId");

                log.Properties["contractorId"] = contractor.ContractorId;

                log.Properties["queueItemId"] = await this.SendToBackendService(
                    DigitalDispatchService.CallEventType.Note,
                    contractor,
                    rawJson,
                    ccp.Calls.First().CallKey);

                var resp = StatusCode((int)HttpStatusCode.OK, new AaaResponseModel<object>()
                {
                    StatusCode = "Received",
                    Header = new AaaResponseModel<object>.HeaderModel()
                    {
                        Id = ccp.Id.ToString()
                    }
                });
                Response.Headers["X-Twbk-Id"] = log.Properties["queueItemId"].ToString();

                return resp;
            }
            finally
            {
                logger.Log(log);
            }
        }
    }

    public sealed class AaaResponseModel<T>
    {
        public string StatusCode { get; set; } = "200";
        public string StatusDescription { get; set; }
        public HeaderModel Header { get; set; }

        public AaaSystem SourceSystem { get; set; }

        public sealed class HeaderModel
        {
            public string Id { get; set; }

            public string Version { get; set; }
            public string Type { get; set; }
            public string SubType { get; set; }
            public string Status { get; set; }
        }

        public T ResponsePayload { get; set; }
    }

    public sealed class ResponsePayload
    {
        public string CallKey { get; set; }
        public string Pta { get; set; }
        public string MemberID { get; set; }
        public int PtaInMinutes { get; set; }
    }

}
