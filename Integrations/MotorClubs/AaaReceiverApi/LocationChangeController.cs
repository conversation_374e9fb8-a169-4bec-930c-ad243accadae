using Extric.Towbook.Accounts;
using Extric.Towbook.Integration.MotorClubs.Services;
using Extric.Towbook.Integrations.MotorClubs.Aaa;
using Extric.Towbook.Integrations.MotorClubs.Aaa.National;
using Newtonsoft.Json;
using NLog;
using System;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using Extric.Towbook.WebShared;
using Microsoft.AspNetCore.Mvc;

namespace Extric.Towbook.API.Integration.MotorClubs.Aaa
{
    [Route("receivers/aaa/locationChange")]
    public class LocationChangeController : ControllerBase
    {
        private static readonly Logger logger = LogManager.GetCurrentClassLogger();

        [HttpPost]
        [Route("")]
        public async Task<ObjectResult> Post()
        {
            var rawJson = await Request.Body.ReadAsStringAsync();

            var log = new LogEventInfo();

            log.Level = LogLevel.Info;
            log.Message = "LocationChange";
            log.Properties.Add("masterAccountName", "AaaNational");
            log.Properties.Add("json", rawJson);

            if (this.Request.Headers.HasAuthorization())
                log.Properties.Add("Authorization", this.Request.Headers.AuthorizationParameter());

            try
            {
                var ccp = JsonConvert.DeserializeObject<LocationChangePayload>(rawJson);

                var ad = AaaDispatch.GetByMasterAccountDispatchId(ccp.Calls.First().CallKey, MasterAccountTypes.AaaNational);

                if (ad == null)
                    throw new Exception("Unknown CallKey");

                var contractor = AaaContractor.GetById(ad.AaaContractorId);
                if (contractor == null)
                    throw new Exception("Unknown ContractorId");

                log.Properties["contractorId"] = contractor.ContractorId;

                log.Properties["queueItemId"] = await this.SendToBackendService(
                    DigitalDispatchService.CallEventType.Update,
                    contractor,
                    rawJson,
                    ccp.Calls.First().CallKey);

                var resp = StatusCode((int)HttpStatusCode.OK, new AaaResponseModel<ResponsePayload>()
                {
                    StatusCode = "200",
                    StatusDescription = "Received",
                    SourceSystem = new AaaSystem(),
                    Header = new AaaResponseModel<ResponsePayload>.HeaderModel()
                    {
                        Id = ccp.Id,
                        Status = "OK",
                        SubType = ccp.SubType,
                        Type = ccp.RequestEventType,
                        Version = ccp.Version
                    },
                    ResponsePayload = new ResponsePayload()
                    {
                        CallKey = ccp.Calls.First().CallKey,
                        MemberID = "",
                        Pta = "",
                        PtaInMinutes = 0,
                    }
                });

                Response.Headers["X-Twbk-Id"] = log.Properties["queueItemId"].ToString();

                return resp;
            }
            finally
            {
                logger.Log(log);
            }

        }
    }
}
