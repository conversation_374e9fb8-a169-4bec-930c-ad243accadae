<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <RootNamespace>Extric.Towbook.Integrations.MotorClubs.Gerber</RootNamespace>
    <AssemblyName>Extric.Towbook.Integrations.MotorClubs.Gerber</AssemblyName>
	<OutputType>Library</OutputType>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="RestSharp" Version="111.4.1" />
  </ItemGroup>
  
  <ItemGroup>
    <ProjectReference Include="..\..\..\Extric.Towbook\Extric.Towbook.csproj" />
  </ItemGroup>
  
</Project>