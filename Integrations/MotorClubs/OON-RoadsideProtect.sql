CREATE OR ALTER PROCEDURE [MCDispatch].[OONRoadsideProtectProvidersGetByGeolocationQA] (                                          
 @SLAT AS FLOAT,                                          
 @SLON AS FLOAT,              
 @CountryId int = 1              
) AS                               
set transaction isolation level read uncommitted                                 
--declare @slat float= 42.824803                        
--declare @slon float = -82.485464
--declare @CountryId int = 1              
drop table if exists  #RoadsideProtectRadiusCalls                               
      
select top 5  r.Latitude, r.Longitude, r.Distance, c.Name, c.Address, C.Phone, C.Fax, C.City, C.State, c.Zip  , c.CompanyId                                    
into #RoadsideProtectRadiusCalls                                    
      From(                                          
SELECT TOP 10 UserId, Latitude, Longitude, SQRT(                                          
    POWER(69.1 * (Latitude - @SLAT), 2) +                                          
    POWER(69.1 * (@SLON - Longitude) * COS(Latitude / 57.3), 2)) AS Distance                                          
FROM UserLocationsCurrent with (nolock) where timestamp > '5/1/2023'/* dateadd(hour,-1,getdate()) */ ORDER BY 4 ) r                                           
inner join users u with (nolock) on u.userid=r.UserId                              
inner join companies c with (nolock) on c.companyid=u.CompanyId and c.Country=@CountryId     and              
c.CompanyId in (10000 ,7124,205423,205424,205426)                              
--and c.companyid not in (select companyid from MCDispatch.RoadsideProtectProviders with (nolock) where IsEnabled=1 and )                               
where r.distance < 55                                     
      
select top 5 y.*, a.AccountId from(                              
SELECT top 10  c.Latitude, c.Longitude, SQRT(                                          
    POWER(69.1 * (c.Latitude - @SLAT), 2) +                                          
    POWER(69.1 * (@SLON - c.Longitude) * COS(c.Latitude / 57.3), 2)) AS Distance, c.Name, c.Address, C.Phone, C.Fax, C.City, C.State, c.Zip  , c.CompanyId                                        
FROM companies c                                           
where c.companyid in (10000 ,7124,205423,205424,205426) and c.Country=@CountryId and  SQRT(                                          
    POWER(69.1 * (c.Latitude - @SLAT), 2) +                                          
    POWER(69.1 * (@SLON - c.Longitude) * COS(c.Latitude / 57.3), 2)) < 55                                          
 and c.companyid not in (Select companyid from #RoadsideProtectRadiusCalls)                                    
 --and c.companyid not in (select companyid from MCDispatch.RoadsideProtectProviders with (nolock))                              
union all                                    
select * From #RoadsideProtectRadiusCalls   where companyid is not null) y                               
left outer join accounts a on a.CompanyId=y.CompanyId and a.MasterAccountId=42   and a.deleted=0

GO

GRANT EXEC ON [MCDispatch].[OONRoadsideProtectProvidersGetByGeolocationQA] TO public
GO


CREATE OR ALTER PROCEDURE [MCDispatch].[OONRoadsideProtectProvidersGetByGeolocation] (                                                
 @SLAT AS FLOAT,                                                
 @SLON AS FLOAT,                    
 @CountryId int = 1                    
) AS                                     
set transaction isolation level read uncommitted                                       
--declare @slat float= 42.824803                              
--declare @slon float = -82.485464      
--declare @CountryId int = 1                    
drop table if exists  #RoadsideProtectRadiusCalls                                     
            
select top 5  r.Latitude, r.Longitude, r.Distance, c.Name, c.Address, C.Phone, C.Fax, C.City, C.State, c.Zip  , c.CompanyId                                          
into #RoadsideProtectRadiusCalls                                          
      From(                                                
SELECT TOP 10 UserId, Latitude, Longitude, SQRT(                                                
    POWER(69.1 * (Latitude - @SLAT), 2) +                                                
    POWER(69.1 * (@SLON - Longitude) * COS(Latitude / 57.3), 2)) AS Distance                                                
FROM UserLocationsCurrent with (nolock) where timestamp >  dateadd(hour,-1,getdate()) ORDER BY 4 ) r                                                 
inner join users u with (nolock) on u.userid=r.UserId                                    
inner join companies c with (nolock) on c.companyid=u.CompanyId and c.Country=@CountryId
 and c.companyid not in (select companyid from MCDispatch.RoadsideProtectContractors with (nolock) where masteraccountid=24 )                                     
 and c.companyid not in (select CompanyId from Integration.ProviderCompanyKeyValues  with (nolock) where ProviderCompanyKeyId=572 and value='1')              
where r.distance < 55                                           
            
select top 5 y.*, a.AccountId from(                                    
SELECT top 10  c.Latitude, c.Longitude, SQRT(                                                
    POWER(69.1 * (c.Latitude - @SLAT), 2) +                                                
    POWER(69.1 * (@SLON - c.Longitude) * COS(c.Latitude / 57.3), 2)) AS Distance, c.Name, c.Address, C.Phone, C.Fax, C.City, C.State, c.Zip  , c.CompanyId                                              
FROM companies c                                                 
where  c.Country=@CountryId and  SQRT(                                                
    POWER(69.1 * (c.Latitude - @SLAT), 2) +                                                
    POWER(69.1 * (@SLON - c.Longitude) * COS(c.Latitude / 57.3), 2)) < 55                                                
 and c.companyid not in (Select companyid from #RoadsideProtectRadiusCalls)                                          
 and c.companyid not in (select companyid from MCDispatch.RoadsideProtectContractors with (nolock) where masteraccountid=24 )                                    
  and c.companyid not in (select CompanyId from Integration.ProviderCompanyKeyValues  with (nolock) where ProviderCompanyKeyId=572 and value='1')              
union all                                           
select * From #RoadsideProtectRadiusCalls   where companyid is not null) y                                     
left outer join accounts a on a.CompanyId=y.CompanyId and a.MasterAccountId=42   and a.deleted=0      
GO

GRANT EXEC ON [MCDispatch].[OONRoadsideProtectProvidersGetByGeolocation] TO public
GO