using System;

namespace Extric.Towbook.Integrations.MotorClubs.Honk
{
    [Serializable]
    public class HonkException : Exception
    {
        public string Json { get; private set; }
        public int HttpStatusCode { get; private set; }

        public string Url { get; private set; }
        public string Request { get; private set; }

        public HonkException() { }
        public HonkException(string message) : base(message) { }
        public HonkException(string message, int httpStatusCode, string json) : base(message) {
            Json = json;
            HttpStatusCode = httpStatusCode;
        }

        public HonkException(string message, int httpStatusCode, string json, string url, string request) : base(message)
        {
            Json = json;
            HttpStatusCode = httpStatusCode;
            Url = url;
            Request = request;
        }


        public HonkException(string message, Exception inner) : base(message, inner) { }
        protected HonkException(
          System.Runtime.Serialization.SerializationInfo info,
          System.Runtime.Serialization.StreamingContext context)
            : base(info, context) { }
    }

    public class HonkInvalidAccessTokenException : HonkException
    {
        public HonkInvalidAccessTokenException(string json) : base("A valid OAuth token was not provided.", 401, json) { }
    }

    public class HonkServiceUnavailable : HonkException
    {
        public HonkServiceUnavailable(string json) : base("The Service is temporarily unavailable", 503, json) { }
    }

    public class HonkUnknownException : HonkException
    {
        public HonkUnknownException(string json) : base("An error occurred on the server:" + json, 500, json) { }
    }
}
