using Newtonsoft.Json;

namespace Extric.Towbook.Integration.MotorClubs.StackThree.Model
{

    /// <summary>
    /// Represents a Stack Three Job
    /// </summary>
    public class DispatchModel
    {
        public ServiceRequest ServiceRequest { get; set; }
    }

    public class ServiceRequest
    {
        public General General { get; set; }
        public Customer Customer { get; set; }
        public Vehicle Vehicle { get; set; }
        public Pickuplocation PickUpLocation { get; set; }
        public DropOffLocation DropOffLocation { get; set; }

        public ServiceRequestOon Oon { get; set; }
    }

    public class ServiceRequestOon
    {
        public decimal OfferPrice { get; set; }
        public decimal CustomerPay { get; set; }
        public decimal GoaPrice { get; set; }
        public decimal CancelPrice { get; set; }
    }

    public class General
    {
        public string ClientId { get; set; }
        public string ProgramId { get; set; }
        public string RequestType { get; set; }
        public string ServiceType { get; set; }
        public string SpecialInstructions { get; set; }
        public int ExpirationTime { get; set; }

        public string ContractorId { get; set; }

        public int ContractorIdInt
        {
            get
            {
                if (int.TryParse(this.ContractorId, out int result))
                    return result;

                return 0;
            }
        }

        public int DispatchId { get; set; }
        public string TowingType { get; set; }

        public string ServiceOrTowingType
        {
            get
            {
                return ServiceType ?? TowingType;
            }
        }

        public string VendorEstimatedFee { get; set; }
    }

    public class Customer
    {
        public string ClaimNo { get; set; }
        public string PolicyNo { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string BackupPhoneNo { get; set; }
        public string BenefitLimitType { get; set; }
        public string BenefitLimitValue { get; set; }

    }

    public class Vehicle
    {
        [JsonProperty("class")]
        public string VehicleClass{ get; set; }
        public int Year { get; set; }
        public string Make { get; set; }
        public string Model { get; set; }
        public string Color { get; set; }
        public string Vin { get; set; }
        public string Drivetrain { get; set; }
        public string PlateNo { get; set; }
        public string CurrentMileage { get; set; }

        public override string ToString()
        {
            return Year + " " + Make + " " + Model + (!string.IsNullOrWhiteSpace(Color) ?  " (" + Color + ")" : "");
        }
    }

    public class Pickuplocation
    {
        public string LocationArea { get; set; }
        public string DetailedLandmark { get; set; }
        public string StreetAddress { get; set; }
        public string City { get; set; }
        public string State { get; set; }
        public string PostalCode { get; set; }
        public string Country { get; set; }
        public decimal Latitude { get; set; }
        public decimal Longitude { get; set; }
        public string Notes { get; set; }

        public override string ToString()
        {
            return StreetAddress + ", " + City + " " + State + " " + PostalCode;
        }
    }

    public class DropOffLocation
    {
        public string LocationArea { get; set; }
        public string DetailedLandmark { get; set; }
        public string StreetAddress { get; set; }
        public string City { get; set; }
        public string State { get; set; }
        public string PostalCode { get; set; }
        public string Country { get; set; }
        public decimal Latitude { get; set; }
        public decimal Longitude { get; set; }
        public string Notes { get; set; }


        public override string ToString()
        {
            return StreetAddress + ", " + City + " " + State + " " + PostalCode;
        }
    }

}
