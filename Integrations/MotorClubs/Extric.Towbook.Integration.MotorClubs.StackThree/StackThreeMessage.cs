using Extric.Towbook.Utility;
using System;
using System.Linq;

namespace Extric.Towbook.Integrations.MotorClubs.StackThree
{
    /// <summary>
    /// StackThree Notify request model.. This class was implemented to adhere to the documentation provided
    /// by StackThree for their Digital Dispatching API.
    /// </summary>
    [Table("MCDispatch.StackThreeMessages")]
    public class StackThreeMessage
    {
        [Key("StackThreeMessageId")]
        public long StackThreeMessageId { get; set; }
	    public int StackThreeContractorId { get; set; }
        public int MessageType { get; set; }
        public string DispatchId { get;set; }
        public string JsonData { get; set; }

        public DateTime? CreateDate { get; set; }

        public StackThreeMessage() { }

        public StackThreeMessage Save()
        {
            CreateDate = DateTime.Now;
            if (this.StackThreeMessageId == 0)
                this.StackThreeMessageId = SqlMapper.Insert<StackThreeMessage>(this);
            else
                SqlMapper.Update<StackThreeMessage>(this);

            return this;
        }

        public static StackThreeMessage GetById(int id)
        {
            return SqlMapper.Query<StackThreeMessage>(
                "SELECT * FROM MCDispatch.StackThreeMessages WHERE StackThreeMessageId=@Id", 
                new { @Id = id }).FirstOrDefault();
        }
    }
}
