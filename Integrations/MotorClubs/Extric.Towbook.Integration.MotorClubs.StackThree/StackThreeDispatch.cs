using Extric.Towbook.Dispatch;
using Extric.Towbook.Utility;
using System;
using System.Linq;

namespace Extric.Towbook.Integrations.MotorClubs.StackThree
{
    [Table("MCDispatch.StackThreeDispatches")]
    public class StackThreeDispatch
    {
        [Key("StackThreeDispatchId")]
        public long StackThreeDispatchId { get; set; }
        public int StackThreeContractorId { get; set; }
        public string DispatchId { get; set; }
        public string DispatchJson { get; set; }
        public int CallRequestId { get; set; }
        public int? Eta { get; set; }       

        public StackThreeDispatch() { }

        public static StackThreeDispatch GetByDispatchId(string id, int StackThreeContractorId)
        {
            return SqlMapper.Query<StackThreeDispatch>(
                "SELECT TOP 1 * FROM MCDispatch.StackThreeDispatches WITH (nolock) " +
                "WHERE DispatchId = @Id AND StackThreeContractorId=@StackThreeContractorId ORDER BY 1 DESC",
                new
                {
                    Id = id,
                    StackThreeContractorId = StackThreeContractorId
                }).FirstOrDefault();
        }

        public static StackThreeDispatch GetByCallRequestId(int callRequestId)
        {
            return SqlMapper.Query<StackThreeDispatch>(
                "SELECT * FROM MCDispatch.StackThreeDispatches WITH (nolock) WHERE CallRequestId = @CallRequestId", 
                new { CallRequestId = callRequestId }).FirstOrDefault();
        }

        public void Save()
        {
            if (this.StackThreeDispatchId == 0)
                this.StackThreeDispatchId = SqlMapper.Insert<StackThreeDispatch>(this);
            else
                SqlMapper.Update<StackThreeDispatch>(this);
        }

        public static StackThreeDispatch GetByDispatchIdCompleted(string dispatchId)
        {
            return SqlMapper.Query<StackThreeDispatch>(
                "SELECT TOP 1 * FROM MCDispatch.StackThreeDispatches STD WITH (nolock) " +
                "INNER JOIN DispatchEntryRequests DER WITH (nolock) on DER.CallRequestId=STD.CallRequestId " +
                "INNER JOIN DispatchEntries DE WITH (nolock) on DE.DispatchEntryId=DER.DispatchEntryId and DE.Status=5 and DE.Deleted=0 " +
                "WHERE STD.DispatchId = @Id ORDER BY 1 DESC",
                new
                {
                    Id = dispatchId
                }).FirstOrDefault();
        }
    }
}
