IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='IsscMessages' AND TABLE_SCHEMA='MCDispatch')
	CREATE TABLE MCDispatch.IsscMessages (
		IsscMessageId int IDENTITY(1,1) NOT NULL,
		IsscConfigId int,
		MessageType int NOT NULL,
		Direction int NOT NULL,
		[Status] int NOT NULL,
		[Content] varchar(MAX) NOT NULL,
		CreateDate datetime NOT NULL,

		CONSTRAINT PK_IsscMessages PRIMARY KEY CLUSTERED (IsscMessageId ASC)
	)
GO


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='IsscConfig' AND TABLE_SCHEMA='MCDispatch')
	--
	-- Holds configuration for ISSC.
	--
	CREATE TABLE MCDispatch.IsscConfig (
		IsscConfigId INT DEFAULT 1,
		Guid uniqueidentifier ROWGUIDCOL,
		UrlBase VARCHAR(200),
		ApiKey VARCHAR(100),
		AutoReconnectOnDisconnectEvent BIT DEFAULT(1),
		Username varchar(16),
		Password varchar(32), 
		[RowVersion] RowVersion NOT NULL
				
		CONSTRAINT PK_IsscConfig PRIMARY KEY CLUSTERED (IsscConfigId ASC)
	)
GO


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='IsscConnection' AND TABLE_SCHEMA='MCDispatch')	
	CREATE TABLE MCDispatch.IsscConnection (
		IsscConnectionId	INT DEFAULT 1 CHECK (IsscConnectionId=1), -- limit table to 1 row.
		IsConnected  BIT DEFAULT(0),
		LastConnectDate DATETIME,
		LastRemoteDisconnectDate DATETIME,
		LastIncomingHeartbeatSequence INT,
		LastIncomingHeartbeatDate DATETIME,
		LastOutgoingHeartbeatDate DATETIME,
		[RowVersion] RowVersion NOT NULL

		CONSTRAINT PK_IsscConnection PRIMARY KEY CLUSTERED (IsscConnectionId ASC)
	)
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='IsscProviders' AND TABLE_SCHEMA='MCDispatch')	
	CREATE TABLE MCDispatch.IsscProviders (
		IsscProviderId int IDENTITY(1,1) NOT NULL,
		CompanyId int NOT NULL,
		ClientId varchar(255) NOT NULL,
		ContractorId varchar(255),
		TaxId varchar(255) NOT NULL,
		LocationId varchar(100) NULL,
		Token varchar(255) NOT NULL,
		IsLoggedIn bit NOT NULL DEFAULT(0),
		LastLoginDate datetime NULL
				
		CONSTRAINT PK_IsscProviders PRIMARY KEY CLUSTERED (IsscProviderId ASC),
		CONSTRAINT FK_IsscProviders_Companies FOREIGN KEY(CompanyId) REFERENCES dbo.Companies (CompanyId)
	)
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='IsscDispatches' AND TABLE_SCHEMA='MCDispatch')	
	CREATE TABLE MCDispatch.IsscDispatches (
		IsscDispatchId int IDENTITY(1,1) NOT NULL,
		ClientId varchar(255) NOT NULL,
		ContractorId varchar(255) NOT NULL,
		LocationId varchar(100) NULL,
		DispatchId int NOT NULL,
		JobId varchar(255) NOT NULL,
		CallJson varchar(max) NOT NULL,
		CallRequestId int NOT NULL,
		ETA int NULL,
		AuthorizationNumber varchar(50) NULL
		
		CONSTRAINT PK_IsscDispatches PRIMARY KEY CLUSTERED (IsscDispatchId ASC),
		CONSTRAINT FK_IsscDispatches_DispatchEntryRequests FOREIGN KEY(CallRequestId) REFERENCES dbo.DispatchEntryRequests (CallRequestId)
	)
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='IsscProviders' AND TABLE_SCHEMA='MCDispatch' AND COLUMN_NAME = 'AccountId')
	ALTER TABLE MCDispatch.IsscProviders
		ADD AccountId int
		CONSTRAINT FK_IsscProviders_Accounts FOREIGN KEY(AccountId) REFERENCES dbo.Accounts(AccountId)
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='IsscProviders' AND TABLE_SCHEMA='MCDispatch' AND COLUMN_NAME = 'LoginStatus')
	ALTER TABLE MCDispatch.IsscProviders
		ADD LoginStatus int not null default(0)
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='IsscConnection' AND TABLE_SCHEMA='MCDispatch' AND COLUMN_NAME = 'Status')
	ALTER TABLE MCDispatch.IsscConnection
		ADD [Status] int not null default(0)
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='IsscProviders' AND TABLE_SCHEMA='MCDispatch' AND COLUMN_NAME = 'IsValidated')
	ALTER TABLE MCDispatch.IsscProviders
		ADD IsValidated bit default(0)
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='IsscProviders' AND TABLE_SCHEMA='MCDispatch' AND COLUMN_NAME = 'IsDeleted')
	ALTER TABLE MCDispatch.IsscProviders
		ADD IsDeleted int not null default(0)
GO