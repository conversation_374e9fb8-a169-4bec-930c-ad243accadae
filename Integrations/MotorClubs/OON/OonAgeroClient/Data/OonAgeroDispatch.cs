using Extric.Towbook.Utility;
using System;
using System.Collections.Generic;
using System.Linq;

namespace OonAgeroClient.Data
{
    [Table("MCDispatch.OonAgeroDispatches")]
    public class OonAgeroDispatch
    {
        [Key("OonAgeroDispatchId")]
        public long Id { get; set; }
        public string DispatchId { get; set; }
        public int CompanyId { get; set; }
        public int CallRequestId { get; set; }
        public string DispatchJson { get; set; }

        public int Eta { get; set; }
        [Write(false)]
        public DateTime CreateDate { get; set; }

        public decimal OfferPrice { get; set; }
        public decimal ClosestDriverDistanceToDisablement { get; set; }         // hard
        public decimal ClosestDriverDistanceToDestination { get; set; }         // hard
        public decimal CompanyDistanceToDisablement { get; set; }               // easy
        public decimal CompanyDistanceToDestination { get; set; }               // easy
        public decimal DisablementToDestinationDistance { get; set; }           // easy

        public void Save()
        {
            if (Id == 0)
                this.Id = SqlMapper.Insert(this);
            else
                SqlMapper.Update(this);
        }

        public static OonAgeroDispatch GetByCallRequestId(int id)
        {
            return SqlMapper.Query<OonAgeroDispatch>("select * from mcdispatch.OonAgeroDispatches where CallRequestId=@Id", new { Id = id }).FirstOrDefault();
        }

        public static IEnumerable<OonAgeroDispatch> GetByDispatchId(string dispatchId)
        {
            return SqlMapper.Query<OonAgeroDispatch>("select * from mcdispatch.OonAgeroDispatches where DispatchId=@Id", 
                new { Id = dispatchId }).ToCollection();
        }
    }
}
