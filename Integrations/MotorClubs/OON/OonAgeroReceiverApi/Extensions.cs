using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Extric.Towbook.Web;
using Extric.Towbook.WebShared;
using Microsoft.AspNetCore.Mvc;

namespace Extric.Towbook.API.Integration.MotorClubs.OON.Agero
{
    public static class OonExtensions
    {
        //TODO AGG remove use of ApiController
        public static string GetEnvironment(this ControllerBase t)
        {
            if (t.User?.Identity?.Name != null)
                return t.User.Identity.Name;

            var req = HttpContext.Current;

            if (req != null)
            {
                return (string)req.Items["oon:_environment"];
            }

            return "DEV";
        }

        public static string GetRequestingIp(this ControllerBase t) => WebGlobal.GetRequestingIp();
    }
}
