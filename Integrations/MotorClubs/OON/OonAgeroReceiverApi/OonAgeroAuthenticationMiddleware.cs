using System.Threading.Tasks;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;

namespace Extric.Towbook.API.Integration.MotorClubs.OON.Agero.HttpModules
{
    public class OonAgeroAuthenticationMiddleware
    {

        private readonly RequestDelegate _next;

        public OonAgeroAuthenticationMiddleware(RequestDelegate next)
        {
            _next = next;
        }

        public async Task InvokeAsync(HttpContext context, IWebHostEnvironment environment)
        {
            this.BeginInvoke(context);
            await this._next.Invoke(context);
            this.EndInvoke(context);
        }

        public void BeginInvoke(HttpContext context)
        {

            //var application = (HttpApplication)sender;
            //var context = application.Context;

            bool invalidToken = true;

            //string path = context.Request.Path.Substring(context.Request.ApplicationPath.Length).ToLowerInvariant().TrimStart('/');
            string path = context.Request.Path.Value.ToLowerInvariant().TrimStart('/');

            if (!path.Contains("receivers/oon/agero/"))
                return;

            if (context.Request.Headers.ContainsKey("Authorization"))
            {
                string authHeader = context.Request.Headers["Authorization"];
                string key = string.Empty;
                if (authHeader.Contains(" ") ||
                    authHeader.Contains(":"))
                {
                    authHeader = authHeader.Replace(":", " ");

                    var splitHeader = authHeader.Split(' ');

                    if (splitHeader[0].ToLowerInvariant() == "bearer")
                    {
                        key = splitHeader[1];
                    }
                }

                context.Items["oon:_key"] = key;

                if (key == "dZIqXquuhGudCY7QZuJIaCxBny2K8rrl" || // dev
                    key == "ce004b166eb87852Bd9c7c2aec34a33F") // prod
                    invalidToken = false;

                if (key == "dZIqXquuhGudCY7QZuJIaCxBny2K8rrl")
                    context.Items["oon:_environment"] = "DEV";
                else if (key == "ce004b166eb87852Bd9c7c2aec34a33F")
                    context.Items["oon:_environment"] = "PROD";

                context.User = new System.Security.Principal.GenericPrincipal(
                    new System.Security.Principal.GenericIdentity(context.Items["oon:_environment"].ToString()), new[] { "api" });


            }

            if (invalidToken)
            {
                WriteInvalidToken(context, false);
                return;
            }
        }

        public void EndInvoke(HttpContext context)
        {
            //HttpApplication application = (HttpApplication)sender;

            //if (application != null)
            //{
            //    var context = application.Context;

            if (context != null)
            {
                //string path = context.Request.Path.Substring(
                //    context.Request.ApplicationPath.Length).ToLowerInvariant().TrimStart('/');
                // TOTO: substring after ApplicationPath
                string path = context.Request.Path.Value.ToLowerInvariant().TrimStart('/');
                if (path.Contains("receivers/oon/agero/") &&
                        context.Response.StatusCode == 401)
                {
                    WriteInvalidToken(context, false);
                }
            }
            //}
        }

        public static void WriteInvalidToken(HttpContext context, bool missing = false)
        {
            context.Response.Clear();
            context.Response.Headers.Clear();

            context.Response.ContentType = "text/json";
            context.Response.WriteAsync(JsonConvert.SerializeObject(
                new
                {
                    error = missing ? "Bearer Token is required." : "Invalid Token. Please ensure your token is valid."
                }));
            context.Response.StatusCode = 401;
            return;
        }
    }
    public static class OonAgeroAuthenticationMiddlewareExtensions
    {
        public static IApplicationBuilder UseOonAgeroApiAuthentication(
            this IApplicationBuilder builder)
        {
            return builder.UseMiddleware<OonAgeroAuthenticationMiddleware>();
        }
    }

}