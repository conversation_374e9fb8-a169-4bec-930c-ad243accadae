using Extric.Towbook.Utility;
using System;
using System.Linq;

namespace Extric.Towbook.Integrations.MotorClubs.Sykes
{
    /// <summary>
    /// Sykes Notify request model.. This class was implemented to adhere to the documentation provided
    /// by Sykes for their Digital Dispatching API.
    /// </summary>
    [Table("MCDispatch.SykesMessages")]
    public class SykesMessage
    {
        [Key("SykesMessageId")]
        public long SykesMessageId { get; set; }
        public long TypeId { get; set; }
        public string ContractorId { get; set; }
        public string DispatchId { get; set; }
        public string JsonData { get; set; }
        public DateTime CreateDate { get; set; }

        public SykesMessage()
        {
        }

        public void Save()
        {
            CreateDate = DateTime.Now;

            if (this.SykesMessageId == 0)
                this.SykesMessageId = SqlMapper.Insert<SykesMessage>(this);
            else
                SqlMapper.Update<SykesMessage>(this);
        }

        public static SykesMessage GetById(int id)
        {
            return SqlMapper.Query<SykesMessage>(
                "SELECT * FROM MCDispatch.SykesMessages WHERE SykesMessageId=@Id",
                new { @Id = id }).FirstOrDefault();
        }

        public static SykesMessage GetByContractorIdAndDispatchId(string contractorId, string dispatchId)
        {
            return SqlMapper.Query<SykesMessage>(
                "SELECT * FROM MCDispatch.SykesMessages WHERE ContractorId=@ContractorId AND DispatchId=@DispatchId",
                new { @ContractorId = contractorId, @DispatchId = dispatchId }).FirstOrDefault();
        }
    }
}
