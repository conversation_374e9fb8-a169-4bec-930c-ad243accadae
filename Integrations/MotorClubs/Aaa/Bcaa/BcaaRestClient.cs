using System;
using Extric.Towbook.Accounts;
using Extric.Towbook.Configuration;
using Extric.Towbook.Integrations.MotorClubs.Aaa.National;
using Extric.Towbook.Utility;
using Newtonsoft.Json;
using NLog;
using RestSharp;
using RestSharp.Authenticators.OAuth2;

namespace Extric.Towbook.Integrations.MotorClubs.Aaa.Bcaa
{
    public sealed class BcaaRestClient
    {
        private static readonly Logger logger = LogManager.GetCurrentClassLogger();
        private RestClient _restClient;

        public string UrlBase = "";

        public string ClientId { get; private set; }
        private string ClientSecret { get; set; }
        public string Scope { get; private set; }
        public string TokenBaseUrl { get; private set; }
        public string EnvironmentName { get; set; }
        public string CertificateThumbprint { get; set; }
        public string Ip { get; set; }
        public int CompanyId { get; private set; }
        public long QueueItemId { get; private set; }

        public int CallId { get; private set; }
        public int CallRequestId { get; private set; }
        public string DispatchId { get; private set; }
        public AaaContractor Contractor { get; private set; }

        public string ApiClientId { get; private set; }
        public string ApiClientSecret { get; private set; }
        public BcaaRestClient(
            string environment,
            string ip,
            int companyId,
            long queueItemId,
            int callRequestId,
            int callId,
            string dispatchId,
            AaaContractor contractor)
        {
            EnvironmentName = environment;
            CompanyId = companyId;
            QueueItemId = queueItemId;
            CallRequestId = callRequestId;
            CallId = callId;
            DispatchId = dispatchId;
            Contractor = contractor;

            if (environment == "TEST")
            {
                TokenBaseUrl = Core.GetAppSetting("MotorClubs:Bcaa:Test:TokenUrl");
                ClientId = Core.GetAppSetting("MotorClubs:Bcaa:Test:ClientId");
                ClientSecret = Core.GetAppSetting("MotorClubs:Bcaa:Test:ClientSecret");
                UrlBase = Core.GetAppSetting("MotorClubs:Bcaa:Test:UrlBase");
                ApiClientId = Core.GetAppSetting("MotorClubs:Bcaa:Test:ApiClientId");
                ApiClientSecret = Core.GetAppSetting("MotorClubs:Bcaa:Test:ApiClientSecret");
                Scope = ".default";
            }
            else if (environment == "DEV")
            {
                UrlBase = "";
                ClientId = "";
                ClientSecret = "";
            }
            else if (environment == "PROD")
            {
                TokenBaseUrl = Core.GetAppSetting("MotorClubs:Bcaa:Production:TokenUrl");
                ClientId = Core.GetAppSetting("MotorClubs:Bcaa:Production:ClientId");
                ClientSecret = Core.GetAppSetting("MotorClubs:Bcaa:Production:ClientSecret");
                UrlBase = Core.GetAppSetting("MotorClubs:Bcaa:Production:UrlBase");
                ApiClientId = Core.GetAppSetting("MotorClubs:Bcaa:Production:ApiClientId");
                ApiClientSecret = Core.GetAppSetting("MotorClubs:Bcaa:Production:ApiClientSecret");
                Scope = ".default";
            }

            Ip = ip;
        }

        public LogEventInfo NewLogEvent(string message, string json = null)
        {
            var log = new LogEventInfo();

            log.Level = LogLevel.Info;
            log.Message = message;

            if (json != null)
                log.Properties["json"] = json;

            log.Properties["commitId"] = Core.GetCommitId();
            log.Properties["masterAccountName"] = MasterAccountTypes.GetName(MasterAccountTypes.Bcaa);
            log.Properties["masterAccountId"] = MasterAccountTypes.Bcaa;
            log.Properties["environment"] = EnvironmentName;
            log.Properties["requestingIp"] = Ip;

            log.Properties["commitId"] = Core.GetCommitId();

            if (CompanyId > 0)
                log.Properties["companyId"] = CompanyId;

            if (QueueItemId > 0)
                log.Properties["queueItemId"] = QueueItemId;

            if (CallId > 0)
                log.Properties["callId"] = CallId;

            if (CallRequestId > 0)
                log.Properties["callRequestId"] = CallRequestId;

            if (!string.IsNullOrWhiteSpace(DispatchId))
                log.Properties["dispatchId"] = DispatchId;

            if (Contractor != null)
                log.Properties["contractorId"] = Contractor.ContractorId;

            return log;
        }


        public class DispatchAcceptModel
        {
            public string Type { get; set; } = "accept";
            public string DispatchId { get; set; }
            public string ContractorId { get; set; }
            public string LocationId { get; set; }

            public int Eta { get; set; }
            public int? ReasonId { get; set; }
        }

        public class DispatchRefuseModel
        {
            public string Type { get; set; } = "refuse";
            public string DispatchId { get; set; }
            public string ContractorId { get; set; }
            public string LocationId { get; set; }
            public int ReasonId { get; set; }
            public string ReasonName { get; set; }
        }

        public class DispatchCancelModel
        {
            public string Type { get; set; } = "cancel";
            public string DispatchId { get; set; }
            public string ContractorId { get; set; }
            public string LocationId { get; set; }

            [Obsolete("Not used by AAA. Can't remove or it breaks their model. ResolutionCode should be used instead.")]
            public int ReasonId { get; set; }
            public string ResolutionCode { get; set; }
            public string Comments { get; set; }
        }



        public class DispatchStatusUpdateModel
        {
            public string Type { get; set; } = "statusUpdate";
            public string DispatchId { get; set; }
            public string ContractorId { get; set; }
            public string LocationId { get; set; }
            public string Status { get; set; }
            public DriverModel Driver { get; set; }
        }

        public class DispatchPhotoModel
        {
            public string Type { get; set; } = "photo";
            public string DispatchId { get; set; }
            public string ContractorId { get; set; }
            public string LocationId { get; set; }
            public decimal Latitude { get; set; }
            public decimal Longitude { get; set; }
            public DateTime Timestamp { get; set; }
            public string Url { get; set; }
        }

        public class DispatchGoaModel
        {
            public string Type { get; set; } = "goa";
            public string DispatchId { get; set; }
            public string ContractorId { get; set; }
            public string LocationId { get; set; }
            public string ResolutionCode { get; set; }
            public string Comments { get; set; }
        }

        public class DriverModel
        {
            public string Id { get; set; }
            public string FirstName { get; set; }
            public string LastName { get; set; }
            public decimal Latitude { get; set; }
            public decimal Longitude { get; set; }
        }

        public class DispatchCompleteModel
        {
            public string Type { get; set; } = "complete";
            public string DispatchId { get; set; }
            public string ContractorId { get; set; }
            public string LocationId { get; set; }
            public DriverModel Driver { get; set; }
            public string ResolutionCode { get; set; }
            public InvoiceItemModel[] InvoiceItems { get; set; }

            public DateTime? DispatchedTime { get; set; }
            public DateTime? EnrouteTime { get; set; }
            public DateTime? OnSceneTime { get; set; }
            public DateTime? TowingTime { get; set; }
            public DateTime? DestinationTime { get; set; }
            public DateTime? CompletionTime { get; set; }

            public int? Odometer { get; set; }
            public string Vin { get; set; }

            public class InvoiceItemModel
            {
                public string Code { get; set; }
                public decimal Quantity { get; set; }
                public decimal Price { get; set; }
            }
        }

        public void Accept(DispatchAcceptModel payload)
        {
            var log = NewLogEvent("Accept", payload.ToJson());

            try
            {
                if (payload.Type != "accept")
                    throw new ArgumentException("Type must be: accept");

                if (string.IsNullOrWhiteSpace(payload.DispatchId))
                    throw new ArgumentException("DispatchId is null or empty - it must be specified.");

                if (string.IsNullOrWhiteSpace(payload.ContractorId))
                    throw new ArgumentException("ContractorId is null or empty - it must be specified.");

                var resp = Send("/accept", payload.ToJson(), Method.Post);

                log.Properties["contractorId"] = payload.ContractorId;
                log.Properties["dispatchId"] = payload.DispatchId;
                log.Properties["response"] = resp.Content;
                log.Properties["status"] = resp.StatusCode;

                if (!resp.IsSuccessful)
                {
                    log.Level = LogLevel.Error;

                    throw new Exception("Error invoking accept endpoint:" + resp.StatusCode + "/" + resp.Content);
                }
            }
            finally
            {
                logger.Log(log);
            }
        }


        public void Refuse(DispatchRefuseModel payload)
        {
            var log = NewLogEvent("Refuse", payload.ToJson());

            try
            {
                if (payload.Type != "refuse")
                    throw new ArgumentException("Type must be: refuse");

                if (string.IsNullOrWhiteSpace(payload.DispatchId))
                    throw new ArgumentException("DispatchId is null or empty - it must be specified.");

                if (string.IsNullOrWhiteSpace(payload.ContractorId))
                    throw new ArgumentException("ContractorId is null or empty - it must be specified.");

                if (payload.ReasonId == 0)
                    throw new ArgumentException("ReasonId is 0 - it must have a value specified.");

                if (string.IsNullOrWhiteSpace(payload.ReasonName))
                    throw new ArgumentException("ReasonName is null or empty - it must be specified.");

                var resp = Send("/refuse", payload.ToJson(), Method.Post);

                log.Properties["contractorId"] = payload.ContractorId;
                log.Properties["dispatchId"] = payload.DispatchId;
                log.Properties["response"] = resp.Content;
                log.Properties["status"] = resp.StatusCode;

                if (!resp.IsSuccessful)
                {
                    log.Level = LogLevel.Error;

                    throw new Exception("Error invoking Refused endpoint:" + resp.StatusCode + "/" + resp.Content);
                }
            }
            finally
            {
                logger.Log(log);
            }
        }

        private static readonly string[] ValidStatuses = new string[] { "DISPATCHED", "EN_ROUTE", "ON_SCENE", "TOWING", "DESTINATION", "COMPLETE" };

        public class StatusCodes
        {
            public const string Dispatched = "DISPATCHED";
            public const string Enroute = "EN_ROUTE";
            public const string OnScene = "ON_SCENE";
            public const string Towing = "TOWING";
            public const string Destination = "DESTINATION";
            public const string Complete = "COMPLETE";
        }

        public void StatusUpdate(DispatchStatusUpdateModel payload)
        {
            var log = NewLogEvent("StatusUpdate", payload.ToJson());

            try
            {
                if (payload.Type != "statusUpdate")
                    throw new ArgumentException("Type must be: statusUpdate");

                if (payload.Status != StatusCodes.Dispatched &&
                    payload.Status != StatusCodes.Enroute &&
                    payload.Status != StatusCodes.OnScene &&
                    payload.Status != StatusCodes.Towing &&
                    payload.Status != StatusCodes.Destination &&
                    payload.Status != StatusCodes.Complete)
                    throw new ArgumentException("Invalid Status value passed: " + payload.Status + ".");

                var resp = Send("/status", payload.ToJson(), Method.Post);

                log.Properties["contractorId"] = payload.ContractorId;
                log.Properties["dispatchId"] = payload.DispatchId;
                log.Properties["response"] = resp.Content;
                log.Properties["status"] = resp.StatusCode;

                if (!resp.IsSuccessful)
                {
                    log.Level = LogLevel.Error;

                    if (resp.StatusCode == 0)
                        throw new Exception("Network Error, retry recommended.");
                    else
                        throw new Exception("Error invoking StatusUpdate endpoint:" + resp.StatusCode + "/" + resp.Content);
                }
            }
            finally
            {
                logger.Log(log);
            }
        }

        public class DispatchBreadcrumbPayload
        {
            public string Type { get; set; } = "breadcrumb";
            public string DispatchId { get; set; }
            public string DriverId { get; set; }
            public DateTime Timestamp { get; set; }
            public decimal Latitude { get; set; }
            public decimal Longitude { get; set; }
        }

        public void Breadcrumb(DispatchBreadcrumbPayload payload)
        {
            var log = NewLogEvent("Breadcrumb", payload.ToJson());

            try
            {
                if (payload.Type != "breadcrumb")
                    throw new ArgumentException("Type must be: breadcrumb");

                var resp = Send("/breadrumbs", payload.ToJson(), Method.Post);

                log.Properties["dispatchId"] = payload.DispatchId;
                log.Properties["response"] = resp.Content;
                log.Properties["status"] = resp.StatusCode;

                if (!resp.IsSuccessful)
                {
                    log.Level = LogLevel.Error;
                    if (resp.StatusCode == 0)
                        throw new Exception("Network Error, retry recommended.");
                    else
                        throw new Exception("Error invoking Refused endpoint:" + resp.StatusCode + "/" + resp.Content);
                }
            }
            catch (Exception y)
            {
                log.Level = LogLevel.Error;
                log.Exception = y;
                throw;
            }
            finally
            {
                logger.Log(log);
            }
        }

        public void Photo(DispatchPhotoModel payload)
        {
            var log = NewLogEvent("Photo", payload.ToJson());

            try
            {
                if (payload.Type != "photo")
                    throw new ArgumentException("Type must be: photo");

                if (string.IsNullOrWhiteSpace(payload.DispatchId))
                    throw new ArgumentException("DispatchId is null or empty - it must be specified.");

                if (string.IsNullOrWhiteSpace(payload.ContractorId))
                    throw new ArgumentException("ContractorId is null or empty - it must be specified.");

                var resp = Send("/photo", payload.ToJson(), Method.Post);

                log.Properties["contractorId"] = payload.ContractorId;
                log.Properties["dispatchId"] = payload.DispatchId;
                log.Properties["response"] = resp.Content;
                log.Properties["status"] = resp.StatusCode;

                if (!resp.IsSuccessful)
                {
                    log.Level = LogLevel.Error;
                    if (resp.StatusCode == 0)
                        throw new Exception("Network Error, retry recommended.");
                    else
                        throw new Exception("Error invoking Photo endpoint:" + resp.StatusCode + "/" + resp.Content);
                }
            }
            finally
            {
                logger.Log(log);
            }
        }

        public class DispatchUpdateVehiclePayload
        {
            public string Type { get; set; } = "update_vehicle";
            public string DispatchId { get; set; }
            public string ContractorId { get; set; }
            public VehicleUpdate Vehicle { get; set; }

            public sealed class VehicleUpdate
            {
                public string Year { get; set; }
                public string Make { get; set; }
                public string Model { get; set; }
                public string Color { get; set; }
                public string DriveType { get; set; }
                public string LicensePlate { get; set; }
                public string Vin { get; set; }
                public string Odometer { get; set; }
            }
        } 

        public sealed class DispatchUpdateIncidentAddressPayload
        {
            
            public string Type { get; set; } = "update_incident_address";
            public string ContractorId { get; set; }
            public string DispatchId { get; set; }
            public IncAddress IncidentAddress { get; set; }

            public class IncAddress
            {
                public string Address { get; set; }
                public string City { get; set; }
                public string State { get; set; }
                public string Zip { get; set; }
                public decimal Latitude { get; set; }
                public decimal Longitude { get; set; }
                public string Landmark { get; set; }
            }
        }

        public sealed class DispatchUpdateDestinationAddressPayload
        {
            public string Type { get; set; } = "update_destination_address";
            public string DispatchId { get; set; }
            public string ContractorId { get; set; }
            public DestAddress DestinationAddress { get; set; }

            public sealed class DestAddress
            {
                public string Address { get; set; }
                public string City { get; set; }
                public string State { get; set; }
                public string Zip { get; set; }
                public decimal Latitude { get; set; }
                public decimal Longitude { get; set; }
            }
        }


        public void Update(DispatchUpdateVehiclePayload payload)
        {
            var log = NewLogEvent("DispatchUpdateVehicle", payload.ToJson());

            try
            {
                if (payload.Type != "update_vehicle")
                    throw new ArgumentException("Type must be: update_vehicle");

                if (string.IsNullOrWhiteSpace(payload.DispatchId))
                    throw new ArgumentException("DispatchId is null or empty - it must be specified.");

                var resp = Send("/update/vehicle", payload.ToJson(), Method.Post);

                log.Properties["contractorId"] = payload.ContractorId;
                log.Properties["dispatchId"] = payload.DispatchId;
                log.Properties["response"] = resp.Content;
                log.Properties["status"] = resp.StatusCode;

                if (!resp.IsSuccessful)
                {
                    log.Level = LogLevel.Error;
                    if (resp.StatusCode == 0)
                        throw new Exception("Network Error, retry recommended.");
                    else
                        throw new Exception("Error invoking Cancel endpoint:" + resp.StatusCode + "/" + resp.Content);
                }
            }
            catch (Exception y)
            {
                log.Level = LogLevel.Error;
                log.Exception = y;
                throw;
            }
            finally
            {
                logger.Log(log);
            }
        }

        public void Update(DispatchUpdateIncidentAddressPayload payload)
        {
            var log = NewLogEvent("DispatchUpdateIncidentAddress", payload.ToJson());

            try
            {
                if (payload.Type != "update_incident_address")
                    throw new ArgumentException("Type must be: update_incident_address");

                if (string.IsNullOrWhiteSpace(payload.DispatchId))
                    throw new ArgumentException("DispatchId is null or empty - it must be specified.");

                var resp = Send("/update/incidentaddress", payload.ToJson(), Method.Post);

                log.Properties["contractorId"] = payload.ContractorId;
                log.Properties["dispatchId"] = payload.DispatchId;
                log.Properties["response"] = resp.Content;
                log.Properties["status"] = resp.StatusCode;

                if (!resp.IsSuccessful)
                {
                    log.Level = LogLevel.Error;
                    if (resp.StatusCode == 0)
                        throw new Exception("Network Error, retry recommended.");
                    else
                        throw new Exception("Error invoking Cancel endpoint:" + resp.StatusCode + "/" + resp.Content);
                }
            }
            catch (Exception y)
            {
                log.Level = LogLevel.Error;
                log.Exception = y;
                throw;
            }
            finally
            {
                logger.Log(log);
            }
        }

        public void Update(DispatchUpdateDestinationAddressPayload payload)
        {
            var log = NewLogEvent("DispatchUpdateDestinationAddress", payload.ToJson());

            try
            {
                if (payload.Type != "update_destination_address")
                    throw new ArgumentException("Type must be: update_destination_address");

                if (string.IsNullOrWhiteSpace(payload.DispatchId))
                    throw new ArgumentException("DispatchId is null or empty - it must be specified.");

                var resp = Send("/update/destinationaddress", payload.ToJson(), Method.Post);

                log.Properties["contractorId"] = payload.ContractorId;
                log.Properties["dispatchId"] = payload.DispatchId;
                log.Properties["response"] = resp.Content;
                log.Properties["status"] = resp.StatusCode;

                if (!resp.IsSuccessful)
                {
                    log.Level = LogLevel.Error;
                    if (resp.StatusCode == 0)
                        throw new Exception("Network Error, retry recommended.");
                    else
                        throw new Exception("Error invoking Cancel endpoint:" + resp.StatusCode + "/" + resp.Content);
                }
            }
            catch (Exception y)
            {
                log.Level = LogLevel.Error;
                log.Exception = y;
                throw;
            }
            finally
            {
                logger.Log(log);
            }
        }

        public void Cancel(DispatchCancelModel payload)
        {
            var log = NewLogEvent("Cancel", payload.ToJson());

            try
            {
                if (payload.Type != "cancel")
                    throw new ArgumentException("Type must be: cancel");

                if (string.IsNullOrWhiteSpace(payload.ContractorId))
                    throw new ArgumentException("ContractorId is null or empty - it must be specified.");

                if (string.IsNullOrWhiteSpace(payload.ResolutionCode))
                    throw new ArgumentException("ResolutionCode is null or empty - it must be specified.");

                if (string.IsNullOrWhiteSpace(payload.DispatchId))
                    throw new ArgumentException("DispatchId is null or empty - it must be specified.");

                var resp = Send("/cancel", payload.ToJson(), Method.Post);

                log.Properties["contractorId"] = payload.ContractorId;
                log.Properties["dispatchId"] = payload.DispatchId;
                log.Properties["response"] = resp.Content;
                log.Properties["status"] = resp.StatusCode;

                if (!resp.IsSuccessful)
                {
                    log.Level = LogLevel.Error;
                    if (resp.StatusCode == 0)
                        throw new Exception("Network Error, retry recommended.");
                    else
                        throw new Exception("Error invoking Cancel endpoint:" + resp.StatusCode + "/" + resp.Content);
                }
            }
            catch (Exception y)
            {
                log.Level = LogLevel.Error;
                log.Exception = y;
                throw;
            }
            finally
            {
                logger.Log(log);
            }
        }

        public void Goa(DispatchGoaModel payload)
        {
            var log = NewLogEvent("GOA", payload.ToJson());

            try
            {
                if (payload.Type != "goa")
                    throw new ArgumentException("Type must be goa");

                if (string.IsNullOrWhiteSpace(payload.ResolutionCode))
                    throw new ArgumentException("ResolutionCode is null or empty - it must be specified.");

                var resp = Send("/goa", payload.ToJson(), Method.Post);

                log.Properties["contractorId"] = payload.ContractorId;
                log.Properties["dispatchId"] = payload.DispatchId;
                log.Properties["response"] = resp.Content;
                log.Properties["status"] = resp.StatusCode;

                if (!resp.IsSuccessful)
                {
                    log.Level = LogLevel.Error;

                    if (resp.StatusCode == 0)
                        throw new Exception("Network Error, retry recommended.");
                    else
                        throw new Exception("Error invoking GOA endpoint:" + resp.StatusCode + "/" + resp.Content);
                }
            }
            catch (Exception y)
            {
                log.Level = LogLevel.Error;
                log.Exception = y;
                throw;
            }
            finally
            {
                logger.Log(log);
            }
        }

        public void Complete(DispatchCompleteModel payload)
        {
            var log = NewLogEvent("Complete", payload.ToJson());

            try
            {
                if (payload.Type != "complete")
                    throw new ArgumentException("Type must be: complete");

                if (string.IsNullOrWhiteSpace(payload.ResolutionCode))
                    throw new ArgumentException("ResolutionCode is null or empty - it must be specified.");

                if (string.IsNullOrWhiteSpace(payload.DispatchId))
                    throw new ArgumentException("DispatchId is null or empty - it must be specified.");

                var resp = Send("/complete", payload.ToJson(), Method.Post);

                log.Properties["dispatchId"] = payload.DispatchId;
                log.Properties["response"] = resp.Content;
                log.Properties["status"] = resp.StatusCode;

                if (!resp.IsSuccessful)
                {
                    log.Level = LogLevel.Error;

                    if (resp.StatusCode == 0)
                        throw new Exception("Network Error, retry recommended.");
                    else
                        throw new Exception("Error invoking Complete endpoint:" + resp.StatusCode + "/" + resp.Content);
                }
            }
            catch (Exception y)
            {
                log.Level = LogLevel.Error;
                log.Exception = y;
                throw;
            }
            finally
            {
                logger.Log(log);
            }
        }

        public string Ping()
        {
            var log = NewLogEvent("Ping");

            try
            {
                var resp = Send("/ping", null, Method.Get);

                log.Properties["response"] = resp.Content;
                log.Properties["status"] = resp.StatusCode;

                if (!resp.IsSuccessful)
                {
                    log.Level = LogLevel.Error;

                    throw new Exception("Error invoking Ping endpoint:" + resp.StatusCode + "/" + resp.Content);
                }
                return resp.Content;
            }
            catch (Exception y)
            {
                log.Level = LogLevel.Error;
                log.Exception = y;
                throw;
            }
            finally
            {
                logger.Log(log);
            }
        }

        public string OAuthAccessTokenAsync()
        {
            string keyName = "bcaa_access_token:" + EnvironmentName;

            var existing = Core.GetRedisValue(keyName);
            if (existing != null)
            {
                return existing;
            }

            var request = new RestRequest("/oauth2/v2.0/token", Method.Post);
            request.AddParameter("scope", Scope, ParameterType.GetOrPost);
            request.AddParameter("grant_type", "client_credentials", ParameterType.GetOrPost);
            request.AddParameter("client_id", ClientId, ParameterType.GetOrPost);
            request.AddParameter("client_secret", ClientSecret, ParameterType.GetOrPost);

            var client = new RestClient(AppServicesHelper.HttpClient, new RestClientOptions(TokenBaseUrl));
            var response = client.Execute(request);

            string r = response.Content;
            Console.WriteLine(r);
            if (response.StatusCode == System.Net.HttpStatusCode.OK)
            {
                var resp = JsonConvert.DeserializeObject<TokenResponse>(r);
                Core.SetRedisValue(keyName, resp.AccessToken, TimeSpan.FromSeconds(resp.ExpiresIn - 10));

                // Reset _restClient so it will use the new token
                _restClient = null;
                return resp.AccessToken;
            }
            else
            {
                throw new System.Exception("Response=" + response.StatusCode + "...body=" + r);
            }
        }

        public RestResponse Send(string url, string payload, Method method)
        {
            var client = GetRestClient();
            var request = new RestRequest("/v1/towbook/case/" + url.TrimStart('/'), method);

            request.AddParameter("client_id", ApiClientId, ParameterType.HttpHeader);
            request.AddParameter("client_secret", ApiClientSecret, ParameterType.HttpHeader);

            if (method != Method.Get)
                request.AddParameter("application/json", payload, ParameterType.RequestBody);

            var response = client.Execute(request);

            Console.WriteLine(DateTime.Now.ToString() + " " + UrlBase + url + ": " + response.Content);

            return response;
        }

        private RestClient GetRestClient()
        {
            var token = OAuthAccessTokenAsync();
            if (_restClient != null)
                return _restClient;

            var options = new RestClientOptions(UrlBase)
            {
                Authenticator = new OAuth2AuthorizationRequestHeaderAuthenticator(token, "Bearer")
            };




            _restClient = new RestClient(AppServicesHelper.HttpClient, options);
            return _restClient;
        }
    }
}
