using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Extric.Towbook.Integrations.MotorClubs.Aaa.National
{
    public sealed class AaaStatusUpdateCallEvent
    {
        public string CallKey { get; set; }
        public string CallDate { get; set; }
        public string CallId { get; set; }
        public string EventTime { get; set; }
        public CallEventStatusDetail StatusDetail { get; set; }
    }

    public sealed class CallEventStatusDetail
    {
        /// <summary>
        /// // AS, DI, ER, OL, TW, TC
        /// </summary>
        public string Status { get; set; }
        public string ReasonCode { get; set; }
        public string ModifiedDate { get; set; }

        public string Eta { get; set; }

        /// <summary>
        /// Required on Outbound calls
        /// </summary>
        public ChannelContact UpdatedBy { get; set; }
        /// <summary>
        /// Required on Outbound calls
        /// </summary>
        public StatusFacility Facility { get; set; }
        /// <summary>
        /// Required on Outbound calls
        /// </summary>
        public StatusTruck Truck { get; set; }
    }

    public sealed class StatusTruck
    {
        public string Id { get; set; }
        public TruckDriver Driver { get; set; }
        public class TruckDriver
        {
            public string Id { get; set; }
        }
    }

    public class StatusFacility
    {
        public string FoiType { get; set; } = "ERS";
        public object Location { get; set; }

        /// <summary>
        /// Facility ID
        /// </summary>
        public string Id { get; set; }
    }

    public class StatusUpdatedBy
    {
        public string ContactType { get; set; }
    }

}
