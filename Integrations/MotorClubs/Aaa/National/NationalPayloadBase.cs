using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Extric.Towbook.Integrations.MotorClubs.Aaa.National;

namespace Extric.Towbook.Integrations.MotorClubs.Aaa.National
{
    public class NationalPayloadBase
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string RequestEventType { get; set; }
        public string SubType { get; set; }
        public string Status { get; set; }
        public string Version { get; set; } = "1.0.0";
        public AaaSystem SourceSystem { get; set; } = AaaSystem.Towbook();
        public AaaSystem TargetSystem { get; set; }
        public long CreateDate { get; set; } = ((DateTimeOffset)DateTime.UtcNow).ToUnixTimeSeconds();
        public object[] Calls { get; set; }
        public object[] CallEvents { get; set; }
    }

    public sealed class CallCancelPayload
    {
        public string Id { get; set; }
        public string RequestEventType { get; set; }
        public string SubType { get; set; }
        public string Version { get; set; }
        public AaaSystem SourceSystem { get; set; }
        public AaaSystem TargetSystem { get; set; }
        public long CreateDate { get; set; }
        public AaaStatusUpdateCallEvent[] CallEvents { get; set; }
    }


}
