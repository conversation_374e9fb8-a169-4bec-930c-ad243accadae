using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Extric.Towbook.Integrations.MotorClubs.Aaa.Ace
{
    public class DispatchModel
    {
        public string DispatchId { get; set; }
        public string ContractorId { get; set; }
        public string LocationId { get; set; }
        public string JobId { get; set; }
        public string RequiredAcknowledgeTimeInSeconds { get; set; }
        public string PurchaseOrderNumber { get; set; }
        public string Type { get; set; }
        public string Notes { get; set; }
        public Call Call { get; set; }
        public string DrivingDirection { get; set; }
        public string DriverNotes { get; set; }
    }

    public class Call
    {
        public CallHeader Header { get; set; }
        public CallInfo CallInfo { get; set; }
        public string CallType { get; set; }
        public MemberContactInfo MemberContactInfo { get; set; }
        public Vehicle Vehicle { get; set; }
        public Location BreakdownLocation { get; set; }
        public Location TowDestination { get; set; }
        public Requestedservice RequestedService { get; set; }
        public DriverData DriverData { get; set; }
        public ProvidedServiceItem[] ProvidedServices { get; set; } // what is in here?
        public bool RedFlag { get; set; }
        public ServiceStatus ServiceStatus { get; set; }
        public Payment Payment { get; set; }
        public Comment[] Comments { get; set; }
        public CallEvent[] CallEvents { get; set; }

    }

    public class ProvidedServiceItem
    {
        public string Service { get; set; }
        public decimal Quantity { get; set; }
        public decimal Charge { get; set; }
    }

    public class CallHeader
    {
        public object Id { get; set; }
        public DateTime DateTime { get; set; }
        public string OperationType { get; set; }
        public Channel Channel { get; set; }
        public SourceOrg SourceOrg { get; set; }
        public TargetOrg TargetOrg { get; set; }
    }

    public class Channel
    {
        public string Name { get; set; }
    }

    public class SourceOrg
    {
        public string Code { get; set; }
        public string Name { get; set; }
    }

    public class TargetOrg
    {
        public string Code { get; set; }
        public string Name { get; set; }
    }

    public class CallInfo
    {
        public string CallId { get; set; }
        public string Status { get; set; }
        public string CallDate { get; set; }
        public string CallKey { get; set; }
        public long CreatedDateTimeUTC { get; set; }
    }

    public class MemberContactInfo
    {
        public Name Name { get; set; }
        public PhoneNumber[] PhoneNumbers { get; set; }
        public MemberNumber MemberNumber { get; set; }
        public string JoinYear { get; set; }
        public string CurrentBenefitLevel { get; set; }
    }

    public class Name
    {
        public string NameTitle { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }

        public override string ToString()
        {
            return FirstName + " " + LastName;
        }
    }

    public class MemberNumber
    {
        public string CardNumber { get; set; }
    }

    public class PhoneNumber
    {
        public string Type { get; set; }
        public string PhoneExtension { get; set; }
        public string DisplayPhoneNumber { get; set; }
        public bool OkToText { get; set; }
    }

    public class Vehicle
    {
        public int? ModelYear { get; set; }
        public string MakeName { get; set; }
        public string ModelName { get; set; }
        public string Color { get; set; }
        public string LicenseState { get; set; }
        public string LicenseNumber { get; set; }
        public VehicleDetail VehicleDetail { get; set; }
    }

    public class VehicleDetail
    {
        public string Vin { get; set; }
        public string TrimLevel { get; set; }
        public string BodyType { get; set; }
        public string BodyTypeFullName()
        {
            switch (BodyType)
            {
                case "2M": return "2-wheeled motor cycle";
                case "3M": return "3-wheeled motorcycle";
                case "DW": return "dual wheels";
                case "PS": return "passenger car/truck";
                case "R1": return "RV < 23 ft";
                case "R2": return "RV 24-32 ft";
                case "F3": return "RV > 33 ft";
                case "TR": return "Trailer";
            }

            return "";
        }

        public string DriveType { get; set; }
        public decimal? TrailerLength { get; set; }
        public string HitchType { get; set; }
        public string RvClass { get; set; }
        public decimal? RvLength { get; set; }
        public int? Odometer { get; set; }
        public string SpecialEquipmentNeeds { get; set; }
    }

    public class TowAddress
    {
        public string City { get; set; }
        public string State { get; set; }
        public string StreetAddress { get; set; }
        public string ZipCode { get; set; }
        public string ForeignCountryName { get; set; }
        public string[] DisplayAddressLines { get; set; }
        public string[] CrossStreets { get; set; }

        public override string ToString()
        {
            return (StreetAddress + (!string.IsNullOrWhiteSpace(StreetAddress) ? ", " : "") +
                City + " " + State + " " + ZipCode).Trim();
        }
    }

    public class Location
    {
        public string Name { get; set; }
        public string Instruction { get; set; }
        public string MileMarker { get; set; }
        public string Highway { get; set; }
        public string Landmark { get; set; }
        public string Latitude { get; set; }
        public string Longitude { get; set; }
        public TowAddress Address { get; set; }
    }


    public class Requestedservice
    {
        public string Priority { get; set; }
        public TroubleCode[] TroubleCodes { get; set; }
        public int? NumberOfPassengers { get; set; }
        public bool? FlatBedRequested { get; set; }
        public bool? FlatBedRequired { get; set; }
        public string TypeOfFuel { get; set; }
        public string TimeZoneOffset { get; set; }
        public string TimeZoneId { get; set; }
    }

    public class TroubleCode
    {
        public string Type { get; set; }
        public string Code { get; set; }
        public string Description { get; set; }
    }

    public class DriverData
    {
        public string ServicingStationEmployeeId { get; set; }
        public string TruckId { get; set; }
        public string ServicingStationEmployeeName { get; set; }
        public Facility Facility { get; set; }
    }

    public class Facility
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string FacilityId { get; set; }
        public string FacilityName { get; set; }
    }

    public class ServiceStatus
    {
        public int? WaitTimeMinutes { get; set; }
        public string Status { get; set; }
        public string ScheduledStartTime { get; set; }
        public string AppointmentTime { get; set; }
        public DateTime? GetAppointmentTime()
        {
            if (!string.IsNullOrWhiteSpace(AppointmentTime))
            {
                try
                {
                    return DateTime.Parse(AppointmentTime.Replace("[UTC]", "").Replace("\"", "")).ToLocalTime();
                }
                catch {}
            }
            
            return null;
        }

        public string PromisedTimeOfArrival { get; set; }
        public string EstimatedTimeOfArrival { get; set; }
    }

    public class Payment
    {
        public bool Required { get; set; }
        public string PaymentType { get; set; }
        public string PaymentCode { get; set; }
    }

    public class Comment
    {
        public string Text { get; set; }
        public string Visibility { get; set; }
        public string CommentType { get; set; }
        public DateTime CommentDateTime { get; set; }
        public string Commenter { get; set; }
    }

    public class CallEvent
    {
        public string Status { get; set; }
        public DateTime EventTime { get; set; }
        public string UpdatedBy { get; set; }
    }
}
