using Newtonsoft.Json;

namespace Extric.Towbook.Integrations.MotorClubs.Aaa.ACG
{
    public sealed class AcknowledgePlatformEventPayload
    {
        [JsonProperty("External_Id__c")]
        public string ExternalId { get; set; }

        [JsonProperty("Event_Id__c")]
        public string EventId { get; set; }

        [JsonProperty("ExternalSystem__c")]
        public string ExternalSystem { get; set; } = "Towbook";

        [JsonProperty("Sync_Status__c")]
        public string SyncStatus { get; set; }

        [JsonProperty("Sync_Status_Description__c")]
        public string SyncStatusDescription { get; set; }
    }
}
