using System;
using Newtonsoft.Json;

namespace Extric.Towbook.Integrations.MotorClubs.Aaa.ACG
{
    public sealed class CallAssignmentPayload
    {
        public string ServiceAppointmentId { get; set; }
        public string WorkOrderId { get; set; }
        [JsonProperty("serviceResourceId")]
        public string ServiceResourceId { get; set; }
        public string Status { get; set; }
        public string CallStatus { get; set; }
        /// <summary>
        /// only pass if you're declining.
        /// </summary>
        public string DeclineReason { get; set; }
        public DateTime ScheduledStart { get; set; }
        public DateTime ScheduledEnd { get; set; }
        public DateTime? TowScheduledStart { get; set; }
        public DateTime? TowScheduledEnd { get; set; }
        public string ExternalSystem { get; set; } = "Towbook";
        /// <summary>
        /// distance in miles (decimal ok)
        /// </summary>
        public string ApproximateTravelDistanceTo { get; set; }
        /// <summary>
        /// time in minutes (no decimals) 
        /// </summary>
        public string EstimatedTravelTime { get; set; }

    }
}
