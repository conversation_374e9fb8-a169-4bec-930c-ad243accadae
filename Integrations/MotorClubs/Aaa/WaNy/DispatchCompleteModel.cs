using System;

namespace Extric.Towbook.Integrations.MotorClubs.Aaa.WaNy
{
    public sealed class DispatchCompleteModel
    {
        public string Type { get; set; } = "complete";
        public string CallKey { get; set; }
        public string FacilityId { get; set; }
        public string LocationId { get; set; }
        public DriverModel Driver { get; set; }
        public string ResolutionCode { get; set; }
        public InvoiceItemModel[] InvoiceItems { get; set; }

        public DateTime? DispatchedTime { get; set; }
        public DateTime? EnrouteTime { get; set; }
        public DateTime? OnSceneTime { get; set; }
        public DateTime? TowingTime { get; set; }
        public DateTime? DestinationTime { get; set; }
        public DateTime? CompletionTime { get; set; }

        public int? Odometer { get; set; }
        public string Vin { get; set; }

        public sealed class InvoiceItemModel
        {
            public string Code { get; set; }
            public decimal Quantity { get; set; }
            public decimal Price { get; set; }
        }
    }
}
