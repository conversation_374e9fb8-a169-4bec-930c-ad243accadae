using System;
using System.Collections.Generic;
using Extric.Towbook.Utility;
using ForceDotNetJwtCompanion;
using NLog;
using RestSharp.Authenticators.OAuth2;
using RestSharp;
using System.Threading.Tasks;
using Newtonsoft.Json;
using ForceDotNetJwtCompanion.Models;
using System.Net.Http;
using System.Net;
using Extric.Towbook.Accounts;

namespace Extric.Towbook.Integrations.MotorClubs.Aaa.WaNy
{
    public sealed class AaaWaNyRestClient
    {
        private static readonly Logger logger = LogManager.GetCurrentClassLogger();
        public string UrlBase = "";

        public string ClientId { get; private set; }
        private string ClientSecret { get; set; }

        public int MasterAccountId { get; private set; }
        public string EnvironmentName { get; private set; }
        public string Ip { get; private set; }
        public int CompanyId { get; private set; }
        public long QueueItemId { get; private set; }

        public int CallId { get; private set; }
        public int CallRequestId { get; private set; }
        public string CallKey { get; private set; }
        public AaaContractor Contractor { get; set; }

        public AaaWaNyRestClient(
            int masterAccountId,
            string environment,
            string ip,
            int companyId,
            long queueItemId,
            int callRequestId,
            int callId,
            string callKey,
            AaaContractor contractor)
        {
            MasterAccountId = masterAccountId;
            EnvironmentName = environment;
            CompanyId = companyId;
            QueueItemId = queueItemId;
            CallRequestId = callRequestId;
            CallId = callId;
            CallKey = callKey;
            Contractor = contractor;
            Ip = ip;

            string envName = "none";

            if (EnvironmentName == "PROD")
                envName = "Production";
            else if (EnvironmentName == "UAT")
                envName = "Uat";
            else if (EnvironmentName == "TEST")
                envName = "Test";
            else if (EnvironmentName == "DEV")
                envName = "Dev";

            if (contractor.MasterAccountId == MasterAccountTypes.AaaNewYork)
            {
                UrlBase = Core.GetAppSetting($"MotorClubs:AaaWCNY:{envName}:UrlBase");
                ClientId = Core.GetAppSetting($"MotorClubs:AaaWCNY:{envName}:ClientId");
                ClientSecret = Core.GetAppSetting($"MotorClubs:AaaWCNY:{envName}:ClientSecret");
            }
            else if (contractor.MasterAccountId == MasterAccountTypes.AaaWashington)
            {
                UrlBase = Core.GetAppSetting($"MotorClubs:AaaWashington:{envName}:UrlBase");
                ClientId = Core.GetAppSetting($"MotorClubs:AaaWashington:{envName}:ClientId");
                ClientSecret = Core.GetAppSetting($"MotorClubs:AaaWashington:{envName}:ClientSecret");
            }
        }

        public LogEventInfo NewLogEvent(string message, string json = null)
        {
            var log = new LogEventInfo();

            log.Level = LogLevel.Info;
            log.Message = message;

            if (json != null)
                log.Properties["json"] = json;

            log.Properties["commitId"] = Core.GetCommitId();
            log.Properties["masterAccountName"] = MasterAccountTypes.GetName(MasterAccountId);
            log.Properties["masterAccountId"] = MasterAccountId;
            log.Properties["environment"] = EnvironmentName;
            log.Properties["requestingIp"] = Ip;

            if (CompanyId > 0)
                log.Properties["companyId"] = CompanyId;

            if (QueueItemId > 0)
                log.Properties["queueItemId"] = QueueItemId;

            if (CallId > 0)
                log.Properties["callId"] = CallId;

            if (CallRequestId > 0)
                log.Properties["callRequestId"] = CallRequestId;

            if (!string.IsNullOrWhiteSpace(CallKey))
                log.Properties["dispatchId"] = CallKey;

            return log;
        }


        public static string GetEnvironmentName(int env)
        {
            if (env == 1)
                return "DEV";
            if (env == 2)
                return "TEST";
            else if (env == 3)
                return "PROD";

            return "DEV";
        }

        public static int GetEnvironmentId(string env)
        {
            if (env == "DEV")
                return 1;
            if (env == "TEST")
                return 2;
            else if (env == "PROD")
                return 3;

            return 1;
        }


        private static readonly string[] ValidStatuses = new string[] {
            "DISPATCHED", "EN_ROUTE", "ON_LOCATION", "TOWING", "DESTINATION", "COMPLETE" };

        public static class StatusCodes
        {
            public const string Dispatched = "DISPATCHED";
            public const string Enroute = "EN_ROUTE";
            public const string OnScene = "ON_LOCATION";
            public const string Towing = "TOWING";
            public const string Destination = "DESTINATION";
            public const string Complete = "COMPLETE";
        }


        public async Task<RestResponse> Send(string url, string payload, Method method, LogEventInfo log = null)
        {
            url = "/services/apexrest/" + url;

            Console.WriteLine(url);

            var token = await GetToken();

            var client = new RestClient(new RestClientOptions()
            {
                BaseUrl = new Uri(token.InstanceUrl),
                Authenticator = new OAuth2AuthorizationRequestHeaderAuthenticator(token.AccessToken, "Bearer")
            });

            var request = new RestRequest(url, method);

            if (method != Method.Get)
            {
                request.AddParameter("application/json", payload, ParameterType.RequestBody);
            }

            var response = await client.ExecuteAsync(request);

            if (log != null)
                log.Properties["url"] = new Uri(client.Options.BaseUrl, request.Resource).ToString();

            Console.WriteLine(DateTime.Now.ToString() + " " + UrlBase + url + ": " + response.Content);

            return response;
        }

        public async Task<AccessTokenPayload> GetToken()
        {
            var newToken = await RenewToken();

            return newToken;
        }

        private readonly static HttpClient _httpClient = new HttpClient();

        private async Task<AuthToken> CallTokenEndpoint(string clientId, string clientSecret, string tokenEndpoint)
        {
            var request = new HttpRequestMessage
            {
                Method = HttpMethod.Post,
                RequestUri = new Uri(tokenEndpoint),
                Content = new FormUrlEncodedContent(new[]
                {
                    new KeyValuePair<string, string>("grant_type", "client_credentials"),
                    new KeyValuePair<string, string>("client_id", clientId),
                    new KeyValuePair<string, string>("client_secret", clientSecret),
                })
            };

            request.Headers.UserAgent.ParseAdd(string.Concat("towbook/5.0"));

            HttpResponseMessage responseMessage;

            try
            {
                responseMessage = await _httpClient.SendAsync(request, HttpCompletionOption.ResponseContentRead);
            }
            catch (Exception exc)
            {
                throw new ForceAuthenticationException(HttpStatusCode.InternalServerError, exc.Message);
            }

            if (responseMessage.IsSuccessStatusCode)
            {
                var authToken = JsonConvert.DeserializeObject<AuthToken>(await responseMessage.Content.ReadAsStringAsync());
                if (authToken != null)
                {
                    return authToken;
                }

                throw new ForceAuthenticationException(HttpStatusCode.InternalServerError, "authToken is invalid or empty");
            }

            try
            {
                var errorResponse = JsonConvert.DeserializeObject<AuthErrorResponse>(await responseMessage.Content.ReadAsStringAsync());
                throw new ForceAuthenticationException(responseMessage.StatusCode, $"{errorResponse?.Error}: {errorResponse?.ErrorDescription}"
                );
            }
            catch (Exception exc)
            {
                throw new ForceAuthenticationException(HttpStatusCode.InternalServerError, exc.Message);
            }
        }

        private async Task<AccessTokenPayload> RenewToken()
        {
            if (string.IsNullOrWhiteSpace(UrlBase))
                throw new Exception("Missing Configuration: UrlBase isn't configured for WWaNyRestClient");

            var authClient = new JwtAuthenticationClient("v57.0", false);
            var token = await CallTokenEndpoint(
                ClientId,
                ClientSecret,
                UrlBase + "/services/oauth2/token");

            return new AccessTokenPayload()
            {
                AccessToken = token.AccessToken,
                InstanceUrl = token.InstanceUrl
            };
        }


        public async Task AcceptAsync(DispatchAcceptModel payload)
        {
            var log = NewLogEvent("Accept", payload.ToJson());

            try
            {
                if (payload.Type != "accept")
                    throw new ArgumentException("Type must be: accept");


                if (string.IsNullOrWhiteSpace(payload.CallKey))
                    throw new ArgumentException("CallKey is null or empty - it must be specified.");

                if (string.IsNullOrWhiteSpace(payload.FacilityId))
                    throw new ArgumentException("FacilityId is null or empty - it must be specified.");

                var resp = await Send(CallUpdateUrl, payload.ToJson(), Method.Post);

                log.Properties["contractorId"] = payload.FacilityId;
                log.Properties["dispatchId"] = payload.CallKey;
                log.Properties["response"] = resp.Content;
                log.Properties["status"] = resp.StatusCode;

                if (!resp.IsSuccessful)
                {
                    log.Level = LogLevel.Error;

                    throw new Exception("Error invoking accept endpoint:" + resp.StatusCode + "/" + resp.Content);
                }
            }
            finally
            {
                logger.Log(log);
            }
        }

        public async Task RefuseAsync(DispatchRefuseModel payload)
        {
            var log = NewLogEvent("Refuse", payload.ToJson());

            try
            {
                if (payload.Type != "refuse")
                    throw new ArgumentException("Type must be: refuse");

                if (string.IsNullOrWhiteSpace(payload.CallKey))
                    throw new ArgumentException("CallKey is null or empty - it must be specified.");

                if (string.IsNullOrWhiteSpace(payload.FacilityId))
                    throw new ArgumentException("FacilityId is null or empty - it must be specified.");

                if (payload.ReasonId == 0)
                    throw new ArgumentException("ReasonId is 0 - it must have a value specified.");

                if (string.IsNullOrWhiteSpace(payload.ReasonName))
                    throw new ArgumentException("ReasonName is null or empty - it must be specified.");

                var resp = await Send(CallUpdateUrl, payload.ToJson(), Method.Post, log);

                log.Properties["contractorId"] = payload.FacilityId;
                log.Properties["dispatchId"] = payload.CallKey;
                log.Properties["response"] = resp.Content;
                log.Properties["status"] = resp.StatusCode;

                if (!resp.IsSuccessful)
                {
                    log.Level = LogLevel.Error;

                    throw new Exception("Error invoking Refused endpoint:" + resp.StatusCode + "/" + resp.Content);
                }
            }
            catch (Exception e)
            {
                log.Level = LogLevel.Error;
                log.Exception = e;
                throw;
            }
            finally
            {
                logger.Log(log);
            }
        }

        public async Task StatusUpdateAsync(DispatchStatusUpdateModel payload)
        {
            var log = NewLogEvent("StatusUpdate", payload.ToJson());

            try
            {
                if (payload.Type != "statusUpdate")
                    throw new ArgumentException("Type must be: statusUpdate");

                if (payload.Status != StatusCodes.Dispatched &&
                    payload.Status != StatusCodes.Enroute &&
                    payload.Status != StatusCodes.OnScene &&
                    payload.Status != StatusCodes.Towing &&
                    payload.Status != StatusCodes.Destination &&
                    payload.Status != StatusCodes.Complete)
                    throw new ArgumentException("Invalid Status value passed: " + payload.Status + ".");

                if (payload.Status != StatusCodes.Enroute)
                    payload.TrackingLink = null;

                var resp = await Send(CallUpdateUrl, payload.ToJson(), Method.Post);

                log.Properties["contractorId"] = payload.FacilityId;   
                log.Properties["dispatchId"] = payload.CallKey;
                log.Properties["response"] = resp.Content;
                log.Properties["status"] = resp.StatusCode;

                if (!resp.IsSuccessful)
                {
                    log.Level = LogLevel.Error;

                    if (resp.StatusCode == 0)
                        throw new Exception("Network Error, retry recommended.");
                    else
                        throw new Exception("Error invoking StatusUpdate endpoint:" + resp.StatusCode + "/" + resp.Content);
                }
            }
            finally
            {
                logger.Log(log);
            }
        }

        public async Task BreadcrumbAsync(DispatchBreadcrumbPayload payload)
        {
            var log = NewLogEvent("Breadcrumb", payload.ToJson());

            try
            {
                if (payload.Type != "breadcrumb")
                    throw new ArgumentException("Type must be: breadcrumb");

                var resp = await Send(CallUpdateUrl, payload.ToJson(), Method.Patch);

                log.Properties["dispatchId"] = payload.CallKey;
                log.Properties["response"] = resp.Content;
                log.Properties["status"] = resp.StatusCode;

                if (!resp.IsSuccessful)
                {
                    log.Level = LogLevel.Error;
                    if (resp.StatusCode == 0)
                        throw new Exception("Network Error, retry recommended.");
                    else
                        throw new Exception("Error invoking Refused endpoint:" + resp.StatusCode + "/" + resp.Content);
                }
            }
            catch (Exception y)
            {
                log.Level = LogLevel.Error;
                log.Exception = y;
                throw;
            }
            finally
            {
                logger.Log(log);
            }
        }

        public async Task PhotoAsync(DispatchPhotoModel payload)
        {
            var log = NewLogEvent("Photo", payload.ToJson());

            try
            {
                if (payload.Type != "photo")
                    throw new ArgumentException("Type must be: photo");

                if (string.IsNullOrWhiteSpace(payload.CallKey))
                    throw new ArgumentException("CallKey is null or empty - it must be specified.");

                if (string.IsNullOrWhiteSpace(payload.FacilityId))
                    throw new ArgumentException("FaciliityId is null or empty - it must be specified.");

                var resp = await Send(CallUpdateUrl, payload.ToJson(), Method.Post);

                log.Properties["contractorId"] = payload.FacilityId;
                log.Properties["dispatchId"] = payload.CallKey;
                log.Properties["response"] = resp.Content;
                log.Properties["status"] = resp.StatusCode;

                if (!resp.IsSuccessful)
                {
                    log.Level = LogLevel.Error;
                    if (resp.StatusCode == 0)
                        throw new Exception("Network Error, retry recommended.");
                    else
                        throw new Exception("Error invoking Photo endpoint:" + resp.StatusCode + "/" + resp.Content);
                }
            }
            finally
            {
                logger.Log(log);
            }
        }

        private const string CallUpdateUrl = "appointment/updatestatus/";

        public async Task CancelAsync(DispatchCancelModel payload)
        {
            var log = NewLogEvent("Cancel", payload.ToJson());

            try
            {
                if (payload.Type != "cancel")
                    throw new ArgumentException("Type must be: cancel");

                if (string.IsNullOrWhiteSpace(payload.FacilityId))
                    throw new ArgumentException("FacilityId is null or empty - it must be specified.");

                if (string.IsNullOrWhiteSpace(payload.CallKey))
                    throw new ArgumentException("CallKey is null or empty - it must be specified.");

                var resp = await Send(CallUpdateUrl, payload.ToJson(), Method.Post);

                log.Properties["contractorId"] = payload.FacilityId;
                log.Properties["dispatchId"] = payload.CallKey;
                log.Properties["response"] = resp.Content;
                log.Properties["status"] = resp.StatusCode;

                if (!resp.IsSuccessful)
                {
                    log.Level = LogLevel.Error;
                    if (resp.StatusCode == 0)
                        throw new Exception("Network Error, retry recommended.");
                    else
                        throw new Exception("Error invoking Cancel endpoint:" + resp.StatusCode + "/" + resp.Content);
                }
            }
            catch (Exception y)
            {
                log.Level = LogLevel.Error;
                log.Exception = y;
                throw;
            }
            finally
            {
                logger.Log(log);
            }
        }

        public async Task GoaAsync(DispatchGoaModel payload)
        {
            var log = NewLogEvent("GOA", payload.ToJson());

            try
            {
                if (payload.Type != "goa")
                    throw new ArgumentException("Type must be goa");

                if (string.IsNullOrWhiteSpace(payload.ResolutionCode))
                    throw new ArgumentException("ResolutionCode is null or empty - it must be specified.");

                var resp = await Send(CallUpdateUrl, payload.ToJson(), Method.Post);

                log.Properties["contractorId"] = payload.FacilityId;
                log.Properties["dispatchId"] = payload.CallKey;
                log.Properties["response"] = resp.Content;
                log.Properties["status"] = resp.StatusCode;

                if (!resp.IsSuccessful)
                {
                    log.Level = LogLevel.Error;

                    if (resp.StatusCode == 0)
                        throw new Exception("Network Error, retry recommended.");
                    else
                        throw new Exception("Error invoking GOA endpoint:" + resp.StatusCode + "/" + resp.Content);
                }
            }
            catch (Exception y)
            {
                log.Level = LogLevel.Error;
                log.Exception = y;
                throw;
            }
            finally
            {
                logger.Log(log);
            }
        }

        public async Task CompleteAsync(DispatchCompleteModel payload)
        {
            var log = NewLogEvent("Complete", payload.ToJson());

            try
            {
                if (payload.Type != "complete")
                    throw new ArgumentException("Type must be: complete");

                if (string.IsNullOrWhiteSpace(payload.ResolutionCode))
                    throw new ArgumentException("ResolutionCode is null or empty - it must be specified.");


                if (payload.ResolutionCode == "R001" ||
                    payload.ResolutionCode == "R002" ||
                    payload.ResolutionCode == "R003" ||
                    payload.ResolutionCode == "R006" ||
                    payload.ResolutionCode == "R007" ||
                    payload.ResolutionCode == "R199")
                {
                    await GoaAsync(new DispatchGoaModel()
                    {
                        CallKey = payload.CallKey,
                        Comments = "GOA",
                        ResolutionCode = payload.ResolutionCode,
                        FacilityId = payload.FacilityId,
                    });

                    log.Level = LogLevel.Info;
                    log.Message = "Redirected to GOA method because of ResolutionCode being " + payload.ResolutionCode;
                    return;
                }

                if (string.IsNullOrWhiteSpace(payload.CallKey))
                    throw new ArgumentException("CallKey is null or empty - it must be specified.");

                if (payload.EnrouteTime == null)
                    throw new ArgumentException("EnrouteTime is null - it must be specified.");

                if (payload.OnSceneTime == null)
                {
                    if (payload.EnrouteTime != null && payload.TowingTime != null)
                    {
                        payload.OnSceneTime = payload.EnrouteTime.Value.AddMinutes(
                            (payload.EnrouteTime.Value - payload.TowingTime.Value).TotalMinutes / 2);
                    }
                    else
                    {
                        throw new ArgumentException("OnSceneTime is null - it must be specified.");
                    }
                }

                var resp = await Send(CallUpdateUrl, payload.ToJson(), Method.Post);

                log.Properties["dispatchId"] = payload.CallKey;
                log.Properties["response"] = resp.Content;
                log.Properties["status"] = resp.StatusCode;

                if (!resp.IsSuccessful)
                {
                    log.Level = LogLevel.Error;

                    if (resp.StatusCode == 0)
                        throw new Exception("Network Error, retry recommended.");
                    else
                        throw new Exception("Error invoking Complete endpoint:" + resp.StatusCode + "/" + resp.Content);
                }
            }
            catch (Exception y)
            {
                log.Level = LogLevel.Error;
                log.Exception = y;
                throw;
            }
            finally
            {
                logger.Log(log);
            }
        }


        public async Task AddNoteAsync(DispatchNoteModel payload)
        {
            var log = NewLogEvent("AddServiceNote", payload.ToJson());

            try
            {
                if (payload.Type != "serviceNote")
                    throw new ArgumentException("Type must be goa");

                if (string.IsNullOrWhiteSpace(payload.Comment))
                    throw new ArgumentException($"{nameof(payload.Comment)} is null or empty - it must be a non-empty string.");
                
                if (string.IsNullOrWhiteSpace(payload.CommentedBy))
                    throw new ArgumentException($"{nameof(payload.CommentedBy)} is null or empty - it must be a non-empty string.");

                var resp = await Send(CallUpdateUrl, payload.ToJson(), Method.Post);

                log.Properties["contractorId"] = payload.FacilityId;
                log.Properties["dispatchId"] = payload.CallKey;
                log.Properties["response"] = resp.Content;
                log.Properties["status"] = resp.StatusCode;

                if (!resp.IsSuccessful)
                {
                    log.Level = LogLevel.Error;

                    if (resp.StatusCode == 0)
                        throw new Exception("Network Error, retry recommended.");
                    else
                        throw new Exception("Error invoking GOA endpoint:" + resp.StatusCode + "/" + resp.Content);
                }
            }
            catch (Exception y)
            {
                log.Level = LogLevel.Error;
                log.Exception = y;
                throw;
            }
            finally
            {
                logger.Log(log);
            }
        }

    }


    public sealed class AccessTokenPayload
    {
        [JsonProperty("access_token")]
        public string AccessToken { get; set; }

        [JsonProperty("instance_url")]
        public string InstanceUrl { get; set; }
        public string Id { get; set; }

        [JsonProperty("token_type")]
        public string TokenType { get; set; }

        [JsonProperty("issued_at")]
        public string IssuedAt { get; set; }
        public string Signature { get; set; }
    }
}
