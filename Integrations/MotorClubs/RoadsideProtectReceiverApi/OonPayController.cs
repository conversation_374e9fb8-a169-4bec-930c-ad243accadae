using NLog;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web.Http;
using Extric.Towbook.Integration.MotorClubs.Services;
using System;
using Newtonsoft.Json;
using Extric.Towbook.Integrations.MotorClubs.RoadsideProtect;
using Extric.Towbook.Accounts;
using Microsoft.AspNetCore.Mvc;
using System.IO;
using Extric.Towbook.WebShared;

namespace Extric.Towbook.API.Integration.MotorClubs.RoadsideProtect
{
    [Route("receivers/RoadsideProtect/pay")]
    public class OonPayController : ControllerBase
    {
        private static readonly Logger logger = LogManager.GetCurrentClassLogger();

        // POST /dispatch 
        [Route("")]
        [HttpPost]
        public async Task<object> Post()
        {
            var encoding = System.Text.Encoding.UTF8;
            string rawJson = "";
            using (StreamReader reader = new StreamReader(Request.Body, encoding))
                rawJson = await reader.ReadToEndAsync();

            var log = this.NewLogEvent("OonPaymentPost", rawJson);

            if (this.Request.Headers.HasAuthorization())
                log.Properties.Add("Authorization", this.Request.Headers.AuthorizationParameter());

            try
            {
                var dispatch = JsonConvert.DeserializeObject<OonPaymentModel>(rawJson);

                if (dispatch == null)
                    return this.ApiFailure("Missing required payment body");

                log.Properties["queueItemId"] = await this.SendToBackendService(
                    DigitalDispatchService.CallEventType.OonPayment,
                    null,
                    rawJson,
                    dispatch.DispatchId,
                    MasterAccountTypes.OonRoadsideProtect);
            }
            catch (Exception ex)
            {
                log.Properties["exception"] = ex;
                log.Level = LogLevel.Error;

                throw;
            }
            finally
            {
                logger.Log(log);
            }

            return Accepted();
        }
    }
}
