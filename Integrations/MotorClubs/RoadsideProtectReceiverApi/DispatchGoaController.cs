using Extric.Towbook.Integrations.MotorClubs.RoadsideProtect;
using Extric.Towbook.Integration.MotorClubs.Services;
using Newtonsoft.Json;
using NLog;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web.Http;
using Extric.Towbook.Utility;
using System;
using Microsoft.AspNetCore.Mvc;
using System.IO;
using Extric.Towbook.WebShared;

namespace Extric.Towbook.API.Integration.MotorClubs.RoadsideProtect
{
    [Route("receivers/RoadsideProtect/goa")]
    public class DispatchGoaController : ControllerBase
    {
        private static readonly Logger logger = LogManager.GetCurrentClassLogger();

        // POST /dispatch 
        [Route("")]
        [HttpPost]
        public async Task<object> Post()
        {
            var encoding = System.Text.Encoding.UTF8;
            string rawJson = "";
            using (StreamReader reader = new StreamReader(Request.Body, encoding))
                rawJson = await reader.ReadToEndAsync();

            var log = this.NewLogEvent("GoaPost", rawJson);
            
            if (this.Request.Headers.HasAuthorization())
                log.Properties.Add("Authorization", this.Request.Headers.AuthorizationParameter());

            try
            {
                var dispatch = JsonConvert.DeserializeObject<IncomingGoaModel>(rawJson);

                if (dispatch.ContractorId == null)
                {
                    log.Properties["warning"] = "Missing ContractorId in request payload - Looking up by DispatchId instead.";
                    var rpd = RoadsideProtectDispatch.GetByDispatchId(dispatch.DispatchId);
                    if (rpd != null)
                    {
                        var rpc = RoadsideProtectContractor.GetById(rpd.RoadsideProtectContractorId);
                        if (rpc != null)
                        {
                            dispatch.ContractorId = rpc.ContractorId;
                            rawJson = dispatch.ToJson();
                        }
                    }
                }

                var contractor = this.GetContractorById(dispatch.ContractorId);

                if (contractor == null)
                    return this.ApiFailure("Unknown ContractorId");

                log.Properties["queueItemId"] = await this.SendToBackendService(DigitalDispatchService.CallEventType.GoaResponse,
                    contractor,
                    rawJson,
                    dispatch.DispatchId);
            }
            catch (Exception e)
            {
                log.Properties["exception"] = e;
                log.Level = LogLevel.Error;
            }
            finally
            {
                logger.Log(log);
            }
            
            return Accepted();
        }
    }
}
