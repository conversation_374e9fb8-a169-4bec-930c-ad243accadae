using Extric.Towbook.Integrations.MotorClubs.RoadsideProtect;
using Extric.Towbook.Integration.MotorClubs.Services;
using Newtonsoft.Json;
using NLog;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web.Http;
using Microsoft.AspNetCore.Mvc;
using System.IO;
using Extric.Towbook.WebShared;

namespace Extric.Towbook.API.Integration.MotorClubs.RoadsideProtect
{
    [Route("receivers/RoadsideProtect/expired")]
    public class DispatchExpiredController : ControllerBase
    {
        private static readonly Logger logger = LogManager.GetCurrentClassLogger();


        // POST /dispatch
        [Route("")]
        [HttpPost]
        public async Task<object> Post()
        {
            var encoding = System.Text.Encoding.UTF8;
            string rawJson = "";
            using (StreamReader reader = new StreamReader(Request.Body, encoding))
                rawJson = await reader.ReadToEndAsync();

            var log = this.NewLogEvent("ExpiredPost", rawJson);
            
            if (this.Request.Headers.HasAuthorization())
                log.Properties.Add("Authorization", this.Request.Headers.AuthorizationParameter());

            try
            {
                var dispatch = JsonConvert.DeserializeObject<IncomingExpiredModel>(rawJson);

                var contractor = this.GetContractorById(dispatch.ContractorId);

                if (contractor == null)
                    return this.ApiFailure("Unknown ContractorId");

                log.Properties["queueItemId"] = await this.SendToBackendService(DigitalDispatchService.CallEventType.Expired,
                    contractor, 
                    rawJson, 
                    dispatch.DispatchId);
            }
            finally
            {
                logger.Log(log);
            }
            
            return Accepted();
        }
    }
}
