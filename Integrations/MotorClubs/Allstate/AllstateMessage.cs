using Extric.Towbook.Utility;
using System;
using System.Linq;

namespace Extric.Towbook.Integrations.MotorClubs.Allstate
{
    [Table("MCDispatch.AllstateMessages")]
    public class AllstateMessage
    {
        [Key]
        public long AllstateMessageId { get; set; }
        public string ContractorId { get; set; }
        public DDXmlMessageDirection Direction { get; set; }

        public string DispatchRequestNumber { get; set; }
        public string PurchaseOrderNumber { get; set; }
        public string Title { get; set; }
        public string Message { get; set; }
        public AllstateMessageType Type { get; set; }
        public int MasterAccountId { get; set; }

        public DateTime CreateDate
        {
            get; set;
        }

        public AllstateMessage() { }

        public AllstateMessage(
            int masterAccountId, 
            DDXmlMessageDirection direction)
        {
            MasterAccountId = masterAccountId;
            Direction = direction;
        }

        public void Save()
        {
            if (CreateDate == DateTime.MinValue)
                CreateDate = DateTime.Now;

            if (this.AllstateMessageId == 0)
                this.AllstateMessageId = SqlMapper.Insert(this);
            else
                SqlMapper.Update(this);
        }

        public static AllstateMessage GetLastByDispatchRequestNumber(string contractorId, string dispatchRequestNumber, int type)
        {
            return SqlMapper.Query<AllstateMessage>(
                "SELECT TOP 1 * FROM MCDispatch.AllstateMessages WITH (nolock) WHERE ContractorId=@ContractorId AND DispatchRequestNumber=@DispatchRequestNumber AND Type=@Type ORDER BY AllstateMessageId DESC",
                new
                {
                    ContractorId = contractorId,
                    DispatchRequestNumber = dispatchRequestNumber,
                    Type = type
                }).FirstOrDefault();
        }
    }

}
