using Newtonsoft.Json;
using System;
using System.Collections.Generic;

namespace Extric.Towbook.Integrations.MotorClubs.Allstate.Rest
{
    public class OutgoingPushRootObject
    {
        [JsonProperty("push")]
        public Push Push { get; set; } = new Push();
    }

    public class Push
    {
        [JsonProperty("apiVersion")]
        public string ApiVersion { get; set; } = "1.0";
        [JsonProperty("partnerId")]
        public string PartnerId { get; set; } = "PR1078";
        [JsonProperty("channelId")]
        public string ChannelId { get; set; } = "CH1052";
        [JsonProperty("sentTime")]
        public DateTime SentTime { get; set; } = DateTime.Now.ToUniversalTime();
        [JsonProperty("pushGPSEvents")]
        public PushGpsEvents PushGPSEvents { get; set; } = new PushGpsEvents();
    }

    public class PushGpsEvents
    {
        [JsonProperty("pushGPSEvent")]
        public List<PushGpsEvent> PushGPSEvent { get; set; } = new List<PushGpsEvent>();
    }

    public class PushEventType
    {
        /// <summary>
        /// Driver is assigned and en route
        /// </summary>
        public const string ETA_EVENT = "ETA_EVENT";

        /// <summary>
        /// Driver is 10 minutes or less away from client location
        /// </summary>
        public const string TEN_MIN_EVENT = "10_MIN_EVENT";

        /// <summary>
        /// Driver will miss ETA, condition is known with up to 5 minutes prior to ETA expiration
        /// </summary>
        public const string SVC_DELAY = "SVC_DELAY";

        /// <summary>
        /// Is the time in minutes that will be updated if the original ETA will not be met by the Provider
        /// </summary>
        public const string NEW_ETA_EVENT = "NEW_ETA_EVENT";

        /// <summary>
        /// Driver has arrived at customer’s location
        /// </summary>
        public const string ON_SCENE = "ON_SCENE";

        /// <summary>
        /// Driver can’t find vehicle or driver of vehicle, this is a terminating event
        /// </summary>
        public const string GOA_EVENT = "GOA_EVENT";

        /// <summary>
        /// Service failed (i.e. jump start not working), this is a terminating event
        /// </summary>
        public const string SVC_FAIL = "SVC_FAIL";

        /// <summary>
        /// Provider has cancelled the service and gives it back to ARS for fulfillment
        /// </summary>
        public const string PROVIDER_CANCEL = "PROVIDER_CANCEL";

        /// <summary>
        /// ARS cancels call, typically because customer no longer needs service, this is a terminating event.
        /// </summary>
        public const string MotorClubCancel = "MOTOR_CLUB_CANCEL";

        /// <summary>
        /// Service is completed as indicated by the driver leaving the client location, this is a terminating event
        /// </summary>
        public const string ServiceComplete = "SVC_COMPLETE";

        /// <summary>
        /// This event is used when provider sends updated location periodically
        /// </summary>
        public const string EtaUpdate = "ETA_UPDATE";

        /// <summary>
        /// Used to report a service failure
        /// </summary>
        public const string ServiceFailure = "SVC_FAIL";

        /// <summary>
        /// Used to report a service failure
        /// </summary>
        public const string ProviderCancel = "PROVIDER_CANCEL";

        public const string ExchangeOnScene = "ON_SCENE_EXCHG";
        public const string ExchangeLoaded = "LOADED_EXCHG";
        public const string ExchangeApproaching10Mins = "10_MIN_EVENT_EXCHG";

        public const string RentalDisablementEnroute = "ETA_EVENT_DISBLE";
        public const string RentalDisablementOnScene = "ON_SCENE_RNTL_DSBLE";
        public const string RentalDisablementLoaded = "LOADED";


        /// <summary>
        /// Applies to TowStorageTow, Tow with Passenger Ride Along, Rental Two Way Tow 
        /// </summary>
        public const string DestinationArrival = "DROP_OFF";

        /// <summary>
        /// Dropped Passenger as Passenger Destination Location
        /// Applies to: Tow With Passenger Ride Along
        /// </summary>
        public const string RentalDisablementPassengerDropped = "DROP_PASSG";

        /// <summary>
        /// Droppeed vehicle at storage location
        /// </summary>
        public const string StorageStored = "VEH_STORAGE";

        /// <summary>
        /// Vehicle released from storage location
        /// Applies to: Tow - Storage - Tow
        /// </summary>
        public const string StorageReleased = "VEH_RELEASE";

        public const string StorageTowEnroute = "ETA_EVENT_DROP";

        public const string SignatureUpload = "S1043";
        public const string PhotoUploadPickup = "S1043";
        public const string PhotoUploadDestination = "S1043";

    }

    public sealed class PushGpsEvent
    {
        [JsonProperty("eventType")]
        public string EventType { get; set; }
        [JsonProperty("eventTime")]
        public DateTime EventTime { get; set; } = DateTime.Now.ToUniversalTime();
        [JsonProperty("lastUpdatedInterval")]
        public int LastUpdatedInterval { get; set; }
        [JsonProperty("responseId")]
        public string ResponseId { get; set; }
        [JsonProperty("authorizationNumber")]
        public string AuthorizationNumber { get; set; }
        [JsonProperty("latitude")]
        public decimal Latitude { get; set; }
        [JsonProperty("longitude")]
        public decimal Longitude { get; set; }
        [JsonProperty("eta")]
        public int Eta { get; set; }
        [JsonProperty("reasonCode")]
        public string ReasonCode { get; set; }
        [JsonProperty("additionalInfo")]
        public string AdditionalInfo { get; set; }
        [JsonProperty("contactName")]
        public string ContactName { get; set; }
        [JsonProperty("customerContacted")]
        public string CustomerContacted { get; set; }
        [JsonProperty("NameValuePair")]
        public NameValuePair[] NameValuePair { get; set; } = null;

        [JsonProperty("driverName")]
        public string DriverName { get; set; }
        
        [JsonProperty("driverMobileOS")]
        public string DriverMobileOS { get; set; }

        [JsonProperty("driverEquipmentType")]
        public string DriverEquipmentType { get; set; }
    }

    public class NameValuePair
    {
        [JsonProperty("Name")]
        public string Name { get; set; }
        [JsonProperty("Value")]
        public string Value { get; set; }
    }
}
