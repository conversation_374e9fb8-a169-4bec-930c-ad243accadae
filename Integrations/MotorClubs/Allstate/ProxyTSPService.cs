//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.0
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------



[System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
[System.ServiceModel.ServiceContractAttribute(Namespace = "http://proxy.dd.pmg.consfin.ge.com", ConfigurationName = "ProxyTSP")]
public interface ProxyTSP
{

    // CODEGEN: Generating message contract since the operation receiveXMLMessage is neither RPC nor document wrapped.
    [System.ServiceModel.OperationContractAttribute(Action = "", ReplyAction = "*")]
    [System.ServiceModel.XmlSerializerFormatAttribute()]
    receiveXMLMessageResponse receiveXMLMessage(receiveXMLMessageRequest request);

    [System.ServiceModel.OperationContractAttribute(Action = "", ReplyAction = "*")]
    System.Threading.Tasks.Task<receiveXMLMessageResponse> receiveXMLMessageAsync(receiveXMLMessageRequest request);
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("svcutil", "4.0.30319.18020")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://proxy.dd.pmg.consfin.ge.com")]
public partial class receiveCredentials
{

    private string userNameField;

    private string passwordField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Form = System.Xml.Schema.XmlSchemaForm.Unqualified, Order = 0)]
    public string UserName
    {
        get
        {
            return this.userNameField;
        }
        set
        {
            this.userNameField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Form = System.Xml.Schema.XmlSchemaForm.Unqualified, Order = 1)]
    public string Password
    {
        get
        {
            return this.passwordField;
        }
        set
        {
            this.passwordField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("svcutil", "4.0.30319.18020")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://proxy.dd.pmg.consfin.ge.com")]
public partial class receiveXMLMessage
{

    private string psXMLMessageField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Form = System.Xml.Schema.XmlSchemaForm.Unqualified, Order = 0)]
    public string PsXMLMessage
    {
        get
        {
            return this.psXMLMessageField;
        }
        set
        {
            this.psXMLMessageField = value;
        }
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(IsWrapped = false)]
public partial class receiveXMLMessageRequest
{

    [System.ServiceModel.MessageHeaderAttribute(Namespace = "http://proxy.dd.pmg.consfin.ge.com")]
    public receiveCredentials receiveCredentials;

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace = "http://proxy.dd.pmg.consfin.ge.com", Order = 0)]
    public receiveXMLMessage receiveXMLMessage;

    public receiveXMLMessageRequest()
    {
    }

    public receiveXMLMessageRequest(receiveCredentials receiveCredentials, receiveXMLMessage receiveXMLMessage)
    {
        this.receiveCredentials = receiveCredentials;
        this.receiveXMLMessage = receiveXMLMessage;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(WrapperName = "receiveXMLMessageResponse", WrapperNamespace = "http://proxy.dd.pmg.consfin.ge.com", IsWrapped = true)]
public partial class receiveXMLMessageResponse
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace = "http://proxy.dd.pmg.consfin.ge.com", Order = 0)]
    [System.Xml.Serialization.XmlElementAttribute(Form = System.Xml.Schema.XmlSchemaForm.Unqualified)]
    public string receiveXMLMessageReturn;

    public receiveXMLMessageResponse()
    {
    }

    public receiveXMLMessageResponse(string receiveXMLMessageReturn)
    {
        this.receiveXMLMessageReturn = receiveXMLMessageReturn;
    }
}

[System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
public interface ProxyTSPChannel : ProxyTSP, System.ServiceModel.IClientChannel
{
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
public partial class ProxyTSPClient : System.ServiceModel.ClientBase<ProxyTSP>, ProxyTSP
{

    public ProxyTSPClient()
    {
    }

    public ProxyTSPClient(string endpointConfigurationName) :
        base(endpointConfigurationName)
    {
    }

    public ProxyTSPClient(string endpointConfigurationName, string remoteAddress) :
        base(endpointConfigurationName, remoteAddress)
    {
    }

    public ProxyTSPClient(string endpointConfigurationName, System.ServiceModel.EndpointAddress remoteAddress) :
        base(endpointConfigurationName, remoteAddress)
    {
    }

    public ProxyTSPClient(System.ServiceModel.Channels.Binding binding, System.ServiceModel.EndpointAddress remoteAddress) :
        base(binding, remoteAddress)
    {
    }

    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    receiveXMLMessageResponse ProxyTSP.receiveXMLMessage(receiveXMLMessageRequest request)
    {
        return base.Channel.receiveXMLMessage(request);
    }

    public string receiveXMLMessage(receiveCredentials receiveCredentials, receiveXMLMessage receiveXMLMessage1)
    {
        receiveXMLMessageRequest inValue = new receiveXMLMessageRequest();
        inValue.receiveCredentials = receiveCredentials;
        inValue.receiveXMLMessage = receiveXMLMessage1;
        receiveXMLMessageResponse retVal = ((ProxyTSP)(this)).receiveXMLMessage(inValue);
        return retVal.receiveXMLMessageReturn;
    }

    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    System.Threading.Tasks.Task<receiveXMLMessageResponse> ProxyTSP.receiveXMLMessageAsync(receiveXMLMessageRequest request)
    {
        return base.Channel.receiveXMLMessageAsync(request);
    }

    public System.Threading.Tasks.Task<receiveXMLMessageResponse> receiveXMLMessageAsync(receiveCredentials receiveCredentials, receiveXMLMessage receiveXMLMessage)
    {
        receiveXMLMessageRequest inValue = new receiveXMLMessageRequest();
        inValue.receiveCredentials = receiveCredentials;
        inValue.receiveXMLMessage = receiveXMLMessage;
        return ((ProxyTSP)(this)).receiveXMLMessageAsync(inValue);
    }
}
