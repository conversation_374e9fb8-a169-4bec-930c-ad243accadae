using Extric.Towbook.Dispatch;
using Extric.Towbook.Utility;
using ProtoBuf;
using StackExchange.Redis;
using System;
using System.Collections.ObjectModel;
using System.Linq;

namespace Extric.Towbook.Integrations.MotorClubs.Allstate
{
    public enum AllstateContractorLoginStatus
    {
        None = 0,
        LoggedOut = 1,
        LoggingIn = 2,
        LoggedIn = 3,
        LoggingOut = 4
    }

    [Table("MCDispatch.AllstateContractors")]
    [ProtoContract(ImplicitFields = ImplicitFields.AllFields, Name = "Contractor")]
    [CacheKey("_ddxmlContractor")]
    
    
    public class AllstateContractor : IDigitalContractor
    {
        [Key]
        public int AllstateContractorId { get; set; }
        public int MasterAccountId { get; set; }
        [PartitionKey]
        public int CompanyId { get; set; }
        public string ContractorId { get; set; }

        /// <summary>
        /// Whether the provider *wants* to be logged in. If this is True, it doesnt mean they are actually logged in,
        /// but it means the implementation should log the user back in if LoginStatus isnt set to LoggedIn.
        /// 
        /// If this is false, auto-login code should not log the contractor back in.
        /// </summary>
        public bool IsLoggedIn { get; set; }
        public DateTime? LastLoginDate { get; set; }
        public int AccountId { get; set; }
        public AllstateContractorLoginStatus LoginStatus { get; set; }
        public bool IsDeleted { get; set; }
        public string LocationCode { get; set; }
        public bool? IsValidated { get; set; }

        public DateTime CreateDate { get; set; }

        public AllstateContractor()
        {

        }

        public AllstateContractor(int masterAccountId, int companyid, int accountId, string contractorId)
        {
            MasterAccountId = masterAccountId;
            CompanyId = companyid;
            AccountId = accountId;
            ContractorId = contractorId;
            IsLoggedIn = false;
            LoginStatus = AllstateContractorLoginStatus.LoggedOut;
        }

        public static AllstateContractor GetByContractorId(string contractorId)
        {
            return SqlMapper.Query<AllstateContractor>(
                "SELECT * FROM MCDispatch.AllstateContractors WHERE ContractorId = @ContractorId AND IsDeleted=0",
                new { @ContractorId = contractorId }).FirstOrDefault();
        }

        public static AllstateContractor GetById(int id)
        {
            if (id < 1)
                return null;

            return SqlMapper.Query<AllstateContractor>(
                "SELECT * FROM MCDispatch.AllstateContractors WHERE AllstateContractorId = @AllstateContractorId AND IsDeleted=0",
                new { AllstateContractorId = id }).FirstOrDefault();
        }

        public static void ResetCache(string contractorId, int masterAccountId)
        {
            if (masterAccountId == 0 || contractorId == null)
                return;

            Cache.Instance.HashDelete("_ddxmlContractors", $"{masterAccountId}:{contractorId.ToUpperInvariant()}");
        }

        public static AllstateContractor GetByContractorId(string contractorId, int masterAccountId) => 
            GetByContractorId(contractorId, masterAccountId, false);
        public static AllstateContractor GetByContractorId(string contractorId, int masterAccountId, bool unrestricted)
        {
            AllstateContractor rx;

            try
            {
                rx = Cache.Instance.HashGet<AllstateContractor>("_ddxmlContractors", $"{masterAccountId}:{contractorId.ToUpperInvariant()}");

                if (rx != null && rx.LocationCode == null)
                {

                    // sometimes a copy gets cached where IsDeleted=true. this should NOT be returned.
                    if (!rx.IsDeleted)
                        return rx;
                }
            }
            catch (RedisTimeoutException)
            {
                // silently ignore redis errors, don't break the allstate integration if redis times out.
            }

            rx = SqlMapper.Query<AllstateContractor>(
                "SELECT * FROM MCDispatch.AllstateContractors WHERE ContractorId = @ContractorId AND MasterAccountId=@MasterAccountId AND IsDeleted=0" + 
                (unrestricted ? "" : " AND LocationCode IS NULL"),
                new { @ContractorId = contractorId, MasterAccountId = masterAccountId }).FirstOrDefault();

            if (rx != null)
                Cache.Instance.HashSet("_ddxmlContractors", $"{masterAccountId}:{contractorId.ToUpperInvariant()}", rx);

            return rx;
        }

        public static AllstateContractor GetByContractorId(string contractorId, int masterAccountId, string locationCode)
        {
            if (locationCode == null)
                return GetByContractorId(contractorId, masterAccountId);

            var rx = Cache.Instance.HashGet<AllstateContractor>("_ddxmlContractors", $"{masterAccountId}:{contractorId.ToUpperInvariant()}:{locationCode}");

            if (rx != null && !rx.IsDeleted)
                return rx;

            rx = SqlMapper.Query<AllstateContractor>(
                "SELECT * FROM MCDispatch.AllstateContractors WHERE ContractorId = @ContractorId AND MasterAccountId=@MasterAccountId AND IsDeleted=0 AND LocationCode=@lc",
                new { @ContractorId = contractorId, MasterAccountId = masterAccountId, lc = locationCode }).FirstOrDefault();

            if (rx != null)
                Cache.Instance.HashSet("_ddxmlContractors", $"{masterAccountId}:{contractorId.ToUpperInvariant()}", rx);

            return rx;
        }

        public static Collection<AllstateContractor> GetByAccountId(int accountId)
        {
            return SqlMapper.Query<AllstateContractor>(
                "SELECT * FROM MCDispatch.AllstateContractors WHERE AccountId = @AccountId AND IsDeleted=0",
                new { AccountId = accountId }).ToCollection();
        }


        public static Collection<AllstateContractor> GetByCompanyId(int companyId)
        {
            return SqlMapper.Query<AllstateContractor>(
                "SELECT * FROM MCDispatch.AllstateContractors WHERE CompanyId = @CompanyId AND IsDeleted=0",
                new { CompanyId = companyId }).ToCollection();
        }

        public static Collection<AllstateContractor> GetByMasterAccountId(int masterAccountId)
        {
            return SqlMapper.Query<AllstateContractor>(
                "SELECT * FROM MCDispatch.AllstateContractors WHERE MasterAccountId=@MasterAccountId",
                new { MasterAccountId = masterAccountId }).ToCollection();
        }

        public void Save()
        {
            if (CreateDate == DateTime.MinValue)
                CreateDate = DateTime.Now;

            if (string.IsNullOrWhiteSpace(ContractorId))
                throw new TowbookException("ContractorID must be specified before saving a DD Contractor object.");

            // contractors should always be uppercase.
            ContractorId = ContractorId.ToUpperInvariant().Trim();

            if (AllstateContractorId == 0)
                AllstateContractorId = Convert.ToInt32(SqlMapper.Insert(this));
            else
                SqlMapper.Update(this);

            if (!IsDeleted)
                Cache.Instance.HashSet("_ddxmlContractors", $"{MasterAccountId}:{ContractorId.ToUpperInvariant()}", this);
        }

        public void UpdateLoginStatus(AllstateContractorLoginStatus newStatus)
        {
            LastLoginDate = DateTime.Now;
            LoginStatus = newStatus;

            Save();
        }

        public void Delete()
        {
            Cache.Instance.HashDelete("_ddxmlContractors", $"{MasterAccountId}:{ContractorId.ToUpperInvariant()}");
            
            IsDeleted = true;
            Save();
        }
    }
}
