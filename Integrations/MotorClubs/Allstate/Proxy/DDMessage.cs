namespace Extric.Towbook.Integrations.MotorClubs.Allstate.Proxy
{
    using Extric.Towbook.Integrations.MotorClubs.Allstate.Rest;
    using Newtonsoft.Json;
    using System;
    using System.Collections.Generic;
    using System.Collections.ObjectModel;
    using System.IO;
    using System.Xml;
    using System.Xml.Serialization;

    public class UserDefinedNameValue
    {
        public string Name { get; set;}
        public string Value { get; set;}

        public UserDefinedNameValue()
        {

        }
        public UserDefinedNameValue(string name, string value)
        {
            Name = name;
            Value = value;
        }
    }

    public class DDMessage : DDMessageNode
    {
        [XmlAttribute("Version")]
        public string Version = "1.0";
        public DDMessageHeader DDMessageHeader { get; set; }
        [XmlArrayItem("Data")]
        public Collection<UserDefinedNameValue> UserDefined { get; set; }

        public DDMessageContent DDContent { get; set; }

        public DDMessage()
        {

        }

        public static DDMessage FromDspJson(string json)
        {
            var dsp = JsonConvert.DeserializeObject<DspContainer>(json).DspMessage ?? 
                JsonConvert.DeserializeObject<DSPMessageBody>(json);

            dsp.PassengerInfo = dsp.JobInfo.PassengerInfo;

            return new DDMessage()
            {
                DDContent = dsp,
                DDMessageHeader = new DDMessageHeader()
                {
                    Key = dsp.Key,
                    ContractorID = dsp.ContractorId,
                    ResponseID = dsp.ResponseId,
                    TransType = "DSP",
                    ResponseType = ""
                }
            };
        }


        public static DDMessage FromCnlJson(string json)
        {
            var cnl = JsonConvert.DeserializeObject<CnlContainer>(json).CnlMessage;

            return new DDMessage()
            {
                DDContent = cnl,
                DDMessageHeader = new DDMessageHeader()
                {
                    Key = cnl.Key,
                    ContractorID = cnl.ContractorId,
                    ResponseID = cnl.ResponseId,
                    TransType = "CNL",
                    ResponseType = ""
                }
            };
        }



        public static DDMessage FromGoaJson(string json)
        {
            var goa = JsonConvert.DeserializeObject<GoaContainer>(json).GoaMessage;

            return new DDMessage()
            {
                DDContent = goa,
                DDMessageHeader = new DDMessageHeader()
                {
                    Key = goa.Key,
                    ContractorID = goa.ContractorId,
                    ResponseID = goa.ResponseId,
                    TransType = "CNL",
                    ResponseType = ""
                }
            };
        }


        public static DDMessage FromRspJson(string json)
        {
            var rsp = JsonConvert.DeserializeObject<RspContainer>(json).RspMessage;

            rsp.DispatchStatus.VehicleReferenceUrl = rsp?.VehicleInfo?.ReferenceURL;
            rsp.DispatchStatus.MemberCallBackNum = rsp?.CallerInfo?.CallbackNumber;

            return new DDMessage()
            {
                DDContent = rsp.DispatchStatus,
                DDMessageHeader = new DDMessageHeader()
                {
                    ContractorID = rsp.ContractorId,
                    ResponseID = rsp.ResponseId,
                    Key = rsp.Key, 
                    TransType = "RSP",
                    ResponseType = ""
                }
            };
        }

        /// <summary>
        /// Initializes a new DDMessage based on the type (DSP, LOG, CNL, etc)
        /// </summary>
        /// <param name="messageType"></param>
        public DDMessage(string messageType)
        {
            this.DDMessageHeader = new DDMessageHeader();
            this.DDMessageHeader.TransType = messageType;

            switch (messageType)
            {
                case "LOG":
                    this.DDContent = new LOGMessage();
                    break;

                case "LOF":
                    this.DDContent = new LOFMessage();
                    break;

                case "CNL":
                    this.DDContent = new CNLMessage();
                    break;

                case "RET":
                    this.DDContent = new RETMessage();
                    break;

                case "DSP":
                    this.DDContent = new DSPMessageBody();
                    break;

                case "RSP":
                    this.DDContent = new RSPMessage();
                    break;

                case "GOA":
                    this.DDContent = new GOAMessage();
                    break;
            }

        }

        public string GetXml()
        {
            string type;

            if (DDMessageHeader.TransType == "DSP")
                type = DDMessageHeader.TransType + "MessageBody";
            else
                type = DDMessageHeader.TransType + "Message";

            return base.GetXml(type).TrimEnd('\0').Trim();
        }

        public XmlElement GetXmlElement()
        {
            string type;

            if (DDMessageHeader.TransType == "DSP")
                type = DDMessageHeader.TransType + "MessageBody";
            else
                type = DDMessageHeader.TransType + "Message";

            return base.GetXmlElement(type);
        }

        private static Dictionary<string, Type> TypeMap { get; } = new Dictionary<string, Type>()
        {
            ["DSP"] = typeof(DSPMessageBody),
            ["RSP"] = typeof(RSPMessage),
            ["CNL"] = typeof(CNLMessage),
            ["ACK"] = typeof(ACKMessage),
            ["ERR"] = typeof(ERRMessage),

            ["LOG"] = typeof(LOGMessage),
            ["LOF"] = typeof(LOFMessage),
            ["RET"] = typeof(RETMessage),
            ["DSI"] = typeof(DSIMessage),
            ["DSR"] = typeof(DSRMessage),
            ["GOA"] = typeof(GOAMessage),
        };

        public static DDMessage FromXml(string xml, Type messageType = null)
        {
            if (messageType == null)
            {
                XmlDocument doc = new XmlDocument();
                doc.LoadXml(xml);
                var tt = doc.SelectSingleNode("/DDMessage/DDMessageHeader/TransType");

                if (tt == null)
                    return null;

                messageType = TypeMap[tt.InnerText];
            }

            if (xml.StartsWith("{"))
            {
                if (messageType.Name == "DSPMessageBody")
                {
                    return FromDspJson(xml);
                }
                else if (messageType.Name == "ERRMessage")
                {
                    return FromErrJson(xml);
                    throw new NotImplementedException("ERRMessage not handled");
                }
                else if (messageType.Name == "RSPMessage")
                {
                    return FromRspJson(xml);
                }
                else if (messageType.Name == "CNLMessage")
                {
                    return FromCnlJson(xml);
                }
                else if (messageType.Name == "GOAMessage")
                {
                    return FromGoaJson(xml);
                }
            }


            var customContentName = messageType?.Name;

            var obj = new XmlSerializer(typeof(DDMessage)).Deserialize(new StringReader(xml)) as DDMessage;

            if (obj != null)
            {
                if (obj.DDContent == null)
                {
                    using (var xmlReader = XmlReader.Create(new StringReader(xml)))
                    {

                        xmlReader.ReadToDescendant(customContentName);

                        var objContent = new XmlSerializer(messageType).Deserialize(xmlReader.ReadSubtree()) as DDMessageContent;
                        obj.DDContent = objContent;
                    }
                }
            }

            return obj;
        }

        private static DDMessage FromErrJson(string json)
        {
            var err = JsonConvert.DeserializeObject<ErrContainer>(json).ErrMessage;

            return new DDMessage()
            {
                DDContent = err,
                DDMessageHeader = new DDMessageHeader()
                {
                    Key = "ALLSTATE",
                    TransType = "ERR",
                    ResponseType = ""
                }
            };
        }

        public static T FromContent<T>(string callXml) where T : class
        {
            return FromXml(callXml, typeof(T)).DDContent as T;
        }
    }
}
