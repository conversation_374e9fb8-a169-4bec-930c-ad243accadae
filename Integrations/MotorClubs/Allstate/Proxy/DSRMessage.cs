namespace Extric.Towbook.Integrations.MotorClubs.Allstate.Proxy
{
    /// <summary>
    /// Dispatch Status Response - From Towbook to the Motor Club.
    /// </summary>
    /// <remarks>
    /// Towbook implemented this message type in collaboration with Quest. This isn't part of the
    /// standard DDXML spec, but we're trying to get other motor clubs to implement it also.
    /// </remarks>
    public class DSRMessage : DDMessageContent
    {
        public string JobID { get; set; }
        public string Status { get; set; }
        public string Lat { get; set; }
        public string Lon { get; set; }
        public string Timestamp { get; set; }
        public string ResourceId { get; set; }
        public string ResourceName { get; set; }
        public string Source { get; set; }
        public string Accuracy { get; set; }
        public string AccuracyUnit { get; set; }
        public string ResponseType { get; set; }
        
    }
}