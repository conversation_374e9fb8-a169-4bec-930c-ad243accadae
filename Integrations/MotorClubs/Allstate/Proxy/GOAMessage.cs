namespace Extric.Towbook.Integrations.MotorClubs.Allstate.Proxy
{
    /// <summary>
    /// Dispatch Status Response - From Towbook to the Motor Club.
    /// </summary>
    /// <remarks>
    /// Towbook implemented this message type in collaboration with Quest. This isn't part of the
    /// standard DDXML spec, but we're trying to get other motor clubs to implement it also.
    /// </remarks>
    public class GOAMessage : DDMessageContent
    {
        public string Key { get; set; }
        public string ContractorId { get; set; }
        public string ResponseId { get; set; }


        // Quest
        public string Response { get; set; }

        // Quest Specific
        public string ResponseReason { get; set; }

        /// <summary>
        /// Allstate specific
        /// </summary>
        public string Comments { get; set; }
    }
}