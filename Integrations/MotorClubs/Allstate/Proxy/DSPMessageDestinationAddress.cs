namespace Extric.Towbook.Integrations.MotorClubs.Allstate.Proxy
{
    using Extric.Towbook.Dispatch;
    using Newtonsoft.Json;
    using System;
    using System.Threading.Tasks;
    using System.Xml.Serialization;

    public class DSPMessageDestinationAddress
    {
        /// <summary>
        /// Used to determine what order the addresses should be traveled to.
        /// </summary>
        [XmlElement(ElementName = "orderNo")]
        public string OrderNo { get; set; }
        [XmlIgnore]
        [JsonIgnore]
        public int OrderNoAsInt
        {
            get
            {
                if (int.TryParse(OrderNo, out int parsed))
                    return parsed;
                else
                    return 0;
            }
        }
        public string LocInfo { get; set; }
        public string Addr1 { get; set; }
        public string Addr2 { get; set; }
        public string City { get; set; }
        public string State { get; set; }
        public string Zip { get; set; }
        public string Lat { get; set; }
        public string Lon { get; set; }
        public string BusinessName { get; set; }
        [JsonProperty("phone")]
        public string BusinessPhone { get; set; }
        public string BusinessContactName { get; set; }
        public string CrsStr1 { get; set; }
        public string CrsStr2 { get; set; }

        /// <summary>
        /// Returns Address, City State Zip
        /// </summary>
        /// <returns></returns>
        public override string ToString()
        {
            return (Addr1 +
                (!string.IsNullOrWhiteSpace(Addr2) ? ", " + Addr2 : "") +
                (!string.IsNullOrWhiteSpace(Addr1) || !string.IsNullOrWhiteSpace(Addr2) ? ", " : "") +
                City + " " +
                State + " " +
                Zip + 
                (!string.IsNullOrWhiteSpace(LocInfo) ? " (" + LocInfo + ")" : "") + 
                (!string.IsNullOrWhiteSpace(BusinessName) ? " (" + BusinessName +")" : ""))
                .Trim().Trim(',');
        }

        /// <summary>
        /// Fills in the passed EntryWaypoint with the relevant info from this object.
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<EntryWaypoint> ToWaypoint(EntryWaypoint input = null)
        {
            if (input == null)
                input = new EntryWaypoint();

            input.Address = this.ToString();

            if (Lat != null && Lon != null && 
                (decimal.TryParse(Lat, out var latitude) && 
                decimal.TryParse(Lon, out var longitude)))
            {
                input.Latitude = latitude;
                input.Longitude = longitude;
            }

            input.Notes = this.LocInfo;
            // TODO: Cross Street, Landmark

            // if lat/long didn't come over from motor club, get it from google
            if ((input.Latitude == 0 || input.Longitude == 0) && !string.IsNullOrEmpty(Addr1) && !string.IsNullOrEmpty(City))
            {
                var geo = await Extric.Towbook.Utility.GeocodeHelper.Geocode(input.Address);

                if (geo != null)
                {
                    input.Latitude = geo.Latitude;
                    input.Longitude = geo.Longitude;
                }
            }

            if (this.OrderNoAsInt != 0)
                input.Position = this.OrderNoAsInt;

            return input;
        }
    }
}
