namespace Extric.Towbook.Integrations.MotorClubs.Allstate.Proxy
{
    using System.IO;
    using System.Text;
    using System.Xml;
    using System.Xml.Schema;

    public class AllstateTextWriter : XmlTextWriter
    {
        public AllstateTextWriter(TextWriter w) : base(w) { }
        public AllstateTextWriter(Stream w, Encoding encoding) : base(w, encoding) { }
        public AllstateTextWriter(string filename, Encoding encoding) : base(filename, encoding) { }

        bool _skip = false;

        public override void WriteStartAttribute(string prefix, string localName, string ns)
        {

            if ((prefix == "xmlns" && (localName == "xsd" || localName == "xsi")) ||  // Omits XSD and XSI declarations.
              ns == XmlSchema.InstanceNamespace)                                      // Omits all XSI attributes.
            {
                _skip = true;
                return;
            }
            base.WriteStartAttribute(prefix, localName, ns);
        }

        public override void WriteString(string text)
        {
            if (_skip) return;
            base.WriteString(text);
        }

        public override void WriteEndAttribute()
        {
            if (_skip)
            {
                _skip = false;
                return;
            }
            base.WriteEndAttribute();
        }

        public override void WriteEndElement()
        {
            base.WriteFullEndElement();
        }
    }
}
