namespace Extric.Towbook.Integrations.MotorClubs.Allstate.Proxy
{
    using Newtonsoft.Json;
    using static Vehicle.VehicleUtility;

    public class DSPMessageVehicleInfo
    {
        public string Year { get; set; }
        public string Color { get; set; }
        public string Make { get; set; }
        public string Model { get; set; }        
        [JsonProperty("license")]
        public string Lic { get; set; }
        public string State { get; set; }
        public string VIN { get; set; }
        /// <summary>
        /// The body type of the vehicle; Light, Heavy, etc
        /// </summary>
        public string VehicleType { get; set; }
        public string AdditionalInfo { get; set; }
        public string FuelType { get; set; }
        public string Odometer { get; set; }
        public string GrossWeightNumber { get; set; }
        public string VehicleHeightNbr { get; set; }
        public string VehicleLengthNbr { get; set; }
        
        public string UpfitInd { get; set; }
        public string UpfitType { get; set; }
        
        public string EngType { get; set; }
        public string EngnFuelType { get; set; }
        public string TrailerWt { get; set; }
        public string TrailerCont { get; set; }
        public string CarryngLoadInd { get; set; }
        public string RecreatnlVehclType { get; set; }
        public string PayloadDesc { get; set; }
        public string VehicleNoseInInd { get; set; }

        
        /// <summary>
        /// Returns a a human friendly representation of the vehicle. 
        /// Example: 2006 Acura TL Black
        /// </summary>
        /// <returns></returns>
        public override string ToString()
        {
            return $"{Year} {GetManufacturerByName(Make)} {GetModelByName(Model)} {Color}".Trim();
        }
    }
}