namespace Extric.Towbook.Integrations.MotorClubs.Allstate
{
    public enum AllstateMessageType
    {
        Unrecognized = 0,
        DSP = 1,
        RSP = 2,
        CNL = 3,
        ACK = 4,
        LOG = 5,
        LOF = 6,
        RET = 7,
        DSI = 8,
        DSR = 9,
        GOA = 10,
        PCD = 11,
        OONPay = 12
    }

    public enum DDXmlMessageDirection
    {
        /// <summary>
        /// Direction not specified
        /// </summary>
        None = 0,

        /// <summary>
        /// Message being sent to a third party (Allstate, for instance)
        /// </summary>
        Outgoing = 1,

        /// <summary>
        /// Message being received from a third party such as Allstate or Quest.
        /// </summary>
        Incoming = 2,
    }
}
