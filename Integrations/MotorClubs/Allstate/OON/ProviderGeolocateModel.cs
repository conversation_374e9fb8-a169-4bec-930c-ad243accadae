using Extric.Towbook.Utility;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Extric.Towbook.Integrations.MotorClubs.Allstate.OON
{
    public class ProviderGeolocateModel
    {
        public decimal Latitude { get; set; }
        public decimal Longitude { get; set; }
        public decimal Distance { get; set; }
        public string Name { get; set; }
        public string Address { get; set; }
        public string Phone { get; set; }
        public string Fax { get; set; }
        public string City { get; set; }
        public string State { get; set; }
        public string Zip { get; set; }
        public int CompanyId { get; set; }
        public int AccountId { get; set; }

        public static ProviderGeolocateModel GetByCompanyId(int companyId, decimal lat, decimal lon)
        {
            return SqlMapper.QuerySP<ProviderGeolocateModel>(
                "MCDispatch.OONAllstateProvidersGetByGeolocationWithCompanyId", 
                new
                {
                    CompanyId = companyId,
                    SLAT = lat,
                    SLON = lon
                }).FirstOrDefault();
        }
    }

}
