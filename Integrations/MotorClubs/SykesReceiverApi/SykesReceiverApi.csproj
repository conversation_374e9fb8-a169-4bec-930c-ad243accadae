<?xml version="1.0" encoding="utf-8"?>
<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<RootNamespace>Extric.Towbook.API.Integration.MotorClubs.Sykes</RootNamespace>
		<AssemblyName>Extric.Towbook.API.Integration.MotorClubs.Sykes</AssemblyName>
		<OutputType>Library</OutputType>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.CSharp" Version="4.7.0" />
		<PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.21.0" />
		<PackageReference Include="NLog" Version="5.3.3" />
		<PackageReference Include="Swashbuckle.AspNetCore" Version="6.7.1" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\..\..\Extric.Towbook.Integration.MotorClubs\Extric.Towbook.Integration.MotorClubs.csproj" />
		<ProjectReference Include="..\..\..\Extric.Towbook.WebShared.Net5\Extric.Towbook.WebShared.Net5.csproj" />
		<ProjectReference Include="..\Sykes\Sykes.csproj" />
	</ItemGroup>
</Project>