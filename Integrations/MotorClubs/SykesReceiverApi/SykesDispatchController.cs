using Extric.Towbook.Accounts;
using Extric.Towbook.Integration.MotorClubs.Services;
using Extric.Towbook.Integrations.MotorClubs.Sykes;
using Extric.Towbook.Utility;
using NLog;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;


namespace Extric.Towbook.API.Integration.MotorClubs.Sykes
{
    [Route("receivers/sykes/v1/{towerId}/dispatch")]
    public class SykesDispatchController : ControllerBase
    {
        private static readonly Logger logger = LogManager.GetCurrentClassLogger();

        [Route("")]
        [HttpPost]
        public async Task<object> Post(string towerId, dynamic obj)
        {
            var log = new LogEventInfo();
            var json = JsonExtensions.ToJson(obj);
            log.Level = LogLevel.Info;
            log.Message = "JobOffer";
            log.Properties.Add("masterAccountId", MasterAccountTypes.Sykes);
            log.Properties.Add("masterAccountName", "Sykes");
            log.Properties.Add("contractorId", towerId);
            log.Properties.Add("json", json);

            var sykesMessage = new SykesMessage();

            // ContractorId for <PERSON> is towerId
            sykesMessage.ContractorId = towerId;
            sykesMessage.JsonData = json;
            // call events:  Received 1(post),  Accepted 7(put), Cancel 2(post),
            sykesMessage.TypeId = 1;
            sykesMessage.DispatchId = "";
            sykesMessage.DispatchId = obj?.referenceId;
            
            sykesMessage.Save();

            var hash = Core.MD5(json);
            log.Properties.Add("hash", hash);

            try
            {
                var sc = SykesContractor.GetByContractorId(sykesMessage.ContractorId);

                if (sc == null)
                {
                    log.Message = "Unknown ContractorID";
                    log.Level = LogLevel.Warn;

                    return BadRequest("Unknown ContractorId: " + sykesMessage.ContractorId);
                }
                else
                {
                    log.Properties["queueItemId"] = await DigitalDispatchService.HandleCallEventAsync(sc.CompanyId, sc.AccountId, sykesMessage.ToJson(null), 
                        DigitalDispatchService.CallEventType.Received,
                        logPrefix: "Sykes");

                    return Ok();
                }
            }
            finally
            {

                logger.Log(log);
            }
        }
        
        [Route("{id}")]
        [HttpPut]
        public async Task<object> Put(string towerId, string id, dynamic obj)
        {
            var log = new LogEventInfo();

            var json = JsonExtensions.ToJson(obj);
            log.Level = LogLevel.Info;
            log.Message = "JobPut";
            log.Properties.Add("masterAccountId", MasterAccountTypes.Sykes);
            log.Properties.Add("masterAccountName", "Sykes");
            log.Properties.Add("contractorId", towerId);
            log.Properties.Add("id", id);
            log.Properties.Add("json", json);

            try
            {

                var sykesMessage = new SykesMessage();

                sykesMessage.ContractorId = towerId;
                sykesMessage.JsonData = json;
                sykesMessage.TypeId = 7; //Update
                sykesMessage.DispatchId = id;
                sykesMessage.Save();

                var sc = SykesContractor.GetByContractorId(sykesMessage.ContractorId);

                if (sc == null)
                {
                    log.Properties["error"] = "Unknown Contractor ID";

                    return BadRequest("Sykes unknown ContractorId: " + towerId);
                }

                var dispatchObject = SykesDispatch.GetByContractorIdAndDispatchId(towerId, id);

                if (dispatchObject == null)
                {
                    return BadRequest("Sykes unknown referenceId: " + id);
                }
                else
                {
                    log.Properties["queueItemId"] = await DigitalDispatchService.HandleCallEventAsync(sc.CompanyId, sc.AccountId, sykesMessage.ToJson(null), DigitalDispatchService.CallEventType.Update,
                        logPrefix: "Sykes");

                    return Ok();
                }
            }
            finally
            {

                logger.Log(log);

            }
        }
    }
}
