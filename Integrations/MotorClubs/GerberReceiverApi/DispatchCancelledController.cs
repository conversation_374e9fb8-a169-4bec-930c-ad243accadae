using Extric.Towbook.Integration.MotorClubs.Services;
using Extric.Towbook.Integrations.MotorClubs.Gerber;
using Extric.Towbook.Utility;
using NLog;
using System;
using System.Net.Http;
using System.Net.Http.Formatting;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;

namespace Extric.Towbook.API.Integration.MotorClubs.Gerber
{
    [Route("receivers/gerber")]
    public class DispatchCancelledController : ControllerBase
    {
        private static readonly Logger logger = LogManager.GetCurrentClassLogger();

        public class CancelModel
        {
            public string DispatchId { get; set; }
            public string Reason { get; set; }
        }

        [Route("dispatch/{dispatchId}/cancelled")]
        [HttpPut]
        public async Task<object> Put(int dispatchId, CancelModel obj)
        {
            var log = new LogEventInfo();
            var json = JsonExtensions.ToJson(obj);
            log.Level = LogLevel.Info;
            log.Message = "DispatchCancelled";
            log.Properties.Add("masterAccountName", "Gerber");
            log.Properties.Add("json", json);

            logger.Log(log);


            if (obj == null)
            {
                return new HttpResponseMessage(System.Net.HttpStatusCode.BadRequest)
                {
                    Content = new ObjectContent<Error>(
                           new Error() { errorCode = "validation:10", message = $"Request body is empty. This method requires the appropriate JSON model to be sent." }, new JsonMediaTypeFormatter(), "application/json")
                };
            }

            if (obj.DispatchId != dispatchId.ToString())
            {
                return new HttpResponseMessage(System.Net.HttpStatusCode.BadRequest)
                {
                    Content = new ObjectContent<Error>(
                           new Error() { errorCode = "validation:3", message = $"dispatchId in body doesn't match dispatchId in url." }, new JsonMediaTypeFormatter(), "application/json")
                };
            }

            if (string.IsNullOrWhiteSpace(obj.Reason))
            {
                return new HttpResponseMessage(System.Net.HttpStatusCode.BadRequest)
                {
                    Content = new ObjectContent<Error>(
                          new Error() { errorCode = "validation:6", message = $"You must specify the reason for the cancellation." }, new JsonMediaTypeFormatter(), "application/json")
                };
            }

            var gd = GerberDispatch.GetByDispatchId(dispatchId.ToString());
            if (gd == null)
            {
                return new HttpResponseMessage(System.Net.HttpStatusCode.BadRequest)
                {
                    Content = new ObjectContent<Error>(
                         new Error() { errorCode = "validation:7", message = $"Cannot cancel {dispatchId} - it doesn't exist in Towbook." }, new JsonMediaTypeFormatter(), "application/json")
                };
            }
            else
            {
                var gp = GerberProvider.GetByProviderId(gd.ProviderId, gd.LocationId);

                await DigitalDispatchService.HandleCallEventAsync(gp.CompanyId, gp.AccountId, obj.ToJson(null), DigitalDispatchService.CallEventType.Cancelled);
            }

            return Ok();
        }
    }
}
