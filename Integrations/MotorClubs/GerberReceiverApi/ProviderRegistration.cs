using Extric.Towbook.Integration;
using System.Collections.Generic;
using System;
using Microsoft.AspNetCore.Routing;

namespace Extric.Towbook.API.Integration.MotorClubs.Gerber
{
    public class ProviderRegistration
    {
        public ProviderRegistration()
        {

        }

        public static void RegisterRoutes(RouteCollection routes)
        {
            //List<Route> rl = new List<Route>();

            //AddRouteGet(rl, routes, "dispatch/{dispatchId}/documents", "DispatchDocuments");
            //AddRouteGet(rl, routes, "dispatch/{dispatchId}/photos", "DispatchPhotos");

            //AddRoute(rl, routes, "dispatch/{dispatchId}/accepted", "DispatchAccepted");
            //AddRoute(rl, routes, "dispatch/{dispatchId}/cancelled", "DispatchCancelled");
            //AddRoute(rl, routes, "dispatch/{dispatchId}/expired", "DispatchExpired");
            //AddRoute(rl, routes, "dispatch/{dispatchId}/refused", "DispatchRefused");
            //AddRoute(rl, routes, "dispatch/{dispatchId}", "Dispatch", true);
            //AddRoute(rl, routes, "getAvailableProviders", "GetAvailableProviders");
            //AddRoute(rl, routes, "registerProvider", "RegisterProvider");

            //foreach (var r in rl)
            //{
            //    if (r.DataTokens == null)
            //        r.DataTokens = new RouteValueDictionary();

            //    r.DataTokens["Namespaces"] = new string[] { "Extric.Towbook.API.Integration.MotorClubs.Gerber" };
            //}

            var p = Extric.Towbook.Integration.Provider.GetByName("Gerber");

            p.RegisterKey(KeyType.Account, "ContractorId");
            p.RegisterKey(KeyType.Account, "LocationId");
        }

        //private static void AddRoute(List<Route> rl, RouteCollection routes, string url, string controller, bool optional = false)
        //{
        //    if (optional)
        //        rl.Add(routes.MapHttpRoute(
        //            name: "MC_Gerber_" + url.Replace("-", ""),
        //            routeTemplate: "receivers/gerber/" + url,
        //            defaults: new { controller = controller, dispatchId = RouteParameter.Optional }));
        //    else
        //        rl.Add(routes.MapHttpRoute(
        //            name: "MC_Gerber_" + url.Replace("-", ""),
        //            routeTemplate: "receivers/gerber/" + url,
        //            defaults: new { controller = controller }));

        //}

        //private static void AddRouteGet(List<Route> rl, RouteCollection routes, string url, string controller)
        //{
        //    rl.Add(routes.MapHttpRoute(
        //        name: "MC_Gerber_" + controller,
        //        routeTemplate: "receivers/gerber/" + url,
        //        defaults: new { controller = controller, Action = "Get" },
        //        constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "GET" }) }));
        //}
    }
}
