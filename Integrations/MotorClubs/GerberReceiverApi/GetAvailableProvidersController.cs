using Extric.Towbook.Integration;
using Extric.Towbook.Integrations.MotorClubs.Gerber;
using Extric.Towbook.Utility;
using Newtonsoft.Json;
using NLog;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Formatting;
using Microsoft.AspNetCore.Mvc;

namespace Extric.Towbook.API.Integration.MotorClubs.Gerber
{
    [Route("receivers/gerber")]
    public class GetAvailableProvidersController : ControllerBase
    {
        private static readonly Logger logger = LogManager.GetCurrentClassLogger();

        public class GetAvailableServiceProvidersModel
        {
            public decimal Latitude { get; set; }
            public decimal Longitude { get; set; }
            public string ServiceType { get; set; }
            public string Weight { get; set; }
        }

        public class GetAvailableServiceProvidersResponseModel
        {
            [JsonIgnore]
            public int CompanyId { get; set; }
            
            public string ProviderId { get; set; }
            public string LocationId { get; set; }
            public string Name { get; set; }
            public string ContactName { get; set; }
            public string ContactEmail { get; set; }
            public string Address { get; set; }
            public string City { get; set; }
            public string State { get; set; }
            public string Zip { get; set; }
            public string Phone { get; set; }
            public string Fax { get; set; }
            public string TaxId { get; set; }
        }

        [Route("getAvailableProviders")]
        [HttpPost]
        public object Post(GetAvailableServiceProvidersModel model)
        {

            var log = new LogEventInfo();
            var json = JsonExtensions.ToJson(model);
            log.Level = LogLevel.Info;
            log.Message = "GetAvailableProviders";
            log.Properties.Add("masterAccountName", "Gerber");
            log.Properties.Add("json", json);
            var response = new List<GetAvailableServiceProvidersResponseModel>();
            try
            {
                if (model == null)
                    return new HttpResponseMessage(System.Net.HttpStatusCode.BadRequest)
                    {
                        Content = new ObjectContent<Error>(
                              new Error() { errorCode = "validation:10", message = $"Request body is empty. This method requires the appropriate JSON model to be sent." },
                              new JsonMediaTypeFormatter(),
                              "application/json")
                    };

                if (model.Latitude == 0 || model.Longitude == 0)
                    return new HttpResponseMessage(System.Net.HttpStatusCode.BadRequest)
                    {
                        Content = new ObjectContent<Error>(
                              new Error() { errorCode = "validation:87", message = $"You must pass a valid latitude/longitude to receive back a list of available service providers. The lat/long provided is equivilant to 0." },
                              new JsonMediaTypeFormatter(),
                              "application/json")
                    };

                if (!(new string[] { "SERVICE", "FLAT_BED", "WHEEL_LIFT" }.Contains(model.ServiceType)))
                    return new HttpResponseMessage(System.Net.HttpStatusCode.BadRequest)
                    {
                        Content = new ObjectContent<Error>(
                              new Error() { errorCode = "validation:88", message = $"You must pass a valid service type. Valid options are: SERVICE, FLAT_BED, WHEEL_LIFT." },
                              new JsonMediaTypeFormatter(),
                              "application/json")
                    };

                if (!(new string[] { "LIGHT", "MEDIUM", "HEAVY" }.Contains(model.Weight)))
                    return new HttpResponseMessage(System.Net.HttpStatusCode.BadRequest)
                    {
                        Content = new ObjectContent<Error>(
                              new Error() { errorCode = "validation:88", message = $"You must pass a valid weight class. Valid options are: LIGHT, MEDIUM, HEAVY" },
                              new JsonMediaTypeFormatter(),
                              "application/json")
                    };

                IEnumerable<string> list = new List<string>();
                list = this.Request.Headers["Authorization"];

                if (list.First().Contains("2jfrVzHk65Karb8FXtcSwS4d"))
                {
                    foreach (var x in GerberProvider.GetAll().Where(o => o.CompanyId == 10000))
                    {
                        response.Add(new GetAvailableServiceProvidersResponseModel()
                        {
                            ProviderId = x.ProviderId,
                            LocationId = x.LocationId
                        });
                    }

                    response.Add(new GetAvailableServiceProvidersResponseModel()
                    {
                        ProviderId = "<EMAIL>",
                        Address = "201 N Riverside Ave",
                        City = "St Clair",
                        State = "MI",
                        Zip = "48079",
                        LocationId = null, // important, LocationId must be null or it breaks gerbers system
                        Name = "Demo Towing",
                        ContactName = "John Doe",
                        Phone = "************",
                        Fax = "************",
                        TaxId = null
                    });

                    return response;
                }
                else
                {
                    var records = SqlMapper.QuerySP<dynamic>("MCDispatch.GerberProvidersGetByGeolocation", new
                    {
                        SLAT = model.Latitude,
                        SLON = model.Longitude
                    }).Select(o =>
                        new GetAvailableServiceProvidersResponseModel()
                        {
                            CompanyId = o.CompanyId,
                            LocationId = o.LocationId,
                            ProviderId = o.ProviderId,
                            Name = o.Name,
                            Address = o.Address,
                            City = o.City,
                            State = o.State,
                            Zip = o.Zip,
                            Phone = o.Phone,
                            Fax = o.Fax
                        }).ToCollection();


                    foreach (var x in records.ToArray())
                    {
                        var serviceCallsOnly = CompanyKeyValue.GetFirstValueOrNull(x.CompanyId,
                                  Extric.Towbook.Integration.Provider.Towbook.ProviderId, "ServiceCallsOnly") == "1";

                        if (serviceCallsOnly && model.ServiceType != "SERVICE")
                        {
                            records = records.Where(o => o.CompanyId != x.CompanyId).ToCollection();
                        }
                    }

                    return records.GroupBy(o => o.ProviderId).Select(o => o.FirstOrDefault()).ToList();

                }
            }
            finally
            {
                log.Properties.Add("response", response.ToJson());
                logger.Log(log);
            }
        }
    }
}
