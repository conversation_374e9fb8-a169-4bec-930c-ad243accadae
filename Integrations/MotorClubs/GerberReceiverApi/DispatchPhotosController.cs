using Extric.Towbook.Dispatch;
using Extric.Towbook.Integration.MotorClubs.Services;
using Extric.Towbook.Integrations.MotorClubs.Gerber;
using Extric.Towbook.Storage;
using Extric.Towbook.Utility;
using NLog;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;

namespace Extric.Towbook.API.Integration.MotorClubs.Gerber
{
    [Route("receivers/gerber")]
    public class DispatchPhotosController : ControllerBase
    {
        private static readonly Logger logger = LogManager.GetCurrentClassLogger();


        public class PhotoModel
        {
            public int Id { get; set; }
            public string ContentType { get; set; }
            public string Description { get; set; }
            public DateTime CreateDate { get; set; }
            public decimal? Latitude { get; set; }
            public decimal? Longitude { get; set; }
            public int? CallStatusId { get; set; }
            public string CallStatusName { get; set; }

            public string Url { get; set; }

            public static async Task<PhotoModel> TranslateDomainToModelAsync(Photo input, int companyId)
            {
                if (input == null)
                    return null;

                var status = input.DispatchEntryStatusId.GetValueOrDefault() > 0 ?
                        await Status.GetByIdAsync(input.DispatchEntryStatusId.Value, 0) : null;

                var pm = new PhotoModel()
                {
                    Id = input.Id,
                    ContentType = input.ContentType,
                    CreateDate = input.CreateDate,
                    Description = input.Description,
                    Latitude = input.Latitude,
                    Longitude = input.Longitude,
                    CallStatusId = input.DispatchEntryStatusId,
                    CallStatusName = status?.Name ?? "",
                    Url = Extric.Towbook.Storage.FileUtility.GetFileUrl(
                                        string.Format("/Storage/dispatchEntries/Photos/{0}/{1}/{2}.jpg",
                                            companyId, input.DispatchEntryId, input.Id),
                                        DateTime.Now.AddDays(365))
            };

                return pm;
            }
        }


        [Route("dispatch/{dispatchId}/photos")]
        [HttpGet]
        public async Task<IEnumerable<PhotoModel>> Get(int dispatchId)
        {
            var log = new LogEventInfo();

            log.Level = LogLevel.Info;
            log.Message = "DispatchPhotos";
            log.Properties.Add("masterAccountName", "Gerber");
            log.Properties.Add("dispatchId", dispatchId);

            logger.Log(log);

            var gd = GerberDispatch.GetByDispatchId(dispatchId.ToString());

            var cr = CallRequest.GetById(gd.CallRequestId);

            var returns = new List<PhotoModel>();

            if (cr?.DispatchEntryId != null)
            {
                foreach (var photo in await Photo.GetByDispatchEntryIdAsync(cr.DispatchEntryId.Value))
                {
                    string path = await FileUtility.GetFileAsync(photo.Location.Replace("%1", cr.CompanyId.ToString()));

                    if (path != null)
                    {
                        returns.Add(await PhotoModel.TranslateDomainToModelAsync(photo, cr.CompanyId));
                    }
                }

                return returns;
            }
            else
            {
                return Array.Empty<PhotoModel>();
            }
        }
    }
}
