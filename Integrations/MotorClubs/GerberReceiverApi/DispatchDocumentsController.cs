using Extric.Towbook.Integration.MotorClubs.Services;
using Extric.Towbook.Utility;
using NLog;
using System;
using Microsoft.AspNetCore.Mvc;

namespace Extric.Towbook.API.Integration.MotorClubs.Gerber
{
    [Route("receivers/gerber")]
    public class DispatchDocumentsController : ControllerBase
    {
        private static readonly Logger logger = LogManager.GetCurrentClassLogger();

        [Route("dispatch/{dispatchId}/documents")]
        [HttpGet]
        public object Get(int dispatchId)
        {
            var log = new LogEventInfo();
            log.Level = LogLevel.Info;
            log.Message = "DispatchDocuments";
            log.Properties.Add("masterAccountName", "Gerber");
            log.Properties.Add("dispatchId", dispatchId);

            logger.Log(log);

            return Ok();
        }
    }
}
