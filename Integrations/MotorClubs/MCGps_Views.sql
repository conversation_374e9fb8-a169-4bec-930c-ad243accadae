

/***** Object:  View [MCDispatch].[vwIsscGpsBreadcrumbExport]    Script Date: 5/13/2024 11:36:59 AM *****/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE view [MCDispatch].[vwIsscGpsBreadcrumbExport] as      
select D.DispatchEntryId, ISC.DispatchId, ISC.ContractorId, ISC.ClientId, ISC.LocationId,  D.DriverId, ULC.Latitude, ULC.Longitude, ULC.Timestamp from DispatchEntryRequests DR WITH (nolock)     
 INNER JOIN DispatchEntries D with (nolock) on D.DispatchEntryId=DR.DispatchEntryId and D.Status > 1 AND D.Status < 5      
 INNER JOIN MCDispatch.IsscDispatches ISC with (nolock) on ISC.CallRequestId=DR.CallRequestId      
 INNER JOIN Drivers DRV WITH (nolock) on DRV.DriverId=D.DriverId      
 INNER JOIN UserLocationsCurrent ULC WITH (nolock) on ULC.UserId=DRV.UserId      
 where DR.CreateDate > DATEADD(hour, -4, getdate()) and DR.status=1 and DR.CompanyId not in (4550,6304,9783,9760,2887)       
 and dr.CallRequestId > IDENT_CURRENT('DispatchEntryRequests')-500000      
 and ulc.Timestamp > dateadd(second, -60, getdate())      
      
GO


# ============================================================================================


/***** Object:  View [MCDispatch].[vwGerberGpsBreadcrumbExport]    Script Date: 5/13/2024 11:39:20 AM *****/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


CREATE view [MCDispatch].[vwGerberGpsBreadcrumbExport] as
select distinct D.DispatchEntryId, D.PurchaseOrderNumber, ISC.ProviderId, ISC.LocationId, C.Name as CompanyName, D.DriverId as ExternalDriverId, D.Status, ISC.JobNumber, 
Drv.Name as DriverName, c.Phone, ISC.DispatchId, ULC.Latitude, ULC.Longitude, ULC.Timestamp from DispatchEntryRequests DR
	INNER JOIN DispatchEntries D with (nolock) on D.DispatchEntryId=DR.DispatchEntryId
	INNER JOIN MCDispatch.GerberDispatches ISC with (nolock) on ISC.CallRequestId=DR.CallRequestId
	LEFT OUTER JOIN Drivers DRV with (nolock) on DRV.DriverId=D.DriverId
	INNER JOIN UserLocationsCurrent ULC WITH (nolock) on ULC.UserId=DRV.UserId
	inner join companies C with (nolock) on C.CompanyId=D.CompanyId
 where DR.CreateDate > DATEADD(hour, -4, getdate()) and DR.status=1 and D.DispatchEntryId > 30200000
   --and ulc.Timestamp > dateadd(second, -60, getdate())
GO


# ============================================================================================


/***** Object:  View [MCDispatch].[vwQuestGpsBreadcrumbExport]    Script Date: 5/13/2024 11:40:03 AM *****/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE view [MCDispatch].[vwQuestGpsBreadcrumbExport] as
/*
select distinct D.DispatchEntryId, 
	D.CompanyId,
	ISC.ContractorId, 
	D.DriverId as DriverId,
	DRV.Name as DriverName,
	ISC.ResponseId, 
	coalesce(ISC.DispatchId, D.PurchaseOrderNumber) as JobId,
	ULC.Latitude, 
	ULC.Longitude, 
	ULC.Timestamp from DispatchEntryRequests DR
	INNER JOIN DispatchEntries D WITH (nolock) on D.DispatchEntryId=DR.DispatchEntryId and   D.DispatchEntryId > ident_current('dispatchentries')-100000
	INNER JOIN MCDispatch.AllstateDispatches ISC WITH (nolock) on ISC.CallRequestId=DR.CallRequestId
	inner join MCDispatch.AllstateContractors AGS WITH (nolock) on AGS.ContractorId=ISC.ContractorId 
		and AGS.MasterAccountId=4
	INNER JOIN Drivers DRV WITH (nolock) on DRV.DriverId=D.DriverId
	INNER JOIN UserLocationsCurrent ULC WITH (nolock) on ULC.UserId=DRV.UserId
 where DR.CreateDate > DATEADD(hour, -4, getdate()) and DR.status=1 and
	 DR.CompanyId not in (4550,6304,9783,9760,2887) and dr.CallRequestId > IDENT_CURRENT('DispatchEntryRequests')-100000
 and ulc.Timestamp > dateadd(second, -60, getdate()) and D.Status < 5
 */ 
select distinct D.DispatchEntryId, 
	D.CompanyId,
	coalesce(coalesce(ISC.ContractorId, (select top 1 ProviderId from MotorClubProviderNumberHistory where companyId=D.CompanyId and accountId=d.accountid)), '0') as ContractorId, 
	D.DriverId as DriverId,
	DRV.Name as DriverName,
	coalesce(ISC.ResponseId,  '0') as ResponseId,
	Coalesce(ISC.DispatchId,D.PurchaseOrderNumber) as JobId,
	ULC.Latitude, 
	ULC.Longitude, 
	ULC.Timestamp from DispatchEntries D WITH (nolock) 
	LEFT OUTER JOIN DispatchEntryRequests DR on DR.DispatchEntryId=D.DispatchEntryId
	LEFT OUTER JOIN MCDispatch.AllstateDispatches ISC WITH (nolock) on ISC.CallRequestId=DR.CallRequestId
	LEFT OUTER JOIN  MCDispatch.AllstateContractors AGS WITH (nolock) on (AGS.ContractorId=ISC.ContractorId 
		and AGS.MasterAccountId=4) OR AGS.AccountId=D.AccountId
	INNER JOIN Drivers DRV WITH (nolock) on DRV.DriverId=D.DriverId
	INNER JOIN UserLocationsCurrent ULC WITH (nolock) on ULC.UserId=DRV.UserId
	INNER JOIN Accounts A on A.AccountId=D.AccountId and A.MasterAccountId=4
 where D.CreateDate > DATEADD(hour, -4, getdate()) and 
	 D.CompanyId not in (4550,6304,9783,9760,2887)
 and ulc.Timestamp > dateadd(second, -60, getdate()) and D.Status < 5
 and D.DispatchEntryId > ident_current('dispatchentries')-30000 and Coalesce(ISC.DispatchId,D.PurchaseOrderNumber) is not null
GO


# ============================================================================================


/***** Object:  View [MCDispatch].[vwFleetnetGpsBreadcrumbExport]    Script Date: 5/13/2024 11:41:27 AM *****/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE   VIEW [MCDispatch].[vwFleetnetGpsBreadcrumbExport] as      
select distinct D.DispatchEntryId, ISC.ProviderId, D.DriverId as ExternalDriverId, DRV.Name as ExternalDriverName, 
TRK.TruckId, TRK.Name as TruckName,     
  ISC.RequestId, ULC.Latitude, ULC.Longitude, ULC.Timestamp,    
  D.PurchaseOrderNumber from DispatchEntryRequests DR      
 INNER JOIN DispatchEntries D on     
 D.DispatchEntryId=DR.DispatchEntryId 
 and D.Status > 1 AND D.Status < 5      
 INNER JOIN MCDispatch.FleetnetJobOffers ISC on ISC.CallRequestId=DR.CallRequestId      
 inner join MCDispatch.FleetnetProviders AGS on AGS.ProviderId=ISC.ProviderId
 INNER JOIN Drivers DRV on DRV.DriverId=D.DriverId     
INNER JOIN UserLocationsCurrent ULC WITH (nolock) on ULC.UserId=DRV.UserId      
LEFT OUTER JOIN Trucks TRK on TRK.TruckId=D.TruckId 
 where DR.CreateDate > DATEADD(hour, -4, getdate()) and DR.status=1 and DR.CompanyId not in (4550,6304,9783,9760,2887) and  D.DispatchEntryId > 65000000      
 and dr.CallRequestId > IDENT_CURRENT('DispatchEntryRequests')-75000      
 and ulc.Timestamp > dateadd(second, -60, getdate())      
    
    
union all   
    
select distinct D.DispatchEntryId, ISC.ProviderId, D.DriverId as ExternalDriverId, DRV.Name as ExternalDriverName,      
TCK.TruckId, TCK.Name as TruckName,
  ISC.RequestId, ULC.Latitude, ULC.Longitude, ULC.Timestamp,    
  D.PurchaseOrderNumber from DispatchEntryRequests DR  with (nolock)    
 INNER JOIN DispatchEntries D  with (nolock) on     
 D.DispatchEntryId=DR.DispatchEntryId and    
 D.Status > 1 AND D.Status < 5      
 INNER JOIN MCDispatch.FleetnetJobOffers ISC with (nolock) on ISC.CallRequestId=DR.CallRequestId      
 inner join MCDispatch.FleetnetProviders AGS with (nolock) on AGS.ProviderId=ISC.ProviderId 
 INNER JOIN Drivers DRV  with (nolock) on DRV.DriverId=D.DriverId      
 INNER JOIN Trucks TCK  with (nolock) on TCK.TruckId=D.TruckId    
 INNER JOIN TruckLocationsCurrent ULC WITH (nolock) on ULC.TruckId=TCK.TruckId
 where DR.CreateDate > DATEADD(hour, -4, getdate()) and DR.status=1 and DR.CompanyId not in (4550,6304,9783,9760,2887) and  D.DispatchEntryId > 65000000      
 and dr.CallRequestId > IDENT_CURRENT('DispatchEntryRequests')-75000      
 and ulc.Timestamp > dateadd(second, -60, getdate())
GO


# ============================================================================================


/***** Object:  View [MCDispatch].[vwNsdGpsBreadcrumbExport]    Script Date: 5/13/2024 11:42:13 AM *****/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE view [MCDispatch].[vwNsdGpsBreadcrumbExport] as
select distinct D.DispatchEntryId, ISC.ContractorId, C.Name as CompanyName, D.DriverId as ExternalDriverId, D.Status, ISC.PurchaseOrderNumber, Drv.Name as DriverName, c.Phone, ISC.ResponseId, ULC.Latitude, ULC.Longitude, ULC.Timestamp from DispatchEntryRequests DR
	INNER JOIN DispatchEntries D with (nolock) on D.DispatchEntryId=DR.DispatchEntryId
	INNER JOIN MCDispatch.AllstateDispatches ISC with (nolock) on ISC.CallRequestId=DR.CallRequestId
	inner join MCDispatch.AllstateContractors AGS with (nolock) on AGS.ContractorId=ISC.ContractorId and AGS.MasterAccountId=7
	INNER JOIN Drivers DRV with (nolock) on DRV.DriverId=D.DriverId
	INNER JOIN UserLocationsCurrent ULC WITH (nolock) on ULC.UserId=DRV.UserId
	inner join companies C with (nolock) on C.CompanyId=D.CompanyId
 where DR.CreateDate > DATEADD(hour, -4, getdate()) and DR.status=1 and DR.CompanyId not in (4550,6304,9783,9760,2887) and 
  D.DispatchEntryId > ********
 and ulc.Timestamp > dateadd(second, -60, getdate())

GO
