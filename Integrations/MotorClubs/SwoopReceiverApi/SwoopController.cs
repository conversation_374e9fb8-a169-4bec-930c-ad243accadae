using System;
using Microsoft.AspNetCore.Mvc;

namespace Extric.Towbook.API.Integration.MotorClubs.Swoop
{
    [Route("receivers/swoop")]
    public class SwoopController : ControllerBase
    {
        [Route("")]
        [HttpGet]
        public object Get()
        {
            return "it works";
        }
        [Route("")]
        [HttpPost]
        public object Post()
        {
            return "Swoop Controller POST works " + Guid.NewGuid().ToString("n") + " - " + DateTime.Now.ToString();
        }
    }
}
