using Extric.Towbook.Integrations.MotorClubs.Trx;
using Extric.Towbook.Integration.MotorClubs.Services;
using Newtonsoft.Json;
using NLog;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Primitives;
using System.Linq;
using System.IO;
using Extric.Towbook.WebShared;

namespace Extric.Towbook.API.Integration.MotorClubs.Trx
{
    [Route("receivers/trx/expired")]
    public class DispatchExpiredController : ControllerBase
    {
        private static readonly Logger logger = LogManager.GetCurrentClassLogger();


        // POST /dispatch 
        [HttpPost]
        [Route("")]
        public async Task<object> Post()
        {
            var rawJson = await Request.Body.ReadAsStringAsync();

            var log = this.NewLogEvent("ExpiredPost", rawJson);

            if (this.Request.Headers.HasAuthorization())
                log.Properties.Add("Authorization", this.Request.Headers.AuthorizationParameter());

            try
            {
                var dispatch = JsonConvert.DeserializeObject<IncomingExpiredModel>(rawJson);

                var contractor = this.GetContractorById(dispatch.ContractorId);

                if (contractor == null)
                    return this.ApiFailure("Unknown ContractorId");

                log.Properties["queueItemId"] = await this.SendToBackendService(DigitalDispatchService.CallEventType.Expired,
                    contractor, 
                    rawJson, 
                    dispatch.DispatchId);
            }
            finally
            {
                logger.Log(log);
            }
            
            return Accepted();
        }
    }
}
