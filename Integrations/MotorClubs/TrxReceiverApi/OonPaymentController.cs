using NLog;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web.Http;
using Extric.Towbook.Integration.MotorClubs.Services;
using System;
using Newtonsoft.Json;
using Extric.Towbook.Accounts;
using Extric.Towbook.Integration.MotorClubs.Trx;
using Microsoft.AspNetCore.Mvc;
using System.IO;
using Extric.Towbook.WebShared;

namespace Extric.Towbook.API.Integration.MotorClubs.Trx
{
    public class OonPaymentController : ControllerBase
    {
        private static readonly Logger logger = LogManager.GetCurrentClassLogger();

        // POST /dispatch 
        public async Task<object> Post()
        {
            var rawJson = await Request.Body.ReadAsStringAsync();

            var log = this.NewLogEvent("OonPaymentPost", rawJson);

            if (this.Request.Headers.HasAuthorization())
                log.Properties.Add("Authorization", this.Request.Headers.AuthorizationParameter());

            try
            {
                var dispatch = JsonConvert.DeserializeObject<OonPaymentModel>(rawJson);

                if (dispatch == null)
                    return this.ApiFailure("Missing required payment body");

                log.Properties["queueItemId"] =
                await this.SendToBackendService(
                    DigitalDispatchService.CallEventType.OonPayment,
                    new  Integrations.MotorClubs.Trx.TrxContractor()
                    {
                        MasterAccountId = MasterAccountTypes.OonTrx
                    },
                    rawJson,
                    dispatch.DispatchId);
            }
            catch (Exception ex)
            {
                log.Properties["exception"] = ex;
                log.Level = LogLevel.Error;

                throw;
            }
            finally
            {
                logger.Log(log);
            }

            return Accepted();
        }
    }
}
