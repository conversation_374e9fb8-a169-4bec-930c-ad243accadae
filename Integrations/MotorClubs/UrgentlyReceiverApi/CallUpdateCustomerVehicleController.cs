using Extric.Towbook.Integration.MotorClubs.Services;
using Extric.Towbook.Integrations.MotorClubs.Urgently;
using Extric.Towbook.Utility;
using NLog;
using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;

namespace Extric.Towbook.API.Integration.MotorClubs.Urgently
{
    [Route("receivers/urgently/partner-dispatch/customer-vehicle")]
    public class CallUpdateCustomerVehicleController : ControllerBase
    {
        private static readonly Logger logger = LogManager.GetCurrentClassLogger();

        //rl.Add(routes.MapHttpRoute(
        //    name: "MC_Urgently_" + url.Replace("-", ""),
        //    routeTemplate: "receivers/urgently/partner-dispatch/" + url,
        //    defaults: new { controller = controller }));
        //AddRoute(rl, routes, "customer-vehicle", "CallUpdateCustomerVehicle");
        [Route("")]
        [HttpPut]
        public async Task<object> Put(dynamic obj)
        {
            // Example input:
            // {"context":{"caseId":"********-1111-1111-1111-********1111","token":null,"tokenIssuer":null,"jobId":null,"clientId":null},"vehicle":{"make":"Acura","model":"TL","color":"Black","year":"2006","vin":"****************"}}

            var log = new LogEventInfo();
            string caseId = obj.context.caseId.ToString();

            var json = JsonExtensions.ToJson(obj);
            log.Level = LogLevel.Info;
            log.Message = "CallUpdateCustomerVehicle";
            log.Properties.Add("masterAccountName", "Urgent.ly");
            log.Properties.Add("json", json);
            log.Properties["dispatchId"] = caseId;

            try
            {

                var jobs = UrgentlyJobOffer.GetByCaseId(caseId);

                var callRequests = jobs.Select(o => Dispatch.CallRequest.GetById(o.CallRequestId));
                var accepted = callRequests.Where(o => o.Status == Dispatch.CallRequestStatus.Accepted && o.DispatchEntryId > 0);

                Dispatch.CallRequest actual = null;

                if (accepted.Count() > 1)
                {
                    foreach (var x in accepted)
                    {
                        var en = await Dispatch.Entry.GetByIdNoCacheAsync(x.DispatchEntryId.Value);
                        if (en != null && en.Status != Dispatch.Status.Cancelled)
                        {
                            actual = x;
                            break;
                        }
                    }
                }
                else
                {
                    actual = accepted.FirstOrDefault();
                }

                var up = UrgentlyProvider.GetByCompanyId(actual.CompanyId).FirstOrDefault();

                if (up == null)
                {
                    return BadRequest("Couldn't find existing in-progress dispatch to update.");
                }
                else
                {
                    var um = new UrgentlyMessage();
                    um.ProviderId = actual.ProviderId;
                    um.JsonData = json;
                    um.TypeId = 4; // call update;
                    um.Save();

                    log.Properties["queueItemId"] =  await DigitalDispatchService.HandleCallEventAsync(up.CompanyId, up.AccountId, um.ToJson(null), DigitalDispatchService.CallEventType.Update,
                        logPrefix: "Urgently");

                    return Ok();
                }
            }
            finally
            {
                logger.Log(log);
            }
        }
    }
}
