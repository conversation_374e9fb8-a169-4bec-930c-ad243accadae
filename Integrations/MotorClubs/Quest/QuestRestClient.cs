using System.Net.Http;
using System;
using RestSharp;
using RestSharp.Authenticators;

namespace Extric.Towbook.Integrations.MotorClubs.Quest;

public class QuestRestClient
{
    private static readonly HttpClient _httpClient = new(new SocketsHttpHandler
    {
        PooledConnectionLifetime = TimeSpan.FromMinutes(5),
        PooledConnectionIdleTimeout = TimeSpan.FromSeconds(30)
    });

    private readonly string URL_BASE;
    private readonly string USERNAME;
    private readonly string PASSWORD;
    private readonly RestClient _restClient;

    public QuestRestClient(string url, string username, string password)
    {
        URL_BASE = url;
        USERNAME = username;
        PASSWORD = password;

        var options = new RestClientOptions(URL_BASE)
        {
            UserAgent = "Towbook/4.0.0",
            Authenticator = new HttpBasicAuthenticator(USERNAME, PASSWORD),
        };

        _restClient = new RestClient(_httpClient, options);
    }

    private RestClient GetRestClient() => _restClient;

    public string SendMessage(string xml)
    {
        var client = GetRestClient();
        var request = new RestRequest("/", RestSharp.Method.Post);

        request.AddParameter("text/xml", xml, RestSharp.ParameterType.RequestBody);

        var response = client.Execute(request);

        return response.Content;
    }
}
