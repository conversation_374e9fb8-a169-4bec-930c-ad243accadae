//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Extric.Towbook.Integrations.MotorClubs.Quest.QuestDigitalDispatchService {
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ServiceModel.ServiceContractAttribute(ConfigurationName="QuestDigitalDispatchService.IDigitalDispatchService")]
    public interface IDigitalDispatchService {
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IDigitalDispatchService/SendMessage", ReplyAction="http://tempuri.org/IDigitalDispatchService/SendMessageResponse")]
        Extric.Towbook.Integrations.MotorClubs.Quest.QuestDigitalDispatchService.Message SendMessage(Extric.Towbook.Integrations.MotorClubs.Quest.QuestDigitalDispatchService.Message request);
        
        // CODEGEN: Generating message contract since the operation has multiple return values.
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IDigitalDispatchService/SendMessage", ReplyAction="http://tempuri.org/IDigitalDispatchService/SendMessageResponse")]
        System.Threading.Tasks.Task<Extric.Towbook.Integrations.MotorClubs.Quest.QuestDigitalDispatchService.Message> SendMessageAsync(Extric.Towbook.Integrations.MotorClubs.Quest.QuestDigitalDispatchService.Message request);
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="Message", WrapperNamespace="http://tempuri.org/", IsWrapped=true)]
    public partial class Message {
        
        [System.ServiceModel.MessageHeaderAttribute(Namespace="http://tempuri.org/")]
        public string password;
        
        [System.ServiceModel.MessageHeaderAttribute(Namespace="http://tempuri.org/")]
        public string username;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=0)]
        public string messageXML;
        
        public Message() {
        }
        
        public Message(string password, string username, string messageXML) {
            this.password = password;
            this.username = username;
            this.messageXML = messageXML;
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    public interface IDigitalDispatchServiceChannel : Extric.Towbook.Integrations.MotorClubs.Quest.QuestDigitalDispatchService.IDigitalDispatchService, System.ServiceModel.IClientChannel {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    public partial class DigitalDispatchServiceClient : System.ServiceModel.ClientBase<Extric.Towbook.Integrations.MotorClubs.Quest.QuestDigitalDispatchService.IDigitalDispatchService>, Extric.Towbook.Integrations.MotorClubs.Quest.QuestDigitalDispatchService.IDigitalDispatchService {
        
        public DigitalDispatchServiceClient() {
        }
        
        public DigitalDispatchServiceClient(string endpointConfigurationName) : 
                base(endpointConfigurationName) {
        }
        
        public DigitalDispatchServiceClient(string endpointConfigurationName, string remoteAddress) : 
                base(endpointConfigurationName, remoteAddress) {
        }
        
        public DigitalDispatchServiceClient(string endpointConfigurationName, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(endpointConfigurationName, remoteAddress) {
        }
        
        public DigitalDispatchServiceClient(System.ServiceModel.Channels.Binding binding, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(binding, remoteAddress) {
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        Extric.Towbook.Integrations.MotorClubs.Quest.QuestDigitalDispatchService.Message Extric.Towbook.Integrations.MotorClubs.Quest.QuestDigitalDispatchService.IDigitalDispatchService.SendMessage(Extric.Towbook.Integrations.MotorClubs.Quest.QuestDigitalDispatchService.Message request) {
            return base.Channel.SendMessage(request);
        }
        
        public void SendMessage(ref string password, ref string username, ref string messageXML) {
            Extric.Towbook.Integrations.MotorClubs.Quest.QuestDigitalDispatchService.Message inValue = new Extric.Towbook.Integrations.MotorClubs.Quest.QuestDigitalDispatchService.Message();
            inValue.password = password;
            inValue.username = username;
            inValue.messageXML = messageXML;
            Extric.Towbook.Integrations.MotorClubs.Quest.QuestDigitalDispatchService.Message retVal = ((Extric.Towbook.Integrations.MotorClubs.Quest.QuestDigitalDispatchService.IDigitalDispatchService)(this)).SendMessage(inValue);
            password = retVal.password;
            username = retVal.username;
            messageXML = retVal.messageXML;
        }
        
        public System.Threading.Tasks.Task<Extric.Towbook.Integrations.MotorClubs.Quest.QuestDigitalDispatchService.Message> SendMessageAsync(Extric.Towbook.Integrations.MotorClubs.Quest.QuestDigitalDispatchService.Message request) {
            return base.Channel.SendMessageAsync(request);
        }
    }
}
