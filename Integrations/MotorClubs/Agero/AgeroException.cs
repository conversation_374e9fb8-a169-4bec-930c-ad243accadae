using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Agero
{
    [Serializable]
    public class AgeroException : Exception
    {
        public string Json { get; private set; }
        public int HttpStatusCode { get; private set; }

        public string Url { get; private set; }
        public string Request { get; private set; }

        public AgeroException() { }
        public AgeroException(string message) : base(message) { }
        public AgeroException(string message, int httpStatusCode, string json) : base(message) {
            Json = json;
            HttpStatusCode = httpStatusCode;
        }

        public AgeroException(string message, int httpStatusCode, string json, string url, string request) : base(message)
        {
            Json = json;
            HttpStatusCode = httpStatusCode;
            Url = url;
            Request = request;
        }


        public AgeroException(string message, Exception inner) : base(message, inner) { }
        protected AgeroException(
          System.Runtime.Serialization.SerializationInfo info,
          System.Runtime.Serialization.StreamingContext context)
            : base(info, context) { }
    }

    public class AgeroInvalidAccessTokenException : AgeroException
    {
        public AgeroInvalidAccessTokenException(string json) : base("A valid OAuth token was not provided.", 401, json) { }
    }

    public class AgeroServiceUnavailable : AgeroException
    {
        public AgeroServiceUnavailable(string json) : base("The Service is temporarily unavailable", 503, json) { }
    }

    public class AgeroRestServiceOutage : AgeroException
    {
        public AgeroRestServiceOutage(string json, string error) : base("Agero is returning unparsable http errors (they don't contain JSON)..." + error, 0, json) { }
    }

    public class AgeroInvalidAuthorizationCodeException : AgeroException
    {
        public AgeroInvalidAuthorizationCodeException(string json) : base("Provided auth code is not valid / already used", 400, json) { }
    }
    
    public class AgeroUnsupportedGrantTypeException : AgeroException
    {
        public AgeroUnsupportedGrantTypeException(string json) : base("Provided grant_type is invalid", 400, json) { }
    }

    public class AgeroResourceNotAllowedException : AgeroException
    {
        public AgeroResourceNotAllowedException(string json) : base("The resource is not allowed by the current user", 403, json) { }
    }
    
    public class AgeroVendorNotFoundException : AgeroException
    {
        public AgeroVendorNotFoundException(string json) : base("Vendor does not exist in the system for the given vendorId", 404, json) { }
    }

    public class AgeroResourceNotFoundException: AgeroException
    {
        public AgeroResourceNotFoundException(string json) : base("No enrollment exist for the requesting 3rd-party partner with the provided identifier", 404, json) { }
    }

    public class AgeroNoJobsFoundException : AgeroException
    {
        public AgeroNoJobsFoundException(string json) : base("No jobs were found for the query", 404, json) { }
    }

    public class AgeroResourceExistsException: AgeroException
    {
        public AgeroResourceExistsException(string json) : base("Once a profile pic is created for a user, it cannot be created again. A PUT should be made instead to update the current profile", 403, json) { }
    }

    public class AgeroNoDriverFoundException: AgeroException
    {
        public AgeroNoDriverFoundException(string json) : base("Returned when no driver could not be found for the provided driver Id", 404, json) { }
    }

    public class AgeroNoVendorFoundException: AgeroException
    {
        public AgeroNoVendorFoundException(string json) : base("Returned when no Vendor could not be found for the provided Vendor Id", 404, json) { }
    }
    public class AgeroNoPictureExistsException:AgeroException
    {
        public AgeroNoPictureExistsException(string json) : base("No picture exist", 404, json) { }
    }

    public class AgeroDispatchNotFoundException:AgeroException
    {
        public AgeroDispatchNotFoundException(string json) : base("This is returned if the dispatch request# provided in the resourceURI could not be found", 404, json) { }
    }

    public class AgeroDispatchExpiredException : AgeroException
    {
        public AgeroDispatchExpiredException(string json) : base("This is returned if the DispatchID that you tried to accept/reject expired on Agero's end.", 403, json) { }
    }

    public class AgeroDispatchAlreadyResponded : AgeroException
    {
        public AgeroDispatchAlreadyResponded(string message, string json) : base(message, 403, json) { }
    }

    public class AgeroSignaturesNotFoundException:AgeroException
    {
        public AgeroSignaturesNotFoundException(string json) : base("This is returned if a dispatch has no signatures", 404, json) { }
    }

    public class AgeroParameterMissingException : AgeroException
    {
        public AgeroParameterMissingException(string json) : base("One or more invalid parameters was passed", 400, json) { }
    }

    public class AgeroUnknownException : AgeroException
    {
        public AgeroUnknownException(string json) : base("An error occurred on the server:" + json, 500, json) { }
    }
}
