using Agero.Types;
using Extric.Towbook.Utility;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Extric.Towbook.Integrations.MotorClubs.Agero
{
    [Table("MCDispatch.AgeroDispatches")]
    public class AgeroDispatch
    {
        [Key]
        public long AgeroDispatchId { get; set; }
        public int VendorId { get; set; }
        public string CallJson { get; set; }
        public int? Eta { get; set; }
        public string AccessToken { get; set; }
        public AcceptDispatchETAReason EtaReason { get; set; }
        public int CallRequestId { get; set; }
        public int DispatchId { get; set; }

        public AgeroDispatch() { }

        public static AgeroDispatch GetByDispatchId(int dispatchId, int vendorId)
        {
            return SqlMapper.Query<AgeroDispatch>("SELECT * FROM MCDispatch.AgeroDispatches WHERE DispatchId = @DispatchId AND VendorId=@VendorId",
                new
                {
                    DispatchId = dispatchId,
                    VendorId = vendorId
                }).FirstOrDefault();
        }

        public static AgeroDispatch GetByCallRequestId(int callRequestId)
        {
            return SqlMapper.Query<AgeroDispatch>("SELECT * FROM MCDispatch.AgeroDispatches WHERE CallRequestId = @CallRequestId", new { CallRequestId = callRequestId }).FirstOrDefault();
        }
        
        public static async Task<AgeroDispatch> GetByCallRequestIdAsync(int callRequestId)
        {
            return (await SqlMapper.QueryAsync<AgeroDispatch>("SELECT * FROM MCDispatch.AgeroDispatches WHERE CallRequestId = @CallRequestId", new { CallRequestId = callRequestId })).FirstOrDefault();
        }

        public void Save()
        {
            if (this.AgeroDispatchId == 0)
                this.AgeroDispatchId = SqlMapper.Insert<AgeroDispatch>(this);
            else
                SqlMapper.Update<AgeroDispatch>(this);
        }
    }
}
