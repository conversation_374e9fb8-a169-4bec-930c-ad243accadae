using Extric.Towbook.Dispatch;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using static Extric.Towbook.Vehicle.VehicleUtility;

namespace Agero.Types
{
    public class OAuthAccessTokenResponse
    {
        public string AccessToken { get; set; }
        public string RefreshToken { get; set; }
        public int VendorId { get; set; }
        public int ExpiresIn { get; set; }
    }

    public class OAuthRefreshTokenResponse
    {
        public string AccessToken { get; set; }
        public string RefreshToken { get; set; }
        public int ExpiresIn { get; set; }
        public int RefreshCount { get; set; }
    }

    public class OAuthSignInResponse
    {
        public int VendorId { get; set; }
        public string Username { get; set; }
        public string Role { get; set; }
    }

    public class DispatchVehicle
    {
        public int Year { get; set; }
        public string Make { get; set; }
        public string Model { get; set; }
        public string Color { get; set; }
        public string VIN { get; set; }
        public string Plate { get; set; }
        public string State { get; set; }

        public string Mileage { get; set; }

        public string VehicleType { get; set; }
        public string DriveTrainType { get; set; }
        public string FuelType { get; set; }

        public DispatchVehicle() { }

        /// <summary>
        /// Returns a a human friendly representation of the vehicle. 
        /// Example: 2006 Acura TL Black
        /// </summary>
        /// <returns></returns>
        public override string ToString()
        {
            return string.Format("{0} {1} {2} {3}",
                this.Year,
                GetManufacturerByName(this.Make),
                GetModelByName(this.Model),
                this.Color).Trim();
        }
    }

    public class VendorDriverLocation
    {
        public string Address1 { get; set; }
        public string Address2 { get; set; }
        public string CrossStreet { get; set; }
        public string Landmark { get; set; }
        public string LocationType { get; set; }
        public string City { get; set; }
        public string Poi1 { get; set; }
        public string Poi2 { get; set; }
        public string State { get; set; }
        public string PostalCode { get; set; }
        public double Latitude { get; set; }
        public double Longitude { get; set; }

        public VendorDriverLocation()
        {
        }
    }

    public class DispatchLocation
    {
        public DispatchContactInfo ContactInfo { get; set; }
        public string DropInstructions { get; set; }
        public string GarageClearanceHeight { get; set; }
        public string GarageLevel { get; set; }
        public bool? IsDriverWithVehicle { get; set; }
        public bool NightDropOff { get; set; }
        public bool? Parked { get; set; }
        public string Address1 { get; set; }
        public string Address2 { get; set; }
        public string CrossStreet { get; set; }
        public string Landmark { get; set; }
        public string LocationType { get; set; }
        public string City { get; set; }
        public string Poi1 { get; set; }
        public string Poi2 { get; set; }
        public string State { get; set; }
        public string PostalCode { get; set; }

        [JsonProperty("latitude")]
        public double Latitude { get; set; }

        [JsonProperty("longitude")]
        public double Longitude { get; set; }

        /// <summary>
        /// Returns Address, City State Zip
        /// </summary>
        /// <returns></returns>
        public override string ToString()
        {
            return Address1 +
                (!String.IsNullOrWhiteSpace(Address2) ? ", " + Address2 : "") +
                (!String.IsNullOrWhiteSpace(Address1) ? ", " : "") +
                City + " " +
                State + " " +
                PostalCode;
        }

        /// <summary>
        /// Fills in the passed EntryWaypoint with the relevant info from this object.
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<EntryWaypoint> ToWaypoint(EntryWaypoint input = null)
        {
            if (input == null)
                input = new EntryWaypoint();

            input.Address = this.ToString();
            input.Latitude = Convert.ToDecimal(this.Latitude);
            input.Longitude = Convert.ToDecimal(this.Longitude);
            input.Notes = this.DropInstructions;

            // TODO: Cross Street, Landmark

            // if lat/long didn't come over from agero, get it from google
            if ((input.Latitude == 0 || input.Longitude == 0) && !string.IsNullOrEmpty(Address1) && !string.IsNullOrEmpty(City))
            {
                var geo = await Extric.Towbook.Utility.GeocodeHelper.Geocode(input.Address);

                if (geo != null)
                {
                    input.Latitude = geo.Latitude;
                    input.Longitude = geo.Longitude;
                }
            }

            return input;
        }

        public DispatchLocation() { }
    }

    public class DispatchContactInfo
    {
        public string CallbackNumber { get; set; }
        public string Name { get; set; }
        public string Phone { get; set; }
        public DispatchContactInfo() { }
    }

    public class DispatchComment
    {
        public string CommentCode { get; set; }
        public string DisplayText { get; set; }
        public string CommentText { get; set; }
        public DispatchComment() { }
    }

    public class DispatchDispatcher
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string OfficeEmail { get; set; }
        public string OfficePhone { get; set; }
        public string ProfilePictureUrl { get; set; }
        public DateTime ProfileLastUpdateAt { get; set; }
        public bool OnDuty { get; set; }

        public DispatchDispatcher() { }
    }

    public class DispatchDetails
    {
        public string DispatchRequestNumber { get; set; }
        public string ReferenceNumber { get; set; }
        [JsonProperty("poNumber")]
        public string PurchaseOrderNumber { get; set; }
        public string ServiceType { get; set; }
        public string Problem { get; set; }
        public string Coverage { get; set; }
        public string Urgency { get; set; }
        public DateTime ReceivedTime { get; set; }
        public DispatchStatus CurrentStatus { get; set; }
        public int ETAStatusCode { get; set; }
        public DateTime ETA { get; set; }
        public int ETAInMinutes { get; set; }
        public DateTime RevisedETA { get; set; }
        public int RevisedETAInMinutes { get; set; }
        public int ETARevisionCount { get; set; }
        public DispatchEquipment Equipment { get; set; }
        public DispatchVehicle Vehicle { get; set; }
        public DispatchLocation DisablementLocation { get; set; }
        public DispatchLocation TowDestination { get; set; }
        public DispatchDriver AssignedDriver { get; set; }
        public List<DispatchComment> Comments { get; set; }
        public List<DispatchSignature> Signatures { get; set; }
        public DispatchDispatcher Dispatcher { get; set; }

        public DispatchCalculatedCost CalculatedCost { get; set; }
        public DispatchDrivingInfo DrivingInfo { get; set; }

        public CallReasonModel CallReason { get; set; }

        public DispatchDetails() { }
    }

    public class CallReasonModel
    {
        public string DisablementReason { get; set; }
        public string CallReasonSummary { get; set; }
        public string ServiceSummary { get; set; }
        public string FlexCallReason { get; set; }
        public string FlatPosition { get; set; }
        public object Lockout { get; set; }
        public string[] QuestionAnswers { get; set; }
    }

    public class DispatchCalculatedCost
    {

        public decimal DeadHeadLeg { get; set; }
        public decimal DisablementToDestination { get; set; }
        public decimal FacilityToDisablement { get; set; }
        public decimal ServiceFee { get; set; }
        public decimal TotalCost { get; set; }
    }
    public class DispatchDrivingInfo
    {
        public decimal FacilityToDisablementDistance { get; set; }
        public decimal DisablementToDestinationDistance { get; set; }
        public decimal DeadHeadLegDistance { get; set; }
        public decimal TotalDistance { get; set; }
    }

    public class DispatchEquipment
    {
        [JsonProperty("equipmentId")]
        public string Id { get; set; }
        public string Type { get; set; }
    }

    public class DispatchDriver
    {
        public DispatchDriverLocation CurrentLocation { get; set; }
        public int DriverId { get; set; }
        public string Status { get; set; }
        public string Name { get; set; }
        public DispatchDriver() { }
    }

    public class DispatchDriverLocation
    {
        public string Latitude { get; set; }
        public string Longitude { get; set; }
    }

    public class DispatchSignature
    {
        public int SignatureId { get; set; }
        public string SigneeName { get; set; }
        public int SigneeType { get; set; }
        public int SignatureReason { get; set; }
        public double Latitude { get; set; }
        public double Longitude { get; set; }
        public int DispatchStatusCode { get; set; }
        public DateTime CapturedDateTime { get; set; }
        public string SignatureUrl { get; set; }

        public DispatchSignature() { }
    }

    public class DispatchDocument
    {
        public int DocumentId { get; set; }
        public string DocumentName { get; set; }
        public string DocumentType { get; set; }
        public string DocumentUrl { get; set; }
        public DateTime LastUpdateTime { get; set; }
    }

    public class DispatchStatus
    {
        public string Code { get; set; }
        public string Description { get; set; }
        public int ETA { get; set; }
        public string Reason { get; set; }
        public int ReasonCode { get; set; }
        public string Source { get; set; }
        public string ExtJobId { get; set; }
        public double Latitude { get; set; }
        public double Longitude { get; set; }
        public DateTime StatusTime { get; set; }
        public int DriverId { get; set; }
        public string ReportedBy { get; set; }
        public string Event { get; set; }

        public DispatchStatus() { }
    }

    public class DriverEquipment
    {
        public string EquipmentId { get; set; }
        public string Type { get; set; }
        public int AddressId { get; set; }

        public DriverEquipment() { }
    }

    public class ProfileResponse
    {
        public int ProfileId { get; set; }
        public UserInfo UserInfo { get; set; }
        public ProfilePreferences Preferences { get; set; }
    }

    public class ProfileRequest
    {
        public UserInfo UserInfo { get; set; }
        public ProfilePreferences Preferences { get; set; }
        public List<Facility> Facilities { get; set; }
        public DriverEquipment Equipment { get; set; }
    }

    public class Facility
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string Address { get; set; }
        public string City { get; set; }
        public string State { get; set; }
        public string ZipCode { get; set; }
        [JsonProperty("lat")]
        public double Latitude { get; set; }
        [JsonProperty("lon")]
        public double Longitude { get; set; }

        public string PrimaryEmailAddress { get; set; }
        public string AlternateEmailAddress { get; set; }

    }

    public class ProfilePreferences
    {
        public string JobFilter { get; set; }
        public string JobViewOption { get; set; }
        public bool GpsTracking { get; set; }
        public bool InactivityAlert { get; set; }
        public bool AutoAssignAllJobs { get; set; }
        public bool ETAAlerts { get; set; }
    }

    public class UserInfo
    {
        public string Name { get; set; }
        public string CellPhone { get; set; }
        public string Email { get; set; }
        public string OfficeEmail { get; set; }
        public string OfficePhone { get; set; }
    }
    public enum PushNotificationType
    {
        None = 0,
        NewJobOffered = 1,
        JobAssigned = 2,
        JogReAssigned = 3,
        ETAEvaluation = 4,
        ETARejected = 5,
        JobApproved = 6
    }

    public class NotificationPayload
    {
        public int DispatchRequestNumber { get; set; }
        public string Title { get; set; }
        public string Message { get; set; }
    }
    public enum EnumeratorType
    {
        None = 0,
        SigneeTypes = 1,
        SignatureReasonTypes = 2,
        SignatureLocationTypes = 3,
        NotificationTypes = 4,
        DispatchStatus = 5
    }

    public class Enumerator
    {
        public int Code { get; set; }
        public string Description { get; set; }
    }

    public class HeatMapEnrollment
    {
        public string HeatMapEnrollmentId { get; set; }
        public string HeatMapUrl { get; set; }
        public string AuthorizationToken { get; set; }
        public HeatMapEnrollment() { }
    }

    public class ServerNotificationSubscription
    {
        public long SubscriptionId { get; set; }
        public string CallBackUrl { get; set; }
        public string NotificationToken { get; set; }
        public string NotificationEvents { get; set; }
    }

    public enum AcceptDispatchETAReason
    {
        None = 0,
        Weather_Emergency = 401,
        Extreme_Traffic_in_the_Area = 403
    }

    public enum CancelDispatchStatusCode
    {
        None = 0,
        SPCancel = 70,
        CustomerCancel = 80,
        VehicleNotFound = 90,
        CustomerNotWithVehicle = 95,
        ServiceAttemptFailed = 99
    }

    public enum ChangeStatusDispatchStatusCode
    {
        Unassigned = 0,
        New = 2,
        Pending = 3,
        Expired = 4,
        Rejected = 5,
        Refused = 6,
        Assigned = 10,
        InRoute = 20,
        OnScene = 30,
        TowInProgress = 40,
        DestinationArrival = 50,
        JobCleared = 60,
        SPCancel = 70,
        CustomerCancel = 80,
        NoVehicle = 90,
        NoCustomer = 95,
        AttemptFailed = 99
    }

    public enum DispatchStatusReason
    {
        None = 0,
        PriorJobDelayed = 201,
        TrafficVehicleProblem = 202,
        OutOfArea = 203,
        AnotherJobPriority = 204,
        NoReasonGiven = 205,
        FoundAlternateSolution = 101,
        ProblemSelfCorrected = 102,
        ChangeMind = 103,
        CustomerCancel = 210,
        SPCustomerCancel = 3005,
        WrongLocationGiven = 207,
        CustomerNotWithVehicle = 206,
        GoaIncorrectService = 208,
        GoaIncorrectEquipment = 209,
        UnsWrongEquipment = 214,
        UnsJumpDidNotWork = 215,
        UnsWrongService = 216,
        UnsNeedAdditionalEquipment = 217,
        SPIncorrectService = 3003,
        SPIncorrectEquipment = 3004
    }

    public enum AgeroNotificationType
    {
        None = 0,
        NewJobOffered = 1,
        JobRefused = 2,
        JobAccepted = 3,
        JobAssigned = 4,
        JobCancelled = 5,
        JobReassigned = 6,
        EtaRejected = 7,
        JobApproved = 8,
        EtaApproved = 9,
        TimeToLeave = 10,
        EtaWillNotMet = 11,
        EtaExpired = 12,
        DispatchUpdate = 13,
        DispatchExpired = 14,
        DispatchCancelled = 15,
        EtaAccepted = 16,
        TestNotification = 17,
        SystemNotification = 18
    }

    public enum AgeroRejectReasonCode
    {
        None = 0,
        PaymentTypeNotAccepted = 400,
        EquipmentNotAvailable = 406,
        OutOfCoverageArea = 409,
        Other = 503
    }


    public class VendorDriverEquipment
    {
        public string VehicleNickname { get; set; }

        public string EquipmentPictureUrl { get; set; }
        public DateTime lastUpdatedAt { get; set; }

        public int AddressId { get; set; }
        public string Type { get; set; }
        public string EquipmentId { get; set; }
    }

    # region HeatMapData

    public class HeatMapDataStatus
    {
        public string Code { get; set; }
        public HeatMapDataStatus() { }
    }
    public class HeatMapDataLocation
    {
        public string Address1 { get; set; }
        public string Address2 { get; set; }
        public string CrossStreet { get; set; }
        public string Landmark { get; set; }
        public string LocationType { get; set; }
        public string City { get; set; }
        public string Poi1 { get; set; }
        public string Poi2 { get; set; }
        public string State { get; set; }
        public string PostalCode { get; set; }
        public double Lat { get; set; }
        public double Lng { get; set; }
        public HeatMapDataLocation() { }
    }
    public class HeatMapDataEquipment
    {
        public string Code { get; set; }
        public HeatMapDataEquipment() { }
    }

    public class HeatMapArray
    {
        public int HeatMapDataCount { get; set; }

        public HeatMapData[] HeatMapData { get; set; }
    }

    public class HeatMapData
    {
        public string VendorId { get; set; }
        public string DriverName { get; set; }
        public HeatMapDataStatus DispatchStatus { get; set; }
        public HeatMapDataLocation CurrentLocation { get; set; }
        public HeatMapDataLocation DisablementLocation { get; set; }
        public HeatMapDataEquipment Equipment { get; set; }
        public HeatMapDataLocation TowDestination { get; set; }
        public HeatMapData() { }
    }

    #endregion

    #region 3rd Party Types

    public class ExternalDriverEquipment
    {
        public string EquipmentId { get; set; }
        public int AddressId { get; set; }
        public string Type { get; set; }
        public string VehicleNickname { get; set; }
        public string EquipmentPictureUrl { get; set; }
        public DateTime LastUpdatedAt { get; set; }
        public ExternalDriverEquipment() { }
    }

    public class ExternalDriverProfile
    {
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string DriverId { get; set; }
        public string Name { get; set; }
        public ExternalDriverEquipment Equipment { get; set; }
        public string OfficeEmail { get; set; }
        public string OfficePhone { get; set; }
        public bool GpsTracking { get; set; }
        public string ProfilePictureUrl { get; set; }
        public DateTime ProfileLastUpdatedAt { get; set; } = DateTime.Now;
        public string ExternalDriverId { get; set; }

        public ExternalDriverProfile() { }
    }

    #endregion

}
