using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Net;
using Agero.Types;
using Extric.Towbook.Configuration;
using Extric.Towbook.Utility;
using Newtonsoft.Json;
using RestSharp;
using RestSharp.Authenticators.OAuth2;


namespace Agero
{
    public class AgeroRestClient
    {
        public const string URL_BASE = "https://api.enterprise.agero.com/";
        public const string CONSUMER_KEY = "1fI2pnfo42fOTzWZEhySJfg3LxY7JiSt";
        public const string CONSUMER_SECRET = "AVBASAmbdl4hoTWU";

        /// <summary>
        /// Base-64 encoded version of the consumer key:consumer secret.
        /// </summary>
        public const string CONSUMER_BASE64 = "MWZJMnBuZm80MmZPVHpXWkVoeVNKZmczTHhZN0ppU3Q6QVZCQVNBbWJkbDRob1RXVQ==";

        private static RestClient _restClient;
        private static RestClient _restClientAuthBase64;
        private static RestClient _restClientAuth;

        public AgeroRestClient() { }

        public static RestClient GetRestClient(string accessToken)
        {
            if (accessToken == null && _restClient != null)
                return _restClient;

            var options = new RestClientOptions(URL_BASE)
            {
                UserAgent = "Towbook/4.0.0"
            };

            if (accessToken != null)
            {
                options.Authenticator = new OAuth2AuthorizationRequestHeaderAuthenticator(accessToken, "Bearer");
                return new RestClient(AppServicesHelper.HttpClient, options);
            }

            return _restClient = new RestClient(AppServicesHelper.HttpClient, options);
        }

        public static RestClient GetRestClient(bool consumerBase64 = true)
        {
            if (consumerBase64 && _restClientAuthBase64 != null)
                return _restClientAuthBase64;

            if (!consumerBase64 && _restClientAuth != null)
                return _restClientAuth;

            var options = new RestClientOptions(URL_BASE)
            {
                Authenticator = new OAuth2AuthorizationRequestHeaderAuthenticator(
                consumerBase64 ? CONSUMER_BASE64 : CONSUMER_KEY, ""),
                UserAgent = "Towbook/4.0.0"
            };

            var client = new RestClient(AppServicesHelper.HttpClient, options);
            if (consumerBase64)
                _restClientAuthBase64 = client;
            else
                _restClientAuth = client;

            return client;
        }

        #region Authentication Jobs

        public OAuthAccessTokenResponse OAuthAccessToken(string code)
        {
            var request = new RestRequest("/v1/id/oauth/accesstoken", Method.Get);
            request.AddParameter("grant_type", "authorization_code");
            request.AddParameter("code", code);
            //request.AddHeader("Authorization", CONSUMER_BASE64);
            var response = GetRestClient().Execute(request);

            string r = response.Content;

            dynamic json = JsonConvert.DeserializeObject<dynamic>(r);

            if (response.StatusCode != HttpStatusCode.OK)
                CustomizeExceptions(response);

            return new OAuthAccessTokenResponse()
            {
                AccessToken = json.token.access_token,
                RefreshToken = json.token.refresh_token,
                VendorId = Convert.ToInt32(json.userClaim.vendorId),
                ExpiresIn = json.token.expires_in
            };
        }

        public OAuthRefreshTokenResponse OAuthRefreshToken(string refresh_token)
        {
            var rx = AgeroSession.GetByRefreshToken(refresh_token);

            if (rx == null)
                throw new AgeroException("Couldn't locate refresh_token in AgeroSessions");

            var request = new RestRequest("/v1/id/oauth/refreshToken", Method.Post);
            request.AddParameter("grant_type", "refresh_token", ParameterType.GetOrPost);
            request.AddParameter("refresh_token", refresh_token, ParameterType.GetOrPost);

            var response = GetRestClient().Execute(request);

            string r = response.Content;
            Console.WriteLine(r);

            dynamic json = JsonConvert.DeserializeObject<dynamic>(r);

            if (response.StatusCode != HttpStatusCode.OK)
                CustomizeExceptions(response);

            AgeroSession ags = AgeroSession.Get(rx.CompanyId, rx.AccountId, Convert.ToInt32(json.vendorId));
            ags.AccessToken = json.access_token;
            ags.RefreshToken = json.refresh_token;
            ags.ExpirationDate = DateTime.Now.AddSeconds(json.expires_in);
            ags.IpAddress = "0.0.0.0";
            ags.OwnerUserId = rx.OwnerUserId;
            ags.Save();

            return new OAuthRefreshTokenResponse()
            {
                AccessToken = json.token.access_token,
                RefreshToken = json.token.refresh_token,
                ExpiresIn = json.token.expires_in,
                RefreshCount = json.token.refresh_count
            };
        }

        private RestResponse Execute(RestRequest req, string accessToken)
        {
            if (req == null)
                throw new ArgumentException("IRestRequest passed in is null.", "req");

            if (accessToken == null)
                throw new ArgumentException("accessToken passed in is null.", "accessToken");

            req.AddParameter("Authorization", "Bearer " + accessToken, ParameterType.HttpHeader);

            return _sharedRestClient.Execute(req);
        }

        public OAuthSignInResponse OAuthSignIn(string clientId, string accessToken)
        {
            var request = new RestRequest("/v1/id/oauth/signIn", Method.Post);

            request.AddParameter("application/json", new { clientId = clientId }.ToJson(), ParameterType.RequestBody);

            var response = Execute(request, accessToken);

            string r = response.Content;

            dynamic json = JsonConvert.DeserializeObject<dynamic>(r);

            if (response.StatusCode != HttpStatusCode.OK)
                CustomizeExceptions(response);

            return new OAuthSignInResponse()
            {
                VendorId = Convert.ToInt32(json.vendorId),
                Username = json.username,
                Role = json.role
            };

        }

        public void OAuthSignOut(string clientId, string accessToken)
        {
            var request = new RestRequest("/v1/id/oauth/signOut", Method.Post);

            request.AddParameter("application/json", new { clientId = clientId }.ToJson(), ParameterType.RequestBody);

            var response = Execute(request, accessToken);

            string r = response.Content;

            if (response.StatusCode != HttpStatusCode.OK)
                CustomizeExceptions(response);
        }

        public class AgeroPhotoModel
        {
            public string EventSource { get; set; } = "Image";
            public int Id { get; set; }
            public DateTime CreateDate { get; set; }
            public string Url { get; set; }
            public long FileSize { get; set; }
            public decimal? Latitude { get; set; }
            public decimal? Longitude { get; set; }
            public string VendorId { get; set; }
            public string DispatchRequestNumber { get; set; }
            public string PurchaseOrderNumber { get; set; }

            public string DispatchStatus { get; set; }

            public AgeroPhotoModel() { }
            public AgeroPhotoModel(int id, DateTime createDate, string url, decimal? lat, decimal? lng, long contentLength,
                string vendorId,
                string dispatchRequestNumber,
                string purchaseOrderNumber)
            {
                Id = id;
                CreateDate = createDate;
                Url = url;
                Latitude = lat;
                Longitude = lng;
                FileSize = contentLength;
                VendorId = vendorId;
                DispatchRequestNumber = dispatchRequestNumber;
                PurchaseOrderNumber = purchaseOrderNumber;
            }
        }


        #endregion


        public void SharePhoto(AgeroPhotoModel p)
        {
            var request = new RestRequest("/am-gateway-webhook/towbook", RestSharp.Method.Post);

            request.AddParameter("Authorization", "RWxyc0NUUVBDQmsxb2tjSk9EbTUyWVVtSEVtcWZkbHU6a05QcmhpMjZTMkxwYlpuMA==", RestSharp.ParameterType.HttpHeader);
            request.AddParameter("application/json", p.ToJson(), RestSharp.ParameterType.RequestBody);

            var response = _restClient.Execute(request);

            Console.WriteLine("**********");
            Console.WriteLine(response.Content);
        }



        public class AgeroSubStatusModel
        {
            public string Id { get; set; } = "0";
            public string EventSource { get; set; } = "DispatchStatus";
            public string VendorId { get; set; }
            public string DispatchRequestNumber { get; set; }
            public string PurchaseOrderNumber { get; set; }
            public object ReferenceNumber { get; set; }
            public DateTime CreateDate { get; set; }
            public string DispatchStatus { get; set; }
            public string DispatchSubStatus { get; set; }
            public decimal Latitude { get; set; }
            public decimal Longitude { get; set; }
        }

        public void SendSubStatus(AgeroSubStatusModel p, bool testMode = false)
        {
            RestClient client;

            if (testMode)
            {
                var options = new RestClientOptions("https://api-test.enterprise.agero.com/am-gateway-webhook/towbook")
                {
                    UserAgent = "Towbook/4.0.0"
                };
                client = new RestClient(AppServicesHelper.HttpClient, options);
            }
            else
            {
                client = _restClient;
            }

            var key = "eG1RQWhESXJHM1BwTGdFQkV4Szc2QXAwYzJBRXhBd2M6b1ZWbjlMWU9TbXBNRkxYaw==";
            var request = new RestRequest("/am-gateway-webhook/towbook", RestSharp.Method.Post);
            if (!testMode)
                key = "RWxyc0NUUVBDQmsxb2tjSk9EbTUyWVVtSEVtcWZkbHU6a05QcmhpMjZTMkxwYlpuMA==";
            request.AddParameter("Authorization", key, RestSharp.ParameterType.HttpHeader);
            request.AddParameter("application/json", p.ToJson(), RestSharp.ParameterType.RequestBody);

            var response = client.Execute(request);
            Console.WriteLine("**********");
            Console.WriteLine(response.Content);
        }


        #region Server to Server / Webhook support

        public void ServerNotificationsSubscribe(string accessToken, string notificationToken, int[] notificationEvents)
        {
            var request = new RestRequest("/v1/id/serverNotifications/subscribe", Method.Post);
            request.AddParameter("application/json", new
            {
                callBackUrl = "https://api.towbook.com/receivers/agero/notify",
                notificationToken = notificationToken,
                notificationEvents = String.Join(",", notificationEvents)
            }.ToJson(), ParameterType.RequestBody);

            var response = Execute(request, accessToken);

            string r = response.Content;

            if (response.StatusCode != HttpStatusCode.OK && response.StatusCode != HttpStatusCode.Created)
                CustomizeExceptions(response);
        }

        public void ServerNotificationsAcknowledge(string accessToken, int id)
        {
            var url = string.Format("/v1/id/serverNotifications/{0}/acknowledge", id);
            var request = new RestRequest(url, Method.Put);

            var response = Execute(request, accessToken);

            string r = response.Content;

            // 11/13/2015 - Conference Call with Agero - Mary Reese, Niton; This method isn't used by 
            // Agero on their side yet and errors returned by calling it can safely be ignored.

            return;

            //if (response.StatusCode != HttpStatusCode.OK)
            //    CustomizeExceptions(response, url);
        }

        public ServerNotificationSubscription[] GetServerNotificationSubscriptions(string accessToken, int vendorId)
        {
            string uri = string.Format("/v1/id/vendors/{0}/serverNotifications/subscriptions", vendorId);

            var request = new RestRequest(uri, Method.Get);
            var response = Execute(request, accessToken);

            string r = response.Content;
            dynamic json = JsonConvert.DeserializeObject<dynamic>(r);

            if (response.StatusCode != HttpStatusCode.OK)
                CustomizeExceptions(response);

            List<ServerNotificationSubscription> result = new List<ServerNotificationSubscription>();
            foreach (var subs in json)
                result.Add(JsonConvert.DeserializeObject<ServerNotificationSubscription>(subs.ToString()));

            return result.ToArray();
        }

        #endregion

        #region Heatmap

        public string ServerHeatmapEnroll()
        {
            var request = new RestRequest("/v1/id/heatmap/enrollments/", Method.Post);
            request.AddParameter("application/json", new
            {
                heatMapUrl = "https://api.towbook.com/receivers/agero/heatmap/",
                authorizationToken = "Pss4bp8jZvftDrJnnEtskw7S"
            }.ToJson(), ParameterType.RequestBody);

            var response = GetRestClient().Execute(request);

            string r = response.Content;
            dynamic json = JsonConvert.DeserializeObject<dynamic>(r);

            if (response.StatusCode != HttpStatusCode.OK && response.StatusCode != HttpStatusCode.Created)
                CustomizeExceptions(response);

            return json.heatMapEnrollmentId;
        }

        public void ServerHeatmapDeEnroll(string enrollmentId)
        {
            var request = new RestRequest("/v1/id/heatmap/enrollments/" + enrollmentId, Method.Delete);

            var response = GetRestClient().Execute(request);

            string r = response.Content;

            dynamic json = JsonConvert.DeserializeObject<dynamic>(r);
            if (response.StatusCode != HttpStatusCode.OK)
                CustomizeExceptions(response);
        }

        public HeatMapEnrollment GetHeatMapEnrollment(int enrollmentId)
        {
            var request = new RestRequest("/v1/id/heatmap/enrollments/" + enrollmentId, Method.Get);
            var response = GetRestClient().Execute(request);

            string r = response.Content;
            dynamic json = JsonConvert.DeserializeObject<dynamic>(r);

            if (response.StatusCode != HttpStatusCode.OK)
                CustomizeExceptions(response);

            return JsonConvert.DeserializeObject<HeatMapEnrollment>(json.ToString());
        }

        public HeatMapEnrollment[] GetHeatMapEnrollments()
        {
            var request = new RestRequest("/v1/id/heatmap/enrollments", Method.Get);
            var response = GetRestClient().Execute(request);

            string r = response.Content;
            dynamic json = JsonConvert.DeserializeObject<dynamic>(r);

            if (response.StatusCode != HttpStatusCode.OK)
                CustomizeExceptions(response);

            List<HeatMapEnrollment> result = new List<HeatMapEnrollment>();
            foreach (var enr in json)
                result.Add(JsonConvert.DeserializeObject<HeatMapEnrollment>(enr.ToString()));

            return result.ToArray();
        }


        public void PostHeatmapData(int enrollmentRequestId, HeatMapArray data)
        {
            var request = new RestRequest("/v1/id/heatmap/" + enrollmentRequestId + "/data", Method.Post);
            request.AddParameter("application/json", data.ToJson(), ParameterType.RequestBody);
            var response = GetRestClient().Execute(request);

            string r = response.Content;

            dynamic json = JsonConvert.DeserializeObject<dynamic>(r);
            if (response.StatusCode != HttpStatusCode.OK)
                CustomizeExceptions(response);
        }

        #endregion

        #region Job Accept/Refuse support

        public void RefuseDispatchRequest(string accessToken, int dispatchNumber, AgeroRejectReasonCode reasonCodeId = AgeroRejectReasonCode.Other)
        {
            if (Extric.Towbook.Core.GetRedisValue("ops/mcds/Agero_RefuseDispatchRequest_Disable") == "1")
                throw new AgeroServiceUnavailable("ops/mcds/Agero_RefuseDispatchRequest_Disable is set to 1.");

            if (reasonCodeId == AgeroRejectReasonCode.None)
                throw new AgeroException("Local Error: Not sending refuse request; you didn't specify a reasonCodeId.");

            var request = new RestRequest(string.Format("/v1/id/dispatches/{0}/refuseDispatchRequest", dispatchNumber), Method.Post);

            request.AddParameter("application/json", new
            {
                reasonCode = Convert.ToInt32(reasonCodeId).ToString()
            }.ToJson(), ParameterType.RequestBody);

            var response = Execute(request, accessToken);

            string r = response.Content;

            if (response.StatusCode != HttpStatusCode.OK)
                CustomizeExceptions(response);
        }

        public void AcceptDispatchRequest(string accessToken, int dispatchNumber, int eta, AcceptDispatchETAReason reason = AcceptDispatchETAReason.None)
        {
            if (Extric.Towbook.Core.GetRedisValue("ops/mcds/Agero_AcceptDispatchRequest_Disable") == "1")
                throw new AgeroServiceUnavailable("ops/mcds/Agero_AcceptDispatchRequest_Disable is set to 1.");

            var request = new RestRequest(string.Format("/v1/id/dispatches/{0}/acceptDispatchRequest", dispatchNumber), Method.Post);

            request.AddParameter("application/json", new
            {
                eta = eta,
                reasonCode = (int)reason
            }.ToJson(), ParameterType.RequestBody);

            var response = Execute(request, accessToken);

            string r = response.Content;

            if (response.StatusCode != HttpStatusCode.OK)
                CustomizeExceptions(response);
        }

        public void RequestPhoneCallDispatchRequest(string accessToken, int dispatchNumber, string callbackNumber, string name, string phone)
        {
            if (callbackNumber == null)
                throw new ArgumentException("callbackNumber is required but it was passed as null.", "callbackNumber");
            callbackNumber = callbackNumber.Replace("-", "").Replace(" ", "").Replace(".", "").Replace("(", "").Replace(")", "");
            if (callbackNumber.Length == 11 && callbackNumber[0] == '1')
                callbackNumber = callbackNumber.Substring(1);


            if (callbackNumber.Length != 10)
            {
                throw new AgeroException("Towbook intercepted request before sending to Agero: Phone number must be 10 characters in length. Phone number is " +
                    callbackNumber.Length + " chars long. Value is: " + callbackNumber + " for dispatchNumber: " + dispatchNumber);
            }

            var request = new RestRequest(string.Format("/v1/id/dispatches/{0}?action=requestPhoneCall", dispatchNumber), Method.Post);
            request.AddParameter("application/json", new
            {
                callbackNumber = callbackNumber,
                name = name,
                phone = phone
            }.ToJson(), ParameterType.RequestBody);

            var response = Execute(request, accessToken);

            string r = response.Content;

            if (response.StatusCode != HttpStatusCode.OK)
                CustomizeExceptions(response);
        }

        public void CancelDispatchRequest(string accessToken, int dispatchNumber, CancelDispatchStatusCode code, DispatchStatusReason reason)
        {
            var request = new RestRequest(string.Format("/v1/id/dispatches/{0}/cancel", dispatchNumber), Method.Post);
            request.AddParameter("application/json", new
            {
                statusCode = Convert.ToInt32(code).ToString(),
                reasonCode = Convert.ToInt32(reason).ToString()
            }.ToJson(), ParameterType.RequestBody);

            var response = Execute(request, accessToken);

            string r = response.Content;
            if (response.StatusCode != HttpStatusCode.OK)
                CustomizeExceptions(response);
        }

        #endregion

        #region Dispatch Detail Jobs

        public DispatchDetails GetDispatchDetail(string accessToken, int dispatchNumber)
        {
            var url = String.Format("/v1/id/dispatches/{0}/detail", dispatchNumber);
            var request = new RestRequest(url, Method.Get);

            var response = Execute(request, accessToken);

            string r = response.Content;

            DispatchDetails result = null;

            if (response.StatusCode != HttpStatusCode.OK)
            {
                CustomizeExceptions(response, url);
            }
            else
            {
                result = JsonConvert.DeserializeObject<DispatchDetails>(r);
            }

            if (result?.Comments != null)
                result.Comments = result.Comments.Where(o => o.CommentCode != "INC").ToList();

            return result;
        }

        public DispatchDetails GetDispatchDetail(string accessToken, string poNumber)
        {
            var url = String.Format("/v1/id/dispatches/poNumbers/{0}/detail", poNumber);
            var request = new RestRequest(url, Method.Get);
            var response = Execute(request, accessToken);

            string r = response.Content;
            dynamic json = JsonConvert.DeserializeObject<dynamic>(r);

            if (response.StatusCode != HttpStatusCode.OK)
                CustomizeExceptions(response, url);

            DispatchDetails result = new DispatchDetails();
            result = JsonConvert.DeserializeObject<DispatchDetails>(json.ToString());
            return result;
        }



        public DispatchDriver GetDispatchAssignedDriver(string accessToken, int dispatchNumber)
        {
            var url = string.Format("/v1/id/dispatches/{0}/assignedDriver", dispatchNumber);
            var request = new RestRequest(url, Method.Get);
            var response = Execute(request, accessToken);

            string r = response.Content;
            dynamic json = JsonConvert.DeserializeObject<dynamic>(r);

            if (response.StatusCode != HttpStatusCode.OK)
                CustomizeExceptions(response, url);

            return JsonConvert.DeserializeObject<DispatchDriver>(json.ToString());
        }

        public void AssignDriverToDispatch(string accessToken, int dispatchNumber, int driverId)
        {
            var url = string.Format("/v1/id/dispatches/{0}/assignedDriver", dispatchNumber);
            var request = new RestRequest(url, Method.Put);

            request.AddParameter("application/json", new
            {
                driverId = driverId.ToString()
            }.ToJson(), ParameterType.RequestBody);

            var response = Execute(request, accessToken);
            string r = response.Content;

            if (response.StatusCode != HttpStatusCode.OK)
                CustomizeExceptions(response, url);
        }


        public class AssignExternalDriverToDispatchModel
        {
            public string externalDriverId { get; set; }
            public AssignExternalDriverToDispatchModelDriverProfile driverProfile { get; set; }
        }

        public class AssignExternalDriverToDispatchModelDriverProfile
        {
            public string Name { get; set; }
            public string ExternalDriverId { get; set; }
            public AssignExternalDriverToDispatchModelEquipment Equipment { get; set; }
            public string OfficeEmail { get; set; }
            public string OfficePhone { get; set; }
            public bool GpsTracking { get; set; }
            public bool OnDuty { get; set; }
            public string ProfilePictureURL { get; set; }
        }

        public class AssignExternalDriverToDispatchModelEquipment
        {
            public string EquipmentId { get; set; }
            public int AddressId { get; set; }
            public string Type { get; set; }
            public string VehicleNickname { get; set; }
        }

        public void AssignExternalDriverToDispatch(string accessToken, int dispatchNumber, int externalDriverId,
            AssignExternalDriverToDispatchModelDriverProfile driver, int vendorId)
        {
            if (driver == null)
                throw new Exception("AgeroRestClient.AssignExternalDriverToDispatch: driver is null");

            driver.OfficePhone = Extric.Towbook.Core.FormatPhoneWithDashesOnly(driver.OfficePhone) ?? "";
            if (!Extric.Towbook.Core.IsPhoneValidStandard(driver.OfficePhone) && driver.OfficePhone.Length > 12)
                driver.OfficePhone = Extric.Towbook.Core.FormatPhoneWithDashesOnly(driver.OfficePhone.Truncate(12));

            var url = string.Format("/v1/id/dispatches/{0}/assignExternalDriver", dispatchNumber);
            var request = new RestRequest(url, Method.Put);

            request.AddParameter("application/json", new
            {
                externalDriverId = externalDriverId.ToString(),
                driverProfile = driver
            }.ToJson(), ParameterType.RequestBody);

            var response = Execute(request, accessToken);

            if (response.StatusCode != HttpStatusCode.OK)
                CustomizeExceptions(response, url);

            if (Extric.Towbook.Core.GetRedisValue("agero_driver_external:" + vendorId + "_" + externalDriverId) == null)
            {

                ExternalUpdateDriverProfile(vendorId, externalDriverId, new ExternalDriverProfile()
                {
                    ExternalDriverId = externalDriverId.ToString(),
                    Name = driver.Name,
                    OfficeEmail = driver.OfficeEmail,
                    OfficePhone = driver.OfficePhone,
                    GpsTracking = true
                });

                Extric.Towbook.Core.SetRedisValue("agero_driver_external:" + vendorId + "_" + externalDriverId, DateTime.Now.ToJson(), TimeSpan.FromDays(7));
            }
        }

        public int AddDispatchSignature(
            string accessToken,
            int dispatchNumber,
            string signeeName,
            string signeeType,
            string locationType,
            string signatureReason,
            double latitude,
            double longitude,
            string dispatchStatusCode,
            string signatureFileName,
            byte[] signatureFile)
        {
            var request = new RestRequest(string.Format("/v1/id/dispatches/{0}/signatures", dispatchNumber), Method.Post);

            request.AlwaysMultipartFormData = true;
            request.AddParameter("signeeName", signeeName, ParameterType.RequestBody);
            request.AddParameter("signeeType", signeeType, ParameterType.RequestBody);
            request.AddParameter("locationType", locationType, ParameterType.RequestBody);
            request.AddParameter("signatureReason", signatureReason, ParameterType.RequestBody);
            request.AddParameter("longitude", longitude, ParameterType.RequestBody);
            request.AddParameter("latitude", latitude, ParameterType.RequestBody);
            request.AddParameter("dispatchStatusCode", dispatchStatusCode, ParameterType.RequestBody);

            request.AddFile("signature", signatureFile, signatureFileName, "image/jpeg");

            var response = Execute(request, accessToken);

            string r = response.Content;
            dynamic json = JsonConvert.DeserializeObject<dynamic>(r);

            if (response.StatusCode != HttpStatusCode.OK)
                CustomizeExceptions(response);

            return json.signatureId;
        }

        public DispatchSignature[] GetDispatchSignatures(string accessToken, int dispatchNumber)
        {
            var request = new RestRequest(string.Format("/v1/id/dispatches/{0}/signatures", dispatchNumber), Method.Get);
            var response = Execute(request, accessToken);

            string r = response.Content;
            dynamic json = JsonConvert.DeserializeObject<dynamic>(r);
            if (response.StatusCode != HttpStatusCode.OK)
                CustomizeExceptions(response);

            List<DispatchSignature> result = new List<DispatchSignature>();
            foreach (var sign in json.results)
                result.Add(JsonConvert.DeserializeObject<DispatchSignature>(sign.ToString()));

            return result.ToArray();
        }

        public DispatchSignature GetDispatchSignature(string accessToken, int dispatchNumber, int signatureId)
        {
            var request = new RestRequest(string.Format("/v1/id/dispatches/{0}/signatures/{1}", dispatchNumber, signatureId), Method.Get);
            var response = Execute(request, accessToken);

            string r = response.Content;
            dynamic json = JsonConvert.DeserializeObject<dynamic>(r);
            if (response.StatusCode != HttpStatusCode.OK)
                CustomizeExceptions(response);

            return JsonConvert.DeserializeObject<DispatchSignature>(json.ToString());
        }

        public int AddDispatchDocument(
            string accessToken,
            int dispatchNumber,
            string documentFileName,
            byte[] documentFile,
            string documentType,
            string documentMimeType)
        {
            var request = new RestRequest(string.Format("/v1/id/dispatches/{0}/documents", dispatchNumber), Method.Post);

            request.AlwaysMultipartFormData = true;
            request.AddParameter("documentType", documentType, ParameterType.RequestBody);

            request.AddFile("document", documentFile, documentFileName, documentMimeType);

            var response = Execute(request, accessToken);

            string r = response.Content;
            dynamic json = JsonConvert.DeserializeObject<dynamic>(r);
            if (response.StatusCode != HttpStatusCode.OK)
                CustomizeExceptions(response);

            return json.documentId;
        }

        public void DeleteDispatchDocument(string accessToken, int dispatchNumber, int documentId)
        {
            var request = new RestRequest(string.Format("/v1/id/dispatches/{0}/documents/{1}", dispatchNumber, documentId), Method.Delete);
            var response = Execute(request, accessToken);

            if (response.StatusCode != HttpStatusCode.OK)
                CustomizeExceptions(response);
        }

        public DispatchComment GetDispatchDocument(string accessToken, int dispatchNumber, int documentId)
        {
            var request = new RestRequest(string.Format("/v1/id/dispatches/{0}/documents/{1}", dispatchNumber, documentId), Method.Get);
            var response = Execute(request, accessToken);

            string r = response.Content;
            dynamic json = JsonConvert.DeserializeObject<dynamic>(r);
            if (response.StatusCode != HttpStatusCode.OK)
                CustomizeExceptions(response);

            return JsonConvert.DeserializeObject<DispatchDocument>(json.ToString());
        }

        public DispatchStatus GetDispatchCurrentStatus(string accessToken, int dispatchNumber)
        {
            var request = new RestRequest(string.Format("/v1/id/dispatches/{0}/status", dispatchNumber), Method.Get);
            var response = Execute(request, accessToken);

            string r = response.Content;
            dynamic json = JsonConvert.DeserializeObject<dynamic>(r);
            if (response.StatusCode != HttpStatusCode.OK)
                CustomizeExceptions(response);

            return JsonConvert.DeserializeObject<DispatchStatus>(json.ToString());
        }

        public void ChangeDispatchStatus(
            string accessToken,
            int dispatchNumber,
            ChangeStatusDispatchStatusCode code,
            string reason,
            int? eta,
            int extJobId,
            string source,
            double? latitude,
            double? longitude,
            DateTime statusTime,
            int externalDriverId = 0)
        {
            if (code == ChangeStatusDispatchStatusCode.Unassigned)
                throw new AgeroException("Unassigned isn't a valid option for ChangeDispatchStatus");

            string url = string.Format("/v1/id/dispatches/{0}/status", dispatchNumber);
            var request = new RestRequest(url, Method.Post);

            dynamic body = new ExpandoObject();

            body.code = Convert.ToInt32(code).ToString();
            body.reason = reason;
            body.eta = eta;
            body.extJobId = extJobId;
            body.source = source;

            /*
             * dont pass this - we're assigning driver with Assignexternaldriver api call instead. this causes agero to wipe the driver.
             * 
            if (externalDriverId > 0)
                body.driverProfile = new
                {
                    externalDriverId = externalDriverId.ToString()
                };*/

            if (latitude != null && longitude != null)
            {
                body.latitude = latitude;
                body.longitude = longitude;
            }

            body.statusTime = statusTime;

            string jsonRequest = JsonExtensions.ToJson(body);
            request.AddParameter("application/json", jsonRequest, ParameterType.RequestBody);

            var response = Execute(request, accessToken);

            if (response.StatusCode != HttpStatusCode.OK)
                CustomizeExceptions(response, url, jsonRequest);
        }

        public DispatchStatus[] GetDispatchStatusHistory(string accessToken, int dispatchNumber)
        {
            var request = new RestRequest(string.Format("/v1/id/dispatches/{0}/status/history", dispatchNumber), Method.Get);
            var response = Execute(request, accessToken);

            string r = response.Content;
            dynamic json = JsonConvert.DeserializeObject<dynamic>(r);
            if (response.StatusCode != HttpStatusCode.OK)
                CustomizeExceptions(response);

            var result = new List<DispatchStatus>();

            foreach (var sts in json.statusHistory)
                result.Add(JsonConvert.DeserializeObject<DispatchStatus>(sts.ToString()));

            return result.ToArray();
        }

        public DispatchStatus UndoDispatchCurrentStatus(string accessToken, int dispatchNumber)
        {
            var request = new RestRequest(string.Format("/v1/id/dispatches/{0}/status/undo", dispatchNumber), Method.Post);
            var response = Execute(request, accessToken);

            string r = response.Content;
            dynamic json = JsonConvert.DeserializeObject<dynamic>(r);
            if (response.StatusCode != HttpStatusCode.OK)
                CustomizeExceptions(response);

            return JsonConvert.DeserializeObject<DispatchStatus>(json.ToString());
        }

        public void TrackDispatchStatusGPSLocation(
            string accessToken,
            int dispatchNumber,
            int eta,
            string code,
            double latitude,
            double longitude,
            string source,
            int extJobId,
            DateTime statusTime)
        {
            var request = new RestRequest(string.Format("/v1/id/dispatches/{0}/status/track", dispatchNumber), Method.Post);
            request.AddParameter("application/json", new
            {
                code = code,
                eta = eta,
                latitude = latitude,
                longitude = longitude,
                source = source,
                extJobId = extJobId,
                statusTime = statusTime
            }.ToJson(), ParameterType.RequestBody);
            var response = Execute(request, accessToken);

            string r = response.Content;
            dynamic json = JsonConvert.DeserializeObject<dynamic>(r);
            if (response.StatusCode != HttpStatusCode.OK)
                CustomizeExceptions(response);
        }

        public void TrackPTOEvent(string accessToken, int dispatchNumber, string @event, double latitude, double longitude)
        {
            var request = new RestRequest(string.Format("/v1/id/dispatches/{0}/status/pto", dispatchNumber), Method.Post);

            request.AddParameter("application/json", new
            {
                @event = @event,
                latitude = latitude,
                longitude = longitude
            }.ToJson(), ParameterType.RequestBody);

            var response = Execute(request, accessToken);
            if (response.StatusCode != HttpStatusCode.OK)
                CustomizeExceptions(response);
        }

        public int RequestExtension(string accessToken, int dispatchNumber)
        {
            var request = new RestRequest(string.Format("/v1/id/dispatches/{0}/requestExtension", dispatchNumber), Method.Post);
            request.AddParameter("application/json", new { }.ToJson(), ParameterType.RequestBody);

            var response = Execute(request, accessToken);

            string r = response.Content;
            dynamic json = JsonConvert.DeserializeObject<dynamic>(r);
            if (response.StatusCode != HttpStatusCode.OK)
                CustomizeExceptions(response);

            return json.responseTimeExtCount;
        }

        #endregion

        #region Driver Jobs

        public void UploadDriverEquipmentPicture(
            string accessToken,
            int vendorId,
            int driverId,
            string pictureFileName,
            byte[] pictureFile,
            bool isUpdate = false)
        {
            var request = new RestRequest(string.Format("/v1/id/vendors/{0}/drivers/{1}/driverEquipment/picture", vendorId, driverId), isUpdate ? Method.Put : Method.Post);

            request.AlwaysMultipartFormData = true;
            request.AddFile("equipmentPicture", pictureFile, pictureFileName, "image/jpeg");

            var response = Execute(request, accessToken);
            string r = response.Content;
            if (response.StatusCode != HttpStatusCode.OK)
                CustomizeExceptions(response);
        }

        public Facility[] GetFacilities(string accessToken, int vendorId)
        {
            string uri = "/v1/id/vendors/{0}/facilities";

            var request = new RestRequest(string.Format(uri, vendorId), Method.Get);
            var response = Execute(request, accessToken);

            string r = response.Content;
            dynamic json = JsonConvert.DeserializeObject<dynamic>(r);
            if (response.StatusCode != HttpStatusCode.OK)
                CustomizeExceptions(response);

            List<Facility> result = new List<Facility>();
            foreach (var facility in json.results)
                result.Add(JsonConvert.DeserializeObject<Facility>(facility.ToString()));

            return result.OrderBy(o => o.Id).ToArray();
        }

        public DispatchDriver[] GetDriversList(string accessToken, int vendorId, int driverTypeId = 0, bool? onDuty = null)
        {
            string uri = "/v1/id/vendors/{0}/drivers";
            if (driverTypeId != 0)
                uri += "/driverType=" + driverTypeId;
            if (onDuty.HasValue)
                uri += "/onDuty=" + onDuty;

            var request = new RestRequest(string.Format(uri, vendorId), Method.Get);
            var response = Execute(request, accessToken);

            string r = response.Content;
            dynamic json = JsonConvert.DeserializeObject<dynamic>(r);
            if (response.StatusCode != HttpStatusCode.OK)
                CustomizeExceptions(response);

            List<DispatchDriver> result = new List<DispatchDriver>();
            foreach (var driver in json.results)
                result.Add(JsonConvert.DeserializeObject<DispatchDriver>(driver.ToString()));

            return result.ToArray();
        }

        public void NotifyDriverCurrentLocation(string accessToken, int driverId, double latitude, double longitude)
        {
            var request = new RestRequest(string.Format("/v1/id/drivers/{0}/currentLocation", driverId), Method.Post);

            request.AddParameter("application/json", new
            {
                latitude = latitude,
                longitude = longitude
            }.ToJson(), ParameterType.RequestBody);

            var response = Execute(request, accessToken);
            string r = response.Content;
            if (response.StatusCode != HttpStatusCode.OK)
                CustomizeExceptions(response);
        }

        public void NotifyCurrentLocation(string accessToken, double latitude, double longitude)
        {
            var request = new RestRequest(string.Format("/v1/id/currentLocation"), Method.Post);

            request.AddParameter("application/json", new
            {
                latitude = latitude,
                longitude = longitude
            }.ToJson(), ParameterType.RequestBody);

            var response = Execute(request, accessToken);
            string r = response.Content;
            if (response.StatusCode != HttpStatusCode.OK)
                CustomizeExceptions(response);
        }

        #endregion

        #region Profile Jobs

        public ProfileResponse SaveProfile(string accessToken, ProfileRequest profile, bool isUpdate)
        {
            string uri = "/v1/id/profile";

            var request = new RestRequest(uri, isUpdate ? Method.Put : Method.Post);

            dynamic userInfo = new
            {
                name = profile.UserInfo.Name,
                cellPhone = profile.UserInfo.CellPhone,
                email = profile.UserInfo.Email,
                officeEmail = profile.UserInfo.OfficeEmail,
                officePhone = profile.UserInfo.OfficePhone
            };

            dynamic preferences = new
            {
                jobFilter = profile.Preferences.JobFilter,
                jobViewOpt = profile.Preferences.JobViewOption,
                gpsTracking = profile.Preferences.GpsTracking,
                inactivityAlert = profile.Preferences.InactivityAlert,
                autoAssignAllJobs = profile.Preferences.AutoAssignAllJobs
            };

            List<dynamic> facilities = new List<dynamic>();

            foreach (var fac in profile.Facilities)
            {
                dynamic facility = new
                {
                    id = fac.Id,
                    name = fac.Name,
                    address = fac.Address,
                    city = fac.City,
                    state = fac.State,
                    zip = fac.ZipCode,
                    lat = fac.Latitude,
                    lon = fac.Longitude
                };
                facilities.Add(facility);
            }

            dynamic equipment = new
            {
                equipmentId = profile.Equipment.EquipmentId,
                type = profile.Equipment.Type,
            };

            request.AddParameter("application/json", new
            {
                userInfo = userInfo.ToJson(),
                preferences = preferences.ToJson(),
                facilities = facilities.ToJson(),
                equipment = equipment.ToJson()
            }.ToJson(), ParameterType.RequestBody);

            var response = Execute(request, accessToken);

            string r = response.Content;
            dynamic json = JsonConvert.DeserializeObject<dynamic>(r);
            if (response.StatusCode != HttpStatusCode.OK)
                CustomizeExceptions(response);

            ProfileResponse result = new ProfileResponse();
            result.ProfileId = json.profileId;
            result.UserInfo = JsonConvert.DeserializeObject<UserInfo>(json.userInfo);
            result.Preferences = JsonConvert.DeserializeObject<ProfilePreferences>(json.preferences);
            return result;
        }

        public void UpdateUserInfo(
            string accessToken,
            string name,
            string cellPhone,
            string email,
            string officeEmail,
            string officePhone)
        {
            var request = new RestRequest(string.Format("/v1/id/profile/userInfo"), Method.Put);

            request.AddParameter("application/json", new
            {
                name = name,
                cellPhone = cellPhone,
                email = email,
                officeEmail = officeEmail,
                officePhone = officePhone
            }.ToJson(), ParameterType.RequestBody);

            var response = Execute(request, accessToken);
            string r = response.Content;
            if (response.StatusCode != HttpStatusCode.OK)
                CustomizeExceptions(response);
        }

        public void UpdateUserPreferences(
            string accessToken,
            string jobFilter,
            string jobViewOpt,
            bool gpsTracking,
            bool etaAlerts)
        {
            var request = new RestRequest(string.Format("/v1/id/profile/userInfo"), Method.Put);

            request.AddParameter("application/json", new
            {
                JobFilter = jobFilter,
                JobViewOpt = jobViewOpt,
                gpsTracking = gpsTracking,
                etaAlerts = etaAlerts
            }.ToJson(), ParameterType.RequestBody);

            var response = Execute(request, accessToken);
            string r = response.Content;
            if (response.StatusCode != HttpStatusCode.OK)
                CustomizeExceptions(response);
        }

        public void UpdateUserProfilePicture(string accessToken, string pictureFileName, byte[] pictureFile, string pictureMimeType, int driverId = 0)
        {
            string uri = "/v1/id/profile/picture";
            if (driverId > 0)
                uri += "?driverId=" + driverId;

            var request = new RestRequest(uri, driverId == 0 ? Method.Post : Method.Put);

            request.AlwaysMultipartFormData = true;
            request.AddFile("profilePicture", pictureFile, pictureFileName, pictureMimeType);

            var response = Execute(request, accessToken);
            string r = response.Content;
            if (response.StatusCode != HttpStatusCode.OK)
                CustomizeExceptions(response);
        }

        public void AddPreferredFacility(string accessToken, int facilityId)
        {
            var request = new RestRequest(string.Format("/v1/id/profile/facilities"), Method.Post);

            request.AddParameter("application/json", new
            {
                facilityId = facilityId
            }.ToJson(), ParameterType.RequestBody);

            var response = Execute(request, accessToken);
            string r = response.Content;
            if (response.StatusCode != HttpStatusCode.OK)
                CustomizeExceptions(response);
        }

        public void RemovePreferredFacility(string accessToken, int facilityId)
        {
            var request = new RestRequest(string.Format("/v1/id/profile/facilities/{0}", facilityId), Method.Delete);

            var response = Execute(request, accessToken);
            string r = response.Content;
            if (response.StatusCode != HttpStatusCode.OK)
                CustomizeExceptions(response);
        }

        #endregion

        #region Notification Jobs

        public void SubscribeToNotifications(
            string accessToken,
            string deviceId,
            string deviceToken,
            string platform,
            string osVersion)
        {
            var request = new RestRequest(string.Format("/v1/id/pushNotification/subscribe"), Method.Post);

            request.AddParameter("application/json", new
            {
                deviceId = deviceId,
                deviceToken = deviceToken,
                platform = platform,
                osVersion = osVersion
            }.ToJson(), ParameterType.RequestBody);

            var response = Execute(request, accessToken);
            string r = response.Content;
            if (response.StatusCode != HttpStatusCode.OK)
                CustomizeExceptions(response);
        }

        public void ReceiveNotification(string accessToken, PushNotificationType type, NotificationPayload payload)
        {
            var request = new RestRequest(string.Format("/v1/id/pushNotification/notify"), Method.Post);

            request.AddParameter("application/json", new
            {
                notificationType = type.ToString(),
                payload = payload.ToJson()
            }.ToJson(), ParameterType.RequestBody);

            var response = Execute(request, accessToken);
            string r = response.Content;
            if (response.StatusCode != HttpStatusCode.OK)
                CustomizeExceptions(response);
        }

        #endregion 

        #region Enumerator Jobs

        public Enumerator[] GetEnumerator(string accessToken, EnumeratorType type)
        {
            string[] uris = { "None", "signeeTypes", "signatureReasonTypes", "signatureLocationTypes", "notificationTypes", "dispatchStatus" };

            var request = new RestRequest(string.Format("/v1/id/enumerations/" + uris[(int)type]), Method.Get);
            var response = Execute(request, accessToken);

            string r = response.Content;
            dynamic json = JsonConvert.DeserializeObject<dynamic>(r);
            if (response.StatusCode != HttpStatusCode.OK)
                CustomizeExceptions(response);

            List<Enumerator> result = new List<Enumerator>();
            foreach (var tp in json.results)
                result.Add(JsonConvert.DeserializeObject<Enumerator>(tp.ToString()));

            return result.ToArray();
        }

        #endregion

        #region 3rd Party Jobs

        public int ExternalExportDriverProfile(int vendorId, ExternalDriverProfile driver)
        {
            var url = "/v1/id/xtrnlParty/vendors/" + vendorId.ToString().PadLeft(5, '0') + "/drivers";
            var request = new RestRequest(url, Method.Post);
            request.AddParameter("application/json", driver.ToJson(), ParameterType.RequestBody);

            var response = GetRestClient(false).Execute(request);

            string r = response.Content;
            dynamic json = JsonConvert.DeserializeObject<dynamic>(r);

            if (response.StatusCode != HttpStatusCode.OK && response.StatusCode != HttpStatusCode.Created)
                CustomizeExceptions(response, url, driver.ToJson());

            return json.driverId;
        }

        public ExternalDriverProfile[] ExternalGetDriverProfiles(int vendorId)
        {
            var request = new RestRequest("/v1/id/xtrnlParty/vendors/" + vendorId + "/drivers", Method.Get);
            var response = GetRestClient(false).Execute(request);

            string r = response.Content;
            dynamic json = JsonConvert.DeserializeObject<dynamic>(r);
            if (response.StatusCode != HttpStatusCode.OK)
                CustomizeExceptions(response);

            List<ExternalDriverProfile> result = new List<ExternalDriverProfile>();
            foreach (var dp in json)
                result.Add(JsonConvert.DeserializeObject<ExternalDriverProfile>(dp.ToString()));

            return result.ToArray();
        }

        public ExternalDriverProfile ExternalGetDriverProfile(int vendorId, int driverId)
        {
            var request = new RestRequest("/v1/id/xtrnlParty/vendors/" + vendorId + "/drivers/" + driverId, Method.Get);
            var response = GetRestClient(false).Execute(request);

            string r = response.Content;
            dynamic json = JsonConvert.DeserializeObject<dynamic>(r);
            if (response.StatusCode != HttpStatusCode.OK)
                CustomizeExceptions(response);

            return JsonConvert.DeserializeObject<ExternalDriverProfile>(json.ToString());
        }

        public void ExternalUpdateDriverProfile(int vendorId, int driverId, ExternalDriverProfile driver)
        {
            var url = "/v1/id/xtrnlParty/vendors/" + vendorId + "/drivers/" + driverId;
            var request = new RestRequest(url, Method.Put);
            request.AddParameter("application/json", driver.ToJson(), ParameterType.RequestBody);

            var response = GetRestClient(false).Execute(request);

            string r = response.Content;
            dynamic json = JsonConvert.DeserializeObject<dynamic>(r);

            if (response.StatusCode != HttpStatusCode.OK)
                CustomizeExceptions(response, url);
        }

        public void ExternalDeleteDriverProfile(int vendorId, int driverId)
        {
            var request = new RestRequest("/v1/id/xtrnlParty/vendors/" + vendorId + "/drivers/" + driverId, Method.Delete);
            var response = GetRestClient(false).Execute(request);

            string r = response.Content;
            dynamic json = JsonConvert.DeserializeObject<dynamic>(r);

            if (response.StatusCode != HttpStatusCode.OK)
                CustomizeExceptions(response);
        }

        public void ExternalUploadDriverProfilePicture(
            string accessToken,
            int vendorId,
            int driverId,
            string pictureFileName,
            byte[] pictureFile,
            bool isUpdate = false)
        {
            var request = new RestRequest(string.Format("/v1/id/vendors/{0}/drivers/{1}/profilePicture", vendorId, driverId), isUpdate ? Method.Put : Method.Post);

            request.AlwaysMultipartFormData = true;
            request.AddFile("profilePicture", pictureFile, pictureFileName, "image/jpeg");

            var response = Execute(request, accessToken);
            string r = response.Content;
            if (response.StatusCode != HttpStatusCode.OK)
                CustomizeExceptions(response);
        }

        public void ExternalDeleteDriverProfilePicture(string accessToken, int vendorId, int driverId)
        {
            var request = new RestRequest("/v1/id/xtrnlParty/vendors/" + vendorId + "/drivers/" + driverId + "/profilePicture", Method.Delete);
            var response = Execute(request, accessToken);

            string r = response.Content;
            dynamic json = JsonConvert.DeserializeObject<dynamic>(r);

            if (response.StatusCode != HttpStatusCode.OK)
                CustomizeExceptions(response);
        }

        public void ExternalUploadDriverEquipmentPicture(
            string accessToken,
            int vendorId,
            int driverId,
            string pictureFileName,
            byte[] pictureFile,
            bool isUpdate = false)
        {
            var request = new RestRequest(string.Format("/v1/id/vendors/{0}/drivers/{1}/equipment/picture", vendorId, driverId), isUpdate ? Method.Put : Method.Post);

            request.AlwaysMultipartFormData = true;
            request.AddFile("equipmentPicture", pictureFile, pictureFileName, "image/jpeg");

            var response = Execute(request, accessToken);
            string r = response.Content;
            if (response.StatusCode != HttpStatusCode.OK)
                CustomizeExceptions(response);
        }

        public void ExternalDeleteDriverEquipmentPicture(string accessToken, int vendorId, int driverId)
        {
            var request = new RestRequest("/v1/id/xtrnlParty/vendors/" + vendorId + "/drivers/" + driverId + "/equipment/picture", Method.Delete);
            var response = Execute(request, accessToken);

            string r = response.Content;
            dynamic json = JsonConvert.DeserializeObject<dynamic>(r);

            if (response.StatusCode != HttpStatusCode.OK)
                CustomizeExceptions(response);
        }
        #endregion


        private static RestClient _sharedRestClient = GetRestClient(null);

        /// <summary>
        /// Reports GPS data for multiple drivers. Has to be called once per vendorId access token.
        /// </summary>
        /// <param name="accessToken"></param>
        /// <param name="data"></param>
        public void BatchLocationReport(string accessToken, BatchCurrentLocation[] data)
        {
            const string url = "/v1/id/drivers/currentLocations";
            const string jsonType = "application/json";
            var request = new RestRequest(url, Method.Post);
            var json = data.ToJson();

            request.AddParameter(jsonType, json, ParameterType.RequestBody);

            var response = Execute(request, accessToken);

            if (!response.IsSuccessful)
                CustomizeExceptions(response, url, data.ToJson());
        }

        public static void CustomizeExceptions(RestResponse response, string url = null, string requestJson = null)
        {
            string errorMessage = string.Empty;

            if (response.Content != null)
            {
                try
                {
                    dynamic error = JsonConvert.DeserializeObject<dynamic>(response.Content);

                    if (error != null)
                    {
                        if (error.fault != null)
                            errorMessage = error.fault.faultstring.ToString();
                        else if (error.error != null)
                            errorMessage = error.error.ToString();
                        else
                            errorMessage = response.Content;

                    }
                    else
                    {
                        errorMessage = response.Content;
                    }
                }
                catch (Exception er)
                {
                    throw new AgeroRestServiceOutage(response.Content, er.Message);
                }
            }

            if (errorMessage.Contains("Invalid access token"))
                throw new AgeroInvalidAccessTokenException(response.Content);
            if (errorMessage.Contains("Invalid access token"))
                throw new AgeroInvalidAccessTokenException(response.Content);
            if (errorMessage.Contains("Authorization Code"))
                throw new AgeroInvalidAuthorizationCodeException(response.Content);
            if (errorMessage.Contains("grant type"))
                throw new AgeroUnsupportedGrantTypeException(response.Content);
            if (errorMessage.Contains("The resource is not allowed"))
                throw new AgeroResourceNotAllowedException(response.Content);
            if (errorMessage.Contains("No Vendor found"))
                throw new AgeroVendorNotFoundException(response.Content);
            if (errorMessage.Contains("Resource not found"))
                throw new AgeroResourceNotFoundException(response.Content);
            if (errorMessage.Contains("A profile exists for this user"))
                throw new AgeroResourceExistsException(response.Content);
            if (errorMessage.Contains("NO Vendor exists"))
                throw new AgeroNoVendorFoundException(response.Content);
            if (errorMessage.Contains("NO Driver exists"))
                throw new AgeroNoDriverFoundException(response.Content);
            if (errorMessage.Contains("No picture exist"))
                throw new AgeroNoPictureExistsException(response.Content);
            if (errorMessage.Contains("Unknown Error"))
                throw new AgeroUnknownException(response.Content);
            if (errorMessage.Contains("No Jobs Found"))
                throw new AgeroNoJobsFoundException(response.Content);
            if (errorMessage.StartsWith("The job has expired"))
                throw new AgeroDispatchExpiredException(response.Content);
            if (errorMessage.Contains("Another dispatcher has already accepted this job."))
                throw new AgeroDispatchAlreadyResponded(errorMessage, response.Content);
            if (errorMessage.Contains("Another dispatcher has already refused this job."))
                throw new AgeroDispatchAlreadyResponded(errorMessage, response.Content);
            if (errorMessage.Contains("A third party driver exists"))
                throw new AgeroResourceExistsException(response.Content);
            if (errorMessage.Contains("The Service is temporarily unavailable"))
                throw new AgeroServiceUnavailable(response.Content);

            if (url != null || requestJson != null)
            {
                Console.WriteLine("-----------");

                if (url != null)
                    Console.WriteLine("URL: " + url + " returned status code " + response.StatusCode.ToString());

                if (requestJson != null)
                    Console.WriteLine("JSON: " + requestJson);

                if (response != null)
                    Console.WriteLine("RESPONSE: " + response.Content);

                Console.WriteLine("-----------");
            }

            throw new AgeroException(errorMessage, (int)response.StatusCode, response.Content, url, requestJson);
        }

    }
}
