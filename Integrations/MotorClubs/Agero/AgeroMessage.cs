using Extric.Towbook.Utility;
using System;
using System.Linq;

namespace Extric.Towbook.Integrations.MotorClubs.Agero
{
    /// <summary>
    /// Agero Notify request model.. This class was implemented to adhere to the documentation provided
    /// by agero for their Digital Dispatching API.
    /// </summary>
    [Table("MCDispatch.AgeroMessages")]
    public class AgeroMessage
    {
        [Key]
        public long AgeroMessageId { get; set; }
        public long NotificationEventId { get; set; }
        public int NotificationCode { get; set; }
        public int VendorId { get; set; }
        public int DispatchRequestNumber { get; set; }
        public string Title { get; set; }
        public string Message { get; set; }
        public int? FacilityId { get; set; }
        public DateTime? CreateDate { get; set; }

        public string DispatchSource { get; set; }

        public AgeroMessage() { }

        public void Save()
        {
            CreateDate = DateTime.Now;
            if (this.AgeroMessageId == 0)
                this.AgeroMessageId = SqlMapper.Insert<AgeroMessage>(this);
            else
                SqlMapper.Update<AgeroMessage>(this);
        }

        public static AgeroMessage GetById(int id)
        {
            return SqlMapper.Query<AgeroMessage>(
                "SELECT * FROM MCDispatch.AgeroMessages WHERE AgeroMessageId=@Id", 
                new { @Id = id }).FirstOrDefault();
        }
    }
}
