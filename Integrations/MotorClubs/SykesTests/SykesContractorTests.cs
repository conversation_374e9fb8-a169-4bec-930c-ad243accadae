using Microsoft.VisualStudio.TestTools.UnitTesting;
using System.Collections.ObjectModel;

namespace Extric.Towbook.Integrations.MotorClubs.Sykes.Tests
{
    [TestClass()]
    [Ignore]
    public class SykesContractorTests
    {
        private const string CONTRACTOR_ID = "FA262CAE-BB54-4FDE-B130-1C3C0438C6A1";
        private const string CONTRACTOR_ID_2 = "FA262CAE-BB54-4FDE-B130-1C3C0438C6A2";
        private const string CONTRACTOR_ID_3 = "TEST_CONTRACTOR_COMP";
        private const int ACCOUNT_ID = 2;
        private const int COMPANY_ID = 2;

        [TestMethod()]
        public void SaveTest()
        {
            SykesContractor sykesContractor = new SykesContractor();
            sykesContractor.AccountId = ACCOUNT_ID;
            sykesContractor.ContractorId = CONTRACTOR_ID;
            sykesContractor.CompanyId = COMPANY_ID;

            sykesContractor.Save();
            Assert.IsTrue(sykesContractor.SykesContractorId > 0);
        }

        [TestMethod()]
        public void GetByContractorIdTest()
        {
            SykesContractor sykesContractor = SykesContractor.GetByContractorId(CONTRACTOR_ID);
            Assert.IsTrue(sykesContractor.ContractorId == CONTRACTOR_ID);
            Assert.IsTrue(sykesContractor.CompanyId == COMPANY_ID);
        }

        [TestMethod()]
        public void GetByContractorIdAndAccountTest()
        {
            SykesContractor sykesContractor = SykesContractor.GetByContractorId(CONTRACTOR_ID, ACCOUNT_ID);
            Assert.IsTrue(sykesContractor.ContractorId == CONTRACTOR_ID);
            Assert.IsTrue(sykesContractor.CompanyId == COMPANY_ID);
        }

        [TestMethod()]
        public void GetByAccountIdTest()
        {
            SykesContractor sykesContractor = new SykesContractor();
            sykesContractor.AccountId = ACCOUNT_ID;
            sykesContractor.ContractorId = CONTRACTOR_ID_2;
            sykesContractor.CompanyId = 1;
            sykesContractor.Save();
            Collection<SykesContractor> sykesContractors = SykesContractor.GetByAccountId(ACCOUNT_ID);
            Assert.IsTrue(sykesContractors.Count > 1);
        }

        [TestMethod()]
        public void GetByCompanyIdTest()
        {
            SykesContractor sykesContractor = new SykesContractor();
            sykesContractor.AccountId = 1;
            sykesContractor.ContractorId = CONTRACTOR_ID_3;
            sykesContractor.CompanyId = COMPANY_ID;
            sykesContractor.Save();
            Collection<SykesContractor> sykesContractors = SykesContractor.GetByCompanyId(COMPANY_ID);
            Assert.IsTrue(sykesContractors.Count > 1);
        }

        [TestMethod()]
        public void DeleteTest()
        {
            SykesContractor sykesContractor1 = SykesContractor.GetByContractorId(CONTRACTOR_ID_2);
            sykesContractor1.Delete();
            SykesContractor sykesContractor2 = SykesContractor.GetByContractorId(CONTRACTOR_ID_3);
            sykesContractor2.Delete();
        }
    }
}