using Extric.Towbook.Utility;
using System;
using System.Linq;

namespace Extric.Towbook.Integrations.MotorClubs.Urgently
{
    /// <summary>
    /// Urgently Notify request model.. This class was implemented to adhere to the documentation provided
    /// by Urgently for their Digital Dispatching API.
    /// </summary>
    [Table("MCDispatch.UrgentlyMessages")]
    public class UrgentlyMessage
    {
        [Key("UrgentlyMessageId")]
        public long UrgentlyMessageId { get; set; }
        public long TypeId { get; set; }
        public string ProviderId { get; set; }
        public string CaseId { get; set; }
        private string jsonData { get; set; }
        public string JsonData
        {
            get
            {
                return jsonData;
            }
            set
            {
                if (value == null)
                    jsonData = null;
                else
                    jsonData = value.Replace("\" H", "\\\" H")
                                .Replace("\" W", "\\\" W")
                                .Replace("\" L", "\\\" L");
            }
        }

        
        public DateTime CreateDate { get; set; }


        public UrgentlyMessage() { }

        public void Save()
        {
            CreateDate = DateTime.Now;
            if (this.UrgentlyMessageId == 0)
                this.UrgentlyMessageId = SqlMapper.Insert<UrgentlyMessage>(this);
            else
                SqlMapper.Update<UrgentlyMessage>(this);
        }

        public static UrgentlyMessage GetById(int id)
        {
            return SqlMapper.Query<UrgentlyMessage>(
                "SELECT * FROM MCDispatch.UrgentlyMessages WHERE UrgentlyMessageId=@Id", 
                new { @Id = id }).FirstOrDefault();
        }
    }
}
