using Extric.Towbook.Integration;
using System.Collections.Generic;
using System;
using Microsoft.AspNetCore.Routing;

namespace Extric.Towbook.API.Integration.MotorClubs.Fleetnet
{
    public class ProviderRegistration
    {
        public ProviderRegistration()
        {

        }

        
        public static void RegisterRoutes(RouteCollection routes)
        {
            //string root = "receivers/fleetnet/{environment}/";

            //var rl = new List<Route>();
            
            //rl.Add(routes.MapHttpRoute(
            //    name: "MC_Fleetnet_DDAPI",
            //    routeTemplate: root,
            //    defaults: new { controller = "Fleetnet" }));

            //foreach (var r in rl)
            //{
            //    if (r.DataTokens == null)
            //        r.DataTokens = new RouteValueDictionary();

            //    r.DataTokens["Namespaces"] = new string[] { "Extric.Towbook.API.Integration.MotorClubs.Fleetnet" };
            //}

            var p = Provider.GetByName("Fleetnet");

            p.<PERSON>(KeyType.Account, "ContractorId");
           
        }

        //private static void AddRoute(List<Route> rl, RouteCollection routes, string url, string controller)
        //{
        //    rl.Add(routes.MapHttpRoute(
        //        name: "MC_Fleetnet_" + url.Replace("-", ""),
        //        routeTemplate: "receivers/fleetnet/" + url,
        //        defaults: new { controller }));

        //}
        
    }
}
