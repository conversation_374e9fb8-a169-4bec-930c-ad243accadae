using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;

namespace Extric.Towbook.API.Integration.MotorClubs.Quest
{
    [DataContract(Name = "DDXMLReceiveMessage", Namespace = "https://api.towbook.com/")]
    public class DDXMLReceiveMessage
    {
        [DataMember(Name = "xmlMessage", Order = 1)]
        public string xmlMessage { get; set; }
    }
}
