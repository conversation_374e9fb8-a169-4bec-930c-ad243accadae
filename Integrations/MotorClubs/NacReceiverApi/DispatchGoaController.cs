using Extric.Towbook.Integrations.MotorClubs.Nac;
using Extric.Towbook.Integration.MotorClubs.Services;
using Newtonsoft.Json;
using NLog;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using System.IO;
using Extric.Towbook.WebShared;

namespace Extric.Towbook.API.Integration.MotorClubs.Nac
{
    [Route("receivers/nac/goa")]
    public class DispatchGoaController : ControllerBase
    {
        private static readonly Logger logger = LogManager.GetCurrentClassLogger();

        // POST /dispatch 
        [Route("")]
        [HttpPost]
        public async Task<object> Post()
        {
            var rawJson = await Request.Body.ReadAsStringAsync();

            var log = this.NewLogEvent("GoaPost", rawJson);

            if (this.Request.Headers.HasAuthorization())
                log.Properties.Add("Authorization", this.Request.Headers.AuthorizationParameter());

            try
            {
                var dispatch = JsonConvert.DeserializeObject<IncomingGoaModel>(rawJson);

                var contractor = this.GetContractorById(dispatch.ContractorId);
                if (contractor == null)
                    return this.ApiFailure("Unknown ContractorId");


                log.Properties["queueItemId"] = await this.SendToBackendService(DigitalDispatchService.CallEventType.GoaResponse,
                    contractor,
                    rawJson,
                    dispatch.DispatchId);
            }
            finally
            {
                logger.Log(log);
            }

            return Accepted();
        }
    }
}
