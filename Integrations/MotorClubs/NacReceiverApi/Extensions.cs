using Extric.Towbook.Accounts;
using Extric.Towbook.Integration.MotorClubs.Services;
using NLog;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Extric.Towbook.Utility;
using Extric.Towbook.Web;
using System.Net.Http;
using Extric.Towbook.Integrations.MotorClubs.Allstate;
using Extric.Towbook.Integrations.MotorClubs.Nac;
using Extric.Towbook.WebShared;

namespace Extric.Towbook.API.Integration.MotorClubs.Nac
{
    public static class GatewayExtensions
    {   
        public static LogEventInfo NewLogEvent(this ControllerBase t, string message, string json = null)
        {
            var log = new LogEventInfo();

            log.Level = LogLevel.Info;
            log.Message = message;

            if (json != null)
                log.Properties["json"] = json;

            log.Properties["commitId"] = Core.GetCommitId();
            log.Properties["masterAccountName"] = "NAC";
            log.Properties["environment"] =  t.GetEnvironment();
            log.Properties["requestingIp"] = t.GetRequestingIp();

            return log;
        }


        public static string GetEnvironment(this ControllerBase t)
        {
            if (t.User?.Identity?.Name != null)
                return t.User.Identity.Name;

            var req = HttpContext.Current;

            if (req != null)
            {
                return (string)req.Items["nac:environment"]?.ToString().ToUpperInvariant();
            }

            return "TEST";
        }

        public static int GetEnvironmentId(this ControllerBase t)
        {
            var env = t.GetEnvironment()?.ToUpperInvariant();

            if (env == "DEV")
                return 1;
            if (env == "TEST")
                return 2;
            else if (env == "PROD")
                return 3;

            return 1;
        }

        public static string GetRequestingIp(this ControllerBase t) => WebGlobal.GetRequestingIp();

        public static HttpResponseMessage ApiFailure(this ControllerBase t, string message, LogEventInfo log = null)
        {
            if (log != null)
            {
                log.Properties["error"] = "ApiFailure: " + message;
                log.Level = LogLevel.Warn;
                log.Properties["status"] = "400";
            }

            return new HttpResponseMessage()
            {
                StatusCode = System.Net.HttpStatusCode.BadRequest,
                Content = new StringContent(
                    new
                    {
                        message = message
                    }.ToJson(),
                    System.Text.Encoding.UTF8,
                     "application/json")
            };
        }

        public static AllstateContractor GetContractorById(this ControllerBase t, string contractorId)
        {
            var ac = AllstateContractor.GetByContractorId(
                contractorId, MasterAccountTypes.Nac);
            
            if (ac == null)
            {
                //ac = AllstateContractor.GetByContractorId(
                //    contractorId, MasterAccountTypes.OonNac, GetEnvironmentId(t));

                // throw new http exception 
            }

            return ac;
        }

        public static async Task<long> SendToBackendService(this ControllerBase t,
            DigitalDispatchService.CallEventType type,
            AllstateContractor contractor,
            string json,
            string dispatchId)
        {
            var env = GetEnvironment(t);
            var testMode = env == "TEST" || env == "DEV";

            var properties = new System.Collections.Generic.Dictionary<string, object>();
            properties["ddxmlContractorId"] = contractor?.AllstateContractorId;

            properties["masterAccountId"] = contractor.MasterAccountId;

            if (testMode)
                properties["TestMode"] = true;

            properties["environment"] = env;
           
            var msg = new NacMessage();

            msg.NacContractorId = contractor?.AllstateContractorId ?? 0;
            msg.JsonData = json;
            msg.DispatchId = dispatchId;
            msg.MessageType = (int)type;
            msg.Save();

            return await DigitalDispatchService.HandleCallEventAsync(
                contractor?.CompanyId ?? 0,
                contractor?.AccountId ?? 0,
                msg.ToJson(),
                type,
                properties: properties,
                logPrefix: "NAC-" + GetEnvironment(t));
        }
    }
}
