using Extric.Towbook.Integration.MotorClubs.Services;
using Newtonsoft.Json;
using NLog;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Extric.Towbook.WebShared;

namespace Extric.Towbook.API.Integration.MotorClubs.Nac
{
    // TODO: confirm this endpoint isn't used, and eliminate it. it was copied from another project. 
    [Route("receivers/nac/responses")]
    public class DispatchResponsesController : ControllerBase
    {
        private static readonly Logger logger = LogManager.GetCurrentClassLogger();

        // POST /dispatch 
        [Route("")]
        [HttpPost]
        public async Task<object> Post()
        {
            var rawJson = await Request.Body.ReadAsStringAsync();

            var log = this.NewLogEvent("ResponsesPost", rawJson);

            if (this.Request.Headers.HasAuthorization())
                log.Properties.Add("Authorization", this.Request.Headers.AuthorizationParameter());

            try
            {
                var dispatch = JsonConvert.DeserializeObject<dynamic>(rawJson);

                var contractor = this.GetContractorById((string)dispatch.ContractorId);

                if (contractor == null)
                    return this.ApiFailure("Unknown ContractorId");

                DigitalDispatchService.CallEventType eventType;

                if (dispatch.ResponseType == "ACCEPTED")
                    eventType = DigitalDispatchService.CallEventType.Accepted;
                else if (dispatch.ResponseType == "REJECTED")
                    eventType = DigitalDispatchService.CallEventType.Rejected;
                else if (dispatch.ResponseType == "EXPIRED")
                    eventType = DigitalDispatchService.CallEventType.Expired;
                else
                    eventType = DigitalDispatchService.CallEventType.None;

                var queueItemId  = await this.SendToBackendService(
                    eventType,
                    contractor,
                    rawJson,
                    (string)dispatch.DispatchId);

                log.Properties["queueItemId"] = queueItemId;

                var rm = new HttpResponseMessage(HttpStatusCode.Accepted);
                rm.Headers.Add("X-Twbk-Id", queueItemId.ToString());

                return rm;
            }
            finally
            {
                logger.Log(log);
            }

            return Accepted();
        }
    }
}
