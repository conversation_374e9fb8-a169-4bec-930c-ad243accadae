using System.Threading.Tasks;
using Extric.Towbook.Dispatch;
using Newtonsoft.Json;

namespace Extric.Towbook.Integrations.MotorClubs.Swoop.Model
{
    public class Location
    {
        public SwoopLocation ServiceLocation { get; set; }
        public SwoopLocation DropoffLocation { get; set; }
        public SwoopContact PickupContact { get; set; }
        public SwoopContact DropoffContact { get; set; }

        public class SwoopContact
        {
            public string Name { get; set; }
            public string Phone { get; set; }
        }

        public class SwoopLocation
        {
            public string Address { get; set; }
            public string Street { get; set; }
            public string City { get; set; }
            public string PostalCode { get; set; }

            [JsonProperty("lat")]
            public decimal? Latitude { get; set; }
            [JsonProperty("lng")]
            public decimal? Longitude { get; set; }
            public string LocationType { get; set; }

            public async Task<EntryWaypoint> ToWaypoint(EntryWaypoint input = null)
            {
                if (input == null)
                    input = new EntryWaypoint();

                input.Address = Address;
                input.Notes = LocationType;

                if (input.Address != null && input.Address.EndsWith(", None"))
                    input.Address = input.Address.Substring(0, input.Address.LastIndexOf(", None"));

                if (input.Address != null && input.Address.StartsWith("Dropped Pin, "))
                    input.Address = input.Address.Substring(13);

                if (Address != null && !string.IsNullOrWhiteSpace(PostalCode) && !Address.Contains(PostalCode))
                    input.Address += " " + PostalCode;

                input.Latitude = Latitude.GetValueOrDefault();
                input.Longitude = Longitude.GetValueOrDefault();

                // if lat/long didn't come over from Swoop, get it from google
                if ((input.Latitude == 0 || input.Longitude == 0) &&
                    !string.IsNullOrEmpty(Address))
                {
                    var geo = await Utility.GeocodeHelper.Geocode(input.Address);

                    if (geo != null)
                    {
                        input.Latitude = geo.Latitude;
                        input.Longitude = geo.Longitude;
                    }
                }

                return input;
            }
        }
    }


}
