using System;

namespace Extric.Towbook.Integrations.MotorClubs.Swoop
{
    [Serializable]
    public class SwoopException : Exception
    {
        public string Json { get; private set; }
        public int HttpStatusCode { get; private set; }

        public string Url { get; private set; }
        public string Request { get; private set; }
        public string UserId { get; private set; }

        public SwoopException() { }
        public SwoopException(string message) : base(message) { }
        public SwoopException(string message, int httpStatusCode, string json) : base(message) {
            Json = json;
            HttpStatusCode = httpStatusCode;
        }

        public SwoopException(string message, int httpStatusCode, string json, string url, string request) : base(message)
        {
            Json = json;
            HttpStatusCode = httpStatusCode;
            Url = url;
            Request = request;
        }


        public SwoopException(string message, int httpStatusCode, string json, string url, string request, string userId) : base(message)
        {
            Json = json;
            HttpStatusCode = httpStatusCode;
            Url = url;
            Request = request;
            UserId = userId;
        }


        public SwoopException(string message, Exception inner) : base(message, inner) { }
        protected SwoopException(
          System.Runtime.Serialization.SerializationInfo info,
          System.Runtime.Serialization.StreamingContext context)
            : base(info, context) { }
    }

    public class SwoopInvalidAccessTokenException : SwoopException
    {
        public SwoopInvalidAccessTokenException(string json) : base("A valid OAuth token was not provided.", 401, json) { }
    }

    public class SwoopServiceUnavailable : SwoopException
    {
        public SwoopServiceUnavailable(string json) : base("The Service is temporarily unavailable", 503, json) { }
    }

    public class SwoopUnknownException : SwoopException
    {
        public SwoopUnknownException(string json) : base("An error occurred on the server:" + json, 500, json) { }
    }
}
