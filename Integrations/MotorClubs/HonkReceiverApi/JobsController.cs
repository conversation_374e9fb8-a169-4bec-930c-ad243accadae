using Extric.Towbook.Accounts;
using Extric.Towbook.Integration.MotorClubs.Services;
using Extric.Towbook.Integrations.MotorClubs.Honk;
using Extric.Towbook.Utility;
using Newtonsoft.Json;
using NLog;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Http;
using Extric.Towbook.WebShared;

namespace Extric.Towbook.API.Integration.MotorClubs.Honk
{

    [Route("receivers/honk/jobs")]
    public class JobsController : ControllerBase
    {
        private static readonly Logger logger = LogManager.GetCurrentClassLogger();


        [HttpPost("{id?}")]
        public async Task<object> Post()
        {
            var rawJson = await Request.Body.ReadAsStringAsync();

            var log = new LogEventInfo();

            log.Level = LogLevel.Info;
            log.Message = "HonkRawJson";
            log.Properties.Add("masterAccountName", "Honk");
            log.Properties.Add("json", rawJson);

            if (this.Request.Headers.HasAuthorization())
                log.Properties.Add("Authorization", this.Request.Headers.AuthorizationParameter());

            try
            {
                var obj = JsonConvert.DeserializeObject<HonkRestClient.JobModelContainer>(rawJson);

                log.Properties["contractorId"] = obj?.Job?.provider_id;

                var last = HonkMessage.GetLastByJobId(obj.Job.provider_id, obj.Job.Id);

                var um = new HonkMessage();

                um.ProviderId = obj.Job.provider_id;
                um.JsonData = rawJson;

                DigitalDispatchService.CallEventType type;

                Dictionary<string, object> properties = null;

                string changes = null;
                if (obj?.Job?.Status == "CANCELLED")
                {
                    um.TypeId = 2; // job cancelled
                    type = DigitalDispatchService.CallEventType.Cancelled;
                }
                else if (obj?.Job?.Status == "DISPATCHED" && obj.Job.provider_id.ToLowerInvariant().Contains("@towbook.net"))
                {
                    um.TypeId = 5; // job accepted
                    type = DigitalDispatchService.CallEventType.Accepted;
                    log.Properties["masterAccountId"] = MasterAccountTypes.OonHonk;
                    
                }
                else
                {
                    if (last != null)
                    {
                        um.TypeId = 3; // job update.

                        var lastModel = JsonConvert.DeserializeObject<HonkRestClient.JobModelContainer>(last.JsonData);
                        changes = JsonExtensions.GetChanges(lastModel, obj, false).ToJson(true);

                        log.Properties["data"] = changes;
                        log.Properties["type"] = "JobUpdate";

                        type = DigitalDispatchService.CallEventType.Update;
                    }
                    else
                    {
                        um.TypeId = 1; // job offer;
                        type = DigitalDispatchService.CallEventType.Received;
                    }
                }

                um.JobId = obj.Job.Id;
                um.Save();
                
                if (changes != null && changes != "null")
                    um.JsonData = changes;

                var up = HonkProvider.GetByProviderId(um.ProviderId);

                if (up == null && um.ProviderId.ToLowerInvariant() != "out_of_network")
                {
                    return BadRequest("Unknown ProviderId: " + um.ProviderId);
                }
                else
                {
                    // force to test environment for now.
                    properties = new Dictionary<string, object>()
                    {
                        ["TestMode"] = true,
                        ["masterAccountId"] = MasterAccountTypes.Honk,
                        ["contractorId"] = um.ProviderId,
                        ["dispatchId"] = obj.Job.Id,
                        ["environment"] = this.GetEnvironment()
                    };

                    if (up != null)
                    {

                        log.Properties["companyId"] = up.CompanyId;
                        log.Properties["accountId"] = up.AccountId;
                    }

                    if (um.ProviderId.ToLowerInvariant() == "out_of_network")
                    {
                        properties["masterAccountId"] = MasterAccountTypes.OonHonk;
                        type = DigitalDispatchService.CallEventType.OonOffered;
                        log.Properties["type"] = "OonDispatch";
                    }
                    else
                    {
                        log.Properties["masterAccountId"] = up.MasterAccountId;
                    }

                    var queueItemId = await DigitalDispatchService.HandleCallEventAsync(
                        up?.CompanyId ?? 0,
                        up?.AccountId ?? 0,
                        um.ToJson(null),
                        type,
                        properties,
                        "Honk");

                    log.Properties["queueItemId"] = queueItemId;

                    //var resp = Accepted();

                    //resp.Headers.Add("X-Twbk-Id", queueItemId.ToString());
                    //return resp;
                    Response.Headers["X-Twbk-Id"] = queueItemId.ToString();
                    return Accepted();                 }
            }
            catch (Exception y)
            {
                log.Level = LogLevel.Error;
                log.Exception = y;

                return new StatusCodeResult(StatusCodes.Status500InternalServerError);
            }
            finally
            {
                logger.Log(log);
            }
        }

    }
}
