using System;
using Microsoft.AspNetCore.Mvc;

namespace Extric.Towbook.API.Integration.MotorClubs.Honk
{
    [Route("receivers/honk")]
    public class HonkController : ControllerBase
    {
        [HttpGet]
        [Route("")]
        public object Get()
        {
            return "";
        }

        [HttpPost]
        [Route("")]
        public object Post()
        {
            return "Honk Controller POST " + Guid.NewGuid().ToString("n") + " - " + DateTime.Now.ToString();
        }
    }

}
