using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Extric.Towbook.Integrations.MotorClubs.Issc
{
    public class IsscCall
    {
        public string Event { get; set; }
        public string ClientID { get; set; }
        public string ContractorID { get; set; }
        public string LocationID { get; set; }

        public string DispatchID { get; set; }

        public string ResponseID { get; set; }
        public IsscAccountInfo AccountInfo { get; set; }
        public IsscJob Job { get; set; }
        public IsscVehicle Vehicle { get; set; }
        public IsscIncident Incident { get; set; }
        public IsscCoverage Coverage { get; set; }
        public IsscDestination Destination { get; set; }

        public IsscCall() { }
    }

    public class IsscAccountInfo
    {
        public string CallBackPhone { get; set; }
        public string CallFromLocation { get; set; }
        public IsscAccountInfoPerson Member { get; set; }
        public IsscAccountInfoPerson Customer { get; set; }
    }

    public class IsscAccountInfoPerson
    {
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string ContactMethod { get; set; }
        public string MemberID { get; set; }
    }

    public class IsscJob
    {
        public int RequiredAcknowledgeTime { get; set; }
        public string JobID { get; set; }
        public string JobDescription { get; set; }
        public DateTime TimeStamp { get; set; }
        public int MaxETA { get; set; }
        public int PreferredETA { get; set; }
        public string Priority { get; set; }
        public DateTime? RequestedFutureDateTime { get; set; }
        public string UrgencyLevel { get; set; }
        public string ServiceComments { get; set; }
        public List<IsscJobTask> PrimaryTasks { get; set; }
        public List<IsscJobQuestion> ServiceQuestions { get; set; }
    }

    public class IsscJobTask
    {
        public bool ETDRequired { get; set; }
        public string Task { get; set; }
        public string[] SecondaryTasks { get; set; }
    }

    public class IsscJobQuestion 
    {
        public string Question { get; set; }
        public string[] Responses { get; set; }
    }

    public class IsscVehicle
    {
        public string Year { get; set; }
        public string Make { get; set; }
        public string Model { get; set; }
        public string Color { get; set; }

        public string License { get; set; }
        public string LicenseState { get; set; }
        public string VIN { get; set; }
        public string VehicleType { get; set; }
        public string FuelType { get; set; }
        public string AdditionalInfo { get; set; }
        public int Odometer { get; set; }
        public string EngineType { get; set; }
    }

    public class IsscIncident
    {
        public string Address1 { get; set; }
        public string Address2 { get; set; }
        public string City { get; set; }
        public string State { get; set; }
        public string Zip { get; set; }
        public string CrossStreet1 { get; set; }
        public string CrossStreet2 { get; set; }
        public bool CustomerWithVehicle { get; set; }
        public string CustomerETA { get; set; }
        public string CustomerRideWithProvider { get; set; }
        public bool SafeLocation { get; set; }
        public string DirectionOfTravel { get; set; }
        public string PersonsInCar { get; set; }
        public string ExitNumber { get; set; }
        public decimal Latitude { get; set; }
        public decimal Longitude { get; set; }
        public int? ConfidenceLatLong { get; set; }
        public string Comments { get; set; }
        public bool Median { get; set; }
        public string MileMarker { get; set; }
        public bool OnOffRamp { get; set; }
        public bool RightShoulder { get; set; }
        public string TimeZone { get; set; }

        public override string ToString()
        {
            string zip = Zip;

            if (zip.Contains("-"))
                zip = zip.Substring(0, zip.IndexOf("-"));

            return string.Format("{0}, {1}, {2} {3} {4}",
                Core.FormatName(Address1),
                Address2,
                Core.FormatName(City),
                State,
                zip)
                .Replace(", , ", ", ")
                .Trim()
                .Trim(',');
        }

        public Dispatch.EntryWaypoint ToWaypoint(Dispatch.EntryWaypoint input)
        {
            if (input == null)
                input = new Dispatch.EntryWaypoint();

            input.Title = "Pickup";

            input.Address = (Address1 + ", " + Core.FormatName(City) + " " + State + " " + Zip).Trim().Trim(',').Replace("  ", " ");
            input.Latitude = Latitude;
            input.Longitude = Longitude;

            return input;
        }
    }

    public class IsscCoverage
    {
        public string Description { get; set; }
        public string BenefitLimit { get; set; }
        public bool CashPaymentOnly { get; set; }
        public string CoPayAmount { get; set; }
        public string CoveredMiles { get; set; }
        public string CoveredMilesText { get; set; }
        public string CoveredServices { get; set; }
        public string DollarLimit { get; set; }
        public string DollarLimitText { get; set; }
        public int? ExpectedTowMiles { get; set; }
        public string FuelCoverageAmount { get; set; }
        public string NonCoveredServices { get; set; }
        public string OverMileageRate { get; set; }
    }

    public class IsscDestination
    {
        public string Address1 { get; set; }
        public string Address2 { get; set; }
        public string City { get; set; }
        public string State { get; set; }
        public string Zip { get; set; }
        public decimal Latitude { get; set; }
        public decimal Longitude { get; set; }
        public int? ConfidenceLatLon { get; set; }
        public string Phone { get; set; }
        public string LocationInfo { get; set; }
        public string Notes { get; set; }
        public string SpecialInstructions { get; set; }
        public string TowComments { get; set; }

        public override string ToString()
        {
            string zip = Zip ?? "";

            if (zip != null && zip.Contains("-"))
                zip = zip.Substring(0, zip.IndexOf("-"));

            if (Address1 == "Unknown" &&
                City == "Unknown" &&
                Zip == "00000-0000")
            {
                if (!string.IsNullOrWhiteSpace(LocationInfo) && !LocationInfo.Contains("Unknown"))
                    return LocationInfo;
                else
                    return "";
            }

            return string.Format("{0}, {1}, {2} {3} {4}",
                Core.FormatName(Address1),
                Address2,
                Core.FormatName(City),
                State,
                zip)
                .Replace(", , ", ", ")
                .Trim()
                .Trim(',');
        }


        public Dispatch.EntryWaypoint ToWaypoint(Dispatch.EntryWaypoint input)
        {
            if (input == null)
                input = new Dispatch.EntryWaypoint();

            input.Title = "Destination";

            input.Address = (Address1 + ", " + Core.FormatName(City) + " " + State + " " + Zip).Trim().Trim(',').Replace("  ", " ");

            // 8/24/2021 : ISSC / Geico is sending over jobs with bad latitudes/longitudes. We won't import their lat/long if the address included contains city/state. 

            if (string.IsNullOrWhiteSpace(City) && string.IsNullOrWhiteSpace(State))
            {
                input.Latitude = Latitude;
                input.Longitude = Longitude;
            }

            return input;
        }
    }
}