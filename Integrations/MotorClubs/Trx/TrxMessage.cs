using Extric.Towbook.Utility;
using System;
using System.Linq;

namespace Extric.Towbook.Integrations.MotorClubs.Trx
{
    /// <summary>
    /// Trx Notify request model.. This class was implemented to adhere to the documentation provided
    /// by Trx for their Digital Dispatching API.
    /// </summary>
    [Table("MCDispatch.TrxMessages")]
    public class TrxMessage
    {
        [Key("TrxMessageId")]
        public long TrxMessageId { get; set; }
	    public int TrxContractorId { get; set; }
        public int MessageType { get; set; }
        public string DispatchId { get;set; }
        public string JsonData { get; set; }

        public DateTime? CreateDate { get; set; }

        public TrxMessage() { }

        public TrxMessage Save()
        {
            CreateDate = DateTime.Now;
            if (this.TrxMessageId == 0)
                this.TrxMessageId = SqlMapper.Insert<TrxMessage>(this);
            else
                SqlMapper.Update<TrxMessage>(this);

            return this;
        }

        public static TrxMessage GetById(int id)
        {
            return SqlMapper.Query<TrxMessage>(
                "SELECT * FROM MCDispatch.TrxMessages WHERE TrxMessageId=@Id", 
                new { @Id = id }).FirstOrDefault();
        }
    }
}
