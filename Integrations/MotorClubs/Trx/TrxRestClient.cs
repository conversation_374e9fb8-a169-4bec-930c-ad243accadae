using Extric.Towbook.Accounts;
using Extric.Towbook.Configuration;
using Extric.Towbook.Utility;
using Newtonsoft.Json;
using NLog;
using RestSharp;
using RestSharp.Authenticators.OAuth2;
using System;
using System.Collections.Generic;
using System.Security.Cryptography;
using System.Text;

namespace Extric.Towbook.Integrations.MotorClubs.Trx
{
    public class IncomingModelBase
    {
        public string DispatchId { get; set; }
        public string ContractorId { get; set; }
        public string LocationId { get; set; }
    }

    public sealed class IncomingAcceptedModel : IncomingModelBase
    {
        public string PoNumber { get; set; }

        public AcceptedPayment Payment { get; set; }

        public sealed class AcceptedPayment
        {
            public string Zip { get; set; }
            public string Cvc { get; set; }
            public string ExpirationDate { get; set; }
            public string Number { get; set; }
        }

    }


    public sealed class IncomingExpiredModel : IncomingModelBase
    {
        public string Notes { get; set; }
    }

    public sealed class IncomingRefusedModel : IncomingModelBase
    {
        public string Notes { get; set; }
    }

    public sealed class IncomingCancelledModel : IncomingModelBase
    {
        public string Notes { get; set; }
    }

    public sealed class IncomingGoaModel : IncomingModelBase
    {
        public bool Approved { get; set; }
        public string Notes { get; set; }
    }


    public class TrxRestClient
    {
        private static readonly Logger logger = LogManager.GetCurrentClassLogger();
        private readonly Dictionary<string, RestClient> _restClientCache = new Dictionary<string, RestClient>();
        private RestClient _restClient;
        public string UrlBase = "";

        public string ClientId { get; private set; }
        private string ClientSecret { get; set; }
        private string Username { get; set; }
        private string Password { get; set; }

        public int MasterAccountId { get; private set; }
        public string EnvironmentName { get; private set; }
        public string Ip { get; private set; }
        public int CompanyId { get; private set; }
        public long QueueItemId { get; private set; }

        public int CallId { get; private set; }
        public int CallRequestId { get; private set; }
        public string DispatchId { get; private set; }

        public TrxRestClient(
            int masterAccountId,
            string environment,
            string ip,
            int companyId, 
            long queueItemId, 
            int callRequestId,
            int callId, 
            string dispatchId)
        {
            MasterAccountId = masterAccountId;
            EnvironmentName = environment;
            CompanyId = companyId;
            QueueItemId = queueItemId;
            CallRequestId = callRequestId;
            CallId = callId;
            DispatchId = dispatchId;

            if (environment == "TEST")
            {
                UrlBase = "https://evolve1--testtrain.my.salesforce.com";
                Username = "<EMAIL>";
                Password = "NVTg2cjA:ZHtm8kYi2oG9DQyC9CXoFffBbPHxSED";
                ClientId = "3MVG9lJB4lV8F4SjfGf0sZm0U_AeaJHXInH4tDed.5Q7YjB3R3D8x5OjbgoCW8nRH7aiEUVBDIwUv2ChCMJVv";
                ClientSecret = "****************************************************************";
            }
            else if (environment == "PROD")
            {

                UrlBase = "https://login.salesforce.com";
                Username = "<EMAIL>";
                Password = "ViNB-fE6Mb2U_A2pdPPb1s6BYDZGWsxsJERZrJU";
                ClientId = "3MVG9Km_cBLhsuPwcTS3Ts19aqyEwHZGXVQq1kdVRVrbY_EBxIo2dMaIYn5OHpdYwiHA.gx45z4zXGY2i6CSz";
                ClientSecret = "****************************************************************";
            }

            Ip = ip;
    }

        public LogEventInfo NewLogEvent(string message, string json = null)
        {
            var log = new LogEventInfo();

            log.Level = LogLevel.Info;
            log.Message = message;

            if (json != null)
                log.Properties["json"] = json;

            log.Properties["commitId"] = Core.GetCommitId();
            log.Properties["masterAccountName"] = MasterAccountTypes.GetName(MasterAccountId);
            log.Properties["masterAccountId"] = MasterAccountId;
            log.Properties["environment"] = EnvironmentName;
            log.Properties["requestingIp"] = Ip;

            log.Properties["commitId"] = Core.GetCommitId();

            if (CompanyId > 0)
                log.Properties["companyId"] = CompanyId;

            if (QueueItemId > 0)
                log.Properties["queueItemId"] = QueueItemId;

            if (CallId > 0)
                log.Properties["callId"] = CallId;

            if (CallRequestId > 0)
                log.Properties["callRequestId"] = CallRequestId;

            if (!string.IsNullOrWhiteSpace(DispatchId))
                log.Properties["dispatchId"] = DispatchId;

            return log;
        }


        public static string GetEnvironmentName(int env)
        {
            if (env == 1)
                return "DEV";
            if (env == 2)
                return "TEST";
            else if (env == 3)
                return "PROD";

            return "DEV";
        }

        public static int GetEnvironmentId(string env)
        {
            if (env == "DEV")
                return 1;
            if (env == "TEST")
                return 2;
            else if (env == "PROD")
                return 3;

            return 1;
        }

        public sealed class DispatchAcceptModel
        {
            public string Type { get; set; } = "accept";
            public string DispatchId { get; set; }
            public string ContractorId { get; set; }
            public int Eta { get; set; }
            /// <summary>
            /// Used for OON accepts only
            /// </summary>
            public AcceptProviderModel Provider { get; set; }
        }


        public sealed class AcceptProviderModel
        {
            public string ContractorId { get; set; }
            public string Name { get; set; }
            public string Address { get; set; }
            public string City { get; set; }
            public string State { get; set; }
            public string Zip { get; set; }
            public decimal Latitude { get; set; }
            public decimal Longitude { get; set; }
            public string Phone { get; set; }
            public string Email { get; set; }
        }


        public sealed class DispatchRefuseModel
        {
            public string Type { get; set; } = "refuse";
            public string DispatchId { get; set; }
            public string ContractorId { get; set; }
            public int ReasonId { get; set; }
            public string ReasonName { get; set; }
        }

        public sealed class DispatchCancelModel
        {
            public string Type { get; set; } = "cancel";
            public string DispatchId { get; set; }
            public string ContractorId { get; set; }
            public string LocationId { get; set; }
            public int ReasonId { get; set; }
            public string ReasonName { get; set; }
            public string Comments { get; set; }
        }

        public sealed class DispatchExpiredModel
        {
            public string Type { get; set; } = "expired";
            public string DispatchId { get; set; }
            public string ContractorId { get; set; }

        }


        public sealed class DispatchStatusUpdateModel
        {
            public string Type { get; set; } = "statusUpdate";
            public string DispatchId { get; set; }
            public string ContractorId { get; set; }
            public string Status { get; set; }
            public DriverModel Driver { get; set; }
        }

        public sealed class DispatchPhotoModel
        {
            public string Type { get; set; } = "photo";
            public string DispatchId { get; set; }
            public string ContractorId { get; set; }
            public decimal Latitude { get; set; }
            public decimal Longitude { get; set; }
            public DateTime Timestamp { get; set; }
            public string Url { get; set; }
        }

        public sealed class DispatchGoaModel
        {
            public string Type { get; set; } = "goa";
            public string DispatchId { get; set; }
            public string ContractorId { get; set; }
            public string ReasonId { get; set; }
            public string Comments { get; set; }
        }

        public sealed class DriverModel
        {
            public string Id { get; set; }
            public string FirstName { get; set; }
            public string LastName { get; set; }
            public decimal Latitude { get; set; }
            public decimal Longitude { get; set; }
        }

        public sealed class DispatchCompleteModel
        {
            public string Type { get; set; } = "complete";
            public string DispatchId { get; set; }
            public string ContractorId { get; set; }
            public string LocationId { get; set; }
            public DriverModel Driver { get; set; }

            public DateTime? DispatchedTime { get; set; }
            public DateTime? EnrouteTime { get; set; }
            public DateTime? OnSceneTime { get; set; }
            public DateTime? TowingTime { get; set; }
            public DateTime? DestinationTime { get; set; }
            public DateTime? CompletionTime { get; set; }

            public int? Odometer { get; set; }
            public string Vin { get; set; }

            public class InvoiceItemModel
            {
                public string Code { get; set; }
                public decimal Quantity { get; set; }
                public decimal Price { get; set; }
            }
        }

        public void Accept(DispatchAcceptModel payload)
        {
            var log = NewLogEvent("Accept", payload.ToJson());

            try
            {
                if (payload.Type != "accept")
                    throw new ArgumentException("Type must be: accept");

                if (string.IsNullOrWhiteSpace(payload.DispatchId))
                    throw new ArgumentException("DispatchId is null or empty - it must be specified.");

                if (string.IsNullOrWhiteSpace(payload.ContractorId))
                    throw new ArgumentException("ContractorId is null or empty - it must be specified.");

                var resp = Send(CallUpdateUrl, payload.ToJson(), Method.Post);

                log.Properties["contractorId"] = payload.ContractorId;
                log.Properties["dispatchId"] = payload.DispatchId;
                log.Properties["response"] = resp.Content;
                log.Properties["status"] = resp.StatusCode;

                if (!resp.IsSuccessful)
                {
                    log.Level = LogLevel.Error;

                    throw new Exception("Error invoking accept endpoint:" + resp.StatusCode + "/" + resp.Content);
                }
            }
            finally
            {
                logger.Log(log);
            }
        }


        public void Refuse(DispatchRefuseModel payload)
        {
            var log = NewLogEvent("Refuse", payload.ToJson());

            try
            {
                if (payload.Type != "refuse")
                    throw new ArgumentException("Type must be: refuse");

                if (string.IsNullOrWhiteSpace(payload.DispatchId))
                    throw new ArgumentException("DispatchId is null or empty - it must be specified.");

                if (string.IsNullOrWhiteSpace(payload.ContractorId))
                    throw new ArgumentException("ContractorId is null or empty - it must be specified.");

                if (payload.ReasonId == 0)
                    throw new ArgumentException("ReasonId is 0 - it must have a value specified.");

                var resp = Send(CallUpdateUrl, payload.ToJson(), Method.Post);

                log.Properties["contractorId"] = payload.ContractorId;
                log.Properties["dispatchId"] = payload.DispatchId;
                log.Properties["response"] = resp.Content;
                log.Properties["status"] = resp.StatusCode;

                if (!resp.IsSuccessful)
                {
                    log.Level = LogLevel.Error;

                    throw new Exception("Error invoking Refused endpoint:" + resp.StatusCode + "/" + resp.Content);
                }
            }
            finally
            {
                logger.Log(log);
            }
        }

        private static readonly string[] ValidStatuses = new string[] { "DISPATCHED", "EN_ROUTE", "ON_SCENE", "TOWING", "DESTINATION", "COMPLETE" };

        public static class StatusCodes
        {
            public const string Dispatched = "DISPATCHED";
            public const string Enroute = "EN_ROUTE";
            public const string OnScene = "ON_SCENE";
            public const string Towing = "TOWING";
            public const string Destination = "DESTINATION";
            public const string Complete = "COMPLETE";
        }

        static string ComputeSignature(string stringToSign)
        {
            string secret = "31A5EAC1-FFAE-4E5A-AA0A-E1720A56A63A";
            using (var hmacsha256 = new HMACSHA256(Encoding.ASCII.GetBytes(secret)))
            {
                var bytes = Encoding.ASCII.GetBytes(stringToSign);
                var hashedBytes = hmacsha256.ComputeHash(bytes);
                //return Encoding.ASCII.GetString(hashedBytes);

                return BitConverter.ToString(hashedBytes).Replace("-", "").ToLower();
            }
        }

        public static string TowbookStatusIdToName(int n)
        {
            string code = null;
            if (n == 1)
                code = TrxRestClient.StatusCodes.Dispatched;
            if (n == 2)
                code = TrxRestClient.StatusCodes.Enroute;
            if (n == 3)
                code = TrxRestClient.StatusCodes.OnScene;
            if (n == 4)
                code = TrxRestClient.StatusCodes.Towing;
            if (n == 5)
                code = TrxRestClient.StatusCodes.Complete;
            if (n == 7)
                code = TrxRestClient.StatusCodes.Destination;

            return code;
        }

        public void StatusUpdate(DispatchStatusUpdateModel payload)
        {
            var log = NewLogEvent("StatusUpdate", payload.ToJson());

            try
            {
                if (payload.Type != "statusUpdate" && 
                    payload.Type != "breadcrumb")
                    throw new ArgumentException("Type must be: statusUpdate or breadcrumb");

                if (payload.Status != StatusCodes.Dispatched &&
                    payload.Status != StatusCodes.Enroute &&
                    payload.Status != StatusCodes.OnScene &&
                    payload.Status != StatusCodes.Towing &&
                    payload.Status != StatusCodes.Destination &&
                    payload.Status != StatusCodes.Complete)
                    throw new ArgumentException("Invalid Status value passed: " + payload.Status + ".");

                string prefix = "";

                if (this.EnvironmentName == "DEV" || this.EnvironmentName == "TEST")
                    prefix = "/sandbox";

                var baseUrl = "https://www.rapitow.com";

                AssignRestClient(baseUrl, baseUrl);

                var ts = DateTime.UtcNow.ToString("yyyyMMddHHmmss");

                var url = "qr=getcallstatustowbook" +
                    "&hcid=EPF623BSR1Gh_zmjVsLQgkqIl7UHFQ65FVrqEkOjHLksQN3DQeW08x6zHL8g4ziN_nQP79xX1G_vGLSQHZgcHEU7zTgTXzAvyQ452pCrewgm0f3rtrpReQ_d7THleMc4Pw" +
                    "&ts=" + ts;


                var comp = ComputeSignature(url);
                url += "&hmac=" + comp;

                var nr = new RestRequest(prefix + "/appserver", RestSharp.Method.Post);
                nr.AddParameter("qr", "getcallstatustowbook", ParameterType.QueryString);
                nr.AddParameter("hcid", "EPF623BSR1Gh_zmjVsLQgkqIl7UHFQ65FVrqEkOjHLksQN3DQeW08x6zHL8g4ziN_nQP79xX1G_vGLSQHZgcHEU7zTgTXzAvyQ452pCrewgm0f3rtrpReQ_d7THleMc4Pw", ParameterType.QueryString);
                nr.AddParameter("ts", ts, ParameterType.QueryString);
                nr.AddParameter("hmac", comp, ParameterType.QueryString);

                nr.AddParameter("application/json",
                    payload.ToJson(), ParameterType.RequestBody);

                var resp = _restClient.Execute(nr);

                log.Properties["contractorId"] = payload.ContractorId;
                log.Properties["dispatchId"] = payload.DispatchId;
                log.Properties["response"] = resp.Content;
                log.Properties["status"] = resp.StatusCode;

                if (!resp.IsSuccessful)
                {
                    log.Level = LogLevel.Error;

                    throw new Exception("Error invoking StatusUpdate endpoint:" + resp.StatusCode + "/" + resp.Content);
                }
            }
            finally
            {
                logger.Log(log);
            }
        }

        public sealed class DispatchBreadcrumbPayload
        {
            public string Type { get; set; } = "breadcrumb";
            public string DispatchId { get; set; }
            public string DriverId { get; set; }
            public DateTime Timestamp { get; set; }
            public decimal Latitude { get; set; }
            public decimal Longitude { get; set; }
        }

        public void Breadcrumb(DispatchBreadcrumbPayload payload)
        {
            var log = NewLogEvent("Breadcrumb", payload.ToJson());

            try
            {
                if (payload.Type != "breadcrumb")
                    throw new ArgumentException("Type must be: breadcrumb");

                var resp = Send(CallUpdateUrl, payload.ToJson(), Method.Patch);

                log.Properties["dispatchId"] = payload.DispatchId;
                log.Properties["response"] = resp.Content;
                log.Properties["status"] = resp.StatusCode;

                if (!resp.IsSuccessful)
                {
                    log.Level = LogLevel.Error;

                    throw new Exception("Error invoking Refused endpoint:" + resp.StatusCode + "/" + resp.Content);
                }
            }
            catch (Exception y)
            {
                log.Level = LogLevel.Error;
                log.Exception = y;
                throw;
            }
            finally
            {
                logger.Log(log);
            }
        }

        public void Photo(DispatchPhotoModel payload)
        {
            var log = NewLogEvent("Photo", payload.ToJson());

            try
            {
                if (payload.Type != "photo")
                    throw new ArgumentException("Type must be: photo");

                if (string.IsNullOrWhiteSpace(payload.DispatchId))
                    throw new ArgumentException("DispatchId is null or empty - it must be specified.");

                if (string.IsNullOrWhiteSpace(payload.ContractorId))
                    throw new ArgumentException("ContractorId is null or empty - it must be specified.");

                var resp = Send(CallUpdateUrl, payload.ToJson(), Method.Patch);

                log.Properties["contractorId"] = payload.ContractorId;
                log.Properties["dispatchId"] = payload.DispatchId;
                log.Properties["response"] = resp.Content;
                log.Properties["status"] = resp.StatusCode;

                if (!resp.IsSuccessful)
                {
                    log.Level = LogLevel.Error;

                    throw new Exception("Error invoking Photo endpoint:" + resp.StatusCode + "/" + resp.Content);
                }
            }
            finally
            {
                logger.Log(log);
            }
        }

        private const string CallUpdateUrl = "/services/apexrest/RETMTB";

        public void Cancel(DispatchCancelModel payload)
        {
            var log = NewLogEvent("Cancel", payload.ToJson());

            try
            {
                if (payload.Type != "cancel")
                    throw new ArgumentException("Type must be: cancel");

                if (string.IsNullOrWhiteSpace(payload.ContractorId))
                    throw new ArgumentException("ContractorId is null or empty - it must be specified.");

                if (string.IsNullOrWhiteSpace(payload.DispatchId))
                    throw new ArgumentException("DispatchId is null or empty - it must be specified.");

                var resp = Send(CallUpdateUrl, payload.ToJson(), Method.Post);

                log.Properties["contractorId"] = payload.ContractorId;
                log.Properties["dispatchId"] = payload.DispatchId;
                log.Properties["response"] = resp.Content;
                log.Properties["status"] = resp.StatusCode;

                if (!resp.IsSuccessful)
                {
                    log.Level = LogLevel.Error;

                    throw new Exception("Error invoking Cancel endpoint:" + resp.StatusCode + "/" + resp.Content);
                }
            }
            catch (Exception y)
            {
                log.Level = LogLevel.Error;
                log.Exception = y;
                throw;
            }
            finally
            {
                logger.Log(log);
            }
        }


        public void Expired(DispatchExpiredModel payload)
        {
            var log = NewLogEvent("Expired", payload.ToJson());

            try
            {
                if (payload.Type != "expired")
                    throw new ArgumentException("Type must be: expired");

                if (string.IsNullOrWhiteSpace(payload.ContractorId))
                    throw new ArgumentException("ContractorId is null or empty - it must be specified.");

                if (string.IsNullOrWhiteSpace(payload.DispatchId))
                    throw new ArgumentException("DispatchId is null or empty - it must be specified.");

                var resp = Send(CallUpdateUrl, payload.ToJson(), Method.Post);

                log.Properties["contractorId"] = payload.ContractorId;
                log.Properties["dispatchId"] = payload.DispatchId;
                log.Properties["response"] = resp.Content;
                log.Properties["status"] = resp.StatusCode;

                if (!resp.IsSuccessful)
                {
                    log.Level = LogLevel.Error;

                    throw new Exception("Error invoking Cancel endpoint:" + resp.StatusCode + "/" + resp.Content);
                }
            }
            catch (Exception y)
            {
                log.Level = LogLevel.Error;
                log.Exception = y;
                throw;
            }
            finally
            {
                logger.Log(log);
            }
        }

        public void Goa(DispatchGoaModel payload)
        {
            var log = NewLogEvent("GOA", payload.ToJson());

            try
            {
                if (payload.Type != "goa")
                    throw new ArgumentException("Type must be goa");

                var resp = Send(CallUpdateUrl, payload.ToJson(), Method.Patch);

                log.Properties["contractorId"] = payload.ContractorId;
                log.Properties["dispatchId"] = payload.DispatchId;
                log.Properties["response"] = resp.Content;
                log.Properties["status"] = resp.StatusCode;

                if (!resp.IsSuccessful)
                {
                    log.Level = LogLevel.Error;

                    throw new Exception("Error invoking GOA endpoint:" + resp.StatusCode + "/" + resp.Content);
                }
            }
            catch (Exception y)
            {
                log.Level = LogLevel.Error;
                log.Exception = y;
                throw;
            }
            finally
            {
                logger.Log(log);
            }
        }

        public void Complete(DispatchCompleteModel payload)
        {
            var log = NewLogEvent("Complete", payload.ToJson());

            try
            {
                if (payload.Type != "complete")
                    throw new ArgumentException("Type must be: complete");

                if (string.IsNullOrWhiteSpace(payload.DispatchId))
                    throw new ArgumentException("DispatchId is null or empty - it must be specified.");

                var resp = Send(CallUpdateUrl, payload.ToJson(), Method.Patch);

                log.Properties["dispatchId"] = payload.DispatchId;
                log.Properties["response"] = resp.Content;
                log.Properties["status"] = resp.StatusCode;

                if (!resp.IsSuccessful)
                {
                    log.Level = LogLevel.Error;

                    throw new Exception("Error invoking Complete endpoint:" + resp.StatusCode + "/" + resp.Content);
                }
            }
            catch (Exception y)
            {
                log.Level = LogLevel.Error;
                log.Exception = y;
                throw;
            }
            finally
            {
                logger.Log(log);
            }
        }

        public RestResponse Send(string url, string payload, Method method)
        {
            var token = GetToken();

            AssignRestClient(token.AccessToken, token.InstanceUrl, token.AccessToken);

            var request = new RestRequest(url, method);
            
            if (method != Method.Get)
                request.AddParameter("application/json", payload, ParameterType.RequestBody);

            var response = _restClient.Execute(request);

            Console.WriteLine(DateTime.Now.ToString() + " " + UrlBase + url + ": " + response.Content);

            return response;
        }

        public void LoginOrOut(string recordId, bool loggedIn)
        {
            var log = NewLogEvent(loggedIn ? "Login" : "Logout", "null");
            try
            {
                var token = GetToken();

                AssignRestClient(token.AccessToken, token.InstanceUrl, token.AccessToken);

                var nr = new RestRequest("/services/data/v54.0/sobjects/Account/" +
                    recordId, Method.Patch);

                nr.AddParameter("application/json",
                    new
                    {
                        Logged_In_Towbook__c = loggedIn
                    }.ToJson(), ParameterType.RequestBody);


                
                var ra = _restClient.Execute(nr);


                Console.WriteLine("**" + ra.Content.ToString());
                Console.WriteLine(ra.StatusCode.ToString());

                if (!ra.IsSuccessful)
                {
                    log.Level = LogLevel.Error;

                    throw new Exception("Error invoking Cancel endpoint:" + ra.StatusCode + "/" + ra.Content);
                }

                log.Properties["contractorId"] = recordId;
                log.Properties["response"] = ra.Content;
                log.Properties["status"] = ra.StatusCode;
            }
            catch (Exception y)
            {
                log.Level = LogLevel.Error;
                log.Exception = y;
                throw;
            }
            finally
            {
                logger.Log(log);
            }
        }

        /// <summary>
        /// Look up a TRX Account inside Salesforce by the ProviderId/ContractorId
        /// </summary>
        /// <param name="providerId"></param>
        /// <returns></returns>
        public TrxAccount GetByProviderId(string providerId)
        {
            var token = GetToken();

            AssignRestClient(token.AccessToken, token.InstanceUrl, token.AccessToken);

            var nr = new RestRequest("/services/data/v54.0/sobjects/Account/Stored_Provider_Id__c/" +
                providerId, Method.Get);

            var ra = _restClient.Execute(nr);

            if (ra.StatusCode == System.Net.HttpStatusCode.NotFound)
                return null;

            return JsonConvert.DeserializeObject<TrxAccount>(ra.Content);
        }

        public sealed class TrxAccount
        {
            public string Id { get; set; }
            public string Name { get; set; }
            public bool Logged_In_Towbook__c { get; set; }
        }


        public TrxAccessTokenPayload GetToken()
        {
            var trx = Core.GetRedisValue("trx:" + this.EnvironmentName);

            if (trx != null)
            {
                return JsonConvert.DeserializeObject<TrxAccessTokenPayload>(trx);
            }

            var newToken = RenewToken();

            Core.SetRedisValue("trx:" + this.EnvironmentName, newToken.ToJson(), TimeSpan.FromMinutes(10));

            return newToken;
        }

        private TrxAccessTokenPayload RenewToken()
        {
            AssignRestClient(UrlBase, UrlBase);

            var nr = new RestRequest("/services/oauth2/token", RestSharp.Method.Post);

            nr.AddParameter("username", Username, ParameterType.GetOrPost);
            nr.AddParameter("password", Password, ParameterType.GetOrPost);
            nr.AddParameter("grant_type", "password", ParameterType.GetOrPost);
            nr.AddParameter("client_id", ClientId, ParameterType.GetOrPost);
            nr.AddParameter("client_secret", ClientSecret, ParameterType.GetOrPost);

            var ra = _restClient.Execute(nr);

            return JsonConvert.DeserializeObject<TrxAccessTokenPayload>(ra.Content);
        }

        private void AssignRestClient(string key, string baseUrl, string authenticator = null)
        {
            if (_restClientCache.TryGetValue(key, out var cachedClient))
            {
                _restClient = cachedClient;
            }
            else
            {
                var options = new RestClientOptions(baseUrl);
                if (!string.IsNullOrWhiteSpace(authenticator))
                {
                    options.Authenticator = new
                        OAuth2AuthorizationRequestHeaderAuthenticator(authenticator, "Bearer");
                }

                _restClient = new RestClient(AppServicesHelper.HttpClient, options);
                _restClientCache.Add(key, _restClient);
            }
        }
    }

    public sealed class TrxAccessTokenPayload
    {
        [JsonProperty("access_token")]
        public string AccessToken { get; set; }

        [JsonProperty("instance_url")]
        public string InstanceUrl { get; set; }
        public string Id { get; set; }

        [JsonProperty("token_type")]
        public string TokenType { get; set; }

        [JsonProperty("issued_at")]
        public string IssuedAt { get; set; }
        public string Signature { get; set; }
    }

}
