using Extric.Towbook.Integration.MotorClubs.Services;
using Extric.Towbook.Utility;
using NLog;
using System;
using System.Web.Http;
using Extric.Towbook.Integrations.MotorClubs.Rapitow;

namespace Extric.Towbook.API.Integration.MotorClubs.Rapitow
{
    public class CallAssignedController : ApiController
    {
        private static readonly Logger logger = LogManager.GetCurrentClassLogger();

        public object Post(dynamic obj)
        {
            var log = new LogEventInfo();
            var json = JsonExtensions.ToJson(obj);

            log.Level = LogLevel.Info;
            log.Message = "CallAssigned";
            log.Properties.Add("masterAccountName", "Urgent.ly");
            log.Properties.Add("json", json);

            logger.Log(log);
            
            var um = new RapitowMessage();
            um.ProviderId = obj.provider.id;
            um.JsonData = json;
            um.TypeId = 7; // call update;
            um.CaseId = obj.context.caseId;
            um.Save();

            var up = RapitowProvider.GetByProviderId(um.ProviderId);

            if (up == null)
            {
                return BadRequest("Unknown ProviderId: " + um.ProviderId);
            }
            else
            {
                DigitalDispatchService.HandleCallEvent(up.CompanyId, up.AccountId, um.ToJson(null), DigitalDispatchService.CallEventType.Accepted);

                return Ok();
            }
        }
    }
}
