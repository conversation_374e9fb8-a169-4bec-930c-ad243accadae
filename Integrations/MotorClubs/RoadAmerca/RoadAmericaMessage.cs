using Extric.Towbook.Utility;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Extric.Towbook.Integrations.MotorClubs.RoadAmerica
{
    [Table("MCDispatch.RoadAmericaMessages")]
    public class RoadAmericaMessage
    {
        [Key]
        public long RoadAmericaMessageId { get; set; }
        public string ContractorId { get; set; }
        public string DispatchRequestNumber { get; set; }
        public string PurchaseOrderNumber { get; set; }
        public string Title { get; set; }
        public string Message { get; set; }
        public RoadAmericaMessageType Type { get; set; }

        public RoadAmericaMessage() { }

        public void Save()
        {
            if (this.RoadAmericaMessageId == 0)
                this.RoadAmericaMessageId = SqlMapper.Insert<RoadAmericaMessage>(this);
            else
                SqlMapper.Update<RoadAmericaMessage>(this);
        }
    }

    public enum RoadAmericaMessageType
    {
        None = 0,
        DSP = 1,
        RSP = 2,
        CNL = 3,
        ACK = 4
    }
}
