//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Extric.Towbook.Integrations.MotorClubs.RoadAmerica.RoadAmericaDigitalDispatch {
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "4.0.0.0")]
    [System.ServiceModel.ServiceContractAttribute(Namespace="https://road-america.com/RAServices/", ConfigurationName="RoadAmericaDigitalDispatch.DDXMLSoap")]
    public interface DDXMLSoap {
        
        // CODEGEN: Generating message contract since element name HelloWorldResult from namespace https://road-america.com/RAServices/ is not marked nillable
        [System.ServiceModel.OperationContractAttribute(Action="https://road-america.com/RAServices/HelloWorld", ReplyAction="*")]
        Extric.Towbook.Integrations.MotorClubs.RoadAmerica.RoadAmericaDigitalDispatch.HelloWorldResponse HelloWorld(Extric.Towbook.Integrations.MotorClubs.RoadAmerica.RoadAmericaDigitalDispatch.HelloWorldRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="https://road-america.com/RAServices/HelloWorld", ReplyAction="*")]
        System.Threading.Tasks.Task<Extric.Towbook.Integrations.MotorClubs.RoadAmerica.RoadAmericaDigitalDispatch.HelloWorldResponse> HelloWorldAsync(Extric.Towbook.Integrations.MotorClubs.RoadAmerica.RoadAmericaDigitalDispatch.HelloWorldRequest request);
        
        // CODEGEN: Generating message contract since element name TestResult from namespace https://road-america.com/RAServices/ is not marked nillable
        [System.ServiceModel.OperationContractAttribute(Action="https://road-america.com/RAServices/Test", ReplyAction="*")]
        Extric.Towbook.Integrations.MotorClubs.RoadAmerica.RoadAmericaDigitalDispatch.TestResponse Test(Extric.Towbook.Integrations.MotorClubs.RoadAmerica.RoadAmericaDigitalDispatch.TestRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="https://road-america.com/RAServices/Test", ReplyAction="*")]
        System.Threading.Tasks.Task<Extric.Towbook.Integrations.MotorClubs.RoadAmerica.RoadAmericaDigitalDispatch.TestResponse> TestAsync(Extric.Towbook.Integrations.MotorClubs.RoadAmerica.RoadAmericaDigitalDispatch.TestRequest request);
        
        // CODEGEN: Generating message contract since element name xml from namespace https://road-america.com/RAServices/ is not marked nillable
        [System.ServiceModel.OperationContractAttribute(Action="https://road-america.com/RAServices/DDXMLReceiveMessage", ReplyAction="*")]
        Extric.Towbook.Integrations.MotorClubs.RoadAmerica.RoadAmericaDigitalDispatch.DDXMLReceiveMessageResponse DDXMLReceiveMessage(Extric.Towbook.Integrations.MotorClubs.RoadAmerica.RoadAmericaDigitalDispatch.DDXMLReceiveMessageRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="https://road-america.com/RAServices/DDXMLReceiveMessage", ReplyAction="*")]
        System.Threading.Tasks.Task<Extric.Towbook.Integrations.MotorClubs.RoadAmerica.RoadAmericaDigitalDispatch.DDXMLReceiveMessageResponse> DDXMLReceiveMessageAsync(Extric.Towbook.Integrations.MotorClubs.RoadAmerica.RoadAmericaDigitalDispatch.DDXMLReceiveMessageRequest request);
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "4.0.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class HelloWorldRequest {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="HelloWorld", Namespace="https://road-america.com/RAServices/", Order=0)]
        public Extric.Towbook.Integrations.MotorClubs.RoadAmerica.RoadAmericaDigitalDispatch.HelloWorldRequestBody Body;
        
        public HelloWorldRequest() {
        }
        
        public HelloWorldRequest(Extric.Towbook.Integrations.MotorClubs.RoadAmerica.RoadAmericaDigitalDispatch.HelloWorldRequestBody Body) {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "4.0.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute()]
    public partial class HelloWorldRequestBody {
        
        public HelloWorldRequestBody() {
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "4.0.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class HelloWorldResponse {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="HelloWorldResponse", Namespace="https://road-america.com/RAServices/", Order=0)]
        public Extric.Towbook.Integrations.MotorClubs.RoadAmerica.RoadAmericaDigitalDispatch.HelloWorldResponseBody Body;
        
        public HelloWorldResponse() {
        }
        
        public HelloWorldResponse(Extric.Towbook.Integrations.MotorClubs.RoadAmerica.RoadAmericaDigitalDispatch.HelloWorldResponseBody Body) {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "4.0.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="https://road-america.com/RAServices/")]
    public partial class HelloWorldResponseBody {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public object HelloWorldResult;
        
        public HelloWorldResponseBody() {
        }
        
        public HelloWorldResponseBody(object HelloWorldResult) {
            this.HelloWorldResult = HelloWorldResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "4.0.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class TestRequest {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="Test", Namespace="https://road-america.com/RAServices/", Order=0)]
        public Extric.Towbook.Integrations.MotorClubs.RoadAmerica.RoadAmericaDigitalDispatch.TestRequestBody Body;
        
        public TestRequest() {
        }
        
        public TestRequest(Extric.Towbook.Integrations.MotorClubs.RoadAmerica.RoadAmericaDigitalDispatch.TestRequestBody Body) {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "4.0.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute()]
    public partial class TestRequestBody {
        
        public TestRequestBody() {
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "4.0.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class TestResponse {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="TestResponse", Namespace="https://road-america.com/RAServices/", Order=0)]
        public Extric.Towbook.Integrations.MotorClubs.RoadAmerica.RoadAmericaDigitalDispatch.TestResponseBody Body;
        
        public TestResponse() {
        }
        
        public TestResponse(Extric.Towbook.Integrations.MotorClubs.RoadAmerica.RoadAmericaDigitalDispatch.TestResponseBody Body) {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "4.0.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="https://road-america.com/RAServices/")]
    public partial class TestResponseBody {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public object TestResult;
        
        public TestResponseBody() {
        }
        
        public TestResponseBody(object TestResult) {
            this.TestResult = TestResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "4.0.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class DDXMLReceiveMessageRequest {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="DDXMLReceiveMessage", Namespace="https://road-america.com/RAServices/", Order=0)]
        public Extric.Towbook.Integrations.MotorClubs.RoadAmerica.RoadAmericaDigitalDispatch.DDXMLReceiveMessageRequestBody Body;
        
        public DDXMLReceiveMessageRequest() {
        }
        
        public DDXMLReceiveMessageRequest(Extric.Towbook.Integrations.MotorClubs.RoadAmerica.RoadAmericaDigitalDispatch.DDXMLReceiveMessageRequestBody Body) {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "4.0.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="https://road-america.com/RAServices/")]
    public partial class DDXMLReceiveMessageRequestBody {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string xml;
        
        public DDXMLReceiveMessageRequestBody() {
        }
        
        public DDXMLReceiveMessageRequestBody(string xml) {
            this.xml = xml;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "4.0.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class DDXMLReceiveMessageResponse {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="DDXMLReceiveMessageResponse", Namespace="https://road-america.com/RAServices/", Order=0)]
        public Extric.Towbook.Integrations.MotorClubs.RoadAmerica.RoadAmericaDigitalDispatch.DDXMLReceiveMessageResponseBody Body;
        
        public DDXMLReceiveMessageResponse() {
        }
        
        public DDXMLReceiveMessageResponse(Extric.Towbook.Integrations.MotorClubs.RoadAmerica.RoadAmericaDigitalDispatch.DDXMLReceiveMessageResponseBody Body) {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "4.0.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="https://road-america.com/RAServices/")]
    public partial class DDXMLReceiveMessageResponseBody {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public string DDXMLReceiveMessageResult;
        
        public DDXMLReceiveMessageResponseBody() {
        }
        
        public DDXMLReceiveMessageResponseBody(string DDXMLReceiveMessageResult) {
            this.DDXMLReceiveMessageResult = DDXMLReceiveMessageResult;
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "4.0.0.0")]
    public interface DDXMLSoapChannel : Extric.Towbook.Integrations.MotorClubs.RoadAmerica.RoadAmericaDigitalDispatch.DDXMLSoap, System.ServiceModel.IClientChannel {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "4.0.0.0")]
    public partial class DDXMLSoapClient : System.ServiceModel.ClientBase<Extric.Towbook.Integrations.MotorClubs.RoadAmerica.RoadAmericaDigitalDispatch.DDXMLSoap>, Extric.Towbook.Integrations.MotorClubs.RoadAmerica.RoadAmericaDigitalDispatch.DDXMLSoap {
        
        public DDXMLSoapClient() {
        }
        
        public DDXMLSoapClient(string endpointConfigurationName) : 
                base(endpointConfigurationName) {
        }
        
        public DDXMLSoapClient(string endpointConfigurationName, string remoteAddress) : 
                base(endpointConfigurationName, remoteAddress) {
        }
        
        public DDXMLSoapClient(string endpointConfigurationName, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(endpointConfigurationName, remoteAddress) {
        }
        
        public DDXMLSoapClient(System.ServiceModel.Channels.Binding binding, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(binding, remoteAddress) {
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        Extric.Towbook.Integrations.MotorClubs.RoadAmerica.RoadAmericaDigitalDispatch.HelloWorldResponse Extric.Towbook.Integrations.MotorClubs.RoadAmerica.RoadAmericaDigitalDispatch.DDXMLSoap.HelloWorld(Extric.Towbook.Integrations.MotorClubs.RoadAmerica.RoadAmericaDigitalDispatch.HelloWorldRequest request) {
            return base.Channel.HelloWorld(request);
        }
        
        public object HelloWorld() {
            Extric.Towbook.Integrations.MotorClubs.RoadAmerica.RoadAmericaDigitalDispatch.HelloWorldRequest inValue = new Extric.Towbook.Integrations.MotorClubs.RoadAmerica.RoadAmericaDigitalDispatch.HelloWorldRequest();
            inValue.Body = new Extric.Towbook.Integrations.MotorClubs.RoadAmerica.RoadAmericaDigitalDispatch.HelloWorldRequestBody();
            Extric.Towbook.Integrations.MotorClubs.RoadAmerica.RoadAmericaDigitalDispatch.HelloWorldResponse retVal = ((Extric.Towbook.Integrations.MotorClubs.RoadAmerica.RoadAmericaDigitalDispatch.DDXMLSoap)(this)).HelloWorld(inValue);
            return retVal.Body.HelloWorldResult;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Extric.Towbook.Integrations.MotorClubs.RoadAmerica.RoadAmericaDigitalDispatch.HelloWorldResponse> Extric.Towbook.Integrations.MotorClubs.RoadAmerica.RoadAmericaDigitalDispatch.DDXMLSoap.HelloWorldAsync(Extric.Towbook.Integrations.MotorClubs.RoadAmerica.RoadAmericaDigitalDispatch.HelloWorldRequest request) {
            return base.Channel.HelloWorldAsync(request);
        }
        
        public System.Threading.Tasks.Task<Extric.Towbook.Integrations.MotorClubs.RoadAmerica.RoadAmericaDigitalDispatch.HelloWorldResponse> HelloWorldAsync() {
            Extric.Towbook.Integrations.MotorClubs.RoadAmerica.RoadAmericaDigitalDispatch.HelloWorldRequest inValue = new Extric.Towbook.Integrations.MotorClubs.RoadAmerica.RoadAmericaDigitalDispatch.HelloWorldRequest();
            inValue.Body = new Extric.Towbook.Integrations.MotorClubs.RoadAmerica.RoadAmericaDigitalDispatch.HelloWorldRequestBody();
            return ((Extric.Towbook.Integrations.MotorClubs.RoadAmerica.RoadAmericaDigitalDispatch.DDXMLSoap)(this)).HelloWorldAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        Extric.Towbook.Integrations.MotorClubs.RoadAmerica.RoadAmericaDigitalDispatch.TestResponse Extric.Towbook.Integrations.MotorClubs.RoadAmerica.RoadAmericaDigitalDispatch.DDXMLSoap.Test(Extric.Towbook.Integrations.MotorClubs.RoadAmerica.RoadAmericaDigitalDispatch.TestRequest request) {
            return base.Channel.Test(request);
        }
        
        public object Test() {
            Extric.Towbook.Integrations.MotorClubs.RoadAmerica.RoadAmericaDigitalDispatch.TestRequest inValue = new Extric.Towbook.Integrations.MotorClubs.RoadAmerica.RoadAmericaDigitalDispatch.TestRequest();
            inValue.Body = new Extric.Towbook.Integrations.MotorClubs.RoadAmerica.RoadAmericaDigitalDispatch.TestRequestBody();
            Extric.Towbook.Integrations.MotorClubs.RoadAmerica.RoadAmericaDigitalDispatch.TestResponse retVal = ((Extric.Towbook.Integrations.MotorClubs.RoadAmerica.RoadAmericaDigitalDispatch.DDXMLSoap)(this)).Test(inValue);
            return retVal.Body.TestResult;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Extric.Towbook.Integrations.MotorClubs.RoadAmerica.RoadAmericaDigitalDispatch.TestResponse> Extric.Towbook.Integrations.MotorClubs.RoadAmerica.RoadAmericaDigitalDispatch.DDXMLSoap.TestAsync(Extric.Towbook.Integrations.MotorClubs.RoadAmerica.RoadAmericaDigitalDispatch.TestRequest request) {
            return base.Channel.TestAsync(request);
        }
        
        public System.Threading.Tasks.Task<Extric.Towbook.Integrations.MotorClubs.RoadAmerica.RoadAmericaDigitalDispatch.TestResponse> TestAsync() {
            Extric.Towbook.Integrations.MotorClubs.RoadAmerica.RoadAmericaDigitalDispatch.TestRequest inValue = new Extric.Towbook.Integrations.MotorClubs.RoadAmerica.RoadAmericaDigitalDispatch.TestRequest();
            inValue.Body = new Extric.Towbook.Integrations.MotorClubs.RoadAmerica.RoadAmericaDigitalDispatch.TestRequestBody();
            return ((Extric.Towbook.Integrations.MotorClubs.RoadAmerica.RoadAmericaDigitalDispatch.DDXMLSoap)(this)).TestAsync(inValue);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        Extric.Towbook.Integrations.MotorClubs.RoadAmerica.RoadAmericaDigitalDispatch.DDXMLReceiveMessageResponse Extric.Towbook.Integrations.MotorClubs.RoadAmerica.RoadAmericaDigitalDispatch.DDXMLSoap.DDXMLReceiveMessage(Extric.Towbook.Integrations.MotorClubs.RoadAmerica.RoadAmericaDigitalDispatch.DDXMLReceiveMessageRequest request) {
            return base.Channel.DDXMLReceiveMessage(request);
        }
        
        public string DDXMLReceiveMessage(string xml) {
            Extric.Towbook.Integrations.MotorClubs.RoadAmerica.RoadAmericaDigitalDispatch.DDXMLReceiveMessageRequest inValue = new Extric.Towbook.Integrations.MotorClubs.RoadAmerica.RoadAmericaDigitalDispatch.DDXMLReceiveMessageRequest();
            inValue.Body = new Extric.Towbook.Integrations.MotorClubs.RoadAmerica.RoadAmericaDigitalDispatch.DDXMLReceiveMessageRequestBody();
            inValue.Body.xml = xml;
            Extric.Towbook.Integrations.MotorClubs.RoadAmerica.RoadAmericaDigitalDispatch.DDXMLReceiveMessageResponse retVal = ((Extric.Towbook.Integrations.MotorClubs.RoadAmerica.RoadAmericaDigitalDispatch.DDXMLSoap)(this)).DDXMLReceiveMessage(inValue);
            return retVal.Body.DDXMLReceiveMessageResult;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Extric.Towbook.Integrations.MotorClubs.RoadAmerica.RoadAmericaDigitalDispatch.DDXMLReceiveMessageResponse> Extric.Towbook.Integrations.MotorClubs.RoadAmerica.RoadAmericaDigitalDispatch.DDXMLSoap.DDXMLReceiveMessageAsync(Extric.Towbook.Integrations.MotorClubs.RoadAmerica.RoadAmericaDigitalDispatch.DDXMLReceiveMessageRequest request) {
            return base.Channel.DDXMLReceiveMessageAsync(request);
        }
        
        public System.Threading.Tasks.Task<Extric.Towbook.Integrations.MotorClubs.RoadAmerica.RoadAmericaDigitalDispatch.DDXMLReceiveMessageResponse> DDXMLReceiveMessageAsync(string xml) {
            Extric.Towbook.Integrations.MotorClubs.RoadAmerica.RoadAmericaDigitalDispatch.DDXMLReceiveMessageRequest inValue = new Extric.Towbook.Integrations.MotorClubs.RoadAmerica.RoadAmericaDigitalDispatch.DDXMLReceiveMessageRequest();
            inValue.Body = new Extric.Towbook.Integrations.MotorClubs.RoadAmerica.RoadAmericaDigitalDispatch.DDXMLReceiveMessageRequestBody();
            inValue.Body.xml = xml;
            return ((Extric.Towbook.Integrations.MotorClubs.RoadAmerica.RoadAmericaDigitalDispatch.DDXMLSoap)(this)).DDXMLReceiveMessageAsync(inValue);
        }
    }
}
