using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Extric.Towbook.Integrations.MotorClubs.RoadAmerica.Proxy
{
    public class DSPMessageVehicleInfo
    {
        public string Year { get; set; }
        public string Color { get; set; }
        public string Make { get; set; }
        public string Model { get; set; }
        public string Lic { get; set; }
        public string State { get;set; }
        public string VIN { get; set; }
        public string VehicleType { get; set; }
        public string Odometer { get; set; }
        public string EngType { get; set; }
        public string TrailerWt { get; set; }
        public string TrailerCont { get; set; }
        public string AdditionalInfo { get; set; }

        /// <summary>
        /// Returns a a human friendly representation of the vehicle. 
        /// Example: 2006 Acura TL Black
        /// </summary>
        /// <returns></returns>
        public override string ToString()
        {
            return string.Format("{0} {1} {2} {3}",
                this.Year,
                this.Make,
                this.Model,
                this.Color).Trim();
        }
    }
}
