using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Extric.Towbook.Integrations.MotorClubs.RoadAmerica.Proxy
{
    public class DSPMessageJobInfo
    {
        public int RequiredAcknowledgeTime { get; set; }
        public string JobID { get; set; }
        public string TimeStamp { get; set; }
        public string MaxETA { get; set; }
        public string PrimaryTask { get; set; }
        public List<string> SecondaryTasks { get; set; }
        public string JobDesc { get; set; }
        public string Priority { get; set; }
        public string HazMat { get; set; }
    }
}
