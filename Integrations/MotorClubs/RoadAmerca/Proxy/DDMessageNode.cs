using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml;
using System.Xml.Serialization;

namespace Extric.Towbook.Integrations.MotorClubs.RoadAmerica.Proxy
{
    public abstract class DDMessageNode
    {
        public virtual string GetXml(string customContentName, Encoding encoding = null)
        {
            XmlElementAttribute myElementAttribute = new XmlElementAttribute();
            myElementAttribute.ElementName = customContentName;
            XmlAttributes myAttributes = new XmlAttributes();
            myAttributes.XmlElements.Add(myElementAttribute);
            XmlAttributeOverrides myOverrides = new XmlAttributeOverrides();
            myOverrides.Add(this.GetType(), "DDContent", myAttributes);
            
            XmlSerializer s = new XmlSerializer(this.GetType(), myOverrides);
            using (var ms = new MemoryStream())
            {
                s.Serialize(new RoadAmericaTextWriter(ms, encoding), this);

                if (encoding == null)
                    return System.Text.Encoding.ASCII.GetString(ms.GetBuffer());

                return encoding.GetString(ms.GetBuffer());
            }
        }
    }
}
