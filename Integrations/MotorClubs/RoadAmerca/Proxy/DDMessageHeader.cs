using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Serialization;

namespace Extric.Towbook.Integrations.MotorClubs.RoadAmerica.Proxy
{
    public class DDMessageHeader : DDMessageNode
    {
        public string HeaderVersion { get; set; }
        public string Key { get; set; }
        public string ContractorID { get; set; }
        public string ResponseID { get; set; }
        public string TransType { get; set; }
        public string MsgVersion { get; set; }
        public string ConRequired { get; set; }
        public string ResponseType { get; set; }

        public DDMessageHeader() { }
        public DDMessageHeader(string contractorId, string transType, string responseType = "ACK", string headerVersion = "1.0", string key = "TOWBOOKSOFTWARE", string msgVersion = "1.0", string conRequired = "Y")
        {
            this.HeaderVersion = headerVersion;
            this.Key = key;
            this.ContractorID = contractorId;
            this.TransType = transType;
            this.MsgVersion = msgVersion;
            this.ConRequired = conRequired;
            this.ResponseType = responseType;
        }
    }
}
