using Extric.Towbook.Utility;
using System;
using System.Linq;

namespace Extric.Towbook.Integrations.MotorClubs.Nsd
{
    [Table("MCDispatch.NsdDispatches")]
    public class NsdDispatch
    {
        [Key("NsdDispatchId")]
        public long NsdDispatchId { get; set; }
        public int NsdContractorId { get; set; }
        public string DispatchId { get; set; }
        public string DispatchJson { get; set; }
        public int CallRequestId { get; set; }
        public int? Eta { get; set; }       

        public NsdDispatch() { }

        public static NsdDispatch GetByDispatchId(string id)
        {
            return SqlMapper.Query<NsdDispatch>(
                "SELECT TOP 1 * FROM MCDispatch.NsdDispatches WITH (nolock) " +
                "WHERE DispatchId = @Id ORDER BY 1 DESC",
                new
                {
                    Id = id
                }).FirstOrDefault();

        }
        public static NsdDispatch GetByDispatchId(string id, int nsdContractorId)
        {
            return SqlMapper.Query<NsdDispatch>(
                "SELECT TOP 1 * FROM MCDispatch.NsdDispatches WITH (nolock) " +
                "WHERE DispatchId = @Id AND nsdContractorId=@nsdContractorId ORDER BY 1 DESC",
                new
                {
                    Id = id,
                    NsdContractorId = nsdContractorId
                }).FirstOrDefault();
        }

        public static NsdDispatch GetByCallRequestId(int callRequestId)
        {
            return SqlMapper.Query<NsdDispatch>(
                "SELECT * FROM MCDispatch.NsdDispatches WITH (nolock) WHERE CallRequestId = @CallRequestId", 
                new { CallRequestId = callRequestId }).FirstOrDefault();
        }

        public void Save()
        {
            if (this.NsdDispatchId == 0)
                this.NsdDispatchId = SqlMapper.Insert<NsdDispatch>(this);
            else
                SqlMapper.Update<NsdDispatch>(this);
        }

        public static NsdDispatch GetByDispatchIdCompleted(string dispatchId)
        {
            return SqlMapper.Query<NsdDispatch>(
                "SELECT TOP 1 * FROM MCDispatch.NsdDispatches STD WITH (nolock) " +
                "INNER JOIN DispatchEntryRequests DER WITH (nolock) on DER.CallRequestId=STD.CallRequestId " +
                "INNER JOIN DispatchEntries DE WITH (nolock) on DE.DispatchEntryId=DER.DispatchEntryId and DE.Status=5 and DE.Deleted=0 " +
                "WHERE STD.DispatchId = @Id ORDER BY 1 DESC",
                new
                {
                    Id = dispatchId
                }).FirstOrDefault();
        }
    }
}
