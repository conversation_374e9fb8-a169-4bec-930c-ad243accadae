{"providerId": "Microsoft.Tools.ServiceModel.Svcutil", "version": "2.1.0", "options": {"inputs": ["https://web1.nsddispatch.com/digitaldispatch/digitaldispatch.asmx"], "namespaceMappings": ["*, Extric.Towbook.Integrations.MotorClubs.Nsd.NsdService"], "outputFile": "Reference.cs", "references": ["AWSSDK.Core, {AWSSDK.Core, 3.7.108.2}", "AWSSDK.S3, {AWSSDK.S3, 3.7.108}", "Azure.Core, {Azure.Core, 1.34.0}", "Azure.Core.Amqp, {Azure.Core.Amqp, 1.3.0}", "Azure.Messaging.ServiceBus, {Azure.Messaging.ServiceBus, 7.15.0}", "Bandwidth.Sdk, {Bandwidth.Sdk, 10.2.0}", "<PERSON><PERSON>, {<PERSON><PERSON>, 2.0.143}", "<PERSON><PERSON><PERSON>, {<PERSON><PERSON><PERSON>, 2.0.78}", "Hashids.net, {Hashids.net, 1.7.0}", "HtmlAgilityPack, {HtmlAgilityPack, 1.11.37}", "ITfoxtec.Identity.Saml2, {ITfoxtec.Identity.Saml2, 4.8.6}", "jwt.dotnetstandard, {jwt.dotnetstandard, 0.0.1}", "<PERSON>ggly, {loggly-csharp, 4.6.1.116}", "Logg<PERSON>.Config, {loggly-csharp-config, 4.6.1.116}", "Microsoft.AspNetCore.Http, {Microsoft.AspNetCore.Http, 2.2.2}", "Microsoft.AspNetCore.Http.Abstractions, {Microsoft.AspNetCore.Http.Abstractions, 2.2.0}", "Microsoft.AspNetCore.Http.Features, {Microsoft.AspNetCore.Http.Features, 2.2.0}", "Microsoft.AspNetCore.WebUtilities, {Microsoft.AspNetCore.WebUtilities, 2.2.0}", "Microsoft.Azure.Amqp, {Microsoft.Azure.Amqp, 2.6.2}", "Microsoft.Azure.Cosmos.Client, {Microsoft.Azure.Cosmos, 3.35.2}", "Microsoft.Azure.Cosmos.Core, {Microsoft.Azure.Cosmos, 3.35.2}", "Microsoft.Azure.Cosmos.Direct, {Microsoft.Azure.Cosmos, 3.35.2}", "Microsoft.Azure.Cosmos.Serialization.HybridRow, {Microsoft.Azure.Cosmos, 3.35.2}", "Microsoft.Azure.NotificationHubs, {Microsoft.Azure.NotificationHubs, 4.1.0}", "Microsoft.Bcl.AsyncInterfaces, {Microsoft.Bcl.AsyncInterfaces, 8.0.0}", "Microsoft.Bcl.HashCode, {Microsoft.Bcl.HashCode, 1.1.0}", "Microsoft.CSharp, {Microsoft.CSharp, 4.7.0}", "Microsoft.Extensions.Caching.Abstractions, {Microsoft.Extensions.Caching.Abstractions, 3.1.8}", "Microsoft.Extensions.Caching.Memory, {Microsoft.Extensions.Caching.Memory, 3.1.8}", "Microsoft.Extensions.Configuration, {Microsoft.Extensions.Configuration, 8.0.0}", "Microsoft.Extensions.Configuration.Abstractions, {Microsoft.Extensions.Configuration.Abstractions, 8.0.0}", "Microsoft.Extensions.Configuration.Binder, {Microsoft.Extensions.Configuration.Binder, 8.0.0}", "Microsoft.Extensions.Configuration.CommandLine, {Microsoft.Extensions.Configuration.CommandLine, 8.0.0}", "Microsoft.Extensions.Configuration.EnvironmentVariables, {Microsoft.Extensions.Configuration.EnvironmentVariables, 8.0.0}", "Microsoft.Extensions.Configuration.FileExtensions, {Microsoft.Extensions.Configuration.FileExtensions, 8.0.0}", "Microsoft.Extensions.Configuration.Json, {Microsoft.Extensions.Configuration.Json, 8.0.0}", "Microsoft.Extensions.Configuration.UserSecrets, {Microsoft.Extensions.Configuration.UserSecrets, 8.0.0}", "Microsoft.Extensions.DependencyInjection, {Microsoft.Extensions.DependencyInjection, 8.0.0}", "Microsoft.Extensions.DependencyInjection.Abstractions, {Microsoft.Extensions.DependencyInjection.Abstractions, 8.0.0}", "Microsoft.Extensions.Diagnostics, {Microsoft.Extensions.Diagnostics, 8.0.0}", "Microsoft.Extensions.Diagnostics.Abstractions, {Microsoft.Extensions.Diagnostics.Abstractions, 8.0.0}", "Microsoft.Extensions.FileProviders.Abstractions, {Microsoft.Extensions.FileProviders.Abstractions, 8.0.0}", "Microsoft.Extensions.FileProviders.Physical, {Microsoft.Extensions.FileProviders.Physical, 8.0.0}", "Microsoft.Extensions.FileSystemGlobbing, {Microsoft.Extensions.FileSystemGlobbing, 8.0.0}", "Microsoft.Extensions.Hosting, {Microsoft.Extensions.Hosting, 8.0.0}", "Microsoft.Extensions.Hosting.Abstractions, {Microsoft.Extensions.Hosting.Abstractions, 8.0.0}", "Microsoft.Extensions.Http, {Microsoft.Extensions.Http, 8.0.0}", "Microsoft.Extensions.Logging, {Microsoft.Extensions.Logging, 8.0.0}", "Microsoft.Extensions.Logging.Abstractions, {Microsoft.Extensions.Logging.Abstractions, 8.0.0}", "Microsoft.Extensions.Logging.Configuration, {Microsoft.Extensions.Logging.Configuration, 8.0.0}", "Microsoft.Extensions.Logging.Console, {Microsoft.Extensions.Logging.Console, 8.0.0}", "Microsoft.Extensions.Logging.Debug, {Microsoft.Extensions.Logging.Debug, 8.0.0}", "Microsoft.Extensions.Logging.EventLog, {Microsoft.Extensions.Logging.EventLog, 8.0.0}", "Microsoft.Extensions.Logging.EventSource, {Microsoft.Extensions.Logging.EventSource, 8.0.0}", "Microsoft.Extensions.ObjectPool, {Microsoft.Extensions.ObjectPool, 5.0.10}", "Microsoft.Extensions.Options, {Microsoft.Extensions.Options, 8.0.0}", "Microsoft.Extensions.Options.ConfigurationExtensions, {Microsoft.Extensions.Options.ConfigurationExtensions, 8.0.0}", "Microsoft.Extensions.Primitives, {Microsoft.Extensions.Primitives, 8.0.0}", "Microsoft.IdentityModel.Abstractions, {Microsoft.IdentityModel.Abstractions, 6.32.0}", "Microsoft.IdentityModel.JsonWebTokens, {Microsoft.IdentityModel.JsonWebTokens, 6.32.0}", "Microsoft.IdentityModel.Logging, {Microsoft.IdentityModel.Logging, 6.32.0}", "Microsoft.IdentityModel.Tokens, {Microsoft.IdentityModel.Tokens, 6.32.0}", "Microsoft.Net.Http.Headers, {Microsoft.Net.Http.Headers, 2.2.0}", "Microsoft.Rest.ClientRuntime, {Microsoft.Rest.ClientRuntime, 2.3.24}", "NaCl, {NaCl.Net, 0.1.13}", "NewRelic.Api.Agent, {NewRelic.Agent.Api, 10.13.0}", "Newtonsoft.J<PERSON>, {Newtonsoft<PERSON>J<PERSON>, 13.0.3}", "NLog, {NLog, 5.2.2}", "NLog.Extensions.Logging, {NLog.Extensions.Logging, 5.3.2}", "NLog.Targets.Loggly, {NLog.Targets.Loggly, 4.8.63}", "NVelocity, {NVelocity, 1.2.0}", "Pipelines.Sockets.Unofficial, {Pipelines.Sockets.Unofficial, 2.2.8}", "<PERSON>, {<PERSON>, 7.2.1}", "protobuf-net, {protobuf-net, 3.2.26}", "protobuf-net.Core, {protobuf-net.Core, 3.2.26}", "<PERSON><PERSON><PERSON>S<PERSON><PERSON>, {<PERSON><PERSON>erServer, 5.0.0}", "RestSharp, {RestSharp, 110.2.0}", "<PERSON><PERSON><PERSON>har<PERSON>, {<PERSON><PERSON>Shar<PERSON>, 2.88.3}", "StackExchange.Redis, {StackExchange.Redis, 2.6.122}", "<PERSON><PERSON>Buff<PERSON>, {<PERSON><PERSON>Buffers, 4.5.1}", "System.Collections.Immutable, {System.Collections.Immutable, 8.0.0}", "System.Collections.Specialized, {System.Collections.Specialized, 4.3.0}", "System.ComponentModel.Annotations, {System.ComponentModel.Annotations, 5.0.0}", "System.Configuration.ConfigurationManager, {System.Configuration.ConfigurationManager, 8.0.0}", "System.Data.DataSetExtensions, {System.Data.DataSetExtensions, 4.5.0}", "System.Data.SqlClient, {System.Data.SqlClient, 4.8.5}", "System.Diagnostics.DiagnosticSource, {System.Diagnostics.DiagnosticSource, 8.0.0}", "System.Diagnostics.EventLog, {System.Diagnostics.EventLog, 8.0.0}", "System.Diagnostics.PerformanceCounter, {System.Diagnostics.PerformanceCounter, 8.0.0}", "System.Formats.Asn1, {System.Formats.Asn1, 6.0.0}", "System.IdentityModel.Tokens.Jwt, {System.IdentityModel.Tokens.Jwt, 6.32.0}", "System.IO, {System.IO, 4.3.0}", "System.IO.Compression, {System.IO.Compression, 4.3.0}", "System.IO.Pipelines, {System.IO.Pipelines, 8.0.0}", "System.Memory, {System.Memory, 4.5.5}", "System.Memory.Data, {System.Memory.Data, 8.0.0}", "System.Net.Http, {System.Net.Http, 4.3.4}", "System.Net.Http.WinHttp<PERSON><PERSON><PERSON>, {System.Net.Http.WinHttpHandler, 8.0.0}", "System.Net.Primitives, {System.Net.Primitives, 4.3.0}", "System.Net.WebSockets, {System.Net.WebSockets, 4.3.0}", "System.Net.WebSockets.Client, {System.Net.WebSockets.Client, 4.3.2}", "System.Numerics.Vectors, {System.Numerics.Vectors, 4.5.0}", "System.Reflection.DispatchProxy, {System.Reflection.DispatchProxy, 4.7.1}", "System.Reflection.Emit, {System.Reflection.Emit, 4.7.0}", "System.Reflection.Emit.ILGeneration, {System.Reflection.Emit.ILGeneration, 4.7.0}", "System.Reflection.Emit.Lightweight, {System.Reflection.Emit.Lightweight, 4.7.0}", "System.Runtime, {System.Runtime, 4.3.1}", "System.Runtime.Caching, {System.Runtime.Caching, 8.0.0}", "System.Runtime.CompilerServices.Unsafe, {System.Runtime.CompilerServices.Unsafe, 6.0.0}", "System.Runtime.Handles, {System.Runtime.Handles, 4.3.0}", "System.Runtime.Serialization.Primitives, {System.Runtime.Serialization.Primitives, 4.3.0}", "System.Security.AccessControl, {System.Security.AccessControl, 6.0.0}", "System.Security.Cryptography.Algorithms, {System.Security.Cryptography.Algorithms, 4.3.1}", "System.Security.Cryptography.Cng, {System.Security.Cryptography.Cng, 5.0.0}", "System.Security.Cryptography.Encoding, {System.Security.Cryptography.Encoding, 4.3.0}", "System.Security.Cryptography.Pkcs, {System.Security.Cryptography.Pkcs, 6.0.1}", "System.Security.Cryptography.Primitives, {System.Security.Cryptography.Primitives, 4.3.0}", "System.Security.Cryptography.ProtectedData, {System.Security.Cryptography.ProtectedData, 8.0.0}", "System.Security.Cryptography.X509Certificates, {System.Security.Cryptography.X509Certificates, 4.3.2}", "System.Security.Cryptography.Xml, {System.Security.Cryptography.Xml, 6.0.1}", "System.Security.Permissions, {System.Security.Permissions, 8.0.0}", "System.Security.Principal.Windows, {System.Security.Principal.Windows, 5.0.0}", "System.Text.Encoding, {System.Text.Encoding, 4.3.0}", "System.Text.Encodings.Web, {System.Text.Encodings.Web, 8.0.0}", "System.Text.<PERSON>, {System.Text.J<PERSON>, 8.0.0}", "System.Text.RegularExpressions, {System.Text.RegularExpressions, 4.3.1}", "System.Threading.Channels, {System.Threading.Channels, 8.0.0}", "System.Threading.Tasks, {System.Threading.Tasks, 4.3.0}", "System.Threading.Tasks.Extensions, {System.Threading.Tasks.Extensions, 4.5.4}", "System.Xml.ReaderWriter, {System.Xml.ReaderWriter, 4.3.0}", "<PERSON><PERSON><PERSON>, {<PERSON><PERSON><PERSON>, 6.10.0}"], "sync": true, "targetFramework": "netstandard2.0", "typeReuseMode": "All"}}