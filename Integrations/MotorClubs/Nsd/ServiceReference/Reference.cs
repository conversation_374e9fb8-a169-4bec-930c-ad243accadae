//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Extric.Towbook.Integrations.MotorClubs.Nsd.NsdService
{
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ServiceModel.ServiceContractAttribute(Namespace="http://web1.nsddispatch.com/digitaldispatch/digitaldispatch.asmx", ConfigurationName="Extric.Towbook.Integrations.MotorClubs.Nsd.NsdService.digitaldispatchSoap")]
    public interface digitaldispatchSoap
    {
        
        // CODEGEN: Generating message contract since element name xmldocMessage from namespace http://web1.nsddispatch.com/digitaldispatch/digitaldispatch.asmx is not marked nillable
        [System.ServiceModel.OperationContractAttribute(Action="http://web1.nsddispatch.com/digitaldispatch/digitaldispatch.asmx/NSDDispatch", ReplyAction="*")]
        Extric.Towbook.Integrations.MotorClubs.Nsd.NsdService.NSDDispatchResponse NSDDispatch(Extric.Towbook.Integrations.MotorClubs.Nsd.NsdService.NSDDispatchRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://web1.nsddispatch.com/digitaldispatch/digitaldispatch.asmx/NSDDispatch", ReplyAction="*")]
        System.Threading.Tasks.Task<Extric.Towbook.Integrations.MotorClubs.Nsd.NsdService.NSDDispatchResponse> NSDDispatchAsync(Extric.Towbook.Integrations.MotorClubs.Nsd.NsdService.NSDDispatchRequest request);
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class NSDDispatchRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="NSDDispatch", Namespace="http://web1.nsddispatch.com/digitaldispatch/digitaldispatch.asmx", Order=0)]
        public Extric.Towbook.Integrations.MotorClubs.Nsd.NsdService.NSDDispatchRequestBody Body;
        
        public NSDDispatchRequest()
        {
        }
        
        public NSDDispatchRequest(Extric.Towbook.Integrations.MotorClubs.Nsd.NsdService.NSDDispatchRequestBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://web1.nsddispatch.com/digitaldispatch/digitaldispatch.asmx")]
    public partial class NSDDispatchRequestBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public System.Xml.XmlElement xmldocMessage;
        
        public NSDDispatchRequestBody()
        {
        }
        
        public NSDDispatchRequestBody(System.Xml.XmlElement xmldocMessage)
        {
            this.xmldocMessage = xmldocMessage;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class NSDDispatchResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Name="NSDDispatchResponse", Namespace="http://web1.nsddispatch.com/digitaldispatch/digitaldispatch.asmx", Order=0)]
        public Extric.Towbook.Integrations.MotorClubs.Nsd.NsdService.NSDDispatchResponseBody Body;
        
        public NSDDispatchResponse()
        {
        }
        
        public NSDDispatchResponse(Extric.Towbook.Integrations.MotorClubs.Nsd.NsdService.NSDDispatchResponseBody Body)
        {
            this.Body = Body;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.Runtime.Serialization.DataContractAttribute(Namespace="http://web1.nsddispatch.com/digitaldispatch/digitaldispatch.asmx")]
    public partial class NSDDispatchResponseBody
    {
        
        [System.Runtime.Serialization.DataMemberAttribute(EmitDefaultValue=false, Order=0)]
        public System.Xml.XmlElement NSDDispatchResult;
        
        public NSDDispatchResponseBody()
        {
        }
        
        public NSDDispatchResponseBody(System.Xml.XmlElement NSDDispatchResult)
        {
            this.NSDDispatchResult = NSDDispatchResult;
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    public interface digitaldispatchSoapChannel : Extric.Towbook.Integrations.MotorClubs.Nsd.NsdService.digitaldispatchSoap, System.ServiceModel.IClientChannel
    {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
    public partial class digitaldispatchSoapClient : System.ServiceModel.ClientBase<Extric.Towbook.Integrations.MotorClubs.Nsd.NsdService.digitaldispatchSoap>, Extric.Towbook.Integrations.MotorClubs.Nsd.NsdService.digitaldispatchSoap
    {
        
        /// <summary>
        /// Implement this partial method to configure the service endpoint.
        /// </summary>
        /// <param name="serviceEndpoint">The endpoint to configure</param>
        /// <param name="clientCredentials">The client credentials</param>
        static partial void ConfigureEndpoint(System.ServiceModel.Description.ServiceEndpoint serviceEndpoint, System.ServiceModel.Description.ClientCredentials clientCredentials);
        
        public digitaldispatchSoapClient(EndpointConfiguration endpointConfiguration) : 
                base(digitaldispatchSoapClient.GetBindingForEndpoint(endpointConfiguration), digitaldispatchSoapClient.GetEndpointAddress(endpointConfiguration))
        {
            this.Endpoint.Name = endpointConfiguration.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public digitaldispatchSoapClient(EndpointConfiguration endpointConfiguration, string remoteAddress) : 
                base(digitaldispatchSoapClient.GetBindingForEndpoint(endpointConfiguration), new System.ServiceModel.EndpointAddress(remoteAddress))
        {
            this.Endpoint.Name = endpointConfiguration.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public digitaldispatchSoapClient(EndpointConfiguration endpointConfiguration, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(digitaldispatchSoapClient.GetBindingForEndpoint(endpointConfiguration), remoteAddress)
        {
            this.Endpoint.Name = endpointConfiguration.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public digitaldispatchSoapClient(System.ServiceModel.Channels.Binding binding, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(binding, remoteAddress)
        {
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        Extric.Towbook.Integrations.MotorClubs.Nsd.NsdService.NSDDispatchResponse Extric.Towbook.Integrations.MotorClubs.Nsd.NsdService.digitaldispatchSoap.NSDDispatch(Extric.Towbook.Integrations.MotorClubs.Nsd.NsdService.NSDDispatchRequest request)
        {
            return base.Channel.NSDDispatch(request);
        }
        
        public System.Xml.XmlElement NSDDispatch(System.Xml.XmlElement xmldocMessage)
        {
            Extric.Towbook.Integrations.MotorClubs.Nsd.NsdService.NSDDispatchRequest inValue = new Extric.Towbook.Integrations.MotorClubs.Nsd.NsdService.NSDDispatchRequest();
            inValue.Body = new Extric.Towbook.Integrations.MotorClubs.Nsd.NsdService.NSDDispatchRequestBody();
            inValue.Body.xmldocMessage = xmldocMessage;
            Extric.Towbook.Integrations.MotorClubs.Nsd.NsdService.NSDDispatchResponse retVal = ((Extric.Towbook.Integrations.MotorClubs.Nsd.NsdService.digitaldispatchSoap)(this)).NSDDispatch(inValue);
            return retVal.Body.NSDDispatchResult;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<Extric.Towbook.Integrations.MotorClubs.Nsd.NsdService.NSDDispatchResponse> Extric.Towbook.Integrations.MotorClubs.Nsd.NsdService.digitaldispatchSoap.NSDDispatchAsync(Extric.Towbook.Integrations.MotorClubs.Nsd.NsdService.NSDDispatchRequest request)
        {
            return base.Channel.NSDDispatchAsync(request);
        }
        
        public System.Threading.Tasks.Task<Extric.Towbook.Integrations.MotorClubs.Nsd.NsdService.NSDDispatchResponse> NSDDispatchAsync(System.Xml.XmlElement xmldocMessage)
        {
            Extric.Towbook.Integrations.MotorClubs.Nsd.NsdService.NSDDispatchRequest inValue = new Extric.Towbook.Integrations.MotorClubs.Nsd.NsdService.NSDDispatchRequest();
            inValue.Body = new Extric.Towbook.Integrations.MotorClubs.Nsd.NsdService.NSDDispatchRequestBody();
            inValue.Body.xmldocMessage = xmldocMessage;
            return ((Extric.Towbook.Integrations.MotorClubs.Nsd.NsdService.digitaldispatchSoap)(this)).NSDDispatchAsync(inValue);
        }
        
        public virtual System.Threading.Tasks.Task OpenAsync()
        {
            return System.Threading.Tasks.Task.Factory.FromAsync(((System.ServiceModel.ICommunicationObject)(this)).BeginOpen(null, null), new System.Action<System.IAsyncResult>(((System.ServiceModel.ICommunicationObject)(this)).EndOpen));
        }
        
        public virtual System.Threading.Tasks.Task CloseAsync()
        {
            return System.Threading.Tasks.Task.Factory.FromAsync(((System.ServiceModel.ICommunicationObject)(this)).BeginClose(null, null), new System.Action<System.IAsyncResult>(((System.ServiceModel.ICommunicationObject)(this)).EndClose));
        }
        
        private static System.ServiceModel.Channels.Binding GetBindingForEndpoint(EndpointConfiguration endpointConfiguration)
        {
            if ((endpointConfiguration == EndpointConfiguration.digitaldispatchSoap))
            {
                System.ServiceModel.BasicHttpBinding result = new System.ServiceModel.BasicHttpBinding();
                result.MaxBufferSize = int.MaxValue;
                result.ReaderQuotas = System.Xml.XmlDictionaryReaderQuotas.Max;
                result.MaxReceivedMessageSize = int.MaxValue;
                result.AllowCookies = true;
                result.Security.Mode = System.ServiceModel.BasicHttpSecurityMode.Transport;
                return result;
            }
            if ((endpointConfiguration == EndpointConfiguration.digitaldispatchSoap12))
            {
                System.ServiceModel.Channels.CustomBinding result = new System.ServiceModel.Channels.CustomBinding();
                System.ServiceModel.Channels.TextMessageEncodingBindingElement textBindingElement = new System.ServiceModel.Channels.TextMessageEncodingBindingElement();
                textBindingElement.MessageVersion = System.ServiceModel.Channels.MessageVersion.CreateVersion(System.ServiceModel.EnvelopeVersion.Soap12, System.ServiceModel.Channels.AddressingVersion.None);
                result.Elements.Add(textBindingElement);
                System.ServiceModel.Channels.HttpsTransportBindingElement httpsBindingElement = new System.ServiceModel.Channels.HttpsTransportBindingElement();
                httpsBindingElement.AllowCookies = true;
                httpsBindingElement.MaxBufferSize = int.MaxValue;
                httpsBindingElement.MaxReceivedMessageSize = int.MaxValue;
                result.Elements.Add(httpsBindingElement);
                return result;
            }
            throw new System.InvalidOperationException(string.Format("Could not find endpoint with name \'{0}\'.", endpointConfiguration));
        }
        
        private static System.ServiceModel.EndpointAddress GetEndpointAddress(EndpointConfiguration endpointConfiguration)
        {
            if ((endpointConfiguration == EndpointConfiguration.digitaldispatchSoap))
            {
                return new System.ServiceModel.EndpointAddress("https://web1.nsddispatch.com/digitaldispatch/digitaldispatch.asmx");
            }
            if ((endpointConfiguration == EndpointConfiguration.digitaldispatchSoap12))
            {
                return new System.ServiceModel.EndpointAddress("https://web1.nsddispatch.com/digitaldispatch/digitaldispatch.asmx");
            }
            throw new System.InvalidOperationException(string.Format("Could not find endpoint with name \'{0}\'.", endpointConfiguration));
        }
        
        public enum EndpointConfiguration
        {
            
            digitaldispatchSoap,
            
            digitaldispatchSoap12,
        }
    }
}
