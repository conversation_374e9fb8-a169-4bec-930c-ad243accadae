using System;
using Newtonsoft.Json;

namespace Extric.Towbook.Integration.MotorClubs.Nsd.Model
{

    public sealed class OonPaymentModel
    {
        public string DispatchId { get; set; }
        public string Notes { get; set; }
        public PaymentDetails Payment { get; set; }

        public sealed class PaymentDetails
        {
            public decimal Amount { get; set; }
            public string Number { get; set; }
            public string ExpirationDate { get; set; }
            public string Cvc { get; set; }
            public string Zip { get; set; }
        }
    }

    public sealed class DispatchModel
    {
        public string DispatchId { get; set; }
        public string Provider { get; set; }
        public string ContractorId { get; set; }
        public string LocationId { get; set; }
        public string[] Actions { get; set; }
        public Job Job { get; set; }

        /// <summary>
        /// For use on OON jobs only.
        /// </summary>
        public Oon Oon { get; set; }

    }

    public sealed class Oon
    {
        public decimal OfferPrice { get; set; }
        public decimal CustomerPay { get; set; }
        public decimal GoaPrice { get; set; }
        public decimal CancelPrice { get; set; }
    }

    public sealed class Job
    {
        public int RequiredAcknowledgeTimeInSeconds { get; set; }
        public string JobId { get; set; }
        public DateTime Timestamp { get; set; }
        public int? MaxETA { get; set; }
        public string Type { get; set; }
        public string BreakdownReason { get; set; }
        public NacCustomer Customer { get; set; }
        public NacVehicle Vehicle { get; set; }
        public NacIncidentAddress IncidentAddress { get; set; }
        public NacIncidentAddress DestinationAddress { get; set; }
        public NacPay Pricing { get; set; }

        public string Notes { get; set; }
    }

    public sealed class NacPay
    {
        public decimal ContractedAmount { get; set; }
        public decimal CustomerPay { get; set; }
        public decimal Goa { get; set; }
    }

    public sealed class NacCustomer
    {
        public string MemberName { get; set; }
        public string CustomerName { get; set; }
        public string CustomerPhone { get; set; }
        public string CustomerPhoneType { get; set; }
    }

    public sealed class NacVehicle
    {
        public string Year { get; set; }
        public string Make { get; set; }
        public string Model { get; set; }
        public string Color { get; set; }
        public string LicensePlate { get; set; }
        public string Vin { get; set; }
        public string DriveType { get; set; }
        public string VehicleType { get; set; }
        public string Odometer { get; set; }
        public string AdditionalInformation { get; set; }
        public bool? NeutralCapable { get; set; }

        public override string ToString()
        {
            return Year + " " + Make + " " + Model + (!string.IsNullOrWhiteSpace(Color) ? " (" + Color + ")" : "");
        }
    }

    public class NacIncidentAddress
    {
        public string Address { get; set; }
        public string City { get; set; }
        public string State { get; set; }
        public string Zip { get; set; }
        public decimal? Latitude { get; set; }
        public decimal? Longitude { get; set; }
        public string Landmark { get; set; }
        public string SafeLocation { get; set; }
        public string PersonInCar { get; set; }
        public string LocationName { get; set; }
        /// <summary>
        /// Residence, Business, Parking Lot, etc
        /// </summary>
        public string LocationType { get; set; }
        public string Notes { get; set; }

        public override string ToString()
        {
            var extra = string.Empty;

            if (!string.IsNullOrWhiteSpace(LocationName))
            {
                extra = LocationName;
            }
            if (!string.IsNullOrWhiteSpace(LocationType))
            {
                if (string.IsNullOrWhiteSpace(extra))
                    extra = LocationType;
                else
                    extra += "/" + LocationType;
            }

            return (Address + (!string.IsNullOrWhiteSpace(Address) ? ", " : "") +
                City + " " +
                State + " " +
                Zip + " " +
                (!string.IsNullOrWhiteSpace(extra) ? " (" + extra + ")" : "")).Trim();
        }
    }


}
