
create or alter view [MCDispatch].[vwRoadsideProtectGpsBreadcrumbExport] as    
select distinct D.DispatchEntryId, AGS.ContractorId,  C.CompanyId, 
 C.Name as CompanyName, D.DriverId as ExternalDriverId, D.Status,  
  <PERSON><PERSON>PurchaseOrderNumber as P<PERSON>, Drv.Name as DriverName, c.Phone, ULC.Latitude, ULC.Longitude, ULC.Timestamp   
   from DispatchEntryRequests DR    
 INNER JOIN DispatchEntries D with (nolock) on D.DispatchEntryId=DR.DispatchEntryId    
 INNER JOIN MCDispatch.RoadsideProtectDispatches ISC with (nolock) on ISC.CallRequestId=DR.CallRequestId    
 inner join MCDispatch.RoadsideProtectContractors AGS with (nolock) on AGS.RoadsideProtectContractorId=ISC.RoadsideProtectContractorId 
 and AGS.MasterAccountId=24
 inner JOIN Drivers DRV with (nolock) on DRV.DriverId=D.DriverId    
 inner JOIN UserLocationsCurrent ULC WITH (nolock) on ULC.UserId=DRV.UserId    
 inner join companies C with (nolock) on C.CompanyId=D.CompanyId    
 where-- DR.CreateDate > DATEADD(hour, -4, getdate()) and 
 DR.status=1 and     
  D.DispatchEntryId > *********    
 and ulc.Timestamp > dateadd(second, -60, getdate())    

 GO
