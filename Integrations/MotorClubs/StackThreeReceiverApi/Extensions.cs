using Extric.Towbook.Accounts;
using Extric.Towbook.Integration.MotorClubs.Services;
using NLog;
using System.Threading.Tasks;
using Extric.Towbook.Web;
using Extric.Towbook.Utility;
using Extric.Towbook.Integrations.MotorClubs.StackThree;
using System.Net.Http;
using Microsoft.AspNetCore.Mvc;
using Extric.Towbook.WebShared;

namespace Extric.Towbook.API.Integration.MotorClubs.StackThree
{
    public static class GatewayExtensions
    {   
        public static LogEventInfo NewLogEvent(this ControllerBase t, string message, string json = null)
        {
            var log = new LogEventInfo();

            log.Level = LogLevel.Info;
            log.Message = message;

            if (json != null)
                log.Properties["json"] = json;

            log.Properties["commitId"] = Core.GetCommitId();
            log.Properties["masterAccountName"] = "StackThree";
            log.Properties["environment"] =  t.GetEnvironment();
            log.Properties["requestingIp"] = t.GetRequestingIp();

            return log;
        }
        public static string GetSystem(this ControllerBase t)
        {
            var req = HttpContext.Current;

            if (req != null)
            {
                return (string)req.Items["stackthree:system"]?.ToString().ToUpperInvariant();
            }

            return null;
        }


        public static string GetEnvironment(this ControllerBase t)
        {
            if (t.User?.Identity?.Name != null)
                return t.User.Identity.Name;

            var req = HttpContext.Current;

            if (req != null)
            {
                return (string)req.Items["stackthree:environment"]?.ToString().ToUpperInvariant();
            }

            return "TEST";
        }

        public static int GetEnvironmentId(this ControllerBase t)
        {
            var env = t.GetEnvironment()?.ToUpperInvariant();

            if (env == "DEV")
                return 1;
            if (env == "TEST")
                return 2;
            else if (env == "PROD")
                return 3;

            return 1;
        }

        public static string GetRequestingIp(this ControllerBase t) => WebGlobal.GetRequestingIp();

        public static HttpResponseMessage ApiFailure(this ControllerBase t, string message)
        {
            return new HttpResponseMessage()
            {
                StatusCode = System.Net.HttpStatusCode.BadRequest,
                Content = new StringContent(
                    new
                    {
                        message = message
                    }.ToJson(),
                    System.Text.Encoding.UTF8,
                     "application/json")
            };
        }

        public static StackThreeContractor GetContractorById(this ControllerBase t, string contractorId)
        {
            var stsystem = GetSystem(t);
            int masterAccountId = 0;
            if (stsystem == "ATLAS")
                masterAccountId = MasterAccountTypes.StackThreeAtlas;
            else if (stsystem == "DRIVEN_SOLUTIONS")
                masterAccountId = MasterAccountTypes.DrivenSolutions;



            var ac = StackThreeContractor.GetByContractorId(
                contractorId, masterAccountId, GetEnvironmentId(t));
            
            if (ac == null)
            {
                ac = StackThreeContractor.GetByContractorId(
                    contractorId, MasterAccountTypes.OonDrivenSolutions, GetEnvironmentId(t));

                // throw new http exception 
            }

            return ac;
        }

        public static async Task<long> SendToBackendService(this ControllerBase t,
            DigitalDispatchService.CallEventType type,
            StackThreeContractor contractor,
            string json,
            string dispatchId)
        {
            var env = GetEnvironment(t);
            var testMode = env == "TEST" || env == "DEV";

            var properties = new System.Collections.Generic.Dictionary<string, object>();
            properties["stackThreeContractorId"] = contractor?.StackThreeContractorId;
           
            var system = GetSystem(t);

            if (system == "ATLAS")
                properties["masterAccountId"] = MasterAccountTypes.StackThreeAtlas;
            else if (system == "DRIVEN_SOLUTIONS")
                properties["masterAccountId"] = MasterAccountTypes.DrivenSolutions;

            if (type == DigitalDispatchService.CallEventType.OonOffered && system == "DRIVEN_SOLUTIONS")
                properties["masterAccountId"] = MasterAccountTypes.OonDrivenSolutions;

            if (contractor?.MasterAccountId == MasterAccountTypes.OonDrivenSolutions)
                properties["masterAccountId"] = MasterAccountTypes.OonDrivenSolutions;

            if (testMode)
                properties["TestMode"] = true;

            properties["environment"] = env;
            var msg = new StackThreeMessage();

            msg.StackThreeContractorId = contractor?.StackThreeContractorId ?? 0;
            msg.JsonData = json;
            msg.DispatchId = dispatchId;
            msg.MessageType = (int)type;
            msg.Save();

            return await DigitalDispatchService.HandleCallEventAsync(
                contractor?.CompanyId ?? 0,
                contractor?.AccountId ?? 0,
                msg.ToJson(),
                type,
                properties: properties,
                logPrefix: "StackThree-" + GetEnvironment(t));
        }
    }
}
