using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;
using System.Threading.Tasks;

namespace Extric.Towbook.API.Integration.MotorClubs.AaaAce.HttpModules
{
    public class StackThreeAuthenticationMiddleware
    {
        private readonly RequestDelegate _next;

        public StackThreeAuthenticationMiddleware(RequestDelegate next)
        {
            _next = next;
        }

        public async Task InvokeAsync(HttpContext context, IWebHostEnvironment environment)
        {
            bool invalidToken = true;

            string path = context.Request.Path.ToString().ToLowerInvariant().TrimStart('/');

            if (!path.Contains("receivers/stackthree/"))
            {
                await _next(context);
                return;
            }

            if (context.Request.Headers["Authorization"].Count > 0)
            {
                string authHeader = context.Request.Headers["Authorization"];
                string key = string.Empty;
                if (authHeader.Contains(" ") ||
                    authHeader.Contains(":"))
                {
                    authHeader = authHeader.Replace(":", " ");

                    var splitHeader = authHeader.Split(' ');

                    if (splitHeader[0].ToLowerInvariant() == "bearer")
                    {
                        key = splitHeader[1];
                    }
                }
                else
                {
                    key = authHeader;
                }

                // test
                if (key == "c2d3d92e02db44c9b00edaf505427057")
                {
                    context.Items["stackthree:environment"] = "TEST";
                    context.Items["stackthree:system"] = "ATLAS";
                    invalidToken = false;
                }
                if (key == "6b2j7aNxKYoOUvT6X6HUlBaM5bhC7FO3Z0/Xoqiyyno=")
                {
                    context.Items["stackthree:environment"] = "TEST";
                    context.Items["stackthree:system"] = "DRIVEN_SOLUTIONS";
                    invalidToken = false;
                }

                // production key
                if (key == "uYIVwgsioI6Ly23M4JT61A==")
                {
                    context.Items["stackthree:environment"] = "PROD";
                    context.Items["stackthree:system"] = "DRIVEN_SOLUTIONS";
                    invalidToken = false;
                }

                if (key == "Yn0hVwzqEkiz7pAAPNyvNQ==")
                {
                    context.Items["stackthree:environment"] = "PROD";
                    context.Items["stackthree:system"] = "ATLAS";
                    invalidToken = false;
                }

                if (!invalidToken)
                {
                    context.User = new System.Security.Principal.GenericPrincipal(
                        new System.Security.Principal.GenericIdentity(context.Items["stackthree:environment"].ToString()), new[] { "api" });
                }
            }

            if (invalidToken)
            {
                await WriteInvalidTokenAsync(context, false);
                return;
            }

            // Call the next delegate/middleware in the pipeline
            await _next(context);
        }


        public static async Task WriteInvalidTokenAsync(HttpContext context, bool missing = false)
        {
            context.Response.Clear();
            context.Response.Headers.Clear();
            context.Response.StatusCode = StatusCodes.Status401Unauthorized;
            string statusDescription;

            if (missing)
                statusDescription = "Bearer Token is required.";
            else
                statusDescription = "Invalid Token. Please ensure your token is valid.";

            context.Response.ContentType = "text/json";

            await context.Response.WriteAsync(JsonConvert.SerializeObject(
                new
                {
                    error = statusDescription
                }));

            return;
        }
    }

    public static class StackThreeAuthenticationMiddlewareExtensions
    {
        public static IApplicationBuilder UseStackThreeAuthentication(
            this IApplicationBuilder builder)
        {
            return builder.UseWhen(context => context.Request.Path.StartsWithSegments(new PathString("/receivers/stackthree")), branch =>
            {
                branch.UseMiddleware<StackThreeAuthenticationMiddleware>();
            });
        }
    }
}
