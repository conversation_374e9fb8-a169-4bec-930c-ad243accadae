using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;
using System.Threading.Tasks;

namespace Extric.Towbook.API.Integration.MotorClubs.Nsd.HttpModules
{
    public class NsdAuthenticationMiddleware
    {
        private readonly RequestDelegate _next;

        public NsdAuthenticationMiddleware(RequestDelegate next)
        {
            _next = next;
        }

        public async Task InvokeAsync(HttpContext context, IWebHostEnvironment environment)
        {
            bool invalidToken = true;

            string path = context.Request.Path.ToString().ToLowerInvariant().TrimStart('/');

            if (!path.Contains("receivers/nsd/"))
            {
                await _next(context);
                return;
            }

            if (context.Request.Headers["Authorization"].Count > 0)
            {
                string authHeader = context.Request.Headers["Authorization"];
                string key = string.Empty;
                if (authHeader.Contains(" ") ||
                    authHeader.Contains(":"))
                {
                    authHeader = authHeader.Replace(":", " ");

                    var splitHeader = authHeader.Split(' ');

                    if (splitHeader[0].ToLowerInvariant() == "bearer")
                    {
                        key = splitHeader[1];
                    }
                }
                else
                {
                    key = authHeader;
                }

                // test
                if (key == "BWOCCyuKN0qpJg/rcpqT6Q==")
                {
                    context.Items["nsd:environment"] = "DEV";
                    invalidToken = false;
                }
                if (key == "lpvz+dL8u0KQDSt3xu0pPw==")
                {
                    context.Items["nsd:environment"] = "TEST";
                    invalidToken = false;
                }

                // production key
                if (key == "njtuuglH8k2ZvtPIS42Z3A==")
                {
                    context.Items["nsd:environment"] = "PROD";
                    invalidToken = false;
                }

                if (!invalidToken)
                {
                    context.User = new System.Security.Principal.GenericPrincipal(
                        new System.Security.Principal.GenericIdentity(context.Items["nsd:environment"].ToString()), new[] { "api" });
                }
            }

            if (invalidToken)
            {
                await WriteInvalidTokenAsync(context, false);
                return;
            }

            // Call the next delegate/middleware in the pipeline
            await _next(context);
        }


        public static async Task WriteInvalidTokenAsync(HttpContext context, bool missing = false)
        {
            context.Response.Clear();
            context.Response.Headers.Clear();
            context.Response.StatusCode = StatusCodes.Status401Unauthorized;
            string statusDescription;

            if (missing)
                statusDescription = "Bearer Token is required.";
            else
                statusDescription = "Invalid Token. Please ensure your token is valid.";

            context.Response.ContentType = "text/json";

            await context.Response.WriteAsync(JsonConvert.SerializeObject(
                new
                {
                    error = statusDescription
                }));

            return;
        }
    }

    public static class NsdAuthenticationMiddlewareExtensions
    {
        public static IApplicationBuilder UseNsdAuthentication(
            this IApplicationBuilder builder)
        {
            return builder.UseWhen(context => context.Request.Path.StartsWithSegments(new PathString("/receivers/nsd")), branch =>
            {
                branch.UseMiddleware<NsdAuthenticationMiddleware>();
            });
        }
    }
}
