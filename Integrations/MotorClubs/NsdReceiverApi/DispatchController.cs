using NLog;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Extric.Towbook.Integration.MotorClubs.Services;
using System;
using Newtonsoft.Json;
using Extric.Towbook.WebShared;
using Extric.Towbook.Integration.MotorClubs.Nsd.Model;

namespace Extric.Towbook.API.Integration.MotorClubs.Nsd
{
    [Route("receivers/nsd/dispatch")]
    public class DispatchController : ControllerBase
    {
        private static readonly Logger logger = LogManager.GetCurrentClassLogger();

        // POST /dispatch 
        [Route("")]
        [HttpPost]
        public async Task<HttpResponseMessage> Post()
        {
            var rawJson = await Request.Body.ReadAsStringAsync();

            var log = this.NewLogEvent("DispatchPost", rawJson);

            if (this.Request.Headers.HasAuthorization())
                log.Properties.Add("Authorization", this.Request.Headers.AuthorizationParameter());

            try
            {
                var dispatch = JsonConvert.DeserializeObject<DispatchModel>(rawJson);

                var contractor = this.GetContractorById((string)dispatch.ContractorId);

                if (contractor == null)
                    return this.ApiFailure("Unknown ContractorId");

                var type = DigitalDispatchService.CallEventType.Received;

                if (dispatch?.Oon != null && dispatch.Oon.OfferPrice > 0)
                {
                    type = DigitalDispatchService.CallEventType.OonOffered;
                }

                var queueItemId = await this.SendToBackendService(
                    type,
                    contractor,
                    rawJson,
                    (string)dispatch.DispatchId);

                log.Properties["queueItemId"] = queueItemId;

                var rm = new HttpResponseMessage(HttpStatusCode.Accepted);
                rm.Headers.Add("X-Twbk-Id", queueItemId.ToString());

                return rm;
            }
            catch (Exception ex)
            {
                log.Properties["exception"] = ex;
                log.Level = LogLevel.Error;

                throw;
            }
            finally
            {
                logger.Log(log);
            }
        }

        [Route("")]
        [HttpPut]
        public async Task<object> Put()
        {
            var rawJson = await Request.Body.ReadAsStringAsync();

            var log = this.NewLogEvent("DispatchPut", rawJson);

            if (this.Request.Headers.HasAuthorization())
                log.Properties.Add("Authorization", this.Request.Headers.AuthorizationParameter());

            try
            {
                var dispatch = JsonConvert.DeserializeObject<dynamic>(rawJson);

                var contractor = this.GetContractorById((string)dispatch.ContractorId);

                if (contractor == null)
                    return this.ApiFailure("Unknown ContractorId");

                log.Properties["queueItemId"] = await this.SendToBackendService(DigitalDispatchService.CallEventType.Update,
                    contractor,
                    rawJson,
                    (string)dispatch.DispatchId);
            }
            finally
            {
                logger.Log(log);
            }

            return Accepted();
        }
    }
}
