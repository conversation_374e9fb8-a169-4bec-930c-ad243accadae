using System;

namespace Extric.Towbook.API.Integration.Square.Exceptions
{
    public class SquareObjectStatusAlreadyRegisteredException : Exception
    {
        public string ObjectId { get; }
        public string Status { get; }
        public string Type { get; }

        public SquareObjectStatusAlreadyRegisteredException() { }
        
        public SquareObjectStatusAlreadyRegisteredException(string objectId, string status, string type) : base($"The {type} with Id={objectId} with status={status} is already registered.")
        {
            ObjectId = objectId;
            Status = status;
            Type = type;
        }
    }
}