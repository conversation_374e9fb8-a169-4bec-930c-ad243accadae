using Extric.Towbook.Utility;
using Square.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Extric.Towbook.API.Integration.Square.Exceptions
{
    /** THIS FILE IS KEPT TO HELP WITH MERGE CONFLICTS. THE ACTUAL CLASS IS IN THE Extric.Towbook.Square project **/
    
    // public class CustomerApiException : Exception
    // {
    //     public int CompanyId { get; set; }
    //     public List<Error> Errors { get; set; }
    //     public string JsonMessage => new { Errors }.ToJson();
    //
    //     public CustomerApiException(int companyId, string message, List<Error> errors) : base(HandleExceptionMessage(message, errors))
    //     {
    //         CompanyId = companyId;
    //         Errors = errors;
    //     }
    //
    //     private static string HandleExceptionMessage(string message, List<Error> Errors)
    //     {
    //         var ret = message ?? "An unexpected error occurred.";
    //
    //         if (Errors != null)
    //             ret = Errors.Aggregate("", (current, error) => error.Detail + (string.IsNullOrEmpty(error.Field) ? "" : $" [{error.Field}]") + "\n");
    //
    //         return ret;
    //     }
    // }
    //
    //
}
