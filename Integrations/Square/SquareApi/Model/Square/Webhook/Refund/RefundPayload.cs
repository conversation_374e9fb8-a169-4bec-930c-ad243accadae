using System;
using System.Collections.Generic;
using Newtonsoft.Json;
using Square.Models;

namespace Extric.Towbook.API.Integration.Square.Model.Square.Webhook.Refund
{
    public class RefundPayload
    {
        public string Id { get; set; }
        
        [JsonProperty("payment_id")]
        public string PaymentId { get; set; }
        
        [JsonProperty("order_id")]
        public string OrderId { get; set; }
        
        [JsonProperty("created_at")]
        public DateTime CreatedAt { get; set; }

        [JsonProperty("version")]
        public string Version { get; set; }
        
        [JsonProperty("updated_at")]
        public string UpdatedAt { get; set; }

        [JsonProperty("destination_type")]
        public string DestinationType { get; set; }
        
        [JsonProperty("amount_money")]
        public Money AmountMoney { get; set; }
        
        [JsonProperty("reason")]
        public string Reason { get; set; }

        public string Status { get; set; }
        [JsonProperty("processing_fee")]
        
        public List<ProcessingFee> ProcessingFee { get; set; }
        
        [JsonProperty("location_id")]
        public string LocationId { get; set; }
    }
}