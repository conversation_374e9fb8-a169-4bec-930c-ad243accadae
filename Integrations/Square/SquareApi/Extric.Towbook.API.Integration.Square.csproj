<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <OutputType>Library</OutputType>
    <RootNamespace>Extric.Towbook.API.Integration.SquareApi</RootNamespace>
    <AssemblyName>Extric.Towbook.API.Integration.Square</AssemblyName>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Microsoft.CSharp" Version="4.7.0" />
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.21.0" />
    <PackageReference Include="NLog" Version="5.3.3" />
    <PackageReference Include="Square" Version="38.0.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.7.1" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\..\Extric.Towbook.WebShared.Net5\Extric.Towbook.WebShared.Net5.csproj" />
    <ProjectReference Include="..\..\MotorClubs\Quest\Quest.csproj" />
    <ProjectReference Include="..\Square\Extric.Towbook.Square.csproj" />
  </ItemGroup>
</Project>