using System.Linq;
using Extric.Towbook.Utility;
using Extric.Towbook.WebShared;
using Extric.Towbook.Integration;
using System.Threading.Tasks;
using Extric.Towbook.API.Integration.Square.Model;
using System;
using System.Net.Http;
using System.Net;
using Extric.Towbook.Management;
using Microsoft.AspNetCore.Mvc;

namespace Extric.Towbook.API.Integration.Square
{
    [Route("integration/square/settings")]
    public class SettingsController : ControllerBase
    {
        [HttpGet, Route("")]
        public async Task<SquareSettingModel> Get([FromQuery] int? accountId = null)
        {
            if (WebGlobal.CurrentUser.Type != Towbook.User.TypeEnum.Manager)
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            Accounts.Account account = null;
            var company = WebGlobal.CurrentUser.Company;

            if (accountId > 1) 
            {
                account = await Accounts.Account.GetByIdAsync(accountId.Value);
                
                if (account == null || !WebGlobal.CurrentUser.HasAccessToCompany(account.CompanyId))
                    throw new TowbookException("You don't have access to the account specified.");

                company = await Company.Company.GetByIdAsync(account.CompanyId);
            }

            var resp = await SquareUtils.ValidateAuthorization(company.Id);
            if (!resp.Successful)
                throw new TowbookException("No Square connection found. You need to connect your square account with Towbook.");

            var authorization = await SquareUtils.GetAuthorizationAsync(company.Id);
            if (authorization == null)
                throw new TowbookException("No Square authorization found. You need to re-connect your square account with Towbook.");

            // force FF on for authenticated user, if not already enabled.
            if (!WebGlobal.CurrentUser.Company.HasFeature(Generated.Features.PaymentIntegrations_Square))
            {
                var cc = Management.CompanyContract.GetExistingOrCreateNew(
                    company.Id, WebGlobal.CurrentUser.Id);

                var ccnew = new Management.CompanyContractFeature()
                {
                    FeatureId = (int)Generated.Features.PaymentIntegrations_Square,
                    CompanyContractId = cc.Id
                };

                await ccnew.Save(company.Id, WebGlobal.CurrentUser.Id);
            }


            var model = new SquareSettingModel()
            {
                CompanyId = company.Id,
                AccountId = account?.Id,
                Environment = SquareUtils.GetEnv(company.Id),
                Authorization = new SquareCompanyAuthorizationModel()
                {
                    Id = authorization.Id,
                    IsValid = authorization.Valid,
                    MerchantId = authorization.MerchantId,
                    LocationId = authorization.Location?.LocationId,
                    CreateDate = authorization.CreateDate
                },
                Locations = resp.Locations
                    .Where(l =>
                        l.Status != null && l.Status.ToUpper() == "ACTIVE"
                        && l.Currency == WebGlobal.CurrentUser.Company.CurrencyCode
                        && l.Capabilities != null && l.Capabilities.Contains("CREDIT_CARD_PROCESSING"))
                    .Select(s => 
                        new SquareCompanyLocationModel { 
                            Name = s.Name, 
                            LocationId = s.LocationId 
                        }).ToArray()
            };

            model = SquareUtils.FillKeyOptionValues(model, company, account);

            return model;
        }


        [HttpPost, Route("")]
        public async Task<HttpResponseMessage> Post(SquareSettingModel model)
        {
            if (WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.Manager)
                throw new TowbookException("Your user account doesn't have access to perform this action.");

            var authorization = await SquareUtils.GetAuthorizationAsync(WebGlobal.CurrentUser.CompanyId);
            if (authorization == null)
                throw new TowbookException("No Square authorization found.  You need to re-connect your square account with Towbook.");

            var locations = await SquareUtils.GetLocations(WebGlobal.CurrentUser.CompanyId);

            if(string.IsNullOrEmpty(model.LocationId) || !locations.Select(s => s.Id).Contains(model.LocationId))
                throw new TowbookException($"The square location provided is invalid or you don't have access to it.");

            var ckv = CompanyKeyValue.GetByCompanyId(WebGlobal.CurrentUser.CompanyId, Provider.Towbook.ProviderId, "Square_AlwaysIncludePaymentLinkOnInvoices").FirstOrDefault() ??
                    new CompanyKeyValue(WebGlobal.CurrentUser.CompanyId, CompanyKey.GetByProviderId(Provider.Towbook.ProviderId, "Square_AlwaysIncludePaymentLinkOnInvoices").Id, "0");
            var oldValue = ckv.Value;
            var newValue = model.Options.IncludePaymentLinkOnInvoices ? "1" : "0";
            if (oldValue != newValue)
            {
                if (model.Options.IncludePaymentLinkOnInvoices)
                {
                    ckv.Value = "1";
                    ckv.Save();
                }
                else
                {
                    ckv.Delete();
                }
            }

            ckv = CompanyKeyValue.GetByCompanyId(WebGlobal.CurrentUser.CompanyId, Provider.Towbook.ProviderId, "Square_AlwaysIncludePaymentLinkOnStatements").FirstOrDefault() ??
                new CompanyKeyValue(WebGlobal.CurrentUser.CompanyId, CompanyKey.GetByProviderId(Provider.Towbook.ProviderId, "Square_AlwaysIncludePaymentLinkOnStatements").Id, "0");
            oldValue = ckv.Value;
            newValue = model.Options.IncludePaymentLinkOnStatements ? "1" : "0";
            if (oldValue != newValue)
            {
                if (model.Options.IncludePaymentLinkOnStatements)
                {
                    ckv.Value = "1";
                    ckv.Save();
                }
                else
                {
                    ckv.Delete();
                }
            }

            ckv = CompanyKeyValue.GetByCompanyId(WebGlobal.CurrentUser.CompanyId, Provider.Towbook.ProviderId, "Square_OptOutOfEmailsOnTransactions").FirstOrDefault() ??
                new CompanyKeyValue(WebGlobal.CurrentUser.CompanyId, CompanyKey.GetByProviderId(Provider.Towbook.ProviderId, "Square_OptOutOfEmailsOnTransactions").Id, "0");
            if (model.Options.OptOutOfConfirmationEmailOnTransactions)
            {
                ckv.Value = "1";
                ckv.Save();
            }
            else
                ckv.Delete();

            ckv = CompanyKeyValue.GetByCompanyId(WebGlobal.CurrentUser.CompanyId, Provider.Towbook.ProviderId, "Square_ExcludeLinkOnPrintedDispatchInvoices").FirstOrDefault() ??
                new CompanyKeyValue(WebGlobal.CurrentUser.CompanyId, CompanyKey.GetByProviderId(Provider.Towbook.ProviderId, "Square_ExcludeLinkOnPrintedDispatchInvoices").Id, "0");
            if (model.Options.ExcludePaymentLinkOnPrintedDispatchInvoices)
            {
                ckv.Value = "1";
                ckv.Save();
            }
            else
                ckv.Delete();

            ckv = CompanyKeyValue.GetByCompanyId(WebGlobal.CurrentUser.CompanyId, Provider.Towbook.ProviderId, "Square_ExcludeLinkOnPrintedImpoundInvoices").FirstOrDefault() ??
                new CompanyKeyValue(WebGlobal.CurrentUser.CompanyId, CompanyKey.GetByProviderId(Provider.Towbook.ProviderId, "Square_ExcludeLinkOnPrintedImpoundInvoices").Id, "0");
            if (model.Options.ExcludePaymentLinkOnPrintedImpoundInvoices)
            {
                ckv.Value = "1";
                ckv.Save();
            }
            else
                ckv.Delete();

            ckv = CompanyKeyValue.GetByCompanyId(WebGlobal.CurrentUser.CompanyId, Provider.Towbook.ProviderId, "Square_ExcludeLinkOnPrintedStatements").FirstOrDefault() ??
                new CompanyKeyValue(WebGlobal.CurrentUser.CompanyId, CompanyKey.GetByProviderId(Provider.Towbook.ProviderId, "Square_ExcludeLinkOnPrintedStatements").Id, "0");
            if (model.Options.ExcludePaymentLinkOnPrintedStatements)
            {
                ckv.Value = "1";
                ckv.Save();
            }
            else
                ckv.Delete();

            #region Tipping
            if (WebGlobal.CurrentUser.Company.HasAccessToBaseFeature("tipping"))
            {
                if (model.IsTippingEnabled)
                {
                    ckv = CompanyKeyValue.GetByCompanyId(WebGlobal.CurrentUser.CompanyId, Provider.Towbook.ProviderId, "Square_EnableTipping").FirstOrDefault() ??
                            new CompanyKeyValue(WebGlobal.CurrentUser.CompanyId, CompanyKey.GetByProviderId(Provider.Towbook.ProviderId, "Square_EnableTipping").Id, "0");

                    if (ckv.Value != "1")
                    {
                        ckv.Value = "1";
                        ckv.Save();
                    }

                    // turn on Editable commissions if tipping is enabled
                    await FeatureExtensions.ActivateCompanyContractFeature((int)Generated.Features.EditableCommissions, WebGlobal.CurrentUser.Company);

                    ckv = CompanyKeyValue.GetByCompanyId(WebGlobal.CurrentUser.CompanyId, Provider.Towbook.ProviderId, "SquareTipping_ExcludeOnPaymentLinks").FirstOrDefault() ??
                            new CompanyKeyValue(WebGlobal.CurrentUser.CompanyId, CompanyKey.GetByProviderId(Provider.Towbook.ProviderId, "SquareTipping_ExcludeOnPaymentLinks").Id, "0");
                    oldValue = ckv.Value;
                    newValue = model.TippingOptions?.ExcludeOnPaymentLinks == true ? "1" : "0";
                    if (oldValue != newValue)
                    {
                        if (model.TippingOptions?.ExcludeOnPaymentLinks == true)
                        {
                            ckv.Value = "1";
                            ckv.Save();
                        }
                        else
                        {
                            ckv.Delete();
                        }
                    }

                    ckv = CompanyKeyValue.GetByCompanyId(WebGlobal.CurrentUser.CompanyId, Provider.Towbook.ProviderId, "SquareTipping_ExcludeOnSquareReader").FirstOrDefault() ??
                            new CompanyKeyValue(WebGlobal.CurrentUser.CompanyId, CompanyKey.GetByProviderId(Provider.Towbook.ProviderId, "SquareTipping_ExcludeOnSquareReader").Id, "0");
                    oldValue = ckv.Value;
                    newValue = model.TippingOptions?.ExcludeOnSquareReader == true ? "1" : "0";
                    if (oldValue != newValue)
                    {
                        if (model.TippingOptions?.ExcludeOnSquareReader == true)
                        {
                            ckv.Value = "1";
                            ckv.Save();
                        }
                        else
                        {
                            ckv.Delete();
                        }
                    }

                    ckv = CompanyKeyValue.GetByCompanyId(WebGlobal.CurrentUser.CompanyId, Provider.Towbook.ProviderId, "SquareTipping_ExcludeOnSquareTerminal").FirstOrDefault() ??
                            new CompanyKeyValue(WebGlobal.CurrentUser.CompanyId, CompanyKey.GetByProviderId(Provider.Towbook.ProviderId, "SquareTipping_ExcludeOnSquareTerminal").Id, "0");
                    oldValue = ckv.Value;
                    newValue = model.TippingOptions?.ExcludeOnSquareTerminal == true ? "1" : "0";
                    if (oldValue != newValue)
                    {
                        if (model.TippingOptions?.ExcludeOnSquareTerminal == true)
                        {
                            ckv.Value = "1";
                            ckv.Save();
                        }
                        else
                        {
                            ckv.Delete();
                        }
                    }


                    // Default tip percentages
                    // 3 values, between 0 and 100, distinct
                    if (model.TippingOptions?.TipPercentages != null &&
                            model.TippingOptions.TipPercentages.Distinct().Count() == 3 &&
                            model.TippingOptions.TipPercentages.All(a => a > 0 && a < 100))
                    {
                        ckv = CompanyKeyValue.GetByCompanyId(WebGlobal.CurrentUser.CompanyId, Provider.Towbook.ProviderId, "SquareTipping_DefaultPercentageAmounts").FirstOrDefault();

                        oldValue = ckv?.Value ?? SquareUtils.DefaultTipPercentages.ToJson();
                        newValue = model.TippingOptions.TipPercentages.OrderBy(o => o).ToJson();

                        if (oldValue != newValue)
                        {
                            if (ckv == null)
                                ckv = new CompanyKeyValue(WebGlobal.CurrentUser.CompanyId, CompanyKey.GetByProviderId(Provider.Towbook.ProviderId, "SquareTipping_DefaultPercentageAmounts").Id, newValue);
                            else
                                ckv.Value = newValue;

                            ckv.Save();
                        }
                        else if (ckv?.Id > 0 && newValue == SquareUtils.DefaultTipPercentages.ToJson())
                            ckv.Delete();
                    }
                }
                else if (model.IsTippingEnabled == false)
                {
                    ckv = CompanyKeyValue.GetByCompanyId(WebGlobal.CurrentUser.CompanyId, Provider.Towbook.ProviderId, "Square_EnableTipping").FirstOrDefault();
                    if (ckv?.Value != null)
                    {
                        ckv.Delete();
                    }
                }
            }

            #endregion
            try
            {
                SquareUtils.SaveCompanyLocation(WebGlobal.CurrentUser.CompanyId, authorization.Id, model.LocationId);
            }
            catch(Exception ee)
            {
                var rep = new SquareController().HandleLocalException(ee, "Company Location");
                
                return new HttpResponseMessage(HttpStatusCode.InternalServerError);
            }

            return new HttpResponseMessage(HttpStatusCode.OK);
        }
    }
}
