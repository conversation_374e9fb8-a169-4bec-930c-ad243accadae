using System.IO;
using Extric.Towbook.Configuration;
using Extric.Towbook;
using Microsoft.Extensions.Configuration;

namespace SquareApiTest;

internal class Configuration
{
    public static void Init() {
        IConfiguration configuration = new ConfigurationBuilder()
            .SetBasePath(Directory.GetCurrentDirectory())
            .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
            .Build();
        IConfig coreConfiguration = new CoreConfiguration(configuration);
        new Core(coreConfiguration);
    }
}
