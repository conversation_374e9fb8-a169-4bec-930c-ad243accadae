using Extric.Towbook.WebShared;
using Extric.Towbook.WebWrapper;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Server.Kestrel.Core;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Newtonsoft.Json.Serialization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Extric.Towbook.API.Integration.Intercom
{
    public class Startup
    {
        public Startup(IConfiguration configuration)
        {
            Configuration = configuration;
        }

        public IConfiguration Configuration { get; }

        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {
            //init the items request context
            services.AddScoped<IRequestContext, NullSafeConcurrentDictionary>();

            services.AddControllers(options =>
            {
                options.AllowEmptyInputInBodyModelBinding = true;
                
            }).AddJsonOptions(options => {
            }).AddNewtonsoftJson(options =>
            {
                options.SerializerSettings.ContractResolver = new CamelCasePropertyNamesContractResolver();
            });

            services.AddHttpContextAccessor();

            // TODO: [CHECK] Set up AllowSynchronousIO to True for HttpResponseException with JsonMediaTypeFormatter (Kestrel and IIS Server)
            services.Configure<KestrelServerOptions>(options =>
            {
                options.AllowSynchronousIO = true;
            });
            services.Configure<IISServerOptions>(options =>
            {
                options.AllowSynchronousIO = true;
            });
            //services.AddMvc().AddControllersAsServices();

        }

        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(IApplicationBuilder app, IWebHostEnvironment env, IHostApplicationLifetime appLifetime)
        {
            //HttpConfiguration config = new HttpConfiguration();

            //config.Formatters.XmlFormatter.UseXmlSerializer = true;
            //config.Formatters.Remove(config.Formatters.JsonFormatter);

            app.UseRouting();

            app.UseEndpoints(endpoints =>
            {
                endpoints.MapControllers();
            });

            appLifetime.ApplicationStopped.Register(async() =>
            {
                await Towbook.Integration.MotorClubs.Services.ServiceBusHelper.Cleanup();
                await EventNotifications.ENServiceBusHelper.Cleanup();
            });

            //setup static accessor 
            IHttpContextAccessor httpContextAccessor = app.ApplicationServices.GetRequiredService<IHttpContextAccessor>();
            IWebHostEnvironment webHostEnvironment = app.ApplicationServices.GetRequiredService<IWebHostEnvironment>();

            Web.HttpContext.Configure(httpContextAccessor, webHostEnvironment, app.ApplicationServices);

            WebApiApplication.Application_Start();

            Web.HttpContextFactory.Instance = new HttpContextNet5();
        }
    }
}
