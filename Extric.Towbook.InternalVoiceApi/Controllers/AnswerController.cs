using Extric.Towbook.Management.InternalVoice;
using Microsoft.AspNetCore.Mvc;
using NLog;
using System;
using System.Collections.Generic;
using System.Configuration;
using static Extric.Towbook.InternalVoiceApi.VoiceApiUtility;

namespace Extric.Towbook.InternalVoiceApi.Controllers
{
    [Route("Answer")]
    public class AnswerController : ControllerBase
    {
        private static readonly Logger logger = LogManager.GetCurrentClassLogger();

        [Route("")]
        [HttpPost]
        public object Post(object o)
        {
            var f = Web.HttpContext.Current.Request.Form;

            var content = new Plivo.XML.Response();

            content.AddRecord(new Dictionary<string, string>
            {
                ["startOnDialAnswer"] = "true",
                ["callbackUrl"] = "http://voice.towbook.com/record",
                ["maxLength"] = (60 * 1000 * 2).ToString()
            });

            var numbers = new string[] {
                "18102899093",
                "18102898076",
                "18108416790",
                "18102898761",
                "18102898762",
                "18102898076",
                "18106893864",
                "18102899003",
                "18102898242"
            };

            var form = DumpForm();

            if (Core.GetAppSetting("InternalVoiceApi:supportCallsForwardTo") != null)
            {
                numbers = Core.GetAppSetting("InternalVoiceApi:supportCallsForwardTo").Replace("\n", "").Replace(" ", "").Replace("\t", "").Split(',');
            }

            Plivo.XML.PlivoElement dial = null;

            if (form["From"].ToString() == "18108419189")
            {
                numbers = new string[] { "18103008989" };

                dial = content.AddDial(new Dictionary<string, string>
                {
                    ["confirmKey"] = "1",
                    ["confirmSound"] = "http://voice.towbook.com/forwardSound?msg=Please+press+1+to+answer.",
                    ["action"] = "http://voice.towbook.com/log",
                    ["redirect"] = "true",
                    ["timeout"] = "15",
                    ["callbackUrl"] = "http://voice.towbook.com/callback"
                });
            }
            else
            {
                dial = content.AddDial(new Dictionary<string, string>
                {
                    ["confirmKey"] = "1",
                    ["confirmSound"] = "http://voice.towbook.com/forwardSound?msg=Please+press+1+to+answer.",
                    ["action"] = "http://voice.towbook.com/log",
                    ["redirect"] = "false",
                    ["callbackUrl"] = "http://voice.towbook.com/callback"
                });
            }


            foreach(var number in numbers)
                dial.AddNumber(number, new Dictionary<string, string>());

            // Dan to take phone calls from 8pm-12am starting 9/6/2016 @ 8PM.  
            //if (DateTime.Now.Hour >= 20)
            //    dial.AddNumber("18108419189", new Dictionary<string, string>());
            
            if (
                (DateTime.Now.Month == 12) &&
                (DateTime.Now.Day == 10 || DateTime.Now.Day == 11) &&
                (DateTime.Now.Year == 2016))
            {
                dial.AddNumber("18102898454", new Dictionary<string, string>());
                dial.AddNumber("18108419189", new Dictionary<string, string>());
            }

            if (
                (DateTime.Now.Month == 12) &&
                (DateTime.Now.Day == 24 || DateTime.Now.Day == 25) &&
                (DateTime.Now.Year == 2016))
            {
                dial.AddNumber("18108419189", new Dictionary<string, string>());
            }

            // @ryan.brooks call schedule handling
            // saturdays and sundays
            // everyday between 8am and 10am
            // all day 12/23/2016
            /*if (DateTime.Now.DayOfWeek == DayOfWeek.Saturday || DateTime.Now.DayOfWeek == DayOfWeek.Sunday ||
                (DateTime.Now.Hour >= 8 || DateTime.Now.Hour < 10) || 
                ((DateTime.Now.Month == 12) && (DateTime.Now.Day == 23) && (DateTime.Now.Year == 2016))
                )
            {
                dial.AddNumber("18102898076", new Dictionary<string, string>());
            }*/

            var logEvent = new LogEventInfo();
            logEvent.LoggerName = logger.Name;
            logEvent.Message = "Answer";
            logEvent.Level = NLog.LogLevel.Info;
            logEvent.TimeStamp = DateTime.Now;
            logEvent.Properties.Add("data", form);
            logger.Log(logEvent);

            var pc = new PhoneCall();

            pc.UniversalId = new Guid(form["CallUUID"].ToString());
            if (form.ContainsKey("ForwardedFrom"))
                pc.ForwardedFrom = form["ForwardedFrom"].ToString();

            pc.CallerNumber = form["From"].ToString();
            pc.CallerName  = form["CallerName"].ToString();
            pc.BillingRate = Convert.ToDecimal(form["BillRate"]);
            pc.DestinationNumber = form["To"].ToString();
            pc.Direction = 1;
            pc.Save();

            return XmlResponse(content);
        }
    }
}
