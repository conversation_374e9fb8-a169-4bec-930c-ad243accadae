using Agero;
using Extric.Towbook.API.Integration.MotorClubs.Issc;
using Extric.Towbook.Integrations.MotorClubs.Agero;
using Extric.Towbook.Integrations.MotorClubs.Issc;
using Extric.Towbook.Integrations.MotorClubs.Urgently;
using Extric.Towbook.Utility;
using Newtonsoft.Json;
using System;
using System.Diagnostics;

namespace Extric.Towbook.MotorClubs
{

    partial class Program
    {
        public class AgeroPassthroughRedisQueue
        {
            public class passthrough
            {
                public AgeroMessage message;
                public AgeroSession session;
            }

            public static void SetupQueue()
            {
                string name = "agero_inbound";

                var rjq = Queues.RedisJobQueue.Create(name);

                rjq.OnMessage(async(r) =>
                {
                    try
                    {
                        if (r.Properties.ContainsKey("payload"))
                        {
                            Console.WriteLine(DateTime.Now.ToShortDateString() + " " + DateTime.Now.ToLongTimeString() + ": " + name + ":" + r.Properties["payload"].ToString().Truncate(80));

                            var ct = JsonConvert.DeserializeObject<passthrough>(r.Properties["payload"]);

                            Stopwatch sw = Stopwatch.StartNew();

                            await new API.Integration.MotorClubs.Agero.NotifyController().ProcessInput(ct.message, ct.session).ConfigureAwait(false);

                            Console.WriteLine(DateTime.Now.ToShortDateString() + " " + DateTime.Now.ToLongTimeString() + " " + 
                                ct?.session?.VendorId + "/" + ct?.message?.AgeroMessageId + "/" + ct?.message?.Title + ": " +
                                sw.ElapsedMilliseconds + "ms");
                        }
                        r.Complete();
                    }
                    catch (Exception y)
                    {
                        Console.WriteLine(DateTime.Now.ToString() + ": " + y.ToString());
                    }
                });
                
                rjq.AsManager();
                Console.WriteLine(name + " setup and listening.");
            }

            public static void AgeroQueuePassthrough()
            {
                while (true)
                {
                    try
                    {
                        SetupQueue();

                        Console.WriteLine("Press Ctrl+C to exit");
                        Console.ReadLine();
                    }
                    catch (Exception e)
                    {
                        Console.WriteLine(DateTime.Now.ToString() + ": " + e.ToString());
                    }
                }
            }
        }
    }
}