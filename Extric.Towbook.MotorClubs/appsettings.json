{
  "ConnectionStrings": {
    "Database": "Data Source=*************;Initial Catalog=Towbook;Integrated Security=False;User Id=TowbookPublic;Password=****************;Encrypt=false",
    "Database.Azure": "Server=tcp:towbook.database.windows.net,1433;Initial Catalog=towbook;Persist Security Info=False;User ID=dashboardapi;Password=******************;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;",
    //"Microsoft.ServiceBus": "Endpoint=sb://towbook-wc-prod-alias.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=HG1wEo7ZrD2QauISIXOjXVF3JXIo9Ifh3GhO4UBUkKs="
    "Microsoft.ServiceBus": "Endpoint=sb://dev-towbook.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=b55hjowoy1/BIrVRgBY1LOXpN2TT/QuSh3Jp8zCMykc="
  },
  "Redis": {
    "Servers": "*************",
    "Credentials": "",
    "SentinelServiceName": "!disable",
    "ConnectionPoolSize": 1
  },
  "CosmosDb": {
    "Url": "https://towbook-prod.documents.azure.com:443/",
    "AuthKey": "****************************************************************************************",
    "Database": "towbook"
  },
  "Square": {
    "ApplicationId": "*****************************",
    "ApplicationSecret": "sq0csp-dlL9Mj1d8ngg3AHSWABRFiH0k28qNMUFjrz6OZ6YHRg",
    "AccessToken": "EAAAEKGlvUZpzHx-sCfGnx2cycbSZzm6dPR84p1-QzBOwcsR0iS8Vf2hH3YuW_fo",
    "WebhookSignatureKey": "TvayThX8C4SJOijyxc_A5Q",
    "Sandbox": {
      "ApplicationId": "*************************************",
      "ApplicationSecret": "sandbox-sq0csb-mqKt56UG85C_U_w9CmkaQ_PsPewLniMebxy2uBZR--Q",
      "AccessToken": "EAAAEJbXqEQjLfhDlFE5aRuxsFjuOdElbb0R07HTGdzJqx2cSh3wiJO7ByH0OMv4",
      "LocationId": "XPSRDD6DQ003P"
    }
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    }
  },
  "Loggly": {
    "CustomerToken": "************************************",
    "ApplicationName": "test",
    "Transport": {
      "EndpointHostname": "logs-01.loggly.com",
      "EndpointPort": 6514,
      "LogTransport": "SyslogSecure"
    },
    //"SimpleTagValue": "ibm-towbook-us-south-prod"
    "SimpleTagValue": "local-towbook-dev"
  },
  "MotorClubs": {
    "Agero": {
      "UrlBase": "",
      "ConsumerKey": "",
      "ConsumerSecret": "",
      "ConsumerBase64": ""
    },
    "Allstate": {
      "Staging": {
        "UrlBase": ""
      },
      "Production": {
        "UrlBase": ""
      }
    },
    "Swoop": {
      "Staging": {
        "UrlBase": "https://staging.joinswoop.com",
        "ClientId": "",
        "ClientSecret": ""
      },
      "Production": {
        "UrlBase": "https://api.joinswoop.com",
        "ClientId": "",
        "ClientSecret": ""
      }
    },
    "Sykes": {
      "Staging": {
        "UrlBase": "https://sta-api.sykesassistance.com/edispatch/v1/"
      },
      "Production": {
        "UrlBase": "https://prd-api.sykesassistance.com/edispatch/api/v1/"
      }
    },
    "Urgently": {
      "Staging": {
        "UrlBase": "https://dev01-apis.urgent.ly/v3/b2b/"
      },
      "Production": {
        "UrlBase": "https://towbook-apis.urgent.ly/v3/b2b/"
      }
    },
    "Honk": {
      "Development": {
        "UrlBase": "https://towbook-sandbox.honkforhelp.com/",
        "KeyBase": "",
        "UrlTracker": "https://tracker-sandbox.roadstruck.com",
        "KeyTracker": ""
      },
      "Production": {
        "UrlBase": "https://towbook-dispatch.honkforhelp.com/",
        "KeyBase": "b3c3c7ac-627d-4c20-bb7f-f0777692a571",
        "UrlTracker": "https://tracker.roadstruck.com",
        "KeyTracker": "b3c3c7ac-627d-4c20-bb7f-f0777692a571"
      }
    },
    "AaaAce": {
      "Uat": {
        "UrlBase": "https://dev.b2bmapi.autoclubextranet.com/e-ers-call",
        "ClientId": "",
        "ClientSecret": ""
      },
      "Test": {
        "UrlBase": "https://uat.b2bmapi.autoclubextranet.com/e-ers-call",
        "ClientId": "",
        "ClientSecret": "",
        "CertificateThumbprint": ""
      },
      "Production": {
        "UrlBase": "https://b2bmapi.autoclubextranet.com/e-ers-call",
        "ClientId": "",
        "ClientSecret": "",
        "CertificateThumbprint": ""
      }
    },
    "AaaWashington": {
      "Uat": {
        "UrlBase": "https://aaawa--sfsdev.sandbox.my.salesforce.com/",
        "ClientId": "3MVG9j6uMOMC1DNgCpzaeXQu_YsJBVz_hv0268PKCiDF7xnslTscXA6W79nEd471XzSz0d2PQHMHUaBRNO9x4",
        "ClientSecret": "****************************************************************"
      },
      "Production": {
        "UrlBase": "",
        "ClientId": "",
        "ClientSecret": ""
      }
    },
    "AaaWCNY": {
      "Uat": {
        "UrlBase": "https://aaawcny--ersdev.sandbox.my.salesforce.com",
        "ClientId": "3MVG9lwuYVfhx9kmLTZI7LyXuPY4HO5vqbp_rKwM410ag3yQTp4p0Q8pP.ItPr50hH6ptNakSwmfZ7MugVfmv",
        "ClientSecret": "****************************************************************"
      },
      "Production": {
        "UrlBase": "",
        "ClientId": "",
        "ClientSecret": ""
      }
    }
  }
}