using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Extric.Towbook.Dispatch;
using Extric.Towbook.Integration;
using Extric.Towbook.Utility;
using Newtonsoft.Json;
using Async = System.Threading.Tasks;

namespace Extric.Towbook.MotorClubs.PredictedArrivalEtas
{
    public class PredictedArrivalService
    {

        public class RealTimeRecord
        {
            public int? CallId { get; set; }
            public int? CallStatus { get; set; }

            public int? DriverId { get; set; }
            public int? UserId { get; set; }

            [JsonIgnore]
            public bool SecondCall { get; set; }

            public decimal? CallLatitude { get; set; }
            public decimal? CallLongitude { get; set; }

            public decimal? UserLatitude { get; set; }
            public decimal? UserLongitude { get; set; }

            public DateTime? UserTimestamp { get; set; }

            public DateTime? CallEta { get; set; }
            public DateTime? DriverEta { get; set; }

            public DateTime LastUpdated { get; set; }
            public decimal? DriverDistanceRemaining { get; set; }
        }

        /// <summary>
        /// Runs an infinite loop that processed predicted eta's for all active calls in the system
        /// </summary>
        public static void RunPredictiveArrivalTimes()
        {
            while (true)
            {
                try
                {
                    var sw = Stopwatch.StartNew();

                    var companyIds = SqlMapper.Query<int>(@"select f.CompanyId From vwcompanyfeatures f with (nolock) inner join vwcompanies c with (nolock) on c.companyid=f.companyId and c.LastLogin > dateadd(day,-3,getdate())
                    where FeatureId = 79").ToArray();

                    Console.WriteLine(DateTime.Now.ToString() + ": Running through " + companyIds.Length + " companies...");

                    Parallel.ForEach(companyIds,
                        new ParallelOptions() { MaxDegreeOfParallelism = 64 },
                        async (o) =>
                        {
                            try
                            {
                                await CalculatePredictedEtasForCompany(o);
                            }
                            catch (Exception catchall)
                            {
                                Console.WriteLine(o + ": Error occurred: " + catchall.ToString());
                            }
                        });

                    if (sw.ElapsedMilliseconds < 60000)
                    {
                        var waitTime = 60000 - (int)sw.ElapsedMilliseconds;
                        Console.WriteLine(DateTime.Now.ToString() + ": Calculations took " + sw.ElapsedMilliseconds + "ms, waiting " + 
                            waitTime + "ms to run again");
                        Thread.Sleep(waitTime);
                    }
                }
                catch (Exception r)
                {
                    Console.WriteLine(r.ToString());
                    Thread.Sleep(10 * 1000);
                }
            }
        }

        private static JsonSerializerSettings jsonSettings = new JsonSerializerSettings()
        {
            NullValueHandling = NullValueHandling.Ignore,
            ContractResolver = new Newtonsoft.Json.Serialization.CamelCasePropertyNamesContractResolver()
        };

        public static async Async.Task CalculatePredictedEtasForCompany(int companyId)
        {
            // 1. get a list of current calls
            // 2. get the user locations for those calls
            // 3. get our real-time data for those calls
            // 4. check if those user location timetsamps are newer than realtime.timestamp, if so, update.
            // 5. send pusher events with a batch of [{callId:12345,liveEta:{45 minutes, 3 miles}, liveEtd: {35 minutes, 2 miles}},{...}]
            // ... to pickup, to destination ... based on status .. < on scene, > on scene - destination arrival or completed.

            // >> DispatchEntryAssetDriverEtas
            // -- {AssetDriverId, AssetId, WaypointId, Timestamp, Minutes, Distance, Version++}

            var db = new Dictionary<int, RealTimeRecord>();
            var sw = Stopwatch.StartNew();
            // Get current calls 
            var comp = await Company.Company.GetByIdAsync(companyId);
            if (comp == null)
            {
                Console.WriteLine("invalid companyId: " + companyId);
                return;
            }

            var en = Entry.GetCurrentByCompany(await Company.Company.GetByIdAsync(companyId), true, false, false, true);

            // CallId, DriverId, UserId,  <Call.Lat, Call.Long>,  UserLatitude, UserLongitude

            foreach (var call in en)
            {
                RealTimeRecord rt = null;
                if (db.ContainsKey(call.Id))
                {
                    rt = db[call.Id];
                }
                else
                {
                    rt = new RealTimeRecord() { CallId = call.Id };
                    db.Add(call.Id, rt);
                }
                rt.CallEta = call.ArrivalETA;

                if (call.Status == Status.EnRoute)
                {
                    var pickup = call.Waypoints.Where(o => o.Title == "Pickup").FirstOrDefault();
                    if (pickup != null)
                    {
                        Console.WriteLine(call.CallNumber + ": Calculate time to pickup");

                        rt.CallStatus = call.Status.Id;
                        rt.CallLatitude = pickup.Latitude;
                        rt.CallLongitude = pickup.Longitude;

                        rt.DriverId = call.DriverId;
                        rt.CallEta = call.ArrivalETA;
                    }
                }
                else if (call.Status == Status.BeingTowed)
                {
                    Console.WriteLine(call.CallNumber + ": Calculate time to destination");

                    var dest = call.Waypoints.Where(o => o.Title == "Destination").FirstOrDefault();
                    if (dest != null)
                    {
                        rt.CallLatitude = dest.Latitude;
                        rt.CallLongitude = dest.Longitude;
                        rt.CallStatus = call.Status.Id;
                        rt.DriverId = call.DriverId;
                        rt.CallEta = null;
                    }
                }
                else if (call.Status == Status.Dispatched)
                {
                    Console.WriteLine(call.CallNumber + ": Calculate time from current call ending point");
                    // calcualte predicted from the call that call.Driver is currently (enroute, on-scene, or towing) 

                    var pickup = call.Waypoints.Where(o => o.Title == "Pickup").FirstOrDefault();

                    var otherCall = en.Where(o => o.DriverId == call.DriverId &&
                        (o.Status.Id == Status.EnRoute.Id ||
                        o.Status.Id == Status.AtSite.Id ||
                        o.Status.Id == Status.BeingTowed.Id ||
                        o.Status.Id == Status.DestinationArrival.Id)).OrderBy(o => o.CallNumber).FirstOrDefault();

                    if (otherCall != null)
                    {
                        rt.CallStatus = call.Status.Id;
                        if (otherCall.Status.Id == Status.EnRoute.Id ||
                            otherCall.Status.Id == Status.AtSite.Id)
                        {
                            rt.UserLatitude = otherCall.Waypoints.Where(o => o.Title == "Pickup").FirstOrDefault()?.Latitude ?? 0;
                            rt.UserLongitude = otherCall.Waypoints.Where(o => o.Title == "Pickup").FirstOrDefault()?.Longitude ?? 0;
                        }
                        else if (otherCall.Status.Id == Status.BeingTowed.Id ||
                            otherCall.Status.Id == Status.DestinationArrival.Id)
                        {
                            rt.UserLatitude = otherCall.Waypoints.Where(o => o.Title == "Pickup").FirstOrDefault()?.Latitude ?? 0;
                            rt.UserLongitude = otherCall.Waypoints.Where(o => o.Title == "Pickup").FirstOrDefault()?.Longitude ?? 0;
                        }

                        rt.CallLatitude = pickup?.Latitude;
                        rt.CallLongitude = pickup?.Longitude;
                        rt.SecondCall = true;
                        rt.DriverId = call.DriverId;
                    }
                }
            }

            foreach (var rt in db.Values.Where(o => o.DriverId.GetValueOrDefault() != 0 && o.CallLatitude.GetValueOrDefault() != 0))
            {
                if (rt.DriverId.GetValueOrDefault() == 0)
                    continue;
                // don't look at user gps if its a second call, we're going to calculate from current call to this call
                if (rt.SecondCall)
                    continue;

                var driver = await Driver.GetByIdAsync(rt.DriverId.GetValueOrDefault());
                if (driver.UserId > 0)
                {
                    var uli = UserLocationHistoryItem.GetCurrentByUserId(driver.UserId, DateTime.Now.AddMinutes(-5), DateTime.Now);
                    if (uli != null)
                    {
                        rt.UserId = driver.UserId;
                        rt.UserLatitude = uli.Latitude;
                        rt.UserLongitude = uli.Longitude;
                        rt.UserTimestamp = uli.Timestamp.ToUniversalTime();
                    }
                    else
                    {
                        Console.WriteLine("DriverId " + driver.UserId + " / driverId " + driver.Id + ": NO GPS FROM LAST 5 MINUTES");
                    }
                }
            }

            foreach (var rt in db.Values.Where(o => o.DriverId.GetValueOrDefault() != 0 && o.CallLatitude.GetValueOrDefault() != 0))
            {
                var est = await API.Controllers.Mapping.GMapUtil.GetMatrix(
                    rt.UserLatitude + "," + rt.UserLongitude,
                    rt.CallLatitude + "," + rt.CallLongitude);

                if (est != null &&
                    est.Status == "OK" &&
                    est.Rows.First().Elements.First().Status == "OK")
                {
                    rt.DriverEta = DateTime.SpecifyKind(DateTime.Now.AddSeconds(est.Rows.FirstOrDefault().Elements.FirstOrDefault().Duration.Value), DateTimeKind.Unspecified).ToUniversalTime();
                    rt.DriverDistanceRemaining = decimal.Round((decimal)est.Rows.FirstOrDefault().Elements.FirstOrDefault().Distance.Value / 1609.344m, 2);
                }
            }

            //Console.WriteLine(db.Values.Where(o => o.DriverEta != DateTime.MinValue).ToJson());

            // must have lat, long of pickup or skip.
            // must have lat, long of destination or skip

            // get last
            // get current
            // broadcast the difference

            var objects = new List<RealTimeRecord>();

            Console.WriteLine(db.Values.Where(o => o.DriverEta > DateTime.MinValue).Count() + " calls to send eta updates on");

            foreach (var x in db.Values.Where(o => o.DriverEta > DateTime.MinValue))
            {
                x.LastUpdated = DateTime.SpecifyKind(DateTime.Now, DateTimeKind.Unspecified).ToUniversalTime();
                var existing = JsonConvert.DeserializeObject<RealTimeRecord>(Core.GetRedisValue("calculatedEta:" + x.CallId) ?? "{}");

                var co = JsonExtensions.GetChanges(
                            existing,
                            x, true);

                var o = JsonConvert.DeserializeObject<RealTimeRecord>(
                    JsonConvert.SerializeObject(co, Formatting.None, jsonSettings));

                objects.Add(JsonConvert.DeserializeAnonymousType(
                    JsonConvert.SerializeObject(co, Formatting.None, jsonSettings), o));

                Core.SetRedisValue("calculatedEta:" + x.CallId,
                    x.ToJson(), TimeSpan.FromMinutes(15));

                Console.WriteLine(x.ToJson(true));
            }

            if (comp.HasFeature(Generated.Features.Roadside))
            {
                var temp = objects.Where(ro => ro.DriverEta != null &&
                    (ro.DriverEta.Value - DateTime.UtcNow).TotalMinutes <= 5);

                var temp2 = temp
                    .Select(xo => en.FirstOrDefault(r => r.Id == xo.CallId && r.Status.Id == Status.EnRoute.Id))
                    .Where(rx => rx != null).ToCollection();
                try
                {
                    await HandlePredictedSmsAlertForRoadside(temp2);
                }
                catch (Exception r)
                {
                    Console.WriteLine(r.ToString());
                }
            }

            if (objects.Any())
                await PushNotificationProvider.Push(PushNotificationProvider.GetChannelName(companyId), "realtime_update", objects, true, true);

            Console.WriteLine(DateTime.Now.ToString() + " " + companyId + ": Finished in " + sw.ElapsedMilliseconds + "ms");
        }

        public static async Async.Task HandlePredictedSmsAlertForRoadside(Collection<Entry> entries)
        {
            const string CachePredictedAlertMessageFormat = "pam_{0}_{1}";  //dispatchEntry, StatusTypeId (Arriving)

            var alreadySentIds = new Collection<int>();
            foreach (var e in entries)
            {
                string key = string.Format(CachePredictedAlertMessageFormat, e.Id, Extric.Roadside.JobProgressStatusType.Arriving.Id);
                var val = Core.GetRedisValue(key);
                if (val == "1")
                    alreadySentIds.Add(e.Id);
            }

            entries = entries.Where(w => !alreadySentIds.Contains(w.Id)).ToCollection();

            if (entries.Any())
            {

                // companies that are set to receive the arriving message alert
                var alerts = Extric.Roadside.JobProgressTextAlertItem.GetByCompanyIds(entries.Select(s => s.CompanyId).ToArray(), new int[] { Extric.Roadside.JobProgressStatusType.Arriving.Id }, true);

                // roadside users 
                var rUsers = Extric.Roadside.RoadsideDispatchUser.GetByDispatchEntryIds(entries.Select(s => s.Id).ToArray());

                // messages already sent for these alert types
                var sentMessages = Extric.Roadside.RoadsideDispatchMessage.GetMessageByJobProgressTextAlertItemIds(rUsers.Select(s => (int)s.Id).ToArray(), alerts.Select(s => s.Id).ToArray());

                // narrow down users to those who have not received a message yet
                rUsers = rUsers.Where(w => !sentMessages.Select(s => s.RoadsideDispatchUserId).ToCollection().Contains(w.Id));

                // send predicted arrival alert by each entry
                foreach (var u in rUsers.GroupBy(g => g.DispatchEntryId))
                {
                    var companyId = entries.Where(x => x.Id == u.Key).FirstOrDefault()?.CompanyId ?? 0;
                    if (!alerts.Where(w => w.CompanyId == companyId).Any())
                        alerts = Extric.Roadside.JobProgressTextAlertItem.GetDefaults(companyId, null);

                    var alert = alerts.Where(w => !w.IsDeleted && entries.Where(x => x.Id == u.Key).Select(s => s.CompanyId).Contains(w.CompanyId)).FirstOrDefault();
                    if (alert != null)
                    {
                        // Trigger Event notification to trigger predicted arrival alert
                        var item = new Extric.Towbook.EventNotifications.RoadsideDispatchQueueItem();
                        item.Type = Extric.Towbook.EventNotifications.RoadsideDispatchTriggerType.PredictedArrivalAlert;
                        item.DispatchEntryId = u.Key;
                        await item.TriggerEvent();

                        string key = string.Format(CachePredictedAlertMessageFormat, u.Key, Extric.Roadside.JobProgressStatusType.Arriving.Id);
                        Core.SetRedisValue(key, "1");
                    }
                }
            }
        }
    }
}
