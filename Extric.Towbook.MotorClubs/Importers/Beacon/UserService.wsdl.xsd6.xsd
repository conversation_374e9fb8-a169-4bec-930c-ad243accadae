<?xml version="1.0" encoding="utf-8"?><xs:schema elementFormDefault="qualified" targetNamespace="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model.TowLien" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:tns="http://schemas.datacontract.org/2004/07/DispatchAnywhere3.Model.TowLien"><xs:complexType name="AddVehicleResult"><xs:complexContent mixed="false"><xs:extension base="tns:Result"><xs:sequence><xs:element minOccurs="0" name="VehicleId" nillable="true" type="xs:string"/></xs:sequence></xs:extension></xs:complexContent></xs:complexType><xs:element name="AddVehicleResult" nillable="true" type="tns:AddVehicleResult"/><xs:complexType name="Result"><xs:sequence><xs:element minOccurs="0" name="ErrorMessage" nillable="true" type="xs:string"/><xs:element minOccurs="0" name="IsSuccessful" type="xs:boolean"/></xs:sequence></xs:complexType><xs:element name="Result" nillable="true" type="tns:Result"/><xs:complexType name="ArrayOfLotLocation"><xs:sequence><xs:element minOccurs="0" maxOccurs="unbounded" name="LotLocation" nillable="true" type="tns:LotLocation"/></xs:sequence></xs:complexType><xs:element name="ArrayOfLotLocation" nillable="true" type="tns:ArrayOfLotLocation"/><xs:complexType name="LotLocation"><xs:sequence><xs:element minOccurs="0" name="Address1" nillable="true" type="xs:string"/><xs:element minOccurs="0" name="Address2" nillable="true" type="xs:string"/><xs:element minOccurs="0" name="City" nillable="true" type="xs:string"/><xs:element minOccurs="0" name="LocationId" type="xs:long"/><xs:element minOccurs="0" name="LocationName" nillable="true" type="xs:string"/><xs:element minOccurs="0" name="Phone" nillable="true" type="xs:string"/><xs:element minOccurs="0" name="State" nillable="true" type="xs:string"/><xs:element minOccurs="0" name="ZipCode" nillable="true" type="xs:string"/></xs:sequence></xs:complexType><xs:element name="LotLocation" nillable="true" type="tns:LotLocation"/></xs:schema>