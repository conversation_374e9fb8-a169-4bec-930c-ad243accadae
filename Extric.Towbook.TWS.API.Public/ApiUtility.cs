using Extric.Towbook.Web;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;

namespace Extric.Towbook.TWS.API.Public
{
    internal static class ApiUtility
    {
        public static void ThrowIfPropertyMissing(string name)
        {
            throw new Extric.Towbook.Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest)
            {
                Content = new StringContent($"Couldn't process request because the {name} property wasn't specified.")
            });
        }

        public static void ThrowIfNoCompanyAccess(int? companyId, string type = "object")
        {
            if (type == null)
                type = "object";

            if (companyId == null)
            {
                throw new Extric.Towbook.Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound)
                {
                    Content = new StringContent($"The {type.ToLowerInvariant()} you requested cannot be found.")
                });
            }

            if (TWSNet5.BeginRequestMiddleware.CurrentCompany.Id != companyId)
            {
                throw new Extric.Towbook.Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.Forbidden)
                {
                    Content = new StringContent($"This company doesn't have access to this {type}.")
                });
            }
        }

        public static void ThrowIfNotFound(object o, string type = "object")
        {
            if (o == null)
                throw new Extric.Towbook.Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound)
                {
                    Content = new StringContent($"The {type} you tried to access cannot be found.")
                });
        }

        public static void ThrowIfFeatureNotAssigned(Generated.Features f)
        {
            if (!TWSNet5.BeginRequestMiddleware.CurrentCompany.HasFeature(f))
                throw new Extric.Towbook.Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound)
                {
                    Content = new StringContent($"This company doesn't have the {f.ToString()} feature included in its current service plan.")
                });
        }

        public static HttpResponseMessage NoContent()
        {
            return new HttpResponseMessage(HttpStatusCode.NoContent);
        }

    }
}