using System.Linq;
using System.Threading.Tasks;
using Extric.Towbook.Impounds;
using Extric.Towbook.TWS.API.Public.Models;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using static Extric.Towbook.TWS.API.Public.ApiUtility;

namespace Extric.Towbook.TWS.API.Public.Controllers;

[EnableCors("CorsPolicy")]
[Route("api/VehicleLocator")]
public class VehicleLocatorController : ControllerBase
{
    [EnableCors("CorsPolicy")]
    [HttpGet]
    public async Task<IActionResult> Get([FromQuery] string plate)
    {
        // handle sample request
        if (new int[] { 2 }.Contains(TWSNet5.BeginRequestMiddleware.CurrentCompany.Id))
            return Ok(VehicleLocatorModel.GetSampleResult());

        return await PostVehicleLocator(plate);
    }

    [EnableCors("CorsPolicy")]
    [HttpPost]
    public async Task<IActionResult> PostVehicleLocator(string licensePlate)
    {
        ThrowIfFeatureNotAssigned(Generated.Features.VehicleLookup);
        ThrowIfNoCompanyAccess(TWSNet5.BeginRequestMiddleware.CurrentCompany.Id);

        if (!string.IsNullOrWhiteSpace(licensePlate))
        {
            var vehicleLocator = (licensePlate.Length == 17) ?
                VehicleLocatorModel.Map(await Impound.GetByVinForVehicleLookupAsync(TWSNet5.BeginRequestMiddleware.CurrentCompany, licensePlate, false))
            : VehicleLocatorModel.Map(await Impound.GetByPlateForVehicleLookupAsync(TWSNet5.BeginRequestMiddleware.CurrentCompany, licensePlate, false));

            return vehicleLocator is not null ? Ok(vehicleLocator) : NoContent();
        }

        return NoContent();
    }
}
