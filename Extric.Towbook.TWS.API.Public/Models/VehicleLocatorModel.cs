using Extric.Towbook.Impounds;
using System.Collections.Generic;
using Extric.Towbook.Utility;
using System;
using System.Net;

namespace Extric.Towbook.TWS.API.Public.Models
{
    public class Location
    {
        public int? LotId { get; set; }
        public string Name { get; set; }
        public string Address { get; set; }
        public string City { get; set; }
        public string State { get; set; }
        public string Zip { get; set; }
        public string Phone { get; set; }

        public DetailItem[] Details { get; set; }

        public string FullAddress {
            get 
            {
                return Address +
                    (string.IsNullOrEmpty(City) ? "" : " " + City) +
                    (string.IsNullOrEmpty(State) ? "" : " " + State) +
                    (string.IsNullOrEmpty(Zip) ? "" : " " + Zip);
            }

            set { } 
        }

        public string MapEmbedSrc { get; set; }

        public static List<DetailItem> GetDetails(int companyId)
        {
            var ret = new List<DetailItem>();

            var ckvNotice = Integration.CompanyKeyValue.GetFirstValueOrNull(companyId, Integration.Provider.Towbook.ProviderId, "VehicleLookupGeneralNotice");
            if (!string.IsNullOrEmpty(ckvNotice))
            {
                ret.Add(new DetailItem() {
                    Title = "Notes",
                    Content = ckvNotice
                });
            }

            return ret;
        }
    }

    public class DetailItem
    {
        public string Title { get; set; }
        public string Content { get; set; }
    }

    public class VehicleLocatorModel
    {
        public int ImpoundId { get; set; }
        public int CompanyId { get; set; }
        public Location Location { get; set; }
        public DetailItem[] Details { get; set; }
        

        public static VehicleLocatorModel Map(Impound i, VehicleLocatorModel orig = null)
        {
            if (i == null)
                return null;

            orig = orig ?? new VehicleLocatorModel();

            orig.ImpoundId = i.Id;
            orig.CompanyId = i.Company?.Id ?? 0;

            orig.Location = new Location()
            {
                LotId = i.Lot?.Id ?? 0,
                Name = i.Lot?.Name,
                Address = i.Lot?.Address,
                City = i.Lot?.City,
                State = i.Lot?.State,
                Zip = i.Lot?.Zip,
                Phone = i.Lot?.Phone
            };

            orig.Location.Details = Location.GetDetails(orig.CompanyId).ToArray();

            if (i.Lot != null && 
                !string.IsNullOrEmpty(i.Lot.Address) && 
                !string.IsNullOrEmpty(i.Lot.City) && 
                !string.IsNullOrEmpty(i.Lot.State))
                orig.Location.MapEmbedSrc = "https://www.google.com/maps/embed/v1/place?key=" + Core.GetAppSetting<string>("Google:Maps:ApiKey") + "&q=" + WebUtility.UrlEncode(orig.Location.FullAddress);
            
            orig.Details = GetDetailItems(i).ToArray();

            return orig;
        }

        public static List<DetailItem> GetDetailItems(Impound i) {

            List<DetailItem> items = new List<DetailItem>();

            if (i == null)
                return items;

            if (i.ImpoundDate.HasValue)
            {
                var companyTime = Core.OffsetDateTime(i.Company, i.ImpoundDate.Value, false);

                items.Add(new DetailItem()
                {
                    Title = "Impound Date",
                    Content = $"{companyTime.ToLongDateString()} @ {companyTime.ToShortTowbookTimeString()}".ToJson().Replace("\"", "")
                });
            }

            var e = i.DispatchEntry;
            if(e != null)
            {
                if (e.Reason != null && !string.IsNullOrEmpty(e.Reason.Name))
                {
                    items.Add(new DetailItem()
                    {
                        Title = "Reason",
                        Content = e.Reason.Name
                    });
                }

                var ckvPrice = Integration.CompanyKeyValue.GetFirstValueOrNull(e.CompanyId, Integration.Provider.Towbook.ProviderId, "VehicleLookupIncludePrice") == "1";
                if (ckvPrice)
                {
                    items.Add(new DetailItem()
                    {
                        Title = "Price (Subject to change)",
                        Content = e.InvoiceTotal.ToString("C")
                    });
                }

                if (e.AccountId > 1 && i.Company != null && i.Company.State != "CA") 
                {
                    items.Add(new DetailItem()
                    {
                        Title = "Authorized By",
                        Content = e.AccountId > 1 ? e.Account.Company : string.Empty
                    });
                }

                if (!string.IsNullOrEmpty(e.TowSource))
                {
                    items.Add(new DetailItem()
                    {
                        Title = "Towed From",
                        Content = e.TowSource
                    });
                }

                if (!string.IsNullOrEmpty(e.VIN))
                {
                    items.Add(new DetailItem()
                    {
                        Title = "VIN",
                        Content = e.VIN
                    });
                }

                if (!string.IsNullOrEmpty(e.MakeModelFormatted.Trim()))
                {
                    items.Add(new DetailItem()
                    {
                        Title = "Vehicle",
                        Content = e.MakeModelFormatted
                    });
                }
            }

            return items;
        }

        internal static VehicleLocatorModel GetSampleResult()
        {
            var details = new List<DetailItem>();

            details.Add(new DetailItem() { Title = "Reason", Content = "Fire Lane Parking" });
            details.Add(new DetailItem() { Title = "Event Date", Content = DateTime.UtcNow.AddHours(-24).ToJson().Replace("\"", "") });
            details.Add(new DetailItem() { Title = "VIN", Content = "19UUA65506A059978" });
            details.Add(new DetailItem() { Title = "Vehicle", Content = "2006 Acura TL" });
            details.Add(new DetailItem() { Title = "Towed From", Content = "1167 S. Carney Drive St. Clair, MI 48079 (Neiman's Market)" });

            var model = new VehicleLocatorModel()
            {
                CompanyId = 2,
                ImpoundId = 1,
                Location = new Location()
                {
                    LotId = 0,
                    Address = "201 N Riverside Ave, Ste E6",
                    City = "St. Clair",
                    State = "Michigan",
                    Zip = "48079",
                    Phone = "(*************"
                },
                Details = details.ToArray()
            };

            model.Location.MapEmbedSrc = "https://www.google.com/maps/embed/v1/place?key=" + Core.GetAppSetting<string>("Google:Maps:ApiKey") + "&q=" + WebUtility.UrlEncode(model.Location.FullAddress);

            return model;
        }
    }
    
}
