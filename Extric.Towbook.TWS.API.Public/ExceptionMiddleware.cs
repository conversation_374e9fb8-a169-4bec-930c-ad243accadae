using System;
using System.Text;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;

namespace TWSNet5;

public class ExceptionMiddleware
{
    private readonly RequestDelegate _next;

    public ExceptionMiddleware(RequestDelegate next)
    {
        _next = next;
    }

    public async Task InvokeAsync(HttpContext httpContext)
    {
        try
        {
            await _next(httpContext);
        }
        catch (Extric.Towbook.Web.HttpResponseException ex)
        {
            Console.WriteLine($"HttpResponseException occurs: {ex.Message}");
            var response = ex.Response;

            if (response != null)
            {
                httpContext.Response.StatusCode = (int)response.StatusCode;
                httpContext.Response.ContentType = response.Content.Headers.ContentType?.ToString();

                var content = await response.Content.ReadAsStringAsync();
                await httpContext.Response.WriteAsync(content);
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"TWS Something went wrong: {ex}");
            await HandleExceptionAsync(httpContext, ex);
        }
    }

    private async Task HandleExceptionAsync(HttpContext context, Exception exception)
    {
        context.Response.StatusCode = StatusCodes.Status500InternalServerError;

        if (exception is TwsException)
        {
            StringBuilder sb = new StringBuilder();
            sb.Append("<html><head><title>Towbook </title><style> body { background-color: black; color: white; font-family: verdana }</style></head><body><h1>" + exception.Message + "</h1><p></p></body></html>");
            sb.Append(exception.Message);

            await context.Response.WriteAsync(sb.ToString());

        }
        else
            await context.Response.WriteAsync(exception.Message);
    }
}

public static class ExceptionMiddlewareExtensions
{
    public static IApplicationBuilder ConfigureExceptionMiddleware(
        this IApplicationBuilder builder)
    {
        return builder.UseMiddleware<ExceptionMiddleware>();
    }
}
