using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Hosting;
using System.Net;

namespace Extric.Towbook.TWS.API.Public
{
    public class Program
    {
        public static void Main(string[] args)
        {
            CreateHostBuilder(args).Build().Run();
        }

        public static IHostBuilder CreateHostBuilder(string[] args) =>
            Host.CreateDefaultBuilder(args)
                .ConfigureWebHostDefaults(webBuilder =>
                {
                    webBuilder.UseStartup<Startup>();
                    webBuilder.ConfigureKestrel(options =>
                    {
                        options.Listen(IPAddress.Any, 5080);
                        options.Listen(IPAddress.Any, 5081, listenOptions =>
                        {
                            //listenOptions.UseHttps("C:\\https\\aspnetapp.pfx",
                            //    "towbook");
                        });
                        options.Limits.MaxRequestBodySize = null;
                    });
                });
    }
}
