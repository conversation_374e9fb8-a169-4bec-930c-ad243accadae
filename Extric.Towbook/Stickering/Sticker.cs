using Extric.Towbook.Integration;
using Extric.Towbook.Utility;
using Glav.CacheAdapter.Core.DependencyInjection;
using ProtoBuf;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Data.SqlClient;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Extric.Towbook.Stickering
{
    public class StickerStatus
    {
        public int Id { get; private set; }
        public string Name { get;  private set; }

        public static StickerStatus Unsaved { get; } = new StickerStatus() { Id = 0, Name = "Unsaved" };
        public static StickerStatus Waiting { get; } = new StickerStatus() { Id = 1, Name = "Waiting" };
        public static StickerStatus Rejected { get; } = new StickerStatus() { Id = 2, Name = "Rejected" };
        public static StickerStatus Approved { get; } = new StickerStatus() { Id = 3, Name = "Approved" };
        public static StickerStatus Resolved { get; } = new StickerStatus() { Id = 4, Name = "Resolved" };
        public static StickerStatus Towable { get; } = new StickerStatus() { Id = 5, Name = "Towable" };
        public static StickerStatus Towed { get; } = new StickerStatus() { Id = 6, Name = "Towed" };
        public static StickerStatus Converted { get; } = new StickerStatus() { Id = 7, Name = "Converted" };
        public static StickerStatus Expired { get; } = new StickerStatus() { Id = 8, Name = "Expired" };
        public static StickerStatus Unapproved { get; } = new StickerStatus() { Id = 9, Name = "Unapproved" };
        public static StickerStatus Unresolved { get; } = new StickerStatus() { Id = 10, Name = "Unresolved" };
        public static StickerStatus Unrejected { get; } = new StickerStatus() { Id = 11, Name = "Unrejected" };
        public static StickerStatus Extended { get; } = new StickerStatus() { Id = 12, Name = "Extended" };
        public static StickerStatus Matured { get; } = new StickerStatus() { Id = 13, Name = "Matured" };

        private StickerStatus()
        {

        }
    }

    public enum StickerAuthorizationType
    {
        None = 0,
        TowManager,
        PropertyManager
    }

    [Serializable]
    [ProtoContract(ImplicitFields = ImplicitFields.AllFields, Name = "Sticker")]
    [DebuggerDisplay("Id = {Id}")]
    public class Sticker
    {
        // note: this value should match the value used in 
        // the StickersGetByCompanyId procedure
        public const int STICKER_PAGE_SIZE = 25;

        private const string cacheFormat = "stkrs-all-{0}";
        private const string cacheFormatSticker = "s-s-account-{0}";
        private const string cacheFormatStickerCompany = "s-s-company-{0}";
        private const int CacheTimeout = 1440;

        [Key("StickerId")]
        public int Id { get; protected set; }
        public int CompanyId { get; set; }

        /// <summary>
        /// Automatically generated sticker number; similar to a call number. 
        /// </summary>
        public int StickerNumber { get; private set; }

        public int OwnerUserId { get; set; }

        public int AccountId { get; set; }
        public string VehicleLocation { get; set; }

        public decimal? VehicleLatitude { get; set; }
        public decimal? VehicleLongitude { get; set; }

        public string CustomNumber { get; set; }
        public string Notes { get; set; }
        public DateTime CreateDate { get; set; }
        public int StatusId { get; set; }
        public string StatusName { get; set; }
        public string StatusColor { get; set; }
        public int ColorId { get; set; }
        public int BodyTypeId { get; set; }

        [Key("Model")]
        public string VehicleModel { get; set; }
        [Key("Make")]
        public string VehicleMake { get; set; }
        [Key("VIN")]
        public string VehicleVIN { get; set; }
        [Key("ModelYear")]
        public int VehicleYear { get; set; }
        public string LicenseNumber { get; set; }
        public string LicenseState { get; set; }

        public int[] Reasons { get; set; }
        public int? DispatchEntryId { get; set; }

        private int? _callNumber = null;
        [Write(false)]
        public int? CallNumber {
            get
            {
                if (_callNumber != null)
                    return _callNumber;

                if (DispatchEntryId != null)
                {
                    var e = Dispatch.Entry.GetById(DispatchEntryId.Value);
                    if (e != null) return e.CallNumber;
                }

                return null;
            }
            set
            {
                _callNumber = value;
            }
        }

        public DateTime? AuthorizationDate { get; set; }
        
        public DateTime? ResolvedDate { get; set; }
        public int? ResolvedUserId { get; set; }
        public DateTime? RejectedDate { get; set; }
        public int? RejectedUserId { get; set; }

        /// <summary>
        /// When can the stickered vehicle be towed? It's a contract violation 
        /// for the towing company to tow a vehicle before this time has been reached.
        /// </summary>
        public DateTime? GracePeriodExpirationDate { get; set; }
        public GracePeriodStartFromType? GracePeriodType { get; set; }

        /// <summary>
        /// When the extend action is performed, this field will contain the date that now is the towable
        /// date.  Keep this value null or it will replace the GracePeriodExpirationDate.
        /// </summary>
        public DateTime? ExtendedExpirationDate { get; set; }

        /// <summary>
        /// When this time is reached, the sticker is no longer valid and CANNOT be towed.
        /// </summary>
        public DateTime? ExpirationDate { get; set; }

        public int StickerSessionId { get; set; }

        /// <summary>
        /// Value to indicate what type of authorization is required.
        /// null or 0 = no authorization required, 1 = TowManager, 2 = PropertyManager
        /// </summary>
        public StickerAuthorizationType? AuthorizationRequired { get; set; }

        /// <summary>
        /// Value to indicate what user approved the sticker
        /// </summary>
        public int? AuthorizationUserId { get; set; }
        public int? AuthorizationUserSignatureId { get; set; }

        [Write(false)]
        public StickerSetting Setting { get; set; }

        public Sticker() { }

        public static Sticker GetById(int id)
        {
            var sticker = Map(SqlMapper.QuerySP<dynamic>("dbo.StickersGetById", new { @StickerId = id })).FirstOrDefault();

            if (sticker != null)
            {
                var reasonIds = new List<int>();

                var reasons = Reason.GetByStickerId(id);

                foreach (var item in reasons)
                    reasonIds.Add(item.Id);

                sticker.Reasons = reasonIds.ToArray();
            }
            return sticker;
        }

        public static IEnumerable<Sticker> GetByAccountId(int accountId)
        {
            return AppServices.Cache.Get<StickerCollection>(String.Format(cacheFormatSticker, accountId), TimeSpan.FromDays(30), () =>
            {
                return new StickerCollection(Map(SqlMapper.QuerySP<dynamic>("dbo.StickersGetByAccountId", new { @AccountId = accountId })));
            }).Collection;
        }

        public static IEnumerable<Sticker> GetByStatusId(int companyId, int statusId, int? accountId = null, int? page = 1)
        {
            var builder = new SqlBuilder();
            var selector = builder.AddTemplate($"SELECT * FROM vwStickers /**where**/ /**orderby**/");

            if (accountId != null)
                builder.Where("AccountId = @AccountId", new { AccountId = accountId.Value });

            builder.Where("CompanyId = @CompanyId", new { CompanyId = companyId });
            builder.Where("Deleted = 0");
            builder.Where("StatusId = @StatusId", new { StatusId = statusId });

            builder.OrderBy("StickerId DESC", null, STICKER_PAGE_SIZE * (page.Value - 1), STICKER_PAGE_SIZE);

            return Map(SqlMapper.Query<dynamic>(selector.RawSql, selector.Parameters)).OrderByDescending(o => o.Id);
        }

        public static IEnumerable<Sticker> GetByCompanyId(int companyId)
        {
            return GetByCompanyId(companyId, null);
        }

        public static IEnumerable<Sticker> GetByCompanyId(int companyId, int? accountId)
        {
            if (accountId != null)
            {
                return AppServices.Cache.Get<StickerCollection>(String.Format(cacheFormatSticker, accountId.Value), TimeSpan.FromDays(30), () =>
                {
                    return new StickerCollection(Map(SqlMapper.QuerySP<dynamic>("dbo.StickersGetByCompanyId", new { @CompanyId = companyId, @AccountId = accountId })).OrderByDescending(o => o.Id));
                }).Collection;
            }

            return AppServices.Cache.Get<StickerCollection>(String.Format(cacheFormatStickerCompany, companyId), TimeSpan.FromDays(30), () =>
            {
                return new StickerCollection(Map(SqlMapper.QuerySP<dynamic>("dbo.StickersGetByCompanyId", new { @CompanyId = companyId, @AccountId = accountId })).OrderByDescending(o => o.Id));
            }).Collection;
        }

        public static IEnumerable<Sticker> GetByCompanyIds(int[] companyIds)
        {
            Collection<Sticker> stickers = new Collection<Sticker>();

            foreach (var cId in companyIds)
            {
                stickers = stickers.Union(GetByCompanyId(cId).ToCollection()).ToCollection();
            }

            return stickers;
        }

        public static Sticker GetByDispatchEntryId(int callId)
        {
            return Map(SqlMapper.QuerySP<dynamic>("dbo.StickersGetByDispatchEntryId", new { @DispatchEntryId = callId })).FirstOrDefault();
        }

        public static async Task<Sticker> GetByDispatchEntryIdAsync(int callId)
        {
            return Map(await SqlMapper.QuerySpAsync<dynamic>("dbo.StickersGetByDispatchEntryId", new { @DispatchEntryId = callId })).FirstOrDefault();
        }

        public static IEnumerable<Sticker> GetBySearchCriteria(int[] companyIds, int? accountId, int? year, string make, string model, string vin, string plate, int? reasonId, DateTime startDate, DateTime endDate)
        {

            return new StickerCollection(Map(SqlMapper.QuerySP<dynamic>("dbo.StickersGetBySearchCriteria", new
            {
                @CompanyIds = String.Join(",", companyIds),
                @AccountId = accountId != 0 ? accountId : null,
                @Year = year != 0 ? year : null,
                @Make = !string.IsNullOrEmpty(make) ? make : null,
                @Model = !string.IsNullOrEmpty(model) ? model : null,
                @VIN = !string.IsNullOrEmpty(vin) ? vin : null,
                @Plate = !string.IsNullOrEmpty(plate) ? plate : null,
                @ReasonId = reasonId != 0 ? reasonId : null,
                @StartDate = startDate,
                @EndDate = endDate
            }))).Collection;
        }

        public static IEnumerable<Sticker> GetStickersReadyForStatusChange()
        {
            return SqlMapper.QuerySP<Sticker>("StickersGetStickersReadyForStatusChange");
        }

        public static IEnumerable<Sticker> GetLast24HourActivity()
        {
            return SqlMapper.QuerySP<Sticker>("StickersGetStickersForDailyReportEmail");
        }

        public static Collection<Sticker> GetBySessionId(int id)
        {
            return Map(SqlMapper.QuerySP<dynamic>("dbo.StickersGetBySessionId", new { StickerSessionId = id })).ToCollection();
        }

        public void DetermineGracePeriod()
        {
            var gps = GracePeriodSetting.Get(CompanyId, AccountId, Reasons.ToArray(), this.CreateDate);

            if (gps == null)
                gps = new GracePeriodSetting();

            this.GracePeriodExpirationDate = GracePeriodSetting.CalculateGraceWaitEndDate(this.CreateDate, gps.StartFromType, gps.Hours ?? 24);
            this.GracePeriodType = gps.StartFromType;
        }

        public bool CheckIfMatured()
        {
            var endDate = ExtendedExpirationDate ?? GracePeriodExpirationDate;
            if (endDate.HasValue) {
                if (DateTime.Now > endDate.Value)
                    return true;
            }
            return false;
        }

        public static bool CheckIfApproved(Sticker s)
        {
            if (s.StatusId == StickerStatus.Unsaved.Id || 
                s.StatusId == StickerStatus.Waiting.Id ||
                s.StatusId == StickerStatus.Approved.Id)
            {
                // first use sticker known state to determine if authorization required
                if (s.AuthorizationRequired > 0 && s.AuthorizationDate.HasValue && s.AuthorizationUserId.HasValue)
                    return true;

                // get company/account settings
                if(s.Setting == null)
                    s.Setting = StickerSetting.GetByCompanyId(s.CompanyId, s.AccountId);

                var ss = s.Setting;

                if (ss == null)
                    ss = new StickerSetting();

                // no approvals required...keep in waiting status
                if (ss != null && !ss.PropertyApprovalRequired && !ss.TowManagerApprovalRequired)
                {
                    s.StatusId = StickerStatus.Waiting.Id;
                    s.AuthorizationDate = null;
                    s.AuthorizationUserId = null;
                    s.AuthorizationUserSignatureId = null;
                    s.AuthorizationRequired = StickerAuthorizationType.None;
                    return true;  // return true to indicate that it is approved (because it is not required)
                }
                else
                {
                    s.AuthorizationRequired = ss.PropertyApprovalRequired ? StickerAuthorizationType.PropertyManager : StickerAuthorizationType.TowManager;

                    if (s.AuthorizationDate != null)
                    {
                        s.StatusId = StickerStatus.Approved.Id;
                        s.StatusName = StickerStatus.Approved.Name;
                    }
                }
            }

            if (s.StatusId == StickerStatus.Approved.Id)
                return true;

            return false;
        }

        public static bool CheckIfTowable(Sticker s)
        {
            if (DateTime.Now > (s.ExtendedExpirationDate ?? s.GracePeriodExpirationDate))
            {
                if (s.StatusId == StickerStatus.Approved.Id || s.StatusId == StickerStatus.Waiting.Id)
                {
                    if (s.AuthorizationRequired > 0 && s.StatusId != StickerStatus.Approved.Id)
                    {
                        return false;
                    }

                    s.StatusId = StickerStatus.Towable.Id;
                    s.StatusName = StickerStatus.Towable.Name;
                }
            }

            if (s.StatusId == StickerStatus.Towable.Id || s.StatusId == StickerStatus.Converted.Id)
                return true;

            return false;
        }

        public static bool CheckIfExpired(Sticker s)
        {
            if (s.StatusId == StickerStatus.Towable.Id ||
                s.StatusId == StickerStatus.Converted.Id)
            {
                // setup expiration date if null
                if (s.ExpirationDate == null)
                {
                    // get company/account settings
                    if (s.Setting == null)
                        s.Setting = StickerSetting.GetByCompanyId(s.CompanyId, s.AccountId);

                    var ss = s.Setting;

                    if (ss != null &&
                        ss.ExpirationHours != null &&
                        ss.ExpirationHours.Value >= 0 &&
                        s.GracePeriodExpirationDate != null)
                    {
                        if (ss.ExpirationHours.Value == 0)
                            s.ExpirationDate = null;
                        else
                        {
                            try
                            {
                                s.ExpirationDate = s.GracePeriodExpirationDate.Value.AddHours(decimal.ToDouble(ss.ExpirationHours.Value));
                            }
                            catch (OverflowException)
                            {
                                s.ExpirationDate = s.GracePeriodExpirationDate.Value;
                            }
                        }
                    }
                }

                if (s.ExpirationDate != null)
                {
                    if (DateTime.Now > s.ExpirationDate.Value)
                    {
                        // !Expired
                        s.StatusId = StickerStatus.Expired.Id;
                        s.StatusName = StickerStatus.Expired.Name;
                        return true;
                    }
                }
            }

            return false;
        }

        public async Task Save(bool sendPusher = true)
        {
            if (this.Reasons == null)
                this.Reasons = Reason.GetByStickerId(this.Id).Select(a => a.Id).ToArray();

            if (this.Reasons == null)
                throw new TowbookException("Sticker must have at least one reason.  GradePeriodExpirationDate cannot be determined.");

            if(Id == 0 || GracePeriodExpirationDate == null)
                DetermineGracePeriod();

            bool approved = CheckIfApproved(this);
            bool towable = CheckIfTowable(this);
            bool expired = CheckIfExpired(this);

            if (GracePeriodExpirationDate == null)
                throw new TowbookException("GradePeriodExpirationDate must be set.");

            int reqId = this.Id;
            if (this.Id == 0)
            {
                this.Id = Convert.ToInt32(SqlMapper.QuerySP<dynamic>("StickersInsert",
                    new
                    {
                        @StatusId = this.StatusId,
                        @AccountId = this.AccountId,
                        @CompanyId = this.CompanyId,
                        @CustomNumber = this.CustomNumber,
                        @Notes = this.Notes,
                        @ColorId = this.ColorId,
                        @BodyTypeId = this.BodyTypeId,
                        @Make = this.VehicleMake,
                        @Model = this.VehicleModel,
                        @VIN = this.VehicleVIN,
                        @ModelYear = this.VehicleYear,
                        @LicenseNumber = this.LicenseNumber,
                        @LicenseState = this.LicenseState,
                        @GracePeriodExpirationDate = this.GracePeriodExpirationDate,
                        @GracePeriodType = (int?)this.GracePeriodType,
                        @ExtendedExpirationDate = this.ExtendedExpirationDate,
                        @AuthorizationDate = this.AuthorizationDate,
                        @AuthorizationUserId = this.AuthorizationUserId,
                        @AuthorizationUserSignatureId = this.AuthorizationUserSignatureId,
                        @AuthorizationRequired = this.AuthorizationRequired,
                        @ResolvedDate = this.ResolvedDate,
                        @ResolvedUserId = this.ResolvedUserId,
                        @RejectedDate = this.RejectedDate,
                        @RejectedUserId = this.RejectedUserId,
                        @VehicleLocation = this.VehicleLocation,
                        @VehicleLatitude = this.VehicleLatitude,
                        @VehicleLongitude = this.VehicleLongitude,
                        @ExpirationDate = this.ExpirationDate,
                        @StickerSessionId = this.StickerSessionId,
                        @OwnerUserId = this.OwnerUserId
                    }).FirstOrDefault().Id);
            }
            else
            {
                SqlMapper.ExecuteSP("StickersUpdateById",
                    new
                    {
                        @StickerId = this.Id,
                        @StatusId = this.StatusId,
                        @CompanyId = this.CompanyId,
                        @AccountId = this.AccountId,
                        @CustomNumber = this.CustomNumber,
                        @Notes = this.Notes,
                        @ColorId = this.ColorId,
                        @BodyTypeId = this.BodyTypeId,
                        @Make = this.VehicleMake,
                        @Model = this.VehicleModel,
                        @VIN = this.VehicleVIN,
                        @ModelYear = this.VehicleYear,
                        @LicenseNumber = this.LicenseNumber,
                        @LicenseState = this.LicenseState,
                        @DispatchEntryId = this.DispatchEntryId,
                        @GracePeriodExpirationDate = this.GracePeriodExpirationDate,
                        @GracePeriodType = (int?)this.GracePeriodType,
                        @ExtendedExpirationDate = this.ExtendedExpirationDate,
                        @AuthorizationDate = this.AuthorizationDate,
                        @AuthorizationUserId = this.AuthorizationUserId,
                        @AuthorizationUserSignatureId = this.AuthorizationUserSignatureId,
                        @ResolvedDate = this.ResolvedDate,
                        @ResolvedUserId = this.ResolvedUserId,
                        @RejectedDate = this.RejectedDate,
                        @RejectedUserId = this.RejectedUserId,
                        @VehicleLocation = this.VehicleLocation,
                        @VehicleLatitude = this.VehicleLatitude ?? 0,
                        @VehicleLongitude = this.VehicleLongitude ?? 0,
                        @ExpirationDate = this.ExpirationDate,
                        @StickerSessionId = this.StickerSessionId,
                        @OwnerUserId = this.OwnerUserId
                    });

                SqlMapper.ExecuteSP("StickerReasonLinksDelete",
                    new
                    {
                        @StickerId = this.Id
                    });

                InvalidateCache(this.Id);
            }

            foreach (int i in this.Reasons)
                SqlMapper.ExecuteSP("StickerReasonLinksInsert",
                new
                {
                    @StickerId = this.Id,
                    @StickerReasonId = i
                });

            if (reqId == 0)
            {
                await AddStatusEvent(towable ? StickerStatus.Towable.Id : approved && this.AuthorizationRequired > 0 ? StickerStatus.Approved.Id : expired ? StickerStatus.Expired.Id : StickerStatus.Waiting.Id);
            }

            if(sendPusher)
                await PushNotificationProvider.UpdateSticker(CompanyId, Id);

        }

        public async Task AddStatusEvent(int statusId)
        {
            await AddStatusEvent(statusId, this.OwnerUserId);
        }

        public async Task AddStatusEvent(int statusId, int ownerUserId)
        {
            await AddStatusEvent(statusId, ownerUserId, true);
        }

        public async Task AddStatusEvent(int statusId, int ownerUserId, bool sendUpdate = true)
        {
            SqlMapper.ExecuteSP("StickerStatusEventsInsert",
                new
                {
                    @StickerId = this.Id,
                    @StickerStatusId = statusId,
                    @UserId = ownerUserId
                });

            if (sendUpdate)
                await PushNotificationProvider.UpdateSticker(CompanyId, Id, "status");
        }

        public async Task Delete()
        {
            SqlMapper.ExecuteSP("dbo.StickersDeleteById",
                new { @StickerId = this.Id });

            await PushNotificationProvider.UpdateSticker(CompanyId, Id, "delete");
        }

        public static async Task RecordAsTowed(Sticker s, int userId)
        {
            s.StatusId = StickerStatus.Towed.Id;
            s.StatusName = StickerStatus.Towed.Name;
            await s.Save(false);

            await s.AddStatusEvent(StickerStatus.Towed.Id, userId, true);
        }

        public static async Task RecordAsTowable(Sticker s, int userId)
        {
            // Allow the change back to towable only if the sticker's current status is towed
            // This will allow the change of statuse when a call is uncompleted.
            if (s.StatusId == StickerStatus.Towed.Id)
            {
                s.StatusId = StickerStatus.Towable.Id;
                s.StatusName = StickerStatus.Towable.Name;
                await s.Save(false);

                await s.AddStatusEvent(StickerStatus.Towable.Id, userId, true);
            }
        }

        public static void InvalidateCache(int id)
        {
            AppServices.Cache.InvalidateCacheItem(String.Format(cacheFormat, id));
        }

        private static IEnumerable<Sticker> Map(IEnumerable<dynamic> list)
        {
            return list.Select(o => new Sticker()
            {
                Id = o.StickerId,
                CompanyId = o.CompanyId,
                AccountId = o.AccountId,
                CustomNumber = o.CustomNumber,
                StickerNumber = o.StickerNumber,
                CreateDate = o.CreateDate,
                Notes = o.Notes,
                ColorId = o.ColorId == null ? 0 : o.ColorId,
                BodyTypeId = o.BodyTypeId == null ? 0 : o.BodyTypeId,
                VehicleMake = o.Make,
                VehicleModel = o.Model,
                VehicleVIN = o.VIN,
                VehicleYear = o.ModelYear == null ? 0 : o.ModelYear,
                LicenseNumber = o.LicenseNumber,
                LicenseState = o.LicenseState,
                StatusId = o.StatusId,
                StatusName = o.StatusName,
                StatusColor = o.StatusColor,
                DispatchEntryId = o.DispatchEntryId,
                AuthorizationDate = o.AuthorizationDate,
                AuthorizationUserId = o.AuthorizationUserId,
                AuthorizationUserSignatureId = o.AuthorizationUserSignatureId,
                GracePeriodExpirationDate = o.GracePeriodExpirationDate,
                GracePeriodType = (GracePeriodStartFromType?)o.GracePeriodType,
                ExtendedExpirationDate = o.ExtendedExpirationDate,
                RejectedDate = o.RejectedDate,
                RejectedUserId = o.RejectedUserId,
                ResolvedDate = o.ResolvedDate,
                ResolvedUserId = o.ResolvedUserId,
                VehicleLocation = o.VehicleLocation,
                VehicleLatitude = o.VehicleLatitude,
                VehicleLongitude = o.VehicleLongitude,
                ExpirationDate = o.ExpirationDate,
                StickerSessionId = o.StickerSessionId ?? 0,
                OwnerUserId = o.OwnerUserId ?? 0
            }).ToCollection();
        }

        public override string ToString()
        {
            return this.ToJson();
        }

        public static void RecordApprovalsSent(int accountId, int[] stickers, int destinationUserId)
        {
            foreach (var x in stickers)
            {
                using (var r = Core.GetConnection())
                {
                    r.Execute("INSERT INTO StickerApprovalNotifications (StickerId,AccountId, SentToUserId) VALUES (@SID, @AID, @UID)", new
                    {
                        SID = x,
                        AID = accountId,
                        UID = destinationUserId
                    });
                }
            }
        }
    }

    [ProtoContract]
    public class StickerCollection
    {
        [ProtoMember(1)]
        public Collection<Sticker> Collection { get; set; }

        public StickerCollection() { }

        public StickerCollection(IEnumerable<Sticker> list)
        {
            Collection = list.ToCollection();
        }
    }
}

