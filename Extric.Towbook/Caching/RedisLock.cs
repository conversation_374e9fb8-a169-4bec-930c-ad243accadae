using StackExchange.Redis;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Extric.Towbook.Utility
{
    public static class RedisExtensions
    {
        public static async Task<DataCacheLock> AcquireLockAsync(this IDatabaseAsync db, string key, TimeSpan? expiry = null, TimeSpan? retryTimeout = null)
        {
            if (db == null)
                throw new ArgumentNullException("db");

            if (key == null)
                throw new ArgumentNullException("key");

            return await DataCacheLock.AcquireAsync(db, key, expiry, retryTimeout);
        }
        public class DataCacheLock : IDisposable
        {
            private StackExchange.Redis.IDatabaseAsync _db;
            public readonly RedisKey Key;
            public readonly RedisValue Value;
            public readonly TimeSpan? Expiry;

            private DataCacheLock(IDatabaseAsync db, string key, TimeSpan? expiry)
            {
                _db = db;
                Key = "ninlock:" + key;
                Value = Core.GetCoreVersion() + "_" + Environment.MachineName + "_" + Guid.NewGuid().ToString("N");
                Expiry = expiry;
            }

            public static async Task<DataCacheLock> AcquireAsync(IDatabaseAsync db, string key, TimeSpan? expiry, TimeSpan? retryTimeout)
            {
                if (expiry == null)
                    expiry = TimeSpan.FromSeconds(10);

                var dataCacheLock = new DataCacheLock(db, key, expiry);
                Console.WriteLine(dataCacheLock.Key.ToString() + ":" + dataCacheLock.Value.ToString());
                
                async Task<bool> task()
                {
                    try
                    {
                        return await dataCacheLock._db.LockTakeAsync(dataCacheLock.Key, dataCacheLock.Value, dataCacheLock.Expiry ?? TimeSpan.MaxValue);
                    }
                    catch
                    {
                        return false;
                    }
                }

                await RetryUntilTrueAsync(task, retryTimeout);

                return dataCacheLock;
            }

            public async Task<bool> AskForMoreTime(TimeSpan extension)
            {
                return await _db.LockExtendAsync(Key, Value, extension);
            }

            public void Dispose()
            {
                Console.WriteLine("release the lock:" + Value);
                _db.LockReleaseAsync(Key, Value);
            }

        }

        private static readonly Random _random = new Random();

        public static async Task<bool> RetryUntilTrueAsync(Func<Task<bool>> task, TimeSpan? retryTimeout)
        {
            int i = 0;
            DateTime utcNow = DateTime.UtcNow;

            while (!retryTimeout.HasValue || DateTime.UtcNow - utcNow < retryTimeout.Value)
            {
                i++;
                if (await task())
                {
                    return true;
                }
                var waitFor = _random.Next((int)Math.Pow(i, 2), (int)Math.Pow(i + 1, 2) + 1);
                Console.WriteLine(waitFor);
                
                await Task.Delay(waitFor);
            }
           
            
            throw new TimeoutException($"Exceeded timeout of {retryTimeout.Value}");
        }
    }
}
