using System;
using System.Linq;
using System.Threading.Tasks;
using Extric.Towbook.Utility;
using Newtonsoft.Json;

namespace Extric.Towbook.Commissions
{
    [Table("CommissionDriverReportOptions")]
    public class CommissionDriverReportOptions
    {
        [Key("CommissionDriverReportOptionId")]
        public int Id { get; set; }
        public int CompanyId { get; set; }
        // For PDF export (NOT FOR CSV EXPORT)
        public bool ShowDriverBaseExport { get; set; }
        public bool ShowCallTotalExport { get; set; }
        // Not currently used
        public bool ShowTruckExport { get; set; }
        public bool ShowPONumberExport { get; set; }
        public bool ShowWeightClassExport { get; set; }
        // Not currently used
        public bool ShowDriversLicenseNumExport { get; set; }
        public bool HideAllButCommissionExport { get; set; }
        // For web report
        public bool ShowDriverBaseWeb { get; set; }
        public bool ShowCallTotalWeb { get; set; }
        public bool ShowTruckWeb { get; set; }
        public bool ShowPONumberWeb { get; set; }
        public bool ShowWeightClassWeb { get; set; }
        public bool ShowDriversLicenseNumWeb { get; set; }
        public bool HideAllButCommissionWeb { get; set; }
        public int OwnerUserId { get; set; }

        [Write(false)]
        public DateTime CreateDate { get; private set; }

        private static CommissionDriverReportOptions Map(dynamic sqlRes)
        {
            return new CommissionDriverReportOptions() 
            {
                Id = sqlRes.CommissionDriverReportOptionId,
                CompanyId = sqlRes.CompanyId,
                ShowDriverBaseExport = sqlRes.ShowDriverBaseExport,
                ShowCallTotalExport = sqlRes.ShowCallTotalExport,
                ShowTruckExport = sqlRes.ShowTruckExport,
                ShowPONumberExport = sqlRes.ShowPONumberExport,
                ShowWeightClassExport = sqlRes.ShowWeightClassExport,
                ShowDriversLicenseNumExport = sqlRes.ShowDriversLicenseNumExport,
                HideAllButCommissionExport = sqlRes.HideAllButCommissionExport,
                ShowDriverBaseWeb = sqlRes.ShowDriverBaseWeb,
                ShowCallTotalWeb = sqlRes.ShowCallTotalWeb,
                ShowTruckWeb = sqlRes.ShowTruckWeb,
                ShowPONumberWeb = sqlRes.ShowPONumberWeb,
                ShowWeightClassWeb = sqlRes.ShowWeightClassWeb,
                ShowDriversLicenseNumWeb = sqlRes.ShowDriversLicenseNumWeb,
                HideAllButCommissionWeb = sqlRes.HideAllButCommissionWeb,
                OwnerUserId = sqlRes.OwnerUserId,
                CreateDate = sqlRes.CreateDate,
            };
        }

        public async Task Save(int currentUserId)
        {
            if (Id < 1)
            {
                OwnerUserId = currentUserId;
                CreateDate = DateTime.Now;
                Id = Convert.ToInt32(await SqlMapper.InsertAsync(this));
            }
            else
            {
                await SqlMapper.UpdateAsync(this);
            }
            await Core.DeleteRedisKeyAsync(GetRedisKey(CompanyId));
        }

        private static string GetRedisKey(int companyId) => $"commissionDriverReportOptions_company:{companyId}";

        public static CommissionDriverReportOptions GetByCompanyId(int companyId)
        {
            string redisKey = GetRedisKey(companyId);
            try 
            {
                string redisVal = Core.GetRedisValue(redisKey);
                if (redisVal != null)
                {   
                    return JsonConvert.DeserializeObject<CommissionDriverReportOptions>(redisVal);
                }
            } catch { }

            var commissionOptions = SqlMapper.QuerySP<CommissionDriverReportOptions>("CommissionDriverReportOptionsGetByCompanyId",
                new { CompanyId = companyId }).FirstOrDefault() ?? GetDefaults(companyId);

            Core.SetRedisValue(redisKey, JsonConvert.SerializeObject(commissionOptions));
            return commissionOptions;
        }

        public static async Task<CommissionDriverReportOptions> GetByCompanyIdAsync(int companyId)
        {
            string redisKey = GetRedisKey(companyId);
            try 
            {
                string redisVal = await Core.GetRedisValueAsync(redisKey);
                if (redisVal != null)
                {
                    return JsonConvert.DeserializeObject<CommissionDriverReportOptions>(redisVal);
                }
            } catch { }

            dynamic sqlRes = (await SqlMapper.QuerySpAsync<dynamic>(
                "CommissionDriverReportOptionsGetByCompanyId",
                new { CompanyId = companyId })).FirstOrDefault();

            var commissionOptions = sqlRes != null ? Map(sqlRes) : GetDefaults(companyId);

            await Core.SetRedisValueAsync(redisKey, JsonConvert.SerializeObject(commissionOptions));
            return commissionOptions;
        }

        public static async Task<CommissionDriverReportOptions> GetById(int id)
        {
            dynamic sqlRes = (await SqlMapper.QuerySpAsync<dynamic>("CommissionDriverReportOptionsGetById",
                new { Id = id })).FirstOrDefault();
            return sqlRes != null ? Map(sqlRes) : null;
        }

        private static CommissionDriverReportOptions GetDefaults(int companyId)
        {
            var company = Company.Company.GetById(companyId);

            // Added 9/2/2016 - AC
            // For companies with Editable Commissions feature, define Towbook default as...
            // show DriverBase, hide invoice total and show Purchase order number
            bool newCompanyWithEditableCommissions = company.HasFeature(Generated.Features.EditableCommissions) && companyId > 10000;

            return new CommissionDriverReportOptions()
            {
                Id = 0,
                CompanyId = companyId,
                ShowDriverBaseExport = newCompanyWithEditableCommissions,
                ShowCallTotalExport = !newCompanyWithEditableCommissions,
                ShowTruckExport = false,
                ShowPONumberExport = newCompanyWithEditableCommissions,
                ShowWeightClassExport = false,
                ShowDriversLicenseNumExport = false,
                HideAllButCommissionExport = false,
                ShowDriverBaseWeb = false,
                ShowCallTotalWeb = true,
                ShowTruckWeb = false,
                ShowPONumberWeb = false,
                ShowWeightClassWeb = false,
                ShowDriversLicenseNumWeb = false,
                HideAllButCommissionWeb = false
            };
        }
    }
}
