using Extric.Towbook.Utility;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using ProtoBuf;
using System.Threading.Tasks;

namespace Extric.Towbook.SquareIntegration
{
    [Table("Integration.SquareCompanyLocations")]
    public class SquareCompanyLocation
    {
        [Key("CompanyLocationId")]
        public int Id { get; set; }
        public int CompanyId { get; set; }
        public int AuthorizationId { get; set; }
        public string LocationId { get; set; }
        
        
        private SquareCompanyAuthorization _authorization;
        
        internal void SetAuthorization(SquareCompanyAuthorization authorization)
        {
            _authorization = authorization;
        }

        public async Task<SquareCompanyAuthorization> Authorization()
        {
            if (_authorization != null) 
                return _authorization;

            if (Id > 0)
            {
                _authorization = await SquareCompanyAuthorization.GetByCompanyId(Id);
            }

            if (_authorization != null)
                return _authorization;

            _authorization = new SquareCompanyAuthorization();

            return _authorization;
        }  
        
        public SquareCompanyLocation()
        {
            Id = 0;
        }
        
        public SquareCompanyLocation(int id, string locationId)
        {
            Id = id;
            LocationId = locationId;
        }

        public SquareCompanyLocation Save()
        {
            if (Id < 1)
                Id = (int)SqlMapper.Insert(this);
            else
                SqlMapper.Update(this);

            return this;
        }

        public static SquareCompanyLocation GetByAuthorizationId(int authorizationId, int companyId)
        {
            return (SqlMapper.Query<SquareCompanyLocation>(
                "SELECT * FROM Integration.SquareCompanyLocations WHERE AuthorizationId=@AuthorizationId AND CompanyId=@CompanyId",    
                new { AuthorizationId = authorizationId, CompanyId = companyId })).FirstOrDefault();
        }
        
        public static List<SquareCompanyLocation> GetAllByAuthorizationId(int authorizationId)
        {
            return SqlMapper.Query<SquareCompanyLocation>(
                "SELECT * FROM Integration.SquareCompanyLocations WHERE AuthorizationId=@AuthorizationId",
                new {AuthorizationId = authorizationId}).ToList();
        }

        public static SquareCompanyLocation GetByLocationId(string locationId)
        {
            return SqlMapper.Query<SquareCompanyLocation>(
                "SELECT * FROM Integration.SquareCompanyLocations WHERE LocationId=@LocationId",
                new { LocationId = locationId }).FirstOrDefault();
        }
    }
}
