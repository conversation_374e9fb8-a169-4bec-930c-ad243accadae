namespace Extric.Towbook.SquareIntegration
{
    /// <summary>
    /// Square Payment Statues
    /// 
    /// Doc Ref: https://github.com/square/square-dotnet-sdk/blob/master/doc/models/payment.md#fields
    /// </summary>
    public class SquarePaymentStatus
    {
        public static readonly string Approved = "APPROVED";
        public static readonly string Completed = "COMPLETED";
        public static readonly string Canceled = "CANCELED";
        public static readonly string Failed = "FAILED";
    }
}