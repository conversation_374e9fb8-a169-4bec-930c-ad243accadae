namespace Extric.Towbook.SquareIntegration
{
    /// <summary>
    ///   Square Refund Statuses
    ///   Doc Ref: https://github.com/square/square-dotnet-sdk/blob/master/doc/models/payment-refund.md#payment-refund
    /// </summary>
    public class SquareRefundStatus
    {
        public static readonly string Approved = "PENDING";
        public static readonly string Completed = "COMPLETED";
        public static readonly string Rejected = "REJECTED";
        public static readonly string Failed = "FAILED";
    }
}