using Extric.Towbook.Utility;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;

namespace Extric.Towbook.SquareIntegration
{
    [Table("Integration.SquareRefunds")]
    public class SquareRefund
    {
        [Key("SquareRefundId")]
        public int Id { get; set; }
        public int CompanyId { get; set; }
        public string PaymentId { get; set; }
        public string RefundId { get; set; }
        public string MerchantId { get; set; }
        public string LocationId { get; set; }
        public string OrderId { get; set; }
        public string Status { get; set; }
        public double Amount { get; set; }
        public string Currency { get; set; }
        public DateTime? RefundCreatedAt { get; set; }
        public DateTime? RefundUpdatedAt { get; set; }
        
        public DateTime? ProcessingFeeEffectiveAt { get; set; }
        public string ProcessingFeeType { get; set; }
        public double ProcessingFeeAmount { get; set; }
        public string ProcessingFeeCurrency { get; set; }
        
        public DateTime CreateDate { get; set; }
        public DateTime? UpdateDate { get; set; }
        
        public int InvoiceId { get; set; }
        
        public int InvoicePaymentId { get; set; }
        
        public int PaymentLinkId { get; set; }

        public string Reason { get; set; }
        public double TipAmount { get; set; }

        public SquareRefund Save()
        {
            if (Id < 1)
            {
                CreateDate = DateTime.Now;
                Id = (int) SqlMapper.Insert(this);
            }
            else
            {
                UpdateDate = DateTime.Now;
                SqlMapper.Update(this);   
            }

            return this;
        }

        public static SquareRefund GetByInvoicePaymentId(int invoicePaymentId)
        {
            return SqlMapper.Query<SquareRefund>("SELECT * FROM Integration.SquareRefunds WHERE InvoicePaymentId=@InvoicePaymentId",    
                new { InvoicePaymentId = invoicePaymentId }).FirstOrDefault();
        }
        
        public static SquareRefund GetByRefundId(string refundId)
        {
            return SqlMapper.Query<SquareRefund>("SELECT * FROM Integration.SquareRefunds WHERE RefundId=@RefundId",    
                new { RefundId = refundId }).FirstOrDefault();
        }

        public static SquareRefund GetByPaymentId(string paymentId)
        {
            return SqlMapper.Query<SquareRefund>("SELECT * FROM Integration.SquareRefunds WHERE PaymentId=@PaymentId",    
                new { PaymentId = paymentId }).FirstOrDefault();
        }
        
        public static IEnumerable<SquareRefund> GetAll(List<int> companies, string searchKey, int pageNumber, int pageSize)
        {
            var searchFilter = "";

            if (!string.IsNullOrEmpty(searchKey))
            {
                var searchKeyInt = -1;
                
                if (Int32.TryParse(searchKey, out var invoicePaymentId))
                    searchKeyInt = invoicePaymentId;

                searchFilter = $"AND (RefundId = @SearchKey OR PaymentId = @SearchKey OR InvoicePaymentId = {searchKeyInt})";
            }
            
            string sql = $@"
                SELECT *
                FROM Integration.SquareRefunds 
                WHERE CompanyId IN @Companies {searchFilter}
                ORDER BY UpdateDate DESC
                OFFSET (@PageNumber - 1) * @PageNumber ROWS FETCH NEXT @PageSize ROWS ONLY;";
            
            return SqlMapper.Query<SquareRefund>(sql, new
            {
                Companies = companies,
                PageSize = pageSize,
                PageNumber = pageNumber,
                SearchKey = searchKey
            });
        }
    }
}
