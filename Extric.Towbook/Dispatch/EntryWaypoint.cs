using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using Extric.Towbook.Integration;
using Extric.Towbook.Utility;
using Glav.CacheAdapter.Core.DependencyInjection;
using NewRelic.Api.Agent;
using ProtoBuf;

namespace Extric.Towbook.Dispatch
{

    public interface IEntryWaypoint
    {
        int Id { get; }
        string Title { get; }
        int Position { get; }
        DateTime? ArrivalTime { get; }
        DateTime? EnrouteTime  { get; }
    }

    [ProtoContract(ImplicitFields = ImplicitFields.AllFields, Name = "Waypoint")]
    [DebuggerDisplay("Id={Id}, Position={Position}, DispatchEntryId={DispatchEntryId}, Address={address}, Lat/Long={Latitude},{Longitude}")]
    public class EntryWaypoint : IEntryWaypoint
    {
        private const string cacheFormat = "s-nt-dewp-{0}";
        private const int CacheTimeout = 1440;

        private int position;
        private string title;
        private decimal latitude;
        private decimal longitude;
        private string notes;
        private int ownerUserId;
        private string address;

        private bool hasToll;
        private DateTime? enrouteTime;
        private DateTime? arrivalTime;

        public int Id { get; set; }
        public int DispatchEntryId { get; set; }
        public DateTime CreateDate { get; set; }

        public int Position
        {
            get { return position; }
            set { SetField(ref position, value, "Position"); }
        }

        public string Title
        {
            get { return title; }
            set { SetField(ref title, value, "Title"); }
        }

        /// <summary>
        /// When setting the address, lat/long will be set to 0. 
        /// </summary>
        public string Address
        {
            get => address;
            set
            {
                if (SetField(ref address, value, "Address"))
                {
                    if (ChangedFields.All(o => o.Field != "Latitude"))
                        Latitude = 0;

                    if (ChangedFields.All(o => o.Field != "Longitude"))
                        Longitude = 0;
                }
            }
        }

        public bool HasToll
        {
            get => hasToll;
            set => SetField(ref hasToll, value, "HasToll");
        }

        public decimal Latitude
        {
            get => latitude;
            set => SetField(ref latitude, value, "Latitude");
        }

        public decimal Longitude
        {
            get => longitude;
            set => SetField(ref longitude, value, "Longitude");
        }

        public string Notes
        {
            get => notes;
            set => SetField(ref notes, value, "Notes");
        }

        public int OwnerUserId
        {
            get => ownerUserId;
            set => SetField(ref ownerUserId, value, "OwnerUserId");
        }

        public DateTime? EnrouteTime
        {
            get => enrouteTime;
            set => SetField(ref enrouteTime, value, "EnrouteTime");
        }

        public DateTime? ArrivalTime
        {
            get => arrivalTime;
            set => SetField(ref arrivalTime, value, "ArrivalTime");
        }

        [ProtoIgnore]
        public List<LoggedFieldChange> ChangedFields { get; private set; }
        public bool IsDirty { get; private set; }

        public void MarkAsDirty()
        {
            IsDirty = true;
        }

        protected bool SetField<T>(ref T field, T value, string propertyName)
        {
            if (!EqualityComparer<T>.Default.Equals(field, value))
            {
                if (ChangedFields == null)
                    ChangedFields = new List<LoggedFieldChange>();

                ChangedFields.Add(new LoggedFieldChange(propertyName, field != null ? field.ToString() : "null", value != null ? value.ToString() : "null"));

                field = value;
                IsDirty = true;

                return true;
            }
            return false;
        }

        public static IEnumerable<EntryWaypoint> Map(IEnumerable<dynamic> list)
        {
            return list.Select(o =>
            {
                var wp = new EntryWaypoint
                {
                    Id = o.WaypointId,
                    Position = o.Position,
                    Title = o.Title,
                    Address = o.Address,
                    Latitude = o.Latitude,
                    Longitude = o.Longitude,
                    DispatchEntryId = o.DispatchEntryId,
                    OwnerUserId = o.OwnerUserId,
                    CreateDate = o.CreateDate,
                    EnrouteTime = o.EnrouteTime,
                    ArrivalTime = o.ArrivalTime,
                    HasToll = o.HasToll ?? false
                };

                wp.ChangedFields.Clear();
                wp.IsDirty = false;

                return wp;

            }).ToCollection();
        }

        public static EntryWaypoint GetById(int id)
        {
            return Map(SqlMapper.QuerySP<dynamic>("dbo.DispatchEntryWaypointsGetById",
                new
                {
                    WaypointId = id
                })).FirstOrDefault();
        }


        /// <summary>
        /// Get all notes for the specified Call.
        /// </summary>
        /// <param name="dispatchEntryId"></param>
        /// <returns></returns>
        public static IEnumerable<EntryWaypoint> GetByDispatchEntryId(int dispatchEntryId)
        {
            return Map(SqlMapper.QuerySP<dynamic>("dbo.DispatchEntryWaypointsGetByDispatchEntryId", new
            {
                DispatchEntryId = dispatchEntryId
            }));
        }

        public static async Task<IEnumerable<EntryWaypoint>> GetByDispatchEntryIdAsync(int dispatchEntryId)
        {
            return Map(await SqlMapper.QuerySpAsync<dynamic>("dbo.DispatchEntryWaypointsGetByDispatchEntryId", new
            {
                DispatchEntryId = dispatchEntryId
            }));
        }

        public static void InvalidateCache(int id)
        {
            AppServices.Cache.InvalidateCacheItem(string.Format(cacheFormat, id));
        }

        public void Save(int userId)
        {
            if (!IsDirty)
            {
                return;
            }

            if (Id < 1)
            {
                Id = Convert.ToInt32(SqlMapper.QuerySP<dynamic>("DispatchEntryWaypointsInsert",
                    new
                    {
                        DispatchEntryId,
                        Position,
                        Title,
                        Address,
                        Latitude,
                        Longitude,
                        OwnerUserId = userId,
                        EnrouteTime,
                        ArrivalTime,
                        HasToll
                    }).First().Id);
            }
            else
            {
                SqlMapper.ExecuteSP("DispatchEntryWaypointsUpdateById",
                    new
                    {
                        WaypointId = Id,
                        DispatchEntryId,
                        Position,
                        Title,
                        Address,
                        Latitude,
                        Longitude,
                        OwnerUserId = userId,
                        EnrouteTime,
                        ArrivalTime,
                        HasToll
                    });
            }

            InvalidateCache(Id);
            IsDirty = false;
        }

        public void Delete()
        {
            if (Id < 1)
                return;

            SqlMapper.ExecuteSP("dbo.DispatchEntryWaypointsDeleteById",
                new { WaypointId = Id });

            InvalidateCache(Id);
        }

        [Trace]
        public static IEnumerable<EntryWaypoint> GetByDispatchEntryIds(IEnumerable<int> list)
        {
            if (list == null || !list.Any())
                return Array.Empty<EntryWaypoint>();

            var rv = new List<EntryWaypoint>();
            const string sql = "SELECT WaypointId, DispatchEntryId, Position, Title, Address, Latitude, Longitude, OwnerUserId, CreateDate, EnrouteTime, ArrivalTime, HasToll  " +
                " FROM DispatchEntryWaypoints w WITH(nolock) WHERE DispatchEntryId IN @DispatchEntryIds";

            foreach (var batch in list.OrderBy(o => o).Batch(250))
            {
                rv.AddRange(Map(SqlMapper.Query<dynamic>(sql, new
                {
                    DispatchEntryIds = batch
                })));
            }

            return rv;
        }

        [Trace]
        public static async Task<IEnumerable<EntryWaypoint>> GetByDispatchEntryIdsAsync(IEnumerable<int> list)
        {
            if (list == null || !list.Any())
                return Array.Empty<EntryWaypoint>();

            var rv = new List<EntryWaypoint>();
            const string sql = "SELECT WaypointId, DispatchEntryId, Position, Title, Address, Latitude, Longitude, OwnerUserId, CreateDate, EnrouteTime, ArrivalTime, HasToll  " +
                " FROM DispatchEntryWaypoints w WITH(nolock) WHERE DispatchEntryId IN @DispatchEntryIds";

            foreach (var batch in list.OrderBy(o => o).Batch(250))
            {
                rv.AddRange(Map(await SqlMapper.QueryAsync<dynamic>(sql, new
                {
                    DispatchEntryIds = batch
                })));
            }

            return rv;
        }
    }

    public class PushCallAddressUpdate
    {
        public int CallId { get; set; }
        public int CompanyId { get; set; }
        public string TowSource { get; set; }
        public string TowDestination { get; set; }
        public EntryWaypoint PickupWaypoint { get; set; }
        public EntryWaypoint DropoffWaypoint { get; set; }

        public PushCallAddressUpdate(Entry entry)
        {
            CallId = entry.Id;
            CompanyId = entry.CompanyId;
        }

        public async Task Push()
        {
             await PushNotificationProvider.Push(
                "private-TWBK_Client_" + CompanyId,
                "call_address_update",
                this, true, true);
        }
    }
}
