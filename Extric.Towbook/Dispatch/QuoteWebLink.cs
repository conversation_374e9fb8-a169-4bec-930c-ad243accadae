using Extric.Towbook.Utility;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace Extric.Towbook.Dispatch
{
    [Table("QuoteWebLinks")]
    public class QuoteWebLink
    {
        [Key("QuoteWebLinkId")]
        public int Id { get; set; }
        public string QuoteId { get; set; }
        public int CompanyId { get; set; }
        public string UrlHash { get; set; }
        public DateTime CreateDate { get; set; }
        public int OwnerUserId { get; set; }
        public int EmailTransactionId { get; set; }

        [Write(false)]
        public string TinyUrl
        {
            get
            {
                return "https://twbk.co/q/" + UrlHash;
            }
            private set { }
        }

        public static async Task<QuoteWebLink> GetByUrlHash(string hash)
        {
            return (await SqlMapper.QueryAsync<QuoteWebLink>(
                @"SELECT * FROM QuoteWebLinks WITH (NOLOCK) WHERE UrlHash = @Hash",
                new { Hash = hash })).FirstOrDefault();
        }

        public async Task Save()
        {
            if (Id <= 0)
                Id = (int)await SqlMapper.InsertAsync(this);
            else
                await SqlMapper.UpdateAsync(this);
        }
    }
}
