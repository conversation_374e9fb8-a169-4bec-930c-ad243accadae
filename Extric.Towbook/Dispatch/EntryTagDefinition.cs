using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Extric.Towbook.Utility;

namespace Extric.Towbook.Dispatch
{
    public class EntryTagDefinition
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string HtmlColor { get; set; }
        public int? CompanyId { get; set; }

        /// <summary>
        /// returns all tags for the specified company + built-in tags.
        /// </summary>
        /// <param name="companyId"></param>
        /// <returns></returns>
        public static IEnumerable<EntryTagDefinition> GetByCompanyId(int companyId)
        {
            return SqlMapper.QuerySP<dynamic>("dbo.DispatchEntryTagDefinitionsGetByCompanyId", new
            {
                @CompanyId = companyId
            }).Select<dynamic, EntryTagDefinition>(o => Map(o));
        }

        public static EntryTagDefinition Map(dynamic o)
        {
            return new EntryTagDefinition()
            {
                Id = o.TagId,
                Name = o.Name,
                Description = o.Description,
                CompanyId = o.CompanyId
            };
        }

        public void Save()
        {
            if (Id > 0 && CompanyId == 0)
                throw new TowbookException("Cannot save built-in tags.");

            if (this.Id < 1)
            {
                this.Id = Convert.ToInt32(SqlMapper.QuerySP<dynamic>("EntryTagDefinitionsInsert",
                    new
                    {
                        CompanyId = this.CompanyId,
                        Name = this.Name,
                        Description = this.Description,
                        HtmlColor = this.HtmlColor
                        
                    }).FirstOrDefault().Id);
            }
            else
            {
                SqlMapper.ExecuteSP("EntryTagDefinitionsUpdateById",
                    new
                    {
                        TagId = this.Id,
                        CompanyId = this.CompanyId,
                        Name = this.Name,
                        Description = this.Description,
                        HtmlColor = this.HtmlColor
                    });
            }
        }
    }
}
