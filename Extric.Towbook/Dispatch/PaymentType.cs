using Extric.Towbook.Utility;
using Glav.CacheAdapter.Core.DependencyInjection;
using ProtoBuf;
using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using Extric.Towbook.Caching;

namespace Extric.Towbook.Dispatch
{

    [ProtoContract]
    [Table("PaymentTypes")]
	public class PaymentType
    {
		public static readonly PaymentType Cash = new PaymentType(0, "Cash");
		public static readonly PaymentType Check = new PaymentType(1, "Check");
		public static readonly PaymentType Visa = new PaymentType(2, "Visa");
		public static readonly PaymentType MasterCard = new PaymentType(3, "MasterCard");
		public static readonly PaymentType Discover = new PaymentType(4, "Discover");
		public static readonly PaymentType AmericanExpress = new PaymentType(5, "AmericanExpress");
		public static readonly PaymentType Other = new PaymentType(6, "Other");
		public static readonly PaymentType Debit = new PaymentType(7, "Debit");
		public static readonly PaymentType Account = new PaymentType(8, "Account");
		public static readonly PaymentType EFT = new PaymentType(10, "EFT");
		public static readonly PaymentType UncollectableBadDebt = new PaymentType(11, "UncollectableBadDebt");
        public static readonly PaymentType RoadSync = new PaymentType(98, "RoadSync");
        public static readonly PaymentType CreditCard = new PaymentType(99, "CreditCard");
        public static readonly PaymentType Square = new PaymentType(100, "Square");
        

        [ProtoMember(1)]
        [Key("PaymentTypeId")]
        public int Id { get; private set; }
        [ProtoMember(2)]
        public string Name { get; set; }
        [ProtoMember(3)]
        public int CompanyId { get; set; }
        [ProtoMember(4)]
        public DateTime CreateDate { get; private set; }
        [ProtoMember(5)]
        public int OwnerUserId { get; set; }
        [ProtoMember(6)]
        public bool IsActive { get; set; }


		public PaymentType() { }
        private PaymentType (int id, string name)
        {
            this.Id = id;
            this.Name = name;
            this.CompanyId = 1;
        }

        public override string ToString()
        {
            return Name;
        }

        public override int GetHashCode()
        {
            return Id.GetHashCode();
        }

        public static implicit operator int(PaymentType type)
        {
            return type?.Id ?? 0;
        }

        public static explicit operator PaymentType(int v)
        {
            return GetById(v);
        }

		public static PaymentType GetById(int id)
        {
            return AppServices.Cache.Get("pt:" + id,
                DateTime.Now.AddHours(24), 
                () => SqlMapper.Query<PaymentType>(
                    "SELECT * FROM PaymentTypes WHERE PaymentTypeId=@Id", new { Id = id }).FirstOrDefault());
        }
        public static Collection<PaymentType> GetByCompanyId(int companyId, bool includeHidden = false)
        {
            var rv = SqlMapper.Query<PaymentType>(
                "SELECT * FROM PaymentTypes WHERE CompanyId IS NULL OR CompanyId=@Id " +
                "UNION " +
                "SELECT PT.* FROM PaymentTypes PT " +
                "INNER JOIN CompaniesShared CS on " +
                "(CS.CompanyId = @Id OR CS.SharedCompanyId = @Id) AND PT.CompanyId = CS.CompanyId", new { Id = companyId })
                .ToCollection();

            if (!includeHidden)
                rv = rv.Where(o => o.Id != 99 && o.Id != 11 && o.Id != 8).ToCollection();

			return rv;
        }

		public async Task<PaymentType> Save()
		{
			if (Id < 1)
			{
				this.CreateDate = DateTime.Now;
				this.IsActive = true;

				Id = (int)await SqlMapper.InsertAsync(this);
			}
			else
			{
				// Don't allow required payment types to be updated
				if (CompanyId <= 1)
					return this;


				await SqlMapper.UpdateAsync(this);
                AppServices.Cache.InvalidateCacheItem("pt:" + this.Id);
            }
            await CacheWorkerUtility.UpdatePaymentType(this);

			return this;
		}

		public async Task<PaymentType> Delete()
		{
			IsActive = false;
			return await Save();
		}

		public override bool Equals(System.Object obj)
        {
            if (obj == null)
            {
                return false;
            }

            PaymentType p = obj as PaymentType;
            if ((System.Object)p == null)
            {
                return false;
            }

            return (Id == p.Id);
        }

        public bool Equals(PaymentType p)
        {
            if ((object)p == null)
            {
                return false;
            }
            
            return (Id == p.Id);
        }

        public static bool operator ==(PaymentType a, PaymentType b)
        {
            if (System.Object.ReferenceEquals(a, b))
            {
                return true;
            }

            // If one is null, but not both, return false.
            if (((object)a == null) || ((object)b == null))
            {
                return false;
            }
            
            return a.Id == b.Id;
        }


        public static bool operator !=(PaymentType a, PaymentType b)
        {
            return !(a == b);
        }
    }
}
