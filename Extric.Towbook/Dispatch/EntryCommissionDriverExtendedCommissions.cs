using Extric.Towbook.Commissions;
using Extric.Towbook.Utility;
using System;
using System.Collections.ObjectModel;
using System.Data;
using System.Linq;
using System.Threading.Tasks;

namespace Extric.Towbook.Dispatch
{
    /// <remarks>
    /// Used to manually split commission values when split commission feature is enabled
    /// </remarks>
    public class EntryCommissionDriverExtendedCommissions
    {
        public int Id { get; set; }
        public int EntryCommissionDriverId { get; set; }
        public int InvoiceItemId { get; set; }
        public decimal Amount { get; set; }
        public CommissionType Type { get; set; }
        public int OwnerUserId { get; set; }
        public DateTime CreateDate { get; set; }
        public bool Deleted { get; set; }

        public EntryCommissionDriverExtendedCommissions()
        {
        }

        public override string ToString()
        {
            return GetValue();
        }

        public string GetValue()
        {
            if (Type == CommissionType.FlatRate)
                return Amount.ToString("C");
            else if (Type == CommissionType.Percentage)
                return Amount.ToString("0.00") + "%";
            else
                return null;
        }

        internal static Collection<EntryCommissionDriverExtendedCommissions> GetByEntryCommissionDriverId(int entryCommissionDriverId)
        {
            if (entryCommissionDriverId < 0)
                throw new TowbookException("Must pass a positive ID to method.");

            return SqlMapper.QuerySP("DispatchEntryCommissionDriverExtendedCommissionsGetByEntryCommissionDriverId",
                new { @EntryCommissionDriverId = entryCommissionDriverId })
                .Select<dynamic, EntryCommissionDriverExtendedCommissions>(o => Map(o))
                .ToCollection();
        }

        public static Collection<EntryCommissionDriverExtendedCommissions> GetByDispatchEntryIds(int[] dispatchEntryIds)
        {
            Collection<EntryCommissionDriverExtendedCommissions> result = new Collection<EntryCommissionDriverExtendedCommissions>();

            foreach (var x in dispatchEntryIds.Batch(500))
            {
                result = result.Union(SqlMapper.Query<dynamic>(@"SELECT * FROM DispatchEntryCommissionDriverExtendedCommissions DC WITH (nolock)
                    INNER JOIN DispatchEntryCommissionDrivers D WITH (nolock) on D.EntryCommissionDriverId = DC.EntryCommissionDriverId
                    WHERE DC.Deleted = 0 and D.Deleted = 0 AND D.DispatchEntryId IN @CallIds",
                new { CallIds = x })
                .Select<dynamic, EntryCommissionDriverExtendedCommissions>(o => Map(o))).ToCollection();
            }

            return result;
        }

        public static EntryCommissionDriverExtendedCommissions GetByInvoiceItemId(int entryCommissionDriverId, int invoiceItemId)
        {
            if (invoiceItemId < 0 || entryCommissionDriverId < 0)
                throw new TowbookException("Must pass a positive ID to method.");

            return SqlMapper.QuerySP("DispatchEntryCommissionDriverExtendedCommissionsGetByInvoiceItemId",
                new {
                    @InvoiceItemId = invoiceItemId,
                    @EntryCommissionDriverId = entryCommissionDriverId
                })
                .Select<dynamic, EntryCommissionDriverExtendedCommissions>(o => Map(o))
                .FirstOrDefault();
        }

        public static EntryCommissionDriverExtendedCommissions Map(dynamic o)
        {
            return new EntryCommissionDriverExtendedCommissions()
            {
                Id = o.EntryCommissionDriverExtendedCommissionId,
                EntryCommissionDriverId = o.EntryCommissionDriverId,
                InvoiceItemId = o.InvoiceItemId,
                Amount = o.Amount,
                Type = (CommissionType)o.Type,
                OwnerUserId = o.OwnerUserId,
                CreateDate = o.CreateDate,
            };
        }


        public void Save(IDbTransaction transaction)
        {
            if (EntryCommissionDriverId < 1)
            {
                throw new TowbookException("Attempted to save an DriverExtendedCommission that doesn't have EntryCommissionDriverId specified.");
            }

            if (Id < 1)
            {
                this.Id = Convert.ToInt32(SqlMapper.QuerySP("DispatchEntryCommissionDriverExtendedCommissionsInsert", new
                {
                    @EntryCommissionDriverId = this.EntryCommissionDriverId,
                    @InvoiceItemId = this.InvoiceItemId,
                    @Amount = this.Amount,
                    @Type = this.Type,
                    @OwnerUserId = this.OwnerUserId
                }, transaction).First().Id);

            }
            else
            {
                SqlMapper.ExecuteSP("DispatchEntryCommissionDriverExtendedCommissionsUpdateById", new
                {
                    @EntryCommissionDriverExtendedCommissionId = this.Id,
                    @EntryCommissionDriverId = this.EntryCommissionDriverId,
                    @InvoiceItemId = this.InvoiceItemId,
                    @Amount = this.Amount,
                    @Type = this.Type,
                    @OwnerUserId = this.OwnerUserId
                }, transaction);
            }
        }

        public static void Delete(int entryCommissionDriverId, int invoiceItemId, IDbTransaction transaction)
        {
            SqlMapper.ExecuteSP("DispatchEntryCommissionDriverExtendedCommissionsDeleteById", new
            {
                @EntryCommissionDriverId = entryCommissionDriverId,
                @InvoiceItemId = invoiceItemId
            }, transaction);
        }
        
        public static async Task DeleteAsync(int entryCommissionDriverId, int invoiceItemId, IDbTransaction transaction)
        {
            await SqlMapper.ExecuteSpAsync("DispatchEntryCommissionDriverExtendedCommissionsDeleteById", new
            {
                @EntryCommissionDriverId = entryCommissionDriverId,
                @InvoiceItemId = invoiceItemId
            }, transaction);
        }
    }
}
