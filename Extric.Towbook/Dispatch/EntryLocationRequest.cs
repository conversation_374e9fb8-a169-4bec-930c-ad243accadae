using Extric.Towbook.Utility;
using ProtoBuf;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using System.Data;
using System.Data.SqlClient;

namespace Extric.Towbook.Dispatch
{
    [ProtoContract]
    public class EntryLocationRequest
    {
        public int Id { get; set; }
        public decimal Latitude { get;set; }
        public decimal Longitude { get; set; }
        public int GpsAccuracy { get; set; }

        /// <summary>
        /// The date/time that the location was recorded
        /// </summary>
        public DateTime LocationDate { get; set; }
        public DateTime CreateDate { get; set; }
        /// <summary>
        /// Ip Address of who submitted the location data
        /// </summary>
        public string IpAddress { get; set; }
        public int? OwnerUserId { get; set; }
        public string MobileNumber { get; set; }
        public int DispatchEntryId { get; set; }

        public EntryLocationRequest()
        {
            Id = 0;
            CreateDate = DateTime.MinValue;
            LocationDate = DateTime.MinValue;
        }

        protected EntryLocationRequest(SqlDataReader reader)
        {
            this.InitializeFromReader(reader);
        }

        public void Save()
        {
            if (this.Id == 0)
            {
                var x = SqlMapper.QuerySP<dynamic>("dbo.DispatchEntryLocationRequestsInsert",
                    new
                    {
                        @OwnerUserId = this.OwnerUserId,
                        @MobileNumber = Core.FormatPhone(this.MobileNumber, true),
                        @DispatchEntryId = this.DispatchEntryId
                    }).FirstOrDefault();

                if (x != null)
                {
                    this.CreateDate = x.CreateDate;
                    this.Id = x.Id;
                }
            }
            else
            {
                SqlMapper.ExecuteSP("dbo.DispatchEntryLocationRequestsUpdate",
                    new
                    {
                        @DispatchEntryLocationRequestId = this.Id,
                        @Latitude = this.Latitude,
                        @Longitude = this.Longitude,
                        @GpsAccuracy = this.GpsAccuracy,
                        @IpAddress = this.IpAddress
                    });
            }
        }

        public static async Task<EntryLocationRequest> GetByIdAsync(int id)
        {
            using (SqlDataReader reader = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString,
                CommandType.StoredProcedure, "DispatchEntryLocationRequestsGetById",
                new SqlParameter("@DispatchEntryLocationRequestId", id)))
            {

                if (await reader.ReadAsync())
                {
                    return new EntryLocationRequest(reader);
                }
                else
                {
                    return null;
                }
            }
        }

        public static async Task<EntryLocationRequest> GetByCallIdAsync(int id)
        {
            using (SqlDataReader reader = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString,
                CommandType.StoredProcedure, "DispatchEntryLocationRequestsGetByCallId",
                new SqlParameter("@DispatchEntryId", id)))
            {

                if (await reader.ReadAsync())
                {
                    return new EntryLocationRequest(reader);
                }
                else
                {
                    return null;
                }
            }
        }

        protected void InitializeFromReader(SqlDataReader reader)
        {
            if (reader != null && !reader.IsClosed)
            {
                Id = reader.GetInt32(0);
                DispatchEntryId = reader.GetValue<int>("DispatchEntryId");

                if (reader["Latitude"] != DBNull.Value)
                    Latitude = Convert.ToDecimal(reader["Latitude"]);

                if (reader["Longitude"] != DBNull.Value)
                    Longitude = Convert.ToDecimal(reader["Longitude"]);

                if (reader["GpsAccuracy"] != DBNull.Value)
                    GpsAccuracy = Convert.ToInt32(reader["GpsAccuracy"]);

                if (reader["IpAddress"] != DBNull.Value)
                    IpAddress = Convert.ToString(reader["IpAddress"]);

                if (reader["OwnerUserId"] != DBNull.Value)
                    OwnerUserId = Convert.ToInt32(reader["OwnerUserId"]);

                if (reader["MobileNumber"] != DBNull.Value)
                    MobileNumber = Convert.ToString(reader["MobileNumber"]);

                if (reader["CreateDate"] != DBNull.Value)
                {
                    DateTime d;
                    DateTime.TryParse(Convert.ToString(reader["CreateDate"]), out d); //reader.GetDateTime(9);
                    CreateDate = d;
                }

                if (reader["LocationDate"] != DBNull.Value)
                {
                    DateTime d;
                    DateTime.TryParse(Convert.ToString(reader["LocationDate"]), out d);
                    LocationDate = d;
                }
            }
        }
    }
}
