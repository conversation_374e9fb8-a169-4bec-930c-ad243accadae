using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Async = System.Threading.Tasks;

namespace Extric.Towbook.Dispatch.CallRequestModels
{
    /// <summary>
    /// CosmosDB Data Model
    /// </summary>
    public class CallRequestModel
    {
        public int Id { get; set; }
        public int CallRequestId { get; set; }
        public string Type { get; set; } = "CallRequest";
        public int CompanyId { get; set; }
        public string CompanyName { get; set; }
        public int AccountId { get; set; }
        public string AccountName { get; set; }
        public int MasterAccountId { get; set; }
        public string ContractorId { get; set; }
        public string Locationid { get; set; }
        public string IncidentAddress { get; set; }
        public string DestinationAddress { get; set; }
        public string Reason { get; set; }
        public string ServiceNeeded { get; set; }
        public string Vehicle { get; set; }
        public string VehicleColor { get; set; }

        public string PurchaseOrderNumber { get; set; }

        public int MaxEta { get; set; }

        public DateTime? ExpirationDate { get; set; }
        public DateTime RequestDate { get; set; }
        public DateTime CreateDate { get; set; }

        public int? CallId { get; set; }
        public int CallNumber { get; set; }
        public int Status { get; set; }

        public int? ResponseReasonId { get; set; }
        public string ResponseReasonName { get; set; }
        public int? ResponseUserId { get; set; }
        public string ResponseUserName { get; set; }
        public int? ResponseEta { get; set; }

        public async Async.Task UpdateInAzure()
        {
            await CosmosDB.Get().UpsertItem("call-requests", this);
        }
    }
}
