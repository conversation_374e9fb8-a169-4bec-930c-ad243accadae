namespace Extric.Towbook.Dispatch
{
    public class GenericConfig
    {
        public int Id { get; set; }
        public string Name { get; set; }

        // this is related to the vehicle class, not the vehicle weight, light vehicles might have different images
        // this array contains the vehicle classes that this region belongs to
        // for instance, "Passenger Door" exists in trucks and sedans, but doesn't in motorcycles
        public int[] VehicleClasses { get; set; }
    }

    public class VehicleDamageClass
    {
        public static  GenericConfig Sedan { get { return new GenericConfig() { Id = 1, Name = "Sedan" }; } }
        public static GenericConfig Truck2D { get { return new GenericConfig() { Id = 2, Name = "Truck2D" }; } }
        public static GenericConfig Truck4D { get { return new GenericConfig() { Id = 3, Name = "Truck4D" }; } }
        public static GenericConfig Van { get { return new GenericConfig() { Id = 4, Name = "Van" }; } }
        public static GenericConfig Motorcycle { get { return new GenericConfig() { Id = 5, Name = "Motorcycle" }; } }
    }

    public class VehicleDamage
    {
        public static readonly GenericConfig[] Regions = new[] {
            new GenericConfig() {  Id = 0, Name = "Driver Head Light",
                VehicleClasses = new int[] { VehicleDamageClass.Sedan.Id, VehicleDamageClass.Truck2D.Id, VehicleDamageClass.Truck4D.Id, VehicleDamageClass.Van.Id } },
            new GenericConfig() {  Id = 1, Name = "Driver Front Fender",
                VehicleClasses = new int[] { VehicleDamageClass.Sedan.Id, VehicleDamageClass.Truck2D.Id, VehicleDamageClass.Truck4D.Id, VehicleDamageClass.Van.Id } },
            new GenericConfig() {  Id = 2, Name = "Driver Front Wheel Well",
                VehicleClasses = new int[] { VehicleDamageClass.Sedan.Id, VehicleDamageClass.Truck2D.Id, VehicleDamageClass.Truck4D.Id, VehicleDamageClass.Van.Id } },
            new GenericConfig() {  Id = 3, Name = "Driver Front Wheel",
                VehicleClasses = new int[] { VehicleDamageClass.Sedan.Id, VehicleDamageClass.Truck2D.Id, VehicleDamageClass.Truck4D.Id, VehicleDamageClass.Van.Id } },
            new GenericConfig() {  Id = 4, Name = "Driver Mirror",
                VehicleClasses = new int[] { VehicleDamageClass.Sedan.Id, VehicleDamageClass.Truck2D.Id, VehicleDamageClass.Truck4D.Id, VehicleDamageClass.Van.Id } },
            new GenericConfig() {  Id = 5, Name = "Driver Front Door",
                VehicleClasses = new int[] { VehicleDamageClass.Sedan.Id, VehicleDamageClass.Truck2D.Id, VehicleDamageClass.Truck4D.Id, VehicleDamageClass.Van.Id } },
            new GenericConfig() {  Id = 6, Name = "Driver Front Window",
                VehicleClasses = new int[] { VehicleDamageClass.Sedan.Id, VehicleDamageClass.Truck2D.Id, VehicleDamageClass.Truck4D.Id, VehicleDamageClass.Van.Id } },
            new GenericConfig() {  Id = 7, Name = "Driver Rear Door",
                VehicleClasses = new int[] { VehicleDamageClass.Sedan.Id, VehicleDamageClass.Truck4D.Id, VehicleDamageClass.Van.Id } },
            new GenericConfig() {  Id = 8, Name = "Driver Rear Window",
                VehicleClasses = new int[] { VehicleDamageClass.Sedan.Id, VehicleDamageClass.Truck4D.Id, VehicleDamageClass.Van.Id } },
            new GenericConfig() {  Id = 9, Name = "Driver Rear Wheel Well",
                VehicleClasses = new int[] { VehicleDamageClass.Sedan.Id, VehicleDamageClass.Truck2D.Id, VehicleDamageClass.Truck4D.Id, VehicleDamageClass.Van.Id } },
            new GenericConfig() {  Id = 10, Name = "Driver Rear Wheel",
                VehicleClasses = new int[] { VehicleDamageClass.Sedan.Id, VehicleDamageClass.Truck2D.Id, VehicleDamageClass.Truck4D.Id, VehicleDamageClass.Van.Id } },
            new GenericConfig() {  Id = 11, Name = "Driver Tail Light",
                VehicleClasses = new int[] { VehicleDamageClass.Sedan.Id, VehicleDamageClass.Truck2D.Id, VehicleDamageClass.Truck4D.Id, VehicleDamageClass.Van.Id } },
            new GenericConfig() {  Id = 12, Name = "Driver Rear Fender",
                VehicleClasses = new int[] { VehicleDamageClass.Sedan.Id, VehicleDamageClass.Truck2D.Id, VehicleDamageClass.Truck4D.Id, VehicleDamageClass.Van.Id } },
            new GenericConfig() {  Id = 13, Name = "Passenger Head Light",
                VehicleClasses = new int[] { VehicleDamageClass.Sedan.Id, VehicleDamageClass.Truck2D.Id, VehicleDamageClass.Truck4D.Id, VehicleDamageClass.Van.Id } },
            new GenericConfig() {  Id = 14, Name = "Passenger Front Fender",
                VehicleClasses = new int[] { VehicleDamageClass.Sedan.Id, VehicleDamageClass.Truck2D.Id, VehicleDamageClass.Truck4D.Id, VehicleDamageClass.Van.Id } },
            new GenericConfig() {  Id = 15, Name = "Passenger Front Wheel Well",
                VehicleClasses = new int[] { VehicleDamageClass.Sedan.Id, VehicleDamageClass.Truck2D.Id, VehicleDamageClass.Truck4D.Id, VehicleDamageClass.Van.Id } },
            new GenericConfig() {  Id = 16, Name = "Passenger Front Wheel",
                VehicleClasses = new int[] { VehicleDamageClass.Sedan.Id, VehicleDamageClass.Truck2D.Id, VehicleDamageClass.Truck4D.Id, VehicleDamageClass.Van.Id } },
            new GenericConfig() {  Id = 17, Name = "Passenger Mirror",
                VehicleClasses = new int[] { VehicleDamageClass.Sedan.Id, VehicleDamageClass.Truck2D.Id, VehicleDamageClass.Truck4D.Id, VehicleDamageClass.Van.Id } },
            new GenericConfig() {  Id = 18, Name = "Passenger Front Door",
                VehicleClasses = new int[] { VehicleDamageClass.Sedan.Id, VehicleDamageClass.Truck2D.Id, VehicleDamageClass.Truck4D.Id, VehicleDamageClass.Van.Id } },
            new GenericConfig() {  Id = 19, Name = "Passenger Front Window",
                VehicleClasses = new int[] { VehicleDamageClass.Sedan.Id, VehicleDamageClass.Truck2D.Id, VehicleDamageClass.Truck4D.Id, VehicleDamageClass.Van.Id } },
            new GenericConfig() {  Id = 20, Name = "Passenger Rear Door",
                VehicleClasses = new int[] { VehicleDamageClass.Sedan.Id, VehicleDamageClass.Truck4D.Id, VehicleDamageClass.Van.Id } },
            new GenericConfig() {  Id = 21, Name = "Passenger Rear Window",
                VehicleClasses = new int[] { VehicleDamageClass.Sedan.Id, VehicleDamageClass.Truck4D.Id, VehicleDamageClass.Van.Id } },
            new GenericConfig() {  Id = 22, Name = "Passenger Rear Wheel Well",
                VehicleClasses = new int[] { VehicleDamageClass.Sedan.Id, VehicleDamageClass.Truck2D.Id, VehicleDamageClass.Truck4D.Id, VehicleDamageClass.Van.Id } },
            new GenericConfig() {  Id = 23, Name = "Passenger Rear Wheel",
                VehicleClasses = new int[] { VehicleDamageClass.Sedan.Id, VehicleDamageClass.Truck2D.Id, VehicleDamageClass.Truck4D.Id, VehicleDamageClass.Van.Id } },
            new GenericConfig() {  Id = 24, Name = "Passenger Tail Light",
                VehicleClasses = new int[] { VehicleDamageClass.Sedan.Id, VehicleDamageClass.Truck2D.Id, VehicleDamageClass.Truck4D.Id, VehicleDamageClass.Van.Id } },
            new GenericConfig() {  Id = 25, Name = "Passenger Rear Fender",
                VehicleClasses = new int[] { VehicleDamageClass.Sedan.Id, VehicleDamageClass.Truck2D.Id, VehicleDamageClass.Truck4D.Id, VehicleDamageClass.Van.Id } },
            new GenericConfig() {  Id = 26, Name = "Front Bumper",
                VehicleClasses = new int[] { VehicleDamageClass.Sedan.Id, VehicleDamageClass.Truck2D.Id, VehicleDamageClass.Truck4D.Id, VehicleDamageClass.Van.Id } },
            new GenericConfig() {  Id = 27, Name = "Front Grill",
                VehicleClasses = new int[] { VehicleDamageClass.Sedan.Id, VehicleDamageClass.Truck2D.Id, VehicleDamageClass.Truck4D.Id, VehicleDamageClass.Van.Id } },
            new GenericConfig() {  Id = 28, Name = "Front Hood",
                VehicleClasses = new int[] { VehicleDamageClass.Sedan.Id, VehicleDamageClass.Truck2D.Id, VehicleDamageClass.Truck4D.Id, VehicleDamageClass.Van.Id } },
            new GenericConfig() {  Id = 29, Name = "Windshield",
                VehicleClasses = new int[] { VehicleDamageClass.Sedan.Id, VehicleDamageClass.Truck2D.Id, VehicleDamageClass.Truck4D.Id, VehicleDamageClass.Van.Id } },
            new GenericConfig() {  Id = 30, Name = "Roof",
                VehicleClasses = new int[] { VehicleDamageClass.Sedan.Id, VehicleDamageClass.Truck2D.Id, VehicleDamageClass.Truck4D.Id, VehicleDamageClass.Van.Id } },
            new GenericConfig() {  Id = 31, Name = "Rear Window",
                VehicleClasses = new int[] { VehicleDamageClass.Sedan.Id, VehicleDamageClass.Truck2D.Id, VehicleDamageClass.Truck4D.Id, VehicleDamageClass.Van.Id } },
            new GenericConfig() {  Id = 32, Name = "Rear Trunk Lid",
                VehicleClasses = new int[] { VehicleDamageClass.Sedan.Id } },
            new GenericConfig() {  Id = 33, Name = "Backside",
                VehicleClasses = new int[] { VehicleDamageClass.Sedan.Id } },
            new GenericConfig() {  Id = 34, Name = "Rear Bumper",
                VehicleClasses = new int[] { VehicleDamageClass.Sedan.Id, VehicleDamageClass.Truck2D.Id, VehicleDamageClass.Truck4D.Id, VehicleDamageClass.Van.Id } },
            new GenericConfig() {  Id = 35, Name = "Truck Bed",
                VehicleClasses = new int[] { VehicleDamageClass.Truck2D.Id, VehicleDamageClass.Truck4D.Id, } },
            new GenericConfig() {  Id = 36, Name = "Tailgate",
                VehicleClasses = new int[] { VehicleDamageClass.Truck2D.Id, VehicleDamageClass.Truck4D.Id, } },
            new GenericConfig() {  Id = 37, Name = "Rear Hatch Door",
                VehicleClasses = new int[] { VehicleDamageClass.Van.Id } },
            new GenericConfig() {  Id = 38, Name = "Front Lower Bumper / Trim",
                VehicleClasses = new int[] { VehicleDamageClass.Sedan.Id, VehicleDamageClass.Truck2D.Id, VehicleDamageClass.Truck4D.Id, VehicleDamageClass.Van.Id } },
            new GenericConfig() {  Id = 39, Name = "Rear Lower Bumper / Trim",
                VehicleClasses = new int[] { VehicleDamageClass.Sedan.Id, VehicleDamageClass.Truck2D.Id, VehicleDamageClass.Truck4D.Id, VehicleDamageClass.Van.Id } }
        };

        public static readonly GenericConfig[] Types = new[] {
            new GenericConfig() {  Id = 0, Name = "Broken" },
            new GenericConfig() {  Id = 1, Name = "Crack" },
            new GenericConfig() {  Id = 2, Name = "Dent" },
            new GenericConfig() {  Id = 3, Name = "Missing" },
            new GenericConfig() {  Id = 7, Name = "Other" },
            new GenericConfig() {  Id = 4, Name = "Puncture" },
            new GenericConfig() {  Id = 5, Name = "Rust" },
            new GenericConfig() {  Id = 6, Name = "Scratch" }
        };

        public static readonly GenericConfig[] Classes = new[]
        {
            new GenericConfig() { Id = 1, Name = "Sedan" },
            new GenericConfig() { Id = 2, Name = "Truck2D" },
            new GenericConfig() { Id = 3, Name = "Truck4D" },
            new GenericConfig() { Id = 4, Name = "Van" },
            new GenericConfig() { Id = 5, Name = "Motorcycle" }
        };
    }
}
