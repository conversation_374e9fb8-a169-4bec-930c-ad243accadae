using Extric.Towbook.Utility;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Extric.Towbook.Dispatch
{
    public enum CallRequestsMetricGroupings
    {
        None = 0,
        Hour = 1,
        Day = 2,
        Week = 3,
        Month = 4
    }

    [Table("DispatchEntryRequestsMetrics")]
    public class CallRequestsMetric
    {
        [Key]
        public long Id { get; set; }
        public int AccountId { get; set; }
        public int ActionId { get; set; }
        public int Count { get; set; }
        public int Interval { get; set; }
        public DateTime CreateDate { get; set; }

        [Write(false)]
        public int Period { get; set; }

        public static IEnumerable<CallRequestsMetric> Get(
            IEnumerable<int> actions,
            CallRequestsMetricGroupings groupBy = CallRequestsMetricGroupings.Day,
            int? accountId = null,
            int? masterAccountId = null,
            DateTime? startDate = null,
            DateTime? endDate = null)
        {
            var builder = new SqlBuilder();
            var selector = builder.AddTemplate(@"SELECT /**select**/ FROM DispatchEntryRequestsMetrics DERM /**innerjoin**/ /**where**/ /**groupby**/");

            // builds the grouping query based on the parameter
            switch (groupBy)
            {
                case CallRequestsMetricGroupings.Month:
                    builder.Select("DATEPART(month, DERM.CreateDate) AS Period");
                    builder.GroupBy("DATEPART(month, DERM.CreateDate)");

                    break;
                case CallRequestsMetricGroupings.Week:
                    builder.Select("DATEPART(week, DERM.CreateDate) AS Period");
                    builder.GroupBy("DATEPART(week, DERM.CreateDate)");

                    break;
                case CallRequestsMetricGroupings.Day:
                    builder.Select("DATEPART(day, DERM.CreateDate) AS Period");
                    builder.GroupBy("DATEPART(day, DERM.CreateDate)");

                    break;
                case CallRequestsMetricGroupings.Hour:
                    builder.Select("DATEPART(hour, DERM.CreateDate) AS Period");
                    builder.GroupBy("DATEPART(hour, DERM.CreateDate)");

                    break;
                default:
                    throw new Exception("You must specify a grouping interval.");
            }

            builder.Select("DERM.ActionId");
            builder.GroupBy("DERM.ActionId");

            builder.Select("SUM(DERM.[Count]) AS [Count]");

            builder.Select("DERM.AccountId");
            builder.GroupBy("DERM.AccountId");

            //builder.Select("DERM.CreateDate");
            //builder.GroupBy("DERM.CreateDate");

            //builder.Select("DERM.Interval");
            //builder.GroupBy("DERM.Interval");

            if (actions.Any())
                builder.Where("DERM.ActionId IN (" + string.Join(",", actions) + ")");
            
            if (masterAccountId.HasValue)
            {
                builder.InnerJoin("Accounts A ON A.AccountId = DERM.DispatchEntryRequestsMetrics.AccountId");
                builder.Where("A.MasterAccountId = @MasterAccountId", new { MasterAccountId = masterAccountId.Value });
            }
            else if (accountId.HasValue)
                builder.Where("DERM.AccountId = @AccountId", new { AccountId = accountId.Value });
            else
                throw new Exception("You must specify either an accountId or a masterAccountId.");

            if (startDate.HasValue)
                builder.Where("DERM.CreateDate >= CAST(@StartDate AS DATE)", new { StartDate = startDate.Value });
            if (endDate.HasValue)
                builder.Where("DERM.CreateDate < DATEADD(dd, 1, CAST(@EndDate AS DATE))", new { EndDate = endDate.Value });
            
            return SqlMapper.Query<CallRequestsMetric>(selector.RawSql, selector.Parameters).ToList();
        }

        public void Save()
        {
            if (Id == 0)
            {
                CreateDate = DateTime.Now;
                Id = SqlMapper.Insert(this);
            }
            else
                SqlMapper.Update(this);
        }

        public static void SaveCalculations(IDictionary<int, int> metrics, int accountId, int interval)
        {
            using (var cx = Core.CreateTransactionScope())
            {
                using (var c = Core.GetConnection())
                {
                    foreach (var m in metrics)
                    {
                        c.Execute(
                            "INSERT INTO DispatchEntryRequestsMetrics (AccountId, ActionId, Count, CreateDate, Interval) VALUES (@AccountId, @ActionId, @Count, getdate(), @Interval)",
                            new { AccountId = accountId, ActionId = m.Key, Count = m.Value, Interval = interval });
                    }

                    // update the last eventId in the metrics history table for that accountId
                    c.ExecuteSP("DispatchEntryRequestsMetricsHistoryMarkLast", new { AccountId = accountId });

                }
                cx.Complete();
            }
        }
    }
}
