using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using Extric.Towbook.Utility;

namespace Extric.Towbook.Dispatch
{
    public class InvoiceItemTax
    {
        public int InvoiceItemId { get; set; }
        public int TaxRateId { get; set; }

        internal InvoiceItemTax(SqlDataReader reader)
        {
            InitializeFromDataReader(reader);
        }

        public InvoiceItemTax()
        {

        }

        private void InitializeFromDataReader(SqlDataReader reader)
        {
            InvoiceItemId = reader.GetValue<int>("InvoiceItemId");
            TaxRateId = reader.GetValue<int>("TaxRateId");
        }

        internal static System.Collections.ObjectModel.Collection<InvoiceItemTax> GetByInvoiceItemId(int id)
        {

            return SqlMapper.QuerySP("InvoiceItemTaxesGetByInvoiceItemId", new { @InvoiceItemId = id }).Select(o => new InvoiceItemTax() { InvoiceItemId = o.InvoiceItemId, TaxRateId = o.TaxRateId }).ToCollection();

        }

        public static System.Collections.ObjectModel.Collection<InvoiceItemTax> GetByInvoiceItemIds(int[] ids)
        {
            List<InvoiceItemTax> results = new List<InvoiceItemTax>();

            foreach(var batch in ids.Batch(500))
            {
                results.AddRange(SqlMapper.Query<dynamic>(@"SELECT * FROM InvoiceItemTaxes WHERE InvoiceItemId IN @Ids",
                    new { Ids = batch })
                    .Select(o => 
                        new InvoiceItemTax() { 
                            InvoiceItemId = o.InvoiceItemId, 
                            TaxRateId = o.TaxRateId 
                        }
                     ));
            }

            return results.ToCollection();
        }
    }
}
