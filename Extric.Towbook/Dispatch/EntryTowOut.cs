using Extric.Towbook.Utility;
using Glav.CacheAdapter.Core.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Extric.Towbook.Dispatch
{
    [Table("DispatchEntryTowOuts")]
    public class EntryTowOut
    {
        private const string cacheFormat = "eto-id-{0}";

        [Key("DispatchEntryTowoutId")]
        public int Id { get; set; }
        public int DispatchEntryId { get; set; }

        [Description("The date and time that the tow out action occured.")]
        public DateTime CreateDate { get; set; }

        [Description("The Towout AssetId")]
        public int? AssetId { get; set; }
        public DateTime TowInCompletionTime { get; set; }
        public DateTime? TowInDispatchTime { get; set; }
        public DateTime? TowInEnrouteTime { get; set; }
        public DateTime? TowInArrivalTime { get; set; }
        public DateTime? TowInTowTime { get; set; }
        public DateTime? TowInDestinationArrivalTime { get; set; }
        public bool? IsDeleted { get; set; }
        public int? DeletedByUserId { get; set; }
        public DateTime? DeletedDate { get; set; }

        public EntryTowOut()
        {

        }

        public static EntryTowOut GetById(int id)
        {
            return AppServices.Cache.Get(String.Format(cacheFormat, id), TimeSpan.FromDays(30), () =>
            {
                return SqlMapper.Query<EntryTowOut>(
                    @"SELECT * FROM DispatchEntryTowouts WHERE DispatchEntryTowoutId=@Id",
                    new { Id = id }).FirstOrDefault();
            });
        }

        public static EntryTowOut GetByDispatchEntryId(int id)
        {
            return AppServices.Cache.Get(String.Format(cacheFormat, id) + "_callId", TimeSpan.FromDays(30), () =>
            {
                return SqlMapper.Query<EntryTowOut>(
                    @"SELECT * FROM DispatchEntryTowouts WHERE DispatchEntryId=@Id and COALESCE(IsDeleted, 0)=0",
                    new { Id = id }).FirstOrDefault();
            });
        }

        public static IEnumerable<EntryTowOut> GetByDispatchEntryIds(int[] ids)
        {
            var ret = new List<EntryTowOut>();

            foreach (var list in ids.Batch(500))
            {
                ret.AddRange(SqlMapper.Query<EntryTowOut>(
                    "SELECT * FROM DispatchEntryTowouts WITH (NOLOCK) WHERE DispatchEntryId IN @a AND COALESCE(IsDeleted, 0)=0",
                    new { a = list }));
            }

            return ret;
        }

        public void Save()
        {
            if (Id <= 0)
            {
                CreateDate = DateTime.Now;
                Id = (int)SqlMapper.Insert(this);
            }
            else
            {
                SqlMapper.Update(this);

                AppServices.Cache.InvalidateCacheItem(String.Format(cacheFormat, Id));
                AppServices.Cache.InvalidateCacheItem(String.Format(cacheFormat, DispatchEntryId) + "_callId");
            }
        }

        public void Delete(User user)
        {
            DeletedByUserId = user.Id;
            DeletedDate = DateTime.Now;
            IsDeleted = true;

            Save();
        }

        public static bool CheckTowOutCallShouldExcludeCommissionByDateRange(EntryTowOut towOut, InvoiceItem ii, Entry e, DateTime startDate, DateTime endDate, Driver byDriver = null)
        {
            if (towOut != null)
            {
                // "assetless" invoice items should always be included
                if (ii.AssetId == null)
                    return false;

                if (byDriver != null &&
                        e.Assets != null &&
                        e.Assets.FirstOrDefault(w => w.Id == ii.AssetId)?.Drivers?.Select(s => s.DriverId)?.Contains(byDriver.Id) != true)
                {
                    // The invoice item doesn't apply to this driver. Exclude this charge.
                    return true;
                }

                var isTowOutCharge = ii.AssetId == towOut.AssetId; // towOut table knows the second asset id

                if (isTowOutCharge)
                {
                    // Towout charges should fall into the date range by the calls completion date
                    if (e.CompletionTime >= startDate && e.CompletionTime < endDate)
                        return false; // date ranged check passed, should not exclude.
                }
                else
                {
                    // Towin charges should fall into the date range by the towout table's TowInCompletionTime
                    if (towOut.TowInCompletionTime >= startDate && towOut.TowInCompletionTime < endDate)
                        return false; // date ranged check passed, should not exclude.
                }

                // exclude this charge. It did not fit the date range for either towin or towout
                return true;
            }

            // not a towout, always include.
            return false;
        }

        public static void QualifyIfAssetDriversShouldRemainOnTowOuts(IEnumerable<Entry> entries, IEnumerable<EntryTowOut> towouts, DateTime startDate, DateTime endDate)
        {
            foreach (var e in entries)
            {
                var towOut = towouts.FirstOrDefault(a => a.DispatchEntryId == e.Id);
                if (towOut != null && e.Assets != null && e.Assets.Count() > 1)
                {
                    e.DriverId = 0;

                    if (!(towOut.TowInCompletionTime > startDate && towOut.TowInCompletionTime < endDate))
                        e.Assets[0].Drivers = new Collection<EntryAssetDriver>();

                    if (!(e.CompletionTime != null && e.CompletionTime.Value > startDate && e.CompletionTime.Value < endDate))
                        e.Assets[1].Drivers = new Collection<EntryAssetDriver>();
                }
            }
        }
    }
}
