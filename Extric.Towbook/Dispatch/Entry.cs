using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Data.SqlClient;
using Extric.Towbook.Accounts;
using Glav.CacheAdapter.Core.DependencyInjection;
using System.Diagnostics;
using ProtoBuf;
using Extric.Towbook.Utility;
using Extric.Towbook.Integration;
using System.Data.SqlTypes;
using Extric.Towbook.ActivityLogging;
using Extric.Towbook.Dispatch.CallModels;
using System.Threading.Tasks;
using Async = System.Threading.Tasks;
using System.Collections.Concurrent;
using System.Text;
using Extric.Towbook.Company.Accounting;
using Extric.Towbook.Generated;
using Extric.Towbook.Vehicle;

namespace Extric.Towbook.Dispatch
{
    [Serializable]
    [ProtoContract(ImplicitFields = ImplicitFields.AllFields, Name = "Call")]
    [DebuggerDisplay("Id = {Id}, Total = {InvoiceTotal}, Balance = {BalanceDue}")]
    public class Entry : TrackableObject, Extric.Towbook.Dispatch.IEntry
    {
        //private static ICacheManager cache = CacheFactory.GetCacheManager();
        private const int CacheTimeout = 10;

        public enum EntryType : byte
        {
            /// <summary>
            /// Represents a default tow ticket call type. Uses the default DspatchEntrySequence number generator.
            /// </summary>
            Call = 1,

            /// <summary>
            /// Represents a Quote call type; these are used to represent quotes to customers, they can't be completed etc. Their call number isn't a call number, it is a quote number.
            /// </summary>
            Quote = 2
        }

        #region internal structures
        public enum EntryStatus
        {
            /// <summary>	
            /// Call entered into system, hasn't been touched yet
            /// </summary>
            /// 
            Waiting = 0,	// Call info entered into system by dispatcher..
            Dispatched = 1,	// Call has been dispatched to driver. 
            EnRoute = 2,	// Truck/Driver en route to pick up location.
            AtSite = 3,	// Truck/Driver has arrived at site
            BeingTowed = 4,	// Currently being hauled by tow truck.
            DestinationArrival = 7,	// Truck/Driver has arrived at destination.
            Completed = 5,	// Call is completed; no further action neccesary.
            GoneOnArrival = 200, // Vehicle went to destination, was gone on arrival.
            Canceled = 255	// Call was cancelled
        }
        public enum EntryPriority
        {
            Normal = 0,
            High = 1,
            Low = 2
        }
        #endregion

        private int _id;
        private int _callNumber;
        private int _companyId;
        private EntryType _type = EntryType.Call;
        private int _accountId;
        private int _version = 1;

        private int _reasonId;

        private string _invoiceNumber;
        private string _purchaseOrderNumber;

        private AccountType _requestedBy = AccountType.Individual;

        private Status _status = Status.Waiting;
        private EntryPriority _priority;

        #region Vehicle
        private string _vin;
        private int _year;
        private int _colorId;
        private int _manufacturerId;
        private int _modelId;
        private int _bodyTypeId;

        private string _licenseNumber;
        private string _licenseState;
        private string _licenseYear;
        private bool? _drivable;
        private int _odometer;

        private int _invoiceStatusId;
        #endregion

        [ProtoIgnore]
        private Invoice _invoice;
        public Invoice Invoice
        {
            get
            {
                if (_invoice != null)
                    return _invoice;

                if (_id > 0)
                {
                    _invoice = Invoice.GetByDispatchEntry(_id);
                    if (_invoice != null)
                        _invoice.DispatchEntry = this;
                }

                if (_invoice != null)
                    return _invoice;

                _invoice = new Invoice();
                _invoice.CompanyId = this.CompanyId;
                _invoice.DispatchEntry = this;

                return _invoice;
            }
            internal set => _invoice = value;
        }

        public async Task<Invoice> GetInvoiceAsync()
        {
            if (_invoice != null)
                return _invoice;

            if (_id > 0)
            {
                _invoice = await Invoice.GetByDispatchEntryAsync(_id);
                if (_invoice != null)
                    _invoice.DispatchEntry = this;
            }

            if (_invoice != null)
                return _invoice;

            _invoice = new Invoice();
            _invoice.CompanyId = this.CompanyId;
            _invoice.DispatchEntry = this;

            return _invoice;
        }

        public async Task<Collection<InvoiceItem>> GetInvoiceItemsAsync()
        {
            return await (await GetInvoiceAsync()).GetInvoiceItemsAsync();
        }

        public int Version
        {
            get => _version;
            set
            {
                if (value < _version)
                    throw new TowbookException("invalid version... equal or less than current! new=" + value + ", current=" + _version);

                _version = value;
            }
        }

        private void updateVersion()
        {
            int newVersion = this._version++;

            SqlHelper.ExecuteNonQuery(Core.ConnectionString, "DispatchEntriesUpdateVersionById",
                this.Id, newVersion);

            this._version = newVersion;
        }

        public static Collection<CachedCall> GetCachedCurrentByCompany(int[] companies)
        {
            var list = new Collection<CachedCall>();
            foreach (var each in companies)
            {
                list = list.Union(Cache.Instance.PartitionGetAll(each,
                     (int partitionId) =>
                     {
                         return GetCurrentByCompanyId(partitionId)
                             .Select(o => new CachedCall(o.Id, o.CompanyId, o.CallNumber, o.Status.Id,
                             CachedCall.GetEndingLocation(o), o.Drivers));
                     })).ToCollection();
            }

            if (Core.GetRedisValue("disable_redis_call_cache") != "1")
                foreach (var each in companies)
                {
                    Cache.Instance.PartitionGetAll<EntryCurrent>(each,
                        (int partitionId) =>
                        {
                            return list.Where(o => o.CompanyId == partitionId)
                                .Select(o =>
                                    new EntryCurrent()
                                    {
                                        EntryId = o.Id,
                                        CompanyId = o.CompanyId,
                                        StatusId = o.StatusId
                                    });
                        });
                }

            return list;
        }

        private string _towSource;
        private string _towDestination;

        private string _notes;

        private Nullable<DateTime> _dispatchTime;
        private Nullable<DateTime> _enrouteTime;
        private Nullable<DateTime> _arrivalTime;
        private Nullable<DateTime> _arrivalETA;
        private Nullable<DateTime> _towTime;
        private Nullable<DateTime> _destinationArrivalTime;
        private Nullable<DateTime> _completionTime;

        private int _ownerUserId;
        private DateTime _createDate;
        private bool _created;
        private bool _impound;
        private bool _released;

        private Dictionary<int, AttributeValue> _attributes;

        public Dictionary<int, AttributeValue> Attributes
        {
            set
            {
                _attributes = value;
            }
            get
            {
                if (_attributes == null)
                {
                    if (_id < 1)
                    {
                        _attributes = new Dictionary<int, AttributeValue>();
                    }
                    else
                    {
                        _attributes = AttributeValue.GetByDispatchEntry(this);
                    }
                }

                return _attributes;
            }
        }

        public async Task<Dictionary<int, AttributeValue>> GetAttributesAsync()
        {
            if (_attributes == null)
            {
                if (_id < 1)
                {
                    _attributes = new Dictionary<int, AttributeValue>();
                }
                else
                {
                    _attributes = await AttributeValue.GetByDispatchEntryAsync(this);
                }
            }

            return _attributes;
        }

        public Collection<InvoiceItem> InvoiceItems
        {
            get => Invoice.InvoiceItems;
            internal set
            {
                if (Invoice == null)
                    Invoice = new Invoice();

                Invoice.InvoiceItems = value;
            }
        }

        private Collection<EntryContact> _contacts = null;
        public Collection<EntryContact> Contacts
        {
            set
            {
                _contacts = value;
            }
            get
            {
                if (_contacts == null)
                    _contacts = EntryContact.GetByDispatchEntry(this);

                return _contacts;
            }
        }

        public async Task<Collection<EntryContact>> GetContactsAsync()
        {
            if (_contacts == null)
                _contacts = await EntryContact.GetByDispatchEntryAsync(this);

            return _contacts;
        }

        public decimal InvoiceSubtotal
        {
            get
            {
                return Invoice.Subtotal;
            }
        }

        public async Task<decimal> GetInvoiceSubtotalAsync()
        {
            return await (await this.GetInvoiceAsync()).GetSubtotalAsync();
        }

        public Dictionary<int, decimal> CalculatedSurchargeTotals
        {
            get
            {
                Dictionary<int, decimal> list = null;

                {
                    list = new Dictionary<int, decimal>();

                    Collection<Surcharges.SurchargeRate> srl = Surcharges.SurchargeRate.GetByCompany(Company);
                    Collection<Surcharges.SurchargeAccountRate> srlA = Surcharges.SurchargeAccountRate.GetByCompany(Company);

                    if (srl == null && srlA == null)
                        return null;

                    if (srl == null)
                    {

                        var empty = new Surcharges.SurchargeRate();
                        empty.Rate = 0;
                        empty.SurchargeId = Extric.Towbook.Surcharges.Surcharge.SURCHARGE_FUEL;
                        empty.Taxable = false;
                        srl = new Collection<Surcharges.SurchargeRate>();
                        srl.Add(empty);
                    }

                    if (srl != null)
                    {
                        foreach (Surcharges.SurchargeRate sr in srl)
                        {
                            Dictionary<int, Surcharges.AccountRateItemExclusion> arExcludes = null;
                            Dictionary<int, Surcharges.RateItemExclusion> rExcludes = null;
                            rExcludes = Surcharges.RateItemExclusion.GetByCompany(Company.Id, sr.SurchargeId);

                            Surcharges.SurchargeAccountRate srx = null;

                            decimal rate = sr.Rate;

                            if (_accountId > 0)
                            {
                                srx = Surcharges.SurchargeAccountRate.GetBySurcharge(sr.Surcharge.Id, _accountId);
                                if (srx != null)
                                    rate = srx.Rate;

                                arExcludes = Surcharges.AccountRateItemExclusion.GetByAccount(_accountId, sr.SurchargeId);
                            }

                            #region save/retrieve surcharge rate from attributes table
                            if (this.Attributes.ContainsKey(AttributeValue.BUILTIN_SURCHARGE_RATE))
                            {
                                decimal tempRate = 0;
                                if (decimal.TryParse(this.Attributes[AttributeValue.BUILTIN_SURCHARGE_RATE].Value, out tempRate))
                                {
                                    rate = tempRate;
                                }
                            }
                            else
                            {
                                this.SetAttribute(AttributeValue.BUILTIN_SURCHARGE_RATE, rate.ToString());
                            }
                            #endregion

                            // check if the rate already is saved as an attribute inside this entry.. if so, use it instead. 
                            // grab the exclusions from this too, if it exists. 

                            List<int> excludes = null;

                            if (this.Attributes.ContainsKey(AttributeValue.BUILTIN_SURCHARGE_EXCLUSIONS))
                            {
                                excludes = new List<int>();

                                if (this.Attributes[AttributeValue.BUILTIN_SURCHARGE_EXCLUSIONS] != null &&
                                    !String.IsNullOrWhiteSpace(this.Attributes[AttributeValue.BUILTIN_SURCHARGE_EXCLUSIONS].Value))
                                {
                                    foreach (var v in this.Attributes[AttributeValue.BUILTIN_SURCHARGE_EXCLUSIONS].Value.Split(','))
                                    {
                                        int tempValue;
                                        if (int.TryParse(v, out tempValue))
                                            excludes.Add(tempValue);
                                    }
                                }
                            }

                            if (excludes == null)
                            {
                                excludes = new List<int>();

                                // account exclusions/inclusions
                                if (arExcludes != null)
                                {
                                    foreach (int id in arExcludes.Keys)
                                    {
                                        if (!excludes.Contains(id))
                                            excludes.Add(id);
                                    }
                                }

                                // #1 -- if exclusions are acting as exclusions, use the company exclusions regardless of account
                                // #2 -- if exclusions are acting as inclusions, and there is no account, use the company exclusions
                                // #3 -- if accounts are not overriding the company, then use company. if srx is null, treat override as false.
                                if (!sr.TreatExclusionsAsInclusions ||  // #1        
                                    (sr.TreatExclusionsAsInclusions && _accountId == 0) || // #2
                                    (sr.TreatExclusionsAsInclusions && _accountId > 0 && (srx == null || !srx.OverrideCompanyInclusions))) // #3
                                {
                                    if (rExcludes != null)
                                    {
                                        foreach (int id in rExcludes.Keys)
                                        {
                                            if (!excludes.Contains(id))
                                                excludes.Add(id);
                                        }
                                    }
                                }

                                this.SetAttribute(AttributeValue.BUILTIN_SURCHARGE_EXCLUSIONS, string.Join(",", excludes));
                            }

                            decimal total = 0;

                            // refresh invoice items with latest
                            foreach (InvoiceItem i in InvoiceItems)
                            {
                                if (i.RateItem != null)
                                {
                                    if (!sr.TreatExclusionsAsInclusions)
                                    {
                                        if (excludes.Contains(i.RateItem.RateItemId))
                                            continue;
                                    }
                                    else
                                    {
                                        if (!excludes.Contains(i.RateItem.RateItemId))
                                            continue;
                                    }
                                }

                                if (!sr.ApplyToCustomInvoiceItems && i.RateItem == null)
                                    continue;

                                if (!i.CustomPrice.HasValue)
                                    continue;

                                // for new calls, this is handled via the exclusions returned by the database, but 
                                // this cannot be removed because it would break old calls that dont have the exclusions set in 
                                // the entry attribute value.
                                if (i.RateItem != null && i.RateItem.Predefined != null)
                                {
                                    // Skip storage rates and fuel charges automatically.

                                    if (i.RateItem.Predefined.Id == PredefinedRateItem.BUILTIN_IMPOUND_DAILYRATE)
                                        continue;

                                    if (i.RateItem.Predefined.Id == PredefinedRateItem.BUILTIN_FUEL_SURCHARGE)
                                        continue;
                                }

                                total += (i.Quantity * i.CustomPrice.Value) * rate;
                            }
                            list.Add(sr.SurchargeId, (total > 0 ? total : 0));
                        }
                    }
                }
                return list;
            }
        }

        [CsvExportable("AmountTax")]
        public decimal InvoiceTax
        {
            get
            {
                return this.Invoice.Tax;
            }

        }

        public async Task<decimal> GetInvoiceTaxAsync()
        {
            return await (await this.GetInvoiceAsync()).GetTaxAsync();
        }


        [CsvExportable("AmountTotal")]
        public decimal InvoiceTotal
        {
            get
            {
                return (InvoiceTax + InvoiceSubtotal);
            }
        }

        public async Task<decimal> GetInvoiceTotalAsync()
        {
            return (await this.GetInvoiceTaxAsync() + await this.GetInvoiceSubtotalAsync());
        }

        [CsvExportable("AmountCollected")]
        public decimal PaymentsApplied
        {
            get;
            internal set;
        }

        [CsvExportable("AmountDue")]
        public decimal BalanceDue
        {
            get
            {
                decimal retval = Invoice.BalanceDue;
                if (retval < 0)
                {
                    // throw new TowbookException("BalanceDue is less than 0 for DispatchEntry " + _id.ToString());
                    return 0;
                }

                return retval;
            }
        }

        public async Task<decimal> GetBalanceDueAsync()
        {
            decimal retval = (await this.GetInvoiceAsync()).BalanceDue;
            if (retval < 0)
            {
                // throw new TowbookException("BalanceDue is less than 0 for DispatchEntry " + _id.ToString());
                return 0;
            }

            return retval;
        }

        public int[] Statements { get; protected set; }

        public Entry()
        {
            _id = -1;
            _created = true; // by default, we want to have this set to true.
        }

        [Obsolete("Prefer using GetById method instead.")]
        public Entry(int id)
        {
            using (SqlDataReader dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                "DispatchEntriesGetById", new SqlParameter("@Id", id)))
            {
                if (dr.Read())
                {
                    InitializeFromDataReader(dr);
                }
                else
                {
                    throw new Extric.Towbook.TowbookException("Dispatch Entry/Call doesn't exist!");
                }
            }
        }

        public Entry(SqlDataReader dr)
        {
            InitializeFromDataReader(dr);
            AppServices.Cache.Add("de:" + Id, TimeSpan.FromMinutes(CacheTimeout), this);
        }

        public Entry(SqlDataReader dr, EntryContact[] contacts, AttributeValue[] values)
        {
            InitializeFromDataReader(dr);
            AppServices.Cache.Add("de:" + Id, TimeSpan.FromMinutes(CacheTimeout), this);
        }

        public static Entry GetById(int id, int companyId)
        {
            var e = GetById(id);

            if (e != null && e.CompanyId != companyId)
                throw new TowbookException("Access Denied - entry isn't owned by specified company");
            else
                return e;
        }

        public static Entry GetById(int id)
        {
            if (id == 0)
                return null;

            var entry = AppServices.Cache.Get<Entry>("de:" + id, TimeSpan.FromMinutes(CacheTimeout), () => GetByIdNoCache(id));

            return entry;
        }

        public static Entry GetByIdNoCache(int id)
        {
            if (id == 0)
                return null;

            using (var dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                "DispatchEntriesGetById", new SqlParameter("@Id", id)))
            {
                if (dr.Read())
                {
                    var en = new Entry(dr);
                    return en;
                }
                else
                {
                    return null;
                }
            }
        }

        public static async Task<Entry> GetByIdAsync(int id)
        {
            if (id == 0)
                return null;

            var entry = await AppServices.Cache.GetAsync("de:" + id, TimeSpan.FromMinutes(CacheTimeout),
                async () => await GetByIdNoCacheAsync(id));

            return entry;
        }

        public static async Task<Entry> GetByIdNoCacheAsync(int id)
        {
            if (id == 0)
                return null;

            using (var dr = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString, 
                System.Data.CommandType.StoredProcedure,
                "DispatchEntriesGetById", new SqlParameter("@Id", id)))
            {
                if (await dr.ReadAsync())
                {
                    var en = new Entry(dr);
                    return en;
                }
                else
                {
                    return null;
                }
            }
        }

        public static IEnumerable<Entry> GetByVin(string vin)
        {
            if (vin == null)
                return null;

            using (SqlDataReader dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                "DispatchEntriesGetByVin", new SqlParameter("@Vin", vin)))
            {
                var calls = new Collection<Entry>();

                while (dr.Read())
                {
                    calls.Add(new Entry(dr));
                }

                return calls;
            }
        }

        public static async Task<IEnumerable<Entry>> GetByVinAsync(string vin)
        {
            if (vin == null)
                return null;

            using (SqlDataReader dr = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString,
                "DispatchEntriesGetByVin", new SqlParameter("@Vin", vin)))
            {
                var calls = new Collection<Entry>();

                while (await dr.ReadAsync())
                {
                    calls.Add(new Entry(dr));
                }

                return calls;
            }
        }
        
        public static IEnumerable<Entry> GetByIds(int[] ids) => GetByIds(ids, null);

        public static async Task<IEnumerable<Entry>> GetByIdsAsync(int[] ids) => await GetByIdsAsync(ids, null);

        public static IEnumerable<Entry> GetByIds(int[] ids, IEnumerable<Invoice> invoices) => GetByIds(ids, invoices, true);

        public static async Task<IEnumerable<Entry>> GetByIdsAsync(int[] ids, IEnumerable<Invoice> invoices) => await GetByIdsAsync(ids, invoices, true);

        public static IEnumerable<Entry> GetByIds(int[] ids, IEnumerable<Invoice> invoices, bool initializeChildrenProperties)
        {
            var entries = new Collection<Entry>();

            if (ids.Count() == 0)
                return entries;

            foreach (var batch in ids.Batch(500))
            {
                var parameters = new List<string>();
                var p = new List<SqlParameter>();
                int i = 0;
                foreach (var row in batch)
                {
                    i++;
                    parameters.Add("@P" + i);
                    p.Add(new SqlParameter("@P" + i, row));
                }

                var sql = $"SELECT * FROM vwDispatchEntries D WITH (NOLOCK) WHERE DispatchEntryId IN ({string.Join(",", parameters)})";

                using (SqlDataReader dr = SqlHelper.ExecuteReader(Core.ConnectionString, System.Data.CommandType.Text, sql, p.ToArray()))
                {
                    while (dr.Read())
                    {
                        entries.Add(new Entry(dr));
                    }
                }
            }

            if (initializeChildrenProperties)
                return EntriesInitializeMultiple(entries, null, invoices);
            else
                return entries;
        }

        public static async Task<IEnumerable<Entry>> GetByIdsAsync(int[] ids, IEnumerable<Invoice> invoices, bool initializeChildrenProperties)
        {
            var entries = new Collection<Entry>();

            if (ids.Count() == 0)
                return entries;

            foreach (var batch in ids.Batch(500))
            {
                var parameters = new List<string>();
                var p = new List<SqlParameter>();
                int i = 0;
                foreach (var row in batch)
                {
                    i++;
                    parameters.Add("@P" + i);
                    p.Add(new SqlParameter("@P" + i, row));
                }

                var sql = $"SELECT * FROM vwDispatchEntries D WITH (NOLOCK) WHERE DispatchEntryId IN ({string.Join(",", parameters)})";

                using (SqlDataReader dr = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString, System.Data.CommandType.Text, sql, p.ToArray()))
                {
                    while (await dr.ReadAsync())
                    {
                        entries.Add(new Entry(dr));
                    }
                }
            }

            if (initializeChildrenProperties)
                return await EntriesInitializeMultipleAsync(entries, null, invoices);
            else
                return entries;
        }


        public static async Task<Entry> GetByCallNumberAsync(int callNumber, Company.Company company)
        {
            using (SqlDataReader dr = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString,
                "DispatchEntriesGetByCallNumber",
                new SqlParameter("@CallNumber", callNumber),
                new SqlParameter("@CompanyId", company.Id)))
            {
                if (await dr.ReadAsync())
                {
                    Entry en = new Entry(dr);
                    return en;
                }
                else
                {
                    return null;
                }
            }
        }

        public static async Task<Entry> GetByPurchaseOrderNumberAsync(int accountId, string purchaseOrderNumber)
        {
            using (SqlDataReader dr = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString,
                "DispatchEntriesGetByPurchaseOrderNumber",
                new SqlParameter("@AccountId", accountId),
                new SqlParameter("@PurchaseOrderNumber", purchaseOrderNumber)))
            {
                if (await dr.ReadAsync())
                {
                    Entry en = new Entry(dr);
                    return en;
                }
                else
                {
                    return null;
                }
            }
        }

        public static async Task<IEnumerable<Entry>> GetByPurchaseOrderNumbersByMasterAccountIdAsync(int companyId, int masterAccountId, string[] purchaseOrderNumbers) =>
            await GetByPurchaseOrderNumbersByMasterAccountIdAsync(new[] { companyId }, masterAccountId, purchaseOrderNumbers);


        public static async Task<IEnumerable<Entry>> GetByPurchaseOrderNumbersByMasterAccountIdAsync(int[] companyIds, int masterAccountId, string[] purchaseOrderNumber)
        {
            var list = new List<Entry>();


            foreach (var batch in purchaseOrderNumber.Batch(250))
            {
                var parameters = new List<string>();

                var p = new List<SqlParameter>();
                p.Add(new SqlParameter("@MasterAccountId", masterAccountId));


                int i = 0;
                foreach (var row in batch)
                {
                    i++;
                    parameters.Add("@P" + i);
                    p.Add(new SqlParameter("@P" + i, row));
                }

                var companyIdParameters = new List<string>();
                foreach (var row in companyIds)
                {
                    i++;
                    companyIdParameters.Add("@CompanyId" + i);
                    p.Add(new SqlParameter("@CompanyId" + i, row));
                }

                var sql = "SELECT x.* FROM vwDispatchEntries x WITH (nolock) inner join accounts A on A.AccountId=x.AccountId and " +
                    " A.MasterAccountId=@MasterAccountId AND x.CompanyId IN(" + string.Join(",", companyIdParameters) + ") AND PurchaseOrderNumber IN ("
                    + string.Join(",", parameters) + ")";

                using (var dr = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString, System.Data.CommandType.Text, sql,
                     p.ToArray()))
                {
                    while (await dr.ReadAsync())
                    {
                        list.Add(new Entry(dr));
                    }
                }
            }

            return await EntriesInitializeMultipleAsync(list.ToCollection());
        }

        /// <summary>
        /// Find a PO by searching for it within a company for the specified masterAccountId.
        /// </summary>
        /// <param name="companyId"></param>
        /// <param name="masterAccountId"></param>
        /// <param name="purchaseOrderNumber"></param>
        /// <returns></returns>
        public static async Task<Entry> GetByMasterAccountPurchaseOrderNumberAsync(int companyId, int masterAccountId, string purchaseOrderNumber)
        {
            using (SqlDataReader dr = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString,
                "DispatchEntriesGetByMasterAccountPurchaseOrderNumber",
                new SqlParameter("@CompanyId", companyId),
                new SqlParameter("@MasterAccountId", masterAccountId),
                new SqlParameter("@PurchaseOrderNumber", purchaseOrderNumber)))
            {
                var list = new List<Entry>();
                while (await dr.ReadAsync())
                {
                    Entry en = new Entry(dr);
                    list.Add(en);
                }

                return list.OrderBy(o => o.Status == Status.Completed).ToList().FirstOrDefault();
            }
        }


        public static async Task<Collection<Entry>> GetByCallNumberAsync(int[] callNumber, Company.Company company)
        {
            if (company == null)
                throw new TowbookException("Must pass non-null Company for GetByCallNumber method.");

            return await GetByCallNumberAsync(callNumber, company.Id);
        }

        public static async Task<Collection<Entry>> GetByCallNumberAsync(int[] callNumber, int companyId)
        {
            Collection<Entry> list = new Collection<Entry>();

            if (callNumber.Length == 0)
                return list;

            string callNumbers = string.Join(",", callNumber);

            if (callNumbers.Length > 8000)
                throw new TowbookException("CallNumber array exceeded length of 8000. Shorten your query to return less calls or contact Support to increase your limit");

            using (SqlDataReader dr = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString,
                "DispatchEntriesGetByCallNumberArray",
                new SqlParameter("@CallNumberArray", callNumbers),
                new SqlParameter("@CompanyId", companyId)))
            {
                while (await dr.ReadAsync())
                {
                    list.Add(new Entry(dr));
                }
            }

            return await EntriesInitializeMultipleAsync(list);
        }

        public static async Task<Collection<Entry>> GetByCallNumberAsync(int callNumber, int[] companies)
        {
            Collection<Entry> list = new Collection<Entry>();

            if (companies.Length == 0)
                return list;

            string companyIds = string.Join(",", companies);

            using (SqlDataReader dr = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString,
                "DispatchEntriesGetCallNumberByCompanyIdArray",
                new SqlParameter("@CallNumber", callNumber),
                new SqlParameter("@CompanyIds", companyIds)))
            {
                while (await dr.ReadAsync())
                {
                    list.Add(new Entry(dr));
                }
            }

            return await EntriesInitializeMultipleAsync(list);
        }

        public static Collection<Entry> GetByStatementId(int statementId, bool unpaidOnly = false)
        {
            Collection<Entry> list = new Collection<Entry>();

            if (statementId < 1)
                return list;

            using (SqlDataReader dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                "DispatchEntriesGetByStatementId",
                new SqlParameter("@StatementId", statementId),
                new SqlParameter("@UnpaidOnly", unpaidOnly)))
            {
                while (dr.Read())
                {
                    list.Add(new Entry(dr));
                }
            }

            return EntriesInitializeMultiple(list);
        }

        public static async Task<Collection<Entry>> GetByStatementIdAsync(int statementId, bool unpaidOnly = false)
        {
            Collection<Entry> list = new Collection<Entry>();

            if (statementId < 1)
                return list;

            using (SqlDataReader dr = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString,
                "DispatchEntriesGetByStatementId",
                new SqlParameter("@StatementId", statementId),
                new SqlParameter("@UnpaidOnly", unpaidOnly)))
            {
                while (await dr.ReadAsync())
                {
                    list.Add(new Entry(dr));
                }
            }

            return await EntriesInitializeMultipleAsync(list);
        }

        public override string ToString()
        {
            return String.Format("Extric.Towbook.Dispatch.Entry [Id = {0}, CompanyId = {1} ]", _id, _companyId);
        }

        private void InitializeFromDataReader(SqlDataReader reader)
        {
            _id = Convert.ToInt32(reader["DispatchEntryId"]);
            _callNumber = Convert.ToInt32((reader["CallNumber"] != DBNull.Value ? reader["CallNumber"] : reader["DispatchEntryId"]));
            _companyId = Convert.ToInt32(reader["CompanyId"]);
            _type = (EntryType)reader.GetValue<byte>("Type");
            _ownerUserId = Convert.ToInt32(reader["OwnerUserId"]);

            _reasonId = reader.GetValue<int>("DispatchReasonId");



            if (reader["VehicleYear"] != DBNull.Value)
                _year = Convert.ToInt32(reader["VehicleYear"]);

            _colorId = reader.GetValue<int>("VehicleColorId");
            _manufacturerId = reader.GetValue<int>("VehicleManufacturerId");
            _modelId = reader.GetValue<int>("VehicleModelId");
            _bodyTypeId = reader.GetValue<int>("VehicleBodyTypeId");
            _vin = reader.GetValue<string>("VehicleVIN");

            if (reader["Priority"] != DBNull.Value)
                _priority = (EntryPriority)Convert.ToInt32(reader["Priority"]);

            _status = Status.GetById(reader.GetValue<int>("Status"), _companyId);
            _created = Convert.ToBoolean(reader["Created"]);
            _impound = Convert.ToBoolean(reader["Impound"]);
            _invoiceStatusId = reader.GetValue<int>("InvoiceStatusId");

            _released = reader.GetValue<bool>("Released");

            try
            {
                PaymentsApplied = reader.GetValue<decimal>("PaymentsApplied");
            }
            catch (IndexOutOfRangeException)
            {
                PaymentsApplied = 0;
            }

            CancellationReason = reader.GetValue<string>("CancellationReason");

            _licenseNumber = Convert.ToString(reader["VehicleLicenseNumber"]);
            _licenseState = Convert.ToString(reader["VehicleLicenseState"]);

            if (reader["VehicleOdometer"] != DBNull.Value)
                _odometer = Convert.ToInt32(reader["VehicleOdometer"]);

            if (reader["VehicleDrivable"] != DBNull.Value)
                _drivable = reader.GetValue<bool>("VehicleDrivable");
            else
                _drivable = false;

            if (reader["Notes"] != DBNull.Value)
                _notes = Convert.ToString(reader["Notes"]);

            _accountId = reader.GetValue<int>("AccountId");
            _ownerUserId = Convert.ToInt32(reader["OwnerUserId"]);

            if (reader["TowSource"] != DBNull.Value)
                _towSource = Convert.ToString(reader["TowSource"]);

            if (reader["TowDestination"] != DBNull.Value)
                _towDestination = Convert.ToString(reader["TowDestination"]);

            _truckId = reader.GetValue<int>("TruckId");
            _driverId = reader.GetValue<int>("DriverId");

            if (reader["DispatchTime"] != DBNull.Value)
                _dispatchTime = Convert.ToDateTime(reader["DispatchTime"]);

            if (reader["ArrivalTime"] != DBNull.Value)
                _arrivalTime = Convert.ToDateTime(reader["ArrivalTime"]);

            if (reader["EnRouteTime"] != DBNull.Value)
                _enrouteTime = reader.GetValue<DateTime>("EnRouteTime");

            if (reader["ArrivalETA"] != DBNull.Value)
                _arrivalETA = Convert.ToDateTime(reader["ArrivalETA"]);

            if (reader["TowTime"] != DBNull.Value)
                _towTime = Convert.ToDateTime(reader["TowTime"]);

            if (reader["DestinationArrivalTime"] != DBNull.Value)
                _destinationArrivalTime = Convert.ToDateTime(reader["DestinationArrivalTime"]);

            if (reader["CompletionTime"] != DBNull.Value)
                _completionTime = Convert.ToDateTime(reader["CompletionTime"]);

            _createDate = Convert.ToDateTime(reader["CreateDate"]);
            _version = reader.GetValue<int>("Version");
            _invoiceNumber = reader.GetValue<string>("InvoiceNumber");
            _purchaseOrderNumber = reader.GetValue<string>("PurchaseOrderNumber");
            DispatchEntryLockId = reader.GetValueOrDefault<int>("DispatchEntryLockId");

            // Vehicle asset if not one of these types: 8:Equipment, 9:Container or 10:Material
            var bodyType = BodyType.GetById(_bodyTypeId);
            IsVehicleAsset = bodyType != null && !new List<string> { "Equipment", "Container", "Material" }.Contains(bodyType.Name);

            Statements = Array.Empty<int>();
        }

        public sealed class SearchQuery
        {
            public string VIN;
            public Nullable<DateTime> StartDate;
            public Nullable<DateTime> EndDate;
            public Driver Driver;
            public Truck Truck;
            public int? UserId;

            public string MakeString = null;
            public string ModelString = null;
            public int? Year;

            public string ContactName = null;
            public string ContactPhone = null;

            public Vehicle.Manufacturer VehicleManufacturer = Vehicle.Manufacturer.None;
            public Vehicle.Model VehicleModel = Vehicle.Model.None;
            public int? AccountId;

            public static bool IsEmpty(SearchQuery sq)
            {
                if (string.IsNullOrWhiteSpace(sq.VIN) &&
                   sq.StartDate == null &&
                   sq.EndDate == null &&
                   (sq.Driver == null || sq.Driver.Id == 0) &&
                   string.IsNullOrWhiteSpace(sq.ContactName) &&
                   string.IsNullOrWhiteSpace(sq.ContactPhone) &&
                   string.IsNullOrWhiteSpace(sq.MakeString) &&
                   string.IsNullOrWhiteSpace(sq.ModelString) &&
                   (sq.Year == null || sq.Year.Value == 0) &&
                   (sq.Truck == null || sq.Truck.Id == 0) &&
                   (sq.UserId == null || sq.UserId.Value == 0) &&
                   (sq.AccountId.GetValueOrDefault() > 0) &&
                   sq.VehicleManufacturer == Vehicle.Manufacturer.None &&
                   sq.VehicleModel == Vehicle.Model.None)
                {
                    return true;
                }
                return false;
            }
        }

        public static Collection<Entry> Search(Company.Company company,
            SearchQuery sq)
        {
            return Search(new int[] { company.Id }, sq);
        }

        public static async Task<Collection<Entry>> SearchAsync(Company.Company company,
           SearchQuery sq)
        {
            return await SearchAsync(new int[] { company.Id }, sq);
        }

        public static Collection<Entry> Search(int companyId,
            SearchQuery sq)
        {
            return Search(new int[] { companyId }, sq);
        }

        public static async Task<Collection<Entry>> SearchAsync(int companyId,
           SearchQuery sq)
        {
            return await SearchAsync(new int[] { companyId }, sq);
        }

        public static Collection<Entry> Search(int[] companyIds,
            SearchQuery sq, int? startAtId = null)
        {
            Collection<Entry> entries = new Collection<Entry>();

            if (companyIds == null)
            {
                throw new ApplicationException("Must pass a non-null Company object");
            }

            if (sq == null)
            {
                throw new ApplicationException("Must pass a non-null SearchQuery object");
            }

            if (SearchQuery.IsEmpty(sq))
                throw new ApplicationException("SearchQuery doesn't contain any non-null/empty values. You must specify at least 1 field.");

            Nullable<int> driverId;
            Nullable<int> truckId;
            driverId = sq.Driver != null ? (Nullable<int>)sq.Driver.Id : null;
            truckId = sq.Truck != null ? (Nullable<int>)sq.Truck.Id : null;

            using (SqlDataReader dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                "DispatchEntriesGetBySearchQuery",
                new SqlParameter("@CompanyId", String.Join(",", companyIds)),
                new SqlParameter("@VIN", (String.IsNullOrEmpty(sq.VIN) ? null : sq.VIN)),
                new SqlParameter("@StartDate", sq.StartDate),
                new SqlParameter("@EndDate", sq.EndDate),
                new SqlParameter("@DriverId", driverId),
                new SqlParameter("@TruckId", truckId),
                new SqlParameter("@ContactName", (String.IsNullOrWhiteSpace(sq.ContactName) ? null : sq.ContactName)),
                new SqlParameter("@ContactPhone", (String.IsNullOrWhiteSpace(sq.ContactPhone) ? null : sq.ContactPhone)),
                new SqlParameter("@MakeString", (String.IsNullOrWhiteSpace(sq.MakeString) ? null : sq.MakeString)),
                new SqlParameter("@ModelString", (String.IsNullOrWhiteSpace(sq.ModelString) ? null : sq.ModelString)),
                new SqlParameter("@VehicleManufacturerId",
                    (sq.VehicleManufacturer != null && sq.VehicleManufacturer != Vehicle.Manufacturer.None ? (int?)sq.VehicleManufacturer.Id : null)),
                new SqlParameter("@VehicleModelId",
                    (sq.VehicleModel != null && sq.VehicleModel != Vehicle.Model.None ? (int?)sq.VehicleModel.Id : null)),
                    new SqlParameter("@OwnerUserId", sq.UserId),
                    new SqlParameter("@AccountId", sq.AccountId),
                    new SqlParameter("@Year", sq.Year),
                    new SqlParameter("@StartAtDispatchEntryNumber", startAtId),
                    new SqlParameter("@Limit", 40)))
            {
                while (dr.Read())
                {
                    entries.Add(new Entry(dr));
                }

                return entries;
            }
        }

        public static async Task<Collection<Entry>> SearchAsync(int[] companyIds,
            SearchQuery sq, int? startAtId = null)
        {
            Collection<Entry> entries = new Collection<Entry>();

            if (companyIds == null)
            {
                throw new ApplicationException("Must pass a non-null Company object");
            }

            if (sq == null)
            {
                throw new ApplicationException("Must pass a non-null SearchQuery object");
            }

            if (SearchQuery.IsEmpty(sq))
                throw new ApplicationException("SearchQuery doesn't contain any non-null/empty values. You must specify at least 1 field.");

            Nullable<int> driverId;
            Nullable<int> truckId;
            driverId = sq.Driver != null ? (Nullable<int>)sq.Driver.Id : null;
            truckId = sq.Truck != null ? (Nullable<int>)sq.Truck.Id : null;

            using (SqlDataReader dr = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString,
                "DispatchEntriesGetBySearchQuery",
                new SqlParameter("@CompanyId", String.Join(",", companyIds)),
                new SqlParameter("@VIN", (String.IsNullOrEmpty(sq.VIN) ? null : sq.VIN)),
                new SqlParameter("@StartDate", sq.StartDate),
                new SqlParameter("@EndDate", sq.EndDate),
                new SqlParameter("@DriverId", driverId),
                new SqlParameter("@TruckId", truckId),
                new SqlParameter("@ContactName", (String.IsNullOrWhiteSpace(sq.ContactName) ? null : sq.ContactName)),
                new SqlParameter("@ContactPhone", (String.IsNullOrWhiteSpace(sq.ContactPhone) ? null : sq.ContactPhone)),
                new SqlParameter("@MakeString", (String.IsNullOrWhiteSpace(sq.MakeString) ? null : sq.MakeString)),
                new SqlParameter("@ModelString", (String.IsNullOrWhiteSpace(sq.ModelString) ? null : sq.ModelString)),
                new SqlParameter("@VehicleManufacturerId",
                    (sq.VehicleManufacturer != null && sq.VehicleManufacturer != Vehicle.Manufacturer.None ? (int?)sq.VehicleManufacturer.Id : null)),
                new SqlParameter("@VehicleModelId",
                    (sq.VehicleModel != null && sq.VehicleModel != Vehicle.Model.None ? (int?)sq.VehicleModel.Id : null)),
                    new SqlParameter("@OwnerUserId", sq.UserId),
                    new SqlParameter("@AccountId", sq.AccountId),
                    new SqlParameter("@Year", sq.Year),
                    new SqlParameter("@StartAtDispatchEntryNumber", startAtId),
                    new SqlParameter("@Limit", 40)))
            {
                while (await dr.ReadAsync())
                {
                    entries.Add(new Entry(dr));
                }

                return entries;
            }
        }
        public static IEnumerable<Entry> FindByPurchaseOrder(
            int[] companies,
            string purchaseOrderNumber)
        {
            var entries = new Collection<Entry>();

            using (SqlDataReader dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                "DispatchEntriesGetByPurchaseOrderNumberCompany",
               new SqlParameter("@CompanyId", string.Join(",", companies)),
               new SqlParameter("@PurchaseOrderNumber", purchaseOrderNumber)))
            {
                while (dr.Read())
                {
                    entries.Add(new Entry(dr));
                }
            }

            return entries;
        }

        public static async Task<IEnumerable<Entry>> FindByPurchaseOrderAsync(
            int[] companies,
            string purchaseOrderNumber)
        {
            var entries = new Collection<Entry>();

            using (SqlDataReader dr = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString,
                "DispatchEntriesGetByPurchaseOrderNumberCompany",
               new SqlParameter("@CompanyId", string.Join(",", companies)),
               new SqlParameter("@PurchaseOrderNumber", purchaseOrderNumber)))
            {
                while (await dr.ReadAsync())
                {
                    entries.Add(new Entry(dr));
                }
            }

            return entries;
        }

        public static IEnumerable<Entry> FindByInvoiceNumber(
            int[] companies,
            string invoiceNumber)
        {
            var entries = new Collection<Entry>();

            using (SqlDataReader dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                "DispatchEntriesGetByInvoiceNumber",
               new SqlParameter("@CompanyId", string.Join(",", companies)),
               new SqlParameter("@InvoiceNumber", invoiceNumber)))
            {
                while (dr.Read())
                {
                    entries.Add(new Entry(dr));
                }
            }

            return entries;
        }


        public static Collection<Entry> GetByCompany(Company.Company company,
            User user,
            DateTime? startDate = null,
            DateTime? endDate = null,
            Int32? dispatchReasonId = null,
            Int32? accountId = null,
            Int32? driverId = null,
            Int32? truckId = null,
            Int32? accountType = null,
            Int32? bodyTypeId = null,
            Int32? accountManagerUserId = null)
        {
            var entries = new Collection<Entry>();

            int? userId = null;

            if (user != null)
                userId = user.Id;

            using (SqlDataReader dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                "DispatchEntriesGetByCompanyId",
                    new SqlParameter("@CompanyId", company.Id),
                    new SqlParameter("@OwnerUserId", userId),
                    new SqlParameter("@StartDate", startDate),
                    new SqlParameter("@EndDate", endDate),
                    new SqlParameter("@DispatchReasonId", dispatchReasonId),
                    new SqlParameter("@AccountId", accountId),
                    new SqlParameter("@DriverId", driverId),
                    new SqlParameter("@TruckId", truckId),
                    new SqlParameter("@AccountType", accountType),
                    new SqlParameter("@BodyTypeId", bodyTypeId),
                    new SqlParameter("@AccountManagerUserId", accountManagerUserId)))
            {
                while (dr.Read())
                {
                    entries.Add(new Entry(dr));
                }
            }
            
            entries = entries.OrderBy(o => o.CreateDate).ToCollection();

            return EntriesInitializeMultiple(entries);
        }


        public static Collection<Entry> GetByCompany(int[] companyIds,
            User user,
            DateTime? startDate = null,
            DateTime? endDate = null,
            int? dispatchReasonId = null,
            int? accountId = null,
            int? driverId = null,
            int? truckId = null,
            int? accountType = null,
            int? bodyTypeId = null,
            int statusId = 0) =>
                GetByCompany(
                    companyIds,
                    user,
                    driverId != null ? new int[] { driverId.Value }.ToArray() : Array.Empty<int>(),
                    startDate,
                    endDate,
                    dispatchReasonId,
                    accountId,
                    truckId,
                    accountType,
                    bodyTypeId,
                    null,
                    new int[] { statusId });

        /// <summary>
        /// Return a report of calls for use in reporting/read-only use only. 
        /// Do not use this in Dispatching. 
        /// It won't apply pricing rules or update storage.
        /// </summary>
        /// <param name="companyIds"></param>
        /// <param name="user"></param>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <param name="dispatchReasonId"></param>
        /// <param name="accountId"></param>
        /// <param name="driverId"></param>
        /// <param name="truckId"></param>
        /// <param name="accountType"></param>
        /// <param name="bodyTypeId"></param>
        /// <param name="masterAccountId"></param>
        /// <param name="statusId"></param>
        /// <returns></returns>
        public static Collection<Entry> GetByCompany(int[] companyIds, User user,
            DateTime? startDate = null,
            DateTime? endDate = null,
            int? dispatchReasonId = null,
            int? accountId = null,
            int? driverId = null,
            int? truckId = null,
            int? accountType = null,
            int? bodyTypeId = null,
            int? masterAccountId = null,
            int statusId = 0) =>
                GetByCompany(
                    companyIds,
                    user,
                    driverId != null ? new int[] { driverId.Value }.ToArray() : Array.Empty<int>(),
                    startDate,
                    endDate,
                    dispatchReasonId,
                    accountId,
                    truckId,
                    accountType,
                    bodyTypeId,
                    masterAccountId,
                    new int[] { statusId });

        public static Collection<Entry> GetByCompany(int[] companyIds,
            User user,
            int[] driverId,
            DateTime? startDate = null,
            DateTime? endDate = null,
            int? dispatchReasonId = null,
            int? accountId = null,
            int? truckId = null,
            int? accountType = null,
            int? bodyTypeId = null,
            int? masterAccountId = null,
            int[] statusId = null) => GetByCompany(companyIds,
                user,
                driverId,
                startDate,
                endDate,
                dispatchReasonId,
                accountId != null ? new int[] { accountId.Value }.ToArray() : Array.Empty<int>(),
                truckId,
                accountType,
                bodyTypeId,
                masterAccountId,
                statusId);

        public static Collection<Entry> GetByCompany(int[] companyIds,
            User user,
            int[] driverId,
            DateTime? startDate = null,
            DateTime? endDate = null,
            int? dispatchReasonId = null,
            int[] accountId = null,
            int? truckId = null,
            int? accountType = null,
            int? bodyTypeId = null,
            int? masterAccountId = null,
            int[] statusId = null,
            int? isLocked = null,
            int? isBilled = null) => GetByCompany(companyIds,
                user,
                driverId,
                startDate,
                endDate,
                dispatchReasonId,
                accountId,
                truckId,
                accountType != null ? new int[] { accountType.Value }.ToArray() : Array.Empty<int>(),
                bodyTypeId,
                masterAccountId,
                statusId);

        private class TempCompanyAccount
        {
            public int CompanyId { get; set; }
            public int AccountId { get; set; }
            public int OriginalAccountId { get; set; }
        }


        public static async Task<Collection<Entry>> GetByCompanyAsync(int[] companyIds,
            User user = null,
            int[] driverId = null,
            DateTime? startDate = null,
            DateTime? endDate = null,
            int? dispatchReasonId = null,
            int[] accountId = null,
            int? truckId = null,
            int[] accountType = null,
            int? bodyTypeId = null,
            int? masterAccountId = null,
            int[] statusId = null,
            int? isLocked = null,
            int? isBilled = null,
            int? accountManagerUserId = null,
            int? auctionInclusion = null,
            IEnumerable<LockedDispatchEntry> lockedDispatchEntries = null,
            bool lookupDatesByLastModified = false,
            bool getDeleted = false)
        {
            foreach (var cid in companyIds)
            {
                var comp = await Towbook.Company.Company.GetByIdAsync(cid);
                await Driver.GetByExactCompanyIdAsync(cid);
                await Truck.GetByCompanyAsync(comp);
                RateItem.GetByCompanyId(cid);
                await TaxRate.GetByCompanyAsync(comp);
                await Account.GetByExactCompanyId(comp, true);
            }

            var stopwatch = Stopwatch.StartNew();

            var pk = companyIds.First().ToString();

            if (companyIds.Length > 1)
                pk = null;

            var queryText = new StringBuilder();
            
            queryText.Append($"select * from c where c.companyId IN ({string.Join(",", companyIds)})");

            var c = new Collection<CosmosParameter>();

            if (user != null)
            {
                queryText.Append(" AND c.owner.id=@ownerUserId");
                c.Add(new CosmosParameter("@ownerUserId", user.Id));
            }

            if (driverId != null && driverId.Any(o => o > 0))
            {
                queryText.Append(" AND (EXISTS(SELECT VALUE t FROM t IN c.assets JOIN dx IN t.drivers");
                queryText.Append($" WHERE dx.driver.id IN({string.Join(",", driverId)})))");
            }

            if (truckId != null && truckId > 0)
            {
                queryText.Append(" AND (EXISTS(SELECT VALUE t2 FROM t2 IN c.assets JOIN tx IN t2.drivers ");
                queryText.Append($" WHERE tx.truck.id IN({string.Join(",", new int[] { truckId.Value })})))");
            }

            if (bodyTypeId != null && bodyTypeId > 0)
            {
                queryText.Append(" AND (EXISTS(SELECT VALUE t2 FROM t2 IN c.assets ");
                queryText.Append($" WHERE t2.bodyType.id IN({string.Join(",", new int[] { bodyTypeId.Value })})))");
            }

            if (lockedDispatchEntries != null)
            { 
                queryText.Append($" AND c.id IN({string.Join(",", lockedDispatchEntries.Select(r => r.DispatchEntryId.ToString().SurroundWith("\"")))})");
            }
            else
            {
                if (startDate != null && endDate != null)
                {
                    if (lookupDatesByLastModified)
                    {
                        var start = startDate.Value;
                        // convert startDate to seconds since unix epoch
                        var startSeconds = (long)(startDate.Value.ToUniversalTime() - new DateTime(1970, 1, 1)).TotalSeconds;
                        var endSeconds = (long)(endDate.Value.ToUniversalTime() - new DateTime(1970, 1, 1)).TotalSeconds;

                        queryText.Append($" AND c._ts >= {startSeconds} and c._ts < {endSeconds}");
                    }
                    else
                    {
                        var endDateStr = endDate.Value.ToString("o");
                        if (endDateStr.Contains("."))
                            endDateStr = endDateStr.Substring(0, endDateStr.IndexOf('.'));

                        var startDateStr = startDate.Value.ToString("o");
                        if (startDateStr.Contains("."))
                            startDateStr = startDateStr.Substring(0, startDateStr.IndexOf('.'));

                        queryText.Append($" AND c.completionTime >= '{startDateStr}' and c.completionTime < '{endDateStr}'");
                    }

                }
            }

            if (dispatchReasonId != null)
            {
                queryText.Append(" AND c.reason.id=@reasonId");
                c.Add(new CosmosParameter("@reasonId", dispatchReasonId));
            }

            if (accountId != null && accountId.Any(o => o > 0))
            {
                queryText.Append($" AND c.account.id IN ({string.Join(",", accountId)})");
            }

            if (accountType != null && accountType.Count() > 0)
            {
                queryText.Append($" AND c.account.typeId in ({string.Join(",", accountType)})");
            }

            if (masterAccountId.GetValueOrDefault() > 0)
            {
                queryText.Append(" AND c.account.masterAccountId=@masterAccountId");
                c.Add(new CosmosParameter("@masterAccountId", masterAccountId));
            }

            if (accountManagerUserId.GetValueOrDefault() > 0)
            {
                queryText.Append(" AND (EXISTS(SELECT VALUE av FROM av IN c.attributes ");
                queryText.Append($"WHERE av.attributeId IN (74) AND av['value'] in (ToString(" + accountManagerUserId + "))))");
            }

            if (auctionInclusion.GetValueOrDefault() > 0)
            {
                // only include auctions
                if(auctionInclusion.GetValueOrDefault() == 1)
                {
                    queryText.Append(" AND IS_DEFINED(c.impoundDetails.auction) ");
                    queryText.Append("AND c.impoundDetails.auction = true");
                }

                // exclude auction
                if (auctionInclusion.GetValueOrDefault() == 2)
                {
                    queryText.Append(" AND (NOT IS_DEFINED(c.impoundDetails.auction) ");
                    queryText.Append("or c.impoundDetails.auction != true)");
                }
            }

            if (statusId != null)
            {
                queryText.Append($" AND c.status.id IN ({string.Join(",", statusId)})");
            }

            if (isLocked != null)
            {
                if (isLocked == 1)
                    queryText.Append($" AND IS_DEFINED(c.insights.lockedByUserId)");
                else if (isLocked == 0)
                    queryText.Append($" AND NOT IS_DEFINED(c.insights.lockedByUserId)");
            }

            if (isBilled != null)
            {
                if (isBilled.Value == 1)
                    queryText.Append(" AND c.invoiceStatusId = 5");
                else if (isBilled.Value == 0)
                    queryText.Append(" AND (NOT IS_DEFINED(c.invoiceStatusId) or c.invoiceStatusId != 5)");
            }

            var query = new Microsoft.Azure.Cosmos.QueryDefinition(queryText.ToString());

            foreach (var x in c)
            {
                query = query.WithParameter(x.Key, x.Value);
            }

            var containerName = "calls";
            if (getDeleted)
                containerName = "calls-deleted";

            // Get it from cosmos
            var cosmosCalls = await CosmosDB.Get().QueryItemsAsync<CosmosCallModel>(
                containerName,
                query,
                pk);

            Console.WriteLine(query.ToJson(true));

            Console.WriteLine("got " + cosmosCalls.Count + " in " + stopwatch.ElapsedMilliseconds);

            var sw3 = Stopwatch.StartNew();
            var entries = (await Task.WhenAll(cosmosCalls.Select(async o => await o.MapBackAsync())))
                .OrderBy(o => o.Id)
                .ToCollection();

            Console.WriteLine("mapped back to C# objects after total " + sw3.ElapsedMilliseconds);

            var invoiceItems = entries.SelectMany(o => o.InvoiceItems);

            var sw4 = Stopwatch.StartNew();
            var commissions = InvoiceItem.InvoiceItemDriverCommission.GetByInvoiceItemIds(
                invoiceItems
                .Select(o => o.Id)
                .OrderBy(o => o).ToArray())
                .GroupBy(x => x.InvoiceItemId)
                .ToDictionary(a => a.Key, b => b.ToCollection());

            foreach (var each in invoiceItems)
            {
                each.DriverCommissions = commissions.GetValueOrDefault(each.Id) ?? new Collection<InvoiceItem.InvoiceItemDriverCommission>();
            }

            Console.WriteLine("mapped back to C# objetcs after total " + sw4.ElapsedMilliseconds);

            foreach (var r in entries)
            {
                // reports don't support acknowledge feature yet. return original status.
                // not sure if this is neccesary.

                if (r.Status?.Id == Status.CancelledAcknowledgePending.Id)
                    r.Status = Status.Cancelled;

                if (r.Status?.Id == Status.CompletedAcknowledgePending.Id)
                    r.Status = Status.Completed;

                AppServices.Cache.Add("de:" + r.Id, TimeSpan.FromMinutes(CacheTimeout), r);
            }

            return entries;
        }

        public static Collection<Entry> GetByCompany(int[] companyIds,
            User user,
            int[] driverId,
            DateTime? startDate = null,
            DateTime? endDate = null,
            int? dispatchReasonId = null,
            int[] accountId = null,
            int? truckId = null,
            int[] accountType = null,
            int? bodyTypeId = null,
            int? masterAccountId = null,
            int[] statusId = null,
            int? isLocked = null,
            int? isBilled = null,
            int? accountManagerUserId = null)
        {
            if (statusId != null && statusId.Length == 1 && statusId[0] == 0)
                statusId = null;

            var entries = new Collection<Entry>();

            int? userId = null;

            if (user != null)
                userId = user.Id;
            if (companyIds == null || companyIds.Length == 0)
                throw new TowbookException("companyIds must be passed with at least one ID");

            if (startDate == null && endDate != null)
                startDate = Convert.ToDateTime("1/1/2000");
            if (endDate == null && startDate != null)
                endDate = Convert.ToDateTime("1/1/2100");

            var sql = @"SELECT DE.*, BillToAccountId=I.AccountId" + (statusId != null && statusId.Contains(255) ? ", CancelledDate=DEC.CreateDate" : "") + @"
                FROM
                    vwDispatchEntries DE WITH (NOLOCK)
                        INNER JOIN Invoices I  WITH (NOLOCK) ON DE.DispatchEntryId = I.DispatchEntryId";

            if (statusId != null)
            {
                if (statusId.Contains(255) && statusId.Any(r => r != 255))
                    sql += @" LEFT OUTER JOIN DispatchEntryCancellations DEC WITH (NOLOCK) ON
                                DE.DispatchEntryId = DEC.DispatchEntryId AND DEC.Deleted = 0";
                else if (statusId.Contains(255))
                    sql += @" INNER JOIN DispatchEntryCancellations DEC WITH (NOLOCK) ON
                                DE.DispatchEntryId = DEC.DispatchEntryId AND DEC.Deleted = 0";
            }

            sql += " WHERE ";

            IEnumerable<TempCompanyAccount> mappings = null;

            if (companyIds.Contains(49039))
            {
                string extra = "";
                int iz = 0;
                var ps = new List<SqlParameter>();
                var dictionary = new Dictionary<string, object>();

                if (accountId != null && accountId.Any())
                {
                    List<string> accountParams1 = new List<string>();
                    foreach (var row in accountId)
                    {
                        iz++;
                        accountParams1.Add("@ACC" + iz);
                        dictionary.Add("@ACC" + iz, row);
                    }

                    extra += " AND(AccountId in (" + string.Join(",", accountParams1) + "))";
                }

                mappings = SqlMapper.Query<TempCompanyAccount>(
                    "select cast(right(ReferenceNumber, len(referencenumber)-charindex('|', referencenumber)) as int) as AccountId,  cast(left(ReferenceNumber, charindex('|', referencenumber)-1) as int) as CompanyId, AccountId as OriginalAccountId from accounts where companyid=49039 and ReferenceNumber like '%|%' and deleted=0 " + extra,
                    new DynamicParameters(dictionary));
                if (mappings.Any())
                {
                    companyIds = mappings.Select(o => o.CompanyId).ToArray();
                    accountId = mappings.Select(o => o.AccountId).ToArray();
                }
            }

            var parameters = new List<string>();
            var length = companyIds.Count();
            var p = new List<SqlParameter>();
            int i = 0;
            foreach (var row in companyIds)
            {
                i++;
                parameters.Add("@P" + i);
                p.Add(new SqlParameter("@P" + i, row));
            }

            sql += " DE.CompanyId IN(" + string.Join(",", parameters) + ")";

            if (user != null)
            {
                sql += " AND DE.OwnerUserId = @OwnerUserId";
                p.Add(new SqlParameter("@OwnerUserId", userId));
            }
            if (startDate != null && endDate != null)
            {
                if (statusId != null && statusId.Count() == 1 && statusId.Contains(255))
                    sql += " AND(DEC.CreateDate >= @StartDate) AND(DEC.CreateDate < @EndDate)";
                else
                    sql += " AND(coalesce(CompletionTime, DE.CreateDate) >= @StartDate)" +
                            " AND(coalesce(CompletionTime, DE.CreateDate) < @EndDate)";
                p.Add(new SqlParameter("@StartDate", startDate));
                p.Add(new SqlParameter("@EndDate", endDate));
            }

            if (driverId != null && driverId.Length > 0)
            {
                var driverParams = new List<string>();
                foreach (var row in driverId)
                {
                    i++;
                    driverParams.Add("@D" + i);
                    p.Add(new SqlParameter("@D" + i, row));
                }

                sql += " AND(DriverId IN (" + string.Join(",", driverParams) + "))";
            }
            if (truckId != null)
            {
                sql += " AND(TruckId = @TruckId)";
                p.Add(new SqlParameter("@TruckId", truckId));
            }
            if (accountId != null && accountId.Any())
            {
                var accountParams = new List<string>();
                foreach (var row in accountId)
                {
                    i++;
                    accountParams.Add("@ACC" + i);
                    p.Add(new SqlParameter("@ACC" + i, row));
                }

                sql += " AND(DE.AccountId in (" + string.Join(",", accountParams) + ") OR (DE.AccountId != I.AccountId AND I.AccountId in (" + string.Join(",", accountParams) + ")))";
            }

            if (masterAccountId != null)
            {
                sql += " AND(DE.AccountId IN (Select AccountId from Accounts WITH (NOLOCK) WHERE MasterAccountId = @MasterAccountId))";
                p.Add(new SqlParameter("@MasterAccountId", masterAccountId));
            }

            if (accountManagerUserId != null)
            {
                sql += " AND(DE.DispatchEntryId IN (SELECT TOP 1 DispatchEntryId FROM DispatchEntryAttributeValues WITH (NOLOCK) WHERE DispatchEntryId=DE.DispatchEntryId AND DispatchEntryAttributeId=74 AND Value=CAST(@AccountManagerUserId AS VARCHAR(255))))";
                p.Add(new SqlParameter("@AccountManagerUserId", accountManagerUserId));
            }

            if (dispatchReasonId != null)
            {
                sql += " AND(DispatchReasonId = @DispatchReasonId)";
                p.Add(new SqlParameter("@DispatchReasonId", dispatchReasonId));
            }
            if (accountType != null && accountType.Any())
            {
                List<string> accountTypeParams = new List<string>();
                foreach (var row in accountType)
                {
                    i++;
                    accountTypeParams.Add("@ACT" + i);
                    p.Add(new SqlParameter("@ACT" + i, row));
                }

                sql += " AND(DE.AccountId IN(select AccountId from Accounts WITH (NOLOCK) WHERE TypeId IN (" + string.Join(",", accountTypeParams) + ") AND Deleted=0 AND CompanyID in(" + string.Join(",", parameters) + ")))";
            }
            if (bodyTypeId != null)
            {
                sql += " AND(VehicleBodyTypeId = @BodyTypeId)";
                p.Add(new SqlParameter("@BodyTypeId", bodyTypeId));
            }

            if (statusId != null && statusId.Any())
            {
                var statusParams = new List<string>();
                foreach (var row in statusId)
                {
                    i++;
                    statusParams.Add("@ST" + i);
                    p.Add(new SqlParameter("@ST" + i, row));
                }

                sql += " AND(Status IN (" + string.Join(",", statusParams) + "))";
            }

            if (isLocked != null)
            {
                if (isLocked.Value == 1)
                    sql += " AND DE.DispatchEntryLockId IS NOT NULL";
                else
                    sql += " AND DE.DispatchEntryLockId IS NULL";
            }

            if (isBilled != null)
            {
                if (isBilled.Value == 1)
                    sql += " AND DE.InvoiceStatusId = 5";
                else
                    sql += " AND COALESCE(DE.InvoiceStatusId , 0) <> 5";
            }

            // tell it not to use cached query plan.. our queries are too different because our parameters vary so much
            // one might be one companyid, another might be 120 companyIds, for example.
            sql += " OPTION (OPTIMIZE FOR UNKNOWN) ";

            using (var dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                System.Data.CommandType.Text,
                sql,
                p.ToArray()))
            {
                while (dr.Read())
                {
                    entries.Add(new Entry(dr));
                }
            }

            var ret = EntriesInitializeMultiple(entries).OrderBy(o => o.CreateDate).ToCollection();

            if (mappings != null)
            {
                var sql2 = "select cast(dr.PurchaseOrderNumber as int) as SenderCallId, de.CallNumber as SenderCallNumber, de.AccountId, dr.DispatchEntryId as SubCallId, cast( dv.Value as int) as SubcontractorId From DispatchEntryRequests  dr" +
                    " inner join DispatchEntries de on de.DispatchEntryId = dr.PurchaseOrderNumber " +
                    " inner join DispatchEntryAttributeValues dv on dv.DispatchEntryAttributeId=67  and dv.DispatchEntryId=de.DispatchEntryId" +
                    " where dr.DispatchEntryId in @x";

                var senderCalls = SqlMapper.Query<TempEntry>(sql2,
                    new
                    {
                        x = entries.Select(o => o.Id).ToArray()
                    }).ToCollection();


                foreach (var e in ret)
                {
                    var sender = senderCalls.FirstOrDefault(r => r.SubCallId == e.Id);

                    if (sender != null)
                    {
                        e.SetAttribute(AttributeValue.BUILTIN_SUBCONTRACTOR_ID, sender.SubcontractorId.ToString());
                        e.AccountId = sender.AccountId;
                        e._id = sender.SenderCallId;
                        e._callNumber = sender.SenderCallNumber;
                    }
                }
            }


            return ret;

        }

        public static async Task<Collection<Entry>> GetByCompanyWithExecuteReaderAsync(int[] companyIds,
            User user,
            int[] driverId,
            DateTime? startDate = null,
            DateTime? endDate = null,
            int? dispatchReasonId = null,
            int[] accountId = null,
            int? truckId = null,
            int[] accountType = null,
            int? bodyTypeId = null,
            int? masterAccountId = null,
            int[] statusId = null,
            int? isLocked = null,
            int? isBilled = null,
            int? accountManagerUserId = null)
        {
            if (statusId != null && statusId.Length == 1 && statusId[0] == 0)
                statusId = null;

            var entries = new Collection<Entry>();

            int? userId = null;

            if (user != null)
                userId = user.Id;
            if (companyIds == null || companyIds.Length == 0)
                throw new TowbookException("companyIds must be passed with at least one ID");

            if (startDate == null && endDate != null)
                startDate = Convert.ToDateTime("1/1/2000");
            if (endDate == null && startDate != null)
                endDate = Convert.ToDateTime("1/1/2100");

            var sql = @"SELECT DE.*, BillToAccountId=I.AccountId" + (statusId != null && statusId.Contains(255) ? ", CancelledDate=DEC.CreateDate" : "") + @"
                FROM
                    vwDispatchEntries DE WITH (NOLOCK)
                        INNER JOIN Invoices I  WITH (NOLOCK) ON DE.DispatchEntryId = I.DispatchEntryId";

            if (statusId != null)
            {
                if (statusId.Contains(255) && statusId.Any(r => r != 255))
                    sql += @" LEFT OUTER JOIN DispatchEntryCancellations DEC WITH (NOLOCK) ON
                                DE.DispatchEntryId = DEC.DispatchEntryId AND DEC.Deleted = 0";
                else if (statusId.Contains(255))
                    sql += @" INNER JOIN DispatchEntryCancellations DEC WITH (NOLOCK) ON
                                DE.DispatchEntryId = DEC.DispatchEntryId AND DEC.Deleted = 0";
            }

            sql += " WHERE ";

            IEnumerable<TempCompanyAccount> mappings = null;

            if (companyIds.Contains(49039))
            {
                string extra = "";
                int iz = 0;
                var ps = new List<SqlParameter>();
                var dictionary = new Dictionary<string, object>();

                if (accountId != null && accountId.Any())
                {
                    List<string> accountParams1 = new List<string>();
                    foreach (var row in accountId)
                    {
                        iz++;
                        accountParams1.Add("@ACC" + iz);
                        dictionary.Add("@ACC" + iz, row);
                    }

                    extra += " AND(AccountId in (" + string.Join(",", accountParams1) + "))";
                }

                mappings = SqlMapper.Query<TempCompanyAccount>(
                    "select cast(right(ReferenceNumber, len(referencenumber)-charindex('|', referencenumber)) as int) as AccountId,  cast(left(ReferenceNumber, charindex('|', referencenumber)-1) as int) as CompanyId, AccountId as OriginalAccountId from accounts where companyid=49039 and ReferenceNumber like '%|%' and deleted=0 " + extra,
                    new DynamicParameters(dictionary));
                if (mappings.Any())
                {
                    companyIds = mappings.Select(o => o.CompanyId).ToArray();
                    accountId = mappings.Select(o => o.AccountId).ToArray();
                }
            }

            var parameters = new List<string>();
            var length = companyIds.Count();
            var p = new List<SqlParameter>();
            int i = 0;
            foreach (var row in companyIds)
            {
                i++;
                parameters.Add("@P" + i);
                p.Add(new SqlParameter("@P" + i, row));
            }

            sql += " DE.CompanyId IN(" + string.Join(",", parameters) + ")";

            if (user != null)
            {
                sql += " AND DE.OwnerUserId = @OwnerUserId";
                p.Add(new SqlParameter("@OwnerUserId", userId));
            }
            if (startDate != null && endDate != null)
            {
                if (statusId != null && statusId.Count() == 1 && statusId.Contains(255))
                    sql += " AND(DEC.CreateDate >= @StartDate) AND(DEC.CreateDate < @EndDate)";
                else
                    sql += " AND(coalesce(CompletionTime, DE.CreateDate) >= @StartDate)" +
                            " AND(coalesce(CompletionTime, DE.CreateDate) < @EndDate)";
                p.Add(new SqlParameter("@StartDate", startDate));
                p.Add(new SqlParameter("@EndDate", endDate));
            }

            if (driverId != null && driverId.Length > 0)
            {
                var driverParams = new List<string>();
                foreach (var row in driverId)
                {
                    i++;
                    driverParams.Add("@D" + i);
                    p.Add(new SqlParameter("@D" + i, row));
                }

                sql += " AND(DriverId IN (" + string.Join(",", driverParams) + "))";
            }
            if (truckId != null)
            {
                sql += " AND(TruckId = @TruckId)";
                p.Add(new SqlParameter("@TruckId", truckId));
            }
            if (accountId != null && accountId.Any())
            {
                var accountParams = new List<string>();
                foreach (var row in accountId)
                {
                    i++;
                    accountParams.Add("@ACC" + i);
                    p.Add(new SqlParameter("@ACC" + i, row));
                }

                sql += " AND(DE.AccountId in (" + string.Join(",", accountParams) + ") OR (DE.AccountId != I.AccountId AND I.AccountId in (" + string.Join(",", accountParams) + ")))";
            }

            if (masterAccountId != null)
            {
                sql += " AND(DE.AccountId IN (Select AccountId from Accounts WITH (NOLOCK) WHERE MasterAccountId = @MasterAccountId))";
                p.Add(new SqlParameter("@MasterAccountId", masterAccountId));
            }

            if (accountManagerUserId != null)
            {
                sql += " AND(DE.DispatchEntryId IN (SELECT TOP 1 DispatchEntryId FROM DispatchEntryAttributeValues WITH (NOLOCK) WHERE DispatchEntryId=DE.DispatchEntryId AND DispatchEntryAttributeId=74 AND Value=CAST(@AccountManagerUserId AS VARCHAR(255))))";
                p.Add(new SqlParameter("@AccountManagerUserId", accountManagerUserId));
            }

            if (dispatchReasonId != null)
            {
                sql += " AND(DispatchReasonId = @DispatchReasonId)";
                p.Add(new SqlParameter("@DispatchReasonId", dispatchReasonId));
            }
            if (accountType != null && accountType.Any())
            {
                List<string> accountTypeParams = new List<string>();
                foreach (var row in accountType)
                {
                    i++;
                    accountTypeParams.Add("@ACT" + i);
                    p.Add(new SqlParameter("@ACT" + i, row));
                }

                sql += " AND(DE.AccountId IN(select AccountId from Accounts WITH (NOLOCK) WHERE TypeId IN (" + string.Join(",", accountTypeParams) + ") AND Deleted=0 AND CompanyID in(" + string.Join(",", parameters) + ")))";
            }
            if (bodyTypeId != null)
            {
                sql += " AND(VehicleBodyTypeId = @BodyTypeId)";
                p.Add(new SqlParameter("@BodyTypeId", bodyTypeId));
            }

            if (statusId != null && statusId.Any())
            {
                var statusParams = new List<string>();
                foreach (var row in statusId)
                {
                    i++;
                    statusParams.Add("@ST" + i);
                    p.Add(new SqlParameter("@ST" + i, row));
                }

                sql += " AND(Status IN (" + string.Join(",", statusParams) + "))";
            }

            if (isLocked != null)
            {
                if (isLocked.Value == 1)
                    sql += " AND DE.DispatchEntryLockId IS NOT NULL";
                else
                    sql += " AND DE.DispatchEntryLockId IS NULL";
            }

            if (isBilled != null)
            {
                if (isBilled.Value == 1)
                    sql += " AND DE.InvoiceStatusId = 5";
                else
                    sql += " AND COALESCE(DE.InvoiceStatusId , 0) <> 5";
            }

            // tell it not to use cached query plan.. our queries are too different because our parameters vary so much
            // one might be one companyid, another might be 120 companyIds, for example.
            sql += " OPTION (OPTIMIZE FOR UNKNOWN) ";

            using (var dr = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString,
                System.Data.CommandType.Text,
                sql,
                p.ToArray()))
            {
                while (await dr.ReadAsync())
                {
                    entries.Add(new Entry(dr));
                }
            }

            var ret = (await EntriesInitializeMultipleAsync(entries)).OrderBy(o => o.CreateDate).ToCollection();

            if (mappings != null)
            {
                var sql2 = "select cast(dr.PurchaseOrderNumber as int) as SenderCallId, de.CallNumber as SenderCallNumber, de.AccountId, dr.DispatchEntryId as SubCallId, cast( dv.Value as int) as SubcontractorId From DispatchEntryRequests  dr" +
                    " inner join DispatchEntries de on de.DispatchEntryId = dr.PurchaseOrderNumber " +
                    " inner join DispatchEntryAttributeValues dv on dv.DispatchEntryAttributeId=67  and dv.DispatchEntryId=de.DispatchEntryId" +
                    " where dr.DispatchEntryId in @x";

                var senderCalls = SqlMapper.Query<TempEntry>(sql2,
                    new
                    {
                        x = entries.Select(o => o.Id).ToArray()
                    }).ToCollection();


                foreach (var e in ret)
                {
                    var sender = senderCalls.FirstOrDefault(r => r.SubCallId == e.Id);

                    if (sender != null)
                    {
                        e.SetAttribute(AttributeValue.BUILTIN_SUBCONTRACTOR_ID, sender.SubcontractorId.ToString());
                        e.AccountId = sender.AccountId;
                        e._id = sender.SenderCallId;
                        e._callNumber = sender.SenderCallNumber;
                    }
                }
            }


            return ret;

        }

        public class TempEntry
        {
            public int SubCallId { get; set; }
            public int SenderCallId { get; set; }
            public int AccountId { get; set; }
            public int SubcontractorId { get; set; }
            public int SenderCallNumber { get; set; }


        }
        /// <summary>
        /// Retrieve a list of completed calls, starting at the specified call number. 
        /// </summary>
        public static async Task<Collection<Entry>> GetCompletedByCompanyAsync(int companyId, int startAtDispatchEntryNumber, int limit = 20)
        {
            return await GetByCompaniesAsync(new int[] { companyId }, 5, startAtDispatchEntryNumber, limit);
        }

        public static async Task<Collection<Entry>> GetByCompaniesAsync(int[] companyIds, int statusId, int startAtDispatchEntryNumber, int limit = 20)
        {
            return await GetByCompaniesAsync(companyIds, null, statusId, startAtDispatchEntryNumber, limit);
        }

        /// <summary>
        /// Retrieve a list of completed calls, starting at the specified call number. 
        /// </summary>
        /// <param name="startAtCallNumber"></param>
        /// <param name="p"></param>
        /// <param name="companyIds">An array of companyId's to return calls for</param>
        public static async Task<Collection<Entry>> GetByCompaniesAsync(int[] companyIds, int[] driverIds, int statusId, int startAtDispatchEntryNumber, int limit = 20)
        {
            if (driverIds == null || !driverIds.Any())
                return await GetByCompaniesFastAsync(companyIds, statusId, startAtDispatchEntryNumber, limit);


            Collection<Entry> list = new Collection<Entry>();
            using (var dr = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString,
                     "DispatchEntriesGetCompletedByCompanyId",
                     new SqlParameter("@CompanyId", String.Join(",", companyIds)),
                     new SqlParameter("@DriverId", (driverIds != null ? String.Join(",", driverIds) : null)),
                     new SqlParameter("@StartAtDispatchEntryNumber", startAtDispatchEntryNumber),
                     new SqlParameter("@Limit", limit),
                     new SqlParameter("@StatusId", statusId)))
            {
                while (await dr.ReadAsync())
                {
                    list.Add(new Entry(dr));
                }
            }

            return list;
        }

        private static async Task<Collection<Entry>> GetByCompaniesFastAsync(int[] companyIds, int statusId, int startAtDispatchEntryNumber, int limit = 20)
        {
            Collection<Entry> list = new Collection<Entry>();
            using (var dr = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString, System.Data.CommandType.Text,
                     $"SELECT TOP {limit} * FROM vwDispatchEntries WITH (nolock) WHERE CompanyId IN({string.Join(",", companyIds)}) AND DispatchEntryId < @StartAtDispatchEntryId AND Status=@StatusId ORDER BY DispatchEntryId DESC",
                     new SqlParameter("@StartAtDispatchEntryId", startAtDispatchEntryNumber),
                     new SqlParameter("@StatusId", statusId)))
            {
                while (await dr.ReadAsync())
                {
                    list.Add(new Entry(dr));
                }
            }

            return list;
        }

        public static async Task<Collection<Entry>> GetByCompaniesAsync(
            int[] companyIds,
            int[] driverIds,
            int statusId,
            int pageNumber,
            int pageSize = 20,
            string sortColumn = "")
        {
            if (pageNumber < 1)
                pageNumber = 1;

            if (!(sortColumn == "" ||
                sortColumn == "CompletionTime" ||
                sortColumn == "CallNumber" ||
                sortColumn == "CreateDate"))
            {
                throw new Exception("SortColumn must be empty, CompletionTime, CreateDate or CallNumber");
            }

            var list = new Collection<Entry>();

            using (var dr = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString,
                "DispatchEntriesGetCompletedByCompanyIdPageNumber",
                new SqlParameter("@CompanyId", String.Join(",", companyIds)),
                new SqlParameter("@DriverId", (driverIds != null ? String.Join(",", driverIds) : null)),
                new SqlParameter("@PageNumber", pageNumber),
                new SqlParameter("@PageSize", pageSize),
                new SqlParameter("@SortColumn", sortColumn),
                new SqlParameter("@StatusId", statusId)))
            {
                while (await dr.ReadAsync())
                {
                    list.Add(new Entry(dr));
                }
            }

            return await EntriesInitializeMultipleAsync(list);
        }

        //If you change this method, don't forget to change the async version as well
        //used in dispatchentries.ashx
        public static Collection<Entry> GetCurrentByCompany(
           Company.Company company,
           bool showCurrent,
           bool showPrevious,
           bool showCancelled,
           bool newDispatchingMode = false)
        {
            return GetCurrentByCompany(new int[] { company.Id }, showCurrent, showPrevious, showCancelled, newDispatchingMode);
        }

        public static async Task<Collection<Entry>> GetCurrentByCompanyAsync(
           Company.Company company,
           bool showCurrent,
           bool showPrevious,
           bool showCancelled,
           bool newDispatchingMode = false)
        {
            return await GetCurrentByCompanyAsync(new int[] { company.Id }, showCurrent, showPrevious, showCancelled, newDispatchingMode);
        }

        //If you change this method, don't forget to change the async version as well
        public static Collection<Entry> GetCurrentByCompany(
            int[] companies,
            bool showCurrent,
            bool showPrevious,
            bool showCancelled,
            bool newDispatchingMode = false,
            int[] driverIds = null)
        {
            Stopwatch sw = Stopwatch.StartNew();
            int completedCalls = 0;
            int cancelledCalls = 0;

            try
            {
                Collection<Entry> entries = new Collection<Entry>();
                Collection<Entry> entriesCompleted = new Collection<Entry>();

                SqlParameter companyParam = null;

                if (newDispatchingMode)
                    companyParam = new SqlParameter("@CompanyId", String.Join(",", companies));
                else
                    companyParam = new SqlParameter("@CompanyId", companies[0]);

                var spName = (newDispatchingMode == true ? "DispatchEntriesGetCurrentByCompanyIdWithCompleted" : "DispatchEntriesGetCurrentByCompanyId");

                if (driverIds != null && driverIds.Any())
                    spName = "DispatchEntriesGetCurrentByCompanyIdWithCompletedByDriverId";

                using (SqlDataReader dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                    spName, // old dispatching
                    companyParam,
                    new SqlParameter("@ShowCurrent", showCurrent),
                    new SqlParameter("@ShowPrevious", showPrevious),
                    new SqlParameter("@ShowCancelled", showCancelled),
                    new SqlParameter("DriverId", driverIds != null ? String.Join(",", driverIds) : null)))
                {
                    while (dr.Read())
                    {
                        int id = dr.GetValue<int>("DispatchEntryId");

                        Entry d = new Entry(dr);

                        if (d != null)
                        {
                            if (d.Status == EntryStatus.Completed)
                            {
                                completedCalls++;


                                if (completedCalls > 20 && !newDispatchingMode)
                                    continue;
                            }
                            if (d.Status == EntryStatus.Canceled)
                            {
                                cancelledCalls++;
                                if (cancelledCalls > 20)
                                    continue;
                            }

                            if (d.Status == EntryStatus.Completed)
                                entriesCompleted.Add(d);
                            else
                                entries.Add(d);
                        }
                    }
                }

                foreach (Entry n in entriesCompleted)
                    entries.Add(n);

                return EntriesInitializeMultiple(entries, sw);
            }
            finally
            {
                //System.Diagnostics.Debug.WriteLine("GetCurrentByCompany/Entry Elapsed " + sw.ElapsedMilliseconds + "ms");
            }
        }

        public static async Task<Collection<Entry>> GetCurrentByCompanyAsync(
            int[] companies,
            bool showCurrent,
            bool showPrevious,
            bool showCancelled,
            bool newDispatchingMode = false,
            int[] driverIds = null)
        {
            Stopwatch sw = Stopwatch.StartNew();
            int completedCalls = 0;
            int cancelledCalls = 0;

            try
            {
                Collection<Entry> entries = new Collection<Entry>();
                Collection<Entry> entriesCompleted = new Collection<Entry>();

                SqlParameter companyParam = null;

                if (newDispatchingMode)
                    companyParam = new SqlParameter("@CompanyId", String.Join(",", companies));
                else
                    companyParam = new SqlParameter("@CompanyId", companies[0]);

                var spName = (newDispatchingMode == true ? "DispatchEntriesGetCurrentByCompanyIdWithCompleted" : "DispatchEntriesGetCurrentByCompanyId");

                if (driverIds != null && driverIds.Any())
                    spName = "DispatchEntriesGetCurrentByCompanyIdWithCompletedByDriverId";

                using (SqlDataReader dr = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString,
                    spName, // old dispatching
                    companyParam,
                    new SqlParameter("@ShowCurrent", showCurrent),
                    new SqlParameter("@ShowPrevious", showPrevious),
                    new SqlParameter("@ShowCancelled", showCancelled),
                    new SqlParameter("DriverId", driverIds != null ? String.Join(",", driverIds) : null)))
                {
                    while (await dr.ReadAsync())
                    {
                        int id = dr.GetValue<int>("DispatchEntryId");

                        Entry d = new Entry(dr);

                        if (d != null)
                        {
                            if (d.Status == EntryStatus.Completed)
                            {
                                completedCalls++;


                                if (completedCalls > 20 && !newDispatchingMode)
                                    continue;
                            }
                            if (d.Status == EntryStatus.Canceled)
                            {
                                cancelledCalls++;
                                if (cancelledCalls > 20)
                                    continue;
                            }

                            if (d.Status == EntryStatus.Completed)
                                entriesCompleted.Add(d);
                            else
                                entries.Add(d);
                        }
                    }
                }

                foreach (Entry n in entriesCompleted)
                    entries.Add(n);

                return await EntriesInitializeMultipleAsync(entries, sw);
            }
            finally
            {
                //System.Diagnostics.Debug.WriteLine("GetCurrentByCompany/Entry Elapsed " + sw.ElapsedMilliseconds + "ms");
            }
        }

        public static IEnumerable<Entry> GetCurrentByCompanyId(int companyId)
        {
            var list = new Collection<Entry>();

            var s = Stopwatch.StartNew();
            using (SqlDataReader dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                "DispatchEntriesGetCurrentByCompanyIdWithCompleted",
                    new SqlParameter("@CompanyId", companyId),
                    new SqlParameter("@ShowCurrent", true),
                    new SqlParameter("@ShowPrevious", false),
                    new SqlParameter("@ShowCancelled", false),
                    new SqlParameter("@DriverId", (int?)null)))
            {
                while (dr.Read())
                {
                    int id = dr.GetValue<int>("DispatchEntryId");

                    Entry d = new Entry(dr);
                    list.Add(d);
                }
            }

            foreach (var c in EntryWaypoint.GetByDispatchEntryIds(list.Select(o => o.Id))
                .GroupBy(o => o.DispatchEntryId))
            {
                list.Where(o => o.Id == c.Key).First().waypoints = c.ToCollection();
            }

            foreach (var c in EntryAsset.GetByDispatchEntries(list.Select(o => o.Id).ToCollection())
                .GroupBy(o => o.DispatchEntryId))
            {
                list.Where(o => o.Id == c.Key).First().assets = c.ToCollection();
            }
            Console.WriteLine(s.ElapsedMilliseconds + "ms");

            return list;
        }

        //If you change this method, don't forget to change the async version as well
        public static Collection<Entry> EntriesInitializeMultiple(IEnumerable<Entry> entries, Stopwatch sw = null, IEnumerable<Invoice> invoices = null)
        {
            
            if (sw == null)
            {
                sw = Stopwatch.StartNew();
            }
            var dispatchIds = entries.OrderBy(o => o.Id).Select(o => o.Id).ToCollection();

            if (dispatchIds.Count == 0)
                return new Collection<Entry>();


            #region Contacts

            var dd = new Dictionary<int, Collection<EntryContact>>();

            foreach (var c in EntryContact.GetByDispatchEntryIds(dispatchIds))
            {
                if (!dd.ContainsKey(c.DispatchEntryId))
                    dd.Add(c.DispatchEntryId, new Collection<EntryContact>());

                dd[c.DispatchEntryId].Add(c);
            }

            //System.Diagnostics.Debug.WriteLine("EntriesInitializeMultiple/Entry Elapsed " + sw.ElapsedMilliseconds + "ms - finished getting contacts");

            #endregion

            #region Waypoints

            var dw = new Dictionary<int, Collection<EntryWaypoint>>();

            foreach (var c in EntryWaypoint.GetByDispatchEntryIds(dispatchIds))
            {
                if (!dw.ContainsKey(c.DispatchEntryId))
                    dw.Add(c.DispatchEntryId, new Collection<EntryWaypoint>());

                dw[c.DispatchEntryId].Add(c);
            }

            //System.Diagnostics.Debug.WriteLine("EntriesInitializeMultiple/Entry Elapsed " + sw.ElapsedMilliseconds + "ms - finished getting waypoints");

            #endregion

            #region Attributes

            var dav = new Dictionary<int, Dictionary<int, AttributeValue>>();

            foreach (var c in AttributeValue.GetByDispatchEntries(dispatchIds))
            {
                if (!dav.ContainsKey(c.DispatchEntryId))
                    dav.Add(c.DispatchEntryId, new Dictionary<int, AttributeValue>());

                if (!dav[c.DispatchEntryId].ContainsKey(c.DispatchEntryAttributeId))
                    dav[c.DispatchEntryId].Add(c.DispatchEntryAttributeId, c);
            }

            //System.Diagnostics.Debug.WriteLine("EntriesInitializeMultiple/Entry Elapsed " + sw.ElapsedMilliseconds + "ms - finished getting attributes");

            #endregion

            #region Assets

            var dAssets = new Dictionary<int, Collection<EntryAsset>>();

            foreach (var c in EntryAsset.GetByDispatchEntries(dispatchIds))
            {
                if (!dAssets.ContainsKey(c.DispatchEntryId))
                    dAssets.Add(c.DispatchEntryId, new Collection<EntryAsset>());

                dAssets[c.DispatchEntryId].Add(c);
            }
            
            //System.Diagnostics.Debug.WriteLine("EntriesInitializeMultiple/Entry Elapsed " + sw.ElapsedMilliseconds + "ms - finished getting attributes");

            #endregion

            var payments = InvoicePayment.GetByDispatchEntryIds(entries.Select(o => o.Id).ToArray(), null).Where(o => !o.IsVoid);

            #region Tags
            /*
            var dTags = new Dictionary<int, Collection<int>>();

            foreach (var c in EntryTag.GetByDispatchEntries(dispatchIds))
            {
                if (!dTags.ContainsKey(c.DispatchEntryId))
                    dTags.Add(c.DispatchEntryId, new Collection<int>());

                dTags[c.DispatchEntryId].Add(c.TagId);
            }

            System.Diagnostics.Debug.WriteLine("EntriesInitializeMultiple/Entry Elapsed " + sw.ElapsedMilliseconds + "ms - finished getting tags");
            */
            #endregion


            foreach (var e in entries)
            {
                if (dd.ContainsKey(e.Id))
                {
                    e.Contacts = dd[e.Id].OrderBy(o => o.Id).ToCollection();
                }
                else
                {
                    e.Contacts = new Collection<EntryContact>();
                }

                if (dav.ContainsKey(e.Id))
                {
                    e.Attributes = dav[e.Id];
                }
                else
                {
                    e.Attributes = new Dictionary<int, AttributeValue>();
                }

                if (dAssets.ContainsKey(e.Id))
                {
                    e.Assets = dAssets[e.Id];
                }
                else
                {
                    e.Assets = new Collection<EntryAsset>();
                }

                if (dw.ContainsKey(e.Id))
                {
                    e.Waypoints = dw[e.Id].OrderBy(o => o.Id).ToCollection();
                }
                else
                {
                    e.Waypoints = new Collection<EntryWaypoint>();
                }
                /*
                if (dTags.ContainsKey(e.Id))
                {
                    e.Tags = dTags[e.Id];
                }
                else
                {
                    e.Tags = new Collection<int>();
                }
                */

            }

            #region Invoices

            if (invoices == null)
                invoices = Invoice.GetByDispatchEntries(dispatchIds);

            Dictionary<int, Invoice> dInvoices = new Dictionary<int, Invoice>();
            if (invoices != null)
            {
                foreach (var invoice in invoices)
                {
                    invoice.SetPayments(payments.Where(payment => payment.InvoiceId == invoice.Id).ToCollection());

                    if (!dInvoices.ContainsKey(invoice.DispatchEntryId))
                        dInvoices[invoice.DispatchEntryId] = invoice;

                    AppServices.Cache.AddToPerRequestCache("inv_d:" + invoice.DispatchEntryId, invoice);
                }
            }
            else
            {
                var rv = Invoice.GetByDispatchEntries(dispatchIds);
                foreach (var c in rv)
                {
                    if (!dInvoices.ContainsKey(c.DispatchEntryId))
                        dInvoices[c.DispatchEntryId] = c;

                    AppServices.Cache.AddToPerRequestCache("inv_d:" + c.DispatchEntryId, c);
                }
            }

            //System.Diagnostics.Debug.WriteLine("EntriesInitializeMultiple/Entry Elapsed " + sw.ElapsedMilliseconds + "ms - finished getting attributes");

            #endregion


            foreach (var e in entries)
            {
                if (dInvoices.ContainsKey(e.Id))
                {
                    e.Invoice = dInvoices[e.Id];
                    e.Invoice.DispatchEntry = e;
                }
            }

            return entries.ToCollection();
        }

        /// <summary>
        /// Takes a clean copy of Entry's, and fills up their Assts, Waypoints, Tags, etc.
        /// </summary>
        /// <param name="sw"></param>
        /// <param name="entries"></param>
        /// <returns></returns>
        public static async Task<Collection<Entry>> EntriesInitializeMultipleAsync(IEnumerable<Entry> entries, Stopwatch sw = null, IEnumerable<Invoice> invoices = null)
        {
            
            if (sw == null)
            {
                sw = Stopwatch.StartNew();
            }
            var dispatchIds = entries.OrderBy(o => o.Id).Select(o => o.Id).ToCollection();

            if (dispatchIds.Count == 0)
                return new Collection<Entry>();


            #region Contacts

            var dd = new Dictionary<int, Collection<EntryContact>>();

            foreach (var c in await EntryContact.GetByDispatchEntryIdsAsync(dispatchIds))
            {
                if (!dd.ContainsKey(c.DispatchEntryId))
                    dd.Add(c.DispatchEntryId, new Collection<EntryContact>());

                dd[c.DispatchEntryId].Add(c);
            }

            //System.Diagnostics.Debug.WriteLine("EntriesInitializeMultiple/Entry Elapsed " + sw.ElapsedMilliseconds + "ms - finished getting contacts");

            #endregion

            #region Waypoints

            var dw = new Dictionary<int, Collection<EntryWaypoint>>();

            foreach (var c in await EntryWaypoint.GetByDispatchEntryIdsAsync(dispatchIds))
            {
                if (!dw.ContainsKey(c.DispatchEntryId))
                    dw.Add(c.DispatchEntryId, new Collection<EntryWaypoint>());

                dw[c.DispatchEntryId].Add(c);
            }

            //System.Diagnostics.Debug.WriteLine("EntriesInitializeMultiple/Entry Elapsed " + sw.ElapsedMilliseconds + "ms - finished getting waypoints");

            #endregion

            #region Attributes

            var dav = new Dictionary<int, Dictionary<int, AttributeValue>>();

            foreach (var c in await AttributeValue.GetByDispatchEntriesAsync(dispatchIds))
            {
                if (!dav.ContainsKey(c.DispatchEntryId))
                    dav.Add(c.DispatchEntryId, new Dictionary<int, AttributeValue>());

                if (!dav[c.DispatchEntryId].ContainsKey(c.DispatchEntryAttributeId))
                    dav[c.DispatchEntryId].Add(c.DispatchEntryAttributeId, c);
            }

            //System.Diagnostics.Debug.WriteLine("EntriesInitializeMultiple/Entry Elapsed " + sw.ElapsedMilliseconds + "ms - finished getting attributes");

            #endregion

            #region Assets

            var dAssets = new Dictionary<int, Collection<EntryAsset>>();

            foreach (var c in EntryAsset.GetByDispatchEntries(dispatchIds))
            {
                if (!dAssets.ContainsKey(c.DispatchEntryId))
                    dAssets.Add(c.DispatchEntryId, new Collection<EntryAsset>());

                dAssets[c.DispatchEntryId].Add(c);
            }
            
            //System.Diagnostics.Debug.WriteLine("EntriesInitializeMultiple/Entry Elapsed " + sw.ElapsedMilliseconds + "ms - finished getting attributes");

            #endregion

            var payments = (await InvoicePayment.GetByDispatchEntryIdsAsync(entries.Select(o => o.Id).ToArray(), null)).Where(o => !o.IsVoid);

            #region Tags
            /*
            var dTags = new Dictionary<int, Collection<int>>();

            foreach (var c in EntryTag.GetByDispatchEntries(dispatchIds))
            {
                if (!dTags.ContainsKey(c.DispatchEntryId))
                    dTags.Add(c.DispatchEntryId, new Collection<int>());

                dTags[c.DispatchEntryId].Add(c.TagId);
            }

            System.Diagnostics.Debug.WriteLine("EntriesInitializeMultiple/Entry Elapsed " + sw.ElapsedMilliseconds + "ms - finished getting tags");
            */
            #endregion


            foreach (var e in entries)
            {
                if (dd.ContainsKey(e.Id))
                {
                    e.Contacts = dd[e.Id].OrderBy(o => o.Id).ToCollection();
                }
                else
                {
                    e.Contacts = new Collection<EntryContact>();
                }

                if (dav.ContainsKey(e.Id))
                {
                    e.Attributes = dav[e.Id];
                }
                else
                {
                    e.Attributes = new Dictionary<int, AttributeValue>();
                }

                if (dAssets.ContainsKey(e.Id))
                {
                    e.Assets = dAssets[e.Id];
                }
                else
                {
                    e.Assets = new Collection<EntryAsset>();
                }

                if (dw.ContainsKey(e.Id))
                {
                    e.Waypoints = dw[e.Id].OrderBy(o => o.Id).ToCollection();
                }
                else
                {
                    e.Waypoints = new Collection<EntryWaypoint>();
                }
                /*
                if (dTags.ContainsKey(e.Id))
                {
                    e.Tags = dTags[e.Id];
                }
                else
                {
                    e.Tags = new Collection<int>();
                }
                */

            }

            #region Invoices

            if (invoices == null)
                invoices = await Invoice.GetByDispatchEntriesAsync(dispatchIds);

            Dictionary<int, Invoice> dInvoices = new Dictionary<int, Invoice>();
            if (invoices != null)
            {
                foreach (var invoice in invoices)
                {
                    invoice.SetPayments(payments.Where(payment => payment.InvoiceId == invoice.Id).ToCollection());

                    if (!dInvoices.ContainsKey(invoice.DispatchEntryId))
                        dInvoices[invoice.DispatchEntryId] = invoice;

                    AppServices.Cache.AddToPerRequestCache("inv_d:" + invoice.DispatchEntryId, invoice);
                }
            }
            else
            {
                var rv = await Invoice.GetByDispatchEntriesAsync(dispatchIds);
                foreach (var c in rv)
                {
                    if (!dInvoices.ContainsKey(c.DispatchEntryId))
                        dInvoices[c.DispatchEntryId] = c;

                    AppServices.Cache.AddToPerRequestCache("inv_d:" + c.DispatchEntryId, c);
                }
            }

            //System.Diagnostics.Debug.WriteLine("EntriesInitializeMultiple/Entry Elapsed " + sw.ElapsedMilliseconds + "ms - finished getting attributes");

            #endregion


            foreach (var e in entries)
            {
                if (dInvoices.ContainsKey(e.Id))
                {
                    e.Invoice = dInvoices[e.Id];
                    e.Invoice.DispatchEntry = e;
                }
            }

            return entries.ToCollection();
        }


        /// <summary>
        /// Get a list of deleted calls for the company.
        /// </summary>
        /// <param name="companyId"></param>
        /// <returns></returns>
        public static Collection<Entry> GetDeletedByCompanyId(int companyId, int pageNumber = 1, int pageSize = 50)
        {
            var entries = new Collection<Entry>();

            using (var dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                "DispatchEntriesGetDeletedByCompanyId",
                    new SqlParameter("@CompanyId", companyId),
                    new SqlParameter("@PageNumber", pageNumber),
                    new SqlParameter("@PageSize", pageSize)))
            {
                while (dr.Read())
                {
                    entries.Add(new Entry(dr));
                }
            }

            return entries;
        }

        public static async Task<Collection<Entry>> GetByAccountAsync(int[] accountIds) => await GetByAccountAsync(accountIds, null, null);
        public static async Task<Collection<Entry>> GetByAccountAsync(int accountId) => await GetByAccountAsync(new[] { accountId }, null, null);

        //used in dispatchentries.ashx
        public static Collection<Entry> GetByAccount(int accountId, DateTime? startRange, DateTime? endRange)
            => GetByAccount(new[] { accountId }, startRange, endRange);

        public static async Task<Collection<Entry>> GetByAccountAsync(int accountId, DateTime? startRange, DateTime? endRange)
            => await GetByAccountAsync(new[] { accountId }, startRange, endRange);

        public static Collection<Entry> GetByAccount(int[] accountIds, Nullable<DateTime> startRange, Nullable<DateTime> endRange)
        {
            var entries = new Collection<Entry>();

            using (var dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                "DispatchEntriesGetByAccountId",
                    new SqlParameter("@AccountId", string.Join(",", accountIds)),
                    new SqlParameter("@StartRange", startRange),
                    new SqlParameter("@EndRange", endRange)))
            {
                while (dr.Read())
                {
                    entries.Add(new Entry(dr));
                }
            }

            return EntriesInitializeMultiple(entries);
        }

        public static async Task<Collection<Entry>> GetByAccountAsync(int[] accountIds, Nullable<DateTime> startRange, Nullable<DateTime> endRange)
        {
            var entries = new Collection<Entry>();

            using (var dr = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString,
                "DispatchEntriesGetByAccountId",
                    new SqlParameter("@AccountId", string.Join(",", accountIds)),
                    new SqlParameter("@StartRange", startRange),
                    new SqlParameter("@EndRange", endRange)))
            {
                while (await dr.ReadAsync())
                {
                    entries.Add(new Entry(dr));
                }
            }

            return await EntriesInitializeMultipleAsync(entries);
        }

        private static string GetByAccountsQueryString(
            IEnumerable<string> companyIds,
            IEnumerable<string> accountIds,
            bool quickSearch,
            bool quickSearchCallNumber,
            int? invoiceStatusId,
            EntrySortEnum sortBy = EntrySortEnum.CallNumber,
            SortOrder sortOrder = SortOrder.Descending,
            bool hasDates = false)
        {
            // Account ids filter
            var acctSql = "";
            if (accountIds.Any())
            {
                acctSql =
                    @"AND (
                        AccountId IN (" + string.Join(", ", accountIds) + @") OR 
                        DispatchEntryId IN (SELECT DispatchEntryId from Invoices WITH (nolock) WHERE AccountId IN (" + String.Join(", ", accountIds) + @") AND CompanyId IN (" + String.Join(", ", companyIds) + @"))
                    )";
            }


            // TODO: This code needs to be completely removed/re-implemented - the way this was written
            // it is looking at all the invoices, all the payments to do a simple filter... extremely 
            // slow/inefficient. adding NOLOCK for now but this doesn't solve how expensive this operation is.


            // Invoice status filter
            var invSql = "";
            if (invoiceStatusId == null || invoiceStatusId == (int)InvoiceStatus.InvoiceStatusType.Completed)
                invSql = "AND (InvoiceStatusId IS NULL OR InvoiceStatusId = 1)";
            if (invoiceStatusId == (int)InvoiceStatus.InvoiceStatusType.PartiallyPaid)
                invSql = @"AND 0 < (SELECT COUNT(1) FROM DispatchEntryPayments dp WITH (nolock) JOIN Invoices i WITH (nolock) 
                                        ON i.InvoiceId = dp.InvoiceId AND ((i.SubTotal + i.Tax) > 0 AND ((i.SubTotal + i.tax) - i.PaymentsTotal) > 0)
                                    WHERE i.DispatchEntryId = d.DispatchEntryId AND COALESCE(dp.IsVoid, 0) = 0)";
            else if (invoiceStatusId == (int)InvoiceStatus.InvoiceStatusType.Paid)
                invSql = @"AND 0 < (SELECT COUNT(1) FROM DispatchEntryPayments dp WITH (nolock)  JOIN Invoices i WITH (nolock) 
                                        ON i.InvoiceId = dp.InvoiceId AND ((i.SubTotal + i.tax) - i.PaymentsTotal = 0)
                                    WHERE i.DispatchEntryId = d.DispatchEntryId AND COALESCE(dp.IsVoid, 0) = 0)";
            else if (invoiceStatusId == (int)InvoiceStatus.InvoiceStatusType.Unpaid)
                invSql = @"AND (
                                (SELECT COUNT(1) FROM DispatchEntryPayments dp JOIN Invoices i WITH (nolock) 
                                        ON i.InvoiceId = dp.InvoiceId AND i.TicketValue > 0 AND (i.SubTotal = 0 OR ((i.SubTotal + i.tax) - COALESCE(i.PaymentsTotal, 0)) > 0)
                                    WHERE i.DispatchEntryId = d.DispatchEntryId AND COALESCE(dp.IsVoid, 0) = 0) > 0
                                OR (Select Count(1) from invoices WITH (nolock) where dispatchEntryId=d.DispatchEntryId AND TicketValue > 0 and SubTotal = 0) > 0
                                OR (Select Count(1) from invoices WITH (nolock) where dispatchEntryId=d.DispatchEntryId AND TicketValue > 0 and SubTotal <> 0 and COALESCE(PaymentsTotal, 0) <= 0) > 0
                                )";
            else if (invoiceStatusId > 1)
                invSql = "AND InvoiceStatusId = " + invoiceStatusId;

            // quick search
            var quickSql = "";
            if (quickSearch)
            {
                quickSql = "AND (";

                if (quickSearchCallNumber)
                    quickSql += "d.CallNumber = @QuickSearchCallNumber OR ";

                quickSql += "d.InvoiceNumber LIKE ('%' + @QuickSearch + '%') OR d.PurchaseOrderNumber LIKE ('%'+ @QuickSearch + '%'))";
            }

            // Script creation
            var sql = @"
                SELECT * FROM  
	                vwDispatchEntries d WITH (NOLOCK)
                WHERE 
	                CompanyId IN (" + String.Join(",", companyIds) + @") 
                    " + acctSql + @"
                    AND Status = 5" +
                    (hasDates ? "AND CompletionTime BETWEEN @FromDate AND @ToDate\n" : "\n") +
                    invSql + @"
                    " + quickSql + @"
                ORDER BY
	                 1 " + (sortOrder == SortOrder.Ascending ? "ASC" : "DESC") + @"
				OFFSET @Offset ROWS 
				FETCH NEXT @FetchNext ROWS ONLY";

            return sql;
        }

        public static async Task<Collection<Entry>> GetByAccountsAsync(
            int[] companyIds,
            int[] accountIds,
            string quickSearch,
            int? invoiceStatusId,
            int pageNumber,
            int pageSize = 200,
            DateTime? fromDate = null,
            DateTime? toDate = null,
            EntrySortEnum sortBy = EntrySortEnum.CallNumber,
            SortOrder sortOrder = SortOrder.Descending)
        {
            var entries = new Collection<Entry>();

            var companies = new Collection<string>();
            var accounts = new Collection<string>();

            var parameters = new List<SqlParameter>()
            {
                new SqlParameter("@InvoiceStatusId", invoiceStatusId),
                new SqlParameter("@Offset", pageSize * (pageNumber - 1)),
                new SqlParameter("@FetchNext", pageSize),
            };

            int i = 0;

            foreach (var row in companyIds)
            {
                i++;
                companies.Add("@C" + i);
                parameters.Add(new SqlParameter("@C" + i, row));
            }
            i = 0;

            foreach (var row in accountIds)
            {
                i++;
                accounts.Add("@A" + i);
                parameters.Add(new SqlParameter("@A" + i, row));
            }

            if (!string.IsNullOrWhiteSpace(quickSearch))
                parameters.Add(new SqlParameter("@QuickSearch", quickSearch));

            bool quickSearchCallNumber = false;
            if (int.TryParse(quickSearch, out int callNumber))
            {
                parameters.Add(new SqlParameter("@QuickSearchCallNumber", callNumber));
                quickSearchCallNumber = true;
            }

            var sql = GetByAccountsQueryString(companies, accounts, !string.IsNullOrWhiteSpace(quickSearch), quickSearchCallNumber, invoiceStatusId, sortBy, sortOrder, fromDate != null && toDate != null);


            if (fromDate != null && toDate != null)
            {
                parameters.Add(new SqlParameter("@FromDate", fromDate));
                parameters.Add(new SqlParameter("@ToDate", toDate));
            }
            //Console.WriteLine(sql);
            Console.WriteLine(parameters.ToJson());

            using (var dr = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString, System.Data.CommandType.Text, sql, parameters.ToArray()))
            {
                while (await dr.ReadAsync())
                {
                    entries.Add(new Entry(dr));
                }
            }

            return await EntriesInitializeMultipleAsync(entries);
        }

        public static async Task<Collection<Entry>> GetByAccountsAsync(
            int[] companyIds,
            int[] accountIds,
            int? invoiceStatusId,
            int pageNumber,
            int pageSize = 50,
            DateTime? fromDate = null,
            DateTime? toDate = null,
            EntrySortEnum sortBy = EntrySortEnum.CallNumber,
            SortOrder sortOrder = SortOrder.Descending)
        {
            
            return await GetByAccountsAsync(companyIds, accountIds, null, invoiceStatusId, pageNumber, pageSize, fromDate, toDate, sortBy, sortOrder);
        }

        public sealed class InvoiceStatusCount
        {
            public int InvoiceStatusId { get; set; }
            public int ItemCount { get; set; }
        }

        public static async Task<Collection<InvoiceStatusCount>> GetInvoiceStatusCountsAsync(int[] companyIds, int accountId)
        {
            var key = "call_isc:" + string.Join(",", companyIds) + "/" + accountId;

            var rsc = await Core.GetRedisValueAsync(key);
            if (rsc != null)
            {
                return Newtonsoft.Json.JsonConvert.DeserializeObject<Collection<InvoiceStatusCount>>(rsc);
            }

            var counts = new Collection<InvoiceStatusCount>();

            var sql = @"SELECT InvoiceStatusId=COALESCE(InvoiceStatusId, 1), ItemCount=COUNT(1) FROM vwDispatchEntries D WITH (nolock)
                WHERE D.CompanyId IN @CompanyIds 
                    AND D.AccountId = @AccountId AND D.Status = 5 
                GROUP BY COALESCE(D.InvoiceStatusId, 1)";

            counts = (await SqlMapper.QueryAsync<InvoiceStatusCount>(sql,
                new { CompanyIds = companyIds, AccountId = accountId }))
                .OrderBy(o => o.InvoiceStatusId)
                .ToCollection();

            await Core.SetRedisValueAsync(key, counts.ToJson(), TimeSpan.FromDays(3));
            await Core.GetRedisDatabaseAsync().SetAddAsync($"cache_call_isc:" + accountId, key);

            return counts;
        }

        public static async Task UpdateInvoiceStatusId(int invoiceId) =>
            await UpdateInvoiceStatusIdInternal(invoiceId, false, null);

        public static async Task UpdateInvoiceStatusId(int invoiceId, ConcurrentBag<Task> bulkTasks) =>
            await UpdateInvoiceStatusIdInternal(invoiceId, false, bulkTasks);

        internal static async Task UpdateInvoiceStatusIdInternal(int invoiceId, bool skipWriteToCosmos, ConcurrentBag<Task> bulkTasks)
        {
            if (invoiceId < 1)
                return;

            // get fresh copy of invoice
            var inv = await Invoice.GetByIdAsync(invoiceId);

            // must provide valid invoice and entry objects
            if (inv.DispatchEntry == null)
                return;

            var updateEntry = false;
            int invoiceStatusId = inv.DispatchEntry.InvoiceStatusId;
            var accountId = inv.DispatchEntry.AccountId;

            foreach (var m in await Core.GetRedisDatabase().SetMembersAsync("cache_call_isc:" + accountId))
            {
                await Core.DeleteRedisKeyAsync(m);
                await Core.GetRedisDatabase().SetRemoveAsync("cache_call_isc:" + accountId, m);
            }

            // Entry must have a status of Completed
            if (inv.DispatchEntry.Status.Id == Status.Completed.Id)
            {
                // For now...
                // Only consider unassigned (0), recent (1), and closed (12) status
                if (!(inv.DispatchEntry.InvoiceStatusId == (int)InvoiceStatus.InvoiceStatusType.Unassigned
                    || inv.DispatchEntry.InvoiceStatusId == (int)InvoiceStatus.InvoiceStatusType.Completed
                    || inv.DispatchEntry.InvoiceStatusId == (int)InvoiceStatus.InvoiceStatusType.Closed))
                    return;

                if ((inv.GrandTotal == 0 || inv.BalanceDue > 0)
                    && (inv.DispatchEntry.InvoiceStatusId == (int)InvoiceStatus.InvoiceStatusType.Closed
                        || inv.DispatchEntry.InvoiceStatusId == (int)InvoiceStatus.InvoiceStatusType.Unassigned))
                {
                    invoiceStatusId = (int)InvoiceStatus.InvoiceStatusType.Completed;
                    updateEntry = true;
                }
                else if (inv.GrandTotal > 0
                            && inv.BalanceDue == 0
                            && (inv.DispatchEntry.InvoiceStatusId == (int)InvoiceStatus.InvoiceStatusType.Completed
                                || inv.DispatchEntry.InvoiceStatusId == (int)InvoiceStatus.InvoiceStatusType.Unassigned))
                {
                    invoiceStatusId = (int)InvoiceStatus.InvoiceStatusType.Closed;
                    updateEntry = true;
                }
            }
            else if (inv.DispatchEntry.Status.Id != Status.Cancelled.Id
                && (inv.DispatchEntry.InvoiceStatusId != (int)InvoiceStatus.InvoiceStatusType.Unassigned
                    || inv.DispatchEntry.InvoiceStatusId != (int)InvoiceStatus.InvoiceStatusType.Completed))
            {
                updateEntry = true;
                invoiceStatusId = (int)InvoiceStatus.InvoiceStatusType.Completed;
            }

            if (updateEntry)
            {
                await UpdateInvoiceStatusIdsInternal(new int[] { inv.DispatchEntry.Id }, invoiceStatusId, skipWriteToCosmos, bulkTasks);
                inv.DispatchEntry.InvoiceStatusId = invoiceStatusId;
            }
        }

        public static async Async.Task UpdateInvoiceStatusIds(int[] callIds, int invoiceStatusId, ConcurrentBag<Task> bulkTasks = null) =>
            await UpdateInvoiceStatusIdsInternal(callIds, invoiceStatusId, false, bulkTasks);

        internal static async Async.Task UpdateInvoiceStatusIdsInternal(int[] callIds, int invoiceStatusId, bool skipWritingToCosmos,
            ConcurrentBag<Task> bulkTasks = null)
        {
            if (callIds.Any())
            {
                var entries = await GetByIdsAsync(callIds);

                if (invoiceStatusId == (int)InvoiceStatus.InvoiceStatusType.ReadyToBill ||
                    invoiceStatusId == (int)InvoiceStatus.InvoiceStatusType.Billed)
                {

                    foreach (var en in entries)
                    {
                        var preventUpdate = CompanyKeyValue.GetFirstValueOrNull(en.CompanyId, Provider.Towbook.ProviderId, "InvoiceStatus_PreventUpdate");
                        if (preventUpdate == null)
                            continue;

                        if (preventUpdate == "1" && !en.Attributes.ContainsKey(AttributeValue.BUILTIN_DISPATCH_AUDITED))
                        {
                            throw new TowbookException($"CallId {en.Id}/CallNumber {en.CallNumber} isn't marked as audited and is required due to InvoiceStatus_PreventUpdate setting.");
                        }
                        else if (preventUpdate == "2" && !await en.IsLockedAsync())
                        {
                            throw new TowbookException($"CallId {en.Id}/CallNumber {en.CallNumber} isn't locked and is required due to InvoiceStatus_PreventUpdate setting.");
                        }
                        else if (preventUpdate == "3" && !await en.IsLockedAsync() && !en.Attributes.ContainsKey(AttributeValue.BUILTIN_DISPATCH_AUDITED))
                        {
                            throw new TowbookException($"CallId {en.Id}/CallNumber {en.CallNumber} must be both audited and locked before moving to this status due to InvoiceStatus_PreventUpdate setting.");
                        }
                    }
                }

                // if preventUpdate == 1.. require audit
                // if preventUpdate == 2.. require lock
                // if preventUpdate == 3.. if not audited and locked

                foreach (var batch in entries.OrderBy(o => o.Id).Batch(250))
                {
                    using (var cnn = Core.GetConnection())
                    {
                        await SqlMapper.ExecuteAsync(cnn,
                            "UPDATE DispatchEntries SET InvoiceStatusId=@InvoiceStatusId WHERE DispatchEntryId in @Ids",
                            new
                            {
                                InvoiceStatusId = invoiceStatusId,
                                Ids = batch.Select(o => o.Id)
                            });
                    }
                }

                if (!skipWritingToCosmos)
                {
                    foreach (var each in entries)
                    {
                        await UpdateInvoiceStatusAzure(each.Id, each.CompanyId, invoiceStatusId, bulkTasks);
                    }
                }

                foreach (var id in callIds)
                {
                    CacheClearById(id);
                }
            }
        }

        public async Task HandleScheduledCallEventNotification()
        {
            if (ArrivalETA == null || Status != Status.Waiting || Id == 0)
                return;

            if (!Company.HasFeature(Features.Notifications_StandardEventNotifications))
                return;

            double bufferHours = 6;

            // consider the exact moment a sheduled call becomes current (6 hour buffer)
            var whenCurrentDate = ArrivalETA.Value.AddHours(0 - bufferHours);

            // consider when the call moves to current by the user
            var moveToCurrentAttr = Attributes?.FirstOrDefault(f => f.Key == Dispatch.AttributeValue.BUILTIN_MOVE_TO_CURRENT_BUFFER_DATE).Value;
            if (moveToCurrentAttr != null && DateTime.TryParse(moveToCurrentAttr.Value, out DateTime dt))
                whenCurrentDate = dt;
            
            if (whenCurrentDate > DateTime.Now)
            {
                // trigger event notification that driver completed the call
                var item = new EventNotifications.DispatchingQueueItem();
                item.Type = EventNotifications.DispatchingTriggerType.CallMoveToCurrent;
                item.DispatchEntryId = Id;
                item.ArrivalEta = Core.OffsetDateTime(this.Company, ArrivalETA.Value, true); // sent in EST (server time)
                item.StartAtTime = whenCurrentDate;

                await item.TriggerEvent();
            }
        }

        public enum EntrySortEnum
        {
            CompanyId,
            CallNumber,
        }

        public enum SortOrder { Ascending, Descending }

        public int Id
        {
            get => _id;
            internal set => _id = value;
        }

        public bool InternalBlockWrites
        {
            get;
            set;
        }

        /// <summary>
        /// Represents the call or quote #. If the Type is Call, then it is the call number. If the type is Quote, then it is the Quote number.
        /// </summary>
        [CsvExportable("Call#", 0)]
        public int CallNumber
        {
            get { return _callNumber; }
            internal set { _callNumber = value; }
        }

        public int? CallRequestId { get; set; }

        public string PurchaseOrderNumber
        {
            get { return _purchaseOrderNumber ?? string.Empty; }
            set { SetField(ref _purchaseOrderNumber, value, "PO #"); }
        }

        [CsvExportable("InvoiceNumber", 1)]
        public string InvoiceNumber
        {
            get { return _invoiceNumber ?? string.Empty; }
            set { SetField(ref _invoiceNumber, value, "Invoice #"); }
        }

        [CsvExportable("Motorclub Membership Number", 3)]
        public string MotorclubMembershipNumber
        {
            get
            {
                if (this.Attributes.ContainsKey(AttributeValue.BUILTIN_MOTORCLUB_MEMBERSHIPNUMBER))
                    return this.Attributes[AttributeValue.BUILTIN_MOTORCLUB_MEMBERSHIPNUMBER].Value;
                else
                    return String.Empty;
            }
        }
        public string MotorClubDispatchNumber
        {
            get
            {
                if (this.Attributes.ContainsKey(AttributeValue.BUILTIN_MOTORCLUB_DISPATCHNUMBER))
                    return this.Attributes[AttributeValue.BUILTIN_MOTORCLUB_DISPATCHNUMBER].Value;
                else
                    return String.Empty;
            }
        }

        public int CompanyId
        {
            get { return _companyId; }
            set { SetField(ref _companyId, value, "CompanyId"); }
        }

        public EntryType Type
        {
            get { return _type; }
            set { SetField(ref _type, value, "Type"); }
        }

        private Extric.Towbook.Company.Company _company;
        public Extric.Towbook.Company.Company Company
        {
            get
            {
                if (_company == null)
                    _company = Extric.Towbook.Company.Company.GetById(_companyId);

                return _company;
            }
            set { SetField(ref _companyId, value != null ? value.Id : 0, "CompanyId"); }
        }

        [CsvExportable("CreateDate", 1000)]
        public DateTime CreateDate
        {
            get { return _createDate; }
            set { SetField(ref _createDate, value, "CreateDate"); }
        }

        public string CancellationReason { get; set; }

        public Status Status
        {
            get => _status;
            set
            {
                if (_status != value)
                {
                    if (value == EntryStatus.Canceled && _id > 0)
                        throw new TowbookException("Use the Cancel() method instead.");

                    SetField(ref _status, value, "Status");

                    switch (_status.Id)
                    {
                        case (int)EntryStatus.Dispatched:
                            if (this.DispatchTime is null)
                                this.DispatchTime = DateTime.Now;
                            break;

                        case (int)EntryStatus.EnRoute:
                            if (this.EnrouteTime is null)
                                this.EnrouteTime = DateTime.Now;
                            break;

                        case (int)EntryStatus.AtSite:
                            if (this.ArrivalTime is null)
                                this.ArrivalTime = DateTime.Now;
                            break;

                        case (int)EntryStatus.BeingTowed:
                            if (this.TowTime is null)
                                this.TowTime = DateTime.Now;
                            break;

                        case (int)EntryStatus.DestinationArrival:
                            if (this.DestinationArrivalTime is null)
                                this.DestinationArrivalTime = DateTime.Now;
                            break;

                        case (int)EntryStatus.Completed:
                            if (this.CompletionTime is null)
                                this.CompletionTime = DateTime.Now;
                            break;
                    }

                }
            }
        }

        public bool Impound
        {
            get { return _impound; }
            set { SetField(ref _impound, value, "Impound"); }
        }

        public bool Released
        {
            get { return _released; }
            set { SetField(ref _released, value, "Released"); }
        }

        [CsvExportable("VehicleYear")]
        public int Year
        {
            get { return _year; }
            set { SetField(ref _year, value, "Year"); }
        }

        public Vehicle.Model Model
        {
            get { return Vehicle.Model.GetById(_modelId); }
            set { SetField(ref _modelId, value != null ? value.Id : 0, "Model"); }
        }

        [CsvExportable("Vehicle")]
        public string MakeModelFormatted
        {
            get
            {
                if (this.Assets.Any())
                {
                    var a = this.assets.First();
                    return a.Make + " " + a.Model;
                }

                string retval = String.Empty;

                retval += VehicleMake + " ";
                retval += VehicleModel + " ";

                return retval;
            }
        }

        public string VehicleMake
        {
            get
            {
                if (this.Assets.Any())
                {
                    var a = this.assets.First();
                    if (!string.IsNullOrWhiteSpace(a.Make))
                        return a.Make;
                }

                string retval = String.Empty;

                if (Attributes.ContainsKey(AttributeValue.BUILTIN_VEHICLE_MANUFACTURER) &&
                    !string.IsNullOrWhiteSpace(Attributes[AttributeValue.BUILTIN_VEHICLE_MANUFACTURER].Value))
                {
                    retval += Attributes[AttributeValue.BUILTIN_VEHICLE_MANUFACTURER].Value + " ";
                }
                else if (Manufacturer != null && Manufacturer.Id != 32767)
                    retval += Manufacturer.Name;

                return retval.Trim();
            }
            set
            {
                // check if make exists in built-in database:
                var n = Vehicle.Manufacturer.GetByName(value);

                if (this.Assets != null)
                {
                    if (this.Assets.Any())
                    {
                        string make = this.Assets.First().Make;
                        this.Assets.First().Make = n?.Name ?? value;
                        SetField(ref make, this.Assets.First().Make, "Model");
                        return;
                    }
                }

                if (n != null)
                {
                    SetField(ref _manufacturerId, n.Id, "Make");

                    if (Attributes.ContainsKey(AttributeValue.BUILTIN_VEHICLE_MANUFACTURER))
                    {
                        Attributes[AttributeValue.BUILTIN_VEHICLE_MANUFACTURER].Value = null;
                    }
                }
                else
                {
                    _manufacturerId = 0;
                    if (!Attributes.ContainsKey(AttributeValue.BUILTIN_VEHICLE_MANUFACTURER))
                    {
                        Attributes[AttributeValue.BUILTIN_VEHICLE_MANUFACTURER] = new AttributeValue() { DispatchEntryAttributeId = AttributeValue.BUILTIN_VEHICLE_MANUFACTURER };
                    }
                    Attributes[AttributeValue.BUILTIN_VEHICLE_MANUFACTURER].Value = value;
                    SetField(ref _manufacturerId, 0, "Make");
                }
            }
        }

        public string VehicleModel
        {
            get
            {
                if (this.Assets.Any())
                {
                    var a = this.assets.First();
                    if (!string.IsNullOrWhiteSpace(a.Model))
                        return a.Model;
                }

                string retval = String.Empty;

                if (Attributes.ContainsKey(AttributeValue.BUILTIN_VEHICLE_MODEL) &&
                    !string.IsNullOrWhiteSpace(Attributes[AttributeValue.BUILTIN_VEHICLE_MODEL].Value))
                {
                    retval += Attributes[AttributeValue.BUILTIN_VEHICLE_MODEL].Value;
                }
                else if (Model != null)
                {
                    retval += Model.Name;
                }

                return retval;

            }
            set
            {
                string model = "";

                if (this.Assets != null)
                {
                    if (this.Assets.Any())
                    {
                        model = this.Assets.First().Model;
                        this.Assets.First().Model = value;
                        SetField(ref model, value, "Model");
                        return;
                    }
                }

                if (this.Manufacturer != null)
                {
                    var y = Vehicle.Model.GetByName(value, this.Manufacturer.Id);
                    if (y != null)
                    {
                        SetField(ref _modelId, y.Id, "Model");
                        if (Attributes.ContainsKey(AttributeValue.BUILTIN_VEHICLE_MODEL))
                        {
                            Attributes[AttributeValue.BUILTIN_VEHICLE_MODEL].Value = null;
                        }
                    }
                    else
                    {
                        if (!Attributes.ContainsKey(AttributeValue.BUILTIN_VEHICLE_MODEL))
                        {
                            Attributes[AttributeValue.BUILTIN_VEHICLE_MODEL] = new AttributeValue() { DispatchEntryAttributeId = AttributeValue.BUILTIN_VEHICLE_MODEL };
                        }

                        Attributes[AttributeValue.BUILTIN_VEHICLE_MODEL].Value = value;

                        SetField(ref model, value, "Model");
                    }
                }
                else
                {
                    _modelId = 0;

                    if (!Attributes.ContainsKey(AttributeValue.BUILTIN_VEHICLE_MODEL))
                    {
                        Attributes[AttributeValue.BUILTIN_VEHICLE_MODEL] = new AttributeValue() { DispatchEntryAttributeId = AttributeValue.BUILTIN_VEHICLE_MODEL };
                    }
                    Attributes[AttributeValue.BUILTIN_VEHICLE_MODEL].Value = value;
                    SetField(ref model, value, "Model");
                }
            }
        }

        public Vehicle.Manufacturer Manufacturer
        {
            get { return Vehicle.Manufacturer.GetById(_manufacturerId); }
            set { SetField(ref _manufacturerId, value != null ? value.Id : 0, "Make"); }
        }

        [CsvExportable("VehicleColor")]
        public Vehicle.Color Color
        {
            get { return Vehicle.Color.GetById(_colorId); }
            set { SetField(ref _colorId, value != null ? value.Id : 0, "ColorId"); }
        }

        public async Task<Vehicle.Color> GetColorAsync()
        {
            return await Vehicle.Color.GetByIdAsync(_colorId);
        }

        public Vehicle.BodyType BodyType
        {
            get
            {
                return Vehicle.BodyType.GetById(_bodyTypeId, _companyId);
            }
            set { SetField(ref _bodyTypeId, value != null ? value.Id : 0, "BodyTypeId"); }
        }

        public async Task<Vehicle.BodyType> GetBodyTypeAsync()
        {
            return await Vehicle.BodyType.GetByIdAsync(_bodyTypeId, _companyId);
        }

        public AccountType RequestedBy
        {
            get { return _requestedBy; }
            set { _requestedBy = value; }
        }

        [CsvExportable("AccountName")]
        public Accounts.Account Account
        {
            get { return Account.GetById(_accountId); }
            set { SetField(ref _accountId, value != null ? value.Id : 0, "Account"); }
        }

        public async Task<Accounts.Account> GetAccountAsync()
        {
            return await Account.GetByIdAsync(_accountId);
        }

        public int AccountId
        {
            get { return _accountId; }
            set { SetField(ref _accountId, value, "Account"); }
        }

        [CsvExportable("Reason")]
        public Reason Reason
        {
            get { return Reason.GetById(_reasonId); }
            set
            {
                if (value?.Id != _reasonId)
                {
                    SetField(ref _reasonId, value != null ? value.Id : 0, "ReasonId");
                }
            }
        }

        public async Task<Reason> GetReasonAsync()
        {
            return await Reason.GetByIdAsync(_reasonId);
        }

        private async Task HandleReasonChange()
        {
            if (_id < 1 || InternalBlockWrites)
                return;

            // If reason is set to GOA, force call to completed if it is cancelled.
            var rsn = await Reason.GetByIdAsync(_reasonId);
            if (rsn != null && rsn.Name != null &&
                (rsn.Name.ToLowerInvariant().Contains("goa") ||
                rsn.Name.ToLowerInvariant().Contains("gone on arrival")))
            {
                if (this.Status.Id == Status.Cancelled.Id)
                {
                    this.Status = Status.Completed;
                    var ec = await EntryCancellation.GetByDispatchEntryIdAsync(this.Id);
                    if (ec != null)
                    {
                        ec.Deleted = true;
                        await ec.Save();
                    }
                    if (this._attributes != null && this._attributes.ContainsKey(AttributeValue.BUILTIN_DISPATCH_CANCEL_ACKNOWLEDGEMENT_JSON))
                    {
                        var attr = this._attributes[AttributeValue.BUILTIN_DISPATCH_CANCEL_ACKNOWLEDGEMENT_JSON];
                        await attr.Delete();
                        this._attributes.Remove(AttributeValue.BUILTIN_DISPATCH_CANCEL_ACKNOWLEDGEMENT_JSON);
                    }
                }
            }
        }

        [CsvExportable("StartAddress")]
        public string TowSource
        {
            get => _towSource;
            set => SetField(ref _towSource, value, "TowSource"); 
        }

        [CsvExportable("DestinationAddress")]
        public string TowDestination
        {
            get => _towDestination;    
            set => SetField(ref _towDestination, value, "TowDestination"); 
        }

        public Truck Truck
        {
            get { return Truck.GetById(_truckId); }
            set { SetField(ref _truckId, value?.Id ?? 0, "TruckId"); }
        }

        public async Task<Truck> GetTruckAsync()
        {
            return await Truck.GetByIdAsync(_truckId);
        }

        [CsvExportable("Driver")]
        public Driver Driver
        {
            get { return Driver.GetById(DriverId); }
            set { SetField(ref _driverId, value?.Id ?? 0, "DriverId"); }
        }

        public async Task<Driver> GetDriverAsync()
        {
            return await Driver.GetByIdAsync(DriverId);
        }

        public DateTime? DispatchTime
        {
            get { return _dispatchTime; }
            set
            {
                if (value == DateTime.MinValue || (value != null && value.Value.Year < 1753))
                    SetField(ref _dispatchTime, null, "DispatchTime");
                else
                    SetField(ref _dispatchTime, value, "DispatchTime");
            }
        }

        public DateTime? ArrivalTime
        {
            get => _arrivalTime;
            set
            {
                if (value == DateTime.MinValue || (value != null && value.Value.Year < 1753))
                    SetField(ref _arrivalTime, null, "ArrivalTime");
                else
                    SetField(ref _arrivalTime, value, "ArrivalTime");
            }
        }

        public DateTime? EnrouteTime
        {
            get => _enrouteTime;
            set
            {
                if (value == DateTime.MinValue || (value != null && value.Value.Year < 1753))
                    SetField(ref _enrouteTime, null, "EnrouteTime");
                else
                    SetField(ref _enrouteTime, value, "EnrouteTime");

            }
        }

        public DateTime? ArrivalETA
        {
            get => _arrivalETA;
            set
            {
                if (value == DateTime.MinValue || (value != null && value.Value.Year < 1753))
                    SetField(ref _arrivalETA, null, "ArrivalETA");
                else
                    SetField(ref _arrivalETA, value, "ArrivalETA");
            }
        }

        public DateTime? TowTime
        {
            get => _towTime;
            set
            {
                if (value == DateTime.MinValue || (value != null && value.Value.Year < 1753))
                    SetField(ref _towTime, null, "TowTime");
                else
                    SetField(ref _towTime, value, "TowTime");
            }
        }

        public DateTime? DestinationArrivalTime
        {
            get => _destinationArrivalTime;
            set
            {
                if (value == DateTime.MinValue || (value != null && value.Value.Year < 1753))
                    SetField(ref _destinationArrivalTime, null, "DestinationArrivalTime");
                else
                    SetField(ref _destinationArrivalTime, value, "DestinationArrivalTime");
            }
        }

        public DateTime? CompletionTime
        {
            get => _completionTime;
            set
            {
                if (value == DateTime.MinValue || (value != null && value.Value.Year < 1753))
                    SetField(ref _completionTime, null, "CompletionTime");
                else
                    SetField(ref _completionTime, value, "CompletionTime");
            }
        }

        public string Notes
        {
            get { return _notes; }
            set { SetField(ref _notes, value, "Notes"); }
        }

        public string LicenseNumber
        {
            get { return _licenseNumber ?? ""; }
            set { SetField(ref _licenseNumber, value, "LicenseNumber"); }
        }

        public string LicenseState
        {
            get { return _licenseState ?? ""; }
            set { SetField(ref _licenseState, value, "LicenseState"); }
        }

        public string LicenseYear
        {
            get { return _licenseYear; }
            set { SetField(ref _licenseYear, value, "LicenseYear"); }
        }

        public bool? Drivable
        {
            get { return _drivable; }
            set { SetField(ref _drivable, value, "Drivable"); }
        }

        public int Odometer
        {
            get { return _odometer; }
            set { SetField(ref _odometer, value, "Odometer"); }
        }

        [CsvExportable("VIN")]
        public string VIN
        {
            get { return (string.IsNullOrWhiteSpace(_vin) ? "" : _vin.ToUpperInvariant()); }
            set { SetField(ref _vin, value, "VIN"); }
        }

        public bool Created
        {
            get
            {
                return _created;
            }
            set
            {
                _created = value;
            }
        }

        public EntryPriority Priority
        {
            get { return _priority; }
            set { SetField(ref _priority, value, "Priority"); }
        }

        public int OwnerUserId
        {
            get { return _ownerUserId; }
            set { SetField(ref _ownerUserId, value, "OwnerUserId"); }
        }

        public bool IsVehicleAsset { get; set; }

        private User _ownerUser;
        public User OwnerUser
        {
            get
            {
                if (_ownerUser == null)
                    _ownerUser = User.GetById(_ownerUserId);
                return _ownerUser;
            }
        }

        public async Task<User> GetOwnerUserAsync()
        {
            if (_ownerUser == null)
                    _ownerUser = await User.GetByIdAsync(_ownerUserId);
                return _ownerUser;
        }

        public bool IsTowoutCall() => Attributes?.FirstOrDefault(a => a.Key == Dispatch.AttributeValue.BUILTIN_TOWOUT_CALL).Value?.Value == "1";

        public static void CacheClearById(int id, int? accountId = null)
        {
            AppServices.Cache.InvalidateCacheItem("de:" + id);

            if (accountId == null)
            {
                var e = Entry.GetById(id);
                if (e != null)
                    accountId = e.AccountId;
            }

            if (accountId != null && accountId > 0)
                Accounts.Account.CacheClearById(accountId.Value);
        }

        public async Async.Task Save(bool dontPush = false, AuthenticationToken token = null, string ipAddress = null) =>
            await Save(dontPush, token, ipAddress, false);


        public async Async.Task Save(bool dontPush, AuthenticationToken token, string ipAddress, bool skipLockCheck) =>
            await Save(dontPush, token, ipAddress, skipLockCheck, false);

        public async Task<IEnumerable<Task>> Save(
            bool dontPush, AuthenticationToken token, string ipAddress, bool skipLockCheck, bool bulkMode) =>
            await Save(dontPush, token, ipAddress, skipLockCheck, bulkMode, false);

        /// <summary>
        /// Saves the Dispatch Call object to the data store.
        /// </summary>
        public async Task<IEnumerable<Task>> Save(
            bool dontPush, AuthenticationToken token, string ipAddress, bool skipLockCheck, bool bulkMode, bool overrideClosedPeriod)
        {
            Collection<Task> tasks = (bulkMode ? new Collection<Task>() : null);

            async Task HandleBulk(Task task)
            {
                if (bulkMode)
                    tasks.Add(task);
                else
                    await task;
            }

            if (_id == 0)
            {
                throw new ApplicationException("No such Dispatch Entry. Can't save " +
                    "object! (this object should have already been discarded!)");
            }

            if (!IsDirty)
            {
                //Debug.WriteLine("!!! No changes for " + this.Id + "...");
            }

            if (InternalBlockWrites)
                throw new ApplicationException("InternalBlockWrites is set. This is a temporary copy of the call and cannot be saved.");

            if (this.DispatchEntryLockId.GetValueOrDefault() > 0 && !skipLockCheck)
            {
                throw new TowbookException($"Call {Id}/Call Number {CallNumber} is locked. It must be unlocked before it can be modified.");
            }

            if (!overrideClosedPeriod && Company.HasFeature(Features.AdvancedBilling_ClosedAccountingPeriod))
            {
                var closedPeriodOptions = ClosedPeriodOption.GetByCompanyId(CompanyId);
                if (this.IsWithinClosedAccountingPeriod(closedPeriodOptions))
                {
                    throw new TowbookException($"Call {Id}/Call Number {CallNumber} is locked by closed period settings. An override is required to modify it.");
                }
            }

            if (_createDate == DateTime.MinValue)
                _createDate = DateTime.Now;

            this.Version++;
            CacheClearById(Id, this.AccountId);

            // Entry root equals same as first asset:


            void translateToAsset(EntryAsset subAsset)
            {
                if (IsFieldDirty("Year") && this._year != subAsset.Year)
                    subAsset.Year = this._year;

                if (IsFieldDirty("Make") && this.VehicleMake != subAsset.Make)
                    subAsset.Make = this.VehicleMake;

                if (IsFieldDirty("Model") && this.VehicleModel != subAsset.Model)
                    subAsset.Model = this.VehicleModel;

                if (IsFieldDirty("VIN") && this._vin != subAsset.Vin)
                    subAsset.Vin = this._vin;

                if (IsFieldDirty("Odometer") && this._odometer != subAsset.Odometer)
                    subAsset.Odometer = this._odometer;

                if (this._licenseYear != subAsset.LicenseYear.ToString())
                {
                    try
                    {
                        subAsset.LicenseYear = Convert.ToInt32(this._licenseYear);
                    }
                    catch (FormatException)
                    {
                        // ignore format exception.
                    }
                }

                if (this._licenseState != subAsset.LicenseState)
                    subAsset.LicenseState = this._licenseState;

                if (this._licenseNumber != subAsset.LicenseNumber)
                    subAsset.LicenseNumber = this._licenseNumber;

                if (this._bodyTypeId != subAsset.BodyTypeId)
                    subAsset.BodyTypeId = this._bodyTypeId;


                if (IsFieldDirty("ColorId") && this._colorId != subAsset.ColorId)
                    subAsset.ColorId = this._colorId;

                if (this.DriverId == 0)
                {
                    if (subAsset.Drivers.Count > 0)
                        subAsset.Drivers[0].DriverId = null;
                }
                else
                {
                    if (subAsset.Drivers.Count > 0)
                    {
                        subAsset.Drivers[0].DriverId = this.DriverId;
                        subAsset.MarkAsDirty();
                    }
                    else
                    {
                        var newDriver = new EntryAssetDriver();
                        newDriver.DriverId = this.DriverId;
                        subAsset.Drivers.Add(newDriver);
                        subAsset.MarkAsDirty();
                    }
                }

                if (this.TruckId == 0)
                {
                    if (subAsset.Drivers.Count > 0)
                        subAsset.Drivers[0].TruckId = null;
                }
                else
                {

                    if (subAsset.Drivers.Count > 0)
                        subAsset.Drivers[0].TruckId = this.TruckId;
                    else
                    {
                        var newDriver = new EntryAssetDriver();
                        newDriver.TruckId = this.TruckId;
                        subAsset.Drivers.Add(newDriver);
                    }
                }
            }

            var newCall = this.Assets.All(o => o.Id < 1) || this.assets.Count == 0;

            var firstAsset = newCall ? this.Assets.FirstOrDefault() : this.Assets.OrderBy(o => o.Id).FirstOrDefault();

            if (this.Assets.Any(o => o.Id > 0))
            {
                firstAsset = this.assets.Where(o => o.Id > 0).OrderBy(o => o.Id).FirstOrDefault();
            }

            if (firstAsset != null)
            {
                if (firstAsset.IsDirty)
                {
                    this._colorId = firstAsset.ColorId;
                    this.Year = firstAsset.Year;
                    this.VehicleMake = firstAsset.Make;
                    this.VehicleModel = firstAsset.Model;
                    this._vin = firstAsset.Vin;
                    this._odometer = firstAsset.Odometer;
                    this.LicenseYear = firstAsset.LicenseYear.ToString();
                    this.LicenseState = firstAsset.LicenseState;
                    this.LicenseNumber = firstAsset.LicenseNumber;
                    this.DriverId = (firstAsset.Drivers != null && firstAsset.Drivers.Count > 0 ? firstAsset.Drivers[0].DriverId.GetValueOrDefault() : 0);
                    this.TruckId = (firstAsset.Drivers != null && firstAsset.Drivers.Count > 0 ? firstAsset.Drivers[0].TruckId.GetValueOrDefault() : 0);
                    this.Drivable = firstAsset.Drivable == Dispatch.Drivable.Drivable || firstAsset.Drivable == Dispatch.Drivable.Unknown;
                    SetField(ref _bodyTypeId, firstAsset.BodyTypeId, "BodyTypeId");
                    SetField(ref _colorId, firstAsset.ColorId, "ColorId");

                    if (this.ChangedFields != null)
                    {
                        this.ChangedFields =
                            this.ChangedFields.Where(o =>
                                o.Field != "Make" &&
                                o.Field != "Model" &&
                                o.Field != "Year" &&
                                o.Field != "LicenseYear" &&
                                o.Field != "LicenseState" &&
                                o.Field != "ColorId" &&
                                o.Field != "BodyTypeId").ToList();
                    }

                    //throw new Exception("Asset changed, overwrite data on Entry." + String.Join(",", firstAsset.ChangedFields.ToArray()));
                }
                else
                {
                    // the asset has not been changed, so, the Entry should match the asset.. 
                    // If not, we should update the first asset to match the entry. 

                    translateToAsset(firstAsset);
                }
            }

            if (!IsFieldDirty("TowSource"))
            {
                var wts = this.Waypoints.FirstOrDefault(cn => cn.Title == "Pickup" && cn.IsDirty);
                if (wts != null)
                    this._towSource = wts.Address;
            }

            if (!IsFieldDirty("TowDestination"))
            {
                var wtd = this.Waypoints.FirstOrDefault(cn => cn.Title == "Destination" && cn.IsDirty);
                if (wtd != null)
                    this._towDestination = wtd.Address;
            }

            bool create = false;
            if (_id == -1)
            {
                await DbInsertAsync();
                create = true;
            }
            else
            {
                DbUpdate();
                create = false;
            }

            if (firstAsset == null)
            {
                firstAsset = new EntryAsset() { DispatchEntryId = this.Id };
                translateToAsset(firstAsset);
                await firstAsset.Save(null, token, ipAddress);
            }

            if (IsFieldDirty("ReasonId"))
                await HandleReasonChange();

            if (IsFieldDirty("ArrivalETA"))
            {
                await HandleScheduledCallEventNotification();
            }

            var activityLogActionType = create ? ActivityLogActionType.Create : ActivityLogActionType.Edit;

            if (this.ClosedPeriodActivitySave)
                activityLogActionType = ActivityLogActionType.ClosedAccountingPeriodActivityEdit;

            await HandleBulk(base.SaveAsync(token,
                ActivityLogType.DispatchEntry,
                _id,
                ActivityLogType.Company,
                _companyId,
                (int)activityLogActionType,
                ipAddress));

            #region Auto Lock
            if (create)
            {
                var ckv = CompanyKeyValue.GetFirstValueOrNull(this.CompanyId, Provider.Towbook.ProviderId, "AutomaticallyLockCallsAfterCompletion");
                if (ckv != null)
                {
                    int hours;
                    if (int.TryParse(ckv, out hours))
                    {
                        if (!this.Attributes.ContainsKey(AttributeValue.BUILTIN_AUTOLOCK_EVENT_JSON))
                        {
                            this.Attributes.Add(AttributeValue.BUILTIN_AUTOLOCK_EVENT_JSON, new AttributeValue()
                            {
                                DispatchEntryAttributeId = AttributeValue.BUILTIN_AUTOLOCK_EVENT_JSON,
                                Value = new AutoLockEventJson()
                                {
                                    Offset = hours,
                                    Applied = false,
                                    AppliedDate = null
                                }.ToJson()
                            });
                        }
                    }
                }
            }
            #endregion



            if (Attributes != null)
            {
                if (IsFieldDirty("PO #"))
                {
                    if (Attributes.ContainsKey(AttributeValue.BUILTIN_ACCOUNT_PURCHASEORDER))
                        AttributeValueExtensions.SetAttribute(this, AttributeValue.BUILTIN_ACCOUNT_PURCHASEORDER, this.PurchaseOrderNumber);
                }
                foreach (var x in Attributes.Values)
                {
                    if (x.DispatchEntryAttributeId == AttributeValue.BUILTIN_DISPATCH_CUSTOMINVOICENUMBER)
                        continue;

                    if (x.DispatchEntryId < 1)
                    {
                        x.DispatchEntryId = this.Id;
                    }
                    if (x.Id != 0)
                    {
                        // only save if ID != 0... if ID=0, the object is deleted and saving it should not be attempted.
                        await HandleBulk(x.SaveAsync(token, ipAddress));
                    }
                }
            }


            if (Assets != null)
            {
                foreach (var asset in Assets)
                {
                    if (asset.DispatchEntryId < 1)
                    {
                        asset.DispatchEntryId = this.Id;
                    }
                    if(!asset.Deleted)
                        await HandleBulk(asset.Save(null, token, ipAddress));
                }
            }

            if (Contacts != null)
            {
                foreach (var x in Contacts)
                {
                    bool firstContact = false;

                    if (x.DispatchEntryId < 1)
                    {
                        x.DispatchEntryId = this.Id;

                        if (x == Contacts.First())
                            firstContact = true;
                    }

                    // if Id == 0, the Contact was Deleted.
                    if (x.Id != 0)
                    {
                        x.Save();

                        // Roadside check: first contact -> send invite to roadside according to account settings
                        if (firstContact && Company.HasFeature(Generated.Features.Roadside) && 
                            !this.Attributes.ContainsKey(19) &&
                            !this.Attributes.ContainsKey(20))
                        {
                            // Do not invite first contact when account is explicitly set to not invite
                            var av = Integration.AccountKeyValue.GetByAccount(_companyId, this.AccountId, Integration.Provider.Towbook.ProviderId, "AutoSendRoadsideInvite").FirstOrDefault();
                            if (av != null && av.Value == "1")
                            {
                                await EntryContact.TriggerRoadsideUserInvite(x);
                            }
                        }
                    }
                }

                Contacts = Contacts.Where(o => o.Id != 0).ToCollection();
            }


            #region Waypoints
            if (Waypoints.Where(o => o.Title != "Pickup" && o.Title != "Destination").Any())
            {
                foreach (var w in Waypoints)
                {
                    w.DispatchEntryId = this.Id;
                    w.Save(this.OwnerUserId);
                }
            }
            else
            {
                if (IsFieldDirty("TowSource"))
                {
                    // waypoints weren't specified... if towsource/towdest changed, we should update the waypoints
                    var pickup = this.Waypoints.FirstOrDefault(cn => cn.Title == "Pickup");
                    if (pickup == null)
                    {
                        pickup = new EntryWaypoint() { DispatchEntryId = this.Id, Title = "Pickup", Position = 1 };
                        this.waypoints.Add(pickup);
                    }
                    else
                    {
                        if (pickup.Address != this.TowSource)
                        {
                            pickup.Latitude = 0;
                            pickup.Longitude = 0;
                        }
                    }

                    if (pickup.DispatchEntryId == 0)
                        pickup.DispatchEntryId = this.Id;

                    pickup.Address = this.TowSource;
                    pickup.Save(this.OwnerUserId);
                }
                else
                {
                    var pickup = this.Waypoints.FirstOrDefault(cn => cn.Title == "Pickup");
                    if (pickup != null && pickup.IsDirty)
                    {
                        if (pickup.DispatchEntryId == 0)
                            pickup.DispatchEntryId = this.Id;

                        pickup.Save(this.OwnerUserId);
                    }
                }

                if (IsFieldDirty("TowDestination"))
                {
                    var dest = this.Waypoints.FirstOrDefault(cn => cn.Title == "Destination");
                    if (dest == null)
                    {
                        dest = new EntryWaypoint() { DispatchEntryId = this.Id, Title = "Destination", Position = 2 };
                        this.waypoints.Add(dest);
                    }
                    else
                    {
                        if (dest.Address != this.TowDestination)
                        {
                            dest.Latitude = 0;
                            dest.Longitude = 0;
                        }
                    }
                    if (dest.DispatchEntryId == 0)
                        dest.DispatchEntryId = this.Id;

                    dest.Address = this.TowDestination;
                    dest.Save(this.OwnerUserId);
                }
                else
                {
                    var dest = this.Waypoints.FirstOrDefault(cn => cn.Title == "Destination");
                    if (dest != null && dest.IsDirty)
                    {
                        if (dest.DispatchEntryId == 0)
                            dest.DispatchEntryId = this.Id;
                        dest.Save(this.OwnerUserId);
                    }
                }
            }
            #endregion

            // force totals to get updated when the call gets saved, and forces the invoice to be created 
            // on the DB.
            this.Invoice.DispatchEntry = this;
            await HandleBulk(this.Invoice.SaveAsync(token, ipAddress, overrideClosedPeriod));

            foreach (var x in Drivers)
            {
                InvoiceItem.InvoiceItemDriverCommission.DeleteByDriverId(this.Invoice.Id, x);
            }

            await HandleBulk(UpdateInvoiceStatusId(this.Invoice.Id));

            this.updateVersion();

            if (!dontPush)
            {
                if (token?.ClientVersionId == Platform.ClientVersion.GetByGitHash("locate-mvc", Platform.ClientVersionType.PublicRequest).Id)
                    await PushNotificationProvider.UpdateCall(this.CompanyId, this.Id, "customer-locate-update-pickup");
                else
                {
                    if (this.CreateDate > DateTime.Now.AddDays(-60) ||
                        this.CreateDate == DateTime.MinValue ||
                        (token?.UserId ?? 0) > 10)
                        await PushNotificationProvider.UpdateCall(this.CompanyId, this.Id);
                }

                await SearchUtility.UpdateCall(this);
            }

            await HandleBulk(UpdateCurrentCallCache(this));

            await AddEntryToQueueForAccounting(this);

            MarkAsDirty(false);

            return tasks;
        }

        [CacheKey("_entriesCurrent")]
        [Serializable]

        [ProtoContract(ImplicitFields = ImplicitFields.AllFields, Name = "CachedCallCurrent")]
        public class EntryCurrent
        {
            [PartitionKey]
            public int CompanyId { get; set; }

            [Key]
            public int EntryId { get; set; }

            public int StatusId { get; set; }
        }

        public static async Async.Task UpdateInAzure(Entry e, bool isDeleted = false, ConcurrentBag<Task> tasks = null)
        {
            var disable = await Core.GetRedisValueAsync("disable_azure_call_cache") == "1";

            if (disable || e == null || e.Id == 0 || e.CompanyId == 0)
                return;

            if (e.Id < 1 || e.CompanyId < 1 || e.Company == null)
                return;

            if (!isDeleted)
            {
                // don't upsert invoice item objects that aren't real.
                e.InvoiceItems = e.InvoiceItems.Where(o => o.Id > 0).ToCollection();

                if (tasks == null)
                    await CosmosDB.Get().UpsertItem("calls", await CallModel.MapAsync(e), new Microsoft.Azure.Cosmos.PartitionKey(e.CompanyId));
                else
                    tasks.Add(CosmosDB.Get().UpsertItemBulk("calls", await CallModel.MapAsync(e), new Microsoft.Azure.Cosmos.PartitionKey(e.CompanyId)));

                if (e.ChangedFields != null)
                {
                    var any = e.ChangedFields.FirstOrDefault(
                                                  o => o.OldValue != null &&
                                                      o.OldValue != "0" &&
                                                      o.OldValue != "" &&
                                                      o.OldValue != o.NewValue &&
                                                      o.Field == "CompanyId");

                    if (any != null && e.Id > 0 &&
                        int.TryParse(any.OldValue, out int nOldCompanyId) &&
                        nOldCompanyId != e.CompanyId)
                    {
                        await CosmosDB.Get().DeleteItem<Dispatch.CallModels.CallModel>(
                            "calls", e.Id.ToString(), nOldCompanyId);
                    }
                }
            }
            else
            {
                var cdb = CosmosDB.Get();
                var dm = await CallModel.MapAsync(e);
                
                dm.DeletionDetails = new DeletionModel()
                {
                    Date = e.DeletedDate,
                    UserId = e.DeletedUserId,
                    IpAddress = e.DeletedIp
                };

                if (tasks == null)
                {
                    await cdb.UpsertItem("calls-deleted", dm, new Microsoft.Azure.Cosmos.PartitionKey(e.CompanyId));
                    await cdb.DeleteItem<CallModel>("calls", e.Id.ToString(), new Microsoft.Azure.Cosmos.PartitionKey(e.CompanyId));
                }
                else
                {
                    tasks.Add(cdb.DeleteItem<CallModel>("calls", e.Id.ToString(), new Microsoft.Azure.Cosmos.PartitionKey(e.CompanyId)));
                    tasks.Add(cdb.UpsertItemBulk("calls-deleted", dm, new Microsoft.Azure.Cosmos.PartitionKey(e.CompanyId)));
                }
            }
        }

        /// <summary>
        /// dummy holder object for passing insights around, it doesn't get saved anywhere.
        /// </summary>
        [ProtoIgnore]
        public IDictionary<string, object> Insights { get; set; }

        /// <summary>
        /// A flag to indicate that this entry (save) is occuring in the 
        /// closed accounting period.
        /// </summary>
        [ProtoIgnore]
        [Write(false)]
        public bool ClosedPeriodActivitySave { get; set; } = false;

        public static async Async.Task UpdateInsight(int dispatchEntryId, int companyid, string key, object value)
        {
            //if (key == "PhotoCount")
            //    return;

            // TODO: use an Azure CosmosDB Stored Procedure to do this.

            var cm = (await CosmosDB.Get().GetItemAsync<CallModel>("calls",
                dispatchEntryId.ToString(), new Microsoft.Azure.Cosmos.PartitionKey(companyid)))?.Resource;

            if (cm != null)
            {
                if (value != null)
                    cm.Insights[key] = value;
                else
                {
                    if (cm.Insights.ContainsKey(key))
                        cm.Insights.Remove(key);
                }

                await CosmosDB.Get().UpsertItem("calls", cm, new Microsoft.Azure.Cosmos.PartitionKey(cm.CompanyId));

                var cacheKey = "insights:" + dispatchEntryId;
                await Core.SetRedisValueAsync(cacheKey, cm.Insights.ToJson(), TimeSpan.FromMinutes(30));
            }
        }

        public static async Async.Task UpdateInvoiceStatusAzure(
            int dispatchEntryId,
            int companyid,
            int invoiceStatusId,
            ConcurrentBag<Task> bulkTasks = null)
        {
            // TODO: use an Azure CosmosDB Stored Procedure to do this.

            var cm = (await CosmosDB.Get().GetItemAsync<CallModels.CallModel>("calls",
                dispatchEntryId.ToString(), new Microsoft.Azure.Cosmos.PartitionKey(companyid)))?.Resource;

            if (cm != null)
            {
                cm.InvoiceStatusId = invoiceStatusId;

                if (bulkTasks == null)
                    await CosmosDB.Get().UpsertItem("calls", cm, new Microsoft.Azure.Cosmos.PartitionKey(cm.CompanyId));
                else
                    bulkTasks.Add(CosmosDB.Get().UpsertItemBulk("calls", cm, new Microsoft.Azure.Cosmos.PartitionKey(cm.CompanyId)));
            }
        }

        public static async Async.Task UpdatePaymentVerifiedAzure(
            int companyId,
            int dispatchEntryId,
            int paymentId,
            int? verificationId)
        {
            // TODO: use an Azure CosmosDB Stored Procedure to do this.

            var cm = (await CosmosDB.Get().GetItemAsync<CallModel>("calls",
                dispatchEntryId.ToString(), new Microsoft.Azure.Cosmos.PartitionKey(companyId)))?.Resource;

            if (cm != null)
            {
                var p = cm.Payments.FirstOrDefault(o => o.Id == paymentId);
                if (p != null)
                {
                    p.PaymentVerificationId = verificationId;

                    await CosmosDB.Get().UpsertItem("calls", cm, new Microsoft.Azure.Cosmos.PartitionKey(cm.CompanyId));
                }
            }
        }

        public sealed class CallUsers
        {
            public int[] Drivers { get; set; }
            public int[] Users { get; set; }
            public bool? IsImpound { get; set; }
        }

        private static async Task SetAccessibleUsers(Entry e, bool force = false)
        {
            if (!force)
                if (await Core.GetRedisValueAsync("calls_access:" + e.Id) != null)
                    return;

            if (CompanyKeyValue.GetFirstValueOrNull(e.CompanyId, Provider.Towbook.ProviderId,
                "AllowDriversToViewUnassignedCalls") == "1")
                return;

            var cu = new CallUsers();

            var users = (await Task.WhenAll(e.Drivers
                .Select(o => Driver.GetByIdAsync(o))))
                .Where(z => z != null)
                .Select(u => u.UserId)
                .ToArray();

            cu.Users = users;
            cu.Drivers = e.Drivers.ToArray();
            
            if (e.Impound)
                cu.IsImpound = true;

            if (cu.Users == null || !cu.Users.Any())
            {
                await Core.DeleteRedisKeyAsync("calls_access:" + e.Id);
            }
            else
            {
                await Core.SetRedisValueAsync("calls_access:" + e.Id,
                    cu.ToJson(), TimeSpan.FromMinutes(15));
            }
        }

        static async Async.Task UpdateCurrentCallCache(Entry e, bool isDeleted = false)
        {
            var disableEc = await Core.GetRedisValueAsync("disable_redis_call_cache") == "1";

            if (e == null || e.Id == 0 || e.CompanyId == 0)
                return;

            await UpdateInAzure(e, isDeleted);

            await SetAccessibleUsers(e, true);


            try
            {
                // ensure the cache is primed, otherwise we might end up creating a 
                // partition set with just this call when there are others in the db
                // that arent in the cache yet. 

                GetCachedCurrentByCompany(new int[] { e.CompanyId });

                if (isDeleted || e.Status == Status.Completed || e.Status == Status.Cancelled)
                {
                    // remove from current call list if its present.
                    Cache.Instance.PartitionDelete<CachedCall>(e.CompanyId, e.Id);

                    if (!disableEc)
                    {
                        if (!isDeleted)
                        {
                            await Cache.Instance.PartitionSetAsync(
                                new EntryCurrent() { CompanyId = e.CompanyId, EntryId = e.Id, StatusId = e.Status.Id });

                            var cl = Cache.Instance.PartitionGetAll<EntryCurrent>(e.CompanyId);

                            if (e.Status == Status.Completed)
                            {
                                var currentCompleted = cl.Where(o => o.StatusId == Status.Completed.Id);

                                foreach (var tr in currentCompleted.OrderByDescending(o => o.EntryId).Skip(20))
                                {
                                    await Cache.Instance.PartitionDeleteAsync(tr);
                                }
                            }
                            else if (e.Status == Status.Cancelled)
                            {
                                var currentCancelled = cl.Where(o => o.StatusId == Status.Cancelled.Id);

                                foreach (var tr in currentCancelled.OrderByDescending(o => o.EntryId).Skip(20))
                                {
                                    await Cache.Instance.PartitionDeleteAsync(tr);
                                }
                            }
                        }
                        else
                        {
                            Cache.Instance.PartitionDelete<EntryCurrent>(e.CompanyId, e.Id);
                        }
                    }

                }
                else
                {
                    // add or replace 
                    await Cache.Instance.PartitionSetAsync(new CachedCall(e.Id, e.CompanyId, e.CallNumber, e.Status.Id,
                        CachedCall.GetEndingLocation(e), e.Drivers));

                    if (!disableEc)
                        await Cache.Instance.PartitionSetAsync(
                            new EntryCurrent() { CompanyId = e.CompanyId, EntryId = e.Id, StatusId = e.Status.Id });
                }

            }
            catch
            {

            }
        }

        [CacheKey("_cachedCalls")]
        [Serializable]

        [ProtoContract(ImplicitFields = ImplicitFields.AllFields, Name = "CachedCall")]
        public class CachedCall
        {
            [Key]
            public int Id { get; }
            [PartitionKey]
            public int CompanyId { get; set; }
            public int CallNumber { get; }
            public int StatusId { get; }
            public LatLng EndingLocation { get; }
            public List<int> Drivers { get; }

            internal static LatLng GetEndingLocation(Entry e)
            {
                if (e.Waypoints.Any(o => o.Position == 0 && o.Title != "Start"))
                {
                    var p = e.Waypoints.FirstOrDefault(o => o.Title == "Pickup");
                    var d = e.Waypoints.FirstOrDefault(o => o.Title == "Destination");

                    p.Position = 1;
                    if (d != null)
                        d.Position = 2;

                    if (d != null && d.Latitude != 0 && d.Longitude != 0 && d.Latitude != d.Longitude)
                        return new LatLng(d.Latitude, d.Longitude);
                    else if (p.Latitude != 0 && p.Longitude != 0 && p.Latitude != p.Longitude)
                        return new LatLng(p.Latitude, p.Longitude);
                }
                else
                {
                    var last = e.Waypoints.OrderBy(o => o.Position).LastOrDefault();
                    if (last != null && (last.Latitude != 0 && last.Longitude != 0 &&
                        last.Latitude != last.Longitude))
                        return new LatLng(last.Latitude, last.Longitude);
                }

                return null;
            }

            public CachedCall() { }

            public CachedCall(int id, int companyId, int callNumber, int statusId, LatLng endingLocation, List<int> drivers)
            {
                Id = id;
                CompanyId = companyId;
                CallNumber = callNumber;
                StatusId = statusId;
                EndingLocation = endingLocation;
                Drivers = drivers;
            }

            public override bool Equals(object obj)
            {
                return obj is CachedCall other &&
                       Id == other.Id &&
                       CallNumber == other.CallNumber &&
                       CompanyId == other.CompanyId &&
                       StatusId == other.StatusId &&
                       EqualityComparer<LatLng>.Default.Equals(EndingLocation, other.EndingLocation) &&
                       EqualityComparer<List<int>>.Default.Equals(Drivers, other.Drivers);
            }

            public override int GetHashCode()
            {
                var hashCode = 1026572767;
                hashCode = hashCode * -1521134295 + Id.GetHashCode();
                hashCode = hashCode * -1521134295 + CompanyId.GetHashCode();
                hashCode = hashCode * -1521134295 + CallNumber.GetHashCode();
                hashCode = hashCode * -1521134295 + StatusId.GetHashCode();
                hashCode = hashCode * -1521134295 + EqualityComparer<LatLng>.Default.GetHashCode(EndingLocation);
                hashCode = hashCode * -1521134295 + EqualityComparer<List<int>>.Default.GetHashCode(Drivers);
                return hashCode;
            }
        }

        [Serializable]

        [ProtoContract(ImplicitFields = ImplicitFields.AllFields, Name = "LatLng")]
        public class LatLng
        {
            public decimal? Latitude { get; set; }
            public decimal? Longitude { get; set; }

            public LatLng() { }
            public LatLng(decimal? lat, decimal lng)
            {
                this.Latitude = lat;
                this.Longitude = lng;
            }
        }

        private void DbInsert()
        {
            Nullable<int> accountId = null;

            if (_accountId > 0)
                accountId = _accountId;

            if (_arrivalETA < SqlDateTime.MinValue.Value)
                _arrivalETA = null;

            using (var r = SqlHelper.ExecuteReader(Core.ConnectionString,
                "DispatchEntriesInsert",
                new SqlParameter("@CompanyId", _companyId),
                new SqlParameter("@AccountId", accountId),
                new SqlParameter("@DispatchReasonId", (_reasonId > 0 ? (int?)_reasonId : null)),
                new SqlParameter("@Notes", _notes),
                new SqlParameter("@VIN", _vin),
                new SqlParameter("@VehicleYear", _year),
                new SqlParameter("@VehicleManufacturer", _manufacturerId > 0 ? (int?)_manufacturerId : null),
                new SqlParameter("@VehicleModel", _modelId > 0 ? (int?)_modelId : null),
                new SqlParameter("@VehicleColorId", _colorId > 0 ? (int?)_colorId : null),
                new SqlParameter("@BodyTypeId", _bodyTypeId > 0 ? (int?)_bodyTypeId : null),
                new SqlParameter("@TowSource", _towSource),
                new SqlParameter("@TowDestination", _towDestination),
                new SqlParameter("@TruckId", TruckId > 0 ? (int?)TruckId : null),
                new SqlParameter("@DriverId", DriverId > 0 ? (int?)DriverId : null),
                new SqlParameter("@DispatchTime", _dispatchTime),
                new SqlParameter("@EnRouteTime", _enrouteTime),
                new SqlParameter("@ArrivalTime", _arrivalTime),
                new SqlParameter("@ArrivalETA", _arrivalETA),
                new SqlParameter("@TowTime", _towTime),
                new SqlParameter("@CompletionTime", _completionTime),
                new SqlParameter("@LicenseNumber", _licenseNumber),
                new SqlParameter("@LicenseState", _licenseState),
                new SqlParameter("@LicenseYear", _licenseYear),
                new SqlParameter("@Drivable", _drivable),
                new SqlParameter("@Odometer", _odometer),
                new SqlParameter("@Impound", _impound),
                new SqlParameter("@Priority", _priority),
                new SqlParameter("@Status", (_status != null ? _status.Id : 0)),
                new SqlParameter("@OwnerUserId", _ownerUserId),
                new SqlParameter("@Created", _created),
                new SqlParameter("@CreateDate", _createDate),
                new SqlParameter("@Type", _type),
                new SqlParameter("@InvoiceStatusId", (_invoiceStatusId > 0 ? (int?)InvoiceStatusId : null)),
                new SqlParameter("@InvoiceNumber", _invoiceNumber),
                new SqlParameter("@PurchaseOrderNumber", _purchaseOrderNumber),
                new SqlParameter("@DestinationArrivalTime", _destinationArrivalTime)))
            {
                if (r.Read())
                {
                    this._id = r.GetValue<int>("Id");
                    this._callNumber = r.GetValue<int>("CallNumber");
                    this.CreateDate = r.GetValue<DateTime>("CreateDate");
                }
            }
        }

        private async Task DbInsertAsync()
        {
            Nullable<int> accountId = null;

            if (_accountId > 0)
                accountId = _accountId;

            if (_arrivalETA < SqlDateTime.MinValue.Value)
                _arrivalETA = null;

            using (var r = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString,
                "DispatchEntriesInsert",
                new SqlParameter("@CompanyId", _companyId),
                new SqlParameter("@AccountId", accountId),
                new SqlParameter("@DispatchReasonId", (_reasonId > 0 ? (int?)_reasonId : null)),
                new SqlParameter("@Notes", _notes),
                new SqlParameter("@VIN", _vin),
                new SqlParameter("@VehicleYear", _year),
                new SqlParameter("@VehicleManufacturer", _manufacturerId > 0 ? (int?)_manufacturerId : null),
                new SqlParameter("@VehicleModel", _modelId > 0 ? (int?)_modelId : null),
                new SqlParameter("@VehicleColorId", _colorId > 0 ? (int?)_colorId : null),
                new SqlParameter("@BodyTypeId", _bodyTypeId > 0 ? (int?)_bodyTypeId : null),
                new SqlParameter("@TowSource", _towSource),
                new SqlParameter("@TowDestination", _towDestination),
                new SqlParameter("@TruckId", TruckId > 0 ? (int?)TruckId : null),
                new SqlParameter("@DriverId", DriverId > 0 ? (int?)DriverId : null),
                new SqlParameter("@DispatchTime", _dispatchTime),
                new SqlParameter("@EnRouteTime", _enrouteTime),
                new SqlParameter("@ArrivalTime", _arrivalTime),
                new SqlParameter("@ArrivalETA", _arrivalETA),
                new SqlParameter("@TowTime", _towTime),
                new SqlParameter("@CompletionTime", _completionTime),
                new SqlParameter("@LicenseNumber", _licenseNumber),
                new SqlParameter("@LicenseState", _licenseState),
                new SqlParameter("@LicenseYear", _licenseYear),
                new SqlParameter("@Drivable", _drivable),
                new SqlParameter("@Odometer", _odometer),
                new SqlParameter("@Impound", _impound),
                new SqlParameter("@Priority", _priority),
                new SqlParameter("@Status", (_status != null ? _status.Id : 0)),
                new SqlParameter("@OwnerUserId", _ownerUserId),
                new SqlParameter("@Created", _created),
                new SqlParameter("@CreateDate", _createDate),
                new SqlParameter("@Type", _type),
                new SqlParameter("@InvoiceStatusId", (_invoiceStatusId > 0 ? (int?)InvoiceStatusId : null)),
                new SqlParameter("@InvoiceNumber", _invoiceNumber),
                new SqlParameter("@PurchaseOrderNumber", _purchaseOrderNumber),
                new SqlParameter("@DestinationArrivalTime", _destinationArrivalTime)))
            {
                if (await r.ReadAsync())
                {
                    this._id = r.GetValue<int>("Id");
                    this._callNumber = r.GetValue<int>("CallNumber");
                    this.CreateDate = r.GetValue<DateTime>("CreateDate");
                }
            }
        }

        private void DbUpdate()
        {
            Nullable<int> accountId = null;
            Nullable<int> ownerUserId = null;

            if (_accountId > 0)
                accountId = _accountId;

            if (_ownerUserId > 0)
                ownerUserId = _ownerUserId;


            SqlHelper.ExecuteNonQuery(Core.ConnectionString,
                "DispatchEntriesUpdateById",
                new SqlParameter("@DispatchEntryId", _id),
                new SqlParameter("@CompanyId", _companyId),
                new SqlParameter("@AccountId", accountId),
                new SqlParameter("@DispatchReasonId", (_reasonId > 0 ? (int?)_reasonId : null)),
                new SqlParameter("@Notes", _notes),
                new SqlParameter("@VIN", _vin),
                new SqlParameter("@VehicleYear", _year),
                new SqlParameter("@VehicleManufacturer", _manufacturerId > 0 ? (int?)_manufacturerId : null),
                new SqlParameter("@VehicleModel", _modelId > 0 ? (int?)_modelId : null),
                new SqlParameter("@VehicleColorId", _colorId > 0 ? (int?)_colorId : null),
                new SqlParameter("@BodyTypeId", _bodyTypeId > 0 ? (int?)_bodyTypeId : null),
                new SqlParameter("@TowSource", _towSource),
                new SqlParameter("@TowDestination", _towDestination),
                new SqlParameter("@TruckId", TruckId > 0 ? (int?)TruckId : null),
                new SqlParameter("@DriverId", DriverId > 0 ? (int?)DriverId : null),
                new SqlParameter("@DispatchTime", _dispatchTime),
                new SqlParameter("@EnRouteTime", _enrouteTime),
                new SqlParameter("@ArrivalTime", _arrivalTime),
                new SqlParameter("@ArrivalETA", _arrivalETA),
                new SqlParameter("@TowTime", _towTime),
                new SqlParameter("@CompletionTime", _completionTime),
                new SqlParameter("@LicenseNumber", _licenseNumber),
                new SqlParameter("@LicenseState", _licenseState),
                new SqlParameter("@LicenseYear", _licenseYear),
                new SqlParameter("@Drivable", _drivable),
                new SqlParameter("@Odometer", _odometer),
                new SqlParameter("@Impound", _impound),
                new SqlParameter("@Priority", _priority),
                new SqlParameter("@Status", (_status != null ? _status.Id : 0)),
                new SqlParameter("@OwnerUserId", _ownerUserId),
                new SqlParameter("@Created", _created),
                new SqlParameter("@CreateDate", _createDate),
                new SqlParameter("@Type", _type),
                new SqlParameter("@InvoiceStatusId", (_invoiceStatusId > 0 ? (int?)InvoiceStatusId : null)),
                new SqlParameter("@InvoiceNumber", _invoiceNumber),
                new SqlParameter("@PurchaseOrderNumber", _purchaseOrderNumber),
                new SqlParameter("@DestinationArrivalTime", _destinationArrivalTime));
        }

        public async Task Cancel(string reason, AuthenticationToken token, string ipAddress) =>
            await Cancel(reason, token, ipAddress, null);

        /// <summary>
        /// Cancels the dispatch call.
        /// </summary>
        /// <param name="reason">Description of the reason the call was cancelled.</param>
        /// <param name="token">user token for activity logging</param>
        /// <param name="ipAddress">user ipAddress for activity logging</param>
        public async Task Cancel(string reason, AuthenticationToken token = null, string ipAddress = null, string notes = null)
        {
            _status = Status.Cancelled;

            CacheClearById(this.Id, this.AccountId);

            var ret = SqlMapper.QuerySP<dynamic>("DispatchEntriesCancelById",
                new
                {
                    @DispatchEntryId = _id
                }).FirstOrDefault();

            if (ret != null)
            {
                this.Version = ret.Version;
                this.CompletionTime = ret.CompletionTime;
            }

            var ec = new EntryCancellation()
            {
                DispatchEntryId = this.Id,
                Reason = reason,
                Notes = notes,
                CreateDate = DateTime.Now,
                OwnerUserId = token != null ? token.UserId : (int?)null,
                IpAddress = ipAddress,
                Deleted = false
            };

            this.CancellationReason = reason;

            if (ec != null)
                await ec.Save(token, ipAddress);

            if (token?.UserId < 100 )
            {
                // motor club cancelled. require acknowledgement.

                if (this.Attributes.ContainsKey(AttributeValue.BUILTIN_DISPATCH_CANCEL_ACKNOWLEDGEMENT_JSON))
                {
                    await this.Attributes[AttributeValue.BUILTIN_DISPATCH_CANCEL_ACKNOWLEDGEMENT_JSON].Delete();
                }

                this.SetAttribute(AttributeValue.BUILTIN_DISPATCH_CANCEL_ACKNOWLEDGEMENT_JSON, "{}");
                await this.Attributes[AttributeValue.BUILTIN_DISPATCH_CANCEL_ACKNOWLEDGEMENT_JSON].SaveAsync();
            }

            if (_impound)
            {
                // make sure to stop accruing storage
                this.Invoice.NextScheduledRecalculate = null;
                await this.Invoice.SaveAsync(token, ipAddress);
            }

            // add internal note to entry explaining reason for user cancellation
            if (token?.UserId > 0 && !string.IsNullOrEmpty(reason))
            {
                var en = new EntryNote()
                {
                    DispatchEntryId = this.Id,
                    Content = $"Call Cancelled [{reason}{(string.IsNullOrEmpty(notes) ? "" : " | " + notes)}]",
                    OwnerUserId = token?.UserId ?? 1,
                    CreateDate = DateTime.Now,
                };
                await en.SaveAsync();
            }
            
            await UpdateCurrentCallCache(this);
            await NotifyDriverOfCancellation(this, token?.UserId ?? 0);
            await PushNotificationProvider.UpdateCall(this.CompanyId, this.Id, "cancel");
        }

        private async Task NotifyDriverOfCancellation(Entry call, int userId)
        {
            var usersToNotify = new List<User>();

            foreach (var driver in call.Drivers)
            {
                var d = await Driver.GetByIdAsync(driver);
                if (d != null && d.UserId > 0)
                {
                    var u = await User.GetByIdAsync(d.UserId);
                    if (u != null)
                    {
                        // dont send it to the user if the user that cancelled it is the driver.
                        if (u.Id != userId)
                        {
                            usersToNotify.Add(u);
                        }
                    }
                }
            }

            string message = String.Empty;
            var name = (await User.GetByIdAsync(userId))?.FullName;

            if (CompanyKeyValue.GetFirstValueOrNull(call.CompanyId, Provider.Towbook.ProviderId, "Towbook_Calls_HideDispatcherFromDrivers") == "1")
                name = call.Company.Name;

            if (!string.IsNullOrWhiteSpace(name))
                message = $"{name} cancelled the call.";

            var hide = CompanyKeyValue.GetFirstValueOrNull(call.CompanyId, Provider.Towbook.ProviderId, "HideAccountDetailsFromDrivers");
            var hideAccount = (hide == "2" || hide == "3" || hide == "4");

            var tasks = new Collection<Task>();
            foreach (var u in usersToNotify)
            {
                var values = new Dictionary<string, string>();

                values.Add("Message", message);

                if (!hideAccount && call.Account != null)
                    values.Add("Account", call.Account.Company);

                values.Add("Time", Core.OffsetDateTime(u.Company, DateTime.Now).ToShortTowbookTimeString());

                var keys = u.GetKeys();

                values.Add("Call Number", call.CallNumber.ToString());
                values.Add("senderUserId", userId > 0 ? userId.ToString() : "1");

                if (!string.IsNullOrEmpty(call.PurchaseOrderNumber))
                    values.Add("PO #", call.PurchaseOrderNumber);

                if (keys.Any(o => o.Key == "notificationhub_registration_id"))
                {
                    tasks.Add(NotificationHubHelper.GeneralChannelInstance.SendNotificationMessage(u,
                        "Call #" + call.CallNumber + " has been cancelled.",
                        values,
                        true, "Call Cancelled"));
                }
            }
            await Task.WhenAll(tasks);
        }


        /// <summary>
        /// Cancels the dispatch call.
        /// </summary>
        /// <param name="reason">Description of the reason the call was cancelled.</param>
        public async Async.Task Cancel(string reason)
        {
            await Cancel(reason, null, null);
        }

        /// <summary>
        /// Uncancels a dispatch call.
        /// </summary>
        /// <param name="id">The dispatch entry id that is to be uncanceled.</param>
        public async Async.Task Uncancel()
        {
            await Uncancel(null, null);
        }

        public static async Task<Collection<Entry>> GetCurrentByCompanySuperchargedAsync(
            int[] companies,
            bool showCurrent,
            bool showPrevious,
            bool showCancelled,
            bool newDispatchingMode = false,
            int[] driverIds = null)
        {
            if (Core.GetRedisValue("disable_redis_call_cache") == "1")
            {
                return await GetCurrentByCompanyAsync(
                    companies, showCurrent, showPrevious, showCancelled,
                    newDispatchingMode, driverIds);
            }

            var list = new Collection<Entry>();

            // We're going to use Redis for getting the list instead of SQL 
            foreach (var each in companies)
            {
                var current = (await Cache.Instance.PartitionGetAllAsync<EntryCurrent>(each,
                  async partitionId =>
                  {
                      var curSlow = await GetCurrentByCompanyAsync(
                          new int[] { each }, true, true, true, true);

                      var rt = new Collection<EntryCurrent>();
                      foreach (var x in curSlow)
                      {
                          rt.Add(new EntryCurrent()
                          {
                              CompanyId = x.CompanyId,
                              EntryId = x.Id,
                              StatusId = x.Status.Id
                          });
                      }

                      return rt;
                  })).ToCollection();

                int[] curStatuses = { 0, 1, 2, 3, 4, 7 };

                if (current == null)
                {
                    // If the list doesn't exist, we'll use our old method and then populate this.
                    Console.WriteLine("Need to populate:" + each);
                }
                else
                {
                    if (current.Any())
                    {
                        if (!showCurrent)
                            current = current.Where(o => !curStatuses.Contains(o.StatusId)).ToCollection();

                        if (!showPrevious)
                            current = current.Where(o => o.StatusId != 5).ToCollection();

                        if (!showCancelled)
                            current = current.Where(o => o.StatusId != 255).ToCollection();

                        list = list.Union(await GetByIdsAsync(
                              current.Select(o => o.EntryId).ToArray())).ToCollection();
                    }
                }
            }

            if (driverIds != null && driverIds.Any())
                list = list.Where(o =>
                    driverIds.Any(r => o.Drivers.Any(ox => ox == r)))
                    .ToCollection();

            return list;
        }

        public async Async.Task Uncancel(AuthenticationToken token = null, string ipAddress = null)
        {
            if (this.Id < 1)
                throw new TowbookException("Can't uncancel call that hasn't been created.");

            _status = Status.Waiting;

            this.Version++;
            CacheClearById(this.Id, this.AccountId);

            SqlHelper.ExecuteNonQuery(Core.ConnectionString,
                "DispatchEntriesUncancelById",
                new SqlParameter("@DispatchEntryId", _id));

            Dispatch.EntryCancellation ec = await Dispatch.EntryCancellation.GetByDispatchEntryIdAsync(this.Id);
            if (ec == null)
            {
                ec = new Dispatch.EntryCancellation()
                {
                    DispatchEntryId = this.Id,
                    Reason = String.Empty,
                    CreateDate = DateTime.Now,
                    OwnerUserId = token != null ? token.UserId : (int?)null,
                    IpAddress = ipAddress
                };
            }
            ec.Deleted = true;
            await ec.Save(token, ipAddress);

            var av = (await AttributeValue.GetByDispatchEntryAsync(this)).FirstOrDefault(o => o.Key == AttributeValue.BUILTIN_DISPATCH_CANCEL_ACKNOWLEDGEMENT_JSON);

            if (av.Value != null)
                await av.Value.Delete();

            await PushNotificationProvider.UpdateCall(this.CompanyId, this.Id, "uncancel");
            await UpdateCurrentCallCache(this);
        }

        public static Collection<Entry> Find(Company.Company company, string search)
        {
            return Find(new int[] { company.Id }, search, null, null);
        }

        public static Collection<Entry> Find(int companyId, string search)
        {
            return Find(new int[] { companyId }, search, null, null);
        }

        /// <summary>
        /// Searches by Call number, invoice number, contact name, contact phone
        /// </summary>
        /// <param name="p"></param>
        /// <returns></returns>
        public static Collection<Entry> Find(int[] companyIds, string search, int? startAtDispatchEntryId = null, int? numberOfResults = null)
        {
            Collection<Entry> entries = new Collection<Entry>();

            if (String.IsNullOrEmpty(search))
                return entries;

            int callNumber = 0;
            bool useCall = true;

            if (!Int32.TryParse(search, out callNumber))
            {
                useCall = false;
            }

            using (SqlDataReader dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                "DispatchEntriesFindByCompanyId",
                    new SqlParameter("@CompanyId", String.Join(",", companyIds)),
                    new SqlParameter("@Search", search),
                    new SqlParameter("@SearchInt",
                        (useCall == true ? (int?)callNumber : null)),
                    new SqlParameter("@StartAtDispatchEntryNumber", startAtDispatchEntryId),
                    new SqlParameter("@Limit", numberOfResults)))
            {
                while (dr.Read())
                {
                    var newEntry = new Entry(dr);

                    if (useCall == true && newEntry.CallNumber == callNumber)
                    {
                        entries.Insert(0, newEntry);
                    }
                    else if (newEntry.InvoiceNumber == search)
                    {
                        entries.Insert(0, newEntry);
                    }
                    else
                        entries.Add(newEntry);
                }
            }

            return entries;
        }

        public async Async.Task Delete()
        {
            SqlHelper.ExecuteNonQuery(Core.ConnectionString,
                "DispatchEntriesDeleteById",
                new SqlParameter("@DispatchEntryId", _id));

            await UpdateCurrentCallCache(this, true);
        }

        public string DeletedIp { get; internal set; }
        public int DeletedUserId { get; internal set; }
        public DateTime DeletedDate { get; internal set; }
        public bool IsDeleted
        {
            get;
            internal set;
        }

        public async Async.Task Delete(AuthenticationToken token, string ipAddress)
        {
            SqlHelper.ExecuteNonQuery(Core.ConnectionString,
                "DispatchEntriesDeleteById",
                new SqlParameter("@DispatchEntryId", _id));

            var x = new TrackableObject();
            await x.SaveAsync(token,
                ActivityLogType.DispatchEntry,
                _id,
                ActivityLogType.Company,
                _companyId,
                3,
                ipAddress);

            var log = new EntryDeleteLog()
            {
                DispatchEntryId = _id,
                DeleteDate = DateTime.Now,
                OwnerUserId = token.UserId
            };
            log.Save();
            
            DeletedUserId = log.OwnerUserId;
            DeletedDate = log.DeleteDate;
            DeletedIp = ipAddress;

            CacheClearById(_id, this.AccountId);

            await PushNotificationProvider.Push(_companyId, "call_update",
                new
                {
                    callId = this.Id,
                    extra = "delete",
                    userId = token.UserId
                });

            await UpdateCurrentCallCache(this, true);
            await SearchUtility.DeleteCall(this);
        }

        /// <summary>
        /// Lock a call
        /// </summary>
        /// <param name="lockUserId"></param>
        /// <returns></returns>
        public async Task<bool> Lock(int lockedByUserId, AuthenticationToken token = null, string ipAddress = null)
        {
            if (this.Impound && !this.Released)
                return false;

            var dmx = SqlMapper.QuerySP<dynamic>("DispatchEntriesLockById",
              new
              {
                  DispatchEntryId = this.Id,
                  LockedByUserId = lockedByUserId
              })
              .FirstOrDefault();


            if (dmx != null)
            {
                var x = new TrackableObject();

                await x.SaveAsync(token,
                    ActivityLogType.DispatchEntry,
                    this.Id,
                    ActivityLogType.Company,
                    this.CompanyId,
                    (int)ActivityLogActionType.Lock,
                    ipAddress);

                var autoMove = CompanyKeyValue.GetFirstValueOrNull(CompanyId, Provider.Towbook.ProviderId, "InvoiceStatus_AutoReadyToBill");
                if (autoMove == "2" || (autoMove == "3" && Attributes.ContainsKey(AttributeValue.BUILTIN_DISPATCH_AUDITED)))
                    await UpdateInvoiceStatusIds(new[] { this.Id }, (int)InvoiceStatus.InvoiceStatusType.ReadyToBill);


                this.DispatchEntryLockId = (int?)dmx.DispatchEntryLockId;
                await UpdateInsight(this.Id, this.CompanyId, "lockedByUserId", lockedByUserId);
                return true;
            }


            return false;
        }
        
        public async Task<bool> Lock(int lockedByUserId) => await Lock(lockedByUserId, null, null);


        public async Task Unlock(int unlockedByUserId, AuthenticationToken token = null, string ipAddress = null)
        {
            var dmx = SqlMapper.QuerySP<dynamic>("DispatchEntriesUnlockById",
              new
              {
                  DispatchEntryId = this.Id,
                  UnlockedByUserId = unlockedByUserId
              })
              .FirstOrDefault();

            if (dmx != null)
            {
                var x = new TrackableObject();

                await x.SaveAsync(token,
                    ActivityLogType.DispatchEntry,
                    this.Id,
                    ActivityLogType.Company,
                    this.CompanyId,
                    (int)ActivityLogActionType.Unlock,
                    ipAddress);

                this.DispatchEntryLockId = null;
            }

            await UpdateInsight(this.Id, this.CompanyId, "lockedByUserId", null);
        }

        public async Task Unlock(int unlockedByUserId) => await Unlock(unlockedByUserId, null, null);

        public int? DispatchEntryLockId;

        /// <summary>
        /// Returns whether the call is currently locked. will NOT attempt to auto-lock the call. Use IsLockedAsync to trigger auto-lock logic. 
        /// </summary>
        public bool IsLocked { get => IsEntryLocked(async: false).GetAwaiter().GetResult(); }

        /// <summary>
        /// Returns whether the call is locked, after applying auto-lock changes if appropriate conditions apply.
        /// </summary>
        /// <returns></returns>
        public async Task<bool> IsLockedAsync() => await IsEntryLocked(async: true);

        private async Task<bool> IsEntryLocked(bool async = false)
        {
            if (Id == 0)
                return false; // can't lock an unsaved call

            if (DispatchEntryLockId.GetValueOrDefault() > 0)
                return true;

            if (Insights != null && Insights.Any(r => r.Key.ToLowerInvariant() == "lockedbyuserid"))
                return true;

            if (InternalBlockWrites)
                return false;

            if (async)
            {
                if (Status != null &&
                   Status == Status.Completed &&
                   CompletionTime != null &&
                   Attributes.Any(f => f.Key == AttributeValue.BUILTIN_AUTOLOCK_EVENT_JSON))
                {
                    var akv = this.Attributes[AttributeValue.BUILTIN_AUTOLOCK_EVENT_JSON];
                    var autoLockEvent = Newtonsoft.Json.JsonConvert.DeserializeObject<AutoLockEventJson>(akv.Value);

                    if (autoLockEvent.Offset != null)
                    {
                        if (!autoLockEvent.Applied)
                        {
                            // check for auto lock scenario...
                            if (this.CompletionTime.Value.AddHours(autoLockEvent.Offset.Value) <= DateTime.Now)
                            {
                                akv.Value = new AutoLockEventJson()
                                {
                                    Applied = true,
                                    AppliedDate = DateTime.Now,
                                    Offset = autoLockEvent.Offset.Value
                                }.ToJson();
                                await akv.SaveAsync();

                                // don't lock a current impound.
                                if (this.Impound && !this.Released)
                                    return false;


                                // fire an event to auto-lock it. Redis pubsub sync?


                                // Lock it with performer of Towbook Internal Event
                                return await Lock(1);
                            }
                        }
                    }
                }
            }

            return false;
        }

        /// <summary>
        /// Returns the DispatchEntryId of the recovered call. 
        /// If it couldn't be undeleted, it will return 0.
        /// </summary>
        /// <param name="token"></param>
        /// <param name="ipAddress"></param>
        /// <param name="companyId"></param>
        /// <param name="callNumber"></param>
        public static async Task<int> UndeleteByCallNumber(AuthenticationToken token, string ipAddress, int companyId, int callNumber)
        {
            var dmx = SqlMapper.QuerySP<dynamic>("DispatchEntriesUndeleteByCallNumber",
                new { CompanyId = companyId, CallNumber = callNumber })
                .FirstOrDefault();

            if (dmx != null)
            {
                int dispatchEntryId = (int)dmx.DispatchEntryId;
                var x = new TrackableObject();

                await x.SaveAsync(token,
                    ActivityLogType.DispatchEntry,
                    dispatchEntryId,
                    ActivityLogType.Company,
                    companyId,
                    6,
                    ipAddress);

                var en = await Entry.GetByIdAsync(dispatchEntryId);
                if (en != null)
                    await Entry.UpdateInAzure(en);

                await PushNotificationProvider.UpdateCall(companyId, dmx.DispatchEntryId, "undelete");

                return dmx.DispatchEntryId;
            }
            else
            {
                return 0;
            }
        }


        /// <summary>
        /// Returns the DispatchEntryId of the recovered call. 
        /// If it couldn't be undeleted, it will return 0.
        /// </summary>
        /// <param name="token"></param>
        /// <param name="ipAddress"></param>
        /// <param name="companyId"></param>
        /// <param name="callNumber"></param>
        public static async Task<int> UndeleteById(AuthenticationToken token, string ipAddress, int id)
        {
            var dmx = SqlMapper.QuerySP<dynamic>("DispatchEntriesUndeleteById",
                new
                {
                    @DispatchEntryId = id,
                    @UserId = token.UserId
                }).FirstOrDefault();

            if (dmx != null && dmx.DispatchEntryId > 0 && dmx.Deleted == false)
            {
                var x = new TrackableObject();

                var en = Entry.GetById(dmx.DispatchEntryId);

                if (en != null)
                {
                    await x.SaveAsync(token,
                        ActivityLogType.DispatchEntry,
                        dmx.DispatchEntryId,
                        ActivityLogType.Company,
                        en.CompanyId,
                        6,
                        ipAddress);

                    await Entry.UpdateInAzure(en);

                    await PushNotificationProvider.UpdateCall(en.CompanyId, dmx.DispatchEntryId, "undelete");
                }

                return dmx.DispatchEntryId;
            }
            else
            {
                return 0;
            }
        }


        public static Collection<Entry> GetByDriver(Driver driver, DateTime startDate, DateTime endDate)
        {
            Collection<Entry> entries = new Collection<Entry>();

            using (SqlDataReader dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                "DispatchEntriesGetByDriverId",
                    new SqlParameter("@DriverId", driver.Id),
                    new SqlParameter("@StartDate", startDate.AddMilliseconds(-1)),
                    new SqlParameter("@StopDate", endDate.AddMilliseconds(1))))
            {
                while (dr.Read())
                {
                    entries.Add(new Entry(dr));
                }
            }
            //System.Diagnostics.Debug.WriteLine("GetByDriver: " + driver.Id.ToString() + "," + startDate + "," + endDate + " returned: " + entries.Count);

            return entries;
        }

        public static async Task<Collection<Entry>> GetByDriversAsync(int[] drivers, DateTime startDate, DateTime endDate, int[] companyIds)
        {
            Collection<Entry> entries = new Collection<Entry>();

            using (SqlDataReader dr = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString,
                "DispatchEntriesGetByDriverIds",
                    new SqlParameter("@CompanyIds", string.Join(",", companyIds)),
                    new SqlParameter("@DriverIds", string.Join(",", drivers)),
                    new SqlParameter("@StartDate", startDate.AddMilliseconds(-1)),
                    new SqlParameter("@StopDate", endDate.AddMilliseconds(1))))
            {
                while (await dr.ReadAsync())
                {
                    entries.Add(new Entry(dr));
                }
            }

            entries = await EntriesInitializeMultipleAsync(entries);

            var commissions = InvoiceItem.InvoiceItemDriverCommission.GetByInvoiceItemIds(entries.SelectMany(o => o.InvoiceItems).Select(o => o.Id).ToArray());

            foreach (var commissionInvoice in commissions.GroupBy(o => o.InvoiceId))
            {
                var inv = entries.Where(o => o.Invoice.Id == commissionInvoice.Key).FirstOrDefault();

                if (inv != null)
                {
                    foreach (var group in commissionInvoice.GroupBy(o => o.InvoiceItemId))
                    {
                        var iil = inv.InvoiceItems.FirstOrDefault(o => o?.Id == group.Key);
                        if (iil != null)
                            iil.DriverCommissions = group.ToCollection();
                    }
                }
            }
            foreach(var each in entries)
            {
                foreach(var invoiceItem in each.Invoice.InvoiceItems)
                {
                    if (!commissions.Any(r => r.InvoiceItemId == invoiceItem.Id))
                        invoiceItem.DriverCommissions = new Collection<InvoiceItem.InvoiceItemDriverCommission>();
                }

            }

            return entries;
        }


        public static Collection<Entry> GetByTruckId(int truckId, DateTime? startDate, DateTime? endDate)
        {
            Collection<Entry> entries = new Collection<Entry>();

            using (SqlDataReader dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                "DispatchEntriesGetByTruckId",
                    new SqlParameter("@DriverId", truckId),
                    new SqlParameter("@StartDate", startDate),
                    new SqlParameter("@StopDate", endDate)))
            {
                while (dr.Read())
                {
                    entries.Add(new Entry(dr));
                }
            }
            //System.Diagnostics.Debug.WriteLine("GetByTruck: " + truckId.ToString() + "," + startDate + "," + endDate + " returned: " + entries.Count);

            return entries;
        }

        public static Collection<Entry> GetUnpaidByCompany(Extric.Towbook.Company.Company company)
        {
            Collection<Entry> entries = new Collection<Entry>();

            using (SqlDataReader dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                "DispatchEntriesGetUnpaidByCompanyId",
                    new SqlParameter("@CompanyId", company.Id)))
            {
                while (dr.Read())
                {
                    entries.Add(new Entry(dr));
                }
            }

            return entries;
        }

        public static bool CheckInvoiceNumberAvailable(string ticket, int companyId)
        {
            using (SqlDataReader dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                "DispatchEntriesGetByInvoiceNumber", ticket, companyId))
            {
                return dr.HasRows;
            }
        }

        public static Collection<Entry> GetAll()
        {
            Collection<Entry> entries = new Collection<Entry>();

            using (SqlDataReader dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                "DispatchEntriesGetAll"))
            {
                while (dr.Read())
                {
                    entries.Add(new Entry(dr));
                }
            }
            return entries;
        }

        private int _truckId;
        private int _driverId;

        public int TruckId
        {
            get { return _truckId; }
            set { SetField(ref _truckId, value, "TruckId"); }
        }

        public int DriverId
        {
            get { return _driverId; }
            set { SetField(ref _driverId, value, "DriverId"); }
        }

        public int ReasonId
        {
            get { return _reasonId; }
            set
            {
                if (value != _reasonId)
                {
                    SetField(ref _reasonId, value, "ReasonId");
                }
            }
        }

        public int InvoiceStatusId
        {
            get { return _invoiceStatusId; }
            set { SetField(ref _invoiceStatusId, value, "InvoiceStatusId"); }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="companyIds"></param>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <param name="accountId"></param>
        /// <param name="driverId"></param>
        /// <param name="truckId"></param>
        /// <param name="dispatcherId"></param>
        /// <param name="filterByInvoiceDateInsteadOfPaymentDate">0: payment date, 1: call createdate, 2: payment create date</param>
        /// <param name="filterVoid"></param>
        /// <param name="includeBillToAccountFakePayments"></param>
        /// <param name="reasonId"></param>
        /// <param name="classId"></param>
        /// <returns></returns>
        public static IncomeData GetOverallIncomeData(int[] companyIds,
            DateTime startDate,
            DateTime endDate,
            int[] accountId = null,
            int? driverId = null,
            int? truckId = null,
            int? dispatcherId = null,
            int filterByInvoiceDateInsteadOfPaymentDate = 0,
            int? filterVoid = 0,
            bool includeBillToAccountFakePayments = true,
            int? reasonId = null,
            int? classId = null)
        {
            var result = new IncomeData();
            var resultSummary = new List<IncomeData.SummaryData>();

            var spName = "ReportsGetIncomingMoneyTotalsFilteredArray2";

            if (!includeBillToAccountFakePayments)
                spName = "ReportsGetIncomingMoneyTotalsFilteredArrayPaymentsOnly2";

            var resultDetails = SqlMapper.QuerySP<IncomeData.DetailData>(spName,
                new
                {
                    CompanyIds = string.Join(",", companyIds),
                    startDate,
                    endDate,
                    accountId = (accountId != null && accountId.Any() ? string.Join(",", accountId) : (string)null),
                    driverId,
                    truckId,
                    dispatcherId,
                    filterByInvoiceDateInsteadOfPaymentDate,
                    filterVoid,
                    reasonId
                }).ToList();

            foreach (var x in resultDetails)
                x.PaymentString = GetPaymentString(x.Type);

            if (classId != null)
                resultDetails = resultDetails.Where(o => o.ClassId == classId.Value).ToList();

            resultSummary.AddRange(resultDetails.Where(o => !o.IsVoid).GroupBy(o => o.Type)
                .Select(rx => new IncomeData.SummaryData()
                {
                    Type = rx.Key,
                    PaymentString = PaymentType.GetById(rx.Key)?.Name ?? rx.Key.ToString(),
                    PaymentCount = rx.Count(),
                    Total = rx.Sum(ro => ro.Amount)
                }));

            result.Summary = resultSummary;
            result.Details = resultDetails
                .OrderBy(o => o.CallNumber)
                .ThenBy(o => o.PaymentDate)
                .ThenBy(o => o.IsVoid).ToList();

            return result;
        }

        public static IncomeData GetOverallIncomeDataForVerificationReport(int[] companyIds,
            DateTime startDate,
            DateTime endDate,
            int? accountId = null,
            int? driverId = null,
            int? truckId = null,
            int? dispatcherId = null,
            int filterByInvoiceDateByPaymentDateByVerifiedDate = 0,
            int? filterVoid = 0,
            int? recordedBy = null,
            int? filterVerified = null)
        {
            IncomeData result = new IncomeData();
            var resultSummary = new List<IncomeData.SummaryData>();
            var resultDetails = new List<IncomeData.DetailData>();

            using (var reader = SqlHelper.ExecuteReader(Core.ConnectionString,
                    "ReportsGetIncomingMoneyTotalsFilteredArray",
                    String.Join(",", companyIds),
                    startDate,
                    endDate,
                    accountId,
                    driverId,
                    truckId,
                    dispatcherId,
                    filterByInvoiceDateByPaymentDateByVerifiedDate,
                    filterVoid,
                    recordedBy,
                    filterVerified))
            {
                while (reader.Read())
                {
                    resultDetails.Add(new IncomeData.DetailData()
                    {
                        Type = reader.GetValue<int>("Type"),
                        PaymentString = GetPaymentString(reader.GetValue<int>("Type")),
                        ReferenceNumber = reader.GetValue<string>("ReferenceNumber"),
                        Amount = reader.GetValue<decimal>("Amount"),
                        DispatchEntryId = reader.GetValue<int>("DispatchEntryId"),
                        CompanyId = reader.GetValue<int>("CompanyId"),
                        CallNumber = reader.GetValue<int>("Callnumber"),
                        AccountPaymentId = reader.GetValue<int>("AccountPaymentId"),
                        PaymentDate = reader.GetValue<DateTime>("PaymentDate"),
                        CreateDate = reader.GetValue<DateTime>("CreateDate"),
                        OwnerUserId = reader.GetValue<int>("OwnerUserId"),
                        ClassId = reader.GetValue<int>("ClassId"),
                        IsVoid = reader.GetValue<bool>("IsVoid"),
                        CompletionTime = reader.GetValue<DateTime>("CompletionTime"),
                        PaymentVerificationId = reader.GetValueOrNull<int>("PaymentVerificationId"),
                        DispatchEntryPaymentId = reader.GetValueOrNull<int>("DispatchEntryPaymentId"),
                        PaymentVerificationUserId = reader.GetValueOrNull<int>("PaymentVerificationUserId"),
                        PaymentVerificationCreateDate = reader.GetValueOrNull<DateTime>("PaymentVerificationCreateDate")
                    });
                }
            }
            resultSummary.AddRange(resultDetails.Where(o => !o.IsVoid).GroupBy(o => o.Type)
                .Select(rx => new IncomeData.SummaryData()
                    {
                        Type = rx.Key,
                        PaymentString = PaymentType.GetById(rx.Key)?.Name ?? rx.Key.ToString(),
                        PaymentCount = rx.Count(),
                        Total = rx.Sum(ro => ro.Amount)
                    }));
            result.Summary = resultSummary;
            result.Details = resultDetails.OrderBy(o => o.CallNumber).ThenBy(o => o.PaymentDate).ThenBy(o => o.IsVoid).ToList();

            return result;
        }

        public static string GetPaymentString(int v)
        {
            var type = PaymentType.GetById(v);
            if (type != null)
                return type.Name;

            return "";
        }

        /// <summary>
        /// Adds the entry to the Queue to be sent to QB for Invoice
        /// </summary>
        private static async Task AddEntryToQueueForAccounting(Entry entry)
        {
            // If the call is completed and QB is connected, add it to the queue
            if (entry.Status.Id == (int)EntryStatus.Completed)
            {
                if (await entry.Company.IsConnectedToAccountingProviderAsync())
                {
                    if (!ProcessQueueHelper.EntryIdExists(entry.Id))
                    {
                        await ProcessQueueHelper.AddEntryToQueueAsync(entry);
                    }
                }
            }
        }

        [ProtoIgnore]
        private Collection<EntryAsset> assets;

        public Collection<EntryAsset> Assets
        {
            get
            {
                if (assets == null)
                {
                    if (_id > 0)
                    {
                        assets = EntryAsset.GetByDispatchEntryId(_id).ToCollection();
                    }
                    else
                    {
                        assets = new Collection<EntryAsset>();
                    }
                }

                return assets;
            }
            set
            {
                assets = value;
            }
        }

        /// <summary>
        /// Returns a read-only list of Drivers that are associated with this call, regardless of asset.
        /// </summary>
        /// <remarks>
        /// If you need to add drivers to the call, do it at the Asset level.
        /// </remarks>
        public List<int> Drivers
        {
            get
            {
                List<int> drivers = new List<int>();

                if (this.DriverId > 0)
                    drivers.Add(this.DriverId);

                foreach (var asset in this.Assets)
                {
                    foreach (var driver in asset.Drivers)
                    {
                        if (driver.DriverId.GetValueOrDefault() != 0 && 
                            !drivers.Contains(driver.DriverId.Value))
                            drivers.Add(driver.DriverId.Value);
                    }
                }

                return drivers;
            }
        }

        /// <summary>
        /// Returns a read-only list of Trucks that are associated with this call, regardless of asset.
        /// </summary>
        /// <remarks>
        /// If you need to add trucks to the call, do it at the Asset level.
        /// </remarks>
        public List<int> Trucks
        {
            get
            {
                List<int> trucks = new List<int>();

                if (this.TruckId > 0)
                    trucks.Add(this.TruckId);

                foreach (var asset in this.Assets)
                {
                    foreach (var driver in asset.Drivers)
                    {
                        if (driver.TruckId != null && !trucks.Contains(driver.TruckId.Value))
                            trucks.Add(driver.TruckId.Value);
                    }
                }

                return trucks;
            }
        }
        [ProtoIgnore]
        public EntryAssetDriver[] DriverTrucks
        {
            get
            {
                var drivers = new List<EntryAssetDriver>();

                foreach (var asset in this.Assets)
                {
                    foreach (var driver in asset.Drivers)
                    {
                        if (driver.DriverId.GetValueOrDefault() != 0)
                            drivers.Add(driver);
                    }
                }
                return drivers.ToArray();
            }
        }

        [ProtoIgnore]
        private Collection<EntryWaypoint> waypoints;

        public Collection<EntryWaypoint> Waypoints
        {
            get
            {
                if (waypoints == null)
                {
                    if (_id > 0)
                    {
                        waypoints = EntryWaypoint.GetByDispatchEntryId(_id).ToCollection();
                    }
                    else
                    {
                        waypoints = new Collection<EntryWaypoint>();
                    }
                }

                return waypoints;
            }
            set
            {
                waypoints = value;
            }
        }

        private Collection<int> tags;

        public Collection<int> Tags
        {
            get
            {
                if (tags == null)
                    tags = EntryTag.GetByDispatchEntryId(this.Id).Select(o => o.Id).ToCollection();

                return tags;
            }
            set
            {
                tags = value;
            }
        }

        public DateTime? LastModifiedTimestamp { get; set; }

        public static async Task<Entry> DuplicateFrom(Entry c, int destinationCompanyId)
        {
            var e = (Entry)c.MemberwiseClone();

            e._id = -1;
            e._callNumber = 0;
            e.CompanyId = destinationCompanyId;
            e.assets = new Collection<EntryAsset>();
            e._invoice = null;

            foreach (var asset in c.Assets)
            {
                var aa = new EntryAsset()
                {
                    DispatchEntryId = e.Id,
                    Year = asset.Year,
                    Make = asset.Make,
                    Model = asset.Model,
                    ColorId = asset.ColorId,
                    LicenseNumber = asset.LicenseNumber,
                    LicenseState = asset.LicenseState,
                    Vin = asset.Vin
                    
                };

                e.Assets.Add(aa);
            }

            await e.Save(true);

            foreach (var invoiceItem in c.InvoiceItems)
            {
                var ii = new InvoiceItem()
                {
                    CustomName = invoiceItem.Name,
                    CustomPrice = invoiceItem.Price,
                    Quantity = invoiceItem.Quantity
                };
                e.Invoice.InvoiceItems.Add(ii);
            }

            await e.Invoice.SaveAsync(null, null);

            foreach (var cont in c.Contacts)
            {
                var cn = new EntryContact() { Name = cont.Name, Notes = cont.Notes, Phone = cont.Phone, DispatchEntryId = e.Id };
                cn.Save();
            }

            if (c.Attributes.ContainsKey(AttributeValue.BUILTIN_DISPATCH_SHARED_DISPATCH_ENTRY_ID))
                c.Attributes.Remove(AttributeValue.BUILTIN_DISPATCH_SHARED_DISPATCH_ENTRY_ID);

            foreach (var values in c.Attributes)
            {
                var av = new AttributeValue() { DispatchEntryId = e.Id, DispatchEntryAttributeId = values.Value.DispatchEntryAttributeId, Value = values.Value.Value };

                // ignore Sticker Number attribute (this is read only and should not be changed)
                if (av.DispatchEntryAttributeId == AttributeValue.BUILTIN_DISPATCH_STICKER_NUMBER)
                    continue;

                if (av.DispatchEntryAttributeId == AttributeValue.BUILTIN_SUBCONTRACTOR_ID ||
                    av.DispatchEntryAttributeId == AttributeValue.BUILTIN_SUBCONTRACTOR_RESPONSE_JSON ||
                    av.DispatchEntryAttributeId == AttributeValue.BUILTIN_AUTOLOCK_EVENT_JSON ||
                    av.DispatchEntryAttributeId == AttributeValue.BUILTIN_INCLUDE_BILLING_NOTES_ON_RECEIPT ||
                    av.DispatchEntryAttributeId == AttributeValue.BUILTIN_DISPATCH_AUDITED)
                    continue;

                await av.SaveAsync();
            }

            var av2 = new AttributeValue() { DispatchEntryId = e.Id, DispatchEntryAttributeId = AttributeValue.BUILTIN_DISPATCH_SHARED_DISPATCH_ENTRY_ID, Value = c.Id.ToString() };
            await av2.SaveAsync();

            return e;
        }
        public override bool Equals(System.Object obj)
        {
            if (obj == null)
                return false;

            Entry hp = obj as Entry;
            if ((System.Object)hp == null)
                return false;

            return (hp.Id == this.Id);
        }

        public bool Equals(Entry l)
        {
            if (l == null)
                return false;

            return (l.Id == this.Id);
        }

        public override int GetHashCode()
        {
            return _id.GetHashCode();
        }

        public decimal LoadedMiles()
        {
            return InvoiceItems.Where(o => ((o.Name != null && o.Name.Contains("loaded miles")) ||
                (o.RateItem != null && o.RateItem.Predefined != null && o.RateItem.Predefined.Id == PredefinedRateItem.BUILTIN_MILEAGE_LOADED)) &&
                o.Quantity > 0 && !(o.CustomName ?? "").Contains("FreeQuantity")).Sum(o => o.Quantity);
        }

        public decimal UnloadedMiles()
        {
            return InvoiceItems.Where(o => ((o.Name != null && o.Name.Contains("unloaded miles")) ||
                (o.RateItem != null && o.RateItem.Predefined != null && o.RateItem.Predefined.Id == PredefinedRateItem.BUILTIN_MILEAGE_UNLOADED)) &&
                o.Quantity > 0 && !(o.CustomName ?? "").Contains("FreeQuantity")).Sum(o => o.Quantity);
        }

        public string CoverageAmount()
        {
            var c = AttributeValue.BUILTIN_MOTORCLUB_COVERAGELIMIT;

            return Attributes.ContainsKey(c) ? Attributes[c].Value : "";
        }

        public string BillingNotes()
        {
            var c = AttributeValue.BUILTIN_BILLING_NOTES;

            return Attributes.ContainsKey(c) ? Attributes[c].Value : "";
        }

        public int? GetAccountManagerUserId()
        {
            var c = AttributeValue.BUILTIN_LINKED_ACCOUNT_MANAGER_USER_ID;
            int userId = 0;

            if (Attributes.ContainsKey(c))
            {
                if (int.TryParse(Attributes[c].Value, out userId) && userId > 1)
                    return userId;
            }

            return (int?)null;
        }


        public static int GetNextYearlySequence(int companyId)
        {
            using (var cnn = Core.GetConnection())
            {
                return SqlMapper.QuerySP<int>("DispatchEntryYearlySequencesGetNextValue",
                    new
                    {
                        CompanyId = companyId
                    }).First();
            }
        }
        
        public static async Task<int> GetNextYearlySequenceAsync(int companyId)
        {
            using (var cnn = Core.GetConnection())
            {
                return (await SqlMapper.QuerySpAsync<int>("DispatchEntryYearlySequencesGetNextValue",
                    new
                    {
                        CompanyId = companyId
                    })).First();
            }
        }

        public struct MaxMinDates
        {
            public DateTime MinDate;
            public DateTime MaxDate;
        }

        /// <summary>
        /// Get the maximum and minimum dates for entries. Empty list defaults to Now for both fields.
        /// </summary>
        /// <param name="entries"></param>
        /// <param name="useCompletionTime">Whether to sort by completion date. When the CompletionTime is missing
        /// it uses the CreateDate.</param>
        /// <returns></returns>
        public static MaxMinDates GetMaxMinDates(List<Entry> entries, bool useCompletionTime = false)
        {
            if (!entries.Any()) return new MaxMinDates { MinDate = DateTime.Now, MaxDate = DateTime.Now };
            // This can be done in a single pass with an aggregator but I think the procedural approach is easier to read
            DateTime minDate = (useCompletionTime && entries[0].CompletionTime != null) ? entries[0].CompletionTime.Value : entries[0].CreateDate;
            DateTime maxDate = minDate;
            foreach (Entry entry in entries)
            {
                if(useCompletionTime && entry.CompletionTime != null) 
                {
                    minDate = minDate < entry.CompletionTime.Value ? minDate : entry.CompletionTime.Value;
                    maxDate = maxDate > entry.CompletionTime.Value ? maxDate : entry.CompletionTime.Value;
                } else {
                    minDate = minDate < entry.CreateDate ? minDate : entry.CreateDate;
                    maxDate = maxDate > entry.CreateDate ? maxDate : entry.CreateDate;
                }
            }
            return new MaxMinDates {  MinDate = minDate, MaxDate = maxDate };
        }
    }

    public class IncomeData
    {
        public List<SummaryData> Summary { get; set; }
        public List<DetailData> Details { get; set; }

        public class SummaryData
        {
            public int Type { get; set; }
            public string PaymentString { get; set; }
            public int PaymentCount { get; set; }
            public decimal Total { get; set; }
        }

        public class DetailData
        {
            public int Type { get; set; }
            public string PaymentString { get; set; }
            public string ReferenceNumber { get; set; }
            public decimal Amount { get; set; }
            public int DispatchEntryId { get; set; }
            public int CompanyId { get; set; }
            public int? AccountPaymentId { get; set; }
            public int CallNumber { get; set; }
            public DateTime? PaymentDate { get; set; }
            public DateTime CreateDate { get; set; }
            public int OwnerUserId { get; set; }
            public int ClassId { get; set; }
            public bool IsVoid { get; set; }
            public DateTime? VoidedDate { get; set; }
            public int? VoidedByUserId { get; set; }

            public DateTime CompletionTime { get; set; }
            public int? PaymentVerificationId { get; set; }
            public int? PaymentVerificationUserId { get; set; }
            public DateTime? PaymentVerificationCreateDate { get; set; }
            public int? DispatchEntryPaymentId { get; set; }
            public bool? Impound { get; set; }
            public bool? PushedToQuickbooks { get; set; }
        }
    }

    class AutoLockEventJson
    {
        public int? Offset { get; set; }
        public bool Applied { get; set; }
        public DateTime? AppliedDate { get; set; }
        
    }
}
