using Extric.Towbook.Utility;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Extric.Towbook.Dispatch
{
    public class EntryFile
    {
        [Key("DispatchEntryFileId")]
        public int Id { get; private set; } = -1;
        public int DispatchEntryId { get; set; }
        public int FileId { get; set; }

        public static EntryFile GetById(int id)
        {
            return SqlMapper.Query<EntryFile>(
                 "SELECT * FROM DispatchEntryFiles WHERE DispatchEntryFileId=@DispatchEntryFileId",
                 new
                 {
                     DispatchEntryFileId = id
                 }).FirstOrDefault();
        }

        public static IEnumerable<EntryFile> GetByDispatchEntryId(int dispatchEntryId)
        {
            return GetByDispatchEntryIds(new int[] { dispatchEntryId });
        }

        public static IEnumerable<EntryFile> GetByDispatchEntryIds(int[] callIds)
        {
            if (callIds == null || !callIds.Any())
                return Array.Empty<EntryFile>();

            return SqlMapper.Query<EntryFile>(
                 "SELECT * FROM DispatchEntryFiles WHERE DispatchEntryId IN @Ids",
                 new
                 {
                     Ids = callIds
                 });
        }

        public static async Task<IEnumerable<EntryFile>> GetByDispatchEntryIdsAsync(int[] callIds)
        {
            if (callIds == null || !callIds.Any())
                return Array.Empty<EntryFile>();

            return await SqlMapper.QueryAsync<EntryFile>(
                 "SELECT * FROM DispatchEntryFiles WHERE DispatchEntryId IN @Ids",
                 new
                 {
                     Ids = callIds
                 });
        }

    }
}
