using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Extric.Towbook.Utility;

namespace Extric.Towbook.Dispatch
{
    /// <summary>
    /// Represents a group of reasons (Roadside, Towing, Private Property, etc)
    /// </summary>
    public class ReasonGroup
    {
        public int Id { get; private set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public DateTime CreateDate { get; set; }

        public int[] Reasons { get; set; }

        public static ReasonGroup GetById(int id)
        {
            return Map(SqlMapper.QuerySP<dynamic>("dbo.DispatchReasonGroupsGetById",
                new { DispatchReasonGroupId = id }).FirstOrDefault());
        }

        private static ReasonGroup Map(dynamic o)
        {
            if (o == null)
                return null;

            return new ReasonGroup()
            {
                Id = o.DispatchReasonGroupId,
                Name = o.Name,
                Description = o.Description,
                CreateDate = o.CreateDate,
                Reasons = (o.Reasons as string ?? "").Split(new char[] {','}, StringSplitOptions.RemoveEmptyEntries).Select(z => Convert.ToInt32(z)).ToArray()
            };
        }

        public static Collection<ReasonGroup> GetAll()
        {
            return SqlMapper.QuerySP<dynamic>("dbo.DispatchReasonGroupsGetAll").Select<dynamic, ReasonGroup>(o => Map(o)).ToCollection();
        }

        public void Save()
        {
            if (Id == 0)
            {
                dynamic d = SqlMapper.QuerySP<dynamic>("dbo.DispatchReasonGroupsInsert",
                    new
                    {
                        @Name = this.Name,
                        @Description = this.Description
                    }).FirstOrDefault();

                Id = Convert.ToInt32(d.Id);
            }
            else
            {
                SqlMapper.ExecuteSP("dbo.DispatchReasonGroupsUpdateById",
                   new
                   {
                       @DispatchReasonGroupId = this.Id,
                       @Name = this.Name,
                       @Description = this.Description
                   });
            }
        }

        public void Delete()
        {
            SqlMapper.ExecuteSP("dbo.DispatchReasonGroupsDeleteById",
                new { @DisclaimerId = this.Id });
        }
    }
}
