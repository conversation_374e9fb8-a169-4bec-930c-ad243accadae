using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Extric.Towbook.Dispatch.CallModels
{
    public class CallTipModel
    {
        public decimal? Total { get; set; }
        public IEnumerable<DriverTipModel> Drivers { get; set; }

        public static CallTipModel Map(DispatchEntryPaymentTip[] tips)
        {
            if (tips == null || tips.Length == 0)
                return null;

            return new CallTipModel()
            {
                Total = tips.Sum(x => x.Amount),
                Drivers = tips
                        .Where(w => w.DriverId > 0)
                        .GroupBy(b => b.DriverId)
                        .Select(s =>
                            new DriverTipModel()
                            {
                                DriverId = s.Key.GetValueOrDefault(),
                                Total = tips.Where(w => w.DriverId == s.Key).Sum(x => x.Amount)
                            })
            };
        }
    }

    public class DriverTipModel
    {
        public int DriverId { get; set; }
        public decimal? Total { get; set; }
        public IEnumerable<PaymentTipModel> Items { get; set; }
    }


    public class PaymentTipModel
    {
        public decimal Amount { get; set; }
        public DateTime CreateDate { get; set; }

        public static PaymentTipModel Map(DispatchEntryPaymentTip o)
        {
            if (o == null)
                return null;

            return new PaymentTipModel()
            {
                Amount = o.Amount,
                CreateDate = o.CreateDate
            };
        }
    }
}
