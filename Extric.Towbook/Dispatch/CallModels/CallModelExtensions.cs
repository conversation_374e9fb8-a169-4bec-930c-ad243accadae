using Extric.Towbook.Company.Accounting;
using Extric.Towbook.Generated;
using Extric.Towbook.Utility;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using static Extric.Towbook.Invoice;

namespace Extric.Towbook.Dispatch.CallModels
{
    public static class InternalCallModelExtensions
    {

        public static async Task<Entry> MapBackAsync(this CosmosCallModel cosmosCall)
        {
            var reverse = new Entry();

            reverse.CompanyId = cosmosCall.CompanyId;
            reverse.CallNumber = cosmosCall.CallNumber;
            reverse.PurchaseOrderNumber = cosmosCall.PurchaseOrderNumber;
            reverse.InvoiceNumber = cosmosCall.InvoiceNumber;
            reverse.Status = await Status.GetByIdAsync(cosmosCall.Status.Id);
            reverse.TowSource = cosmosCall.TowSource;
            reverse.TowDestination = cosmosCall.TowDestination;

            if (cosmosCall.DeletionDetails != null)
            {
                reverse.IsDeleted = true;
                reverse.DeletedIp = cosmosCall.DeletionDetails.IpAddress;
                reverse.DeletedUserId = cosmosCall.DeletionDetails.UserId;
                reverse.DeletedDate = cosmosCall.DeletionDetails.Date;
            }

            if (cosmosCall.Priority != null)
                reverse.Priority = (Entry.EntryPriority)cosmosCall.Priority;

            reverse.Assets = cosmosCall.Assets.Select(o =>
                o.Translate(new EntryAsset()
                {
                    Id = o.Id,
                    DispatchEntryId = cosmosCall.Id
                })).ToCollection();

            reverse.DriverId = cosmosCall.Assets.FirstOrDefault()?.Driver?.Id ?? 0;
            reverse.TruckId = cosmosCall.Assets.FirstOrDefault()?.Truck?.Id ?? 0;
            reverse.Year = cosmosCall.Assets.FirstOrDefault()?.Year ?? 0;
            reverse.BodyType = await Vehicle.BodyType.GetByIdAsync(reverse.Assets.FirstOrDefault()?.BodyTypeId ?? 0);
            reverse.VIN = cosmosCall.Assets.FirstOrDefault()?.Vin ?? "";
            reverse.LicenseNumber = cosmosCall.Assets.FirstOrDefault()?.LicenseNumber ?? "";
            reverse.LicenseState = cosmosCall.Assets.FirstOrDefault()?.LicenseState ?? "";
            reverse.Odometer = cosmosCall.Assets.FirstOrDefault()?.Odometer ?? 0;

            if (reverse.LicenseNumber == null)
                reverse.LicenseNumber = "";

            if (reverse.LicenseState == null)
                reverse.LicenseState = "";

            reverse.Waypoints = (await Task.WhenAll(cosmosCall.Waypoints.Select(async o => await CallWaypointModel.Map(o, new EntryWaypoint() { Id = o.Id }, true)))).ToCollection();
            reverse.Contacts = cosmosCall.Contacts.Select(m => CallContactModel.Map(m, new EntryContact() { Id = m.Id.Value })).ToCollection();
            reverse.OwnerUserId = cosmosCall.Owner?.Id ?? 0;
            reverse.ReasonId = cosmosCall.Reason?.Id ?? 0;
            reverse.AccountId = cosmosCall.Account?.Id ?? 0;
            reverse.Notes = cosmosCall.Notes;
            reverse.Version = (int)cosmosCall.Version;


            reverse.InvoiceItems = (await Task.WhenAll(cosmosCall.InvoiceItems.Select(async o => await CallInvoiceItemModel.MapAsync(o, new InvoiceItem() { Id = o.Id.Value })))).ToCollection();

            reverse.InvoiceItems = reverse.InvoiceItems.Where(o => o.RelatedInvoiceItemId == null ||
                reverse.InvoiceItems.Any(ro => ro.Id == o.RelatedInvoiceItemId)).ToCollection();

            reverse.Attributes = new Dictionary<int, AttributeValue>();

            foreach (var o in cosmosCall.Attributes)
            {
                reverse.SetAttribute(o.AttributeId, o.Value, o.Id.Value, cosmosCall.Id);
            }

            foreach (var item in reverse.Attributes.Values)
            {
                item.MarkAsClean();
            }

            reverse.Impound = cosmosCall.Impound.Value;
            reverse.InvoiceStatusId = cosmosCall.InvoiceStatusId.GetValueOrDefault();
            reverse.Invoice.CompanyId = cosmosCall.CompanyId;
            reverse.Invoice.AccountId = cosmosCall.BillToAccountId;
            reverse.Invoice.Tax = decimal.Round(cosmosCall.InvoiceTax, 2, MidpointRounding.AwayFromZero);
            reverse.Invoice.Subtotal = cosmosCall.InvoiceSubtotal;
            reverse.Invoice.IsTaxExempt = cosmosCall.InvoiceTaxExempt.GetValueOrDefault();

            if (cosmosCall.Payments != null)
                reverse.Invoice.PaymentsTotal = cosmosCall.Payments.Sum(o => o.Amount);
            else
                reverse.Invoice.PaymentsTotal = cosmosCall.PaymentsApplied;

            reverse.Insights = cosmosCall.Insights;

            reverse.CancellationReason = cosmosCall.CancellationReason;
            reverse.CreateDate = cosmosCall.CreateDate.GetValueOrDefault();
            reverse.DispatchTime = cosmosCall.DispatchTime.GetValueOrDefault();
            reverse.EnrouteTime = cosmosCall.EnrouteTime.GetValueOrDefault();
            reverse.ArrivalETA = cosmosCall.ArrivalETA.GetValueOrDefault();
            reverse.ArrivalTime = cosmosCall.ArrivalTime.GetValueOrDefault();
            reverse.TowTime = cosmosCall.TowTime.GetValueOrDefault();
            reverse.DestinationArrivalTime = cosmosCall.DestinationArrivalTime.GetValueOrDefault();
            reverse.CompletionTime = cosmosCall.CompletionTime.GetValueOrDefault();
            reverse.Invoice.SetPayments(cosmosCall.Payments.Select(o => PaymentModel.Map(o)).ToCollection());

            if (cosmosCall.Payments.Any())
                reverse.PaymentsApplied = cosmosCall.Payments.Where(r => !r.IsVoid).Sum(o => o.Amount);
            else
                reverse.PaymentsApplied = cosmosCall.PaymentsApplied;

            reverse.MarkAsClean();
            reverse.Id = cosmosCall.Id;
            reverse.InternalBlockWrites = true;
            reverse.Invoice.InternalBlockWrites = true;
            reverse.Invoice.DispatchEntry = reverse;
            reverse.Invoice.Id = cosmosCall.InvoiceItems.Where(r => r.InvoiceId > 0).FirstOrDefault()?.InvoiceId ?? 0;
            reverse.Invoice.MarkAsClean();

            reverse.Invoice.ClassBalances = cosmosCall.BalanceByClass.Select(r => new ClassBalanceSummary(r.Id, r.Total, r.Payments, r.Total));

            if (reverse.Invoice.InvoiceItems.All(o => o.ClassId == 0))
            {
                var none = reverse.Invoice.ClassBalances.FirstOrDefault(o => o.Id == 0);

                if (none != null)
                {
                    foreach (var tr in reverse.Invoice.ClassBalances.Where(o => o.Id > 0))
                    {
                        none.Payments += tr.Payments;
                    }

                    reverse.Invoice.ClassBalances = new ClassBalanceSummary[] { none };
                }
            }

            if (cosmosCall.ImpoundDetails != null)
            {
                reverse.Invoice.Impound = new Impounds.Impound()
                {
                    InternalBlockWrites = true,
                    Invoice = reverse.Invoice,
                    DispatchEntry = reverse,
                    ImpoundDate = cosmosCall.ImpoundDetails.ImpoundDate,
                    ReleaseDate = cosmosCall.ImpoundDetails.ReleaseDate,
                    Auction = cosmosCall.ImpoundDetails.Auction.GetValueOrDefault(),
                    Hold = cosmosCall.ImpoundDetails.PoliceHold.GetValueOrDefault(),
                    Account = reverse.Account,
                    Company = reverse.Company,
                    Lot = await Impounds.Lot.GetByIdAsync(cosmosCall.CompanyId, cosmosCall.ImpoundLotId.GetValueOrDefault())
                };
                reverse.Released = cosmosCall.ImpoundDetails.ReleaseDate != null;
            }
            reverse.Invoice.SetPayments(cosmosCall.Payments.Select(o => new InvoicePayment()
            {
                InvoiceId = o.InvoiceId,
                Amount = o.Amount,
                PaymentType = PaymentType.GetById(o.Type),
                IsVoid = o.IsVoid,
                ClassId = o.ClassId,
                OwnerUserId = o.UserId,

                PaymentVerificationId = o.PaymentVerificationId,
                CreateDate = o.CreateDate,

                ReferenceNumber = o.ReferenceNumber,
                PaymentDate = o.PaymentDate,
                Id = o.Id,
                VoidedDate = o.VoidedDate,
                VoidedByUserId = o.VoidedByUserId

            }).ToCollection());

            var dtDateTime = new DateTime(1970, 1, 1, 0, 0, 0, 0, System.DateTimeKind.Utc);
            reverse.LastModifiedTimestamp = dtDateTime.AddSeconds(cosmosCall._ts.Value).ToLocalTime();

            return reverse;
        }

        internal static EntryAsset Translate(this CallAssetModel thisAsset, EntryAsset original = null)
        {
            if (original == null)
                original = new EntryAsset();

            if (thisAsset.Id > 0 && original.Id != thisAsset.Id)
                throw new TowbookException("AssetId doesn't match; cannot process.");

            original.Id = thisAsset.Id;

            if (thisAsset.Drivers != null)
            {
                var drivers = thisAsset.Drivers.Translate(original);

                original.Drivers = drivers;
                original.MarkAsDirty();
            }

            if (thisAsset.Drivable != null)
            {
                original.Drivable = (thisAsset.Drivable == true ? Drivable.Drivable :
                                     thisAsset.Drivable == false ? Drivable.NotDrivable :
                                     Drivable.Unknown);
            }
            else
            {
                try
                {
                    original.Drivable = (Drivable)thisAsset.DrivableId;
                } 
                catch
                {
                    original.Drivable = Drivable.Unknown;
                }
            }

            if (thisAsset.Keys != null)
                original.Keys = thisAsset.Keys;

            if (thisAsset.KeysLocation != null)
                original.KeysLocation = thisAsset.KeysLocation;

            if (thisAsset.BodyType != null)
                original.BodyTypeId = thisAsset.BodyType?.Id ?? 0;

            if (original.BodyTypeId == 0)
            {
                // if a body type isn't set, default it to 1. 
                // due to bug in android app.
                original.BodyTypeId = 1;
            }

            if (thisAsset.Color != null)
                original.ColorId = thisAsset.Color?.Id ?? 0;

            if (thisAsset.Make != null)
                original.Make = thisAsset.Make;

            if (thisAsset.Model != null)
                original.Model = thisAsset.Model;

            if (thisAsset.Odometer != null)
                original.Odometer = thisAsset.Odometer.GetValueOrDefault();

            if (thisAsset.Year != null)
                original.Year = thisAsset.Year.GetValueOrDefault();

            if (thisAsset.LicenseYear != null)
                original.LicenseYear = thisAsset.LicenseYear != null ? Convert.ToInt32(thisAsset.LicenseYear) : 0;

            if (thisAsset.LicenseNumber != null)
                original.LicenseNumber = thisAsset.LicenseNumber;

            if (thisAsset.LicenseState != null)
                original.LicenseState = thisAsset.LicenseState;

            if (thisAsset.Vin != null)
                original.Vin = thisAsset.Vin;

            if (thisAsset.DriveType != null)
                original.DriveType = thisAsset.DriveType;

            if (thisAsset.Notes != null)
                original.Notes = thisAsset.Notes;

            if (thisAsset.UnitNumber != null)
                original.UnitNumber = thisAsset.UnitNumber;

            if (thisAsset.AirbagStatus.HasValue)
            {
                switch (thisAsset.AirbagStatus)
                {
                    case 1:
                        original.AirbagStatus = AirbagStatus.NotDeployed;
                        break;

                    case 2:
                        original.AirbagStatus = AirbagStatus.Deployed;
                        break;

                    default:
                        original.AirbagStatus = AirbagStatus.Unknown;
                        break;
                }
            }

            return original;
        }

        internal static Collection<Dispatch.EntryAssetDriver> Translate(this DriverTruckPairModel[] input, EntryAsset asset)
        {
            // TODO: convert to extension method for API only.

            var retval = new Collection<Dispatch.EntryAssetDriver>();

            foreach (var ad in input)
            {
                if (ad.Driver == null && ad.Truck == null && asset != null)
                {
                    // if the id is specified, we need to leave the original as is
                    if (ad.Id > 0)
                    {
                        var o = asset.Drivers?.FirstOrDefault(w => w.Id == ad.Id);
                        if (o != null)
                            retval.Add(o);
                    }
                    else
                    {
                        continue;
                    }
                }

                var n = new Dispatch.EntryAssetDriver()
                {
                    Id = ad.Id.GetValueOrDefault(),
                    DriverId = ad.Driver?.Id,
                    TruckId = ad.Truck?.Id,
                    CurrentWaypointId = ad.Driver?.CurrentWaypointId
                };


                // If the model only contains 1 item, and it's missing it's ID, automatically assume it's the first driver.

                if (asset != null && input.Length == 1 && asset.Drivers != null && asset.Drivers.Count == 1)
                {
                    if (n.Id == 0)
                        n.Id = asset.Drivers[0].Id;

                    var x = asset.Drivers.FirstOrDefault(o => o.Id == n.Id);

                    if (x != null)
                    {
                        if (n.TruckId == null && x.TruckId.GetValueOrDefault() > 0)
                        {
                            n.TruckId = x.TruckId;
                        }

                        if (n.CurrentWaypointId == null && x.CurrentWaypointId.GetValueOrDefault() > 0)
                        {
                            n.CurrentWaypointId = x.CurrentWaypointId;
                        }
                    }
                }

                retval.Add(n);
            }

            return retval;
        }

        
    }

    public static class PublicCallModelExtensions
    {
        
        public static bool IsMultiAddressCall(this CallModel thisModel)
        {
            if (thisModel == null || thisModel.Waypoints == null || thisModel.Waypoints.Count() <= 2)
                return false;

            if (thisModel.Attributes?.FirstOrDefault(a => a.AttributeId == AttributeValue.BUILTIN_TOWOUT_CALL)?.Value == "1")
                return false;

            if (!string.IsNullOrWhiteSpace(thisModel.Attributes?.FirstOrDefault(a => a.AttributeId == AttributeValue.BUILTIN_ALLSTATE_JOB_INFO_PRIMARY_TASK)?.Value ?? ""))
                return true;

            if (thisModel.Waypoints.Where(w => w.Title != "Start").Count() > 2)
                return true;

            return false;
        }

        public static bool IsMultiAddressCall(this Entry e)
        {
            if (e == null || e.Waypoints == null || e.Waypoints.Count() <= 2)
                return false;

            if (e.Attributes != null)
            {
                if (e.Attributes.ContainsKey(AttributeValue.BUILTIN_TOWOUT_CALL) && e.Attributes[AttributeValue.BUILTIN_TOWOUT_CALL].Value == "1")
                    return false;

                if (e.Attributes.ContainsKey(AttributeValue.BUILTIN_ALLSTATE_JOB_INFO_PRIMARY_TASK) && e.Attributes[AttributeValue.BUILTIN_ALLSTATE_JOB_INFO_PRIMARY_TASK].Value != "")
                    return true;
            }

            if (e.Waypoints.Where(w => w.Title != "Start").Count() > 2)
                return true;

            return false;
        }
    }
}
