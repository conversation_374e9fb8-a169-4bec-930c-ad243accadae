using System;
using System.Threading.Tasks;

namespace Extric.Towbook.Dispatch.CallModels
{
    public sealed class NoteModel
    {
        public int Id { get; set; }
        public string Content { get; set; }
        public DateTime CreateDate { get; set; }
        public int OwnerUserId { get; set; }
        public string OwnerName { get; set; }
        public bool ShowDelete { get; set; }

        public static async Task<NoteModel> MapAsync(EntryNote n)
        {
            var m = new NoteModel();

            m.Id = n.Id;
            m.OwnerUserId = n.OwnerUserId;
            m.OwnerName = (await User.GetByIdAsync(m.OwnerUserId))?.FullName ?? "Unknown";
            m.CreateDate = n.CreateDate;
            m.Content = n.Content;

            return m;
        }

        public static EntryNote Map(NoteModel model)
        {
            var n = new EntryNote();

            n.OwnerUserId = model.OwnerUserId;
            n.CreateDate = model.CreateDate;

            if (!string.IsNullOrWhiteSpace(model.Content))
                n.Content = model.Content;

            return n;
        }
    }
}
