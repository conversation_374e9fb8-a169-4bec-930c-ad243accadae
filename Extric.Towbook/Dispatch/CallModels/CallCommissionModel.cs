using System.Collections.Generic;

namespace Extric.Towbook.Dispatch.CallModels
{
    /// <summary>
    /// Represents the commission values for a call
    /// </summary>
    public class CallCommissionModel
    {
        public int? CallId { get; set; }

        /// <summary>
        /// A % or % amount representing how much of the ticket to pay commission on. 
        /// If it is null, then it is assumed to be 100%/default. 100% and null should be treated as the same meaning from the client. 
        /// If the client needs to specify 100% though, it needs to send 100%. A null value will be treated by the server as if the value wasn't specified.
        /// </summary>
        public string TotalTicketValue { get; set; }

        /// <summary>
        /// Array of Drivers with their respective commission amounts/shares.
        /// </summary>
        public IEnumerable<CallCommissionDriverModel> Drivers { get; set; }

        public CallCommissionModel()
        {

        }
    }
}