using System;
using Extric.Towbook.SquareIntegration;

namespace Extric.Towbook.Dispatch.CallModels
{
    public class PaymentModel
    {
        public int Id { get; set; }
        public int InvoiceId { get; set; }
        public decimal Amount { get; set; }
        public int Type { get; set; }

        public int ClassId { get; set; }
        public string ReferenceNumber { get; set; }
        public DateTime CreateDate { get; set; }
        public DateTime? PaymentDate { get; set; }
        public int UserId { get; set; }
        public int? PaymentVerificationId { get; set; }
        public bool IsVoid { get; set; }
        public int? VoidedByUserId { get; set; }
        public DateTime? VoidedDate { get; set; }

        public static PaymentModel Map(InvoicePayment o)
        {
            return new PaymentModel()
            {
                Id = o.Id,
                InvoiceId = o.InvoiceId,
                Amount = o.Amount,
                Type = o.PaymentType,
                ClassId = o.ClassId,
                ReferenceNumber = o.ReferenceId,
                CreateDate = o.CreateDate,
                PaymentDate = o.PaymentDate,
                UserId = o.OwnerUserId,
                PaymentVerificationId = o.PaymentVerificationId,
                IsVoid = o.IsVoid,
                VoidedByUserId = o.VoidedByUserId,
                VoidedDate = o.VoidedDate
            };
        }

        public static InvoicePayment Map(PaymentModel o)
        {
            return new InvoicePayment()
            {
                Id = o.Id,
                InvoiceId = o.InvoiceId,
                Amount = o.Amount,
                PaymentType = PaymentType.GetById(o.Type),
                ClassId = o.ClassId,
                ReferenceNumber = o.ReferenceNumber,
                CreateDate = o.CreateDate,
                PaymentDate = o.PaymentDate,
                OwnerUserId = o.UserId,
                PaymentVerificationId = o.PaymentVerificationId,
                IsVoid = o.IsVoid,
                VoidedByUserId = o.VoidedByUserId,
                VoidedDate = o.VoidedDate
            };
        }
    }
}
