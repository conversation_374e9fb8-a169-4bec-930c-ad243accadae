using System;
using System.Linq;
using System.Threading.Tasks;
using Extric.Towbook.Dispatch;

namespace Extric.Towbook.Dispatch.CallModels
{
    /// <summary>
    /// Represents a Waypoint related to a call. Pickup Location, Destination, Service Location, Rental Car, etc.
    /// </summary>
    public class CallWaypointModel : IEntryWaypoint
    {
        public int Id { get; set; }
        public int Position { get; set; }
        public string Title { get; set; }
        public string Address { get; set; }


        // client should never send these. these will be calculated based on zip code or google maps. 
        public string City { get; set; }
        public string State { get; set; }
        public string Zip { get; set; }

        public decimal? Latitude { get; set; }
        public decimal? Longitude { get; set; }

        public DateTime? EnrouteTime { get; set; }
        public DateTime? ArrivalTime { get; set; }

        public string Notes { get; set; }

        public bool HasToll { get; set; }

        public static CallWaypointModel Map(EntryWaypoint w)
        {
            var wp = new CallWaypointModel()
            {
                Id = w.Id,
                Position = w.Position,
                Title = w.Title ?? "",
                Address = w.Address,
                Latitude = w.Latitude,
                Longitude = w.Longitude,
                ArrivalTime = w.ArrivalTime,
                EnrouteTime = w.EnrouteTime,
                Notes = w.Notes,
                HasToll = w.HasToll
            };

            wp.Zip = Core.GetZipFromAddress(wp.Address);
            //wp.City = LocationUtility.GetCityByZip(wp.Zip);
            //wp.State = LocationUtility.GetStateByZip(wp.State);

            return wp;
        }
        public static async Task<EntryWaypoint> Map(CallWaypointModel w, EntryWaypoint p) => await Map(w, p, false);

        public static async Task<EntryWaypoint> Map(CallWaypointModel w, EntryWaypoint p, bool skipGeocode)
        {
            if (p == null)
                p = new EntryWaypoint();

            if (w.Position != 0)
                p.Position = w.Position;

            if (w.Title != null)
                p.Title = w.Title;

            if (w.Latitude != null) { 
                if(p.ChangedFields == null || !p.ChangedFields.Any(a => a.Field.Contains("Latitude")))
                    p.Latitude = w.Latitude.GetValueOrDefault();
            }

            if (w.Longitude != null)
            {
                if (p.ChangedFields == null || !p.ChangedFields.Any(a => a.Field.Contains("Longitude")))
                    p.Longitude = w.Longitude.GetValueOrDefault();
            }

            if (w.Address != null)
                p.Address = w.Address;
            if (!skipGeocode)
            {
                if (p.ChangedFields.Any(r => r.Field == "Address") &&
                    !p.ChangedFields.Any(r => r.Field == "Latitude" || r.Field == "Longitude"))
                {
                    // clear lat/long if user sends a new address but doesn't pass a new lat/long.
                    p.Latitude = 0;
                    p.Longitude = 0;

                    var geo = await Utility.GeocodeHelper.Geocode(p.Address);

                    if (geo != null)
                    {
                        p.Latitude = geo.Latitude;
                        p.Longitude = geo.Longitude;
                    }
                }
            }

            if (w.Notes != null)
                p.Notes = w.Notes;

            if (w.EnrouteTime != null)
                p.EnrouteTime = w.EnrouteTime;

            if (w.ArrivalTime != null)
                p.ArrivalTime = w.ArrivalTime;

            p.HasToll = w.HasToll;

            return p;
        }
    }
}