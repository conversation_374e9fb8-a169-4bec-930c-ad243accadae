using System.Collections.Generic;
using System.Linq;

namespace Extric.Towbook.Dispatch.CallModels
{
    public class CallAttributeValueModel
    {
        public int? Id { get; set; }
        public int AttributeId { get; set; }
        public string Value { get; set; }

        public CallAttributeValueModel() { }
        public CallAttributeValueModel(int id, int attributeId, string value)
        {
            Id = id;
            AttributeId = attributeId;
            Value = value;
        }

        public static CallAttributeValueModel[] Map(IEnumerable<AttributeValue> list)
        {
            // don't return attributes with an empty string - we dont allow empty attribute values in our database
            // so they shouldn't be mapped to the client either.
            return list.Where(o => !string.IsNullOrWhiteSpace(o.Value))
                .Select(o => new CallAttributeValueModel()
                {
                    Value = o.Value,
                    AttributeId = o.DispatchEntryAttributeId,
                    Id = o.Id
                }).OrderBy(o => o.Id).ToArray();
        }

        public static AttributeValue Map(CallAttributeValueModel o)
        {
            return new AttributeValue()
            {
                Value = o.Value,
                DispatchEntryAttributeId = o.AttributeId,
                Id = o.Id.GetValueOrDefault()
            };
        }
    }
}
