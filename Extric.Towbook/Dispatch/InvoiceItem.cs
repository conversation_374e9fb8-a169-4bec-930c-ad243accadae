using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Data.SqlClient;
using Extric.Towbook.Accounts;
using ProtoBuf;
using Glav.CacheAdapter.Core.DependencyInjection;
using System.Linq;
using Newtonsoft.Json;
using Extric.Towbook.ActivityLogging;
using Extric.Towbook.Utility;
using Extric.Towbook.Integration;
using System.Threading.Tasks;

namespace Extric.Towbook.Dispatch
{
    [ProtoContract]
    [Serializable]
    public partial class InvoiceItem : TrackableObject
    {
        [ProtoMember(1)]
        private int _id;

        [ProtoMember(2)]
        private string _customName;

        [ProtoMember(3)]
        private decimal? _customPrice;

        [ProtoMember(4)]
        private decimal _quantity;

        [ProtoMember(5)]
        private bool? _taxable;

        [ProtoMember(6)]
        private int _invoiceId;

        [ProtoMember(7)]
        private int _rateItemId;

        [ProtoMember(8)]
        private int? _assetId;

        [ProtoMember(9)]
        private bool _hidden;

        private Entry _entry;
        private Invoice _invoice;

        [ProtoMember(10)]
        private int[] _drivers;

        [ProtoMember(11)]
        private int? _relatedInvoiceItemId;

        [ProtoMember(12)]
        private InvoiceItemLock _locked;

        [ProtoMember(13)]
        private int _classId;
        [ProtoMember(14)]
        private string _notes;

        public enum InvoiceItemLock : int
        {
            Unlocked = 0,
            LockedByUser = 1,
        }

        public bool IsChanged
        {
            get { return this.IsDirty; }
        }

        public override string ToString()
        {
            return Name + "; " + (Taxable ? "TAXABLE; " : "NOTAX") + Quantity + " @ " + Price.ToString("C") + "=" + Total.ToString("C");
        }

        private Collection<InvoiceItemTax> _taxRates;

        /// <summary>
        /// set the internal changed flag to true to allow a Save to occur even if a property hasn't changed that sets the _changed flag. 
        /// 
        /// </summary>
        public void MarkChanged()
        {
            this.MarkAsDirty();
        }

        public InvoiceItem()
        {
            _id = -1;
            this.MarkAsDirty();
        }

        [Obsolete("Prefer using GetById method instead")]
        public InvoiceItem(int id)
        {
            using (SqlDataReader dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                "InvoiceItemsGetById",
                new SqlParameter("@InvoiceItemId", id)))
            {
                if (dr.Read())
                {
                    InitializeFromDataReader(dr);
                }
                else
                {
                    throw new Extric.Towbook.TowbookException(String.Format("Invoice Item {0} doesn't exist!", id));
                }
            }
        }

        internal InvoiceItem(SqlDataReader reader)
        {
            InitializeFromDataReader(reader);
        }

        internal InvoiceItem(SqlDataReader reader, Dispatch.Entry entry)
        {
            _entry = entry;
            InitializeFromDataReader(reader);
        }

        internal static InvoiceItem Map(dynamic row)
        {
            var n = new InvoiceItem();
            n._id = row.InvoiceItemId;
            n._invoiceId = row.InvoiceId;
            n._customName = row.CustomName;
            n._customPrice = row.CustomPrice;
            n._quantity = row.Quantity;
            n._rateItemId = row.RateItemId;
            n._assetId = row.AssetId;
            n._classId = row.ClassId;
            if (row.Notes != null)
                n._notes = row.Notes;

            if (row.Taxable != null)
                n._taxable = row.Taxable;

            n._hidden = row.Hidden;

            n._relatedInvoiceItemId = row.RelatedInvoiceItemId;
            n._locked = row.Locked;
            n._notes = row.Notes;

            return n;
        }

        private void InitializeFromDataReader(SqlDataReader reader)
        {
            _id = Convert.ToInt32(reader["InvoiceItemId"]);
            _invoiceId = reader.GetValue<int>("InvoiceId");
            _customName = reader.GetValue<string>("CustomName");
            _customPrice = reader.GetValue<decimal>("CustomPrice");
            _quantity = reader.GetValue<decimal>("Quantity");
            _rateItemId = reader.GetValue<int>("RateItemId");
            _assetId = reader.GetValueOrNull<int>("AssetId");
            _classId = reader.GetValue<int>("ClassId");

            if (reader["Taxable"] != DBNull.Value)
                _taxable = reader.GetValue<bool>("Taxable");
            else
            {
                _taxable = null;
                Console.WriteLine("wasted query");
            }

            _hidden = reader.GetValue<bool>("Hidden");

            
            if (_entry != null)
            {
                if (_entry.Account != null && RateItem != null)
                {
                    IRateItem x2 = Accounts.RateItem.GetByRateItem(_entry.Account, RateItem);

                    if (x2 != null)
                        RateItem = x2;
                }
            }

            if (RateItem != null && this._taxable == null)
            {
                if (TaxRate.GetByCompany(Company.Company.GetById(RateItem.Companies.First())).Any())
                {
                    Taxable = RateItem.Taxable;
                }
            }
            
            _relatedInvoiceItemId = reader.GetValueOrNull<int>("RelatedInvoiceItemId");
            if (reader["Locked"] != DBNull.Value)
                _locked = (InvoiceItemLock)reader.GetValue<int>("Locked");

            _notes = reader.GetValue<string>("Notes");
        }

        /// <summary>
		/// Saves the InvoiceItem object to the data store.
		/// </summary>
		internal void Save(SqlConnection connection)
        {
            if (connection == null)
                throw new Exception("SqlTransaction must be passed to save InvoiceItem.");

            if (InvoiceId == 0)
                throw new Extric.Towbook.TowbookException("InvoiceId is less than 1.");

            if (_id == 0 || Deleted)
                return;

            if (!IsChanged && _quantity > 0)
                return; // no changes to save!

            if (this.RateItem != null && this.RateItem.Predefined != null)
            {
                // enforce predefined items cannot have a custom name.
                if (this.CustomName != null && this.RelatedInvoiceItemId == null)
                    this.CustomName = null;
            }

            if (this.RateItem.IsTieredStorageItem())
                this.CustomPrice = 0; // force price to 0 for tiered storage root item. 

            if (_id == -1)
            {
                if (_quantity == 0 && Price == 0)
                {
                    if (!RateItem.IsStorageItem())
                        return; // don't save it if quantity is 0.
                }

                // don't save items with a quantity of 0 unless they are storage items or time based rate item
                if (_quantity == 0 && 
                    !RateItem.IsStorageItem() && 
                    !RateItem.IsTieredStorageItem() &&
                    !RateItem.IsTimeBasedItem())
                    return;

                DbInsert(connection);
            }
            else
            {
                if (_quantity == 0 && Price == 0 && !RateItem.IsStorageItem())
                {
                    this.Delete(connection);
                }
                else
                {
                    if (IsChanged)
                        DbUpdate(connection);
                }
            }

            if (_id > 0 && appliedPricingRuleId > 0 && !Deleted)
            {
                var iira = new InvoiceItemRuleApplied(appliedPricingRuleId, 0, _id);
                iira.Save();
            }

            // make sure we set the item back to clean so we dont save it unneccesarily again.
            this.MarkAsClean();
        }

        public static void CacheClearListByInvoiceId(int id)
        {
            AppServices.Cache.InvalidateCacheItem("inv_items:" + id);
        }

        public static void CacheClearByInvoiceItemId(int id)
        {
            AppServices.Cache.InvalidateCacheItem("inv_item:" + id);
        }

        internal bool Deleted { get; private set; }
        internal void Delete(SqlConnection connection)
        {
            if (this._id < 1 || Deleted)
                return;

            SqlHelper.ExecuteNonQuery(connection, "InvoiceItemsDeleteById",
                new SqlParameter("@InvoiceItemId", _id));

            Deleted = true;
        }

        internal async Task DeleteAsync(SqlConnection connection)
        {
            if (this._id < 1 || Deleted)
                return;

            await SqlHelper.ExecuteNonQueryAsync(connection, System.Data.CommandType.StoredProcedure, 
                "InvoiceItemsDeleteById",
                new SqlParameter("@InvoiceItemId", _id));

            Deleted = true;
        }

        private void DbInsert(SqlConnection connection = null)
        {
            Nullable<int> rateId = (_rateItemId > 0 ? (int?)_rateItemId : null);
            if (connection == null)
            {
                _id = Convert.ToInt32(SqlHelper.ExecuteScalar(Core.ConnectionString,
                    "InvoiceItemsInsert",
                    new SqlParameter("@InvoiceId", InvoiceId),
                    new SqlParameter("@RateItemId", rateId),
                    new SqlParameter("@CustomName", _customName),
                    new SqlParameter("@CustomPrice", _customPrice),
                    new SqlParameter("@Quantity", _quantity),
                    new SqlParameter("@AssetId", _assetId),
                    new SqlParameter("@Taxable", Taxable),
                    new SqlParameter("@Hidden", _hidden),
                    new SqlParameter("@Notes", _notes)));
            }
            else
            {
                _id = Convert.ToInt32(SqlHelper.ExecuteScalar(connection,
                    "InvoiceItemsInsertV2",
                    new SqlParameter("@InvoiceId", InvoiceId),
                    new SqlParameter("@RateItemId", rateId),
                    new SqlParameter("@CustomName", _customName),
                    new SqlParameter("@CustomPrice", _customPrice),
                    new SqlParameter("@Quantity", _quantity),
                    new SqlParameter("@AssetId", _assetId),
                    new SqlParameter("@Taxable", Taxable),
                    new SqlParameter("@Hidden", _hidden),
                    new SqlParameter("@RelatedInvoiceItemId", _relatedInvoiceItemId),
                    new SqlParameter("@Locked", _locked),
                    new SqlParameter("@ClassId", _classId),
                    new SqlParameter("@Notes", _notes)
                    ));
            }
        }

        private void DbUpdate(SqlConnection connection = null)
        {
            Nullable<int> rateId = (_rateItemId > 0 ? (int?)_rateItemId : null);

            if (connection == null)
            {
                SqlHelper.ExecuteNonQuery(Core.ConnectionString,
                    "InvoiceItemsUpdateById",
                    new SqlParameter("@InvoiceItemId", _id),
                    new SqlParameter("@InvoiceId", InvoiceId),
                    new SqlParameter("@RateItemId", rateId),
                    new SqlParameter("@CustomName", _customName),
                    new SqlParameter("@CustomPrice", _customPrice),
                    new SqlParameter("@Quantity", _quantity),
                    new SqlParameter("@AssetId", _assetId),
                    new SqlParameter("@Taxable", Taxable),
                    new SqlParameter("@Hidden", _hidden),
                    new SqlParameter("Notes", _notes));
            }
            else
            {
                SqlHelper.ExecuteNonQuery(connection,
                    "InvoiceItemsUpdateByIdV2",
                    new SqlParameter("@InvoiceItemId", _id),
                    new SqlParameter("@InvoiceId", InvoiceId),
                    new SqlParameter("@RateItemId", rateId),
                    new SqlParameter("@CustomName", _customName),
                    new SqlParameter("@CustomPrice", _customPrice),
                    new SqlParameter("@Quantity", _quantity),
                    new SqlParameter("@AssetId", _assetId),
                    new SqlParameter("@Taxable", Taxable),
                    new SqlParameter("@Hidden", _hidden),
                    new SqlParameter("@RelatedInvoiceItemId", _relatedInvoiceItemId),
                    new SqlParameter("@Locked", _locked),
                    new SqlParameter("@ClassId", _classId),
                    new SqlParameter("@Notes", _notes));
            }

        }

        public int? CategoryId
        {
            get
            {
                if (this.RateItem != null)
                {
                    return this.RateItem.CategoryId;
                }

                if (this.IsStorageItem())
                {
                    return 5;
                }

                return null;
            }
        }

        public int InvoiceId
        {
            get { return _invoiceId; }
            set { SetField(ref _invoiceId, value, "InvoiceId"); }
        }

        public int Id
        {
            get => _id;
            internal set => _id = value;
        }

        private IRateItem _tempRateItem;

        [JsonIgnore]
        public IRateItem RateItem
        {
            get
            {
                if (_tempRateItem == null)
                {
                    _tempRateItem = Extric.Towbook.RateItem.GetById(_rateItemId);
                }

                return _tempRateItem;
            }
            set
            {
                if (value == null)
                {
                    if (_rateItemId != 0)
                    {
                        SetField(ref _rateItemId, 0, "RateItemId");
                    }
                    return;
                }
                else if (_rateItemId != value.RateItemId)
                {
                    SetField(ref _rateItemId, value.RateItemId, "RateItemId");
                    _tempRateItem = value;
                }
                else if (_tempRateItem != value)
                    _tempRateItem = value;
            }
        }

        /// <summary>
        /// Returns either the custom name or the rate item name. Use this for data bindings/display purposes.
        /// </summary>
        public string Name
        {
            get
            {
                if (!String.IsNullOrEmpty(_customName))
                    return _customName;
                if (RateItem != null)
                    return RateItem.Name;

                return "(unknown)";
            }
        }


        /// <summary>
        /// Returns the price that you should use for calculations; returns either CustomPrice, or RateItem.Price if CustomPrice is null.
        /// </summary>
        public decimal Price
        {
            get => decimal.Round(CustomPrice != null ? CustomPrice.Value : 0, 2, MidpointRounding.AwayFromZero);
        }

        public decimal GetTicketValue(Invoice invoice) => invoice.TicketValue;

        /// <summary>
        /// Return the commission for this invoice item, taking into account any manual adjustments that the user made.
        /// </summary>
        /// <param name="driverId"></param>
        /// <param name="invoice"></param>
        /// <param name="commissions"></param>
        /// <seealso cref="GetSplitCommission(Entry entry, Driver driver, EntryCommissionDriver ecd)"/>
        /// <returns></returns>
        public decimal GetCommission(int driverId, Invoice invoice, IEnumerable<EntryCommissionDriver> commissions)
        {
            if (driverId == 0)
                return 0;

            // verify the driver is valid
            var driver = Driver.GetById(driverId);
            if (driver == null)
                throw new TowbookException("DriverId " + driverId + " doesn't exist");

            decimal fuelSurcharge = invoice.InvoiceItems.FirstOrDefault(w => w._rateItemId == Towbook.RateItem.BUILTIN_FUEL_SURCHARGE)?.CustomPrice ?? 0;
            decimal ticketAmount = invoice.Subtotal - fuelSurcharge;

            decimal baseAmount = GetTicketValue(invoice);
            decimal baseRate = 1;

            if (!invoice.DispatchEntry.Attributes.ContainsKey(AttributeValue.BUILTIN_DISPATCH_COMMISSION_TICKETVALUE) &&
                this.AssetId != null)
            {
                var asset = invoice.DispatchEntry.Assets.FirstOrDefault(o => o.Id == this.AssetId);
                var driverCount = asset?.Drivers.Select(o => o.DriverId).Distinct().Count() ?? 0;

                if (driverCount > 1)
                {
                    
                    if (invoice.DispatchEntry.Company.HasFeature(Generated.Features.EditableCommissionsPercentageSplit) && 
                        !DriverCommissions.Any(a => a.DriverId == driverId) /* only split across drivers if invoiceItemDriverCommissions has not been determined*/)
                        baseRate = 1.0M / driverCount;
                }
            }
            else
                baseRate = baseAmount == 0 ? 1 : (baseAmount / (ticketAmount > 0 ? ticketAmount : 1));
            
            var ecd = (commissions ?? EntryCommissionDriver.GetByDispatchEntryId(invoice.DispatchEntry.Id))?
                .Where(o => o.DriverId == driverId)
                .FirstOrDefault();

            var commission = GetCommissionByDriverIdWithDriverBase(driverId, invoice, ecd).Amount * baseRate;

            
            if (ecd != null)
            {
                if (ecd.Type == Commissions.CommissionType.FlatRate)
                    commission = ecd.FlatAmount / invoice.InvoiceItems.Count;
                else if (ecd.Type == Commissions.CommissionType.Percentage)
                    commission *= (ecd.Percentage / 100);
            }

            return commission;
        }

        internal class CommissionStandardRateAssociation
        {
            public int? AccountId { get; set; }
            public int? AccountTypeId { get; set; }

            public int UseAccountId { get; set; }
        }

        public class CommissionReturn
        {
            public decimal Amount { get; set; }
            public decimal DriverBase { get; set; }
            public CommissionReturn()
            {

            }

            public CommissionReturn(decimal amount, decimal driverBase)
            {
                Amount = amount;

                if (amount != 0)
                    DriverBase = driverBase;
            }
        }

        public decimal GetCommissionByDriverId(int driverId, Invoice invoice)
        {
            return GetCommissionByDriverIdWithDriverBase(driverId, invoice).Amount;
        }

        public CommissionReturn GetCommissionByDriverIdWithDriverBase(int driverId, Invoice invoice) => GetCommissionByDriverIdWithDriverBase(driverId, invoice, null);

        public CommissionReturn GetCommissionByDriverIdWithDriverBase(int driverId, Invoice invoice, EntryCommissionDriver ecd)
        {
            // very important: if you don't exit, it will create a row
            // for every driver in the whole company.
            if (invoice != null && 
                invoice.DispatchEntry != null && 
                !invoice.DispatchEntry.Drivers.Contains(driverId))
                return new CommissionReturn();

            CommissionReturn getCommission()
            {
                if (this.Quantity == 0)
                    return new CommissionReturn();

                var ifs = invoice.InvoiceItems.Where(w => w.RateItem?.RateItemId == Towbook.RateItem.BUILTIN_FUEL_SURCHARGE).Sum(s => s.Total);
                var commissionBaseAmount = invoice.GetTicketValue(driverId, ecd);
                var commissionBaseRate = commissionBaseAmount / ((invoice.Subtotal - ifs) > 0 ? (invoice.Subtotal - ifs) : 1);
                decimal driverBase = 0;

                driverBase = this.Total * commissionBaseRate;

                var lineTotal = Total;

                // verify the driver is valid
                var driver = Driver.GetById(driverId);
                if (driver == null)
                    throw new TowbookException("DriverId " + driverId + " doesn't exist");

                EntryAsset asset = null;

                if ((invoice == null || invoice.DispatchEntry == null) ||
                    (invoice.DispatchEntry.Attributes.ContainsKey(AttributeValue.BUILTIN_DISPATCH_COMMISSION_TICKETVALUE) &&
                    invoice.InvoiceItems.Any() &&
                    (invoice.DispatchEntry.Attributes[AttributeValue.BUILTIN_DISPATCH_COMMISSION_TICKETVALUE].Value == "0" ||
                    invoice.DispatchEntry.Attributes[AttributeValue.BUILTIN_DISPATCH_COMMISSION_TICKETVALUE].Value == "-")))
                    return new CommissionReturn();

                var callDrivers = invoice.DispatchEntry.Drivers;

                #region Ensure that driver is associated with the Entry.
                if (!callDrivers.Contains(driverId))
                {
                    // if the driver isn't associated with this call in any way whatsoever, then return 0.
                    return new CommissionReturn();
                }
                #endregion

                if (_assetId != null)
                {
                    asset = invoice.DispatchEntry.Assets.Where(o => o.Id == _assetId).FirstOrDefault();
                    #region Ensure that the driver is associated with this invoiceItem's asset.
                    if (asset != null)
                    {
                        if (!asset.Drivers.Where(o => o.DriverId.GetValueOrDefault() == driverId).Any())
                        {
                            // the item has an asset assigned, and the driver isn't associated with it.
                            return new CommissionReturn();
                        }
                    }
                    #endregion
                }

                int bodyTypeId = 0;

                if (asset != null)
                    bodyTypeId = asset.BodyTypeId;
                else if (invoice.DispatchEntry != null && invoice.DispatchEntry.Assets.Any())
                    bodyTypeId = invoice.DispatchEntry.Assets[0].BodyTypeId; // assume the first asset body type


                if (true)
                {
                    // for Quik Pik... they have a Standard Motor Club Rates account that they want 
                    // driver commissions to be based off of, instead of the rate for a given motor club. 
                    // so, we wil get the Rate Item price from StandardMotorClub account and use it in place of the 
                    // rateItem cost / rateItemExtendedCost

                    int accountId = 0;
                    var csr = CompanyKeyValue.GetFirstValueOrNull(invoice.CompanyId,
                        Provider.Towbook.ProviderId, "CommissionStandardRatesJson");

                    //csr = "[{accountTypeId:'5', useAccountId:'585027'}]";

                    if (csr != null)
                    {
                        var aid = invoice.DispatchEntry.Account?.Id;
                        var type = (int)invoice.DispatchEntry.Account?.Type;

                        var model = JsonConvert.DeserializeObject<CommissionStandardRateAssociation[]>(csr);

                        // try to find a match by account first
                        var match = model.Where(o => o.AccountId == aid).FirstOrDefault();
                        if (match == null)
                        {
                            // if that doesn't work, try to find by Type
                            match = model.Where(o => o.AccountTypeId == type).FirstOrDefault();
                        }
                        else { Console.WriteLine("using accountId match"); }

                        if (match != null)
                        {
                            Console.WriteLine("using " + match.UseAccountId);
                            accountId = match.UseAccountId;
                        }
                    }

                    if (accountId > 0)
                    {
                        var baseRateAccount = Account.GetById(accountId);

                        var specialRate = Accounts.RateItem.GetByRateItem(baseRateAccount, this.RateItem);

                        decimal specialRateItemCost = 0;

                        if (specialRate != null)
                        {
                            if (specialRate.ExtendedRateItems.ContainsKey(bodyTypeId))
                                specialRateItemCost = specialRate.ExtendedRateItems[bodyTypeId].Amount;
                            else
                                specialRateItemCost = specialRate.Cost;

                            if (CustomPrice < 0 && specialRateItemCost > 0)
                                specialRateItemCost = -specialRateItemCost;

                            lineTotal = specialRateItemCost * Quantity;

                            Console.WriteLine("changed from " + Total + " to " + lineTotal);
                        }
                    }

                }

                Commissions.Commission cs;

                // Commission can occur at:
                // Driver.RateItem
                // Driver
                // Company.RateItem
                // Company

                var standard = Commissions.CommissionRateItemBodyType.GetByRateItem(0, driverId, null, driver.CompanyId);

                // if no rateitem... then try by driver, if no driver, then go by company, if no company, zero.
                if (RateItem == null)
                {
                    // if ad hoc item (no rateItem), check against settings to prevent base driver commission and block.
                    var kv = CompanyKeyValue.GetFirstValueOrNull(driver.CompanyId, Provider.Towbook.ProviderId, "BlockCommissionOnAdHocInvoiceItems");
                    if (kv == "1")
                        return new CommissionReturn();

                    if (standard.ContainsKey(bodyTypeId))
                    {
                        return new CommissionReturn()
                        {
                            Amount = standard[bodyTypeId].Type == Commissions.CommissionType.FlatRate ?
                            standard[bodyTypeId].FlatRate :
                            Math.Round(lineTotal * (standard[bodyTypeId].Percentage / 100), 2, MidpointRounding.AwayFromZero),
                            DriverBase = driverBase
                        };
                    }
                    else if (driver.CommissionRate != null)
                        return new CommissionReturn()
                        {
                            Amount = Math.Round(lineTotal * (driver.CommissionRate.Value / 100), 2, MidpointRounding.AwayFromZero),
                            DriverBase = driverBase
                        };
                    else if (invoice.DispatchEntry.Company.StandardDriverCommission > 0)
                        return new CommissionReturn()
                        {
                            Amount = Math.Round(lineTotal * (invoice.DispatchEntry.Company.StandardDriverCommission / 100), 2,
                            MidpointRounding.AwayFromZero),
                            DriverBase = driverBase
                        };
                    else
                        return new CommissionReturn();
                }

                // verify if there is any schedule group commissions set for this company and the current time
                var scheduleGroup = Commissions.CommissionScheduleGroup.GetByDate(invoice.CompanyId, Core.OffsetDateTime(invoice.DispatchEntry.Company, invoice.DispatchEntry.CreateDate));
                int? scheduleGroupId = null;
                if (scheduleGroup != null)
                    scheduleGroupId = scheduleGroup.CommissionScheduleGroupId;

                if (bodyTypeId > 0)
                    cs = Commissions.Commission.GetCommissionByRateItem(this.RateItem, driver, bodyTypeId, scheduleGroupId, invoice.CompanyId);
                else
                {
                    // Is this a tiered storage item child?
                    // If so, we need to assume the parent of any tiered storage rate item
                    if (this.RateItem.IsStorageItem() &&
                        !RateItem.IsTieredStorageItem() &&
                        this.RateItem.ParentRateItemId > 0)
                    {
                        var ri = Extric.Towbook.RateItem.GetById(this.RateItem.ParentRateItemId);
                        cs = Commissions.Commission.GetCommissionByRateItem(ri, driver, scheduleGroupId, invoice.CompanyId);
                    }
                    else
                    {
                        cs = Commissions.Commission.GetCommissionByRateItem(this.RateItem, driver, scheduleGroupId, invoice.CompanyId);
                    }
                }

                EntryCommissionDriverExtendedCommissions ecdec = ecd?.ExtendedCommissions.FirstOrDefault(o => o.InvoiceItemId == Id);
                if (ecdec != null)
                {
                    if (cs.Type == Commissions.CommissionType.FlatRate)
                        return new CommissionReturn(cs.FlatRate, driverBase);
                    else
                    {
                        if (ecdec.Type == Commissions.CommissionType.FlatRate)
                            return new CommissionReturn(ecdec.Amount * (cs.Percentage / 100), driverBase);
                        else
                            return new CommissionReturn(Total * (ecdec.Amount / 100) * (cs.Percentage / 100), driverBase);
                    }
                }
                else
                {
                    if (cs.Type == Commissions.CommissionType.FlatRate)
                        return new CommissionReturn() { Amount = cs.FlatRate, DriverBase = driverBase };
                    else if (cs.Type == Commissions.CommissionType.Percentage)
                        return
                            new CommissionReturn()
                            {
                                Amount = Math.Round(lineTotal * (cs.Percentage / 100), 2, MidpointRounding.AwayFromZero),
                                DriverBase = driverBase
                            };
                }

                return new CommissionReturn(0, driverBase);
            }

            var existingCommission = DriverCommissions.FirstOrDefault(o => o.DriverId == driverId);
            if (existingCommission != null)
            {
                return new CommissionReturn(existingCommission.Amount, existingCommission.DriverBase);
            }

            var c = getCommission();
            if (c.Amount == 0)
                c.DriverBase = 0;

            if (c.Amount != 0 && this.RelatedInvoiceItemId != null)
            {
                var related = invoice.InvoiceItems.FirstOrDefault(o => o.Id == this.RelatedInvoiceItemId);
                if (related.GetCommissionByDriverIdWithDriverBase(driverId, invoice, ecd).Amount == 0)
                    c.Amount = 0;
            }
             
            var newItem = new InvoiceItemDriverCommission()
            {
                InvoiceItemId = this.Id,
                DriverId = driverId,
                InvoiceId = invoice.Id,
                Amount = c.Amount,
                DriverBase = c.DriverBase
            };

            try
            {
                newItem.Save();
            }
            catch
            {
                // ignore errors like: Cannot insert duplicate key row in object
                // 'dbo.InvoiceItemDriverCommissions' with unique index 'UX_InvoiceItemDriverCommissions_UniqueDriver'.
            }

            DriverCommissions.Add(newItem);

            return c;
        }

        public decimal GetCommissionWrapper(Invoice invoice, int driverId, IEnumerable<EntryCommissionDriver> ecd)
        {
            var splitMode = invoice.DispatchEntry.Company.HasFeature(Generated.Features.EditableCommissionsPercentageSplit);
            var pc = splitMode ?
                GetSplitCommission(invoice.DispatchEntry, Driver.GetById(driverId), 
                ecd.FirstOrDefault(r => r.DispatchEntryId == invoice.DispatchEntryId && r.DriverId == driverId))?.Amount ?? 0 :
                GetCommission(driverId, invoice, ecd);

            return pc; 
        }

        /// <summary>
        /// Returns the calculated commission for this Invoice Item
        /// </summary>
        /// <param name="entry"></param>
        /// <param name="driver"></param>
        /// <param name="ecd"></param>
        /// <seealso cref="GetCommission(int, Invoice, IEnumerable{EntryCommissionDriver})"/>
        /// <returns></returns>
        public CommissionReturn GetSplitCommission(Entry entry, Driver driver, EntryCommissionDriver ecd)
        {
            int bodyTypeId = entry.BodyType?.Id ?? 0;

            var ri = RateItem ?? new RateItem();

            CommissionReturn getSplitCommission()
            {
                var cs = Commissions.Commission.GetCommissionByRateItem(ri, driver, bodyTypeId, null, entry.CompanyId);

                var ifs = entry.Invoice.InvoiceItems.Where(w => w.RateItem?.RateItemId == Towbook.RateItem.BUILTIN_FUEL_SURCHARGE).Sum(s => s.Total);
                var commissionBaseAmount = entry.Invoice.GetTicketValue(false);
                var commissionBaseRate = commissionBaseAmount / ((entry.Invoice.Subtotal - ifs) > 0 ? (entry.Invoice.Subtotal - ifs) : 1);
                decimal driverBase = 0;

                EntryCommissionDriverExtendedCommissions ecdec = ecd?.ExtendedCommissions.FirstOrDefault(o => o.InvoiceItemId == Id);

                driverBase = this.Total * commissionBaseRate * ((ecd?.Percentage ?? 100) / 100);

                if (!Commissions.Commission.ValidateIfFreeQuantityShouldBeCommissionedToDriver(this, entry.Invoice, driver.Id, new[] { ecd }))
                    return new CommissionReturn(0, driverBase);

                if (ecdec != null)
                {

                    if (cs.Type == Commissions.CommissionType.FlatRate)
                        return new CommissionReturn(cs.FlatRate, driverBase);
                    else
                    {
                        if (ecdec.Type == Commissions.CommissionType.FlatRate)
                            return new CommissionReturn(ecdec.Amount * (cs.Percentage / 100), driverBase);
                        else
                            return new CommissionReturn(Total * (ecdec.Amount / 100) * (cs.Percentage / 100), driverBase);
                    }
                }
                else
                {
                    // split between drivers
                    var numberOfDrivers = entry.Drivers.Distinct().Count();

                    if (AssetId != null)
                    {
                        var asset = entry.Assets.FirstOrDefault(o => o.Id == AssetId);
                        if (asset != null && asset.Drivers.Any())
                        {
                            if (!asset.Drivers.Any(o => o.DriverId == driver.Id))
                                return new CommissionReturn();

                            numberOfDrivers = asset.Drivers
                                .Select(a => a.DriverId)
                                .Where(w => w.GetValueOrDefault() > 0 && Commissions.Commission.ValidateIfFreeQuantityShouldBeCommissionedToDriver(this, entry.Invoice, w.Value, new[] { ecd }))
                                .Distinct()
                                .Count();
                        }
                    }

                    if (cs.Type == Commissions.CommissionType.FlatRate)
                        return new CommissionReturn(cs.FlatRate, driverBase);
                    else
                    {
                        if (numberOfDrivers == 0)
                            return new CommissionReturn();

                        // split 100% between the number of drivers
                        return new CommissionReturn(Math.Round(this.Total * ((100 / (decimal)numberOfDrivers) / 100) * (cs.Percentage / 100), 2, MidpointRounding.AwayFromZero), driverBase);
                    }
                }

            }

            var existingCommission = DriverCommissions.FirstOrDefault(o => o.DriverId == driver.Id);

            if (existingCommission != null)
            {
                return new CommissionReturn(existingCommission.Amount,
                    existingCommission.DriverBase);
            }
            else
            {
                var sc = getSplitCommission();

                if (sc.Amount != 0 && RelatedInvoiceItemId != null)
                {
                    var related = entry.Invoice.InvoiceItems.FirstOrDefault(o => o.Id == this.RelatedInvoiceItemId);
                    var relatedCommissionAmount = related?.GetCommissionByDriverIdWithDriverBase(driver.Id, entry.Invoice, ecd)?.Amount ?? 0.0M;

                    if (relatedCommissionAmount == 0)
                        sc.Amount = 0;
                    else if (sc.Amount < 0 && relatedCommissionAmount < Math.Abs(sc.Amount))
                    {
                        // Do not let the amount of the free quantity credit commission amount exceed
                        // the related (usually mileage) invoice item's commission amount. 
                        // When the related commission amount is less than the full free quantity commission
                        // amount, make the free quantity amount equal in magnitude to the related commission amount.
                        sc.Amount = 0 - relatedCommissionAmount;
                    }
                }
                
                var newItem = new InvoiceItemDriverCommission()
                {
                    InvoiceItemId = this.Id,
                    InvoiceId = InvoiceId,
                    DriverId = driver.Id,
                    Amount = sc.Amount,
                    DriverBase = sc.DriverBase
                };

                if (driverCommissions == null || !driverCommissions.Any(r => r.InvoiceId == InvoiceId && r.DriverId == driver.Id && r.InvoiceItemId == Id))
                {
                    if (newItem.Amount == 0)
                        newItem.DriverBase = 0;

                    newItem.Save();

                    if (driverCommissions == null)
                        driverCommissions = new Collection<InvoiceItemDriverCommission>();
                    driverCommissions.Add(newItem);
                }

                return new CommissionReturn(newItem.Amount, newItem.DriverBase);
                
            }
        }

        private Collection<InvoiceItemDriverCommission> driverCommissions;
        internal Collection<InvoiceItemDriverCommission> DriverCommissions
        {
            get
            {
                if (driverCommissions == null)
                {
                    if (this.Id < 1 || Deleted)
                        driverCommissions = new Collection<InvoiceItemDriverCommission>();
                    else
                        driverCommissions = InvoiceItemDriverCommission.GetByInvoiceItemId(this.Id).Where(w => w.InvoiceId == this.InvoiceId).ToCollection();
                }

                return driverCommissions;
            }
            set { driverCommissions = value; }
        }

        private int appliedPricingRuleId;
        public int AppliedPricingRuleId
        {
            get { return appliedPricingRuleId; }
            set { appliedPricingRuleId = value; }
        }

        /// <summary>
        /// Returns the total charge for this item 
        /// </summary>
        public decimal Total
        {
            get
            {
                // start with actual quantity.
                decimal newQuantity = Quantity;
                if (newQuantity < 0)
                    newQuantity = 0;

                return Math.Round(newQuantity * Price, 2, MidpointRounding.AwayFromZero);
            }
        }

        public string CustomName
        {
            get { return _customName; }
            set { SetField(ref _customName, value, "Name"); }
        }

        public string Notes
        {
            get => _notes;
            set => SetField(ref _notes, value, "Notes");
        }


        public decimal? CustomPrice
        {
            get { return _customPrice; }
            set {

                if (value > 200000)
                    throw new TowbookException($"Price value of {value} is too large on Invoice {InvoiceId}.  Max value allowed is 200,000.");

                SetField(ref _customPrice, value, "Price"); 
            }
        }

        /// <summary>
        /// Optionally, associate the item with an individual Asset on a Call/Entry.
        /// </summary>
        public int? AssetId
        {
            get { return _assetId; }
            set { SetField(ref _assetId, value, "AssetId"); }
        }

        public decimal Quantity
        {
            get { return _quantity; }
            set {

                if (value > 1000000)
                    throw new TowbookException($"Quantity of {value} is too large on Invoice {InvoiceId}, InvoiceItemId {Id}. Max value allowed is 1,000,000.");

                SetField(ref _quantity, value, "Quantity"); 
            }
        }

        /// <summary>
        /// Sets whether this individual item is taxable.
        /// </summary>
		public bool Taxable
        {
            get
            {
                string name = (this.CustomName ?? "").ToLowerInvariant().Replace("-", "");
                if (_taxable != null)
                    return _taxable.Value;
                else if (RateItem != null)
                    return RateItem.Taxable;
                else if (name.Contains("tax")
                    && !name.Contains("tax exempt")
                    && !name.Contains("non-tax")
                    && !name.Contains("notax")
                    && !name.Contains("taxi") 
                    && !name.StartsWith("discount:"))
                {
                    return true;
                }
                return false;
            }
            set
            {
                SetField(ref _taxable, value, "Taxable");
            }
        }

        public Collection<InvoiceItemTax> TaxRates
        {
            get
            {
                if (_taxRates == null)
                    _taxRates = InvoiceItemTax.GetByInvoiceItemId(this.Id);

                return _taxRates;
            }
            set
            {
                SetField(ref _taxRates, value, "Tax Rates");
            }
        }

        public static void DeleteByRateAndDispatchEntry(Entry de, int rateItemId)
        {
            if (de.Id < 1) throw new TowbookException("passed entry isn't in system.");
            if (rateItemId < 1) throw new TowbookException("RateItemId passed is less than 1.");

            SqlHelper.ExecuteNonQuery(Core.ConnectionString, "InvoiceItemsDeleteByEntryRateItem",
                new SqlParameter("@DispatchEntryId", de.Id),
                new SqlParameter("@RateItemId", rateItemId));
        }

        //If you change this method, don't forget to change the async version as well
        public static Collection<InvoiceItem> GetByInvoiceId(int id)
        {
            var l = new Collection<InvoiceItem>();

            using (SqlDataReader dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                "InvoiceItemsGetByInvoiceId", new SqlParameter("@InvoiceId", id)))
            {
                while (dr.Read())
                {
                    l.Add(new InvoiceItem(dr));
                }
            }

            return l.OrderBy(o => o.Id).ToCollection();
        }

        public static async Task<Collection<InvoiceItem>> GetByInvoiceIdAsync(int id)
        {
            var l = new Collection<InvoiceItem>();

            using (SqlDataReader dr = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString,
                "InvoiceItemsGetByInvoiceId", new SqlParameter("@InvoiceId", id)))
            {
                while (await dr.ReadAsync())
                {
                    l.Add(new InvoiceItem(dr));
                }
            }

            return l.OrderBy(o => o.Id).ToCollection();
        }

        //If you change this method, don't forget to change the async version as well
        public static Collection<InvoiceItem> GetByInvoiceId(int[] id)
        {
            var l = new Collection<InvoiceItem>();

            foreach (var x in id.Batch(1000))
            {
                using (SqlDataReader dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                    "InvoiceItemsGetByInvoiceIdArray", new SqlParameter("@InvoiceId", string.Join(",", x))))
                {
                    while (dr.Read())
                    {
                        l.Add(new InvoiceItem(dr));
                    }
                }
            }

            l = l.OrderBy(o => o.InvoiceId)
                .ThenBy(o => o.Id).ToCollection();

            return l;
        }

        public static async Task<Collection<InvoiceItem>> GetByInvoiceIdAsync(int[] id)
        {
            var l = new Collection<InvoiceItem>();

            foreach (var x in id.Batch(1000))
            {
                using (SqlDataReader dr = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString,
                    "InvoiceItemsGetByInvoiceIdArray", new SqlParameter("@InvoiceId", string.Join(",", x))))
                {
                    while (await dr.ReadAsync())
                    {
                        l.Add(new InvoiceItem(dr));
                    }
                }
            }

            l = l.OrderBy(o => o.InvoiceId)
                .ThenBy(o => o.Id).ToCollection();

            return l;
        }

        internal static Collection<InvoiceItem> GetFromExistingReader(SqlDataReader dr, Invoice parent = null)
        {
            var il = new CacheCollection<InvoiceItem>();

            if (!dr.IsClosed && dr.HasRows)
            {
                while (dr.Read())
                {
                    il.Items.Add(new InvoiceItem(dr) { _invoice = parent });
                }
            }

            return il.ItemsOrEmpty();
        }

        public static InvoiceItem GetById(int invoiceItemId)
        {
            if (invoiceItemId < 0)
                return new InvoiceItem() { Id = invoiceItemId };

            using (SqlDataReader dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                "InvoiceItemsGetById",
                new SqlParameter("@InvoiceItemId", invoiceItemId)))
            {
                if (dr.Read())
                {
                    return new InvoiceItem(dr);
                }
                else
                {
                    return null;
                }
            }
        }

        public static async Task<InvoiceItem> GetByIdAsync(int invoiceItemId)
        {
            if (invoiceItemId < 0)
                return new InvoiceItem() { Id = invoiceItemId };

            using (SqlDataReader dr = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString,
                "InvoiceItemsGetById",
                new SqlParameter("@InvoiceItemId", invoiceItemId)))
            {
                if (await dr.ReadAsync())
                {
                    return new InvoiceItem(dr);
                }
                else
                {
                    return null;
                }
            }
        }


        public bool Hidden
        {
            get { return _hidden; }
            set { SetField(ref _hidden, value, "Hidden"); }
        }

        public int[] Drivers
        {
            get { return _drivers ?? Array.Empty<int>(); }
            set { SetField(ref _drivers, value, "Drivers"); }
        }

        public int? RelatedInvoiceItemId
        {
            get { return _relatedInvoiceItemId; }
            set
            {
                SetField(ref _relatedInvoiceItemId, value, "Related InvoiceItemId");
            }
        }

        [JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public InvoiceItemLock Locked
        {
            get { return _locked; }
            set { SetField(ref _locked, value, "Locked"); }
        }

        public int ClassId
        {
            get { return _classId; }
            set { SetField(ref _classId, value, "Class"); }
        }
    }
}
