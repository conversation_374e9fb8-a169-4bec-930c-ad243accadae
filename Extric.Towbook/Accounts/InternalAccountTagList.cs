using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using ProtoBuf;
using System.Collections.ObjectModel;
using System.ComponentModel;

namespace Extric.Towbook.Accounts
{
    [ProtoContract]
    public class InternalAccountTagList
    {
        [ProtoMember(1)]
        public Collection<AccountTag> Tags { get; set; } = new Collection<AccountTag>();

        [DefaultValue(false), ProtoMember(2)]
        private bool IsEmptyList
        {
            get { return Tags.Count == 0; }
            set { }
        }

        public InternalAccountTagList()
        {

        }
    }
}
