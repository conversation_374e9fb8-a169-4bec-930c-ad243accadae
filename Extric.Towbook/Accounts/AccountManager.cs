using Extric.Towbook.Utility;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Extric.Towbook.Accounts
{
    [Table("AccountManagers")]
    public class AccountManager
    {
        [Key("AccountManagerId")]
        public int Id { get; set; }
        public int AccountId { get; set; }
        public int UserId { get; set; }
        public DateTime CreateDate { get; set; }
        public int OwnerUserId { get; set; }
        public bool IsDeleted { get; set; }
        public DateTime? DeleteDate { get; set; }
        public int? DeletedByUserId { get; set; }

        public static AccountManager GetById(int id)
        {
            return SqlMapper.Query<AccountManager>(
                @"SELECT * FROM AccountManagers WITH (NOLOCK) WHERE AccountManagerId=@Id",
                new { Id = id }).FirstOrDefault();
        }

        public static AccountManager GetByAccountId(int accountId)
        {
            return SqlMapper.Query<AccountManager>(
                    @"SELECT * FROM AccountManagers WITH (NOLOCK) WHERE AccountId=@AccountId and IsDeleted=0",
                    new { AccountId = accountId }).LastOrDefault();
        }
        
        public static async Task<AccountManager> GetByAccountIdAsync(int accountId)
        {
            return (await SqlMapper.QueryAsync<AccountManager>(
                    @"SELECT * FROM AccountManagers WITH (NOLOCK) WHERE AccountId=@AccountId and IsDeleted=0",
                    new { AccountId = accountId })).LastOrDefault();
        }

        public static IEnumerable<AccountManager> GetByAccountIds(int[] accountIds)
        {
            List<AccountManager> list = new List<AccountManager>();
            
            if (accountIds.Length == 0)
                return list;

            foreach (var batch in accountIds.Batch(500))
            {
                list.AddRange(SqlMapper.Query<AccountManager>(
                        @"SELECT * FROM AccountManagers WITH (NOLOCK) WHERE AccountId in @AccountIds and IsDeleted=0",
                        new { AccountIds = batch }));
            }

            return list;
        }

        public void Save(User user)
        {

            if (Id <= 0)
            {
                if (user != null)
                    OwnerUserId = user.Id;

                CreateDate = DateTime.Now;

                Id = (int)SqlMapper.Insert(this);
            }
            else
                SqlMapper.Update(this);
        }

        public void Delete(User user)
        {
            if (user == null)
                throw new TowbookException("A user must be provided to delete.");

            IsDeleted = true;
            DeleteDate = DateTime.Now;
            DeletedByUserId = user.Id;

            Save(null);
        }
    }
}
