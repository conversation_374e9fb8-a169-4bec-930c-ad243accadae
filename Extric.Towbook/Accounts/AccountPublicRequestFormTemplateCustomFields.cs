using Extric.Towbook.Utility;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Extric.Towbook.Accounts
{
    [Table("AccountPublicRequestFormTemplateReasons")]
    public class AccountPublicRequestFormTemplateReason
    {
        [Key("AccountPublicRequestFormTemplateReasonId")]
        public int Id { get; set; }
        public int TemplateId { get; set; }
        public int ReasonId { get; set; }


        public static IEnumerable<AccountPublicRequestFormTemplateReason> GetByTemplateIds(int[] templateIds)
        {
            if (!templateIds.Any())
                return Array.Empty<AccountPublicRequestFormTemplateReason>();

            return SqlMapper.Query<AccountPublicRequestFormTemplateReason>(
                    @"SELECT * FROM AccountPublicRequestFormTemplateReasons WITH (NOLOCK) WHERE TemplateId in @Ids",
                    new { Ids = templateIds });
        }

        public void Save()
        {
            SqlMapper.Insert(this);
        }

        public void Delete()
        {
            SqlMapper.Delete<AccountPublicRequestFormTemplateReason>(this);
        }
    }
}
