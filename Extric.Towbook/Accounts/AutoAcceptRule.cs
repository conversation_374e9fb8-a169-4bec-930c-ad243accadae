using Extric.Towbook.Utility;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Extric.Towbook.Accounts
{
    [Table("MCDispatch.AutoAcceptRules")]
    public class AutoAcceptRule
    {
        [Key("AutoAcceptRuleId")]
        public int Id { get; set; }
        public int? AccountId { get; set; }
        public int CompanyId { get; set; }
        public DateTime CreateDate { get; set; }
        public int OwnerUserId { get; set; }
        public string Zip { get; set; }
        public int Eta { get; set; }
        public bool IsDeleted { get; set; }
        public AutoAcceptService Type { get; set; }


        public enum AutoAcceptService : int
        {
            All = 0,
            Service = 1,
            Towing = 2
        }

        public AutoAcceptRule()
        {

        }

        public static IEnumerable<AutoAcceptRule> GetByAccountId(int accountId)
        {
            return SqlMapper.Query<AutoAcceptRule>("SELECT * from MCDispatch.AutoAcceptRules WHERE " +
                "AccountId=@A AND IsDeleted=0",
                new { A = accountId }).ToList();
        }


        public static async Task<AutoAcceptRule> GetByZipAsync(int accountId, string zip, AutoAcceptService type)
        {
            var r =  (await SqlMapper.QueryAsync<AutoAcceptRule>("SELECT * from MCDispatch.AutoAcceptRules WHERE " +
                "AccountId=@A AND (Zip=@Z OR Zip='*') AND (Type=@T or Type=0) AND IsDeleted=0 ORDER By Type DESC, Zip DESC",
                new { A = accountId, Z = zip, T = type })).FirstOrDefault();

            if (r == null)
            {
                r = (await SqlMapper.QueryAsync<AutoAcceptRule>("SELECT * from MCDispatch.AutoAcceptRules WHERE " +
                    "(CompanyId=(SELECT CompanyId FROM Accounts WHERE AccountId=@A) AND AccountId IS NULL) AND (Zip=@Z OR Zip='*') AND (Type=@T or Type=0) AND IsDeleted=0 ORDER By Type DESC, Zip DESC",
                    new { A = accountId, Z = zip, T = type })).FirstOrDefault();

                return r;
            }
            else
            {
                return r;
            }
        }

        public void Save()
        {
            if (Zip == null || (Zip.Length != 5 && Zip != "*"))
                throw new Exception("Invalid Zip. Must be 5 characters or *.");

            if (AccountId == 0 && CompanyId == 0)
                throw new Exception("CompanyId and AccountID are 0.");
            
            if (Id == 0)
            {
                this.CreateDate = DateTime.Now;
                Id = (int)SqlMapper.Insert(this);
            }
            else
                SqlMapper.Update(this);
        }
    }
}
