using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Extric.Towbook.Utility;

namespace Extric.Towbook.Accounts
{
    public enum DayOfTheWeek
    {
        Monday = 1,
        Tuesday,
        Wednesday,
        Thursday,
        Friday,
        Saturday,
        Sunday
    }

    [Table("AccountPublicRequestFormTemplateOfficeHours")]
    public class AccountPublicRequestFormTemplateOfficeHour : IUsesSqlKey
    {
        [Key("AccountPublicRequestFormOfficeHourId")]
        public int Id { get; set; }
        public int AccountPublicRequestFormTemplateId { get; set; }
        public DayOfTheWeek DayOfWeek { get; set; }
        public TimeSpan StartTime { get; set; }
        public TimeSpan EndTime { get; set; }


        public static async Task<IEnumerable<AccountPublicRequestFormTemplateOfficeHour>> GetByTemplateId(int templateId)
        {
            return await SqlMapper.QueryAsync<AccountPublicRequestFormTemplateOfficeHour>(
                "SELECT * FROM AccountPublicRequestFormTemplateOfficeHours WHERE AccountPublicRequestFormTemplateId=@TemplateId",
                    new
                    {
                        TemplateId = templateId,
                    });
        }

        public async Task Save()
        {
            if (AccountPublicRequestFormTemplateId < 1)
                return;

            await SqlMapper.InsertAsync(this);
        }

        public static void Delete(int templateId)
        {
            if (templateId < 1)
                return;

            using (var cnn = Core.GetConnection())
            {
                SqlMapper.Execute(cnn, 
                    "DELETE FROM AccountPublicRequestFormTemplateOfficeHours WHERE AccountPublicRequestFormTemplateId = @TemplateId", 
                    new { 
                        TemplateId = templateId 
                    });
            }
        }
    }
}
