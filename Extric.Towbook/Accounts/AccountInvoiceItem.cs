using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data;
using System.Data.SqlClient;
using System.Collections.ObjectModel;
using System.Threading.Tasks;

namespace Extric.Towbook.Accounts
{
    public class AccountInvoiceItem
    {
        public int Id { get; private set; }
        public int InvoiceId { get; set; }
        public IRateItem RateItem { get; set; }
        public string Memo { get; set; }
        public decimal Amount { get; set; }
        public DateTime Date { get; set; }

        public AccountInvoiceItem()
        {
            Id = -1;
        }

        protected AccountInvoiceItem(IDataReader reader)
        {
            InitializeFromDataReader(reader);
        }

        private void InitializeFromDataReader(IDataReader reader)
        {
            Id = reader.GetValue<int>("AccountInvoiceItemId");
            InvoiceId = reader.GetValue<int>("AccountInvoiceId");

            if (reader["RateItemId"] != DBNull.Value)
                RateItem = Extric.Towbook.RateItem.GetById(Convert.ToInt32(reader["RateItemId"]));

            Date = reader.GetValue<DateTime>("Date");
            Memo = reader.GetValue<string>("Memo");
			Amount = reader.GetValue<decimal>("Amount");
        }
        
		public static async Task<AccountInvoiceItem> GetByIdAsync(int accountInvoiceItemId)
		{
            if (accountInvoiceItemId < 1)
                throw new Exception("accountInvoiceItemId must be greater than zero.");

			using (SqlDataReader dr = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString,
				"RateItemsGetByPredefinedId", new SqlParameter("@AccountInvoiceItemId", accountInvoiceItemId)))
            {
                if (await dr.ReadAsync())
                {
                    return new AccountInvoiceItem(dr);
				}
				else
				{
                    return null;
				}
			}
		}

        public static async Task<Collection<AccountInvoiceItem>> GetByInvoice(Statement invoice)
        {
            Collection<AccountInvoiceItem> list = new Collection<AccountInvoiceItem>();

            using (SqlDataReader dr = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString,
                "AccountInvoiceItemsGetByInvoiceId", invoice.Id))
            {


                while (await dr.ReadAsync())
                {
                    AccountInvoiceItem r = new AccountInvoiceItem(dr);
                    list.Add(r);
                }
            }

            return list;
        }

        /// <summary>
        /// Saves the User object to the data store.
        /// </summary>
        public void Save()
        {
            if (Id == 0)
            {
                throw new Extric.Towbook.TowbookException("No such AccountInvoiceItem. Can't save " +
                    "object! (this object should have already been discarded!)");
            }

            if (Id == -1)
            {
                DbInsert();
            }
            else
            {
                DbUpdate();
            }
        }

        public void Delete(User deleter)
        {
            SqlHelper.ExecuteNonQuery(Core.ConnectionString,
                "AccountInvoiceItemsDeleteByid",
                new SqlParameter("@AccountInvoiceItemId", Id));

            InvoiceId = 0;
            Id = -1;
            Memo = null;
            Amount = 0;
            Date = DateTime.MinValue;
        }

        private void DbInsert()
        {
            Id = Convert.ToInt32(SqlHelper.ExecuteScalar(Core.ConnectionString,
                "AccountInvoiceItemsInsert",
                new SqlParameter("@AccountInvoiceId", InvoiceId),
                new SqlParameter("@RateItemId", (RateItem != null ? (int?) RateItem.RateItemId : null )),
                new SqlParameter("@Memo", Memo),
                new SqlParameter("@Amount", Amount),
                new SqlParameter("@Date", Date)
                ));

        }

        private void DbUpdate()
        {
            SqlHelper.ExecuteNonQuery(Core.ConnectionString,
                "AccountInvoiceItemsUpdateById",
                new SqlParameter("@AccountInvoiceItemId", Id),
                new SqlParameter("@AccountInvoiceId", InvoiceId),
                new SqlParameter("@RateItemId", (RateItem != null ? (int?) RateItem.RateItemId : null)),
                new SqlParameter("@Memo", Memo),
                new SqlParameter("@Amount", Amount),
                new SqlParameter("@Date", Date)
                );
        }


    }
}
