using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Data.SqlClient;
using ProtoBuf;
using Glav.CacheAdapter.Core.DependencyInjection;
using Extric.Towbook.Utility;
using System.Linq;
using System.Diagnostics;
using Extric.Towbook.ActivityLogging;
using System.Threading.Tasks;

namespace Extric.Towbook.Accounts
{
    [ProtoContract(ImplicitFields = ImplicitFields.AllFields, Name = "AExtRateIem")]
    [DebuggerDisplay("Id={Id}, AccountId={AccountId}, Amount={Amount.ToString(\"C\")}")]
    public class ExtendedRateItem : TrackableObject, IExtendedRateItem
    {
        private const int CacheTimeout = 1440;
        private const string CacheKeyCompanyAll = "aExtRate_c_all:";
        public int Id { get; internal set; }
        public int AccountId { get; set; }
        public int BodyTypeId { get; set; }
        public int RateItemId { get; set; }
        
        private decimal amount;
        public decimal Amount
        {
            get => amount;
            set => SetField(ref amount, value, "Amount");
        }

        public decimal BaseAmount { get; set; }
        public DateTime CreateDate { get; internal set; }

        private decimal freeQuantity;
        public decimal FreeQuantity
        {
            get => freeQuantity;
            set => SetField(ref freeQuantity, value, "Free Units");
        }

        public ExtendedRateItem()
        {
            Id = -1;
        }

        [Obsolete]
        public ExtendedRateItem(int id)
        {
            using (SqlDataReader dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                "AccountRateItemsExtendedGetById", new SqlParameter("@RateItemId", id)))
            {
                if (dr.Read())
                {
                    InitializeFromDataReader(dr);
                }
                else
                {
                    Id = 0;
                    throw new ApplicationException("ExtendedRateItem doesn't exist!" + id);
                }
            }
        }

        public static async Task<ExtendedRateItem> GetByIdAsync(int id)
        {
            using (SqlDataReader dr = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString,
                "AccountRateItemsExtendedGetById", new SqlParameter("@RateItemId", id)))
            {
                if (await dr.ReadAsync())
                {
                    return new ExtendedRateItem(dr);
                }
                else
                {
                    return null;
                }
            }
        }

        public override string ToString()
        {
            return "Id=" + Id + ", AccountId=" + AccountId + ", Amount=" + Amount;
        }
        public ExtendedRateItem(SqlDataReader reader)
        {
            InitializeFromDataReader(reader);
        }

        /// <summary>
        /// Retrieves all Extric.Towbook.Accounts.ExtendedRateItem's for the specified company, and places them in the cache.
        /// </summary>
        /// <param name="companyId"></param>
        /// <returns></returns>
        public static Collection<ExtendedRateItem> GetByCompanyId(int companyId)
        {
            var r = AppServices.Cache.Get(CacheKeyCompanyAll + companyId,
                TimeSpan.FromMinutes(CacheTimeout), () =>
            {
                var rows = SqlMapper.QuerySP("AccountRateItemsExtendedGetByCompanyId",
                new { @CompanyId = companyId });

                if (rows != null && rows.Count() > 0)
                {
                    return new InternalExtendedRateItemCollection()
                    {
                        Collection = rows.Select<dynamic, ExtendedRateItem>(o => Map(o)).ToCollection()
                    };
                }
                else
                {
                    var temp = new InternalExtendedRateItemCollection() { Collection = new Collection<ExtendedRateItem>() };
                    temp.Collection.Add(new ExtendedRateItem() { Id = 0 });
                    return temp;
                }
            });

            if (r.Collection == null || (r.Collection.Count == 1 && r.Collection[0].Id == 0))
                return new Collection<ExtendedRateItem>();
            else
                return r.Collection;
        }

        protected static ExtendedRateItem Map(dynamic input)
        {
            return new ExtendedRateItem()
            {
                Id = input.AccountRateItemExtendedId,
                AccountId = input.AccountId,
                RateItemId = input.RateItemId,
                BodyTypeId = input.BodyTypeId,
                BaseAmount = (decimal)input.Amount,
                amount = (decimal)input.Amount,
                CreateDate = input.CreateDate,
                freeQuantity = (decimal)(input.FreeQuantity ?? 0)
            };
        }

        public static Dictionary<int, IExtendedRateItem> GetByRateItem(Accounts.RateItem ri)
        {
            var rateItems = new Dictionary<int, IExtendedRateItem>();

            var items = GetByCompanyId(ri.Account.CompanyId).Where(o =>
                (o.RateItemId == ri.BaseRateItem.Id &&
                o.AccountId == ri.AccountId));

            foreach (ExtendedRateItem r in items)
            {
                rateItems.Add(r.BodyTypeId, r);
            }

            return rateItems;
        }

        private void InitializeFromDataReader(SqlDataReader reader)
        {
            Id = reader.GetValue<int>("AccountRateItemExtendedId");
            AccountId = reader.GetValue<int>("AccountId");
            RateItemId = reader.GetValue<int>("RateItemId");
            BodyTypeId = reader.GetValue<int>("BodyTypeId");
            BaseAmount = amount = reader.GetValue<decimal>("Amount");
            CreateDate = reader.GetValue<DateTime>("CreateDate");
            freeQuantity = reader.GetValueOrDefault<int>("FreeQuantity");
        }

        #region Insert/Update/Delete methods

        /// <summary>
        /// Saves the ExtendedRateItem object to the data store.
        /// </summary>
        public void Save()
        {
            if (!IsDirty)
                return;

            if (Id == 0)
            {
                throw new Extric.Towbook.TowbookException("No such Account.ExtendedRateItem. Can't save " +
                    "object! (this object should have already been discarded!)" + Id);
            }
            try
            {
                if (Id == -1)
                {
                    DbInsert();
                }
                else
                {
                    DbUpdate();
                }
            }
            finally
            {
                InvalidateCache();
                MarkAsClean();
            }
        }

        protected void InvalidateCache(int companyId = 0)
        {
            if (companyId == 0)
                companyId = Account.GetById(AccountId).CompanyId;

            //System.Diagnostics.Debug.WriteLine("Extric.Towbook.Accounts.ExtendedRateItem.InvalidateCache @companyId=" + companyId);
            AppServices.Cache.InvalidateCacheItem(CacheKeyCompanyAll + companyId);
        }

        public void Delete(User deleter)
        {
            SqlHelper.ExecuteNonQuery(Core.ConnectionString,
                "AccountRateItemsExtendedDeleteByid",
                new SqlParameter("@AccountRateItemExtendedId", Id));

            Id = 0;

            InvalidateCache();
            MarkAsClean();
        }

        private void DbInsert()
        {
            Id = Convert.ToInt32(SqlHelper.ExecuteScalar(Core.ConnectionString,
                "AccountRateItemsExtendedInsert",
                new SqlParameter("@RateItemId", RateItemId),
				new SqlParameter("@AccountId", AccountId),
                new SqlParameter("@BodyTypeId", BodyTypeId),
                new SqlParameter("@Amount", Amount),
                new SqlParameter("@FreeQuantity", FreeQuantity)));
        }

        private void DbUpdate()
        {
            SqlHelper.ExecuteNonQuery(Core.ConnectionString,
                "AccountRateItemsExtendedUpdateById",
                new SqlParameter("@AccountRateItemExtendedId", Id),
				new SqlParameter("@AccountId", AccountId),
                new SqlParameter("@RateItemId", RateItemId),
                new SqlParameter("@BodyTypeId", BodyTypeId),
                new SqlParameter("@Amount", Amount),
                new SqlParameter("@FreeQuantity", FreeQuantity)
                );
        }

        #endregion
    }
}
