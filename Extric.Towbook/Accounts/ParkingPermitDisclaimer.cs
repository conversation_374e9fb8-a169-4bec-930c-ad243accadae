using Extric.Towbook.Utility;
using Glav.CacheAdapter.Core.DependencyInjection;
using ProtoBuf;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Extric.Towbook.Accounts
{
    [ProtoContract]
    [Table("AccountParkingPermitDisclaimers")]
    public class ParkingPermitDisclaimer
    {
        private const string cacheFormat = "a-ppd-{0}";
        private const string cacheFormatAccount = "a-ppd-account-{0}";

        public int Id { get; private set; }
        public int CompanyId { get; set; }
        public int? AccountId { get; set; }
        public int? ReasonId { get; set; }
        public string Content { get; set; }
        public DateTime CreateDate { get; set; }
        public int OwnerUserId { get; set; }
        public bool Deleted { get; set; }
        public DateTime? DeleteDate { get; set; }
        public int? DeletedByUserId { get; set; }

        public ParkingPermitDisclaimer()
        {
            Id = 0;
        }

        public static ParkingPermitDisclaimer GetByCompanyId(int companyId, int? accountId = null, int? reasonId = null)
        {
            Collection<ParkingPermitDisclaimer> disclaimers = AppServices.Cache.Get(
                String.Format(cacheFormatAccount, accountId), TimeSpan.FromDays(30), () =>
            {
                return new ParkingPermitDisclaimerCollection(
                    Map(SqlMapper.QuerySP<dynamic>("dbo.AccountParkingPermitDisclaimersGetByCompanyId", new {
                        @CompanyId = companyId, @AccountId = accountId, @ReasonId = reasonId })));
            }).Collection;


            return disclaimers
                .OrderByDescending(a => a.ReasonId)
                .ThenByDescending(b => b.AccountId)
                .FirstOrDefault();
        }


        public static ParkingPermitDisclaimer GetById(int id)
        {
            return Map(SqlMapper.QuerySP<dynamic>("dbo.AccountParkingPermitDisclaimerGetById",
                new { @ParkingPermitDisclaimerId = id })).FirstOrDefault();
        }

        private static IEnumerable<ParkingPermitDisclaimer> Map(IEnumerable<dynamic> list)
        {
            return list.Select(o => new ParkingPermitDisclaimer()
            {
                Id = o.ParkingPermitDisclaimerId,
                CompanyId = o.CompanyId,
                AccountId = o.AccountId,
                ReasonId = o.ReasonId,
                Content = o.Content,
                CreateDate = o.CreateDate,
                OwnerUserId = o.OwnerUserId,
                Deleted = o.Deleted,
                DeleteDate = o.DeleteDate,
                DeletedByUserId = o.DeletedByUserId
            }).ToCollection();
        }

        public void Delete(User user)
        {
            // There is no update method. Permit Id exists due to conversion. Save it first before delete.
            using (var c = Core.GetConnection())
            {
                c.Execute("UPDATE AccountParkingPermitDisclaimers SET Deleted=1, DeletedByUserId=@DeletedUserId, DeleteDate=@DeleteDate WHERE ParkingPermitDisclaimerId=@Id",
                    new
                    {
                        Id = Id,
                        DeletedUserId = user.Id,
                        DeleteDate = DateTime.Now
                    });
            }
            
        }

        public void Save(User user)
        {
            if (this.Content != null && this.Content.Length > 4000)
            {
                this.Content = JsonExtensions.Truncate(this.Content, 4000);
            }

            if (Id < 1)
            {
                CreateDate = DateTime.Now;
                OwnerUserId = user.Id;

                this.Id = (int)SqlMapper.Insert(this);
            }
            else
            {
                this.Delete(user);

                CreateDate = DateTime.Now;
                OwnerUserId = user.Id;
                this.Id = (int)SqlMapper.Insert(this);
            }

            AppServices.Cache.InvalidateCacheItem(string.Format(cacheFormatAccount, AccountId));
        }
    }

    [ProtoContract]
    public class ParkingPermitDisclaimerCollection
    {
        [ProtoMember(1)]
        public Collection<ParkingPermitDisclaimer> Collection { get; set; }

        public ParkingPermitDisclaimerCollection() { }

        public ParkingPermitDisclaimerCollection(IEnumerable<ParkingPermitDisclaimer> list)
        {
            Collection = list.ToCollection();
        }
    }
}
