using System;
using System.Collections.Generic;
using System.Linq;
using Extric.Towbook.Utility;
using Glav.CacheAdapter.Core.DependencyInjection;
using ProtoBuf;
using System.Collections.ObjectModel;
using System.Threading.Tasks;
using Extric.Towbook.Caching;

namespace Extric.Towbook.Accounts
{
    [Serializable]
    [ProtoContract(ImplicitFields = ImplicitFields.AllFields, Name = "AccountRateItemRule")]
    public class AccountRateItemRule
    {
        private const string cacheFormat = "a-pr-{0}";
        private const string cacheFormatAccount = "a-pr-account-{0}";

        public int Id { get; private set; }
        public int? AccountId { get; set; }
        public int RateItemId { get; set; }
        public int? ReasonId { get; set; }
        public int? BodyTypeId { get; set; }
        public decimal? DefaultQuantity { get; set; }
        
        /// <summary>
        /// If this is above 0.00, then the rule should be added once the daysHeldBillable of an impound reaches this value.
        /// </summary>
        public decimal ImpoundAddAfterThisManyDaysHeld { get; set; }

        public int OwnerUserId { get; set; }
        public DateTime CreateDate { get; private set; }
        public bool Deleted { get; private set; }

        public static IEnumerable<AccountRateItemRule> GetByCompanyId(int companyId)
        {
            return GetByCompanyId(new int[] { companyId });
        }

        /// <summary>
        /// Retrieve all of the RateItem rules for the specified companies
        /// </summary>
        /// <param name="companyIds"></param>
        /// <returns></returns>
        public static IEnumerable<AccountRateItemRule> GetByCompanyId(int[] companyIds)
        {
            return AppServices.Cache.Get(string.Format(cacheFormatAccount, string.Join(",", companyIds)),
                TimeSpan.FromMinutes(60), () =>
            {
                return new CacheCollection<AccountRateItemRule>(
                    SqlMapper.QuerySP<dynamic>("dbo.AccountRateItemRulesGetByCompanyId",
                        new
                        {
                            @CompanyId = string.Join(",", companyIds)
                        })
                        .Select<dynamic, AccountRateItemRule>(o => Map(o))
                    );
            }).Items;
        }

        /// <summary>
        /// Retrieve all the RateItem/Pricing Rules for the specified account.
        /// </summary>
        /// <param name="accountId"></param>
        /// <returns></returns>
        public static IEnumerable<AccountRateItemRule> GetByAccountId(int accountId)
        {
            return AppServices.Cache.Get(String.Format(cacheFormatAccount, accountId), TimeSpan.FromMinutes(60), () =>
            {
                return new CacheCollection<AccountRateItemRule>(
                    SqlMapper.QuerySP<dynamic>("dbo.AccountRateItemRulesGetByAccountId",
                        new
                        {
                            @AccountId = accountId
                        })
                        .Select<dynamic, AccountRateItemRule>(o => Map(o)));
            }).Items ?? new Collection<AccountRateItemRule>();
        }

        public static AccountRateItemRule Map(dynamic o)
        {
            return new AccountRateItemRule()
            {
                Id = o.AccountRateItemRuleId,
                AccountId = o.AccountId,
                RateItemId = o.RateItemId,
                ReasonId = o.ReasonId,
                BodyTypeId = o.BodyTypeId,
                DefaultQuantity = Convert.ToDecimal(o.DefaultQuantity),
                ImpoundAddAfterThisManyDaysHeld = o.ImpoundAddAfterThisManyDaysHeld != null ? (decimal)o.ImpoundAddAfterThisManyDaysHeld : 0,
                OwnerUserId = o.OwnerUserId,
                CreateDate = o.CreateDate,
                Deleted = o.Deleted, 
            };
        }


        public async Task<AccountRateItemRule> Save()
        {
            if (this.Id == 0)
            {
                var d = SqlMapper.QuerySP<dynamic>("dbo.AccountRateItemRulesInsert",
                    new
                    {
                        @AccountId = this.AccountId,
                        @RateItemId = this.RateItemId,
                        @ReasonId = this.ReasonId,
                        @BodyTypeId = this.BodyTypeId,
                        @DefaultQuantity = this.DefaultQuantity,
                        @ImpoundAddAfterThisManyDaysHeld = this.ImpoundAddAfterThisManyDaysHeld,
                        @OwnerUserId = this.OwnerUserId
                    }).FirstOrDefault();

                this.Id = d.Id;
                this.CreateDate = d.CreateDate;
            }
            else
            {
                SqlMapper.ExecuteSP("dbo.AccountRateItemRulesUpdateById",
                    new
                    {
                        @AccountRateItemRuleId = this.Id,
                        @AccountId = this.AccountId,
                        @RateItemId = this.RateItemId,
                        @ReasonId = this.ReasonId,
                        @BodyTypeId = this.BodyTypeId,
                        @DefaultQuantity = this.DefaultQuantity,
                        @ImpoundAddAfterThisManyDaysHeld = this.ImpoundAddAfterThisManyDaysHeld,
                        @OwnerUserId = this.OwnerUserId
                    });
            }

            AppServices.Cache.InvalidateCacheItem(String.Format(cacheFormat, this.Id));
            AppServices.Cache.InvalidateCacheItem(String.Format(cacheFormatAccount, AccountId));
            if (AccountId != null)
            {
                var account = await Account.GetByIdAsync(AccountId.Value);
                int companyId = account.CompanyId;
                await CacheWorkerUtility.UpdateAccountRateItemRule(this, companyId);
            }

            return this;
        }

        public async Task Delete()
        {
            if (Id == 0) return;

            SqlMapper.ExecuteSP("dbo.AccountRateItemRulesDeleteById",
                new { @AccountRateItemRuleId = Id, });

            Deleted = true;
            if (AccountId != null)
            {
                var account = await Account.GetByIdAsync(AccountId.Value);
                int companyId = account.CompanyId;
                await CacheWorkerUtility.DeleteAccountRateItemRule(this, companyId);
            }
        }

        /// <summary>
        /// Get an account rate Item Rule by Id. Will not return a deleted rule
        /// </summary>
        /// <param name="id">Account Rate Item Rule Id</param>
        /// <returns></returns>
        public static AccountRateItemRule GetById(int id)
        {
            return SqlMapper.QuerySP<dynamic>("dbo.AccountRateItemRulesGetById",
                new
                {
                    @AccountRateItemRuleId = id
                })
                .Select<dynamic, AccountRateItemRule>(o => Map(o)).FirstOrDefault();
        }
    }
}
