using Extric.Towbook.Utility;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace Extric.Towbook.Management
{
    [Table("Billing.CompanyContractFeatures")]
    public class CompanyContractFeature
    {
        [Key("CompanyContractFeatureId")]
        public int Id { get; set; }
        public int CompanyContractId { get; set; }
        public int FeatureId { get; set; }
        public decimal MonthlyAmount { get; set; }
        public DateTime CreateDate { get; set; }
        public DateTime? ExpirationDate { get; set; }
        public bool IsDeleted { get; set; }

        public CompanyContractFeature()
        {

        }
        public CompanyContractFeature(Generated.Features f)
        {
            FeatureId = (int)f;
        }

        public async Task Save(int companyId, int userId)
        {
            if (CreateDate == DateTime.MinValue)
                CreateDate = DateTime.Now;

            if (Id == 0)
            {
                this.Id = (int)await SqlMapper.InsertAsync(this);
            }
            else
            {
                await SqlMapper.UpdateAsync(this);
            }

            await Core.DeleteRedisKeyAsync("comp_features:" + companyId);

            var feat = Feature.GetById(FeatureId);

            await Caching.CacheWorkerUtility.UpdateFeature(new
                 Caching.CacheWorkerUtility.CacheFeatureUpdateModel()
            {
                Action = "update",
                CompanyId = companyId,
                OwnerUserId = userId,
                FeatureId = feat.Id,
                Enabled = !IsDeleted
            });
        }
    }
}
