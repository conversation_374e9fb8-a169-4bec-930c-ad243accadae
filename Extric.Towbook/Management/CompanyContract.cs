using System;
using System.Linq;
using Extric.Towbook.Utility;
using System.Data;
using System.Data.SqlClient;
using System.Collections.ObjectModel;

namespace Extric.Towbook.Management
{
    public enum BillingFrequency : int
    {
        Monthly = 1,
        Quarterly = 2,
        BiAnnual = 3,
        Annual = 4
    }

    [Table("Billing.CompanyContracts")]
    public class CompanyContract
    {
        [Key("CompanyContractId")]
        public int Id { get; set; }
        public int CompanyId { get; set; }
        public int OwnerUserId { get; set; }

        /// <summary>
        /// Which Towbook Employee sold the contract
        /// </summary>
        public int SalesUserId { get; set; }

        [Ignore(true)]
        [Write(false)]
        public CompanyContractFeature[] Features { get; private set; } = Array.Empty<CompanyContractFeature>();

        public bool IsPaying { get; set; }
        public bool IsCancelled { get; set; }

        public decimal BillingAmount { get; set; }
        public int BillingDay { get; set; }
        public int BillingSystemId { get; set; }
        [Write(false)]
        public BillingFrequency BillingFrequency { get; set; }

        [Write(false)]
        public DateTime? LastBillDate { get; set; }
        [Write(false)]
        public DateTime? LastPaymentReceived { get; set; }
        [Write(false)]
        public DateTime? NextBillingDate { get; set; }

        [Write(false)]
        public DateTime CreateDate { get; set; }

        public static CompanyContract GetByCompanyId(int companyId)
        {
            using (var cnn = Core.GetConnection())
            {
                using (var multi = cnn.QueryMultiple(
@"SELECT *, Id=CompanyContractId FROM Billing.CompanyContracts WHERE CompanyId=@CompanyId

SELECT F.*, Id=F.CompanyContractFeatureId FROM Billing.CompanyContractFeatures F INNER JOIN Billing.CompanyContracts CC ON CC.CompanyContractId=F.CompanyContractId WHERE CC.CompanyId=@CompanyId AND  F.IsDeleted=0
UNION
SELECT F.*, Id=F.CompanyContractFeatureId FROM Billing.CompanyContractFeatures F 
	INNER JOIN CompaniesShared CS on CS.CompanyId=@CompanyId OR CS.SharedCompanyId=@CompanyId
	INNER JOIN Billing.CompanyContracts CC ON CC.CompanyContractId=F.CompanyContractId AND CC.CompanyId=CS.CompanyId
	WHERE F.IsDeleted=0
union
select 0,0,12,0,'1/1/2000', null,0,0 from Companies c where c.companyId=@CompanyId and c.companyid not in (select companyid from billing.CompanyContracts where billingamount > 0 and BillingAmount < 99)
",
                    new { @CompanyId = companyId }, null, null, CommandType.Text))
                {

                    var r = multi.Read<CompanyContract>().FirstOrDefault();

                    if (r != null)
                    {

                        Collection<CompanyContractFeature> features = new Collection<CompanyContractFeature>();

                        foreach (var f in multi.Read<CompanyContractFeature>())
                        {
                            features.Add(f);
                        }

                        r.Features = features.ToArray();
                    }


                    return r;

                }
            }
        }

        public static CompanyContract GetExistingOrCreateNew(int companyId, int userId)
        {
            var cc = GetByCompanyId(companyId);
            if (cc == null)
            {
                cc = new CompanyContract();

                cc.CompanyId = companyId;
                cc.OwnerUserId = userId;
                cc.BillingSystemId = 1;
                cc.SalesUserId = userId;
                cc.Save();
            }
            return cc;
        }

        public void Save()
        {
            if (this.Id < 1)
                this.Id = (int)SqlMapper.Insert<CompanyContract>(this);
            else
                SqlMapper.Update<CompanyContract>(this);
        }
    }
}
