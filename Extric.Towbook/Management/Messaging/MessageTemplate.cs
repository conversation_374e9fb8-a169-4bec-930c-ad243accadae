using Commons.Collections;
using Extric.Towbook.Utility;
using NVelocity;
using NVelocity.App;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Text;

namespace Extric.Towbook.Management.Messaging
{
    public class MessageTemplate
    {
        public enum BaseOnProperty
        {
            CreateDate = 0,
            FirstLoginDate = 1
        }

        private static List<User.TypeEnum> userTypes = new List<User.TypeEnum>();
        private static void FillUserTypes()
        {
            if (userTypes.Count == 7)
            {
                return;
            }
            userTypes.Add(User.TypeEnum.Manager);
            userTypes.Add(User.TypeEnum.Dispatcher);
            userTypes.Add(User.TypeEnum.Driver);
            userTypes.Add(User.TypeEnum.Accountant);
            userTypes.Add(User.TypeEnum.PoliceOfficer);
            userTypes.Add(User.TypeEnum.AccountUser);
            userTypes.Add(User.TypeEnum.SystemAdministrator);
        }

        public int MessageTemplateId { get; set; }
        public string Name { get; set; }
        public string FromName { get; set; }
        public string FromEmail { get; set; }
        public string Subject { get; set; }
        public string Body { get; set; }
        public BaseOnProperty BasedOn { get; set; }
        public bool DisableIfFirstLoginNotNull { get; set; }
        public int MinHoursBeforeSend { get; set; }
        public string ExactTime { get; set; }
        public List<MessageRecipient> Recipients { get; set; }

        public int ExactTimeHours
        {
            get
            {
                if (!String.IsNullOrEmpty(this.ExactTime))
                {
                    return Convert.ToInt32(this.ExactTime.Substring(0, this.ExactTime.IndexOf(':')));
                }
                return -1;
            }
        }

        public int ExactTimeMinutes
        {
            get
            {
                if (!String.IsNullOrEmpty(this.ExactTime))
                {
                    return Convert.ToInt32(this.ExactTime.Substring(this.ExactTime.IndexOf(':') + 1));
                }
                return -1;
            }
        }

        public MessageTemplate()
        {
            MessageTemplateId = 0;
            FillUserTypes();
        }

        public static Collection<MessageTemplate> GetAll()
        {
            var messageTemplates = MapMessageTemplate(SqlMapper.QuerySP<dynamic>("dbo.MessageTemplatesGetAll"));

            foreach (var mt in messageTemplates)
            {
                mt.Recipients = MessageRecipient.GetByMessageTemplateId(mt.MessageTemplateId);
            }
            
            return messageTemplates;
        }

        public static MessageTemplate GetById(int id)
        {
            var messageTemplate = GetAll().Where(w => w.MessageTemplateId == id).FirstOrDefault();

            return messageTemplate;
        }

        public static Collection<MessageTemplate> GetByUserTypeId(int userTypeId)
        {
            var messageTemplates = MapMessageTemplate(SqlMapper.QuerySP<dynamic>("dbo.MessageTemplatesGetByRecipientUserTypeId",
                new
                {
                    @UserTypeId = userTypeId
                }));

            foreach (var mt in messageTemplates)
            {
                mt.Recipients = MessageRecipient.GetByMessageTemplateId(mt.MessageTemplateId);
            }

            return messageTemplates;
        }

        public void Save()
        {
            if (this.MessageTemplateId == 0)
            {
                this.MessageTemplateId = DbSaveMessageTemplate();

                //TODO: naybe try one of the approaches described here: http://stackoverflow.com/questions/1069311/passing-an-array-of-parameters-to-a-stored-procedure
                foreach (var item in this.Recipients)
                {
                    item.Save(this.MessageTemplateId);
                }
            }
            else
            {
                DbUpdateMessageTemplate();

                foreach (var item in this.Recipients)
                {
                    item.Save(this.MessageTemplateId);
                }
            }
        }

        private int DbSaveMessageTemplate()
        {
            var id = Convert.ToInt32(SqlMapper.QuerySP<dynamic>("dbo.MessageTemplatesInsert",
                new
                {
                    @Name = this.Name,
                    @FromName = this.FromName,
                    @FromEmail = this.FromEmail,
                    @Subject = this.Subject,
                    @Body = this.Body,
                    @BasedOn = this.BasedOn,
                    @DisableIfFirstLoginNotNull = this.DisableIfFirstLoginNotNull,
                    @MinHoursBeforeSend = this.MinHoursBeforeSend,
                    @ExactTime = this.ExactTime
                }).First().MessageTemplateId);

            return id;
        }

        private void DbUpdateMessageTemplate()
        {
            SqlMapper.ExecuteSP("dbo.MessageTemplatesUpdate",
                new
                {
                    @MessageTemplateId = this.MessageTemplateId,
                    @Name = this.Name,
                    @FromName = this.FromName,
                    @FromEmail = this.FromEmail,
                    @Subject = this.Subject,
                    @Body = this.Body,
                    @BasedOn = this.BasedOn,
                    @DisableIfFirstLoginNotNull = this.DisableIfFirstLoginNotNull,
                    @MinHoursBeforeSend = this.MinHoursBeforeSend,
                    @ExactTime = this.ExactTime
                });
        }

        public void Delete()
        {
            SqlMapper.ExecuteSP("dbo.MessageTemplatesDelete",
                new
                {
                    @MessageTemplateId = this.MessageTemplateId
                });
        }

        public string ParseTemplate(Company.Company company)
        {
            return ParseTemplate(company, null);
        }
        
        public string ParseTemplate(Company.Company company, User user)
        {
            var ve = new VelocityEngine();
            var ep = new ExtendedProperties();

            ve.Init(ep);
            var vc = new VelocityContext();
            
            vc.Put("time", DateTime.Now);
            vc.Put("Company", company);
            if (user != null)
            {
                vc.Put("User", user);
            }
            else
            {
                vc.Put("User", string.Empty);
            }

            StringWriter writer = new StringWriter();

            ve.Evaluate(vc, writer, this.Name, this.Body);
            return writer.GetStringBuilder().ToString().Replace("<TK:Dynamic>", "").Replace("</TK:Dynamic>", "");
        }

        private static Collection<MessageTemplate> MapMessageTemplate(IEnumerable<dynamic> list)
        {
            var result = new Collection<MessageTemplate>();

            foreach (var item in list)
            {
                var mt = new MessageTemplate()
                {
                    MessageTemplateId = item.MessageTemplateId,
                    Name = item.Name,
                    FromName = item.FromName,
                    FromEmail = item.FromEmail,
                    Subject = item.Subject,
                    Body = item.Body,
                    DisableIfFirstLoginNotNull = item.DisableIfFirstLoginNotNull,
                    BasedOn = Enum.Parse(typeof(MessageTemplate.BaseOnProperty), item.BasedOn.ToString()),
                    MinHoursBeforeSend = item.MinHoursBeforeSend,
                    ExactTime = item.ExactTime
                };

                mt.Recipients = new List<MessageRecipient>();

                result.Add(mt);
            }

            return result;
        }
    }

    public class MessageRecipient
    {
        public int Id { get; set; }
        public User.TypeEnum UserTypeId { get; set; }
        public string Name { get; set; }
        public bool Active { get; set; }

        public MessageRecipient()
        {
            Id = 0;
        }

        public static List<MessageRecipient> GetByMessageTemplateId(int messageTemplate)
        {
            return MapRecipients(SqlMapper.QuerySP<dynamic>("dbo.MessageRecipientsGetByMessageTemplateId",
                    new
                    {
                        @MessageTemplateId = messageTemplate
                    }));
        }

        public void Save(int messageTemplateId)
        {
            if (this.Id == 0)
            {
                DbSaveMessageRecipients(messageTemplateId, (int)this.UserTypeId);
            }
            else
            {
                DbUpdateMessageRecipients(messageTemplateId, (int)this.UserTypeId, this.Active);
            }
        }

        private void DbSaveMessageRecipients(int messageTemplateId, int userTypeId)
        {
            SqlMapper.QuerySP<dynamic>("dbo.MessageRecipientsInsert",
                new
                {
                    @MessageTemplateId = messageTemplateId,
                    @UserTypeId = userTypeId
                });
        }

        private void DbUpdateMessageRecipients(int MessageTemplateId, int userTypeId, bool active)
        {
            SqlMapper.ExecuteSP("dbo.MessageRecipientsUpdate",
                new
                {
                    @MessageTemplateId = MessageTemplateId,
                    @UserTypeId = userTypeId,
                    @Active = active
                });
        }

        private static List<MessageRecipient> MapRecipients(IEnumerable<dynamic> list)
        {
            var result = new List<MessageRecipient>();
            foreach (var item in list)
            {
                var recipient = new MessageRecipient()
                {
                    UserTypeId = Enum.Parse(typeof(User.TypeEnum), item.UserTypeId.ToString()),
                    Name = item.Name,
                    Active = item.Active
                };
                recipient.Id = (int)recipient.UserTypeId;
                result.Add(recipient);
            }
            return result;
        }       
    }
}
