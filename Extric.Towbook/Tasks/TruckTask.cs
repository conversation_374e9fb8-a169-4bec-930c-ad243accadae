using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Collections.ObjectModel;
using Extric.Towbook.Utility;
using System.Data.SqlClient;

namespace Extric.Towbook.Tasks
{
    public class TruckTask : ITask
    {
        #region Abstract Method Implementations

        public override void Save()
        {
            throw new NotImplementedException();
        }


        public  void GetByTaskId(int id)
        {
            throw new NotImplementedException();
        }

        public override void Delete(int userId)
        {
            throw new NotImplementedException();
        }

        #endregion

        public int TruckId { get; set; }

        public TruckTask()
        {
            this.Title = "Default Truck Task Title";
        }

        public Collection<TruckTask> GetByTruck(int truckId)
        {
            Collection<TruckTask> list = new Collection<TruckTask>();

            using (var sc = Core.GetConnection())
            {
                foreach (var x in sc.Query<TruckTask>("SELECT * FROM TruckTasks TT INNER JOIN Tasks T on T.TaskId=TT.TaskId WHERE TT.TruckId=@TruckId", new { TruckId = truckId }))
                {
                    list.Add(x);
                }
            }

            return list;
        }
    }
}
