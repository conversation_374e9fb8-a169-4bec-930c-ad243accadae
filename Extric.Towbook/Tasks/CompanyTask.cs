using Extric.Towbook.Utility;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Data.SqlClient;
using System.Linq;
using System.Threading.Tasks;

namespace Extric.Towbook.Tasks
{
    public class CompanyTask
    {
        public const int BasicTask = 1;
        public const int ImpoundTask = 2;
        public const int TruckTask = 3;
        public const int DriverTask = 4;
        public const int AccountTask = 5;

        /// <summary>
        /// Returns the list of tasks (not deleted) for the company, and if supplied, the owner user
        /// </summary>
        /// <param name="companyId"></param>
        /// <param name="userId"></param>
        /// <returns></returns>
        public static List<ITask> GetCurrentTasks(int companyId, int? userId = null)
        {
            List<ITask> tasks = new List<ITask>();

            var impoundTasks = Tasks.ImpoundTask.GetByCompanyId(companyId, userId).ToList();

            tasks.AddRange(impoundTasks);

            return tasks;
        }
    }
}

namespace Extric.Towbook
{
    public enum TaskPriority
    {
        Low = 0,
        Normal = 1,
        High = 2
    }

    public enum TaskStatus
    {
        NotStarted = 0,
        InProgress = 1,
        Completed = 2
    }

    public interface ITask
    {
        /// <summary>
        /// The Id used to refer to the task. You should use this ID in general to refer to tasks, not the task-specific ID (like ImpoundTaskId, for instance)
        /// </summary>
        int TaskId { get; set; }

        /// <summary>
        /// Persist the rich-task to the database.
        /// </summary>
        void Save();
    }

    public class TruckMaintenanceTask : ITask
    {

        public int TaskId
        {
            get
            {
                throw new NotImplementedException();
            }
            set
            {
                throw new NotImplementedException();
            }
        }

        public void Save()
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// Represents a Reminder assigned to an individual User, or Company.
    /// </summary>
    public class CompanyTask
    {
        public TaskPriority Priority { get; set; }
       
        public ITask TaskDetails { get; set; }

        public string Type
        {
            get
            {
                switch (TypeId)
                {
                    case 1: return "Regular";
                    case 2: return "Impound";
                    case 3: return "Truck Maintenance";
                }
                return "unknown";
            }
        }

        public CompanyTask()
        {
            Id = -1;
            Deleted = false;
            CreateDate = DateTime.Now;
            TypeId = 1;
        }

        public CompanyTask(SqlDataReader reader)
        {
            InitializeFromDataReader(reader);
        }

        public static CompanyTask GetById(int id)
        {
            using (SqlDataReader dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                "TasksGetById", new SqlParameter("@TaskId", id)))
            {
                if (dr.Read())
                {
                    return new CompanyTask(dr);
                }
                else
                {
                    return null;
                }
            }
        }

        public static async Task<CompanyTask> GetByIdAsync(int id)
        {
            using (var dr = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString,
                "TasksGetById", new SqlParameter("@TaskId", id)))
            {
                if (await dr.ReadAsync())
                {
                    return new CompanyTask(dr);
                }
                else
                {
                    return null;
                }
            }
        }

        public static async Task<IEnumerable<CompanyTask>> GetByIdsAsync(int[] taskIds)
        {
            List<CompanyTask> list = new List<CompanyTask>();

            if (taskIds.Length == 0)
                return list;

            foreach (var batch in taskIds.Batch(500))
            {
                var parameters = new List<string>();
                var p = new List<SqlParameter>();
                int i = 0;

                foreach (var row in batch)
                {
                    i++;
                    parameters.Add("@P" + i);
                    p.Add(new SqlParameter("@P" + i, row));
                }


                using (var dr = await SqlHelper.ExecuteReaderAsync(
                    Core.ConnectionString,
                    System.Data.CommandType.Text,
                    $"SELECT * FROM Tasks WITH (NOLOCK) WHERE TaskId IN ({string.Join(", ", parameters)})",
                    p.ToArray()))
                {
                    while (await dr.ReadAsync())
                    {
                        list.Add(new CompanyTask(dr));
                    }
                }
            }

            return list;
        }

        public static Collection<CompanyTask> GetByUser(User user, bool showCompany, bool showCompleted)
        {
            Collection<CompanyTask> entries = new Collection<CompanyTask>();

            Nullable<int> userId = null;

            if (user != null)
                userId = user.Id;

            using (SqlDataReader dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                "TasksGetByUserId",
                    new SqlParameter("@UserId", userId),
                    new SqlParameter("@ShowCompletedTasks", showCompleted),
                    new SqlParameter("@ShowCompanyTasks", showCompany)))
            {
                while (dr.Read())
                {
                    entries.Add(new CompanyTask(dr));
                }
            }

            return entries;
        }

        private void InitializeFromDataReader(SqlDataReader reader)
        {
            Id = reader.GetValue<int>("TaskId");
            CompanyId = reader.GetValue<int>("CompanyId");

            if (reader["ParentTaskId"] != DBNull.Value)
                ParentTaskId = Convert.ToInt32(reader["ParentTaskId"]);

            if (reader["OwnerUserId"] != DBNull.Value)
                OwnerUserId = Convert.ToInt32(reader["OwnerUserId"]);

            if (reader["Title"] != DBNull.Value)
                Title = Convert.ToString(reader["Title"]);

            if (reader["Details"] != DBNull.Value)
                Details = Convert.ToString(reader["Details"]);

            if (reader["Priority"] != DBNull.Value)
                Priority = (TaskPriority)Convert.ToInt32(reader["Priority"]);

            if (reader["DueDate"] != DBNull.Value)
                DueDate = Convert.ToDateTime(reader["DueDate"]);

            if (reader["CompletionDate"] != DBNull.Value)
                CompletionDate = Convert.ToDateTime(reader["CompletionDate"]);

            if (reader["Status"] != DBNull.Value)
                Status = (TaskStatus)Convert.ToInt32(reader["Status"]);

            if (reader["TypeId"] != DBNull.Value)
                TypeId = Convert.ToInt32(reader["TypeId"]);

            CreateDate = Convert.ToDateTime(reader["CreateDate"]);
            Deleted = Convert.ToBoolean(reader["Deleted"]);

            // Initialize User Mappings 
            //InitializeUserMappings();
        }

        private void InitializeUserMappings()
        {
            Collection<TaskUserMapping> entries = new Collection<TaskUserMapping>();

            using (SqlDataReader dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                "TaskUsersGetByTaskId",
                    new SqlParameter("@TaskId", Id)))
            {
                while (dr.Read())
                {
                    entries.Add(new TaskUserMapping(dr));
                }
            }
            Users = entries;
        }


        /// <summary>
        /// Deletes the task permanently.
        /// </summary>
        /// <param name="performer">User that is deleting the reminder</param>
        public void Delete(User performer)
        {
            SqlHelper.ExecuteNonQuery(Core.ConnectionString,
                "TasksDeleteById",
                new SqlParameter("@TaskId", Id));
        }

        public void UnDelete(User performer)
        {
            // TODO: Can be made async
            SqlHelper.ExecuteNonQuery(Core.ConnectionString,
                "TasksUndeleteById",
                new SqlParameter("@TaskId", Id));
        }

        /// <summary>
        /// Saves the Task object to the data store.
        /// </summary>
        public void Save()
        {
            if (Id == 0)
            {
                throw new Extric.Towbook.TowbookException("No such Task. Can't save " +
                    "object! (this object should have already been discarded!)");
            }

            if (CompanyId < 1 )
                throw new Extric.Towbook.TowbookException("Company must be set to a value before saving Task.");

            if (Id == -1)
            {
                DbInsert();
            }
            else
            {
                DbUpdate();
            }

            if (TaskDetails != null)
            {
                TaskDetails.TaskId = this.Id;
                TaskDetails.Save();
            }
        }


        #region Private Database Manipulation Functions

        private void DbInsert()
        {
            Nullable<int> userId = null;

            if (OwnerUserId != null)
                userId = OwnerUserId;

            Id = Convert.ToInt32(SqlHelper.ExecuteScalar(Core.ConnectionString,
                "TasksInsert",
                new SqlParameter("@CompanyId", CompanyId),
                new SqlParameter("@ParentTaskId", ParentTaskId),
                new SqlParameter("@OwnerUserId", OwnerUserId),
                new SqlParameter("@Title", Title),
                new SqlParameter("@Details", Details),
                new SqlParameter("@Priority", Priority),
                new SqlParameter("@DueDate", DueDate),
                new SqlParameter("@Status", Status),
                new SqlParameter("@TypeId", TypeId)));
        }

        private void DbUpdate()
        {
            Nullable<int> userId = null;

            if (OwnerUserId != null)
                userId = OwnerUserId;

            SqlHelper.ExecuteNonQuery(Core.ConnectionString,
                "TasksUpdateById",
                new SqlParameter("@TaskId", Id),
                new SqlParameter("@CompanyId", CompanyId),
                new SqlParameter("@ParentTaskId", ParentTaskId),
                new SqlParameter("@OwnerUserId", OwnerUserId),
                new SqlParameter("@Title", Title),
                new SqlParameter("@Details", Details),
                new SqlParameter("@Priority", Priority),
                new SqlParameter("@DueDate", DueDate),
                new SqlParameter("@Status", Status),
                new SqlParameter("@TypeId", TypeId),
                new SqlParameter("@CompletionDate", CompletionDate));

        }

        private void DbSaveUserMappings()
        {
            // TODO: implement this function!
            foreach (TaskUserMapping tu in Users)
            {
                tu.Save();
            }
        }

        #endregion

        #region Public Properties

        public int Id { get; private set; }
        public Collection<TaskUserMapping> Users { get; set; }
        public TaskStatus Status { get; set; }
        public string Details { get; set; }
        public int? OwnerUserId { get; set; }
        public int CompanyId { get; set; }
        public DateTime CreateDate { get; private set; }
        public DateTime? DueDate { get; set; }
        public DateTime? CompletionDate { get; set; }
        public bool Deleted { get; private set; }
        public string Title { get; set; }
        public int TypeId { get; set; }
        public int ParentTaskId { get; set; }
        #endregion



        public static CompanyTask NewImpoundTask()
        {
            CompanyTask t = new CompanyTask();

            //t.TaskDetails = new ImpoundTask();


            return t; 
        }
    }

    public class TaskUserMapping
    {
        private int _id; // -1 = not yet saved; 0 - object deleted, do not allow any further changes!
        private int? _userId;
        private int _taskId;
        private bool _completed = false;

        private bool _changed = false;

        public TaskUserMapping()
        {

        }

        public TaskUserMapping(SqlDataReader reader)
        {
            InitializeFromDataReader(reader);
        }

        private void InitializeFromDataReader(SqlDataReader reader)
        {
            _id = Convert.ToInt32(reader["TaskUserId"]);

            if (reader["UserId"] != DBNull.Value)
                _userId = Convert.ToInt32(reader["UserId"]);

            if (reader["TaskId"] != DBNull.Value)
                _taskId = Convert.ToInt32(reader["TaskId"]);

            _completed = Convert.ToBoolean(reader["Completed"]);
        }

        public void Save()
        {
            if (_id == 0)
                return; // Do nothing: this object has been deleted

            if (_changed)
            {
                // TODO: Not implemented!!!
                if (_id == -1)
                {
                    DbInsert();
                }
                else
                {
                    DbUpdate();
                }
            }
        }

        private void DbInsert()
        {
            _id = Convert.ToInt32(SqlHelper.ExecuteScalar(Core.ConnectionString,
                "TaskUsersInsert",
                new SqlParameter("@TaskId", _taskId),
                new SqlParameter("@UserId", _userId),
                new SqlParameter("@Completed", _completed)));
        }

        private void DbUpdate()
        {

            SqlHelper.ExecuteNonQuery(Core.ConnectionString,
                "TaskUsersUpdateById",
                new SqlParameter("@TaskUserId", _id),
                new SqlParameter("@TaskId", _taskId),
                new SqlParameter("@UserId", _userId),
                new SqlParameter("@Completed", _completed));

        }


        public void Delete()
        {
            SqlHelper.ExecuteNonQuery(Core.ConnectionString,
                "TaskUsersDeleteById",
                new SqlParameter("@TaskUserId", _id));

            _id = 0;

            // TODO: Record an action that this user was removed from the task? 
        }

        public int Id
        {
            get
            {
                return _id;
            }
        }

        public int? UserId
        {
            get
            {
                return _userId;
            }
            set
            {
                if (_userId != value)
                {
                    _userId = value;
                    _changed = true;
                }
            }
        }

        public int TaskId
        {
            get
            {
                return _taskId;
            }
            set
            {
                if (_taskId != value)
                {
                    _taskId = value;
                    _changed = true;
                }
            }
        }

        public bool Completed
        {
            get
            {
                return _completed;
            }
            set
            {
                if (_completed != value)
                {
                    _completed = value;
                    _changed = true;
                }
            }
        }
    }
}
