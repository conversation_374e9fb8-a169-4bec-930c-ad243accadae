using System;
using System.Collections.Generic;
using System.Linq;
using Extric.Towbook.Impounds;
using System.Collections.ObjectModel;
using Extric.Towbook.Utility;
using System.Data;
using System.Threading.Tasks;

namespace Extric.Towbook.Tasks
{
    public class ImpoundTask : ITask
    {
        public int ImpoundTaskId { get; set; }
        public int ImpoundId { get; set; }
        public int? ImpoundReminderDefinitionId { get; set; } 

        public ImpoundTask()
        {
            this.Title = "Default Impound Task Title";
        }

        /// <summary>
        /// Deletes the ImpoundTask.
        /// </summary>
        /// <param name="userId">The user deleting the Task</param>
        public override void Delete(int userId)
        {
            SqlMapper.ExecuteSP("ImpoundTasksDeleteById",
                new { ImpoundTaskId = this.ImpoundTaskId, UserId = userId });

            var th = TaskHistory.GetByActionId(this.TaskId, TaskEvent.Delete.Id);
            if(th == null)
            {
                th = new TaskHistory()
                {
                    TaskId = TaskId,
                    ActionId = TaskEvent.Delete.Id,
                    CreateDate = DateTime.Now,
                    OwnerUserId = userId
                };

                th.Save();
            }

            this.Deleted = true;
        }

        public override void Save()
        {
            this.TypeId = CompanyTask.ImpoundTask;

            if (ImpoundTaskId == 0)
            {
                var retVal = SqlMapper.QuerySP("ImpoundTasksInsert",
                    new
                    {
                        ImpoundId = this.ImpoundId,
                        CompanyId = this.CompanyId,
                        TypeId = this.TypeId,
                        Title = this.Title,
                        Details = this.Details,
                        DueDate = this.DueDate,
                        OwnerUserId = this.OwnerUserId,
                        ImpoundReminderDefinitionId = this.ImpoundReminderDefinitionId
                    }).First();

                this.TaskId = retVal.TaskId;
                this.ImpoundTaskId = retVal.ImpoundTaskId;
            }
            else
            {
                SqlMapper.ExecuteSP("ImpoundTasksUpdateById",
                    new
                    {
                        ImpoundTaskId = this.ImpoundTaskId,
                        TaskId = this.TaskId,
                        ImpoundId = this.ImpoundId,
                        CompanyId = this.CompanyId,
                        TypeId = this.TypeId,
                        Title = this.Title,
                        Details = this.Details,
                        DueDate = this.DueDate,
                        OwnerUserId = this.OwnerUserId,
                        ImpoundReminderDefinitionId = this.ImpoundReminderDefinitionId
                    });
            }

            if (Reminders.Count > 0)
            {
                foreach (var reminder in this.Reminders)
                {
                    if (reminder.TaskId == 0)
                        reminder.TaskId = this.TaskId;

                    reminder.Save();
                }
            }
        }

        /// <summary>
        /// Retrieves all ImpoundTasks associated with the specified Impound. Async version
        /// </summary>
        /// <param name="impoundId"></param>
        /// <returns></returns>
        public static async Task<Collection<ImpoundTask>> GetByImpoundAsync(int impoundId)
        {
            return (await SqlMapper
                .QueryAsync<ImpoundTask>(
                    "SELECT * FROM ImpoundTasks IT WITH (nolock) INNER JOIN Tasks T WITH (nolock) on T.TaskId=IT.TaskId and T.Deleted=0 WHERE IT.ImpoundId=@ImpoundId",
                    new { ImpoundId = impoundId }))
                .ToCollection();
            
        }

        public static async Task<Collection<ImpoundTask>> GetByImpoundsAsync(IEnumerable<int> impoundIds)
        {
            var list = new Collection<ImpoundTask>();

            if (impoundIds.Count() == 0)
                return list;

            foreach (var batchIds in impoundIds.Batch(500))
            {
                var dbSet = await SqlMapper.QueryAsync<ImpoundTask>(
                    "SELECT * FROM ImpoundTasks IT WITH (nolock) INNER JOIN Tasks T WITH (nolock) on T.TaskId=IT.TaskId and T.Deleted=0 WHERE IT.ImpoundId IN @ImpoundId",
                    new { ImpoundId = batchIds });
                foreach (var x in dbSet)
                {
                    list.Add(x);
                }
            }

            return list;
        }

        /// <summary>
        /// Retrieves an ImpoundTask by its TaskId
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public static ImpoundTask GetByTaskId(int taskId)
        {
            var result = SqlMapper.QuerySP<ImpoundTask>("ImpoundTasksGetByTaskId", 
                new { TaskId = taskId }).FirstOrDefault();

            return result;
        }

        /// <summary>
        /// Retrieve all impound tasks for a specfiic company. Optionally pass in the OwnerUserId to filter by user.
        /// </summary>
        /// <param name="companyId"></param>
        /// <param name="ownerUserId"></param>
        /// <returns></returns>
        public static Collection<ImpoundTask> GetByCompanyId(int companyId, int? ownerUserId = null)
        {
            var result = SqlMapper.QuerySP<ImpoundTask>("ImpoundTasksGetByCompanyId",
                new { CompanyId = companyId }).ToCollection();

            return result;
        }

        /// <summary>
        /// Retrieves an ImpoundTask by the ImpoundTaskId. This is NOT the same as TaskId!
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public static ImpoundTask GetByImpoundTaskId(int impoundTaskId)
        {
            return SqlMapper.QuerySP<ImpoundTask>("ImpoundTasksGetByImpoundTaskId",
                new { ImpoundTaskId = impoundTaskId }).FirstOrDefault();
        }

        /// <summary>
        /// Retrieves an ImpoundTask by the ImpoundTaskId. This is NOT the same as TaskId!
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public static async Task<ImpoundTask> GetByImpoundTaskIdAsync(int impoundTaskId)
        {
            return (await SqlMapper.QuerySpAsync<ImpoundTask>("ImpoundTasksGetByImpoundTaskId",
                new { ImpoundTaskId = impoundTaskId })).FirstOrDefault();
        }

        /// <summary>
        /// Helper method to create all the templated/reminder definition tasks for an impound.
        /// This method is safe to call multiple times -- it won't insert duplicates.
        /// </summary>
        /// <param name="impoundId">ImpoundId to create the tasks for</param>
        /// <param name="companyId">CompanyId that the impound belongs to</param>
        /// <returns></returns>
        public static async Task<int> CreateTasksForImpoundAsync(int impoundId, int companyId)
        {
            // TODO: Can be made async
            Impound impound = Impound.GetById(impoundId);
            
            int counter = 0;

            if (impound == null || impound.ImpoundDate == null)
                return 0;

            if (impound != null)
            {
                int typeId = 0;

                if (impound.ImpoundType != null && impound.ImpoundDate != null)
                {
                    typeId = (int)impound.ImpoundType;
                }

                // don't create tasks for impounds that are already released.
                if (impound.ReleaseDate != null)
                    return 0;

                Collection<ReminderItem> reminderItems = await ReminderItem.GetByImpoundTypeIdAsync(typeId, companyId, false);

                var existingTasks = await ImpoundTask.GetByImpoundAsync(impoundId);

                foreach (ReminderItem reminderItem in reminderItems)
                {
                    if (reminderItem == null)
                        continue;

                    if (existingTasks.Where(o => o.ImpoundReminderDefinitionId == reminderItem.Id).Any())
                        continue;

                    var reminderDate = impound.ImpoundDate.Value.AddDays(reminderItem.Days);

                    ImpoundTask it = new ImpoundTask()
                    {
                        ImpoundId = impound.Id,
                        ImpoundReminderDefinitionId = reminderItem.Id,
                        CompanyId = companyId,
                        Title = reminderItem.Title,
                        Details = reminderItem.Description,
                        DueDate = reminderDate,
                    };
                    it.Reminders.Add(new TaskReminder(reminderDate));
                    
                    // TODO: Can be made async, and this is not performant (n database/network calls on a loop)
                    it.Save();

                    counter++;
                }
            }

            return counter;
        }
    }
}
