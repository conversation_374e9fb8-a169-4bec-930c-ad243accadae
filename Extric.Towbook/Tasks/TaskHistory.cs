using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Extric.Towbook.Utility;

namespace Extric.Towbook.Tasks
{
    [Table("TaskHistory")]
    public class TaskHistory
    {
        [Key("TaskHistoryId")]
        public int Id { get; set; }
        public int TaskId { get; set; }
        public int ActionId { get;  set; }
        public DateTime CreateDate { get; set; }
        public int OwnerUserId { get; set; }
        public bool? Deleted { get; set; }
        public int? DeletedByUserId { get; set; }
        public DateTime? DeletedDate { get; set; }

        public static IEnumerable<TaskHistory> GetByTaskId(int taskId)
        {
            return GetByTaskIds(new int[] { taskId });
        }

        public static IEnumerable<TaskHistory> GetByTaskIds(int[] taskIds)
        {
            if (taskIds == null || taskIds.Count() == 0)
                return new TaskHistory[0];

            return SqlMapper.Query<TaskHistory>(
                    "SELECT * FROM TaskHistory WITH (NOLOCK) WHERE TaskId in @TaskIds", 
                    new { 
                        TaskIds = taskIds
                    });
        }

        public static async Task<IEnumerable<TaskHistory>> GetByTaskIdsAsync(int[] taskIds)
        {
            if (taskIds == null || taskIds.Count() == 0)
                return new TaskHistory[0];

            return await SqlMapper.QueryAsync<TaskHistory>(
                    "SELECT * FROM TaskHistory WITH (NOLOCK) WHERE TaskId in @TaskIds",
                    new
                    {
                        TaskIds = taskIds
                    });
        }

        /// <summary>
        /// Get the last active history item that hasn't been deleted (reversed).
        /// </summary>
        public static TaskHistory GetByActionId(int taskId, int actionId)
        {
            return GetByTaskId(taskId)
                .LastOrDefault(w => 
                    w.Deleted.GetValueOrDefault() == false && 
                    w.ActionId == actionId);
        }

        public void Save()
        {
            if (Id <= 0)
            {
                Id = (int)SqlMapper.Insert(this);
            }
            else
            {
                SqlMapper.Update(this);
            }
        }
    }
}
