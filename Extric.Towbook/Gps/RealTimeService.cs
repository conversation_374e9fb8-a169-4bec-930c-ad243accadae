using System;
using System.Linq;
using System.Threading.Tasks;
using Extric.Towbook.Integration;

namespace Extric.Towbook.Gps
{
    /// <summary>
    /// <PERSON>les delivering real-time updates for GPS location updates
    /// </summary>
    public class RealTimeService
    {
        public static async Task DeliverPusher(int companyId, TruckLocationHistoryItem e)
        {
            await PushNotificationProvider.Push(
                GetChannelForGps(companyId),
                "truck_location_updated",
                e, true, true);
            
            await ConvertTruckToLocationEvent(companyId, e);

            Console.WriteLine("sent pusher event to " + GetChannelForGps(companyId));
        }

        /// <summary>
        /// Converts a truck location event to a User location event so it will show on our map
        /// Only works if the truck is assigned to a driver and the driver is linked to a user.
        /// </summary>
        private static async Task ConvertTruckToLocationEvent(int companyId, TruckLocationHistoryItem f)
        {
            var defaultTruck = DriverTruckDefault.GetByTruckId(f.TruckId).FirstOrDefault();

            if (defaultTruck != null)
            {
                var userId = (await Driver.GetByIdAsync(defaultTruck.DriverId))?.UserId;
                if (userId != null)
                {
                    await Core.SetRedisValueAsync("gpsDevice:" + userId, "1", TimeSpan.FromSeconds(60));
                    await PushNotificationProvider.Push(
                        GetChannelForGps(companyId),
                        "user_location_updated",
                        new {
                            Id = (int)-f.Id,
                            TruckId = f.TruckId,
                            UserId = userId.Value,
                            Latitude = f.Latitude,
                            Longitude = f.Longitude,
                            Timestamp = f.Timestamp,
                            GpsSource = f.GetGpsSourceName(),
                            Speed = f.Speed,
                            Status = f.Status
                        }, true, true);

                    Console.WriteLine("sent pusher event to " + GetChannelForGps(companyId));
                }
            }
        }
        
        public static async Task DeliverPusher(Company.Company company, UserLocationHistoryItem e)
        {
            var timestamp = Core.OffsetDateTime(company, e.Timestamp, true);
            if (timestamp > DateTime.Now.AddSeconds(-300))
            {
                await PushNotificationProvider.Push(
                    GetChannelForGps(company.Id),
                    "user_location_updated",
                    e, true, true);

                Console.WriteLine("sent pusher event to " + GetChannelForGps(company.Id));
            }
        }

        public static string GetChannelForGps(int companyId)
        {
            return $"private-gps-{companyId}";
        }
    }
}
