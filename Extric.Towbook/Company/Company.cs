using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Data.SqlClient;
using System.Data;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel;
using Glav.CacheAdapter.Core.DependencyInjection;
using ProtoBuf;
using System.Linq;
using Extric.Towbook.Integration;
using Extric.Towbook.Utility;
using Extric.Towbook.Impounds;
using Newtonsoft.Json;

using Async = System.Threading.Tasks;
using System.Threading.Tasks;
using Extric.Towbook.Generated;
using Dapper;

namespace Extric.Towbook.Company
{
    /// <summary>
    /// Represents a Towing Company account; all data associated with a Towbook Client/Towing Company
    /// is mapped through this object.
    /// </summary>
    /// 
    [Serializable]
    [ProtoContract(ImplicitFields = ImplicitFields.AllFields, Name = "Company")]
    [CacheKey("_companies")]
    public partial class Company
    {
        private const int CacheTimeout = 60;

        public enum CompanyCountry
        {
            USA = 1,
            Canada = 2,
            Australia = 3,
            UK = 4,
            NewZealand = 5,
            SouthAfrica = 6,
            UnitedArabEmirates = 7,
            TrinidadAndTobago = 8
        }

        public enum AccountingMethodEnum
        {
            Cash = 0,
            Accrual = 1
        }

        public enum CompanyType
        {
            TowingCompany = 1,
            LawEnforcement = 3,
            AnsweringService = 4,
        }

        public enum TaxModeEnum
        {
            /// <summary>
            /// Charge a single tax rate, specified on this object.
            /// </summary>
            Single = 0,

            /// <summary>
            /// Charge multiple tax rates, using the Sales Tax object.
            /// </summary>
            Multiple = 1
        }

        public enum InvoicingSystemEnum
        {
            None = 0,
            Towbook = 1,
            Quickbooks = 2,
        }

        private int _id = -1;
        private string _name;

        private CompanyCountry _country = CompanyCountry.USA;
        private AccountingMethodEnum _accountingMethod;
        private InvoicingSystemEnum _invoicingSystemId;

        private string _invoicingTagline;

        private string _address;
        private string _city;
        private string _state;
        private string _zip;
        /*private string _mailingAddress;
        private string _mailingCity;
        private string _mailingState;
        private string _mailingZip;
        */

        private string _phone;
        private string _fax;
        private string _email;
        private string _website;
        private float _timezoneOffset;
        private bool _timezoneUseDST;

        private TaxModeEnum _taxMode;

        private decimal _invoicingLateFlatAmount;
        private decimal _invoicingLateAPR;
        private int _invoicingLateGracePeriod;

        [CsvExportable("CompanyId")]
        [PartitionKey]
        public int Id
        {
            get => _id;
            set => _id = value;
        }

        private string _news;
        public string News
        {
            get => _news;
            set => _news = value;
        }

        public int VinRequiredLength { get; set; }

        public decimal StandardDriverCommission
        {
            get;
            set;
        }

        [CsvExportable("CompanyType")]
        public CompanyType Type
        {
            get;
            set;
        }

        public TaxModeEnum TaxMode
        {
            get { return _taxMode; }
            set { _taxMode = value; }
        }
        [JsonIgnore]
        public string LocaleStateName
        {
            get
            {
                switch (_country)
                {
                    case CompanyCountry.USA:
                        return "State";

                    case CompanyCountry.Canada:
                        return "Province";
                }

                return "Province";
            }
        }

        [JsonIgnore]
        public string LocaleMile
        {
            get
            {
                switch (_country)
                {
                    case CompanyCountry.USA:
                        return "mile";
                    case CompanyCountry.Canada:
                        return "kilometer";
                }

                return "kilometer";
            }
        }

        /// <summary>
        /// Use this instead of using the name gallon or liter.
        /// </summary>
        /// [JsonIgnore]
        public string LocaleVolume
        {
            get
            {
                switch (_country)
                {
                    case CompanyCountry.USA:
                    case CompanyCountry.UK:
                        return "gallon";

                    case CompanyCountry.Canada:
                        return "liter";
                }

                return "liter";
            }
        }

        [JsonIgnore]
        public string LocaleZipCode
        {
            get
            {
                switch (_country)
                {
                    case CompanyCountry.USA:
                        return "Zip";

                    case CompanyCountry.Canada:
                        return "Postal Code";
                }

                return "Postal Code";
            }
        }

        [JsonIgnore]
        public List<Driver> Drivers
        {
            get
            {
                if (_id > 0)
                    return Driver.GetByCompany(this);
                else
                    return null;
            }
        }

        [JsonIgnore]
        public List<Truck> Trucks
        {
            get
            {
                if (_id > 0)
                    return Truck.GetByCompany(this);
                else
                    return null;
            }
        }

        [JsonIgnore]
        public Collection<Extric.Towbook.Company.Note> Notes
        {
            get
            {
                return Note.GetByCompany(this);
            }
        }
        [JsonIgnore]
        /// <summary>
        /// Returns a read-only list of tax rates for this company
        /// </summary>
        public Collection<Extric.Towbook.TaxRate> TaxRates
        {
            get => TaxRate.GetByCompany(this);
        }

        [CsvExportable("CompanyCreateDate")]
        public DateTime CreateDate
        {
            get; set;
        }

        [CsvExportable("CompanyCountry")]
        public CompanyCountry Country
        {
            get { return _country; }
            set { _country = value; }
        }
        [DisplayName("Time Zone")]
        public float TimezoneOffset
        {
            get
            {
                return _timezoneOffset;
            }
            set
            {
                _timezoneOffset = value;
            }
        }

        public bool TimezoneUseDST
        {
            get
            {
                // TODO: need to change how we handle timezone offsetting. 
                if (this.Country == CompanyCountry.Australia)
                {
                    if (State != null && State.ToLowerInvariant().StartsWith("vic"))
                    {
                        var tz = TimeZoneInfo.FindSystemTimeZoneById("AUS Eastern Standard Time");
                        return (tz.IsDaylightSavingTime(DateTime.Now));
                    }
                }
                if ((this._state == "HI" && this.TimezoneOffset == -5) ||
                    (this._state == "AZ" && !_timezoneUseDST) ||
                    this._state?.ToLowerInvariant() == "selangor" ||
                    (this.Country == CompanyCountry.SouthAfrica && !_timezoneUseDST))
                    return false;

                return _timezoneUseDST;
            }
            set
            {
                _timezoneUseDST = value;
            }
        }

        [ProtoIgnore]
        private DispatchStatistics currentDispatchStatistics;

        [JsonIgnore]
        public DispatchStatistics CurrentDispatchStatistics
        {
            get
            {
                if (currentDispatchStatistics == null)
                    currentDispatchStatistics = new DispatchStatistics(this);

                return currentDispatchStatistics;
            }
        }

        public AccountingMethodEnum AccountingMethod
        {
            get
            {
                return _accountingMethod;
            }
            set
            {
                _accountingMethod = value;
            }
        }

        public InvoicingSystemEnum InvoicingSystem
        {
            get
            {
                return _invoicingSystemId;
            }
            set
            {
                _invoicingSystemId = value;
            }
        }

        public string InvoicingTagline
        {
            get { return _invoicingTagline; }
            set { _invoicingTagline = value; }
        }

        public decimal InvoicingLateFlatAmount
        {
            get { return _invoicingLateFlatAmount; }
            set { _invoicingLateFlatAmount = value; }
        }

        public decimal InvoicingLateAPR
        {
            get { return _invoicingLateAPR; }
            set { _invoicingLateAPR = value; }
        }

        public int InvoicingLateGracePeriod
        {
            get { return _invoicingLateGracePeriod; }
            set { _invoicingLateGracePeriod = value; }
        }


        [NonSerialized]
        private StorageRate _storageRate;

        [JsonIgnore]
        public StorageRate StorageRate
        {
            get
            {
                _storageRate = null;

                if (_storageRate == null)
                {
                    _storageRate = StorageRate.GetByCompanyId(this.Id);
                    if (_storageRate == null)
                    {
                        _storageRate = new StorageRate();
                        _storageRate.CompanyId = this.Id;
                        _storageRate.StorageRoundingValue = 1;
                    }
                }

                return _storageRate;
            }
        }

        [Required()]
        [CsvExportable("CompanyName")]
        public string Name
        {
            get => _name?.Replace("\u2019", "'") ?? string.Empty;
            set => _name = value;
        }

        public string ShortName { get; set; }

        [Required()]
        [CsvExportable("CompanyAddress")]
        public string Address
        {
            get => _address ?? string.Empty;
            set => _address = value;
        }

        private class StateConfig
        {
            public string Long { get; set; }
            public string Short { get; set; }
            public float? TimezoneOffset { get; set; } = null;
            public bool? DST { get; set; } = true;

            public override int GetHashCode()
            {
                if (Long == null || Short == null)
                {
                    return base.GetHashCode();
                }
                else
                {
                    return Long.GetHashCode() + Short.GetHashCode();
                }
            }

        }

        private static StateConfig[] StatesUSA
        {
            get
            {
                return new StateConfig[] {
                    new StateConfig() {  Long = "Alabama", Short = "AL" },
                    new StateConfig() {  Long = "Alaska", Short = "AK", DST = true, TimezoneOffset = -4 },
                    new StateConfig() {  Long = "Arizona", Short = "AZ", DST = false, TimezoneOffset = -2 },
                    new StateConfig() {  Long = "Arkansas", Short = "AR" },
                    new StateConfig() {  Long = "California", Short = "CA", DST = true, TimezoneOffset = -3 },
                    new StateConfig() {  Long = "Colorado", Short = "CO", DST = true, TimezoneOffset = -2 },
                    new StateConfig() {  Long = "Connecticut", Short = "CT" },
                    new StateConfig() {  Long = "Delaware", Short = "DE" },
                    new StateConfig() {  Long = "District of Columbia", Short = "DC" },
                    new StateConfig() {  Long = "Florida", Short = "FL" },
                    new StateConfig() {  Long = "Georgia", Short = "GA" },
                    new StateConfig() {  Long = "Hawaii", Short = "HI", TimezoneOffset = -5, DST = false },
                    new StateConfig() {  Long = "Idaho", Short = "ID" },
                    new StateConfig() {  Long = "Illinois", Short = "IL", TimezoneOffset = -1, DST = true },
                    new StateConfig() {  Long = "Indiana", Short = "IN" },
                    new StateConfig() {  Long = "Iowa", Short = "IA", TimezoneOffset = -1, DST = true },
                    new StateConfig() {  Long = "Kansas", Short = "KS", TimezoneOffset = -1, DST = true },
                    new StateConfig() {  Long = "Kentucky", Short = "KY" },
                    new StateConfig() {  Long = "Louisiana", Short = "LA" },
                    new StateConfig() {  Long = "Maine", Short = "ME" },
                    new StateConfig() {  Long = "Montana", Short = "MT" },
                    new StateConfig() {  Long = "Nebraska", Short = "NE" },
                    new StateConfig() {  Long = "Nevada", Short = "NV", TimezoneOffset = -3, DST = true },
                    new StateConfig() {  Long = "New Hampshire", Short = "NH" },
                    new StateConfig() {  Long = "New Jersey", Short = "NJ" },
                    new StateConfig() {  Long = "New Mexico", Short = "NM", TimezoneOffset = -2, DST = true},
                    new StateConfig() {  Long = "New York", Short = "NY" },
                    new StateConfig() {  Long = "North Carolina", Short = "NC" },
                    new StateConfig() {  Long = "North Dakota", Short = "ND" },
                    new StateConfig() {  Long = "Ohio", Short = "OH" },
                    new StateConfig() {  Long = "Oklahoma", Short = "OK", TimezoneOffset = -1, DST = true },
                    new StateConfig() {  Long = "Oregon", Short = "OR", TimezoneOffset = -3, DST = true },
                    new StateConfig() {  Long = "Maryland", Short = "MD" },
                    new StateConfig() {  Long = "Massachusetts", Short = "MA" },
                    new StateConfig() {  Long = "Michigan", Short = "MI" },
                    new StateConfig() {  Long = "Minnesota", Short = "MN", TimezoneOffset = -1, DST = true },
                    new StateConfig() {  Long = "Mississippi", Short = "MS" },
                    new StateConfig() {  Long = "Missouri", Short = "MO" },
                    new StateConfig() {  Long = "Pennsylvania", Short = "PA" },
                    new StateConfig() {  Long = "Rhode Island", Short = "RI" },
                    new StateConfig() {  Long = "South Carolina", Short = "SC" },
                    new StateConfig() {  Long = "South Dakota", Short = "SD" },
                    new StateConfig() {  Long = "Tennessee", Short = "TN" },
                    new StateConfig() {  Long = "Texas", Short = "TX", TimezoneOffset = -1, DST = true },
                    new StateConfig() {  Long = "Utah", Short = "UT", TimezoneOffset = -2, DST = true },
                    new StateConfig() {  Long = "Vermont", Short = "VT" },
                    new StateConfig() {  Long = "Virginia", Short = "VA" },
                    new StateConfig() {  Long = "Washington", Short = "WA", DST = true, TimezoneOffset = -3 },
                    new StateConfig() {  Long = "West Virginia", Short = "WV" },
                    new StateConfig() {  Long = "Wisconsin", Short = "WI", DST = true, TimezoneOffset = -1 },
                    new StateConfig() {  Long = "Wyoming", Short = "WY" },
                    new StateConfig() {  Long = "Mexico", Short = "MEX" },
                    new StateConfig() {  Long = "Alberta", Short = "AB", DST = true, TimezoneOffset = -2 },
                    new StateConfig() {  Long = "British Columbia", Short = "BC" }, // BC has people in PST and MST.
                    new StateConfig() {  Long = "Manitoba", Short = "MB",  },
                    new StateConfig() {  Long = "New Brunswick", Short = "NB" },
                    new StateConfig() {  Long = "Newfoundland and Labrador", Short = "NL", TimezoneOffset = 1.5f },
                    new StateConfig() {  Long = "Nova Scotia", Short = "NS" },
                    new StateConfig() {  Long = "Northwest Territories", Short = "NT",  },
                    new StateConfig() {  Long = "Nunavut",  Short = "NU", },
                    new StateConfig() {  Long = "Ontario", Short = "ON",  },
                    new StateConfig() {  Long = "Prince Edward Island", Short = "PE", },
                    new StateConfig() {  Long = "Quebec", Short = "QC",  },
                    new StateConfig() {  Long = "Saskatchewan",  Short = "SK",},
                    new StateConfig() {  Long = "Yukon", Short = "YT",  }
                };
            }
        }

        private static StateConfig[] StatesCanada
        {
            get
            {
                return new StateConfig[] {
                    new StateConfig() {  Long = "Alberta", Short = "AB", DST = true, TimezoneOffset = -2 },
                    new StateConfig() {  Long = "British Columbia", Short = "BC" },
                    new StateConfig() {  Long = "Manitoba", Short = "MB", TimezoneOffset = -1, DST = true  },
                    new StateConfig() {  Long = "New Brunswick", Short = "NB", TimezoneOffset = 1 },
                    new StateConfig() {  Long = "Newfoundland and Labrador", Short = "NL", TimezoneOffset = 1.5f },
                    new StateConfig() {  Long = "Nova Scotia", Short = "NS" },
                    new StateConfig() {  Long = "Northwest Territories", Short = "NT",  },
                    new StateConfig() {  Long = "Nunavut",  Short = "NU", },
                    new StateConfig() {  Long = "Ontario", Short = "ON",  },
                    new StateConfig() {  Long = "Prince Edward Island", Short = "PE", },
                    new StateConfig() {  Long = "Quebec", Short = "QC",  },
                    new StateConfig() {  Long = "Saskatchewan",  Short = "SK" },
                    new StateConfig() {  Long = "Yukon", Short = "YT",  TimezoneOffset = -3  }
                };
            }
        }

        private static StateConfig[] StatesAustralia
        {
            get
            {
                return new[] {
                    new StateConfig() {  Long = "Queensland", Short = "Qld" },
                    new StateConfig() {  Long = "South Australia", Short = "SA" },
                    new StateConfig() {  Long = "Tasmania", Short = "Tas" },
                    new StateConfig() {  Long = "Victoria", Short = "Vic" },
                    new StateConfig() {  Long = "Western Australia", Short = "WA" },
                    new StateConfig() {  Long = "Northern Territory", Short = "NT" },
                    new StateConfig() {  Long = "New South Wales", Short = "NSW" },
                    new StateConfig() {  Long = "Australian Capital Territory", Short = "ACT" }
                };
            }
        }

        private static StateConfig[] StatesUAE
        {
            get
            {
                return new[] {
                    new StateConfig() {  Long = "Umm al Qaywayn", Short = "Umm al Qaywayn", TimezoneOffset = 4, DST = false },
                    new StateConfig() {  Long = "Ra's al Khaymah", Short = "Ra's al Khaymah", TimezoneOffset = 4, DST = false },
                    new StateConfig() {  Long = "Dubai", Short = "Dubai", TimezoneOffset = 4, DST = false },
                    new StateConfig() {  Long = "Ash Shariqah", Short = "Ash Shariqah", TimezoneOffset = 4, DST = false },
                    new StateConfig() {  Long = "Al Fujayrah", Short = "Al Fujayrah", TimezoneOffset = 4, DST = false },
                    new StateConfig() {  Long = "`Ajman", Short = "Ajman", TimezoneOffset = 4, DST = false },
                    new StateConfig() {  Long = "Abu Zaby", Short = "Abu Zaby", TimezoneOffset = 4, DST = false },
                    new StateConfig() {  Long = "Australian Capital Territory", Short = "ACT", TimezoneOffset = 4, DST = false }
                };
            }
        }


        [Required()]
        [CsvExportable("CompanyState")]
        public string State
        {
            get => _state ?? string.Empty;
            set
            {
                _state = value;

                if (_state != null)
                {
                    _state = _state.Trim();

                    StateConfig sc = null;
                    switch (this.Country)
                    {
                        case CompanyCountry.USA:
                            sc = StatesUSA.Where(o =>
                                o.Long.ToLowerInvariant() == _state.ToLower() ||
                                o.Short.ToLowerInvariant() == _state.ToLowerInvariant()).FirstOrDefault();
                            break;
                        case CompanyCountry.Canada:
                            sc = StatesCanada.Where(o =>
                                o.Long.ToLowerInvariant() == _state.ToLower() ||
                                o.Short.ToLowerInvariant() == _state.ToLowerInvariant()).FirstOrDefault();
                            break;
                        case CompanyCountry.Australia:
                            sc = StatesAustralia.Where(o =>
                                o.Long.ToLowerInvariant() == _state.ToLower() ||
                                o.Short.ToLowerInvariant() == _state.ToLowerInvariant()).FirstOrDefault();
                            break;
                        case CompanyCountry.UnitedArabEmirates:
                            sc = StatesUAE.Where(o => o.Long.ToLowerInvariant() == _state.ToLower()).FirstOrDefault();
                            break;
                    }

                    if (sc != null)
                    {
                        _state = sc.Short;

                        if (sc.DST != null)
                            TimezoneUseDST = sc.DST.Value;

                        if (sc.TimezoneOffset != null)
                            TimezoneOffset = sc.TimezoneOffset.Value;
                    }

                    if (_state.Length == 2)
                        _state = _state.ToUpperInvariant();
                }
            }
        }

        [Required()]
        [CsvExportable("CompanyCity")]
        public string City
        {
            get => _city ?? string.Empty;
            set => _city = value;
        }

        [CsvExportable("CompanyZip")]
        public string Zip
        {
            get => _zip ?? string.Empty;
            set => _zip = value;
        }

        [CsvExportable("CompanyPhone")]
        public string Phone
        {
            get => _phone ?? string.Empty;
            set
            {
                if (value != null)
                {
                    _phone = Core.FormatPhoneWithDashesOnly(value);
                }
                else
                {
                    _phone = value;
                }
            }
        }

        [CsvExportable("CompanyFax")]
        public string Fax
        {
            get => _fax ?? string.Empty;
            set => _fax = value;
        }

        [CsvExportable("CompanyEmail")]
        public string Email
        {
            get => _email ?? string.Empty;
            set => _email = value;
        }

        [CsvExportable("CompanyWebsite")]
        public string Website
        {
            get
            {
                return _website;
            }
            set
            {
                _website = value;
            }
        }

        public decimal Latitude { get; set; }
        public decimal Longitude { get; set; }

        /*************************************************/
        #region Additional Properties for Account Reports
        [JsonIgnore]
        public List<User> Users
        {
            get
            {
                if (_id > 0)
                    return User.GetByCompanyId(this.Id).ToList();
                else
                    return null;
            }
        }

        private User firstUser;
        [CsvExportable("CompanyFirstUser")]
        [JsonIgnore]
        public User FirstUser
        {
            get
            {
                return firstUser;
            }
            protected set
            {
                firstUser = value;
            }
        }

        [JsonIgnore]
        [CsvExportable("CompanyStatus")]
        public string Status { get; set; }


        [JsonIgnore]
        [CsvExportable("CompanyTruckCount")]
        public int? TruckCount { get; set; }
        [JsonIgnore]
        [CsvExportable("CompanyUserCount")]
        public int? UserCount { get; set; }
        [JsonIgnore]
        [CsvExportable("CompanyDriverCount")]
        public int? DriverCount { get; set; }
        [JsonIgnore]
        [CsvExportable("CompanyRateItemCount")]
        public int? RateItemCount { get; set; }
        [JsonIgnore]
        [CsvExportable("CompanyCallCountForever")]
        public int? CallCountForever { get; set; }

        [JsonIgnore]
        [CsvExportable("CompanyCallCount365Days")]
        public int? CallCount365 { get; set; }

        [ProtoIgnore]
        [JsonIgnore]
        [CsvExportable("CompanyCallCount180Days")]
        public int? CallCount180 { get; set; }
        [ProtoIgnore]
        [JsonIgnore]
        [CsvExportable("CompanyCallCount90Days")]
        public int? CallCount90 { get; set; }

        [JsonIgnore]
        [CsvExportable("CompanyCallCount60days")]
        public int? CallCount60 { get; set; }
        [JsonIgnore]
        [CsvExportable("CompanyCallCount30Days")]
        public int? CallCount30 { get; set; }
        [JsonIgnore]
        [CsvExportable("CompanyFirstLoginDate")]
        public DateTime? FirstLogin { get; set; }
        [JsonIgnore]
        [CsvExportable("CompanyLastLoginDate")]
        public DateTime? LastLogin { get; set; }
        [JsonIgnore]
        [CsvExportable("CompanyDaysLeftInTrial")]
        public int? DaysLeft
        {
            get
            {
                if (this.MonthlyFee <= 0)
                {
                    var createDateLocalTime = this.CreateDate.ToLocalTime();
                    var currentDate = DateTime.Now;
                    TimeSpan ts = new TimeSpan(createDateLocalTime.Hour, createDateLocalTime.Minute, createDateLocalTime.Second);
                    currentDate = currentDate.Date + ts;

                    //First (this.CreateDate) and Last (today) days are not taken into consideration for the calculation
                    return 30 - (currentDate - createDateLocalTime).Days;
                }
                else
                    return null;
            }
        }
        [JsonIgnore]
        [CsvExportable("CompanyMonthlyFee")]
        public decimal MonthlyFee { get; set; }
        [JsonIgnore]
        [CsvExportable("CompanyYearAverage")]
        public decimal YearAverage { get; set; }
        #endregion
        /*************************************************/

        /*************************************************/
        #region Additional Properties for Accounting Provider Integration
        [JsonIgnore]
        public bool IsConnectedToAccountingProvider
        {
            get
            {
                try
                {
                    if (!this.HasFeature(Extric.Towbook.Generated.Features.QuickBooks))
                    {
                        return false;
                    }


                    var realmId = CompanyKeyValue.GetByCompanyId(_id, AccountingProvider.ProviderId, "RealmId");
                    var accessToken = CompanyKeyValue.GetByCompanyId(_id, AccountingProvider.ProviderId, "AccessToken");
                    var refreshToken = CompanyKeyValue.GetByCompanyId(_id, AccountingProvider.ProviderId, "RefreshToken");
                    var dataSource = CompanyKeyValue.GetByCompanyId(_id, AccountingProvider.ProviderId, "DataSource");
                    var agent = Agent.Session.GetByCompanyId(_id);

                    return agent != null || (realmId.FirstOrDefault()?.Value != null &&
                        refreshToken.FirstOrDefault()?.Value != null &&
                        accessToken.FirstOrDefault()?.Value != null &&
                        dataSource.FirstOrDefault().Value != null);
                }
                catch
                {
                    return false;
                }

            }
        }

        [JsonIgnore]
        private Extric.Towbook.Integration.Provider _accountingProvider;
        public Extric.Towbook.Integration.Provider AccountingProvider
        {
            get
            {
                if (!this.HasFeature(Extric.Towbook.Generated.Features.QuickBooks))
                {
                    _accountingProvider = null;
                    return _accountingProvider;
                }
                if (_accountingProvider == null)
                {
                    _accountingProvider = Extric.Towbook.Integration.Provider.GetByCompanyId(_id).Where(w => w.Type == Integration.ProviderType.Accounting).FirstOrDefault();
                }
                return _accountingProvider;
            }
        }

        [JsonIgnore]
        public Collection<Extric.Towbook.Integration.CompanyKeyValue> AccoutingProviderCompanyKeys
        {
            get
            {
                if (!this.HasFeature(Extric.Towbook.Generated.Features.QuickBooks))
                {
                    return null;
                }

                var providerCompanyKeys = Extric.Towbook.Integration.CompanyKey.GetByProviderId(this.AccountingProvider.ProviderId);
                var providerCompanyKeyValues = Extric.Towbook.Integration.CompanyKeyValue.GetByCompany(_id);

                var result = (from ck in providerCompanyKeys
                              join ckv in providerCompanyKeyValues
                              on ck.Id equals ckv.KeyId
                              select ckv).ToList();

                if (result.Count > 0)
                {
                    return new Collection<Extric.Towbook.Integration.CompanyKeyValue>(result);
                }
                else
                {
                    return null;
                }
            }
        }
        [JsonIgnore]
        public ScheduleConfigurationHelper.ScheduleOption AccoutingProcessSchedule
        {
            get
            {
                if (!this.HasFeature(Extric.Towbook.Generated.Features.QuickBooks))
                {
                    return ScheduleConfigurationHelper.ScheduleOption.None;
                }

                ScheduleConfigurationHelper.ScheduleOption scheduleOption = ScheduleConfigurationHelper.ScheduleOption.None;
                if (Enum.TryParse<ScheduleConfigurationHelper.ScheduleOption>(ScheduleConfigurationHelper.GetByCompanyId(_id).Value, true, out scheduleOption))
                {
                    return scheduleOption;
                }
                return scheduleOption;
            }
            private set
            {
            }
        }

        [JsonIgnore]
        public bool AllowAccountlessInvoices
        {
            get
            {
                if (!this.HasFeature(Extric.Towbook.Generated.Features.QuickBooks))
                {
                    return false;
                }

                var key = Provider.QuickBooks.GetKey(KeyType.Company, "AllowAccountlessInvoices");
                if (key == null)
                {
                    return false;
                }
                var companyKV = CompanyKeyValue.GetByCompany(_id).Where(w => w.KeyId == key.Id).FirstOrDefault();

                if (companyKV != null)
                {
                    return companyKV.Value == Boolean.TrueString || companyKV.Value == "1";
                }
                return companyKV != null;
            }
            private set
            {

            }
        }
        #endregion
        /*************************************************/

        /*************************************************/
        #region Additional Properties for Interim Notes
            
        private CompanyInterimNotes interimNotes;
        [JsonIgnore]
        public CompanyInterimNotes InterimNotes
        {
            get
            {
                return new CompanyInterimNotes();
                /*
                if (interimNotes == null)
                    interimNotes = CompanyInterimNotes.GetByCompanyId(Id);

                return interimNotes;*/
            }
            internal set
            {
                interimNotes = value;
            }
        }

        #endregion
        /*************************************************/

        /// <summary>
        /// Create a new Company
        /// </summary>
        public Company()
        {
            _id = -1;
        }
        /// <summary>
        /// Initialize an existing object
        /// </summary>        
        /// <param name="id">Id of the company</param>

        [Obsolete("Prefer using GetById method instead.")]
        public Company(int id)
        {
            using (SqlDataReader dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                "CompaniesGetById", new SqlParameter("@CompanyId", id)))
            {
                if (dr.Read())
                {
                    InitializeFromDataReader(dr);
                    AppServices.Cache.Add("company:" + Id, TimeSpan.FromMinutes(CacheTimeout), this);
                }
                else
                {
                    throw new Extric.Towbook.TowbookException("Company doesn't exist!");
                }
            }
        }

        protected Company(IDataReader dr)
        {
            InitializeFromDataReader(dr);

            //cache.Add("company:"+ Id, this, CacheItemPriority.Normal, null, new SlidingTime(TimeSpan.FromMinutes(CacheTimeout)));  //Commented by Edgarin, GlavCache applied			            
            AppServices.Cache.Add("company:" + Id, TimeSpan.FromMinutes(CacheTimeout), this);  //Added by Edgarin, GlavCache applied			
        }

        /// <summary>
        /// Returns a company instance, with the new properties for Account Reports filled
        /// </summary>
        /// <param name="dr"></param>
        /// <param name="aFlag"></param>
        protected Company(IDataReader dr, int? type)
        {
            InitializaFromDataReaderForAccountReport(dr, type);

            AppServices.Cache.Add("company:" + Id, TimeSpan.FromMinutes(CacheTimeout), this);
        }

        public static Company GetById(int id)
        {
            var company = Cache.Instance.Get(id, (id) =>
            {
                if (id < 1)
                    return null;

                return AppServices.Cache.Get("company:" + id,
                    TimeSpan.FromMinutes(CacheTimeout), () =>
                    {
                        using SqlDataReader dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                            "CompaniesGetById", new SqlParameter("@CompanyId", id));
                        return dr.Read() ? new Company(dr) : null;
                    });
            });
            if (company != null && company.Type == 0)
            {
                company.Type = CompanyType.TowingCompany;
            }

            return company;
        }
        public static async Task<Company> GetByIdAsync(int id)
        {
            var company = await Cache.Instance.GetAsync<Company>(id, async(id) =>
            {
                if (id < 1)
                    return null;

                return await AppServices.Cache.GetAsync("company:" + id,
                    TimeSpan.FromMinutes(CacheTimeout), async() =>
                    {
                        using var dr = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString, CommandType.StoredProcedure,
                            "CompaniesGetById", new SqlParameter("@CompanyId", id));
                        return await dr.ReadAsync() ? new Company(dr) : null;
                    });
            });
            if (company != null && company.Type == 0)
            {
                company.Type = CompanyType.TowingCompany;
            }

            return company;
        }

        public static IEnumerable<Company> GetByIds(int[] ids)
        {
            if (ids == null || ids.Length == 0)
                return Array.Empty<Company>();


            var parameters = new List<string>();
            var p = new List<SqlParameter>();

            int i = 0;
            foreach (var row in ids)
            {
                i++;
                parameters.Add("@P" + i);
                p.Add(new SqlParameter("@P" + i, row));
            }

            using (SqlDataReader dr = SqlHelper.ExecuteReader(
                Core.ConnectionString, CommandType.Text,
                $"SELECT * FROM vwCompanies WITH (NOLOCK) WHERE CompanyId IN({string.Join(",", parameters)})",
                p.ToArray()))
            {
                var list = new List<Company>();

                while (dr.Read())
                {
                    list.Add(new Company(dr));
                }

                return list;
            }
        }

        public static async Task<IEnumerable<Company>> GetByIdsAsync(int[] ids)
        {
            if (ids == null || ids.Length == 0)
                return Array.Empty<Company>();


            var parameters = new List<string>();
            var p = new List<SqlParameter>();

            int i = 0;
            foreach (var row in ids)
            {
                i++;
                parameters.Add("@P" + i);
                p.Add(new SqlParameter("@P" + i, row));
            }

            using (SqlDataReader dr = await SqlHelper.ExecuteReaderAsync(
                Core.ConnectionString, CommandType.Text,
                $"SELECT * FROM vwCompanies WITH (NOLOCK) WHERE CompanyId IN({string.Join(",", parameters)})",
                p.ToArray()))
            {
                var list = new List<Company>();

                while (await dr.ReadAsync())
                {
                    list.Add(new Company(dr));
                }

                return list;
            }
        }

        public override string ToString()
        {
            return String.Format("Extric.Towbook.Company [Name = {0}, Id = {1}, CreateDate = {2}]", _name, _id, CreateDate);
        }

        private void InitializeFromDataReader(IDataReader reader)
        {
            _id = reader.GetValue<int>("CompanyId");

            _name = reader.GetValue<string>("Name");
            ShortName = reader.GetValue<string>("ShortName");
            if (string.IsNullOrWhiteSpace(ShortName))
                ShortName = _name;

            CreateDate = reader.GetValue<DateTime>("CreateDate");

            _email = reader.GetValue<string>("Email");
            _phone = reader.GetValue<string>("Phone");

            _fax = reader.GetValue<string>("Fax");
            _website = reader.GetValue<string>("Website");

            if (reader["Address"] != DBNull.Value)
                _address = Convert.ToString(reader["Address"]);

            if (reader["City"] != DBNull.Value)
                _city = Convert.ToString(reader["City"]);

            if (reader["State"] != DBNull.Value)
                _state = Convert.ToString(reader["State"]);

            if (reader["Zip"] != DBNull.Value)
                _zip = Convert.ToString(reader["Zip"]);


            if (reader["AccountingMethod"] != DBNull.Value)
                _accountingMethod = (AccountingMethodEnum)Convert.ToInt32(reader["AccountingMethod"]);

            if (reader["InvoicingSystemId"] != DBNull.Value)
                _invoicingSystemId = (InvoicingSystemEnum)Convert.ToInt32(reader["InvoicingSystemId"]);

            if (reader["InvoicingLateFlatAmount"] != DBNull.Value)
                _invoicingLateFlatAmount = Convert.ToDecimal(reader["InvoicingLateFlatAmount"]);

            if (reader["InvoicingLateAPR"] != DBNull.Value)
                _invoicingLateAPR = Convert.ToDecimal(reader["InvoicingLateAPR"]);

            if (reader["InvoicingLateGracePeriod"] != DBNull.Value)
                _invoicingLateGracePeriod = Convert.ToInt32(reader["InvoicingLateGracePeriod"]);

            if (reader["InvoicingTagline"] != DBNull.Value)
                _invoicingTagline = Convert.ToString(reader["InvoicingTagline"]);

            if (reader["Country"] != DBNull.Value)
                _country = (CompanyCountry)Convert.ToInt32(reader["Country"]);


            if (reader["TimezoneOffset"] != DBNull.Value)
                _timezoneOffset = Convert.ToSingle(reader["TimezoneOffset"]);

            if (reader["TimezoneUseDST"] != DBNull.Value)
                _timezoneUseDST = Convert.ToBoolean(reader["TimezoneUseDST"]);

            if (reader["TaxMode"] != DBNull.Value)
                _taxMode = (TaxModeEnum)Convert.ToInt32(reader["TaxMode"]);

            if (reader["StandardDriverCommission"] != DBNull.Value)
                StandardDriverCommission = Convert.ToDecimal(reader["StandardDriverCommission"]);

            FirstLogin = reader.GetValueOrNull<DateTime>("FirstLogin");
            LastLogin = reader.GetValueOrNull<DateTime>("LastLogin");
            VinRequiredLength = reader.GetValue<int>("VinRequiredLength");

            _news = reader.GetValue<string>("News");

            switch (reader.GetValue<int>("Type"))
            {
                case 3:
                    Type = CompanyType.LawEnforcement;
                    break;

                case 4:
                    Type = CompanyType.AnsweringService;
                    break;

                default:
                    Type = CompanyType.TowingCompany;
                    break;

            }

            if (reader["Latitude"] != DBNull.Value)
                Latitude = Convert.ToDecimal(reader["Latitude"]);
            
            if (reader["Longitude"] != DBNull.Value)
                Longitude = Convert.ToDecimal(reader["Longitude"]);
            
            if (reader["MonthlyFee"] != DBNull.Value)
                MonthlyFee = Convert.ToDecimal(reader["MonthlyFee"]);

        }

        private void InitializaFromDataReaderForAccountReport(IDataReader reader, int? type)
        {
            InitializeFromDataReader(reader);

            /* Properties for Account Reports */
            if (reader["TruckCount"] != DBNull.Value)
                TruckCount = Convert.ToInt32(reader["TruckCount"]);

            if (reader["UserCount"] != DBNull.Value)
                UserCount = Convert.ToInt32(reader["UserCount"]);

            if (reader["DriverCount"] != DBNull.Value)
                DriverCount = Convert.ToInt32(reader["DriverCount"]);

            if (reader["RateItemCount"] != DBNull.Value)
                RateItemCount = Convert.ToInt32(reader["RateItemCount"]);

            if (reader["CallCountForever"] != DBNull.Value)
                CallCountForever = Convert.ToInt32(reader["CallCountForever"]);

            YearAverage = 0;
            if (reader["CallCount365"] != DBNull.Value)
            {
                CallCount365 = Convert.ToInt32(reader["CallCount365"]);
                YearAverage = CallCount365.Value / 365;
            }
            
            if (reader["CallCount180"] != DBNull.Value)
                CallCount180 = Convert.ToInt32(reader["CallCount180"]);

            if (reader["CallCount90"] != DBNull.Value)
                CallCount90 = Convert.ToInt32(reader["CallCount90"]);

            if (reader["CallCount60"] != DBNull.Value)
                CallCount60 = Convert.ToInt32(reader["CallCount60"]);

            
            if (reader["CallCount30"] != DBNull.Value)
                CallCount30 = Convert.ToInt32(reader["CallCount30"]);                

            if (reader["FirstLogin"] != DBNull.Value)
                FirstLogin = DateTime.Parse(reader["FirstLogin"].ToString());

            if (reader["LastLogin"] != DBNull.Value)
                LastLogin = DateTime.Parse(reader["LastLogin"].ToString());

            //if (reader["DaysLeft"] != DBNull.Value)
            //    DaysLeft = Convert.ToInt32(reader["DaysLeft"]);

            MonthlyFee = Convert.ToDecimal(reader["MonthlyFee"]);

            if ((type == null || type == 2) && MonthlyFee > 0)
                Status = "Paying"; //Status = "Active"; //Change from Active to Paying based on TBA-148
            else if ((type == null || type == 2) && LastLogin.HasValue && (DateTime.Now - LastLogin.Value).Days > 30 && MonthlyFee > 0)
                Status = "Idle";
            else if ((type == null || type == 1) && !LastLogin.HasValue)
                Status = "Request";
            else if ((type == null || type == 1) && LastLogin.HasValue && MonthlyFee <= 0)
                Status = "Evaluating";
            else if (type == null || type == 99)
                Status = "Cancelled";
        }

        public static Collection<Company> GetAll()
        {
            return AppServices.Cache.Get<Collection<Company>>("company:all",
                        TimeSpan.FromMinutes(CacheTimeout), () =>
                        {
                            Collection<Company> companies = new Collection<Company>();

                            using (SqlDataReader dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                                "CompaniesGetAll"))
                            {
                                while (dr.Read())
                                {
                                    companies.Add(new Company(dr));
                                }
                            }

                            return companies;
                        });
        }
        public static async Task<Collection<Company>> GetAllAsync()
        {
            return await AppServices.Cache.GetAsync<Collection<Company>>("company:all",
                        TimeSpan.FromMinutes(CacheTimeout), async () =>
                        {
                            Collection<Company> companies = new Collection<Company>();

                            using (SqlDataReader dr = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString,
                                "CompaniesGetAll"))
                            {
                                while (await dr.ReadAsync())
                                {
                                    companies.Add(new Company(dr));
                                }
                            }

                            return companies;
                        });
        }


        public enum NullType { ShowAll = 3, ShowOnlyNull = 1, ShowNotNull = 2 }

        public static async Task<Collection<Company>> GetByTypeAsync(int type, NullType nt)
        {
            Collection<Company> companies = new Collection<Company>();

            using (SqlDataReader dr = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString,
                "CompaniesGetByType", new SqlParameter("@Type", type), new SqlParameter("@ShowIsNull", nt)))
            {
                while (await dr.ReadAsync())
                {
                    companies.Add(new Company(dr));
                }
            }

            return companies;
        }

        public enum CompanySortEnum
        {
            CompanyId,
            CreateDate,
            Name,
            City,
            State,
            Zip,
            CallCount30
        }

        public enum SortOrder { Ascending, Descending }

        public static async Task<IEnumerable<Company>> GetAsync(int pageNumber, 
            int pageSize = 50, 
            CompanySortEnum sortBy = CompanySortEnum.CompanyId, 
            SortOrder sortOrder = SortOrder.Ascending)
        {
            var builder = new SqlBuilder(true);
            var selector = builder.AddTemplate(
            @"SELECT * from vwCompanies D 
                /**rightjoin**/ /**where**/ /**orderby**/");

            //builder.Where("D.CompanyId = @companyId", new { companyId = 2 });
            //builder.Where("Status = @status", new { status = 0 });
            //builder.RightJoin("Companies C ON C.CompanyId=D.CompanyId");
            builder.OrderBy(Enum.GetName(typeof(CompanySortEnum), sortBy) + " " + (sortOrder == SortOrder.Ascending ? "ASC" : "DESC"), null, pageSize * (pageNumber - 1), pageSize);
       
            builder.AddParameters(new { X = 4 });

            //SELECT * from vwCompanies D
            // ORDER BY CompanyId DESC OFFSET 5000 ROWS FETCH NEXT 5000 ROWS ONLY

            using (var conn = Core.GetConnection())
            {
                Console.WriteLine(selector.RawSql);
                var z = (await conn.QueryAsync(selector.RawSql, selector.DapperParameters)).Select(o => new Company()
                {
                    Id = o.CompanyId,
                    Name = o.Name,
                    ShortName = o.ShortName,
                    CreateDate = o.CreateDate,
                    Email = o.Email,
                    Phone = o.Phone,
                    Fax = o.Fax,
                    Website = o.Website,
                    Address = o.Address,
                    City = o.City,
                    State = o.State,
                    Zip = o.Zip,
                    Country = o.Country != null ? (CompanyCountry)o.Country : CompanyCountry.USA,
                    TimezoneOffset = (float)(o.TimezoneOffset ?? 0),
                    TimezoneUseDST = o.TimezoneUseDST,
                    TaxMode = (TaxModeEnum) o.TaxMode,
                    StandardDriverCommission = (decimal)(o.StandardDriverCommission ?? 0),
                    FirstLogin = o.FirstLogin,
                    LastLogin = o.LastLogin,
                    VinRequiredLength = o.VinRequiredLength,
                    Type = (CompanyType)o.Type,
                    DriverCount = o.DriverCount,
                    TruckCount = o.TruckCount,
                    RateItemCount = o.RateItemCount,
                    CallCount30 = o.CallCount30,
                    CallCount60 = o.CallCount60,
                    CallCount365 = o.CallCount365,
                    CallCountForever = o.CallCountForever
                });

                return z.ToList();
            }        
        }

        public static async Task<IEnumerable<Company>> GetByStatusTypeStateDateAsync(int? status, int? type, string state, DateTime? startDate, DateTime? endDate)
        {
            Collection<Company> r = new Collection<Company>();
            using (SqlDataReader dr = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString,
                CommandType.StoredProcedure,
                "CompaniesGetByStatusTypeStateDate", 
                new SqlParameter("@Status", status),
                new SqlParameter("@Type", type),
                new SqlParameter("@State", state),
                new SqlParameter("@StartDate", startDate),
                new SqlParameter("@EndDate", endDate)))
            {
                while (await dr.ReadAsync())
                {
                    r.Add(new Company(dr, type));
                }

                if (await dr.NextResultAsync())
                {
                    while (await dr.ReadAsync())
                    {
                        var u = new User()
                        {
                            Id = dr.GetValue<int>("UserId"),
                            PrimaryCompanyId = dr.GetValue<int>("CompanyId"),
                            FullName = dr.GetValue<string>("FullName"),
                            Email = dr.GetValue<string>("Email"),
                        };

                        var c = r.FirstOrDefault(o => o.Id == u.PrimaryCompanyId);
                        if (c != null)
                            c.FirstUser = u;
                    }
                }

                if (await dr.NextResultAsync())
                {
                    while (await dr.ReadAsync())
                    {
                        var interimNotes = new CompanyInterimNotes(dr.GetValue<int>("CompanyId"), dr.GetValue<string>("Notes"), dr.GetValue<string>("OpenItems"));

                        var c = r.Where(o => o.Id == interimNotes.CompanyId).FirstOrDefault();
                        if (c != null)
                            c.InterimNotes = interimNotes;
                    }
                }

                // force null interimNotes to a non null value.. major perf improvement.. really, all of this needs to be removed
                // from the company class. It shouldn't have been put here in the first place.
                foreach (var x in r)
                {
                    if (x.interimNotes == null)
                        x.interimNotes = new CompanyInterimNotes();
                }
            }

            return r;
        }

        public async Task Save()
        {
            if (_id == 0)
            {
                throw new TowbookException("No such Company. Can't save " +
                    "object! (this object should have already been discarded!)");
            }
            try
            {
                if (_id == -1)
                {
                    this.CreateDate = DateTime.Now;
                    DbInsert();
                }
                else
                {
                    DbUpdate();
                }
            }
            finally
            {
                await InvalidateCache();
                await PushNotificationProvider.Push(_id, "company_update", new { companyId = _id });
                await Caching.CacheWorkerUtility.UpdateCompany(this);
            }
        }

        public async Async.Task InvalidateCache()
        {
                // We delete the entries because updating the company name then saving the value to Redis
                // could put an incorrect ShortName for the company in Redis. The short name is
                // coalesced from the sharedcompany table along with the Company table, so we'll
                // let the next request from vwCompanies update redis instead of doing it here.
                await Cache.Instance.PartitionDeleteAsync(this);
                await Cache.Instance.DeleteAsync(this);
                ClearCacheById(_id);
            
        }

        private void DbInsert()
        {
            _id = Convert.ToInt32(SqlHelper.ExecuteScalar(Core.ConnectionString,
                "CompaniesInsert",
                new SqlParameter("@Name", _name),
                new SqlParameter("@Phone", _phone),
                new SqlParameter("@Fax", _fax),
                new SqlParameter("@Email", _email),
                new SqlParameter("@Website", _website),
                new SqlParameter("@Address", _address),
                new SqlParameter("@City", _city),
                new SqlParameter("@State", _state),
                new SqlParameter("@Zip", _zip),
                new SqlParameter("@Country", _country),
                new SqlParameter("@AccountingMethod", _accountingMethod),
                new SqlParameter("@InvoicingSystemId", _invoicingSystemId),
                new SqlParameter("@InvoicingTagline", _invoicingTagline),
                new SqlParameter("@InvoicingLateFlatRate", _invoicingLateFlatAmount),
                new SqlParameter("@InvoicingLateAPR", _invoicingLateAPR),
                new SqlParameter("@InvoicingLateGracePeriod", _invoicingLateGracePeriod),
                new SqlParameter("@TimezoneOffset", _timezoneOffset),
                new SqlParameter("@TimezoneUseDST", _timezoneUseDST),
                new SqlParameter("@TaxMode", _taxMode),
                new SqlParameter("@StandardDriverCommission", StandardDriverCommission),
                new SqlParameter("@Latitude", Latitude),
                new SqlParameter("@Longitude", Longitude)));
        }

        private void DbUpdate()
        {
            SqlHelper.ExecuteNonQuery(Core.ConnectionString,
                "CompaniesUpdateById",
                new SqlParameter("@CompanyId", _id),
                new SqlParameter("@Name", _name),
                new SqlParameter("@Phone", _phone),
                new SqlParameter("@Fax", _fax),
                new SqlParameter("@Email", _email),
                new SqlParameter("@Website", _website),
                new SqlParameter("@Address", _address),
                new SqlParameter("@City", _city),
                new SqlParameter("@State", _state),
                new SqlParameter("@Zip", _zip),
                new SqlParameter("@Country", _country),
                new SqlParameter("@AccountingMethod", _accountingMethod),
                new SqlParameter("@InvoicingSystemId", _invoicingSystemId),
                new SqlParameter("@InvoicingTagline", _invoicingTagline),
                new SqlParameter("@InvoicingLateFlatAmount", _invoicingLateFlatAmount),
                new SqlParameter("@InvoicingLateAPR", _invoicingLateAPR),
                new SqlParameter("@InvoicingLateGracePeriod", _invoicingLateGracePeriod),
                new SqlParameter("@TimezoneOffset", _timezoneOffset),
                new SqlParameter("@TimezoneUseDST", _timezoneUseDST),
                new SqlParameter("@TaxMode", _taxMode),
                new SqlParameter("@StandardDriverCommission", StandardDriverCommission),
                new SqlParameter("@Latitude", Latitude),
                new SqlParameter("@Longitude", Longitude));
        }

        public static void ClearCacheById(int id)
        {
            AppServices.Cache.InvalidateCacheItem("company:" + id);
        }

        private static int[] GetCompanyFeaturesFromSql(int companyId)
        {
            return Utility.SqlMapper.Query<int>("SELECT FeatureId from vwCompanyFeatures WHERE CompanyId=@CompanyId", new { companyId }).ToArray();
        }

        private static async Task<int[]>GetCompanyFeaturesFromSqlAsync(int companyId)
        {
            return (await Utility.SqlMapper.QueryAsync<int>("SELECT FeatureId from vwCompanyFeatures WHERE CompanyId=@CompanyId", new { companyId })).ToArray();
        }


        private static int[] GetCompanyFeatures(int companyId)
        {
            if (companyId == 0)
                return Array.Empty<int>();

            return AppServices.Cache.Get("companyFeatures:" + companyId,
              TimeSpan.FromMinutes(5), () =>
              {
                  var rv = Core.GetRedisValue("comp_features:" + companyId);
                  if (rv != null)
                  {
                      return rv.Split(',').Select(o => Convert.ToInt32(o)).ToArray();
                  }

                  var items = GetCompanyFeaturesFromSql(companyId);

                  Core.SetRedisValue("comp_features:" + companyId, string.Join(",", items), TimeSpan.FromHours(4));

                  return items.ToArray();
              });
        }
        private static async Task<int[]> GetCompanyFeaturesAsync(int companyId)
        {
            if (companyId == 0)
                return Array.Empty<int>();

            return await AppServices.Cache.GetAsync("companyFeatures:" + companyId,
              TimeSpan.FromMinutes(5), async() =>
              {
                  var rv = await Core.GetRedisValueAsync("comp_features:" + companyId);
                  if (rv != null)
                  {
                      return rv.Split(',').Select(o => Convert.ToInt32(o)).ToArray();
                  }

                  var items = await GetCompanyFeaturesFromSqlAsync(companyId);

                  await Core.SetRedisValueAsync("comp_features:" + companyId, string.Join(",", items), TimeSpan.FromHours(4));

                  return items.ToArray();
              });
        }

        private static readonly string[] addStates = new string[] { 
            "KS", "WI", "SC", "MN", "NV", "LA", "MD",
            "KY", "TX", "MS", "TN" ,"IN" ,"OK", "AL",
            "OH", "GA", "AK", "AZ", "CO", "DE", "ID",
            "IA", "ME", "MA", "MI", "CA", "CT", "HI",
            "MT", "NE", "NH", "NJ", "NM", "NC", "ND",
            "PA", "RI", "SD", "UT", "VT", "VA", "WA", 
            "WV", "WY", "OR", "AR", "IL", "FL", "NY",
            "MO" };

        public bool HasFeature(Generated.Features feature)
        {
            if (CheckDefaultFeatures(feature))
                return true;

            var hasFeature = GetCompanyFeatures(Id).Contains((int)feature);


            if (hasFeature && feature == Features.SecurityTools && DateTime.Now < new DateTime(2025, 6, 26, 8, 45, 0))
            {
                if (new int[] { 21755,22391,23448,88896,169528,169534,227457,239018,
                    261596,277270,277752,291349,9681,23090,46224,48393,189882,225258,
                    228805,251519,261071,291357,69591,161306,194924,228806,230683,233796,
                    242411,274330,282347,24776,183797,238959,250115,261075,271426,282222,
                    22789,161327,274721,277340,299617,20368,24880,38345,161305,161308,161311,
                    163717,202483,214475,239999,267021,277753,8900,9240,21328,92283,123433,
                    134111,161307,161310,188427,202482,22390,29925,83706,147267,161309,161312,
                    163718,273416,277754,301921 
                }.Contains(this.Id)) 
                    return true;

                // This feature is only available after 6/19/2025 8:45 AM.
                return false;
            }

            return hasFeature;
        }

        public async Task<bool> HasFeatureAsync(Generated.Features feature)
        {
            if (CheckDefaultFeatures(feature))
                return true;

            return (await GetCompanyFeaturesAsync(Id)).Contains((int)feature);
        }

        private bool CheckDefaultFeatures(Features feature)
        {
            // FOR COVID19. We are enabling this for all companies effective immediately
            // during covid19 pandemic to help companies. 
            if (feature == Generated.Features.Videos)
                return true;

            if (feature == Generated.Features.AutoDataDirectIntegration &&
                (addStates.Contains(State)))
                return true;

            if (feature == Generated.Features.Undelete)
                return true;

            if (feature == Generated.Features.PaymentIntegrations_Square_SquareTerminal)
                return true;

            if (feature == Generated.Features.AccountTags)
                return true;

            if (feature == Generated.Features.BetaPrivatePropertyMaps)
                return HasAccessToBaseFeature("customTags");

            if (feature == Generated.Features.BetaProfilePhotoUpload)
                return HasAccessToBaseFeature("userDriverNewFieldsAndPhotos");

            if (feature == Generated.Features.BetaPromptDriversforTruckAssignments)
                return HasAccessToBaseFeature("inspectionDefault");

            if (feature == Generated.Features.BetaPropertyReleaseForms)
                return HasAccessToBaseFeature("mobilePropertyRelease");

            if (feature == Generated.Features.PeddleIntegration)
            {
                var hasOverride = Management.FeatureExtensions.HasFeatureOverrideOrNull(this, feature);
                if (hasOverride.HasValue)
                    return hasOverride.Value;
            }
            return false;
        }

        public bool HasAccessToBaseFeature(string feature)
        {
            var raw = Core.GetRedisValue("basefeature_" + feature);
            if (!string.IsNullOrEmpty(raw))
            {
                var cIds = JsonConvert.DeserializeObject<int[]>(raw);
                return cIds.Contains(this.Id);
            }

            return true;
        }

        public async Task<bool> IsConnectedToAccountingProviderAsync()
        {
            try
            {
                if (!await this.HasFeatureAsync(Extric.Towbook.Generated.Features.QuickBooks))
                {
                    return false;
                }
                var realmId = await CompanyKeyValue.GetByCompanyIdAsync(_id, AccountingProvider.ProviderId, "RealmId");
                var accessToken = await CompanyKeyValue.GetByCompanyIdAsync(_id, AccountingProvider.ProviderId, "AccessToken");
                var refreshToken = await CompanyKeyValue.GetByCompanyIdAsync(_id, AccountingProvider.ProviderId, "RefreshToken");
                var dataSource = await CompanyKeyValue.GetByCompanyIdAsync(_id, AccountingProvider.ProviderId, "DataSource");
                var agent = await Agent.Session.GetByCompanyIdAsync(_id);
                return agent != null || (realmId.FirstOrDefault()?.Value != null &&
                    refreshToken.FirstOrDefault()?.Value != null &&
                    accessToken.FirstOrDefault()?.Value != null &&
                    dataSource.FirstOrDefault().Value != null);
            }
            catch
            {
                return false;
            }
        }

        [JsonIgnore]
        public string CountryFullName
        {
            get
            {
                switch (Country)
                {
                    case CompanyCountry.USA: return "United States";
                    case CompanyCountry.Canada: return "Canada";
                    case CompanyCountry.Australia: return "Australia";
                    case CompanyCountry.UK: return "United Kingdom";
                    case CompanyCountry.NewZealand: return "New Zealand";
                    case CompanyCountry.SouthAfrica: return "South Africa";
                }

                return "";
            }            
        }

        [JsonIgnore]

        public System.Globalization.CultureInfo CultureInfo
        {
            get
            {
                switch (this.Country)
                {
                    case CompanyCountry.NewZealand:
                        return new System.Globalization.CultureInfo("en-NZ");

                    case CompanyCountry.Australia:
                        return new System.Globalization.CultureInfo("en-AU");

                    case CompanyCountry.SouthAfrica:
                        return new System.Globalization.CultureInfo("en-ZA");

                    case CompanyCountry.UK:
                        return new System.Globalization.CultureInfo("en-GB");

                    default:
                        return new System.Globalization.CultureInfo("en-US");
                }
            }
        }

        [JsonIgnore]
        public string CurrencyCode
        {
            get {
                switch (this.Country)
                {
                    case CompanyCountry.Canada:
                        return "CAD";

                    case CompanyCountry.Australia:
                        return "AUD";

                    case CompanyCountry.NewZealand:
                        return "NZD";

                    case CompanyCountry.SouthAfrica:
                        return "ZAR";

                    case CompanyCountry.UK:
                        return "GDP";

                    default:
                        return "USD";
                }
            }
        }

        /// <summary>
        /// It returns an address that can be resolved by a maps engine like google maps or TomTom
        /// the result will have a format like this: <address>, <city> <state> <zip>?,<Country>
        /// </summary>
        /// <returns></returns>
        public string GetComposedAddress ()
        {
            var format = "{0}, {1}, {2}";

            if (Zip != "")
                format += " {3}";

            if (Country != CompanyCountry.USA)
                format += ", {4}";
            

            return String.Format(format, Address, City, State, Zip, CountryFullName);
        }

        public async Task<List<Lot>> GetLots()
        {
            var lots = await Lot.GetByCompanyAsync(this);

            return lots;
        }
    }
}
