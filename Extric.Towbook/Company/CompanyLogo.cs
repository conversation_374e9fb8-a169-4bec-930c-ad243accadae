using Extric.Towbook.Storage;
using Extric.Towbook.Utility;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Extric.Towbook.Company
{
    /// <summary>
    /// Represents a logo to show on the companies Invoice.
    /// </summary>
    [Table("CompanyLogos")]
    public class CompanyLogo
    {
        [Key("CompanyLogoId")]
        public int Id { get; set; }
        public int CompanyId { get; set; }
        public int OwnerUserId { get; set; }
        public bool IsUploaded { get; set; }

        [Write(false)]
        public DateTime CreateDate { get; set; }

        [Write(false)]
        public string Path
        {
            get
            {
                if (Id < 1 || CompanyId < 1)
                    return null;

                //$"/Storage/companies/{CompanyId}/logos/{Id}.jpg";
                return System.IO.Path.Combine(System.IO.Path.GetTempPath(), "Towbook", "Storage", "companies", 
                    CompanyId.ToString(), "logos", Id + ".jpg");

            }
        }

        [Write(false)]
        public string Url
        {
            get
            {
                if (Id < 1 || CompanyId < 1)
                    return null;

                return FileUtility.GetPresignedUrlForDownloadFromClient(
                    Path, "image/jg", 900);
            }
        }

        public static CompanyLogo GetByCompanyId(int companyId)
        {
            return SqlMapper.Query<CompanyLogo>(
                "SELECT TOP 1 * FROM CompanyLogos WHERE CompanyId=@CompanyId AND IsDeleted=0 AND IsUploaded=1 ORDER BY 1 DESC", 
                new { CompanyId = companyId } ).FirstOrDefault() ;
        }


        public static IEnumerable<CompanyLogo> GetAll()
        {
            return SqlMapper.Query<CompanyLogo>(
                "SELECT * FROM CompanyLogos WHERE IsDeleted=0 ORDER BY 1 DESC");
        }

        public void Save()
        {
            if (CompanyId < 1)
                throw new ArgumentException("CompanyId must be set", "CompanyId");

            if (OwnerUserId < 1)
                throw new ArgumentException("OwnerUserId must be set", "OwnerUserId");

            if (this.Id < 1)
                SqlMapper.Insert(this);
            else
                SqlMapper.Update(this);
        }
    }
}
