using System;
using System.Collections.Generic;
using Extric.Towbook.Dispatch.CallModels;
using Extric.Towbook.Dispatch;
using Extric.Towbook.Generated;
using System.Threading.Tasks;

namespace Extric.Towbook.Company.Accounting
{
    public static class ClosedPeriodExtensions
    {
        public static bool IsWithinClosedAccountingPeriod(this CallModel call)
        {
            if (call == null)
                return false;

            var closedPeriodOptions = ClosedPeriodOption.GetByCompanyId(call.CompanyId);
            return call.IsWithinClosedAccountingPeriod(closedPeriodOptions);
        }

        //PROPAGATE ASYNC METHOD
        public static async Task<bool> IsWithinClosedAccountingPeriodAsync(this CallModel call)
        {
            if (call == null)
                return false;

            var closedPeriodOptions = await ClosedPeriodOption.GetByCompanyIdAsync(call.CompanyId);
            return await call.IsWithinClosedAccountingPeriodAsync(closedPeriodOptions);
        }

        public static bool IsWithinClosedAccountingPeriod(this CallModel call, ClosedPeriodOption option)
        {
            if (call == null || call.CompletionTime == null)
                return false;

            var company = Company.GetById(call.CompanyId);
            if (company == null)
                return false;

            if (!company.HasFeature(Features.AdvancedBilling_ClosedAccountingPeriod))
                return false;

            if (option?.ClosedDate == null ||
                option.Enabled == false ||
                option?.ModificationMethod == ModificationMethodEnum.None ||
                option?.Enabled == false)
                return false;

            if (call.Impound == true)
            {
                if (call.ImpoundDetails?.ReleaseDate != null)
                    return !IsDateOutsideOfClosedAccountingPeriod(option, call.ImpoundDetails.ReleaseDate.Value);

                return false;
            }

            return call.CompletionTime.Value < CloseDateInLocalTime(option.ClosedDate.Value, company);
        }

        public static async Task<bool> IsWithinClosedAccountingPeriodAsync(this CallModel call, ClosedPeriodOption option)
        {
            if (call == null || call.CompletionTime == null)
                return false; 
            
            var company = await Company.GetByIdAsync(call.CompanyId);
            if (company == null)
                return false;

            if (!await company.HasFeatureAsync(Features.AdvancedBilling_ClosedAccountingPeriod))
                return false;

            if (option?.ClosedDate == null ||
                option.Enabled == false ||
                option?.ModificationMethod == ModificationMethodEnum.None ||
                option?.Enabled == false)
                return false;

            if (call.Impound == true)
            {
                if (call.ImpoundDetails?.ReleaseDate != null)
                    return !await IsDateOutsideOfClosedAccountingPeriodAsync(option, call.ImpoundDetails.ReleaseDate.Value);

                return false;
            }

            return call.CompletionTime.Value < CloseDateInLocalTime(option.ClosedDate.Value, company);
        }

        public static bool IsWithinClosedAccountingPeriod(this Entry entry)
        {
            if (entry == null)
                return false;

            if (!entry.Company.HasFeature(Generated.Features.AdvancedBilling_ClosedAccountingPeriod))
                return false;

            var closedPeriodOptions = ClosedPeriodOption.GetByCompanyId(entry.CompanyId);
            return entry.IsWithinClosedAccountingPeriod(closedPeriodOptions);
        }

        public static bool IsWithinClosedAccountingPeriod(this Entry entry, ClosedPeriodOption option)
        {
            if (entry == null || entry.CompletionTime == null)
                return false;

            if (option?.ClosedDate == null ||
                option.Enabled == false ||
                option?.ModificationMethod == ModificationMethodEnum.None ||
                option?.Enabled == false)
                return false;

            if (entry.Impound == true)
            {
                var impound = Impounds.Impound.GetByDispatchEntry(entry);
                if (impound?.ReleaseDate != null)
                    return !IsDateOutsideOfClosedAccountingPeriod(option, impound.ReleaseDate.Value);

                return false;
            }

            var company = Company.GetById(entry.CompanyId);
            if (!company.HasFeature(Features.AdvancedBilling_ClosedAccountingPeriod))
                return false;

            return entry.CompletionTime.Value < CloseDateInLocalTime(option.ClosedDate.Value, company);
        }

        public static bool IsWithinClosedAccountingPeriod(this Impounds.Impound imp)
        {
            if (imp == null)
                return false;

            var closedPeriodOptions = ClosedPeriodOption.GetByCompanyId(imp.DispatchEntry.CompanyId);
            return IsWithinClosedAccountingPeriod(imp, closedPeriodOptions);
        }

        public static bool IsWithinClosedAccountingPeriod(this Impounds.Impound imp, ClosedPeriodOption option)
        {
            if (imp == null)
                return false;

            var entry = imp.DispatchEntry;
            if (entry == null || entry.CompletionTime == null || entry.Company == null)
                return false;

            if (option?.ClosedDate == null ||
                option.Enabled == false ||
                option?.ModificationMethod == ModificationMethodEnum.None ||
                option?.Enabled == false)
                return false;

            var company = Company.GetById(entry.CompanyId);
            if (!company.HasFeature(Features.AdvancedBilling_ClosedAccountingPeriod))
                return false;

            if (imp?.ReleaseDate != null)
                return !IsDateOutsideOfClosedAccountingPeriod(option, imp.ReleaseDate.Value);

            return false;
        }

        public static bool IsDateOutsideOfClosedAccountingPeriod(ClosedPeriodOption option, DateTime checkDate)
        {
            if (option == null ||
                option.Enabled == false ||
                option.ModificationMethod == ModificationMethodEnum.None ||
                option.ClosedDate == null)
                return true;


            var company = Company.GetById(option.CompanyId);
            if (!company.HasFeature(Features.AdvancedBilling_ClosedAccountingPeriod))
                return false;

            return IsDateOutsideOfClosedAccountingPeriod(option, company, checkDate);
        }

        public static async Task<bool> IsDateOutsideOfClosedAccountingPeriodAsync(ClosedPeriodOption option, DateTime checkDate)
        {
            if (option == null ||
                option.Enabled == false ||
                option.ModificationMethod == ModificationMethodEnum.None ||
                option.ClosedDate == null)
                return true;


            var company = await Company.GetByIdAsync(option.CompanyId);
            if (!await company.HasFeatureAsync(Features.AdvancedBilling_ClosedAccountingPeriod))
                return false;

            return IsDateOutsideOfClosedAccountingPeriod(option, company, checkDate);
        }

        public static bool IsDateOutsideOfClosedAccountingPeriod(ClosedPeriodOption option, Company company, DateTime checkDate)
        {
            if (option == null ||
                option.Enabled == false ||
                option.ModificationMethod == ModificationMethodEnum.None ||
                option.ClosedDate == null)
                return true;

            return checkDate > CloseDateInLocalTime(option.ClosedDate.Value, company);
        }

        public static bool IsDateOutsideOfClosedAccountingPeriod(Company company, DateTime checkDate, DateTime closedDate)
        {
            if (closedDate == null)
                return false;

            return checkDate > CloseDateInLocalTime(closedDate, company);
        }

        public static DateTime CloseDateInLocalTime(DateTime closeDate, Company company)
        {
            var closeDateJustBeforeMidnight = new DateTime(closeDate.Year, closeDate.Month, closeDate.Day, 23, 59, 59);
            return Core.OffsetDateTime(company, closeDateJustBeforeMidnight, true);
        }
    }
}
