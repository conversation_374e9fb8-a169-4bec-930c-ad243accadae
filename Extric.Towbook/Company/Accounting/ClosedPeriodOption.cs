using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;
using Extric.Towbook.ActivityLogging;
using Extric.Towbook.Integration;
using Extric.Towbook.Utility;
using Glav.CacheAdapter.Core.DependencyInjection;
using Newtonsoft.Json;

namespace Extric.Towbook.Company.Accounting
{
    public enum ModificationMethodEnum
    {
        None = 0,
        [Description("Modify with Warning")]
        Warning = 1,
        [Description("Modify with Password")]
        Password = 2
    }

    [Table("ClosedAccountingPeriodOptions")]
    public class ClosedPeriodOption : TrackableObject
    {
        #region trackable members
        private DateTime? closedDate;
        private ModificationMethodEnum method;
        private string password;
        private bool enabled;
        #endregion

        [Key("ClosedAccountingPeriodOptionId")]
        public int Id { get; set; }
        public int CompanyId { get; set; }
        public int OwnerUserId { get; set; }
        public DateTime CreateDate { get; set; }
        public DateTime? ClosedDate
        {
            get => closedDate;
            set => SetField(ref closedDate, value, "CloseDate");
        }
        public ModificationMethodEnum ModificationMethod
        {
            get => method;
            set => SetField(ref method, value, "ModificationMethod");
        }
        public string ModificationPassword
        {
            get => password;
            set => SetField(ref password, value, "Password");
        }
        public bool Enabled { get => enabled; set => SetField(ref enabled, value, "Enabled"); }
        public bool Deleted { get; set; }
        public DateTime? DeletedDate { get; set; }
        public int? DeletedByUserId { get; set; }

        private static string GetRedisCompanyKey (int companyId) => $"closedPeriod_c:{companyId}";

        internal static ClosedPeriodOption Map(ClosedPeriodOption o)
        {
            if (o == null)
                return o;

            var x = new ClosedPeriodOption
            {
                Id = o.Id, CompanyId = o.CompanyId, OwnerUserId = o.OwnerUserId, CreateDate = o.CreateDate,
                Deleted = o.Deleted,
                DeletedByUserId = o.DeletedByUserId,
                DeletedDate = o.DeletedDate,
                // trackable fields
                Enabled = o.Enabled,
                closedDate = o.ClosedDate,
                method = o.ModificationMethod,
                password = o.ModificationPassword
            };

            x.MarkAsClean();

            return x;
        }

        public static ClosedPeriodOption GetByCompanyId(int companyId)
        {
            return AppServices.Cache.Get("closedPeriod:" + companyId, TimeSpan.FromMinutes(5), () =>
            {
                string redisKey = GetRedisCompanyKey(companyId);
                string redisValue = Core.GetRedisValue(redisKey);
                if (redisValue != null)
                {
                    var option = Map(JsonConvert.DeserializeObject<ClosedPeriodOption>(redisValue));
                    if (redisValue == "{}")
                        option.CompanyId = companyId;
                    return option;
                }

                var sqlValue = SqlMapper.Query<ClosedPeriodOption>(
                    "SELECT * FROM ClosedAccountingPeriodOptions WITH (NOLOCK) WHERE Deleted=0 AND CompanyId=@CompanyId",
                    new { CompanyId = companyId }).FirstOrDefault();

                Core.SetRedisValue(redisKey, sqlValue != null ? JsonConvert.SerializeObject(sqlValue) : "{}", TimeSpan.FromDays(30));

                return Map(sqlValue);
            });
        }

        public static async Task<ClosedPeriodOption> GetByCompanyIdAsync(int companyId)
        {
            return await AppServices.Cache.Get("closedPeriod:" + companyId, TimeSpan.FromMinutes(5), async () =>
            {
                string redisKey = GetRedisCompanyKey(companyId);
                string redisValue = await Core.GetRedisValueAsync(redisKey);
                if (redisValue != null)
                {
                    var option = Map(JsonConvert.DeserializeObject<ClosedPeriodOption>(redisValue));
                    if (redisValue == "{}")
                        option.CompanyId = companyId;
                    return option;
                }

                var sqlValue = (await SqlMapper.QueryAsync<ClosedPeriodOption>(
                    "SELECT * FROM ClosedAccountingPeriodOptions WITH (NOLOCK) WHERE Deleted=0 AND CompanyId=@CompanyId",
                    new { CompanyId = companyId }
                    )).FirstOrDefault();

                await Core.SetRedisValueAsync(redisKey, sqlValue != null ? JsonConvert.SerializeObject(sqlValue) : "{}", TimeSpan.FromDays(30));

                return Map(sqlValue);
            });
        }

        public static IEnumerable<ClosedPeriodOption> GetByCompanyIds(int[] companyIds)
        {
            var options = new List<ClosedPeriodOption>();

            // TODO: this isn't performant (n database/network calls on a loop)
            foreach (int companyId in companyIds)
            {
                var option = GetByCompanyId(companyId);

                if(option != null)
                    options.Add(GetByCompanyId(companyId));
            }

            return options;
        }

        public static async Task<IEnumerable<ClosedPeriodOption>> GetByCompanyIdsAsync(int[] companyIds)
        {
            var options = new List<ClosedPeriodOption>();

            // TODO: this isn't performant (n database/network calls on a loop)
            foreach (int companyId in companyIds)
            {
                var option = await GetByCompanyIdAsync(companyId);

                if(option != null)
                    options.Add(await GetByCompanyIdAsync(companyId));
            }

            return options;
        }

        public async Task<ClosedPeriodOption> SaveAsync(User user, string ipAddress = null)
        {
            if (user == null || CompanyId == 0 || ModificationMethod == ModificationMethodEnum.None)
                throw new Exception("User or company or modification method is missing and must be provided.");

            if (!ChangedFields.Any(a => new string[] { "Password", "CloseDate", "ModificationMethod", "Enabled" }.Contains(a.Field)))
                return this;

            var changeLogs = new List<string>();

            // Log all changes when first turning settings on
            if (Id == 0)
            {
                changeLogs.Add($"Turned closed period {(Enabled ? "on" : "off")}");
                if (ClosedDate != null)
                    changeLogs.Add($"Close date set to {ClosedDate.Value.ToShortDateString()}");
                if (ModificationMethod != ModificationMethodEnum.None)
                    changeLogs.Add($"Modify method set to {ModificationMethod.ToString()}");
                if (password != null)
                    changeLogs.Add("Updated password");
            }

            // Delete the existing settings (keep historical copy)
            if (Id > 0)
            {
                if (ChangedFields.Any(a => a.Field == "Enabled"))
                {
                    changeLogs.Add($"Turned closed period {(Enabled ? "on" : "off")}");
                }
                if (ChangedFields.Any(a => a.Field == "CloseDate"))
                {
                    if (ClosedDate != null)
                        changeLogs.Add($"Close date set to {ClosedDate.Value.ToShortDateString()}");
                }
                if (ChangedFields.Any(a => a.Field == "ModificationMethod"))
                {
                    changeLogs.Add($"Modify method set to {ModificationMethod}");
                }
                if (ChangedFields.Any(a => a.Field == "Password") && password != null && ModificationMethod == ModificationMethodEnum.Password)
                {
                    changeLogs.Add("Updated password");
                }

                InternalDelete(user);

                // reset Id to always insert new row
                Id = 0;
                Deleted = false;
                DeletedByUserId = (int?)null;
                DeletedDate = (DateTime?)null;
            }


            // Don't make this an else if - existing but not deleted settings will also go through this block
            if (Id == 0 && !Deleted)
            {
                OwnerUserId = user.Id;
                CreateDate = DateTime.Now;
                Id = (int)await SqlMapper.InsertAsync(this);

                await PostUpdateTasks();
            }

            #region record changes as history items
            if (changeLogs.Any() && Id > 0)
            {
                var changedLogDate = DateTime.Now;

                foreach (string changeLog in changeLogs)
                {
                    var log = new ClosedPeriodOptionHistory()
                    {
                        ClosedAccountingPeriodOptionId = Id,
                        CompanyId = this.CompanyId,
                        Description = changeLog,
                        CreateDate = changedLogDate
                    };

                    await log.SaveAsync(user, ipAddress);
                }
            }
            #endregion

            return this;
        }

        private void InternalDelete(User user)
        {
            if (user == null)
                return;

            Deleted = true;
            DeletedDate = DateTime.Now;
            DeletedByUserId = user.Id;

            SqlMapper.Update(this);
        }

        public async Task Delete(User user)
        {
            InternalDelete(user);
            var c = Company.GetById(CompanyId);
            await PostUpdateTasks();
        }

        private async Task PostUpdateTasks()
        {
            await Core.DeleteRedisKeyAsync(GetRedisCompanyKey(CompanyId));
            AppServices.Cache.InvalidateCacheItem("closedPeriod:" + CompanyId);
            var company = Company.GetById(CompanyId);
            await company.InvalidateCache();
            await PushNotificationProvider.Push(CompanyId, "company_update", new { companyId = CompanyId });
            await Caching.CacheWorkerUtility.UpdateCompany(company);
        }
    }
}
