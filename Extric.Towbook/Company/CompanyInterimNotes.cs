using Extric.Towbook.Utility;
using Glav.CacheAdapter.Core.DependencyInjection;
using ProtoBuf;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Extric.Towbook.Company
{
    [ProtoContract]
    public class CompanyInterimNotes
    {
        //private const string CacheCompanyInterimNotes = "int_cin_companyNotes_{0}";

        public int CompanyInterimNotesId { get; set; }
        public int CompanyId { get; set; }
        public string Notes { get; set; }
        public string OpenItems { get; set; }

        public CompanyInterimNotes()
        {
        }

        public CompanyInterimNotes(int companyId, string notes, string openItems)
        {
            this.CompanyId = companyId;
            this.Notes = notes;
            this.OpenItems = openItems;
        }

        public static CompanyInterimNotes GetByCompanyId(int companyId)
        {
            //return AppServices.Cache.Get<CompanyInterimNotes>(string.Format(CacheCompanyInterimNotes, companyId),
            //    TimeSpan.FromDays(30),
            //    () =>
            //    {
            //        return Map(SqlMapper.QuerySP<dynamic>("dbo.CompanyInterimNotesGetByCompanyId",
            //            new
            //            {
            //                @CompanyId = companyId
            //            }));
            //    });
            return Map(SqlMapper.QuerySP<dynamic>("dbo.CompanyInterimNotesGetByCompanyId",
                        new
                        {
                            @CompanyId = companyId
                        }));
        }

        public void Save()
        {
            SqlMapper.ExecuteSP("dbo.CompanyInterimNotesSave",
                                new
                                {
                                    @CompanyId = this.CompanyId,
                                    @Notes = this.Notes,
                                    @OpenItems = this.OpenItems
                                });

            //AppServices.Cache.InvalidateCacheItem(string.Format(CacheCompanyInterimNotes, this.CompanyId));
        }

        private static CompanyInterimNotes Map(IEnumerable<dynamic> data)
        {
            if (data.Any())
            {
                var note = data.Single();
                return new CompanyInterimNotes()
                {
                    CompanyInterimNotesId = note.CompanyInterimNotesId,
                    CompanyId = note.CompanyId,
                    Notes = note.Notes,
                    OpenItems = note.OpenItems
                };
            }
            else
            {
                return new CompanyInterimNotes(0, string.Empty, string.Empty);
            }
        }
    }
}
