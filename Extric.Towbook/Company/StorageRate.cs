using Glav.CacheAdapter.Core.DependencyInjection;
using ProtoBuf;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Extric.Towbook.Company
{
    [Serializable]
    [ProtoContract]
    public class StorageRate
    {
        private const int CacheTimeout = 1440;
        
        public enum StorageChargeStartEnum
        {
            [Description("Begin at the next midnight crossover")]
            Midnight = 0,
            [Description("Begin immediately after the grace period")]
            Immediate = 1,
            [Description("Begin immediately at impound date regardless of a grace period assigned")]
            ImmediateIgnoreGrace = 2,
            [Description("Each 24 hrs from the time of impound after the grace period has ended")]
            Each24HoursIgnoreGrace = 3
        }

        #region Public Properties
      
        [ProtoMember(1)]
        public int Id { get; private set; }
        
        [ProtoMember(2)]
        public int CompanyId { get; set; }

        [ProtoMember(3)]
        public StorageChargeStartEnum StorageChargeStart { get; set; }

        [ProtoMember(4)]
        public int StorageChargeInitialHoursLimitToOneDay { get; set; }

        [ProtoMember(5)]
        public double StorageRoundingValue { get; set; }

        /// <summary>
        /// If this isn't null, then the storage rate belongs to an Account rather than the global company. 
        /// </summary>
        [ProtoMember(6)]
        public int AccountId { get; set; }

        [ProtoMember(7)]
        public int StorageGracePeriodHours { get; set; }

        [ProtoMember(8)]
        public decimal MaximumCharge { get; set; }

        /// <summary>
        /// Determines whether or not to charge storage on Saturdays
        /// </summary>
        [ProtoMember(9)]
        public bool FreeSaturdays { get; set; }

        /// <summary>
        /// Determines whether or not to charge storage on Sundays
        /// </summary>
        [ProtoMember(10)]
        public bool FreeSundays { get; set; }

        /// <summary>
        /// Determines whether or not to charge at Midnight regardless of grace period
        /// </summary>
        [ProtoMember(11)]
        public bool MidnightWaitForGracePeriod { get; set; }

        #endregion

        public StorageRate()
        {
            Id = -1;
        }

        public static StorageRate GetByCompanyId(int companyId)
        {
            return AppServices.Cache.Get("storageRate_c:" + companyId,
                DateTime.Now.AddMinutes(30), () =>
                {
                    using (SqlDataReader dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                        "StorageRatesGetByCompanyId", new SqlParameter("@CompanyId", companyId)))
                    {
                        if (dr.Read())
                        {
                            return new CacheCollection<StorageRate>(new[] { new StorageRate(dr) });
                        }
                        else
                        {
                            return new CacheCollection<StorageRate>();
                        }
                    }
                }).ItemsOrEmpty().FirstOrDefault();
        }
        public static async Task<StorageRate> GetByCompanyIdAsync(int companyId)
        {
            var container = await AppServices.Cache.GetAsync("storageRate_c:" + companyId,
                DateTime.Now.AddMinutes(30), async () =>
                {
                    using (SqlDataReader dr = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString,
                        "StorageRatesGetByCompanyId", new SqlParameter("@CompanyId", companyId)))
                    {
                        if (await dr.ReadAsync())
                        {
                            return new CacheCollection<StorageRate>(new[] { new StorageRate(dr) });
                        }
                        else
                        {
                            return new CacheCollection<StorageRate>();
                        }
                    }
                });

            return container.ItemsOrEmpty().FirstOrDefault();
        }

        public static StorageRate GetByAccountId(int companyId, int accountId)
        {
            var container = AppServices.Cache.Get("storageRate_c_a:" + companyId + "," + accountId,
                DateTime.Now.AddMinutes(30), () =>
                {
                    using (SqlDataReader dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                        "StorageRatesGetByAccountId",
                        new SqlParameter("@CompanyId", companyId),
                        new SqlParameter("@AccountId", accountId)))
                    {
                        if (dr.Read())
                        {
                            return new CacheCollection<StorageRate>(new[] { new StorageRate(dr) });
                        }
                        else
                        {
                            return new CacheCollection<StorageRate>();
                        }
                    }
                });

            return container.ItemsOrEmpty().FirstOrDefault();
        }

        public static async Task<StorageRate> GetByAccountIdAsync(int companyId, int accountId)
        {
            var container = await AppServices.Cache.GetAsync("storageRate_c_a:" + companyId + "," + accountId,
                DateTime.Now.AddMinutes(30), async () =>
                {
                    using (SqlDataReader dr = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString,
                        "StorageRatesGetByAccountId",
                        new SqlParameter("@CompanyId", companyId),
                        new SqlParameter("@AccountId", accountId)))
                    {
                        if (await dr.ReadAsync())
                        {
                            return new CacheCollection<StorageRate>(new[] { new StorageRate(dr) });
                        }
                        else
                        {
                            return new CacheCollection<StorageRate>();
                        }
                    }
                });

            return container.ItemsOrEmpty().FirstOrDefault();
        }

        protected StorageRate(SqlDataReader reader)
		{
			InitializeFromDataReader(reader);
		}

        private void InitializeFromDataReader(SqlDataReader reader)
		{
			Id = reader.GetValue<int>("StorageRateId");
            CompanyId = reader.GetValue<int>("CompanyId");
            AccountId = reader.GetValue<int>("AccountId");

            MaximumCharge = reader.GetValue<decimal>("MaximumCharge");
            StorageGracePeriodHours = Convert.ToInt32(reader["ImpoundStorageRatesGracePeriodHours"]);

            StorageChargeStart = (StorageChargeStartEnum)reader.GetValue<int>("ImpoundStorageChargeStart");
            StorageChargeInitialHoursLimitToOneDay = reader.GetValue<int>("ImpoundStorageInitialHoursLimitToOneDay");
            StorageRoundingValue = reader.GetValue<double>("ImpoundStorageRound");

            FreeSaturdays = reader.GetValue<bool>("FreeSaturdays");
            FreeSundays = reader.GetValue<bool>("FreeSundays");

            MidnightWaitForGracePeriod = reader.GetValue<bool>("MidnightWaitForGracePeriod");
		}

        public  static StorageRate Copy(StorageRate input)
        {
            return new StorageRate
            {
                Id = input.Id,
                CompanyId = input.CompanyId,
                AccountId = input.AccountId,
                MaximumCharge = input.MaximumCharge,
                StorageGracePeriodHours = input.StorageGracePeriodHours,
                StorageChargeStart = input.StorageChargeStart,
                StorageChargeInitialHoursLimitToOneDay = input.StorageChargeInitialHoursLimitToOneDay,
                StorageRoundingValue = input.StorageRoundingValue,
                FreeSaturdays = input.FreeSaturdays,
                FreeSundays = input.FreeSundays,
                MidnightWaitForGracePeriod = input.MidnightWaitForGracePeriod
            };
        }

        /// <summary>
        /// Saves the Storage Rate object to the data store.
        /// </summary>
        public void Save()
        {
            if (Id == 0)
            {
                throw new Extric.Towbook.TowbookException("No such Item. Can't save " +
                    "object! (this object should have already been discarded!)");
            }

            if (Id == -1)
            {
                DbInsert();
            }
            else
            {
                DbUpdate();
            }

            if (this.AccountId == 0)
                AppServices.Cache.InvalidateCacheItem("storageRate_c:" + this.CompanyId);
            
            if (this.AccountId != 0)
                AppServices.Cache.InvalidateCacheItem("storageRate_c_a:" + this.CompanyId + "," + this.AccountId);
        }

        private void DbInsert()
        {
            Id = Convert.ToInt32(SqlHelper.ExecuteScalar(Core.ConnectionString,
                "StorageRatesInsert",
                new SqlParameter("@CompanyId", CompanyId),
                new SqlParameter("@AccountId", (AccountId > 0 ? (int?)AccountId : null)),
                new SqlParameter("@MaximumCharge", MaximumCharge),
				new SqlParameter("@ImpoundStorageRatesGracePeriodHours", StorageGracePeriodHours),
                new SqlParameter("@ImpoundStorageChargeStart", StorageChargeStart),
                new SqlParameter("@ImpoundStorageInitialHoursLimitToOneDay", StorageChargeInitialHoursLimitToOneDay),
                new SqlParameter("@FreeSaturdays", FreeSaturdays),
                new SqlParameter("@FreeSundays", FreeSundays),
                new SqlParameter("@MidnightWaitForGracePeriod", MidnightWaitForGracePeriod)
                ));
        }

        private void DbUpdate()
        {
            SqlHelper.ExecuteNonQuery(Core.ConnectionString,
                "StorageRatesUpdateById",
                new SqlParameter("@StorageRateId", Id),
                new SqlParameter("@CompanyId", CompanyId),
                new SqlParameter("@AccountId", (AccountId > 0 ? (int?) AccountId : null)),
                new SqlParameter("@MaximumCharge", MaximumCharge),
				new SqlParameter("@ImpoundStorageRatesGracePeriodHours", StorageGracePeriodHours),
                new SqlParameter("@ImpoundStorageChargeStart", StorageChargeStart),
                new SqlParameter("@ImpoundStorageInitialHoursLimitToOneDay", StorageChargeInitialHoursLimitToOneDay),
                new SqlParameter("@FreeSaturdays", FreeSaturdays),
                new SqlParameter("@FreeSundays", FreeSundays),
                new SqlParameter("@MidnightWaitForGracePeriod", MidnightWaitForGracePeriod));
        }
    }
}
