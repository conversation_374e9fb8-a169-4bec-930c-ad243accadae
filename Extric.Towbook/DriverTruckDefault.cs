using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Extric.Towbook.Integration;
using Extric.Towbook.Utility;
using Glav.CacheAdapter.Core.DependencyInjection;
using Async = System.Threading.Tasks;

namespace Extric.Towbook
{
    public enum DriverTruckDefaultSource
    {
        Unspecified = 0,
        DefaultTruckAPI = 1, 
        TomTom = 2,
    }

    public class DriverTruckDefault
    {
        private const string cacheFormat = "dta-{0}";

        public int Id { get; set; }
        public int DriverId { get; set; }
        public int TruckId { get; set; }
        public DriverTruckDefaultSource SourceId { get; set; }
        public bool Deleted { get; set; }
        public DateTime CreateDate { get; private set; }
        public DateTime ModifiedDate { get; private set; }
        public int ModifiedUserId { get; set; }

        public DriverTruckDefault()
        {

        }

        public static DriverTruckDefault Map(dynamic o)
        {
            if (o == null)
                return null;

            return new DriverTruckDefault()
            {
                Id = o.DriverTruckDefaultId,
                DriverId = o.DriverId,
                TruckId = o.TruckId,
                SourceId = (DriverTruckDefaultSource)o.SourceId,
                Deleted = o.Deleted,
                CreateDate = o.CreateDate,
                ModifiedDate = o.ModifiedDate,
                ModifiedUserId = o.ModifiedUserId
            };
        }

        public async Task<DriverTruckDefault> Save(int userId)
        {
            if (TruckId == 0)
                return null;
            if (DriverId == 0)
                return null;


            if (this.Id == 0)
            {
                var d = SqlMapper.QuerySP<dynamic>("dbo.DriverTruckDefaultsInsert",
                    new
                    {
                        @DriverId = DriverId,
                        @TruckId = TruckId,
                        @SourceId = SourceId,
                        @ModifiedUserId = userId
                    }).FirstOrDefault();

                this.Id = d.DriverTruckDefaultId;
                this.ModifiedUserId = d.ModifiedUserId; 
                this.CreateDate = d.CreateDate;
                this.ModifiedDate = d.ModifiedDate;
            }
            else
            {
                SqlMapper.ExecuteSP("dbo.DriverTruckDefaultsUpdateById",
                    new
                    {
                        @DriverTruckDefaultId = this.Id,
                        @DriverId = this.DriverId,
                        @TruckId = this.TruckId,
                        @SourceId = this.SourceId,
                        @Deleted = this.Deleted,
                        @ModifiedUserId = userId
                    });
            }

            AppServices.Cache.InvalidateCacheItem(String.Format(cacheFormat, this.Id));

            var companyId = (await Driver.GetByIdAsync(DriverId))?.CompanyId;

            await Caching.CacheWorkerUtility.UpdateDriverTruckDefault(this);

            if (companyId != null)
                await PushNotificationProvider.Push(companyId.Value, "driver_truck_update", 
                    new {
                        driverId = DriverId,
                        truckId = TruckId,
                        sourceId = SourceId,
                        type = Deleted ? "delete" : "update"
                    });
            return this;
        }

        public async Async.Task Delete(int userId)
        {
            this.Deleted = true;
            await Save(userId);
        }

        public static Collection<DriverTruckDefault> GetByCompanyId(int companyId)
        {
            return SqlMapper.QuerySP<dynamic>("dbo.DriverTruckDefaultsGetByCompanyId",
                new { @CompanyId = companyId})
                .Select<dynamic, DriverTruckDefault>(o => Map(o))
                .ToCollection();
        }

        public static Collection<DriverTruckDefault> GetByCompanyId(int[] companyId)
        {
            return SqlMapper.QuerySP<dynamic>("dbo.DriverTruckDefaultsGetByCompanyId",
                new { @CompanyId = string.Join(",", companyId) })
                .Select<dynamic, DriverTruckDefault>(o => Map(o))
                .ToCollection();
        }

        public static DriverTruckDefault GetByDriverId(int driverId)
        {
            return SqlMapper.QuerySP<dynamic>("dbo.DriverTruckDefaultsGetByDriverId",
                new { @DriverId = driverId })
                .Select(o => Map(o)).FirstOrDefault();
        }

        public static async Task<DriverTruckDefault> GetByDriverIdAsync(int driverId)
        {
            return (await SqlMapper.QuerySpAsync<dynamic>("dbo.DriverTruckDefaultsGetByDriverId",
                new { @DriverId = driverId }))
                .Select(o => Map(o)).FirstOrDefault();
        }

        public static Collection<DriverTruckDefault> GetByTruckId(int truckId)
        {
            return SqlMapper.QuerySP<dynamic>("dbo.DriverTruckDefaultsGetByTruckId",
                new { TruckId = truckId })
                .Select<dynamic, DriverTruckDefault>(o => Map(o)).ToCollection();
        }

        public static async Task<Collection<DriverTruckDefault>> GetByTruckIdAsync(int truckId)
        {
            return (await SqlMapper.QuerySpAsync<dynamic>("dbo.DriverTruckDefaultsGetByTruckId",
                new { TruckId = truckId }))
                .Select<dynamic, DriverTruckDefault>(o => Map(o)).ToCollection();
        }


    }
}
