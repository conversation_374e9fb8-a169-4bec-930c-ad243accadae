using Extric.Towbook.Utility;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Extric.Towbook.EventNotifications
{
    [Table("EventUserTypeNotifications")]
    public class EventUserTypeNotification
    {
        [Key("EventUserTypeNotificationId")]
        public int Id { get; set; }
        public int EventNotificationId { get; set; }
        public int UserTypeId { get; set; }
        public int CompanyId { get; set; }
        public bool? RequireEmail { get; set; }
        public bool? RequireText { get; set; }
        public bool? RequirePushNotification { get; set; }
        public bool? RequireWebNotification { get; set; }
        public int OwnerUserId { get; set; }
        public DateTime CreateDate { get; private set; }
        public bool Deleted { get; private set; }
        public int? DeletedByUserId { get; set; }
        public DateTime? DeleteDate { get; private set; }

        public EventUserTypeNotification()
        {
            Id = 0;
        }

        public static EventUserTypeNotification GetById(int id)
        {
            return SqlMapper.Query<EventUserTypeNotification>(
                "SELECT * FROM EventUserTypeNotifications WHERE EventUserTypeNotificationId=@Id and Deleted=0", new { Id = id }).FirstOrDefault();
        }

        public static async Task<EventUserTypeNotification> GetByIdAsync(int id)
        {
            return (await SqlMapper.QueryAsync<EventUserTypeNotification>(
                "SELECT * FROM EventUserTypeNotifications WHERE EventUserTypeNotificationId=@Id and Deleted=0", new { Id = id })).FirstOrDefault();
        }

        public static Collection<EventUserTypeNotification> GetByCompanyId(int companyId)
        {
            var rv = SqlMapper.Query<EventUserTypeNotification>(
                "SELECT * FROM EventUserTypeNotifications WHERE CompanyId=@Id and Deleted=0", new { Id = companyId })
                .ToCollection();

            return rv;
        }
        
        public static async Task<Collection<EventUserTypeNotification>> GetByCompanyIdAsync(int companyId)
        {
            return (await SqlMapper.QueryAsync<EventUserTypeNotification>(
                "SELECT * FROM EventUserTypeNotifications WHERE CompanyId=@Id and Deleted=0", new { Id = companyId }))
                .ToCollection();
        }

        public static Collection<EventUserTypeNotification> GetByCompanyId(int companyId, int notificationItemId)
        {
            var rv = SqlMapper.Query<EventUserTypeNotification>(
                "SELECT * FROM EventUserTypeNotifications WHERE CompanyId=@Id AND EventNotificationId=@NotificationItemId AND Deleted=0", new { Id = companyId, NotificationItemId = notificationItemId })
                .ToCollection();

            return rv;
        }

        public static IEnumerable<EventUserTypeNotification> GetByCompanyId(int companyId, int notificationItemId, int userTypeId)
        {
            return SqlMapper.Query<EventUserTypeNotification>(
                $"SELECT * FROM EventUserTypeNotifications WHERE " +
                $"  CompanyId=@CompanyId AND EventNotificationId=@Id AND UserTypeId=@UserTypeId AND Deleted=0", 
                new {
                    CompanyId = companyId,
                    Id = notificationItemId,
                    UserTypeId = userTypeId
                });
        }

        public static async Task<IEnumerable<EventUserTypeNotification>> GetByCompanyIdAsync(int companyId, int notificationItemId, int userTypeId)
        {
            return await SqlMapper.QueryAsync<EventUserTypeNotification>(
                $"SELECT * FROM EventUserTypeNotifications WHERE " +
                $"  CompanyId=@CompanyId AND EventNotificationId=@Id AND UserTypeId=@UserTypeId AND Deleted=0",
                new
                {
                    CompanyId = companyId,
                    Id = notificationItemId,
                    UserTypeId = userTypeId
                });
        }

        public EventUserTypeNotification Save(int? userId = null)
        {
            if (Id < 1)
            {
                this.CreateDate = DateTime.Now;
                this.OwnerUserId = userId ?? 0;

                Id = (int)SqlMapper.Insert(this);
            }
            else
            {
                SqlMapper.Update(this);
            }

            return this;
        }

        public EventUserTypeNotification Delete(int userId)
        {
            Deleted = true;
            DeleteDate = DateTime.Now;
            DeletedByUserId = userId;
            return Save();
        }

        public static void Revert(int notificationId, int companyId, int userId)
        {
            var items = GetByCompanyId(companyId, notificationId);
            foreach (var item in items)
                item.Delete(userId);
        }
    }
}
