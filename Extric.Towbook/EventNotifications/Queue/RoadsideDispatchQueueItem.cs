using Extric.Towbook.Utility;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Extric.Towbook.EventNotifications
{
    public enum RoadsideDispatchTriggerType
    {
        Unspecified = 0,
        CreateRoadsideUser = 1,
        PredictedArrivalAlert = 2
    }

    public class RoadsideDispatchQueueModel
    {
        public int DispatchId { get; set; }
        public int? DispatchEntryContactId { get; set; }
        public int? DispatchEntryId { get; set; }
        public RoadsideDispatchTriggerType Type { get; set; }

        public static RoadsideDispatchQueueModel Map(RoadsideDispatchQueueItem o)
        {
            return new RoadsideDispatchQueueModel()
            {
                DispatchId = o.DispatchId,
                Type = o.Type,
                DispatchEntryContactId = o.DispatchEntryContactId,
                DispatchEntryId = o.DispatchEntryId
            };
        }

    }



    public class RoadsideDispatchQueueItem : EventNotificationQueueItem
    {
        public int DispatchId { get; set; }
        public int? DispatchEntryContactId { get; set; }
        public int? DispatchEntryId { get; set; }
        public RoadsideDispatchTriggerType Type { get; set; }

        // for testing purposes
        protected bool TestMode { get; set; }

        public override string QueueName
        {
            get { return GetQueueName(); }
        }

        public override string MessagePrefix
        {
            get { return "RoadsideDispatchSyncService/"; }
        }


        public RoadsideDispatchQueueItem()
        {
            Type = RoadsideDispatchTriggerType.Unspecified;
        }

        public static string GetQueueName()
        {
            return "EN-RoadsideDispatchQueue";
        }

        public override async Task TriggerEvent()
        {
            if (Type == RoadsideDispatchTriggerType.Unspecified)
                return;

            Dictionary<string, object> x = new Dictionary<string, object>();

            x.Add("EventNotificationId", (int)Type);

            if (TestMode)
            {
                x.Add("TestMode", "true");
            }

            var success = false;
            switch (Type)
            {
                case RoadsideDispatchTriggerType.CreateRoadsideUser:
                    success = await ENServiceBusHelper.SendMessageAsync(QueueName, string.Empty, RoadsideDispatchQueueModel.Map(this), "Roadside/Dispatch Queue Item Event -> Create Roadside User", x, null);
                    break;

                case RoadsideDispatchTriggerType.PredictedArrivalAlert:
                    success = await ENServiceBusHelper.SendMessageAsync(QueueName, string.Empty, RoadsideDispatchQueueModel.Map(this), "Roadside / Dispatch Queue Item Event-> Predicted Arrival Alert", x, null);
                    break;
            }
        }
    }
}
