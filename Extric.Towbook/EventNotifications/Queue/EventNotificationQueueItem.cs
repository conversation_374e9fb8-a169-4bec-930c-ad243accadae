using System.Collections.Generic;
using Extric.Towbook.Utility;
using NLog;
using System;
using System.Collections.Concurrent;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Async = System.Threading.Tasks;


namespace Extric.Towbook.EventNotifications
{
    public enum DeliveryMethodType
    {
        Internal = 0,
        Email,
        Text,
        Mobile,
        Desktop
    }

    abstract public class EventNotificationQueueItem
    {
        abstract public string QueueName { get; }
        abstract public string MessagePrefix { get; }

        abstract public Task TriggerEvent();
    }

    // ENServiceBusHelper It's been moved into it's own file

    public class LogInfo
    {
        public int ServiceType { get; set; }
        public string MessageId { get; set; }
        public string ServiceName { get; set; }
        public int EventNotificationId { get; set; }
        public int CompanyId { get; set; }
        public int AccountId { get; set; }
        public int CallNumber { get; set; }
        public int DispatchEntryId { get; set; }
        public bool TestMode { get; set; }
        public bool FlushRequested { get; set; }
        public string SecondaryPrefix { get; set; }

        public List<LogInfoEvent> Events { get; set; } = new List<LogInfoEvent>();

        private int _index = 0;

        public LogInfo(string messageId, int companyId, bool testMode, string serviceName = "EN Log")
        {
            MessageId = messageId;
            CompanyId = companyId;
            TestMode = testMode;
            ServiceName = serviceName;
        }

        public string Prefix(string message)
        {
            var secondaryPrefix = (string.IsNullOrWhiteSpace(SecondaryPrefix) ? "" : "-" + SecondaryPrefix);

            return $"{MessageId}-{_index++:00}{secondaryPrefix}: {message}";
        }

        public Dictionary<object, object> GetProperties(Dictionary<object, object> appendTo = null)
        {
            var properties = appendTo ?? new Dictionary<object, object>();

            if (!properties.ContainsKey("eventNotificationId"))
                properties.Add("eventNotificationId", EventNotificationId);

            if (!properties.ContainsKey("companyId"))
                properties.Add("companyId", CompanyId);

            if (!properties.ContainsKey("accountId") && AccountId > 0)
                properties.Add("accountId", AccountId);

            if (!properties.ContainsKey("dispatchEntryId") && DispatchEntryId > 0)
                properties.Add("dispatchEntryId", DispatchEntryId);

            if (!properties.ContainsKey("callNumber") && CallNumber > 0)
                properties.Add("callNumber", CallNumber);

            if (!properties.ContainsKey("testMode"))
                properties.Add("testMode", TestMode);

            if (!properties.ContainsKey("serviceName"))
                properties.Add("serviceName", ServiceName);

            return properties;
        }

        public void AddEvent(string message, LogLevel level, Dictionary<object, object> properties = null)
        {
            Events.Add(new LogInfoEvent()
            {
                Message = Prefix(message),
                Level = level,
                Properties = properties
            });
        }

        public void FlushEvents(Logger logger)
        {
            foreach (var ev in Events)
            {
                logger.LogEvent(ev.Message, CompanyId, ev.Level, GetProperties(ev.Properties));
            }

            Events.Clear();
            FlushRequested = false;
        }
    }

    public class LogInfoEvent
    {
        public string Message { get; set; }
        public LogLevel Level { get; set; }
        public Dictionary<object, object> Properties { get; set; }
    }

}
