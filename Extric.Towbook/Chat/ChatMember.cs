using Extric.Towbook.Integration;
using Extric.Towbook.Utility;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;

namespace Extric.Towbook.Chat
{
    public class ChatMember : IUsesSqlKey
    {
        #region Constants
        /// <summary>
        /// Participant is the default role of a chat member.  Participants are included
        /// by their role on the call; for instance, drivers get added as participants.
        /// </summary>
        public const int BUILTIN_CHATMEMBER_ROLE_PARTICIPANT = 1;

        /// <summary>
        /// At the time of a call being dispatched, a Dispatcher will be assigned as a
        /// participant.  However, dispatchers are unique in that they can see chat messages
        /// on call chats when they are not particpants
        /// </summary>
        public const int BUILTIN_CHATMEMBER_ROLE_DISPATCHER = 2;

        /// <summary>
        /// Supervisor is a role that has special access to chats and certain actions.  
        /// This is intended for managers user types or chat creaters.
        /// </summary>
        public const int BUILTIN_CHATMEMBER_ROLE_SUPERVISOR = 3; 
        #endregion


        [Key("ChatMemberId")]
        public int ChatMemberId { get; set; }

        public long ChatId { get; set; }

        /// <summary>
        /// The UserId that this member class represents.
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// The role that this user has (admin, participant, etc)
        /// </summary>
        public int RoleId { get; set; }

        /// <summary>
        /// If the member is muted, they're not allowed to send messages to the chat.
        /// </summary>
        public bool Muted { get; set; }

        /// <summary>
        /// Who added this member?
        /// </summary>
        public int OwnerUserId { get; set; }

        /// <summary>
        /// When was the member added.
        /// </summary>
        public DateTime CreateDate { get; private set; }

        /// <summary>
        /// Is the member still in the chat?
        /// </summary>
        public bool Deleted { get; set; }

        [Write(false)]
        public int? StatusId { get; set; }

        public ChatMember()
        {

        }

        public static async Task<Collection<ChatMember>> GetByChatIdAsync(long chatId, bool ignoreDeleted = true)
        {
            string ignore = " AND Deleted=0";
            if (!ignoreDeleted)
                ignore = "";

            return (await SqlMapper.QueryAsync<ChatMember>(
                "SELECT * FROM ChatMembers WHERE ChatId=@ChatId" + ignore,
                new
                {
                    ChatId = chatId
                })).ToCollection();
        }

        public static Collection<ChatMember> GetByUserId(long chatId, int userId)
        {
            return SqlMapper.Query<ChatMember>(
                "SELECT * FROM ChatMembers WHERE ChatId=@ChatId AND UserId=@UserId AND Deleted=0",
                new {ChatId = chatId, UserId = userId}).ToCollection();
        }

        public static IEnumerable<ChatMember> GetByChatIdWithStatus(long chatId)
        {
            return SqlMapper.Query<ChatMember>(
                @"SELECT CM.*, CUS.StatusId FROM ChatMembers CM LEFT JOIN ChatUserStatuses CUS ON CUS.UserId = CM.UserId  WHERE ChatId=@ChatId",
                new { ChatId = chatId });
        }

        public static async Task<IEnumerable<ChatMember>> GetByChatIdWithStatusAsync(long chatId)
        {
            return await SqlMapper.QueryAsync<ChatMember>(
                @"SELECT CM.*, CUS.StatusId FROM ChatMembers CM LEFT JOIN ChatUserStatuses CUS ON CUS.UserId = CM.UserId  WHERE ChatId=@ChatId",
                new { ChatId = chatId });
        }

        public void Save()
        {
            if (this.CreateDate == DateTime.MinValue)
                this.CreateDate = DateTime.Now;

            if (ChatMemberId < 1)
            {
                SqlMapper.Insert(this);
            }
            else
            {
                SqlMapper.Update(this);
            }
        }
        
        public async Task SaveAsync()
        {
            if (this.CreateDate == DateTime.MinValue)
                this.CreateDate = DateTime.Now;

            if (ChatMemberId < 1)
            {
                await SqlMapper.InsertAsync(this);
            }
            else
            {
                await SqlMapper.UpdateAsync(this);
            }
        }

        public void Delete()
        {
            Deleted = true;
            Save();
        }
    }
}
