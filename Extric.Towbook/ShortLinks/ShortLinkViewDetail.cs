using Extric.Towbook.Utility;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Extric.Towbook.ShortLinks
{
    [Table("ShortLinkVisitDetails")]
    public class ShortLinkViewDetail
    {
        [Key("ShortLinkVisitDetailId")]
        public int Id { get; set; }
        public int ShortLinkId { get; set; }
        public DateTime VisitDate { get; set; }
        public string IpAddress { get; set; }
        public string OS { get; set; }
        public string Browser { get; set; }

        public void Save()
        {
            if (Id <= 0)
            {
                VisitDate = DateTime.Now;
                Id = (int)SqlMapper.Insert(this);
            }
            else
            {
                SqlMapper.Update(this);
            }
        }
    }
}
