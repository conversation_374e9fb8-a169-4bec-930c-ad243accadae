using Microsoft.Azure.Cosmos;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json.Serialization;
using NLog;
using System;
using System.Collections.ObjectModel;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using Async = System.Threading.Tasks;

namespace Extric.Towbook
{

    public class KeysJsonConverter : JsonConverter
    {
        private readonly Type[] _types;

        public KeysJsonConverter(params Type[] types)
        {
            _types = types;
        }

        public override void WriteJson(JsonWriter writer, object value, JsonSerializer serializer)
        {
            JToken t = JToken.FromObject(value.ToString());
            t.<PERSON>rite<PERSON>o(writer);
        }

        public override object ReadJson(JsonReader reader, Type objectType, object existingValue, JsonSerializer serializer)
        {
            return Convert.ToInt32(reader.Value);
        }

        public override bool CanRead
        {
            get { return true; }
        }

        public override bool CanConvert(Type objectType)
        {
            return true;
        }
    }


    public class JsonPropertyJsonConverter : JsonConverter
    {
        private readonly Type[] _types;

        public JsonPropertyJsonConverter(params Type[] types)
        {
            _types = types;
        }

        public override void WriteJson(JsonWriter writer, object value, JsonSerializer serializer)
        {
            // turn the string into a json object.
            JToken t = JToken.Parse(value.ToString());
            t.WriteTo(writer);
        }

        public override object ReadJson(JsonReader reader, Type objectType, object existingValue, JsonSerializer serializer)
        {
            // turn the json object into a string.
            var jo = JObject.Load(reader);

            return jo.ToString();
        }

        public override bool CanRead
        {
            get { return true; }
        }

        public override bool CanConvert(Type objectType)
        {
            return true;
        }
    }


    public class ShouldSerializeContractResolver : CamelCasePropertyNamesContractResolver
    {
        public static readonly ShouldSerializeContractResolver Instance = new ShouldSerializeContractResolver();

        protected override JsonProperty CreateProperty(MemberInfo member, MemberSerialization memberSerialization)
        {
            JsonProperty property = base.CreateProperty(member, memberSerialization);

            if (property.DeclaringType.Name == "CallModel")
            {
                switch (property.PropertyName)
                {
                    case "id":
                        {
                            if (property.PropertyType == typeof(int))
                            {

                                property.PropertyType = typeof(string);
                                property.Converter = new KeysJsonConverter(typeof(int));
                                property.Ignored = false;
                                property.ShouldSerialize =
                                    instance =>
                                    {
                                        return true;
                                    };
                            }
                        }
                        break;
                    case "availableActions":
                    case "channels":
                    case "chatChannelSid":
                    case "lastModifiedTimestamp":
                    case "statuses":
                    case "_ts":
                        property.Ignored = true;
                        break;
                }
            }
            if (property.DeclaringType.Name == "CallContactModel")
            {
                if (property.PropertyType == typeof(string))
                {
                    property.ShouldSerialize =
                        instance => !string.IsNullOrWhiteSpace(instance?.GetType().GetProperty(property.UnderlyingName)?.GetValue(instance) as string);
                }

                switch (property.PropertyName)
                {
                    case "isProblemCustomer":
                    case "callId":
                        property.Ignored = true;
                        break;
                }


            }
            else if (property.DeclaringType.Name == "ActivityLogItem")
            {
                switch (property.PropertyName)
                {
                    case "id":
                        {
                            if (property.PropertyType == typeof(long))
                            {
                                property.PropertyType = typeof(string);
                                property.Converter = new KeysJsonConverter(typeof(long));
                                property.Ignored = false;
                                property.ShouldSerialize =
                                    instance =>
                                    {
                                        return true;
                                    };
                            }
                        }
                        break;
                }
            }
            else if (property.DeclaringType.Name == "NotificationMessage")
            {
                switch (property.PropertyName)
                {
                    case "id":
                        {
                            if (property.PropertyType == typeof(int))
                            {
                                property.PropertyType = typeof(string);
                                property.Converter = new KeysJsonConverter(typeof(int));
                                property.Ignored = false;
                                property.ShouldSerialize =
                                    instance =>
                                    {
                                        return true;
                                    };
                            }
                        }
                        break;
                    case "json":
                        {
                            if (property.PropertyType == typeof(string))
                            {
                                property.PropertyType = typeof(JObject);
                                property.Converter = new JsonPropertyJsonConverter();
                                property.Ignored = false;
                                property.ShouldSerialize =
                                    instance =>
                                    {
                                        return true;
                                    };
                            }
                        }
                        break;
                }
            }
            else if (property.DeclaringType.Name == "QuoteModel")
            {
                switch (property.PropertyName)
                {
                    case "availableActions":
                    case "url":
                        property.Ignored = true;
                        break;
                }
            }

            return property;
        }
    }

    /// <summary>
    /// The Towbook Cosmos JSON.NET serializer. Needed to handle converting our ID's from Int to String.
    /// </summary>
    internal sealed class CosmosJsonTowbookSerializer : CosmosSerializer
    {
        private static readonly Encoding DefaultEncoding = new UTF8Encoding(false, true);
        private readonly JsonSerializerSettings SerializerSettings;

        /// <summary>
        /// Create a serializer that uses the JSON.net serializer
        /// </summary>
        /// <remarks>
        /// This is internal to reduce exposure of JSON.net types so
        /// it is easier to convert to System.Text.Json
        /// </remarks>
        internal CosmosJsonTowbookSerializer()
        {
            var jsonSerializerSettings = new JsonSerializerSettings()
            {
                NullValueHandling = NullValueHandling.Ignore,
                Formatting = Formatting.Indented,
                ContractResolver = new ShouldSerializeContractResolver()
            };

            this.SerializerSettings = jsonSerializerSettings;
        }

        /// <summary>
        /// Convert a Stream to the passed in type.
        /// </summary>
        /// <typeparam name="T">The type of object that should be deserialized</typeparam>
        /// <param name="stream">An open stream that is readable that contains JSON</param>
        /// <returns>The object representing the deserialized stream</returns>
        public override T FromStream<T>(Stream stream)
        {
            using (stream)
            {
                if (typeof(Stream).IsAssignableFrom(typeof(T)))
                {
                    return (T)(object)stream;
                }

                using (StreamReader sr = new StreamReader(stream))
                {
                    using (JsonTextReader jsonTextReader = new JsonTextReader(sr))
                    {
                        JsonSerializer jsonSerializer = this.GetSerializer();
                        return jsonSerializer.Deserialize<T>(jsonTextReader);
                    }
                }
            }
        }

        /// <summary>
        /// Converts an object to a open readable stream
        /// </summary>
        /// <typeparam name="T">The type of object being serialized</typeparam>
        /// <param name="input">The object to be serialized</param>
        /// <returns>An open readable stream containing the JSON of the serialized object</returns>
        public override Stream ToStream<T>(T input)
        {
            MemoryStream streamPayload = new MemoryStream();
            using (StreamWriter streamWriter = new StreamWriter(streamPayload,
                encoding: CosmosJsonTowbookSerializer.DefaultEncoding, bufferSize: 1024, leaveOpen: true))
            {
                using (JsonWriter writer = new JsonTextWriter(streamWriter))
                {
                    writer.Formatting = Newtonsoft.Json.Formatting.None;
                    JsonSerializer jsonSerializer = this.GetSerializer();
                    jsonSerializer.Serialize(writer, input);
                    writer.Flush();
                    streamWriter.Flush();
                }
            }

            streamPayload.Position = 0;
            return streamPayload;
        }

        /// <summary>
        /// JsonSerializer has hit a race conditions with custom settings that cause null reference exception.
        /// To avoid the race condition a new JsonSerializer is created for each call
        /// </summary>
        private JsonSerializer GetSerializer()
        {
            return JsonSerializer.Create(this.SerializerSettings);
        }
    }

    public class CosmosParameter
    {
        public string Key { get; set; }
        public object Value { get; set; }
        public CosmosParameter(string key, object value)
        {
            Key = key;
            Value = value;
        }
    }

    public class CosmosDB
    {
        public CosmosClient Client { get; private set; }

        private static Logger logger = LogManager.GetCurrentClassLogger();

        public string DatabaseId;
        private string CosmosdbUrl;
        private string CosmosdbAuthKey;

        private static readonly CosmosDB instance = PrivateInit();
        private static readonly CosmosDB instanceReadOnly = PrivateInit(true);

        public CosmosDB(string url, string authKey)
        {
            DatabaseId = GetDatabaseName();
            CosmosdbUrl = url;
            CosmosdbAuthKey = authKey;
        }

        public CosmosDB(string databaseId, string url, string authKey)
        {
            DatabaseId = databaseId;
            CosmosdbUrl = url;
            CosmosdbAuthKey = authKey;
        }

        private static string GetDatabaseName()
        {
            return Core.CosmosDatabase ?? "towbook";
        }

        private static CosmosDB Init(string url, string key, bool preferSecondary)
        {
            var client = new CosmosDB(url, key);

            if (string.IsNullOrEmpty(url))
                throw new TowbookException("Deployment Error: CosmosDB Configuration is missing value for: url");

            if (string.IsNullOrEmpty(key))
                throw new TowbookException("Deployment Error: CosmosDB Configuration is missing value for: key");

            string userAgent = "";

            bool isBulk = (Core.GetAppSetting("CosmosDb:AllowBulkExecution") == "1");

            if (Core.GetAppSetting("CosmosDb:ApplicationName") != null)
            {
                userAgent = "-" + Core.GetAppSetting("CosmosDb:ApplicationName");
            }

            var options = new CosmosClientOptions()
            {
                ConnectionMode = ConnectionMode.Direct,
                RequestTimeout = TimeSpan.FromSeconds(10),
                MaxRetryAttemptsOnRateLimitedRequests = 10,
                MaxRetryWaitTimeOnRateLimitedRequests = TimeSpan.FromSeconds(30),
                Serializer = new CosmosJsonTowbookSerializer(),
                ApplicationName = "Towbook-" + Core.GetCoreVersion() + userAgent,
                AllowBulkExecution = isBulk
            };

            if (preferSecondary)
                options.ApplicationRegion = "North Central US";
            if ( GetDatabaseName() == "braintree")
                options.SerializerOptions.PropertyNamingPolicy = CosmosPropertyNamingPolicy.CamelCase;

            client.Client = new CosmosClient(url, key, options);
            
            logger.Info($"CosmosDB initialized with {client.Client.Endpoint}-" + options.ApplicationRegion + (isBulk ? "-BulkMode" : ""));

            return client;
        }

        /// <summary>
        /// Gets a thread-safe platform-wide cosmosDB object to work with.
        /// </summary>
        /// <returns></returns>
        private static CosmosDB PrivateInit(bool secondary = false)
        {
            var temp = Init(Core.CosmosUrl ,
                Core.CosmosAuthKey, secondary);

            temp.database = temp.Client.GetDatabase(GetDatabaseName());

            return temp;
        }

        public static CosmosDB Get() => instance;
        public static CosmosDB GetReadOnly() => instanceReadOnly;

        private static readonly string[] validContainers = { 
            "calls", 
            "calls-deleted",
            "quotes", 
            "call-requests", 
            "gps-history",
            "event-notifications",
            "notifications",
            "braintree",
            "report-history",
        };

        public static void IsValidContainerName(string name)
        {
            if (!validContainers.Contains(name))
                throw new Exception("Unexpected Container Name, if this is valid update the IsValidContainerName list: " + name);
        }

        private Database database;
        public ItemResponse<T> InsertItem<T>(string container, T item)
        {
            IsValidContainerName(container);

            //var sw = Stopwatch.StartNew();
            try
            {
                return database.GetContainer(container).CreateItemAsync(item).Result;
            }
            finally
            {
                //System.Diagnostics.Debug.WriteLine("Upsert took " + sw.ElapsedMilliseconds + "ms");
            }
        }

        public async Task<T> InsertItemAsync<T>(string container, T item)
        {
            IsValidContainerName(container);
            return await database.GetContainer(container).CreateItemAsync(item);
        }

        public async Async.Task UpsertItem<T>(string container, T item)
        {
            IsValidContainerName(container);

            await database.GetContainer(container).UpsertItemAsync(item, 
                requestOptions: new ItemRequestOptions() { EnableContentResponseOnWrite = false} );
        }

        public async Async.Task UpsertItem<T>(string container, T item, PartitionKey pk) => await UpsertItem(container, item, pk, true);
        public async Async.Task UpsertItem<T>(string container, T item, PartitionKey pk, bool allowRetry)
        {
            IsValidContainerName(container);

            try
            {
                await database.GetContainer(container).UpsertItemAsync(item,
                    partitionKey: pk,
                    requestOptions: new ItemRequestOptions() { EnableContentResponseOnWrite = false });
            }
            catch(CosmosException ce)
            {
                if (ce.SubStatusCode == 3200 && allowRetry)
                {
                    await Task.Delay(500);
                    await UpsertItem(container, item, pk, false);
                    return;
                }

                throw;
            }
        }

        public Task UpsertItemBulk<T>(string container, T item, PartitionKey pk)
        {
            IsValidContainerName(container);
            Console.WriteLine("** BULK FLOW ** " + container + pk.ToString());
            var s = Stopwatch.StartNew();
            try
            {
                return database.GetContainer(container).UpsertItemAsync(item,
                    partitionKey: pk,
                    requestOptions: new ItemRequestOptions() { EnableContentResponseOnWrite = false });
            }
            finally
            {
                Console.WriteLine(s.ElapsedMilliseconds + "ms");
            }
        }

        public async Async.Task DeleteItem<T>(string container, string id, int key)
        {
            await DeleteItem<T>(container, id, new PartitionKey(key));
        }

        public async Async.Task DeleteItem<T>(string container, string id, PartitionKey key)
        {
            IsValidContainerName(container);

            try
            {
                await database.GetContainer(container).DeleteItemAsync<T>(id, key,
                    new ItemRequestOptions() { EnableContentResponseOnWrite = false });
            }
            catch //(Exception y)
            {
                //Console.WriteLine(y.Message);
            }
        }

        public ItemResponse<T> GetItem<T>(string container, string id, PartitionKey key)
        {
            IsValidContainerName(container);

            try
            {
                return database.GetContainer(container).ReadItemAsync<T>(id, key).Result;
            }
            catch (Exception)
            {
                // Debug.WriteLine(y.Message);
                return null;
            }
        }

        public async Task<ItemResponse<T>> GetItemAsync<T>(string container, string id, PartitionKey key)
        {
            IsValidContainerName(container);

            try
            {
                return await database.GetContainer(container).ReadItemAsync<T>(id, key);
            }
            catch (Exception)
            {
                // Debug.WriteLine(y.Message);
                return null;
            }
        }

        public Collection<T> QueryItems<T>(string container, QueryDefinition sqlQuery)
        {
            IsValidContainerName(container);
            var result = new Collection<T>();
            string continuationToken = null;
            do
            {
                
                using (var feedIterator = database.GetContainer(container).GetItemQueryIterator<T>(sqlQuery,
                    continuationToken))
                {

                    while (feedIterator.HasMoreResults)
                    {
                        FeedResponse<T> feedResponse = feedIterator.ReadNextAsync().Result;
                        continuationToken = feedResponse.ContinuationToken;
                        foreach (T item in feedResponse)
                        {
                            result.Add(item);
                        }
                    }
                }
            } while (continuationToken != null);

            return result;
        }

        public async Task<Collection<T>> QueryItemsAsync<T>(string container, 
            QueryDefinition sqlQuery,
            string partitionKey = null)
        {
            IsValidContainerName(container);
            var result = new Collection<T>();

            QueryRequestOptions ro = null;

            if (partitionKey != null)
                ro = new QueryRequestOptions() { PartitionKey = new PartitionKey(Convert.ToDouble(partitionKey)) };

            using (var feedIterator = database.GetContainer(container).GetItemQueryIterator<T>(sqlQuery, null, ro))
            {
                while (feedIterator.HasMoreResults)
                {
                    var feedResponse = await feedIterator.ReadNextAsync();

                    foreach (T item in feedResponse)
                    {
                        result.Add(item);
                    }
                }
            }

            return result;
        }

        public async Task<T> QueryScalarAsync<T>(string container,
           QueryDefinition sqlQuery,
           string partitionKey = null)
        {
            IsValidContainerName(container);
            var result = new Collection<T>();

            QueryRequestOptions ro = null;

            if (partitionKey != null)
                ro = new QueryRequestOptions() { PartitionKey = new PartitionKey(Convert.ToDouble(partitionKey)) };

            using (var feedIterator = database.GetContainer(container).GetItemQueryIterator<T>(sqlQuery, null, ro))
            {
                while (feedIterator.HasMoreResults)
                {
                    var feedResponse = await feedIterator.ReadNextAsync();

                    return feedResponse.SingleOrDefault();
                }
            }

            return default(T);
        }
    }
}
