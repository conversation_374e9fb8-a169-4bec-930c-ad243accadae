using Extric.Towbook.Utility;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace Extric.Towbook.AutoDataDirect
{
    [Table("Integration.AutoDataDirectConnections")]
    public class AutoDataDirectConnection
    {
        [Key("AutoDataDirectConnectionId")]
        public int Id { get; set; }
        public int CompanyId { get; set; }
        public int OwnerUserId { get; set; }
        public bool IsDeleted { get; set; }
        public string AccessToken { get; set; }
        public string RefreshToken { get; set; }
        public DateTime ExpiresNext { get; set; } 
        public DateTime LastUpdated { get; set; }
        public DateTime CreateDate { get; set; }
        

        public static AutoDataDirectConnection GetByCompanyId(int id)
        {
            return SqlMapper.Query<AutoDataDirectConnection>(
                "SELECT * FROM Integration.AutoDataDirectConnections WHERE CompanyId=@Id AND IsDeleted=0",
                new
                {
                    Id = id
                }).FirstOrDefault();
        }
        public static AutoDataDirectConnection GetByUserId(int companyId, int userId)
        {
            return SqlMapper.Query<AutoDataDirectConnection>(
                "SELECT * FROM Integration.AutoDataDirectConnections WHERE CompanyId=@CompanyId and OwnerUserId=@OwnerUserId AND IsDeleted=0",
                new
                {
                    CompanyId = companyId,
                    OwnerUserId = userId
                }).FirstOrDefault();
        }

        public static async Task<AutoDataDirectConnection> GetByUserIdAsync(int companyId, int userId)
        {
            return (await SqlMapper.QueryAsync<AutoDataDirectConnection>(
                "SELECT * FROM Integration.AutoDataDirectConnections WHERE CompanyId=@CompanyId and OwnerUserId=@OwnerUserId AND IsDeleted=0",
                new
                {
                    CompanyId = companyId,
                    OwnerUserId = userId
                })).FirstOrDefault();
        }

        public void Save()
        {
            if (this.Id == 0)
            {
                SqlMapper.Insert(this);
            }
            else
            {
                SqlMapper.Update(this);
            }
        }

        public void Delete(User user)
        {
            if (user?.Id != OwnerUserId)
                throw new TowbookException("Only the owner can delete their own Connection.");

            IsDeleted = true;
            Save();
        }
    }
}
