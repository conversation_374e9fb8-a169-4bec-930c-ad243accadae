using Extric.Towbook.Utility;
using System.IO;
using System.Xml.Serialization;

namespace Extric.Towbook.AutoDataDirect
{
	/// <summary>
	/// AutoDataDirect's API's return an <xmlerror /> payload when an error occurs.
	/// Deserialize the XML into this class to work with it.
	/// </summary>
    [XmlRoot("xmlerror")]
	public class XmlError
	{
		[XmlElement("message")]
		public string Message { get; set; }

		[XmlElement("source")]
		public string Source { get; set; }

		[XmlElement("number")]
		public string Number { get; set; }

		public string ToXml() => AddUtility.XmlSerialize(this);

		public static XmlError FromXml(string xml)
		{
			var xs = XmlHelper<XmlError>.Serializer;

			using (var sr = new StringReader(xml))
			{
				var output = xs.Deserialize(sr) as XmlError;

				return output;
			}
		}
	}
}
