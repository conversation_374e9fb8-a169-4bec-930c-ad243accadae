using Extric.Towbook.Utility;

namespace Extric.Towbook.Agent.QuickBooks
{
    [Table("Agent.QBTaxCodes")]
    public class TaxCode : IAgentEntityBase
    {
        [Key("TaxCodeId")]
        public long Id { get; set; }
        public int AgentSessionId { get; set; }
        public string ObjectId { get; set; }
        public string Name { get; set; }
        public bool IsActive { get; set; }
        public bool IsTaxable { get; set; }
    }
}
