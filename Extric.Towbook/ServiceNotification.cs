using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Data.SqlClient;
using Extric.Towbook.Utility;

namespace Extric.Towbook
{
    public class ServiceNotification
    {
        private int _id;
        private int _type;
        private string _title;
        private string _message;
        private string _externalLink;
        private int _actionType;
        private DateTime _createDate;
        private DateTime _endDate;

        public int[] UserTypes { get; set; }

        public ServiceNotification()
        {
            _id = -1;
        }

        public int Id
        {
            get
            {
                return _id;
            }
        }

        public int Type
        {
            get
            {
                return _type;
            }
            set
            {
                _type = value;
            }
        }

        public string Title
        {
            get
            {
                return _title;
            }
            set
            {
                if (value.Length > 250)
                {
                    throw new ArgumentException("Content length cannot exceed 250 characters");
                }
                _title = value;
            }
        }

        public string Message
        {
            get
            {
                return _message;
            }
            set
            {
                if (value.Length > 2000)
                {
                    throw new ArgumentException("Message length cannot exceed 2000 characters");
                }
                _message = value;
            }
        }

        public string ExternalLink
        {
            get
            {
                return _externalLink;
            }
            set
            {
                if (value.Length > 250)
                {
                    throw new ArgumentException("Message length cannot exceed 2000 characters");
                }
                _externalLink = value;
            }
        }

        public DateTime CreateDate
        {
            get
            {
                return _createDate;
            }
        }

        public DateTime EndDate
        {
            get
            {
                return _endDate;
            }
            set
            {
                _endDate = value;
            }
        }

        public int ActionType
        {
            get
            {
                return _actionType;
            }
            set
            {
                _actionType = value;
            }
        }

        public ServiceNotification(SqlDataReader reader)
        {
            InitializeFromDataReader(reader);
        }

        private void InitializeFromDataReader(SqlDataReader reader)
        {
            _id = reader.GetValue<int>("NotificationId");
            _type = reader.GetValue<int>("Type");
            _title = reader.GetValue<string>("Title");
            _message = reader.GetValue<string>("Message");
            _externalLink = reader.GetValue<string>("ExtLink");
            _actionType = reader.GetValue<int>("ActionType");
            
            _createDate = Convert.ToDateTime(reader["CreateDate"]);
            _endDate = Convert.ToDateTime(reader["EndDate"]);

            try
            {
                this.UserTypes = (reader.GetValue<string>("UserTypes") ?? "").Split(new string[] { "," }, StringSplitOptions.RemoveEmptyEntries)
                    .Select(o => Convert.ToInt32(o))
                    .ToArray();
            }
            catch { }
        }

        public static Collection<ServiceNotification> Get(int userId)
        {
            Collection<ServiceNotification> Notifications = new Collection<ServiceNotification>();

            using (SqlDataReader dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                "ServiceNotificationsGetAll",
                new SqlParameter("@UserId", userId)))
            {
                while (dr.Read())
                {
                    Notifications.Add(new ServiceNotification(dr));
                }

            }
            return Notifications;
        }

        public static async Task<Collection<ServiceNotification>> GetAsync(int userId)
        {
            Collection<ServiceNotification> Notifications = new Collection<ServiceNotification>();

            using (SqlDataReader dr = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString,
                "ServiceNotificationsGetAll",
                new SqlParameter("@UserId", userId)))
            {
                while (await dr.ReadAsync())
                {
                    Notifications.Add(new ServiceNotification(dr));
                }

            }
            return Notifications;
        }

        public void Save()
        {
            if (this.Id == 0)
            {
                this._id = Convert.ToInt32(SqlMapper.QuerySP<dynamic>("ServiceNotificationsInsert",
                    new
                    {
                        Type = _type,
                        Title = _title,
                        Message = _message,
                        ExtLink = _externalLink,
                        ActionType = _actionType,
                        CreateDate = _createDate,
                        EndDate = _endDate
                    }).FirstOrDefault().Id);
            }
            else
            {
                SqlMapper.ExecuteSP("ServiceNotificationsUpdateById",
                    new
                    {
                        NotificationId = Id,
                        Type = _type,
                        Title = _title,
                        Message = _message,
                        ExtLink = _externalLink,
                        ActionType = _actionType,
                        CreateDate = _createDate,
                        EndDate = _endDate
                    });
            }
        }

        public static void Remove(int notificationId, int userId)
        {
            SqlMapper.ExecuteSP("dbo.ServiceNotificationsRemoveUser",
                    new { NotificationId = notificationId, UserId = userId });
        }
    }
}
