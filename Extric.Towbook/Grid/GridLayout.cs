using Extric.Towbook.Utility;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Extric.Towbook.Grid
{
    public class GridLayout
    {
        public int Id { get; set; }
        public int GridId { get; set; }
        public int CompanyId { get; set; }
        public int? UserId { get; set; }
        public string TabName { get; set; }
        public string Columns { get; set; }
        public string FilterJson { get; set; }

        // properties
        private GridLayoutDetail _detail { get; set; }
        public GridLayoutDetail Detail { 
            get {
                if(_detail == null)
                    _detail =  GridLayoutDetail.GetByLayoutIds(new int[] { Id }.ToArray()).FirstOrDefault();

                return _detail;
            }
            set { _detail = value; }
        }

        public GridLayout()
        {
            Id = 0;
            GridId = 0;
            _detail = null;
        }

        protected GridLayout(SqlDataReader reader)
        {
            InitializeFromDataReader(reader);
        }

        private void InitializeFromDataReader(SqlDataReader reader)
        {
            Id = Convert.ToInt32(reader["Id"]);
            GridId = Convert.ToInt32(reader["GridId"]);
            CompanyId = Convert.ToInt32(reader["CompanyId"]);

            if (reader["UserId"] != DBNull.Value)
                UserId = Convert.ToInt32(reader["UserId"]);
            
            TabName = Convert.ToString(reader["TabName"]);
            Columns = Convert.ToString(reader["Columns"]);
            FilterJson = Convert.ToString(reader["FilterJson"]);
        }

        public static GridLayout GetById(int id)
        {
            return SqlMapper.Query<GridLayout>("SELECT * FROM GridLayouts WHERE Id = @Id", new { Id = id }).FirstOrDefault();
        }

        public static async Task<GridLayout> GetByIdAsync(int id)
        {
            return (await SqlMapper.QueryAsync<GridLayout>("SELECT * FROM GridLayouts WHERE Id = @Id", new { Id = id })).FirstOrDefault();
        }

        public static Collection<GridLayout> GetByGridId(int gridId, int companyId, int? userId)
        {
            var layouts = new Collection<GridLayout>();

            using (var dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                "GridLayoutsGetByGridId",
                new SqlParameter("@GridId", gridId),
                new SqlParameter("@CompanyId", companyId),
                new SqlParameter("@UserId", userId)))
            {
                while (dr.Read())
                {
                    layouts.Add(new GridLayout(dr));
                }
            }

            // get Details
            Collection<GridLayoutDetail> details = GridLayoutDetail.GetByLayoutIds(layouts.Select(s => s.Id).ToArray());
            foreach (var layout in layouts)
            {
                if (details.Select(s => s.GridLayoutId).Contains(layout.Id))
                    layout.Detail = details.FirstOrDefault(w => w.GridLayoutId == layout.Id);
            }

            return layouts;
        }

        public static async Task<Collection<GridLayout>> GetByGridIdAsync(int gridId, int companyId, int? userId)
        {
            var layouts = new Collection<GridLayout>();

            using (var dr = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString,
                "GridLayoutsGetByGridId",
                new SqlParameter("@GridId", gridId),
                new SqlParameter("@CompanyId", companyId),
                new SqlParameter("@UserId", userId)))
            {
                while (await dr.ReadAsync())
                {
                    layouts.Add(new GridLayout(dr));
                }
            }

            // get Details
            Collection<GridLayoutDetail> details = await GridLayoutDetail.GetByLayoutIdsAsync(layouts.Select(s => s.Id).ToArray());
            foreach (var layout in layouts)
            {
                if (details.Select(s => s.GridLayoutId).Contains(layout.Id))
                    layout.Detail = details.FirstOrDefault(w => w.GridLayoutId == layout.Id);
            }

            return layouts;
        }

        public static Collection<GridLayout> GetByGridIds(int[] gridIds, int companyId, int? userId)
        {
            var layouts = new Collection<GridLayout>();

            if (gridIds == null || gridIds.Length == 0)
                return layouts;

            layouts = SqlMapper.Query<GridLayout>(
                "SELECT * FROM dbo.GridLayouts WHERE GridId IN @GridId and CompanyId=@CompanyId and (UserId IS NULL OR UserId=@UserId) and Deleted=0  ", 
                new { 
                    GridId = gridIds,
                    CompanyId = companyId,
                    UserId = userId
                }).ToCollection();

            return layouts;
        }

        public static async Task<Collection<GridLayout>> GetByGridIdsAsync(int[] gridIds, int companyId, int? userId)
        {
            var layouts = new Collection<GridLayout>();

            if (gridIds == null || gridIds.Length == 0)
                return layouts;

            return (await SqlMapper.QueryAsync<GridLayout>(
                "SELECT * FROM dbo.GridLayouts WHERE GridId IN @GridId and CompanyId=@CompanyId and (UserId IS NULL OR UserId=@UserId) and Deleted=0  ",
                new
                {
                    GridId = gridIds,
                    CompanyId = companyId,
                    UserId = userId
                })).ToCollection();
        }

        public async Task<int> SaveAsync(User user = null)
        {
            if (GridId < 1) throw new Extric.Towbook.TowbookException("GridId must be set!");

            if (Id == 0)
                Id = await DbInsertAsync();
            else
                await DbUpdateAsync();


            if (Detail != null)
            {
                Detail.GridLayoutId = this.Id;
                await Detail.SaveAsync(user);
            }

            return Id;
        }

        private async Task<int> DbUpdateAsync()
        {
            using (var conn = Core.GetConnection())
            {
                await SqlHelper.ExecuteScalarAsync(conn, System.Data.CommandType.StoredProcedure,
                    "GridLayoutUpdateById",
                    new SqlParameter("@Id", Id),
                    new SqlParameter("@GridId", GridId),
                    new SqlParameter("@CompanyId", CompanyId),
                    new SqlParameter("@UserId", UserId),
                    new SqlParameter("@TabName", TabName),
                    new SqlParameter("@Columns", Columns),
                    new SqlParameter("@FilterJson", FilterJson)
                   );
            }

            return Id;
        }

        private async Task<int> DbInsertAsync()
        {
            using (var conn = Core.GetConnection())
                return Convert.ToInt32(await SqlHelper.ExecuteScalarAsync(conn, System.Data.CommandType.StoredProcedure,
                    "GridLayoutInsert",
                    new SqlParameter("@GridId", GridId),
                    new SqlParameter("@CompanyId", CompanyId),
                    new SqlParameter("@UserId", UserId),
                    new SqlParameter("@TabName", TabName),
                    new SqlParameter("@Columns", Columns),
                    new SqlParameter("@FilterJson", FilterJson)
                   ));
        }

        public void Delete()
        {
            if (Id == 0) throw new Extric.Towbook.TowbookException("GridId must be set!");

            SqlHelper.ExecuteScalar(Core.ConnectionString,
                "GridLayoutDeleteById",
                new SqlParameter("@Id", Id));
        }
    }
}
