using Extric.Towbook.Utility;
using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;

namespace Extric.Towbook.Grid
{
    [Table("GridLayoutDetails")]
    public class GridLayoutDetail : IUsesSqlKey
    {
        [Key]
        public int GridLayoutDetailId { get; private set; }

        public int GridLayoutId { get; set; }
        public DateTime LastSaveDate { get; set; }
        public int? OwnerUserId { get; set; }
        public GridLayoutDetailType GridLayoutDetailTypeId { get; set; }
        public string Data { get; set; }

        public GridLayoutDetail()
        {
            GridLayoutDetailId = 0;
        }

        public async Task SaveAsync(User user = null)
        {
            if (Data == "[]")
                return;

            if (user != null)
            {
                OwnerUserId = user.Id;
                LastSaveDate = DateTime.Now;
            }

            if (GridLayoutDetailId > 0)
                await DbUpdateAsync();
            else
                await DbInsertAsync();
        }

        private async Task DbInsertAsync()
        {
            this.GridLayoutDetailId = (await SqlMapper.QueryAsync<int>("INSERT INTO GridLayoutDetails (GridLayoutId, GridLayoutDetailTypeId, OwnerUserId, LastSaveDate, Data) OUTPUT Inserted.GridLayoutDetailId VALUES (@GridLayoutId, @GridLayoutDetailTypeId, @OwnerUserId, @LastSaveDate, @Data)",
                new
                {
                    GridLayoutId = this.GridLayoutId,
                    GridLayoutDetailTypeId = (int)GridLayoutDetailType.RawJson,
                    OwnerUserId = this.OwnerUserId,
                    LastSaveDate = DateTime.Now,
                    Data = this.Data
                })).FirstOrDefault();
        }

        private async Task DbUpdateAsync()
        {
            await SqlMapper.UpdateAsync(this);
        }

        public static Collection<GridLayoutDetail> GetByLayoutIds(int[] layoutIds)
        {
            if (layoutIds == null || layoutIds.Length == 0)
                return new Collection<GridLayoutDetail>();

            return SqlMapper.Query<GridLayoutDetail>(
                    @"SELECT * FROM GridLayoutDetails WHERE GridLayoutId IN @LayoutIds", new { LayoutIds = layoutIds }
                ).ToCollection();
        }

        public static async Task<Collection<GridLayoutDetail>> GetByLayoutIdsAsync(int[] layoutIds)
        {
            if (layoutIds == null || layoutIds.Length == 0)
                return new Collection<GridLayoutDetail>();

            return (await SqlMapper.QueryAsync<GridLayoutDetail>(
                    @"SELECT * FROM GridLayoutDetails WHERE GridLayoutId IN @LayoutIds", new { LayoutIds = layoutIds }
                )).ToCollection();
        }

        public override string ToString()
        {
            return Data;
        }
    }


    public enum GridLayoutDetailType : int
    {
        RawJson = 1
    }
}
