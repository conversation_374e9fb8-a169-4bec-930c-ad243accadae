using System.Collections.Generic;

namespace Extric.Towbook.Utility
{
    public static class CollectionExtensions
    {
        public static V GetValueOrDefault<K, V>(this Dictionary<K, V> dic, K key)
        {
            V ret;
            bool found = dic.TryGetValue(key, out ret);
            if (found)
            {
                return ret;
            }
            return default(V);
        }
    }
}
