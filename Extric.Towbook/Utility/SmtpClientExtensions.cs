using Extric.Towbook.Accounts;
using Extric.Towbook.Dispatch;
using Extric.Towbook.Storage;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Mail;
using Extric.Towbook.Integration;
using Extric.Towbook.Configuration;
using NLog;
using System.Threading.Tasks;

namespace Extric.Towbook.Utility
{
    public static class SmtpClientExtensions
    {

        private static readonly Logger logger = LogManager.GetCurrentClassLogger();

        /*
        public class SmtpClientConfiguration
        {
            public string Host { get; set; }
            public int Port { get; set; }
            public string Username { get; set; }
            public string Password { get; set; }
            public bool Ssl { get; set; }
        }

        public static SmtpClient GetClient()
        {
            // todo: this doesn't respect the 5 seconds when used form a console app because it caches in the perrequest cache.
            // this needs to read from redis, and if not in redis, it falls back to reading from web.config
            var c = AppServices.Cache.Get<SmtpClientConfiguration>(TimeSpan.FromSeconds(5), () =>
            {
                var x = new SmtpClientConfiguration()
            {
                Host = "smtp.sendgrid.net",
                Port = 587,
                Username = "apikey",
                Password = "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx",
                Ssl = true
            };
                Console.WriteLine("New");
                return x;
            });

            if (c == null)
                return new SmtpClient();

            return new SmtpClient(c.Host, c.Port)
            {
                Credentials = new NetworkCredential() { UserName = c.Username, Password = c.Password },
                EnableSsl = c.Ssl
            };
        }
        */
        public static SmtpClient Get(this SmtpClient smtpClient)
        {
            smtpClient = new SmtpClient()
            {
                Host = SmtpConfiguration.Host,
                Port = SmtpConfiguration.Port,
                Credentials = new NetworkCredential(SmtpConfiguration.UserName, SmtpConfiguration.Password),
                EnableSsl = SmtpConfiguration.EnableSsl
            };

            return smtpClient;
        }

        public static async Task Send(this SmtpClient obj, MailMessage message, User u, string category = "Application")
        {
            await Send(obj, message, u, category, EmailType.None, 0, new int[] { }, Guid.Empty, null, null, null, null);
        }

        public static async Task Send(this SmtpClient obj, MailMessage message, User u, Dictionary<string, string> metaData, string category = "Application")
        {
            await Send(obj, message, u, category, EmailType.None, 0, new int[] { }, Guid.Empty, null, null, null, metaData);
        }

        public static async Task Send(this SmtpClient obj, MailMessage message, User u, string category, EmailType type, int referenceId)
        {
            await Send(obj, message, u, category, type, referenceId, Array.Empty<int>());
        }
        public static async Task Send(this SmtpClient obj, MailMessage message, User u, string category, EmailType type, int referenceId, int[] batchedCallIds)
        {
            await Send(obj, message, u, category, type, referenceId, batchedCallIds, null, null);
        }


        public static async Task Send(
            this SmtpClient obj,
            MailMessage message,
            User u,
            string category,
            EmailType type,
            int referenceId,
            int[] batchedCallIds,
            EntryWebLink entryWebLink = null,
            StatementWebLink statementWebLink = null)
        { 
            await Send(obj, message, u, category, type, referenceId, batchedCallIds, new Guid(), entryWebLink, statementWebLink, null); 
        }


        public static async Task Send(
            this SmtpClient obj,
            MailMessage message,
            User u,
            string category,
            EmailType type,
            int referenceId,
            int[] batchedCallIds,
            Guid quoteId,
            EntryWebLink entryWebLink = null,
            StatementWebLink statementWebLink = null,
            QuoteWebLink quoteWebLink = null)
        {
            await Send(obj, message, u, category, type, referenceId, batchedCallIds, quoteId, entryWebLink, statementWebLink, quoteWebLink, null);
        }


        public static async Task Send(
            this SmtpClient obj,
            MailMessage message,
            User u,
            string category,
            EmailType type,
            int referenceId,
            int[] batchedCallIds,
            Guid quoteId,
            EntryWebLink entryWebLink = null,
            StatementWebLink statementWebLink = null,
            QuoteWebLink quoteWebLink = null,
            Dictionary<string, string> metaData = null)
        {
            var log = new LogEventInfo();

            log.Level = LogLevel.Info;

            int companyId = u?.Company?.Id ?? 0;
            if (u?.Id < 1)
                u = null;
            
            int userId = u?.Id ?? 0;
            
            metaData = metaData ?? new Dictionary<string, string>();

            log.Message = "SmtpClient.Send";
            log.Properties["companyId"] = companyId;
            log.Properties["companyName"] = Company.Company.GetById(companyId)?.Name;
            log.Properties["commitId"] = Core.GetCommitId();
            log.Properties["referenceId"] = referenceId;
            log.Properties["type"] = Enum.GetName(typeof(EmailType), type);

            if (message.To.Any())
                log.Properties["to"] = message.To.Select(s => s.Address.ToLowerInvariant());
            if(!string.IsNullOrEmpty(message.From?.Address))
                log.Properties["from"] = message.From.Address;
            if(message.Attachments.Any())
                log.Properties["attachments"] = message.Attachments.Count();
            if (!string.IsNullOrEmpty(message.Subject))
                log.Properties["subject"] = message.Subject;
            if (message.CC.Any())
                log.Properties["cc"] = message.CC.Select(s => s.Address.ToLowerInvariant());

            metaData["type"] = Enum.GetName(typeof(EmailType), type);
            metaData["referenceId"] = referenceId.ToString();
            metaData["companyId"] = companyId.ToString();

            if (u != null)
            {
                log.Properties["username"] = u.Username;
                log.Properties["userId"] = u.Id;
                log.Properties["userType"] = Enum.GetName(typeof(User.TypeEnum), u.Type);
            }

            try
            {
                bool blocked = false;
                var tempTo = new MailAddressCollection();

                foreach (var a in message.To)
                {
                    tempTo.Add(a);
                }

                try
                {
                    var firstSmtp = message.Headers.GetValues("X-SMTPAPI")?.FirstOrDefault();
                    if (firstSmtp != null)
                    {
                        var smtpApi = JsonConvert.DeserializeObject<dynamic>(firstSmtp);

                        if (smtpApi != null && smtpApi.to != null)
                        {
                            var toRemove = tempTo.Where(o => o.Address.ToLowerInvariant() == "<EMAIL>").FirstOrDefault();
                            var added = false;

                            foreach (var xy in smtpApi.to)
                            {
                                if (!tempTo.Where(o => o.Address.ToLowerInvariant() == xy.ToString().ToLowerInvariant()).Any())
                                {
                                    tempTo.Add(new MailAddress(xy.ToString().ToLowerInvariant()));
                                    added = true;
                                }
                            }

                            if (added)
                            {
                                tempTo.Remove(toRemove);
                            }
                        }
                    }
                }
                catch
                {

                }

                var transactions = new List<EmailTransaction>();

                // check the bounce list
                foreach (var addr in tempTo)
                {
                    EmailTransaction trans = new EmailTransaction();

                    if (companyId > 0)
                        trans.CompanyId = companyId;

                    if (userId > 0)
                        trans.UserId = userId;

                    trans.CreateDate = DateTime.Now;
                    trans.EmailAddress = addr.Address;
                    trans.Type = type;
                    trans.ReferenceId = referenceId;

                    blocked = SendgridUtility.IsEmailBlocked(companyId, addr.Address);

                    trans.Status = blocked ? EventStatusType.Undeliverable : EventStatusType.Created;
                    transactions.Add(trans);
                }

                // Need to save the transaction(s) now to get the EmailTransactionId.  
                // The ids are added to the X-SMTPAPI header of the email so that the future send grid
                // webhook event can update the status of the EmailTransactionEventStatus table.
                foreach (var t in transactions)
                {
                    // EmailType.Account is an special email case from the account view that contains multiple
                    // callIds.  We need to add the callIds to the DispatchEntryIds property now to create the 
                    // necessary EmailTransactionDispatchEntries rows when saving the transaction object.
                    if (type == EmailType.Account && batchedCallIds.Length > 0 && u != null)
                        t.DispatchEntryIds = batchedCallIds;

                    if (type == EmailType.Statement)
                        t.StatementId = referenceId;

                    t.Save();

                    // insert EmailTransactionEventStatus rows in preparation for send grid web hook events
                    // for now, only worry about invoice emails and statement emails
                    if (new[] { EmailType.DispatchEntry, EmailType.Impound, EmailType.Statement }.Contains(type))
                    {
                        int? callId = null;
                        if (metaData.ContainsKey("callId") && type == EmailType.Impound)
                        {
                            int impoundCallId = 0;

                            if (int.TryParse(metaData["callId"], out impoundCallId) && impoundCallId > 0)
                                callId = impoundCallId;
                        }
                        else if (type == EmailType.DispatchEntry)
                            callId = referenceId;

                        if(batchedCallIds.Length > 0 && batchedCallIds.Contains(callId.GetValueOrDefault()))
                        {
                            metaData["callIds"] = string.Join("-", batchedCallIds);
                        }

                        var etes = new EmailTransactionEventStatus()
                        {
                            EmailTransactionId = t.Id,
                            DispatchEntryId = callId,
                            StatementId = type == EmailType.Statement ? referenceId : (int?)null,
                            Status = EventStatusType.Created
                        };

                        await etes.Save();
                    }

                    if (type == EmailType.Account && batchedCallIds.Length > 0)
                    {
                        metaData["callId"] = batchedCallIds.First().ToString();
                        metaData["callIds"] = string.Join("-", batchedCallIds);
                        metaData["accountId"] = referenceId.ToString();

                        var etes = new EmailTransactionEventStatus()
                        {
                            EmailTransactionId = t.Id,
                            DispatchEntryId = batchedCallIds.First(),
                            StatementId = (int?)null,
                            Status = EventStatusType.Created
                        };

                        await etes.Save();
                    }
                }

                // add EmailTransactionIds to the X-SMTPAPI header of the email via the unique_args
                if(metaData.ContainsKey("emailTransactionIds"))
                    metaData["emailTransactionIds"]  = string.Join(",", transactions.Select(s => s.Id));
                else
                    metaData.Add("emailTransactionIds", string.Join(",", transactions.Select(s => s.Id)));

                var tags = new List<string>();

                if (category != null)
                    tags.Add(category);

                if (companyId > 0)
                    tags.Add("company_" + companyId);

                if (userId > 0)
                    tags.Add("user" + userId);

                var json = new {
                        category = tags,
                        unique_args = metaData
                    }.ToJson();
                message.Headers.Add("X-SMTPAPI", json);

                log.Properties["json"] = json;
                log.Properties["data"] = new {
                        PaymentLinkType = entryWebLink?.Id > 0 ? "dispatch" : statementWebLink?.Id > 0 ? "statement" : quoteWebLink?.Id > 0 ? "quote" : "none",
                        PaymentLinkUrl = entryWebLink?.TinyUrl ?? statementWebLink?.TinyUrl ?? quoteWebLink?.TinyUrl ?? ""
                    }.ToJson();

                if (!blocked)
                    obj.Send(message);

                try
                {
                    var cf = new CompanyFile();
                    int? cId = null;

                    if (type == EmailType.IncomingEmailForward)
                        return;

                    if (type == EmailType.DispatchEntry)
                    {
                        cf.DispatchEntries.Add(referenceId);

                        if(batchedCallIds.Length > 0 && batchedCallIds.Contains(referenceId))
                        {
                            cf.DispatchEntries = cf.DispatchEntries.Union(batchedCallIds).Distinct().ToCollection();
                        }

                        try
                        {
                            var autoMove = CompanyKeyValue.GetFirstValueOrNull(u.CompanyId,
                                Provider.Towbook.ProviderId, "Towbook_Calls_EmailMoveToBilledAutomatically") ?? "1";

                            if (autoMove == "1")
                            {
                                if (batchedCallIds.Length > 0 && batchedCallIds.Contains(referenceId))
                                {
                                    await Dispatch.Entry.UpdateInvoiceStatusIds(batchedCallIds, (int)Accounts.InvoiceStatus.InvoiceStatusType.Billed);
                                }
                                else
                                {
                                    await Dispatch.Entry.UpdateInvoiceStatusIds(new int[] { referenceId }, (int)Accounts.InvoiceStatus.InvoiceStatusType.Billed);
                                }
                            }
                        }
                        catch
                        {
                            // ignore auto-move exceptions.
                        }
                    }
                    else if (type == EmailType.Impound)
                    { 
                        if (batchedCallIds.Length > 0)
                        {
                            cf.DispatchEntries = cf.DispatchEntries.Union(batchedCallIds).Distinct().ToCollection();
                        }
                    }
                    else if (type == EmailType.Statement)
                    {
                        if (u != null)
                        {
                            var st = await Statement.GetByIdAsync(referenceId);

                            if (st != null && st.Company.Id == u.CompanyId)
                                cf.Accounts.Add(st.AccountId);
                        }
                    }
                    else if (type == EmailType.Account && batchedCallIds.Length > 0)
                    {
                        cf.DispatchEntries = cf.DispatchEntries.Union(batchedCallIds).Distinct().ToCollection();

                        await Dispatch.Entry.UpdateInvoiceStatusIds(batchedCallIds, (int)Accounts.InvoiceStatus.InvoiceStatusType.Billed);
                    }
                    else if (type == EmailType.Quote && quoteId != Guid.Empty)
                    {
                        if (u != null)
                        {
                            foreach (var t in transactions)
                                t.QuoteId = quoteId;
                        }

                        if (quoteWebLink != null)
                            cId = quoteWebLink.CompanyId;
                    }


                    if (u != null)
                    {
                        var textContent = message.ToEmlFile();

                        cf.Filename = "Sent_Email_" + DateTime.Now.ToFileTimeUtc() + "_" + referenceId + ".eml";
                        cf.Size = textContent.Length;
                        cf.CompanyId = cId ?? u.CompanyId;
                        cf.Description = "Copy of Sent Email for " + type.ToString();
                        cf.OwnerUserId = u.Id;
                        cf.RawUrl = cf.Filename;
                        cf.Save(true);

                        foreach (var t in transactions)
                        {
                            // save file record per email transaction
                            var fileTransaction = new EmailTransactionFile()
                            {
                                EmailTransactionId = t.Id,
                                FileId = cf.Id
                            };
                            fileTransaction.Save();


                            if (entryWebLink != null)
                            {
                                entryWebLink.EmailTransactionId = t.Id;
                                entryWebLink.Save();
                            }

                            if (statementWebLink != null)
                            {
                                statementWebLink.EmailTransactionId = t.Id;
                                statementWebLink.Save();
                            }

                            if (quoteWebLink != null)
                            {
                                quoteWebLink.EmailTransactionId = t.Id;
                                await quoteWebLink.Save();
                            }
                        }

                        await FileUtility.SendFileWithString(cf.LocalLocation, textContent);
                    }
                }
                catch (Exception r)
                {
                    log.Properties["exception"] = r;
                    Console.WriteLine(r.ToString());
                    // todo: log
                }

            }
            catch (Exception y)
            {
                log.Properties["exception"] = y;
                log.Level = LogLevel.Error;

                throw;
            }
            finally
            {
                logger.Log(log);
            }
        }

        /// <summary>
        /// Attachs a CompanyFile as an Attachment to an AttachmentCollection. Useful for adding a file as 
        /// an attachment to an MailMessage.
        /// </summary>
        /// <param name="obj"></param>
        /// <param name="cf"></param>
        public static async Task AddAsync(this AttachmentCollection obj, CompanyFile cf)
        {
            if (cf != null)
            {
                var path = await FileUtility.GetFileAsync(Towbook.Web.HttpContextFactory.Instance.ServerMapPath(cf.LocalLocation));
                if (path != null && System.IO.File.Exists(path))
                {
                    var attach = new Attachment(path, System.Net.Mime.MediaTypeNames.Application.Octet);
                    attach.ContentDisposition.FileName = cf.Filename;

                    obj.Add(attach);
                }
                else
                {
                    if (path == null)
                        throw new Exception("path is NULL - wasnt found on S3 from " + cf.LocalLocation);
                    else
                        throw new Exception("path doesnt exist - " + cf.LocalLocation);
                }
            }
            else
                throw new Exception("CompanyFile passed is NULL.");
        }
    }
}
