using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.IO;
using Newtonsoft.Json;
using System.Text.RegularExpressions;
using System.Text;
using System.Security.Cryptography;
using System.Threading.Tasks;
using System.Net.Http;

namespace Extric.Towbook.Utility
{
    public class GeocodeHelper
    {
        private const string apiKey = "AIzaSyDfo89LzuZ-7JjbV6-ToUFfj0JVdStynMw";

        private static readonly HttpClient httpClient = new HttpClient();
        /// <summary>
        /// Removes any text and surrounding paranethes from the passed in string.
        /// </summary>
        /// <param name="s">Example: pass in 201 N Riverside (Towbook)</param>
        /// <returns>Example Return: 201 N Riverside</returns>
        public static string FormatAddressForGeocoding(string s)
        {
            if (string.IsNullOrWhiteSpace(s))
                return s;

            var input = s;
            var output = Regex.Replace(input, @" ?\(.*?\)", string.Empty);

            if (output.EndsWith(", USA"))
                output = output.Substring(0, output.LastIndexOf(", USA"));

            return output;
        }

        public static async Task<LocationModel> Geocode(string address)
        {
            if (string.IsNullOrWhiteSpace(address))
                return null;

            address = FormatAddressForGeocoding(address);

            var cacheKey = "geocoder:" + address.ToLowerInvariant().Replace(" ", "");

            var rv = await Core.GetRedisValueAsync(cacheKey);

            if (rv != null && false)
                return JsonConvert.DeserializeObject<LocationModel>(rv);

            var url = $"https://maps.googleapis.com/maps/api/geocode/json?address={WebUtility.UrlEncode(address)}&key={apiKey}";

            var response = await MakeRequest(url);

            IEnumerable<LocationModel> locations = LocationModel.MapFromGoogle(response);

            if (locations.Count() > 1)
            {
                var matched = locations.FirstOrDefault(o => o.FormattedAddress.Contains(address));
                if (matched != null) locations = new LocationModel[] { matched };
            }

            var finalResult = locations.FirstOrDefault();

            if (finalResult != null && finalResult.Address == null)
            {
                var tempAddress = locations.FirstOrDefault(r => r.Address != null)?.Address;

                if (tempAddress != null)
                    finalResult.Address = tempAddress;
            }

            
            if (finalResult != null)
            {
                await Core.SetRedisValueAsync(cacheKey,
                    finalResult.ToJson(), TimeSpan.FromDays(3));
            }
            else
            {
                // save invalid values so we don't hit google for the same invalid value twice. 
                // we pay for each request.
                await Core.SetRedisValueAsync(cacheKey, "null", TimeSpan.FromDays(3));
            }

            return finalResult;
        }

        private static readonly JsonSerializer serializer = new JsonSerializer();

        public static async Task<dynamic> MakeRequest(string requestUrl)
        {
            for (int retries = 0; retries < 3; retries++)
            {
                try
                {
                    var response = await httpClient.GetAsync(requestUrl);
                    {
                        if (response.StatusCode != HttpStatusCode.OK)
                            throw new Exception(String.Format(
                            "Server error (HTTP {0}).",
                            response.StatusCode));

                        var json = await response.Content.ReadAsStringAsync();

                        return serializer.Deserialize(new JsonTextReader(new StringReader(json)));
                    }
                }
                catch
                {
                    await Task.Delay(100);
                    // automatically retry 
                    continue;
                }
            }
            return null;
        }
    }

    public class DistanceMatrixUtility
    {

        public class Distance
        {
            public string Text { get; set; }
            public int Value { get; set; }

            public override string ToString()
            {
                return Text;
            }

        }

        public class Duration
        {
            public string Text { get; set; }
            public int Value { get; set; }

            public override string ToString()
            {
                return Text;
            }
        }

        public class Element
        {
            public Distance Distance { get; set; }
            public Duration Duration { get; set; }
            public string Status { get; set; }
        }

        public class Row
        {
            public List<Element> Elements { get; set; }
        }

        public class MatrixModel
        {
            [JsonProperty("origin_addresses")]
            public List<string> Origins { get; set; }

            [JsonProperty("destination_addresses")]
            public List<string> Destinations { get; set; }

            [JsonProperty("rows")]
            public List<Row> Rows { get; set; }
            public string Status { get; set; }

            /// <summary>
            /// Travel distance in Miles. 
            /// </summary>
            public decimal Miles
            {
                get
                {
                    if (this.Rows.Count == 1 &&
                        this.Rows[0].Elements.Count == 1 &&
                        this.Rows[0].Elements[0].Distance != null)
                    {
                        return this.Rows[0].Elements[0].Distance.Value / 1609.344M;
                    }
                    return 0;
                }
            }

            /// <summary>
            /// Travel time in seconds
            /// </summary>
            public decimal Time
            {
                get
                {
                    if (this.Rows.Count == 1 &&
                        this.Rows[0].Elements.Count == 1 &&
                        this.Rows[0].Elements[0].Duration != null)
                    {
                        return this.Rows[0].Elements[0].Duration.Value;
                    }
                    return 0;
                }
            }
        }

        private static readonly HttpClient httpClient = new HttpClient();

        public static async Task<MatrixModel> GetMatrixAsync(string origin, string destination, string clientKeyOverride = null)
        {
            string units = "imperial";

            var requestUrl = string.Format(
                "https://maps.googleapis.com/maps/api/distancematrix/json?units={2}&origins={0}&destinations={1}&sensor=false",
                origin, destination, units);

            if (clientKeyOverride == null)
            {
                requestUrl += "&client=" + clientKey;
                requestUrl = GoogleSignedUrl.Sign(requestUrl, cryptoKey);
            }
            else
            {
                requestUrl += "&key=" + clientKeyOverride;
            }

            try
            {
                var result = await httpClient.GetAsync(requestUrl);
                return JsonConvert.DeserializeObject<MatrixModel>(await result.Content.ReadAsStringAsync());
            }
            catch (Exception)
            {
                return null;
            }
        }

        private const string clientKey = "gme-extricllc";
        private const string cryptoKey = "Enk_4yAMKlf1KPPcsstXvpQ0Tv0=";

        public struct GoogleSignedUrl
        {

            public static string Sign(string url, string keyString)
            {
                ASCIIEncoding encoding = new ASCIIEncoding();

                // converting key to bytes will throw an exception, need to replace '-' and '_' characters first.
                string usablePrivateKey = keyString.Replace("-", "+").Replace("_", "/");
                byte[] privateKeyBytes = Convert.FromBase64String(usablePrivateKey);

                Uri uri = new Uri(url);
                byte[] encodedPathAndQueryBytes = encoding.GetBytes(uri.LocalPath + uri.Query);

                // compute the hash
                HMACSHA1 algorithm = new HMACSHA1(privateKeyBytes);
                byte[] hash = algorithm.ComputeHash(encodedPathAndQueryBytes);

                // convert the bytes to string and make url-safe by replacing '+' and '/' characters
                string signature = Convert.ToBase64String(hash).Replace("+", "-").Replace("/", "_");

                // Add the signature to the existing URI.
                return uri.Scheme + "://" + uri.Host + uri.LocalPath + uri.Query + "&signature=" + signature;
            }
        }

    }


    public class LocationModel
    {
        private static LocationModel FillField(LocationModel input, object value, object field)
        {
            if (field == null)
                return input;

            switch (field.ToString())
            {
                case "City":
                    input.City = value.ToString();
                    break;

                case "County":
                    break;

                case "State":
                    input.State = value.ToString();
                    break;

                case "Country":
                    input.Country = value.ToString();
                    break;
            }

            return input;
        }

        private static LocationModel FillFieldGoogle(LocationModel input, IEnumerable<dynamic> fields, object value)
        {
            if (fields == null || value == null)
                return input;

            foreach (var kk in fields.Select(o => o.ToString()))
            {
                string k = kk.ToString();
                switch (k)
                {
                    case "point_of_interest":
                        input.Name = value.ToString();
                        break;


                    case "establishment":
                    case "locality":
                        input.City = value.ToString();
                        break;

                    case "street_number":
                        if (input.Address == null)
                            input.Address = "";
                        input.Address = value + " " + input.Address;
                        break;

                    case "route":
                        if (input.Address == null) input.Address = "";
                        input.Address += value.ToString();
                        break;


                    case "postal_code":
                        input.Zip = value.ToString();
                        break;

                    case "administrative_area_level_1":
                        input.State = value.ToString();
                        break;
                    case "administrative_area_level_2":
                        input.County = value.ToString();
                        break;
                    case "administrative_area_level_3":
                        input.Township = value.ToString();
                        break;
                    case "country":
                        input.Country = value.ToString();
                        break;
                }
            }

            return input;
        }

        public static IEnumerable<LocationModel> MapFromGoogle(dynamic o)
        {
            if (o == null)
                return Array.Empty<LocationModel>();

            var r = new List<LocationModel>();

            if (o.status == "REQUEST_DENIED")
            {
                throw new TowbookException("Geocoder Exception:" + o.error_message);
            }
            else if (o.status == "ZERO_RESULTS")
            {
                return Array.Empty<LocationModel>();
            }

            foreach (var z in o.results)
            {
                var item = new LocationModel();

                foreach (var y in z.address_components)
                {
                    item = FillFieldGoogle(item, y.types, y.short_name.Value);
                }

                item.FormattedAddress = z.formatted_address;

                item.Latitude = z.geometry.location.lat;
                item.Longitude = z.geometry.location.lng;

                r.Add(item);
            }

            return r;
        }

        /// <summary>
        /// Optional Landmark name. If the address is for a Supermarket for instance, sometimes this will be filled in automatically.
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Street Address (Number + Name)
        /// </summary>
        public string Address { get; set; }

        public string FormattedAddress { get; set; }

        /// <summary>
        /// City where the location resides
        /// </summary>
        public string City { get; set; }

        /// <summary>
        /// Province or State 
        /// </summary>
        public string State { get; set; }

        /// <summary>
        /// Zip or Postal Code
        /// </summary>
        public string Zip { get; set; }

        /// <summary>
        /// Country where the address resides
        /// </summary>
        public string Country { get; set; }

        public string County { get; set; }
        public string Township { get; set; }
        
        /// <summary>
        /// Latitude
        /// </summary>
        public decimal Latitude { get; set; }

        /// <summary>
        /// Longitude
        /// </summary>
        public decimal Longitude { get; set; }

        /// <summary>
        /// A string identifying where this data came from (osm, gm, manual, etc)
        /// </summary>
        public string Info { get; set; }
    }
}
