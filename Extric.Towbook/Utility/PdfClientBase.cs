using System;
using System.Collections.Generic;
using System.IO;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Extric.Towbook.Configuration;
using Newtonsoft.Json;
using NLog;

namespace Extric.Towbook.Utility
{
    public class PdfClientBase
    {
        private const string KEY_HEADER_NAME = "PDF-APP-KEY";
        private const string APP_KEY = "F86514FE-A3D4-4789-BA5C-BF8C82EDACC0";
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();

        private static readonly PdfConfiguration _pdfConfiguration = AppServicesHelper.PdfConfiguration;
        private static readonly Uri _hostUri = new Uri(_pdfConfiguration.GetUrlHost());

        private static HttpClient _httpClient = null;

        //WARNING : This must be initialized manually always.
        public static void Init(HttpClient httpClient)
        {
            _httpClient = httpClient;
        }

        public static async Task<MemoryStream> GeneratePdf(
            string html,
            string baseUrl,
            FileType type = FileType.PDF,
            string footerHtml = null,
            bool noMarginBottom = false)
        {
            return await GeneratePdf(
                html, baseUrl, null, null, null, type, footerHtml, noMarginBottom: noMarginBottom);
        }

        public static async Task<MemoryStream> GeneratePdf(
            string html,
            string baseUrl,
            OutputArea outputArea,
            PageSize pageSize,
            FileType type = FileType.PDF,
            string footerHtml = null,
            string title = null,
            string creator = null,
            bool noMarginBottom = false)
        {
            return await GeneratePdf(
                html,
                baseUrl,
                outputArea,
                pageSize, null,
                type,
                footerHtml,
                false,
                title,
                creator,
                noMarginBottom: noMarginBottom);
        }

        public static async Task<MemoryStream> GeneratePdf(
            string html,
            string baseUrl,
            OutputArea outputArea,
            PageSize pageSize,
            byte[] pdfToMerge,
            FileType type,
            string footerHtml,
            bool enableDefaultValues = false,
            string title = null,
            string creator = null,
            bool noMarginBottom = false)
        {
            try
            {
                var conversionRequest = new
                {
                    NoMarginBottom = noMarginBottom,
                    Html = html,
                    BaseUrl = baseUrl ?? _pdfConfiguration.DefaultBaseUrl,
                    Footer = footerHtml,
                    Type = type,
                    pdfToMergeBase64 = pdfToMerge,
                    OutputArea = outputArea,
                    PageSize = pageSize,
                    WithDefault = enableDefaultValues,
                    Title = title,
                    Creator = creator
                };

                var json = JsonConvert.SerializeObject(conversionRequest);
                var msg = GetAuthenticatedRequest(HttpMethod.Post, "/api/Pdf/convert/from-html", json);

                using (var response = await _httpClient.SendAsync(msg))
                {
                    await ThrowIfNotSuccessStatusCode(response);
                    var result = new MemoryStream(await response.Content.ReadAsByteArrayAsync());
                    return result;
                }
            }
            catch (System.Exception ex)
            {
                _logger.Error(ex, "Error generating PDF from HTML.");
                throw;
            }
        }

        public static async Task<MemoryStream> GeneratePdf(
            List<string> htmls,
            string baseUrl,
            string footerHtml = null,
            bool merged = false,
            FileType fileType = FileType.PDF)
        {
            return await GeneratePdf(htmls, baseUrl, null, null, footerHtml, merged, false, fileType);
        }

        public static async Task<MemoryStream> GeneratePdf(
            List<string> htmls,
            string baseUrl,
            OutputArea outputArea,
            PageSize pageSize,
            string footerHtml,
            bool merged,
            bool enableDefaultValues = false,
            FileType fileType = FileType.PDF)
        {
            try
            {
                var conversionRequest = new
                {
                    Htmls = htmls,
                    BaseUrl = baseUrl ?? _pdfConfiguration.DefaultBaseUrl,
                    Footer = footerHtml,
                    OutputArea = outputArea,
                    PageSize = pageSize,
                    Merged = merged,
                    WithDefault = enableDefaultValues,
                    Type = fileType
                };

                var json = JsonConvert.SerializeObject(conversionRequest);
                var msg = GetAuthenticatedRequest(HttpMethod.Post, "/api/Pdf/convert/from-html-list", json);

                using (var response = await _httpClient.SendAsync(msg))
                {
                    await ThrowIfNotSuccessStatusCode(response);

                    var result = new MemoryStream(await response.Content.ReadAsByteArrayAsync());
                    return result;
                }
            }
            catch (System.Exception ex)
            {
                _logger.Error(ex, "Error generating PDF from HTML List.");
                throw;
            }
        }

        private static async Task ThrowIfNotSuccessStatusCode(HttpResponseMessage response)
        {
            if (!response.IsSuccessStatusCode)
            {
                var error = await response.Content.ReadAsStringAsync();
                _logger.Error("Error generating PDF: {0}", error);
                throw new Exception(response.StatusCode + ":" + error);
            }
        }

        public static async Task<MemoryStream> GeneratePdfFromTif(
            string imageBase64, string fileName)
        {
            try
            {
                var conversionRequest = new
                {
                    FileName = fileName,
                    FileDataBase64 = imageBase64
                };

                var json = JsonConvert.SerializeObject(conversionRequest);

                var msg = GetAuthenticatedRequest(HttpMethod.Post, "/api/Pdf/convert/from-tif", json);
                using (var response = await _httpClient.SendAsync(msg))
                {
                    await ThrowIfNotSuccessStatusCode(response);
                    var result = new MemoryStream(await response.Content.ReadAsByteArrayAsync());
                    return result;
                }
            }
            catch (System.Exception ex)
            {
                _logger.Error(ex, "Error generating PDF from TIF.");
                throw;
            }
        }

        public static async Task<Dictionary<string, string>> GetFields(byte[] pdfDocument)
        {
            try
            {
                var base64 = Convert.ToBase64String(pdfDocument);
                var getFieldsRequest = new
                {
                    TemplateId = 1,
                    PdfFileBase64 = base64
                };

                var json = JsonConvert.SerializeObject(getFieldsRequest);
                var data = new StringContent(json, Encoding.UTF8, "application/json");

                var msg = GetAuthenticatedRequest(HttpMethod.Post, "/api/Pdf/fields", json);
                using (var response = await _httpClient.SendAsync(msg))
                {
                    await ThrowIfNotSuccessStatusCode(response);
                    var result = JsonConvert.DeserializeObject<FieldsResponse>(await response.Content.ReadAsStringAsync());
                    return result.Fields;
                }
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error getting fields from PDF.");
                throw;
            }
        }

        public static async Task<MemoryStream> FillOutPdf(
            byte[] pdfDocument,
            Dictionary<string, string> fields,
            List<SignaturePdf> signatures,
            CloningPdf cloning,
            ImageToPdf image)
        {
            try
            {
                var base64 = Convert.ToBase64String(pdfDocument);
                var fillOutPdfRequest = new
                {
                    PdfFileBase64 = base64,
                    Fields = fields,
                    Cloning = cloning,
                    Image = image,
                    Signatures = signatures
                };

                var json = JsonConvert.SerializeObject(fillOutPdfRequest);

                var msg = GetAuthenticatedRequest(HttpMethod.Post, "/api/Pdf/fill-out", json);

                using (var response = await _httpClient.SendAsync(msg))
                {
                    await ThrowIfNotSuccessStatusCode(response);
                    var result = new MemoryStream(await response.Content.ReadAsByteArrayAsync());
                    return result;
                }
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error filling the PDF document.");
                throw;
            }
        }

        private static HttpRequestMessage GetAuthenticatedRequest(HttpMethod method, string url, string json = null)
        {
            var m = new HttpRequestMessage(method, new Uri(_hostUri, url));

            m.Headers.Add(KEY_HEADER_NAME, APP_KEY);

            if (json != null)
                m.Content = new StringContent(json, Encoding.UTF8, "application/json");

            return m;
        }
    }

    public enum FileType
    {
        PDF,
        PNG,
        JPEG
    }

    public class OutputArea
    {
        public OutputArea(float x, float y, float width, float height)
        {
            X = x;
            Y = y;
            Width = width;
            Height = height;
        }

        public float X { get; set; }
        public float Y { get; set; }
        public float Width { get; set; }
        public float Height { get; set; }
    }

    public class PageSize
    {
        public PageSize(float width, float height)
        {
            Width = width;
            Height = height;
        }

        public float Width { get; set; }
        public float Height { get; set; }
    }

    public class PageSizes
    {
        public static readonly PageSize Letter;

        public static readonly PageSize A4;

        static PageSizes()
        {
            Letter = new PageSize(8.5f, 11f);
            A4 = new PageSize(8.3f, 11.7f);
        }
    }

    public class FieldsResponse
    {
        public Dictionary<string, string> Fields { get; set; }
    }

    public class SignaturePdf
    {
        public string SignatureBase64 { get; set; }
        public int? TranslateX { get; set; }
        public int? TranslateY { get; set; }
    }

    public class ImageToPdf
    {
        public ImageToPdf(string url, int x, int y, float? scaleX, float? scaleY)
        {
            Url = url;
            X = x;
            Y = y;
            ScaleX = scaleX;
            ScaleY = scaleY;
        }

        public string Url { get; set; }
        public int X { get; set; }
        public int Y { get; set; }
        public float? ScaleX { get; set; }
        public float? ScaleY { get; set; }
    }

    public class CloningPdf
    {
        public int TemplateId { get; set; }
        public List<Dictionary<string, string>> Fields { get; set; }
        public bool RenameFields { get; set; } = true;
    }
}
