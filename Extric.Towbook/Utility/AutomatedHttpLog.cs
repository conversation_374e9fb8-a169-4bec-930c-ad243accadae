using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Extric.Towbook.Utility
{
    public class AutomatedHttpLog
    {
        public DateTime RequestTime { get; set; }
        public DateTime RequestEnd { get; set; }
        public string RequestUrl { get; set; }
        public string MachineIp { get; set; }
        public Dictionary<string, string> RequestHeaders { get; set; }
        public Dictionary<string, string> ResponseHeaders { get; set; }
        public string RequestBody { get; set; }
        public string ResponseBody { get; set; }
        public int ResponseCode { get; set; }
        public string ResponseCodeDescription { get; set; }

        public AutomatedHttpLog()
        {
            this.RequestHeaders = new Dictionary<string, string>();
            this.ResponseHeaders = new Dictionary<string, string>();
        }
    }
}
