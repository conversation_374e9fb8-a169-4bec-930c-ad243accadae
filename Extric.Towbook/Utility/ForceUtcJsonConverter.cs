using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;

namespace Extric.Towbook.Utility
{
    public class ForceUtcJsonConverter : JsonConverter
    {
        private bool receiveOnly;
        public bool ReturnUtc { get; set; }

        public Company.Company Company { get; set; }
        public ForceUtcJsonConverter()
        {
            receiveOnly = false;
        }

        public ForceUtcJsonConverter(bool receiveOnly)
        {
            this.receiveOnly = receiveOnly;
        }


        public override bool CanConvert(Type objectType)
        {
            return
                objectType == typeof(DateTime) ||
                objectType == typeof(DateTime?);
        }

        public DateTime OffsetRead(DateTime value)
        {
            if (ReturnUtc)
            {
                return value.ToUniversalTime();
            }
            else
            {
                return Core.OffsetDateTime(Company, value, true);
            }
        }


        public DateTime OffsetWrite(DateTime value)
        {
            if (ReturnUtc)
            {
                return value.ToUniversalTime();
            }
            else
            {
                return Core.OffsetDateTime(Company, value);
            }
        }



        /// <summary>
        /// Occurs on data being READ from the client to the server; ie data received from a POST or PUT REQEUST.
        /// </summary>
        /// <param name="reader"></param>
        /// <param name="objectType"></param>
        /// <param name="existingValue"></param>
        /// <param name="serializer"></param>
        /// <returns></returns>
        public override object ReadJson(JsonReader reader, Type objectType, object existingValue, JsonSerializer serializer)
        {
            bool isNullable = (Nullable.GetUnderlyingType(objectType) != null);
            Type type = (Nullable.GetUnderlyingType(objectType) ?? objectType);

            if (reader.TokenType == JsonToken.Null)
            {
                if (!isNullable)
                    throw new JsonSerializationException();
                return null;
            }

            var token = JToken.Load(reader);
            if (token.Type == JTokenType.String || token.Type == JTokenType.Date)
            {
                if (token.Value<string>() == "")
                {
                    if (objectType == typeof(DateTime))
                        return DateTime.MinValue;
                    else if (objectType == typeof(DateTime?))
                        return null;
                    else
                        return token.Value<string>();
                }

                try
                {
                    

                    var localTime = token.Value<DateTime>();

                    if (localTime.Kind == DateTimeKind.Utc)
                        return localTime.ToLocalTime(); // convert to local SERVER time, Extric operates all its servers in the Eastern time zone.
                    else
                        return Core.OffsetDateTime(Company, localTime, true);
                }
                catch (FormatException)
                {
                    try
                    {
                        // resolves an issue with Android app that sends 0000-12-31 instead of 0001-01-01. 
                        // this causes an exception.
                        if (token.ToString().StartsWith("0000-12-31T"))
                            token = "0001-01-01T00:00:00";

                        // handle common user mistakes that come from our app
                        // the app really needs to be validating the date/times before sending it over
                        // but until this happens, this solves a lot of the stupid user input.
                        token = token.ToString().Replace("oo", "00")
                            .Replace(";", ":")
                            .Replace(".", "")
                            .Replace("ppm", "pm")
                            .Replace("amm", "am");

                        var localTime = token.Value<DateTime>();

                        if (localTime.Kind == DateTimeKind.Utc)
                            return localTime.ToLocalTime(); // convert to local SERVER time, Extric operates all its servers in the Eastern time zone.
                        else
                            return Core.OffsetDateTime(Company, localTime, true);

                    }
                    catch (FormatException fe2)
                    {
                        throw new FormatException("QA_InvalidDateValue: " + token.Value<string>() + "...", fe2);
                    }
                }
            }

            return token.Value<DateTime>();
        }

        public override void WriteJson(JsonWriter writer, object value, JsonSerializer serializer)
        {
            if (value != null && !receiveOnly)
            {
                var d = (DateTime)value;
                writer.WriteValue(d.ToUniversalTime());
            }
            else
            {
                // convert from eastern -> local user time...
                var date = value as DateTime?;
                if (date != null)
                {
                    if (Company != null)
                    {
                        // write the value in the users local time based off of the Company.TimezoneOffset/UseDST,
                        // in the future we should return UTC.
                        if (date.Value.Kind == DateTimeKind.Utc)
                            writer.WriteValue(date.Value);
                        else
                            writer.WriteValue(OffsetWrite(date.Value));

                        return;
                    }
                }

                writer.WriteValue(value);
            }
        }
    }
}
