using HtmlAgilityPack;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using NLog;
using System.Collections;
using System.Reflection;
using System.Net.Cache;

namespace Extric.Towbook.Utility
{
    static public class ext
    {

        public static IEnumerable<Cookie> GetAllCookies(this CookieContainer c)
        {
            Hashtable k = (Hashtable)c.GetType().GetField("m_domainTable", BindingFlags.Instance | BindingFlags.NonPublic).GetValue(c);
            foreach (DictionaryEntry element in k)
            {
                SortedList l = (SortedList)element.Value.GetType().GetField("m_list", BindingFlags.Instance | BindingFlags.NonPublic).GetValue(element.Value);
                foreach (var e in l)
                {
                    var cl = (CookieCollection)((DictionaryEntry)e).Value;
                    foreach (<PERSON>ie fc in cl)
                    {
                        yield return fc;
                    }
                }
            }
        }
    }

    static public class WebRequestHelper
    {
        internal const string USER_AGENT = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/71.0.3578.98 Safari/537.36";

        public class FormFieldFile
        {
            public string FileName { get; set; }
            public byte[] Content { get; set; }
            public string ContentType { get; set; }
        }

        static public HtmlDocument GetHtml(string url, Dictionary<string, string> postData, ref CookieContainer cookieJar, bool testMode = false)
        {
            AutomatedHttpLog log = new AutomatedHttpLog();
            return GetHtml(url, postData, ref cookieJar, ref log);
        }

        static public HtmlDocument GetHtmlMultipart(string url, List<KeyValuePair<string, string>> postData, List<KeyValuePair<string, FormFieldFile>> files, ref CookieContainer cookieJar, ref AutomatedHttpLog log, bool testMode = false)
        {
            var result = new HtmlDocument();

            var boundary = "----WebKitFormBoundary" + DateTime.Now.Ticks.ToString("x");
            var boundaryBytes = Encoding.ASCII.GetBytes("\r\n--" + boundary + "\r\n");

            log.RequestUrl = url;
            System.Net.ServicePointManager.Expect100Continue = false;
            var request = (HttpWebRequest)WebRequest.Create(url);

            request.Method = "POST";
            request.KeepAlive = true;
            request.Headers.Add("Cache-Control", "max-age=0");
            request.Accept = "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8";
            request.UserAgent = USER_AGENT;
            request.ContentType = "multipart/form-data; boundary=" + boundary;
            request.Referer = url;
            request.AutomaticDecompression = DecompressionMethods.Deflate | DecompressionMethods.GZip;
            request.Headers.Add("Accept-Language", " en-US,en;q=0.8");
            request.CookieContainer = cookieJar == null ? new CookieContainer() : cookieJar;
            cookieJar = request.CookieContainer;

            
            // For every write to rs, also write to ms
            // This will create a duplicate copy of the request body for logging purposes
            var rs = request.GetRequestStream();
            var ms = new MemoryStream();
            try
            {
                foreach (var fi in postData)
                {
                    rs.Write(boundaryBytes, 0, boundaryBytes.Length);
                    ms.Write(boundaryBytes, 0, boundaryBytes.Length);

                    var fiBytes = Encoding.UTF8.GetBytes($"Content-Disposition: form-data; name=\"{fi.Key}\"\r\n\r\n{fi.Value}");

                    rs.Write(fiBytes, 0, fiBytes.Length);
                    ms.Write(fiBytes, 0, fiBytes.Length);
                }

                if (files != null && files.Count > 0)
                {
                    foreach (var ff in files)
                    {
                        rs.Write(boundaryBytes, 0, boundaryBytes.Length);
                        ms.Write(boundaryBytes, 0, boundaryBytes.Length);

                        var ffBytes = Encoding.UTF8.GetBytes($"Content-Disposition: form-data; name=\"{ff.Key}\"; filename=\"{ff.Value.FileName}\"\r\nContent-Type: {ff.Value.ContentType}\r\n\r\n");

                        rs.Write(ffBytes, 0, ffBytes.Length);
                        ms.Write(ffBytes, 0, ffBytes.Length);

                        if (ff.Value.Content != null)
                        {
                            rs.Write(ff.Value.Content, 0, ff.Value.Content.Length);

                            // File contents omitted from logging
                            var logMsg = Encoding.UTF8.GetBytes($"[ File contents omitted from logging.  Size: {ff.Value.Content.Length:N0} bytes ]");
                            ms.Write(logMsg, 0, logMsg.Length);
                        }
                    }
                }

                var footer = "\r\n--" + boundary + "--\r\n";

                rs.Write(Encoding.ASCII.GetBytes(footer), 0, Encoding.ASCII.GetByteCount(footer));
                ms.Write(Encoding.ASCII.GetBytes(footer), 0, Encoding.ASCII.GetByteCount(footer));
            }
            finally
            {
                log.RequestBody = Encoding.UTF8.GetString(ms.GetBuffer());

                rs.Close();
                ms.Close();
            }

            foreach (string header in request.Headers.AllKeys)
            {
                if (!log.RequestHeaders.ContainsKey(header))
                    log.RequestHeaders.Add(header, request.Headers[header]);
            }

            log.RequestTime = DateTime.Now;
            if (!testMode)
            {
                HttpWebResponse response = null;                

                try
                {
                    response = (HttpWebResponse)request.GetResponse();
                }
                catch (WebException ex)
                {
                    // ignore internal errors so we can at least get response cookies
                    response = (HttpWebResponse)ex.Response;
                }
                log.RequestEnd = DateTime.Now;

                foreach (string header in response.Headers.AllKeys)
                {
                    if (!log.ResponseHeaders.ContainsKey(header))
                        log.ResponseHeaders.Add(header, response.Headers[header]);
                }

                log.ResponseCode = Convert.ToInt32(response.StatusCode);
                log.ResponseCodeDescription = response.StatusDescription;

                string content = string.Empty;
                using (var stream = response.GetResponseStream())
                {
                    using (var reader = new StreamReader(stream, Encoding.UTF8))
                    {
                        content = reader.ReadToEnd();
                    }
                }

                log.ResponseBody = content;

                // This loop may be completely unnecessary; .NET automatically updates cookieJar using a pointer to response.Cookies
                // So cookies don't need to be re-added to cookieJar.  Doing so can create duplicate cookies (which might be ok)
                foreach (Cookie cookie in response.Cookies)
                    cookieJar.Add(new Cookie(cookie.Name.Trim(), cookie.Value.Trim(), cookie.Path, cookie.Domain));

                result.LoadHtml(content);
            }

            return result;
        }

        static public HtmlDocument GetHtml(string url, Dictionary<string, string> postData, ref CookieContainer cookieJar, ref AutomatedHttpLog log, bool testMode = false, bool sendAsQueryString = false, bool sendAsJsonData = false)
        {
            HtmlDocument result = new HtmlDocument();

            log.RequestUrl = url;
            System.Net.ServicePointManager.Expect100Continue = false;
            System.Net.ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12 | SecurityProtocolType.Tls11 | SecurityProtocolType.Tls; // comparable to modern browsers

            log.RequestTime = DateTime.Now;
            if (!testMode)
            {
                HttpWebResponse response = null;

                // Prepare to try request up to 3 times, in case of closed connections
                for (int i = 0; i < 3; i++)
                {
                    HttpWebRequest request = (HttpWebRequest)WebRequest.Create(url);
                    request.Referer = url;
                    request.CookieContainer = cookieJar == null ? new CookieContainer() : cookieJar;

                    request.AutomaticDecompression = DecompressionMethods.Deflate | DecompressionMethods.GZip;
                    request.ContentType = sendAsJsonData ? "application/json" : "application/x-www-form-urlencoded";

                    if (url.Contains("partners.geico.com"))
                        request.Headers.Add("Origin", "https://partners.geico.com");

                    request.UserAgent = USER_AGENT;
                    request.CachePolicy = new HttpRequestCachePolicy(HttpCacheAgeControl.MaxAge, TimeSpan.FromMinutes(0));
                    cookieJar = request.CookieContainer;

                    if (url == "https://reinventing-roadside.allstate.com/rr-prd/rspweb/common/isUserLoggedIn.do" ||
                        (postData != null && postData.Count > 0 && !sendAsQueryString))
                    {
                        request.Method = "POST";
                        using (var reader = request.GetRequestStream())
                        {

                            string bodyContents = "";
                            if (postData != null)
                            {
                                if (!sendAsJsonData)
                                {
                                    var outgoingPost = HttpUtility.ParseQueryString(String.Empty);
                                    StringBuilder sbPostData = new StringBuilder();
                                    //foreach (KeyValuePair<string, string> item in postData)
                                    //    sbPostData.AppendFormat("&{0}={1}", HttpUtility.UrlEncode(item.Key), HttpUtility.UrlEncode(item.Value));

                                    foreach (var x in postData)
                                        outgoingPost.Add(x.Key, x.Value);

                                    bodyContents = outgoingPost.ToString();
                                }
                                else
                                {
                                    bodyContents = postData.ToJson();
                                }
                            }

                            log.RequestBody = bodyContents;

                            byte[] finalPost = Encoding.UTF8.GetBytes(bodyContents);
                            reader.Write(finalPost, 0, finalPost.Length);
                        }
                    }
                    if (sendAsQueryString && postData != null)
                    {
                        string prefix = "&";
                        foreach (KeyValuePair<string, string> item in postData)
                        {
                            prefix = (postData.First().Key == item.Key) ? "?" : "&";
                            url += string.Format("{0}{1}={2}", prefix, item.Key, item.Value);
                        }
                    }

                    foreach (string header in request.Headers.AllKeys)
                    {
                        if (!log.RequestHeaders.ContainsKey(header))
                            log.RequestHeaders.Add(header, request.Headers[header]);
                    }

                    try
                    {
                        response = (HttpWebResponse)request.GetResponse();
                        break;
                    }
                    catch (WebException ex)
                    {
                        // If the server closed the connection and didn't keep it alive
                        if (ex.Message.Contains("The underlying connection was closed: A connection that was expected to be kept alive was closed by the server."))
                        {
                            // If this is not the 3rd attempt, try again
                            if (i < 2)
                                continue;
                        }

                        // Ignore internal errors so we can at least get response cookies
                        response = (HttpWebResponse)ex.Response;

                        // FIX, the ex object doesn't have a Response property because the request never was completed
                        // This can happen with an error like: "The remote name could not be resolved ...", "Proxy Error...", or similar 
                        if (response == null)
                        {
                            throw ex;
                        }
                    }
                }
                
                log.RequestEnd = DateTime.Now;

                foreach (string header in response.Headers.AllKeys)
                {
                    if (!log.ResponseHeaders.ContainsKey(header))
                        log.ResponseHeaders.Add(header, response.Headers[header]);
                }

                log.ResponseCode = Convert.ToInt32(response.StatusCode);
                log.ResponseCodeDescription = response.StatusDescription;

                string content = string.Empty;
                using (var stream = response.GetResponseStream())
                {
                    using (var reader = new StreamReader(stream, Encoding.UTF8))
                    {
                        content = reader.ReadToEnd();
                    }
                }

                log.ResponseBody = content;

                // This loop may be completely unnecessary; .NET automatically updates cookieJar using a pointer to response.Cookies
                // So cookies don't need to be re-added to cookieJar.  Doing so can create duplicate cookies (which might be ok)
                //foreach (Cookie cookie in response.Cookies)
                //    cookieJar.Add(new Cookie(cookie.Name.Trim(), cookie.Value.Trim(), cookie.Path, cookie.Domain));

                result.LoadHtml(content);
            }

            return result;
        }
      


        static public string Post(string url, string postData, 
            string csrfToken, ref CookieContainer cookieJar, ref AutomatedHttpLog log, bool testMode = false,
            string bearerToken = null)
        {
            HtmlDocument result = new HtmlDocument();

            log.RequestUrl = url;
            System.Net.ServicePointManager.Expect100Continue = false;
            System.Net.ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12 | SecurityProtocolType.Tls11 | SecurityProtocolType.Tls; // comparable to modern browsers

            HttpWebRequest request = (HttpWebRequest)WebRequest.Create(url);
            request.Referer = url;

            if (request.Referer.StartsWith("https://partners.geico.com/ERPBWeb"))
                request.Referer = "https://partners.geico.com/ERPBWeb";

            request.CookieContainer = cookieJar == null ? new CookieContainer() : cookieJar;

            request.AutomaticDecompression = DecompressionMethods.Deflate | DecompressionMethods.GZip;
            request.ContentType = "application/json";

            if (url.Contains("partners.geico.com"))
            {
                request.Headers.Add("Origin", "https://partners.geico.com");
                request.Accept = "application/json, text/javascript, */*; q=0.01";
            }

            if (csrfToken != null)
                request.Headers.Add("X-CSRF-TOKEN", csrfToken);

            request.Headers.Add("X-Requested-With", "XMLHttpRequest");

            if (bearerToken != null)
                request.Headers.Add("Authorization", "Bearer " + bearerToken);

            request.UserAgent = USER_AGENT;
            request.CachePolicy = new HttpRequestCachePolicy(HttpCacheAgeControl.MaxAge, TimeSpan.FromMinutes(0));
            cookieJar = request.CookieContainer;

            foreach (string header in request.Headers.AllKeys)
            {
                if (!log.RequestHeaders.ContainsKey(header))
                    log.RequestHeaders.Add(header, request.Headers[header]);
            }
            request.Method = "POST";

            using (var reader = request.GetRequestStream())
            {

                string bodyContents = "";
                if (postData != null)
                {
                    bodyContents = postData;
                }

                log.RequestBody = bodyContents;

                byte[] finalPost = Encoding.UTF8.GetBytes(bodyContents);
                reader.Write(finalPost, 0, finalPost.Length);
            }
        

        log.RequestTime = DateTime.Now;


            if (!testMode)
            {
                HttpWebResponse response = null;

                // Prepare to try request up to 3 times, in case of closed connections
                for (int i = 0; i < 3; i++)
                {
                    try
                    {
                        
                        response = (HttpWebResponse)request.GetResponse();
                        break;
                    }
                    catch (WebException ex)
                    {
                        // If the server closed the connection and didn't keep it alive
                        if (ex.Message.Contains("The underlying connection was closed: A connection that was expected to be kept alive was closed by the server."))
                        {
                            // If this is not the 3rd attempt, try again
                            if (i < 2)
                                continue;
                        }

                        // Ignore internal errors so we can at least get response cookies
                        response = (HttpWebResponse)ex.Response;

                        // FIX, the ex object doesn't have a Response property because the request never was completed
                        // This can happen with an error like: "The remote name could not be resolved ...", "Proxy Error...", or similar 
                        if (response == null)
                        {
                            throw ex;
                        }
                    }
                }

                log.RequestEnd = DateTime.Now;

                foreach (string header in response.Headers.AllKeys)
                {
                    if (!log.ResponseHeaders.ContainsKey(header))
                        log.ResponseHeaders.Add(header, response.Headers[header]);
                }

                log.ResponseCode = Convert.ToInt32(response.StatusCode);
                log.ResponseCodeDescription = response.StatusDescription;

                string content = string.Empty;
                using (var stream = response.GetResponseStream())
                {
                    using (var reader = new StreamReader(stream, Encoding.UTF8))
                    {
                        content = reader.ReadToEnd();
                    }
                }

                log.ResponseBody = content;

                // This loop may be completely unnecessary; .NET automatically updates cookieJar using a pointer to response.Cookies
                // So cookies don't need to be re-added to cookieJar.  Doing so can create duplicate cookies (which might be ok)
                foreach (Cookie cookie in response.Cookies)
                    cookieJar.Add(new Cookie(cookie.Name.Trim(), cookie.Value.Trim(), cookie.Path, cookie.Domain));

                return content;
            }

            return null;
        }


        static public string Get(string url,
            ref CookieContainer cookieJar, ref AutomatedHttpLog log,
            string bearerToken)
        {
            HtmlDocument result = new HtmlDocument();

            log.RequestUrl = url;
            System.Net.ServicePointManager.Expect100Continue = false;
            System.Net.ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12 | SecurityProtocolType.Tls11 | SecurityProtocolType.Tls; // comparable to modern browsers

            HttpWebResponse response = null;
            // Prepare to try request up to 3 times, in case of closed connections
            for (int i = 0; i < 3; i++)
            {
                try
                {
                    HttpWebRequest request = (HttpWebRequest)WebRequest.Create(url);

                    request.Referer = url;
                    if (request.Referer.StartsWith("https://partners.geico.com/ERPBWeb"))
                        request.Referer = "https://partners.geico.com/ERPBWeb";


                    request.CookieContainer = cookieJar == null ? new CookieContainer() : cookieJar;

                    request.AutomaticDecompression = DecompressionMethods.Deflate | DecompressionMethods.GZip;
                    request.ContentType = "application/json";

                    request.Headers.Add("X-Requested-With", "XMLHttpRequest");

                    if (bearerToken != null)
                        request.Headers.Add("Authorization", "Bearer " + bearerToken);

                    request.UserAgent = USER_AGENT;
                    cookieJar = request.CookieContainer;

                    foreach (string header in request.Headers.AllKeys)
                    {
                        if (!log.RequestHeaders.ContainsKey(header))
                            log.RequestHeaders.Add(header, request.Headers[header]);
                    }
                    request.Method = "GET";

                    log.RequestTime = DateTime.Now;

                    response = (HttpWebResponse)request.GetResponse();
                    break;
                }
                catch (WebException ex)
                {
                    // If the server closed the connection and didn't keep it alive
                    if (ex.Message.Contains("The underlying connection was closed: A connection that was expected to be kept alive was closed by the server."))
                    {
                        // If this is not the 3rd attempt, try again
                        if (i < 2)
                            continue;
                    }

                    // Ignore internal errors so we can at least get response cookies
                    response = (HttpWebResponse)ex.Response;

                    // FIX, the ex object doesn't have a Response property because the request never was completed
                    // This can happen with an error like: "The remote name could not be resolved ...", "Proxy Error...", or similar 
                    if (response == null)
                    {
                        throw ex;
                    }
                }
            }

            log.RequestEnd = DateTime.Now;

            foreach (string header in response.Headers.AllKeys)
            {
                if (!log.ResponseHeaders.ContainsKey(header))
                    log.ResponseHeaders.Add(header, response.Headers[header]);
            }

            log.ResponseCode = Convert.ToInt32(response.StatusCode);
            log.ResponseCodeDescription = response.StatusDescription;

            string content = string.Empty;
            using (var stream = response.GetResponseStream())
            {
                using (var reader = new StreamReader(stream, Encoding.UTF8))
                {
                    content = reader.ReadToEnd();
                }
            }

            log.ResponseBody = content;

            // This loop may be completely unnecessary; .NET automatically updates cookieJar using a pointer to response.Cookies
            // So cookies don't need to be re-added to cookieJar.  Doing so can create duplicate cookies (which might be ok)
            foreach (Cookie cookie in response.Cookies)
                cookieJar.Add(new Cookie(cookie.Name.Trim(), cookie.Value.Trim(), cookie.Path, cookie.Domain));

            return content;
        }

        static public Stream GetData(string url, Dictionary<string, string> postData, ref CookieContainer cookieJar)
        {
            HttpWebRequest request = (HttpWebRequest)WebRequest.Create(url);
            request.CookieContainer = cookieJar == null ? new CookieContainer() : cookieJar;
            cookieJar = request.CookieContainer;

            if (postData != null && postData.Count > 0)
            {
                request.Method = "POST";
                request.ContentType = "application/x-www-form-urlencoded";
                Stream reader = request.GetRequestStream();
                try
                {
                    StringBuilder sbPostData = new StringBuilder();
                    foreach (KeyValuePair<string, string> item in postData)
                        sbPostData.AppendFormat("&{0}={1}", HttpUtility.UrlEncode(item.Key), HttpUtility.UrlEncode(item.Value));

                    byte[] finalPost = Encoding.UTF8.GetBytes(sbPostData.ToString());
                    reader.Write(finalPost, 0, finalPost.Length);
                }
                finally
                {
                    reader.Close();
                }
            }

            HttpWebResponse response = (HttpWebResponse)request.GetResponse();

            foreach (Cookie cookie in response.Cookies)
                cookieJar.Add(new Cookie(cookie.Name.Trim(), cookie.Value.Trim(), cookie.Path, cookie.Domain));

            return response.GetResponseStream();
        }

        static public void CopyStream(Stream input, Stream output)
        {
            byte[] buffer = new byte[8 * 1024];
            int len;
            while ((len = input.Read(buffer, 0, buffer.Length)) > 0)
            {
                output.Write(buffer, 0, len);
            }
        }

        static public void PrintCookies(string message, CookieContainer cookieJar)
        {
            // Usage:  PrintCookies($"PRINTING COOKIES - AFTER call to: \n{url}", cookieJar);

            Console.WriteLine(message);

            try
            {
                var cookiesList = new List<Cookie>();

                // Get the domains from the domain table
                var table = (Hashtable)cookieJar.GetType().InvokeMember("m_domainTable",
                    BindingFlags.NonPublic | BindingFlags.GetField | BindingFlags.Instance, null, cookieJar, new object[] {});

                // For each domain
                foreach (var key in table.Keys) 
                {
                    // Remove initial '.' or GetCookies() will fail
                    var keyStr = key.ToString().TrimStart('.');

                    // Get http cookies
                    foreach (Cookie c in cookieJar.GetCookies(new Uri($"http://{keyStr}/")))
                        if (!cookiesList.Contains(c)) cookiesList.Add(c);

                    // Get https cookies
                    foreach (Cookie c in cookieJar.GetCookies(new Uri($"https://{keyStr}/")))
                        if (!cookiesList.Contains(c)) cookiesList.Add(c);
                }

                Console.WriteLine($"\n  {cookiesList.Count} DISTINCT COOKIES ");

                foreach (var c in cookiesList.OrderBy(k => k.Name.ToLower()))
                    Console.WriteLine($"    Name = {c.Name} ; Value = {string.Join("", c.Value.Take(100))} ; Domain = {c.Domain}");

                Console.WriteLine();
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
            }
        }
    }
    public class FormElementCollection : Dictionary<string, string>
    {
        //
        // Note: To filter by formId or formName, you need to set HtmlAgilityPack.HtmlNode.ElementsFlags
        //       before to call WebRequestHelper.GetHtml(...)
        // example:
        //       HtmlAgilityPack.HtmlNode.ElementsFlags.Remove("form");
        //       var html = WebRequestHelper.GetHtml(url, null, ref this.cookieContainer);
        //
        public FormElementCollection(HtmlDocument htmlDoc, string formId = "", string formName = "", bool includeDisabled = true)
        {
            HtmlNodeCollection all;

            if (formId != "")
            {
                all = htmlDoc.DocumentNode.SelectNodes(
                    $"//form[@id='{formId}']//select | " +
                    $"//form[@id='{formId}']//input | " +
                    $"//form[@id='{formId}']//textarea");
            }
            else if (formName != "")
            {
                all = htmlDoc.DocumentNode.SelectNodes(
                    $"//form[@name='{formName}']//select | " +
                    $"//form[@name='{formName}']//input | " +
                    $"//form[@name='{formName}']//textarea");
            }
            else
            {
                all = htmlDoc.DocumentNode.SelectNodes("//select | //input | //textarea");
            }

            if (all == null)
                return;

            foreach (var element in all)
            {
                switch (element.Name.ToLowerInvariant())
                {
                    case "input":
                        AddInputElement(element, includeDisabled);
                        break;
                    case "textarea":
                        AddTextareaElement(element, includeDisabled);
                        break;
                    case "select":
                        AddMenuElement(element, includeDisabled);
                        break;
                }
            }
        }

        private void AddInputElement(HtmlNode element, bool includeDisabled = true)
        {
            string name = element.GetAttributeValue("name", "");
            string value = element.GetAttributeValue("value", "");
            string type = element.GetAttributeValue("type", "");
            //Console.WriteLine("--> name: {0}, value: {1}", name, value);
            if (string.IsNullOrEmpty(name)) return;

            if (!includeDisabled)
            {
                var disabled = element.GetAttributeValue("disabled", "enabled");
                if (!disabled.Equals("enabled"))
                    return;
            }

            switch (type.ToLower())
            {
                case "checkbox":
                case "radio":
                    string isChecked = element.GetAttributeValue("checked", "unchecked");

                    if (!isChecked.Equals("unchecked"))
                    {
                        if (!ContainsKey(name))
                            Add(name, "");

                        if (value == "")
                            this[name] = "on";
                        else
                            this[name] = value;
                    }
                    break;
                case "button":
                case "submit":
                    // we should skip the following input types: button, submit
                    // because it can or can't to have a specified name or it can have repeated name
                    break;
                default:
                    if (!ContainsKey(name))
                        Add(name, value);
                    else
                        this[name] = value;
                    break;
            }
        }

        private void AddMenuElement(HtmlNode element, bool includeDisabled = true)
        {
            string name = element.GetAttributeValue("name", "");
            var options = element.Descendants("option");

            if (string.IsNullOrEmpty(name)) return;

            if (!includeDisabled)
            {
                var disabled = element.GetAttributeValue("disabled", "enabled");
                if (!disabled.Equals("enabled"))
                    return;
            }

            // choose the first option as default
            var firstOp = options.First();
            string defaultValue = firstOp.GetAttributeValue("value", firstOp.NextSibling.InnerText);

            if (!ContainsKey(name))
                Add(name, defaultValue);
            else
                this[name] = defaultValue;

            // check if any option is selected
            foreach (var option in options)
            {
                string selected = option.GetAttributeValue("selected", "notSelected");
                if (!selected.Equals("notSelected"))
                {
                    string selectedValue = option.GetAttributeValue("value", option.NextSibling.InnerText);
                    this[name] = selectedValue;
                }
            }
        }

        private void AddTextareaElement(HtmlNode element, bool includeDisabled = true)
        {
            string name = element.GetAttributeValue("name", "");
            if (string.IsNullOrEmpty(name)) return;

            if (!includeDisabled)
            {
                var disabled = element.GetAttributeValue("disabled", "enabled");
                if (!disabled.Equals("enabled"))
                    return;
            }

            if (!ContainsKey(name))
                Add(name, element.InnerText);
            else
                this[name] = element.InnerText;
        }
    }

}
