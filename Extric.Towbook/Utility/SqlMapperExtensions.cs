using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Reflection;
using System.Reflection.Emit;
using System.Text;
using System.Data.SqlClient;
using System.Collections.ObjectModel;
using System.Collections.Concurrent;
using System.Threading.Tasks;
using Dapper;
using static Microsoft.Azure.Amqp.Serialization.SerializableType;

namespace Extric.Towbook.Utility
{

    /// <summary>
    /// Inherit from this interface to indicate to <PERSON><PERSON> that KeyAttributes are used and need to be looked at.
    /// </summary>
    public interface IUsesSqlKey { }

    public static class DateTimeExtensions
    {
        private static readonly TimeZoneInfo timezone = TimeZoneInfo.FindSystemTimeZoneById("Eastern Standard Time");
        public static DateTime ToDatabaseTime(this DateTime value)
        {
            if (timezone == null)
                return value;

            try
            {
                return TimeZoneInfo.ConvertTime(value, timezone);
            }
            catch (ArgumentException)
            {
                return value;
            }
        }
    }

    /// <summary>
    /// Towbook-specific extensions to reduce code writing
    /// </summary>
    /// <remarks>
    /// Downside: it ties the code to SQL Server, but that's okay for Towbook since we're not going to move away from SQL Server.
    /// </remarks>
    public static partial class SqlMapper
    {
        public static void ConfigureSqlMapper()
        {
            var implementingTypes = AppDomain.CurrentDomain.GetAssemblies()
                .Where(o => o.FullName.StartsWith("Extric"))
                .SelectMany(assembly => assembly.GetTypes())
                .Where(t => 
                {
                    if (!t.IsClass)
                        return false;

                    try
                    {
                        var pi = t.GetProperties().FirstOrDefault(prop =>
                                            prop.GetCustomAttributes(true)
                                                .OfType<KeyAttribute>()
                                                .Any());
                        return (pi != null);
                    }
                    catch (Exception err)
                    {
                        //Console.WriteLine(t.FullName + ":" + err.Message);
                        return false;
                    }
                })
                .ToList();

            foreach (var type in implementingTypes)
            {
                Dapper.SqlMapper.SetTypeMap(type,
                    new Dapper.CustomPropertyTypeMap(type,
                    (t, columnName) =>
                    {
                        var pi = t.GetProperties().FirstOrDefault(prop =>
                                            prop.GetCustomAttributes(false)
                                                .OfType<KeyAttribute>()
                                                .Any(attr => attr.Name == columnName));

                        return pi != null ? pi : t.GetProperties().FirstOrDefault(prop => prop.Name.ToLowerInvariant() == columnName.ToLowerInvariant());
                    }));

                Console.WriteLine("configured " + type.AssemblyQualifiedName);
            }
        }
        /// <summary>
        /// Executes a stored procedure
        /// </summary>
        /// <returns></returns>
        public static int ExecuteSP(this IDbConnection cnn, string sql, dynamic param = null, IDbTransaction transaction = null, int? commandTimeout = null)
        {
            return Execute(cnn, sql, param, transaction, commandTimeout, CommandType.StoredProcedure);
        }
        public static async Task<int> ExecuteSpAsync(this IDbConnection cnn, string sql, dynamic param = null, IDbTransaction transaction = null, int? commandTimeout = null)
        {
            return await Dapper.SqlMapper.ExecuteAsync(cnn, sql, param, transaction, commandTimeout, CommandType.StoredProcedure);
        }
        public static async Task<int> ExecuteAsync(this IDbConnection cnn, string sql, object param = null, IDbTransaction? transaction = null, int? commandTimeout = null, CommandType? commandType = null)
        {
            return await Dapper.SqlMapper.ExecuteAsync(cnn, sql, param, transaction, commandTimeout, commandType);
        }


        public static int ExecuteSP(string sql, dynamic param)
		{
			using (var cnn = new SqlConnection (Core.ConnectionString))
			{
				cnn.Open();
				return Execute (cnn, sql, param, null, null, CommandType.StoredProcedure);
			}
		}


        public static async Task<int> ExecuteSpAsync(string sql, dynamic param)
        {
            using (var cnn = new SqlConnection(Core.ConnectionString))
            {
                await cnn.OpenAsync();
                return await Dapper.SqlMapper.ExecuteAsync(cnn, sql, param, null, null, CommandType.StoredProcedure);
            }
        }


        public static int ExecuteSP(string sql, dynamic param = null, IDbTransaction transaction = null, int? commandTimeout = null, CommandType? commandType = CommandType.StoredProcedure, string connectionString = null)
        {
            using (var cnn = new SqlConnection(connectionString == null ? Core.ConnectionString : connectionString))
            {
                cnn.Open();
                return Execute(cnn, sql, param, transaction, commandTimeout, CommandType.StoredProcedure);
            }
        }

        public static async Task<int> ExecuteSpAsync(string sql, dynamic param = null, IDbTransaction transaction = null, int? commandTimeout = null, CommandType? commandType = CommandType.StoredProcedure, string connectionString = null)
        {
            using (var cnn = new SqlConnection(connectionString == null ? Core.ConnectionString : connectionString))
            {
                await cnn.OpenAsync();
                return await Dapper.SqlMapper.ExecuteAsync(cnn, sql, param, transaction, commandTimeout, CommandType.StoredProcedure);
            }
        }


        /// <summary>
        /// Executes a query with a default commandType of Stored Procedure
        /// </summary>
        /// <param name="sql"></param>
        /// <param name="param"></param>
        /// <param name="transaction"></param>
        /// <param name="buffered"></param>
        /// <param name="commandTimeout"></param>
        /// <param name="commandType"></param>
        /// <param name="connectionString"></param>
        /// <returns></returns>
        public static IEnumerable<dynamic> QuerySP(
			string sql,
			dynamic param)
		{
			using (var cnn = new SqlConnection(Core.ConnectionString))
			{
				cnn.Open();
				return Query<DapperRow>(cnn, sql, param as object, null, true, null, CommandType.StoredProcedure);
			}
		}


        /// <summary>
        /// Executes a query with a default commandType of Stored Procedure
        /// </summary>
        /// <param name="sql"></param>
        /// <param name="param"></param>
        /// <param name="transaction"></param>
        /// <param name="buffered"></param>
        /// <param name="commandTimeout"></param>
        /// <param name="commandType"></param>
        /// <param name="connectionString"></param>
        /// <returns></returns>
        public static async Task<IEnumerable<dynamic>> QuerySpAsync(
            string sql,
            dynamic param)
        {
            using (var cnn = new SqlConnection(Core.ConnectionString))
            {
                await cnn.OpenAsync();
                return await Dapper.SqlMapper.QueryAsync<dynamic>(
                    cnn: cnn, 
                    sql: sql, 
                    param: param as object, 
                    commandType: CommandType.StoredProcedure);
            }
        }


        /// <summary>
        /// Executes a query with a default commandType of Stored Procedure
        /// </summary>
        /// <param name="sql"></param>
        /// <param name="param"></param>
        /// <param name="transaction"></param>
        /// <param name="buffered"></param>
        /// <param name="commandTimeout"></param>
        /// <param name="commandType"></param>
        /// <param name="connectionString"></param>
        /// <returns></returns>
        public static IEnumerable<dynamic> QuerySP(string sql,
            dynamic param,
            IDbTransaction transaction = null,
            bool buffered = true,
            int? commandTimeout = null,
            CommandType? commandType = CommandType.StoredProcedure,
            string connectionString = null)
        {
            using (var cnn = new SqlConnection(connectionString == null ? Core.ConnectionString : connectionString))
            {
                cnn.Open();
                return Query<DapperRow>(cnn, sql, param as object, transaction, buffered, commandTimeout, commandType);
            }
        }

		/// <summary>
		/// Executes a query, returning the data typed as per T
		/// </summary>
		/// <remarks>the dynamic param may seem a bit odd, but this works around a major usability issue in vs, if it is Object vs completion gets annoying. Eg type new [space] get new object</remarks>
		/// <returns>A sequence of data of the supplied type; if a basic type (int, string, etc) is queried then the data from the first column in assumed, otherwise an instance is
		/// created per row, and a direct column-name===member-name mapping is assumed (case insensitive).
		/// </returns>
		public static IEnumerable<T> QuerySP<T>(string sql, dynamic param)
		{
			using (var cnn = new SqlConnection(Core.ConnectionString))
			{
				cnn.Open();
				var data = QueryInternal<T>(cnn, sql, param as object, null, null, CommandType.StoredProcedure);
				return data.ToList ();
			}
		}
        /// <summary>
        /// Executes a query, returning the data typed as per T
        /// </summary>
        /// <remarks>the dynamic param may seem a bit odd, but this works around a major usability issue in vs, if it is Object vs completion gets annoying. Eg type new [space] get new object</remarks>
        /// <returns>A sequence of data of the supplied type; if a basic type (int, string, etc) is queried then the data from the first column in assumed, otherwise an instance is
        /// created per row, and a direct column-name===member-name mapping is assumed (case insensitive).
        /// </returns>
        public static async Task<IEnumerable<T>> QuerySpAsync<T>(string sql, object param = null, IDbTransaction transaction = null, int? commandTimeout = null)
        {
            using (var cnn = new SqlConnection(Core.ConnectionString))
            {
                await cnn.OpenAsync();
                var data = await Dapper.SqlMapper.QueryAsync<T>(cnn, sql, param, transaction, commandTimeout, CommandType.StoredProcedure);
                return data.ToList();
            }
        }
        public static async Task<IEnumerable<T>> QueryAsync<T>(string sql, object param)
        {
            using (var cnn = new SqlConnection(Core.ConnectionString))
            {
                await cnn.OpenAsync();
                // check if T inherits IUsesSqlKey
                //if (typeof(T).Name.StartsWith("Extric") && 
                //    !typeof(T).GetInterfaces().Contains(typeof(IUsesSqlKey)))
                //{
                //    throw new Exception(typeof(T).FullName + " must implement IUsesSqlKey to use this method.");
                //}

                var data = await Dapper.SqlMapper.QueryAsync<T>(cnn, sql, param, null, null);
                return data.ToList();
            }
        }

        /// <summary>
        /// Executes a query, returning the data typed as per T
        /// </summary>
        /// <remarks>the dynamic param may seem a bit odd, but this works around a major usability issue in vs, if it is Object vs completion gets annoying. Eg type new [space] get new object</remarks>
        /// <returns>A sequence of data of the supplied type; if a basic type (int, string, etc) is queried then the data from the first column in assumed, otherwise an instance is
        /// created per row, and a direct column-name===member-name mapping is assumed (case insensitive).
        /// </returns>
        public static IEnumerable<T> QuerySP<T>(string sql, dynamic param = null, IDbTransaction transaction = null, bool buffered = true, int? commandTimeout = null, CommandType? commandType = CommandType.StoredProcedure,
            string connectionString = null)
        {
            using (var cnn = new SqlConnection(connectionString == null ? Core.ConnectionString : connectionString))
            {
                cnn.Open();
                var data = QueryInternal<T>(cnn, sql, param as object, transaction, commandTimeout, commandType);
                return buffered ? data.ToList() : data;
            }
        }

        public static IEnumerable<T> Query<T>(string sql, dynamic param = null, IDbTransaction transaction = null, bool buffered = true, int? commandTimeout = null, CommandType? commandType = null,
        string connectionString = null)
        {
            using (var cnn = new SqlConnection(connectionString == null ? Core.ConnectionString : connectionString))
            {
                cnn.Open();
                var data = QueryInternal<T>(cnn, sql, param as object, transaction, commandTimeout, commandType);
                return buffered ? data.ToList() : data;
            }
        }

        public static Collection<T> ToCollection<T>(this IEnumerable<T> enumerable)
        {
            if (enumerable == null)
                return new Collection<T>();

            var collection = new Collection<T>();
            foreach (T i in enumerable)
                collection.Add(i);
            return collection;
        }

        public static dynamic ToDynamic(this object value)
        {
            IDictionary<string, object> expando = new System.Dynamic.ExpandoObject();

            foreach (System.ComponentModel.PropertyDescriptor property in System.ComponentModel.TypeDescriptor.GetProperties(value.GetType()))
                expando.Add(property.Name, property.GetValue(value));

            return expando as System.Dynamic.ExpandoObject;
        }



        public static GridReader QueryMultiple(string sql, dynamic param = null, IDbTransaction transaction = null, int? commandTimeout = null, CommandType? commandType = null, string connectionString = null)
        {

            using (var cnn = new SqlConnection(connectionString == null ? Core.ConnectionString : connectionString))
            {
                cnn.Open();
                return QueryMultiple(cnn, sql, param, transaction, commandTimeout, commandType);
            }
        }

        public static long Insert<T>(T entityToInsert, IDbTransaction transaction = null, int? commandTimeout = null, string connectionString = null) where T : class
        {
            using (var cnn = new SqlConnection(connectionString == null ? Core.ConnectionString : connectionString))
            {
                cnn.Open();
                return cnn.Insert<T>(entityToInsert, transaction, commandTimeout);
            }
        }

        public static async Task<long> InsertAsync<T>(T entityToInsert, IDbTransaction transaction = null, int? commandTimeout = null, string connectionString = null) where T : class
        {
            using (var cnn = new SqlConnection(connectionString == null ? Core.ConnectionString : connectionString))
            {
                await cnn.OpenAsync();
                return await cnn.InsertAsyncTb<T>(entityToInsert, transaction, commandTimeout);
            }
        }
        public static bool Update<T>(T entityToInsert, IDbTransaction transaction = null, int? commandTimeout = null, string connectionString = null) where T : class
        {
            using (var cnn = new SqlConnection(connectionString == null ? Core.ConnectionString : connectionString))
            {
                cnn.Open();
                return cnn.Update<T>(entityToInsert, transaction, commandTimeout);
            }
        }

        public static async Task<bool> UpdateAsync<T>(T entityToInsert, IDbTransaction transaction = null, int? commandTimeout = null, string connectionString = null) where T : class
        {
            using (var cnn = new SqlConnection(connectionString == null ? Core.ConnectionString : connectionString))
            {
                await cnn.OpenAsync();
                return await cnn.UpdateAsyncTb<T>(entityToInsert, transaction, commandTimeout);
            }
        }

        public static bool Delete<T>(T entityToInsert, IDbTransaction transaction = null, int? commandTimeout = null, string connectionString = null) where T : class
        {
            using (var cnn = new SqlConnection(connectionString == null ? Core.ConnectionString : connectionString))
            {
                cnn.Open();
                return cnn.Delete<T>(entityToInsert, transaction, commandTimeout);
            }
        }

        public static bool SoftDelete<T>(T entityToInsert, IDbTransaction transaction = null, int? commandTimeout = null, string connectionString = null) where T : class
        {
            using (var cnn = new SqlConnection(connectionString == null ? Core.ConnectionString : connectionString))
            {
                cnn.Open();
                return cnn.SoftDelete<T>(entityToInsert, transaction, commandTimeout);
            }
        }
    }


    public static class SqlMapperExtensions
    {
        public interface IProxy
        {
            bool IsDirty { get; set; }
        }

        private static readonly ConcurrentDictionary<RuntimeTypeHandle, IEnumerable<PropertyInfo>> KeyProperties = new ConcurrentDictionary<RuntimeTypeHandle, IEnumerable<PropertyInfo>>();
        private static readonly ConcurrentDictionary<RuntimeTypeHandle, IEnumerable<PropertyInfo>> TypeProperties = new ConcurrentDictionary<RuntimeTypeHandle, IEnumerable<PropertyInfo>>();
        private static readonly ConcurrentDictionary<RuntimeTypeHandle, string> GetQueries = new ConcurrentDictionary<RuntimeTypeHandle, string>();
        private static readonly ConcurrentDictionary<RuntimeTypeHandle, string> TypeTableName = new ConcurrentDictionary<RuntimeTypeHandle, string>();

        private static readonly Dictionary<string, ISqlAdapter> AdapterDictionary = new Dictionary<string, ISqlAdapter>() {
                       {"sqlconnection", new SqlServerAdapter()} };

        private static IEnumerable<PropertyInfo> KeyPropertiesCache(Type type)
        {

            IEnumerable<PropertyInfo> pi;
            if (KeyProperties.TryGetValue(type.TypeHandle, out pi))
            {
                return pi;
            }

            var allProperties = TypePropertiesCache(type);
            var keyProperties = allProperties.Where(p => p.GetCustomAttributes(true).Any(a => a is KeyAttribute)).ToList();

            if (keyProperties.Count == 0)
            {
                var idProp = allProperties.Where(p => p.Name.ToLower() == "id").FirstOrDefault();
                if (idProp != null)
                {
                    keyProperties.Add(idProp);
                }
            }

            KeyProperties[type.TypeHandle] = keyProperties;

            return keyProperties;
        }
        private static IEnumerable<PropertyInfo> TypePropertiesCache(Type type)
        {
            IEnumerable<PropertyInfo> pis;
            if (TypeProperties.TryGetValue(type.TypeHandle, out pis))
            {
                return pis;
            }

            var properties = type.GetProperties().Where(IsWriteable);
            TypeProperties[type.TypeHandle] = properties;
            return properties;
        }

        public static bool IsWriteable(PropertyInfo pi)
        {
            object[] attributes = pi.GetCustomAttributes(typeof(WriteAttribute), false);
            if (attributes.Length == 1)
            {
                WriteAttribute write = (WriteAttribute)attributes[0];
                return write.Write;
            }
            return true;
        }

        public static bool IsIgnored(PropertyInfo pi)
        {
            object[] attributes = pi.GetCustomAttributes(typeof(IgnoreAttribute), false);
            if (attributes.Length == 1)
            {
                IgnoreAttribute write = (IgnoreAttribute)attributes[0];
                return write.Ignore;
            }
            return true;
        }

        /// <summary>
        /// Returns a single entity by a single id from table "Ts". T must be of interface type.
        /// Id must be marked with [Key] attribute.
        /// Created entity is tracked/intercepted for changes and used by the Update() extension.
        /// </summary>
        /// <typeparam name="T">Interface type to create and populate</typeparam>
        /// <param name="connection">Open SqlConnection</param>
        /// <param name="id">Id of the entity to get, must be marked with [Key] attribute</param>
        /// <returns>Entity of T</returns>
        public static T Get<T>(this IDbConnection connection, dynamic id, IDbTransaction transaction = null, int? commandTimeout = null) where T : class
        {
            var type = typeof(T);
            string sql;
            if (!GetQueries.TryGetValue(type.TypeHandle, out sql))
            {
                var keys = KeyPropertiesCache(type);
                if (keys.Count() > 1)
                    throw new DataException("Get<T> only supports an entity with a single [Key] property");
                if (keys.Count() == 0)
                    throw new DataException("Get<T> only supports en entity with a [Key] property");

                var onlyKey = keys.First();

                var keyAttribute = onlyKey.GetCustomAttribute(typeof(KeyAttribute)) as KeyAttribute;

                var onlyKeyName = keyAttribute?.Name ?? onlyKey.Name;

                var name = GetTableName(type);


                // TODO: pluralizer
                // TODO: query information schema and only select fields that are both in information schema and underlying class / interface
                sql = "select * from " + name + " where " + onlyKeyName + " = @id";
                GetQueries[type.TypeHandle] = sql;
            }

            var dynParms = new DynamicParameters();
            dynParms.Add("@id", id);

            T obj = null;

            if (type.IsInterface)
            {
                var res = connection.Query(sql, dynParms).FirstOrDefault() as IDictionary<string, object>;

                if (res == null)
                    return (T)((object)null);

                obj = ProxyGenerator.GetInterfaceProxy<T>();

                foreach (var property in TypePropertiesCache(type))
                {
                    var val = res[property.Name];
                    property.SetValue(obj, val, null);
                }

                ((IProxy)obj).IsDirty = false; //reset change tracking and return
            }
            else
            {
                obj = connection.Query<T>(sql, dynParms, transaction: transaction, commandTimeout: commandTimeout).FirstOrDefault();
            }
            return obj;
        }

        private static string GetTableName(Type type)
        {
            string name;
            if (!TypeTableName.TryGetValue(type.TypeHandle, out name))
            {
                if (type.Name.Substring(type.Name.Length - 1) == "y")
                    name = type.Name.Substring(0, type.Name.Length - 1) + "ies";
                else
                    name = type.Name + "s";
                if (type.IsInterface && name.StartsWith("I"))
                    name = name.Substring(1);

                //NOTE: This as dynamic trick should be able to handle both our own Table-attribute as well as the one in EntityFramework
                var tableattr = type.GetCustomAttributes(false).Where(attr => attr.GetType().Name == "TableAttribute").SingleOrDefault() as
                dynamic;
                if (tableattr != null)
                    name = tableattr.Name;
                TypeTableName[type.TypeHandle] = name;
            }
            return name;
        }

        /// <summary>
        /// Inserts an entity into table "Ts" and returns identity id.
        /// </summary>
        /// <param name="connection">Open SqlConnection</param>
        /// <param name="entityToInsert">Entity to insert</param>
        /// <returns>Identity of inserted entity</returns>
        public static long Insert<T>(this IDbConnection connection, T entityToInsert, IDbTransaction transaction = null, int? commandTimeout = null) where T : class
        {

            var type = typeof(T);

            var name = GetTableName(type);

            var sbColumnList = new StringBuilder(null);

            var allProperties = TypePropertiesCache(type);
            var keyProperties = KeyPropertiesCache(type);

            // Get all "Key" attributes defined but ignore the identity key (for an insert)
            var allPropertiesExceptFirstKey = allProperties.Except(keyProperties.Take(1));

            for (var i = 0; i < allPropertiesExceptFirstKey.Count(); i++)
            {
                var property = allPropertiesExceptFirstKey.ElementAt(i);
                var att = property.GetCustomAttribute(typeof(KeyAttribute)) as KeyAttribute;

                var keyName = att?.Name ?? property.Name;

                sbColumnList.AppendFormat("[{0}]", keyName);
                if (i < allPropertiesExceptFirstKey.Count() - 1)
                    sbColumnList.Append(", ");
            }

            var sbParameterList = new StringBuilder(null);
            for (var i = 0; i < allPropertiesExceptFirstKey.Count(); i++)
            {
                var property = allPropertiesExceptFirstKey.ElementAt(i);
                sbParameterList.AppendFormat("@{0}", property.Name);
                if (i < allPropertiesExceptFirstKey.Count() - 1)
                    sbParameterList.Append(", ");
            }
            ISqlAdapter adapter = GetFormatter(connection);
            int id = adapter.Insert(connection, transaction, commandTimeout, name, sbColumnList.ToString(), sbParameterList.ToString(), keyProperties, entityToInsert);
            return id;
        }

        public static async Task<long> InsertAsyncTb<T>(this IDbConnection connection, T entityToInsert, IDbTransaction transaction = null, int? commandTimeout = null) where T : class
        {
            var type = typeof(T);

            var name = GetTableName(type);

            var sbColumnList = new StringBuilder(null);

            var allProperties = TypePropertiesCache(type);
            var keyProperties = KeyPropertiesCache(type);

            // Get all "Key" attributes defined but ignore the identity key (for an insert)
            var allPropertiesExceptFirstKey = allProperties.Except(keyProperties.Take(1));

            for (var i = 0; i < allPropertiesExceptFirstKey.Count(); i++)
            {
                var property = allPropertiesExceptFirstKey.ElementAt(i);

                var ignoreAtt = property.GetCustomAttribute(typeof(IgnoreAttribute)) as IgnoreAttribute;
                if (ignoreAtt != null)
                {
                    continue;
                }

                var att = property.GetCustomAttribute(typeof(KeyAttribute)) as KeyAttribute;
                var keyName = att?.Name ?? property.Name;

                sbColumnList.AppendFormat("[{0}]", keyName);
                if (i < allPropertiesExceptFirstKey.Count() - 1)
                    sbColumnList.Append(", ");
            }

            var sbParameterList = new StringBuilder(null);
            for (var i = 0; i < allPropertiesExceptFirstKey.Count(); i++)
            {
                var property = allPropertiesExceptFirstKey.ElementAt(i);
                var ignoreAtt = property.GetCustomAttribute(typeof(IgnoreAttribute)) as IgnoreAttribute;
                if (ignoreAtt != null)
                {
                    continue;
                }

                sbParameterList.AppendFormat("@{0}", property.Name);
                if (i < allPropertiesExceptFirstKey.Count() - 1)
                    sbParameterList.Append(", ");
            }

            ISqlAdapter adapter = GetFormatter(connection);
            int id = await adapter.InsertAsync(connection, transaction, commandTimeout, name, 
                sbColumnList.ToString().Trim().Trim(','), 
                sbParameterList.ToString().Trim().Trim(','), keyProperties, entityToInsert);
            return id;
        }



        /// <summary>
        /// Updates entity in table "Ts", checks if the entity is modified if the entity is tracked by the Get() extension.
        /// </summary>
        /// <typeparam name="T">Type to be updated</typeparam>
        /// <param name="connection">Open SqlConnection</param>
        /// <param name="entityToUpdate">Entity to be updated</param>
        /// <returns>true if updated, false if not found or not modified (tracked entities)</returns>
        public static bool Update<T>(this IDbConnection connection, T entityToUpdate, IDbTransaction transaction = null, int? commandTimeout = null) where T : class
        {
            var proxy = entityToUpdate as IProxy;
            if (proxy != null)
            {
                if (!proxy.IsDirty) return false;
            }

            var type = typeof(T);

            var keyProperties = KeyPropertiesCache(type);
            if (!keyProperties.Any())
                throw new ArgumentException("Entity must have at least one [Key] property");

            var name = GetTableName(type);

            var sb = new StringBuilder();
            sb.AppendFormat("update {0} set ", name);

            var allProperties = TypePropertiesCache(type);
            var nonIdProps = allProperties.Where(a => !keyProperties.Contains(a));

            for (var i = 0; i < nonIdProps.Count(); i++)
            {
                var property = nonIdProps.ElementAt(i);
                sb.AppendFormat("{0} = @{1}", property.Name, property.Name);
                if (i < nonIdProps.Count() - 1)
                    sb.AppendFormat(", ");
            }
            sb.Append(" where ");
            for (var i = 0; i < keyProperties.Count(); i++)
            {
                var property = keyProperties.ElementAt(i);
                var att = property.GetCustomAttribute(typeof(KeyAttribute)) as KeyAttribute;

                var keyName = att?.Name ?? property.Name;

                sb.AppendFormat("{0} = @{1}", keyName, property.Name);
                if (i < keyProperties.Count() - 1)
                    sb.AppendFormat(" and ");
            }
            var updated = connection.Execute(sb.ToString(), entityToUpdate, commandTimeout: commandTimeout, transaction: transaction);
            return updated > 0;
        }



        /// <summary>
        /// Updates entity in table "Ts", checks if the entity is modified if the entity is tracked by the Get() extension.
        /// </summary>
        /// <typeparam name="T">Type to be updated</typeparam>
        /// <param name="connection">Open SqlConnection</param>
        /// <param name="entityToUpdate">Entity to be updated</param>
        /// <returns>true if updated, false if not found or not modified (tracked entities)</returns>
        public static async Task<bool> UpdateAsyncTb<T>(this IDbConnection connection, T entityToUpdate, IDbTransaction transaction = null, int? commandTimeout = null) where T : class
        {
            var proxy = entityToUpdate as IProxy;
            if (proxy != null)
            {
                if (!proxy.IsDirty) return false;
            }

            var type = typeof(T);

            var keyProperties = KeyPropertiesCache(type);
            if (!keyProperties.Any())
                throw new ArgumentException("Entity must have at least one [Key] property");

            var name = GetTableName(type);

            var sb = new StringBuilder();
            sb.AppendFormat("update {0} set ", name);

            var allProperties = TypePropertiesCache(type);
            var nonIdProps = allProperties.Where(a => !keyProperties.Contains(a));

            for (var i = 0; i < nonIdProps.Count(); i++)
            {
                var property = nonIdProps.ElementAt(i);
                sb.AppendFormat("{0} = @{1}", property.Name, property.Name);
                if (i < nonIdProps.Count() - 1)
                    sb.AppendFormat(", ");
            }
            sb.Append(" where ");
            for (var i = 0; i < keyProperties.Count(); i++)
            {
                var property = keyProperties.ElementAt(i);

                var ignoreAtt = property.GetCustomAttribute(typeof(IgnoreAttribute)) as IgnoreAttribute;
                if (ignoreAtt != null)
                    continue;

                var att = property.GetCustomAttribute(typeof(KeyAttribute)) as KeyAttribute;

                var keyName = att?.Name ?? property.Name;

                sb.AppendFormat("{0} = @{1}", keyName, property.Name);
                if (i < keyProperties.Count() - 1)
                    sb.AppendFormat(" and ");
            }
            var updated = await connection.ExecuteAsync(sb.ToString(), entityToUpdate, commandTimeout: commandTimeout, transaction: transaction);
            return updated > 0;
        }



        /// <summary>
        /// Delete entity in table "Ts".
        /// </summary>
        /// <typeparam name="T">Type of entity</typeparam>
        /// <param name="connection">Open SqlConnection</param>
        /// <param name="entityToDelete">Entity to delete</param>
        /// <returns>true if deleted, false if not found</returns>
        public static bool Delete<T>(this IDbConnection connection, T entityToDelete, IDbTransaction transaction = null, int? commandTimeout = null) where T : class
        {
            if (entityToDelete == null)
                throw new ArgumentException("Cannot Delete null Object", "entityToDelete");

            var type = typeof(T);

            var keyProperties = KeyPropertiesCache(type);
            if (keyProperties.Count() == 0)
                throw new ArgumentException("Entity must have at least one [Key] property");

            var name = GetTableName(type);

            var sb = new StringBuilder();
            sb.AppendFormat("delete from {0} where ", name);

            for (var i = 0; i < keyProperties.Count(); i++)
            {
                var property = keyProperties.ElementAt(i);
                var att = property.GetCustomAttribute(typeof(KeyAttribute)) as KeyAttribute;

                var keyName = att?.Name ?? property.Name;

                sb.AppendFormat("{0} = @{1}", keyName, property.Name);
                if (i < keyProperties.Count() - 1)
                    sb.AppendFormat(" and ");
            }
            var deleted = connection.Execute(sb.ToString(), entityToDelete, transaction: transaction, commandTimeout: commandTimeout);
            return deleted > 0;
        }

        /// <summary>
        /// Delete entity in table "Ts" by setting IsDeleted column to 1
        /// </summary>
        /// <typeparam name="T">Type of entity</typeparam>
        /// <param name="connection">Open SqlConnection</param>
        /// <param name="entityToDelete">Entity to delete</param>
        /// <returns>true if soft deleted, false if not found</returns>
        public static bool SoftDelete<T>(this IDbConnection connection, T entityToDelete, IDbTransaction transaction = null, int? commandTimeout = null) where T : class
        {
            if (entityToDelete == null)
                throw new ArgumentException("Cannot soft delete null object", "entityToDelete");

            var type = typeof(T);

            var keyProperties = KeyPropertiesCache(type);
            if (keyProperties.Count() == 0)
                throw new ArgumentException("Entity must have at least one [Key] property");

            var name = GetTableName(type);

            var sb = new StringBuilder();
            sb.AppendFormat("update {0} set IsDeleted = 1 where ", name);

            for (var i = 0; i < keyProperties.Count(); i++)
            {
                var property = keyProperties.ElementAt(i);
                var att = property.GetCustomAttribute(typeof(KeyAttribute)) as KeyAttribute;

                var keyName = att?.Name ?? property.Name;

                sb.AppendFormat("{0} = @{1}", keyName, property.Name);
                if (i < keyProperties.Count() - 1)
                    sb.AppendFormat(" and ");
            }
            var updated = connection.Execute(sb.ToString(), entityToDelete, transaction: transaction, commandTimeout: commandTimeout);
            return updated > 0;
        }

        public static ISqlAdapter GetFormatter(IDbConnection connection)
        {
            string name = connection.GetType().Name.ToLower();
            if (!AdapterDictionary.ContainsKey(name))
                return new SqlServerAdapter();
            return AdapterDictionary[name];
        }

        class ProxyGenerator
        {
            private static readonly Dictionary<Type, object> TypeCache = new Dictionary<Type, object>();

            private static AssemblyBuilder GetAsmBuilder(string name)
            {
                var assemblyBuilder = AssemblyBuilder.DefineDynamicAssembly(new AssemblyName { Name = name },
                    AssemblyBuilderAccess.Run);

                return assemblyBuilder;
            }

            public static T GetClassProxy<T>()
            {
                // A class proxy could be implemented if all properties are virtual
                // otherwise there is a pretty dangerous case where internal actions will not update dirty tracking
                throw new NotImplementedException();
            }


            public static T GetInterfaceProxy<T>()
            {
                Type typeOfT = typeof(T);

                object k;
                if (TypeCache.TryGetValue(typeOfT, out k))
                {
                    return (T)k;
                }
                var assemblyBuilder = GetAsmBuilder(typeOfT.Name);

                var moduleBuilder = assemblyBuilder.DefineDynamicModule("SqlMapperExtensions." + typeOfT.Name); //NOTE: to save, add "asdasd.dll" parameter

                var interfaceType = typeof(SqlMapperExtensions.IProxy);
                var typeBuilder = moduleBuilder.DefineType(typeOfT.Name + "_" + Guid.NewGuid(),
                TypeAttributes.Public | TypeAttributes.Class);
                typeBuilder.AddInterfaceImplementation(typeOfT);
                typeBuilder.AddInterfaceImplementation(interfaceType);

                //create our _isDirty field, which implements IProxy
                var setIsDirtyMethod = CreateIsDirtyProperty(typeBuilder);

                // Generate a field for each property, which implements the T
                foreach (var property in typeof(T).GetProperties())
                {
                    var isId = property.GetCustomAttributes(true).Any(a => a is KeyAttribute);
                    CreateProperty<T>(typeBuilder, property.Name, property.PropertyType, setIsDirtyMethod, isId);
                }

                var generatedType = typeBuilder.CreateTypeInfo();

                //assemblyBuilder.Save(name + ".dll"); //NOTE: to save, uncomment

                var generatedObject = Activator.CreateInstance(generatedType);

                TypeCache.Add(typeOfT, generatedObject);
                return (T)generatedObject;
            }


            private static MethodInfo CreateIsDirtyProperty(TypeBuilder typeBuilder)
            {
                var propType = typeof(bool);
                var field = typeBuilder.DefineField("_" + "IsDirty", propType, FieldAttributes.Private);
                var property = typeBuilder.DefineProperty("IsDirty",
                System.Reflection.PropertyAttributes.None,
                propType,
                new Type[] { propType });

                const MethodAttributes getSetAttr = MethodAttributes.Public | MethodAttributes.NewSlot | MethodAttributes.SpecialName |
                MethodAttributes.Final | MethodAttributes.Virtual | MethodAttributes.HideBySig;

                // Define the "get" and "set" accessor methods
                var currGetPropMthdBldr = typeBuilder.DefineMethod("get_" + "IsDirty",
                getSetAttr,
                propType,
                Type.EmptyTypes);
                var currGetIL = currGetPropMthdBldr.GetILGenerator();
                currGetIL.Emit(OpCodes.Ldarg_0);
                currGetIL.Emit(OpCodes.Ldfld, field);
                currGetIL.Emit(OpCodes.Ret);
                var currSetPropMthdBldr = typeBuilder.DefineMethod("set_" + "IsDirty",
                getSetAttr,
                null,
                new Type[] { propType });
                var currSetIL = currSetPropMthdBldr.GetILGenerator();
                currSetIL.Emit(OpCodes.Ldarg_0);
                currSetIL.Emit(OpCodes.Ldarg_1);
                currSetIL.Emit(OpCodes.Stfld, field);
                currSetIL.Emit(OpCodes.Ret);

                property.SetGetMethod(currGetPropMthdBldr);
                property.SetSetMethod(currSetPropMthdBldr);
                var getMethod = typeof(SqlMapperExtensions.IProxy).GetMethod("get_" + "IsDirty");
                var setMethod = typeof(SqlMapperExtensions.IProxy).GetMethod("set_" + "IsDirty");
                typeBuilder.DefineMethodOverride(currGetPropMthdBldr, getMethod);
                typeBuilder.DefineMethodOverride(currSetPropMthdBldr, setMethod);

                return currSetPropMthdBldr;
            }

            private static void CreateProperty<T>(TypeBuilder typeBuilder, string propertyName, Type propType, MethodInfo setIsDirtyMethod, bool isIdentity)
            {
                //Define the field and the property
                var field = typeBuilder.DefineField("_" + propertyName, propType, FieldAttributes.Private);
                var property = typeBuilder.DefineProperty(propertyName,
                System.Reflection.PropertyAttributes.None,
                propType,
                new Type[] { propType });

                const MethodAttributes getSetAttr = MethodAttributes.Public | MethodAttributes.Virtual |
                MethodAttributes.HideBySig;

                // Define the "get" and "set" accessor methods
                var currGetPropMthdBldr = typeBuilder.DefineMethod("get_" + propertyName,
                getSetAttr,
                propType,
                Type.EmptyTypes);

                var currGetIL = currGetPropMthdBldr.GetILGenerator();
                currGetIL.Emit(OpCodes.Ldarg_0);
                currGetIL.Emit(OpCodes.Ldfld, field);
                currGetIL.Emit(OpCodes.Ret);

                var currSetPropMthdBldr = typeBuilder.DefineMethod("set_" + propertyName,
                getSetAttr,
                null,
                new Type[] { propType });

                //store value in private field and set the isdirty flag
                var currSetIL = currSetPropMthdBldr.GetILGenerator();
                currSetIL.Emit(OpCodes.Ldarg_0);
                currSetIL.Emit(OpCodes.Ldarg_1);
                currSetIL.Emit(OpCodes.Stfld, field);
                currSetIL.Emit(OpCodes.Ldarg_0);
                currSetIL.Emit(OpCodes.Ldc_I4_1);
                currSetIL.Emit(OpCodes.Call, setIsDirtyMethod);
                currSetIL.Emit(OpCodes.Ret);

                //TODO: Should copy all attributes defined by the interface?
                if (isIdentity)
                {
                    var keyAttribute = typeof(KeyAttribute);
                    var myConstructorInfo = keyAttribute.GetConstructor(new Type[] { });
                    var attributeBuilder = new CustomAttributeBuilder(myConstructorInfo, new object[] { });
                    property.SetCustomAttribute(attributeBuilder);
                }

                property.SetGetMethod(currGetPropMthdBldr);
                property.SetSetMethod(currSetPropMthdBldr);
                var getMethod = typeof(T).GetMethod("get_" + propertyName);
                var setMethod = typeof(T).GetMethod("set_" + propertyName);
                typeBuilder.DefineMethodOverride(currGetPropMthdBldr, getMethod);
                typeBuilder.DefineMethodOverride(currSetPropMthdBldr, setMethod);
            }

        }
    }

    [AttributeUsage(AttributeTargets.Class)]
    public class TableAttribute : Attribute
    {
        public TableAttribute(string tableName)
        {
            Name = tableName;
        }
        public string Name { get; private set; }
    }

    // do not want to depend on data annotations that is not in client profile
    [AttributeUsage(AttributeTargets.Property)]
    public class KeyAttribute : Attribute
    {
        public string Name { get; private set; }
        public KeyAttribute() { }
        public KeyAttribute(string sqlColumnName)
        {
            Name = sqlColumnName;
        }
    }

    [AttributeUsage(AttributeTargets.Property)]
    public class IgnoreAttribute : Attribute
    {
        public bool Ignore { get; private set; }
        public IgnoreAttribute(bool ignore)
        {
            Ignore = ignore;

        }

    }

    [AttributeUsage(AttributeTargets.Property)]
    public class WriteAttribute : Attribute
    {
        public WriteAttribute(bool write)
        {
            Write = write;
        }
        public bool Write { get; private set; }
    }


    public interface ISqlAdapter
    {
        int Insert(IDbConnection connection, IDbTransaction transaction, int? commandTimeout, String tableName, string columnList, string parameterList, IEnumerable<PropertyInfo> keyProperties, object entityToInsert);
        Task<int> InsertAsync(IDbConnection connection, IDbTransaction transaction, int? commandTimeout, String tableName, string columnList, string parameterList, IEnumerable<PropertyInfo> keyProperties, object entityToInsert);
    }

    public class SqlServerAdapter : ISqlAdapter
    {
        public int Insert(IDbConnection connection, IDbTransaction transaction, int? commandTimeout, String tableName, string columnList, string parameterList, IEnumerable<PropertyInfo> keyProperties, object entityToInsert)
        {
            string cmd = String.Format("insert into {0} ({1}) values ({2})", tableName, columnList, parameterList);

            connection.Execute(cmd, entityToInsert, transaction: transaction, commandTimeout: commandTimeout);

            //NOTE: would prefer to use IDENT_CURRENT('tablename') or IDENT_SCOPE but these are not available on SQLCE
            var r = connection.Query("select @@IDENTITY id", transaction: transaction, commandTimeout: commandTimeout);

            int id = (r.First().id != null ? (int)r.First().id : 0);
            ﻿if (keyProperties.Any())
                 keyProperties.First().SetValue(entityToInsert, id, null);
             return id;
        }
        public async Task<int> InsertAsync(IDbConnection connection, IDbTransaction transaction, int? commandTimeout, String tableName, string columnList, string parameterList, IEnumerable<PropertyInfo> keyProperties, object entityToInsert)
        {
            var idColumn = (keyProperties.FirstOrDefault()?.GetCustomAttribute(typeof(KeyAttribute)) as KeyAttribute)?.Name ?? keyProperties.FirstOrDefault()?.Name;

            string cmd;
            if (idColumn != null)
            {
                cmd = String.Format("insert into {0} ({1}) OUTPUT Inserted.{3} VALUES ({2}) ", tableName, columnList, parameterList, idColumn);
            }
            else
            {
                cmd = String.Format("insert into {0} ({1}) OUTPUT Inserted.* VALUES ({2}) ", tableName, columnList, parameterList, idColumn);

                return await connection.ExecuteAsync(cmd, entityToInsert, transaction: transaction, commandTimeout: commandTimeout);

            }
            var r = await connection.ExecuteScalarAsync<int>(cmd, entityToInsert, transaction: transaction, commandTimeout: commandTimeout);

           
            if (keyProperties.Any())
                keyProperties.First().SetValue(entityToInsert, r, null);
            return r;
        }
    }

    public class SqlBuilder
    {
        // pulled from https://github.com/StackExchange/dapper-dot-net/blob/master/Dapper.SqlBuilder/SqlBuilder.cs

        Dictionary<string, Clauses> data = new Dictionary<string, Clauses>();
        int seq;

        class Clause
        {
            public string Sql { get; set; }
            public object Parameters { get; set; }
            public bool IsInclusive { get; set; }
        }

        class Clauses : List<Clause>
        {
            string joiner;
            string prefix;
            string postfix;

            public Clauses(string joiner, string prefix = "", string postfix = "")
            {
                this.joiner = joiner;
                this.prefix = prefix;
                this.postfix = postfix;
            }

            public string ResolveClauses(DynamicParameters p)
            {
                foreach (var item in this)
                {
                    p.AddDynamicParams(item.Parameters);
                }
                return this.Any(a => a.IsInclusive)
                    ? prefix +
                      string.Join(joiner,
                          this.Where(a => !a.IsInclusive)
                              .Select(c => c.Sql)
                              .Union(new[]
                              {
                                  " ( " +
                                  string.Join(" OR ", this.Where(a => a.IsInclusive).Select(c => c.Sql).ToArray()) +
                                  " ) "
                              })) + postfix
                    : prefix + string.Join(joiner, this.Select(c => c.Sql)) + postfix;
            }

            public string ResolveClausesDapper(Dapper.DynamicParameters p)
            {
                foreach (var item in this)
                {
                    p.AddDynamicParams(item.Parameters);
                }
                return this.Any(a => a.IsInclusive)
                    ? prefix +
                      string.Join(joiner,
                          this.Where(a => !a.IsInclusive)
                              .Select(c => c.Sql)
                              .Union(new[]
                              {
                                  " ( " +
                                  string.Join(" OR ", this.Where(a => a.IsInclusive).Select(c => c.Sql).ToArray()) +
                                  " ) "
                              })) + postfix
                    : prefix + string.Join(joiner, this.Select(c => c.Sql)) + postfix;
            }

        }

        public class Template
        {
            readonly string sql;
            readonly SqlBuilder builder;
            readonly object initParams;
            int dataSeq = -1; // Unresolved
            bool dapperLibMode; 
            public Template(SqlBuilder builder, string sql, dynamic parameters)
            {
                this.initParams = parameters;
                this.sql = sql;
                this.builder = builder;
            }

            public Template(SqlBuilder builder, string sql, dynamic parameters, bool dapperLibMode)
            {
                this.initParams = parameters;
                this.sql = sql;
                this.builder = builder;
                this.dapperLibMode = dapperLibMode;
            }

            static System.Text.RegularExpressions.Regex regex =
                new System.Text.RegularExpressions.Regex(@"\/\*\*.+\*\*\/", System.Text.RegularExpressions.RegexOptions.Compiled | System.Text.RegularExpressions.RegexOptions.Multiline);

            void ResolveSql()
            {
                if (dapperLibMode)
                {
                    ResolveSqlDapper();
                    return;
                }

                if (dataSeq != builder.seq)
                {
                    DynamicParameters p = new DynamicParameters(initParams);

                    rawSql = sql;

                    foreach (var pair in builder.data)
                    {
                        rawSql = rawSql.Replace("/**" + pair.Key + "**/", pair.Value.ResolveClauses(p));
                    }
                    parameters = p;

                    // replace all that is left with empty
                    rawSql = regex.Replace(rawSql, "");

                    dataSeq = builder.seq;
                }
            }
            void ResolveSqlDapper()
            {
                if (dataSeq != builder.seq)
                {
                    if (!dapperLibMode)
                    {
                        throw new Exception("FATAL use of SqlMapperExtensions - DapperParameters can't be used if class wasn't initialized with useWithDapperLib set to true");
                    }

                    Dapper.DynamicParameters p = new Dapper.DynamicParameters(initParams);

                    rawSql = sql;

                    foreach (var pair in builder.data)
                    {
                        rawSql = rawSql.Replace("/**" + pair.Key + "**/", pair.Value.ResolveClausesDapper(p));
                    }
                    dapperParameters = p;

                    // replace all that is left with empty
                    rawSql = regex.Replace(rawSql, "");

                    dataSeq = builder.seq;
                }
            }

            string rawSql;
            object parameters;

            public string RawSql { get { ResolveSql(); return rawSql; } }
            public object Parameters { get { ResolveSql(); return parameters; } }

            private Dapper.DynamicParameters dapperParameters;
            public Dapper.DynamicParameters DapperParameters
            {
                get
                {
                    ResolveSqlDapper();

                    return dapperParameters;
                }
            }
        }


        public SqlBuilder()
        {
        }

        private bool useWithDapperLib = false;
        public SqlBuilder(bool useWithDapperLib)
        {
            this.useWithDapperLib = useWithDapperLib;
        }
        public Template AddTemplate(string sql, dynamic parameters = null)
        {
            return new Template(this, sql, parameters, useWithDapperLib);
        }

        void AddClause(string name, string sql, object parameters, string joiner, string prefix = "", string postfix = "", bool IsInclusive = false)
        {
            Clauses clauses;
            if (!data.TryGetValue(name, out clauses))
            {
                clauses = new Clauses(joiner, prefix, postfix);
                data[name] = clauses;
            }
            clauses.Add(new Clause { Sql = sql, Parameters = parameters });
            seq++;
        }

        public SqlBuilder Intersect(string sql, dynamic parameters = null)
        {
            AddClause("intersect", sql, parameters, joiner: "\nINTERSECT\n ", prefix: "\n ", postfix: "\n");
            return this;
        }

        public SqlBuilder InnerJoin(string sql, dynamic parameters = null)
        {
            AddClause("innerjoin", sql, parameters, joiner: "\nINNER JOIN ", prefix: "\nINNER JOIN ", postfix: "\n");
            return this;
        }

        public SqlBuilder LeftJoin(string sql, dynamic parameters = null)
        {
            AddClause("leftjoin", sql, parameters, joiner: "\nLEFT JOIN ", prefix: "\nLEFT JOIN ", postfix: "\n");
            return this;
        }

        public SqlBuilder RightJoin(string sql, dynamic parameters = null)
        {
            AddClause("rightjoin", sql, parameters, joiner: "\nRIGHT JOIN ", prefix: "\nRIGHT JOIN ", postfix: "\n");
            return this;
        }

        public SqlBuilder Where(string sql, dynamic parameters = null)
        {
            AddClause("where", sql, parameters, " AND ", prefix: "WHERE ", postfix: "\n");
            return this;
        }

        public SqlBuilder OrWhere(string sql, dynamic parameters = null)
        {
            AddClause("where", sql, parameters, " AND ", prefix: "WHERE ", postfix: "\n", IsInclusive: true);
            return this;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="sql"></param>
        /// <param name="parameters"></param>
        /// <param name="offset">How many rows to offset the query by (start at row 10000, for instance)</param>
        /// <param name="fetchNext">How many rows to return (page size), 50 for instance.</param>
        /// <returns></returns>
        public SqlBuilder OrderBy(string sql, dynamic parameters = null, int? offset = null, int? fetchNext = 0)
        {
            if (offset != null && fetchNext != null)
            {
                sql = sql + " OFFSET " + offset + " ROWS FETCH NEXT " + fetchNext + " ROWS ONLY";
            }

            AddClause("orderby", sql, parameters, " , ", prefix: "ORDER BY ", postfix: "\n");
            return this;
        }

        public SqlBuilder Select(string sql, dynamic parameters = null)
        {
            AddClause("select", sql, parameters, " , ", prefix: "", postfix: "\n");
            return this;
        }

        public SqlBuilder AddParameters(dynamic parameters)
        {
            AddClause("--parameters", "", parameters, "");
            return this;
        }

        public SqlBuilder Join(string sql, dynamic parameters = null)
        {
            AddClause("join", sql, parameters, joiner: "\nJOIN ", prefix: "\nJOIN ", postfix: "\n");
            return this;
        }

        public SqlBuilder GroupBy(string sql, dynamic parameters = null)
        {
            AddClause("groupby", sql, parameters, joiner: " , ", prefix: "\nGROUP BY ", postfix: "\n");
            return this;
        }

        public SqlBuilder Having(string sql, dynamic parameters = null)
        {
            AddClause("having", sql, parameters, joiner: "\nAND ", prefix: "HAVING ", postfix: "\n");
            return this;
        }
    }
}
