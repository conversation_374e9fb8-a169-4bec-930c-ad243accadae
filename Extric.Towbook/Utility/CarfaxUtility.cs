using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using System.Xml;
using Extric.Towbook.Vehicle;
using Newtonsoft.Json;
using NLog;

namespace Extric.Towbook.Utility
{
    public class CarfaxUtility
    {
        private static readonly Logger logger = LogManager.GetCurrentClassLogger();


        public class CarfaxVin
        {

            public int Year { get; set; }
            public string Make { get; set; }
            public string Model { get; set; }

            public int MakeId { get; set; }
            public int ModelId { get; set; }

            public string Vin { get; set; }

            public int Weight { get; set; }

            /// <summary>
            /// Sedan, Coupe, ...
            /// </summary>
            public string Body { get; set; }

            /// <summary>
            /// Drive type (FWD, AWD, RWD, etc)
            /// </summary>
            public string DriveType { get; set; }

            /// <summary>
            /// Fuel Type (Gas, Diesel, etc)
            /// </summary>
            public string Fuel { get; set; }

            /// <summary>
            /// Automatic, Manual (Acura TL returns a 6 SPD MANUAL  M 6, for instance)
            /// </summary>
            public string Transmission { get; set; }

            public bool Cached { get; set; }
        }


        private static string DataFromUrl(string url, string requestData)
        {
            int statusCode = 0;
            string msg = "";
            for (int retries = 0; retries < 3; retries++)
            {
                try
                {
                    HttpWebRequest request = WebRequest.Create(url) as HttpWebRequest;

                    if (requestData != null)
                    {
                        request.Method = "POST";

                        byte[] bytes = Encoding.UTF8.GetBytes(requestData.ToString());

                        request.ContentLength = bytes.Length;

                        using (Stream putStream = request.GetRequestStream())
                        {
                            putStream.Write(bytes, 0, bytes.Length);
                        }
                    }

                    using (HttpWebResponse response = request.GetResponse() as HttpWebResponse)
                    {
                        if (response.StatusCode != HttpStatusCode.OK)
                        {
                            statusCode = (int)response.StatusCode;
                            msg = response.StatusDescription;

                            throw new Exception(String.Format(
                            "Server error (HTTP {0}: {1}).",
                            response.StatusCode,
                            response.StatusDescription));
                        }

                        using (var s = response.GetResponseStream())
                        {
                            string xml = string.Empty;

                            using (StreamReader sr = new StreamReader(s, System.Text.Encoding.GetEncoding("utf-8")))
                            {
                                xml = sr.ReadToEnd();
                            }

                            return xml;
                        }
                    }
                }
                catch (WebException e)
                {
                    HttpWebResponse rsp = (HttpWebResponse)e.Response;

                    if (rsp != null)
                    {
                        try
                        {
                            using (StreamReader reader = new StreamReader(rsp.GetResponseStream()))
                            {
                                statusCode = (int)rsp.StatusCode;
                                msg = reader.ReadToEnd();
                            }
                        }
                        catch (Exception)
                        {
                            statusCode = (int)rsp.StatusCode;
                        }
                    }


                    System.Threading.Thread.Sleep(100);
                    // automatically retry 
                    continue;
                }
            }

            if (statusCode >= 400)
            {
                throw new TowbookException("Unable to translate license plate to VIN: " + msg + ", code: " + statusCode);
            }

            return null;
        }


        public static async Task<CarfaxVin> FromLicensePlateAsync(string plate, string state, string vin = "")
        {
            if (string.IsNullOrWhiteSpace(plate) && string.IsNullOrWhiteSpace(state) && string.IsNullOrWhiteSpace(vin))
                return null;

            plate = plate?.ToLowerInvariant();

            // don't waste a lookup on an invalid plate.
            if (plate == "none" || plate == "no tag" || plate == "no plates" || plate == "notag" || plate == "na" || state == "na" || vin == "unknown" || plate == "unknown")
                return null;

            // don't waste a lookup on an invalid vin
            if (!string.IsNullOrWhiteSpace(vin) && vin.Length != 17)
                return null;
            
            string cacheKey = $"cf_vin_ptv:{plate?.ToLowerInvariant()}_{state?.ToLowerInvariant()}_{vin?.ToLowerInvariant()}";

            var exists = await Core.GetRedisValueAsync(cacheKey);
            var expires = Core.GetRedisDatabase().KeyTimeToLive(cacheKey);
            if (expires == null)
            {
                Core.DeleteRedisKey(cacheKey);
                exists = null;
            }

            if (exists != null)
                return JsonConvert.DeserializeObject<CarfaxVin>(exists);

            CarfaxVin cfv = new CarfaxVin();

            const string requestDataFormat = @"<carfax-request> 
                <license-plate><![CDATA[{0}]]></license-plate>
                <state><![CDATA[{1}]]></state>
                <vin><![CDATA[{2}]]></vin>
                <product-data-id>0C487B52AAB3BA6D</product-data-id>
                <location-id>Carfax</location-id>
                </carfax-request>";

            string rawXml = DataFromUrl("https://quickvin.carfax.com/1", String.Format(requestDataFormat, plate, state, vin));

            if (rawXml == null)
                return null;

            using (var xml = XmlReader.Create(new StringReader(rawXml)))
            {
                xml.MoveToContent();

                xml.ReadToFollowing("vin-info");
                if (xml.EOF)
                {
                    Core.SetRedisValue(cacheKey, "null", TimeSpan.FromDays(1));
                    return null;
                }
                var reader = xml.ReadSubtree();

                while (!reader.EOF)
                {
                    if (reader.IsStartElement())
                    {
                        switch (reader.Name)
                        {
                            case "vin":
                                cfv.Vin = reader.ReadElementContentAsString();
                                continue;

                            case "carfax-vin-decode":
                                var r = reader.ReadSubtree();

                                while (!r.EOF)
                                {
                                    if (r.IsStartElement())
                                    {
                                        #region process the inner blocks (trim, etc)
                                        switch (r.Name)
                                        {
                                            case "trim":
                                                var rt = r.ReadSubtree();

                                                while (!rt.EOF)
                                                {
                                                    if (rt.IsStartElement())
                                                    {
                                                        string name = rt.Name;

                                                        switch (name)
                                                        {
                                                            case "aaia-legacy-make":
                                                                cfv.Make = rt.ReadElementContentAsString().Trim();
                                                                break;
                                                            case "base-make-name":
                                                                if (string.IsNullOrEmpty(cfv.Make))
                                                                    cfv.Make = rt.ReadElementContentAsString().Trim();
                                                                else
                                                                    rt.Read();
                                                                break;

                                                            case "base-year-model":
                                                                cfv.Year = rt.ReadElementContentAsInt();
                                                                break;

                                                            case "aaia-legacy-model":
                                                                cfv.Model = rt.ReadElementContentAsString().Trim();
                                                                break;

                                                            case "oem-base-model":
                                                                if (string.IsNullOrEmpty(cfv.Model))
                                                                    cfv.Model = rt.ReadElementContentAsString().Trim();
                                                                else
                                                                    rt.Read();
                                                                break;

                                                            case "oem-base-shipping-weight":
                                                                var tempWeight = rt.ReadElementContentAsString().Trim();
                                                                int weight = 0;
                                                                if (Int32.TryParse(tempWeight, out weight))
                                                                    cfv.Weight = weight;
                                                                break;

                                                            case "nonoem-fuel":
                                                                cfv.Fuel = rt.ReadElementContentAsString().Trim();
                                                                break;

                                                            case "nonoem-drive":
                                                                cfv.DriveType = rt.ReadElementContentAsString().Trim();
                                                                break;
                                                            case "nonoem-body":
                                                                cfv.Body = rt.ReadElementContentAsString().Trim();
                                                                break;
                                                            case "oem-transmission-type":
                                                                cfv.Transmission = rt.ReadElementContentAsString().Trim();
                                                                break;
                                                            default:
                                                                rt.Read();
                                                                break;
                                                        }
                                                    }
                                                    else
                                                    {
                                                        rt.Read();
                                                    }
                                                    continue;
                                                }
                                                break;
                                            default:
                                                r.Read();
                                                break;
                                        }
                                        #endregion
                                    }
                                    else
                                    {
                                        r.Read();
                                    }
                                }
                                break;
                        }
                        reader.Read();

                    }
                    else
                    {
                        reader.Read();
                    }
                }

                Manufacturer make = await Manufacturer.GetByNameAsync(cfv.Make);

                if (make != null && cfv.Model != null)
                {
                    cfv.Make = make.Name;
                    cfv.MakeId = make.Id;
                    var r = await Model.GetByNameAsync(cfv.Model, make.Id);

                    if (r != null)
                    {
                        cfv.Model = r.Name;
                        cfv.ModelId = r.Id;
                    }
                }

                Core.SetRedisValue(cacheKey, cfv.ToJson(), TimeSpan.FromDays(21));

                var lei = new LogEventInfo()
                {
                    Message = "Looked up " + (plate != null ? "Plate" : "VIN"),
                    Level = LogLevel.Info
                };
                if (plate != null)
                    lei.Properties.Add("plate", plate);
                if (state != null)
                    lei.Properties.Add("state", state.ToUpperInvariant());
                if (vin != null)
                    lei.Properties.Add("vin", vin?.ToUpperInvariant());

                lei.Properties.Add("result", cfv);

                logger.Log(lei);

                return cfv;
            }
        }
    }
}
