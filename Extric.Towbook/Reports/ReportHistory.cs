using System;
using System.Collections.ObjectModel;
using System.Threading.Tasks;
using Extric.Towbook.Utility;
using Microsoft.Azure.Cosmos;
using Newtonsoft.Json;

namespace Extric.Towbook.Reports
{
    public class ReportHistory
    {
        [Key]
        public string Id { get; set; }

        [PartitionKey]
        public int CompanyId { get; set; }
        public int UserId { get; set; }
        public DateTime CreateDate { get; set; }
        public ReportType Report { get; set; }
        public string IpAddress { get; set; }
        public string FilterString { get; set; }
        public ReportOptions ReportOptions { get; set; }
        public long Duration { get; set; }
        public int[] CompanyIds { get; set; }

        [Write(false)]
        public string ReportName => Report.ToString();
        [Write(false)]
        public string UserFullName => User.GetById(UserId)?.FullName;

        private const string CollectionName = "report-history";


        public static async Task<Collection<ReportHistory>> GetByDateRange(int[] companyIds, DateTime startDate, DateTime endDate)
        {
            return await CosmosDB.Get().QueryItemsAsync<ReportHistory>(CollectionName,
                new QueryDefinition("select * from c where array_contains(@companyIds, c.companyId, true) and c.createDate >= @startDate and c.createDate <= @endDate order by c.createDate desc")
                    .WithParameter("@companyIds", companyIds)
                    .WithParameter("@startDate", startDate)
                    .WithParameter("@endDate", endDate)
                );
        }

        public static async Task<Collection<ReportHistory>> GetByUserId(int[] companyIds, int userId, DateTime startDate, DateTime endDate)
        {

            return await CosmosDB.Get().QueryItemsAsync<ReportHistory>(CollectionName,
                new QueryDefinition("select * from c where array_contains(@companyIds, c.companyId, true) and c.createDate >= @startDate and c.createDate <= @endDate and c.userId = @userId order by c.createDate desc")
                    .WithParameter("@companyIds", companyIds)
                    .WithParameter("@startDate", startDate)
                    .WithParameter("@endDate", endDate)
                    .WithParameter("@userId", userId)
                );
        }

        public static void LogReport(int companyId, 
            int[] companyIds,
            int userId,
            ReportType report,
            string ipAddress,
            string filterString,
            ReportOptions reportOptions,
            long duration)
        {
            var historyItem = new ReportHistory
            {
                Id = Guid.NewGuid().ToString(),
                CompanyId = companyId,
                CompanyIds = companyIds,
                UserId = userId,
                CreateDate = DateTime.Now,
                Report = report,
                IpAddress = ipAddress,
                FilterString = filterString,
                ReportOptions = reportOptions,
                Duration = duration,
            };
            CosmosDB.Get().InsertItem(CollectionName, historyItem);
        }

        public static async Task LogReportAsync(int companyId,
            int[] companyIds,
            int userId,  
            ReportType report,
            string ipAddress,
            string filterString,
            ReportOptions reportOptions,
            long duration)
        {
            var historyItem = new ReportHistory
            {
                Id = Guid.NewGuid().ToString(),
                CompanyId = companyId,
                CompanyIds = companyIds,
                UserId = userId,
                CreateDate = DateTime.Now,
                Report = report,
                IpAddress = ipAddress,
                FilterString = filterString,
                ReportOptions = reportOptions,
                Duration = duration,
            };
            await CosmosDB.Get().InsertItemAsync(CollectionName, historyItem);
        }
    }
#nullable enable
    public sealed class ReportOptions
    {
        public bool Export { get; set; }
        public ExportType ExportType { get; set; }
        public int? OwnerUserId { get; set; }
        public DateTime? DateStart { get; set; }
        public DateTime? DateEnd { get; set; }
        public int? DispatchReasonId { get; set; }
        public int? AccountId { get; set; }
        public int? AccountType { get; set; }
        public int? MasterAccountId { get; set; }
        public int? AccountManagerUserId { get; set; }
        public int? DriverId { get; set; }
        public int? PushedToQuickbooks { get; set; }
        public int[]? AccountIds { get; set; }
        public int[]? DriverIds { get; set; }
        public int[]? CompanyId { get; set; }
        public int[]? PaymentTypeIds { get; set; }
        public int[]? AccountTypeIds { get; set; }
        public int? TruckId { get; set; }
        public int[]? TruckIds { get; set; }
        public int? UserType { get; set; }
        public int? Weight { get; set; }
        public int? RateItemId { get; set; }
        public int[]? RateItemIds { get; set; }
        public int? PaymentTypeId { get; set; }
        public string? PaymentStatus { get; set; }
        public DateGroupType? DateGroupType { get; set; }
        public GroupOption? GroupOption { get; set; }
        public int? ReportByType { get; set; }
        public int? TotalInvoiceFilter { get; set; }
        public int? AccountingMethod { get; set; }
        public Accounts.Account.AgedInvoice.ImpoundInclusion? Impounds { get; set; }
        public int? PaymentOptions { get; set; }
        public int? Payouts { get; set; }
        public int? InspectionResult { get; set; }
        public int? ForceRefresh { get; set; }
        public bool RefreshOnlyGraph { get; set; }
        public string? CustomExport { get; set; }
        public int? DateColumn { get; set; }
        public int? StickerReason { get; set; }
        public int? StickerStatus { get; set; }
        public string? Format { get; set; }
        public int? IsAudited { get; set; }
        public int? IsLocked { get; set; }
        public int? IsBilled { get; set; }
        public int? RecordedById { get; set; }
        public int? PaymentBy { get; set; }
        public int? VerifiedById { get; set; }
        public int? VerifiedStatus { get; set; }
        public int? PerformerUserId { get; set; }
        public int? TaxExemptStatus { get; set; }
        public int? ClassId { get; set; }

        // Payroll Report options 
        public bool? ShowMakeModel { get; set; }
        public bool? ShowReason { get; set; }
        public bool? ShowReason2 { get; set; }
        public bool? ShowInvoice { get; set; }
        public bool? ShowCustomField { get; set; }
        public bool? ShowBillToAccount { get; set; }
        public bool? ShowMiles { get; set; }
        public bool? ShowMilesUnloaded { get; set; }
        public bool? ShowCoverage { get; set; }
        public bool? ShowPO { get; set; }
        public bool? ShowDriverBase { get; set; }
        public bool? ShowCashBalance { get; set; }
        public bool? ShowWeightClass { get; set; }
        public bool? ShowReimbursements { get; set; }
        public bool? ShowPhotos { get; set; }
        public bool? AllCompanies { get; set; }
        public bool? ShowTakeFromPaycheck { get; set; }
        public bool? FlipCallAndPo { get; set; }
    }
#nullable disable

    public enum ExportType
    {
        None = 0,
        CSV = 1,
        PDF = 2,
    }
}