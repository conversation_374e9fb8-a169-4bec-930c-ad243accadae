using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Sockets;
using System.Text;
using System.Threading.Tasks;
using Extric.Towbook.Utility;
using ProtoBuf;
using Newtonsoft.Json;

namespace Extric.Towbook.ActivityLogging
{
    public class TrackableObject
    {
        [ProtoIgnore]
        [Write(false)]
        [JsonIgnore]
        public List<LoggedFieldChange> ChangedFields { get; set; }

        [ProtoIgnore]
        [Write(false)]
        [JsonIgnore]
        public bool IsDirty { get; private set; }

        public bool IsFieldDirty(string field)
        {
            if (ChangedFields == null)
                return false;

            return ChangedFields.Any(o => o.Field == field);
        }

        public void MarkAsDirty(bool value = true)
        {
            IsDirty = value;

            if (!value)
                ChangedFields = new List<LoggedFieldChange>();
        }

        /// <summary>
        /// Use this when you want to mark the object as clean again (call after saving the object to a data store, for instance)
        /// </summary>
        public void MarkAsClean()
        {
            MarkAsDirty(false);
        }


        /// <summary>
        /// Use this when you want to mark a field as clean again (call after initializing a property, for instance)
        /// </summary>
        public void MarkAsClean(string fieldName)
        {
            if (ChangedFields == null)
                return;

            ChangedFields = ChangedFields.Where(o => o.Field != fieldName).ToList();
            if (!ChangedFields.Any())
                IsDirty = false;
        }


        protected void SetField<T>(ref T field, T value, string propertyName)
        {
            if (EqualityComparer<T>.Default.Equals(field, value))
                return;

            if (ChangedFields == null)
                ChangedFields = new List<LoggedFieldChange>();

            var changedField = new LoggedFieldChange(propertyName, field != null ? field.ToString() : "null", value != null ? value.ToString() : "null");

            LoggedFieldChange original = ChangedFields.FirstOrDefault(o => o.Field == propertyName);

            if (original != null)
            {
                ChangedFields.Remove(original);
                changedField.OldValue = original.OldValue;

                if (changedField.NewValue == changedField.OldValue)
                {
                    field = value;

                    if (!ChangedFields.Any()) {
                        IsDirty = false;
                    }

                    // Console.WriteLine("Setting " + propertyName + " from=" + field + " to NewValue=" + value +
                    //    (x != null & x.OldValue != null ? "... initialValue before any changes was: " +
                    //    x.OldValue : "") + " so, we're removing this change log entry");

                    return;
                }
            }

            // Console.WriteLine("Setting " + propertyName + " from=" + field + " to NewValue=" + value + 
            //    (x != null & x.OldValue != null ? "... initialValue before any changes was: " + 
            //    x.OldValue : ""));

            // do not return the MinValue of a decimal...convert it to "0"
            if (changedField.OldValue == Decimal.MinValue.ToString())
                changedField.OldValue = "0";

            if (changedField.OldValue == DateTime.MinValue.ToString())
                changedField.OldValue = "null";

            // treat null and empty string as the same. 
            if (changedField.OldValue == "null" && value.ToString() == "")
                return;

            if (changedField.OldValue == "null" && (value is IEnumerable<object>) && !(value as IEnumerable<object>).Any())
            {
                // original is null, new value is empty.. treat it as the same.
                return;
            }
                
            if (!(changedField.OldValue == "0" && changedField.NewValue == "null") &&
                !(changedField.OldValue == "null" && changedField.NewValue == "0"))
            {
                ChangedFields.Remove(original);

                ChangedFields.Add(changedField);
            }

            field = value;
            IsDirty = true;
        }

        protected string GetOriginalValueOrNull(string field)
        {
            var r = this.ChangedFields.FirstOrDefault(o => o.Field == field);
            if (r != null)
                return r.OldValue;
            else
                return null;
        }

        public static string GetIp()
        {
            return Dns.GetHostEntry(Dns.GetHostName())
                .AddressList.FirstOrDefault(ip => ip.AddressFamily == AddressFamily.InterNetwork && 
                !ip.ToString().StartsWith("169.254")).ToString();
        }

        public void Save(AuthenticationToken token,
            ActivityLogType logType, 
            int? id,
            ActivityLogType? parentObjectTypeId,
            int? parentObjectId, 
            int actionId, string ipAddress)
        {
            ActivityLogItem ali = new ActivityLogItem(
                (token != null ? token.UserId : 0),
                (token != null ? token.ClientVersionId.GetValueOrDefault() : 0),
                logType,
                id,
                parentObjectId,
                parentObjectTypeId,
                actionId,
                (ipAddress != null ? ipAddress : GetIp()));

            // if actionId == 1(create) don't store change details.
            if (ChangedFields != null && ChangedFields.Count > 0)
            {
                ali.Details = new ActivityLogItemDetail()
                {
                    ActivityLogItemDetailTypeId = ActivityLogItemDetailType.LoggedFieldChange,
                    Data = ChangedFields.ToJson()
                };
            }
            
            ali.Save();

            //Console.WriteLine("root save >> " + ali.ToJson(true));
        }

        public async Task SaveAsync(AuthenticationToken token,
            ActivityLogType logType,
            int? id,
            ActivityLogType? parentObjectTypeId,
            int? parentObjectId,
            int actionId, string ipAddress)
        {
            ActivityLogItem ali = new ActivityLogItem(
                (token != null ? token.UserId : 0),
                (token != null ? token.ClientVersionId.GetValueOrDefault() : 0),
                logType,
                id,
                parentObjectId,
                parentObjectTypeId,
                actionId,
                (ipAddress != null ? ipAddress : GetIp()));

            // if actionId == 1(create) don't store change details.
            if (ChangedFields != null && ChangedFields.Count > 0)
            {
                ali.Details = new ActivityLogItemDetail()
                {
                    ActivityLogItemDetailTypeId = ActivityLogItemDetailType.LoggedFieldChange,
                    Data = ChangedFields.ToJson()
                };
            }

            await ali.SaveAsync();

            //Console.WriteLine("root save >> " + ali.ToJson(true));
        }
    }
}
