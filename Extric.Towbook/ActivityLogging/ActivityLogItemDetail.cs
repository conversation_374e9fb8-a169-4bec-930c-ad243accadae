using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Extric.Towbook.Utility;
using Newtonsoft.Json;

namespace Extric.Towbook.ActivityLogging
{
    public class ActivityLogItemDetail
    {
        [Key]
        public long ActivityLogItemDetailId { get; set; }
        public long ActivityLogItemId { get; set; }

        /// <summary>
        /// What type of data is it (LoggedFieldChange, PlainText)
        /// </summary>
        public ActivityLogItemDetailType ActivityLogItemDetailTypeId { get; set; }
        [JsonIgnore]
        public String Data { get; set; }

        [Write(false)]
        [JsonProperty("data")]
        public object DataX
        {
            get
            {
                if (Data == null)
                    return null;
                try
                {
                    if (Data.StartsWith("["))
                        return JsonConvert.DeserializeObject<LoggedFieldChange[]>(Data);
                    else
                        return JsonConvert.DeserializeObject<object>(Data);
                }
                catch
                {
                    return null;
                }
                
            }
        }


        public void Save()
        {
            if (Data == "[]")
                return;

            // Limit data change to 7500 characters. 
            if (Data.Length > 7500)
            {
                // if JSON, complete the object to avoid parse error
                if (Data.StartsWith("[{"))
                {
                    Data = Data.Substring(0, 7496);
                    Data += "\"}]";
                }
                else
                    Data = Data.Substring(0, 7500);
            }

            SqlMapper.Insert(this);
        }

        public override string ToString()
        {
            return Data;
        }

        internal static IEnumerable<ActivityLogItemDetail> GetByIds(IEnumerable<long> ids)
        {
            var ret = new List<ActivityLogItemDetail>();
            
            foreach (var list in ids.Batch(500))
            {
                ret.AddRange(SqlMapper.Query<ActivityLogItemDetail>(
                    "SELECT * FROM ActivityLogItemDetails WITH (NOLOCK) WHERE ActivityLogItemId IN @a",
                    new { a = list }));
            }

            return ret;

        }
    }

    public enum ActivityLogItemDetailType : short
    {
        LoggedFieldChange = 1,
        String = 2,
        Date = 3,
        RawJson = 4
    }
}
