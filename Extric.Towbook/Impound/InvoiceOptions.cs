using Extric.Towbook.Utility;
using System;
using System.Data;
using System.Data.SqlClient;
using System.Threading.Tasks;

namespace Extric.Towbook.Impounds
{
    [Table("ImpoundInvoiceOptions")]
    public class InvoiceOptions
    {
        [Key]
        public int ImpoundInvoiceOptionsId { get; set; }
        public int CompanyId { get; set; }
        public int? AccountId { get; set; }
        public bool ShowCallNumber { get; set; } = true;
        public bool ShowInvoiceNumber { get; set; }
        public bool ShowWindowEnvelope { get; set; }
        public bool ShowTruck { get; set; }
        public bool ShowDriver { get; set; }
        public bool ShowNotes { get; set; }
        public bool ShowCreateDate { get; set; }
        public bool ShowArrivalTime { get; set; }
        public bool ShowEnrouteTime { get; set; }
        public bool ShowCompletionTime { get; set; }
        public bool ShowDestinationArrivalTime { get; set; }
        public bool ShowDispatchTime { get; set; }
        public bool HideReferenceNumber { get; set; }
        public bool HideTowSource { get; set; }
        public bool HideTowDestination { get; set; }
        public bool HidePrintDate { get; set; }
        public bool HideReason { get; set; }
        public bool HideDrivable { get; set; }
        public bool HideDriverLicense { get; set; }
        public bool HideKeys { get; set; }
        public bool HideStockNumber { get; set; }
        public bool HideImpoundDate { get; set; }
        public bool HideAccountContact { get; set; }
        ///<summary> Duplicate of ShowCompletionTime. Needed for inserts to work with current table but use ShowCompletion Time instead.</summary>
        public bool HideCompletionTime { get; private set; }
        public bool ShowPoliceHold { get; set; }
        public bool ShowReleaseNotes { get; set; }
        public bool ShowTowingTime { get; set; }
        public bool ShowCallContact { get; set; }
        public bool IncludeDriverSignature { get; set; }
        public bool ShowDispatcher { get; set; }
        public bool HideRateItemCategoryNames { get; set; }
        public bool ShowCompanyEmail { get; set; }

        public InvoiceOptions()
        {
        }

        public InvoiceOptions(IDataReader reader)
        {
            InitializeFromDataReader(reader);
        }

        public static InvoiceOptions GetDefaultOptions(int companyId)
        {
            var defaultOptions = new InvoiceOptions();

            defaultOptions.CompanyId = companyId;

            return defaultOptions;
        }

        private void InitializeFromDataReader(IDataReader reader)
        {
            ImpoundInvoiceOptionsId = reader.GetValue<int>("ImpoundInvoiceOptionsId");
            CompanyId = reader.GetValue<int>("CompanyId");
            AccountId = reader.GetValueOrNull<int>("AccountId");
            ShowCallNumber = reader.GetValue<bool>("ShowCallNumber");
            ShowInvoiceNumber = reader.GetValue<bool>("ShowInvoiceNumber");
            ShowWindowEnvelope = reader.GetValue<bool>("ShowWindowEnvelope");
            ShowTruck = reader.GetValue<bool>("ShowTruck");
            ShowDriver = reader.GetValue<bool>("ShowDriver");
            ShowDispatcher = reader.GetValue<bool>("ShowDispatcher");
            ShowNotes = reader.GetValue<bool>("ShowNotes");
            ShowCreateDate = reader.GetValue<bool>("ShowCreateDate");
            ShowArrivalTime = reader.GetValue<bool>("ShowArrivalTime");
            ShowEnrouteTime = reader.GetValue<bool>("ShowEnrouteTime");
            ShowCompletionTime = reader.GetValue<bool>("ShowCompletionTime");
            ShowDispatchTime = reader.GetValue<bool>("ShowDispatchTime");
            ShowDestinationArrivalTime = reader.GetValue<bool>("ShowDestinationArrivalTime");
            HideReferenceNumber = reader.GetValue<bool>("HideReferenceNumber");
            HideTowSource = reader.GetValue<bool>("HideTowSource");
            HideTowDestination = reader.GetValue<bool>("HideTowDestination");
            HidePrintDate = reader.GetValue<bool>("HidePrintDate");
            HideReason = reader.GetValue<bool>("HideReason");
            HideDrivable = reader.GetValue<bool>("HideDrivable");
            HideDriverLicense = reader.GetValue<bool>("HideDriverLicense");
            HideKeys = reader.GetValue<bool>("HideKeys");
            HideStockNumber = reader.GetValue<bool>("HideStockNumber");
            HideImpoundDate = reader.GetValue<bool>("HideImpoundDate");
            HideAccountContact = reader.GetValue<bool>("HideAccountContact");
            HideCompletionTime = !ShowCompletionTime;
            ShowPoliceHold = reader.GetValue<bool>("ShowPoliceHold");
            ShowReleaseNotes = reader.GetValue<bool>("ShowReleaseNotes");
            ShowTowingTime = reader.GetValue<bool>("ShowTowingTime");
            ShowCallContact = reader.GetValue<bool>("ShowCallContact");
            IncludeDriverSignature = reader.GetValue<bool>("IncludeDriverSignature");
            HideRateItemCategoryNames = reader.GetValue<bool>("HideRateItemCategoryNames");
            ShowCompanyEmail = reader.GetValue<bool>("ShowCompanyEmail");
        }

        public void Save()
        {
            if (ImpoundInvoiceOptionsId == 0)
            {
                HideCompletionTime = !ShowCompletionTime;

                ImpoundInvoiceOptionsId = Convert.ToInt32(SqlMapper.Insert(this));
            }
            else
            {
                SqlMapper.Update(this);
            }
        }

        public static InvoiceOptions GetByCompanyId(int companyId)
        {
            using (SqlDataReader reader = SqlHelper.ExecuteReader(Core.ConnectionString,
                    CommandType.StoredProcedure, "ImpoundInvoiceOptionsGetByCompanyId",
                    new SqlParameter("@CompanyId", companyId)))
            {
                if (reader.Read())
                {
                    return new InvoiceOptions(reader);
                }
                else
                {
                    return GetDefaultOptions(companyId);
                }
            }
        }

        public static async Task<InvoiceOptions> GetByCompanyIdAsync(int companyId)
        {
            using (var reader = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString,
                    CommandType.StoredProcedure, "ImpoundInvoiceOptionsGetByCompanyId",
                    new SqlParameter("@CompanyId", companyId)))
            {
                if (await reader.ReadAsync())
                {
                    return new InvoiceOptions(reader);
                }
                else
                {
                    return GetDefaultOptions(companyId);
                }
            }
        }
    }
}
