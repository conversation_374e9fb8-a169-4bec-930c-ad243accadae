using System;
using System.Collections.Generic;
using System.Text;
using System.Data.SqlClient;
using Glav.CacheAdapter.Core.DependencyInjection;
using ProtoBuf;
using System.Linq;
using Extric.Towbook.Company;
using Async = System.Threading.Tasks;
using System.Threading.Tasks;

namespace Extric.Towbook.Impounds
{
    [Serializable]
    [ProtoContract]
    public class Lot
    {
        private const int CacheTimeout = 1440;

        [ProtoMember(1)]
        private int _id = -1;
        [ProtoMember(2)]
        private int _companyId;
        [ProtoMember(3)]
        private string _name;
        [ProtoMember(4)]
        private string _contactPerson;
        [ProtoMember(5)]
        private string _phone;
        [ProtoMember(6)]
        private string _address;
        [ProtoMember(7)]
        private string _city;
        [ProtoMember(8)]
        private string _state;
        [ProtoMember(9)]
        private string _zip;

        [ProtoMember(10)]
        private string _email;

        [ProtoMember(11)]
        private Nullable<int> _capacity;
        [ProtoMember(12)]
        private string _notes;
        [ProtoMember(13)]
        private DateTime _createDate;
        [ProtoMember(14)]
        private bool _deleted;

        [ProtoMember(15)]
        private int _defaultTaxRateId;


        /// <summary>
        /// Create a new Impound Lot
        /// </summary>
        public Lot()
        {
            _id = -1;
            AccountId = 1; // owned by Company. 1 should be treated as NULL.
        }

        protected Lot(SqlDataReader reader)
        {
            InitializeFromDataReader(reader);
            AppServices.Cache.Add("lot:" + Id, TimeSpan.FromMinutes(CacheTimeout), this);
        }

        /// <summary>
        /// Initialize an existing object
        /// </summary>
        /// <param name="id">Id of the company</param>
        [Obsolete("Prefer using GetById method instead")]
        public Lot(int id, int companyId)
        {
            using (SqlDataReader dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                "ImpoundLotsGetById", new SqlParameter("@Id", id),
                    new SqlParameter("@CompanyId", companyId)))
            {
                if (dr.Read())
                {
                    InitializeFromDataReader(dr);
                }
                else
                {
                    throw new ApplicationException("Impound Lot doesn't exist!");
                }
            }
        }

        public override string ToString()
        {
            if (!String.IsNullOrEmpty(_name))
            {
                return _name;
            }
            else
            {
                return String.Format("Extric.Towbook.Impound.Lot [Name = {0}, Id = {1}, CreateDate = {2}]", _name, _id, _createDate);
            }
        }

        private void InitializeFromDataReader(SqlDataReader reader)
        {
            _id = Convert.ToInt32(reader["ImpoundLotId"]);
            _companyId = Convert.ToInt32(reader["CompanyId"]);

            _name = reader.GetValue<string>("Name");

            _address = reader.GetValue<string>("Address");
            _city = reader.GetValue<string>("City");
            _state = reader.GetValue<string>("State");
            _zip = reader.GetValue<string>("Zip");

            _phone = reader.GetValue<string>("Phone");

            _contactPerson = reader.GetValue<string>("ContactPerson");
            _email = reader.GetValue<string>("Email");
            _notes = reader.GetValue<string>("Notes");
            _capacity = reader.GetValue<int>("Capacity");
            _deleted = reader.GetValue<bool>("Deleted");
            _createDate = reader.GetValue<DateTime>("CreateDate");

            AccountId = reader.GetValue<int>("AccountId");
            DefaultTaxRateId = reader.GetValue<int>("DefaultTaxRateId");

            Latitude = reader.GetValue<decimal>("Latitude");
            Longitude = reader.GetValue<decimal>("Longitude");

            try
            {
                this.Companies = (reader.GetValue<string>("Companies") ?? "").Split(new string[] { "," }, StringSplitOptions.RemoveEmptyEntries)
                    .Select(o => Convert.ToInt32(o))
                    .Union(new[] { this.CompanyId }).ToArray();
            }
            catch { }

        }

        //If you change this method, don't forget to change the async version as well
        public static Lot GetById(int companyId, int lotId)
        {
            if (companyId == 0 || lotId == 0)
                return null;

            return AppServices.Cache.Get<Lot>("lot:" + lotId, TimeSpan.FromMinutes(CacheTimeout), () =>
            {
                using (SqlDataReader dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                    "ImpoundLotsGetById", new SqlParameter("@Id", lotId),
                        new SqlParameter("@CompanyId", companyId)))
                {
                    if (dr.Read())
                    {
                        return new Lot(dr);
                    }
                    else
                        return null;
                }
            });
        }

        public static async Task<Lot> GetByIdAsync(int companyId, int lotId)
        {
            if (companyId == 0 || lotId == 0)
                return null;

            return await AppServices.Cache.GetAsync<Lot>("lot:" + lotId, TimeSpan.FromMinutes(CacheTimeout), async () =>
            {
                using (SqlDataReader dr = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString,
                    "ImpoundLotsGetById", new SqlParameter("@Id", lotId),
                        new SqlParameter("@CompanyId", companyId)))
                {
                    if (await dr.ReadAsync())
                    {
                        return new Lot(dr);
                    }
                    else
                        return null;
                }
            });
        }

        //If you change this method, don't forget to change the async version as well
        public static List<Lot> GetByCompany(Company.Company company)
        {
            List<Lot> lots = new List<Lot>();

            using (SqlDataReader dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                "ImpoundLotsGetByCompanyId", company.Id, false))
            {
                while (dr.Read())
                {
                    lots.Add(new Lot(dr));
                }
            }

            return lots;
        }

        /// <summary>
        /// Returns a list of all active impound lots (doesn't return deleted ones); doesn't return account lots, only company lots.
        /// </summary>
        public static async Task<List<Lot>> GetByCompanyAsync(Company.Company company)
        {
            List<Lot> lots = new List<Lot>();

            using (SqlDataReader dr = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString,
                "ImpoundLotsGetByCompanyId", company.Id, false))
            {
                while (await dr.ReadAsync())
                {
                    lots.Add(new Lot(dr));
                }
            }

            return lots;
        }

        /// <summary>
        /// Retrieve a list of Impound/Storage Lots for companies specified.
        /// </summary>
        /// <param name="companies"></param>
        /// <param name="includeAccounts"></param>
        /// <returns></returns>
        public static List<Lot> GetByCompany(Company.Company[] companies, bool includeAccounts)
        {
            var r = new List<Lot>();

            foreach (var c in companies)
            {
                r = r.Union(GetByCompany(c, includeAccounts), new LotComparerOnIdOnly()).ToList();
            }

            return r;
        }

        private class LotComparerOnIdOnly : IEqualityComparer<Lot>
        {
            public int Compare(Lot x, Lot y)
            {
                return x.Id.CompareTo(y.Id);
            }

            public bool Equals(Lot x, Lot y)
            {
                return x.Id == y.Id;
            }

            public int GetHashCode(Lot obj)
            {
                return obj.Id.GetHashCode();
            }
        }


        /// <summary>
        /// Returns a list of all active user accounts (doesn't return deleted ones)
        /// </summary>
        public static List<Lot> GetByCompany(Company.Company company, bool includeAccounts)
        {
            if (!includeAccounts)
                return GetByCompany(company);

            return AppServices.Cache.Get("lot_c:" + company.Id, TimeSpan.FromMinutes(CacheTimeout), () =>
            {
                List<Lot> lots = new List<Lot>();

                using (SqlDataReader dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                    "ImpoundLotsGetByCompanyId", company.Id, true))
                {
                    while (dr.Read())
                    {
                        lots.Add(new Lot(dr));
                    }
                }

                return new CacheCollection<Lot>(lots);
            }).ItemsOrEmpty().ToList();

        }

        //If you change this method, don't forget to change the async version as well
        public static List<Lot> GetByCompany(Company.Company company,
            Accounts.Account account)
        {
            return AppServices.Cache.Get("lot_ca:" + company.Id + ", " + account.Id, TimeSpan.FromMinutes(CacheTimeout), () =>
            {
                List<Lot> lots = new List<Lot>();

                using (SqlDataReader dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                    "ImpoundLotsGetByAccountId",
                    company.Id,
                    account.Id))
                {
                    while (dr.Read())
                    {
                        lots.Add(new Lot(dr));
                    }
                }

                return new CacheCollection<Lot>(lots);

            }).ItemsOrEmpty().ToList();
        }

        /// <summary>
        /// Returns a list of all active user accounts (doesn't return deleted ones)
        /// </summary>
        public static async Task<List<Lot>> GetByCompanyAsync(Company.Company company,
            Accounts.Account account)
        {
            return (await AppServices.Cache.GetAsync("lot_ca:" + company.Id + ", " + account.Id, TimeSpan.FromMinutes(CacheTimeout), async () =>
            {
                List<Lot> lots = new List<Lot>();

                using (SqlDataReader dr = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString,
                    "ImpoundLotsGetByAccountId",
                    company.Id,
                    account.Id))
                {
                    while (await dr.ReadAsync())
                    {
                        lots.Add(new Lot(dr));
                    }
                }

                return new CacheCollection<Lot>(lots);

            })).ItemsOrEmpty().ToList();
        }

        /// <summary>
        /// Saves the Driver object to the data store.
        /// </summary>
        public async Async.Task Save()
        {
            if (_id == 0)
            {
                throw new ApplicationException("No such Truck. Can't save " +
                    "object! (this object should have already been discarded!)");
            }

            if (_id == -1)
            {
                DbInsert();
            }
            else
            {
                DbUpdate();
            }

            AppServices.Cache.InvalidateCacheItem("lot:" + Id);
            AppServices.Cache.InvalidateCacheItem("lot_c:" + _companyId); // list of lots 

            if (AccountId != null)
                AppServices.Cache.InvalidateCacheItem("lot_ca:" + _companyId + ", " + AccountId);

            await Caching.CacheWorkerUtility.UpdateImpoundLot(this);
        }

        public async Async.Task Delete()
        {
            SqlHelper.ExecuteScalar(Core.ConnectionString,
                "ImpoundLotsDeleteById",
                new SqlParameter("@ImpoundLotId", _id));

            AppServices.Cache.InvalidateCacheItem("lot:" + Id);
            await Caching.CacheWorkerUtility.DeleteImpoundLot(this);
        }

        /// <summary>
        /// It returns an address that can be resolved by a maps engine like google maps or TomTom
        /// the result will have a format like this: <address>, <city>, <state>, <zip>?, <Country>?
        /// 
        /// If you have a company object loaded before try pass it as parameter to avoid make an additional query
        /// </summary>
        /// <returns></returns>
        public string GetComposedAddress(Company.Company company = null)
        {
            var format = "{0}";

            if (company == null)
                company = Company.Company.GetById(CompanyId);

            var addr = Address;
            if (string.IsNullOrEmpty(Address))
                addr = Name;  // default to lot name if no address

            if (!string.IsNullOrEmpty(addr))
            {
                if (!string.IsNullOrEmpty(City) || !string.IsNullOrEmpty(State) || !string.IsNullOrEmpty(Zip))
                    format += ", ";
            }

            if (!string.IsNullOrEmpty(City))
                format += "{1}, ";

            if (!string.IsNullOrEmpty(State))
                format += "{2}";

            if (!string.IsNullOrEmpty(Zip))
                format += " {3}";

            if (company.Country != Company.Company.CompanyCountry.USA && company != null)
                format += ", {4}";

            return String.Format(format, addr, City, State, Zip, company?.CountryFullName);
        }

        public async Task<string> GetComposedAddressAsync(Company.Company company = null)
        {
            var format = "{0}";

            if (company == null)
                company = await Company.Company.GetByIdAsync(CompanyId);

            var addr = Address;
            if (string.IsNullOrEmpty(Address))
                addr = Name;  // default to lot name if no address

            if (!string.IsNullOrEmpty(addr))
            {
                if (!string.IsNullOrEmpty(City) || !string.IsNullOrEmpty(State) || !string.IsNullOrEmpty(Zip))
                    format += ", ";
            }

            if (!string.IsNullOrEmpty(City))
                format += "{1}, ";

            if (!string.IsNullOrEmpty(State))
                format += "{2}";

            if (!string.IsNullOrEmpty(Zip))
                format += " {3}";

            if (company.Country != Company.Company.CompanyCountry.USA && company != null)
                format += ", {4}";

            return String.Format(format, addr, City, State, Zip, company?.CountryFullName);
        }

        [Obsolete("Use GetComposedAddress(company) on the Impound itself")]
        public static string GetComposedAddress(Lot lot)
        {
            return lot.GetComposedAddress(null);
        }

        private void DbInsert()
        {
            _id = Convert.ToInt32(SqlHelper.ExecuteScalar(Core.ConnectionString,
                "ImpoundLotsInsert",
                new SqlParameter("@CompanyId", _companyId),
                new SqlParameter("@Accountid", AccountId),
                new SqlParameter("@Name", _name),
                new SqlParameter("@ContactPerson", _contactPerson),
                new SqlParameter("@Address", _address),
                new SqlParameter("@City", _city),
                new SqlParameter("@State", _state),
                new SqlParameter("@Zip", _zip),
                new SqlParameter("@Phone", _phone),
                new SqlParameter("@Email", _email),
                new SqlParameter("@Capacity", _capacity),
                new SqlParameter("@Notes", _notes),
                new SqlParameter("@DefaultTaxRateId", _defaultTaxRateId),
                new SqlParameter("@Latitude", Latitude),
                new SqlParameter("@Longitude", Longitude)));
        }

        private void DbUpdate()
        {
            SqlHelper.ExecuteNonQuery(Core.ConnectionString,
                "ImpoundLotsUpdateById",
                new SqlParameter("@ImpoundLotId", _id),
                new SqlParameter("@CompanyId", _companyId),
                new SqlParameter("@Accountid", AccountId),
                new SqlParameter("@Name", _name),
                new SqlParameter("@ContactPerson", _contactPerson),
                new SqlParameter("@Address", _address),
                new SqlParameter("@City", _city),
                new SqlParameter("@State", _state),
                new SqlParameter("@Zip", _zip),
                new SqlParameter("@Phone", _phone),
                new SqlParameter("@Email", _email),
                new SqlParameter("@Capacity", _capacity),
                new SqlParameter("@Notes", _notes),
                new SqlParameter("@DefaultTaxRateId", _defaultTaxRateId),
                new SqlParameter("@Latitude", Latitude),
                new SqlParameter("@Longitude", Longitude));
        }


        #region Properties

        public int Id
        {
            get { return _id; }
        }

        public int CompanyId
        {
            get { return _companyId; }
            set { _companyId = value; }
        }

        public int? AccountId { get; set; }

        public string Name
        {
            get { return _name; }
            set { _name = value; }
        }

        public string Address
        {
            get { return _address; }
            set { _address = value; }
        }

        public string City
        {
            get { return _city; } 
            set { _city = value; }
        }

        public string State
        {
            get { return _state; }
            set { _state = value; }
        }

        public string Zip
        {
            get { return _zip; }
            set { _zip = value; }
        }

        public string Phone
        {
            get { return _phone; }
            set { _phone = value; }
        }

        public int? Capacity
        {
            get { return _capacity; }
            set { _capacity = value; }
        }

        public string Email
        {
            get { return _email; }
            set { _email = value; }
        }

        public string Notes
        {
            get
            {
                return _notes;
            }
            set
            {
                _notes = value;
            }
        }

        public string ContactPerson
        {
            get { return _contactPerson; }
            set { _contactPerson = value; }
        }

        public int DefaultTaxRateId
        {
            get { return _defaultTaxRateId; }
            set { _defaultTaxRateId = value; }
        }

        public DateTime CreateDate
        {
            get { return _createDate; }
        }

        public bool Deleted
        {
            get { return _deleted; }
        }

        public int[] Companies { get; private set; }

        public decimal Latitude { get; set; }
        public decimal Longitude { get; set; }

        #endregion


    }
}
