using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Extric.Towbook.Utility;
using Glav.CacheAdapter.Core.DependencyInjection;

namespace Extric.Towbook
{
    public class ExternalLocationHistoryItem : ILocationHistoryItem
    {
        private const string cacheFormat = "e-lhi-{0}-{1}";
        private const int CacheTimeout = 1440;

        public int Id { get; set; }
        public int CompanyId { get; set; }
        public string DeviceId { get; set; }
        public decimal Latitude { get; set; }
        public decimal Longitude { get; set; }
        public DateTime Timestamp { get; set; }


        public static ExternalLocationHistoryItem GetById(int id)
        {
            return Map(SqlMapper.QuerySP<dynamic>("dbo.ExternalLocationHistoryItemsGetById",
                new
                {
                    @ExternalLocationHistoryItemId = id
                })).FirstOrDefault();
        }

        /// <summary>
        /// Get recent location history items
        /// </summary>
        /// <param name="UserId"></param>
        /// <returns></returns>
        public static IEnumerable<ExternalLocationHistoryItem> GetByDeviceId(int companyId, string deviceId)
        {
            return AppServices.Cache.Get<CacheCollection<ExternalLocationHistoryItem>>(String.Format(cacheFormat, companyId, deviceId), TimeSpan.FromDays(30), () =>
            {
                return new CacheCollection<ExternalLocationHistoryItem>(Map(SqlMapper.QuerySP<dynamic>("dbo.ExternalLocationHistoryItemsGetByDeviceId", new
                {
                    @UserId = companyId,
                    @DeviceId = deviceId
                })));
            }).Items;
        }

        /// <summary>
        /// Save the location history item to the database
        /// </summary>
        public void Save()
        {
            if (this.Timestamp == DateTime.MinValue)
                this.Timestamp = DateTime.Now;

            this.Id = Convert.ToInt32(SqlMapper.QuerySP<dynamic>("dbo.ExternalLocationHistoryItemsInsert",
                new
                {
                    CompanyId = this.CompanyId,
                    DeviceId = this.DeviceId,
                    Latitude = this.Latitude,
                    Longitude = this.Longitude,
                    Timestamp = this.Timestamp
                }, null, true, 2).FirstOrDefault().Id);

            InvalidateCache(this.CompanyId, this.DeviceId);
        }

        public void Delete()
        {
            SqlMapper.ExecuteSP("dbo.ExternalLocationHistoryItemsDeleteById",
                new { @DispatchExternalLocationHistoryItemId = this.Id });
        }

        public static IEnumerable<ExternalLocationHistoryItem> Map(IEnumerable<dynamic> list)
        {
            return list.Select(o => new ExternalLocationHistoryItem()
            {
                Id = o.ExternalLocationHistoryItemId,
                CompanyId = o.CompanyId,
                Latitude = o.Latitude,
                Longitude = o.Longitude,
                Timestamp = o.Timestamp
            }).ToCollection();
        }

        public static void InvalidateCache(int companyId, string deviceId)
        {
            AppServices.Cache.InvalidateCacheItem(String.Format(cacheFormat, companyId, deviceId));
        }

        public static IEnumerable<ExternalLocationHistoryItem> GetRecentByCompanies(Company.Company[] companies)
        {
            List<ExternalLocationHistoryItem> list = new List<ExternalLocationHistoryItem>();
            foreach (var c in companies)
            {
                foreach (ExternalLocationHistoryItem group in GetRecentByCompanyId(c.Id))
                {
                    list.Add(group);
                }
            }
            return list;
        }

        public static IEnumerable<ExternalLocationHistoryItem> GetRecentByCompanyId(int companyId)
        {
            return Map(SqlMapper.QuerySP<dynamic>("dbo.ExternalLocationHistoryItemsGetRecentByCompanyId", new
            {
                @CompanyId = companyId
            }));
        }
    }
}
