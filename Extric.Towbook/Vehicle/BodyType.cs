using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Text;
using System.Data.SqlClient;
using Glav.CacheAdapter.Core.DependencyInjection;
using ProtoBuf;
using System.Collections.Concurrent;
using System.Threading.Tasks;

namespace Extric.Towbook.Vehicle
{
    [ProtoContract]
    internal class InternalBodyTypeCollection
    {
        [ProtoMember(1)]
        public Collection<BodyType> BodyTypes { get; set; }

        public InternalBodyTypeCollection()
        {
            BodyTypes = new Collection<BodyType>();
        }

    }

	/// <summary>
	/// What type of body the vehicle has (2-door car, 4-door car, pickup, etc)
	/// </summary>
    [Serializable]
    [ProtoContract(ImplicitFields = ImplicitFields.AllFields, Name = "BodyType")]
    public class BodyType
    {
        private const int CacheTimeout = 1440;

        private int _id;
        private string _name;
        private int? _companyId;

        public BodyType()
        {
            _id = -1;
        }

        [Obsolete("Prefer using GetById method instead")]
        public BodyType(int id)
        {
            using (SqlDataReader dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                "VehicleBodyTypesGetById", new SqlParameter("@Id", id)))
            {
                if (dr.Read())
                    InitializeFromDataReader(dr);
                else
                    throw new Extric.Towbook.TowbookException("BodyType doesn't exist!");
            }
        }

        protected BodyType(SqlDataReader reader)
        {
            InitializeFromDataReader(reader);
            AppServices.Cache.Add("bt:" + Id, TimeSpan.FromMinutes(CacheTimeout), this);
        }

        public override string ToString()
        {
            return _name;
        }

        private void InitializeFromDataReader(SqlDataReader reader)
        {
            _id = reader.GetValue<int>("BodyTypeId");
            _name = reader.GetValue<string>("Name");
            _companyId = reader.GetValueOrNull<int>("CompanyId");
        }

        private static ConcurrentDictionary<int, BodyType> _cache = new ConcurrentDictionary<int, BodyType>();

        public static BodyType GetById(int id)
        {
            if (id == 0)
                return null;

            if (_cache.TryGetValue(id, out BodyType result))
                return result;

            using (var dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                "VehicleBodyTypesGetById", new SqlParameter("@Id", id)))
            {
                if (dr.Read())
                {
                    var r = new BodyType(dr);

                    _cache.TryAdd(id, r);

                    return r;
                }
                else
                {
                    return null;
                }
            }
        }

        public static async Task<BodyType> GetByIdAsync(int id)
        {
            if (id == 0)
                return null;

            if (_cache.TryGetValue(id, out BodyType result))
                return result;

            using (var dr = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString,
                "VehicleBodyTypesGetById", new SqlParameter("@Id", id)))
            {
                if (await dr.ReadAsync())
                {
                    var r = new BodyType(dr);

                    _cache.TryAdd(id, r);

                    return r;
                }
                else
                {
                    return null;
                }
            }
        }

        /// <summary>
        /// Gets a BodyType by its ID, for the specified company. If you don't specify the company, it will use the default name and ignore
        /// any possible aliased name.
        /// </summary>
        /// <param name="id"></param>
        /// <param name="companyId"></param>
        /// <returns></returns>
        public static BodyType GetById(int id, int companyId)
        {
            if (id == 0)
                return null;

            return AppServices.Cache.Get<BodyType>("bt:" + id + "_c" + companyId, TimeSpan.FromMinutes(CacheTimeout), () =>
            {
                using (SqlDataReader dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                "VehicleBodyTypesGetById",
                new SqlParameter("@Id", id),
                new SqlParameter("@CompanyId", companyId)))
                {
                    if (dr.Read())
                        return new BodyType(dr);
                    else
                        return null;
                }
            });
        }


        /// <summary>
        /// Gets a BodyType by its ID, for the specified company. If you don't specify the company, it will use the default name and ignore
        /// any possible aliased name.
        /// </summary>
        /// <param name="id"></param>
        /// <param name="companyId"></param>
        /// <returns></returns>
        public static async Task<BodyType> GetByIdAsync(int id, int companyId)
        {
            if (id == 0)
                return null;

            return await AppServices.Cache.GetAsync<BodyType>("bt:" + id + "_c" + companyId, TimeSpan.FromMinutes(CacheTimeout), async () =>
            {
                using (var dr = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString,
                "VehicleBodyTypesGetById",
                new SqlParameter("@Id", id),
                new SqlParameter("@CompanyId", companyId)))
                {
                    if (await dr.ReadAsync())
                        return new BodyType(dr);
                    else
                        return null;
                }
            });
        }

        public static Collection<BodyType> GetAll()
        {
            return AppServices.Cache.Get<InternalBodyTypeCollection>("bt", TimeSpan.FromMinutes(CacheTimeout), () =>
            {
                InternalBodyTypeCollection bodyTypes = new InternalBodyTypeCollection();

                using (SqlDataReader dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                    "VehicleBodyTypesGetAll"))
                {
                    while (dr.Read())
                    {
                        bodyTypes.BodyTypes.Add(new BodyType(dr));
                    }
                }
                return bodyTypes;
            }).BodyTypes ?? new Collection<BodyType>();
        }

        /// <summary>
        /// Retrieve all body types for the specified company, taking into considerations hiding preferences and aliased names.
        /// </summary>
        /// <param name="companyId"></param>
        /// <returns></returns>
        public static Collection<BodyType> GetByCompanyId(int companyId)
        {
            return AppServices.Cache.Get<InternalBodyTypeCollection>("bt_company" + companyId, TimeSpan.FromMinutes(CacheTimeout), () =>
            {
                InternalBodyTypeCollection bodyTypes = new InternalBodyTypeCollection();

                using (SqlDataReader dr = SqlHelper.ExecuteReader(Core. ConnectionString,
                    "VehicleBodyTypesGetAll",
                    new SqlParameter("@CompanyId", companyId)))
                {
                    while (dr.Read())
                    {
                        bodyTypes.BodyTypes.Add(new BodyType(dr));
                    }
                }
                return bodyTypes;
            }).BodyTypes ?? new Collection<BodyType>();
        }

        /// <summary>
        /// Retrieve all body types for the specified company, taking into considerations hiding preferences and aliased names.
        /// </summary>
        /// <param name="companyId"></param>
        /// <returns></returns>
        public static async Task<Collection<BodyType>> GetByCompanyIdAsync(int companyId)
        {
            return (await AppServices.Cache.GetAsync<InternalBodyTypeCollection>("bt_company" + companyId, TimeSpan.FromMinutes(CacheTimeout), async () =>
            {
                InternalBodyTypeCollection bodyTypes = new InternalBodyTypeCollection();

                using (var dr = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString,
                    "VehicleBodyTypesGetAll",
                    new SqlParameter("@CompanyId", companyId)))
                {
                    while (await dr.ReadAsync())
                    {
                        bodyTypes.BodyTypes.Add(new BodyType(dr));
                    }
                }
                return bodyTypes;
            })).BodyTypes ?? new Collection<BodyType>();
        }

        public void Save()
        {
            if (CompanyId == null)
                throw new TowbookException("Cannot save BodyType's that aren't owned by a company");

            throw new NotImplementedException("Saving of custom body types isn't implemented yet.");
        }

        public int Id
        {
            get { return _id; }
        }

        public string Name
        {
            get { return _name; }
            set { _name = value; }
        }

        public int? CompanyId
        {
            get { return _companyId; }
            set { _companyId = value; }
        }
    }

}
