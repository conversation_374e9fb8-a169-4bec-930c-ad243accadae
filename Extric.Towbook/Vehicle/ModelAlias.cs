using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Extric.Towbook.Utility;
using Glav.CacheAdapter.Core.DependencyInjection;
using ProtoBuf;

namespace Extric.Towbook.Vehicle
{
    [ProtoContract]
    public class ModelAlias
    {
        [ProtoMember(0)]
        public int Id { get; set; }

        [ProtoMember(1)]
        public int ModelId { get; set; }

        [ProtoMember(2)]
        public string Name { get; set; }

        public static Collection<ModelAlias> GetAll()
        {
            return AppServices.Cache.Get<CacheCollection<ModelAlias>>("v_ModelAlias",
                TimeSpan.FromDays(30),
                () =>
                {
                    return new CacheCollection<ModelAlias>(
                        SqlMapper.Query<dynamic>("SELECT * FROM VehicleModelAliases")
                        .Select(o =>
                            new ModelAlias()
                            {
                                Id = o.VehicleModelAliasId,
                                ModelId = o.ModelId,
                                Name = o.Name
                            }).ToCollection());
                }).Items;
        }
    }
}
