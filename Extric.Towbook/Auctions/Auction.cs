using Extric.Towbook.Utility;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Threading.Tasks;


namespace Extric.Towbook.Auctions
{
    public enum IncludeType
    {
        All,
        Current,
        Completed
    }

    /// <summary>
    /// Represents an Auction event.
    /// </summary>
    [Table("Auctions")]
    public class Auction
    {
        [Key("AuctionId")]
        public int Id { get; set; }
        public int CompanyId { get; set; }
        public int? ImpoundLotId { get; set; }
        public DateTime CreateDate { get; set; }
        public int OwnerUserId { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public int ItemCount { get; set; }
        public int PhotoCount { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public bool Deleted { get; set; }
        public DateTime? DeleteDate { get; set; }
        public int? DeletedByUserId { get; set; }

        public string RemoteId { get; set; }
        public string RemoteSystem { get; set; } // Joyride etc

        public static Auction GetById(int id) => 
            GetByIds(new int[] { id }).FirstOrDefault();

        public static IEnumerable<Auction> GetByIds(int[] ids)
        {
            var ret = new List<Auction>();

            if (ids == null || ids.Length == 0)
                return ret;

            foreach(var batch in ids.Batch(500))
            {
                ret.AddRange(
                    SqlMapper.Query<Auction>(
                        @"SELECT * FROM Auctions WITH (NOLOCK) WHERE AuctionId IN @Ids", new { Ids = batch }));
            }

            return ret;
        }

        /// <summary>
        /// Gets the last 60 days of auctions based on the Start Date column (if paging isn't used).
        /// Filtering is possible by companyIds and (optionally) lotIds
        /// </summary>
        public static IEnumerable<Auction> GetByCompanyIds(
            int[] companyIds,
            IEnumerable<int> lotIds = null,
            IncludeType include = IncludeType.Current,
            int? pageNumber = null,
            int? pageSize = null)
        {
            if (companyIds.Length == 0)
                return null;

            var builder = new SqlBuilder();
            
            var selector = builder.AddTemplate("SELECT * FROM Auctions WITH (NOLOCK) /**where**/ /**orderby**/");

            builder.Where("CompanyId IN @Ids", new { Ids = companyIds });
            builder.Where("(Deleted IS NULL OR Deleted = 0)");

            switch (include)
            {
                case IncludeType.Current:
                    builder.Where("(EndDate IS NULL OR EndDate > @AfterDate)", new { AfterDate = DateTime.Now });
                    break;
                case IncludeType.Completed:
                    builder.Where("(EndDate IS NOT NULL AND EndDate <= @NowDate)", new { NowDate = DateTime.Now });
                    break;
            }

            if (pageNumber == null || pageNumber == 0)
                pageNumber = 1;
            if (pageSize == null || pageSize > 100)
                pageSize = 100;

            builder.OrderBy("AuctionId DESC", null, pageSize.Value * (pageNumber.Value - 1), pageSize.Value);

            if (lotIds != null)
            {
                builder.Where("(ImpoundLotId IS NULL OR ImpoundLotId IN @LotIds)", new { LotIds = lotIds.ToArray() });
            }

            return SqlMapper.Query<Auction>(
                    selector.RawSql, selector.Parameters);
        }

        public static int GetItemCount(int id) => Auction
           .GetItemCounts(new int[] { id })
           .Where(w => w.Key == id)
           .Sum(z => z.Value);


        /// <summary>
        /// Get the count of assets assigned to an auction
        /// </summary>
        /// <param name="ids">the array of auction ids</param>
        /// <returns>A dictionary with key of AuctionId and Value of the count number</returns>
        public static Dictionary<int, int> GetItemCounts(int[] ids)
        {
            Dictionary<int, int> counts = new Dictionary<int, int>();

            if (ids.Length == 0)
                return counts;

            foreach (var batch in ids.Batch(250))
            {
                var parameters = new List<string>();
                var p = new List<SqlParameter>();
                int i = 0;

                foreach (var row in batch)
                {
                    i++;
                    parameters.Add("@P" + i);
                    p.Add(new SqlParameter("@P" + i, row));
                }

                var sql = @"
                            SELECT 
	                            A.AuctionId, Count(*) as Count
                            FROM
                             Auctions A WITH (NOLOCK)
	                            INNER JOIN DispatchEntryAuctionDetails AD WITH (NOLOCK) ON AD.AuctionId = A.AuctionId
                            WHERE 
	                            A.AuctionId IS NOT NULL 
	                            AND A.AuctionId IN (" + string.Join(", ", parameters) + @")
                            GROUP BY A.AuctionId
                            ";


                using (var dr = SqlHelper.ExecuteReader(
                    Core.ConnectionString,
                    System.Data.CommandType.Text,
                    sql,
                    p.ToArray()))
                {
                    while (dr.Read())
                    {
                        counts.Add(
                            dr.GetValue<int>("AuctionId"),
                            dr.GetValue<int>("Count"));
                    }
                }
            }

            return counts;
        }
        public static async Task<Dictionary<int, int>> GetItemCountsAsync(int[] ids)
        {
            Dictionary<int, int> counts = new Dictionary<int, int>();

            if(ids.Length == 0)
                return counts;

            foreach (var batch in ids.Batch(250))
            {
                var parameters = new List<string>();
                var p = new List<SqlParameter>();
                int i = 0;

                foreach (var row in batch)
                {
                    i++;
                    parameters.Add("@P" + i);
                    p.Add(new SqlParameter("@P" + i, row));
                }

                var sql = @"
                            SELECT 
	                            A.AuctionId, Count(*) as Count
                            FROM
                             Auctions A WITH (NOLOCK)
	                            INNER JOIN DispatchEntryAuctionDetails AD WITH (NOLOCK) ON AD.AuctionId = A.AuctionId
                            WHERE 
	                            A.AuctionId IS NOT NULL 
	                            AND A.AuctionId IN (" + string.Join(", ", parameters) + @")
                            GROUP BY A.AuctionId
                            ";


                using (var dr = await SqlHelper.ExecuteReaderAsync(
                    Core.ConnectionString,
                    System.Data.CommandType.Text,
                    sql,
                    p.ToArray()))
                {
                    while (await dr.ReadAsync())
                    {
                        counts.Add(
                            dr.GetValue<int>("AuctionId"), 
                            dr.GetValue<int>("Count"));
                    }
                }
            }

            return counts;
        }

        public void UpdateCounts(int? itemCount = null, int? photocount = null)
        {
            if (itemCount == null && photocount == null)
                return;

            Save(null, itemCount, photocount);
        } 


        public void Save(User user = null, int? itemCount = null, int? photoCount = null)
        {
            if (Id <= 0)
            {
                if (user != null)
                    OwnerUserId = user.Id;

                CreateDate = DateTime.Now;

                Id = (int)SqlMapper.Insert(this);
            }
            else
            {
                if (itemCount != null)
                    this.ItemCount = itemCount.Value;
                else
                    this.ItemCount = Auction.GetItemCount(this.Id);

                if (photoCount != null)
                    this.PhotoCount = photoCount.Value;

                SqlMapper.Update(this);
            }
        }

        public void Delete(User user)
        {
            if (user == null)
                throw new TowbookException("A user must be provided to delete.");

            Deleted = true;
            DeleteDate = DateTime.Now;
            DeletedByUserId = user.Id;

            Save(null);
        }
    }
}
