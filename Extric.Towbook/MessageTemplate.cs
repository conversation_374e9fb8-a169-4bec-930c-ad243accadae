using System;
using System.Linq;
using System.Text;
using System.Data;
using System.Data.SqlClient;
using System.Collections.ObjectModel;
using Extric.Towbook.Dispatch;
using Glav.CacheAdapter.Core.DependencyInjection;
using Extric.Towbook.Integration;
using System.Threading.Tasks;

namespace Extric.Towbook
{
    /// <summary>
    /// Wireless Dispatching Notifation template message class
    /// </summary>
    public class WdnMessageTemplate
    {
        //private static ICacheManager cache = CacheFactory.GetCacheManager();
        private const int CacheTimeout = 10;

        public int Id { get; private set; }
        public int TypeId { get; set; }
        public int CompanyId { get; set; }
        public string Message { get; set; }

        public WdnMessageTemplate()
        {

        }

        protected WdnMessageTemplate(IDataReader reader)
		{
			InitializeFromDataReader(reader);
        }

        private void InitializeFromDataReader(IDataReader dr)
        {
            Id = dr.GetValue<int>("WdnMessageTemplateId");
            CompanyId = dr.GetValue<int>("CompanyId");
            TypeId = dr.GetValue<int>("WdnMessageTypeId");
            Message = dr.GetValue<string>("Message");
        }

        public string ParseMessage(Entry entry)
        {
            if (entry == null) 
                throw new TowbookException("Can't parse null Entry.");

            var sb = new StringBuilder(Message);

            string customer = string.Empty;
            string customerPhone = string.Empty;
            string reason = string.Empty;
            string account = string.Empty;

            sb.Replace("##call##", entry.CallNumber.ToString());
            sb.Replace("##location-pickup##", entry.TowSource);
            sb.Replace("##dispatcher##", entry.OwnerUser?.FullName ?? "");

            sb.Replace("##color##", (entry.Color != null ? entry.Color.Name : ""));
            sb.Replace("##make-model##", (entry.MakeModelFormatted));
            sb.Replace("##vehicle.year##", (entry.Year > 0 ? entry.Year.ToString() : ""));
            sb.Replace("##location-destination##", entry.TowDestination);
            sb.Replace("##plate##", entry.LicenseNumber);

            if (entry.Contacts.Count > 0)
            {
                customer = entry.Contacts[0].Name + " " + entry.Contacts[0].Phone;

                if (CompanyKeyValue.GetFirstValueOrNull(entry.CompanyId, Provider.Towbook.ProviderId, "PreventDriversFromViewingContactDetails") != "1")
                {
                    customerPhone = entry.Contacts[0].Phone;
                }
            }

            if (CompanyKeyValue.GetFirstValueOrNull(entry.CompanyId, Provider.Towbook.ProviderId, "HideAccountDetailsFromDrivers") != "1")
            {
                if (entry.Account != null)
                    account = entry.Account.Company;
            }

            if (entry.Reason != null)
                reason = entry.Reason.Name;

            sb.Replace("##account##", account);
            sb.Replace("##account.name##", account);

            sb.Replace("##reason##", reason);
            sb.Replace("##invoicenumber##", entry.InvoiceNumber);

            string motorClubMemershipNumber = "";
            if (entry.Attributes.ContainsKey(AttributeValue.BUILTIN_MOTORCLUB_MEMBERSHIPNUMBER))
                motorClubMemershipNumber = "MN#: " + entry.Attributes[AttributeValue.BUILTIN_MOTORCLUB_MEMBERSHIPNUMBER].Value;

            sb.Replace("##membership_number##", motorClubMemershipNumber);

            string motorClubDispatchNumber = "";
            if (entry.Attributes.ContainsKey(AttributeValue.BUILTIN_MOTORCLUB_DISPATCHNUMBER))
                motorClubDispatchNumber = "DN#: " + entry.Attributes[AttributeValue.BUILTIN_MOTORCLUB_DISPATCHNUMBER].Value;
            sb.Replace("##mc_dispatch_number##", motorClubDispatchNumber);

            // hack to add custom field "Issue/id:5491" for companyId 9786... this needs to be able to be
            // dynamic but we need this account, they're yuugee.
            string customField5491 = "";
            if (entry.Attributes.ContainsKey(5491))
                customField5491 = "Issue: " + entry.Attributes[5491].Value;
            sb.Replace("##customField5491##", customField5491);

            string acPO = "";

            if (CompanyKeyValue.GetFirstValueOrNull(entry.CompanyId, Provider.Towbook.ProviderId, "PreventDriversFromViewingPONumber") != "1")
            {
                if (!string.IsNullOrWhiteSpace(entry.PurchaseOrderNumber))
                    acPO = "PO#: " + entry.PurchaseOrderNumber;
            }

            sb.Replace("##account.purchaseorder##", acPO);

            sb.Replace("##bodyType##", entry.BodyType != null ? entry.BodyType.Name : "");

            sb.Replace("##invoiceTotal##", entry.InvoiceTotal.ToString("C"));

            var notes = "";

            var towout = entry.GetAttribute(AttributeValue.BUILTIN_TOWOUT_CALL) == "1";
            if (towout)
                notes = "Tow Out: Vehicle released from storage";
            else
            {
                if (CompanyKeyValue.GetFirstValueOrNull(entry.CompanyId, Provider.Towbook.ProviderId, "PreventDriversFromViewingNotes") != "1")
                    notes = entry.Notes;
            }

            sb.Replace("##notes##", notes);

            sb.Replace("##customer##", customer);
            sb.Replace("##customerphone##", customerPhone);
            sb.Replace("  ", " ");

            return sb.ToString().Trim();
        }

        public static async Task<WdnMessageTemplate> GetByTypeIdAsync(int typeId, int companyId)
        {
            return await AppServices.Cache.GetAsync("wdnMsgTemp" + typeId + "_" + companyId, TimeSpan.FromMinutes(CacheTimeout), async () =>
            {
                using (SqlDataReader dr = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString,
                    "WdnMessageTemplatesGetByTypeId", new SqlParameter("@TypeId", typeId),
                    new SqlParameter("@CompanyId", companyId)))
                {
                    if (await dr.ReadAsync())
                    {
                        return new WdnMessageTemplate(dr);
                    }
                    else
                    {
                        return null;
                    }
                }
            });
        }

        public static async Task<Collection<WdnMessageTemplate>> GetByCompanyAsync(Company.Company company)
        {
            Collection<WdnMessageTemplate> list = new Collection<WdnMessageTemplate>();

            using (SqlDataReader dr = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString,
                "WdnMessageTemplatesGetByCompanyId",
                    new SqlParameter("@CompanyId", company.Id)))
            {
                while (await dr.ReadAsync())
                {
                    list.Add(new WdnMessageTemplate(dr));
                }
            }

            return list;
        }


    }
}
