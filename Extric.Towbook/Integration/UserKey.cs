using Extric.Towbook.Integration.Helpers;
using Extric.Towbook.Utility;
using Glav.CacheAdapter.Core.DependencyInjection;
using ProtoBuf;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;

namespace Extric.Towbook.Integration
{
    [ProtoContract]
    public class UserKey : IKey
    {
        private const string CacheKeyAll = "ik_user_all";

        [ProtoMember(1)]
        public int Id { get; private set; }
        [ProtoMember(2)]
        public int ProviderId { get; set; }
        [ProtoMember(3)]
        public string Name { get; set; }

        /// <summary>
        /// Retrieve a list of all User keys in the system.
        /// </summary>
        /// <returns>Returns a Collection of all the providers available.</returns>
        public static Collection<UserKey> GetAll()
        {
            return AppServices.Cache.Get(CacheKeyAll, TimeSpan.FromDays(30), () =>
            {
                return new CacheCollection<UserKey>(Map(SqlMapper.QuerySP<dynamic>("Integration.ProviderUserKeysGetAll")));
            }).Items;
        }

        /// <summary>
        /// Retrieve a list of all User keys in the system.
        /// </summary>
        /// <returns>Returns a Collection of all the providers available.</returns>
        public static async Task<Collection<UserKey>> GetAllAsync()
        {
            return (await AppServices.Cache.GetAsync(CacheKeyAll, TimeSpan.FromDays(30), async() =>
            {
                return new CacheCollection<UserKey>(Map(await SqlMapper.QuerySpAsync<dynamic>("Integration.ProviderUserKeysGetAll", null)));
            })).Items;
        }

        /// <summary>
        /// Retrieve a list of all of the UserKey's available to the system for the specified ProviderId.
        /// </summary>
        /// <param name="providerId">The provider to return keys for</param>
        /// <returns>Returns a Collection of all the providers available.</returns>
        public static Collection<UserKey> GetByProviderId(int providerId)
        {
            return GetAll().Where(o => o.ProviderId == providerId).ToCollection();
        }

        /// <summary>
        /// Return a Key with the specified Name for the specified provider.
        /// </summary>
        public static UserKey GetByProviderId(int providerId, string name)
        {
            return GetByProviderId(providerId).Where(n => n.Name.ToLowerInvariant() == name.ToLowerInvariant()).FirstOrDefault();
        }

        /// <summary>
        /// Return a Key with the specified Name for the specified provider.
        /// </summary>
        public static async Task<UserKey> GetByProviderIdAsync(int providerId, string name)
        {
            return (await GetAllAsync()).FirstOrDefault(o => o.ProviderId == providerId &&
                string.Equals(o.Name, name, StringComparison.CurrentCultureIgnoreCase));
        }

        /// <summary>
        /// Helper method to map the SQL columns to the respective object properties. 
        /// </summary>
        private static Collection<UserKey> Map(IEnumerable<dynamic> list)
        {
            return list.Select(o => new UserKey()
            {
                Id = o.ProviderUserKeyId,
                ProviderId = o.IntegrationProviderId,
                Name = o.Name,
            }).ToCollection();
        }

        public UserKey Save()
        {
            this.Id = Convert.ToInt32(SqlMapper.QuerySP<dynamic>("Integration.ProviderUserKeysInsert",
                    new
                    {
                        @IntegrationProviderId = this.ProviderId,
                        @Name = this.Name,
                    }).FirstOrDefault().Id);

            AppServices.Cache.InvalidateCacheItem(CacheKeyAll);

            return this;
        }
    }
}
