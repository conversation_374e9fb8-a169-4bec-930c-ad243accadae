/* these classes are unfortunately neccesary because protobuf-net doesn't support seriailizing generic collections directly. */

using Extric.Towbook.Utility;
using ProtoBuf;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;

namespace Extric.Towbook.Integration.Helpers
{
    #region Key Collections

    [ProtoContract]
    public class CompanyKeyCollection
    {
        [ProtoMember(1)]
        public Collection<CompanyKey> Collection { get; set; }

        public CompanyKeyCollection() { }

        public CompanyKeyCollection(IEnumerable<CompanyKey> list)
        {
            Collection = list.ToCollection();
        }
    }

    [ProtoContract]
    public class DispatchEntryKeyCollection
    {
        [ProtoMember(1)]
        public Collection<DispatchEntryKey> Collection { get; set; }

        public DispatchEntryKeyCollection() { }

        public DispatchEntryKeyCollection(IEnumerable<DispatchEntryKey> list)
        {
            Collection = list.ToCollection();
        }
    }

    [ProtoContract]
    public class DispatchPaymentEntryKeyCollection
    {
        [ProtoMember(1)]
        public Collection<InvoicePaymentKey> Collection { get; set; }

        public DispatchPaymentEntryKeyCollection() { }

        public DispatchPaymentEntryKeyCollection(IEnumerable<InvoicePaymentKey> list)
        {
            Collection = list.ToCollection();
        }
    }

    [ProtoContract]
    public class RateItemKeyCollection
    {
        [ProtoMember(1)]
        public Collection<RateItemKey> Collection { get; set; }

        public RateItemKeyCollection() { }

        public RateItemKeyCollection(IEnumerable<RateItemKey> list)
        {
            Collection = list.ToCollection();
        }
    }

    [ProtoContract]
    public class DriverKeyCollection
    {
        [ProtoMember(1)]
        public Collection<DriverKey> Collection { get; set; }

        public DriverKeyCollection() { }

        public DriverKeyCollection(IEnumerable<DriverKey> list)
        {
            Collection = list.ToCollection();
        }
    }

    [ProtoContract]
    public class TruckKeyCollection
    {
        [ProtoMember(1)]
        public Collection<TruckKey> Collection { get; set; }

        public TruckKeyCollection() { }

        public TruckKeyCollection(IEnumerable<TruckKey> list)
        {
            Collection = list.ToCollection();
        }
    }

    [ProtoContract]
    public class AccountKeyCollection
    {
        [ProtoMember(1)]
        public Collection<AccountKey> Collection { get; set; }

        public AccountKeyCollection() { }

        public AccountKeyCollection(IEnumerable<AccountKey> list)
        {
            Collection = list.ToCollection();
        }
    }

    [ProtoContract]
    public class PaymentMethodKeyCollection
    {
        [ProtoMember(1)]
        public Collection<PaymentMethodKey> Collection { get; set; }

        public PaymentMethodKeyCollection() { }

        public PaymentMethodKeyCollection(IEnumerable<PaymentMethodKey> list)
        {
            Collection = list.ToCollection();
        }
    }
    #endregion

    #region Key Value Collections
    [ProtoContract]
    public class CompanyKeyValueCollection
    {
        [ProtoMember(1)]
        public Collection<CompanyKeyValue> Collection { get; set; }

        public CompanyKeyValueCollection() { }

        public CompanyKeyValueCollection(IEnumerable<CompanyKeyValue> list)
        {
            Collection = list.ToCollection();
        }
    }

    [ProtoContract]
    public class DispatchEntryKeyValueCollection
    {
        [ProtoMember(1)]
        public Collection<DispatchEntryKeyValue> Collection { get; set; }

        public DispatchEntryKeyValueCollection() { }

        public DispatchEntryKeyValueCollection(IEnumerable<DispatchEntryKeyValue> list)
        {
            Collection = list.ToCollection();
        }
    }

    [ProtoContract]
    public class DispatchPaymentEntryKeyValueCollection
    {
        [ProtoMember(1)]
        public Collection<InvoicePaymentKeyValue> Collection { get; set; }

        public DispatchPaymentEntryKeyValueCollection() { }

        public DispatchPaymentEntryKeyValueCollection(IEnumerable<InvoicePaymentKeyValue> list)
        {
            Collection = list.ToCollection();
        }
    }

    [ProtoContract]
    public class RateItemKeyValueCollection
    {
        [ProtoMember(1)]
        public Collection<RateItemKeyValue> Collection { get; set; }

        public RateItemKeyValueCollection() { }

        public RateItemKeyValueCollection(IEnumerable<RateItemKeyValue> list)
        {
            Collection = list.ToCollection();
        }
    }

    [ProtoContract]
    public class DriverKeyValueCollection
    {
        [ProtoMember(1)]
        public Collection<DriverKeyValue> Collection { get; set; }

        public DriverKeyValueCollection() { }

        public DriverKeyValueCollection(IEnumerable<DriverKeyValue> list)
        {
            Collection = list.ToCollection();
        }
    }

    [ProtoContract]
    public class TruckKeyValueCollection
    {
        [ProtoMember(1)]
        public Collection<TruckKeyValue> Collection { get; set; }

        public TruckKeyValueCollection() { }

        public TruckKeyValueCollection(IEnumerable<TruckKeyValue> list)
        {
            Collection = list.ToCollection();
        }
    }

    [ProtoContract]
    public class AccountKeyValueCollection
    {
        [ProtoMember(1)]
        public Collection<AccountKeyValue> Collection { get; set; }

        public AccountKeyValueCollection() { }

        public AccountKeyValueCollection(IEnumerable<AccountKeyValue> list)
        {
            Collection = list.ToCollection();
        }
    }

    [ProtoContract]
    public class ProviderCollection
    {
        [ProtoMember(1)]
        public Collection<Provider> Collection { get; set; }

        public ProviderCollection() { }

        public ProviderCollection(IEnumerable<Provider> list)
        {
            Collection = list.ToCollection();
        }
    }

    [ProtoContract]
    public class PaymentMethodKeyValueCollection
    {
        [ProtoMember(1)]
        public Collection<PaymentMethodKeyValue> Collection { get; set; }

        public PaymentMethodKeyValueCollection() { }

        public PaymentMethodKeyValueCollection(IEnumerable<PaymentMethodKeyValue> list)
        {
            Collection = list.ToCollection();
        }
    }
    #endregion
}
