using Extric.Towbook.Integration.Helpers;
using Extric.Towbook.Utility;
using Glav.CacheAdapter.Core.DependencyInjection;
using ProtoBuf;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;

namespace Extric.Towbook.Integration
{
    [ProtoContract]
    public class CompanyKey : IKey
    {
        private const string CacheKeyAll = "ik_company_all";

        [ProtoMember(1)]
        public int Id { get; private set; }
        [ProtoMember(2)]
        public int ProviderId { get; set; }
        [ProtoMember(3)]
        public string Name { get; set; }

        /// <summary>
        /// Retrieve a list of all company keys in the system.
        /// </summary>
        /// <returns>Returns a Collection of all the providers available.</returns>
        public static Collection<CompanyKey> GetAll()
        {
            return AppServices.Cache.Get<CompanyKeyCollection>(CacheKeyAll, TimeSpan.FromDays(30), () =>
            {
                return new CompanyKeyCollection(Map(SqlMapper.QuerySP<dynamic>("Integration.ProviderCompanyKeysGetAll")));
            }).Collection;
        }

        /// <summary>
        /// Retrieve a list of all of the providers available to the system. 
        /// </summary>
        /// <param name="providerId">The provider to return keys for</param>
        /// <returns>Returns a Collection of all the providers available.</returns>
        public static Collection<CompanyKey> GetByProviderId(int providerId)
        {
            return GetAll().Where(o => o.ProviderId == providerId).ToCollection();
        }


        /// <summary>
        /// Return a Key with the specified Name for the specified provider.
        /// </summary>
        public static CompanyKey GetByProviderId(int providerId, string name)
        {
            return GetByProviderId(providerId).Where(n => n.Name.ToLowerInvariant() == name.ToLowerInvariant()).FirstOrDefault();
        }


        /// <summary>
        /// Helper method to map the SQL columns to the respective object properties. 
        /// </summary>
        private static Collection<CompanyKey> Map(IEnumerable<dynamic> list)
        {
            return list.Select(o => new CompanyKey()
            {
                Id = o.ProviderCompanyKeyId,
                ProviderId = o.IntegrationProviderId,
                Name = o.Name,
            }).ToCollection();
        }

        public CompanyKey Save()
        {
            this.Id = Convert.ToInt32(SqlMapper.QuerySP<dynamic>("Integration.ProviderCompanyKeysInsert",
                    new
                    {
                        @IntegrationProviderId = this.ProviderId,
                        @Name = this.Name,
                    }).FirstOrDefault().Id);

            AppServices.Cache.InvalidateCacheItem(CacheKeyAll);

            return this;
        }
    }
}