using Extric.Towbook.Integration.Helpers;
using Extric.Towbook.Utility;
using Glav.CacheAdapter.Core.DependencyInjection;
using ProtoBuf;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;

namespace Extric.Towbook.Integration
{
    [ProtoContract]
    public class CompanyKeyValue : IKeyValue
    {
        private const string CacheCompanyFormat = "int_ckv_{0}";

        [ProtoMember(1)]
        public int Id { get; set; }
        
        [ProtoMember(2)]
        public int KeyId { get; set; }
        
        [ProtoMember(3)]
        public string Value { get; set; }
        
        [ProtoMember(4)]
        public int CompanyId { get; set; }

        [ProtoMember(5)]
        private int providerId;

        public CompanyKeyValue()
        {

        }

        public CompanyKeyValue(int companyId, int keyId, string value)
        {
            this.CompanyId = companyId;
            this.KeyId = keyId;
            this.Value = value;
        }

        public override string ToString()
        {
            return string.Format("KeyId={0}, Value={1}, Id={2}, CompanyId={3}",
                KeyId, Value, Id, CompanyId);
        }

        /// <summary>
        /// Retrieves all the key values for the specified company, for all providers.
        /// </summary>
        /// <param name="companyId">Company/client to return values for</param>
        /// <returns>Collection of key values</returns>
        public static Collection<CompanyKeyValue> GetByCompany(int companyId)
        {
            return AppServices.Cache.Get(string.Format(CacheCompanyFormat, companyId),
                TimeSpan.FromDays(30),
                () =>
                {
                    return new CompanyKeyValueCollection(Map(SqlMapper.QuerySP<dynamic>("Integration.ProviderCompanyKeyValuesGetByCompanyId",
                        new
                        {
                            @CompanyId = companyId
                        })));
                }).Collection ?? new Collection<CompanyKeyValue>();
        }

        public static Collection<CompanyKeyValue> GetByValueExact(IKey  key, string value)
        {
            return Map(SqlMapper.QuerySP<dynamic>("Integration.ProviderCompanyKeyValuesGetByValue",
                new
                {
                    @KeyId = key.Id,
                    @Value = value
                }));
        }


        /// <summary>
        /// Retrieves all the key values for the specified provider and companyId.
        /// </summary>
        /// <param name="providerId">Provider to return values values for; you can't return multiple providers data with this method.</param>
        /// <param name="companyId">Company/client to return values for</param>
        /// <returns>Collection of key values</returns>
        public static Collection<CompanyKeyValue> GetByCompany(int companyId, int providerId)
        {
            return GetByCompany(companyId).Where(o => o.providerId == providerId).ToCollection();
        }

        public static Collection<CompanyKeyValue> GetByCompany(int id, int providerId, string keyName) =>
            GetByCompanyId(id, providerId, keyName);

        public static Collection<CompanyKeyValue> GetByCompany(int companyId, int providerId, string[] keys)
        {
            return Map(SqlMapper.Query<dynamic>(
                @"SELECT kv.ProviderCompanyKeyValueId, kv.CompanyId, kv.ProviderCompanyKeyId, kv.Value, pk.IntegrationProviderId
                    FROM    Integration.ProviderCompanyKeyValues kv WITH (nolock)
                            INNER JOIN Integration.ProviderCompanyKeys pk WITH (nolock) on pk.ProviderCompanyKeyId=kv.ProviderCompanyKeyId
                    WHERE   kv.CompanyId = @CompanyId AND pk.IntegrationProviderId=@ProviderId AND pk.Name IN @Keys",
            new
            {
                CompanyId = companyId,
                ProviderId = providerId,
                Keys = keys
            }));
        }

        public void Save()
        {
            if (CompanyId < 1)
                throw new TowbookException("CompanyKeyValue.CompanyId is a required field. Cannot save if it is empty.");

            if (KeyId < 1)
                throw new TowbookException("CompanyKeyValue.KeyId is a required field. Cannot save if it is empty.");

            if (this.Id == 0)
            {
                this.Id = Convert.ToInt32(SqlMapper.QuerySP<dynamic>("Integration.ProviderCompanyKeyValuesInsert",
                    new
                    {
                        @CompanyId = this.CompanyId,
                        @ProviderCompanyKeyId = this.KeyId,
                        @Value = this.Value
                    }).FirstOrDefault().Id);
            }
            else
            {
                SqlMapper.ExecuteSP("Integration.ProviderCompanyKeyValuesUpdateById",
                    new
                    {
                        @ProviderCompanyKeyValueId = this.Id,
                        @CompanyId = this.CompanyId,
                        @ProviderCompanyKeyId = this.KeyId,
                        @Value = this.Value
                    });
            }

            var keyName = CompanyKey.GetAll().Where(o => o.Id == KeyId).FirstOrDefault();
            string key = $"ckv_{CompanyId}_{keyName.ProviderId}_{keyName.Name}";
            Core.SetRedisValue(key, new CompanyKeyValue[] { this }.ToJson());
            
            AppServices.Cache.InvalidateCacheItem(string.Format(CacheCompanyFormat, CompanyId));
        }

        public void Delete()
        {
            if (this.Id > 0)
            {
                SqlMapper.ExecuteSP("Integration.ProviderCompanyKeyValuesDeleteById",
                    new { @ProviderCompanyKeyValueId = this.Id });

                var keyName = CompanyKey.GetAll().Where(o => o.Id == KeyId).FirstOrDefault();
                string key = $"ckv_{CompanyId}_{keyName.ProviderId}_{keyName.Name}";

                Core.DeleteRedisKey(key);

                AppServices.Cache.InvalidateCacheItem(string.Format(CacheCompanyFormat, CompanyId));
            }
        }

        private static Collection<CompanyKeyValue> Map(IEnumerable<dynamic> list)
        {
            if (list == null)
                return new Collection<CompanyKeyValue>();

            return list.Select(o => new CompanyKeyValue()
            {
                Id = o.ProviderCompanyKeyValueId,
                CompanyId = o.CompanyId,
                KeyId = o.ProviderCompanyKeyId,
                Value = o.Value,

                providerId = o.IntegrationProviderId
            }).ToCollection();
        }

        public static Collection<CompanyKeyValue> GetByCompanyId(int id, int providerId, string keyName)
        {
            string key = $"ckv_{id}_{providerId}_{keyName}";
            var prc = new Glav.CacheAdapter.Web.PerRequestCacheHelper();

            try
            {
                string rv = prc.TryGetItemFromPerRequestCache<string>(key);

                if (rv == null)
                {
                    rv = Core.GetRedisValue(key);
                    if (rv != null)
                        prc.AddToPerRequestCache(key, rv);
                }

                if (rv != null)
                {
                    if (rv == "[]" && keyName.StartsWith("Aaa"))
                    {
                        // don't respect cache missing Aaa keys... grab it from the database automatically again always.
                    }
                    else
                    {
                        return Newtonsoft.Json.JsonConvert.DeserializeObject<Collection<CompanyKeyValue>>(rv);
                    }
                }
            }
            catch
            {
                // ignore deserialization/cache errors
            }

            var ret = Map(SqlMapper.QuerySP<dynamic>("Integration.ProviderCompanyKeyValuesGetByKey",
                new
                {
                    @CompanyId = id,
                    @ProviderId = providerId,
                    @KeyName = keyName,
                }));

            var persistValue = ret.ToJson();

            Core.SetRedisValue(key, persistValue, TimeSpan.FromMinutes(20));
            prc.AddToPerRequestCache(key, persistValue);

            return ret;
        }

        public static async Task<Collection<CompanyKeyValue>> GetByCompanyIdAsync(int id, int providerId, string keyName)
        {
            string key = $"ckv_{id}_{providerId}_{keyName}";
            var prc = new Glav.CacheAdapter.Web.PerRequestCacheHelper();

            try
            {
                string rv = prc.TryGetItemFromPerRequestCache<string>(key);

                if (rv == null)
                {
                    rv = await Core.GetRedisValueAsync(key);
                    if (rv != null)
                        prc.AddToPerRequestCache(key, rv);
                }

                if (rv != null)
                {
                    if (rv == "[]" && keyName.StartsWith("Aaa"))
                    {
                        // don't respect cache missing Aaa keys... grab it from the database automatically again always.
                    }
                    else
                    {
                        return Newtonsoft.Json.JsonConvert.DeserializeObject<Collection<CompanyKeyValue>>(rv);
                    }
                }
            }
            catch
            {
                // ignore deserialization/cache errors
            }

            var ret = Map(await SqlMapper.QuerySpAsync<dynamic>("Integration.ProviderCompanyKeyValuesGetByKey",
                new
                {
                    @CompanyId = id,
                    @ProviderId = providerId,
                    @KeyName = keyName,
                }));

            var persistValue = ret.ToJson();

            await Core.SetRedisValueAsync(key, persistValue, TimeSpan.FromMinutes(20));
            prc.AddToPerRequestCache(key, persistValue);

            return ret;
        }


        /// <summary>
        /// Retrieves the first value, or null, for the specified company, providerId and keyName.
        /// </summary>
        /// <param name="companyId"></param>
        /// <param name="providerId"></param>
        /// <param name="keyName"></param>
        /// <returns>Value for the specified key, or null</returns>
        public static string GetFirstValueOrNull(int companyId, int providerId, string keyName)
        {
            return GetByCompanyId(companyId, providerId, keyName).FirstOrDefault()?.Value;
        }

        public static async Task<string> GetFirstValueOrNullAsync(int companyId, int providerId, string keyName)
        {
            var values = await GetByCompanyIdAsync(companyId, providerId, keyName);
            return values.FirstOrDefault()?.Value;
        }

        /// <summary>
        /// Returns the requested value, or a new CompanyKeyValue with the CompanyId and Key prefilled.
        /// </summary>
        /// <param name="companyId"></param>
        /// <param name="providerId"></param>
        /// <param name="keyName"></param>
        /// <returns></returns>
        public static CompanyKeyValue GetFirstValueOrNew(int companyId, int providerId, string keyName)
        {
            var key = CompanyKey.GetByProviderId(providerId, keyName);

            if (key == null)
                throw new TowbookException("Key isn't defined: " + keyName);

            var r = GetByCompanyId(companyId, providerId, keyName).FirstOrDefault();

            return r ?? new CompanyKeyValue()
            {
                CompanyId = companyId,
                KeyId = key.Id
            };

        }

        public static async Task<CompanyKeyValue> GetFirstValueOrNewAsync(int companyId, int providerId, string keyName)
        {
            var key = CompanyKey.GetByProviderId(providerId, keyName);

            if (key == null)
                throw new TowbookException("Key isn't defined: " + keyName);

            var results = await GetByCompanyIdAsync(companyId, providerId, keyName);
            var r = results.FirstOrDefault();

            return r ?? new CompanyKeyValue()
            {
                CompanyId = companyId,
                KeyId = key.Id
            };
        }


        public static void InsertOrUpdate(int companyId, int providerId, string key, string keyValue)
        {
            string ck = $"ckv_{companyId}_{providerId}_{key}";
            Core.DeleteRedisKey(ck);

            var kv = GetByCompanyId(companyId, providerId, key).FirstOrDefault();

            if (kv == null)
                kv = new CompanyKeyValue()
                {
                    CompanyId = companyId,
                    KeyId = CompanyKey.GetByProviderId(providerId, key).Id
                };

            kv.Value = keyValue;
            kv.Save();

            var prc = new Glav.CacheAdapter.Web.PerRequestCacheHelper();
            prc.AddToPerRequestCache(ck, kv);


        }
    }

    public static class CompanyKeyValueExtensions
    {
        /// <summary>
        /// Returns the value for the specified key in the list of CompanyKeyValues passed in.
        /// </summary>
        /// <param name="list">CompanyKeyValues to scan for the selected key</param>
        /// <param name="key"></param>
        /// <returns>Value of the first CompanyKeyValue that matches</returns>   
        public static string ValueOrNull(this IEnumerable<CompanyKeyValue> list, string key)
        {
            var ak = CompanyKey.GetByProviderId(Provider.Towbook.ProviderId, key);

            if (ak != null && list != null && list.Any())
            {
                var akv = list.Where(o => o.KeyId == ak.Id).FirstOrDefault();
                if (akv != null)
                    return akv.Value;
            }

            return null;
        }

        /// <summary>
        /// Same as calling ValueOrNull, but returns String.Empty instead of null.
        /// </summary>
        /// <param name="list">CompanyKeyValues to scan for the selected key</param>
        /// <param name="key"></param>
        /// <returns>Value of the first CompanyKeyValue that matches</returns>
        public static string ValueOrEmpty(this IEnumerable<CompanyKeyValue> list, string key)
        {
            return ValueOrNull(list, key) ?? string.Empty;
        }
    }
}
