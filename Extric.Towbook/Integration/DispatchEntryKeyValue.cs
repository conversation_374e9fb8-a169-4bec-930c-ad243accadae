using Extric.Towbook.Integration.Helpers;
using Extric.Towbook.Utility;
using Glav.CacheAdapter.Core.DependencyInjection;
using ProtoBuf;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;

namespace Extric.Towbook.Integration
{
    [ProtoContract]
    public class DispatchEntryKeyValue : IKeyValue
    {
        private const string CacheDispatchEntryFormat = "int_dekv_{0}";
        private const string CacheDispatchEntryCompanyFormat = "int_dekv_company_{0}";

        [ProtoMember(5)]
        protected int providerId;

        [ProtoMember(1)]
        public int Id { get; private set; }

        [ProtoMember(2)]
        public int KeyId { get; set; }

        [ProtoMember(3)]
        public string Value { get; set; }

        [ProtoMember(4)]
        public int DispatchEntryId { get; set; }


        public DispatchEntryKeyValue()
        {

        }

        public DispatchEntryKeyValue(int dispatchEntryId, int keyId, string value)
        {
            this.DispatchEntryId = dispatchEntryId;
            this.KeyId = keyId;
            this.Value = value;
        }

        public override string ToString()
        {
            return string.Format("KeyId={0}, Value={1}, Id={2}, DispatchEntryId={3}",
                KeyId, Value, Id, DispatchEntryId);
        }

        /// <summary>
        /// Retrieves all the key values for the specified DispatchEntry, for all providers.
        /// </summary>
        /// <param name="DispatchEntryId">DispatchEntry/client to return values for</param>
        /// <returns>Collection of key values</returns>
        public static Collection<DispatchEntryKeyValue> GetByCompany(int companyId)
        {
            return AppServices.Cache.Get<DispatchEntryKeyValueCollection>(string.Format(CacheDispatchEntryCompanyFormat, companyId),
                TimeSpan.FromDays(30),
                () =>
                {
                    return new DispatchEntryKeyValueCollection(Map(SqlMapper.QuerySP<dynamic>("Integration.ProviderDispatchEntryKeyValuesGetByCompanyId",
                        new
                        {
                            @CompanyId = companyId
                        })));
                }).Collection ?? new Collection<DispatchEntryKeyValue>(); ;
        }

        public static Collection<DispatchEntryKeyValue> GetByDispatchEntryId(int dispatchEntryId)
        {
            return Map(SqlMapper.QuerySP<dynamic>("Integration.ProviderDispatchEntryKeyValuesGetByDispatchEntryId",
                new
                {
                    @DispatchEntryId = dispatchEntryId
                }));
        }

        /// <summary>
        /// Retrieves all the key values for the specified provider and companyId.
        /// </summary>
        public static Collection<DispatchEntryKeyValue> GetByCompany(int companyId, int providerId)
        {
            return GetByCompany(companyId).Where(o => o.providerId == providerId).ToCollection();
        }

        public static Collection<DispatchEntryKeyValue> GetByCompanyIdAndDispatchEntryId(int companyId, int DispatchEntryId)
        {
            return GetByCompany(companyId).Where(o => o.DispatchEntryId == DispatchEntryId).ToCollection();
        }

        public void Save()
        {
            if (this.Id == 0)
            {
                this.Id = Convert.ToInt32(SqlMapper.QuerySP<dynamic>("Integration.ProviderDispatchEntryKeyValuesInsert",
                    new
                    {
                        @DispatchEntryId = this.DispatchEntryId,
                        @ProviderDispatchEntryKeyId = this.KeyId,
                        @Value = this.Value
                    }).FirstOrDefault().Id);
            }
            else
            {
                SqlMapper.ExecuteSP("Integration.ProviderDispatchEntryKeyValuesUpdateById",
                    new
                    {
                        @ProviderDispatchEntryKeyValueId = this.Id,
                        @DispatchEntryId = this.DispatchEntryId,
                        @ProviderDispatchEntryKeyId = this.KeyId,
                        @Value = this.Value
                    });
            }

            AppServices.Cache.InvalidateCacheItem(string.Format(CacheDispatchEntryCompanyFormat, Dispatch.Entry.GetById(DispatchEntryId).CompanyId));
            AppServices.Cache.InvalidateCacheItem(string.Format(CacheDispatchEntryFormat, Id));
        }

        public void Delete()
        {
            SqlMapper.ExecuteSP("Integration.ProviderDispatchEntryKeyValuesDeleteById",
                new { @ProviderDispatchEntryKeyValueId = this.Id });
        }

        private static Collection<DispatchEntryKeyValue> Map(IEnumerable<dynamic> list)
        {
            return list.Select(o => new DispatchEntryKeyValue()
            {
                Id = o.ProviderDispatchEntryKeyValueId,
                DispatchEntryId = o.DispatchEntryId,
                KeyId = o.ProviderDispatchEntryKeyId,
                Value = o.Value,

                providerId = o.IntegrationProviderId
            }).ToCollection();
        }

        public static Collection<DispatchEntryKeyValue> GetByDispatchEntryId(int id, int providerId, string keyName)
        {
            var k = DispatchEntryKey.GetByProviderId(providerId, keyName);

            if (k == null)
                throw new TowbookException("keyName requested isn't defined: " + keyName);

            var r = GetByDispatchEntryId(id);
            if (r != null)
                return r.Where(o => o.KeyId == k.Id).ToCollection();
            else
                return null;
        }

        public static Collection<DispatchEntryKeyValue> GetByCompanyId(int id, int providerId, string keyName)
        {
            var key = DispatchEntryKey.GetByProviderId(providerId, keyName);

            if (key == null)
                throw new TowbookException("Key isn't defined: " + keyName);

            var r = GetByCompany(id);
            if (r != null)
                return r.Where(o => o.KeyId == key.Id).ToCollection();
            else
                return null;
        }

        public static Collection<DispatchEntryKeyValue> GetRecentByCompanyId(int id, int providerId, string keyName)
        {
            var key = DispatchEntryKey.GetByProviderId(providerId, keyName);

            if (key == null)
                throw new TowbookException("Key isn't defined: " + keyName);

            return Map(SqlMapper.QuerySP<dynamic>("Integration.ProviderDispatchEntryKeyValuesGetByCompanyAndKeyIdRecent",
                new
                {
                    @CompanyId = id,
                    @KeyId = key.Id
                }));
        }

    }
}
