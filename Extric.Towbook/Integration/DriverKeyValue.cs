using Extric.Towbook.Integration.Helpers;
using Extric.Towbook.Utility;
using Glav.CacheAdapter.Core.DependencyInjection;
using ProtoBuf;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Extric.Towbook.Integration
{
    [ProtoContract]
    public class DriverKeyValue : IKeyValue
    {
        private const string CacheDriverFormat = "int_dkv_{0}";
        private const string CacheDriverCompanyFormat = "int_dkv_company_{0}";

        [ProtoMember(5)]
        protected int providerId;

        [ProtoMember(1)]
        public int Id { get; private set; }

        [ProtoMember(2)]
        public int KeyId { get; set; }

        [ProtoMember(3)]
        public string Value { get; set; }

        [ProtoMember(4)]
        public int DriverId { get; set; }


        public override string ToString()
        {
            return string.Format("KeyId={0}, Value={1}, Id={2}, DriverId={3}",
                KeyId, Value, Id, DriverId);
        }

        /// <summary>
        /// Retrieves all the key values for the specified Driver, for all providers.
        /// </summary>
        /// <param name="DriverId">Driver/client to return values for</param>
        /// <returns>Collection of key values</returns>
        public static Collection<DriverKeyValue> GetByCompany(int companyId)
        {
            return AppServices.Cache.Get<DriverKeyValueCollection>(string.Format(CacheDriverCompanyFormat, companyId),
                TimeSpan.FromMinutes(1),
                () =>
                {
                    return new DriverKeyValueCollection(Map(SqlMapper.QuerySP<dynamic>("Integration.ProviderDriverKeyValuesGetByCompanyId",
                        new
                        {
                            @CompanyId = companyId
                        })));
                }).Collection ?? new Collection<DriverKeyValue>();
        }
        public static Collection<DriverKeyValue> Get(int companyId, int providerId, string keyName)
        {
            var k = DriverKey.GetByProviderId(providerId, keyName);

            if (k == null)
                throw new TowbookException("Key isn't defined: " + keyName);

            return AppServices.Cache.Get($"byKey_{companyId}_{providerId}_{keyName}", 
                TimeSpan.FromMinutes(1), () =>
                {
                    return Map(SqlMapper.Query<dynamic>(
                        @"SELECT kv.ProviderDriverKeyValueId, kv.DriverId, kv.ProviderDriverKeyId, kv.Value, pk.IntegrationProviderId
		                    FROM	Integration.ProviderDriverKeyValues kv WITH (NOLOCK)
	    		                    INNER JOIN Integration.ProviderDriverKeys pk WITH (NOLOCK) on pk.ProviderDriverKeyId=kv.ProviderDriverKeyId
                                    INNER JOIN Drivers d WITH (NOLOCK) on d.DriverId=kv.DriverId
		                    WHERE   (d.CompanyId=@CompanyId AND pk.IntegrationProviderId=@ProviderId AND kv.ProviderDriverKeyId=@KeyId)",
                        new
                        {
                            ProviderId = providerId,
                            KeyId = k.Id,
                            CompanyId = companyId
                        }));
                });
        }


        public static Collection<DriverKeyValue> GetByDriver(int companyId, int driverId, int providerId, string keyName)
        {
            var k = DriverKey.GetByProviderId(providerId, keyName);

            if (k == null)
                throw new TowbookException("Key isn't defined: " + keyName);

            return AppServices.Cache.Get("byDriver_" + driverId + "_" +
                providerId + "_" + keyName, TimeSpan.FromMinutes(5), () =>
                {
                    return Map(SqlMapper.Query<dynamic>(
                        @"SELECT kv.ProviderDriverKeyValueId, kv.DriverId, kv.ProviderDriverKeyId, kv.Value, pk.IntegrationProviderId
		                    FROM	Integration.ProviderDriverKeyValues kv
	    		                    INNER JOIN Integration.ProviderDriverKeys pk on pk.ProviderDriverKeyId=kv.ProviderDriverKeyId
		                    WHERE   (DriverId = @DriverId AND pk.IntegrationProviderId=@ProviderId AND kv.ProviderDriverKeyId=@KeyId)",
                        new
                        {
                            @DriverId = driverId,
                            @ProviderId = providerId,
                            @KeyId = k.Id
                        }));
                });
        }

        public static async Task<Collection<DriverKeyValue>> GetByDriverAsync(int companyId, int driverId, int providerId, string keyName)
        {
            var k = await DriverKey.GetByProviderIdAsync(providerId, keyName);

            if (k == null)
                throw new TowbookException("Key isn't defined: " + keyName);

            string key = "byDriver_" + driverId + "_" + providerId + "_" + keyName;

            return await AppServices.Cache.GetAsync(key,
              TimeSpan.FromMinutes(5),
              async () => {
                  var rv = await Core.GetRedisValueAsync(key);
                  if (rv != null)
                  {
                      return Newtonsoft.Json.JsonConvert.DeserializeObject<Collection<DriverKeyValue>>(rv);
                  }

                  Console.WriteLine("Retrieving from DB " + key);
                  var ret = Map(await SqlMapper.QueryAsync<dynamic>(
                      @"SELECT kv.ProviderDriverKeyValueId, kv.DriverId, kv.ProviderDriverKeyId, kv.Value, pk.IntegrationProviderId
                                FROM    Integration.ProviderDriverKeyValues kv
                                        INNER JOIN Integration.ProviderDriverKeys pk on pk.ProviderDriverKeyId=kv.ProviderDriverKeyId
                                WHERE   (DriverId = @DriverId AND pk.IntegrationProviderId=@ProviderId AND kv.ProviderDriverKeyId=@KeyId)",
                      new
                      {
                          @DriverId = driverId,
                          @ProviderId = providerId,
                          @KeyId = k.Id
                      }));

                  await Core.SetRedisValueAsync(key, ret.ToJson(), TimeSpan.FromMinutes(5));

                  return ret;
              });
        }

        public static Collection<DriverKeyValue> GetByDriver(int companyId, IEnumerable<int> driverIds, 
            int providerId, string[] keyNames)
        {

            List<int> keyIds = new List<int>();

            foreach (var keyName in keyNames)
            {
                var k = DriverKey.GetByProviderId(providerId, keyName);

                if (k == null)
                    throw new TowbookException("Key isn't defined: " + keyName);

                keyIds.Add(k.Id);
            }


            var ret = new List<DriverKeyValue>();

            foreach(var batch in driverIds.Batch(500))
            {
                ret.AddRange(Map(SqlMapper.Query<dynamic>(
                @"  SELECT	kv.ProviderDriverKeyValueId, kv.DriverId, kv.ProviderDriverKeyId, kv.Value, pk.IntegrationProviderId
		            FROM	Integration.ProviderDriverKeyValues kv
	    		            INNER JOIN Integration.ProviderDriverKeys pk on pk.ProviderDriverKeyId=kv.ProviderDriverKeyId
		            WHERE   (DriverId IN @DriverIds AND pk.IntegrationProviderId=@ProviderId AND kv.ProviderDriverKeyId IN @KeyIds)",
                new
                {
                    @DriverIds = batch,
                    @ProviderId = providerId,
                    @KeyIds = keyIds
                })));
            }

            return ret.ToCollection();
        }

        /// <summary>
        /// Retrieves all the key values for the specified provider and companyId.
        /// </summary>
        public static Collection<DriverKeyValue> GetByCompany(int companyId, int providerId)
        {
            return GetByCompany(companyId).Where(o => o.providerId == providerId).ToCollection();
        }

        /// <summary>
        /// Retrieves all they key values for the specified driver under the provider/company specified.
        /// </summary>
        /// <param name="companyId"></param>
        /// <param name="providerId"></param>
        /// <param name="driverId"></param>
        /// <returns></returns>
        public static Collection<DriverKeyValue> GetByCompany(int companyId, int providerId, int driverId)
        {
            return GetByCompany(companyId).Where(o => o.providerId == providerId && o.DriverId == driverId).ToCollection();
        }

        public static Collection<DriverKeyValue> GetByDriver(int companyId, int DriverId)
        {
            return GetByCompany(companyId).Where(o => o.DriverId == DriverId).ToCollection();
        }

        public void Save()
        {
            if (this.Id == 0)
            {
                // don't save null. 
                if (this.Value == null)
                    return;

                this.Id = Convert.ToInt32(SqlMapper.QuerySP<dynamic>("Integration.ProviderDriverKeyValuesInsert",
                    new
                    {
                        @DriverId = this.DriverId,
                        @ProviderDriverKeyId = this.KeyId,
                        @Value = this.Value
                    }).FirstOrDefault().Id);
            }
            else
            {
                SqlMapper.ExecuteSP("Integration.ProviderDriverKeyValuesUpdateById",
                    new
                    {
                        @ProviderDriverKeyValueId = this.Id,
                        @DriverId = this.DriverId,
                        @ProviderDriverKeyId = this.KeyId,
                        @Value = this.Value
                    });

                AppServices.Cache.InvalidateCacheItem(string.Format(CacheDriverFormat, Id));
            }

            AppServices.Cache.InvalidateCacheItem(string.Format(CacheDriverCompanyFormat, Driver.GetById(DriverId).CompanyId));
            
        }

        public void Delete()
        {
            SqlMapper.ExecuteSP("Integration.ProviderDriverKeyValuesDeleteById",
                new { @ProviderDriverKeyValueId = this.Id });
        }

        private static Collection<DriverKeyValue> Map(IEnumerable<dynamic> list)
        {
            return list.Select(o => new DriverKeyValue()
            {
                Id = o.ProviderDriverKeyValueId,
                DriverId = o.DriverId,
                KeyId = o.ProviderDriverKeyId,
                Value = o.Value,

                providerId = o.IntegrationProviderId
            }).ToCollection();
        }
    }
}
