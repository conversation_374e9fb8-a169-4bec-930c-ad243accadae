using Extric.Towbook.Integration.Helpers;
using Extric.Towbook.Utility;
using Glav.CacheAdapter.Core.DependencyInjection;
using ProtoBuf;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;

namespace Extric.Towbook.Integration
{
    [ProtoContract]
    public class DriverKey : IKey
    {
        private const string CacheKeyAll = "ik_driver_all";

        [ProtoMember(1)]
        public int Id { get; private set; }
        [ProtoMember(2)]
        public int ProviderId { get; set; }
        [ProtoMember(3)]
        public string Name { get; set; }

        /// <summary>
        /// Retrieve a list of all DriverKeys in the system.
        /// </summary>
        /// <returns>Returns a Collection of all the providers available.</returns>
        public static Collection<DriverKey> GetAll()
        {
            return AppServices.Cache.Get(CacheKeyAll, TimeSpan.FromDays(30), () =>
            {
                return new DriverKeyCollection(Map(SqlMapper.QuerySP<dynamic>("Integration.ProviderDriverKeysGetAll")));
            }).Collection;
        }

        /// <summary>
        /// Asynchronous method that retrieves a list of all DriverKeys in the system.
        /// </summary>
        /// <returns>Returns a Collection of all the providers available.</returns>
        public static async Task<Collection<DriverKey>> GetAllAsync()
        {
            return (await AppServices.Cache.GetAsync(CacheKeyAll, TimeSpan.FromDays(30), async () =>
            {
                Console.WriteLine("Retrieving DriverKeys from database");
                var queryResult = await SqlMapper.QuerySpAsync<dynamic>("Integration.ProviderDriverKeysGetAll");
                return new DriverKeyCollection(Map(queryResult));
            })).Collection;
        }

        /// <summary>
        /// Retrieve a list of all of the DriverKeys available for the specified ProviderId
        /// </summary>
        /// <param name="providerId">The provider to return keys for</param>
        /// <returns>Returns a Collection of all the providers available.</returns>
        public static Collection<DriverKey> GetByProviderId(int providerId)
        {
            return GetAll().Where(o => o.ProviderId == providerId).ToCollection();
        }

        /// <summary>
        /// Asynchronous method that retrieves a list of all of the DriverKeys available for 
        /// the specified ProviderId
        /// </summary>
        /// <param name="providerId">The provider to return keys for</param>
        /// <returns>Returns a Collection of all the providers available.</returns>
        public static async Task<Collection<DriverKey>> GetByProviderIdAsync(int providerId)
        {
            return (await GetAllAsync()).Where(o => o.ProviderId == providerId).ToCollection();
        }

        /// <summary>
        /// Return a Key with the specified Name for the specified provider.
        /// </summary>
        public static DriverKey GetByProviderId(int providerId, string name)
        {
            return GetByProviderId(providerId)
                .FirstOrDefault(n => n.Name.ToLowerInvariant() == name.ToLowerInvariant());
        }

        /// <summary>
        /// Asynchronous method that returns a Key with the specified Name for the 
        /// specified provider.
        /// </summary>
        public static async Task<DriverKey> GetByProviderIdAsync(int providerId, string name)
        {
            return (await GetByProviderIdAsync(providerId))
                .FirstOrDefault(n => n.Name.ToLowerInvariant() == name.ToLowerInvariant());
        }


        /// <summary>
        /// Helper method to map the SQL columns to the respective object properties. 
        /// </summary>
        private static Collection<DriverKey> Map(IEnumerable<dynamic> list)
        {
            return list.Select(o => new DriverKey()
            {
                Id = o.ProviderDriverKeyId,
                ProviderId = o.IntegrationProviderId,
                Name = o.Name,
            }).ToCollection();
        }

        public DriverKey Save()
        {
            this.Id = Convert.ToInt32(SqlMapper.QuerySP<dynamic>("Integration.ProviderDriverKeysInsert",
                    new
                    {
                        @IntegrationProviderId = this.ProviderId,
                        @Name = this.Name,
                    }).FirstOrDefault().Id);

            AppServices.Cache.InvalidateCacheItem(CacheKeyAll);

            return this;
        }
    }
}
