using Extric.Towbook.Utility;
using ProtoBuf;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Extric.Towbook.Integration
{
    [Table("dbo.MotorClubTaskErrors")]
    public class MotorClubTaskError
    {
        [Key]
        public int MotorClubErrorId { get; protected set; }
        public int MotorClubTaskId { get; set; }
        public string Error { get; set; }
        public DateTime CreateDate { get; set; }

        public void Save()
        {
            if (this.MotorClubErrorId == 0)
            {
                this.MotorClubErrorId = Convert.ToInt32(SqlMapper.Insert<MotorClubTaskError>(this));
            }
            else
            {
                SqlMapper.Update<MotorClubTaskError>(this);
            }
        }
    }
}
