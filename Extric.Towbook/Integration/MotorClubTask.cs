using Extric.Towbook.Utility;
using ProtoBuf;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Extric.Towbook.Integration
{
    [ProtoContract]
    public class MotorClubTask
    {
        public int Id { get; protected set; }
        public MotorClubTaskType Type { get; set; }
        public string Username { get; set; }
        public string Password { get; set; }
        public int CompanyId { get; set; }
        public int AccountId { get; set; }
        public MotorClubTaskStatus Status { get; set; }
        public DateTime StatusDate { get; set; }

        public static IEnumerable<MotorClubTask> GetNext(int rowCount = 1)
        {
            return new MotorClubTaskCollection(Map(SqlMapper.QuerySP<dynamic>("dbo.MotorClubTasksGetNext", new
            {
                @RowCount = rowCount
            }))).Collection;
        }

        public static IEnumerable<MotorClubTask> GetByCompanyId(int companyId)
        {
            return new MotorClubTaskCollection(Map(SqlMapper.Query<dynamic>("select * from motorclubtasks where companyid=@c", new
            {
                @c= companyId
            }))).Collection;
        }

        public void UpdateStatus(MotorClubTaskStatus status, string error = null)
        {
            SqlMapper.ExecuteSP("dbo.MotorClubTasksUpdateStatus", new
                {
                    @MotorClubTaskId = this.Id,
                    @Status = status,
                    @Error = error
                });
        }

        private static IEnumerable<MotorClubTask> Map(IEnumerable<dynamic> list)
        {
            return list.Select(o => new MotorClubTask()
            {
                Id = o.MotorClubTaskId,
                AccountId = o.AccountId,
                CompanyId = o.CompanyId,
                Type = (MotorClubTaskType)o.Type,
                Status = (MotorClubTaskStatus)o.Status,
                Username = o.Username,
                Password = o.Password,
                StatusDate = o.StatusDate
            }).ToCollection();
        }
    }

    [ProtoContract]
    public class MotorClubTaskCollection
    {
        [ProtoMember(1)]
        public Collection<MotorClubTask> Collection { get; set; }

        public MotorClubTaskCollection() { }

        public MotorClubTaskCollection(IEnumerable<MotorClubTask> list)
        {
            Collection = list.ToCollection();
        }
    }

    public enum MotorClubTaskType
    {
        None = 0,
        CAA = 4,
        AAANorthway = 5,
        AAAWp = 6,
        AAAPioneerValley = 7,

        D3Colorado = 8,
        AAANewYork = 9,
        D3SouthernPennsylvania = 10,

        Sykes = 11,

        AAARenew = 12,
        CAANeo = 13,
        CAASk = 14,
        CaaAtlantic = 15
    }

    public enum MotorClubTaskStatus
    {
        None = 0,
        Idle = 1,
        Queued = 2,
        Processing = 3,
        Processed = 4,
        Ignored = 5,
        Error = 6
    }
}
