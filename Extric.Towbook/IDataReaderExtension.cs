using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data;
using System.Globalization;

namespace Extric.Towbook
{

    public static class IDataReaderExtensions
    {
        public static T GetValue<T>(this IDataRecord dr, string columnName) where T : IConvertible
        {
            T retValue = default(T);

            object obj = null;
            try
            {
                obj = dr.GetValue(dr.GetOrdinal(columnName));
            }
            catch (IndexOutOfRangeException)
            {
                obj = null;
            }


            if (obj != null && obj != System.DBNull.Value)
                retValue = (T)Convert.ChangeType(obj, typeof(T), CultureInfo.InvariantCulture);

            return retValue;
        }

        /// <summary>
        /// This method will return the value of the specified columnName, cast to 
        /// the type specified in T. However, if the value found in the reader is 
        /// DBNull, this method will return null.
        /// Additionally, if the column doesn't exist, null will be returned also.
        /// </summary>
        /// <typeparam name="T">The type to which the value found in the reader should be cast.</typeparam>
        /// <param name="dr">The reader in which columnName is found.</param>
        /// <param name="columnName">The columnName to retrieve.</param>
        /// <returns>The column value within the reader typed as T, or null if the column doesn't exist.</returns>
        public static T? GetValueOrNull<T>(this IDataRecord dr, string columnName) where T : struct
        {
            return GetValueOrNull<T>(dr, columnName, false);
        }

        public static T? GetValueOrNull<T>(this IDataRecord dr, string columnName, bool treatZeroAsDefault) where T : struct
        {
            T retValue = default(T);

            object obj = null;
            try
            {
                obj = dr.GetValue(dr.GetOrdinal(columnName));
            }
            catch (IndexOutOfRangeException)
            {
                obj = null;
            }

            if (obj != null && obj != System.DBNull.Value)
                retValue = (T)Convert.ChangeType(obj, typeof(T), CultureInfo.InvariantCulture);

            if (retValue.Equals(default(T)))
            {
                if (treatZeroAsDefault && obj != System.DBNull.Value)
                    return retValue;
                else
                    return new Nullable<T>();
            }
            else
                return retValue;
        }
        

        public static Guid GetGuid(this IDataRecord dr, string columnName)
        {
            object obj = dr.GetValue(dr.GetOrdinal(columnName));

            if (obj != null && obj != System.DBNull.Value)
                return (Guid)obj;
            else
                return Guid.Empty;
        }

        /// <summary>
        /// This method will return the value of the specified columnName, cast to 
        /// the type specified in T. However, if the value found in the reader is 
        /// DBNull, this method will return the default value of the type T. 
        /// Additionally, if the column doesn't exist, null will be returned also.
        /// </summary>
        /// <typeparam name="T">The type to which the value found in the reader should be cast.</typeparam>
        /// <param name="reader">The reader in which columnName is found.</param>
        /// <param name="columnName">The columnName to retrieve.</param>
        /// <returns>The column value within the reader typed as T.</returns>
        public static T GetValueOrDefault<T>(this IDataReader reader, string columnName)
        {
            object columnValue = null;

            try
            {
                columnValue = reader[columnName];
            }
            catch (IndexOutOfRangeException)
            {
                columnValue = DBNull.Value;
            }

            T returnValue = default(T);
            if (!(columnValue is DBNull))
            {
                returnValue = (T)Convert.ChangeType(columnValue, typeof(T));
            }
            return returnValue;
        } 



    }
}
