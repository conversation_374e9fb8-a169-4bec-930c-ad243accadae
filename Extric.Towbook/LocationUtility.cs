using Extric.Towbook.Utility;
using System.Collections.Concurrent;
using System.Linq;

namespace Extric.Towbook
{
    public class LocationUtility
    {
        public static ConcurrentDictionary<string, string> cities = new ConcurrentDictionary<string, string>();

        public class LocationLookupResult
        {
            public string ZipCode { get; set; }
            public string City { get; set; }
            public string State { get; set; }
            public string Latitude { get; set; }
            public string Longitude { get; set; }
        }

        public static string GetCityByZip(string zip)
        {
            if (string.IsNullOrWhiteSpace(zip))
                return null;

            // maybe make a method that return sboth City and Zip.
            // make sure this uses caching.

            if (cities.TryGetValue(zip, out var city))
            {
                return city;
            }
            else
            {
                var zc = SqlMapper.QuerySP<LocationLookupResult>("Geo.GetCityByZip", new { @Zip = zip }).FirstOrDefault();
                if (zc != null)
                {
                    cities.TryAdd(zip, zc.City);
                    return zc.City;
                }
            }

            return null;
        }

        public static string GetStateByZip(string zip)
        {
            if (zip == null || zip.Length < 5)
                return null;

            if (zip.Substring(0, 5) == "48079")
                return "Michigan";

            return null;
        }
    }
}
