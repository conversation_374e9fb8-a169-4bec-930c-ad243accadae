using Extric.Towbook.Utility;
using Glav.CacheAdapter.Core.DependencyInjection;
using System;
using System.Collections.Generic;

namespace Extric.Towbook.PreTripInspections
{
    [Table("PreTripInspectionItemCategoryInclusions")]
    public class PreTripInspectionItemCategoryInclusion
    {
        private const string cacheFormat = "pticategoryinclusions-all";

        [Key("PreTripInspectionItemCategoryInclusionId")]
        public int Id { get; set; }
        public int CategoryId { get; set; }
        public int TruckTypeId { get; set; }

        public static IEnumerable<PreTripInspectionItemCategoryInclusion> GetByCategoryIds(int[] categoryIds)
        {
            return AppServices.Cache.Get(cacheFormat, TimeSpan.FromDays(30), () =>
            {
                return new CacheCollection<PreTripInspectionItemCategoryInclusion>(
                    SqlMapper.Query<PreTripInspectionItemCategoryInclusion>(
                        "SELECT * FROM PreTripInspectionItemCategoryInclusions WHERE CategoryId in @CategoryIds", new { CategoryIds = categoryIds }));
            }).Items;
        }

        public static IEnumerable<PreTripInspectionItemCategoryInclusion> GetByTruckTypeIds(int[] truckTypeIds)
        {
            return SqlMapper.Query<PreTripInspectionItemCategoryInclusion>(
                    "SELECT * FROM PreTripInspectionItemCategoryInclusions WHERE TruckTypeId in @TruckTypeIds", new { TruckTypeIds = truckTypeIds });
        }
    }

    
    
}
