using Extric.Towbook.Utility;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;

namespace Extric.Towbook.PreTripInspections
{
    public class PreTripInspection : IUsesSqlKey
    {
        [Key("PreTripInspectionId")]
        public int Id { get; set; }
        public int CompanyId { get; set; }
        public int OwnerUserId { get; set; }
        public int TruckId { get; set; }
        public int? Odometer { get; set; }
        public string Notes { get; set; }

        public decimal Latitude { get; set; }
        public decimal Longitude { get; set; }

        public int? PreTripInspectionItemTypeDetailId { get; set; }

        [Write(false)]
        public Collection<PreTripInspectionItem> Items { get; set; } = new Collection<PreTripInspectionItem>();

        [Write(false)]
        public Collection<PreTripInspectionPhoto> Photos { get; set; } = new Collection<PreTripInspectionPhoto>();

        [Write(false)]
        public int UncheckedCount { get; set; }

        public DateTime InspectionDate { get; set; }

        public static PreTripInspection GetById(int id)
        {
            var o = SqlMapper.Query<PreTripInspection>(
                @"SELECT * FROM PreTripInspections WHERE PreTripInspectionId = @Id",
                new { Id = id }).FirstOrDefault();

            if (o == null)
                return null;

            o.Items = PreTripInspectionItem.Get(id);
            o.Photos = PreTripInspectionPhoto.Get(id);

            return o;
        }

        public static async Task<PreTripInspection> GetByIdAsync(int id)
        {
            var o = (await SqlMapper.QueryAsync<PreTripInspection>(
                @"SELECT * FROM PreTripInspections WHERE PreTripInspectionId = @Id",
                new { Id = id })).FirstOrDefault();

            if (o == null)
                return null;

            o.Items = await PreTripInspectionItem.GetAsync(id);
            o.Photos = await PreTripInspectionPhoto.GetAsync(id);

            return o;
        }

        public static IEnumerable<PreTripInspection> Search(int[] companyIds, DateTime startDate, DateTime endDate, int? truckId, int? ownerUserId)
        {
            var o = SqlMapper.Query<PreTripInspection>(@"
                SELECT pt.*,
                      UncheckedCount = (SELECT COUNT(1) FROM PreTripInspectionItems WHERE PreTripInspectionId = pt.PreTripInspectionId AND Checked = 0)
                FROM PreTripInspections pt WITH (nolock)
                WHERE CompanyId IN @CompanyIds AND
                      InspectionDate BETWEEN @StartDate AND @EndDate AND
                      TruckId = COALESCE(@TruckId, TruckId) AND
                      OwnerUserId = COALESCE(@OwnerUserId, OwnerUserId)
                ORDER BY InspectionDate DESC",
                new
                {
                    StartDate = startDate,
                    EndDate = endDate,
                    TruckId = truckId,
                    OwnerUserId = ownerUserId,
                    CompanyIds = companyIds
                });

            return o;
        }

        public static async Task<IEnumerable<PreTripInspection>> SearchAsync(int[] companyIds, DateTime startDate, DateTime endDate, int? truckId, int? ownerUserId)
        {
            var o = await SqlMapper.QueryAsync<PreTripInspection>(@"
                SELECT pt.*,
                      UncheckedCount = (SELECT COUNT(1) FROM PreTripInspectionItems WHERE PreTripInspectionId = pt.PreTripInspectionId AND Checked = 0)
                FROM PreTripInspections pt WITH (nolock)
                WHERE CompanyId IN @CompanyIds AND
                      InspectionDate BETWEEN @StartDate AND @EndDate AND
                      TruckId = COALESCE(@TruckId, TruckId) AND
                      OwnerUserId = COALESCE(@OwnerUserId, OwnerUserId)
                ORDER BY InspectionDate DESC",
                new
                {
                    StartDate = startDate,
                    EndDate = endDate,
                    TruckId = truckId,
                    OwnerUserId = ownerUserId,
                    CompanyIds = companyIds
                });

            return o;
        }

        public void Save()
        {
            using (var cx = Core.CreateTransactionScope())
            {
                Id = (int)SqlMapper.Insert(this);
                
                PreTripInspectionItem.Save(Id, Items);

                cx.Complete();
            }
        }

        public void Delete()
        {
            using (var cx = Core.CreateTransactionScope())
            {
                PreTripInspectionItem.Delete(Id);
                PreTripInspectionPhoto.Delete(Id);

                SqlMapper.Delete(this);

                cx.Complete();
            }
        }
    }
}
