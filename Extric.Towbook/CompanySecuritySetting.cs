using System.ComponentModel.DataAnnotations;

namespace Extric.Towbook
{
    using System;
    using System.Collections.ObjectModel;
    using System.Data.SqlClient;
    using System.Linq;
    using System.Text.RegularExpressions;
    using System.Threading.Tasks;
    using Glav.CacheAdapter.Core.DependencyInjection;
    using ProtoBuf;
    using Utility;
    using static Extric.Towbook.User;

    /// <summary>
    /// Company Security Settings.
    /// </summary>
    [Serializable]
    [ProtoContract(ImplicitFields = ImplicitFields.AllFields, Name = "CompanySecuritySetting")]
    [CacheKey("_company_security_setting_all")]
    public partial class CompanySecuritySetting
    {
        private const int CacheTimeout = 10;
        private SsoProvider _ssoProvider;

        #region Fields
        public int Id { get; set; } = -1;
        public int CompanyId { get; set; }
        public int ProviderId { get; set; }

        [Required]
        public string CompanyDomain { get; set; }

        public int SessionTimeOut { get; set; } = 1440;

        public bool SsoEnable { get; set; } = false;

        public TypeEnum DefaultUserType { get; set; } = TypeEnum.Dispatcher;

        [Required]
        public string MetadataURL { get; set; }

        [Required]
        public string Idp { get; set; }

        public SsoProvider SsoProvider => GetSsoProvider();

        public bool CreateNewUsersAsDisabled { get; set; }
        public string Secret { get; set; }

        #endregion

        public CompanySecuritySetting(int companyId)
        {
            CompanyId = companyId;
            if (companyId == 1953)
            {
                SessionTimeOut = 10;
            }
        }

        internal CompanySecuritySetting(SqlDataReader reader)
        {
            InitializeFromDataReader(reader);
            AppServices.Cache.Add("companySecuritySetting:" + Id, TimeSpan.FromMinutes(CacheTimeout), this);
            AppServices.Cache.Add("companySecuritySettingByCompanyDomain:" + CompanyDomain, TimeSpan.FromMinutes(CacheTimeout), this);
            AppServices.Cache.Add("companySecuritySettingByCompanyId:" + CompanyId, TimeSpan.FromMinutes(CacheTimeout), this);
        }


        private void InitializeFromDataReader(SqlDataReader reader)
        {
            Id = Convert.ToInt32(reader["CompanySecuritySettingId"]);
            CompanyId = Convert.ToInt32(reader["CompanyId"]);
            ProviderId = Convert.ToInt32(reader["SSOProviderId"]);

            if (reader["CompanyDomain"] != DBNull.Value)
                CompanyDomain = Convert.ToString(reader["CompanyDomain"]);

            if (reader["SsoEnable"] != DBNull.Value)
                SsoEnable = Convert.ToBoolean(reader["SsoEnable"]);

            if (reader["DefaultUserType"] != DBNull.Value)
                DefaultUserType = (TypeEnum)Convert.ToInt32(reader["DefaultUserType"]);

            if (reader["MetadataURL"] != DBNull.Value)
                MetadataURL = Convert.ToString(reader["MetadataURL"]);

            if (reader["Idp"] != DBNull.Value)
                Idp = Convert.ToString(reader["Idp"]);

            if (reader["Secret"] != DBNull.Value)
                Secret = Convert.ToString(reader["Secret"]);

            //if (reader["CreateNewUsersAsDisabled"] != DBNull.Value)
            //    Idp = Convert.ToString(reader["CreateNewUsersAsDisabled"]);
        }



        private void DbInsert()
        {
            Id = SqlMapper.ExecuteSP("CompanySecuritySettingsInsert",
                new
                {
                    @CompanyId = CompanyId,
                    @SSOProviderId = ProviderId,
                    @CompanyDomain = CompanyDomain,
                    @SessionTimeOut = SessionTimeOut,
                    @SsoEnable = SsoEnable,
                    @DefaultUserType = DefaultUserType,
                    @MetadataURL = MetadataURL,
                    @Idp = Idp,
                    @Secret = Secret
                });
        }

        private void DbUpdate()
        {
            SqlMapper.ExecuteSP("CompanySecuritySettingsUpdateById",
                new
                {
                    @CompanySecuritySettingId = Id,
                    @SSOProviderId = ProviderId,
                    @CompanyDomain = CompanyDomain,
                    @SessionTimeOut = SessionTimeOut,
                    @SsoEnable = SsoEnable,
                    @DefaultUserType = DefaultUserType,
                    @MetadataURL = MetadataURL,
                    @Idp = Idp,
                    @Secret = Secret
                });
        }

        /// <summary>
        /// Saves the User object to the data store.
        /// </summary>
        public void Save()
        {
            try
            {
                if (Id == 0)
                {
                    throw new ApplicationException("No such CompanySecuritySetting. Can't save " +
                        "object! (this object should have already been discarded!)");
                }
                if (String.IsNullOrWhiteSpace(CompanyDomain))
                {
                    throw new ArgumentException("Valid Company Domain is a required field.");
                }
                if (Regex.IsMatch(CompanyDomain, @"[^\w.\-]+"))
                {
                    throw new ArgumentException("Valid Company Domain contains only Alphanumeric characters and - . or _.");
                }

                if (SessionTimeOut < 5)
                {
                    throw new ArgumentException("Valid greater or equal than 5 minutes Session Timeout is required.");
                }

                if (SsoEnable)
                {
                    if (String.IsNullOrWhiteSpace(MetadataURL))
                    {
                        throw new ArgumentException("Metadata URL is a required field.");
                    }

                    if (String.IsNullOrWhiteSpace(Idp))
                    {
                        throw new ArgumentException("Identity Provider is a required field.");
                    }

                    //if ("SAML".Equals(SsoType) )
                    //{
                    //    if (String.IsNullOrWhiteSpace(SamlLoginURL))
                    //    {
                    //        throw new ArgumentException("SAML Login URL is a required field.");
                    //    } 
                    //    else
                    //    {
                    //        if (!ValidateUrl(SamlLoginURL))
                    //        {
                    //            throw new ArgumentException("SAML Login URL is not a valid URL.");
                    //        }
                    //    }
                    //    if (String.IsNullOrWhiteSpace(Certificate))
                    //    {
                    //        throw new ArgumentException("Certificate is a required field.");
                    //    }
                    //    else
                    //    {
                    //        if (!ValidateCertificate(Certificate))
                    //        {
                    //            throw new ArgumentException("Certificate is not valid.");
                    //        }
                    //    }
                    //    if (!String.IsNullOrWhiteSpace(SamlLogoutURL))
                    //    {
                    //        if (!ValidateUrl(SamlLogoutURL))
                    //        {
                    //            throw new ArgumentException("SAML Logout URL is not a valid URL.");
                    //        }
                    //    }
                    //}
                }

                if (Id == -1)
                {
                    if (GetByCompanyDomain(CompanyDomain) != null)
                    {
                        throw new TowbookException("Attempted to use a company domain that is already taken: " + CompanyDomain);
                    }

                    DbInsert();
                }
                else
                {
                    var otherCompanySetting = GetByCompanyDomain(CompanyDomain);
                    if (otherCompanySetting != null && !(otherCompanySetting.Id == Id))
                    {
                        throw new TowbookException("Attempted to use a company domain that is already taken: " + CompanyDomain);
                    }
                    DbUpdate();
                }
            }
            finally
            {
                AppServices.Cache.InvalidateCacheItem("companySecuritySetting:" + Id);
                AppServices.Cache.InvalidateCacheItem("companySecuritySettingByCompanyDomain:" + CompanyDomain);
                AppServices.Cache.InvalidateCacheItem("companySecuritySettingByCompanyId:" + CompanyId);
            }
        }

        /// <summary>
        /// Returns a list of all active user accounts (doesn't return deleted ones)
        /// </summary>
        public static async Task<Collection<CompanySecuritySetting>> GetAll()
        {
            var companySecuritySettings = new Collection<CompanySecuritySetting>();

            using (SqlDataReader dr = await SqlHelper.ExecuteReaderAsync(Core.ConnectionString,
                "CompanySecuritySettingsGetAll"))
            {
                while (await dr.ReadAsync())
                {
                    companySecuritySettings.Add(new CompanySecuritySetting(dr));
                }
            }
            return companySecuritySettings;
        }

        public static async Task<Collection<CompanySecuritySetting>> GetAllSsoEnabled()
        {
            var companySecuritySettings = await GetAll();
            if (companySecuritySettings.Count > 0)
            {
                return companySecuritySettings.Where(s => s.SsoEnable).ToCollection();
            }

            return companySecuritySettings;
        }

        public static async Task<Collection<CompanySecuritySetting>> GetEnabledSsoByType(string ssoType)
        {
            var companySecuritySettings = await GetAll();
            if (companySecuritySettings.Count > 0)
            {
                return companySecuritySettings.Where(s => s.SsoProvider.SsoType == ssoType && s.SsoEnable).ToCollection();
            }

            return companySecuritySettings;
        }

        public static CompanySecuritySetting GetById(int id)
        {
            return AppServices.Cache.Get<CompanySecuritySetting>("companySecuritySetting:" + id, TimeSpan.FromMinutes(CacheTimeout), () =>
            {
                System.Diagnostics.Debug.WriteLine("CompanySecuritySetting {0} not cached, retrieving from database", id);

                using (SqlDataReader dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                    "CompanySecuritySettingsGetById", new SqlParameter("@CompanySecuritySettingId", id)))
                {
                    if (dr.Read())
                    {
                        return new CompanySecuritySetting(dr);
                    }
                    else
                    {
                        return null;
                    }
                }
            });
        }

        public static CompanySecuritySetting GetByCompanyDomain(String companyDomain)
        {
            return AppServices.Cache.Get<CompanySecuritySetting>("companySecuritySettingsByCompanyDomain:" + companyDomain, TimeSpan.FromMinutes(CacheTimeout), () =>
            {
                System.Diagnostics.Debug.WriteLine("companySecuritySettingByCompanyDomain {0} not cached, retrieving from database", companyDomain);
                using (SqlDataReader dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                "CompanySecuritySettingsGetByCompanyDomain", new SqlParameter("@CompanyDomain", companyDomain)))
                {
                    if (dr.Read())
                    {
                        return new CompanySecuritySetting(dr);
                    }
                    else
                    {
                        return null;
                    }
                }
            });
        }
        public static bool CheckDomainExists(string companyDomain, int id = -1)
        {
            return Convert.ToBoolean(SqlHelper.ExecuteScalar(Core.ConnectionString,
                "CompanySecuritySettingsCheckDomainExists",
                    new SqlParameter("@CompanyDomain", companyDomain),
                    new SqlParameter("@Id", id)));
        }

        public static CompanySecuritySetting GetByCompanyId(int companyId)
        {
            return AppServices.Cache.Get<CompanySecuritySetting>("companySecuritySettingByCompanyId:" + companyId, TimeSpan.FromMinutes(CacheTimeout), () =>
            {
                System.Diagnostics.Debug.WriteLine("companySecuritySettingByCompanyId {0} not cached, retrieving from database", companyId);
                using (SqlDataReader dr = SqlHelper.ExecuteReader(Core.ConnectionString,
                "CompanySecuritySettingsGetByCompanyId", new SqlParameter("@CompanyId", companyId)))
                {
                    if (dr.Read())
                    {
                        return new CompanySecuritySetting(dr);
                    }
                    else
                    {
                        return null;
                    }
                }
            });
        }

        public static string GetDomain(string email)
        {
            if (string.IsNullOrWhiteSpace(email))
                return null;

            if (Uri.TryCreate($"mailto:{email.Trim()}", UriKind.Absolute, out var result))
                return result.Host;

            return null;
        }

        private SsoProvider GetSsoProvider()
        {
            if (_ssoProvider != null)
            {
                return _ssoProvider;
            }
            _ssoProvider = SsoProvider.GetById(ProviderId);
            return _ssoProvider;
        }

        private async Task<SsoProvider> GetSsoProviderAsync()
        {
            if (_ssoProvider != null)
            {
                return _ssoProvider;
            }

            _ssoProvider = await SsoProvider.GetByIdAsync(ProviderId);
            return _ssoProvider;
        }

    }
}
