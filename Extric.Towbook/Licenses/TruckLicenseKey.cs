using Extric.Towbook.Integration.Helpers;
using Extric.Towbook.Utility;
using Glav.CacheAdapter.Core.DependencyInjection;
using ProtoBuf;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using c = Extric.Towbook.Company;

namespace Extric.Towbook.Licenses
{
    [ProtoContract]
    public class TruckLicenseKeyCollection
    {
        [ProtoMember(1)]
        public Collection<TruckLicenseKey> Collection { get; set; }

        public TruckLicenseKeyCollection() { }

        public TruckLicenseKeyCollection(IEnumerable<TruckLicenseKey> list)
        {
            Collection = list.ToCollection();
        }
    }

    [ProtoContract]
    public class TruckLicenseKey : ILicenseKey
    {
        private const string CacheKeyAll = "lk_truck_all";

        [ProtoMember(1)]
        public int Id { get; set; }
        
        [ProtoMember(2)]
        public string Name { get; set; }
        
        [ProtoMember(3)]
        public bool Required { get; set; }

        [ProtoMember(4)]
        public bool ShowValueOnPrintables { get; set; }
        
        [ProtoMember(5)]
        public bool ShowExpirationOnPrintables { get; set; } 

        /// <summary>
        /// Retrieve a list of all Truck keys in the system.
        /// </summary>
        /// <returns>Returns a Collection of all the providers available.</returns>
        public static Collection<TruckLicenseKey> GetAll()
        {
            return AppServices.Cache.Get<TruckLicenseKeyCollection>(CacheKeyAll, TimeSpan.FromDays(30), () =>
            {
                return new TruckLicenseKeyCollection(Map(SqlMapper.QuerySP<dynamic>("dbo.TruckLicenseKeysGetAll")));
            }).Collection;
        }

        /// <summary>
        /// Helper method to map the SQL columns to the respective object properties. 
        /// </summary>
        private static Collection<TruckLicenseKey> Map(IEnumerable<dynamic> list)
        {
            return list.Select(o => new TruckLicenseKey()
            {
                Id = o.TruckLicenseKeyId,
                Name = o.Name,
                Required = o.Required,
                ShowValueOnPrintables = o.ShowValueOnPrintables,
                ShowExpirationOnPrintables = o.ShowExpirationOnPrintables
            }).ToCollection();
        }

        public TruckLicenseKey Save()
        {
            this.Id = Convert.ToInt32(SqlMapper.QuerySP<dynamic>("Integration.ProviderTruckLicenseKeysInsert",
                new
                {
                    @Name = this.Name,
                    @Required = this.Required,
                    @ShowValueOnPrintables = this.ShowValueOnPrintables,
                    @ShowExpirationOnPrintables = this.ShowExpirationOnPrintables
                }).FirstOrDefault().Id);

            AppServices.Cache.InvalidateCacheItem(CacheKeyAll);

            return this;
        }

        /// <summary>
        /// Retrieve a list of all key/values in the system for CompanyLicense.
        /// </summary>
        /// <returns>Returns a Collection of all the key/vlaues available.</returns>
        public static Collection<TruckLicenseKey> GetLicenceKeyData(c.Company comp)
        {
            var allAssociation = SqlMapper.QuerySP<dynamic>("dbo.TruckLicenseKeyAssociationsGetAll");

            var idValueAssociation = allAssociation.Where(w => (w.Field == "State" && w.Value == comp.State)
                || (w.Field == "Zip" && w.Value == comp.Zip)
                || (w.Field == "Country" && w.Value == comp.Country.ToString())
                ).Select(s =>
                {
                    return new KeyValuePair<int, string>(
                        s.TruckLicenseKeyId, s.Value);
                }).ToList();

            var result = new Collection<TruckLicenseKey>();
            var allTrucksKeys = GetAll();
            foreach (var x in idValueAssociation)
            {
                if (!result.Where(w => w.Id == x.Key).Any())
                {
                    //Query all the CompanyLicenseKeys where CompanyLicenceKeyId == KeyId
                    result.Add(allTrucksKeys.Where(w => w.Id == x.Key).Single());
                }
            }

            return result;
        }
        

    }
}