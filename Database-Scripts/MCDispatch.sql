/*************************************************************************************************
 ** Allstate
 *************************************************************************************************/

 IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='AllstateMessages' AND TABLE_SCHEMA='MCDispatch')
	CREATE TABLE MCDispatch.AllstateMessages (
		AllstateMessageId int IDENTITY(1,1) NOT NULL,
		ContractorId varchar(50) NULL,
		DispatchRequestNumber varchar(50) NULL,
		PurchaseOrderNumber varchar(50) NULL,
		Title varchar(50) NULL,
		[Message] varchar(max) NULL,
		[Type] int NULL,
		CreateDate datetime DEFAULT(getdate())

		CONSTRAINT PK_AllstateMessages PRIMARY KEY CLUSTERED (AllstateMessageId ASC)
	)
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='AllstateContractors' AND TABLE_SCHEMA='MCDispatch')
	CREATE TABLE MCDispatch.AllstateContractors (
		AllstateContractorId int IDENTITY(1,1) NOT NULL,
		CompanyId int NOT NULL,
		ContractorId varchar(255) NOT NULL,
		IsLoggedIn bit NOT NULL DEFAULT((0)),
		LastLoginDate datetime NULL,
		AccountId int NOT NULL,
		LoginStatus int NOT NULL,
		IsDeleted bit NOT NULL DEFAULT((0)),
		LocationCode varchar(50) NULL,
		CreateDate datetime DEFAULT(getdate())

		CONSTRAINT PK_AllstateContractors PRIMARY KEY CLUSTERED (AllstateContractorId ASC),
		CONSTRAINT FK_AllstateContractors_Accounts FOREIGN KEY(AccountId) REFERENCES dbo.Accounts (AccountId),
		CONSTRAINT FK_AllstateContractors_Companies FOREIGN KEY(CompanyId) REFERENCES dbo.Companies (CompanyId)
	)
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='AllstateDispatches' AND TABLE_SCHEMA='MCDispatch')
	CREATE TABLE MCDispatch.AllstateDispatches (
		AllstateDispatchId int IDENTITY(1,1) NOT NULL,
		ContractorId varchar(50) NULL,
		CallXml varchar(max) NOT NULL,
		ETA int NULL,
		ETAReason int NOT NULL DEFAULT((0)),
		CallRequestId int NOT NULL,
		DispatchId varchar(50) NOT NULL,
		PurchaseOrderNumber varchar(50) NULL,
		CreateDate datetime DEFAULT(getdate())

		CONSTRAINT PK_AllstateDispatches PRIMARY KEY CLUSTERED (AllstateDispatchId ASC),
		CONSTRAINT FK_AllstateDispatches_DispatchEntryRequests FOREIGN KEY(CallRequestId) REFERENCES dbo.DispatchEntryRequests (CallRequestId)
	)
GO

grant select on MCDispatch.AllstateMessages to public
grant update on MCDispatch.AllstateMessages to public
grant insert on MCDispatch.AllstateMessages to public

grant select on MCDispatch.AllstateContractors to public
grant update on MCDispatch.AllstateContractors to public
grant insert on MCDispatch.AllstateContractors to public

grant select on MCDispatch.AllstateDispatches to public
grant update on MCDispatch.AllstateDispatches to public
grant insert on MCDispatch.AllstateDispatches to public


IF NOT EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='LoginStatuses' AND TABLE_SCHEMA='MCDispatch')
BEGIN
	CREATE TABLE MCDispatch.LoginStatuses (
		LoginStatusId int  NOT NULL,
		Name varchar(50) NOT NULL

		CONSTRAINT [PK_LoginStatuses] PRIMARY KEY CLUSTERED  ( LoginStatusId ASC ),
		CONSTRAINT [UX_LoginStatuses_Name] UNIQUE ( Name )
	)

	INSERT INTO MCDispatch.LoginStatuses (LoginStatusId, Name)
	VALUES 
	(0, 'None'),
	(1, 'Logged Out'),
	(2, 'Logging In'),
	(3, 'Logged In'),
	(4, 'LoggingOut')
END
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='AllstateDispatches' AND COLUMN_NAME = 'CreateDate')
	ALTER TABLE MCDispatch.AllstateDispatches ADD CreateDate datetime DEFAULT(getdate())
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='AllstateMessages' AND COLUMN_NAME = 'CreateDate')
	ALTER TABLE MCDispatch.AllstateMessages ADD CreateDate datetime DEFAULT(getdate())
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='AllstateMessages' AND COLUMN_NAME = 'Direction')
	ALTER TABLE MCDispatch.AllstateMessages ADD Direction int DEFAULT(2)
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='AllstateContractors' AND COLUMN_NAME = 'MasterAccountId')
	ALTER TABLE MCDispatch.AllstateContractors ADD MasterAccountId int NOT NULL DEFAULT(2) -- default to Allstate.
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='AllstateMessages' AND COLUMN_NAME = 'MasterAccountId')
	ALTER TABLE MCDispatch.AllstateMessages ADD MasterAccountId int NOT NULL DEFAULT(2) -- default to Allstate.
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='AllstateDispatches' AND COLUMN_NAME = 'MasterAccountId')
	ALTER TABLE MCDispatch.AllstateDispatches ADD MasterAccountId int NOT NULL DEFAULT(2) -- default to Allstate.
GO


IF OBJECT_ID('[MCDispatch].[vwIsscGpsBreadcrumbExport]') IS NULL EXEC ('CREATE VIEW [MCDispatch].[vwIsscGpsBreadcrumbExport] AS SELECT 1 as Temp')
GO
ALTER view [MCDispatch].[vwIsscGpsBreadcrumbExport] as      
select D.DispatchEntryId, ISC.DispatchId, ISC.ContractorId, ISC.ClientId, ISC.LocationId,  D.DriverId, ULC.Latitude, ULC.Longitude, ULC.Timestamp from DispatchEntryRequests DR WITH (nolock)     
 INNER JOIN DispatchEntries D with (nolock) on D.DispatchEntryId=DR.DispatchEntryId and D.Status > 1 AND D.Status < 5      
 INNER JOIN MCDispatch.IsscDispatches ISC with (nolock) on ISC.CallRequestId=DR.CallRequestId      
 INNER JOIN Drivers DRV WITH (nolock) on DRV.DriverId=D.DriverId      
 INNER JOIN UserLocationsCurrent ULC WITH (nolock) on ULC.UserId=DRV.UserId      
 where DR.CreateDate > DATEADD(hour, -4, getdate()) and DR.status=1 and DR.CompanyId not in (4550,6304,9783,9760,2887)       
 and dr.CallRequestId > IDENT_CURRENT('DispatchEntryRequests')-500000      
 and ulc.Timestamp > dateadd(second, -60, getdate())      

GO


IF OBJECT_ID('[MCDispatch].[vwIsscProviders]') IS NULL EXEC ('CREATE VIEW [MCDispatch].[vwIsscProviders] AS SELECT 1 as Temp')
GO
ALTER view [MCDispatch].[vwIsscProviders] as   
select P.*, LS.Name as LoginStatusName, C.State, C.Name as Company, C.Phone as Phone from mcdispatch.IsscProviders P  
 inner join companies C on C.companyid=p.CompanyId  
 left outer join MCDispatch.LoginStatuses LS on LS.LoginStatusId=P.LoginStatus  
 where P.IsDeleted=0
GO
