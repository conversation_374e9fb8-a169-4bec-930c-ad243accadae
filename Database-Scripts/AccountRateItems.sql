

IF OBJECT_ID('dbo.AccountRateItemsGetByCompanyId') IS NULL EXEC('CREATE PROCEDURE [dbo].[AccountRateItemsGetByCompanyId]  AS SET NOCOUNT ON;')
GO

ALTER PROCEDURE [dbo].[AccountRateItemsGetByCompanyId]
(
	@CompanyId int
)
AS
	SET NOCOUNT ON

	SELECT ARI.*, A.CompanyId FROM AccountRateItems  ARI
		INNER JOIN Accounts A ON A.AccountId=ARi.AccountId AND A.CompanyId=@CompanyId
		WHERE ARI.Deleted=0

	GO


GRANT EXECUTE ON [AccountRateItemsGetByCompanyId] TO public



IF OBJECT_ID('dbo.AccountRateItemsGetById') IS NULL EXEC('CREATE PROCEDURE [dbo].[AccountRateItemsGetById]  AS SET NOCOUNT ON;')
GO

ALTER PROCEDURE [dbo].[AccountRateItemsGetById] (
	@AccountRateItemId int
) AS
	SET NOCOUNT ON
	SELECT ARI.*, A.CompanyId FROM AccountRateItems ARI
		INNER JOIN Accounts A on A.AccountId=ARI.AccountId
	WHERE AccountRateItemId=@AccountRateItemId
GO


IF OBJECT_ID('dbo.AccountRateItemsGetByRateItemId') IS NULL EXEC('CREATE PROCEDURE [dbo].[AccountRateItemsGetByRateItemId]  AS SET NOCOUNT ON;')
GO

ALTER PROCEDURE [dbo].[AccountRateItemsGetByRateItemId] (
	@AccountId int,
	@RateItemId int
) AS
	SET NOCOUNT ON
	IF (@AccountId is null)
		SELECT ARI.*, A.CompanyId 
			FROM AccountRateItems ARI
				INNER JOIN Accounts A on A.AccountId=ARI.AccountId AND A.Deleted=0
			WHERE RateItemId=@RateItemId AND ARI.Deleted=0
	ELSE
		SELECT ARI.*, A.CompanyId 
			FROM AccountRateItems ARI 
				INNER JOIN Accounts A on A.AccountId=ARI.AccountId AND A.Deleted=0
				INNER JOIN RateItems RI on RI.RateItemId=ARi.RateItemId 
			WHERE 
				ARI.AccountId=@AccountId AND
				ARI.Deleted=0 AND
				RI.Deleted=0 AND
				ARI.RateItemId=@RateItemId 
GO


IF OBJECT_ID('dbo.AccountRateItemsGetPredefinedByAccountId') IS NULL EXEC('CREATE PROCEDURE [dbo].[AccountRateItemsGetPredefinedByAccountId]  AS SET NOCOUNT ON;')
GO

ALTER PROCEDURE [dbo].[AccountRateItemsGetPredefinedByAccountId] (
	@AccountId int
) AS
	SET NOCOUNT ON
	SELECT ARI.*, A.CompanyId 
		FROM AccountRateItems ARI
			INNER JOIN Accounts A on A.AccountId=ARI.AccountId
		WHERE RateItemId IN 
			(SELECT RateItemId FROM RateItems WHERE RateItemPredefinedId IS NOT NULL AND Deleted=0) AND ARI.AccountId=@AccountId AND ARI.Deleted=0
GO
	
IF OBJECT_ID('dbo.AccountRateItemsGetPredefinedRateItemByAccountId') IS NULL EXEC('CREATE PROCEDURE [dbo].[AccountRateItemsGetPredefinedRateItemByAccountId]  AS SET NOCOUNT ON;')
GO

ALTER PROCEDURE [dbo].[AccountRateItemsGetPredefinedRateItemByAccountId] (
	@AccountId int,
	@PredefinedRateItemId int
) AS
	SET NOCOUNT ON
	SELECT ARI.*, A.CompanyId 
		FROM AccountRateItems ARI
			INNER JOIN Accounts A on A.AccountId=ARI.AccountId
		WHERE 
			RateItemId IN (SELECT RateItemId FROM RateItems WHERE RateItemPredefinedId=@PredefinedRateItemId AND Deleted=0) AND ARI.AccountId=@AccountId AND ARI.Deleted=0

GO



IF OBJECT_ID('dbo.AccountRateItemsExtendedGetByRateItemId') IS NULL EXEC('CREATE PROCEDURE [dbo].[AccountRateItemsExtendedGetByRateItemId]  AS SET NOCOUNT ON;')
GO

ALTER PROCEDURE [dbo].[AccountRateItemsExtendedGetByRateItemId] (
	@RateItemId int,
	@AccountId int
)
AS
	SELECT ARIE.*, A.CompanyId FROM AccountRateItemsExtended ARIE
		INNER JOIN Accounts A on A.AccountId=ARIE.AccountID
	WHERE 
		RateItemId=@RateItemId AND 
		ARIE.AccountId=@AccountId
GO


IF OBJECT_ID('dbo.AccountRateItemsExtendedGetByCompanyId') IS NULL EXEC('CREATE PROCEDURE [dbo].[AccountRateItemsExtendedGetByCompanyId]  AS SET NOCOUNT ON;')
GO

ALTER PROCEDURE [dbo].[AccountRateItemsExtendedGetByCompanyId] (
	@CompanyId int
)
AS
	SELECT ARIE.*, A.CompanyId FROM AccountRateItemsExtended ARIE
		INNER JOIN Accounts A on A.AccountId=ARIE.AccountID
	WHERE 
		A.CompanyId=@CompanyId
GO


GRANT EXECUTE ON dbo.AccountRateItemsExtendedGetByCompanyId TO public