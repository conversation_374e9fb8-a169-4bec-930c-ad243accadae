
IF OBJECT_ID('[dbo].[AccountParkingPermits]') IS NULL 
	CREATE TABLE [dbo].[AccountParkingPermits]
	(
		[ParkingPermitId] INT NOT NULL PRIMARY KEY IDENTITY, 
		[AccountId] INT NOT NULL,
		[StatusId] INT NOT NULL,
		[AccountContactId] INT, 
		[LicensePlate] VARCHAR(10) NOT NULL, 
		[LicensePlateState] VARCHAR(50) NULL,
		[VehicleMake] VARCHAR(50) NULL, 
		[VehicleModel] VARCHAR(50) NULL, 
		[VehicleColor] VARCHAR(50) NULL, 
		[FullName] VARCHAR(50) NULL, 
		[Address] VARCHAR(100) NULL, 
		[City] VARCHAR(32) NULL, 
		[State] VARCHAR(32) NULL, 
		[Zip] VARCHAR(50) NULL, 
		[Email] VARCHAR(100) NULL, 
		[EmailVerified] BIT NOT NULL DEFAULT 0, 
		[CellPhone] VARCHAR(32) NULL, 
		[CellPhoneVerified] BIT NOT NULL DEFAULT 0, 
		[AltPhone] VARCHAR(32) NULL, 
		[CreateDate] DATETIME NOT NULL DEFAULT getdate(), 
		[OwnerUserId] INT NOT NULL, 
		[ExpirationDate] DATETIME NULL, 
		[Notes] VARCHAR(100) NULL,
		[Deleted] BIT DEFAULT 0
	)
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='AccountParkingPermits' AND COLUMN_NAME = 'VIN')
	ALTER TABLE AccountParkingPermits ADD VIN varchar(17) NULL
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='AccountParkingPermits' AND COLUMN_NAME = 'ParkingPermitListId')
	ALTER TABLE AccountParkingPermits ADD ParkingPermitListId int null
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='AccountParkingPermits' AND COLUMN_NAME = 'VehicleYear')
	ALTER TABLE AccountParkingPermits ADD VehicleYear int null
GO


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='AccountParkingPermits' AND COLUMN_NAME = 'UnitNumber')
	ALTER TABLE AccountParkingPermits ADD UnitNumber varchar(10)
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='AccountParkingPermits' AND COLUMN_NAME = 'CustomPermitNumber')
	ALTER TABLE AccountParkingPermits ADD CustomPermitNumber varchar(20)
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='AccountParkingPermits' AND COLUMN_NAME = 'LeaseEndDate')
	ALTER TABLE AccountParkingPermits ADD LeaseEndDate DateTime NULL
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='AccountParkingPermits' AND COLUMN_NAME = 'StatusId')
	ALTER TABLE AccountParkingPermits ADD StatusId INT NULL
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='AccountParkingPermits' AND COLUMN_NAME = 'ParkingSpaceNumber')
	ALTER TABLE AccountParkingPermits ADD ParkingSpaceNumber varchar(20)
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='AccountParkingPermits' AND COLUMN_NAME = 'IsHandicap')
	ALTER TABLE AccountParkingPermits ADD IsHandicap BIT DEFAULT(0)
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='AccountParkingPermits' AND COLUMN_NAME = 'DecalColorId')
	ALTER TABLE AccountParkingPermits ADD DecalColorId INT
GO


IF OBJECT_ID('dbo.AccountParkingPermitDecalColors') IS NULL 
	CREATE TABLE [dbo].[AccountParkingPermitDecalColors]
	(
		DecalColorId int identity(1,1) not null,
		Name varchar(50), 
		Description varchar(255),
		CompanyId int,
		CreateDate datetime,
		OwnerUserId int default(1),
		IsDeleted bit default(0)

		CONSTRAINT [PK_AccountParkingPermitDecalColors] PRIMARY KEY CLUSTERED (DecalColorId ASC)
	)
GO


IF (NOT EXISTS (SELECT 1 FROM AccountParkingPermitDecalColors WHERE DecalColorId=1))
BEGIN
    SET IDENTITY_INSERT AccountParkingPermitDecalColors ON
        INSERT INTO AccountParkingPermitDecalColors (DecalColorId, Name, Description, CompanyId, CreateDate, OwnerUserId, IsDeleted) VALUES (1, 'Blue', '', NULL, GETDATE(), 1, 0)
    SET IDENTITY_INSERT AccountParkingPermitDecalColors OFF
END

IF (NOT EXISTS (SELECT 1 FROM AccountParkingPermitDecalColors WHERE DecalColorId=2))
BEGIN
    SET IDENTITY_INSERT AccountParkingPermitDecalColors ON
        INSERT INTO AccountParkingPermitDecalColors (DecalColorId, Name, Description, CompanyId, CreateDate, OwnerUserId, IsDeleted) VALUES (2, 'Red', '', NULL, GETDATE(), 1, 0)
    SET IDENTITY_INSERT AccountParkingPermitDecalColors OFF
END

IF (NOT EXISTS (SELECT 1 FROM AccountParkingPermitDecalColors WHERE DecalColorId=3))
BEGIN
    SET IDENTITY_INSERT AccountParkingPermitDecalColors ON
        INSERT INTO AccountParkingPermitDecalColors (DecalColorId, Name, Description, CompanyId, CreateDate, OwnerUserId, IsDeleted) VALUES (3, 'Orange', '', NULL, GETDATE(), 1, 0)
    SET IDENTITY_INSERT AccountParkingPermitDecalColors OFF
END

IF (NOT EXISTS (SELECT 1 FROM AccountParkingPermitDecalColors WHERE DecalColorId=4))
BEGIN
    SET IDENTITY_INSERT AccountParkingPermitDecalColors ON
        INSERT INTO AccountParkingPermitDecalColors (DecalColorId, Name, Description, CompanyId, CreateDate, OwnerUserId, IsDeleted) VALUES (4, 'Green', '', NULL, GETDATE(), 1, 0)
    SET IDENTITY_INSERT AccountParkingPermitDecalColors OFF
END

IF (NOT EXISTS (SELECT 1 FROM AccountParkingPermitDecalColors WHERE DecalColorId=5))
BEGIN
    SET IDENTITY_INSERT AccountParkingPermitDecalColors ON
        INSERT INTO AccountParkingPermitDecalColors (DecalColorId, Name, Description, CompanyId, CreateDate, OwnerUserId, IsDeleted) VALUES (5, 'Yellow', '', NULL, GETDATE(), 1, 0)
    SET IDENTITY_INSERT AccountParkingPermitDecalColors OFF
END

IF (NOT EXISTS (SELECT 1 FROM AccountParkingPermitDecalColors WHERE DecalColorId=6))
BEGIN
    SET IDENTITY_INSERT AccountParkingPermitDecalColors ON
        INSERT INTO AccountParkingPermitDecalColors (DecalColorId, Name, Description, CompanyId, CreateDate, OwnerUserId, IsDeleted) VALUES (6, 'White', '', NULL, GETDATE(), 1, 0)
    SET IDENTITY_INSERT AccountParkingPermitDecalColors OFF
END

IF (NOT EXISTS (SELECT 1 FROM AccountParkingPermitDecalColors WHERE DecalColorId=7))
BEGIN
    SET IDENTITY_INSERT AccountParkingPermitDecalColors ON
        INSERT INTO AccountParkingPermitDecalColors (DecalColorId, Name, Description, CompanyId, CreateDate, OwnerUserId, IsDeleted) VALUES (7, 'Black', '', NULL, GETDATE(), 1, 0)
    SET IDENTITY_INSERT AccountParkingPermitDecalColors OFF
END

IF (NOT EXISTS (SELECT 1 FROM AccountParkingPermitDecalColors WHERE DecalColorId=8))
BEGIN
    SET IDENTITY_INSERT AccountParkingPermitDecalColors ON
        INSERT INTO AccountParkingPermitDecalColors (DecalColorId, Name, Description, CompanyId, CreateDate, OwnerUserId, IsDeleted) VALUES (8, 'Brown', '', NULL, GETDATE(), 1, 0)
    SET IDENTITY_INSERT AccountParkingPermitDecalColors OFF
END


IF OBJECT_ID('dbo.AccountParkingPermitLists') IS NULL 
	CREATE TABLE [dbo].[AccountParkingPermitLists]
	(
		ParkingPermitListId int identity(1,1) not null,
		Name varchar(50), 
		Description varchar(255),
		CompanyId int,
		CreateDate datetime,
		OwnerUserId int default(1),
		IsDeleted bit default(0) not null

		CONSTRAINT [PK_AccountParkingPermitLists] PRIMARY KEY CLUSTERED (ParkingPermitListId ASC)
	)
GO

IF (NOT EXISTS (SELECT 1 FROM AccountParkingPermitLists WHERE ParkingPermitListId=1))
BEGIN
    SET IDENTITY_INSERT AccountParkingPermitLists ON
        INSERT INTO AccountParkingPermitLists (ParkingPermitListId, Name, Description, CompanyId, CreateDate, OwnerUserId, IsDeleted) VALUES (1, 'Residents', 'Don''t tow vehicles on this list.', NULL, GETDATE(), 1, 0)
    SET IDENTITY_INSERT AccountParkingPermitLists OFF
END

IF (NOT EXISTS (SELECT 1 FROM AccountParkingPermitLists WHERE ParkingPermitListId=2))
BEGIN
    SET IDENTITY_INSERT AccountParkingPermitLists ON
        INSERT INTO AccountParkingPermitLists (ParkingPermitListId, Name, Description, CompanyId, CreateDate, OwnerUserId, IsDeleted) VALUES (2, 'Management', 'Don''t tow vehicles on this list.', NULL, GETDATE(), 1, 0)
    SET IDENTITY_INSERT AccountParkingPermitLists OFF
END

IF (NOT EXISTS (SELECT 1 FROM AccountParkingPermitLists WHERE ParkingPermitListId=3))
BEGIN
    SET IDENTITY_INSERT AccountParkingPermitLists ON
        INSERT INTO AccountParkingPermitLists (ParkingPermitListId, Name, Description, CompanyId, CreateDate, OwnerUserId, IsDeleted) VALUES (3, 'Do Not Tow', '', NULL, GETDATE(), 1, 0)
    SET IDENTITY_INSERT AccountParkingPermitLists OFF
END

IF (NOT EXISTS (SELECT 1 FROM AccountParkingPermitLists WHERE ParkingPermitListId=4))
BEGIN
    SET IDENTITY_INSERT AccountParkingPermitLists ON
        INSERT INTO AccountParkingPermitLists (ParkingPermitListId, Name, Description, CompanyId, CreateDate, OwnerUserId, IsDeleted) VALUES (4, 'Temporary', '', NULL, GETDATE(), 1, 0)
    SET IDENTITY_INSERT AccountParkingPermitLists OFF
END

IF (NOT EXISTS (SELECT 1 FROM AccountParkingPermitLists WHERE ParkingPermitListId=5))
BEGIN
    SET IDENTITY_INSERT AccountParkingPermitLists ON
        INSERT INTO AccountParkingPermitLists (ParkingPermitListId, Name, Description, CompanyId, CreateDate, OwnerUserId, IsDeleted) VALUES (5, 'Visitor', '', NULL, GETDATE(), 1, 0)
    SET IDENTITY_INSERT AccountParkingPermitLists OFF
END

IF (NOT EXISTS (SELECT 1 FROM AccountParkingPermitLists WHERE ParkingPermitListId=6))
BEGIN
    SET IDENTITY_INSERT AccountParkingPermitLists ON
        INSERT INTO AccountParkingPermitLists (ParkingPermitListId, Name, Description, CompanyId, CreateDate, OwnerUserId, IsDeleted) VALUES (6, 'Contractor', '', NULL, GETDATE(), 1, 0)
    SET IDENTITY_INSERT AccountParkingPermitLists OFF
END




IF OBJECT_ID('dbo.AccountParkingPermitsInsert') IS NULL EXEC('CREATE PROCEDURE dbo.AccountParkingPermitsInsert AS SET NOCOUNT ON;')
GO

ALTER PROCEDURE AccountParkingPermitsInsert (
	@AccountId int, 
	@StatusId int,
	@AccountContactId int, 
	@ParkingPermitListId int,
	@LicensePlate varchar(10), 
	@LicensePlateState varchar(50), 
	@VehicleYear int,
	@VehicleMake varchar(50), 
	@VehicleModel varchar(50), 
	@VehicleColor varchar(50), 
	@VIN varchar(17),
	@FullName varchar(50), 
	@Address varchar(100), 
	@City varchar(32), 
	@State varchar(32), 
	@Zip varchar(50), 
	@Email varchar(100), 
	@EmailVerified bit, 
	@CellPhone varchar(32), 
	@CellPhoneVerified bit, 
	@AltPhone varchar(32), 
	@OwnerUserId int, 
	@ExpirationDate datetime, 
    @LeaseEndDate datetime,
	@Notes varchar(100),
	@UnitNumber varchar(10),
	@CustomPermitNumber varchar(20),
    @ParkingSpaceNumber varchar(20),
    @IsHandicap bit,
    @DecalColorId int
) AS 
	
INSERT INTO AccountParkingPermits (
	AccountId,
	StatusId,
	AccountContactId,
	ParkingPermitListId,
	LicensePlate,
	LicensePlateState,
	VehicleYear, 
	VehicleMake,
	VehicleModel,
	VehicleColor,
	VIN, 
	FullName,
	Address,
	City,
	State,
	Zip,
	Email,
	EmailVerified,
	CellPhone,
	CellPhoneVerified,
	AltPhone,
	OwnerUserId,
	ExpirationDate,
    LeaseEndDate,
	Notes,
	UnitNumber,
	CustomPermitNumber,
    ParkingSpaceNumber,
    IsHandicap,
    DecalColorId
) VALUES (
	@AccountId,
	@StatusId,
	@AccountContactId,
	@ParkingPermitListId,
	@LicensePlate,
	@LicensePlateState,
	@VehicleYear,
	@VehicleMake,
	@VehicleModel,
	@VehicleColor,
	@VIN,
	@FullName,
	@Address,
	@City,
	@State,
	@Zip,
	@Email,
	@EmailVerified,
	@CellPhone,
	@CellPhoneVerified,
	@AltPhone,
	@OwnerUserId,
	@ExpirationDate,
    @LeaseEndDate,
	@Notes,
	@UnitNumber,
	@CustomPermitNumber,
    @ParkingSpaceNumber,
    @IsHandicap,
    @DecalColorId
)

SELECT ParkingPermitID as Id, CreateDate FROM AccountParkingPermits WHERE ParkingPermitID=@@IDENTITY
GO

IF OBJECT_ID('dbo.AccountParkingPermitsUpdateById') IS NULL EXEC('CREATE PROCEDURE dbo.AccountParkingPermitsUpdateById AS SET NOCOUNT ON;')
GO
ALTER PROCEDURE AccountParkingPermitsUpdateById (
	@ParkingPermitId int, 
	@StatusId int,
	@AccountId int, 
	@AccountContactId int, 
	@ParkingPermitListId int,
	@LicensePlate varchar(10), 
	@LicensePlateState varchar(50), 
	@VehicleYear int,
	@VehicleMake varchar(50), 
	@VehicleModel varchar(50), 
	@VehicleColor varchar(50), 
	@VIN varchar(17),
	@FullName varchar(50), 
	@Address varchar(100), 
	@City varchar(32), 
	@State varchar(32), 
	@Zip varchar(50), 
	@Email varchar(100), 
	@EmailVerified bit, 
	@CellPhone varchar(32), 
	@CellPhoneVerified bit, 
	@AltPhone varchar(32), 
	@OwnerUserId int, 
	@ExpirationDate datetime,
    @LeaseEndDate datetime,
	@Notes varchar(100),
	@UnitNumber varchar(10),
	@CustomPermitNumber varchar(20),
    @ParkingSpaceNumber varchar(20),
    @IsHandicap bit,
    @DecalColorId int
) AS 
	
UPDATE AccountParkingPermits SET 
	AccountId=@AccountId,
	StatusId=@StatusId,
	AccountContactId=@AccountContactId,
	ParkingPermitListId=@ParkingPermitListId,
	LicensePlate=@LicensePlate,
	LicensePlateState=@LicensePlateState,
	VehicleYear=@VehicleYear,
	VehicleMake=@VehicleMake,
	VehicleModel=@VehicleModel,
	VehicleColor=@VehicleColor,
	VIN=@VIN,
	FullName=@FullName,
	Address=@Address,
	City=@City,
	State=@State,
	Zip=@Zip,
	Email=@Email,
	EmailVerified=@EmailVerified,
	CellPhone=@CellPhone,
	CellPhoneVerified=@CellPhoneVerified,
	AltPhone=@AltPhone,
	OwnerUserId=@OwnerUserId,
	ExpirationDate=@ExpirationDate,
    LeaseEndDate=@LeaseEndDate,
	Notes=@Notes,
	UnitNumber=@UnitNumber,
	CustomPermitNumber=@CustomPermitNumber,
    ParkingSpaceNumber = @ParkingSpaceNumber,
    IsHandicap = @IsHandicap,
    DecalColorId = @DecalColorId
 WHERE ParkingPermitId=@ParkingPermitId
 
GO
IF OBJECT_ID('dbo.AccountParkingPermitsGetById') IS NULL EXEC('CREATE PROCEDURE dbo.AccountParkingPermitsGetById  AS SET NOCOUNT ON;')
GO

ALTER PROCEDURE dbo.[AccountParkingPermitsGetById] (
	@ParkingPermitId int
) AS
	SELECT * FROM AccountParkingPermits WHERE ParkingPermitId=@ParkingPermitId
GO

IF OBJECT_ID('dbo.AccountParkingPermitsGetByAccountId') IS NULL EXEC('CREATE PROCEDURE dbo.AccountParkingPermitsGetByAccountId  AS SET NOCOUNT ON;')
GO

ALTER PROCEDURE dbo.[AccountParkingPermitsGetByAccountId] (
	@AccountId int
) AS
	SELECT * FROM AccountParkingPermits WHERE 
        AccountId=@AccountId AND 
        Deleted=0 AND 
        Coalesce(StatusId, 0) in (0,1,2,3) -- Valid, Expired, Revoked, Extended
GO


IF OBJECT_ID('dbo.AccountParkingPermitsGetByListId') IS NULL EXEC('CREATE PROCEDURE dbo.AccountParkingPermitsGetByListId  AS SET NOCOUNT ON;')
GO

ALTER PROCEDURE dbo.[AccountParkingPermitsGetByListId] (
	@ListId  int,
	@AccountId int
) AS
	SELECT * FROM AccountParkingPermits WHERE AccountId=@AccountId AND ParkingPermitListId=@ListId AND Deleted=0
GO

IF OBJECT_ID('dbo.AccountParkingPermitsGetByLicensePlate') IS NULL EXEC('CREATE PROCEDURE dbo.AccountParkingPermitsGetByLicensePlate  AS SET NOCOUNT ON;')
GO

ALTER PROCEDURE dbo.[AccountParkingPermitsGetByLicensePlate] (
 @AccountId int,    
 @LicensePlate varchar(10)    
) AS    
 SELECT * FROM AccountParkingPermits   
 WHERE 
	AccountId=@AccountId AND 
	LicensePlate=@LicensePlate AND 
	Deleted=0
 ORDER by StatusId ASC
GO


IF OBJECT_ID('dbo.AccountParkingPermitsGetByCustomNumber') IS NULL EXEC('CREATE PROCEDURE dbo.AccountParkingPermitsGetByCustomNumber AS SET NOCOUNT ON;')
GO

ALTER PROCEDURE dbo.[AccountParkingPermitsGetByCustomNumber] (
	@AccountId int,
	@CustomPermitNumber varchar(20)
) AS
	SELECT * FROM AccountParkingPermits WHERE AccountId=@AccountId AND CustomPermitNumber=@CustomPermitNumber AND Deleted=0 AND Coalesce(StatusId, 0) <> -1
GO

	
IF OBJECT_ID('dbo.AccountParkingPermitsDeleteById') IS NULL EXEC('CREATE PROCEDURE dbo.AccountParkingPermitsDeleteById  AS SET NOCOUNT ON;')
GO

ALTER PROCEDURE dbo.[AccountParkingPermitsDeleteById] (
	@ParkingPermitId int
) AS
	UPDATE AccountParkingPermits 
	SET Deleted=1 WHERE ParkingPermitId=@ParkingPermitId
GO

GRANT EXECUTE ON dbo.AccountParkingPermitsDeleteById TO public
GRANT EXECUTE ON dbo.AccountParkingPermitsInsert TO public
GRANT EXECUTE ON dbo.AccountParkingPermitsUpdateById TO public
GRANT EXECUTE ON dbo.AccountParkingPermitsGetById TO public
GRANT EXECUTE ON dbo.AccountParkingPermitsGetByAccountId TO public
GRANT EXECUTE ON dbo.AccountParkingPermitsGetByLicensePlate TO public

GRANT SELECT ON AccountParkingPermitLists TO public
GRANT SELECT ON AccountParkingPermits TO public

GRANT EXEC ON dbo.AccountParkingPermitsGetByListId to public

GRANT SELECT ON AccountParkingPermitDecalColors to public







IF OBJECT_ID('[dbo].[AccountParkingPermitRequests]') IS NULL 
	CREATE TABLE [dbo].[AccountParkingPermitRequests]
	(
		[ParkingPermitRequestId] INT NOT NULL PRIMARY KEY IDENTITY,
        [SessionId] INT NULL,
		[AccountId] INT NOT NULL,
        [CustomPermitNumber] varchar(20) NULL,
        [UnitNumber] varchar(10) NULL,
		[FullName] VARCHAR(50) NULL, 
        [CellPhone] VARCHAR(32) NOT NULL, 
		[CellPhoneVerified] BIT NOT NULL DEFAULT 0, 		
        [Email] VARCHAR(100) NULL, 
		[EmailVerified] BIT NOT NULL DEFAULT 0, 
		[CreateDate] DATETIME NOT NULL DEFAULT getdate(), 
		[OwnerUserId] INT NOT NULL, 
        [LastActivity] DATETIME NULL,
		[Deleted] BIT DEFAULT 0,
        [DeletedByUserId] INT NULL,
        [DeletedDate] DATETIME NULL
        
	)
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='AccountParkingPermitRequests' AND COLUMN_NAME = 'URL')
	ALTER TABLE AccountParkingPermitRequests ADD [URL] varchar(50) NULL
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='AccountParkingPermitRequests' AND COLUMN_NAME = 'Code')
	ALTER TABLE AccountParkingPermitRequests ADD Code varchar(10) NULL
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='AccountParkingPermitRequests' AND COLUMN_NAME = 'DeletedDate')
	ALTER TABLE AccountParkingPermitRequests ADD DeletedDate DateTime NULL
GO

IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='AccountParkingPermitRequests' AND COLUMN_NAME = 'ParkingPermitId')
	EXEC sp_rename 'AccountParkingPermitRequests.ParkingPermitId', 'SessionId', 'COLUMN';
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='AccountParkingPermitRequests' AND COLUMN_NAME = 'ResendCount')
	ALTER TABLE AccountParkingPermitRequests ADD ResendCount Int NULL
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='AccountParkingPermitRequests' AND COLUMN_NAME = 'PermitUserId')
	ALTER TABLE AccountParkingPermitRequests ADD PermitUserId Int NULL
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='AccountParkingPermitRequests' AND COLUMN_NAME = 'PermitUserTypeId')
	ALTER TABLE AccountParkingPermitRequests ADD PermitUserTypeId Int NULL
GO



IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS WHERE CONSTRAINT_NAME='FK_AccountParkingPermitRequests_UserId')
BEGIN
    ALTER TABLE
        AccountParkingPermitRequests 
    ADD CONSTRAINT 
        [FK_AccountParkingPermitRequests_UserId] FOREIGN KEY ([PermitUserId]) REFERENCES PPIO.PermitUsers (UserId)
END



IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS WHERE CONSTRAINT_NAME='FK_AccountParkingPermitRequests_AccountId')
BEGIN
    ALTER TABLE
        AccountParkingPermitRequests 
    ADD CONSTRAINT 
        [FK_AccountParkingPermitRequests_AccountId] FOREIGN KEY ([AccountId]) REFERENCES dbo.Accounts (AccountId)
END



IF OBJECT_ID('dbo.AccountParkingPermitRequestsGetByAccountId') IS NULL EXEC('CREATE PROCEDURE dbo.AccountParkingPermitRequestsGetByAccountId  AS SET NOCOUNT ON;')
GO

ALTER PROCEDURE dbo.AccountParkingPermitRequestsGetByAccountId (
	@AccountId int,
	@PageNumber int,
	@PageSize int
) AS
	SELECT * FROM 
        AccountParkingPermitRequests 
    WHERE 
        AccountId=@AccountId AND 
        Deleted=0
    ORDER BY
        ParkingPermitRequestId DESC
    OFFSET (@PageSize * @PageNumber)-@PageSize ROWS FETCH NEXT @PageSize ROWS ONLY;
GO


IF OBJECT_ID('dbo.AccountParkingPermitRequestsGetById') IS NULL EXEC('CREATE PROCEDURE dbo.AccountParkingPermitRequestsGetById  AS SET NOCOUNT ON;')
GO

ALTER PROCEDURE dbo.AccountParkingPermitRequestsGetById (
	@ParkingPermitRequestId int
) AS
	SELECT * FROM AccountParkingPermitRequests WHERE ParkingPermitRequestId=@ParkingPermitRequestId
GO




IF OBJECT_ID('dbo.AccountParkingPermitRequestsInsert') IS NULL EXEC('CREATE PROCEDURE dbo.AccountParkingPermitRequestsInsert AS SET NOCOUNT ON;')
GO

ALTER PROCEDURE AccountParkingPermitRequestsInsert (
	@AccountId int,
	@UnitNumber varchar(10),
	@CustomPermitNumber varchar(20),
	@FullName varchar(50), 
	@Email varchar(100), 
	@CellPhone varchar(32), 
	@OwnerUserId int,
    @UserTypeId int,
    @UserId int,
    @Url varchar(50),
	@Code varchar(10)
) AS 
	
INSERT INTO AccountParkingPermitRequests (
	AccountId,
	FullName,
	Email,
	CellPhone,
	OwnerUserId,
	UnitNumber,
	CustomPermitNumber,
    PermitUserTypeId,
    PermitUserId,
    [Url],
    Code
) VALUES (
	@AccountId,
	@FullName,
	@Email,
	@CellPhone,
	@OwnerUserId,
	@UnitNumber,
	@CustomPermitNumber,
    @UserTypeId,
    @UserId,
    @Url,
    @Code
)

SELECT ParkingPermitRequestId as Id, CreateDate FROM AccountParkingPermitRequests WHERE ParkingPermitRequestId=@@IDENTITY
GO


IF OBJECT_ID('dbo.AccountParkingPermitRequestsUpdateById') IS NULL EXEC('CREATE PROCEDURE dbo.AccountParkingPermitRequestsUpdateById AS SET NOCOUNT ON;')
GO
ALTER PROCEDURE AccountParkingPermitRequestsUpdateById (
    @ParkingPermitRequestId int,
	@SessionId int,
	@AccountId int, 
	@CustomPermitNumber varchar(20),
	@UnitNumber varchar(10),
	@FullName varchar(50), 
	@Email varchar(100), 
	@EmailVerified bit, 
	@CellPhone varchar(32), 
	@CellPhoneVerified bit, 
	@OwnerUserId int, 
	@LastActivity datetime, 
	@Url varchar(50),
	@Code varchar(10),
    @ResendCount int,
    @UserId int,
    @UserTypeId int
) AS 
	
UPDATE AccountParkingPermitRequests SET 
	SessionId=@SessionId,
    AccountId=@AccountId,
	UnitNumber=@UnitNumber,
	CustomPermitNumber=@CustomPermitNumber,
	FullName=@FullName,
	Email=@Email,
	EmailVerified=@EmailVerified,
	CellPhone=@CellPhone,
	CellPhoneVerified=@CellPhoneVerified,
	OwnerUserId=@OwnerUserId,
	LastActivity=@LastActivity,
	[Url]=@Url,
    Code=@Code,
    ResendCount=@ResendCount,
    PermitUserId = @UserId,
    PermitUserTypeId = @UserTypeId
 WHERE ParkingPermitRequestId=@ParkingPermitRequestId
 
GO




IF OBJECT_ID('dbo.AccountParkingPermitRequestsDeleteById') IS NULL EXEC('CREATE PROCEDURE dbo.AccountParkingPermitRequestsDeleteById  AS SET NOCOUNT ON;')
GO

ALTER PROCEDURE dbo.[AccountParkingPermitRequestsDeleteById] (
	@ParkingPermitRequestId int,
	@DeletedByUserId int,
    @DeletedDate datetime
) AS
	UPDATE AccountParkingPermitRequests 
	SET Deleted=1, DeletedByUserId=@DeletedByUserId, DeletedDate=@DeletedDate WHERE ParkingPermitRequestId=@ParkingPermitRequestId
GO

GRANT EXECUTE ON dbo.AccountParkingPermitRequestsGetByAccountId TO public
GRANT EXECUTE ON dbo.AccountParkingPermitRequestsGetById TO public

GRANT EXECUTE ON dbo.AccountParkingPermitRequestsDeleteById TO public
GRANT EXECUTE ON dbo.AccountParkingPermitRequestsUpdateById TO public
GRANT EXECUTE ON dbo.AccountParkingPermitRequestsInsert TO public


GRANT SELECT ON AccountParkingPermitRequests TO public


IF OBJECT_ID('[dbo].[AccountParkingPermitDisclaimers]') IS NULL 
	CREATE TABLE [dbo].[AccountParkingPermitDisclaimers]
	(
		[ParkingPermitDisclaimerId] INT NOT NULL PRIMARY KEY IDENTITY,
		[CompanyId] INT NOT NULL,
        [AccountId] INT NULL,
        [ReasonId] INT NULL,
        [Content] VARCHAR(max),
        [CreateDate] DATETIME NOT NULL DEFAULT getdate(),
        [OwnerUserId] INT NOT NULL,
        [Deleted] bit NOT NULL DEFAULT 0,
        [DeleteDate] DATETIME NULL,
        [DeletedByUserId] INT NULL,
	)
GO
GRANT SELECT ON AccountParkingPermitDisclaimers TO public


IF OBJECT_ID('dbo.AccountParkingPermitDisclaimersGetByCompanyId') IS NULL EXEC('CREATE PROCEDURE dbo.AccountParkingPermitDisclaimersGetByCompanyId  AS SET NOCOUNT ON;')
GO

ALTER PROCEDURE dbo.AccountParkingPermitDisclaimersGetByCompanyId (
    @CompanyId int,
    @AccountId int,
    @ReasonId int
) AS
	SELECT * FROM AccountParkingPermitDisclaimers WHERE
        ((CompanyId=@CompanyId AND AccountId=@AccountId AND ReasonId=@ReasonId) OR
        (CompanyId=@CompanyId AND AccountId=@AccountId AND ReasonId IS NULL) OR
        (CompanyId=@CompanyId AND AccountId IS NULL AND ReasonId IS NULL))
        AND Deleted=0
GO
GRANT EXECUTE ON dbo.AccountParkingPermitDisclaimersGetByCompanyId TO public




-- permit settings and validations

IF OBJECT_ID('dbo.AccountParkingPermitSettings') IS NULL 
	CREATE TABLE dbo.AccountParkingPermitSettings
	(
		ParkingPermitSettingId INT NOT NULL PRIMARY KEY IDENTITY,
		CompanyId INT NOT NULL,
        AccountId INT NULL,
        PermitsPerResident INT NOT NULL DEFAULT(1),
        GuestPermitsPerResident INT NOT NULL DEFAULT(0),
        ExpirationType INT,
        GuestExpirationDays INT,
        RequireGuestVehicleInfo INT NULL,
        RequireVehicleRegistration BIT DEFAULT(0),
        RequireVehicleRegistrationExpiration BIT DEFAULT(0),
        RequireColor BIT DEFAULT(0),
        RequireVin BIT DEFAULT(0),
        RequireEmail BIT DEFAULT(0),
		ParkingPermitPublicLinkId INT NULL,
        TaxRateId INT NULL,
        CreateDate DATETIME NOT NULL DEFAULT getdate(),
        OwnerUserId INT NOT NULL,
        Deleted bit NOT NULL DEFAULT 0,
        DeleteDate DATETIME NULL,
        DeletedByUserId INT NULL
	)
GO
GRANT SELECT ON AccountParkingPermitSettings TO public
GRANT UPDATE ON AccountParkingPermitSettings TO PUBLIC
GRANT INSERT ON AccountParkingPermitSettings TO PUBLIC


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='AccountParkingPermitSettings' AND COLUMN_NAME = 'GuestPermitsPerResident')
	ALTER TABLE AccountParkingPermitSettings ADD GuestPermitsPerResident INT NOT NULL DEFAULT(0)
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='AccountParkingPermitSettings' AND COLUMN_NAME = 'GuestExpirationDays')
	ALTER TABLE AccountParkingPermitSettings ADD GuestExpirationDays INT
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='AccountParkingPermitSettings' AND COLUMN_NAME = 'ExpirationType')
	ALTER TABLE AccountParkingPermitSettings ADD ExpirationType INT
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='AccountParkingPermitSettings' AND COLUMN_NAME = 'ParkingPermitPublicLinkId')
	ALTER TABLE AccountParkingPermitSettings ADD ParkingPermitPublicLinkId INT
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='AccountParkingPermitSettings' AND COLUMN_NAME = 'TaxRateId')
	ALTER TABLE AccountParkingPermitSettings ADD TaxRateId INT NULL
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='AccountParkingPermitSettings' AND COLUMN_NAME = 'RequireGuestVehicleInfo')
	ALTER TABLE AccountParkingPermitSettings ADD RequireGuestVehicleInfo INT
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='AccountParkingPermitSettings' AND COLUMN_NAME = 'AllowGuestPassUpdate')
	ALTER TABLE AccountParkingPermitSettings ADD AllowGuestPassUpdate BIT
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='AccountParkingPermitSettings' AND COLUMN_NAME = 'RequireAddress')
	ALTER TABLE AccountParkingPermitSettings ADD RequireAddress BIT
GO

IF OBJECT_ID('dbo.AccountParkingPermitsFindByAccountId') IS NULL EXEC('CREATE PROCEDURE [dbo].[AccountParkingPermitsFindByAccountId] AS SET NOCOUNT ON;')
GO

ALTER PROCEDURE [dbo].[AccountParkingPermitsFindByAccountId] (
	@AccountId int,
	@Search		VARCHAR(64),
	@SearchInt	INT = NULL,
	@Offset INT,
	@FetchNext INT
)  AS

IF (@SearchInt IS NULL)
	BEGIN
		SELECT * FROM 
			AccountParkingPermits PP
		WHERE
			PP.Deleted = 0 AND
			PP.AccountId=@AccountId AND
			   (PP.LicensePlate LIKE '%' + @Search + '%' OR
				PP.VIN LIKE '%' + @Search + '%' OR
				PP.Notes LIKE '%' + @Search + '%' OR
				PP.FullName LIKE '%' + @Search + '%' OR
				PP.CellPhone LIKE '%' + @Search + '%' OR
				PP.CustomPermitNumber LIKE '%' + @Search + '%' OR
				PP.UnitNumber LIKE '%' + @Search + '%' OR
				PP.VehicleMake LIKE '%' + @Search + '%' OR
				PP.VehicleModel LIKE '%' + @Search + '%' OR
				PP.VehicleColor LIKE '%' + @Search + '%' OR
				PP.Email LIKE '%' + @Search + '%')
	    ORDER BY 
			PP.ParkingPermitId
		OFFSET @Offset ROWS
		FETCH NEXT @FetchNext ROWS ONLY

	END
ELSE
	BEGIN
		SELECT * FROM 
			AccountParkingPermits PP
	    WHERE
			PP.Deleted = 0 AND
			PP.AccountId = @AccountId AND 
			   (PP.ParkingPermitId=@SearchInt OR
				PP.LicensePlate LIKE '%' + @Search + '%' OR
				PP.VIN LIKE '%' + @Search + '%' OR
				PP.Notes LIKE '%' + @Search + '%' OR
				PP.FullName LIKE '%' + @Search + '%' OR
				PP.CellPhone LIKE '%' + @Search + '%' OR
				PP.CustomPermitNumber LIKE '%' + @Search + '%' OR
				PP.UnitNumber LIKE '%' + @Search + '%' OR
				PP.VehicleMake LIKE '%' + @Search + '%' OR
				PP.VehicleModel LIKE '%' + @Search + '%' OR
				PP.VehicleColor LIKE '%' + @Search + '%' OR
				PP.Email LIKE '%' + @Search + '%')
		ORDER BY 
			PP.ParkingPermitId
		OFFSET @Offset ROWS
		FETCH NEXT @FetchNext ROWS ONLY
	END
RETURN
GO

GRANT EXEC ON AccountParkingPermitsFindByAccountId TO public


-- permit fees

IF OBJECT_ID('dbo.AccountParkingPermitFees') IS NULL 
	CREATE TABLE dbo.AccountParkingPermitFees
	(
		ParkingPermitFeeId INT NOT NULL PRIMARY KEY IDENTITY,
        CompanyId INT NOT NULL,
        AccountId INT NULL,
        PermitListId INT NOT NULL DEFAULT(1),
        FeeNumber INT NOT NULL,
        Amount DECIMAL DEFAULT(0),
        CreateDate DATETIME NOT NULL DEFAULT getdate(),
        OwnerUserId INT NOT NULL,
        Deleted bit NOT NULL DEFAULT 0,
        DeleteDate DATETIME NULL,
        DeletedByUserId INT NULL
	)
GO
GRANT SELECT ON AccountParkingPermitFees TO PUBLIC
GRANT UPDATE ON AccountParkingPermitFees TO PUBLIC
GRANT INSERT ON AccountParkingPermitFees TO PUBLIC


IF OBJECT_ID('dbo.AccountParkingPermitPublicLinks') IS NULL 
    CREATE TABLE dbo.AccountParkingPermitPublicLinks
    (
        ParkingPermitPublicLinkId INT NOT NULL PRIMARY KEY IDENTITY,
        AccountId INT NULL,
        [Url] varchar(50) NULL,
		[Hash] varchar(10) NULL,
		PropertyCode varchar(100) NULL,
        CreateDate DATETIME NOT NULL DEFAULT getdate(),
        OwnerUserId INT NOT NULL,
        [Disabled] bit NOT NULL DEFAULT 0,
        
    )
GO
GRANT SELECT ON AccountParkingPermitPublicLinks TO PUBLIC
GRANT UPDATE ON AccountParkingPermitPublicLinks TO PUBLIC
GRANT INSERT ON AccountParkingPermitPublicLinks TO PUBLIC

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='AccountParkingPermitPublicLinks' AND COLUMN_NAME = 'Hash')
	ALTER TABLE AccountParkingPermitPublicLinks ADD Hash VARCHAR(10)
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='AccountParkingPermitPublicLinks' AND COLUMN_NAME = 'PropertyCode')
	ALTER TABLE AccountParkingPermitPublicLinks ADD PropertyCode VARCHAR(100)
GO

-- //////////////////////////////
-- Permit Approval Sessions
-- //////////////////////////////
IF OBJECT_ID('dbo.AccountParkingPermitApprovalSessions') IS NULL 
    CREATE TABLE dbo.AccountParkingPermitApprovalSessions
    (
        ApprovalSessionId INT NOT NULL PRIMARY KEY IDENTITY,
        RequestId INT NOT NULL,
		SessionOrderId INT NULL,
		AccountId INT NOT NULL,
        CompanyId INT NOT NULL,
		SessionStatusId INT NULL,
        CreateDate DATETIME NOT NULL DEFAULT getdate(),
        ApprovalDate DATETIME NULL,
		ApprovedByUserId INT NULL,
        Deleted bit NOT NULL DEFAULT 0,
        StatusMessage varchar(1000)
    )
GO
GRANT SELECT ON AccountParkingPermitApprovalSessions TO PUBLIC
GRANT UPDATE ON AccountParkingPermitApprovalSessions TO PUBLIC
GRANT INSERT ON AccountParkingPermitApprovalSessions TO PUBLIC

IF (NOT EXISTS(SELECT 1 FROM sys.indexes WHERE name='IX_AccountParkingPermitApprovalSessions_RequestId'))
BEGIN
    CREATE NONCLUSTERED INDEX [IX_AccountParkingPermitApprovalSessions_RequestId]
        ON [dbo].[AccountParkingPermitApprovalSessions] ([RequestId])

END

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS WHERE CONSTRAINT_NAME='FK_AccountParkingPermitApprovalSessions_AccountId')
BEGIN
    ALTER TABLE
        AccountParkingPermitApprovalSessions 
    ADD CONSTRAINT 
        [FK_AccountParkingPermitApprovalSessions_AccountId] FOREIGN KEY ([AccountId]) REFERENCES dbo.Accounts (AccountId)
END

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS WHERE CONSTRAINT_NAME='FK_AccountParkingPermitApprovalSessions_CompanyId')
BEGIN
    ALTER TABLE
        AccountParkingPermitApprovalSessions 
    ADD CONSTRAINT 
        [FK_AccountParkingPermitApprovalSessions_CompanyId] FOREIGN KEY ([CompanyId]) REFERENCES dbo.Companies (CompanyId)
END

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS WHERE CONSTRAINT_NAME='FK_AccountParkingPermitApprovalSessions_RequestId')
BEGIN
    ALTER TABLE
        AccountParkingPermitApprovalSessions 
    ADD CONSTRAINT 
        [FK_AccountParkingPermitApprovalSessions_RequestId] FOREIGN KEY ([RequestId]) REFERENCES dbo.AccountParkingPermitRequests (ParkingPermitRequestId)
END

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS WHERE CONSTRAINT_NAME='FK_AccountParkingPermitApprovalSessions_OrderId')
BEGIN
    ALTER TABLE
        AccountParkingPermitApprovalSessions 
    ADD CONSTRAINT 
        [FK_AccountParkingPermitApprovalSessions_OrderId] FOREIGN KEY ([SessionOrderId]) REFERENCES dbo.AccountParkingPermitApprovalSessionOrders (ApprovalSessionOrderId)
END


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='AccountParkingPermitApprovalSessions' AND COLUMN_NAME = 'StatusMessage')
	ALTER TABLE AccountParkingPermitApprovalSessions ADD StatusMessage VARCHAR(1000)
GO



IF OBJECT_ID('dbo.AccountParkingPermitApprovalSessionStatuses') IS NULL 
    CREATE TABLE dbo.AccountParkingPermitApprovalSessionStatuses
    (
        SessionStatusId INT NOT NULL PRIMARY KEY IDENTITY,
        Name VARCHAR(30),
		Description VARCHAR(200)
    )
GO


IF (NOT EXISTS (SELECT 1 FROM AccountParkingPermitApprovalSessionStatuses WHERE SessionStatusId=1))
BEGIN
    SET IDENTITY_INSERT AccountParkingPermitApprovalSessionStatuses ON 
    Insert into AccountParkingPermitApprovalSessionStatuses (SessionStatusId, Name, Description) values (1, 'ReadyForApproval', 'The first submission of a permit request that is waiting for approval.')
    SET IDENTITY_INSERT AccountParkingPermitApprovalSessionStatuses OFF
END

IF (NOT EXISTS (SELECT 1 FROM AccountParkingPermitApprovalSessionStatuses WHERE SessionStatusId=2))
BEGIN
    SET IDENTITY_INSERT AccountParkingPermitApprovalSessionStatuses ON 
    Insert into AccountParkingPermitApprovalSessionStatuses (SessionStatusId, Name, Description) values (2, 'Approved', 'A session that has been approved.')
    SET IDENTITY_INSERT AccountParkingPermitApprovalSessionStatuses OFF
END

IF (NOT EXISTS (SELECT 1 FROM AccountParkingPermitApprovalSessionStatuses WHERE SessionStatusId=3))
BEGIN
    SET IDENTITY_INSERT AccountParkingPermitApprovalSessionStatuses ON 
    Insert into AccountParkingPermitApprovalSessionStatuses (SessionStatusId, Name, Description) values (3, 'Revoked', 'A session that has been rejected.')
    SET IDENTITY_INSERT AccountParkingPermitApprovalSessionStatuses OFF
END

IF (NOT EXISTS (SELECT 1 FROM AccountParkingPermitApprovalSessionStatuses WHERE SessionStatusId=4))
BEGIN
    SET IDENTITY_INSERT AccountParkingPermitApprovalSessionStatuses ON 
    Insert into AccountParkingPermitApprovalSessionStatuses (SessionStatusId, Name, Description) values (4, 'Error', 'A session that had an error in the process. See StatusMessage on the ApprovalSessions table.')
    SET IDENTITY_INSERT AccountParkingPermitApprovalSessionStatuses OFF
END


IF (NOT EXISTS (SELECT 1 FROM AccountParkingPermitApprovalSessionStatuses WHERE SessionStatusId=5))
BEGIN
    SET IDENTITY_INSERT AccountParkingPermitApprovalSessionStatuses ON 
    Insert into AccountParkingPermitApprovalSessionStatuses (SessionStatusId, Name, Description) values (5, 'Expired', 'A session that has expired.')
    SET IDENTITY_INSERT AccountParkingPermitApprovalSessionStatuses OFF
END


IF (NOT EXISTS (SELECT 1 FROM AccountParkingPermitApprovalSessionStatuses WHERE SessionStatusId=6))
BEGIN
    SET IDENTITY_INSERT AccountParkingPermitApprovalSessionStatuses ON 
    Insert into AccountParkingPermitApprovalSessionStatuses (SessionStatusId, Name, Description) values (6, 'ChangeRequest', 'A change to an original request that has been submitted.')
    SET IDENTITY_INSERT AccountParkingPermitApprovalSessionStatuses OFF
END






IF OBJECT_ID('dbo.AccountParkingPermitApprovalSessionOrders') IS NULL 
    CREATE TABLE dbo.AccountParkingPermitApprovalSessionOrders
    (
        ApprovalSessionOrderId INT NOT NULL PRIMARY KEY IDENTITY,
		ApprovalSessionId INT NOT NULL,
		Token VARCHAR(50) NULL,
		Last4 VARCHAR(4) NULL,
		Brand VARCHAR(25) NULL,
		IpAddress VARCHAR(50) NULL,
        PreAuthTotal INT NULL,
		PreAuth VARCHAR(256),
        PreAuthFee INT NULL,
		ApprovedTotal BIGINT NULL,
		ApprovedAuth VARCHAR(256),
        ApprovedFee INT NULL,
        ApplicationFeeId varchar(256),
        StripeTransferId varchar(256),
        StripePaymentId varchar(256)
    )
GO
GRANT SELECT ON AccountParkingPermitApprovalSessionOrders TO PUBLIC
GRANT UPDATE ON AccountParkingPermitApprovalSessionOrders TO PUBLIC
GRANT INSERT ON AccountParkingPermitApprovalSessionOrders TO PUBLIC

IF (NOT EXISTS(SELECT 1 FROM sys.indexes WHERE name='IX_AccountParkingPermitApprovalSessionOrders_ApprovalSessionId'))
BEGIN
    CREATE NONCLUSTERED INDEX [IX_AccountParkingPermitApprovalSessionOrders_ApprovalSessionId]
        ON [dbo].[AccountParkingPermitApprovalSessionOrders] ([ApprovalSessionId])
END

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='AccountParkingPermitApprovalSessionOrders' AND COLUMN_NAME = 'PreAuthFee')
	ALTER TABLE AccountParkingPermitApprovalSessionOrders ADD PreAuthFee INT NULL
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='AccountParkingPermitApprovalSessionOrders' AND COLUMN_NAME = 'ApprovedFee')
	ALTER TABLE AccountParkingPermitApprovalSessionOrders ADD ApprovedFee INT NULL
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='AccountParkingPermitApprovalSessionOrders' AND COLUMN_NAME = 'ApplicationFeeId')
	ALTER TABLE AccountParkingPermitApprovalSessionOrders ADD ApplicationFeeId varchar(256)
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='AccountParkingPermitApprovalSessionOrders' AND COLUMN_NAME = 'StripeTransferId')
	ALTER TABLE AccountParkingPermitApprovalSessionOrders ADD StripeTransferId varchar(256)
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='AccountParkingPermitApprovalSessionOrders' AND COLUMN_NAME = 'StripePaymentId')
	ALTER TABLE AccountParkingPermitApprovalSessionOrders ADD StripePaymentId varchar(256)
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='AccountParkingPermitApprovalSessionOrders' AND COLUMN_NAME = 'PaymentIntentId')
	ALTER TABLE AccountParkingPermitApprovalSessionOrders ADD PaymentIntentId varchar(256) NULL
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='AccountParkingPermitApprovalSessionOrders' AND COLUMN_NAME = 'PaymentIntentClientSecret')
	ALTER TABLE AccountParkingPermitApprovalSessionOrders ADD PaymentIntentClientSecret varchar(256) NULL
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='AccountParkingPermitApprovalSessionOrders' AND COLUMN_NAME = 'PaymentMethodId')
	ALTER TABLE AccountParkingPermitApprovalSessionOrders ADD PaymentMethodId varchar(256) NULL
GO



IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS WHERE CONSTRAINT_NAME='FK_AccountParkingPermitApprovalSessionOrders_SessionId')
BEGIN
    ALTER TABLE
        AccountParkingPermitApprovalSessionOrders
    ADD CONSTRAINT 
        [FK_AccountParkingPermitApprovalSessionOrders_SessionId] FOREIGN KEY ([ApprovalSessionId]) REFERENCES dbo.AccountParkingPermitApprovalSessions (ApprovalSessionId)
END


IF OBJECT_ID('dbo.AccountParkingPermitApprovalSessionItems') IS NULL 
    CREATE TABLE dbo.AccountParkingPermitApprovalSessionItems
    (
        ApprovalSessionItemId INT NOT NULL PRIMARY KEY IDENTITY,
		ApprovalSessionId INT NOT NULL,
        ParkingPermitId INT NULL,
		ParkingPermitListId INT NOT NULL,
		Amount Decimal NULL,
		Approved bit null,
		LicensePlate VARCHAR(10) NOT NULL, 
		LicensePlateState VARCHAR(50) NULL,
		VehicleMake VARCHAR(50) NULL, 
		VehicleModel VARCHAR(50) NULL, 
		VehicleColor VARCHAR(50) NULL, 
		VehicleYear INT NULL,
		VIN VARCHAR(17) NULL, 
		StateRegistrationNumber VARCHAR(50) NULL,
		StateRegistrationExpiration VARCHAR(50) NULL
    )
GO
GRANT SELECT ON AccountParkingPermitApprovalSessionItems TO PUBLIC
GRANT UPDATE ON AccountParkingPermitApprovalSessionItems TO PUBLIC
GRANT INSERT ON AccountParkingPermitApprovalSessionItems TO PUBLIC

IF (NOT EXISTS(SELECT 1 FROM sys.indexes WHERE name='IX_AccountParkingPermitApprovalSessionItems_ApprovalSessionId'))
BEGIN
    CREATE NONCLUSTERED INDEX [IX_AccountParkingPermitApprovalSessionItems_ApprovalSessionId]
        ON [dbo].[AccountParkingPermitApprovalSessionItems] ([ApprovalSessionId])
END

IF (NOT EXISTS(SELECT 1 FROM sys.indexes WHERE name='IX_AccountParkingPermitApprovalSessionItems_ParkingPermitId'))
BEGIN
    CREATE NONCLUSTERED INDEX [IX_AccountParkingPermitApprovalSessionItems_ParkingPermitId]
        ON [dbo].[AccountParkingPermitApprovalSessionItems] ([ParkingPermitId])
END

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='AccountParkingPermitApprovalSessionItems' AND COLUMN_NAME = 'VehicleYear')
	ALTER TABLE AccountParkingPermitApprovalSessionItems ADD VehicleYear INT NULL
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS WHERE CONSTRAINT_NAME='FK_AccountParkingPermitApprovalSessionItems_SessionId')
BEGIN
    ALTER TABLE
        AccountParkingPermitApprovalSessionItems
    ADD CONSTRAINT 
        [FK_AccountParkingPermitApprovalSessionItems_SessionId] FOREIGN KEY ([ApprovalSessionId]) REFERENCES dbo.AccountParkingPermitApprovalSessions (ApprovalSessionId)
END

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS WHERE CONSTRAINT_NAME='FK_AccountParkingPermitApprovalSessionItems_PermitId')
BEGIN
    ALTER TABLE
        AccountParkingPermitApprovalSessionItems
    ADD CONSTRAINT 
        [FK_AccountParkingPermitApprovalSessionItems_PermitId] FOREIGN KEY ([ParkingPermitId]) REFERENCES dbo.AccountParkingPermits (ParkingPermitId)
END

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS WHERE CONSTRAINT_NAME='FK_AccountParkingPermitApprovalSessionItems_PermitListId')
BEGIN
    ALTER TABLE
        AccountParkingPermitApprovalSessionItems
    ADD CONSTRAINT 
        [FK_AccountParkingPermitApprovalSessionItems_PermitListId] FOREIGN KEY ([ParkingPermitListId]) REFERENCES dbo.AccountParkingPermitLists (ParkingPermitListId)
END





IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='AccountParkingPermitStatuses')
    CREATE TABLE [dbo].[AccountParkingPermitStatuses](
        [StatusId] [int] IDENTITY(1,1) NOT NULL,
        [CompanyId] [int] NULL,
        [Name] [varchar](50) NOT NULL,
        [ExtendedName] [varchar](100) NULL,
        [BuiltIn] [bit] NOT NULL,
        [HtmlColor] [varchar](50) NULL,
        [Deleted] [bit] DEFAULT ((0)) NOT NULL,
        
        CONSTRAINT [PK_AccountParkingPermitStatuses] PRIMARY KEY CLUSTERED ([StatusId] ASC),
        CONSTRAINT [FK_AccountParkingPermitStatuses_Companies] FOREIGN KEY([CompanyId]) REFERENCES [dbo].[Companies] ([CompanyId])
    )
GO



IF (NOT EXISTS (SELECT 1 FROM AccountParkingPermitStatuses WHERE StatusId=-1))
BEGIN
    SET IDENTITY_INSERT AccountParkingPermitStatuses ON
        INSERT INTO AccountParkingPermitStatuses (StatusId, Name, BuiltIn, HtmlColor, Deleted) VALUES (-1, 'Invalid', 1, '#000000', 0)
    SET IDENTITY_INSERT AccountParkingPermitStatuses OFF
END

IF (NOT EXISTS (SELECT 1 FROM AccountParkingPermitStatuses WHERE StatusId=0))
BEGIN
    SET IDENTITY_INSERT AccountParkingPermitStatuses ON
        INSERT INTO AccountParkingPermitStatuses (StatusId, Name, BuiltIn, HtmlColor, Deleted) VALUES (0, 'Valid', 1, '#000000', 0)
    SET IDENTITY_INSERT AccountParkingPermitStatuses OFF
END

IF (NOT EXISTS (SELECT 1 FROM AccountParkingPermitStatuses WHERE StatusId=1))
BEGIN
    SET IDENTITY_INSERT AccountParkingPermitStatuses ON
        INSERT INTO AccountParkingPermitStatuses (StatusId, Name, BuiltIn, HtmlColor, Deleted) VALUES (1, 'Expired', 1, '#000000', 0)
    SET IDENTITY_INSERT AccountParkingPermitStatuses OFF
END

IF (NOT EXISTS (SELECT 1 FROM AccountParkingPermitStatuses WHERE StatusId=2))
BEGIN
    SET IDENTITY_INSERT AccountParkingPermitStatuses ON
        INSERT INTO AccountParkingPermitStatuses (StatusId, Name, BuiltIn, HtmlColor, Deleted) VALUES (2, 'Revoked', 1, '#000000', 0)
    SET IDENTITY_INSERT AccountParkingPermitStatuses OFF
END

IF (NOT EXISTS (SELECT 1 FROM AccountParkingPermitStatuses WHERE StatusId=3))
BEGIN
    SET IDENTITY_INSERT AccountParkingPermitStatuses ON
        INSERT INTO AccountParkingPermitStatuses (StatusId, Name, BuiltIn, HtmlColor, Deleted) VALUES (3, 'Extended', 1, '#000000', 0)
    SET IDENTITY_INSERT AccountParkingPermitStatuses OFF
END



IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='AccountParkingPermitStatusEvents')
    CREATE TABLE [dbo].[AccountParkingPermitStatusEvents](
		[StatusEventId] [int] IDENTITY(1,1) NOT NULL,
		[PermitId] [int] NULL,
		[StatusId] [int] NOT NULL,
		[UserId] [int] NOT NULL,
		[CreateDate] [datetime] NOT NULL,
        
        CONSTRAINT [PK_AccountParkingPermitStatusEvents] PRIMARY KEY CLUSTERED ([StatusEventId] ASC),
        CONSTRAINT [FK_AccountParkingPermitStatusEvents_Permits] FOREIGN KEY([PermitId]) REFERENCES [dbo].[AccountParkingPermits] ([ParkingPermitId]),
        CONSTRAINT [FK_AccountParkingPermitStatusEvents_Users] FOREIGN KEY([UserId]) REFERENCES [dbo].[Users] ([UserId])
    )
GO


IF OBJECT_ID('dbo.AccountParkingPermitStatusEventsInsert') IS NULL EXEC('CREATE PROCEDURE [dbo].[AccountParkingPermitStatusEventsInsert] AS SET NOCOUNT ON;')
GO

ALTER PROCEDURE [dbo].[AccountParkingPermitStatusEventsInsert]
    @PermitId int,
    @StatusId int,
    @UserId int,
    @CreateDate datetime
AS
    INSERT INTO AccountParkingPermitStatusEvents (PermitId, StatusId, UserId, CreateDate)
    VALUES(@PermitId, @StatusId, @UserId, @CreateDate)
GO

GRANT EXECUTE ON AccountParkingPermitStatusEventsInsert TO PUBLIC


IF OBJECT_ID('dbo.AccountParkingPermitStatusUpdate') IS NULL EXEC('CREATE PROCEDURE [dbo].[AccountParkingPermitStatusUpdate] AS SET NOCOUNT ON;')
GO

ALTER PROCEDURE [dbo].[AccountParkingPermitStatusUpdate]
    @PermitId int,
    @StatusId int
AS
    UPDATE AccountParkingPermits SET StatusId=@StatusId
    WHERE ParkingPermitId=@PermitId
GO


GRANT EXECUTE ON AccountParkingPermitStatusUpdate TO PUBLIC




IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='AccountParkingPermitRequestOneTimeCodes')
    CREATE TABLE [dbo].[AccountParkingPermitRequestOneTimeCodes](
        OneTimeCodeId INT IDENTITY(1,1) NOT NULL,
        PermitRequestId INT NOT NULL,
        ShortCode INT NOT NULL,
		IpAddress VARCHAR(50) NULL,
        CreateDate DATETIME NOT NULL,
        CapturedDate DATETIME NULL,
    )
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS WHERE CONSTRAINT_NAME='PK_AccountParkingPermitRequestOneTimeCodes_Id')
BEGIN
    ALTER TABLE
        AccountParkingPermitRequestOneTimeCodes 
    ADD CONSTRAINT 
        [PK_AccountParkingPermitRequestOneTimeCodes_Id] PRIMARY KEY CLUSTERED ([OneTimeCodeId])
END



-- //////////////////////////////
-- Parking Permit Photos
-- //////////////////////////////
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='AccountParkingPermitPhotos')
    CREATE TABLE [dbo].[AccountParkingPermitPhotos] (
        [ParkingPermitPhotoId] [int] IDENTITY(1,1) NOT NULL,
        [ParkingPermitId] [int] NOT NULL,
        [ContentType] [varchar](50) NOT NULL,
        [Description] [varchar](100) NULL,
        [CreateDate] [datetime] DEFAULT (getdate()) NOT NULL,
        [RemoteIp] [varchar](46) NULL,
        [OwnerUserId] [int] NULL,
        [Deleted] [bit] DEFAULT ((0)) NOT NULL,
        
        CONSTRAINT [PK_AccountParkingPermitPhotos] PRIMARY KEY CLUSTERED ([ParkingPermitPhotoId] ASC),
        CONSTRAINT [FK_AccountParkingPermitPhotos_Permits] FOREIGN KEY([ParkingPermitId]) REFERENCES [dbo].[AccountParkingPermits] ([ParkingPermitId])
    )
GO

GRANT SELECT on AccountParkingPermitPhotos to Public
GRANT INSERT on AccountParkingPermitPhotos to Public
GRANT UPDATE on AccountParkingPermitPhotos to Public

IF OBJECT_ID('dbo.AccountParkingPermitPhotosInsert') IS NULL EXEC('CREATE PROCEDURE [dbo].[AccountParkingPermitPhotosInsert] AS SET NOCOUNT ON;')
GO

ALTER PROCEDURE [dbo].[AccountParkingPermitPhotosInsert]
    @ParkingPermitId int,
    @ContentType varchar(50),
    @Description varchar(100) = NULL,
    @RemoteIp varchar(46) = NULL,
    @OwnerUserId int = NULL
AS
    INSERT INTO
        AccountParkingPermitPhotos (ParkingPermitId, ContentType, Description, CreateDate, RemoteIp, OwnerUserId)
    VALUES
        (@ParkingPermitId, @ContentType, @Description, getdate(), @RemoteIp, @OwnerUserId)

    SELECT @@IDENTITY AS Id
GO

IF OBJECT_ID('dbo.AccountParkingPermitPhotosDeleteById') IS NULL EXEC('CREATE PROCEDURE [dbo].[AccountParkingPermitPhotosDeleteById] AS SET NOCOUNT ON;')
GO

ALTER PROCEDURE [dbo].[AccountParkingPermitPhotosDeleteById]
    @ParkingPermitPhotoId int
AS
    UPDATE AccountParkingPermitPhotos SET Deleted = 1 WHERE ParkingPermitPhotoId = @ParkingPermitPhotoId
GO

IF OBJECT_ID('dbo.AccountParkingPermitPhotosGetById') IS NULL EXEC('CREATE PROCEDURE [dbo].[AccountParkingPermitPhotosGetById] AS SET NOCOUNT ON;')
GO

ALTER PROCEDURE [dbo].[AccountParkingPermitPhotosGetById]
    @ParkingPermitPhotoId int
AS
    SELECT * FROM AccountParkingPermitPhotos WHERE ParkingPermitPhotoId=@ParkingPermitPhotoId AND Deleted = 0
GO

IF OBJECT_ID('dbo.AccountParkingPermitPhotosGetByPermitId') IS NULL EXEC('CREATE PROCEDURE [dbo].[AccountParkingPermitPhotosGetByPermitId] AS SET NOCOUNT ON;')
GO

ALTER PROCEDURE [dbo].[AccountParkingPermitPhotosGetByPermitId]
    @ParkingPermitId int
AS
    SELECT * FROM AccountParkingPermitPhotos WHERE ParkingPermitId=@ParkingPermitId AND Deleted = 0
GO

-----------------------------------
-- Parking Permit Insights
-----------------------------------
IF OBJECT_ID('dbo.AccountParkingPermitInsightsGetByArray') IS NULL EXEC('CREATE PROCEDURE dbo.AccountParkingPermitInsightsGetByArray AS SET NOCOUNT ON;')
GO
ALTER PROCEDURE dbo.AccountParkingPermitInsightsGetByArray (
    @ParkingPermitIds varchar(max) 
) AS
 
    SELECT ParkingPermitId, 'PhotoCount' AS Name, CAST(SUM(Value) AS VARCHAR) AS Value FROM (
        SELECT ParkingPermitId, COUNT(*) AS Value FROM AccountParkingPermitPhotos  P
            INNER JOIN dbo.ArrayToTable(@ParkingPermitIds, ',') AS X ON X.Fld = P.ParkingPermitId AND P.Deleted=0
        GROUP BY ParkingPermitId
    ) v1
    GROUP BY ParkingPermitId

GO

GRANT EXEC on AccountParkingPermitInsightsGetByArray to Public


-- //////////////////////////////
-- Parking Permit Addresses
-- //////////////////////////////
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='AccountParkingPermitAddresses')
    CREATE TABLE [dbo].[AccountParkingPermitAddresses] (
        [ParkingPermitAddressId] [int] IDENTITY(1,1) NOT NULL,
        [ParkingPermitId] [int] NULL,
        [ParkingPermitRequestId] [int] NULL,
        [Type] [int] NOT NULL DEFAULT(0),
        [Address] VARCHAR(100) NULL, 
		[City] VARCHAR(32) NULL, 
		[State] VARCHAR(32) NULL, 
		[Zip] VARCHAR(50) NULL,
        [CreateDate] DateTime NOT NULL, 
        [OwnerUserId] [int] NULL,
        [IsDeleted] [bit] DEFAULT ((0)) NOT NULL,
        
        CONSTRAINT [PK_AccountParkingPermitAddresses] PRIMARY KEY CLUSTERED ([ParkingPermitAddressId] ASC),
        CONSTRAINT [FK_AccountParkingPermitAddresses_Permits] FOREIGN KEY([ParkingPermitId]) REFERENCES [dbo].[AccountParkingPermits] ([ParkingPermitId]),
        CONSTRAINT [FK_AccountParkingPermitAddresses_Requests] FOREIGN KEY([ParkingPermitRequestId]) REFERENCES [dbo].[AccountParkingPermitRequests] ([ParkingPermitRequestId])
    )
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='AccountParkingPermitAddresses' AND COLUMN_NAME = 'CreateDate')
	ALTER TABLE AccountParkingPermitAddresses ADD CreateDate INT NULL
GO

GRANT SELECT on AccountParkingPermitAddresses to Public
GRANT INSERT on AccountParkingPermitAddresses to Public
GRANT UPDATE on AccountParkingPermitAddresses to Public





-- //////////////////////////////
-- Parking Permit Users
-- //////////////////////////////
IF NOT EXISTS (
SELECT  schema_name
FROM    information_schema.schemata
WHERE   schema_name = 'PPIO' ) 
 
BEGIN
EXEC sp_executesql N'CREATE SCHEMA PPIO'  
END
GO




IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = 'PPIO' AND TABLE_NAME='PermitUsers')
CREATE TABLE PPIO.PermitUsers
(
	UserId INT IDENTITY(1,1) NOT NULL,
	Name VARCHAR(100) NULL,
	Phone VARCHAR(32) NULL,
	Email VARCHAR(100) NULL,
	UserTypeId INT NULL,
	Username VARCHAR(50) NULL,
	PasswordHash BINARY(64) NULL,
	CreateDate DateTime NULL,
	Deleted BIT DEFAULT(0),
	Salt UNIQUEIDENTIFIER,
	CONSTRAINT [PK_User_UserId] PRIMARY KEY CLUSTERED (UserId ASC)
)

GO


GRANT SELECT on PPIO.PermitUsers to Public
GRANT INSERT on PPIO.PermitUsers to Public
GRANT UPDATE on PPIO.PermitUsers to Public


IF OBJECT_ID('PPIO.PermitUserInsert') IS NULL EXEC('CREATE PROCEDURE PPIO.PermitUserInsert AS SET NOCOUNT ON;')
GO

	ALTER PROCEDURE PPIO.PermitUserInsert
		@UserTypeId INT,
		@Username VARCHAR(50) = NULL, 
		@Password VARCHAR(50) = NULL,
		@Name VARCHAR(100) = NULL,
		@Phone VARCHAR(32) = NULL,
		@Email VARCHAR(100) = NULL
	AS
	BEGIN
		SET NOCOUNT ON

		DECLARE @salt UNIQUEIDENTIFIER=NEWID();
		
		BEGIN TRY
			INSERT INTO PPIO.[PermitUsers] (UserTypeId, Username, PasswordHash, Salt, Name, Phone, Email, CreateDate)
			VALUES(@UserTypeId, @Username, HASHBYTES('SHA2_512', @Password+CAST(@salt AS VARCHAR(36))), @salt, @Name, @Phone, @Email, GetDate())
	
			SELECT @@Identity as Id
		END TRY
		BEGIN CATCH
			return ERROR_MESSAGE()
		END CATCH

		
	END
GO


IF OBJECT_ID('PPIO.PermitUserUpdate') IS NULL EXEC('CREATE PROCEDURE PPIO.PermitUserUpdate AS SET NOCOUNT ON;')
GO

	ALTER PROCEDURE PPIO.PermitUserUpdate
		@UserId INT,
		@Username VARCHAR(50) = NULL, 
		@Password VARCHAR(50) = NULL,
		@Name VARCHAR(100) = NULL,
		@Phone VARCHAR(32) = NULL,
		@Email VARCHAR(100) = NULL
	AS
	BEGIN
		SET NOCOUNT ON

		BEGIN TRY

			IF @Password IS NULL 
				BEGIN

				UPDATE PPIO.PermitUsers SET
					Name = @Name,
					Phone = @Phone,
					Email = @Email
				WHERE UserId=@UserId
			END
		
			ELSE
				BEGIN

				DECLARE @salt UNIQUEIDENTIFIER=NEWID();
				UPDATE PPIO.PermitUsers SET
					Username = @Username, 
					PasswordHash = HASHBYTES('SHA2_512', @Password+CAST(@salt AS VARCHAR(36))),
					Salt = @Salt,
					Name = @Name,
					Phone = @Phone,
					Email = @Email
				WHERE UserId=@UserId

			END
		END TRY
		BEGIN CATCH
			return ERROR_MESSAGE()
		END CATCH
		
	END
GO


IF OBJECT_ID('PPIO.PermitUserLogin') IS NULL EXEC('CREATE PROCEDURE PPIO.PermitUserLogin AS SET NOCOUNT ON;')
GO

	ALTER PROCEDURE PPIO.PermitUserLogin
		@Username VARCHAR(50),
		@Password VARCHAR(50)
	AS
	BEGIN
		SET NOCOUNT ON

		DECLARE @UserId INT
	
		IF EXISTS (SELECT TOP 1 UserId FROM PPIO.PermitUsers WHERE Username=@Username and Deleted=0)
			BEGIN
				SET @UserID=(SELECT UserId FROM PPIO.PermitUsers WHERE Username=@Username AND PasswordHash=HASHBYTES('SHA2_512', @Password+CAST(Salt AS VARCHAR(36))))

				IF (@UserId IS NULL)
					RAISERROR('Incorrect Password', 18, 1)

				Select @UserId 
			END
		ELSE
			RAISERROR('Invalid Login', 18, 1)

	END
GO




/*//////////////////////////////
   Parking Permit View
   Connects permits to approval sessions, session items, requests (invites), orders
///////////////////////////// */
IF OBJECT_ID('dbo.[vwPermits]') IS NULL EXEC ('CREATE VIEW dbo.[vwPermits] AS SELECT 1 as Temp')
GO
    ALTER VIEW [dbo].[vwPermits]
    AS 

    SELECT 
      P.ParkingPermitId, 
      P.AccountId,
	  P.StatusId,
	  P.ParkingPermitListId,
      R.ParkingPermitRequestId AS RequestId, 
      SI.ApprovalSessionItemId AS SessionItemId, 
      SI.ApprovalSessionId AS SessionId,
	  S.SessionOrderId AS OrderId,
	  R.PermitUserId,
      R.PermitUserTypeId,
	  P.CustomPermitNumber,
	  P.UnitNumber,
	  P.LicensePlate,
	  P.LicensePlateState,
	  P.VehicleYear,
	  P.VehicleMake,
	  P.VehicleModel,
	  P.VehicleColor,
	  P.FullName,
	  P.Email,
	  P.CellPhone,
	  P.LeaseEndDate
    From AccountParkingPermits P
        LEFT OUTER JOIN AccountParkingPermitApprovalSessionItems SI ON SI.ParkingPermitId=P.ParkingPermitId
        LEFT OUTER JOIN AccountParkingPermitApprovalSessions S ON S.ApprovalSessionId=SI.ApprovalSessionId
        LEFT OUTER JOIN AccountParkingPermitRequests R ON R.ParkingPermitRequestId=S.RequestId
    WHERE P.Deleted=0
GO


GRANT SELECT on vwPermits to Public


/*//////////////////////////////
   Parking Permit Invite View
   Connects invites to permit users, permits, approval sessions, session items, and orders
///////////////////////////// */
IF OBJECT_ID('dbo.[vwPermitInvites]') IS NULL EXEC ('CREATE VIEW dbo.[vwPermitInvites] AS SELECT 1 as Temp')
GO  

ALTER VIEW [dbo].[vwPermitInvites]
    AS
    
    SELECT 
		R.ParkingPermitRequestId as RequestId,
		R.AccountId,
		S.ApprovalSessionId as SessionId,
		S.SessionOrderId as OrderId,
		U.UserId,
        Coalesce(U.UserTypeId, R.PermitUserTypeId) as UserTypeId,
		P.ParkingPermitId as PermitId, 
		P.StatusId as PermitStatusId,
		R.FullName as InviteName,
		R.CellPhone as InvitePhone,
		R.Email as InviteEmail,
		P.FullName as PermitName,
		P.CellPhone as PermitPhone,
		P.Email as PermitEmail, 
		R.Code
    FROM AccountParkingPermitRequests R
	    LEFT JOIN AccountParkingPermitApprovalSessions S ON S.RequestId=R.ParkingPermitRequestId
	    LEFT JOIN AccountParkingPermitApprovalSessionItems SI on SI.ApprovalSessionId=S.ApprovalSessionId
	    LEFT JOIN AccountParkingPermits P ON P.ParkingPermitId=SI.ParkingPermitId AND P.Deleted=0
		LEFT OUTER JOIN PPIO.PermitUsers U ON U.UserId=R.PermitUserId
	Where R.Deleted=0
GO

GRANT SELECT on vwPermitInvites to Public