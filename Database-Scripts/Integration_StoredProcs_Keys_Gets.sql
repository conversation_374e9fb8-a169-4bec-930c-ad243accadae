
IF OBJECT_ID('Integration.ProviderDriverKeysGetAll') IS NULL EXEC('CREATE PROCEDURE Integration.ProviderDriverK<PERSON>sGetAll AS SET NOCOUNT ON;')
GO

ALTER PROCEDURE Integration.ProviderDriverKeysGetAll
AS
SELECT        ProviderDriverKeyId, IntegrationProviderId, Name
FROM            Integration.ProviderDriverKeys
GO

IF OBJECT_ID('Integration.ProviderTruckKeysGetAll') IS NULL EXEC('CREATE PROCEDURE Integration.ProviderTruckKeysGetAll AS SET NOCOUNT ON;')
GO

ALTER PROCEDURE Integration.ProviderTruckKeysGetAll 
AS
SELECT        ProviderTruckKeyId, IntegrationProviderId, Name
FROM            Integration.ProviderTruckKeys
GO


IF OBJECT_ID('Integration.ProviderUserKeysGetAll') IS NULL EXEC('CREATE PROCEDURE Integration.ProviderUserKeysGetAll AS SET NOCOUNT ON;')
GO

ALTER PROCEDURE Integration.ProviderUserKeysGetAll 
AS
SELECT        ProviderUserKeyId, IntegrationProviderId, Name
FROM            Integration.ProviderUserKeys
GO


IF OBJECT_ID('Integration.ProviderAccountKeysGetAll') IS NULL EXEC('CREATE PROCEDURE Integration.ProviderAccountKeysGetAll AS SET NOCOUNT ON;')
GO
ALTER PROCEDURE Integration.ProviderAccountKeysGetAll
AS
SELECT        ProviderAccountKeyId, IntegrationProviderId, Name
FROM            Integration.ProviderAccountKeys
GO


IF OBJECT_ID('Integration.ProviderCompanyKeysGetAll') IS NULL EXEC('CREATE PROCEDURE Integration.ProviderCompanyKeysGetAll AS SET NOCOUNT ON;')
GO
ALTER PROCEDURE Integration.ProviderCompanyKeysGetAll
AS
SELECT        ProviderCompanyKeyId, IntegrationProviderId, Name
FROM            Integration.ProviderCompanyKeys
GO


IF OBJECT_ID('Integration.ProviderDispatchEntryKeysGetAll') IS NULL EXEC('CREATE PROCEDURE Integration.ProviderDispatchEntryKeysGetAll AS SET NOCOUNT ON;')
GO

ALTER PROCEDURE Integration.ProviderDispatchEntryKeysGetAll
AS
SELECT        ProviderDispatchEntryKeyId, IntegrationProviderId, Name
FROM            Integration.ProviderDispatchEntryKeys
GO


IF OBJECT_ID('Integration.ProviderRateItemKeysGetAll') IS NULL EXEC('CREATE PROCEDURE Integration.ProviderRateItemKeysGetAll AS SET NOCOUNT ON;')
GO

ALTER PROCEDURE Integration.ProviderRateItemKeysGetAll
AS
SELECT        ProviderRateItemKeyId, IntegrationProviderId, Name
FROM            Integration.ProviderRateItemKeys
GO



IF OBJECT_ID('Integration.ProviderDriverKeysGetByProviderId') IS NULL EXEC('CREATE PROCEDURE Integration.ProviderDriverKeysGetByProviderId AS SET NOCOUNT ON;')
GO

ALTER PROCEDURE Integration.ProviderDriverKeysGetByProviderId (
    @IntegrationProviderId	 	INT
)
AS
SELECT        ProviderDriverKeyId, IntegrationProviderId, Name
FROM            Integration.ProviderDriverKeys
WHERE        (IntegrationProviderId = @IntegrationProviderId)
GO


IF OBJECT_ID('Integration.ProviderTruckKeysGetByProviderId') IS NULL EXEC('CREATE PROCEDURE Integration.ProviderTruckKeysGetByProviderId AS SET NOCOUNT ON;')
GO

ALTER PROCEDURE Integration.ProviderTruckKeysGetByProviderId (
    @IntegrationProviderId	INT
)
AS
SELECT        ProviderTruckKeyId, IntegrationProviderId, Name
FROM            Integration.ProviderTruckKeys
WHERE        (IntegrationProviderId = @IntegrationProviderId)
GO


IF OBJECT_ID('Integration.ProviderUserKeysGetByProviderId') IS NULL EXEC('CREATE PROCEDURE Integration.ProviderUserKeysGetByProviderId AS SET NOCOUNT ON;')
GO

ALTER PROCEDURE Integration.ProviderUserKeysGetByProviderId (
    @IntegrationProviderId  INT
) 
AS
SELECT        ProviderUserKeyId, IntegrationProviderId, Name
FROM            Integration.ProviderUserKeys
WHERE        (IntegrationProviderId = @IntegrationProviderId)
GO


IF OBJECT_ID('Integration.ProviderAccountKeysGetByProviderId') IS NULL EXEC('CREATE PROCEDURE Integration.ProviderAccountKeysGetByProviderId AS SET NOCOUNT ON;')
GO

ALTER PROCEDURE Integration.ProviderAccountKeysGetByProviderId (
    @IntegrationProviderId	INT
)
AS
SELECT        ProviderAccountKeyId, IntegrationProviderId, Name
FROM            Integration.ProviderAccountKeys
WHERE        (IntegrationProviderId = @IntegrationProviderId)
GO


IF OBJECT_ID('Integration.ProviderCompanyKeysGetByProviderId') IS NULL EXEC('CREATE PROCEDURE Integration.ProviderCompanyKeysGetByProviderId AS SET NOCOUNT ON;')
GO

ALTER PROCEDURE Integration.ProviderCompanyKeysGetByProviderId (
    @IntegrationProviderId	INT
)
AS
SELECT        ProviderCompanyKeyId, IntegrationProviderId, Name
FROM            Integration.ProviderCompanyKeys
WHERE        (IntegrationProviderId = @IntegrationProviderId)
GO


IF OBJECT_ID('Integration.ProviderDispatchEntryKeysGetByProviderId') IS NULL EXEC('CREATE PROCEDURE Integration.ProviderDispatchEntryKeysGetByProviderId AS SET NOCOUNT ON;')
GO

ALTER PROCEDURE Integration.ProviderDispatchEntryKeysGetByProviderId (
    @IntegrationProviderId  INT
)
AS
SELECT        ProviderDispatchEntryKeyId, IntegrationProviderId, Name
FROM            Integration.ProviderDispatchEntryKeys
WHERE        (IntegrationProviderId = @IntegrationProviderId)
GO


IF OBJECT_ID('Integration.ProviderRateItemKeysGetByProviderId') IS NULL EXEC('CREATE PROCEDURE Integration.ProviderRateItemKeysGetByProviderId AS SET NOCOUNT ON;')
GO

ALTER PROCEDURE Integration.ProviderRateItemKeysGetByProviderId (
    @IntegrationProviderId	INT
)
AS
SELECT        ProviderRateItemKeyId, IntegrationProviderId, Name
FROM            Integration.ProviderRateItemKeys
WHERE        (IntegrationProviderId = @IntegrationProviderId)
GO


IF OBJECT_ID('Integration.ProvidersGetById') IS NULL EXEC('CREATE PROCEDURE Integration.ProvidersGetById AS SET NOCOUNT ON;')
GO

ALTER PROCEDURE Integration.ProvidersGetById (
    @IntegrationProviderId  INT
)
AS
SELECT        IntegrationProviderId, Name, TypeId, CreateDate
FROM            Integration.Providers
WHERE        (IntegrationProviderId = @IntegrationProviderId)
GO


IF OBJECT_ID('Integration.ProvidersGetAll') IS NULL EXEC('CREATE PROCEDURE Integration.ProvidersGetAll AS SET NOCOUNT ON;')
GO

ALTER PROCEDURE Integration.ProvidersGetAll
AS
	SELECT	IntegrationProviderId, Name, TypeId, CreateDate
	FROM	Integration.Providers
GO



IF OBJECT_ID('Integration.ProviderTypesGetAll') IS NULL EXEC('CREATE PROCEDURE Integration.ProviderTypesGetAll AS SET NOCOUNT ON;')
GO

ALTER PROCEDURE Integration.ProviderTypesGetAll
AS
SELECT        ProviderDriverKeyId, IntegrationProviderId, Name
FROM            Integration.ProviderDriverKeys
GO


IF OBJECT_ID('Integration.ProvidersGetByCompanyId') IS NULL EXEC('CREATE PROCEDURE Integration.ProvidersGetByCompanyId AS SET NOCOUNT ON;')
GO

/*------------------------------------------------------------------------------- 
  - Get a list of providers that are available to the specified company. 
  ------------------------------------------------------------------------------- */
ALTER PROCEDURE Integration.ProvidersGetByCompanyId (
    @CompanyId INT
)
AS
	-- TODO: implement filtering so all providers don't show to all companies.  Low priority. 
	SELECT        IntegrationProviderId, Name, TypeId, CreateDate
	FROM            Integration.Providers
GO

/*Payments*/
IF OBJECT_ID('Integration.InvoicePaymentKeysGetAll') IS NULL EXEC('CREATE PROCEDURE Integration.InvoicePaymentKeysGetAll AS SET NOCOUNT ON;')
GO
ALTER PROCEDURE Integration.InvoicePaymentKeysGetAll
AS
	SELECT InvoicePaymentKeyId
		  ,IntegrationProviderId
		  ,Name
	  FROM Integration.InvoicePaymentKeys
GO
/*End Payments*/

IF OBJECT_ID('Integration.ProviderPaymentMethodKeysGetAll') IS NULL EXEC('CREATE PROCEDURE Integration.ProviderPaymentMethodKeysGetAll AS SET NOCOUNT ON;')
GO
ALTER PROCEDURE Integration.ProviderPaymentMethodKeysGetAll
AS
SELECT        ProviderPaymentMethodKeyId, IntegrationProviderId, Name
FROM            Integration.ProviderPaymentMethodKeys
GO


IF OBJECT_ID('Integration.ProviderTaxRateKeysGetAll') IS NULL EXEC('CREATE PROCEDURE Integration.ProviderTaxRateKeysGetAll AS SET NOCOUNT ON;')
GO
ALTER PROCEDURE Integration.ProviderTaxRateKeysGetAll
AS
	SELECT        ProviderTaxRateKeyId, IntegrationProviderId, Name
	FROM            Integration.ProviderTaxRateKeys
GO