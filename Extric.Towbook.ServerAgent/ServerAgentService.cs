using System;
using System.Threading;
using System.Threading.Tasks;
using Extric.Towbook.EventNotifications;
using Extric.Towbook.Integration.MotorClubs.Services;
using Extric.Towbook.Utility;
using Microsoft.Extensions.Hosting;
using NLog;

namespace Extric.Towbook.Services.ServerAgent
{
    partial class ServerAgentService : IHostedService
    {
        private static readonly Logger logger = LogManager.GetCurrentClassLogger();

        private readonly IHostApplicationLifetime _appLifetime;
        private readonly string[] _args;

        private static int runningThreadCount = 0;

        private int maxThreads = 10;

        private Consumer consumer;

        public ServerAgentService(string[] args, IHostApplicationLifetime appLifetime)
        {
            _args = args;
            _appLifetime = appLifetime;
            _appLifetime.ApplicationStopped.Register(OnStopped);
        }

        public Task StartAsync(CancellationToken cancellationToken)
        {
            logger.Info("ServerAgentService Start Async ...");
            OnStart(_args);

            if (Core.GetConnectionString("Microsoft.ServiceBus").Contains("dev-towbook"))
                Console.WriteLine("Connected to dev-towbook");

            return Task.CompletedTask;
        }

        public Task StopAsync(CancellationToken cancellationToken)
        {
            logger.Info("ServerAgentService Stop Async ...");
            OnStop();
            return Task.CompletedTask;
        }

        protected void OnStart(string[] args)
        {
            for (int i = 0; i < args.Length; i++)
                if (args[i] == "/threads" || args[i] == "/t")
                    maxThreads = Convert.ToInt32(args[i + 1]);

            //EventLog.WriteEntry("Application", "Starting", EventLogEntryType.Information);

            //AccountBalanceSync();
            System.Threading.Tasks.Task.Run(async () => await AccountBalanceAsync());
        }

        public async System.Threading.Tasks.Task AccountBalanceAsync()
        {
            const string queueName = "towbook-account-balances";
            await ServiceBusHelper.CreateProducerQueueAsync(queueName);
            var options = new Extric.Towbook.EventNotifications.OnMessageOptions()
            {
                AutoCompleteMessages = false,
                MaxConcurrentCalls = 64 // 1 during testing, use 16 during production, maybe pull this from app.config?
            };

            consumer = await ServiceBusHelper.CreateConsumerQueueAsync(queueName, options);
            await consumer.OnMessage(async (r) =>
            {
                var body = r.GetBody<string>();

                logger.LogEvent("Processing Account Balance Update = {0}, Body = {1}", null, LogLevel.Info, r.Message.MessageId, body);

                var accountId = r.Message.ApplicationProperties["accountId"];
                var companyId = r.Message.ApplicationProperties["companyId"];

                if (accountId != null)
                {
                    var raccountId = Convert.ToInt32(accountId);
                    var rcompanyId = Convert.ToInt32(companyId);

                    /*    if (!Account.UpdateBalance(raccountId, rcompanyId, -1))
                        {
                            logger.LogEvent("Balance update failed for accountId {0}", rcompanyId, LogLevel.Error, raccountId, new { accountId = raccountId });
                            r.DeadLetter("UpdateBalance returned false", "");
                        }
                        else
                        {
                            logger.LogEvent("Updated Balance successfully.", null, LogLevel.Info, r.MessageId, new
                            {
                                accountId = raccountId,
                                newBalance = Account.GetById(raccountId).GetBalance(rcompanyId)
                            });
                        }
                        */
                    //r.com.Complete();
                    await r.CompleteAsync();
                }
            });
        }


        protected void OnStop()
        {
            logger.Log(LogLevel.Info, "ServerAgentService Stopping on {0}", Environment.MachineName);

            AsyncHelper.RunSync(() => consumer.CleanAsync());

            while (runningThreadCount > 0)
            {

                Thread.Sleep(1000);
            }
        }

        private void OnStopped()
        {
            logger.Info("ServerAgentService stopped successfully");
            LogManager.Shutdown();
            Console.WriteLine("ServerAgentService Stopped finished");
        }

    }
}
