using System;
using System.Collections.ObjectModel;
using System.Threading.Tasks;
using Extric.Towbook.Impounds;

namespace Extric.Towbook.ServerAgent
{
    class ImpoundReminders : ReminderAgent
    {
        public override async Task RunNow()
        {
            Collection<ImpoundReminder> reminders = await ImpoundReminder.GetByTriggerDateByProcessedAsync(DateTime.Today, false);    
        
            foreach( ImpoundReminder reminder in reminders)
            {
                ReminderItem reminderItem = ReminderItem.GetById(reminder.ImpoundReminderDefinitionId);
                Impound impound = Impound.GetById(reminder.ImpoundId);

                if (reminderItem.SendEmail)
                {
                    Console.WriteLine("send notificatino by email for " + impound.ToString());
                }

                if (reminderItem.SendFax)
                {
                    Console.WriteLine("send notificatino by fax for " + impound.ToString());
                }

                if (reminderItem.SendReminder)
                {
                    Console.WriteLine("send notificatino by reminder for " + impound.ToString());
                }

                reminder.Processed = true;
                reminder.ProcessedDate = DateTime.Today;

                reminder.Save();
            }
        }
    }
}
