name: Deploy

on:
  push:
    branches: [master]

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

jobs:
  build-deploy:
    runs-on: ubuntu-latest
    environment:
      name: "Development"
      url: ${{ steps.deploy.outputs.webapp-url }}
    env:
      GITHUB_TOKEN: ${{ github.token }}
      FONTAWESOME_NPM_AUTH_TOKEN: ${{ secrets.FONTAWESOME_NPM_AUTH_TOKEN }}

    steps:
      - name: Checkout Code
        uses: actions/checkout@v4

      - name: Get Version Number
        id: version
        run: echo "version=$(echo ${GITHUB_SHA::7})" >> $GITHUB_OUTPUT

      - name: Post Deployment Start to Slack
        id: slack
        uses: slackapi/slack-github-action@v1.26.0
        env:
          SLACK_BOT_TOKEN: ${{ secrets.SLACK_BOT_TOKEN }}
        with:
          channel-id: "${{ secrets.SLACK_CHANNEL_ID }}"
          payload: |
            {
              "text": "Deployment Started - ${{ steps.version.outputs.version }}",
              "blocks": [
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": ":rocket: *Deployment Started*"
                  }
                },
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": "Commit <https://github.com/${{ github.repository }}/tree/${{ github.sha }}|${{ steps.version.outputs.version }}> is being deployed."
                  }
                },
                {
                  "type": "divider"
                },
                {
                  "type": "context",
                  "elements": [
                    {
                      "type": "mrkdwn",
                      "text": "<https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}|View Workflow Run>"
                    }
                  ]
                }
              ]
            }

      - name: Docker Login
        uses: azure/docker-login@v2
        with:
          login-server: ${{ secrets.REGISTRY_SERVER }}
          username: ${{ secrets.REGISTRY_USERNAME }}
          password: ${{ secrets.REGISTRY_PASSWORD }}

      - name: Build Image
        id: build
        run: >
          docker build . -t ${{ secrets.REGISTRY_SERVER }}/${{ github.event.repository.name }}:${{ steps.version.outputs.version }}
          --build-arg VERSION=${{ steps.version.outputs.version }}
          --build-arg GITHUB_TOKEN
          --build-arg FONTAWESOME_NPM_AUTH_TOKEN

      - name: Publish Image
        id: publish
        run: docker push ${{ secrets.REGISTRY_SERVER }}/${{ github.event.repository.name }}:${{ steps.version.outputs.version }}

      - name: Deploy
        id: deploy
        uses: azure/webapps-deploy@v3
        with:
          publish-profile: ${{ secrets.AZURE_WEBAPP_PUBLISH_PROFILE }}
          images: "${{ secrets.REGISTRY_SERVER }}/${{ github.event.repository.name }}:${{ steps.version.outputs.version }}"

      - name: Post Deployment Complete to Slack
        uses: slackapi/slack-github-action@v1.26.0
        env:
          SLACK_BOT_TOKEN: ${{ secrets.SLACK_BOT_TOKEN }}
        with:
          channel-id: "${{ secrets.SLACK_CHANNEL_ID }}"
          update-ts: ${{ steps.slack.outputs.ts }}
          payload: |
            {
              "text": "Deployment Completed - ${{ steps.version.outputs.version }}",
              "blocks": [
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": ":white_check_mark: *Deployment Completed*"
                  }
                },
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": "Commit <https://github.com/${{ github.repository }}/tree/${{ github.sha }}|${{ steps.version.outputs.version }}> has been successfully deployed."
                  },
                  "accessory": {
                    "type": "button",
                    "text": {
                      "type": "plain_text",
                      "text": "View",
                      "emoji": true
                    },
                    "value": "view",
                    "url": "${{ steps.deploy.outputs.webapp-url }}",
                    "action_id": "view"
                  }
                },
                {
                  "type": "divider"
                },
                {
                  "type": "context",
                  "elements": [
                    {
                      "type": "mrkdwn",
                      "text": "<https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}|View Workflow Run>"
                    }
                  ]
                }
              ]
            }

      - name: Post Build Failure to Slack
        uses: slackapi/slack-github-action@v1.26.0
        if: always() && steps.build.outcome == 'failure'
        env:
          SLACK_BOT_TOKEN: ${{ secrets.SLACK_BOT_TOKEN }}
        with:
          channel-id: "${{ secrets.SLACK_CHANNEL_ID }}"
          update-ts: ${{ steps.slack.outputs.ts }}
          payload: |
            {
              "text": "Build Failed - ${{ steps.version.outputs.version }}",
              "blocks": [
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": ":rotating_light: *Build Failed*"
                  }
                },
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": "Commit <https://github.com/${{ github.repository }}/tree/${{ github.sha }}|${{ steps.version.outputs.version }}> has faild to build."
                  }
                },
                {
                  "type": "divider"
                },
                {
                  "type": "context",
                  "elements": [
                    {
                      "type": "mrkdwn",
                      "text": "<https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}|View Workflow Run>"
                    }
                  ]
                }
              ]
            }

      - name: Post Deployment Failure to Slack
        uses: slackapi/slack-github-action@v1.26.0
        if: always() && (steps.publish.outcome == 'failure' || steps.deploy.outcome == 'failure')
        env:
          SLACK_BOT_TOKEN: ${{ secrets.SLACK_BOT_TOKEN }}
        with:
          channel-id: "${{ secrets.SLACK_CHANNEL_ID }}"
          update-ts: ${{ steps.slack.outputs.ts }}
          payload: |
            {
              "text": "Deployment Failed - ${{ steps.version.outputs.version }}",
              "blocks": [
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": ":rotating_light: *Deployment Failed*"
                  }
                },
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": "Commit <https://github.com/${{ github.repository }}/tree/${{ github.sha }}|${{ steps.version.outputs.version }}> has faild to deploy."
                  }
                },
                {
                  "type": "divider"
                },
                {
                  "type": "context",
                  "elements": [
                    {
                      "type": "mrkdwn",
                      "text": "<https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}|View Workflow Run>"
                    }
                  ]
                }
              ]
            }

      - name: Post Cancelled Message to Slack
        uses: slackapi/slack-github-action@v1.26.0
        if: ${{ cancelled() }}
        env:
          SLACK_BOT_TOKEN: ${{ secrets.SLACK_BOT_TOKEN }}
        with:
          channel-id: "${{ secrets.SLACK_CHANNEL_ID }}"
          update-ts: ${{ steps.slack.outputs.ts }}
          payload: |
            {
              "text": "Deployment Cancelled - ${{ steps.version.outputs.version }}",
              "blocks": [
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": ":no_entry_sign: *Deployment Cancelled*"
                  }
                },
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": "Deployment of commit <https://github.com/${{ github.repository }}/tree/${{ github.sha }}|${{ steps.version.outputs.version }}> has been cancelled."
                  }
                },
                {
                  "type": "divider"
                },
                {
                  "type": "context",
                  "elements": [
                    {
                      "type": "mrkdwn",
                      "text": "<https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}|View Workflow Run>"
                    }
                  ]
                }
              ]
            }
