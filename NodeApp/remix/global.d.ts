import "@remix-run/server-runtime";
import type { Towbook } from "@towbook/sdk";
import type { CachifiedOptions } from "@epic-web/cachified";
import type { Logger } from "winston";

declare module "@remix-run/server-runtime" {
  export interface AppLoadContext {
    requestId: string;
    user?: {
      username: string;
      company: {
        id: number;
        name: string;
      };
    };
    logger: Logger;
    towbook: Towbook;
    cache: <Value>(
      options: Omit<CachifiedOptions<Value>, "cache"> & {
        /**
         * This defines the prefix of the cache key.
         *
         * By default if a user is logged in, the key will be the user prefix, otherwise it will act as null.
         *
         *  - global: `global:`
         *  - user: `user:${username}:`
         *  - company: `company:${companyId}:`
         *  - null: No cache prefix (Full control of the cache key)
         */
        scope?: "global" | "user" | "company" | null;
      },
    ) => Promise<Value>;
  }
}
