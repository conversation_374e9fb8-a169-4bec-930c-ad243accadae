import type { LoaderFunctionArgs } from "@remix-run/node";
import { requireAuth } from "~/services/auth.server";

export async function loader({ request }: LoaderFunctionArgs) {
  await requireAuth(request);

  return null;
}

export default function Component() {
  return (
    <>
      <div className="border-b border-slate-5 bg-white px-5 py-3.5 text-xl font-medium">
        Good afternoon, <PERSON>. Welcome to Towbook!
      </div>
      <div className="bg-slate-2 py-[15px] pl-5">
        <div id="dashboards"></div>
      </div>
      <script type="module" src="/dist/dashboards.js"></script>
    </>
  );
}
