import type { LoaderFunctionArgs } from "@remix-run/node";
import { requireAuth } from "~/services/auth.server";

export async function loader({ request }: LoaderFunctionArgs) {
  await requireAuth(request);

  return null;
}

export default function Component() {
  return (
    <>
      <div
        data-upsell-banner
        data-title="Advanced Billing"
        data-description="Save time on your billing by mass emailing all your statements out to at once instead of sending them one-by-one!"
        data-cta-label="Click Here to Upgrade Now"
        data-intercom-message="Hello! I'm interested in upgrading my account to be able to use the advanced billing features."
        data-tag="New"
        data-image="/UI/images/upsell-banner/upsell-advanced-billing.jpg"
      >
        <p>
          The new billing features include several brand-new features created to
          save you time on your billing as well as increase the flexibility you
          have to manage your billing in Towbook.
        </p>
        <p>This includes:</p>
        <ul>
          <li>Mass Emailing Statements</li>
          <li>Undeliverable Email Reporting</li>
          <li>Preferred billing method per customer</li>
          <li>Personalized email messages and subject lines per customer</li>
          <li>Credit Hold</li>
        </ul>
      </div>
      <script type="module" src="/dist/upsell-banner.js"></script>
    </>
  );
}
