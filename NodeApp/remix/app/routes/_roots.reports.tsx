import type { LoaderFunctionArgs } from "@remix-run/node";
import { requireAuth } from "~/services/auth.server";

export async function loader({ request }: LoaderFunctionArgs) {
  await requireAuth(request);

  return null;
}

export default function Component() {
  return (
    <>
      <div id="reports-prompt"></div>
      <div id="reports"></div>
      <script type="module" src="/dist/reports.js"></script>
    </>
  );
}
