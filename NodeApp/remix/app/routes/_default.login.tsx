import {
  Form,
  Link,
  redirect,
  useActionData,
  useNavigation,
} from "@remix-run/react";
import type {
  LoaderFunctionArgs,
  ActionFunctionArgs,
  LinksFunction,
} from "@remix-run/node";
import { <PERSON><PERSON>, Field, Input, TowbookLogo } from "@towbook/flatbed";
import { authenticator } from "~/services/auth.server";
import { sessionStorage } from "~/services/session.server";
import { getFormProps, getInputProps, useForm } from "@conform-to/react";
import { z } from "zod";
import { handleSubmission } from "~/utils/forms";
import { parseWithZod } from "@conform-to/zod";
import styles from "~/styles/index.css?url";

export const links: LinksFunction = () => {
  return [{ rel: "stylesheet", href: styles }];
};

const SUCCESS_REDIRECT = "/";

const LoginFormSchema = z.object({
  username: z.string().min(1, "Username is required"),
  password: z.string().min(1, "Password is required"),
});

export async function loader({ request }: LoaderFunctionArgs) {
  await authenticator.isAuthenticated(request, {
    successRedirect: SUCCESS_REDIRECT,
  });

  return null;
}

export async function action({ request, context }: ActionFunctionArgs) {
  return await handleSubmission({
    request,
    logger: context.logger,
    intents: {
      login: {
        schema: LoginFormSchema,
        hideFields: ["password"],
        async mutationFn() {
          const user = await authenticator.authenticate("form", request, {
            context,
            throwOnError: true,
          });

          // Get the session from the request
          const session = await sessionStorage.getSession(
            request.headers.get("cookie"),
          );

          // Set the user in the session
          session.set(authenticator.sessionKey, user);

          /**
           * Redirect the user to the returnTo URL if it exists, otherwise
           * redirect the user to the user page. Also, set the session cookie.
           */
          return redirect(session.get("returnTo") || SUCCESS_REDIRECT, {
            headers: {
              "set-cookie": await sessionStorage.commitSession(session, {
                expires: new Date(user.expiresAt),
              }),
            },
          });
        },
      },
    },
  });
}

export default function Component() {
  const actionData = useActionData<typeof action>();
  const navigation = useNavigation();

  const isBusy =
    navigation.state !== "idle" &&
    navigation.formData?.get("intent") === "login";

  const [form, fields] = useForm<z.infer<typeof LoginFormSchema>>({
    lastResult: actionData,
    onValidate({ formData }) {
      return parseWithZod(formData, {
        schema: LoginFormSchema,
      });
    },
  });

  return (
    <div className="grid min-h-full place-items-center bg-slate-1 px-6 py-8 dark:bg-black">
      <div className="w-full max-w-sm">
        <Link to="/" tabIndex={-1} className="mb-10 flex">
          <TowbookLogo className="mx-auto h-10" isColor />
        </Link>
        <div className="rounded-lg bg-white p-10 shadow-md shadow-slateA-3 outline outline-1 outline-slateA-4 dark:bg-slate-1 dark:outline-slateA-5">
          <div className="mb-8 text-center text-2xl">
            Log in to your account
          </div>
          <Form
            method="post"
            className="flex flex-col gap-4"
            {...getFormProps(form)}
          >
            {form.errors && (
              <div className="rounded-lg bg-red-3 px-5 py-3 text-center text-sm text-red-11">
                {form.errors?.join(", ")}
              </div>
            )}
            <Field.Root size="large">
              <Field.Label>Username</Field.Label>
              <Field.Control>
                <Input
                  autoFocus
                  {...getInputProps(fields.username, { type: "text" })}
                />
              </Field.Control>
              <Field.Error>{fields.username.errors}</Field.Error>
            </Field.Root>
            <Field.Root size="large">
              <Field.Label>Password</Field.Label>
              <Field.Control>
                <Input
                  {...getInputProps(fields.password, { type: "password" })}
                />
              </Field.Control>
              <Field.Error>{fields.password.errors}</Field.Error>
            </Field.Root>
            <Button
              size="large"
              type="submit"
              name="intent"
              value="login"
              className="mt-2"
              disabled={isBusy}
            >
              {isBusy ? "Logging in..." : "Log in"}
            </Button>
          </Form>
        </div>
        <div className="mt-6 text-center text-sm text-slate-11">
          By logging in you agree to our{" "}
          <a
            href="https://towbook.com/terms-of-service"
            target="_blank"
            className="decoration font-medium text-blue-10 underline decoration-blue-6 underline-offset-2 transition-colors hover:text-blue-9"
            rel="noreferrer"
          >
            Terms of Service
          </a>
          .
        </div>
      </div>
    </div>
  );
}
