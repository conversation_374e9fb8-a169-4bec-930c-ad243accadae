import type { Towbook } from "@towbook/sdk";
import { LoginInput } from "@towbook/sdk";
import { Authenticator } from "remix-auth";
import { sessionStorage } from "~/services/session.server";
import { FormStrategy } from "remix-auth-form";
import invariant from "tiny-invariant";
import { redirect } from "@remix-run/node";

export const authenticator = new Authenticator<
  Awaited<ReturnType<Towbook["auth"]["login"]>>
>(sessionStorage);

authenticator.use(
  new FormStrategy(async ({ form, context }) => {
    invariant(context, "Authenticator: Context is required");
    const { towbook } = context;

    const username = form.get("username");
    const password = form.get("password");

    const input = LoginInput.parse({
      username,
      password,
    });

    const userAuth = await towbook.auth.login(input);

    return userAuth;
  }),
);

export async function requireAuth(request: Request) {
  const user = await authenticator.isAuthenticated(request);

  if (!user) {
    const session = await sessionStorage.getSession(
      request.headers.get("cookie"),
    );
    session.set("returnTo", request.url);

    throw redirect("/login", {
      headers: {
        "set-cookie": await sessionStorage.commitSession(session),
      },
    });
  }

  return user;
}
