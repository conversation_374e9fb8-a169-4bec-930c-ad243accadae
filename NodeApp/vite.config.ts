import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import { Towbook } from "@towbook/sdk";
import path from "path";
import fs from "fs";

/**
 * If the project is using flatbed or sdk as a local package,
 * we need to exclude it from the optimizeDeps
 */
const projectPath = fs.realpathSync(".");
const flatbedPath = fs.realpathSync("./node_modules/@towbook/flatbed");
const sdkPath = fs.realpathSync("./node_modules/@towbook/sdk");

const exclude = [
  !flatbedPath.startsWith(projectPath) ? "@towbook/flatbed" : undefined,
  !sdkPath.startsWith(projectPath) ? "@towbook/sdk" : undefined,
].filter(Boolean) as string[];

const BASE_URL = "https://app.towbook.com";

// https://vitejs.dev/config/
export default defineConfig(async () => {
  const towbook = new Towbook({
    api: `${BASE_URL}/api`,
  });

  const user = {
    username: "bigmoney",
    password: "winter17",
  };

  if (!user.username || !user.password) {
    throw new Error(
      "Please set your username and password in the vite.config.ts file",
    );
  }

  const { token } = await towbook.auth.login(user);

  return {
    optimizeDeps: {
      exclude,
    },
    plugins: [react()],
    publicDir: path.resolve(__dirname, "../app"),
    css: {
      postcss: {
        plugins: [
          require("postcss-import")(),
          require("tailwindcss/nesting")(),
          require("tailwindcss")(),
        ],
      },
    },
    server: {
      host: "0.0.0.0",
      fs: {
        strict: false,
      },
      proxy: {
        "/api": {
          target: BASE_URL,
          changeOrigin: true,
          headers: {
            "x-api-token": token,
          },
        },
      },
    },
  };
});
