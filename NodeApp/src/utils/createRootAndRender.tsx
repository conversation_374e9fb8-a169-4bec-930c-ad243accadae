import * as React from "react";
import { TowbookProvider } from "@towbook/sdk";
import ReactDOM from "react-dom/client";
import { QueryClient } from "@tanstack/react-query";
import { ReactQueryDevtools } from "@tanstack/react-query-devtools";
import ms from "ms";
import { UserQueryClientProvider } from "./UserQueryClientProvider";

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      gcTime: ms("7d"),
    },
  },
});

export function createRootAndRender(
  elementId: string | Element | HTMLElement,
  component: React.ReactNode,
  options: {
    fixBodyMaxWidth: boolean;
  } = {
    fixBodyMaxWidth: true,
  },
) {
  if (typeof elementId === "string") {
    if (typeof window.forceRender !== "object") {
      window.forceRender = {};
    }

    window.forceRender[elementId] = () => {
      createRootAndRender(elementId, component, options);
    };
  }

  // If towbookDebug is set to true in localStorage, we'll log warnings
  const towbookDebug =
    window.localStorage.getItem("towbookDebug") === "true" || false;

  let element: HTMLElement | Element | null;

  // If elementId is a string, we assume it's an id and try to find the element
  if (typeof elementId === "string") {
    element = document.getElementById(elementId);

    // If we can't find the element, we warn and return, only if we're in debug mode
    if (!element) {
      if (towbookDebug) {
        console.warn(`No #${elementId} root element found`);
      }

      // If we're not in debug mode, we just return silently
      return;
    }
  } else {
    // If elementId is not a string, we assume it's an element and use it directly
    element = elementId;
  }

  if (options.fixBodyMaxWidth) {
    fixBodyMaxWidth();
  }

  element.classList.add("react-root");

  const root = ReactDOM.createRoot(element);

  root.render(
    <React.StrictMode>
      <UserQueryClientProvider
        client={queryClient}
        appName="towbook-web"
        version="prod"
        user={`user-${window.towbookCurrentUser?.id ?? "unknown"}`}
      >
        <TowbookProvider>
          {component}
          <ReactQueryDevtools />
        </TowbookProvider>
      </UserQueryClientProvider>
    </React.StrictMode>,
  );
}

function fixBodyMaxWidth() {
  const body = document.body;

  if (body) {
    body.style.maxWidth = "initial";
    body.style.margin = "";
  }

  const headerElement = document.getElementById("header")?.parentElement;

  if (headerElement) {
    headerElement.style.maxWidth = "2000px";
    headerElement.style.margin = "0 auto";
  }

  const pageHolder = document.getElementById("pageHolder");

  if (pageHolder) {
    pageHolder.style.maxWidth = "2000px";
    pageHolder.style.margin = "0 auto";
  }

  const footer = document.getElementById("footer");

  if (footer) {
    footer.style.maxWidth = "2000px";
    footer.style.margin = "0 auto";
  }
}
