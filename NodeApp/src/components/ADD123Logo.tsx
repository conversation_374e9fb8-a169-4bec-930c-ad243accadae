export function ADD123Logo(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      {...props}
      viewBox="0 0 71 64"
    >
      <path
        fill="#3A7AC7"
        stroke="#000"
        d="M53 4H3v28h19.5l9 8.5L53 42V4Z"
        style={{
          fill: "#3a7ac7",
          color: "color(display-p3 .2275 .4784 .7804)",
          fillOpacity: 1,
          stroke: "#000",
          strokeOpacity: 1,
        }}
      />
      <path
        fill="#0F0E0C"
        d="M40.136 60.599a2.799 2.799 0 1 1 0-5.6 2.8 2.8 0 0 1 0 5.6Zm11.386-21.282s-2.896-1.265-5.36-1.265h-13.36s-2.057-2.438-4.786-5.01c-2.735-2.59-4.948-3.99-4.948-3.99H4.64V6.973A2.034 2.034 0 0 1 6.334 5.28h43.599a2.01 2.01 0 0 1 1.588 1.495v32.542Zm4.614-13.198V.65H0v33.162h21.479a53.908 53.908 0 0 0 4.588 3.99c2.104 2.103 4.266 5 4.266 5h14.145c4.693 0 6.745 1.728 7.854 2.848l3.828 3.792V28.494c6.412 1.411 11.224 7.13 11.224 13.973 0 7.89-6.427 14.317-14.306 14.317h-6.839c-.495-2.922-3.015-5.161-6.078-5.161a6.192 6.192 0 0 0-6.187 6.187 6.192 6.192 0 0 0 6.187 6.188c2.974 0 5.453-2.11 6.052-4.906h6.88c9.172 0 16.626-7.47 16.626-16.64-.026-8.105-5.88-14.88-13.584-16.334"
        style={{
          fill: "#0f0e0c",
          color: "color(display-p3 .0588 .0549 .0471)",
          fillOpacity: 1,
        }}
      />
    </svg>
  );
}
