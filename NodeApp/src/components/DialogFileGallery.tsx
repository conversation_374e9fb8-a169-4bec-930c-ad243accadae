import * as React from "react";
import { <PERSON><PERSON>, <PERSON>alog, Icon } from "@towbook/flatbed";
import {
  faChevronLeft,
  faChevronRight,
} from "@fortawesome/pro-solid-svg-icons";

export interface FileInput {
  id: string | number;
  url?: string;
  isPhoto: boolean;
  filename: string;
}

export function DialogFileGallery({
  files,
  defaultFile,
  lazyLoad,
  children,
}: {
  files: FileInput[];
  defaultFile?: number;
  lazyLoad?: boolean;
  children: React.ReactNode;
}) {
  lazyLoad = lazyLoad ?? false;
  const [activeFile, setActiveFile] = React.useState(defaultFile || 0);

  const prevFile = React.useCallback(() => {
    const next = activeFile - 1;
    if (next >= 0) {
      setActiveFile(next);
    } else {
      setActiveFile(files.length - 1);
    }
  }, [activeFile, files.length]);

  const nextFile = React.useCallback(() => {
    const next = activeFile + 1;
    if (next < files.length) {
      setActiveFile(next);
    } else {
      setActiveFile(0);
    }
  }, [activeFile, files.length]);

  const handleKeyPress = React.useCallback<
    React.KeyboardEventHandler<HTMLDivElement>
  >(
    (e) => {
      if (e.key === "ArrowLeft") prevFile();
      if (e.key === "ArrowRight") nextFile();
    },
    [prevFile, nextFile],
  );

  const goToFile = React.useCallback(
    (index: number) => {
      setActiveFile(index);
    },
    [setActiveFile],
  );

  return (
    <Dialog.Root>
      <Dialog.Trigger asChild className="appearance-none">
        {children}
      </Dialog.Trigger>
      <Dialog.Content
        className="w-full h-full flex flex-col p-4"
        onKeyDown={handleKeyPress}
      >
        <div className="flex justify-end items-center mb-4">
          <Dialog.Close />
        </div>
        {files[activeFile] && (
          <>
            <div className="flex-1 relative">
              {files.length > 1 && (
                <>
                  <button
                    onClick={prevFile}
                    className="z-10 absolute top-0 left-0 h-full appearance-none flex items-center justify-center px-10 opacity-50 hover:opacity-100 transition-opacity group"
                  >
                    <div className="rounded-full p-5 bg-slateA-10 flex items-center justify-center transition-transform group-hover:-translate-x-3">
                      <Icon
                        icon={faChevronLeft}
                        className="w-8 h-8 text-white"
                      />
                    </div>
                  </button>
                  <button
                    onClick={nextFile}
                    className="z-10 absolute top-0 right-0 h-full appearance-none flex items-center justify-center px-10 opacity-50 hover:opacity-100 transition-opacity group"
                  >
                    <div className="rounded-full p-5 bg-slateA-10 flex items-center justify-center transition-transform group-hover:translate-x-3">
                      <Icon
                        icon={faChevronRight}
                        className="w-8 h-8 text-white"
                      />
                    </div>
                  </button>
                </>
              )}
              <div className="z-10 absolute bottom-0 right-0 appearance-none flex items-center justify-center px-10 opacity-50 hover:opacity-100 transition-opacity group">
                {files[activeFile].isPhoto === true && (
                  <Button>
                    <a
                      href={`/File.ashx?id=${files[activeFile]?.id}&view=1`}
                      target="_blank"
                      rel="noreferrer"
                      title="Download Photo"
                    >
                      Download
                    </a>
                  </Button>
                )}
              </div>
              <div className="absolute w-full h-full inset-0 object-contain object-center">
                {files[activeFile].isPhoto ? (
                  <img
                    loading={lazyLoad ? "lazy" : "eager"}
                    src={files[activeFile]?.url}
                    alt={files[activeFile]?.filename}
                    className=" w-full h-full object-contain object-center"
                  />
                ) : (
                  <div className="h-full grid place-items-center">
                    <div className="grid place-items-center">
                      <div>File: {files[activeFile]?.filename}</div>
                      <Button className=" object-contain object-center">
                        <a
                          href={`/File.ashx?id=${files[activeFile].id}&view=1`}
                          target="_blank"
                          rel="noreferrer"
                          title="Download Photo"
                        >
                          Download
                        </a>
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            </div>
            <div className="flex justify-center pt-4">
              {files.map((file, index) => (
                <button
                  onClick={() => goToFile(index)}
                  className={`appearance-none p-2`}
                  key={file.id}
                >
                  <div
                    className={`rounded-full w-4 h-4 ${
                      index === activeFile ? "bg-blue-9" : "bg-slate-7"
                    }`}
                  />
                </button>
              ))}
            </div>
          </>
        )}
      </Dialog.Content>
    </Dialog.Root>
  );
}
