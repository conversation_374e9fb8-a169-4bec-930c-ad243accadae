import { useQuery } from "@tanstack/react-query";
import { useTowbook } from "@towbook/sdk";

export function useAuction(auctionId?: number | string) {
  const towbook = useTowbook();

  return useQuery({
    queryKey: ["auctions", Number(auctionId)],
    queryFn: async () => {
      if (!auctionId) return null;
      return towbook.auctions.get(auctionId);
    },
    enabled: <PERSON><PERSON><PERSON>(auctionId),
    staleTime: Infinity,
    gcTime: Infinity,
  });
}
