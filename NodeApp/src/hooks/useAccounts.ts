import { Towbook, useTowbook } from "@towbook/sdk";
import { useQuery, UseQueryResult } from "@tanstack/react-query";

export function useAccounts(config?: {
  params?: Parameters<Towbook["accounts"]["list"]>[0];
  options?: Parameters<Towbook["request"]["get"]>[2];
}): UseQueryResult<Awaited<ReturnType<Towbook["accounts"]["list"]>>> {
  const towbook = useTowbook();

  return useQuery({
    queryKey: ["accounts", JSON.stringify(config)],
    queryFn: async () => {
      const request = await towbook.request.get(
        "/accounts",
        config?.params,
        config?.options,
      );
      return await request.json();
    },
    refetchOnWindowFocus: false,
    staleTime: Infinity,
    gcTime: Infinity,
  });
}
