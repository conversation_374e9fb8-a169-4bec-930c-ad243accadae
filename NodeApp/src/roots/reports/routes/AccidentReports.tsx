import React from "react";
import { useTowbook } from "@towbook/sdk";
import { Report } from "../components/Report";
import {
  formatters,
  formValuesToSearchParams,
  getInitialFilters,
  useFilterSetStorage,
  zodFormStringsToStringArray,
  zodImpounds,
  zodRefineDateRange,
} from "../utils";
import { createColumnHelper } from "@tanstack/react-table";
import {
  Button,
  DataTable,
  DropdownMenu,
  ExtractObjectType,
  Icon,
} from "@towbook/flatbed";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { Parameters } from "../components/Parameters";
import { z } from "zod";
import { parseWithZod } from "@conform-to/zod";
import {
  FormProvider,
  getFormProps,
  getInputProps,
  useForm,
} from "@conform-to/react";
import ms from "ms";
import { useSearchParams } from "react-router-dom";
import { faFileExport } from "@fortawesome/pro-regular-svg-icons";
import { useDownload } from "../../../hooks/useDownload";
import { faArrowUpRightFromSquare } from "@fortawesome/pro-solid-svg-icons";
import { useCompanies } from "../../../hooks/useCompanies";

const reportType = "AccidentReports";
const reportName = "Accident Reports";
const reportFnName = "accidentReports";
const allReportsQueryKey = ["reports", reportFnName];
const reportKey = "dispatching/accident-reports";
const today = new Date();
const yesterday = new Date(today.getTime() - 1000 * 60 * 60 * 24);

const DEFAULT_FORM_VALUES: z.input<typeof AccidentReportsParamsSchema> = {
  dateStart: yesterday.toISOString().split("T")[0],
  dateEnd: today.toISOString().split("T")[0],
  impounds: "0",
};

const AccidentReportsParamsSchema = z
  .object({
    dateStart: z.string(),
    dateEnd: z.string(),
    companyId: zodFormStringsToStringArray.optional(),
    accountId: z
      .string()
      .optional()
      .transform((id) => id || undefined),
    performerUserId: z
      .string()
      .optional()
      .transform((id) => id || undefined),
    impounds: zodImpounds,
  })
  .refine(...zodRefineDateRange);

export default function AccidentReports() {
  const towbook = useTowbook();
  const { data: companies } = useCompanies();
  const isMultiCompany = React.useMemo(
    () => (companies?.length || 1) > 1,
    [companies],
  );

  const [searchParams, setSearchParams] = useSearchParams();
  const [filterSets, setFilterSets] = useFilterSetStorage<
    {
      name: string;
      filters: z.input<typeof AccidentReportsParamsSchema>;
    }[]
  >(reportType, reportName, reportKey);

  const [parameters, setParameters] = React.useState<z.infer<
    typeof AccidentReportsParamsSchema
  > | null>(
    AccidentReportsParamsSchema.nullable().parse(
      getInitialFilters(searchParams, filterSets),
    ),
  );
  const reportQueryKey = [...allReportsQueryKey, JSON.stringify(parameters)];

  const reportQuery = useQuery({
    queryKey: reportQueryKey,
    queryFn: () => {
      if (!parameters) {
        return null;
      }
      return towbook.reports[reportFnName](parameters);
    },
    enabled: Boolean(parameters),
    staleTime: ms("5m"),
  });

  const queryClient = useQueryClient();

  const [form, fields] = useForm<z.input<typeof AccidentReportsParamsSchema>>({
    defaultValue: getInitialFilters(
      searchParams,
      filterSets,
      DEFAULT_FORM_VALUES,
    ),
    onValidate({ formData }) {
      return parseWithZod(formData, {
        schema: AccidentReportsParamsSchema,
      });
    },
    onSubmit(event, { submission }) {
      event.preventDefault();
      if (submission?.payload) {
        const parsedPayload = AccidentReportsParamsSchema.parse(
          submission.payload,
        );

        /**
         * If the parsed payload is different from the current parameters,
         * update the search params and the parameters.
         *
         * If the parameters are the same, we don't need to update the search params,
         * but we still need to invalidate the query to force a re-fetch.
         *
         * This is necessary because the `useQuery` hook will not re-run
         * if the parameters are the same as the previous parameters.
         */
        if (JSON.stringify(parsedPayload) !== JSON.stringify(parameters)) {
          setSearchParams(formValuesToSearchParams(parsedPayload));
          setParameters(parsedPayload);
        } else {
          queryClient.resetQueries({
            queryKey: ["reports", reportFnName, JSON.stringify(parsedPayload)],
          });
        }
      }
    },
  });

  const handleResetForm = React.useCallback(() => {
    form.update({
      value: DEFAULT_FORM_VALUES,
    });
    setParameters(null);
    setSearchParams(formValuesToSearchParams(DEFAULT_FORM_VALUES));
  }, [form, setSearchParams]);

  const columnHelper = React.useMemo(
    () =>
      createColumnHelper<
        ExtractObjectType<
          NonNullable<(typeof reportQuery)["data"]>["reportData"]
        >
      >(),
    [],
  );

  const columns = React.useMemo(() => {
    return [
      [
        columnHelper.accessor("id", {
          header: "Report Number",
          size: 200,
        }),
        columnHelper.accessor((rowData) => formatters.date(rowData.date), {
          header: "Date",
          size: 200,
        }),
      ],
      isMultiCompany
        ? [
            columnHelper.accessor("companyName", {
              header: "Company",
              size: 200,
            }),
          ]
        : [],
      [
        columnHelper.accessor("callNumber", {
          header: "Call Number",
          size: 200,
        }),
        columnHelper.accessor("accountName", {
          header: "Account",
          size: 300,
        }),
        columnHelper.accessor("ownerUserName", {
          header: "Created By",
          size: 250,
        }),
        columnHelper.display({
          id: "report-button",
          cell: (props) => (
            <a
              className="text-blue-11 hover:text-blue-10 hover:no-underline cursor-pointer"
              href={`/dispatch2/AccidentReport/Report.aspx?id=${props.row.original.id}&callId=${props.row.original.callId}&pdf=1`}
              target="_blank"
              rel="noopener noreferrer"
            >
              View Report
              <Icon className="ml-2" icon={faArrowUpRightFromSquare} />
            </a>
          ),
          enableGrouping: false,
          enableResizing: false,
          enablePinning: false,
          enableHiding: false,
        }),
      ],
    ].flat();
  }, [columnHelper, isMultiCompany]);

  const download = useDownload();

  const getExport = React.useCallback(
    (exportType?: string, shouldUseFormValues = false) => {
      download.downloadFile(
        `${reportType}.xlsx`,
        fetch(`${towbook.options.api}/reports?format=xlsx&export=1`, {
          method: "POST",
          body: JSON.stringify({
            ...(shouldUseFormValues
              ? AccidentReportsParamsSchema.parse(form.value)
              : parameters),
            reportType,
            ...(exportType && { customExport: exportType }),
          }),
        }),
      );
    },
    [download, parameters, towbook.options.api],
  );

  return (
    <form
      {...getFormProps(form)}
      onBlur={() => form.validate()}
      className="flex overflow-auto"
    >
      <FormProvider context={form.context}>
        <Report.Root>
          <Report.Header reportKey={reportKey}>{reportName}</Report.Header>
          <Report.Parameters
            resetButton={
              <Button
                variant="light"
                onClick={() => {
                  handleResetForm();
                }}
              >
                Reset
              </Button>
            }
            updateOptions={
              <ExportOptions getExport={getExport} shouldUseFormValues />
            }
            filterSets={filterSets}
            loadFilterSet={(filters, submitForm) => {
              form.update({ value: filters });
              setParameters(AccidentReportsParamsSchema.parse(filters));
              setSearchParams(formValuesToSearchParams(filters));
              submitForm?.();
            }}
            saveFilterSet={(setName) =>
              setFilterSets((oldPresets) => [
                ...oldPresets,
                {
                  name: setName,
                  filters: form.value as z.infer<
                    typeof AccidentReportsParamsSchema
                  >,
                },
              ])
            }
            renameFilterSet={(setName, newName) =>
              setFilterSets((oldPresets) => {
                const presetIndex = oldPresets.findIndex(
                  (p) => p.name === setName,
                );
                return [
                  ...oldPresets.slice(0, presetIndex),
                  { ...oldPresets[presetIndex], name: newName },
                  ...oldPresets.slice(presetIndex + 1),
                ];
              })
            }
            deleteFilterSet={(setName) =>
              setFilterSets((oldPresets) =>
                oldPresets.filter((p) => p.name !== setName),
              )
            }
          >
            <input {...getInputProps(fields.impounds, { type: "hidden" })} />
            <Parameters.DateRange
              startName={fields.dateStart.name}
              endName={fields.dateEnd.name}
            />
            <Parameters.Company name={fields.companyId.name} />
            <Parameters.Account name={fields.accountId.name} />
            <Parameters.User
              name={fields.performerUserId.name}
              label="Performed By"
            />
          </Report.Parameters>
          <Report.Content>
            <DataTable.Provider
              data={reportQuery.data?.reportData}
              columns={columns}
              options={{
                initialState: {
                  columnPinning: {
                    left: ["checkbox", "actions"],
                  },
                },
              }}
            >
              <Report.QueryState query={reportQuery}>
                <Report.Table className="row-span-2">
                  <DataTable.Root className="flex-1">
                    <DataTable.Toolbar>
                      <DataTable.Search />
                      <DataTable.Actions>
                        <DataTable.Action.Reset />
                        <DataTable.Action.Columns />
                        <DropdownMenu.Root>
                          <DropdownMenu.Trigger asChild>
                            <Button variant="light">
                              <Icon
                                icon={faFileExport}
                                className="text-slate-9 mr-2"
                              />
                              Export
                            </Button>
                          </DropdownMenu.Trigger>
                          <DropdownMenu.Content>
                            <ExportOptions getExport={getExport} />
                          </DropdownMenu.Content>
                        </DropdownMenu.Root>
                        <DataTable.Action.Fullscreen />
                      </DataTable.Actions>
                    </DataTable.Toolbar>
                    <DataTable.Filters />
                    <DataTable.Table>
                      <DataTable.TableHeader />
                      <DataTable.TableBody>
                        {({ rows }) =>
                          rows.map(({ row, virtualItem }) => (
                            <DataTable.TableBodyRow
                              key={row.id}
                              row={row}
                              virtualItem={virtualItem}
                            />
                          ))
                        }
                      </DataTable.TableBody>
                      <DataTable.TableFooter />
                    </DataTable.Table>
                  </DataTable.Root>
                </Report.Table>
              </Report.QueryState>
            </DataTable.Provider>
          </Report.Content>
        </Report.Root>
      </FormProvider>
    </form>
  );
}

function ExportOptions({
  getExport,
  shouldUseFormValues = false,
}: {
  getExport: (exportType?: string, shouldUseFormValues?: boolean) => void;
  shouldUseFormValues?: boolean;
}) {
  return (
    <DropdownMenu.Item
      onSelect={() => getExport(undefined, shouldUseFormValues)}
    >
      Export to Excel
    </DropdownMenu.Item>
  );
}
