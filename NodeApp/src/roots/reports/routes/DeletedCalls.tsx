import React from "react";
import { useTowbook } from "@towbook/sdk";
import { Report } from "../components/Report";
import {
  formatters,
  formValuesToSearchParams,
  getInitialFilters,
  useFilterSetStorage,
  zodFormStringsToStringArray,
} from "../utils";
import { createColumnHelper } from "@tanstack/react-table";
import {
  Button,
  DataTable,
  DropdownMenu,
  ExtractObjectType,
  Icon,
} from "@towbook/flatbed";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import ms from "ms";
import { faFileExport } from "@fortawesome/pro-regular-svg-icons";
import { useDownload } from "../../../hooks/useDownload";
import { useUsers } from "../../../hooks/useUsers";
import { useSearchParams } from "react-router-dom";
import { z } from "zod";
import { parseWithZod } from "@conform-to/zod";
import { FormProvider, getFormProps, useForm } from "@conform-to/react";
import { Parameters } from "../components/Parameters";

const reportType = "DeletedCalls";
const reportName = "Deleted Calls";
const reportFnName = "deletedCalls";
const reportKey = "dispatching/deleted-calls";
const allReportsQueryKey = ["reports", reportFnName];

const DEFAULT_FORM_VALUES: z.input<typeof DeletedCallsParamsSchema> = {};

const DeletedCallsParamsSchema = z.object({
  companyId: zodFormStringsToStringArray.optional(),
});

export default function DeletedCalls() {
  const towbook = useTowbook();
  const users = useUsers();

  const [searchParams, setSearchParams] = useSearchParams();
  const [filterSets, setFilterSets] = useFilterSetStorage<
    {
      name: string;
      filters: z.input<typeof DeletedCallsParamsSchema>;
    }[]
  >(reportType, reportName, reportKey);

  const [parameters, setParameters] = React.useState<z.infer<
    typeof DeletedCallsParamsSchema
  > | null>(
    DeletedCallsParamsSchema.nullable().parse(
      getInitialFilters(searchParams, filterSets),
    ),
  );
  const reportQueryKey = [...allReportsQueryKey, JSON.stringify(parameters)];

  const reportQuery = useQuery({
    queryKey: reportQueryKey,
    queryFn: () => {
      if (!parameters) {
        return null;
      }
      return towbook.reports[reportFnName](parameters);
    },
    enabled: Boolean(parameters),
    staleTime: ms("5m"),
  });

  const queryClient = useQueryClient();

  const [form, fields] = useForm<z.input<typeof DeletedCallsParamsSchema>>({
    defaultValue: getInitialFilters(
      searchParams,
      filterSets,
      DEFAULT_FORM_VALUES,
    ),
    onValidate({ formData }) {
      return parseWithZod(formData, {
        schema: DeletedCallsParamsSchema,
      });
    },
    onSubmit(event, { submission }) {
      event.preventDefault();
      if (submission?.payload) {
        const parsedPayload = DeletedCallsParamsSchema.parse(
          submission.payload,
        );

        /**
         * If the parsed payload is different from the current parameters,
         * update the search params and the parameters.
         *
         * If the parameters are the same, we don't need to update the search params,
         * but we still need to invalidate the query to force a re-fetch.
         *
         * This is necessary because the `useQuery` hook will not re-run
         * if the parameters are the same as the previous parameters.
         */
        if (JSON.stringify(parsedPayload) !== JSON.stringify(parameters)) {
          setSearchParams(formValuesToSearchParams(parsedPayload));
          setParameters(parsedPayload);
        } else {
          queryClient.resetQueries({
            queryKey: ["reports", reportFnName, JSON.stringify(parsedPayload)],
          });
        }
      }
    },
  });

  const handleResetForm = React.useCallback(() => {
    form.update({
      value: DEFAULT_FORM_VALUES,
    });
    setParameters(null);
    setSearchParams(formValuesToSearchParams(DEFAULT_FORM_VALUES));
  }, [form, setSearchParams]);

  const columnHelper = React.useMemo(
    () =>
      createColumnHelper<
        ExtractObjectType<
          NonNullable<(typeof reportQuery)["data"]>["reportData"]
        >
      >(),
    [],
  );

  const columns = React.useMemo(
    () => [
      columnHelper.accessor((rowData) => formatters.date(rowData.date), {
        header: "Date",
      }),
      columnHelper.accessor("callNumber", { header: "Call Number", size: 200 }),
      columnHelper.accessor("invoiceNumber", {
        header: "Invoice Number",
        size: 200,
      }),
      columnHelper.accessor("purchaseOrderNumber", {
        header: "PO Number",
        size: 200,
      }),
      columnHelper.accessor("driver", { header: "Driver" }),
      columnHelper.accessor("truck", { header: "Truck" }),
      columnHelper.accessor("account", { header: "Account" }),
      columnHelper.accessor("reason", { header: "Reason" }),
      columnHelper.accessor("vehicle", { header: "Vehicle" }),
      columnHelper.accessor("status", { header: "Status" }),
      columnHelper.accessor("dispatcher", { header: "Dispatcher", size: 200 }),
      columnHelper.accessor(
        (rowData) => formatters.currency(rowData.invoiceTotal),
        {
          header: "Total",
        },
      ),
      columnHelper.accessor(
        (rowData) => formatters.currency(rowData.paymentsTotal),
        {
          header: "Payments",
          size: 200,
        },
      ),
      columnHelper.accessor(
        (rowData) => formatters.date(rowData.deleteDate || ""),
        { header: "Deleted" },
      ),
      columnHelper.accessor(
        (rowData) =>
          users.data?.find((user) => user.id === rowData.ownerUserId)?.name ||
          "",
        { header: "Deleted By", size: 200 },
      ),
      columnHelper.display({
        id: "report-button",
        cell: (props) => (
          <a
            className="text-blue-11 hover:text-blue-10 hover:no-underline cursor-pointer"
            href={`/DispatchEditor/Editor.aspx?id=${props.row.original.id}&undelete=1`}
            target="_blank"
            rel="noopener noreferrer"
          >
            Undelete
          </a>
        ),
        enableGrouping: false,
        enableResizing: false,
        enablePinning: false,
        enableHiding: false,
      }),
    ],
    [columnHelper, users.data],
  );

  const download = useDownload();

  const getExport = React.useCallback(
    (exportType?: string, shouldUseFormValues = false) => {
      download.downloadFile(
        `${reportType}.xlsx`,
        fetch(`${towbook.options.api}/reports?format=xlsx&export=1`, {
          method: "POST",
          body: JSON.stringify({
            ...(shouldUseFormValues
              ? DeletedCallsParamsSchema.parse(form.value)
              : parameters),
            reportType,
            ...(exportType && { customExport: exportType }),
          }),
        }),
      );
    },
    [download, towbook.options.api],
  );

  return (
    <form
      {...getFormProps(form)}
      onBlur={() => form.validate()}
      className="flex overflow-auto"
    >
      <FormProvider context={form.context}>
        <Report.Root>
          <Report.Header reportKey={reportKey}>{reportName}</Report.Header>
          <Report.Parameters
            resetButton={
              <Button
                variant="light"
                onClick={() => {
                  handleResetForm();
                }}
              >
                Reset
              </Button>
            }
            updateOptions={
              <ExportOptions getExport={getExport} shouldUseFormValues />
            }
            filterSets={filterSets}
            loadFilterSet={(filters, submitForm) => {
              form.update({ value: filters });
              setParameters(DeletedCallsParamsSchema.parse(filters));
              setSearchParams(formValuesToSearchParams(filters));
              submitForm?.();
            }}
            saveFilterSet={(setName) =>
              setFilterSets((oldPresets) => [
                ...oldPresets,
                {
                  name: setName,
                  filters: form.value as z.infer<
                    typeof DeletedCallsParamsSchema
                  >,
                },
              ])
            }
            renameFilterSet={(setName, newName) =>
              setFilterSets((oldPresets) => {
                const presetIndex = oldPresets.findIndex(
                  (p) => p.name === setName,
                );
                return [
                  ...oldPresets.slice(0, presetIndex),
                  { ...oldPresets[presetIndex], name: newName },
                  ...oldPresets.slice(presetIndex + 1),
                ];
              })
            }
            deleteFilterSet={(setName) =>
              setFilterSets((oldPresets) =>
                oldPresets.filter((p) => p.name !== setName),
              )
            }
          >
            <Parameters.Company name={fields.companyId.name} />
          </Report.Parameters>
          <Report.Content>
            <DataTable.Provider
              data={reportQuery.data?.reportData}
              columns={columns}
            >
              <Report.QueryState query={reportQuery}>
                <Report.Table className="row-span-2">
                  <DataTable.Root className="flex-1">
                    <DataTable.Toolbar>
                      <DataTable.Search />
                      <DataTable.Actions>
                        <DataTable.Action.Reset />
                        <DataTable.Action.Columns />
                        <DropdownMenu.Root>
                          <DropdownMenu.Trigger asChild>
                            <Button variant="light">
                              <Icon
                                icon={faFileExport}
                                className="text-slate-9 mr-2"
                              />
                              Export
                            </Button>
                          </DropdownMenu.Trigger>
                          <DropdownMenu.Content>
                            <ExportOptions getExport={getExport} />
                          </DropdownMenu.Content>
                        </DropdownMenu.Root>

                        <DataTable.Action.Fullscreen />
                      </DataTable.Actions>
                    </DataTable.Toolbar>
                    <DataTable.Filters />
                    <DataTable.Table>
                      <DataTable.TableHeader />
                      <DataTable.TableBody>
                        {({ rows }) =>
                          rows.map(({ row, virtualItem }) => (
                            <Report.CallRow
                              key={row.id}
                              row={row}
                              virtualItem={virtualItem}
                            />
                          ))
                        }
                      </DataTable.TableBody>
                      <DataTable.TableFooter />
                    </DataTable.Table>
                  </DataTable.Root>
                </Report.Table>
              </Report.QueryState>
            </DataTable.Provider>
          </Report.Content>
        </Report.Root>
      </FormProvider>
    </form>
  );
}

function ExportOptions({
  getExport,
  shouldUseFormValues = false,
}: {
  getExport: (exportType?: string, shouldUseFormValues?: boolean) => void;
  shouldUseFormValues?: boolean;
}) {
  return (
    <DropdownMenu.Item
      onSelect={() => getExport(undefined, shouldUseFormValues)}
    >
      Export to Excel
    </DropdownMenu.Item>
  );
}
