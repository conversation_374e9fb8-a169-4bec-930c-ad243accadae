import React from "react";
import { useTowbook } from "@towbook/sdk";
import { Report } from "../components/Report";
import {
  formatters,
  formValuesToSearchParams,
  getInitialFilters,
  useFilterSetStorage,
  zodFormStringsToStringArray,
  zodImpounds,
} from "../utils";
import { createColumnHelper } from "@tanstack/react-table";
import {
  Button,
  DataTable,
  DropdownMenu,
  ExtractObjectType,
  Icon,
} from "@towbook/flatbed";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { Parameters } from "../components/Parameters";
import { z } from "zod";
import { parseWithZod } from "@conform-to/zod";
import { FormProvider, getFormProps, useForm } from "@conform-to/react";
import ms from "ms";
import { useSearchParams } from "react-router-dom";
import { faFileExport } from "@fortawesome/pro-regular-svg-icons";
import { useDownload } from "../../../hooks/useDownload";

const reportType = "AccountReceivable";
const reportName = "Accounts Receivable";
const reportFnName = "accountReceivable";
const allReportsQueryKey = ["reports", reportFnName];
const reportKey = "financial/ar-aging";
const today = new Date();

const DEFAULT_FORM_VALUES: z.input<typeof AccountReceivableParamsSchema> = {
  dateEnd: today.toISOString().split("T")[0],
  impounds: "3",
};

const AccountReceivableParamsSchema = z.object({
  dateEnd: z.string(),
  companyId: zodFormStringsToStringArray.optional(),
  accountIds: zodFormStringsToStringArray.optional(),
  accountTypeIds: zodFormStringsToStringArray.optional(),
  impounds: zodImpounds,
});

export default function AccountReceivable() {
  const towbook = useTowbook();

  const [searchParams, setSearchParams] = useSearchParams();
  const [filterSets, setFilterSets] = useFilterSetStorage<
    {
      name: string;
      filters: z.input<typeof AccountReceivableParamsSchema>;
    }[]
  >(reportType, reportName, reportKey);

  const [parameters, setParameters] = React.useState<z.infer<
    typeof AccountReceivableParamsSchema
  > | null>(
    AccountReceivableParamsSchema.nullable().parse(
      getInitialFilters(searchParams, filterSets),
    ),
  );
  const reportQueryKey = [...allReportsQueryKey, JSON.stringify(parameters)];

  const reportQuery = useQuery({
    queryKey: reportQueryKey,
    queryFn: () => {
      if (!parameters) {
        return null;
      }
      return towbook.reports[reportFnName](parameters);
    },
    enabled: Boolean(parameters),
    staleTime: ms("5m"),
  });

  const queryClient = useQueryClient();

  const [form, fields] = useForm<z.input<typeof AccountReceivableParamsSchema>>(
    {
      defaultValue: getInitialFilters(
        searchParams,
        filterSets,
        DEFAULT_FORM_VALUES,
      ),
      onValidate({ formData }) {
        return parseWithZod(formData, {
          schema: AccountReceivableParamsSchema,
        });
      },
      onSubmit(event, { submission }) {
        event.preventDefault();
        if (submission?.payload) {
          const parsedPayload = AccountReceivableParamsSchema.parse(
            submission.payload,
          );

          /**
           * If the parsed payload is different from the current parameters,
           * update the search params and the parameters.
           *
           * If the parameters are the same, we don't need to update the search params,
           * but we still need to invalidate the query to force a re-fetch.
           *
           * This is necessary because the `useQuery` hook will not re-run
           * if the parameters are the same as the previous parameters.
           */
          if (JSON.stringify(parsedPayload) !== JSON.stringify(parameters)) {
            setSearchParams(formValuesToSearchParams(parsedPayload));
            setParameters(parsedPayload);
          } else {
            queryClient.resetQueries({
              queryKey: [
                "reports",
                reportFnName,
                JSON.stringify(parsedPayload),
              ],
            });
          }
        }
      },
    },
  );

  const handleResetForm = React.useCallback(() => {
    form.update({
      value: DEFAULT_FORM_VALUES,
    });
    setParameters(null);
    setSearchParams(formValuesToSearchParams(DEFAULT_FORM_VALUES));
  }, [form, setSearchParams]);

  const columnHelper = React.useMemo(
    () =>
      createColumnHelper<
        ExtractObjectType<
          NonNullable<(typeof reportQuery)["data"]>["reportData"]
        >
      >(),
    [],
  );
  const subColumnHelper = React.useMemo(
    () =>
      createColumnHelper<
        ExtractObjectType<
          ExtractObjectType<
            NonNullable<(typeof reportQuery)["data"]>["reportData"]
          >["currentInvoices"]
        >
      >(),
    [],
  );

  const columns = React.useMemo(
    () => [
      columnHelper.accessor("company", {
        header: "Account",
        cell: (props) => (
          <>
            <span className="mr-2">{props.getValue()}</span>
            <a
              className="text-blue-11 hover:text-blue-10 hover:no-underline cursor-pointer"
              href={`/accounts/account.aspx?id=${props.row.original.accountId}`}
            >
              Open Account in new window
            </a>
          </>
        ),
        size: 450,
      }),
      columnHelper.accessor((rowData) => formatters.currency(rowData.current), {
        header: "Date Current",
        size: 200,
      }),
      columnHelper.accessor((rowData) => formatters.currency(rowData.d1), {
        header: "1-30",
      }),
      columnHelper.accessor((rowData) => formatters.currency(rowData.d31), {
        header: "31-60",
      }),
      columnHelper.accessor((rowData) => formatters.currency(rowData.d61), {
        header: "61-90",
      }),
      columnHelper.accessor((rowData) => formatters.currency(rowData.d91), {
        header: "Over 90",
      }),
      columnHelper.accessor((rowData) => formatters.currency(rowData.total), {
        header: "Total",
      }),
    ],
    [columnHelper],
  );

  const subColumns = React.useMemo(() => {
    const columns = [
      subColumnHelper.accessor("callNumber", {
        header: "Call Number",
        cell: (props) => (
          <a
            className="text-blue-11 hover:text-blue-10 hover:no-underline cursor-pointer"
            onClick={() => editCall(props.row.original.dispatchEntryId)}
          >
            {props.getValue()}
          </a>
        ),
      }),
      subColumnHelper.accessor("invoiceNumber", {
        header: "Invoice Number",
      }),
      subColumnHelper.accessor("purchaseOrderNumber", {
        header: "PO Number",
      }),
      subColumnHelper.accessor((rowData) => formatters.date(rowData.jobDate), {
        header: "Job Date",
      }),
      subColumnHelper.accessor(
        (rowData) => formatters.currency(rowData.balanceDue),
        {
          header: "Balance Due",
        },
      ),
    ];
    return columns;
  }, [subColumnHelper]);

  const download = useDownload();

  const exportTypeNames = React.useMemo(
    () => ({
      detailed: "Detailed Report",
    }),
    [],
  );

  const getExport = React.useCallback(
    (exportType?: string, shouldUseFormValues = false) => {
      download.downloadFile(
        `${reportType}${exportType ? ` (${exportTypeNames[exportType]})` : ""}.xlsx`,
        fetch(`${towbook.options.api}/reports?format=xlsx&export=1`, {
          method: "POST",
          body: JSON.stringify({
            ...(shouldUseFormValues
              ? AccountReceivableParamsSchema.parse(form.value)
              : parameters),
            reportType,
            ...(exportType && { customExport: exportType }),
          }),
        }),
      );
    },
    [download, exportTypeNames, parameters, towbook.options.api],
  );

  return (
    <form
      {...getFormProps(form)}
      onBlur={() => form.validate()}
      className="flex overflow-auto"
    >
      <FormProvider context={form.context}>
        <Report.Root>
          <Report.Header reportKey={reportKey}>{reportName}</Report.Header>
          <Report.Parameters
            resetButton={
              <Button
                variant="light"
                onClick={() => {
                  handleResetForm();
                }}
              >
                Reset
              </Button>
            }
            updateOptions={
              <ExportOptions getExport={getExport} shouldUseFormValues />
            }
            filterSets={filterSets}
            loadFilterSet={(filters, submitForm) => {
              form.update({ value: filters });
              setParameters(AccountReceivableParamsSchema.parse(filters));
              setSearchParams(formValuesToSearchParams(filters));
              submitForm?.();
            }}
            saveFilterSet={(setName) =>
              setFilterSets((oldPresets) => [
                ...oldPresets,
                {
                  name: setName,
                  filters: form.value as z.infer<
                    typeof AccountReceivableParamsSchema
                  >,
                },
              ])
            }
            renameFilterSet={(setName, newName) =>
              setFilterSets((oldPresets) => {
                const presetIndex = oldPresets.findIndex(
                  (p) => p.name === setName,
                );
                return [
                  ...oldPresets.slice(0, presetIndex),
                  { ...oldPresets[presetIndex], name: newName },
                  ...oldPresets.slice(presetIndex + 1),
                ];
              })
            }
            deleteFilterSet={(setName) =>
              setFilterSets((oldPresets) =>
                oldPresets.filter((p) => p.name !== setName),
              )
            }
          >
            <Parameters.Date name={fields.dateEnd.name} label="End Date" />
            <Parameters.Company name={fields.companyId.name} />
            <Parameters.Accounts name={fields.accountIds.name} />
            <Parameters.AccountTypes name={fields.accountTypeIds.name} />
            <Parameters.IncludeImpounds name={fields.impounds.name} />
          </Report.Parameters>
          <Report.Content>
            <DataTable.Provider
              data={reportQuery.data?.reportData}
              columns={columns}
              options={{
                initialState: {
                  columnPinning: {
                    left: ["checkbox", "actions"],
                  },
                },
              }}
            >
              <Report.QueryState query={reportQuery}>
                <Report.Summary>
                  {Object.entries(reportQuery.data?.summary || {}).map(
                    ([key, value]) => (
                      <Report.SummaryItem key={key} name={key} value={value} />
                    ),
                  )}
                </Report.Summary>
                <Report.ChartSection className="col-span-2">
                  <Report.Chart
                    className="row-span-2"
                    dateGroupArray={reportQuery.data?.dateGroupArray}
                    valueGroupArray={reportQuery.data?.valueGroupArray}
                    yAxisLabel={reportQuery.data?.yAxisLabel}
                    type="bar"
                    getYLabel={(value) => formatters.currency(value)}
                  />
                </Report.ChartSection>
                <Report.Table>
                  <DataTable.Root className="flex-1">
                    <DataTable.Toolbar>
                      <DataTable.Search />
                      <DataTable.Actions>
                        <DataTable.Action.Reset />
                        <DataTable.Action.Columns />
                        <DropdownMenu.Root>
                          <DropdownMenu.Trigger asChild>
                            <Button variant="light">
                              <Icon
                                icon={faFileExport}
                                className="text-slate-9 mr-2"
                              />
                              Export
                            </Button>
                          </DropdownMenu.Trigger>
                          <DropdownMenu.Content>
                            <ExportOptions getExport={getExport} />
                          </DropdownMenu.Content>
                        </DropdownMenu.Root>

                        <DataTable.Action.Fullscreen />
                      </DataTable.Actions>
                    </DataTable.Toolbar>
                    <DataTable.Filters />
                    <DataTable.Table>
                      <DataTable.TableHeader />
                      <DataTable.TableBody>
                        {({ rows }) =>
                          rows.map(({ row, virtualItem }) => (
                            <Report.CallTableRow
                              key={row.id}
                              row={row}
                              virtualItem={virtualItem}
                              columns={subColumns}
                              dataSources={[
                                "currentInvoices",
                                "d1Invoices",
                                "d31Invoices",
                                "d61Invoices",
                                "d91Invoices",
                              ]}
                            />
                          ))
                        }
                      </DataTable.TableBody>
                      <DataTable.TableFooter />
                    </DataTable.Table>
                  </DataTable.Root>
                </Report.Table>
              </Report.QueryState>
            </DataTable.Provider>
          </Report.Content>
        </Report.Root>
      </FormProvider>
    </form>
  );
}

function ExportOptions({
  getExport,
  shouldUseFormValues = false,
}: {
  getExport: (exportType?: string, shouldUseFormValues?: boolean) => void;
  shouldUseFormValues?: boolean;
}) {
  return (
    <>
      <DropdownMenu.Item
        onSelect={() => getExport(undefined, shouldUseFormValues)}
      >
        Export to Excel
      </DropdownMenu.Item>
      <DropdownMenu.Item
        onSelect={() => getExport("detailed", shouldUseFormValues)}
      >
        Export Detailed Report
      </DropdownMenu.Item>
    </>
  );
}
