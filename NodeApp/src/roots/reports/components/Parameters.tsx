import React from "react";
import {
  Field,
  InputDate,
  InputDateRange,
  MultiBox,
  Select,
} from "@towbook/flatbed";
import { getInputProps, useField, useInputControl } from "@conform-to/react";
import { useReasons } from "../../../hooks/useReasons";
import { useAccounts } from "../../../hooks/useAccounts";
import { useAccountTypes } from "../../../hooks/useAccountTypes";
import { useRateItems } from "../../../hooks/useRateItems";
import { useDrivers } from "../../../hooks/useDrivers";
import { useTrucksFull } from "../../../hooks/useTrucks";
import { useUsers } from "../../../hooks/useUsers";
import { useMasterAccounts } from "../../../hooks/useMasterAccounts";
import { usePaymentTypes } from "../../../hooks/usePaymentTypes";
import { UserTypes } from "@towbook/sdk";
import { useStickerStatuses } from "../../../hooks/useStickerStatuses";
import { useStickerReasons } from "../../../hooks/useStickerReasons";
import { useCompanies } from "../../../hooks/useCompanies";
import { useHasFeature } from "../../../hooks/useFeatures";

const exampleDate = "1970-01-01";
const dateLength = exampleDate.length;
const exampleDatetime = "1970-01-01:T00:00:00";
const datetimeLength = exampleDatetime.length - 1;
function ParametersDateRange({
  startName,
  endName,
  granularity = "day",
}: {
  startName: string;
  endName: string;
  granularity?: React.ComponentProps<typeof InputDateRange>["granularity"];
}) {
  const [startMeta] = useField<string>(startName);
  const [endMeta] = useField<string>(endName);

  const defaultStart = React.useMemo(() => {
    if (!startMeta.initialValue) return "";
    return startMeta.initialValue.substring(
      0,
      granularity === "day" ? dateLength : datetimeLength,
    );
  }, [startMeta.initialValue, granularity]);
  const defaultEnd = React.useMemo(() => {
    if (!endMeta.initialValue) return "";
    return endMeta.initialValue.substring(
      0,
      granularity === "day" ? dateLength : datetimeLength,
    );
  }, [endMeta.initialValue, granularity]);

  return (
    <>
      <Field.Root>
        <Field.Label>Start Date</Field.Label>
        <Field.Control>
          <InputDate
            {...getInputProps(startMeta, { type: "date" })}
            defaultValue={defaultStart}
            granularity={granularity}
          />
        </Field.Control>
        <Field.Error>{startMeta.errors}</Field.Error>
      </Field.Root>
      <Field.Root>
        <Field.Label>End Date</Field.Label>
        <Field.Control>
          <InputDate
            {...getInputProps(endMeta, { type: "date" })}
            defaultValue={defaultEnd}
            granularity={granularity}
          />
        </Field.Control>
        <Field.Error>{endMeta.errors}</Field.Error>
      </Field.Root>
    </>
  );
}

function ParametersDate({
  name,
  label = "Date",
}: {
  name: string;
  label?: string;
}) {
  const [meta] = useField<string>(name);

  return (
    <Field.Root>
      <Field.Label>{label}</Field.Label>
      <Field.Control>
        <InputDate {...getInputProps(meta, { type: "date" })} />
      </Field.Control>
      <Field.Error>{meta.errors}</Field.Error>
    </Field.Root>
  );
}

function ParametersCompany({ name }: { name: string }) {
  const [meta] = useField<string>(name);
  const { data: companies = [] } = useCompanies();

  if (companies.length < 2) {
    return null;
  }

  return (
    <Field.Root>
      <Field.Label>Company</Field.Label>
      <Field.Control>
        <Select
          name={meta.name}
          defaultValue={
            Array.isArray(meta.initialValue)
              ? meta.initialValue[0]
              : meta.initialValue
          }
        >
          <option value="">All</option>
          {(companies || []).map((company) => (
            <option key={company.id} value={company.id}>
              {company.name}
            </option>
          ))}
        </Select>
      </Field.Control>
      <Field.Error>{meta.errors}</Field.Error>
    </Field.Root>
  );
}

function ParametersCompanies({ name }: { name: string }) {
  const { data: companies = [] } = useCompanies();
  const mappedCompanies = React.useMemo(
    () => companies.map((company) => ({ id: company.id, name: company.name })),
    [companies],
  );

  const [meta] = useField<string[]>(name);
  const { value, change } = useInputControl(meta);
  const selectedCompanies = React.useMemo(
    () =>
      Array.isArray(value)
        ? value.flatMap(
            (id) =>
              mappedCompanies.find((company) => String(company.id) === id) ||
              [],
          )
        : [],
    [value],
  );

  if (companies.length < 2) {
    return null;
  }

  return (
    <Field.Root>
      <Field.Label>Company</Field.Label>
      <Field.Control>
        <MultiBox
          options={mappedCompanies}
          selectedOptions={selectedCompanies}
          setSelectedOptions={(value) => {
            change(value.map((option) => String(option.id)));
          }}
          placeholder="All"
        />
      </Field.Control>
      <Field.Error>{meta.errors}</Field.Error>
    </Field.Root>
  );
}

function ParametersBasisDate({ name }: { name: string }) {
  const [meta] = useField<string>(name);

  return (
    <Field.Root>
      <Field.Label>Base Report on</Field.Label>
      <Field.Control>
        <Select name={meta.name} defaultValue={meta.initialValue}>
          <option value="0">Completion Date</option>
          <option value="1">Lock Date</option>
          <option value="2">Modified Date</option>
        </Select>
      </Field.Control>
      <Field.Error>{meta.errors}</Field.Error>
    </Field.Root>
  );
}

function ParametersReason({ name }: { name: string }) {
  const [meta] = useField<string>(name);
  const { data: reasons } = useReasons();

  return (
    <Field.Root>
      <Field.Label>Reason</Field.Label>
      <Field.Control>
        <Select name={meta.name} defaultValue={meta.initialValue}>
          <option value="">All</option>
          {(reasons || []).map((reason) => (
            <option key={reason.id} value={reason.id}>
              {reason.reason}
            </option>
          ))}
        </Select>
      </Field.Control>
      <Field.Error>{meta.errors}</Field.Error>
    </Field.Root>
  );
}

function ParametersMasterAccount({ name }: { name: string }) {
  const [meta] = useField<string>(name);
  const { data: masterAccounts } = useMasterAccounts(5); //5 = MotorClub

  return (
    <Field.Root>
      <Field.Label>Master Account</Field.Label>
      <Field.Control>
        <Select name={meta.name} defaultValue={meta.initialValue}>
          <option value="">All</option>
          {(masterAccounts || []).map((masterAccount) => (
            <option
              key={masterAccount.masterAccountId}
              value={masterAccount.masterAccountId}
            >
              {masterAccount.name}
            </option>
          ))}
        </Select>
      </Field.Control>
      <Field.Error>{meta.errors}</Field.Error>
    </Field.Root>
  );
}

function ParametersSubAccounts({ name }: { name: string }) {
  const { data: accounts } = useAccounts();
  const subAccounts = React.useMemo(
    () =>
      accounts
        ?.filter(
          (account) =>
            account.defaultSubcontractorAccountId &&
            account.defaultSubcontractorAccountId < 0,
        )
        ?.map((account) => ({ id: account.id, name: account.name })) || [],
    [accounts],
  );

  const [meta] = useField<string[]>(name);
  const { value, change } = useInputControl(meta);
  const [selectedAccounts, setSelectedAccounts] = React.useState(
    Array.isArray(value)
      ? value.flatMap(
          (id) =>
            subAccounts.find((account) => String(account.id) === id) || [],
        )
      : [],
  );

  return (
    <Field.Root>
      <Field.Label>Rotation Account</Field.Label>
      <Field.Control>
        <MultiBox
          options={subAccounts}
          selectedOptions={selectedAccounts}
          setSelectedOptions={(value) => {
            setSelectedAccounts(value);
            change(value.map((option) => String(option.id)));
          }}
          placeholder="All"
        />
      </Field.Control>
      <Field.Error>{meta.errors}</Field.Error>
    </Field.Root>
  );
}

function ParametersAccount({
  name,
  companyFieldName,
}: {
  name: string;
  companyFieldName?: string;
}) {
  const { data: accounts = [] } = useAccounts({
    options: { headers: { "X-Company": "all" } },
  });
  const accountsByCompany = useEntitiesByCompany(accounts, companyFieldName);

  const options = React.useMemo(() => {
    if (accountsByCompany.length < 2) {
      return accountsByCompany.flatMap((company) =>
        company.subEntities.map((truck) => (
          <option key={truck.id} value={truck.id}>
            {truck.name}
          </option>
        )),
      );
    }

    return accountsByCompany.map((company) => (
      <optgroup key={company.id} label={company.name}>
        {company.subEntities.map((truck) => (
          <option key={truck.id} value={truck.id}>
            {truck.name}
          </option>
        ))}
      </optgroup>
    ));
  }, [accountsByCompany]);
  const [meta] = useField<string>(name);

  return (
    <Field.Root>
      <Field.Label>Account</Field.Label>
      <Field.Control>
        <Select name={meta.name} defaultValue={meta.initialValue}>
          <option value="">All</option>
          {options}
        </Select>
      </Field.Control>
      <Field.Error>{meta.errors}</Field.Error>
    </Field.Root>
  );
}

function ParametersAccounts({
  name,
  companyFieldName = "companyId",
}: {
  name: string;
  companyFieldName?: string;
}) {
  const { data: accounts = [] } = useAccounts({
    options: { headers: { "X-Company": "all" } },
  });
  const mappedAccounts = useMappedEntities(accounts, companyFieldName);
  const [meta] = useField<string[]>(name);
  const { value, change } = useInputControl(meta);
  const selectedAccounts = React.useMemo(
    () =>
      Array.isArray(value)
        ? value.flatMap(
            (id) =>
              mappedAccounts.find((account) => String(account.id) === id) || [],
          )
        : [],
    [value],
  );

  const [companyMeta] = useField<string[]>(companyFieldName);
  const selectedCompanyIds = formStringToStringArray(companyMeta.value).join(
    "-",
  );
  React.useEffect(() => {
    change([]);
  }, [selectedCompanyIds]);

  return (
    <Field.Root>
      <Field.Label>Account</Field.Label>
      <Field.Control>
        <MultiBox
          options={mappedAccounts}
          selectedOptions={selectedAccounts}
          setSelectedOptions={(value) => {
            change(value.map((option) => String(option.id)));
          }}
          placeholder="All"
        />
      </Field.Control>
      <Field.Error>{meta.errors}</Field.Error>
    </Field.Root>
  );
}

function ParametersAccountTypes({ name }: { name: string }) {
  const { data: accountTypes } = useAccountTypes();
  const mappedAccountTypes = React.useMemo(
    () =>
      accountTypes?.map((accountType) => ({
        id: accountType.type,
        name: accountType.name,
      })) || [],
    [accountTypes],
  );

  const [meta] = useField<string[]>(name);
  const { value, change } = useInputControl(meta);
  const [selectedAccountTypes, setSelectedAccountTypes] = React.useState(
    Array.isArray(value)
      ? value.flatMap(
          (id) =>
            mappedAccountTypes.find(
              (accountType) => String(accountType.id) === id,
            ) || [],
        )
      : [],
  );

  return (
    <Field.Root>
      <Field.Label>Account Type</Field.Label>
      <Field.Control>
        <MultiBox
          options={mappedAccountTypes}
          selectedOptions={selectedAccountTypes}
          setSelectedOptions={(value) => {
            setSelectedAccountTypes(value);
            change(value.map((option) => String(option.id)));
          }}
          placeholder="All"
        />
      </Field.Control>
      <Field.Error>{meta.errors}</Field.Error>
    </Field.Root>
  );
}

function ParametersDriver({
  name,
  companyFieldName,
}: {
  name: string;
  companyFieldName?: string;
}) {
  const { data: drivers = [] } = useDrivers({
    options: { headers: { "X-Company": "all" } },
  });
  const formattedDrivers = React.useMemo(
    () => markEntitiesInactive(drivers, (driver) => !!driver.endDate),
    [drivers],
  );
  const driversByCompany = useEntitiesByCompany(
    formattedDrivers,
    companyFieldName,
  );

  const options = React.useMemo(() => {
    if (driversByCompany.length < 2) {
      return driversByCompany.flatMap((company) =>
        company.subEntities.map((truck) => (
          <option key={truck.id} value={truck.id}>
            {truck.name}
          </option>
        )),
      );
    }

    return driversByCompany.map((company) => (
      <optgroup key={company.id} label={company.name}>
        {company.subEntities.map((truck) => (
          <option key={truck.id} value={truck.id}>
            {truck.name}
          </option>
        ))}
      </optgroup>
    ));
  }, [driversByCompany]);
  const [meta] = useField<string>(name);

  return (
    <Field.Root>
      <Field.Label>Driver</Field.Label>
      <Field.Control>
        <Select name={meta.name} defaultValue={meta.initialValue}>
          <option value="">All</option>
          {options}
        </Select>
      </Field.Control>
      <Field.Error>{meta.errors}</Field.Error>
    </Field.Root>
  );
}

function ParametersDrivers({
  name,
  companyFieldName = "companyId",
}: {
  name: string;
  companyFieldName?: string;
}) {
  const { data: drivers = [] } = useDrivers({
    options: { headers: { "X-Company": "all" } },
  });
  const formattedDrivers = React.useMemo(
    () => markEntitiesInactive(drivers, (driver) => !!driver.endDate),
    [drivers],
  );
  const mappedDrivers = useMappedEntities(formattedDrivers, companyFieldName);
  const [meta] = useField<string[]>(name);
  const { value, change } = useInputControl(meta);
  const selectedDrivers = React.useMemo(
    () =>
      Array.isArray(value)
        ? value.flatMap(
            (id) =>
              mappedDrivers.find((driver) => String(driver.id) === id) || [],
          )
        : [],
    [value],
  );

  const [companyMeta] = useField<string[]>(companyFieldName);
  const selectedCompanyIds = formStringToStringArray(companyMeta.value).join(
    "-",
  );
  React.useEffect(() => {
    change([]);
  }, [selectedCompanyIds]);

  return (
    <Field.Root>
      <Field.Label>Driver</Field.Label>
      <Field.Control>
        <MultiBox
          options={mappedDrivers}
          selectedOptions={selectedDrivers}
          setSelectedOptions={(value) => {
            change(value.map((option) => String(option.id)));
          }}
          placeholder="All"
        />
      </Field.Control>
      <Field.Error>{meta.errors}</Field.Error>
    </Field.Root>
  );
}

function ParametersTruck({
  name,
  companyFieldName,
}: {
  name: string;
  companyFieldName?: string;
}) {
  const { data: trucks = [] } = useTrucksFull({
    options: { headers: { "X-Company": "all" } },
  });
  const formattedTrucks = React.useMemo(
    () => markEntitiesInactive(trucks, (truck) => !truck.isActive),
    [trucks],
  );
  const trucksByCompany = useEntitiesByCompany(
    formattedTrucks,
    companyFieldName,
  );

  const options = React.useMemo(() => {
    if (trucksByCompany.length < 2) {
      return trucksByCompany.flatMap((company) =>
        company.subEntities.map((truck) => (
          <option key={truck.id} value={truck.id}>
            {truck.name}
          </option>
        )),
      );
    }

    return trucksByCompany.map((company) => (
      <optgroup key={company.id} label={company.name}>
        {company.subEntities.map((truck) => (
          <option key={truck.id} value={truck.id}>
            {truck.name}
          </option>
        ))}
      </optgroup>
    ));
  }, [trucksByCompany]);

  const [meta] = useField<string>(name);

  return (
    <Field.Root>
      <Field.Label>Truck</Field.Label>
      <Field.Control>
        <Select name={meta.name} defaultValue={meta.initialValue}>
          <option value="">All</option>
          {options}
        </Select>
      </Field.Control>
      <Field.Error>{meta.errors}</Field.Error>
    </Field.Root>
  );
}

function ParametersTrucks({
  name,
  companyFieldName = "companyId",
}: {
  name: string;
  companyFieldName?: string;
}) {
  const { data: trucks = [] } = useTrucksFull({
    options: { headers: { "X-Company": "all" } },
  });
  const formattedTrucks = React.useMemo(
    () => markEntitiesInactive(trucks, (truck) => !truck.isActive),
    [trucks],
  );
  const mappedTrucks = useMappedEntities(formattedTrucks, companyFieldName);
  const [meta] = useField<string[]>(name);
  const { value, change } = useInputControl(meta);
  const selectedTrucks = React.useMemo(
    () =>
      Array.isArray(value)
        ? value.flatMap(
            (id) => mappedTrucks.find((truck) => String(truck.id) === id) || [],
          )
        : [],
    [value],
  );

  const [companyMeta] = useField<string[]>(companyFieldName);
  const selectedCompanyIds = formStringToStringArray(companyMeta.value).join(
    "-",
  );
  React.useEffect(() => {
    change([]);
  }, [selectedCompanyIds]);

  return (
    <Field.Root>
      <Field.Label>Truck</Field.Label>
      <Field.Control>
        <MultiBox
          options={mappedTrucks}
          selectedOptions={selectedTrucks}
          setSelectedOptions={(value) => {
            change(value.map((option) => String(option.id)));
          }}
          placeholder="All"
        />
      </Field.Control>
      <Field.Error>{meta.errors}</Field.Error>
    </Field.Root>
  );
}

function ParametersWeight({ name }: { name: string }) {
  const [meta] = useField<string>(name);

  return (
    <Field.Root>
      <Field.Label>Weight Class</Field.Label>
      <Field.Control>
        <Select name={meta.name} defaultValue={meta.initialValue}>
          <option value="">All</option>
          <option value="1">Light</option>
          <option value="2">Medium</option>
          <option value="3">Heavy</option>
          <option value="4">Motorcycle</option>
          <option value="5">Trailer</option>
          <option value="6">Tractor</option>
          <option value="7">Other</option>
          <option value="8">Equipment</option>
          <option value="9">Container</option>
          <option value="10">Material</option>
        </Select>
      </Field.Control>
      <Field.Error>{meta.errors}</Field.Error>
    </Field.Root>
  );
}

function ParametersUser({
  name,
  userType,
  label,
  companyFieldName,
}: {
  name: string;
  userType?: UserTypes;
  label?: string;
  companyFieldName?: string;
}) {
  const { data: users = [] } = useUsers({
    options: { headers: { "X-Company": "all" } },
  });
  const filteredUsers = React.useMemo(
    () => (userType ? users.filter((user) => user.type === userType) : users),
    [userType, users],
  );
  const labeledUsers = React.useMemo(
    () => markEntitiesInactive(filteredUsers, (user) => user.disabled),
    [filteredUsers],
  );
  const usersByCompany = useEntitiesByCompany(labeledUsers, companyFieldName);

  const options = React.useMemo(() => {
    if (usersByCompany.length < 2) {
      return usersByCompany.flatMap((company) =>
        company.subEntities.map((truck) => (
          <option key={truck.id} value={truck.id}>
            {truck.name}
          </option>
        )),
      );
    }

    return usersByCompany.map((company) => (
      <optgroup key={company.id} label={company.name}>
        {company.subEntities.map((truck) => (
          <option key={truck.id} value={truck.id}>
            {truck.name}
          </option>
        ))}
      </optgroup>
    ));
  }, [usersByCompany]);
  const [meta] = useField<string>(name);

  return (
    <Field.Root>
      <Field.Label>{label || "User"}</Field.Label>
      <Field.Control>
        <Select name={meta.name} defaultValue={meta.initialValue}>
          <option value="">All</option>
          {options}
        </Select>
      </Field.Control>
      <Field.Error>{meta.errors}</Field.Error>
    </Field.Root>
  );
}

function ParametersUserType({ name }: { name: string }) {
  const [meta] = useField<string>(name);

  return (
    <Field.Root>
      <Field.Label>User Type</Field.Label>
      <Field.Control>
        <Select name={meta.name} defaultValue={meta.initialValue}>
          <option value="">All</option>
          <option value={UserTypes.Manager}>Managers</option>
          <option value={UserTypes.Dispatcher}>Dispatchers</option>
          <option value={UserTypes.Driver}>Drivers</option>
        </Select>
      </Field.Control>
      <Field.Error>{meta.errors}</Field.Error>
    </Field.Root>
  );
}

function ParametersPaymentStatus({ name }: { name: string }) {
  const [meta] = useField<string>(name);

  return (
    <Field.Root>
      <Field.Label>Payment Status</Field.Label>
      <Field.Control>
        <Select name={meta.name} defaultValue={meta.initialValue}>
          <option value="">All</option>
          <option value="p">Paid</option>
          <option value="u">Unpaid</option>
          <option value="partially">Partially</option>
        </Select>
      </Field.Control>
      <Field.Error>{meta.errors}</Field.Error>
    </Field.Root>
  );
}

function ParametersPaymentType({ name }: { name: string }) {
  const [meta] = useField<string>(name);
  const { data: paymentTypes = [] } = usePaymentTypes();

  return (
    <Field.Root>
      <Field.Label>Payment Type</Field.Label>
      <Field.Control>
        <Select name={meta.name} defaultValue={meta.initialValue}>
          <option value="">All</option>
          {paymentTypes.map((paymentType) => (
            <option key={paymentType.id} value={paymentType.id}>
              {paymentType.name}
            </option>
          ))}
        </Select>
      </Field.Control>
      <Field.Error>{meta.errors}</Field.Error>
    </Field.Root>
  );
}

function ParametersPaymentTypes({ name }: { name: string }) {
  const { data: paymentTypes } = usePaymentTypes();
  const mappedPaymentTypes = React.useMemo(
    () =>
      paymentTypes?.map((paymentType) => ({
        id: paymentType.id,
        name: paymentType.name,
      })) || [],
    [paymentTypes],
  );

  const [meta] = useField<string[]>(name);
  const { value, change } = useInputControl(meta);
  const [selectedPaymentTypes, setSelectedPaymentTypes] = React.useState(
    Array.isArray(value)
      ? value.flatMap(
          (id) =>
            mappedPaymentTypes.find(
              (paymentType) => String(paymentType.id) === id,
            ) || [],
        )
      : [],
  );

  return (
    <Field.Root>
      <Field.Label>Payment Type</Field.Label>
      <Field.Control>
        <MultiBox
          options={mappedPaymentTypes}
          selectedOptions={selectedPaymentTypes}
          setSelectedOptions={(value) => {
            setSelectedPaymentTypes(value);
            change(value.map((option) => String(option.id)));
          }}
          placeholder="All"
        />
      </Field.Control>
      <Field.Error>{meta.errors}</Field.Error>
    </Field.Root>
  );
}

function ParametersPaymentOption({ name }: { name: string }) {
  const [meta] = useField<string>(name);

  return (
    <Field.Root>
      <Field.Label>Report Includes</Field.Label>
      <Field.Control>
        <Select name={meta.name} defaultValue={meta.initialValue}>
          <option value="0">Payments</option>
          <option value="1">Voided Payments</option>
          <option value="2">All Payments</option>
        </Select>
      </Field.Control>
      <Field.Error>{meta.errors}</Field.Error>
    </Field.Root>
  );
}

function ParametersPaymentOptionWithTip({ name }: { name: string }) {
  const hasFeature = useHasFeature();
  const [meta] = useField<string>(name);

  if (!hasFeature("Square Tipping")) {
    return <ParametersPaymentOption name={name} />;
  }

  return (
    <Field.Root>
      <Field.Label>Report Includes</Field.Label>
      <Field.Control>
        <Select name={meta.name} defaultValue={meta.initialValue}>
          <option value="0">Payments + Tips</option>
          <option value="4">Payments Only</option>
          <option value="1">Voided Payments</option>
          <option value="3">Tips Only</option>
          <option value="2">All Payments</option>
        </Select>
      </Field.Control>
      <Field.Error>{meta.errors}</Field.Error>
    </Field.Root>
  );
}

function ParametersBasisPayment({ name }: { name: string }) {
  const [meta] = useField<string>(name);

  return (
    <Field.Root>
      <Field.Label>Base Report On</Field.Label>
      <Field.Control>
        <Select name={meta.name} defaultValue={meta.initialValue}>
          <option value="">Payment Date</option>
          <option value="1">Recorded Date</option>
          <option value="2">Verified Date</option>
        </Select>
      </Field.Control>
      <Field.Error>{meta.errors}</Field.Error>
    </Field.Root>
  );
}

function ParametersBaseReportOnDate({ name }: { name: string }) {
  const [meta] = useField<string>(name);

  return (
    <Field.Root>
      <Field.Label>Base Report On</Field.Label>
      <Field.Control>
        <Select name={meta.name} defaultValue={meta.initialValue}>
          <option value="0">Date Entered/Recorded</option>
          <option value="1">Payment Date</option>
        </Select>
      </Field.Control>
      <Field.Error>{meta.errors}</Field.Error>
    </Field.Root>
  );
}

function ParametersVerifiedStatus({ name }: { name: string }) {
  const [meta] = useField<string>(name);

  return (
    <Field.Root>
      <Field.Label>Verified Status</Field.Label>
      <Field.Control>
        <Select name={meta.name} defaultValue={meta.initialValue}>
          <option value="">All</option>
          <option value="1">Yes</option>
          <option value="0">No</option>
        </Select>
      </Field.Control>
      <Field.Error>{meta.errors}</Field.Error>
    </Field.Root>
  );
}

function ParametersRateItems({
  name,
  companyFieldName = "companyId",
}: {
  name: string;
  companyFieldName?: string;
}) {
  const { data: rateItems = [] } = useRateItems({
    options: { headers: { "X-Company": "all" } },
  });
  const mappedRateItems = useMappedEntities(rateItems, companyFieldName);
  const [meta] = useField<string[]>(name);
  const { value, change } = useInputControl(meta);
  const selectedRateItems = React.useMemo(
    () =>
      Array.isArray(value)
        ? value.flatMap(
            (id) =>
              mappedRateItems.find((rateItem) => String(rateItem.id) === id) ||
              [],
          )
        : [],
    [value],
  );

  const [companyMeta] = useField<string[]>(companyFieldName);
  const selectedCompanyIds = formStringToStringArray(companyMeta.value).join(
    "-",
  );
  React.useEffect(() => {
    change([]);
  }, [selectedCompanyIds]);

  return (
    <Field.Root>
      <Field.Label>Service Item</Field.Label>
      <Field.Control>
        <MultiBox
          options={mappedRateItems}
          selectedOptions={selectedRateItems}
          setSelectedOptions={(value) => {
            change(value.map((option) => String(option.id)));
          }}
          placeholder="All"
        />
      </Field.Control>
      <Field.Error>{meta.errors}</Field.Error>
    </Field.Root>
  );
}

function ParametersStickerStatus({ name }: { name: string }) {
  const [meta] = useField<string>(name);
  const { data: statuses = [] } = useStickerStatuses();

  return (
    <Field.Root>
      <Field.Label>Status</Field.Label>
      <Field.Control>
        <Select name={meta.name} defaultValue={meta.initialValue}>
          <option value="">All</option>
          {statuses.map((status) => (
            <option key={status.id} value={status.id}>
              {status.name}
            </option>
          ))}
        </Select>
      </Field.Control>
      <Field.Error>{meta.errors}</Field.Error>
    </Field.Root>
  );
}

function ParametersStickerReason({ name }: { name: string }) {
  const [meta] = useField<string>(name);
  const { data: reasons = [] } = useStickerReasons();

  return (
    <Field.Root>
      <Field.Label>Reason</Field.Label>
      <Field.Control>
        <Select name={meta.name} defaultValue={meta.initialValue}>
          <option value="">All</option>
          {reasons.map((reason) => (
            <option key={reason.id} value={reason.id}>
              {reason.name}
            </option>
          ))}
        </Select>
      </Field.Control>
      <Field.Error>{meta.errors}</Field.Error>
    </Field.Root>
  );
}

function ParametersTaxExemptStatus({ name }: { name: string }) {
  const [meta] = useField<string>(name);

  return (
    <Field.Root>
      <Field.Label>Tax Exempt</Field.Label>
      <Field.Control>
        <Select name={meta.name} defaultValue={meta.initialValue}>
          <option value="">All</option>
          <option value="1">Tax Exempt Only</option>
        </Select>
      </Field.Control>
      <Field.Error>{meta.errors}</Field.Error>
    </Field.Root>
  );
}

function ParametersIncludeImpounds({ name }: { name: string }) {
  const [meta] = useField<string>(name);

  return (
    <Field.Root>
      <Field.Label>Report Includes</Field.Label>
      <Field.Control>
        <Select name={meta.name} defaultValue={meta.initialValue}>
          <option value="0">All Impounds</option>
          <option value="3">
            All impounds (exclude storage on unreleased calls)
          </option>
          <option value="5">Exclude storage lots</option>
          <option value="1">Impounds released in date range only</option>
          <option value="2">No impounds</option>
        </Select>
      </Field.Control>
      <Field.Error>{meta.errors}</Field.Error>
    </Field.Root>
  );
}

function ParametersPushedToQuickBooks({ name }: { name: string }) {
  const [meta] = useField<string>(name);

  return (
    <Field.Root>
      <Field.Label>Pushed To QuickBooks</Field.Label>
      <Field.Control>
        <Select name={meta.name} defaultValue={meta.initialValue}>
          <option value="">All</option>
          <option value="1">Yes</option>
          <option value="0">No</option>
        </Select>
      </Field.Control>
      <Field.Error>{meta.errors}</Field.Error>
    </Field.Root>
  );
}

function ParametersIsAudited({ name }: { name: string }) {
  const [meta] = useField<string>(name);

  return (
    <Field.Root>
      <Field.Label>Audit Status</Field.Label>
      <Field.Control>
        <Select name={meta.name} defaultValue={meta.initialValue}>
          <option value="">All</option>
          <option value="1">Yes</option>
          <option value="0">No</option>
        </Select>
      </Field.Control>
      <Field.Error>{meta.errors}</Field.Error>
    </Field.Root>
  );
}

function ParametersIsLocked({ name }: { name: string }) {
  const [meta] = useField<string>(name);

  return (
    <Field.Root>
      <Field.Label>Locked Status</Field.Label>
      <Field.Control>
        <Select name={meta.name} defaultValue={meta.initialValue}>
          <option value="">All</option>
          <option value="1">Yes</option>
          <option value="0">No</option>
        </Select>
      </Field.Control>
      <Field.Error>{meta.errors}</Field.Error>
    </Field.Root>
  );
}

function ParametersIsBilled({ name }: { name: string }) {
  const [meta] = useField<string>(name);

  return (
    <Field.Root>
      <Field.Label>Billed Status</Field.Label>
      <Field.Control>
        <Select name={meta.name} defaultValue={meta.initialValue}>
          <option value="">All</option>
          <option value="1">Yes</option>
          <option value="0">No</option>
        </Select>
      </Field.Control>
      <Field.Error>{meta.errors}</Field.Error>
    </Field.Root>
  );
}

function ParametersAccountingMethod({ name }: { name: string }) {
  const [meta] = useField<string>(name);

  return (
    <Field.Root>
      <Field.Label>Accounting Method</Field.Label>
      <Field.Control>
        <Select name={meta.name} defaultValue={meta.initialValue}>
          <option value="0">Cash</option>
          <option value="1">Accrual</option>
        </Select>
      </Field.Control>
      <Field.Error>{meta.errors}</Field.Error>
    </Field.Root>
  );
}

function ParametersTotalInvoicedFilter({ name }: { name: string }) {
  const [meta] = useField<string>(name);

  return (
    <Field.Root>
      <Field.Label>Display By</Field.Label>
      <Field.Control>
        <Select name={meta.name} defaultValue={meta.initialValue}>
          <option value="0">Total Invoiced</option>
          <option value="1">Total Calls</option>
        </Select>
      </Field.Control>
      <Field.Error>{meta.errors}</Field.Error>
    </Field.Root>
  );
}

function ParametersImpounds({ name }: { name: string }) {
  const [meta] = useField<string>(name);

  return (
    <Field.Root>
      <Field.Label>Report Includes</Field.Label>
      <Field.Control>
        <Select name={meta.name} defaultValue={meta.initialValue}>
          <option value="0">All Impounds</option>
          <option value="1">Impounds released in date range only</option>
          <option value="2">No Impounds</option>
        </Select>
      </Field.Control>
      <Field.Error>{meta.errors}</Field.Error>
    </Field.Root>
  );
}

function ParametersPayouts({ name }: { name: string }) {
  const [meta] = useField<string>(name);

  return (
    <Field.Root>
      <Field.Label>Payouts</Field.Label>
      <Field.Control>
        <Select name={meta.name} defaultValue={meta.initialValue}>
          <option value="">All</option>
          <option value="0">Exclude Payouts</option>
          <option value="1">Include Payouts</option>
        </Select>
      </Field.Control>
      <Field.Error>{meta.errors}</Field.Error>
    </Field.Root>
  );
}

function ParametersInspectionResult({ name }: { name: string }) {
  const [meta] = useField<string>(name);

  return (
    <Field.Root>
      <Field.Label>Outcome</Field.Label>
      <Field.Control>
        <Select name={meta.name} defaultValue={meta.initialValue}>
          <option value="0">All</option>
          <option value="1">Fail</option>
          <option value="2">Pass</option>
        </Select>
      </Field.Control>
      <Field.Error>{meta.errors}</Field.Error>
    </Field.Root>
  );
}

export const Parameters = {
  DateRange: ParametersDateRange,
  Date: ParametersDate,
  Company: ParametersCompany,
  Companies: ParametersCompanies,
  BasisDate: ParametersBasisDate,
  Reason: ParametersReason,
  MasterAccount: ParametersMasterAccount,
  SubAccounts: ParametersSubAccounts,
  Account: ParametersAccount,
  Accounts: ParametersAccounts,
  AccountTypes: ParametersAccountTypes,
  Driver: ParametersDriver,
  Drivers: ParametersDrivers,
  Truck: ParametersTruck,
  Trucks: ParametersTrucks,
  Weight: ParametersWeight,
  User: ParametersUser,
  UserType: ParametersUserType,
  PaymentStatus: ParametersPaymentStatus,
  PaymentType: ParametersPaymentType,
  PaymentTypes: ParametersPaymentTypes,
  PaymentOption: ParametersPaymentOption,
  PaymentOptionWithTip: ParametersPaymentOptionWithTip,
  BasisPayment: ParametersBasisPayment,
  BaseReportOnDate: ParametersBaseReportOnDate,
  VerifiedStatus: ParametersVerifiedStatus,
  RateItems: ParametersRateItems,
  StickerStatus: ParametersStickerStatus,
  StickerReason: ParametersStickerReason,
  TaxExemptStatus: ParametersTaxExemptStatus,
  IncludeImpounds: ParametersIncludeImpounds,
  PushedToQuickBooks: ParametersPushedToQuickBooks,
  IsAudited: ParametersIsAudited,
  IsLocked: ParametersIsLocked,
  IsBilled: ParametersIsBilled,
  AccountingMethod: ParametersAccountingMethod,
  Impounds: ParametersImpounds,
  Payouts: ParametersPayouts,
  TotalInvoicedFilter: ParametersTotalInvoicedFilter,
  InspectionResult: ParametersInspectionResult,
};

function useEntitiesByCompany(
  entityList: Array<{
    id: number;
    name?: string;
    companies?: number[];
    companyId?: number;
  }>,
  companyFieldName = "companyId",
): Array<{
  id: number;
  name: string;
  subEntities: Array<{ id: number; name?: string }>;
}> {
  const { data: companies = [] } = useCompanies();
  const selectedCompanyIds = useField<string>(companyFieldName)[0].value;
  return React.useMemo(() => {
    const companyIds = Array.isArray(selectedCompanyIds)
      ? selectedCompanyIds
      : selectedCompanyIds
        ? [selectedCompanyIds]
        : [];
    const companiesToShow = companies.filter(
      (company) =>
        (companyIds === undefined ||
          companyIds.length === 0 ||
          companyIds.includes(String(company.id))) &&
        entityList?.find((entity) =>
          "companies" in entity
            ? entity.companies?.includes(company.id)
            : entity.companyId === company.id,
        ),
    );

    return companiesToShow.map((company) => ({
      id: company.id,
      name: company.name,
      subEntities: entityList.filter((entity) =>
        entity.companies
          ? entity.companies.includes(1) ||
            entity.companies.includes(company.id)
          : entity.companyId === company.id,
      ),
    }));
  }, [selectedCompanyIds, entityList, companies]);
}

function useMappedEntities(
  entityList: Array<{
    id: number;
    name?: string;
    companies?: number[];
    companyId?: number;
  }>,
  companyFieldName = "companyId",
  { prependCompany }: { prependCompany: boolean } = { prependCompany: true },
): Array<{ id: number; name: string }> {
  const { data: companies = [] } = useCompanies();
  const selectedCompanyIds = useField<string>(companyFieldName)[0].value;
  return React.useMemo(() => {
    const companyIds = Array.isArray(selectedCompanyIds)
      ? selectedCompanyIds
      : selectedCompanyIds
        ? [selectedCompanyIds]
        : [];
    const companiesInUse = companies.filter((company) =>
      entityList?.find((entity) =>
        "companies" in entity
          ? entity.companies?.includes(company.id)
          : entity.companyId === company.id,
      ),
    );

    return entityList
      .filter(
        (entity) =>
          companyIds === undefined ||
          companyIds.length === 0 ||
          (entity.companies
            ? entity.companies.some(
                (company) =>
                  company === 1 || companyIds.includes(String(company)),
              )
            : companyIds.includes(String(entity.companyId))),
      )
      .map((entity) => ({
        id: entity.id,
        name:
          companies.length === 1 || companyIds?.length === 1 || !prependCompany
            ? entity.name || ""
            : prependCompanyName(companiesInUse, entity),
      }));
  }, [selectedCompanyIds, entityList, companies]);
}

function formStringToStringArray(
  value: string | (string | undefined)[] | undefined,
) {
  return Array.isArray(value) ? value : value ? [value] : [];
}

function prependCompanyName(
  companies: { id: number; name: string }[],
  entity: { companyId?: number; companies?: number[]; name?: string },
) {
  const entityCompany = entity.companies
    ? entity.companies.at(-1)
    : entity.companyId;

  const company = companies.find((company) => company.id === entityCompany);
  if (!company) {
    return entity.name;
  }
  return company.name + ": " + entity.name;
}

function markEntitiesInactive<T extends { name?: string }>(
  entities: T[],
  predicate: (entity: T) => boolean,
): T[] {
  return entities
    .map((entity) => ({
      ...entity,
      name: predicate(entity) ? `${entity.name} - Inactive` : entity.name,
    }))
    .sort((a, b) => {
      const isAInactive = predicate(a);
      const isBInactive = predicate(b);
      return isAInactive && isBInactive
        ? 0
        : isAInactive
          ? 1
          : isBInactive
            ? -1
            : 0;
    }) as T[];
}
