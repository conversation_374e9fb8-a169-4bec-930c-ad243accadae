import { z, ZodType } from "zod";
import { useLocalStorage } from "../../utils/hooks";
import { useUser } from "../../hooks/useUser";

const currencyFormatter = new Intl.NumberFormat("en-US", {
  style: "currency",
  currency: "USD",
});

export const formatters = {
  date: (value?: string | null) =>
    value ? new Date(value).toLocaleDateString() : "",
  datetime: (value?: string | null) =>
    value ? new Date(value).toLocaleString() : "",
  time: (value?: string | null) =>
    value ? new Date(value).toLocaleTimeString() : "",
  currency: (value?: number) =>
    value !== undefined ? currencyFormatter.format(value) : "",
  percent: (value: number, fractionDigits = 2) =>
    value.toFixed(fractionDigits) + "%",
};

export const useFilterSetsLocalKey = () => {
  const user = useUser();
  return `reports/filterSets/${user.data?.id}`;
};
export const useFilterSetStorage = <
  FilterType extends Array<{
    name: string;
    filters: Record<string, any>;
  }>,
>(
  reportType: string,
  reportName: string,
  reportKey: string,
): [FilterType, (filterSet: React.SetStateAction<FilterType>) => void] => {
  const filterSetsLocalKey = useFilterSetsLocalKey();
  const [allFilterSets, setAllFilterSets] = useLocalStorage<
    Record<
      string,
      { reportName: string; reportKey: string; filterSets: FilterType }
    >
  >(filterSetsLocalKey, {});
  return [
    allFilterSets[reportType]?.filterSets || [],
    (filterSet) => {
      if (typeof filterSet === "function") {
        return setAllFilterSets({
          ...allFilterSets,
          [reportType]: {
            reportName,
            reportKey,
            filterSets: filterSet(allFilterSets[reportType]?.filterSets || []),
          },
        });
      }
      setAllFilterSets({
        ...allFilterSets,
        [reportType]: { reportName, reportKey, filterSets: filterSet },
      });
    },
  ];
};

export const getInitialFilters = <T>(
  searchParams: URLSearchParams,
  filterSets: { name: string; filters: T }[],
  defaultFilters: T | null = null,
) => {
  const filterSet = searchParams.get("filterSet");
  if (filterSet) {
    return (
      filterSets.find((set) => set.name === filterSet)?.filters ||
      defaultFilters
    );
  }

  return searchParams.size > 0
    ? parseSearchParams(searchParams)
    : defaultFilters;
};

export const parseSearchParams = (search: URLSearchParams) => {
  return Array.from(search.entries()).reduce(
    (acc, [key, value]) => {
      if (Object.keys(arrayParameters.shape).includes(key)) {
        if (acc[key] === undefined) {
          return { ...acc, [key]: [value] };
        }
        return { ...acc, [key]: [...acc[key], value] };
      }
      return { ...acc, [key]: value };
    },
    {} as Record<string, string | string[]>,
  );
};

export const formValuesToSearchParams = (
  formValues: z.input<typeof searchableParameters>,
) => {
  return Object.entries(searchableParameters.parse(formValues))
    .reduce((acc, [key, value]) => {
      if (value === undefined) {
        return acc;
      }
      if (Array.isArray(value)) {
        return `${acc}${value.map((v) => `${key}=${v}&`).join("")}`;
      }

      return `${acc}${key}=${value}&`;
    }, "?")
    .slice(0, -1);
};

const idLikeToString = z
  .union([z.string(), z.number()])
  .transform((value) => String(value));
export const zodFormStringsToStringArray = z
  .union([idLikeToString, z.array(idLikeToString)])
  .transform((value) => {
    if (Array.isArray(value)) {
      return value.map((v) => v.toString());
    }
    if (value === "") {
      return undefined;
    }
    return [value.toString()];
  });
export const zodImpounds = z
  .enum(["0", "1", "2", "3", "4", "5", "6"], {
    description:
      "All = 0, ReleasedOnly = 1, None = 2, WithoutStorageCharges = 3, AllWithOnlyTowing = 4, ExcludeStorageLots = 5, ImpoundOnly = 6",
  })
  .optional();
export const zodGroupOptionAccount = z
  .enum(["1", "2", "TopAccounts", "AccountType"])
  .optional()
  .default("TopAccounts")
  .transform((value) => {
    switch (value) {
      case "1":
        return "TopAccounts";
      case "2":
        return "AccountType";
      default:
        return value;
    }
  });
export const zodGroupOptionCall = z
  .enum(["0", "1", "CallVolume", "Revenue"])
  .optional()
  .default("CallVolume")
  .transform((value) => {
    switch (value) {
      case "0":
        return "CallVolume";
      case "1":
        return "Revenue";
      default:
        return value;
    }
  });
export const zodGroupOptionCompany = z
  .enum(["1", "2", "Company", "PaymentType"])
  .optional()
  .default("Company")
  .transform((value) => {
    switch (value) {
      case "1":
        return "Company";
      case "2":
        return "PaymentType";
      default:
        return value;
    }
  });
export const zodGroupOptionExpenses = z
  .enum(["1", "2", "Expenses", "GrossProfit"])
  .optional()
  .default("Expenses")
  .transform((value) => {
    switch (value) {
      case "1":
        return "Expenses";
      case "2":
        return "GrossProfit";
      default:
        return value;
    }
  });
export const zodGroupOptionPayment = z
  .enum(["2", "3", "PaymentType", "Account"])
  .optional()
  .default("PaymentType")
  .transform((value) => {
    switch (value) {
      case "2":
        return "PaymentType";
      case "3":
        return "Account";
      default:
        return value;
    }
  });
export const zodGroupOptionRevenue = z
  .enum(["1", "2", "PaymentType", "Revenue"])
  .optional()
  .default("PaymentType")
  .transform((value) => {
    switch (value) {
      case "1":
        return "PaymentType";
      case "2":
        return "Revenue";
      default:
        return value;
    }
  });
export const zodGroupOptionTotal = z
  .enum(["1", "2", "TotalInvoiced", "TotalCalls"])
  .optional()
  .default("TotalInvoiced")
  .transform((value) => {
    switch (value) {
      case "1":
        return "TotalInvoiced";
      case "2":
        return "TotalCalls";
      default:
        return value;
    }
  });
export const zodAdditionalGroupOption = z
  .enum(["0", "1", "2", "3", "4", "5"])
  .optional()
  .default("0");
export const zodDateOptions = z
  .enum(["0", "1", "2", "3", "4", "Hour", "Day", "Week", "Month", "Year"])
  .optional()
  .default("Day")
  .transform((value) => {
    switch (value) {
      case "0":
        return "Hour";
      case "1":
        return "Day";
      case "2":
        return "Week";
      case "3":
        return "Month";
      case "4":
        return "Year";
      default:
        return value;
    }
  });

export const zodRefineDateRange: Parameters<ZodType["refine"]> = [
  (params: { dateStart: string; dateEnd: string }) =>
    new Date(params.dateStart) <= new Date(params.dateEnd),
  {
    path: ["dateEnd"],
    message: "End Date must be after Start Date",
  },
];

const arrayParameters = z.object({
  rateItemIds: zodFormStringsToStringArray,
  paymentTypeIds: zodFormStringsToStringArray,
  companyId: zodFormStringsToStringArray,
});
const searchableParameters = arrayParameters
  .extend({
    ownerUserId: idLikeToString,
    performerUserId: idLikeToString,
    dateStart: z.string(),
    dateEnd: z.string(),
    dispatchReasonId: idLikeToString,
    accountId: idLikeToString,
    accountType: z.string(),
    masterAccountId: idLikeToString,
    accountManagerUserId: idLikeToString,
    driverId: idLikeToString,
    truckId: idLikeToString,
    userType: z.string(),
    weight: z.string(),
    rateItemId: idLikeToString,
    paymentTypeId: idLikeToString,
    paymentStatus: z.string(),
    groupOption: idLikeToString,
    dateGroupType: idLikeToString,
    additionalGroupOption: z.string(),
    reportByType: idLikeToString,
    totalInvoicedFilter: z.string(),
    accountingMethod: idLikeToString,
    impounds: idLikeToString,
    paymentOptions: idLikeToString,
    payouts: z.string(),
    inspectionResult: idLikeToString,
    forceRefresh: z.string(),
    recordedById: idLikeToString,
    paymentBy: idLikeToString,
    verifiedStatus: z.string(),
    verifiedById: idLikeToString,
    taxExemptStatus: idLikeToString,
    dateRangeField: z.string(),
    classId: idLikeToString,
    invoiceStatusId: idLikeToString,
    dateColumn: z.string(),
    stickerReason: z.string(),
    stickerStatus: z.string(),
    pushedToQuickBooks: z.string(),
    isAudited: z.string(),
    isLocked: z.string(),
    isBilled: z.string(),
    startDate: z.string(),
    endDate: z.string(),
    userId: idLikeToString,
  })
  .partial();
