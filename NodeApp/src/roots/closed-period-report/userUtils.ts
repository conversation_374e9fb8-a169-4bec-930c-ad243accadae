export interface UserResponse {
  type: number;
  id: number;
  disabled: boolean;
  deleted?: boolean;
  companyId: number;
  name?: string | undefined;
}

/**
 * Sorts users for dropdown menu. Disabled then deleted users are put at the bottom of the list.
 * @param a User List Response
 * @param b User List Respons
 * @returns
 */
export function sortUsers(a: UserResponse, b: UserResponse) {
  if (a.deleted && !b.deleted) return 1;
  if (b.deleted && !a.deleted) return -1;
  if (a.disabled && !b.disabled) return 1;
  if (b.disabled && !a.disabled) return -1;
  if (a.name && b.name) return a.name.localeCompare(b.name);
  if (!a.name && !b.name) return 0;
  if (!a.name) return 1;
  if (!b.name) return -1;
  return 0;
}
