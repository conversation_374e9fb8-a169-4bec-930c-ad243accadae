import * as React from "react";
import { Button, Checkbox, Dialog, Icon, Tabs } from "@towbook/flatbed";
import { useTowbook } from "@towbook/sdk";
import { useMutation, useQuery } from "@tanstack/react-query";
import { z } from "zod";
import {
  faExclamationCircle,
  faSpinnerThird,
} from "@fortawesome/pro-solid-svg-icons";
import { cva } from "cva";

const exportCommissionReportOptionsList = [
  { name: "showDriverBaseExport", optionName: "Show Driver Base" },
  {
    name: "showCallTotalExport",
    optionName: "Show Call Total",
  },
  // { name: "showTruckExport", optionName: "Show Truck" },
  { name: "showPONumberExport", optionName: "Show PO Number" },
  { name: "showWeightClassExport", optionName: "Show Weight Class" },
  // {
  //   name: "showDriversLicenseNumExport",
  //   optionName: "Show Drivers License Number",
  // },
  {
    name: "hideAllButCommissionExport",
    optionName: "Hide All But Commission",
    title: "Overrides all optional columns",
  },
];

const webCommissionReportOptionsList = [
  { name: "showDriverBaseWeb", optionName: "Show Driver Base" },
  { name: "showPONumberWeb", optionName: "Show PO Number" },
  { name: "showWeightClassWeb", optionName: "Show Weight Class" },
  {
    name: "hideAllButCommissionWeb",
    optionName: "Hide All But Commission",
    title: "Overrides all optional columns",
  },
];

const CommissionDriverReportOptionsSchema = z.object({
  id: z.number(),
  companyId: z.number(),
  showDriverBaseExport: z.boolean(),
  showCallTotalExport: z.boolean(),
  showTruckExport: z.boolean(),
  showPONumberExport: z.boolean(),
  showWeightClassExport: z.boolean(),
  showDriversLicenseNumExport: z.boolean(),
  hideAllButCommissionExport: z.boolean(),
  showDriverBaseWeb: z.boolean(),
  showPONumberWeb: z.boolean(),
  showWeightClassWeb: z.boolean(),
  hideAllButCommissionWeb: z.boolean(),
});

type CommissionReportOptions = z.infer<
  typeof CommissionDriverReportOptionsSchema
>;

function useCommissionReportOptionsUpdate() {
  const towbook = useTowbook();
  return useMutation({
    mutationFn: async (options: CommissionReportOptions) => {
      console.log("commission report options run update", options);
      if (options.id == 0) {
        return towbook.commissionsDriverReportOptions.create(options);
      } else {
        return towbook.commissionsDriverReportOptions.update(options);
      }
    },
    onSuccess: (data) => {
      console.log("commission report options update success", data);
    },
    onError: (error) => {
      console.error("commission report options update error", error);
    },
  });
}

function useCommissionReportOptions() {
  const towbook = useTowbook();
  return useQuery({
    queryKey: ["commissionDriverReportOptions"],
    queryFn: async () => {
      return await towbook.commissionsDriverReportOptions.get();
    },
    refetchOnWindowFocus: true,
  });
}

const checkboxStyle = cva("", {
  variants: {
    disabled: {
      true: "grayscale opacity-60",
    },
  },
});

export function CommissionReportSettings() {
  const commissionOptions = useCommissionReportOptions();

  const [dialogOpen, toggleDialog] = React.useReducer(
    (state: any, value: boolean) => {
      if (value !== undefined) return value;
      else return !state;
    },
    false,
  );

  const [webHideAll, setWebHideAll] = React.useState(false);
  const [exportHideAll, setExportHideAll] = React.useState(false);

  React.useEffect(() => {
    if (commissionOptions.data?.hideAllButCommissionExport) {
      setExportHideAll(true);
    }
    if (commissionOptions.data?.hideAllButCommissionWeb) {
      setWebHideAll(true);
    }
  }, [
    commissionOptions.data?.hideAllButCommissionExport,
    commissionOptions.data?.hideAllButCommissionWeb,
  ]);

  const updateCommissionOptions = useCommissionReportOptionsUpdate();

  const handleSubmit = React.useCallback(
    (event: any) => {
      event.preventDefault();
      const formEntries = new FormData(event.target).entries();
      const updatedOpts = Object.fromEntries(
        formEntries,
      ) as unknown as CommissionReportOptions;

      updatedOpts.companyId = commissionOptions.data?.companyId ?? 0;
      updatedOpts.id = commissionOptions.data?.id ?? 0;
      updatedOpts.showDriverBaseExport = !!updatedOpts.showDriverBaseExport;
      updatedOpts.showCallTotalExport = !!updatedOpts.showCallTotalExport;
      updatedOpts.showTruckExport = !!updatedOpts.showTruckExport;
      updatedOpts.showPONumberExport = !!updatedOpts.showPONumberExport;
      updatedOpts.showWeightClassExport = !!updatedOpts.showWeightClassExport;
      updatedOpts.showDriversLicenseNumExport =
        !!updatedOpts.showDriversLicenseNumExport;
      updatedOpts.hideAllButCommissionExport =
        !!updatedOpts.hideAllButCommissionExport;
      updatedOpts.showDriverBaseWeb = !!updatedOpts.showDriverBaseWeb;
      updatedOpts.showPONumberWeb = !!updatedOpts.showPONumberWeb;
      updatedOpts.showWeightClassWeb = !!updatedOpts.showWeightClassWeb;
      updatedOpts.hideAllButCommissionWeb =
        !!updatedOpts.hideAllButCommissionWeb;

      updateCommissionOptions.mutate(updatedOpts, {
        onSuccess: () => {
          toggleDialog(false);
          commissionOptions.refetch();
        },
      });
    },
    [commissionOptions, updateCommissionOptions],
  );

  return (
    <Dialog.Root open={dialogOpen} onOpenChange={toggleDialog}>
      <Dialog.Trigger asChild className="cursor-pointer">
        <a>Commission Report Settings</a>
      </Dialog.Trigger>
      <Dialog.Content className="max-w-2xl px-8 py-6 grid grid-rows-[auto_1fr] gap-4">
        <div className="grid grid-cols-[1fr_auto] gap-4 items-start">
          <Dialog.Title>Commission Report Settings</Dialog.Title>
          <Dialog.Close />
        </div>
        {commissionOptions.isLoading ? (
          <div className="flex items-center justify-center flex-row bg-slate-2 rounded-lg px-6 py-5 gap-3">
            <div>
              <Icon
                icon={faSpinnerThird}
                className="text-slate-9 w-4 h-4 animate-spin"
              />
            </div>
            <div className="text-slate-11 text-sm">Loading Options...</div>
          </div>
        ) : (
          <div className="flex flex-col min-h-0">
            {updateCommissionOptions.isError ? (
              <div className="bg-redA-3 rounded-md px-5 py-3.5 text-red-12 flex gap-4 items-center mb-3">
                <div>
                  <Icon
                    icon={faExclamationCircle}
                    square
                    className="text-red-10 w-6 h-6 flex"
                  />
                </div>
                <div>
                  <div className="font-medium">
                    There was an error saving changes
                  </div>
                  <div className="text-red-11">
                    {updateCommissionOptions.error?.message}
                  </div>
                </div>
              </div>
            ) : (
              ""
            )}
            <form className="flex flex-col min-h-0" onSubmit={handleSubmit}>
              <Tabs.Root defaultValue="web" className="flex flex-col min-h-0">
                <Tabs.List className="mb-4 flex-shrink-0">
                  <Tabs.Trigger value="web">Report Options</Tabs.Trigger>
                  <Tabs.Trigger value="export">
                    Print (PDF) Options
                  </Tabs.Trigger>
                </Tabs.List>
                <Tabs.Content
                  value="export"
                  forceMount
                  className="overflow-auto grid grid-flow-row gap-2 state-inactive:hidden"
                >
                  {commissionOptions.data &&
                    exportCommissionReportOptionsList.map((option) => (
                      <div
                        className="inline-flex place-items-center"
                        key={option.name}
                        title={option.title}
                      >
                        <Checkbox.Root
                          defaultChecked={
                            (commissionOptions.data as any)[option.name]
                          }
                          name={option.name}
                          disabled={
                            option.name !== "hideAllButCommissionExport" &&
                            exportHideAll
                          }
                          className={checkboxStyle({
                            disabled:
                              option.name !== "hideAllButCommissionExport" &&
                              exportHideAll,
                          })}
                          onCheckedChange={(checked) => {
                            if (option.name == "hideAllButCommissionExport") {
                              setExportHideAll(checked);
                            }
                          }}
                        >
                          {option.optionName}
                        </Checkbox.Root>
                      </div>
                    ))}
                </Tabs.Content>
                <Tabs.Content
                  value="web"
                  forceMount
                  className="overflow-auto grid grid-flow-row gap-2 state-inactive:hidden"
                >
                  {commissionOptions.data &&
                    webCommissionReportOptionsList.map((option) => (
                      <div
                        className="inline-flex place-items-center"
                        key={option.name}
                      >
                        <Checkbox.Root
                          defaultChecked={
                            (commissionOptions.data as any)[option.name]
                          }
                          name={option.name}
                          disabled={
                            option.name !== "hideAllButCommissionWeb" &&
                            webHideAll
                          }
                          className={checkboxStyle({
                            disabled:
                              option.name !== "hideAllButCommissionWeb" &&
                              webHideAll,
                          })}
                          onCheckedChange={(checked) => {
                            if (option.name == "hideAllButCommissionWeb") {
                              setWebHideAll(checked);
                            }
                          }}
                        >
                          {option.optionName}
                        </Checkbox.Root>
                      </div>
                    ))}
                </Tabs.Content>
              </Tabs.Root>
              <div className="flex justify-end mt-4">
                <Button
                  type="submit"
                  className="flex items-center gap-2"
                  disabled={updateCommissionOptions.isPending}
                >
                  {updateCommissionOptions.isPending ? (
                    <>
                      <Icon icon={faSpinnerThird} className="animate-spin" />{" "}
                      Saving...
                    </>
                  ) : (
                    "Save Changes"
                  )}
                </Button>
              </div>
            </form>
          </div>
        )}
      </Dialog.Content>
    </Dialog.Root>
  );
}
