import * as React from "react";
import { Dialog, Icon } from "@towbook/flatbed";
import { useCompany } from "../../../hooks/useCompany";
import { faSpinnerThird } from "@fortawesome/pro-duotone-svg-icons";
import { useAuctionItem } from "../../../hooks/useAuctionItem";
import { useImpound } from "../../tasks-letters/hooks/useImpound";
import { useConfig } from "../../../hooks/useConfig";
import { FormAuctionItem } from "./FormAuctionItem";

export function DialogAuctionItem({
  impoundId,
  open,
  onOpenChange,
  onSuccess,
  children,
}: {
  impoundId: number;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  onSuccess?: () => void;
  children?: React.ReactNode;
}) {
  const [dialogOpen, setDialogOpen] = React.useState(open);

  const impound = useImpound(impoundId);
  const auctionItem = useAuctionItem(impound.data?.call.auctionDetails?.id);
  const company = useCompany();
  const config = useConfig();

  const mode =
    impound.data?.call.impoundDetails.auction == false ? "create" : "update";

  if (!company.data) return null;

  return (
    <Dialog.Root
      open={dialogOpen}
      onOpenChange={(open) => {
        setDialogOpen(open);
        onOpenChange?.(open);
      }}
    >
      {!!children && <Dialog.Trigger asChild>{children}</Dialog.Trigger>}
      <Dialog.Content className="flex flex-col w-[728px] px-8 py-6">
        <div className="grid grid-cols-[1fr_auto] gap-4 items-center mb-4">
          <div className="font-medium text-xl">
            {mode === "create" ? "Create" : "Modify"} Auction Item
          </div>
          <Dialog.Close />
        </div>
        {!impound.data ||
        (mode === "update" && !auctionItem) ||
        !config.data ? (
          <div className="flex items-center justify-center flex-col bg-slate-2 rounded-lg p-8 gap-2">
            <div>
              <Icon
                icon={faSpinnerThird}
                className="text-slate-9 w-10 h-10 animate-spin"
              />
            </div>
            <div className="font-medium text-slate-11 text-base">
              Loading...
            </div>
          </div>
        ) : (
          <FormAuctionItem
            impoundId={impoundId}
            closeDialog={() => {
              setDialogOpen(false);
              onOpenChange?.(false);
            }}
            onSuccess={onSuccess}
          />
        )}
      </Dialog.Content>
    </Dialog.Root>
  );
}
