import * as React from "react";
import * as faDuotone from "@fortawesome/pro-duotone-svg-icons";
import { Badge, Button, Dialog, Icon } from "@towbook/flatbed";
import { useLocalStorage } from "../../utils/hooks";
import ms from "ms";
import { cva } from "cva";

const contentStyle = cva(
  "text-sm leading-snug text-slate-11 space-y-2.5 [&>ul]:list-disc [&>ul]:pl-5",
);

export function UpsellBanner({
  title,
  description,
  content,
  ctaLabel,
  image,
  tag = "New",
  intercomMessage,
}: {
  title?: string;
  description?: string;
  content?: string;
  ctaLabel?: string;
  image?: string;
  tag?: string;
  intercomMessage?: string;
}) {
  const ref = React.useRef<HTMLDivElement>(null);

  /**
   * Mark the banner as rendered so it shows up.
   * By default, the banner is set to display: none so that the
   * user doesn't see a flash of the banner content before the JS loads.
   */
  React.useEffect(() => {
    if (ref.current && ref.current.parentElement) {
      ref.current.parentElement.dataset.upsellBanner = "rendered";
    }
  }, []);

  const [dismissed, setDismissed] = useLocalStorage<null | number>(
    `upsellBanner_${title}`,
    null,
  );

  const dismiss = React.useCallback(() => {
    setDismissed(new Date().getTime());
  }, [setDismissed]);

  React.useEffect(() => {
    if (dismissed && new Date().getTime() - dismissed > ms("30d")) {
      setDismissed(null);
    }
  }, [dismissed, setDismissed]);

  const upgrade = React.useCallback(() => {
    if (typeof Intercom === "undefined") return;
    Intercom("showNewMessage", intercomMessage);
  }, [intercomMessage]);

  if (dismissed) return null;

  return (
    <div
      ref={ref}
      className="grid grid-flow-col grid-cols-[auto_1fr_auto] gap-6 rounded-md bg-white px-6 py-5 shadow-md shadow-slateA-3 outline outline-1 outline-slateA-5 max-w-5xl"
    >
      <Icon icon={faDuotone.faSparkles} className="h-8 text-amber-10" />
      <div className="flex items-center">
        <div className="">
          <Badge color="amber" className="mb-1.5" size="small">
            {tag}
          </Badge>
          <div className="text-lg font-medium">{title}</div>
          <p className="max-w-prose text-sm text-slate-11">{description}</p>
          <div className="mt-2.5 flex items-center gap-3">
            <Dialog.Root>
              <Dialog.Trigger asChild>
                <Button variant="light" size="small">
                  Learn More
                </Button>
              </Dialog.Trigger>
              <Dialog.Content>
                <div className="w-[700px] px-8 py-6">
                  <div className="mb-6 flex items-start gap-6">
                    <Dialog.Title className="flex items-center gap-3">
                      <span className="font-medium">{title}</span>
                      <Badge color="amber">{tag}</Badge>
                    </Dialog.Title>
                    <Dialog.Close className="ml-auto" />
                  </div>
                  <div className="mb-6 grid auto-cols-fr grid-flow-col gap-6">
                    <div
                      className={contentStyle()}
                      dangerouslySetInnerHTML={{ __html: content || "" }}
                    />
                    {!!image && (
                      <div>
                        <div className="relative">
                          <img
                            src={image}
                            className="aspect-video rounded-md bg-slate-2 object-cover object-left-top"
                            alt={title}
                          />
                        </div>
                      </div>
                    )}
                  </div>
                  {typeof Intercom !== "undefined" ? (
                    <div className="flex justify-center flex-col items-center gap-2">
                      <Button size="large" onClick={upgrade}>
                        {ctaLabel}
                      </Button>
                      <div className="text-slate-11">
                        Or call us at{" "}
                        <a href="tel:+18103205063">(*************</a> for more
                        information.
                      </div>
                    </div>
                  ) : (
                    <div className="flex justify-center flex-col items-center gap-2">
                      <div className="text-blue-10 font-medium text-lg">
                        Call us at <a href="tel:+18103205063">(*************</a>{" "}
                        for more information.
                      </div>
                    </div>
                  )}
                </div>
              </Dialog.Content>
            </Dialog.Root>
            <Button variant="dark" size="small" onClick={dismiss}>
              Dismiss
            </Button>
          </div>
        </div>
      </div>
      {!!image && (
        <div className="flex items-center">
          <div className="relative">
            <img
              src={image}
              className="aspect-video h-32 rounded-[5px] bg-slate-2 object-cover object-left-top"
              alt={title}
            />
          </div>
        </div>
      )}
    </div>
  );
}
