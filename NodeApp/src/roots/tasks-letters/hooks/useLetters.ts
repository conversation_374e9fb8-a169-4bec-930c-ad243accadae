import { useTowbook } from "@towbook/sdk";
import { useQuery } from "@tanstack/react-query";

export function useLetters(impoundId: number) {
  const towbook = useTowbook();

  return useQuery({
    queryKey: ["letters", impoundId],
    queryFn: async () => {
      return await towbook.impounds.letters.list(impoundId);
    },
    enabled: impoundId !== undefined,
    staleTime: Infinity,
    gcTime: Infinity,
  });
}

export function useLetter({
  impoundId,
  letterTemplateId,
}: {
  impoundId: number;
  letterTemplateId: number;
}) {
  const towbook = useTowbook();

  return useQuery({
    queryKey: ["impounds", impoundId, "letters", letterTemplateId],
    queryFn: async () => {
      if (!impoundId || !letterTemplateId)
        throw new Error("Missing required parameters");

      return await towbook.impounds.letters.get({
        impoundId,
        letterTemplateId,
      });
    },
  });
}
