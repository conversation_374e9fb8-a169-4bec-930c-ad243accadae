namespace Extric.Towbook.Web.PublicRequestCallMvc
{
    public static class WebApiConfig
    {
        //public static void Register(HttpConfiguration config)
        //{
        //    // Web API configuration and services

        //    // Web API routes
        //    //config.MapHttpAttributeRoutes();

        //    config.Routes.MapHttpRoute(
        //        name: "Files",
        //            routeTemplate: "api/file/upload/{id}",
        //            defaults: new { controller = "File", action = "UploadFile" }
        //        );
        //    config.Routes.MapHttpRoute(
        //        name: "DefaultApi",
        //        routeTemplate: "api/{id}/{controller}",
        //        defaults: new { id = RouteParameter.Optional, controller = "PublicRequest" }
        //    );

        //    config.Routes.MapHttpRoute(
        //        name: "Model_Query",
        //        routeTemplate: "model/{controller}",
        //        defaults: new { controller = "PublicRequest", action="model" }
        //    );

        //    config.Routes.MapHttpRoute(
        //        name: "Files",
        //        routeTemplate: "file/{controller}",
        //        defaults: new { controller = "File" }
        //    );
        //}
    }
}
