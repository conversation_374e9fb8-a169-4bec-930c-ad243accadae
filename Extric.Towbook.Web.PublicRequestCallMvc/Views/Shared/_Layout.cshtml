@model Extric.Towbook.Web.PublicRequestCallMvc.Models.PublicRequestCallModel
@using Extric.Towbook.Web.PublicRequestCallMvc
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@ViewBag.Title - Powered By Towbook</title>
    <link href="~/Content/Site.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <script src="~/Scripts/jquery-2.1.3.min.js" type="text/javascript"></script>
    <script src="~/Scripts/jquery-ui-1.9.2.min.js" type="text/javascript"></script>
    <script src="~/Scripts/modernizr-2.8.3.js"></script>
    <script src="~/Scripts/validator.js"></script>
    <script src="~/Scripts/jquery.tmpl.min.js"></script>
    <script src="~/Scripts/towbook.js"></script>
    <script src="~/Scripts/jquery.fileupload.js"></script>
    <script src="~/Scripts/jquery.fileupload-ui.js"></script>
    <script src="~/Scripts/jquery.iframe-transport.js"></script>
    <script src="https://cdn.jsdelivr.net/sweetalert2/4.2.7/sweetalert2.min.js" type="text/javascript"></script>
    <link type="text/css" rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,400,500">
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/sweetalert2/4.2.7/sweetalert2.min.css"/>
    <script src="https://maps.googleapis.com/maps/api/js?libraries=places&key=AIzaSyCcF4svM8msb5DDCcyXez196zP0xazjsio"></script>
</head>
<body>

    <script type="text/javascript">
        // [START region_geolocation]
        // Bias the autocomplete object to the user's geographical location,
        // as supplied by the browser's 'navigator.geolocation' object.
        function geolocate() {
            if (navigator.geolocation) {
                navigator.geolocation.getCurrentPosition(function (position) {
                    var geolocation = new google.maps.LatLng(
                        position.coords.latitude, position.coords.longitude);
                    var circle = new google.maps.Circle({
                        center: geolocation,
                        radius: position.coords.accuracy
                    });
                    autocompleteTowSource.setBounds(circle.getBounds());
                    autocompleteTowDestination.setBounds(circle.getBounds());
                });
            }
        }
        // [END region_geolocation]
    </script>

    <script type="text/x-jQuery-tmpl" id="t-fieldType1">
        <div class="form-group" style="display: ${display}">
            <label for="${Id}" class="col-sm-2 control-label">${Name}</label>
            <div class="col-sm-10">
                <input id="attribute-${Id}" type="text" class="form-control attribute {{if Required}}required{{/if}}" placeholder="${Name}" data-id="${Id}" value="${DefaultValue}"/>
                <div class="help-block with-errors"></div>
            </div>
        </div>
    </script>

    <script type="text/x-jQuery-tmpl" id="t-fieldType2">
        <div class="form-group" style="display: ${display}">
            <label for="${Id}" class="col-sm-2 control-label">${Name}</label>
            <div class="col-sm-10">
                <input id="attribute-${Id}" type="number" class="form-control attribute {{if Required}}required{{/if}}" placeholder="${Name}" data-id="${Id}" data-type-id="${Type}" value="${DefaultValue}"/>
                <div class="help-block with-errors"></div>
            </div>
        </div>
    </script>

    <script type="text/x-jQuery-tmpl" id="t-fieldType3">
        <div class="form-group" style="display: ${display}">
            <label for="${Id}" class="col-sm-2 control-label">${Name}</label>
            <div class="col-sm-10">
                <select id="attribute-${Id}" class="form-control attribute {{if Required}}required{{/if}}" data-id="${Id}" data-type-id="${Type}" >
                    <option value="-1">not specified</option>
                    <option value="0" {{if DefaultValue == 'no'}}selected{{/if}}>no</option>
                    <option value="1" {{if DefaultValue == 'yes'}}selected{{/if}}>yes</option>
                </select>
            </div>
        </div>
    </script>

    <script type="text/x-jQuery-tmpl" id="t-fieldType4">
        <div class="form-group" style="display: ${display}">
            <label for="${Id}" class="col-sm-2 control-label">${Name}</label>
            <div class="col-sm-10">
                <input id="attribute-${Id}" type="date" class="form-control attribute {{if Required}}required{{/if}}" placeholder="${Name}" data-id="${Id}" data-type-id="${Type}" value="${DefaultValue}"/>
                <div class="help-block with-errors"></div>
            </div>
        </div>
    </script>

    <script type="text/x-jQuery-tmpl" id="t-fieldType5">
        <div class="form-group" style="display: ${display}">
            <label for="${Id}" class="col-sm-2 control-label">${Name}</label>
            <div class="col-sm-10">
                <input id="attribute-${Id}" type="datetime-local" class="form-control attribute {{if Required}}required{{/if}}" placeholder="${Name}" data-id="${Id}" data-type-id="${Type}" value="${DefaultValue}"/>
                <div class="help-block with-errors"></div>
            </div>
        </div>
    </script>

    <script type="text/x-jQuery-tmpl" id="t-fieldType6">
        <div class="form-group" style="display: ${display}">
            <label for="${Id}" class="col-sm-2 control-label">${Name}</label>
            <div class="col-sm-10">
                <input id="attribute-${Id}" type="time" class="form-control attribute {{if Required}}required{{/if}}" placeholder="${Name}" data-id="${Id}" data-type-id="${Type}" value="${DefaultValue}"/>
                <div class="help-block with-errors"></div>
            </div>
        </div>
    </script>

    <script type="text/x-jquery-tmpl" id="t-fileAttachment">
        <div class="form-group">
            <label for="file-${attachmentCounter}" class="col-sm-2 control-label"></label>
            <div class="col-sm-10">
                <input type="text" id="tb-file-path-${attachmentCounter}" class="tb-file-path" value="No file chosen..." readonly="readonly" data-id="${attachmentCounter}" />
                <input type="button" class="tb-file-remove btn btn-danger" data-id="${attachmentCounter}" value="Remove" style="display: none;" />
                <span class="btn btn-primary fileinput-button">
                    <span>Select file...</span>
                    <input id="file-${attachmentCounter}" class="tb-file-input form-control" name="MyFile" type="file" data-id="${attachmentCounter}" data-error="The file you selected is not a valid file type or the size is too large." accept="image/*,application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel" />
                </span>
                <div class="help-block with-errors"></div>
            </div>
        </div>
    </script>


    <div class="container">
        <div class="row">
            <div class="col-xs-3 hidden-xs @(ViewBag.Model.Logo == null ? "hidden" : "") "><img class="logo" src="@((ViewBag.Model.Logo ?? "").Replace("http://", "//"))" /></div>
            <!-- Add the extra clearfix for only the required viewport -->
            <div class="clearfix visible-xs-block"></div>
            <div class="col-xs-9">
                <address>
                    <h3>@ViewBag.Model.CompanyName</h3>
                    @ViewBag.Model.CompanyFullAddress<br />
                    Phone: <a href="tel:@ViewBag.Model.CompanyPhone">@ViewBag.Model.CompanyPhone</a><br />
                    @if (!String.IsNullOrWhiteSpace(ViewBag.Model.CompanyFax))
                    {
                        <text>Fax:</text> @ViewBag.Model.CompanyFax<br />
                    }
                </address>
            </div>

        </div>

        <div id="requestFormContainer">
            <form class="form-horizontal" id="requestForm" data-toggle="validator">
                <div class="alert alert-info fade in">
                    <h3>Request Service</h3>
                    <p>Fill in details below to request service from @ViewBag.Model.CompanyName.</p>
                </div>

                <div class="panel panel-info">
                    <div class="panel-heading">Vehicle Information</div>
                    <div class="panel-body">
                        <div class="form-group" style="display: @(ViewBag.Accounts.Count == 0 ? "none" : "block")">
                            <label for="RequestingAccount" class="col-sm-2 control-label">
                            @if (ViewBag.Model.CompanyId == 49039)
                                {<text>Zone</text> }
                            else
                            { <text>Requested By</text>}
                            </label>
                            <div class="col-sm-10">
                                <select id="RequestingAccount" class="form-control" data-error="You must specify a @if (ViewBag.Model.CompanyId == 49039) {<text>zone</text> } else { <text>requesting location.</text>}" @(ViewBag.Accounts.Count > 0 ? "required" : "")>
                                    <option value="">
                                        Pick a  @if (ViewBag.Model.CompanyId == 49039)
                                        {<text>zone</text> }
                                    else
                                    { <text>requesting account.</text>}
                                    </option>
                                    @foreach (var a in ViewBag.Accounts)
                                    {
                                        <option value="@a.Id">@a.Company</option>
                                    }
                                </select>
                                <div class="help-block with-errors"></div>
                            </div>

                        </div>

                        <div class="form-group" style="@if (ViewBag.Model.CompanyId != 49039) { <text>display:none</text>}">
                            <label for="weight" class="col-sm-2 control-label">Weight</label>
                            <div class="col-sm-10">
                                <select id="weight" class="form-control" required data-error="You must specify the weight class">
                                    <option value="1">Light</option>
                                    @if (ViewBag.Model.CompanyId == 49039)
                                    {
                                        <option value="3">Heavy</option>
                                    }
                                </select>
                            </div>
                        </div>
                        <div class="form-group" style="display: @(ViewBag.BillToAccounts.Count == 0 ? "none" : "block")">
                            <label for="BillToAccount" class="col-sm-2 control-label">Bill To</label>
                            <div class="col-sm-10">
                                <select id="BillToAccount" class="form-control" data-error="You must specify a Bill To account." @(ViewBag.BillToAccounts.Count > 0 ? "required" : "")>
                                    <option value="">Pick an account to bill for this service</option>
                                    @foreach (var b in ViewBag.BillToAccounts)
                                    {
                                        <option value="@b.Id">@b.Company</option>
                                    }
                                </select>
                                <div class="help-block with-errors"></div>
                            </div>
                        </div>


                        <div class="form-group" style="display: @(ViewBag.Accounts.Count > 0 ? "none" : "block")">
                          <label for="checkLocation" class="col-sm-2 control-label" style="display: @(string.IsNullOrWhiteSpace(ViewBag.Model.AccountFullAddress) ? "none" : "block")">@(string.IsNullOrWhiteSpace(ViewBag.Model.AccountFullAddress) ? "" : "Vehicle Location")</label>
                          <div class="col-sm-10" style="display: @(ViewBag.Accounts.Count != 0 || string.IsNullOrWhiteSpace(ViewBag.Model.AccountFullAddress) ? "none" : "block")">
                            <div class="checkbox">
                              <label>
                                <input id="checkLocation" type="checkbox" @(ViewBag.HasTemplate || ViewBag.Accounts.Count != 0 || ViewBag.Model.AccountFullAddress == "" ? "" : "checked")> Pick up at @ViewBag.Model.AccountCompanyName @ViewBag.Model.AccountFullAddress
                              </label>
                            </div>
                          </div>
                        </div>
                        <div class="form-group">
                            <label for="towSource" class="col-sm-2 control-label">@(ViewBag.HasTemplate || ViewBag.Accounts.Count != 0 || string.IsNullOrWhiteSpace(ViewBag.Model.AccountFullAddress) ? "Vehicle Location" : "")</label>
                            <div class="col-sm-10">
                                <input id="towSource" 
                                       type="text" 
                                       class="tb-geocode-address form-control @(WebFormUtility.IsFieldRequired("Location Information", "Pickup Location", ViewBag.Template) ? "required" : "")" 
                                       onfocus="geolocate()" 
                                       placeholder="Enter the location of the vehicle" @(string.IsNullOrEmpty(ViewBag.Model.AccountFullAddress) ? "" : "disabled") 
                                       data-error="Enter the address or location of the vehicle to be serviced." 
                                       value="@WebFormUtility.GetLocationDefaultAddress("Pickup Location", ViewBag.Template, string.Empty)" 
                                       @(WebFormUtility.IsFieldRequired("Location Information", "Pickup Location", ViewBag.Template) ? "required" : "")/>
                                <div class="help-block with-errors"></div>
                            </div>
                        </div>
                        
                        <div class="form-group" style="display: @(WebFormUtility.IsFieldVisible("Location Information", "Destination Location", ViewBag.Template, !ViewBag.Model.RoadsideOnly) ? "block" : "none")">
                            <label for="towDestination" class="col-sm-2 control-label">Destination</label>
                            <div class="col-sm-10">
                              <input id="towDestination"
                                     type="text"
                                     class="tb-geocode-address form-control @(WebFormUtility.IsFieldRequired("Location Information", "Destination Location", ViewBag.Template) ? "required" : "")"
                                     onfocus="geolocate()"
                                     placeholder="Enter where the vehicle is being towed to"
                                     data-error="Enter the address or location where the vehicle should be delivered to."
                                     value="@WebFormUtility.GetLocationDefaultAddress("Destination Location", ViewBag.Template, string.Empty)" @(WebFormUtility.IsFieldRequired("Location Information", "Destination Location", ViewBag.Template) ? "required" : "") />
                              <div class="help-block with-errors"></div>
                            </div>
                        </div>
                        
                        
                        <div class="form-group" id="section-reasons" style="display: @(WebFormUtility.IsFieldVisible("Other Information", "Reason", ViewBag.Template, true) ? "block" : "none")">
                            <label for="reason" class="col-sm-2 control-label">Reason</label>
                            <div class="col-sm-10">
                                <select id="reason" 
                                        class="form-control @(WebFormUtility.IsFieldRequired("Other Information", "Reason", ViewBag.Template) ? "required" : "")" 
                                        data-error="Please specify a Reason."
                                        @(WebFormUtility.IsFieldRequired("Other Information", "Reason", ViewBag.Template) ? "required" : "")>
                                    <option value="">Pick the reason for this request.</option>
                                </select>
                                <div class="help-block with-errors"></div>
                            </div>
                        </div>
                        
                        <div class="form-group" style="display: @(WebFormUtility.IsFieldVisible("Vehicle Details", "License Plate", ViewBag.Template, true) ? "block" : "none")">
                            <label for="LicensePlate" class="col-sm-2 control-label">License Plate</label>
                            <div class="col-sm-10">
                              <input id="LicensePlate"
                                     type="text"
                                     class="form-control @(WebFormUtility.IsFieldRequired("Vehicle Details", "License Plate", ViewBag.Template) ? "required" : "")"
                                     placeholder="Enter the vehicle license plate"
                                     data-error="A license plate is required."
                                     @(WebFormUtility.IsFieldRequired("Vehicle Details", "License Plate", ViewBag.Template) ? "required" : "") />
                              <div class="help-block with-errors"></div>
                            </div>
                        </div>

                        <div class="form-group" style="display: @(WebFormUtility.IsFieldVisible("Vehicle Details", "License Plate State", ViewBag.Template, true) ? "block" : "none")">
                            <label for="LicensePlateState" class="col-sm-2 control-label">License Plate State</label>
                            <div class="col-sm-10">
                              <select id="LicensePlateState"
                                      class="form-control @(WebFormUtility.IsFieldRequired("Vehicle Details", "License Plate State", ViewBag.Template) ? "required" : "")"
                                      data-error="A license plate state is required."
                                      @(WebFormUtility.IsFieldRequired("Vehicle Details", "License Plate State", ViewBag.Template) ? "required" : "")></select>
                              <div class="help-block with-errors"></div>
                            </div>
                        </div>
                        <div class="form-group" style="display: @(WebFormUtility.IsFieldVisible("Vehicle Details", "VIN", ViewBag.Template, true) ? "block" : "none")">
                            <label for="VIN" class="col-sm-2 control-label">VIN</label>
                            <div class="col-sm-10">
                              <input id="VIN"
                                     type="text"
                                     class="form-control @(WebFormUtility.IsFieldRequired("Vehicle Details", "VIN", ViewBag.Template) ? "required" : "")"
                                     placeholder="Enter the vehicle VIN"
                                     data-error="The vehicle VIN is required."
                                     @(WebFormUtility.IsFieldRequired("Vehicle Details", "VIN", ViewBag.Template) ? "required" : "") />
                              <div class="help-block with-errors"></div>
                            </div>
                        </div>
                        <div class="form-group" style="display: @(WebFormUtility.IsFieldVisible("Vehicle Details", "Year", ViewBag.Template, true) ? "block" : "none")">
                            <label for="year" class="col-sm-2 control-label">Year</label>
                            <div class="col-sm-10">
                              <select id="year"
                                      class="form-control @(WebFormUtility.IsFieldRequired("Vehicle Details", "Year", ViewBag.Template) ? "required" : "")"
                                      data-error="The vehicle year is required."
                                      @(WebFormUtility.IsFieldRequired("Vehicle Details", "Year", ViewBag.Template) ? "required" : "")></select>
                              <div class="help-block with-errors"></div>
                            </div>
                        </div>
                        <div class="form-group" style="display: @(WebFormUtility.IsFieldVisible("Vehicle Details", "Make", ViewBag.Template, true) ? "block" : "none")">
                            <label for="make" class="col-sm-2 control-label">Make</label>
                            <div class="col-sm-10">
                              <select id="make"
                                      class="form-control @(WebFormUtility.IsFieldRequired("Vehicle Details", "Make", ViewBag.Template) ? "required" : "")"
                                      data-error="The vehicle make is required."
                                      @(WebFormUtility.IsFieldRequired("Vehicle Details", "Make", ViewBag.Template) ? "required" : "")></select>
                              <div class="help-block with-errors"></div>
                            </div>
                        </div>
                        <div class="form-group" style="display: @(WebFormUtility.IsFieldVisible("Vehicle Details", "Model", ViewBag.Template, true) ? "block" : "none")">
                            <label for="model" class="col-sm-2 control-label">Model</label>
                            <div class="col-sm-10">
                              <select id="model"
                                      class="form-control @(WebFormUtility.IsFieldRequired("Vehicle Details", "Model", ViewBag.Template) ? "required" : "")"
                                      data-error="The vehicle model is required."
                                      @(WebFormUtility.IsFieldRequired("Vehicle Details", "Model", ViewBag.Template) ? "required" : "")></select>
                              <div class="help-block with-errors"></div>
                            </div>
                        </div>
                        <div class="form-group" style="display: @(WebFormUtility.IsFieldVisible("Vehicle Details", "Color", ViewBag.Template, true) ? "block" : "none")">
                            <label for="color" class="col-sm-2 control-label">Color</label>
                            <div class="col-sm-10">
                              <select id="color"
                                      class="form-control @(WebFormUtility.IsFieldRequired("Vehicle Details", "Color", ViewBag.Template) ? "required" : "")"
                                      data-error="The vehicle color is required."
                                      @(WebFormUtility.IsFieldRequired("Vehicle Details", "Color", ViewBag.Template) ? "required" : "")></select>
                              <div class="help-block with-errors"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="panel panel-info">
                    <div class="panel-heading" id="AdditionalSection">Additional Information</div>
                    <div class="panel-body">
                        <div class="form-group" style="display: @(WebFormUtility.IsFieldVisible("Other Information", "Notes", ViewBag.Template, true) ? "block" : "none")">
                            <label for="notes" class="col-sm-2 control-label">Notes</label>
                            <div class="col-sm-10">
                              <textarea id="notes"
                                        class="form-control @(WebFormUtility.IsFieldRequired("Other Information", "Notes", ViewBag.Template) ? "required" : "")"
                                        rows="3"
                                        @(WebFormUtility.IsFieldRequired("Other Information", "Notes", ViewBag.Template) ? "required" : "")>@WebFormUtility.GetDefaultValue("Other Information", "Notes", ViewBag.Template, string.Empty)</textarea>
                              <div class="help-block with-errors"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="panel panel-info" style="display: @(WebFormUtility.IsSectionVisible("Contact Details", ViewBag.Template, true) ? "block" : "none")">
                    <div class="panel-heading">Contact Information</div>
                    <div class="panel-body">
                        <div class="form-group" style="display: @(WebFormUtility.IsFieldVisible("Contact Details", "Name", ViewBag.Template, true) ? "block" : "none")">
                            <label for="ContactName" class="col-sm-2 control-label">First/Last Name</label>
                            <div class="col-sm-10">
                                <input id="ContactName" 
                                       type="text" 
                                       class="form-control @(WebFormUtility.IsFieldRequired("Contact Details", "Name", ViewBag.Template) ? "required" : "")" 
                                       placeholder="Enter your name" 
                                       value="@WebFormUtility.GetContactDefaultValue("Name", ViewBag.Template, string.Empty)"
                                       data-error="Your name is required.  We need to know who to contact."
                                       @(WebFormUtility.IsFieldRequired("Contact Details", "Name", ViewBag.Template) ? "required" : "")/>
                                <div class="help-block with-errors"></div>
                            </div>
                        </div>
                        <div class="form-group" id="section-contact-phone" style="display: @(WebFormUtility.IsFieldVisible("Contact Details", "Phone Number", ViewBag.Template, true) ? "block" : "none")">
                            <label for="ContactPhone" class="col-sm-2 control-label">Phone Number</label>
                            <div class="col-sm-10">
                              <input id="ContactPhone"
                                     type="text"
                                     class="form-control @(WebFormUtility.IsFieldRequired("Contact Details", "Phone Number", ViewBag.Template) ? "required" : "")"
                                     placeholder="Best way to speak to you"
                                     value="@WebFormUtility.GetContactDefaultValue("Phone Number", ViewBag.Template, string.Empty)"
                                     data-error="A phone number is required."
                                     @(WebFormUtility.IsFieldRequired("Contact Details", "Phone Number", ViewBag.Template) ? "required" : "") />
                              <div class="help-block with-errors"></div>
                            </div>
                        </div>
                        <div class="form-group" id="section-contact-email" style="display: @(WebFormUtility.IsFieldVisible("Contact Details", "Email", ViewBag.Template, true) ? "block" : "none")">
                            <label for="ContactEmail" class="col-sm-2 control-label">Email</label>
                            <div class="col-sm-10">
                                <input id="ContactEmail" 
                                       type="email" 
                                       class="form-control @(WebFormUtility.IsFieldRequired("Contact Details", "Email", ViewBag.Template) ? "required" : "")" 
                                       placeholder="Your email address" 
                                       value="@WebFormUtility.GetContactDefaultValue("Email", ViewBag.Template, string.Empty)"
                                       data-error="That email address is invalid." 
                                       @(WebFormUtility.IsFieldRequired("Contact Details", "Email", ViewBag.Template) ? "required" : "")/>
                                <div class="help-block with-errors"></div>
                                <br />
                                <span style="font-style: italic; font-size: small; color: #888;">
                                    We respect your privacy and will never sell, rent, lease or give away your information to any third party.<br /><br />

                                    By providing a telephone number and submitting the form you are consenting to be contacted by SMS text message (our message frequency may vary). Message & data rates may apply. Reply STOP to opt-out of further messaging.
                                </span>
                            </div>
                        </div>
                    </div>
                </div>


                <div class="form-group">
                    <div class="col-sm-offset-2 col-sm-10">
                        <button id="requestCall" type="submit" class="btn btn-primary">Send</button>
                    </div>
                </div>
            </form>
        </div>
        <div id="requestComplete" style="display: none;">
            <div class="panel panel-info">
                <div class="panel-heading">Request Complete</div>
                <div class="panel-body"></div>
            </div>
            <button type="button" class="btn btn-primary" onclick="location.reload();">Request Another service</button>
        </div>

        <div id="requestDisabled" class="panel panel-info" style="display:none;">
            <div class="panel-heading">Public request access is disabled</div>
            <div class="panel-body">The ability to request a service is disabled at this time.  Please call us at @ViewBag.Model.CompanyPhone to request a service.</div>
        </div>

        <div class="row">
            <div class="col-xs-12">
                @RenderBody()
                <hr />
                <footer>
                    <p>&copy; @DateTime.Now.Year - Towbook</p>
                </footer>
            </div>
        </div>
    </div>

    <script src="~/Scripts/bootstrap.min.js"></script>

    <script>
    (function (i, s, o, g, r, a, m) {
        i['GoogleAnalyticsObject'] = r; i[r] = i[r] || function () {
            (i[r].q = i[r].q || []).push(arguments)
        }, i[r].l = 1 * new Date(); a = s.createElement(o),
        m = s.getElementsByTagName(o)[0]; a.async = 1; a.src = g; m.parentNode.insertBefore(a, m)
    })(window, document, 'script', '//www.google-analytics.com/analytics.js', 'ga');

    ga('create', 'UA-546662-14', 'auto');
    ga('send', 'pageview');

    </script>
</body>
</html>