
<script type="text/javascript">
    /* Global declarations */
    var accounts = @Html.Raw(ViewBag.AccountsJSON);
    // unique identifier for attachments
    var attachmentCounter = 0;
    // jqXHRData needed for grabbing by data object of fileupload scope
    var jqXHRData = [];
    // file extensions allowed
    var fileTypeRegexp = new RegExp("^.*\.(jpg|JPG|bmp|BMP|png|PNG|gif|GIF|tiff|TIFF|tif|TIF|doc|DOC|docx|DOCX|pdf|PDF)$");
    // google map object
    var autocompleteTowSource, autocompleteTowDestination;

    var getCallData;
    var callData = @Html.Raw(ViewBag.PublicRequestCallModel);
    var template = @Html.Raw(ViewBag.TemplateJSON);

    Date.prototype.toDateInputValue = (function () {
      var local = new Date(this);
      local.setMinutes(this.getMinutes() - this.getTimezoneOffset());
      return local.toJSON().slice(0, 10);
    });

    String.prototype.fromDateInputValue = (function () {
      var local = new Date(this);
      local.setMinutes(local.getMinutes() + new Date().getTimezoneOffset());
      return local.toLocaleDateString('en-us');
    });

    Date.prototype.toDateTimeInputValue = (function () {
      var local = new Date(this);
      local.setMinutes(this.getMinutes() - this.getTimezoneOffset());
      var dateStr = local.toJSON();
      return dateStr.slice(0, 10) + "T" + dateStr.slice(11, dateStr.length - 1);
    });

    String.prototype.fromDateTimeInputValue = (function () {
      var local = new Date(this);
      return local.toLocaleDateString('en-us') + " " + local.toLocaleString('en-US', { hour: 'numeric', minute: 'numeric', hour12: true });
    });

    Date.prototype.toTimeInputValue = (function () {
      var local = new Date(this);
      local.setMinutes(this.getMinutes() - this.getTimezoneOffset());
      var dateStr = local.toJSON();
      return dateStr.slice(11, dateStr.length - 1);
    });

    String.prototype.fromTimeInputValue = (function () {
      var local = new Date(new Date().toLocaleDateString() + " " + this);
      return local.toLocaleString('en-US', { hour: 'numeric', minute: 'numeric', hour12: true });
    });

    function showLoading(title) {
      if (!title)
        title = "Please wait...";

      swal({
        title: title,
        allowOutsideClick: false,
        closeOnEsc: false,
        onOpen: function () {
          swal.showLoading()
        }
      });
    }

    $(function () {
        'use strict';

        // DOM obj declarations
        var disabledObj = $('#requestDisabled');
        var formObj = $('#requestForm');
        var successObj = $('#requestComplete');
        var yearObj = $('#year');
        var makeObj = $('#make');
        var modelObj = $('#model');
        var plateObj = $('#LicensePlate');
        var stateObj = $('#LicensePlateState');
        var vinObj = $('#VIN');
        var weightObj = $('#weight');
        var colorObj = $('#color');
        var reasonObj = $('#reason');
        var notesObj = $('#notes');
        var nameObj = $('#ContactName');
        var phoneObj = $('#ContactPhone');
        var emailObj = $('#ContactEmail');
        var accountObj = $('#RequestingAccount');
        var billToAccountObj = $('#BillToAccount');

        compileTemplate('t-fieldType1', $("#t-fieldType1"));
        compileTemplate('t-fieldType2', $("#t-fieldType2"));
        compileTemplate('t-fieldType3', $("#t-fieldType3"));
        compileTemplate('t-fieldType4', $("#t-fieldType4"));
        compileTemplate('t-fieldType5', $("#t-fieldType5"));
        compileTemplate('t-fieldType6', $("#t-fieldType6"));
        compileTemplate('t-fileAttachment', $("#t-fileAttachment"));

        // Function expressions section
        getCallData = function() {
            var makeId = parseInt(makeObj.val());
            if (!isNaN(makeId) && makeId > 0) {
              callData["make"] = $('#make option:selected').text();
              callData["makeId"] = makeId;
            }

            var modelId = parseInt(modelObj.val());
            if (!isNaN(modelId) && modelId > 0) {
              callData["model"] = $('#model option:selected').text();
              callData["modelId"] = modelId;
            }

            var colorId = parseInt(colorObj.val());
            if (!isNaN(colorId) && colorId > 0) {
              callData["colorId"] = colorId;
            }

            var reasonId = parseInt(reasonObj.val());
            if(!isNaN(reasonId) && reasonId > 0) {
              callData["reasonId"] = reasonObj.val();
            }

            var bodyTypeId = parseInt(weightObj.val());
            if (!isNaN(bodyTypeId) && bodyTypeId > 0) {
              callData["bodyTypeId"] = weightObj.val();
            }

            var licenseState = stateObj.val();
            if (licenseState != "(none)")
              callData["licenseState"] = stateObj.val();

            callData["licenseNumber"] = plateObj.val();
            callData["year"] = yearObj.val();
            callData["vin"] = vinObj.val();
            callData["notes"] = notesObj.val();
            callData["contactName"] = nameObj.val();
            callData["contactPhone"] = phoneObj.val();
            callData["contactEmail"] = emailObj.val();

            if ($('#checkLocation').is(":checked"))
                callData["towSource"] = "@(ViewBag.Model != null ? ViewBag.Model.AccountFullAddress : "")";
            else
                callData["towSource"] = $("#towSource").val();

            callData["towDestination"] = $('#towDestination').val();

            // Collect company/account attribute values
            var obj = [];
            $('.attribute').each(function(index, item) {
              var attributeId = $(this).data('id');
              var value = $(this).val();
              var type = parseInt($(this).data('typeId'));
              if (!isNaN(type)) {
                if (type == 4)
                  value = $(this).val().fromDateInputValue();

                if (type == 5)
                  value = $(this).val().fromDateTimeInputValue();

                if (type == 6)
                  value = $(this).val().fromTimeInputValue();
              }

              obj.push({
                  Id: attributeId,
                  Value: value
              });
            });

            callData["attributeValues"] = obj;

            callData["accountId"] = @(ViewBag.Model != null ? ViewBag.Model.AccountId : "");
            if(!isEmpty(accounts)) {
                var accountId = $(accountObj).val();
                callData["accountId"] = accountId;
                var account = $.grep(accounts, function(o) { return o.id == accountId; });
                if(account.length){
                    callData["impound"] = account[0].impoundStorageLotId > 1 ? true : false;
                    callData["storageLotId"] = account[0].impoundStorageLotId;
                }

            }

            if($(billToAccountObj).is(':visible'))
                callData["billToAccountId"] = $(billToAccountObj).val();

            console.log("[calldata]", callData);

            return callData;
        }

        // Function declaration section
        function populateModel(makeId, modelId) {
            modelObj.find('option')
                .remove()
                .end()
                .append('<option value="0">(none)</option>');

            $.ajax({
                url: "/model?makeId=" + makeId
            }).done(function (data) {
                console.log("AC: new models to populate ", data);

                $.each(data, function(index, item) {
                    modelObj.append(new Option(item.Value, item.Key));
                });

                if(modelId != null)
                    modelObj.val(modelId);
            });
        }

        function plateToVin(plate, state) {
            if(plate == "" || state == 0)
                return;
            console.log("AC: looking up VIN using ", plate, state);

            $.ajax({
                url: ("/api/@(ViewBag.Model != null ? Extric.Towbook.Utility.EncryptionHelper.ToHashId(ViewBag.Model.AccountPublicRequestLinkId) : "")?plate=" + plate + "&state=" + state),
                success: function (result) {
                    console.log("AC: plate to vin found: ", result);
                    if (result.Year >= 1900) {
                        var weight = result.Weight;

                        yearObj.val(result.Year);
                        makeObj.val(result.MakeId);
                        populateModel(result.MakeId, result.ModelId);
                        vinObj.val(result.Vin);

                        if (weight > 0 && weight <= 1500) {
                            weightObj.val(4);
                        } else if (weight > 1500 && weight <= 10000) {
                            weightObj.val(1);
                        } else if (weight > 20000 && weight <= 30000) {
                            weightObj.val(2);
                        } else if (weight > 30000) {
                            weightObj.val(3);
                        }
                    }
                },
                complete: function () {
                }
            });
        }

        function addFileAttachmentRow(addLabel) {
            var fileObj = applyTemplate('t-fileAttachment', {attachmentCounter: attachmentCounter++});

            if(addLabel)
                fileObj.find('label').html("Attachment(s)");

            $('#AdditionalSection').next().append(fileObj);

            // Add row events
            fileObj.find('.tb-file-input').on('change', function() {
                var id = $(this).data('id');
                $('#tb-file-path-' + id).val(this.files[0].name);

                if($('.tb-file-path').last().val() != "No file chosen...")
                    addFileAttachmentRow(false);
            });

            fileObj.find('.tb-file-remove').on('click', function() {
                var id = $(this).data('id');

                jqXHRData = $.grep(jqXHRData, function(item) {
                    return item.id != id;
                });

                // make sure the label doesn't disappear
                if(fileObj.find('label').html().length)
                    fileObj.next().find('label').html("Attachment(s)");

                fileObj.remove();
            });

            // this event works with plugin called blueimp https://blueimp.github.io/jQuery-File-Upload/
            fileObj.find('.tb-file-input').fileupload({
                url: 'api/file/upload/',
                dataType: 'json',
                add: function (e, data) {

                    // check file size and file types
                    if(data.files[0].size > 1024*1024*4)
                    {
                        $(this).data('error', 'File exceeds maximum allowed size of 4MB.')
                        $(this).attr('required', 'true');
                    }
                    else if(data.files[0]['type'] == "")
                    {
                        $(this).data('error', 'The type of file attached is not allowed.');
                        $(this).attr('required', 'true');
                    }
                    else if(data.files[0]['type'].length && !fileTypeRegexp.test(data.files[0].name))
                    {
                        $(this).data('error', 'File type "' + data.files[0].type + '" is not allowed.');
                        $(this).attr('required', 'true');
                    }

                    // This is where we add the file data object to our global array
                    var id = $(this).data('id');
                    jqXHRData.push({id: id, jqXHRData: data});

                    fileObj.find('.tb-file-remove').show();
                },
                beforeSend: function () {
                    this.url += this.token;
                },
                done: function (event, data) {
                    console.log('AC: finished file upload.  File Id is ' + data.result.CompanyFileId + '. Result message is "' + data.result.Message) + '."';
                },
                fail: function (event, data) {
                    console.log('AC: file upload FAILED.');
                    if (data.files[0].error) {
                        console.log('AC: file upload FAILED. Result message is "' + data.messages[data.files[0].error] + '."');
                    }
                }
            }).on('fileuploadadd', function(e,data) {

                console.log("AC: inside fileuploadadd event.");


            });
        }

        // Event Handler section
        $('#LicensePlate').on('blur', function() {
            var plate = plateObj.val();
            var state = stateObj.val();

            plateToVin(plate, state);
        }).on('keyup', function() {
            var plate = plateObj.val();

            if(plate == "")
                plateObj.css({"text-transform": "initial"});
            else
                plateObj.css({"text-transform": "uppercase"});
        });

        $('#LicensePlateState').change(function() {
            $(this).find(":selected").each(function() {
                var plate = plateObj.val();
                var state = stateObj.val();

                plateToVin(plate, state);
            });
        });

        $('#make').change(function() {
            populateModel($(this).val(), null);
        });

        $('#checkLocation').change(function() {
            if(this.checked)
            {
                $('#towSource')
                    .attr('disabled', true)
                    .closest('.form-group')
                    .removeClass('has-error')
                    .find('.help-block')
                    .html("");

              if($('#towSource').val() == "")
                $('#towSource').val("@ViewBag.Model.AccountFullAddress");
            }
            else
            {
              $('#towSource')
                .attr('disabled', false);
            }
        });

        $(accountObj).change(function() {
            var id = $(accountObj).val();
            var curr = $('#towSource').val();
            var check = $.grep(accounts, function(o, i) {
                return o.fullAddress == curr;
            });
            var ra = $.grep(accounts, function(o, i) {
                return o.id == id;
            });

            if(ra.length)
            {
                if(ra[0].impoundStorageLotId > 0)
                {
                    $('#towDestination').val(ra[0].lotFullAddress);

                    if(ra[0].type == @((int)Extric.Towbook.Accounts.AccountType.PrivateProperty) &&
                        curr == "" || check.length != 0)
                    {
                        $('#towSource').val(ra[0].fullAddress).trigger('blur');
                    }
                }
                else if((ra[0].type == @((int)Extric.Towbook.Accounts.AccountType.ServiceShop)
                    || ra[0].type == @((int) Extric.Towbook.Accounts.AccountType.BodyShop)
                    || ra[0].type == @((int) Extric.Towbook.Accounts.AccountType.Transport)
                    || ra[0].type == @((int) Extric.Towbook.Accounts.AccountType.Dealership))
                    && (curr == "" || check.length != 0))
                {
                    $('#towDestination').val(ra[0].fullAddress).trigger('blur');
                }
                else
                {
                    $('#towSource').val(ra[0].fullAddress).trigger('blur');
                }
            }



            // change billToAccount selection if it isn't already set
            var bVal = $(billToAccountObj).val();
            if(bVal == "") {
                if($("#BillToAccount option[value=" + id + "]").length)
                    $(billToAccountObj).val(id);
            }
        });

        // final submit and POST
        $("#requestForm").validator().on('submit', function(e) {
            var call = getCallData();
            console.log("AC: ready to send!", callData);

            $.ajax({
                url: "api/@(ViewBag.Model != null ? Extric.Towbook.Utility.EncryptionHelper.ToHashId(ViewBag.Model.AccountPublicRequestLinkId) : "")/",
                type: 'POST',
                data: JSON.stringify(callData),
                contentType: "application/json; charset=utf-8",
                beforeSend: function () {
                  showLoading();
                }
            }).done(function (result) {
                var data = JSON.parse(result);

                console.log("AC: Entry created successfully! ", data);

                // now upload file(s) now that we have the dispatchEntryId of the new entry
                $.each(jqXHRData, function(index, item) {
                    if (!isEmpty(item)  && !isEmpty(item.jqXHRData)) {
                        item.jqXHRData.token = data.token;
                        item.jqXHRData.submit();
                    }
                });

                formObj.hide();

                successObj.show().find('.panel-body').append('<p>Thank you. You have successfully requested a service @(ViewBag.Model != null ? "from " + ViewBag.Model.CompanyName : ""). @(ViewBag.Model != null ? "If you have any questions or concerns please call us at " + ViewBag.Model.CompanyPhone + "." : "")</p><p style="font-weight: bold">The reference number for this request is #' + data.callNumber + '.</p>');
                swal.close();
            }).error(function (xhr, status, error) {
                console.log("AC: Error! " + status + ": " + error, xhr);

                swal({
                  type: 'error',
                  title: 'There was a problem...',
                  text: xhr.responseText || "Server returned status of " + xhr.status
                });
            });

          return false;
        });

        // Main initilize method to prepare and display form
        (function initializeFields() {

            // setup validation for #towSource if the account doesn't have an address
            @if (ViewBag.Accounts != null || (ViewBag.Model != null && string.IsNullOrWhiteSpace(ViewBag.Model.AccountFullAddress))) {
            <text>
            $('#towSource').attr('disabled', false).prop('required', 'true');
            </text>
            }

            autocompleteTowSource = new google.maps.places.Autocomplete(document.getElementById('towSource'));
            autocompleteTowDestination = new google.maps.places.Autocomplete(document.getElementById('towDestination'));

            // user selects an address from the suggestion
            google.maps.event.addListener(autocompleteTowSource, 'place_changed', function() {
                var place = autocompleteTowSource.getPlace();
                if(!isEmpty(place)) {
                    document.getElementById('towSource').value = place.formatted_address;
                }
            });

            google.maps.event.addListener(autocompleteTowDestination, 'place_changed', function() {
                var place = autocompleteTowDestination.getPlace();
                if(!isEmpty(place)) {
                    document.getElementById('towDestination').value = place.formatted_address;
                }
            });

             @if (ViewBag.Model != null && ViewBag.Model.CompanyId == 49039)
             {
                    <text>
                $('#section-attachment, #section-contact-phone, #section-contact-email').hide();

                    </text>
             }
             else
             {
                 <text>addFileAttachmentRow(true);</text>
             }
             // Add first file attachment option
            $.ajax({
                url: "api/@(ViewBag.Model != null ? Extric.Towbook.Utility.EncryptionHelper.ToHashId(ViewBag.Model.AccountPublicRequestLinkId) : "")/",
                type: "GET"
            }).done(function(data) {
                console.log("Finished loading config ", data);

                // don't allow form to show if the public account link has been disabled
                if(data.Disabled == true)
                {
                    disabledObj.show();
                    return;
                }
                else
                {
                    formObj.show();
                }

                // initialize selects with returned data
                makeObj.append(new Option("(none)", 0));
                $.each(data["VehicleMakes"], function(index, item) {
                    makeObj.append(new Option(item.Name, item.Id));
                });

                modelObj.append(new Option("(none)", 0));

                colorObj.append(new Option("(none)", 0));
                $.each(data["VehicleColors"], function(index, item) {
                    colorObj.append(new Option(item.Name, item.Id));
                });

                if (data["Reasons"] && data["Reasons"].length > 0) {
                    $.each(data["Reasons"], function (index, item) {
                        reasonObj.append(new Option(item.Name, item.Id));
                    });

                    if (data["Reasons"].length == 1)
                      $(reasonObj).val(data["Reasons"][0].Id);

                } else {
                    $('#section-reasons').hide();
                }


                var year = (new Date).getFullYear() + 1;
                yearObj.append(new Option("(none)", 0));
                for (var i = year; i > year - 100; i--) {
                    yearObj.append('<option value="' + i + '">' + i + '</option>\r\n');
                }

                stateObj.append(new Option("(none)", 0));
                $.each(data["States"], function(index, item) {
                    stateObj.append(new Option(item.Long, item.Short));
                });

                // add additional fields specified in dbo.AccountPublicRequestFormFields
                if(!isEmpty(data.Fields)) {
                    for(var i = 0; i < data.Fields.length; i++) {
                        var field = data.Fields[i];
                        var row = null;

                        if (field.Visible)
                          field.display = "block";
                        else
                          field.display = "none";

                        switch(field.Type) {
                            case 2: // input field with numbers only (no text)
                                row = applyTemplate('t-fieldType2', field);
                                break;

                            case 3: // tri-state select (yes, no, not specified)
                                row = applyTemplate('t-fieldType3', field);
                                break;

                            case 4: // date only
                                row = applyTemplate('t-fieldType4', field)
                                break;

                            case 5: // date time only
                                row = applyTemplate('t-fieldType5', field)
                                break;

                            case 6: // time only
                                row = applyTemplate('t-fieldType6', field)
                                break;

                            default: // standard input field
                                row = applyTemplate('t-fieldType1', field);
                                break;
                        }

                        if(row != null)
                        {
                            // Add validation, if necessary
                            if(field.Required == true)
                            {
                                $(row).find('select').prop('required', 'true');
                                $(row).find('input').prop('required', 'true');
                            }

                            $('#AdditionalSection').next().prepend(row);


                          if (field.Type == 4 && field.DefaultValue && field.DefaultValue.length) {
                            $(row).find('input').val(new Date(field.DefaultValue).toDateInputValue());
                          }

                          if (field.Type == 5 && field.DefaultValue && field.DefaultValue.length) {
                            $(row).find('input').val(new Date(field.DefaultValue).toDateTimeInputValue());
                          }

                          if (field.Type == 6 && field.DefaultValue && field.DefaultValue.length) {
                            var dateOnly = new Date().toLocaleDateString("en-US");
                            var fullDateString = dateOnly + " " + field.DefaultValue;
                            $(row).find('input').val(new Date(fullDateString).toTimeInputValue());
                          }
                        }
                    }
                }
            });
        })()


        $('#checkLocation').trigger('change');

        @if (ViewBag.Model != null && ViewBag.Model.CompanyId == 10884)
            {
                 <text> $('#checkLocation').parent().remove();
        $('#towSource')
            .attr('disabled', false)
            .prop('required', 'true');
        </text>
            }
    });

</script>


@{
    @using Microsoft.AspNetCore.Http.Extensions;

    var hostName = System.Net.Dns.GetHostName();
    var ips = await System.Net.Dns.GetHostAddressesAsync(hostName);

    //@if (HttpContext.Current.Request.ServerVariables["LOCAL_ADDR"] == "************" && !HttpContext.Current.Request.IsSecureConnection)
    @if (Array.Exists(ips, i => i.ToString().Equals("")) && !Context.Request.IsHttps)
    {
        //HttpContext.Current.Response.Redirect(HttpContext.Current.Request.Url.AbsoluteUri.Replace("http://", "https://"), true);
        Context.Response.Redirect(UriHelper.GetEncodedUrl(Context.Request).Replace("http://", "https://"), true);
    }
}
