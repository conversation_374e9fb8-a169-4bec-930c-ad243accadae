using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Mail;
using System.Threading.Tasks;
using Extric.Towbook.Accounts;
using Extric.Towbook.API.Controllers;
using Extric.Towbook.API.Models.Accounts;
using Extric.Towbook.Dispatch;
using Extric.Towbook.Generated;
using Extric.Towbook.Integration;
using Extric.Towbook.Utility;
using Extric.Towbook.Web.PublicRequestCallMvc.Models;
using Extric.Towbook.WebShared;
using Microsoft.AspNetCore.Mvc;
using Extric.Towbook.EventNotifications;

namespace Extric.Towbook.Web.PublicRequestCallMvc.Controllers
{
    [ApiController]
    [Route("api")]
    public class PublicRequestController : Controller
    {
        public static string GetRequestingIp()
        {
            return WebGlobal.GetRequestingIp();

        }


        private enum EmailType
        {
            ToTowbookAccount = 0,
            ToRequester = 1
        }

        private static dynamic statesUSA = new[] {
            new { Long = "Alabama", Short = "AL" },
            new { Long = "Alaska", Short = "AK" },
            new { Long = "Arizona", Short = "AZ" },
            new { Long = "Arkansas", Short = "AR" },
            new { Long = "California", Short = "CA" },
            new { Long = "Colorado", Short = "CO" },
            new { Long = "Connecticut", Short = "CT" },
            new { Long = "Delaware", Short = "DE" },
            new { Long = "District of Columbia", Short = "DC" },
            new { Long = "Florida", Short = "FL" },
            new { Long = "Georgia", Short = "GA" },
            new { Long = "Hawaii", Short = "HI" },
            new { Long = "Idaho", Short = "ID" },
            new { Long = "Illinois", Short = "IL" },
            new { Long = "Indiana", Short = "IN" },
            new { Long = "Iowa", Short = "IA" },
            new { Long = "Kansas", Short = "KS" },
            new { Long = "Kentucky", Short = "KY" },
            new { Long = "Louisiana", Short = "LA" },
            new { Long = "Maine", Short = "ME" },
            new { Long = "Montana", Short = "MT" },
            new { Long = "Nebraska", Short = "NE" },
            new { Long = "Nevada", Short = "NV" },
            new { Long = "New Hampshire", Short = "NH" },
            new { Long = "New Jersey", Short = "NJ" },
            new { Long = "New Mexico", Short = "NM" },
            new { Long = "New York", Short = "NY" },
            new { Long = "North Carolina", Short = "NC" },
            new { Long = "North Dakota", Short = "ND" },
            new { Long = "Ohio", Short = "OH" },
            new { Long = "Oklahoma", Short = "OK" },
            new { Long = "Oregon", Short = "OR" },
            new { Long = "Maryland", Short = "MD" },
            new { Long = "Massachusetts", Short = "MA" },
            new { Long = "Michigan", Short = "MI" },
            new { Long = "Minnesota", Short = "MN" },
            new { Long = "Mississippi", Short = "MS" },
            new { Long = "Missouri", Short = "MO" },
            new { Long = "Pennsylvania", Short = "PA" },
            new { Long = "Rhode Island", Short = "RI" },
            new { Long = "South Carolina", Short = "SC" },
            new { Long = "South Dakota", Short = "SD" },
            new { Long = "Tennessee", Short = "TN" },
            new { Long = "Texas", Short = "TX" },
            new { Long = "Utah", Short = "UT" },
            new { Long = "Vermont", Short = "VT" },
            new { Long = "Virginia", Short = "VA" },
            new { Long = "Washington", Short = "WA" },
            new { Long = "West Virginia", Short = "WV" },
            new { Long = "Wisconsin", Short = "WI" },
            new { Long = "Wyoming", Short = "WY" },
            new { Long = "Mexico", Short = "MEX" }
        };

        private static dynamic statesCanada = new[] {
            new { Short = "AB", Long = "Alberta" },
            new { Short = "BC", Long = "British Columbia" },
            new { Short = "MB", Long = "Manitoba" },
            new { Short = "NB", Long = "New Brunswick" },
            new { Short = "NL", Long = "Newfoundland and Labrador" },
            new { Short = "NS", Long = "Nova Scotia" },
            new { Short = "NT", Long = "Northwest Territories" },
            new { Short = "NU", Long = "Nunavut" },
            new { Short = "ON", Long = "Ontario" },
            new { Short = "PE", Long = "Prince Edward Island" },
            new { Short = "QC", Long = "Quebec" },
            new { Short = "SK", Long = "Saskatchewan" },
            new { Short = "YT", Long = "Yukon" }
        };

        private static dynamic statesAustralia = new[] {
            new { Long = "Queensland", Short = "Qld" },
            new { Long = "South Australia", Short = "SA" },
            new { Long = "Tasmania", Short = "Tas" },
            new { Long = "Victoria", Short = "Vic" },
            new { Long = "Western Australia", Short = "WA" },
            new { Long = "Northern Territory", Short = "NT" },
            new { Long = "New South Wales", Short = "NSW" },
            new { Long = "Australian Capital Territory", Short = "ACT" }
        };

        private async Task SendEmail(
            int callNumber,
            int dispatchEntryId,
            Company.Company company,
            Account account,
            PublicRequestCallModel call,
            EmailType type)
        {
            if (type == EmailType.ToRequester && string.IsNullOrEmpty(call.ContactEmail))
                return;

            string color = call.ColorId > 0 ? (await Vehicle.Color.GetByIdAsync(call.ColorId)).Name : "";
            using (MailMessage msg = new MailMessage())
            {
                if (type == EmailType.ToTowbookAccount)
                {
                    msg.From = new MailAddress("<EMAIL>", "Towbook Management Software");

                    var overrideEmail = CompanyKeyValue.GetFirstValueOrNull(
                        call.CompanyId,
                        Provider.Towbook.ProviderId,
                        "PublicRequestCallEmailTo");

                    if (overrideEmail != null)
                        msg.To.Add(overrideEmail);
                    else
                        msg.To.Add(Integrations.Email.EmailAddress.GetTowbookDotNetEmailAddressForCompany(call.CompanyId));

                    msg.Subject = "[#" + callNumber + "] New Service request from " + account.Company;
                    msg.Body += "You have received a service request from " + account.Company + ". Here is a summary of the information that has been provided:\r\n\r\n";
                }
                else if (type == EmailType.ToRequester)
                {
                    if (!Core.IsEmailValid(call.ContactEmail))
                        return;

                    var fromEmail = Integrations.Email.EmailAddress.GetTowbookDotNetEmailAddressForCompany(call.CompanyId);

                    if (!Core.IsEmailValid(fromEmail))
                        return;

                    msg.From = new MailAddress(fromEmail, company.Name);
                    msg.ReplyToList.Add(new MailAddress(company.Email));
                    msg.To.Add(call.ContactEmail);
                    msg.Subject = "[#" + callNumber + "] We have received your service request";
                    msg.Body = call.ContactName + ",\r\n\r\n";
                    msg.Body += "Thank you for requesting a service from " + company.Name + ". We have received your request and will dispatch a representative as quickly as possible.  Here is a summary of the information you have provided:\r\n\r\n";
                }

                msg.Priority = MailPriority.High;
                msg.IsBodyHtml = false;

                if (call.Year != 0 || string.IsNullOrWhiteSpace(call.Make) || !string.IsNullOrWhiteSpace(call.Model) || !string.IsNullOrWhiteSpace(color))
                {
                    msg.Body += "Vehicle: " + (call.Year > 0 ? call.Year + " " : "") + call.Make + " " + call.Model + " " + color + "\r\n";
                }

                if (!string.IsNullOrWhiteSpace(call.LicenseNumber))
                    msg.Body += "Vehicle Tag: " + call.LicenseNumber + " " + (call.LicenseState != "0" ? call.LicenseState : "") + "\r\n";

                if (!string.IsNullOrWhiteSpace(call.VIN))
                    msg.Body += "Vehicle VIN: " + call.VIN + "\r\n";

                msg.Body += "Pickup Location: " + call.TowSource + "\r\n";

                if (!string.IsNullOrWhiteSpace(call.Notes))
                    msg.Body += "Notes: " + call.Notes + "\r\n";

                foreach (var av in call.AttributeValues)
                {
                    var a = await Dispatch.Attribute.GetByIdAsync((int)av.Id);
                    if (a != null)
                    {
                        var value = av.Value;
                        if (a.Type == AttributeType.Boolean)
                            value = av.Value == "1" ? "yes" : av.Value == "0" ? "no" : "";

                        msg.Body += a.Name + ": " + av.Value + "\r\n";
                    }
                }
                if (!string.IsNullOrWhiteSpace(call.ContactName))
                    msg.Body += "Contact Name: " + call.ContactName + "\r\n";
                if (!string.IsNullOrWhiteSpace(call.ContactEmail))
                    msg.Body += "Contact Email: " + call.ContactEmail + "\r\n";
                if (!string.IsNullOrWhiteSpace(call.ContactPhone))
                    msg.Body += "Contact Phone: " + call.ContactPhone + "\r\n\r\n";

                if (type == EmailType.ToRequester)
                {
                    msg.Body += "If you have any questions, please feel free to contact " + company.Name + " at " + company.Phone + " and use the reference number " + callNumber + ".\r\n\r\n";

                    msg.Body += "Thank you,\r\n\r\n";

                    msg.Body += company.Name + "\r\n";
                    msg.Body += company.Phone + "\r\n";
                }

                if (type == EmailType.ToTowbookAccount)
                {
                    msg.Body += "We have automatically created the call for you and is waiting for you to dispatch.  Please visit https://app.towbook.com/ to dispatch this call or modify the call details.\r\n";

                    msg.Body += "Thank you,\r\n\r\n";

                    msg.Body += "Towbook Management Software\r\n";
                    msg.Body += "<EMAIL>\r\n";
                }

                var metaData = new Dictionary<string, string>()
                {
                    { "accountId", call.AccountId.ToString() },
                    { "companyId", call.CompanyId.ToString() },
                };

                using (var sc = new SmtpClient().Get())
                {
                    await sc.Send(msg, await Towbook.User.GetByIdAsync(1), metaData, "PublicRequestForm");
                }
            }
        }

        public class FieldModel
        {
            public int Id { get; set; }
            public string Name { get; set; }
            public int Type { get; set; }
            public bool Required { get; set; }
            public bool Visible { get; set; }
            public string DefaultValue { get; set; }
        }

        private static FieldModel MapAttribute(Extric.Towbook.Accounts.AccountPublicRequestFormField prf, Dispatch.Attribute att, bool visible = true, string defaultValue = "")
        {
            var required = prf?.Required ?? false;

            return new FieldModel
            {
                Id = att.Id,
                Name = att.Name,
                Type = (int)att.Type,
                Required = required,
                Visible = visible,
                DefaultValue = defaultValue
            };
        }

        [HttpGet]
        [Route("{id}")]
        public async Task<object> Get(string id)
        {
            var m = new List<VehicleMakeModel>();
            var c = new List<KeyValueModel>();
            var bodyTypes = new List<KeyValueModel>();
            var fields = new List<FieldModel>();
            var reasons = new List<KeyValueModel>();
            dynamic states = Array.Empty<object>();
            var l = await AccountPublicRequestLink.GetByIdAsync(EncryptionHelper.FromHash(id));
            AccountPublicRequestFormTemplateSetting setting = null;
            var templateCustomFields = new List<AccountPublicRequestFormTemplateCustomField>();

            if (l != null)
            {
                var company = await Company.Company.GetByIdAsync(l.CompanyId);
                if (company != null)
                {
                    if (company.Country == Company.Company.CompanyCountry.USA)
                        states = statesUSA;
                    else if (company.Country == Company.Company.CompanyCountry.Canada)
                        states = statesCanada;
                    else if (company.Country == Company.Company.CompanyCountry.Australia)
                        states = statesAustralia;
                }

                bodyTypes = (await Extric.Towbook.Vehicle.BodyType.GetByCompanyIdAsync(l.CompanyId)).Select(o => new KeyValueModel() { Id = o.Id, Name = o.Name }).ToList();

                var allReasons = (await Reason.GetByCompany(company))
                    .Where(o => !o.IsGoa() && o.IsActive);

                if (await company.HasFeatureAsync(Features.Accounts_AdvancedWebForms) && l.TemplateId > 0)
                {
                    setting = AccountPublicRequestFormTemplateSetting.GetByTemplateId(l.TemplateId.GetValueOrDefault());
                    if (setting != null)
                    {
                        if (setting.ReasonDefaultOption == DefaultReasonValueOption.UseDefaultReason)
                        {
                            reasons = allReasons
                                    .Where(w => setting.ReasonDefaultValueId == w.Id)
                                    .Select(o => new KeyValueModel() { Id = o.Id, Name = o.Name })
                                    .ToList();
                        }
                        else
                        {
                            var templateReasons = AccountPublicRequestFormTemplateReason.GetByTemplateIds(new[] { l.TemplateId.GetValueOrDefault() });
                            if (templateReasons.Any())
                            {
                                reasons = allReasons
                                    .Where(w => templateReasons.Any(a => a.ReasonId == w.Id))
                                    .Select(o => new KeyValueModel() { Id = o.Id, Name = o.Name })
                                    .ToList();
                            }
                        }

                        templateCustomFields = AccountPublicRequestFormTemplateCustomField.GetByTemplateId(l.TemplateId.GetValueOrDefault()).ToList();
                    }
                    else
                    {
                        reasons = allReasons.Select(o => new KeyValueModel() { Id = o.Id, Name = o.Name }).ToList();
                    }
                }
                else
                    reasons = allReasons.Select(o => new KeyValueModel() { Id = o.Id, Name = o.Name }).ToList();
            }

            foreach (var x in (await Vehicle.Manufacturer.GetAllAsync()))
            {
                m.Add(new VehicleMakeModel() { Id = x.Id, Name = x.Name });
            }

            foreach (var x in (await Vehicle.Color.GetAllAsync()))
            {
                c.Add(new KeyValueModel() { Id = x.Id, Name = x.Name });
            }


            // Original way of adding custom fields through db entries (obsoleted by advanced web form templates)
            foreach (var x in await Extric.Towbook.Accounts.AccountPublicRequestFormField.GetByAccountId(l.AccountId, l.CompanyId))
            {
                Dispatch.Attribute att = x.Attribute;

                if (att != null)
                {
                    fields.Add(MapAttribute(x, att));
                }
            }

            if (fields.Count == 0)
            {
                // purchase order number
                Dispatch.Attribute poAtt = await Dispatch.Attribute.GetByIdAsync(4);

                var visible = true;
                var defaultValue = "";

                if (setting != null)
                {
                    if (setting.PurchaseOrderDefaultOption == DefaultValueOption.SpecifyDefault)
                        defaultValue = setting.PurchaseOrderDefaultValue;

                    if (setting.PurchaseOrderOption == SettingOption.Hidden)
                        visible = false;

                    var prf = new AccountPublicRequestFormField()
                    {
                        Required = setting.PurchaseOrderOption == SettingOption.Required
                    };

                    fields.Add(MapAttribute(prf, poAtt, visible, defaultValue));
                }
                else
                    fields.Add(MapAttribute(null, poAtt));
            }

            if (templateCustomFields.Any() && setting != null)
            {
                foreach (var tcf in templateCustomFields)
                {
                    var attribute = await Dispatch.Attribute.GetByIdAsync(tcf.DispatchEntryAttributeId);

                    var visible = tcf.IncludeOption != SettingOption.Hidden;
                    var defaultValue = "";

                    if (tcf.DefaultOption == DefaultValueOption.SpecifyDefault)
                        defaultValue = tcf.DefaultValue;

                    var prf = new AccountPublicRequestFormField()
                    {
                        Required = tcf.IncludeOption == SettingOption.Required
                    };

                    fields.Add(MapAttribute(prf, attribute, visible, defaultValue));
                }
            }

            var ret = new
            {
                VehicleMakes = m,
                VehicleColors = c,
                VehicleTypes = bodyTypes.ToArray(),
                States = states,
                Disabled = l.Deleted,
                Fields = fields.ToArray(),
                Reasons = reasons
            };

            return ret;
        }

        [HttpGet]
        public async Task<object> GetResolver([FromQuery] string plate, [FromQuery] string state, [FromQuery] int makeId)
        {
            if ((plate == null && state == null && makeId == 0) ||
                (plate != null && state != null && makeId > 0))
            {
                return NotFound();
            }

            if (plate != null && state != null)
            {
                return Get(plate, state);
            }

            if (makeId > 0)
            {
                return await Model(makeId);
            }

            return NotFound();
        }

        [HttpGet]
        [Route("~/model")]
        [Route("~/model/[controller]")]
        public async Task<object> GetModel([FromQuery] int makeId) => makeId > 0 ? await Model(makeId) : NotFound();

        private object Get([FromQuery] string plate, [FromQuery] string state)
        {
            try
            {
                var eu = ExperianUtility.FromLicensePlate(plate, state);
                if (eu != null)
                {
                    var vin = eu.Vehicle.FirstOrDefault().Vin;
                    var r = VinDecoder.Decode(vin);

                    if (r != null)
                    {
                        return r;
                    }
                    else
                    {
                        return new
                        {
                            Vin = vin,
                            Year = eu.Vehicle.First().Vinspec.Year,
                            Make = eu.Vehicle.First().Vinspec.Make,
                            Model = eu.Vehicle.First().Vinspec.Model,
                            Body = eu.Vehicle.First().Vinspec.Body
                        };
                    }
                }

                return NoContent(); // new HttpResponseMessage(HttpStatusCode.NoContent);
            }
            catch (TowbookException)
            {
                return StatusCode((int)HttpStatusCode.ServiceUnavailable, "Towbook is currently unable to process License Plate to VIN requests. Please try again later.");
            }
        }

        private static async Task<object> Model([FromQuery] int makeId)
        {
            Dictionary<int, string> m = new Dictionary<int, string>();

            foreach (var x in await Vehicle.Model.GetByManufacturerIdAsync(makeId))
            {
                if (x.ManufacturerId == makeId)
                    m.Add(x.Id, x.Name);
            }

            return m.ToArray();
        }

        [HttpPost]
        [Route("{id}")]
        public async Task<object> Post([FromRoute] string id, [FromBody] PublicRequestCallModel m)
        {
            if (string.IsNullOrEmpty(m.TowSource))
            {
                return BadRequest($"A pickup location is required. You must specify an address of service.");
            }

            var r = new AccountPublicRequest();
            var l = await AccountPublicRequestLink.GetByIdAsync(EncryptionHelper.FromHash(id));

            if (l == null)
            {
                return StatusCode((int)HttpStatusCode.Forbidden, $"Invalid id {id}. The link is invalid or you don't have permission to perform this action.");
            }


            var account = await Account.GetByIdAsync(m.AccountId);

            // verify that the model's account's companyId is associated with the link's companyId
            if (account == null || !account.Companies.Contains(l.CompanyId))
            {
                return StatusCode((int)HttpStatusCode.Forbidden, $"Invalid account or you don't have permission to perform this action.");
            }

            if (l.Accounts.Count() > 0 && !l.Accounts.Any(o => o.Id == account.Id))
            {
                return StatusCode((int)HttpStatusCode.Forbidden, $"Invalid account or you don't have permission to perform this action.");
            }

            var companyId = account.CompanyId;

            // check for override company id
            var akv = AccountKeyValue.GetFirstValueOrNull(account.CompanyId, account.Id, Provider.Towbook.ProviderId, "OverrideToSharedCompanyId");
            if (akv != null)
            {
                int cId;
                if (int.TryParse(akv, out cId))
                    companyId = cId;
            }

            var company = await Company.Company.GetByIdAsync(companyId);

            var e = new Dispatch.Entry()
            {
                AccountId = account.Id,
                Account = account,
                CompanyId = company.Id,
                Company = company,
                OwnerUserId = 1,
                TowSource = m.TowSource,
                TowDestination = m.TowDestination,
                Notes = m.Notes
            };

            // validate submitted data against required
            if (await company.HasFeatureAsync(Features.Accounts_AdvancedWebForms) &&
                l.TemplateId > 0)
            {
                var template = AccountPublicRequestFormTemplate.GetById(l.TemplateId.GetValueOrDefault());
                var setting = AccountPublicRequestFormTemplateSetting.GetByTemplateId(l.TemplateId.GetValueOrDefault());
                if (template != null && setting != null)
                {
                    var templateModel = await AccountPublicRequestFormTemplateModel.MapAsync(
                        template,
                        null,
                        null,
                        setting,
                        AccountPublicRequestFormTemplateModel.DefaultSections());

                    var validationResponseMessage = await WebFormUtility.ValidateRequiredFieldsAsync(templateModel, m);
                    if (!string.IsNullOrEmpty(validationResponseMessage))
                    {
                        return StatusCode((int)HttpStatusCode.Forbidden, validationResponseMessage);
                    }

                    // apply impound lot, if necessary
                    if (setting.DestinationImpoundLotId > 0 && setting.DestinationLocationDefaultOption == DefaultLocationValueOption.UseImpoundLot)
                    {
                        m.StorageLotId = setting.DestinationImpoundLotId.GetValueOrDefault();
                    }
                }
            }

            // add reason
            if (m.ReasonId != null)
            {
                var reason = await Dispatch.Reason.GetByIdAsync(m.ReasonId.Value);
                if (reason != null)
                {
                    if (reason.CompanyId == 0 || reason.CompanyId == m.CompanyId)
                        e.ReasonId = m.ReasonId.Value;
                }
            }

            // build and add Asset to call
            var ea = new Dispatch.EntryAsset()
            {
                Year = m.Year,
                Make = m.Make,
                Model = m.Model,
                ColorId = m.ColorId,
                BodyTypeId = m.BodyTypeId,
                Vin = m.VIN,
                LicenseNumber = m.LicenseNumber,
                LicenseState = m.LicenseState
            };

            e.Assets.Add(ea);

            if (e.Account != null)
            {
                // If this account has a DefaultPriority of High, then set it
                if (e.Account.DefaultPriority == 1)
                    e.Priority = Entry.EntryPriority.High;

                // If this account has a DefaultPO
                if (!string.IsNullOrWhiteSpace(e.Account.DefaultPO))
                {
                    // If we don't already have a PO set
                    if (string.IsNullOrEmpty(e.PurchaseOrderNumber))
                    {
                        // Set it
                        e.PurchaseOrderNumber = e.Account.DefaultPO;
                    }
                }
            }

            AuthenticationToken token = AuthenticationToken.GetByUserId(8, 184);
            string ipAddress = GetRequestingIp();

            // get purchase order number
            var x = m.AttributeValues.FirstOrDefault(f => f.Id == 4);
            if (x != null) e.PurchaseOrderNumber = (x.Value);

            // Add Contact
            var ec = new Dispatch.EntryContact()
            {
                Name = (m.ContactName),
                Phone = Core.FormatPhoneWithDashesOnly(m.ContactPhone),
                Email = Core.IsEmailValid(m.ContactEmail) ? m.ContactEmail : "",
                Type = Dispatch.ContactType.Individual
            };

            e.Contacts.Add(ec);

            // handle Attributes
            foreach (var f in m.AttributeValues)
            {
                var av = new Dispatch.AttributeValue()
                {
                    DispatchEntryAttributeId = f.Id,
                    Value = f.Value,
                };

                e.Attributes.Add(av.DispatchEntryAttributeId, av);
            }

            // add current call acknowledge attribute
            e.SetAttribute(Dispatch.AttributeValue.BUILTIN_DISPATCH_CURRENT_CALL_ACKNOWLEDGEMENT_JSON, "{}");

            await e.Save(false, token, ipAddress);


            #region FSC - Fuel Surcharges
            var fuel = e.InvoiceItems.FirstOrDefault(w => w.RateItem != null && w.RateItem.RateItemId == RateItem.BUILTIN_FUEL_SURCHARGE);
            if (fuel == null && e.CalculatedSurchargeTotals.ContainsKey(RateItem.BUILTIN_FUEL_SURCHARGE))
            {
                if (e.Attributes.ContainsKey(Dispatch.AttributeValue.BUILTIN_SURCHARGE_RATE))
                    e.SetAttribute(Dispatch.AttributeValue.BUILTIN_SURCHARGE_RATE, "");

                if (e.Attributes.ContainsKey(Dispatch.AttributeValue.BUILTIN_SURCHARGE_EXCLUSIONS))
                    e.SetAttribute(Dispatch.AttributeValue.BUILTIN_SURCHARGE_EXCLUSIONS, "");

                var surcharge = await Surcharges.SurchargeRate.GetBySurchargeAsync(Surcharges.Surcharge.SURCHARGE_FUEL, e.CompanyId);
                if (surcharge != null)
                {
                    if (fuel == null)
                        fuel = new InvoiceItem();

                    fuel.InvoiceId = e.Invoice.Id;
                    fuel.RateItem = await RateItem.GetByIdAsync(RateItem.BUILTIN_FUEL_SURCHARGE);
                    fuel.Quantity = 1;
                    fuel.CustomPrice = e.CalculatedSurchargeTotals[Surcharges.Surcharge.SURCHARGE_FUEL];
                    fuel.Taxable = surcharge.Taxable;

                    if (fuel.Id < 1)
                        e.Invoice.InvoiceItems.Add(fuel);
                }
            }

            if (fuel != null && fuel.Id < 1)
                await e.Invoice.SaveAsync(token, ipAddress);

            #endregion

            // Impound ?
            if (m.Impound)
            {
                e.Impound = true;
                if (m.StorageLotId != null)
                {
                    Extric.Towbook.Impounds.Impound impound = new Extric.Towbook.Impounds.Impound();
                    if (impound != null)
                    {
                        impound.Lot = await Impounds.Lot.GetByIdAsync(m.CompanyId, m.StorageLotId.Value);
                        impound.Company = await Extric.Towbook.Company.Company.GetByIdAsync(m.CompanyId);
                        impound.DispatchEntry = e;
                        impound.OwnerUserId = e.OwnerUserId;
                        await impound.Save(await Extric.Towbook.User.GetByIdAsync(e.OwnerUserId));

                        if (e.Invoice != null && impound != null && impound.Lot.AccountId != null)
                        {
                            var inv = e.Invoice;
                            inv.AccountId = impound.Lot.AccountId.Value;
                            await inv.SaveAsync(token, ipAddress);
                        }
                    }
                }
            }

            // Add BillToAccount (if not null)
            if (e.Invoice != null && m.BillToAccountId != null)
            {
                var inv = e.Invoice;
                inv.AccountId = m.BillToAccountId;
                await inv.SaveAsync(token, ipAddress);
            }

            // Save request
            r.AccountPublicRequestLinkId = l.Id;
            r.AccountId = l.AccountId;
            r.CreateDate = DateTime.Now;
            r.IpAddress = ipAddress;
            r.DispatchEntryId = e.Id;
            r.Save();

            // generate a token and add as redis key to allow auxilary request to occur for a temporary amount of time per callId
            // (and not expose the callId in the request).  This allows files to be uploaded to a call anonymously with a short use token.
            var idempotencyKey = Guid.NewGuid().ToString("N");
            var key = $"publicRequestToken:{idempotencyKey}";
            Core.SetRedisValue(key, e.Id.ToString(), TimeSpan.FromMinutes(5));

            r.Token = idempotencyKey;

            // Add built-in attribute to indicate that this call is created by the public request
            var aprid = new Dispatch.AttributeValue();
            aprid.DispatchEntryAttributeId = Extric.Towbook.Dispatch.AttributeValue.BUILTIN_ACCOUNT_PUBLIC_REQUEST_ID;
            aprid.Value = r.Id.ToString();
            aprid.DispatchEntryId = e.Id;
            await aprid.SaveAsync(token, ipAddress);

            // Send confirmation email
            await SendEmail(e.CallNumber, e.Id, e.Company, e.Account, m, EmailType.ToRequester);

            // Send notification email
            if (!string.IsNullOrEmpty(e.Company.Email))
                await SendEmail(e.CallNumber, e.Id, e.Company, e.Account, m, EmailType.ToTowbookAccount);

            // Trigger Event notification here that email was parsed into a call
            if (await e.Company.HasFeatureAsync(Generated.Features.Notifications_StandardEventNotifications))
            {
                var item = new Extric.Towbook.EventNotifications.DispatchingQueueItem();
                item.Type = Extric.Towbook.EventNotifications.DispatchingTriggerType.CallSentViaWebRequestForm;
                item.DispatchEntryId = e.Id;
                await item.TriggerEvent();
            }

            #region Priority Call Text Alert
            if (await e.Company.HasFeatureAsync(Features.PriorityCallTextAlert) && e.Account != null)
            {
                if (AccountKeyValue.GetFirstValueOrNull(e.CompanyId, e.Account.Id, Provider.Towbook.ProviderId, "EnablePriorityCallTextAlertToAllManagers") == "1")
                {
                    var item = new EventNotifications.AccountQueueItem()
                    {
                        AccountId = e.Account.Id,
                        Type = EventNotifications.AccountTriggerType.PriorityCallTextAlert,
                        DispatchEntryId = e.Id,
                    };

                    await item.TriggerEvent();
                }
            }
            #endregion

            var rx = await NotifySubscribers(e.Id);

            //return new HttpResponseMessage() { 
            //    StatusCode = HttpStatusCode.Created, 
            //    Content = new StringContent(r.ToJson()) 
            //};
            return StatusCode((int)HttpStatusCode.Created, r.ToJson());
        }

        public static async Task<bool> NotifySubscribers(int callId)
        {
            var call = Entry.GetById(callId);

            if (call.Account == null)
                return false;

            var c = await Company.Company.GetByIdAsync(call.CompanyId);

            if (await c.HasFeatureAsync(Features.MotorClubIntegration_MobileNotifications))
            {
                var usersToNotify = Extric.Towbook.User.GetByCompanyId(c.Id).Where(o => o.Disabled == false && o.Deleted == false &&
                    (o.Type == Extric.Towbook.User.TypeEnum.Manager || o.Type == Extric.Towbook.User.TypeEnum.Dispatcher) &&
                    (!(o.Notes ?? "").Contains("DisableMobileNotifications")));

                string message = "New call created by " + call.Account.Company;

                foreach (var u in usersToNotify)
                {
                    var values = new Dictionary<string, string>();

                    values.Add("Message", message);
                    values.Add("Received Time", Core.OffsetDateTime(c, DateTime.Now).ToShortTowbookTimeString());

                    var keys = u.GetKeys();

                    values.Add("Call Number", call.CallNumber.ToString());

                    if (!string.IsNullOrEmpty(call.PurchaseOrderNumber))
                        values.Add("PO #", call.PurchaseOrderNumber);

                    if (keys.Any(o => o.Key == "notificationhub_registration_id"))
                    {
                        var alertSound = Extric.Towbook.EventNotifications.EventNotificationAlertSound.GetByItemId(call.CompanyId, (int)EventNotificationType.CallSentViaWebRequestForm);
                        var sound = alertSound?.EventNotificationSoundId > 0 ? Extric.Towbook.EventNotifications.EventNotificationSound.GetById(alertSound.EventNotificationSoundId) : null;

                        await NotificationHubHelper.GeneralChannelInstance.SendNotificationMessage(u,
                            "New account call request from " + call.Account.Company,
                            values, true, "Call Created", sound);
                    }

                    Console.WriteLine("Notified: " + u.FullName + "/" + u.Username);
                }
            }

            return true;
        }
    }
}
