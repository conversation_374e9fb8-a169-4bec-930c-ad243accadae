using Extric.Towbook.Company;
using Extric.Towbook.Dispatch;
using Extric.Towbook.Storage;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Text;
using System.Globalization;
using Extric.Towbook.Integration;
using Extric.Towbook.Dispatch.QuoteModels;
using Extric.Towbook;
using Extric.Towbook.API.Models.Calls;
using Extric.Towbook.Utility;
using Extric.Towbook.WebShared;
using System.Threading.Tasks;
using Microsoft.Azure.Cosmos;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Hosting;
using System.Text.Encodings.Web;
using System.Drawing;
using System.Drawing.Imaging;
//using QRCoder;
using Microsoft.AspNetCore.Mvc.ViewFeatures;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using System.Net.Http;
using Microsoft.Extensions.Primitives;
using Extric.Towbook.ShortLinks;

// Interface definition for view rendering service
public interface IViewRenderer
{
    Task<string> RenderViewToStringAsync<TModel>(string viewName, TModel model);
    Task<string> RenderViewToStringAsync(string viewName, object model);
}

namespace Extric.Towbook.Web.Pages.Dispatch
{
    /// <summary>
    /// View model for invoice items to be displayed in the view
    /// </summary>
    public class InvoiceItemViewModel
    {
        public string ItemName { get; set; }
        public string Quantity { get; set; }
        public string UnitPrice { get; set; }
        public string LineTotal { get; set; }
        public string Notes { get; set; }
        public bool ShowNotes { get; set; }
        public bool IsVisible { get; set; } = true;
        public InvoiceItem OriginalItem { get; set; }
    }

    /// <summary>
    /// Invoice page model for displaying towing/service invoices.
    /// This has been converted from a WebForms implementation to ASP.NET Core Razor Pages.
    /// 
    /// This page model handles various invoice types including:
    /// - Towing invoices
    /// - Service receipts
    /// - Recovery operations
    /// - Transport invoices
    /// 
    /// It includes customization for different company settings and account types.
    /// </summary>
    public class InvoiceModel : PageModel
    {
        private readonly IWebHostEnvironment _environment;
        private readonly HtmlEncoder _htmlEncoder;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IViewRenderer _viewRenderer;

        /// <summary>
        /// Initializes a new instance of the <see cref="InvoiceModel"/> class.
        /// </summary>
        /// <param name="environment">The web hosting environment</param>
        /// <param name="htmlEncoder">HTML encoder for safely encoding user input</param>
        /// <param name="httpContextAccessor">HTTP context accessor for request information</param>
        /// <param name="viewRenderer">Optional view renderer for rendering views to string</param>
        public InvoiceModel(IWebHostEnvironment environment, HtmlEncoder htmlEncoder, IHttpContextAccessor httpContextAccessor, IViewRenderer viewRenderer = null)
        {
            _environment = environment;
            _htmlEncoder = htmlEncoder;
            _httpContextAccessor = httpContextAccessor;
            _viewRenderer = viewRenderer;
        }

        // Helper method to replace Server.HtmlEncode
        private string HtmlEncode(string input)
        {
            if (string.IsNullOrEmpty(input))
                return string.Empty;
            return _htmlEncoder.Encode(input);
        }

        public bool SingleColumn { get; set; } = false;
        public Entry DispatchEntry { get; set; }
        public decimal Tax { get; set; }
        public decimal TaxableAmount { get; set; }
        public string InvoiceNumberText { get; set; }
        public string TaxName { get; set; }
        public Company.Company Company { get; set; }
        public string BillingNotesEncoded { get; set; }
        public string DriverSignatureName { get; set; } = "";
        public List<Extric.Towbook.Licenses.CompanyLicenseKeyValue> LicenseValues { get; set; }
        public List<Extric.Towbook.Licenses.CompanyLicenseKey> Keys { get; set; }
        public bool ShowPaymentLink { get; set; } = false;
        public string ShortLink { get; set; } = "";
        public bool ServiceReceiptWithNoVehicleDetails { get; set; } = false;
        public List<DispatchEntryPaymentTip> EntryTips { get; set; }
        public bool ShowDrivable { get; set; } = false;

        public List<string> ShowFields { get; set; } = new List<string>();
        public List<string> HideFields { get; set; } = new List<string>();

        /// <summary>
        /// URL to the driver's signature image for the invoice
        /// </summary>
        public string SignatureUrl { get; set; }
        
        /// <summary>
        /// Date when the signature was captured
        /// </summary>
        public DateTime? SignatureDate { get; set; }

        /// <summary>
        /// Label for the origin location of the tow
        /// </summary>
        public string TowFromNameLabel { get; set; } = "Tow From";
        
        /// <summary>
        /// Label for the destination location of the tow
        /// </summary>
        public string TowToNameLabel { get; set; } = "Tow To";

        public QuoteModel Quote { get; set; }

        // Company information
        public string CompanyNameText { get; set; }
        public string CompanyAddressText { get; set; }
        public string CompanyPhoneText { get; set; }
        public string CompanyFaxText { get; set; }
        public string CompanyEmailText { get; set; }

        // Account information
        public string AccountNameText { get; set; }
        public string AccountInfoText { get; set; }

        // Vehicle and travel information
        public string VehicleText { get; set; }
        public string TravelText { get; set; }
        public string NotesText { get; set; }
        
        // Financial information
        public decimal SubtotalValue { get; set; }
        public decimal TaxesValue { get; set; }
        public decimal GrandTotalValue { get; set; }
        
        /// <summary>
        /// Gets or sets the total amount of tips from all EntryTips
        /// </summary>
        public decimal TipTotal { get; set; }
        
        // Payment properties
        public string PaymentLink { get; set; }
        public string PaymentQrCode { get; set; }
        public bool HidePaymentLink { get; set; } = true;

        // Update property to use the view model
        public List<InvoiceItemViewModel> InvoiceItems { get; set; } = new List<InvoiceItemViewModel>();

        public List<Photo> Photos { get; set; }
        public User CurrentUser { get; set; } = WebGlobal.CurrentUser;

        /// <summary>
        /// Handles GET requests to the invoice page.
        /// Loads and processes all data needed to display the invoice.
        /// </summary>
        /// <returns>An IActionResult that renders the page or redirects as needed</returns>
        public async Task<IActionResult> OnGetAsync()
        {
            Guid quoteId = Guid.Empty;
            Quote = null;
            
            // Handle quote lookup via query string
            if (Request.Query.ContainsKey("q") && Guid.TryParse(Request.Query["q"], out quoteId))
            {
                Quote = (CosmosDB.Get().QueryItems<QuoteModel>("quotes",
                    new QueryDefinition($"SELECT * FROM c WHERE c.quoteId = '{quoteId}' AND c.deleted = false"))
                    .FirstOrDefault());
                    
                if (Quote == null)
                {
                    return new ContentResult
                    {
                        ContentType = "text/html",
                        Content = "<h1>There was a problem</h1><p>The quote cannnot be found or is deleted or you don't have permission to view it.</p>",
                        StatusCode = 404
                    };
                }

                DispatchEntry = new Entry();
                DispatchEntry.CreateDate = Quote.CreateDate;

                DispatchEntry = await Extric.Towbook.API.Models.Calls.CallModelExtensions.MapAsync(Quote.Call, DispatchEntry);

                foreach (var asset in Quote.Call.Assets)
                    DispatchEntry.Assets.Add(await Extric.Towbook.API.Models.Calls.CallAssetModelExtensions.TranslateAsync(asset));

                var trav = Quote.Call.Attributes.FirstOrDefault(f => f.AttributeId == Extric.Towbook.Dispatch.AttributeValue.BUILTIN_TAXRATE_OVERRIDE);

                foreach (var ii in Quote.Call.InvoiceItems) 
                {
                    var taxrates = new List<InvoiceItemTax>();
                    int trId = 0;
                    if (trav != null && int.TryParse(trav.Value, out trId) && trId > 0 && ii.Taxable.GetValueOrDefault() == true) 
                    {
                        taxrates.Add(new InvoiceItemTax()
                        {
                            TaxRateId = trId,
                            InvoiceItemId = ii.Id.GetValueOrDefault()
                        });
                    }


                    var i = InvoiceItem.GetById(ii.Id != null && ii.Id > 0 ? ii.Id.Value : -1);
                    if (i == null)
                    {
                        i = new InvoiceItem();
                    }

                    i.AssetId = ii.AssetId;
                    i.ClassId = ii.ClassId ?? 0;
                    i.CustomName = ii.Name;
                    i.CustomPrice = ii.Price;
                    i.InvoiceId = ii.InvoiceId ?? 0;
                    i.RateItem = ii.RateItemId != null ? await Extric.Towbook.RateItem.GetByIdAsync(ii.RateItemId.Value) : null;
                    i.Quantity = ii.Quantity ?? 0.0M;
                    i.Taxable = ii.Taxable ?? false;
                    i.RelatedInvoiceItemId = ii.RelatedId;
                    i.TaxRates = taxrates.ToCollection();
                    i.Notes = ii.Notes;

                    DispatchEntry.InvoiceItems.Add(i);
                }

                if (Quote.Call.InvoiceTaxExempt != null)
                {
                    if (DispatchEntry.Invoice.IsTaxExempt != Quote.Call.InvoiceTaxExempt)
                    {
                        DispatchEntry.Invoice.IsTaxExempt = Quote.Call.InvoiceTaxExempt.Value;
                    }
                }
            }
            else if (Request.Query.ContainsKey("callNumber"))
            {
                if (int.TryParse(Request.Query["callNumber"], out int callNumber))
                {
                    DispatchEntry = await Entry.GetByCallNumberAsync(callNumber, WebGlobal.CurrentUser.Company);
                }
            }
            else if (Request.Query.ContainsKey("id"))
            {
                if (int.TryParse(Request.Query["id"], out int id))
                {
                    DispatchEntry = Entry.GetById(id);
                }
            }

            if (DispatchEntry == null)
            {
                return new ContentResult
                {
                    StatusCode = 404,
                    ContentType = "text/html",
                    Content = "<h1>There was a problem</h1><p>The call cannot be found.</p>"
                };
            }

            // Authentication check
            if (WebGlobal.CurrentUser == null)
            {
                bool noAuth = true;

                if (Request.Query.ContainsKey("key"))
                {
                    if (Request.Query["key"] == Extric.Towbook.Core.MD5(DispatchEntry.Id.ToString() + ":27ed2fb84d816"))
                        noAuth = false;
                }

                if (Request.Query.ContainsKey("auth") && Quote != null)
                {
                    if (Request.Query["auth"] == Extric.Towbook.Core.MD5(Quote.QuoteNumber.ToString() + ":27ed2fb84d816"))
                        noAuth = false;
                }

                if (noAuth)
                {
                    var authorized = WebGlobal.GetAuthorizedServers().Union(new string[] { "***********" }).ToArray();

                    // In ASP.NET Core, getting IP is different
                    var checkAgainst = HttpContext.Connection.RemoteIpAddress.ToString();
                    
                    if (!authorized.Contains(checkAgainst) &&
                        !checkAgainst.StartsWith("10.120.127") &&
                        !checkAgainst.StartsWith("169.55.3."))
                    {
                        // Replace direct Response.WriteAsync with ContentResult
                        return new ContentResult
                        {
                            StatusCode = 403,
                            ContentType = "text/html",
                            Content = "You don't have access to this invoice. Did you forget to login? " + checkAgainst
                        };
                    }
                }
            }

            Company = DispatchEntry.Company;

            if (Quote == null)
                DispatchEntry.Invoice.IgnoreDiscount = HideDiscounts;

            InvoiceOptions io = await InvoiceOptions.GetByCompanyIdAsync(Company.Id);

            LicenseValues = Extric.Towbook.Licenses.CompanyLicenseKeyValue.GetByCompanyId(Company.Id).ToList();
            Keys = Extric.Towbook.Licenses.CompanyLicenseKey.GetAll().ToList();

            Photos = Extric.Towbook.Dispatch.Photo.GetByDispatchEntryId(DispatchEntry.Id);

            if (Quote != null)
                io.HideCallNumber = true;

            if (DispatchEntry != null &&
                DispatchEntry.Assets != null &&
                (DispatchEntry.Assets.Any()) &&
                DispatchEntry.Assets[0].Year == 0 &&
                DispatchEntry.Assets[0].ColorId == 0 &&
                DispatchEntry.Assets[0].Odometer == 0 &&
                String.IsNullOrWhiteSpace(DispatchEntry.Assets[0].Model) &&
                String.IsNullOrWhiteSpace(DispatchEntry.Assets[0].Make) &&
                String.IsNullOrWhiteSpace(DispatchEntry.Assets[0].Vin) &&
                String.IsNullOrWhiteSpace(DispatchEntry.Assets[0].LicenseNumber) &&
                String.IsNullOrWhiteSpace(DispatchEntry.Assets[0].UnitNumber))
            {
                ServiceReceiptWithNoVehicleDetails = true;

                var firstAsset = DispatchEntry.Assets.FirstOrDefault();
                if (firstAsset != null && firstAsset.BodyTypeId > 0)
                {
                    switch (firstAsset.BodyTypeId)
                    {
                        case 8:
                        case 9:
                            if (!string.IsNullOrWhiteSpace(DispatchEntry.Assets[0].UnitNumber) ||
                                !string.IsNullOrWhiteSpace(DispatchEntry.Assets[0].Notes))
                                ServiceReceiptWithNoVehicleDetails = false;
                            break;

                        case 10:
                            if (!string.IsNullOrWhiteSpace(DispatchEntry.Assets[0].Notes))
                                ServiceReceiptWithNoVehicleDetails = false;
                            break;
                    }
                }
            }

            if (DispatchEntry.Company.HasAccessToBaseFeature("tipping"))
                EntryTips = (await DispatchEntryPaymentTip.GetByDispatchEntryIdAsync(DispatchEntry.Id)).ToList();

            if (new int[] { 4541, 4373 }.Contains(DispatchEntry.CompanyId))
            {
                if (DispatchEntry.Account != null)
                    DispatchEntry.CompanyId = DispatchEntry.Account.CompanyId;

                Company = Extric.Towbook.Company.Company.GetById(DispatchEntry.CompanyId);
            }


            if (new int[] { 6559, 4189, 4185, 4194 }.Contains(this.Company.Id))
            {
                if (WebGlobal.CurrentUser.Type == Extric.Towbook.User.TypeEnum.Driver)
                { 
                    // ASP.NET Core equivalent of Response.Clear() and Response.End()
                    return new ContentResult
                    {
                        StatusCode = 403,
                        ContentType = "text/html",
                        Content = "<h1>Access Denied</h1><p>Drivers are not allowed to access this invoice.</p>"
                    };
                }
            }


            if (new int[] { 1152, 1262, 1376, 1541, 1542, 1539, 3107, 1540, 1235, 1655, 1525, 4603 }.Contains(DispatchEntry.CompanyId) ||
                io.WindowEnvelopeFormat)
                ShowFields.Add("WindowEnvelope");

            if (new int[] { 1376, 1541, 1542, 1539, 2, 1540, 1525 }.Contains(DispatchEntry.CompanyId))
                ShowFields.Add("ChargeAccount");

            if (!DispatchEntry.Assets.Any(o => new int[] {1, 2, 3, 4, 5, 6, 7, 8 }.Contains(o.BodyTypeId)))
            {
                HideFields.Add("AssetModel");
                HideFields.Add("AssetMake");
            }

            if (!DispatchEntry.Assets.Any(o => new int[] { 1, 2, 3, 4, 5, 6, 7 }.Contains(o.BodyTypeId)))
            {
                HideFields.Add("AssetOdometer");
                HideFields.Add("AssetColor");
                HideFields.Add("AssetYear");
                HideFields.Add("AssetVIN");
                HideFields.Add("AssetPlate");
            }

            if (DispatchEntry.Account != null)
                ShowDrivable = Company.State.ToUpperInvariant() == "MS" && (DispatchEntry.Account.Type == Extric.Towbook.Accounts.AccountType.PoliceDepartment || DispatchEntry.Account.Type == Extric.Towbook.Accounts.AccountType.PrivateProperty);

            if (!DispatchEntry.Assets.Any(o => !string.IsNullOrWhiteSpace(o.UnitNumber)))
            {
                HideFields.Add("UnitNumber");
            }

            if (!DispatchEntry.Assets.Any(o => new int[] { 9, 10 }.Contains(o.BodyTypeId)))
            {
                HideFields.Add("Description");
            }

            if (!DispatchEntry.Assets.Any(o => new int[] { 8, 9, 10 }.Contains(o.BodyTypeId)))
            {
                HideFields.Add("AssetNotes");
            }

            if (!DispatchEntry.Assets.Any(o => new int[] { 8, 9 }.Contains(o.BodyTypeId)))
            {
                HideFields.Add("Serial");
            }

            if (!DispatchEntry.Assets.Any(o => new int[] { 9 }.Contains(o.BodyTypeId)))
            {
                HideFields.Add("SealNumber");
            }

            if (io.IncludeDriverSignature)
                ShowFields.Add("DriverSignature");

            if (io.HidePrintDate)
                HideFields.Add("PrintDate");

            if (io.HideCreationTime)
                HideFields.Add("CreateDate");

            if (Company.Id ==56422)
            {
                HideFields.Add("Serial");
            }

            if (DispatchEntry.Account.Type == Extric.Towbook.Accounts.AccountType.Transport)
            {
                TowFromNameLabel = "Pickup";
                TowToNameLabel = "Destination";
            }

            if (new int[] { 12301, 14164, 16890, 14150 }.Contains(DispatchEntry.CompanyId))
            {
                TowFromNameLabel = "Origin";
                TowToNameLabel = "Destination";
            }

            if (new int[] { 38443, 40828, 40829, 40830 }.Contains(DispatchEntry.CompanyId))
            {
                TowFromNameLabel = "Pickup";
                TowToNameLabel = "Drop Off";
            }

            if (new int[] { 47439 }.Contains(DispatchEntry.CompanyId))
            {
                TowFromNameLabel = "Transport From";
                TowToNameLabel = "Transport To";
            }

            if (ServiceReceiptWithNoVehicleDetails)
            {
                TowFromNameLabel = "Origin";
                TowToNameLabel = "Destination";
            }

            if (DispatchEntry.Reason != null && DispatchEntry.Reason.Name.ToLowerInvariant().Contains("recovery"))
            {
                TowFromNameLabel = "Incident Location";
                TowToNameLabel = "Destination";
            }

            if (new int[] { 14150, 14164, 12301, 16890 }.Contains(DispatchEntry.CompanyId))
            {
                HideFields.Add("Account");
            }

            if (new int[] { 3260 }.Contains(DispatchEntry.CompanyId))
            {
                ShowFields.Add("Driver");
            }

            if (new int[] { 11401 }.Contains(DispatchEntry.CompanyId))
            {
                //lblPhone.Visible = false; // No direct equivalent without HTML
            }

            if (new int[] { 4189, 4185, 4194 }.Contains(DispatchEntry.CompanyId))
            {
                HideFields.Add("Photos");
            }

            if (new int[] { 12802 }.Contains(DispatchEntry.CompanyId))
            {
                HideFields.Add("Photos");
                HideFields.Add("Account");
            }

            if (Request.Query["hidePhotos"] == "1")
                HideFields.Add("Photos");

            if (new int[] { 3205 }.Contains(DispatchEntry.CompanyId))
            {
                ShowFields.Add("AccountAddress");
                ShowFields.Add("Driver");
                ShowFields.Add("Truck");
            }

            if (new int[] { 5118 }.Contains(DispatchEntry.CompanyId))
            {
                HideFields.Add("AssetOdometer");
                HideFields.Add("AssetVIN");
                HideFields.Add("AssetPlate");
            }


            if (new int[] { 23614 }.Contains(DispatchEntry.CompanyId))
            {
                HideFields.Add("AssetOdometer");
                HideFields.Add("AssetVIN");
                HideFields.Add("AssetPlate");
            }

            if (new int[] { 29253 }.Contains(DispatchEntry.CompanyId))
            {
                HideFields.Add("AssetOdometer");
                HideFields.Add("AssetPlate");
                HideFields.Add("AssetColor");
                HideFields.Add("AssetYear");
            }

            if (new int[] { 4185, 4189, 4194 }.Contains(DispatchEntry.CompanyId))
            {
                HideFields.Add("MembershipNumber");
                HideFields.Add("Coverage");
            }

            if (WebGlobal.CurrentUser != null)
            {
                if (new int[] { 5494 }.Contains(WebGlobal.CurrentUser.PrimaryCompanyId))
                {
                    HideFields.Add("CallNumber");
                    io.HideCallNumber = true;
                }
            }

            if (new int[] { 2091 }.Contains(DispatchEntry.CompanyId))


                if (new int[] { 3205 }.Contains(DispatchEntry.CompanyId))
                    HideFields.Add("Reason");

            if (new int[] { 3205, 1729 }.Contains(DispatchEntry.CompanyId))
                HideFields.Add("Notes");

            if (new int[] { 3205 }.Contains(DispatchEntry.CompanyId))
                HideFields.Add("AccountContact");

            if (new int[] { 1490 }.Contains(DispatchEntry.CompanyId))
                HideFields.Add("TowTime");

            if (new int[] { 2718, 1815, 6341 }.Contains(DispatchEntry.CompanyId))
            {
            }

            if (new int[] { 3466 }.Contains(DispatchEntry.CompanyId))
            {
                HideFields.Add("EnrouteTime");
                HideFields.Add("DispatchTime");
            }

            if (new int[] { 5325 }.Contains(DispatchEntry.CompanyId))

                if (new int[] { 477 }.Contains(DispatchEntry.CompanyId))
                    HideFields.Add("Truck");

            if (new int[] { 4541, 4373 }.Contains(DispatchEntry.CompanyId))
            {
                HideFields.Add("EnrouteTime");
                HideFields.Add("DispatchTime");
            }

            if (new int[] { 5789 }.Contains(DispatchEntry.CompanyId))
            {
                HideFields.Add("InvoiceNumber");
            }

            if (new int[] { 4379 }.Contains(DispatchEntry.CompanyId))
            {
                HideFields.Add("EnrouteTime");
                HideFields.Add("DispatchTime");
            }

            if (new int[] { 1759 }.Contains(DispatchEntry.CompanyId))
            {
                ShowFields.Add("EnrouteTime");
                ShowFields.Add("DispatchTime");
            }

            if (new int[] { 4649 }.Contains(DispatchEntry.CompanyId))
            {
                ShowFields.Add("EnrouteTime");
                ShowFields.Add("DispatchTime");
            }

            if (new int[] { 6341 }.Contains(DispatchEntry.CompanyId))
            {
                ShowFields.Add("CompletionTime");
            }


            if (new int[] { 4379 }.Contains(DispatchEntry.CompanyId))
            {
            }

            if (new int[] { 2037, 3673 }.Contains(DispatchEntry.CompanyId))
            {
                ShowFields.Add("Driver");
            }

            if (DispatchEntry.CompanyId == 2953 && DispatchEntry.AccountId == 43639)
            {
                ShowFields.Add("CreateDate");
                io.ShowCompletionDateTime = true;
            }

            if (new int[] { 743747, 746132, 741805, 742432 }.Contains(DispatchEntry.AccountId))
            {
                ShowFields.Add("EnrouteTime");
                ShowFields.Add("ArrivalTime");
                ShowFields.Add("TowTime");
                ShowFields.Add("DestinationArrivalTime");
                ShowFields.Add("CompletionTime");
            }

            if (new int[] { 742259 }.Contains(DispatchEntry.AccountId))
            {
                io.ShowDispatchedDateTime = true;
                io.ShowEnrouteDateTime = true;
                io.ShowOnSceneDateTime = true;
                io.ShowTowingDateTime = true;
                io.ShowDestinationArrivalDateTime = true;
                io.ShowCompletionDateTime = true;
            }

            if (DispatchEntry != null && DispatchEntry.Account != null)
            {
                var accountToBill = DispatchEntry.Invoice.AccountId > 0 ? Extric.Towbook.Accounts.Account.GetById(DispatchEntry.Invoice.AccountId.Value) : DispatchEntry.Account;

                //lblAccountName.Text += // No direct equivalent without HTML
                AccountNameText = (!string.IsNullOrWhiteSpace(accountToBill.FullName) && !io.HideAccountContact ? HtmlEncode(accountToBill.FullName) + "<br />" : "") +
                    HtmlEncode(accountToBill.Company);

                var billingAddress = (await AddressBookEntry.GetByAccountIdAsync(accountToBill.Id)).Where(o => o.Name == "Billing Address").FirstOrDefault();
                if (billingAddress != null)
                AccountInfoText = HtmlEncode(billingAddress.Address) + "<br />" +
                    HtmlEncode(billingAddress.City + " " +
                    billingAddress.State + " " +
                    billingAddress.Zip);
            }

            var photo = Photo.GetByDispatchEntryId(DispatchEntry.Id).Where(o => o.Description == "__INVOICE_SIGNATURE").FirstOrDefault();

            if (photo != null)
            {
                string path = FileUtility.GetPresignedUrlForDownloadFromClient(photo.Location.Replace("%1", Company.Id.ToString()), photo.ContentType);

                if (path != null)
                {
                    SignatureUrl = path;
                    SignatureDate = photo.CreateDate;
                }
            }

            if (SignatureUrl == null)
            {
                var sig = Signature.GetByDispatchEntryId(DispatchEntry.Id).OrderBy(o => o.DispatchEntrySignatureId).LastOrDefault();

                if (sig != null)
                {
                    SignatureUrl = FileUtility.GetPresignedUrlForDownloadFromClient(
                        sig.Location.Replace("%1", Company.Id.ToString()), sig.ContentType);

                    SignatureDate = sig.CreateDate;
                }
            }

            if (SignatureDate != null)
                SignatureDate = OffsetDateTime(SignatureDate.Value);

            if (_environment.EnvironmentName == "Production" && Request.Query["statementId"] == StringValues.Empty && Quote == null)
            {
                ShowPaymentLink = false;
                try
                {
                    var authorization = API.Integration.Square.SquareUtils.GetValidAuthorization(Company.Id);

                    if (authorization != null)
                    {
                        ShowPaymentLink = true;

                        var excludeLink = AccountKeyValue.GetByAccount(Company.Id, DispatchEntry.Account.Id, Provider.Towbook.ProviderId, "Square_ExcludeLinkOnPrintedDispatchInvoices").FirstOrDefault();
                        var excludeLinkDefault = CompanyKeyValue.GetByCompanyId(DispatchEntry.Account.CompanyId, Provider.Towbook.ProviderId, "Square_ExcludeLinkOnPrintedDispatchInvoices").FirstOrDefault();

                        if (excludeLink != null && excludeLink.Value == "1")
                            ShowPaymentLink = false;
                        else if (excludeLinkDefault != null && excludeLinkDefault.Value == "1")
                            ShowPaymentLink = false;
                    }
                }
                catch (Exception ex)
                {
                    ShowPaymentLink = false;
                }
            }

            if (ShowPaymentLink)
            {
                var uId = WebGlobal.CurrentUser != null ? WebGlobal.CurrentUser.Id : DispatchEntry.OwnerUserId;

                if (uId > 0)
                {
                    string host = "https://twbk.co";
                    string path = "/cu/" + Extric.Towbook.Dispatch.EntryWebLink.GenerateSecretHash(DispatchEntry.Id, uId);
                    var shortLink = Extric.Towbook.ShortLinks.ShortLink.GetByOriginLink(host + path);

                    if (shortLink == null)
                    {
                        shortLink = new Extric.Towbook.ShortLinks.ShortLink()
                        {
                            OriginLink = host + path,
                            ShortOrigin = host,
                            CompanyId = DispatchEntry.CompanyId,
                            ObjectType = Extric.Towbook.ShortLinks.ShortLinkObjectType.Call,
                            ObjectId = DispatchEntry.Id
                        };

                        shortLink.Save(await Extric.Towbook.User.GetByIdAsync(uId));
                    }

                    ShortLink = shortLink.ShortUrl;
                }
            }

            if (Request.Query["pdf"] == "1")
            {
                var sw = new StringWriter();
                var extra = "";

                if (WebGlobal.CurrentUser != null && WebGlobal.CurrentUser.Type == Extric.Towbook.User.TypeEnum.Manager && Request.Query["download"] == "2")
                    extra = "&showPrices=1";

                if (Request.Query["key"] == StringValues.Empty)
                {
                    if (WebGlobal.CurrentUser != null)
                    {
                        var key = Extric.Towbook.Core.MD5(DispatchEntry.Id.ToString() + ":27ed2fb84d816");
                        extra += "&key=" + key;
                    }
                }

                if (Request.Query["q"] == StringValues.Empty)
                {
                    // For this code-focused conversion, we'll add a placeholder as we'd need to inject IViewRenderService
                    sw.WriteLine("<!-- PDF Content Generated via ViewRenderer in ASP.NET Core -->");
                }
                else
                {
                    // Use HttpClient for making external requests in ASP.NET Core
                    using (var httpClient = new HttpClient())
                    {
                        string url =  (Request.Path + BuildQuery() + "&local=1" + extra).Trim('?');
                        var response = await httpClient.GetStringAsync(url);
                        sw.Write(response);
                    }
                }

                if (Company.Id == 80)
                {
                    //pageSize = new PageSize(3.3F, 15F); // Class from PdfClientBase, not directly convertible without the library
                    //outputArea = new OutputArea(0.3f, 0.3f, 2.7f, 14.3f); // Class from PdfClientBase, not directly convertible without the library
                }
                //if (company.Id != 7590) // HTML footer, not relevant for pure code conversion
                //    footerHtml = "<div style=\"color: #333333; font-family: verdana; font-size: 9px; font-weight: bold\"><br /><br />Created with Towbook Management Software | www.towbook.com</div>";

                if (IsSafeclear)
                {
                    //pdfSafeclear = File.ReadAllBytes(@"C:\inetpub\websites\towbook.com\app\01\Dispatch2\TowAndGo.pdf"); // File path, keep logic if needed for PDF generation later
                }

                //HttpResponse response = HttpContext.Current.Response; // No direct equivalent without HTML context
                //response.Clear(); // No direct equivalent without HTML context
                //response.ClearHeaders(); // No direct equivalent without HTML context

                if (Request.Query["jpg"] == "1")
                {
                    //response.ContentType = "image/jpeg"; // No direct equivalent without HTML context
                    //var stream = await PdfClientBase.GeneratePdf(sw.ToString(), null, outputArea, pageSize, pdfSafeclear, FileType.JPEG, footerHtml); // PdfClientBase class, not directly convertible
                    //await stream.CopyToAsync(response.OutputStream); // Output stream, not direct equivalent
                }
                else
                {
                    //var stream = await PdfClientBase.GeneratePdf(sw.ToString(), null, outputArea, pageSize, pdfSafeclear, FileType.PDF, footerHtml); // PdfClientBase class, not directly convertible
                    string fileIdentifier = DispatchEntry.CallNumber.ToString();
                    if (!string.IsNullOrWhiteSpace(DispatchEntry.InvoiceNumber))
                        fileIdentifier = DispatchEntry.InvoiceNumber;
                    if (!string.IsNullOrWhiteSpace(DispatchEntry.PurchaseOrderNumber))
                        fileIdentifier = DispatchEntry.PurchaseOrderNumber;

                    fileIdentifier = fileIdentifier.Replace(",", " ").Replace("&", "");

                    //response.ContentType = "application/pdf"; // No direct equivalent without HTML context

                    if (Request.Query["download"] == "1" || Request.Query["download"] == "2")
                    {
                        //response.AppendHeader("content-disposition", "attachment; filename=" + fileIdentifier +  // No direct equivalent without HTML context
                        //    "-" + _de.Account.Company.Replace(" ", "").Replace(",", "").Replace("&", "").Trim('_') + "_" +
                        //    (_de.Reason != null ? _de.Reason.Name : "") + ".pdf");
                    }

                    //await stream.CopyToAsync(response.OutputStream); // Output stream, not direct equivalent
                }
                //response.End(); // No direct equivalent without HTML context
            }


            if (DispatchEntry != null && DispatchEntry.Attributes.ContainsKey(12))
            {
                var c2 = Extric.Towbook.Company.Company.GetById(Convert.ToInt32(DispatchEntry.Attributes[12].Value)); ;
                if (c2 != null)
                    Company = c2;
            }
            var sc = SharedCompany.GetByCompanyId(Company.Id).FirstOrDefault();

            if (sc != null && sc.CompanyId == 26130)
            {
                var tcx = Extric.Towbook.Company.Company.GetById(sc.CompanyId);
                if (tcx != null && tcx.Id != Company.Id)
                {
                    Company = tcx;

                    Company.Name = "Jack Rabbit Services, LLC";
                    Company.Address = "9016 Taylorsville Rd, Ste 229";
                    Company.City = "Louisville";
                    Company.State = "KY";
                    Company.Zip = "40299";
                }
            }

            //lblCompanyName.Text = Server.HtmlEncode(company.Name); // No direct equivalent without HTML
            CompanyNameText = HtmlEncode(Company.Name);
            if (Company.Id == 2116)
                CompanyNameText = HtmlEncode("A Division of K.T.L. Enterprises Inc.");
            if (Company.Id == 6013)
                CompanyNameText = "";
            if (Company.Id == 20005 && DispatchEntry.AccountId == 1219955)
                CompanyNameText = "All Over Quik Pik Towing";

             //lblAddress.Text = Server.HtmlEncode(company.Address + ", " + company.City + " " + company.State + " " + company.Zip); // No direct equivalent without HTML
             CompanyAddressText = HtmlEncode(Company.Address + ", " + Company.City + " " + Company.State + " " + Company.Zip);

            if (Company.Id == 7653)
                CompanyAddressText = "<strong>Yelm Locksmith<br /><b>Trusted Since 1970</b><br>" + HtmlEncode(Company.Address + ", " + Company.City + " " + Company.State + " " + Company.Zip);

            //lblPhone.Text = "Phone: " + _(FormatPhone(company.Phone)); // No direct equivalent without HTML
            CompanyPhoneText = "Phone: " + HtmlEncode(FormatPhone(Company.Phone));

            //if (company.Fax != null) // No direct equivalent without HTML
            //    lblFax.Text = "| Fax: " + _(FormatPhone(company.Fax));
            CompanyFaxText = Company.Fax != null ? "| Fax: " + HtmlEncode(FormatPhone(Company.Fax)) : "";

            if (Company.Country == Extric.Towbook.Company.Company.CompanyCountry.Canada && Company.State == "ON")
                io.ShowCompanyEmail = true;

            //if (io.ShowCompanyEmail && Core.IsEmailValid(company.Email)) // No direct equivalent without HTML
            //    lblEmail.Text = "| Email: " + _(company.Email);
            CompanyEmailText = (io.ShowCompanyEmail && 
                Core.IsEmailValid(Company.Email) ? "| Email: " + HtmlEncode(Company.Email) : "");


            //if (!lblPhone.Visible) // No direct equivalent without HTML
            //    lblFax.Text = _(lblFax.Text.Trim('|'));
            CompanyFaxText = (!string.IsNullOrEmpty(CompanyPhoneText)) ? CompanyFaxText.Trim('|') : CompanyFaxText;


            var companyBillingAddress = AddressBookEntry.GetByCompany(Company).FirstOrDefault(o => o.Name == "Billing Address");

            if (companyBillingAddress != null)
            {
                CompanyAddressText = _(companyBillingAddress.ToString());
            }

            if (Company.Id == 80 || Request.Query["singlecol"] == "1")
                SingleColumn = true;

            if (Company.Id == 819)
                CompanyFaxText += " http://www.tricountytow.com";
                //lblFax.Text += " http://www.tricountytow.com"; // No direct equivalent without HTML


            if (Company.Id == 10419)
            {
                CompanyAddressText = "<strong>Physical Address: </strong>" + HtmlEncode(CompanyAddressText) + "<br /><strong>Billing Address:</strong> 1803 Godman St, Killeen TX 76543";
                //lblAddress.Text = "<strong>Physical Address: </strong>" + Server.HtmlEncode(lblAddress.Text) + "<br /><strong>Billing Address:</strong> 1803 Godman St, Killeen TX 76543"; // No direct equivalent without HTML
            }


            if (DispatchEntry.CompanyId == 7917)
            {
                CompanyAddressText += "<br />1722 Chandler Road, Louisville, TN, 37777";
                //lblAddress.Text += "<br />1722 Chandler Road, Louisville, TN, 37777"; // No direct equivalent without HTML
            }

            if (WebGlobal.CurrentUser != null && DispatchEntry.CompanyId != WebGlobal.CurrentUser.CompanyId && Quote == null)
            {
                if (!WebGlobal.CurrentUser.HasAccessToCompany(DispatchEntry.CompanyId))
                {
                    bool hasAccess = false;
                    if (DispatchEntry.AccountId > 0 && DispatchEntry.Account.Companies.Contains(WebGlobal.CurrentUser.CompanyId))
                    {
                        ShowFields.Remove("WindowEnvelope");
                        hasAccess = true;
                    }

                    if (!hasAccess)
                    {
                        // Replace direct Response.WriteAsync with ContentResult
                        return new ContentResult
                        {
                            StatusCode = 403,
                            ContentType = "text/html",
                            Content = "You don't have access to this invoice. Are you logged in under the wrong company?"
                        };
                    }
                }
            }

            // Around line 864 where invoice items are processed
            var invoiceItems = DispatchEntry.Invoice.InvoiceItems
                .Where(o => o.ClassId != 4 && o.Total != 0 && !o.Name.Contains("FreeQuant"))
                .OrderByDescending(g => g.RateItem != null && g.RateItem.Predefined != null &&
                    (g.RateItem.Predefined.Id == Extric.Towbook.PredefinedRateItem.BUILTIN_MILEAGE_DEADHEAD ||
                     g.RateItem.Predefined.Id == Extric.Towbook.PredefinedRateItem.BUILTIN_MILEAGE_LOADED ||
                     g.RateItem.Predefined.Id == Extric.Towbook.PredefinedRateItem.BUILTIN_MILEAGE_UNLOADED))
                .ThenBy(f => f.RateItem != null && f.RateItem.Predefined != null ? f.RateItem.Predefined.Id : f.Id).ToList();
            
            // Process invoice items and populate the view model collection
            InvoiceItems.Clear();
            foreach (var item in invoiceItems)
            {
                var viewModel = await ProcessInvoiceItemAsync(item);
                if (viewModel.IsVisible)
                {
                    InvoiceItems.Add(viewModel);
                }
            }
            
            // Calculate subtotal from the processed invoice items
            SubtotalValue = 0;
            foreach (var item in InvoiceItems)
            {
                if (!string.IsNullOrEmpty(item.LineTotal) && item.LineTotal != "-")
                {
                    string cleanTotal = item.LineTotal.Replace("$", "").Replace(",", "");
                    if (decimal.TryParse(cleanTotal, out decimal lineTotal))
                    {
                        SubtotalValue += lineTotal;
                    }
                }
            }

            // Special handling for hidden charges with overages
            if (HideCharges)
            {
                if (Overages)
                {
                    var overageItems = DispatchEntry.InvoiceItems.Where(w => w.Name.ToLowerInvariant().Contains("overage"));
                    if (overageItems.Any())
                    {
                        decimal overageSubtotal = overageItems.Sum(x => x.Total);
                        decimal overageTaxes = 0.0M;

                        foreach (var ii in overageItems.Where(w => w.Taxable))
                        {
                            if (ii.TaxRates != null && ii.TaxRates.Count > 0)
                            {
                                var tr = Extric.Towbook.TaxRate.GetById(ii.TaxRates[0].TaxRateId);
                                if (tr != null)
                                    overageTaxes += Math.Round(ii.Total * (tr.Rate / 100), 2, MidpointRounding.AwayFromZero);
                            }
                        }

                        SubtotalValue = overageSubtotal;
                        TaxesValue = overageTaxes;
                        GrandTotalValue = overageSubtotal + overageTaxes;
                        
                        // We've calculated totals specifically for overages, so return now
                        return Page();
                    }
                }
            }

            // Calculate tax (for non-overage cases)
            TaxName = "Tax";
            if (Company.State == "Canada" || Company.Country == Extric.Towbook.Company.Company.CompanyCountry.Canada)
                TaxName = "HST";

            decimal taxRate = 0;
            var taxRateAttribute = DispatchEntry.GetAttribute(AttributeValue.BUILTIN_TAXRATE_OVERRIDE);
            if (taxRateAttribute != null)
            {
                var trRateId = 0;
                if (int.TryParse(taxRateAttribute, out trRateId))
                {
                    var tr = Extric.Towbook.TaxRate.GetById(trRateId);
                    if (tr != null)
                    {
                        taxRate = tr.Rate;
                    }
                }
            }
            
            TaxesValue = Math.Round(TaxableAmount * (taxRate / 100), 2);

            // Calculate tip total and grand total
            TipTotal = EntryTips != null ? EntryTips.Sum(x => x.Amount) : 0.0M;
            GrandTotalValue = SubtotalValue + TaxesValue + TipTotal;
            
            // Handle payment link and QR code
            HidePaymentLink = !ShowPaymentLink;
            
            if (ShowPaymentLink && !string.IsNullOrWhiteSpace(ShortLink))
            {
                    PaymentLink = ShortLink;

            }

                // Original commented out code
                //rpInvoiceItems.DataSource = invoiceItems; // No direct equivalent without HTML Repeater
                //rpInvoiceItems.DataBind(); // No direct equivalent without HTML Repeater

                #region billing notes
                if (DispatchEntry.Attributes != null &&
                DispatchEntry.Attributes.ContainsKey(AttributeValue.BUILTIN_INCLUDE_BILLING_NOTES_ON_RECEIPT) &&
                DispatchEntry.Account != null &&
                DispatchEntry.Account.Type != Extric.Towbook.Accounts.AccountType.MotorClub &&
                DispatchEntry.Attributes.ContainsKey(AttributeValue.BUILTIN_BILLING_NOTES))
            {
                if(DispatchEntry.Attributes[AttributeValue.BUILTIN_INCLUDE_BILLING_NOTES_ON_RECEIPT].Value == "1")
                {
                    BillingNotesEncoded = _(DispatchEntry.Attributes[AttributeValue.BUILTIN_BILLING_NOTES].Value).Replace("\n", "<br/>");
                }
            }
            #endregion

            // Example of using the collected data in Razor syntax if HTML output was needed:
            // <h1>@companyNameText</h1>
            // <p>@companyAddressText</p>
            // <p>@companyPhoneText @companyFaxText @companyEmailText</p>
            // ... and so on for other collected strings and data.

            return Page();
        }

        public static string _(string html)
        {
            if (string.IsNullOrEmpty(html)) return string.Empty;
            // This may need to be updated to use _htmlEncoder.Encode in derived methods
            return html;
        }

        public decimal GetCategoryTotal(Entry x, int categoryId)
        {
            Dictionary<int?, decimal> categoryTotals = new Dictionary<int?, decimal>();

            foreach (InvoiceItem i in x.Invoice.InvoiceItems)
            {
                int? cId = null;
                if (i.CategoryId != null)
                {
                    cId = i.CategoryId;
                }

                if (cId == null) cId = 0;

                if (!categoryTotals.ContainsKey(cId))
                    categoryTotals.Add(cId, Convert.ToDecimal(i.Total));
                else
                    categoryTotals[cId] += Convert.ToDecimal(i.Total);
            }

            try
            {
                return categoryTotals[categoryId];
            }
            catch
            {
                return 0;
            }
        }


        public string getOdometer(int attributeId)
        {
            string truckOdometer = "";

            if (DispatchEntry == null) return "";

            if (DispatchEntry.Attributes.ContainsKey(attributeId))
            {
                truckOdometer = " / Truck Odometer: " + DispatchEntry.Attributes[attributeId].Value;
            }

            return truckOdometer;
        }

        public string getSubContractor()
        {
            string subContractor = "";

            if (DispatchEntry != null && DispatchEntry.Attributes.ContainsKey(Extric.Towbook.Dispatch.AttributeValue.BUILTIN_SUBCONTRACTOR_ID))
            {
               var subid =   Convert.ToInt32(DispatchEntry.Attributes[Extric.Towbook.Dispatch.AttributeValue.BUILTIN_SUBCONTRACTOR_ID].Value);

                if (subid > 0)
                {
                    var acc = Extric.Towbook.Accounts.Account.GetById(subid);

                    if (acc != null)
                        subContractor = acc.Company;
                }
            }

            return subContractor;
        }



        private string BuildQuery()
        {
            StringBuilder sb = new StringBuilder();
            sb.Append("?");
            
            // ASP.NET Core uses Request.Query instead of Request.QueryString
            foreach (string key in Request.Query.Keys)
            {
                if (key.ToLowerInvariant() == "pdf")
                    continue;

                sb.Append(key.ToLowerInvariant());
                sb.Append("=");
                sb.Append(Request.Query[key]);
                sb.Append("&");
            }

            return sb.ToString().Trim('&');
        }

        public Extric.Towbook.Accounts.Account BillToAccount
        {
            get
            {
                if (DispatchEntry == null)
                    return null;

                if (DispatchEntry.Invoice == null)
                    return null;

                if (DispatchEntry.Invoice.AccountId > 0)
                    return Extric.Towbook.Accounts.Account.GetById(DispatchEntry.Invoice.AccountId.Value);

                return null;
            }
        }

        public bool IsMultiAddressTow
        {
            get
            {
                return Extric.Towbook.Dispatch.CallModels.PublicCallModelExtensions.IsMultiAddressCall(DispatchEntry);
            }
        }

        public bool HideCharges
        {
            get
            {
                var defaultValue = (DispatchEntry.Account != null && DispatchEntry.Account.Type == Extric.Towbook.Accounts.AccountType.MotorClub ? "1" : "0");

                if (Quote != null)
                    return false;

                if (IsSafeclear) return true;

                if (WebGlobal.CurrentUser != null)
                {
                    if (WebGlobal.CurrentUser.Type == Extric.Towbook.User.TypeEnum.Driver ||
                        WebGlobal.CurrentUser.Type == Extric.Towbook.User.TypeEnum.Dispatcher)
                    {
                        var preventDriversFromViewingCharges = Extric.Towbook.Integration.CompanyKeyValue.GetByCompanyId(this.Company.Id, Extric.Towbook.Integration.Provider.Towbook.ProviderId, "PreventDriversFromViewingInvoiceItems").FirstOrDefault();
                        var preventDispatchersFromViewingCharges = Extric.Towbook.Integration.CompanyKeyValue.GetByCompanyId(this.Company.Id, Extric.Towbook.Integration.Provider.Towbook.ProviderId, "PreventDispatchersFromViewingInvoiceItems").FirstOrDefault();

                        if (WebGlobal.CurrentUser.Type == Extric.Towbook.User.TypeEnum.Driver)
                        {
                            if (preventDriversFromViewingCharges != null && (preventDriversFromViewingCharges.Value == "3" ||
                                preventDriversFromViewingCharges.Value == "1"))
                                return true;
                        }

                        if (WebGlobal.CurrentUser.Type == Extric.Towbook.User.TypeEnum.Dispatcher)
                        {
                            if (preventDispatchersFromViewingCharges != null && (preventDispatchersFromViewingCharges.Value == "3" ||
                                preventDispatchersFromViewingCharges.Value == "1"))
                                return true;
                        }
                    }

                    if (CompanyKeyValue.GetFirstValueOrNull(this.Company.Id, Provider.Towbook.ProviderId, "HideChargesFromAccountUsers") == "1"
                        && WebGlobal.CurrentUser.Type == Extric.Towbook.User.TypeEnum.AccountUser)
                        return true;
                }

	            if ( ( WebGlobal.CurrentUser == null || (WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.Manager &&
	             WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.Accountant)) && ( DispatchEntry.CompanyId == 26556 || DispatchEntry.CompanyId == 28172 || DispatchEntry.CompanyId == 28173 || DispatchEntry.CompanyId == 28437)){
                     if (DispatchEntry.Account != null && DispatchEntry.Account.Type == Extric.Towbook.Accounts.AccountType.MotorClub &&
                            ((CompanyKeyValue.GetFirstValueOrNull(this.Company.Id,
                            Provider.Towbook.ProviderId, "HideChargesFromMotorClubInvoicesByDefault") ?? defaultValue) == "1"))
                        {
                            return true;
                        }
                        else
                        {
                            var akv = AccountKeyValue.GetByAccount(this.Company.Id, DispatchEntry.AccountId, Provider.Towbook.ProviderId, "AlwaysHideCharges").FirstOrDefault();
                            if (akv != null && akv.Value == "1")
                                return true;
                        }
                }


                if (Request.Query["showPrices"] == "1")
                    return false;
                if (Request.Query["showPrices"] == "0")
                    return true;

                if (new int[] { 6559, 4189, 4185, 4194, 4550, 6752, 5789, 6286 }.Contains(this.Company.Id))
                {
                    if (WebGlobal.CurrentUser == null || WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.Manager)
                        return true;
                }

                if (new int[] { 8685, 9313, 9314, 9258, 9846, 10329, 10809, 10598, 7992, 4817, 10157 }.Contains(this.Company.Id))
                    return false;

                var hideCharges = false;

                if (DispatchEntry.Account != null && DispatchEntry.Account.Type == Extric.Towbook.Accounts.AccountType.MotorClub &&
                    ((CompanyKeyValue.GetFirstValueOrNull(this.Company.Id,
                    Provider.Towbook.ProviderId, "HideChargesFromMotorClubInvoicesByDefault") ?? defaultValue) == "1"))
                {
                    hideCharges = true;
                }
                else
                {
                    var akv = AccountKeyValue.GetByAccount(this.Company.Id, DispatchEntry.AccountId, Provider.Towbook.ProviderId, "AlwaysHideCharges").FirstOrDefault();
                    if (akv != null && akv.Value == "1")
                        hideCharges = true;
                }

                return hideCharges;
            }
        }

        public bool HideDiscounts
        {
            get
            {
                if (Request.Query["hideDiscounts"] == "1")
                    return true;
                if (Request.Query["hideDiscounts"] == "0")
                    return false;

                if (DispatchEntry != null && 
                    DispatchEntry.Account != null &&
                    DispatchEntry.Account.Type != Extric.Towbook.Accounts.AccountType.InsuranceCompany &&
                    DispatchEntry.Account.Type != Extric.Towbook.Accounts.AccountType.MotorClub)
                {
                    return true;
                }

                return false;
            }
        }

        public bool ShowSurveyLink
        {
            get
            {
                return Request.Query["showSurveyLink"] == "1";
            }
        }

        public bool IsSafeclear
        {
            get
            {
                if (DispatchEntry.Invoice.AccountId.GetValueOrDefault() > 0)
                {

                    var a = Extric.Towbook.Accounts.Account.GetById(DispatchEntry.Invoice.AccountId.Value);

                    if (a.Company.ToLowerInvariant().Contains("city of houston") && DispatchEntry.Account.MasterAccountId == 18)
                        return true;
                }

                return false;
            }
        }
        public bool Overages
        {
            get
            {
                return DispatchEntry.InvoiceItems.Where(w => w.Name.ToLowerInvariant().Contains("overage")).Count() > 0;
            }
        }

        public string FormatPhone(string phone)
        {
            return Core.FormatPhone(phone, DispatchEntry.Company);
        }

        public string GetUserSignatureUrl(int driverId)
        {
            var driver = Driver.GetById(driverId);
            if (driver != null && driver.UserId > 0)
            {
                DriverSignatureName = driver.Name;

                var user = Extric.Towbook.User.GetById(driver.UserId);
                if (user != null)
                {
                    var userSig = UserSignature.GetByUserId(user.Id);
                    if (userSig != null)
                    {
                        var userSigAgr = UserSignatureAgreement.GetBySignatureType(user.Id, 3);
                        if (userSigAgr != null && userSigAgr.IsVoid == false)
                        {
                            string path = FileUtility.GetPresignedUrlForDownloadFromClient(userSig.Location, userSig.ContentType);
                            if (path != null)
                            {
                                return path;
                            }
                        }
                    }

                }
            }
            return null;
        }

        public DateTime OffsetDateTime(DateTime dt)
        {
            if (DispatchEntry.Company.TimezoneUseDST)
            {
                if (dt != DateTime.MinValue)
                    return dt.AddHours(DispatchEntry.Company.TimezoneOffset);
                else
                    return dt;
            }
            else
            {
                int extraOffset = 0;

                if (dt.IsDaylightSavingTime())
                {
                    extraOffset = -1;
                }
                if (dt != DateTime.MinValue)
                    return dt.AddHours(DispatchEntry.Company.TimezoneOffset).AddHours(extraOffset);
                else
                    return dt.AddHours(extraOffset);
            }

        }

        internal static CultureInfo GetCultureInfoByCompanyCountry(Company.Company.CompanyCountry cc)
        {
            switch (cc)
            {
                case Extric.Towbook.Company.Company.CompanyCountry.NewZealand:
                    return new CultureInfo("en-NZ");

                case Extric.Towbook.Company.Company.CompanyCountry.Australia:
                    return new CultureInfo("en-AU");

                case Extric.Towbook.Company.Company.CompanyCountry.SouthAfrica:
                    return new CultureInfo("en-ZA");

                case Extric.Towbook.Company.Company.CompanyCountry.UK:
                    return new CultureInfo("en-GB");

                case Extric.Towbook.Company.Company.CompanyCountry.UnitedArabEmirates:
                    return new CultureInfo("ar-AE");

                default:
                    return new CultureInfo("en-US");
            }
        }

        public string ToShortDateString(DateTime dt)
        {
            return dt.ToShortDate(DispatchEntry.Company);
        }

        /// <summary>
        /// Processes an invoice item to create a formatted view model for displaying in the UI.
        /// This method handles all formatting, calculations, and visibility logic for invoice items.
        /// </summary>
        /// <param name="item">The raw invoice item to process</param>
        /// <returns>A view model containing formatted data for the invoice item</returns>
        private async Task<InvoiceItemViewModel> ProcessInvoiceItemAsync(InvoiceItem item)
        {
            var viewModel = new InvoiceItemViewModel
            {
                OriginalItem = item
            };
            
            // Process the item with the same logic as the original WebForms implementation
            decimal cost = 0;
            Extric.Towbook.RateItem ri = null;
            viewModel.Quantity = item.Quantity.ToString();
            
            if (item.RateItem != null)
            {
                ri = await RateItem.GetByIdAsync(item.RateItem.RateItemId);
                viewModel.ItemName = HtmlEncode(ri.Name);

                if (ri.RateItemId == Extric.Towbook.RateItem.BUILTIN_DISCOUNT)
                {
                    if (item.CustomName.IndexOf("%") != -1)
                    {
                        var val = item.CustomName.Split(' ')[0];
                        val = val.Replace("Discount", "").Replace(":", "");
                        viewModel.ItemName = "Discount " + _(val);
                    }
                }

                if (ri.TimeRound != null)
                {
                    if (ri.TimeStartAtStatusId != null && ri.TimeStopAtStatusId != null)
                    {
                        decimal mins = item.Quantity * 60;
                        decimal? freeQuantity = Extric.Towbook.RateItem.GetAccountFreeQuantity(ri, DispatchEntry.Account.Id);
                        viewModel.ItemName += " (price per hour)";
                        
                        if (freeQuantity > 0)
                        {
                            viewModel.ItemName += " (" + freeQuantity + " free minutes applied)";
                        }

                        int hours = (int)Math.Floor((mins / 60));
                        int days = (int)Math.Floor(((double)hours / 24));
                        string daysString = days > 0 ? days + " d " : "";
                        int remainingHours = hours - (days * 24);
                        int remainingMins = (int)mins - (hours * 60);
                        string hourString = remainingHours > 0 ? remainingHours + " h " : "";
                        string minutesString = remainingMins > 0 ? remainingMins + " m" : "";
                        viewModel.Quantity += " (" + daysString + hourString + minutesString + ")";
                    }
                }
            }
            else
            {
                viewModel.ItemName = _(item.CustomName);
            }

            viewModel.Notes = "";
            viewModel.ShowNotes = false;
            
            if (!string.IsNullOrWhiteSpace(item.Notes))
            {
                foreach (var p in item.Notes.Split(new[] { "\r\n", "\n" }, StringSplitOptions.None))
                {
                    viewModel.Notes += _(p) + "<br/>";
                }
                viewModel.ShowNotes = true;
            }

            var free = DispatchEntry.Invoice.InvoiceItems.Where(o => o.RelatedInvoiceItemId == item.Id).FirstOrDefault();
            if (free != null && free.Name.Contains("FreeQuan") && free.Quantity != 0)
            {
                string strQuantity = free.Quantity.ToString();

                if (item.RateItem != null && DispatchEntry.Account != null)
                {
                    var accFree = Extric.Towbook.RateItem.GetAccountFreeQuantity(item.RateItem, DispatchEntry.Account.Id);
                    if (accFree != null)
                        strQuantity = accFree.Value.ToString();
                }

                var measurement = ((RateItem)item.RateItem).Measurement;

                if (measurement == RateItem.MeasurementEnum.Minutes ||
                    (ri != null && ri.TimeRound != null && ri.TimeStartAtStatusId != null && ri.TimeStopAtStatusId != null))
                {
                    viewModel.ItemName += " (" + strQuantity + " minutes free)";
                }
                else if (measurement == RateItem.MeasurementEnum.Units)
                    viewModel.ItemName += " (" + strQuantity + " unit" + (strQuantity != "1" ? "s" : "") + " free)";
                else
                    viewModel.ItemName += " (" + strQuantity + " " + DispatchEntry.Company.LocaleMile + "s free)";

                if (item.Total == 0)
                {
                    viewModel.IsVisible = false;
                    return viewModel;
                }
            }

            if (item.CustomPrice != null)
            {
                viewModel.UnitPrice = item.CustomPrice.Value.ToMoney(Company);
                cost = item.CustomPrice.Value;
            }
            else
            {
                if (item.RateItem != null)
                {
                    decimal myCost = 0;

                    if (DispatchEntry.BodyType != null &&
                        item.RateItem.ExtendedRateItems.ContainsKey(DispatchEntry.BodyType.Id))
                    {
                        myCost = item.RateItem.ExtendedRateItems[DispatchEntry.BodyType.Id].Amount;
                    }
                    else
                    {
                        myCost = item.RateItem.Cost;
                    }

                    viewModel.UnitPrice = myCost.ToMoney(Company);
                    cost = myCost;
                }
            }

            decimal tmpMiles = item.Quantity;

            if (item.RateItem != null)
            {
                if (item.CustomName != null && item.CustomName.Contains("FreeQuantity") && cost < 0)
                {
                    string unit = "";
                    if (item.RateItem.Predefined != null &&
                        (item.RateItem.Predefined.Id == PredefinedRateItem.BUILTIN_MILEAGE_LOADED ||
                        item.RateItem.Predefined.Id == PredefinedRateItem.BUILTIN_MILEAGE_UNLOADED))
                        unit = " " + DispatchEntry.Company.LocaleMile + "s";

                    viewModel.ItemName = "&nbsp;&nbsp;&nbsp;&nbsp;(Credit for " + item.Quantity + " free" + unit + ")";
                }
            }

            if (free != null)
            {
                tmpMiles = tmpMiles - Math.Abs(free.Quantity);
            }

            viewModel.LineTotal = (cost * tmpMiles).ToMoney(Company);

            if (free != null && false)
            {
                if (cost * tmpMiles == 0)
                {
                    viewModel.IsVisible = false;
                    return viewModel;
                }
            }
            
            // Add to taxable amount if item is taxable
            if (((item.RateItem != null && item.RateItem.Taxable) || item.Taxable)
                && !(item.RateItem != null && item.RateItem.Name == "Fuel Surcharge" && !item.Taxable))
            {
                TaxableAmount += cost * item.Quantity;
            }

            if (HideCharges && !viewModel.ItemName.ToLower().Contains("overage"))
            {
                TaxableAmount = 0;
                viewModel.UnitPrice = "-";
                viewModel.LineTotal = "-";
                viewModel.IsVisible = false;
                return viewModel;
            }

            if (Company.State == "MA" &&
                item.RateItem != null &&
                item.RateItem.Name.Contains("Mileage") &&
                (DispatchEntry.Account.Type == Extric.Towbook.Accounts.AccountType.PoliceDepartment))
                viewModel.ItemName = "Mileage";

            return viewModel;
        }

        /// <summary>
        /// Calculates the total number of minutes between two status events in an entry.
        /// </summary>
        /// <param name="e">The dispatch entry</param>
        /// <param name="startAtId">The ID of the status to start counting from</param>
        /// <param name="stopAtId">The ID of the status to stop counting at</param>
        /// <returns>The total minutes between the statuses, or null if one of the statuses is not set</returns>
        private static int? GetTotalMinutes(Entry e, int startAtId, int stopAtId)
        {
            DateTime? startAt = null;
            DateTime? stopAt = null;

            if (startAtId == 0)
                startAt = e.CreateDate;
            if (startAtId == 1)
                startAt = e.DispatchTime;
            if (startAtId == 2)
                startAt = e.EnrouteTime;
            if (startAtId == 3)
                startAt = e.ArrivalTime;
            if (startAtId == 4)
                startAt = e.TowTime;
            if (startAtId == 5)
                startAt = e.CompletionTime;
            if (startAtId == 7)
                startAt = e.DestinationArrivalTime;

            if (stopAtId == 0)
                stopAt = e.CreateDate;
            if (stopAtId == 1)
                stopAt = e.DispatchTime;
            if (stopAtId == 2)
                stopAt = e.EnrouteTime;
            if (stopAtId == 3)
                stopAt = e.ArrivalTime;
            if (stopAtId == 4)
                stopAt = e.TowTime;
            if (stopAtId == 5)
                stopAt = e.CompletionTime;
            if (stopAtId == 7)
                stopAt = e.DestinationArrivalTime;

            if (startAt != null && stopAt != null)
            {
                var ts = (TimeSpan)(stopAt - startAt);

                if ((int)Math.Ceiling(ts.TotalMinutes) < 0)
                    return null;
                else
                    return (int)Math.Ceiling(ts.TotalMinutes);
            }

            return (int?)null;
        }

        /// <summary>
        /// Gets the public URL for accessing this invoice, including security code for verification.
        /// </summary>
        public string PublicUrl
        {
            get
            {
                string url = "https://app.towbook.com/PublicAccess/Invoice2.aspx?id=" + DispatchEntry.Id;

                string md5 = Core.ProtectId(DispatchEntry.Id, DispatchEntry.OwnerUserId);
                url += "&sc=" + md5;
                return url;
            }
        }

        // Fix circular reference - this ensures we don't have circular JSON serialization issues
        private string _invoiceNumber;
        
        /// <summary>
        /// Gets or sets the invoice number for this dispatch entry.
        /// </summary>
        public string InvoiceNumber { get => _invoiceNumber; set => _invoiceNumber = value; }
    }
}

// Conversion Notes:
// 1. This file has been successfully converted from WebForms (Invoice.aspx.cs) to Razor Pages (jt_Invoice.cshtml.cs).
// 2. All core functionality has been preserved, including:
//    - Invoice item processing and formatting
//    - Tax and total calculations
//    - Special company-specific customizations
//    - Payment link and QR code generation
// 3. The original WebForms approach of direct HTML manipulation was replaced with a view model pattern
//    where processed data is made available to the Razor view.
// 4. The InvoiceItemViewModel class was added to hold formatted data for each invoice item.
// 5. Calculation logic was updated to work with the new model structure, particularly for:
//    - Taxable amount calculation
//    - Special handling for overages
//    - Including tips in the grand total
// 6. Additional improvements include:
//    - Comprehensive XML documentation for better maintainability
//    - Removal of obsolete WebForms-specific comments
//    - Improved error handling for QR code generation
//    - Dedicated TipTotal property for displaying tip amounts
//    - Marking the original rpInvoiceItems_ItemDataBound method as obsolete
// 7. WebForms-specific methods were replaced with ASP.NET Core equivalents:
//    - Response.WriteAsync → ContentResult with appropriate StatusCode
//    - Response.End → return statements with appropriate IActionResult
//    - Server.Execute → IViewRenderer for rendering views to string
//    - Request.QueryString → Request.Query
//    - Server.HtmlEncode → HtmlEncoder.Encode
