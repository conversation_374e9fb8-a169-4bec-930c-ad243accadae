using System.Collections.ObjectModel;
using System.Text;
using Extric.Towbook.Company;
using Extric.Towbook.Impounds;
using Extric.Towbook.Utility;
using Extric.Towbook.Web;
using Extric.Towbook.WebShared;
using Extric.Towbook.WebShared.Net5;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Primitives;

namespace Extric.Towbook.App.Pages.Impounds
{
    [Route("Impounds/[controller]")]
    public class LetterTemplateController : ControllerBase
    {
        protected int _impoundId;
        protected Impound _impound;
        protected int _letterTemplateId;
        protected LetterTemplate _letterTemplate;

        public static string GetWebAppUrl()
        {
            return Core.GetAppSetting("Towbook:WebAppUrl") ?? "http://app.towbook.com";
        }

        [HttpGet("")]
        public async Task<IActionResult> Index([FromQuery]int impoundId)
        {
            if (Request.Query["impoundId"] == StringValues.Empty)
                throw new TowbookException("An letter template id was not specified in the query parameters. Please provide an id.");

            if (Request.Query["impoundId"] == StringValues.Empty)
                throw new TowbookException("An impoundId was not specified in the query parameters. Please provide an impound.");

            _impoundId = impoundId;
            _impound = Impound.GetById(_impoundId);

            if (_impound == null || _impound.Company == null || !WebGlobal.CurrentUser.HasAccessToCompany(_impound.Company.Id))
                throw new TowbookException("The impound doesn't exist or you don't have permission to access it.");

            _letterTemplateId = Convert.ToInt32(Request.Query["id"]);

            bool No10EnvelopeFormatted = new int[] { 1418, 1419, 1420, 1658, 1666, 1698, 1699, 1700, 1723, 1824, 1850, 1975, 2076, 2093 }.Contains(_letterTemplateId);
            bool landscapeFormatted = new int[] { 1678 }.Contains(_letterTemplateId);

            var contactId = Convert.ToInt32(Request.Query["contactId"]);
            if (contactId > 0
                && _impound.DispatchEntry != null
                && _impound.DispatchEntry.Contacts != null
                && _impound.DispatchEntry.Contacts.Any(a => a.Id == contactId))
            {
                _impound.DispatchEntry.Contacts = _impound.DispatchEntry.Contacts.Where(w => w.Id == contactId).ToCollection();
            }

            // check if pdf file exists.
            var isPdf = System.IO.File.Exists("../storage/forms/" + _letterTemplateId + ".pdf");

            if (isPdf)
            {
                var domain = GetWebAppUrl();
                var inline = Request.Query["inline"] == StringValues.Empty ? true : Request.Query["inline"] == "false";
                var url = domain + "/impounds/TemplatePdf.aspx?id=" + _impoundId + "&templateId=" + _letterTemplateId;

                if (Request.Query["debug"] == "true")
                    url = url + "&debug=true";

                if (Request.Query["flatten"] == "true")
                    url = url + "&flatten=true";

                return Redirect(url);
            }

            var tlist = (await LetterTemplate.GetByCompanyAsync(_impound.Company)).ToList();
            var outsideTemplate = CompanyLetterTemplate.GetByCompanyId(_impound.Company.Id).FirstOrDefault(f => f.LetterTemplateId == _letterTemplateId);
            if (outsideTemplate != null && tlist.Any(a => a.Id != _letterTemplateId))
            {
                var letter = await LetterTemplate.GetByIdAsync(outsideTemplate.LetterTemplateId);
                letter.CompanyId = _impound.Company.Id;
                tlist.Add(letter);
            }

            _letterTemplate = tlist.FirstOrDefault(f => f.Id == _letterTemplateId);
            if (_letterTemplate == null || (!_letterTemplate.IsStateLetter && !WebGlobal.CurrentUser.HasAccessToCompany(_letterTemplate.CompanyId)))
                throw new TowbookException("The letter template doesn't exist or you don't have permission to access it.");

            if (_letterTemplate.No10EnvelopeFormatted)
                No10EnvelopeFormatted = true;

            if (Request.Query["No10"] == "1")
            {
                No10EnvelopeFormatted = true;
            }

            var coreAttributes = await Dispatch.Attribute.GetByDispatchEntryAsync(WebGlobal.CurrentUser.Company, _impound.DispatchEntry, false);

            Dictionary<string, string> attributes = new Dictionary<string, string>();

            foreach (var av in coreAttributes)
            {
                string value = "";

                if (_impound.DispatchEntry!.Attributes.TryGetValue(av.Id, out Dispatch.AttributeValue? xvalue))
                {
                    value = xvalue.Value;
                }
                else
                {
                    value = "<span style='color: red; font-weight: bold; font-size:25px;'>[" + av.Name.ToUpper() + " NOT ENTERED]</span>";
                    _impound.DispatchEntry.Attributes.Add(av.Id, new Extric.Towbook.Dispatch.AttributeValue() { Value = value });
                }

                if (!attributes.ContainsKey(av.Name))
                    attributes.Add(av.Name, value);
            }

            if (!attributes.ContainsKey("towing_company_name"))
            {
                attributes.Add("towing_company_name", "$Company.Name, $Company.Address, $Company.City $Company.State $Company.Zip, $Company.Phone");
            }

            // Washington State has forms that require an owner that is different that the current owner. On the form DLR-430-510 the towing company
            // is required to list the "owner of record indicated on Report of Sale".  This new type of owner needs to be a contact on the impound.
            Extric.Towbook.Dispatch.EntryContact reportOfSaleOwner = _impound.DispatchEntry.Contacts.FirstOrDefault(o => o.Type == Extric.Towbook.Dispatch.ContactType.ReportOfSaleOwner);

            _impound.CreateDate = WebGlobal.OffsetDateTime(_impound.CreateDate);
            _impound.DispatchEntry.CreateDate = WebGlobal.OffsetDateTime(_impound.DispatchEntry.CreateDate);

            if (reportOfSaleOwner != null)
                _impound.DispatchEntry.Contacts.Add(reportOfSaleOwner);

            if (Request.Query["pdf"] != "0")
            {
                var sw = new System.IO.StringWriter();

                string imageHtml = "";
                var cl = Extric.Towbook.Company.CompanyLogo.GetByCompanyId(_impound.Company.Id);

                if (cl != null)
                {
                    imageHtml = "<img src=\"" + cl.Url + "\" />";
                }
                else
                {
                    string[] opportunities = new string[] { "_template.jpg", "_full.jpg", "_left.jpg", ".jpg" };
                    string baseLocal = @"..\ui\images\customer.logo\" + WebGlobal.CurrentUser.Company.Id;
                    string baseRemote = "https://app.towbook.com/ui/images/customer.logo/" + WebGlobal.CurrentUser.Company.Id;
                    string filename = "";

                    foreach (var file in opportunities)
                    {

                        if (System.IO.File.Exists(baseLocal + file))
                        {
                            filename = baseRemote + file;
                            break;
                        }
                    }

                    if (!string.IsNullOrEmpty(filename))
                        imageHtml = "<img src=\"" + filename + "\" />";
                }

                /////////////////

                var htmls = new List<string>();

                //Create a PdfDocument object and convert
                //the Url into the PdfDocument object
                OutputArea? outputArea = null;
                PageSize? pageSize = null;
                string? footerHtml = null;

                if (landscapeFormatted)
                    pageSize = PageSizes.A4;
                else
                    outputArea = new OutputArea(0.3f, 0.3f, 7.8f, 10.3f);

                bool hideFooter = new int[] { 17609 }.Contains(WebGlobal.CurrentUser.CompanyId);
                if (!hideFooter)
                    footerHtml = "<div style=\"color: #333333; font-family: verdana; font-size: 9px; font-weight: bold\"><br /><br />Created with Towbook Management Software | www.towbook.com <div style=\"display:inline-block; float:right; font-weight: normal;\">Printed " + DateTime.Now.ToShortDate(_impound.Company) + "</div></div>";

                if (No10EnvelopeFormatted)
                {

                    // make sure there is at least one contact to avoid an error
                    if (_impound.DispatchEntry.Contacts.Count == 0)
                        _impound.DispatchEntry.Contacts.Add(new Extric.Towbook.Dispatch.EntryContact());

                    bool ccContacts = new int[] { 1347, 1348, 2271 }.Contains(_letterTemplateId);

                    foreach (var contact in _impound.DispatchEntry.Contacts)
                    {
                        var letter = await GenerateLetterHtmlAsync(_letterTemplate,
                            _impound.DispatchEntry.Contacts.Where(w => w.Id == contact.Id).ToCollection(),
                            attributes,
                            coreAttributes,
                            imageHtml,
                            true,
                            (ccContacts ? _impound.DispatchEntry.Contacts.Where(w => w.Id != contact.Id).ToCollection() : null));

                        htmls.Add(letter);
                    }
                }
                else
                {
                    var letter = await GenerateLetterHtmlAsync(_letterTemplate, _impound.DispatchEntry.Contacts, attributes, coreAttributes, imageHtml, false, null);

                    htmls.Add(letter);
                }

                var stream = No10EnvelopeFormatted ? await PdfClient.GeneratePdf(htmls, null, outputArea, pageSize, footerHtml, true)
                    : await PdfClient.GeneratePdf(htmls, null);
                var sfilename = impoundId + "-" + _letterTemplate.Id + ".pdf";

                Response.Headers["Content-Disposition"] = $"inline; filename={sfilename}";

                return File(stream, "application/pdf");
            }

            var response = new HttpResponseMessage();

            var sb = new StringBuilder();
            sb.AppendLine("<html><head><style> body { max-width: 1000px; margin: 0 auto; font-size: 14px } p.tight {margin: 0.5em 0;} * { font-family: calibri, verdana }</style></head><body>");

            sb.AppendLine((await Impound.ParseTemplateAsync(_letterTemplate, _impound)).Replace(
                "$CurrentDate", Core.OffsetDateTime(_impound.Company, DateTime.Now).ToShortDate(_impound.Company)));
            sb.AppendLine("</body></html>");
            
            return Content(sb.ToString());
        }

        private async Task<string> GenerateLetterHtmlAsync(Extric.Towbook.LetterTemplate template,
            Collection<Extric.Towbook.Dispatch.EntryContact> Contacts,
            Dictionary<string, string> attributes,
            Collection<Extric.Towbook.Dispatch.Attribute> coreAttributes,
            string imageHtml,
            bool no10Envelope = false,
            Collection<Extric.Towbook.Dispatch.EntryContact>? ccContacts = null)
        {
            System.IO.StringWriter sw = new System.IO.StringWriter();
            if (_impound == null)
                return "NULL IMPOUND";

            bool showStockNumberAsInvoiceNumber = new int[] { 3087, 5348 }.Contains(_impound.Company.Id);
            var No10EnvelopeFormatted = no10Envelope;
            bool hideTitle = new int[] { 1418, 1419, 1420, 1698, 1699, 1700, 2348 }.Contains(template.Id);
            bool useLienholder = new int[] { 1419, 1666, 1850 }.Contains(template.Id);
            bool showTotalInSummaryBlock = new int[] { 3087 }.Contains(_impound.Company.Id);
            bool showDailyStorageInSummaryBlock = (_impound.Company.State.ToLowerInvariant() == "WA" && WebGlobal.CurrentUser.Company.Country == Extric.Towbook.Company.Company.CompanyCountry.USA)
                                                    || new int[] { 835 }.Contains(_impound.Company.Id) ? false : true;

            string releaseBirthDate = "N/A";
            string releasePhone = "N/A";

            string tdlrNumber = null;
            string vsfNumber = null;

            var rd = _impound.ReleaseDetails;
            if (rd != null)
            {

                if (rd.BirthDate != null && rd.BirthDate > DateTime.MinValue)
                    releaseBirthDate = rd.BirthDate.Value.ToShortDate(_impound.Company);
                if (!string.IsNullOrWhiteSpace(rd.Phone))
                    releasePhone = rd.Phone;
            }

            if (_impound.DispatchEntry.Attributes.ContainsKey(12))
            {
                var cx = Extric.Towbook.Company.Company.GetById(Convert.ToInt32(_impound.DispatchEntry.Attributes[12].Value));
                if (cx != null)
                {
                    _impound.Company = cx;
                    _impound.DispatchEntry.Company = cx;
                }
            }


            var licenseValues = Extric.Towbook.Licenses.CompanyLicenseKeyValue.GetByCompanyId(_impound.Company.Id);
            var keys = Extric.Towbook.Licenses.CompanyLicenseKey.GetByCompany(_impound.Company);
            var printables = licenseValues.Where(y => keys.Where(o => o.Id == y.KeyId && o.ShowValueOnPrintables == true).Any());
            Dictionary<string, string> licenses = new Dictionary<string, string>();

            foreach (var k in keys)
            {
                if (!attributes.ContainsKey(k.Name) && licenseValues.Select(s => s.KeyId).Contains(k.Id))
                {
                    var lv = licenseValues.Where(w => w.KeyId == k.Id).FirstOrDefault();
                    if (lv != null)
                    {
                        licenses.Add(k.Name, lv.Value);
                    }
                }
            }

            if (printables != null)
            {
                var tdlr = printables.Where(o => o.KeyId == 2).FirstOrDefault();
                var vsf = printables.Where(o => o.KeyId == 3).FirstOrDefault();

                if (tdlr != null)
                    tdlrNumber = tdlr.Value;
                if (vsf != null)
                    vsfNumber = vsf.Value;
            }

            string companyLicenses = "";
            int count = 1;
            foreach (var p in printables)
            {
                if (count != 1 && (count % 2) > 0) // wrap at each odd printables count
                    companyLicenses += "<br/>";

                var key = keys.FirstOrDefault(s => s.Id == p.KeyId);
                companyLicenses += "<span style='font-size: small;'>" + key.Name + " " + p.Value + "&nbsp&nbsp;</span>";

                count++;
            }

            string companyHeader = "<div id=\"company_header\" style=\"text-align: center; font-family: segoe ui, 'Open Sans' !important; font-size: medium;\"><span style=\"font-size: 20px\">$Company.Name<br /></span><span style=\"font-size: small;\">$Company.Address, </span><span style=\"font-size: small;\">$Company.City $Company.State $Company.Zip<br /></span><span style=\"font-size: small;\">Office: $Company.Phone&nbsp&nbsp;</span>" +
                (_impound.Company.Fax != null ? "<span style=\"font-size: small;\">Fax: $Company.Fax</span>" : "") +
                "<span style=\"font-size: small\"><br>" + companyLicenses + "</span>" +
                "<div style=\"text-align: center; font-size: 20px; line-height: 20px; padding-top: 0.4em;\">" + template.Title + "</div></div> ";

            string companyHeaderNoTitle = "<div id=\"company_header\"><p style=\"text-align: center; font-family: segoe ui, 'Open Sans' !important; font-size: medium;\"><span style=\"font-size: 24px\">$Company.Name<br /></span><span style=\"font-size: small;\">$Company.Address, </span><span style=\"font-size: small;\">$Company.City $Company.State $Company.Zip<br /></span><span style=\"font-size: small;\">Office: $Company.Phone&nbsp&nbsp;</span>" +
                (_impound.Company.Fax != null ? "<span style=\"font-size: small;\">Fax: $Company.Fax</span>" : "") +
                "<span style=\"font-size: small;\"><br/>" + companyLicenses + "</span></p></div>";

            if (new int[] { 3308, 2020 }.Contains(_impound.Company.Id))
            {
                companyHeader = companyHeader.Replace("$Company.Name", _impound.Lot.Name).Replace("$Company.Address", _impound.Lot.Address).Replace("$Company.City", _impound.Lot.City).Replace("$Company.State", _impound.Lot.State).Replace("$Company.Zip", _impound.Lot.Zip);
            }


            var impoundContacts = _impound.DispatchEntry.Contacts;

            if (template.Id == 1347 || template.Id == 1348)
                impoundContacts = _impound.DispatchEntry.Contacts.Where(w => w.Type != Extric.Towbook.Dispatch.ContactType.PropertyManager).ToCollection();

            string contacts = String.Empty;
            foreach (var contact in impoundContacts)
            {
                if (contact.Name.Length > 0)
                {
                    contacts += "<div style=\"display: inline-block; width: 200px; padding-bottom: 10px;\"><strong>" + contact.Type.ToString().Replace("Unspecified", "Other") + "</strong><br />" + contact.Name + "<br />" + contact.Address + "<br />" + contact.City + "&nbsp;" + contact.State + "&nbsp;" + contact.Zip + "</div>";
                }
            }

            string summaryBlock = "<p><span><b>" + (showStockNumberAsInvoiceNumber ? "Invoice #:" : "Stock #:") + " </b>" + (showStockNumberAsInvoiceNumber ? _impound.DispatchEntry.InvoiceNumber : _impound.Id.ToString()) + "</span><strong><span>&nbsp; &nbsp; Year/Make/Model&nbsp;</span></strong><span>$Ticket.Year $Ticket.MakeModelFormatted &nbsp; &nbsp;</span><strong><span>License Plate #&nbsp;</span></strong><span>$Ticket.LicenseNumber &nbsp; &nbsp;</span><strong><span size=\"2\">VIN #&nbsp;</span></strong><span>$Ticket.VIN<br /></span><strong>Date Towed</strong><span>&nbsp;" + Core.OffsetDateTime(_impound.Company, _impound.ImpoundDate.Value).ToShortDate(_impound.Company) + "&nbsp;<strong>Towed From </strong>&nbsp;$Ticket.TowSource&nbsp;&nbsp;</span>" + (showDailyStorageInSummaryBlock ? "<strong>Daily Storage</strong><span>&nbsp;$Impound.StorageDailyRate.ToString(\"C\")&nbsp;Per Day&nbsp;</span>" : "") + (showTotalInSummaryBlock ? "<strong>Total Due</strong><span>&nbsp;$Impound.InvoiceTotal.ToString(\"C\") as of " + Core.OffsetDateTime(_impound.Company, DateTime.Now).ToShortDateString(_impound.Company) + "</span>" : "") + "</p>";

            if (template.Id == 1347 || template.Id == 1348)
                summaryBlock = "<p class='tight' style='margin-top: 0px;'><span style='font-size: small;'><b>Invoice #: </b>" + _impound.DispatchEntry.InvoiceNumber + "</span><strong><span style='font-size: small;'>&nbsp;&nbsp;Year/Make&nbsp;</span></strong><span style='font-size: small;'>" + _impound.DispatchEntry.Year + "&nbsp;" + _impound.DispatchEntry.MakeModelFormatted + "</span><strong><span style='font-size: small;'>&nbsp;&nbsp;License Plate #&nbsp;</span></strong><span style='font-size: small;' size='2'>" + _impound.DispatchEntry.LicenseNumber + "&nbsp; &nbsp;</span><strong><span style='font-size: small;' size='2'>VIN #&nbsp;</span></strong><span style='font-size: small;' size='2'>" + _impound.DispatchEntry.VIN + "<br/></span><strong style='font-size: small;'>Date Towed</strong><span style='font-size: small;'>&nbsp;" + Core.OffsetDateTime(_impound.Company, _impound.ImpoundDate.Value).ToShortDate(_impound.Company) + "&nbsp;</span><strong style='font-size: small;'>Accepted for Storage Date</strong><span style='font-size: small;'>&nbsp;" + Core.OffsetDateTime(_impound.Company, _impound.ImpoundDate.Value).ToShortDateString(_impound.Company) + "&nbsp;</span><strong style='font-size: small;'>First Date Storage is assessed </strong><span style='font-size: small;'>" + Core.OffsetDateTime(_impound.Company, _impound.ImpoundDate.Value).ToShortDateString(_impound.Company) + "&nbsp;</span><span style='font-size: small;'>&nbsp;</span><span style='font-size: small;'><strong>Towed From </strong>&nbsp;" + _impound.DispatchEntry.TowSource + "&nbsp;&nbsp;</span><strong style='font-size: small;'>Daily Storage</strong><span style='font-size: small;'>&nbsp;" + _impound.StorageDailyRate.ToString("C") + "&nbsp;Per Day&nbsp;</span><strong style='font-size: small;'>Driver</strong><span style='font-size: small;'>&nbsp;$DriverName&nbsp;(TDLR #$DriverTDLR#)</span></p>";


            const string impoundLot = "$Impound.Lot.Address, $Impound.Lot.City $Impound.Lot.State";
            const string invoiceItems = "<div>#foreach($i in $Ticket.InvoiceItems) <div style=\"display: block;\"><div style=\"display: inline-block; width: 400px;\"><strong>$i.Name</strong></div><div style=\"display: inline-block; width: 100px;\">$i.Quantity @ $i.Price.ToString(\"C\")</div><div style=\"display: inline-block; width: 100px;\">$i.Total.ToString(\"C\")</div></div> #end </div>";

            if (_impound.Company.Id == 2051)
                summaryBlock = summaryBlock.Replace("<span><b>Stock #: </b>$Impound.Id</span><strong><span>&nbsp; &nbsp; ", "<strong><span>");

            string inlineBodyStyle = "<style> " +
                    "body { max-width: 1000px; margin: 0 auto; font-size: 14px } " +
                    "* { font-family: calibri, verdana !important } " +
                    "#company_header { font-family: segoe ui light, 'Open Sans' !important; font-size: 1.2em } " +
                    "#no10_envelope_addresses { display: none; }" +
                    ".tight { margin: 0.5em 0; } " + "</style>";
            string no10Addresses = "&nbsp;";

            if (No10EnvelopeFormatted)
            {
                inlineBodyStyle = "<style> " +
                    "body { text-align: left; white-space: normal; margin: 0; padding: 0; height: 11in; width; 8.5in; margin-left: 0.4in; margin-top: 0.4in; margin-bottom: 0.4in; font-size: 13px } " +
                    "table { font-size: 13 px }" +
                    "* { font-family: calibri, verdana !important }" +
                    "#company_header { font-family: segoe ui light, 'Open Sans' !important; font-size: 1.2em }" +
                    "#company_header p { font-align: left !important } " +
                    "#no10_envelope_addresses { position: relative; height: 2.450in; width: 100%; }" +
                    "#body_content { position: relative; left: 0; width: 100%; white-space: normal; margin: -10px 0 0 0; padding: 0 }" +
                    "#return_address { position:absolute; width: 3.25in; text-align: left; margin-left: 0.25in; z-index:100; } " +
                    "#recipient_address { position: absolute; top: 1.542in; width: 3.25in; height: 0.875in; margin-left: 0.25in; } " +
                    "#cc_address { position: absolute; right: 0px; top: 0px; bottom: 0px; width: 3.0in; } " +
                    "#cc_address_col { height: 2.25in; columns-count: 2; -webkit-column-count: 2; column-gap: .10in; -webkit-column-gap: .10in; overflow: hidden; } " +
                    "#tx_second_notice_title { display: none; } " +
                    "#recipient_address .larger { font-size: 1.2em } " +
                    ".tight { margin: 0.4em 0; } " + "</style>";

                if (!string.IsNullOrWhiteSpace(companyHeaderNoTitle))
                    hideTitle = true;

                string title = hideTitle ? "" : "<div style=\"font-size: 20px; text-align: center;\">" + template.Title + "</div>";
                var recipient = useLienholder ? Contacts.Where(w => w.Type == Extric.Towbook.Dispatch.ContactType.Lienholder).FirstOrDefault() : Contacts.FirstOrDefault();
                string cc = "";

                if (ccContacts != null)
                {
                    var showTypes = ccContacts.Count() < 5;
                    cc += "<div id=\"cc_address_col\">";
                    foreach (var con in ccContacts)
                    {
                        cc += "<div style =\"display: inline-block; width: 100%; padding-bottom: 10px;\">" +
                            (showTypes ? "<strong>" + con.Type.ToString().Replace("Unspecified", "Other") + "</strong><br />" : "") +
                            con.Name + "<br />" + con.Address + "<br />" + con.City + " " + con.State + " " + con.Zip + "</div>";
                    }
                    cc += "</div>";
                }

                string returnAddress = _impound.Company.Name + "<br/>" +
                                        _impound.Company.Address + "<br/>" +
                                        _impound.Company.City + " " + _impound.Company.State + " " + _impound.Company.Zip + "<br/>" +
                                        _impound.Company.Phone +
                                        (!string.IsNullOrWhiteSpace(_impound.Company.Fax) ? "<br/>" + _impound.Company.Fax : "") +
                                        (!string.IsNullOrWhiteSpace(_impound.Company.Email) ? "<br/>" + _impound.Company.Email : "");

                if (_impound.Company.Id == 2064)
                {
                    returnAddress = "Blue Hill Wrecker & Towing<br/>14815 MacArthur Drive North, Little Rock, AR 72118<br/>265 Park Street, Clinton, AR 72031<br/>Office: 501‐851‐1575 / 501‐745‐6129<br/>Fax: ************";
                }

                var abooks = AddressBookEntry.GetByCompany(_impound.Company);

                var billingAddress = abooks.Where(o => o.Name == "Billing Address").FirstOrDefault();
                if (billingAddress != null)
                {
                    returnAddress = _impound.Company.Name + "<br/>" + billingAddress.Address + "<br/>" + billingAddress.City + " " + billingAddress.State + " " + billingAddress.Zip + "<br/>" + billingAddress.Phone +
                                        (!string.IsNullOrWhiteSpace(_impound.Company.Fax) ? "<br/>" + _impound.Company.Fax : "") +
                                        (!string.IsNullOrWhiteSpace(_impound.Company.Email) ? "<br/>" + _impound.Company.Email : "");
                }

                var formAddress = abooks.Where(o => o.Name == "Form Address").FirstOrDefault();
                if (formAddress != null)
                {
                    returnAddress = _impound.Company.Name + "<br/>" + formAddress.Address + "<br/>" + formAddress.City + " " + formAddress.State + " " + formAddress.Zip + "<br/>" + formAddress.Phone +
                                        (!string.IsNullOrWhiteSpace(_impound.Company.Fax) ? "<br/>" + _impound.Company.Fax : "") +
                                        (!string.IsNullOrWhiteSpace(_impound.Company.Email) ? "<br/>" + _impound.Company.Email : "");
                }

                if (_impound.Company.Id == 3087 && (template.Id == 1658 || template.Id == 1666))
                    title += "<div style=\"font-size: 20px\">Notice of Impound</div>";

                if (template.Id == 1348)
                    title = "<div style =\"font-size: 20px; text-align: center; padding-top: 0px; margin-top: -20px; \">Second Notice (Texas)<span style=\"display: block; margin: 0; padding: 0; font-size: 13px;\">Final Notice Consent To Sell Impounded Vehicle</span></div>";

                no10Addresses = "<div id=\"return_address\">" + returnAddress + "</div>" +
                    "<div id=\"cc_address\">" + cc + "</div>" +
                    "<div id=\"recipient_address\"><span class=\"larger\">" + (recipient != null ? recipient.Name + "&nbsp;<br />" + recipient.Address + "<br />" + recipient.City + " " + recipient.State + " " + recipient.Zip : "[no contact specified as " + (useLienholder ? "'lienholder'" : "'owner'") + "]") + "</span></div>";

                companyHeader = string.Empty;
                companyHeaderNoTitle = string.Empty;
                contacts = title;
            }

            sw.Write("<html><head>" + inlineBodyStyle + "</head><body><div id=\"no10_envelope_addresses\">" + no10Addresses + "</div><div id=\"body_content\">");

            string driverName = "";
            string driverTDLR = "";
            var driverTDLRLicenseKey = (await Extric.Towbook.Licenses.DriverLicenseKey.GetLicenceKeyDataAsync(_impound.Company)).Where(w => w.Name == "Driver TDLR#").FirstOrDefault();
            if (_impound.DispatchEntry != null && _impound.DispatchEntry.Drivers != null)
            {
                var divider = _impound.DispatchEntry.Drivers.Count > 1 ? " / " : "";
                foreach (var d in _impound.DispatchEntry.Drivers)
                {
                    var driver = await Driver.GetByIdAsync(d);
                    if (driver != null)
                    {
                        driverName += driver.Name + divider;

                        if (driverTDLRLicenseKey != null)
                        {
                            var value = Extric.Towbook.Licenses.DriverLicenseKeyValue.GetByDriverId(d).Where(w => w.KeyId == driverTDLRLicenseKey.Id).FirstOrDefault();
                            if (value != null)
                                driverTDLR += value.Value + divider;
                        }
                    }
                }
            }

            string truckName = "";
            string truckTDLR = "";
            string truckTag = "";
            var truckTDLRLicenseKey = Extric.Towbook.Licenses.TruckLicenseKey.GetLicenceKeyData(_impound.Company).Where(w => w.Name == "Truck TDLR#").FirstOrDefault();
            if (_impound.DispatchEntry != null && _impound.DispatchEntry.Trucks != null)
            {
                var divider = _impound.DispatchEntry.Drivers.Count > 1 ? " / " : "";
                foreach (var t in _impound.DispatchEntry.Trucks)
                {
                    var truck = await Truck.GetByIdAsync(t);

                    if (truckTDLRLicenseKey != null)
                    {
                        var value = Extric.Towbook.Licenses.TruckLicenseKeyValue.GetByTruckId(t).Where(w => w.KeyId == truckTDLRLicenseKey.Id).FirstOrDefault();
                        if (value != null)
                            truckTDLR = value.Value;
                    }

                    if (truck != null)
                    {
                        truckName = truck.Name + (string.IsNullOrWhiteSpace(truckTDLR) ? "" : " [" + truckTDLR + "]") + divider;
                        truckTag = truck.PlateNumber + divider;
                    }
                }
            }

            var court = AddressBookEntry.GetByCompany(_impound.Company).Where(o => o.Name.ToLowerInvariant().Contains("district court")).SingleOrDefault();
            var courtName = "";
            var courtAddress = "";
            var courtCity = "";
            var courtState = "";
            var courtZip = "";
            var courtFullAddress = "";
            var courtPhone = "";
            if (court != null)
            {
                courtName = court.Name;
                courtAddress = court.Address;
                courtCity = court.City;
                courtState = court.State;
                courtZip = court.Zip;
                courtFullAddress = courtAddress + " " + courtCity + ", " + courtState + " " + courtZip;
                courtPhone = court.Phone;
            }

            var deficiencyLienAmount = _impound.Invoice.BalanceDue;
            if (_impound.Company.State.ToUpperInvariant() == "WA" &&
                WebGlobal.CurrentUser.Company.Country == Company.Company.CompanyCountry.USA && deficiencyLienAmount > 500)
            {
                var tr = await TaxRate.GetByCompanyAsync(_impound.Company);
                if (tr.Count > 0)
                {
                    var rate = tr[0].Rate;
                    deficiencyLienAmount = 500 + (500 * (rate / 100));
                }
            }

            #region contacts
            if (impoundContacts.Any())
            {
                for (var i = 1; i <= 10; i++)
                {
                    var contact = impoundContacts.ElementAtOrDefault(i - 1) ?? new Extric.Towbook.Dispatch.EntryContact();
                    var lien = impoundContacts.Where(w => w.Type == Extric.Towbook.Dispatch.ContactType.Lienholder).ElementAtOrDefault(i - 1) ?? new Extric.Towbook.Dispatch.EntryContact();
                    var owner = impoundContacts.Where(w => w.Type == Extric.Towbook.Dispatch.ContactType.Owner).ElementAtOrDefault(i - 1) ?? new Extric.Towbook.Dispatch.EntryContact();
                    var indy = impoundContacts.Where(w => w.Type == Extric.Towbook.Dispatch.ContactType.Individual).ElementAtOrDefault(i - 1) ?? new Extric.Towbook.Dispatch.EntryContact();
                    var insuranceContact = impoundContacts.Where(w => w.Type == Extric.Towbook.Dispatch.ContactType.Insurance).ElementAtOrDefault(i - 1) ?? new Extric.Towbook.Dispatch.EntryContact();

                    template.Contents = template.Contents.Replace("$ImpoundContactName" + i, contact.Name)
                                            .Replace("$ImpoundContactAddress" + i, contact.Address)
                                            .Replace("$ImpoundContactCity" + i, contact.City)
                                            .Replace("$ImpoundContactState" + i, contact.State)
                                            .Replace("$ImpoundContactZip" + i, contact.Zip)
                                            .Replace("$ImpoundContactPhone" + i, contact.Phone)
                                            .Replace("$ImpoundContactCityStateZip" + i, contact.City + ", " + contact.State + " " + contact.Zip)
                                            .Replace("$ImpoundContactFullAddress" + i, contact.Address + " " + contact.City + ", " + contact.State + " " + contact.Zip)
                                            .Replace("$ImpoundLienholderName" + i, lien.Name)
                                            .Replace("$ImpoundLienholderAddress" + i, lien.Address)
                                            .Replace("$ImpoundLienholderCity" + i, lien.City)
                                            .Replace("$ImpoundLienholderState" + i, lien.State)
                                            .Replace("$ImpoundLienholderZip" + i, lien.Zip)
                                            .Replace("$ImpoundLienholderPhone" + i, lien.Phone)
                                            .Replace("$ImpoundLienholderCityStateZip" + i, lien.City + ", " + lien.State + " " + lien.Zip)
                                            .Replace("$ImpoundLienholderFullAddress" + i, lien.Address + " " + lien.City + ", " + lien.State + " " + lien.Zip)
                                            .Replace("$ImpoundOwnerName" + i, owner.Name)
                                            .Replace("$ImpoundOwnerAddress" + i, owner.Address)
                                            .Replace("$ImpoundOwnerCity" + i, owner.City)
                                            .Replace("$ImpoundOwnerState" + i, owner.State)
                                            .Replace("$ImpoundOwnerZip" + i, owner.Zip)
                                            .Replace("$ImpoundOwnerPhone" + i, owner.Phone)
                                            .Replace("$ImpoundOwnerCityStateZip" + i, owner.City + ", " + owner.State + " " + owner.Zip)
                                            .Replace("$ImpoundOwnerFullAddress" + i, owner.Address + " " + owner.City + ", " + owner.State + " " + owner.Zip)
                                            .Replace("$ImpoundIndividualName" + i, indy.Name)
                                            .Replace("$ImpoundIndividualAddress" + i, indy.Address)
                                            .Replace("$ImpoundIndividualCity" + i, indy.City)
                                            .Replace("$ImpoundIndividualState" + i, indy.State)
                                            .Replace("$ImpoundIndividualZip" + i, indy.Zip)
                                            .Replace("$ImpoundIndividualPhone" + i, indy.Phone)
                                            .Replace("$ImpoundIndividualCityStateZip" + i, indy.City + ", " + indy.State + " " + indy.Zip)
                                            .Replace("$ImpoundIndividualFullAddress" + i, indy.Address + " " + indy.City + ", " + indy.State + " " + indy.Zip)
                                            .Replace("$ImpoundInsuranceName" + i, insuranceContact.Name)
                                            .Replace("$ImpoundInsuranceAddress" + i, insuranceContact.Address)
                                            .Replace("$ImpoundInsuranceCity" + i, insuranceContact.City)
                                            .Replace("$ImpoundInsuranceState" + i, insuranceContact.State)
                                            .Replace("$ImpoundInsuranceZip" + i, insuranceContact.Zip)
                                            .Replace("$ImpoundInsurancePhone" + i, insuranceContact.Phone)
                                            .Replace("$ImpoundInsuranceCityStateZip" + i, insuranceContact.City + ", " + insuranceContact.State + " " + insuranceContact.Zip)
                                            .Replace("$ImpoundInsuranceFullAddress" + i, insuranceContact.Address + " " + insuranceContact.City + ", " + insuranceContact.State + " " + insuranceContact.Zip);
                }
            }
            #endregion

            template.Contents = template.Contents.Replace("$CompanyHeaderNoTitle", companyHeaderNoTitle).Replace("$CompanyHeader", companyHeader).Replace("$Contacts", contacts).Replace("$SummaryBlock", summaryBlock).Replace("$ImpoundLot", impoundLot).Replace("$Charges", invoiceItems).Replace("$TDLR", tdlrNumber)
                .Replace("$StorageTotal", (_impound.GetCategoryTotal(3) + _impound.GetCategoryTotal(2) + _impound.InvoiceStorageTotal).ToString("C"))
                .Replace("$TowingTotal", (_impound.GetCategoryTotal(1) + _impound.GetCategoryTotal(0)).ToString("C"))
                .Replace("$TowingAndUncategorizedTotal", (_impound.GetCategoryTotal(1) + _impound.GetCategoryTotal(0)).ToString("C"))
                .Replace("$UncategorizedTotal", _impound.GetCategoryTotal(0).ToString("C"))
                .Replace("$RecoveryTotal", _impound.GetCategoryTotal(16).ToString("C"))
                .Replace("$TowingAndRecoveryTotal", (_impound.GetCategoryTotal(16) + _impound.GetCategoryTotal(1)).ToString("C"))
                .Replace("$TowingRecoveryAndUncategorizedTotal", (_impound.GetCategoryTotal(16) + _impound.GetCategoryTotal(1) + _impound.GetCategoryTotal(0)).ToString("C"))
                .Replace("$TdlrTowingCompany", attributes["towing_company_name"])
                .Replace("$DriverName", driverName)
                .Replace("$DriverTDLR#", driverTDLR)
                .Replace("$TruckName", truckName)
                .Replace("$TruckPlate", truckTag)
                .Replace("$CallNumber", (_impound.DispatchEntry != null ? _impound.DispatchEntry.CallNumber.ToString() : ""))
                .Replace("$AccountType", ((int)_impound.Account.Type).ToString())
                .Replace("$CourtName", courtName)
                .Replace("$CourtAddress", courtAddress)
                .Replace("$CourtFullAddress", courtFullAddress)
                .Replace("$CourtCity", courtCity)
                .Replace("$CourtState", courtState)
                .Replace("$CourtZip", courtZip)
                .Replace("$CourtPhone", courtPhone)
                .Replace("$DeficiencyLienAmount", deficiencyLienAmount.ToString("C"))
                .Replace("$Billing_Notes", _impound.DispatchEntry.BillingNotes());

            string companyTwo = _impound.Company.Name;

            if (_impound.Company.Id == 3308)
                companyTwo = "Phoenix Towing";

            var loadedInvoiceItem = _impound.DispatchEntry.InvoiceItems.Where(w =>
                w.RateItem != null &&
                w.RateItem.Predefined != null &&
                w.RateItem.Predefined.Id == Extric.Towbook.PredefinedRateItem.BUILTIN_MILEAGE_LOADED).FirstOrDefault();

            var paymentTotal = _impound.DispatchEntry.InvoiceTotal - _impound.DispatchEntry.BalanceDue;
            try
            {
                sw.Write((await Impound.ParseTemplateAsync(template, _impound, licenses))
                 .Replace("$CurrentDate", DateTime.Now.ToShortDate(_impound.Company))
                 .Replace("$ReleaseBirthDate", releaseBirthDate)
                 .Replace("$ReleasePhone", releasePhone)
                 .Replace("$TowingCompanyName", companyTwo)
                 .Replace("$Fax", "")
                 .Replace("$Logo", imageHtml)
                 .Replace("$OffsetImpoundDate", WebGlobal.OffsetDateTime(_impound.ImpoundDate.Value).ToShortDate(_impound.Company))
                 .Replace("$OffsetImpoundTime", WebGlobal.OffsetDateTime(_impound.ImpoundDate.Value).ToShortTowbookTimeString())
                 .Replace("$ImpoundDateLegal", string.Format(new DateAsLegal(), "{0}", _impound.ImpoundDate.Value))
                 .Replace("$DaysHeldBillable", _impound.DaysHeldBillable.ToString())
                 .Replace("$AuctionDateLegal", string.Format(new DateAsLegal(), "{0}", attributes.ContainsKey("Auction Date") ? attributes["Auction Date"] : ""))
                 .Replace("$LoadedMiles", (loadedInvoiceItem != null ? loadedInvoiceItem.Quantity.ToString() : String.Empty))
                 .Replace("$PaymentTotal", paymentTotal.ToString("C"))
                 .Replace("$BalanceDue", _impound.DispatchEntry.BalanceDue.ToString("C")));


            }
            catch (Exception er)
            {
                var error = new HttpResponseMessage();
                var errorText = new StringBuilder();
                errorText.AppendLine("<Pre>");
                errorText.AppendLine(coreAttributes.ToJson());
                errorText.AppendLine(er.ToJson());
                errorText.AppendLine(template.ToJson());
                error.Content = new StringContent(errorText.ToString());
                error.StatusCode = System.Net.HttpStatusCode.InternalServerError;

                throw new HttpResponseException(error);
            }

            sw.Write("</div></body></html>");


            return sw.ToString();
        }

    }
}
