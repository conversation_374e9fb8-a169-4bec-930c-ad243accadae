@page
@using Extric.Towbook.Accounts
@using Extric.Towbook.Generated
@using Extric.Towbook.Web.Pages.Accounts
@using Extric.Towbook.WebShared
@using Newtonsoft.Json
@model AccountEditorModel
@{
    Layout = "_Layout"; /* Use the appropriate shared layout that replaces your old master page */
    ViewData["Title"] = Model.Title;
}

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title>Towbook - Account Editor</title>

    <!-- ALL CSS from your original snippet: -->
    <style>
        .stickerRow input[type="checkbox"] {
            width: auto;
            display: inline-block;
        }

        .stickerRow .Field > label {
            font-weight: normal;
            color: unset;
            font-size: 14px;
            float: none;
            margin-right: 0px;
            width: 200px;
        }

        .list label {
            position: relative;
            float: left;
            width: 280px;
            margin-right: 20px;
        }
        .list input[type="text"] {
            width: 200px;
            margin: 0;
            padding: 5px;
        }
        .list select {
            width: 200px;
            margin: 0;
        }

        .innerRow td {
            width: 260px;
        }
        .innerRow td ~ td {
            width: auto;
        }
        
        .innerRow table {
            width: 100%;
        }
        .innerRow:hover td:first-of-type {
            background-color: white;
        }
        .innerRow:hover td:first-of-type table tr td {
            background-color: white;
        }
        .innerRow:hover td:first-of-type table tr:hover td {
            background-color: #efefef;
        }
        
        .innerRow table tr td:first-of-type label {
            width: 260px;
            margin-right: 9px;
        }

        .innerRow table tr td:not(:first-of-type) {
            width: 260px;
        }
        .innerRow table tr td:last-of-type {
            width: auto;
        }
        
        td .fa-exclamation,
        td .fa-check {
            display: none;
            padding-left: 6px;
            padding-right: 6px;
        }
        td.invalid .fa-exclamation {
            display: inline-block;
            font-size: 18px;
        }
        td.valid .fa-check {
            display: inline-block;
            font-size: 18px;
        }
        #propertyCodeMessage {
            display: none;
        }
        #propertyCodeMessage a {
            display: inline-block;
        }
        td.invalid #propertyCodeMessage {
            color: red;
        }

        .editor.address-minimal .Title label {
            font-weight: normal;
        }

        textarea::-webkit-input-placeholder {
            color: #999;
        }
        textarea:-moz-placeholder {
            color: #999;
            opacity: 1;
        }
        textarea::-moz-placeholder {
            color: #999;
            opacity: 1;
        }
        textarea:-ms-input-placeholder {
            color: #999;
        }
        textarea::-ms-input-placeholder {
            color: #999;
        }

        .flex-section {
            display: flex;
            flex-direction: column;
            row-gap: 10px;
        }
        .flex-item {
            display: flex;
            flex-direction: row;
            width: 600px;
        }
        .uniform-label {
            display: flex;
            flex: 2;
        }
        .required-field-selector {
            display: flex;
            flex: 3;
            padding: 5px;
        }

        .fa-asterisk {
            font-size: .6em;
            padding-left: 3px;
        }
        #footnote {
            padding-left: 5px
        }
        #footnote .fa-asterisk {
            padding-right: 5px;
            position: relative;
            top: -3px;
        }
        
        .block-label {
            display: block;
            color: #2B75BE;
            padding-left: 5px;
            padding-top: 10px;
            font-weight: bold;
            font-family: Calibri, Arial;
            font-size: 12px
        }

        .more-info {
            color: #60a8ef;
            cursor: pointer;
            padding-left: 10px;
            padding-top: 5px;
            font-weight: bold;
        }

        .dropdown-disabled {
            background-color: #e9ecef;
            color: #6c757d;
            pointer-events: none;
            cursor: not-allowed;
        }

        #tabStorageRates input {
            border: solid 1px #afafaf
        }
        #stdRates input {
            width: 60px
        }
        .cbx * {
            width: auto;
            border: none
        }
        .cbx input {
            width: auto;
            border: none;
            display: inline
        }
        .cbx label {
            display: inline
        }
        .TabList li {
            font-size: 17px
        }
        #x-rate-item-list {
            padding-top: 10px
        }
        .address-minimal td {
            padding-top: 0 !important;
        }
        #rules li {
            cursor: pointer;
            padding: 10px;
        }
        #rules li:hover {
            background-color: #efefef;
        }

        #rules li strong {
            display: block;
            font-size: 18px;
            font-family: segoe ui light, "Open Sans"
        }

        #externalLink {
            display: block;
            padding: 10px;
            width: 100%
        }

        #content-advancedOptions span {
            display: block;
            margin-top: 5px;
            margin-bottom: 5px
        }

        .stickerRow > td > span {
            line-height: 36px;
        }
        .stickerRow > td > input[type="checkbox"] {
            top: 1px;
            position: relative;
        }
        .stickerRow > td > input[type="text"] {
            padding: 6px;
        }
    </style>
    
    <!-- Add the towbook library and jQuery dependency -->
    <script src="~/lib/jquery/jquery.min.js"></script>
    <script src="~/lib/jquery-blockui/jquery.blockui.min.js"></script>
    <script src="~/js/towbook.js"></script>
    
    <!-- Add JSON data initialization -->
    <script>
        var towbook = towbook || {};
        towbook.rateItems = @Html.Raw((Model.RateItemsJson));
        towbook.reasons = @Html.Raw((Model.ReasonsJson)); 
        towbook.bodyTypes = @Html.Raw((Model.BodyTypesJson));
        var rules = @Html.Raw((Model.RulesJson));
        
        var originalState = @Html.Raw(JsonConvert.SerializeObject(new { 
            motorClubVisible = Model.motorClubVisible,
            taxIdVisible = Model.mcTaxIdVisible,
            locationIdVisible = Model.mcLocationIdVisible,
            showTowbook = false,
            showUsername = Model.mcUsernameVisible,
            showPassword = Model.mcPasswordVisible
        }));

        // Predefined items to exclude
        const predefinedToSkip = @Html.Raw(Model.PredefinedRateItemsToExclude);
    </script>

    <script>
        // Global variables
        var accountId = '@Model.AccountId';
        var masterAccountId = '@(Model.MasterAccountDetail?.Id ?? 0)';
        var masterAccountName = '@(Model.MasterAccountDetail?.Name ?? "Motor Club")';
        var infoChanged = false;
        var waitForId = [];
        var parkingPermitPublicLinkEnabled = @(Model.ParkingPermitPublicLinkDetail != null && !Model.ParkingPermitPublicLinkDetail.Disabled ? "true" : "false");
        var parkingPermitUrl = "@(Model.ParkingPermitPublicLinkDetail != null && !string.IsNullOrEmpty(Model.ParkingPermitPublicLinkDetail.Url) ? Model.ParkingPermitPublicLinkDetail.Url : string.Empty)";
        var propertyCode = "@(Model.ParkingPermitPublicLinkDetail != null && !string.IsNullOrEmpty(Model.ParkingPermitPublicLinkDetail.PropertyCode) ? Model.ParkingPermitPublicLinkDetail.PropertyCode : string.Empty)";
        var autoAcceptJson = null;
        
        var state = {
            motorClubVisible: @(Model.motorClubVisible.ToString().ToLower()),
        };

        // Update state function
        function updateState(newState) {
            state = {...state, ...newState};
            
            // Handle motor club visibility
            if (state.motorClubVisible) {
                $('#motorClub').show();
            } else {
                $('#motorClub').hide();
            }
        }

        // Company change handler
        function onCompanyChange() {
            if ($('#ddlType').val() == '@((int)AccountType.MotorClub)') {
                updateState({ motorClubVisible: true });
            } else {
                updateState({ motorClubVisible: false });
            }
        }

        // Toggle fields function
        function toggleFields(header, container) {
            if ($(container).is(':visible')) {
                $(container).hide();
                $(header).removeClass('open');
            } else {
                $(container).show();
                $(header).addClass('open');
            }
        }

        // First letter to uppercase
        function firstToUpperCase(str) {
            return str.substr(0, 1).toUpperCase() + str.substr(1);
        }

        // Motor Club Login functionality
        function doLogin() {
            if (!isValidMotorClub()) return false;
            
            $.ajax({
                url: '/api/accounts/' + accountId + '/billinglogin',
                type: 'POST',
                data: {
                    username: $('#billingUsername').val(),
                    password: $('#billingPassword').val(),
                    __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val()
                },
                beforeSend: showLoginMessage,
                success: handleLoginSuccess,
                error: handleLoginError
            });
            return true;
        }

        // Handle motor club login response from Pusher
        function handle_login_response(data) {
            var foundJob = waitForId.filter(function(w) { return w.jobId == data.jobId })[0];
            if (foundJob) {
                $('#mcLoginMessageText').text(data.message);
                console.log('login response: ', data);
                if (data.success === true) {
                    infoChanged = false;
                    var input = $("<input>")
                       .attr("type", "hidden")
                       .attr("name", "btnSave").val("Save Changes");

                    $('form').append($(input));
                    $('form').submit();
                    return;
                }
                else {
                    setTimeout(function() {
                        $.unblockUI();                        
                    }, 1500);
                }
            }
        }

        // COD account functionality
        function setCODPaidInFullDLL() {
            if ($('#cbCodAccount').is(':checked')) {
                $('#codSettingsWrapper').show();
            } else {
                $('#codSettingsWrapper').hide();
            }
        }
        
        // Tab switching functionality
        function switchTab(tabName) {
            $('.TabList>li').each(function () {
                $(this).removeClass('selected');
            });
            
            $('.TabContainer').hide();
            
            $('#tab' + firstToUpperCase(tabName)).show();
            $('#li' + firstToUpperCase(tabName)).addClass('selected');
            
            if (window.History && window.History.pushState) {
                var state = {};
                state.tab = tabName;
                
                // If already have a tab in URL, just replace it
                if (window.location.href.indexOf('?tab=') > -1 || window.location.href.indexOf('&tab=') > -1) {
                    History.replaceState(state, document.title, '?tab=' + tabName);
                } else if (window.location.href.indexOf('?') > -1) {
                    // Has other query params, so append
                    History.replaceState(state, document.title, window.location.href + '&tab=' + tabName);
                } else {
                    // No query params, so add tab as first param
                    History.replaceState(state, document.title, window.location.href + '?tab=' + tabName);
                }
            }
        }

        // Address autofill functionality
        function setUsePhysicalAddressAs() {
            if ($('#cbUsePhysicalAddressAs').is(':checked')) {
                $('.billingAddressGroup').hide();
                $('#cbUsePhysicalAddressAs').parent().show();
            } else {
                $('.billingAddressGroup').show();
            }
        }
        
        function setAutoFillContact() {
            if ($('#cbAutoFillContact').is(':checked')) {
                $('.contactInfoGroup').hide();
                $('#cbAutoFillContact').parent().show();
            } else {
                $('.contactInfoGroup').show();
            }
        }

        // Account type change handler
        function accountTypeChangeHandler() {
            var accountTypeId = $('#ddlType').val();
            
            // Show/hide fields based on account type
            if (accountTypeId == '@((int)AccountType.PrivateProperty)') {
                $('#tabStorageRates').parent().show();
                $('#tabAutoAccept').parent().show();
                $('#tabTow').parent().show();
                $('#tabStickering').parent().show();
                $('#tabPermits').parent().show();
            } else if (accountTypeId == '@((int)AccountType.StorageFacility)') {
                $('#tabStorageRates').parent().show();
                $('#tabAutoAccept').parent().hide();
                $('#tabTow').parent().hide();
                $('#tabStickering').parent().hide();
                $('#tabPermits').parent().hide();
            } else if (accountTypeId == '@((int)AccountType.MotorClub)') {
                $('#motorClub').show();
                $('#tabStorageRates').parent().hide();
                $('#tabAutoAccept').parent().hide();
                $('#tabTow').parent().hide();
                $('#tabStickering').parent().hide();
                $('#tabPermits').parent().hide();
            } else {
                $('#tabStorageRates').parent().hide();
                $('#tabAutoAccept').parent().hide();
                $('#tabTow').parent().hide();
                $('#tabStickering').parent().hide();
                $('#tabPermits').parent().hide();
            }
            
            // Handle required fields based on account type
            if (accountTypeId == '@((int)AccountType.InsuranceCompany)') {
                $('#requireEmail').text('*').parent().addClass('required');
            } else {
                $('#requireEmail').text('').parent().removeClass('required');
            }
            
            // Hide/show roadside field
            if (accountTypeId == '@((int)AccountType.PrivateProperty)' || 
                accountTypeId == '@((int)AccountType.StorageFacility)') {
                $('.labelRoadside').hide();
            } else {
                $('.labelRoadside').show();
            }
        }

        // Impound lots functionality
        function impounds_changePg(pg) {
            $('#tabImpoundLots_company').hide();
            $('#tabImpoundLots_other').hide();
            
            if (pg === 'company') {
                $('#tabImpoundLots_company').show();
            } else if (pg === 'other') {
                $('#tabImpoundLots_other').show();
            }
        }

        // Parking permit fee management
        function refreshParkingFees() {
            var residentSpaces = parseInt($('#tbNumberOfParkingSpaces').val()) || 1;
            var guestSpaces = parseInt($('#tbNumberOfGuests').val()) || 0;
            
            // Get the fees table
            var table = $('#permitFeesRow').find('table');
            table.empty();
            
            // Add resident fee rows
            for (var i = 0; i < residentSpaces; i++) {
                var row = $('<tr class="residentRow"></tr>');
                row.append('<td><label>Resident permit #' + (i + 1) + ' fee ($)</label></td>');
                row.append('<td><input type="text" class="x-fee" value="0.00" /></td>');
                table.append(row);
            }
            
            // Add guest fee rows if any
            for (var j = 0; j < guestSpaces; j++) {
                var guestRow = $('<tr class="guestRow"></tr>');
                guestRow.append('<td><label>Guest permit #' + (j + 1) + ' fee ($)</label></td>');
                guestRow.append('<td><input type="text" class="x-fee" value="0.00" /></td>');
                table.append(guestRow);
            }
            
            // Re-attach event handlers
            $('#permitFeesRow').find('.x-fee').on('blur', syncFees);
            
            // Show fee section if enabled
            if ($('#ddlPermitFees').val() === 'true') {
                $('#permitFeesRow').show();
            } else {
                $('#permitFeesRow').hide();
            }
        }
        
        function syncFees() {
            // Collect fees data
            var data = [];
            
            // Get resident fees
            $('#permitFeesRow').find('.residentRow').each(function (i, row) {
                var fee = parseFloat($(row).find('.x-fee').val()) || 0;
                data.push({
                    id: 0,
                    amount: fee,
                    feeNumber: i + 1,
                    isGuest: false
                });
            });
            
            // Get guest fees
            $('#permitFeesRow').find('.guestRow').each(function (i, row) {
                var fee = parseFloat($(row).find('.x-fee').val()) || 0;
                data.push({
                    id: 0,
                    amount: fee,
                    feeNumber: i + 1,
                    isGuest: true
                });
            });
            
            return data;
        }
        
        function savePermitFees() {
            var data = syncFees();
            
            // Clear existing fees first
            $.ajax({ 
                url: '/api/accounts/' + accountId + '/parkingPermitFees', 
                type: 'DELETE'
            }).done(function () {
                // Then save new fees if we have any
                if (data.length > 0) {
                    $.ajax({
                        url: '/api/accounts/' + accountId + '/parkingPermitFees',
                        type: 'POST',
                        contentType: 'application/json',
                        data: JSON.stringify(data)
                    }).done(function (data) {
                        console.log('Fees saved', data);
                    });
                }
            });
        }

        // Public link functionality
        function checkPropertyCode() {
            var code = $('#tbPropertyCode').val();
            var originalCode = $('#hiddenPropertyCode').val();
            
            // Only check if code was changed
            if (code != originalCode && code && code.trim().length > 0) {
                var pc = encodeURIComponent(code);
                $.ajax({
                    url: "/api/accounts/" + accountId + "/parkingPermitPublicLinks/search?propertyCode=" + pc,
                    type: 'GET'
                }).done(function (data) {
                    if (data && data.length > 0) {
                        $('#propertyCodeMessage').show();
                        $('#propertyCodeMessage').find('.message').text("This property code is already in use by another property. Please choose a different code.");
                        $('#propertyCodeMessage').parent().addClass('invalid').removeClass('valid');
                    } else {
                        $('#propertyCodeMessage').show();
                        $('#propertyCodeMessage').find('.message').text("Property code is available.");
                        $('#propertyCodeMessage').parent().addClass('valid').removeClass('invalid');
                    }
                }).error(function (data) {
                    console.error("Error checking property code", data);
                });
            }
        }
        
        function togglePublicLink() {
            var enabled = $("#ddlPublicLinkEnable").val() === "true";
            
            if (enabled) {
                $('#publicLinkRow').show();
                
                // If not already enabled, create a link
                if (!parkingPermitPublicLinkEnabled) {
                    $.ajax({
                        url: '/api/accounts/' + accountId + '/parkingPermitPublicLinks',
                        type: 'POST',
                        data: { propertyCode: $('#tbPropertyCode').val() }
                    }).done(function (data) {
                        parkingPermitPublicLinkEnabled = true;
                        parkingPermitUrl = data.url;
                        $('#publicLinkUrl').html('<a href="' + data.url + '">' + data.url + '</a>');
                    });
                }
            } else {
                $('#publicLinkRow').hide();
                
                // If previously enabled, disable the link
                if (parkingPermitPublicLinkEnabled) {
                    $.ajax({
                        url: '/api/accounts/' + accountId + '/parkingPermitPublicLinks/disable',
                        type: 'POST'
                    }).done(function (data) {
                        parkingPermitPublicLinkEnabled = false;
                    });
                }
            }
        }

        // Auto-accept rules functionality
        function getAutoAcceptDataToSave() {
            var rules = [];
            $(".x-autoaccept-rule").each(function (i, o) {
                var $this = $(o);
                var rule = {
                    id: $this.data('id') || 0,
                    zoneId: $this.find('.x-zone').val(),
                    minimumMiles: parseFloat($this.find('.x-min').val()) || 0,
                    maximumMiles: parseFloat($this.find('.x-max').val()) || 0
                };
                rules.push(rule);
            });
            
            if (rules.length > 0) {
                return JSON.stringify(rules);
            }
            return null;
        }
        
        // Rule editing functions
        function showRuleEditor(id) {
            $('#rule-id').val(id || 0);
            $('#rule-title').text(id ? 'Edit Pricing Rule' : 'Add Pricing Rule');
            $('#ruleErrorBox').hide();
            
            if (id) {
                $.ajax({
                    url: '/api/accounts/' + accountId + '/rules/' + id,
                    type: 'GET'
                }).done(function (data) {
                    $('#rule-description').val(data.description);
                    $('#rule-amount').val(data.amount);
                    $('#rule-type').val(data.type);
                    $.blockUI({ message: $('#geoSelect') });
                });
            } else {
                $('#rule-description').val('');
                $('#rule-amount').val('');
                $('#rule-type').val('Flat');
                $.blockUI({ message: $('#geoSelect') });
            }
        }
        
        function addRule(data) {
            var output = $('<li></li>').data('id', data.id);
            output.html('<strong>' + data.description + '</strong><br />$' + data.amount + ' ' + data.type);
            output.on('click', function() {
                showRuleEditor(data.id);
            });
            
            $('#rules').append(output);
        }
        
        // Enable external link functionality
        function toggleExternalLink() {
            var enabled = $('#enableLink').is(':checked');
            if (enabled) {
                $.ajax({
                    url: '/api/accounts/' + accountId + '/externallink/enable',
                    type: 'POST'
                }).done(function (data) {
                    $('#externalLink').val(data.url);
                });
            } else {
                $.ajax({
                    url: '/api/accounts/' + accountId + '/externallink/disable',
                    type: 'POST'
                }).done(function () {
                    $('#externalLink').val('');
                });
            }
        }

        // Rate list functionality
        function loadRateList(showAll) {
            $('#x-rate-item-list').load('/api/accounts/' + accountId + '/rates?showAll=' + showAll, function() {
                Towbook.applyUIBehaviour("#x-rate-item-list");
            });
        }

        function initializeRateList() {
            // Load initial rate list
            loadRateList(false);

            // Set up rate list event handlers
            $('.x-showAllRateItems').click(function(e) {
                e.preventDefault();
                loadRateList(true);
            });

            $('.x-showAdjustedRateItems').click(function(e) {
                e.preventDefault();
                loadRateList(false);
            });
        }

        // Auto-accept rules functionality
        function initializeAutoAcceptRules() {
            if (!autoAcceptJson) return;

            try {
                var rules = JSON.parse(autoAcceptJson);
                var $container = $('#autoRespondRules');
                $container.empty();

                rules.forEach(function(rule) {
                    var $row = $('<li class="x-autoaccept-rule">').data('id', rule.id || 0);
                    
                    var $zipInput = $('<input type="text" class="x-zip" placeholder="ZIP Code">')
                        .val(rule.zip || '');
                    
                    var $etaInput = $('<input type="text" class="x-eta" placeholder="ETA (minutes)">')
                        .val(rule.eta || '');
                    
                    var $deleteBtn = $('<button type="button">').text('Delete')
                        .click(function() {
                            $row.remove();
                        });

                    $row.append($zipInput, ' ETA: ', $etaInput, ' minutes ', $deleteBtn);
                    $container.append($row);
                });

                // Add new rule button
                var $addBtn = $('<button type="button">').text('Add Rule')
                    .click(function() {
                        var $newRow = $('<li class="x-autoaccept-rule">');
                        
                        var $zipInput = $('<input type="text" class="x-zip" placeholder="ZIP Code">');
                        var $etaInput = $('<input type="text" class="x-eta" placeholder="ETA (minutes)">');
                        
                        var $deleteBtn = $('<button type="button">').text('Delete')
                            .click(function() {
                                $newRow.remove();
                            });

                        $newRow.append($zipInput, ' ETA: ', $etaInput, ' minutes ', $deleteBtn);
                        $container.append($newRow);
                    });

                $container.after($addBtn);
            } catch (e) {
                console.error('Error parsing auto-accept rules:', e);
            }
        }

        // Motor club login helper functions
        function isValidMotorClub() {
            if (!$('#billingUsername').val()) {
                alert('Please enter a username');
                return false;
            }
            if (!$('#billingPassword').val()) {
                alert('Please enter a password');
                return false;
            }
            return true;
        }

        function showLoginMessage() {
            $.blockUI({ 
                message: $('#mcLoginMessageBox'),
                css: { 
                    border: 'none', 
                    padding: '15px', 
                    backgroundColor: '#000', 
                    '-webkit-border-radius': '10px', 
                    '-moz-border-radius': '10px', 
                    opacity: .5, 
                    color: '#fff' 
                }
            });
        }

        function handleLoginSuccess(data) {
            if (data && data.jobId) {
                waitForId.push({ jobId: data.jobId });
            } else {
                $.unblockUI();
                alert('Login failed - invalid response from server');
            }
        }

        function handleLoginError(xhr, status, error) {
            $.unblockUI();
            alert('Login failed: ' + error);
        }

        // Auto-copy function for ETAs
        function doCopy() {
            var firstRule = $('.x-autoaccept-rule').first();
            if (firstRule.length === 0) return;

            var eta = firstRule.find('.x-eta').val();
            if (!eta) {
                alert('Please set an ETA value for the first rule before copying');
                return;
            }

            $('.x-autoaccept-rule').each(function() {
                $(this).find('.x-eta').val(eta);
            });
        }

        $(document).ready(function() {
            // Initialize state
            updateState(state);
            
            // Initialize autoAcceptJson
            autoAcceptJson = $('#autoAcceptJson').val();

            // Set up event handlers for motor club login
            if (typeof pusher !== 'undefined') {
                pusher.channel('<EMAIL>')
                    .bind('backgroundStatusUpdate', function (data) { handle_login_response(data) });
            }
            
            // If motor club username or password are changed, note it
            $('#billingUsername, #billingPassword').on('input propertychange paste', function() {
                infoChanged = true;
            });
            
            // When the form is submitted
            $('form').submit(function(e) {
                // If motor club username/password was changed
                if (infoChanged) {
                    // Keep form from submitting, and try motor club login
                    if(doLogin())
                        e.preventDefault();
                }
                
                // For auto-accept rules
                var autoAcceptData = getAutoAcceptDataToSave();
                if (autoAcceptData) {
                    $('#hiddenAutoAcceptRules').val(autoAcceptData);
                }
                
                // Save permit fees before submission if fees are enabled
                if ($('#ddlPermitFees').val() === 'true') {
                    savePermitFees();
                }
            });
            
            // Setup tab switching logic
            History.Adapter.bind(window, 'statechange', function () {
                var state = History.getState();
                if (state.data.tab) {
                    switchTab(state.data.tab);
                }
            });
            
            // Attach click handlers to tabs
            $(".TabList li > a").each(function (index, item) {
                $(item).click(function () {
                    var tabName = $(this).attr("rel");
                    switchTab(tabName);
                    return false;
                });
            });
            
            // Initialize tabs
            var initialTab = getQueryVariable('tab') || 'info';
            switchTab(initialTab);
            
            // Attach event handlers
            $('#ddlType').change(function() {
                onCompanyChange();
                accountTypeChangeHandler();
            });
            
            $('.toggle-header').click(function() {
                toggleFields(this, $(this).siblings('.toggle-container'));
            });
            
            // COD account handlers
            $('#cbCodAccount').change(setCODPaidInFullDLL);
            setCODPaidInFullDLL();
            
            // Address autofill handlers
            $('#cbUsePhysicalAddressAs').change(setUsePhysicalAddressAs);
            setUsePhysicalAddressAs();
            
            $('#cbAutoFillContact').change(setAutoFillContact);
            setAutoFillContact();
            
            // External link handler
            $('#enableLink').change(toggleExternalLink);
            
            // Required field selector
            $('.required-field-selector').on('change', function () {
                var $this = $(this);
                var field = $this.data('field');
                if ($this.prop('checked')) {
                    $('#' + field).addClass('required');
                    $('#' + field + ' .fa-asterisk').show();
                } else {
                    $('#' + field).removeClass('required');
                    $('#' + field + ' .fa-asterisk').hide();
                }
            });
            
            // Stickering handlers
            $('#cbSelectAllReasons').on('click', function () {
                var isChecked = $(this).prop('checked');
                $('.chkboxReason input[type="checkbox"]').prop('checked', isChecked);
            });
            
            $('#ddlManagerApproval').change(function() {
                if ($(this).val() == "1") {
                    $('#managerSignatureRow').show();
                } else {
                    $('#managerSignatureRow').hide();
                }
            });
            
            // Parking permit handlers
            $('#tbNumberOfParkingSpaces, #tbNumberOfGuests').on('keyup', refreshParkingFees);
            $('#ddlPermitFees').change(function() {
                if ($(this).val() === 'true') {
                    $('#permitFeesRow').show();
                } else {
                    $('#permitFeesRow').hide();
                }
            });
            refreshParkingFees();
            
            // Property code handlers
            $('#tbPropertyCode').on('blur', checkPropertyCode);
            $("#ddlPublicLinkEnable").change(togglePublicLink);
            
            // Initialize UI state
            $('#ddlManagerApproval').trigger('change');
            $('#ddlPermitFees').trigger('change');
            accountTypeChangeHandler();
            
            // Rule editor buttons
            $('#add-rule').on('click', function () {
                showRuleEditor();
            });
            
            $('#rule-delete').on('click', function () {
                var id = $('#rule-id').val();
                if (id > 0) {
                    $.ajax({
                        url: '/api/accounts/' + accountId + '/rules/' + id,
                        type: 'DELETE'
                    }).done(function (data) {
                        $('#rules li').filter(function() { return $(this).data('id') == id; }).remove();
                        $.unblockUI();
                    });
                }
            });
            
            $('#rule-save').on('click', function () {
                var id = $('#rule-id').val();
                var desc = $('#rule-description').val();
                var amount = $('#rule-amount').val();
                var type = $('#rule-type').val();
                
                if (!desc || !amount) {
                    $('#ruleErrorBox').show();
                    return;
                }
                
                var data = {
                    id: id,
                    description: desc,
                    amount: amount,
                    type: type
                };
                
                $.ajax({
                    url: '/api/accounts/' + accountId + '/rules',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify(data)
                }).done(function (data) {
                    if (id > 0) {
                        $('#rules li').filter(function() { return $(this).data('id') == id; }).remove();
                    }
                    addRule(data);
                    $.unblockUI();
                }).error(function (xhr, status, error) {
                    alert("Error saving rule: " + error);
                });
            });
            
            $('.block-cancel').on('click', function () {
                $.unblockUI();
            });
            
            // Initialize rate list
            initializeRateList();
            
            // Initialize auto-accept rules if enabled
            if (Model.ShowAutoAccept) {
                initializeAutoAcceptRules();
            }
            
            // Add event handlers for toggling sections
            $('.SubHeading').click(function() {
                var $this = $(this);
                var targetId = $this.attr('id');
                var contentId = targetId.replace('sh', 'div');
                
                toggleFields('#' + targetId, '#' + contentId);
            });
        });
        
        // Helper function to get query string variables
        function getQueryVariable(variable) {
            var query = window.location.search.substring(1);
            var vars = query.split('&');
            for (var i = 0; i < vars.length; i++) {
                var pair = vars[i].split('=');
                if (decodeURIComponent(pair[0]) == variable) {
                    return decodeURIComponent(pair[1]);
                }
            }
            return null;
        }
    </script>
    
    <!-- Add client-side validation scripts -->
    <script src="~/lib/jquery-validation/dist/jquery.validate.min.js"></script>
    <script src="~/lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js"></script>
    <script src="~/lib/jquery.history/jquery.history.js"></script>

    <!-- Add jQuery templates -->
    <script type="text/x-jquery-tmpl" id="t-rule">
        <li data-id="${id}">
            <strong>${rateItem}</strong>
            ${reason}
            <br />
            ${bodyType}    
        </li>
    </script>

    <script type="text/x-jquery-tmpl" id="t-autoAcceptRule">
        <li data-id="${id}" class="x-autoaccept-rule" style="margin-bottom: 2px">
            <input type="text" class="x-zip" placeholder="ZIP Code" maxlength="5" style="max-width:50px" />
            <select class="x-eta">
                <option value="">Don't respond</option>
                <option value="-9999">Reject</option>
                <option value="30">Accept with 30 minute ETA</option>
                <option value="35">Accept with 35 minute ETA</option>
                <option value="37">Accept with 37 minute ETA</option>
                <option value="38">Accept with 38 minute ETA</option>
                <option value="40">Accept with 40 minute ETA</option>
                <option value="41">Accept with 41 minute ETA</option>
                <option value="42">Accept with 42 minute ETA</option>
                <option value="45">Accept with 45 minute ETA</option>
                @if (Model.ShowAutoAccept) {
                    <option value="46">Accept with 46 minute ETA (variable based on time of day)</option>
                    <option value="56">Accept with 56 minute ETA (variable based on time of day)</option>
                }
                <option value="50">Accept with 50 minute ETA</option>
                <option value="55">Accept with 55 minute ETA</option>
                <option value="60">Accept with 60 minute ETA</option>
                <option value="65">Accept with 65 minute ETA</option>
                <option value="70">Accept with 70 minute ETA</option>
                <option value="75">Accept with 75 minute ETA</option>
                <option value="80">Accept with 80 minute ETA</option>
                <option value="90">Accept with 90 minute ETA</option>
            </select>
        </li>
    </script>

    <script type="text/x-jquery-tmpl" id="t-digitaldispatch-location">
        <li data-id="${id}" class="x-digitaldispatch-location" style="margin-bottom: 2px">
            <span>${name}</span>
            <select class="x-location-company"></select>
            <select class="x-location-account"></select>
        </li>
    </script>
</head>

<body>
    <form method="post" asp-page="./AccountEditor" asp-antiforgery="true">
        @Html.AntiForgeryToken()
        <input type="hidden" asp-for="AccountId" />
        
        <!-- Add state management hidden fields -->
        <input type="hidden" asp-for="RateItemsJson" />
        <input type="hidden" asp-for="ReasonsJson" />
        <input type="hidden" asp-for="BodyTypesJson" />
        <input type="hidden" asp-for="RulesJson" />
        <input type="hidden" asp-for="AutoAcceptJson" />
        <input type="hidden" asp-for="DigitalLocationsJson" />
        <input type="hidden" asp-for="PredefinedRateItemsToExclude" />
        
        <!-- Add validation summary at the top -->
        <div asp-validation-summary="All" class="text-danger"></div>

        <!-- Add form controls with proper model binding -->
        <div class="form-group">
            <label asp-for="txtCompany">Account Name</label>
            <input asp-for="txtCompany" class="form-control" />
            <span asp-validation-for="txtCompany" class="text-danger"></span>
        </div>

        <div class="form-group">
            <label asp-for="txtContactName">Contact Name</label>
            <input asp-for="txtContactName" class="form-control" />
            <span asp-validation-for="txtContactName" class="text-danger"></span>
        </div>

        <div class="form-group">
            <label asp-for="txtEmail">Email</label>
            <input asp-for="txtEmail" class="form-control" />
            <span asp-validation-for="txtEmail" class="text-danger"></span>
        </div>

        <!-- Add form buttons with proper handler names -->
        <div class="form-group">
            <button type="submit" asp-page-handler="Save" class="btn btn-primary">Save Changes</button>
            <button type="submit" asp-page-handler="Cancel" class="btn btn-secondary">Cancel</button>
            @if(Model.AccountId > 0)
            {
                if(!Model.AccountDetail.Deleted)
                {
                    <button type="submit" asp-page-handler="Delete" class="btn btn-danger"
                            onclick="return confirm('Are you sure you want to delete this account?')">
                        Delete
                    </button>
                }
                else 
                {
                    <button type="submit" asp-page-handler="Undelete" class="btn btn-warning"
                            onclick="return confirm('Are you sure you want to undelete this account?')">
                        Undelete
                    </button>
                }
            }
        </div>

        <!-- Keep existing tab control structure -->
        <div id="tabcontrol1">
            <ul class="TabList">
                <li data-target="tabGeneral" class="selected"><a href="#general">Account</a></li>
                <li data-target="tabServiceRates"><a href="#serviceRates">Pricing</a></li>
                <li data-target="tabAutomaticRules"><a href="#automaticRules">Rules</a></li>
                <li data-target="tabImpoundLots"><a href="#impoundLots">Impounds</a></li>
                @if (WebGlobal.CurrentUser.Company.HasFeature(Extric.Towbook.Generated.Features.DispatchToSubcontractors_SubcontractorRotation))
                {
                    <li data-target="tabRotation"><a href="#rotation">Subcontractor Rotation</a></li>
                }
                <li data-target="tabTags"><a href="#tags">Tags</a></li>
                <li data-target="tabUsers"><a href="#users">Users</a></li>
                @if (WebGlobal.CurrentUser.Company.HasFeature(Extric.Towbook.Generated.Features.Stickering))
                {
                    <li data-target="tabStickering"><a href="#stickering">Stickering</a></li>
                }
                @if (WebGlobal.CurrentUser.Company.HasFeature(Extric.Towbook.Generated.Features.ParkingPermits))
                {
                    <li data-target="tabPermits"><a href="#permits">Permits</a></li>
                }
                <li data-target="tabNotes"><a href="#notes">Notes</a></li>
                @if (Model.AccountId > 0)
                {
                    <li data-target="tabStatements"><a href="#statements">Statements</a></li>
                    <li data-target="tabInvoices"><a href="#invoices">Invoices</a></li>
                }
                <li data-target="tabAdvanced"><a href="#advanced">Advanced</a></li>
                @if (Model.ShowAutoAccept)
                {
                    <li data-target="tabDigitalDispatch"><a href="#advanced">Digital Dispatch Rules</a></li>
                }
            </ul>
        </div>

        <!-- Add hidden container for motor club login messages -->
        <div id="mcLoginMessageBox" class="towbook-popup" style="display:none; padding: 15px;">
            <h2 id="mcLoginMessageText">Logging into motor club...</h2>
        </div>
        
        <!-- Add rule editor dialog -->
        <div id="geoSelect" class="towbook-popup" style="display: none">
            <h2 id="rule-title">Add Pricing Rule</h2>
            <div id="ruleErrorBox" style="color:red; display:none;">
                Please enter both a description and amount.
            </div>
            <ul>
                <li>
                    <input type="hidden" id="rule-id" value="0" />
                    <label for="rule-description">Description</label>
                    <input type="text" id="rule-description" />
                </li>
                <li>
                    <label for="rule-amount">Amount</label>
                    <input type="text" id="rule-amount" />
                </li>
                <li>
                    <label for="rule-type">Type</label>
                    <select id="rule-type">
                        <option value="Flat">Flat Fee</option>
                        <option value="Percentage">Percentage</option>
                    </select>
                </li>
                <li>
                    <button id="rule-save" class="button">Save</button>
                    <button id="rule-delete" class="button">Delete</button>
                    <button class="block-cancel button">Cancel</button>
                </li>
            </ul>
        </div>

        <!-- Impound Lots Section -->
        <div class="form-group">
            <strong>Choose your destination preference</strong>
            <div class="radio">
                <input type="radio" asp-for="ImpoundDestinationType" id="rbStorageLotsNone" 
                       value="@AccountImpoundDestination.None" 
                       onclick="impounds_changePg('none');" />
                <label for="rbStorageLotsNone">None. Let me specify an address...</label>
            </div>
            <div class="radio">
                <input type="radio" asp-for="ImpoundDestinationType" id="rbStorageLotsDefault" 
                       value="@AccountImpoundDestination.Default" 
                       onclick="impounds_changePg('company');" />
                <label for="rbStorageLotsDefault">Always impound vehicles...</label>
            </div>
            <div class="radio">
                <input type="radio" asp-for="ImpoundDestinationType" id="rbStorageLotThirdParty" 
                       value="@AccountImpoundDestination.ThirdParty" 
                       onclick="impounds_changePg('other');" />
                <label for="rbStorageLotThirdParty">This accounts vehicles...</label>
            </div>
        </div>

        <div id="tabImpoundLots_company" class="form-group" 
             style="display:@(Model.ImpoundDestinationType != AccountImpoundDestination.ThirdParty ? "block" : "none")">
            <strong>Default storage facility</strong>
            <select asp-for="ImpoundDestinationStorageLotId" class="form-control"
                    asp-items="@(new SelectList(Model.rpImpoundLotsDataSource, "Id", "Display"))">
            </select>
            <span asp-validation-for="ImpoundDestinationStorageLotId" class="text-danger"></span>
        </div>

        <div id="tabImpoundLots_other" class="form-group"
             style="display:@(Model.ImpoundDestinationType == AccountImpoundDestination.ThirdParty ? "block" : "none")">
            <strong>Third-party Storage Facility</strong>
            <select asp-for="ImpoundDestinationStorageLotId" class="form-control"
                    asp-items="@(new SelectList(Model.ThirdPartyImpoundLots, "Id", "Display"))">
            </select>
            <span asp-validation-for="ImpoundDestinationStorageLotId" class="text-danger"></span>
        </div>

        <!-- Add hidden auto-accept rules field back -->
        <input type="hidden" id="hiddenAutoAcceptRules" asp-for="AutoAcceptRules" />

        <!-- Keep existing tab containers -->
        <div id="tabHistory" class="TabContainer" style="display:none">
            <div class="Overview">
                Information recorded here...
            </div>
            <div class="Padded"></div>
        </div>

        <div id="tabTags" class="TabContainer" style="display:none">
            @if(!WebGlobal.CurrentUser.Company.HasFeature(Features.AccountTags))
            {
                <div style="padding-bottom:10px; font-size:25px; font-weight:bold;">
                    You're missing out on a great feature
                </div>
                <div style="padding-bottom:12px;">
                    Contact our team...
                </div>
            }
            <div class="Overview">
                Tag accounts...
            </div>
            <div class="Padded">
                @if(!WebGlobal.CurrentUser.Company.HasFeature(Features.AccountTags))
                {
                    @foreach(var tag in Model.AllAccountTags)
                    {
                        <input type="checkbox" disabled="disabled" />
                        <span style="color:#777;">@tag.Name</span><br />
                    }
                }
                else
                {
                    @foreach(var tag in Model.AllAccountTags)
                    {
                        bool selected = Model.AccountTagIds.Contains(tag.AccountTagId);
                        <input type="checkbox" name="tag_@(tag.AccountTagId)" id="tag@(tag.AccountTagId)"
                               @(selected?"checked":"") />
                        <label for="tag@(tag.AccountTagId)">@tag.Name</label><br />
                    }
                }
            </div>
        </div>

        @if (WebGlobal.CurrentUser.Company.HasFeature(Features.DispatchToSubcontractors_SubcontractorRotation))
        {
            <div id="tabRotation" class="TabContainer" style="display:none">
                <div class="Overview">
                    Configure which subcontractors...
                </div>
                <div class="Padded">
                    <table>
                        <tr>
                            <td>Light</td>
                            <td>Heavy</td>
                            <td>Subcontractor</td>
                        </tr>
                        @foreach(var sub in Model.SubcontractorRotationItems)
                        {
                            <tr>
                                <td>
                                    <input type="checkbox" name="<EMAIL>"
                                           id="<EMAIL>" 
                                           @(sub.HasLight?"checked":"") />
                                </td>
                                <td>
                                    <input type="checkbox" name="<EMAIL>"
                                           id="<EMAIL>" 
                                           @(sub.HasHeavy?"checked":"") />
                                </td>
                                <td>
                                    <label for="<EMAIL>">@sub.Company</label>
                                </td>
                            </tr>
                        }
                    </table>
                </div>
            </div>
        }

        <div id="tabSharing" class="TabContainer" style="display:none">
            <div class="Overview"></div>
            <div class="Padded">
                Towbook lets you share an accounts impounds...
            </div>
        </div>

        <div id="tabUsers" class="TabContainer" style="display:none">
            <div class="Overview">
                Let people from this account...
            </div>

            <div style="padding-bottom:10px;">
                <p>
                    <input type="checkbox" id="enableLink" name="enableLink" />
                    <label for="enableLink">Enable external link for this account</label>
                </p>
                <p>
                    <input type="text" class="readonly" id="externalLink" readonly />
                </p>
            </div>

            <table id="trCompanyOverride" class="editor" style="margin-bottom:20px; 
                   display:@(Model.trCompanyOverrideVisible ? "table" : "none");">
                <tr>
                    <td class="Title">
                        <label>Assign calls to company</label><br />
                        <select id="ddlCompanyOverride" name="CompanyOverride" style="width:250px">
                            @foreach(var co in Model.ddlCompanyOverrideItems)
                            {
                                <!option value="@co.Value" @(co.Selected ? "selected" : "")>@co.Text</!option>
                            }
                        </select>
                    </td>
                </tr>
            </table>

            <div class="Padded">
                <a href="User.aspx?accountId=@Model.AccountId" id="serLaunch"
                   rel="towbook-dialog">Add a new user</a>

                @if(Model.rpUsersDataSource.Any())
                {
                    <table cellspacing="0" cellpadding="0" width="100%">
                        <tr>
                            <td class="HeaderCell" style="width:80%">User Account</td>
                            <td class="HeaderCell" style="width:20%">Last Login</td>
                        </tr>
                        @foreach(var user in Model.rpUsersDataSource)
                        {
                            <tr>
                                <td class="CellLeft">
                                    <a rel="towbook-dialog" 
                                       href="User.aspx?accountId=@Model.AccountId&id=@user.Id" 
                                       class="account-user">
                                       <span class="img" style="background-image:url(../settings/users/user.png);"></span>
                                       @user.FullName
                                       <br />
                                       <span class="prop1">Username: @user.Username</span>
                                       <br />
                                       <span class="prop">Email: @user.Email</span>
                                       @(user.Disabled ? "Disabled" : "")
                                    </a>
                                </td>
                                <td class="Cell">
                                    @user.LastLogin
                                </td>
                            </tr>
                        }
                    </table>
                }
            </div>
        </div>

        @if (Model.EnableStickering)
        {
            <div id="tabStickering" class="TabContainer" style="display:none">
                <div class="Overview">
                    Select the default settings for this account...
                </div>
                <table class="list">
                    <tbody>
                        <!-- propertyApproval, managerApproval, managerSignature, allowExtensions, waitTime, etc. -->
                        <tr>
                            <td>
                                <label for="ddlManagerApproval">Manager approval required?</label>
                                <select id="ddlManagerApproval" name="ManagerApproval">
                                    @foreach(var bItem in Model.ddlManagerApprovalItems)
                                    {
                                       @: <option value="@bItem.Value" @(bItem.Selected?"selected":"")>@bItem.Text</option>
                                    }
                                </select>
                            </td>
                        </tr>
                        @if (WebGlobal.CurrentUser.Company.HasFeature(Features.SignatureTypes))
                        {
                            <tr>
                                <td>
                                    <label for="ddlManagerSignatureRequired">Manager signature required?</label>
                                    <select id="ddlManagerSignatureRequired" name="ManagerSignatureRequired">
                                        @foreach(var bItem in Model.ddlManagerSignatureRequiredItems)
                                        {
                                           @: <option value="@bItem.Value" @(bItem.Selected?"selected":"")>@bItem.Text</option>
                                        }
                                    </select>
                                </td>
                            </tr>
                        }
                        <tr>
                            <td>
                                <label for="ddlAllowExtensions">Allow Extensions?</label>
                                <select id="ddlAllowExtensions" name="AllowExtensions">
                                    @foreach(var bItem in Model.ddlAllowExtensionsItems)
                                    {
                                       @: <option value="@bItem.Value" @(bItem.Selected?"selected":"")>@bItem.Text</option>
                                    }
                                </select>
                            </td>
                        </tr>
                        <tr id="waitTime">
                            <td>
                                <label for="tbWaitTime">Wait Time (hrs) (standard)</label>
                                <input id="tbWaitTime" name="WaitTime"
                                       value="@Model.tbWaitTime" />
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <label for="tbExpirationTime">Sticker Expiration (hrs) (standard)</label>
                                <input id="tbExpirationTime" name="ExpirationTime"
                                       value="@Model.tbExpirationTime" />
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <label for="ddlSessionActivityEmail">Send an activity email...</label>
                                <select id="ddlSessionActivityEmail" name="SessionActivityEmail">
                                    @foreach(var bItem in Model.ddlSessionActivityEmailItems)
                                    {
                                        @:<option value="@bItem.Value" @(bItem.Selected?"selected":"")>@bItem.Text</option>
                                    }
                                </select>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <label for="ddlDailySummaryEmail">Each morning, send a summary email...</label>
                                <select id="ddlDailySummaryEmail" name="DailySummaryEmail">
                                    @foreach(var bItem in Model.ddlDailySummaryEmailItems)
                                    {
                                        <!option value="@bItem.Value" @(bItem.Selected?"selected":"")>@bItem.Text</!option>
                                    }
                                </select>
                            </td>
                        </tr>
                    </tbody>
                </table>

                <table class="list">
                    <thead>
                        <tr class="stickerRow">
                            <td><label>Reason</label></td>
                            <td><label>Hours</label></td>
                            <td><label>Beginning</label></td>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>
                                <input id="cbSelectAllReasons" type="checkbox" />
                                <span>Select all / Deselect all</span>
                            </td>
                            <td></td>
                            <td></td>
                        </tr>
                        @foreach(var rr in Model.rpStickerReasonsDataSource)
                        {
                            <tr class="list stickerRow">
                                <td class="Field chkboxReason">
                                    <input type="hidden" id="<EMAIL>" value="@rr.Id" />
                                    <input type="checkbox" id="<EMAIL>" name="<EMAIL>"
                                           @(rr.Selected?"checked":"") />
                                    <label for="<EMAIL>">@rr.Name</label>
                                </td>
                                <td class="Field">
                                    <input type="text" id="<EMAIL>" name="<EMAIL>"
                                           placeholder="@rr.DefaultHours" 
                                           value="@rr.Hours" />
                                </td>
                                <td class="Title">
                                    <select id="<EMAIL>" name="<EMAIL>">
                                        @foreach(var gItem in Model.GracePeriodStartItems)
                                        {
                                            <!option value="@gItem.Value" 
                                                    @(gItem.Value == rr.StartFromType.ToString() ? "selected":"")>
                                                @gItem.Text
                                            </!option>
                                        }
                                    </select>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        }

        @if (Model.EnablePermits)
        {
            <div id="tabPermits" class="TabContainer" style="display:none">
                <table class="list">
                    <thead>
                        <tr><td>Default settings for this account</td></tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td class="stickerRow">
                                <label for="tbNumberOfParkingSpaces">
                                    Number of parking spaces per resident?
                                </label>
                                <input id="tbNumberOfParkingSpaces" name="NumberOfParkingSpaces"
                                       class="NumbersDecOnly"
                                       value="@Model.tbNumberOfParkingSpaces" />
                            </td>
                        </tr>
                        <tr>
                            <td class="stickerRow">
                                <label for="ddlExpirationType">Initially set the permit expiration date</label>
                                <select id="ddlExpirationType" name="ExpirationType">
                                    @foreach(var et in Model.ddlExpirationTypeItems)
                                    {
                                        <!option value="@et.Value" @(et.Selected?"selected":"")>@et.Text</!option>
                                    }
                                </select>
                            </td>
                        </tr>
                    </tbody>
                </table>

                <table class="list">
                    <thead>
                        <tr><td>Guest Pass settings</td></tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td class="stickerRow">
                                <label for="tbNumberOfGuests">
                                    Number of guest permits allowed...
                                </label>
                                <input id="tbNumberOfGuests" name="NumberOfGuests" class="NumbersDecOnly"
                                       value="@Model.tbNumberOfGuests" />
                            </td>
                        </tr>
                        <tr>
                            <td class="stickerRow">
                                <label for="ddlGuestExpirationDays">Guest permits expire...</label>
                                <select id="ddlGuestExpirationDays" name="GuestExpirationDays">
                                    @foreach(var dItem in Model.ddlGuestExpirationDaysItems)
                                    {
                                        <!option value="@dItem.Value" @(dItem.Selected?"selected":"")>@dItem.Text</!option>
                                    }
                                </select>
                            </td>
                        </tr>
                        <tr>
                            <td class="stickerRow">
                                <label for="ddlRequireGuestVehicle">Guest permits require vehicle details?</label>
                                <select id="ddlRequireGuestVehicle" name="RequireGuestVehicleInfo">
                                    <!option value="1" @(Model.ddlRequireGuestVehicleSelectedValue=="1"?"selected":"")>Yes</!option>
                                    <!option value="0" @(Model.ddlRequireGuestVehicleSelectedValue=="0"?"selected":"")>No</!option>
                                </select>
                            </td>
                        </tr>
                        <tr>
                            <td class="stickerRow">
                                <label for="ddlAllowGuestPassUpdate">Allow residents to change approved guest passes?</label>
                                <select id="ddlAllowGuestPassUpdate" name="AllowGuestPassUpdate">
                                    <!option value="true" @(Model.ddlAllowGuestPassUpdateSelectedValue=="true"?"selected":"")>Yes</!option>
                                    <!option value="false" @(Model.ddlAllowGuestPassUpdateSelectedValue=="false"?"selected":"")>No</!option>
                                </select>
                            </td>
                        </tr>
                    </tbody>
                </table>

                @if (Model.AccountId > 1)
                {
                    <table class="list">
                        <thead>
                            <tr><td>ParkingPermits.io Settings</td></tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="stickerRow">
                                    <label for="ddlPublicLinkEnable">
                                        Enable website access to apply for permits?
                                    </label>
                                    <select id="ddlPublicLinkEnable" name="PublicLinkEnable">
                                        <!option value="false" @(Model.ddlPublicLinkEnableSelectedValue=="false"?"selected":"")>No</!option>
                                        <!option value="true"  @(Model.ddlPublicLinkEnableSelectedValue=="true" ?"selected":"")>Yes</!option>
                                    </select>
                                </td>
                            </tr>
                            <tr id="publicLinkRow" style="display: none" class="innerRow">
                                <td class="stickerRow">
                                    <table>
                                        <tr class="residentRow">
                                            <td><label>Website link</label></td>
                                            <td><span id="publicLinkUrl"></span></td>
                                        </tr>
                                        <tr class="residentRow">
                                            <td><label>Property Code</label></td>
                                            <td>
                                                <input id="tbPropertyCode" name="PropertyCode" 
                                                       value="@Model.tbPropertyCode" />
                                                <input type="hidden" id="hiddenPropertyCode" 
                                                       value="@Model.hiddenPropertyCode" />
                                                <span id="propertyCodeMessage">
                                                    <i class="fa fa-exclamation"></i>
                                                    <i class="fa fa-check"></i>
                                                    <span class="message"></span>
                                                </span>
                                            </td>
                                        </tr>
                                    </table>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                }

                <table class="list">
                    <thead>
                        <tr><td>Permit fees</td></tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td class="stickerRow">
                                <label for="ddlPermitFees">Charge a fee for each permit for this account?</label>
                                <select id="ddlPermitFees" name="PermitFees">
                                </select>
                            </td>
                        </tr>
                        <tr id="permitFeesRow" style="display:none" class="innerRow">
                            <td class="stickerRow">
                                <table></table>
                            </td>
                        </tr>
                    </tbody>
                </table>

                <table class="list">
                    <thead>
                        <tr><td>Required fields for creating permits</td></tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>
                                <input type="checkbox" id="permitContactEmail" name="PermitContactEmail"
                                       @(Model.permitContactEmailChecked ? "checked" : "") />
                                <label for="permitContactEmail">Contact email</label>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <input type="checkbox" id="permitContactAddress" name="PermitContactAddress"
                                       @(Model.permitContactAddressChecked ? "checked" : "") />
                                <label for="permitContactAddress">Contact Address</label>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <input type="checkbox" id="permitVehicleColor" name="PermitVehicleColor"
                                       @(Model.permitVehicleColorChecked ? "checked" : "") />
                                <label for="permitVehicleColor">Vehicle color</label>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <input type="checkbox" id="permitVehicleVin" name="PermitVehicleVin"
                                       @(Model.permitVehicleVinChecked ? "checked" : "") />
                                <label for="permitVehicleVin">Vin</label>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <input type="checkbox" id="permitStateRegistration" name="PermitStateRegistration"
                                       @(Model.permitStateRegistrationChecked ? "checked" : "") />
                                <label for="permitStateRegistration">State Registration number</label>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <input type="checkbox" id="permitStateRegistrationExpiration" name="PermitStateRegistrationExpiration"
                                       @(Model.permitStateRegistrationExpirationChecked ? "checked" : "") />
                                <label for="permitStateRegistrationExpiration">State Registration expiration date</label>
                            </td>
                        </tr>
                    </tbody>
                </table>

                <table class="list">
                    <thead>
                        <tr><td>Property Terms and Conditions/Disclaimer</td></tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>
                                <textarea id="tbDisclaimer" name="ParkingPermitDisclaimer" rows="5">@Model.tbDisclaimer</textarea>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        }

        <div id="tabServiceRates" class="TabContainer" style="display:none">
            <div class="Overview">
                Specify special pricing for this account here. When dispatch calls are entered for this customer or vehicles are impounded for this account, they will automatically use these rates instead of your standard company rates. To specify a custom rate, click on the item.
            </div>
            
            <div class="SubHeading" id="shServiceRates_RateList" onclick="toggleFields('shServiceRates_RateList', 'divServiceRates_RateList');">
                Service Rates - Setup custom rates for this account
            </div>   
            <div class="Padded" id="divServiceRates_RateList">
                <strong></strong> Set them up below. <br /><br />
                @if (Model.AccountId < 1) {
                    <text>Before you can enter service item rates, please click Save Changes below. After clicking Save Changes, this tab will be available.</text>
                } else {
                    <text>
                    - <a href="#" class="x-showAllRateItems">Show all rates (regardless of whether they've been adjusted for this account)</a><br />
                    - <a href="#" class="x-showAdjustedRateItems">Show only rates that've been adjusted for this account</a><br />
                    <a href="RateItem.aspx" id="aRateItemLauncher" rel="towbook-dialog"></a>
                    <div id="x-rate-item-list"></div>
                    </text>
                }
            </div>

            <div class="SubHeading" id="shServiceRates_Discount" onclick="toggleFields('shServiceRates_Discount', 'divServiceRates_Discount');">
                Flat Discount Rate
            </div>
            <div id="divServiceRates_Discount" style="margin: 10px@(Model.DiscountRate == 0 ? "; display: none" : "")">
                Towbook lets you specify a discount rate to discount the charges for this account by the specified percentage.<br /><br />
                <div>        
                    Discount Rate: <span style="border: solid 1px #afafaf; padding: 3px 5px; margin-top: 5px" onclick="$('#discountRate').focus()">
                        <input asp-for="DiscountRate" style="border: none; color: gray; width: 50px; margin: 0; text-align: right" 
                               maxlength="5" class="NumbersDecOnly" placeholder="(no rate specified)" />%
                    </span><br /><br />
                    - Note: If you don't want the discount to apply to one or more of your service items for this account, you can click on the item above and check the box to not discount that particular item.<br />
                </div>
            </div>

            <div class="SubHeading" id="shServiceRates_StorageRateLimits" onclick="toggleFields('shServiceRates_StorageRateLimits', 'divServiceRates_StorageRateLimits');">
                Storage Rate Limits
            </div>
            <div id="divServiceRates_StorageRateLimits" style="margin: 10px@(!Model.IsFuelSurchargeEnabled ? "; display: none" : "")">
                <table>
                    <tr>
                        <td style="width: 240px">Limit daily storage rates to a maximum of:</td>
                        <td>
                            <input asp-for="MaximumCharges" style="border: solid 1px #afafaf; width: 50px" class="NumbersDecOnly" />
                            (per invoice; Leave blank if you don't have a limit)
                        </td>
                    </tr>
                </table>
            </div>

            <div class="SubHeading" id="shServiceRates_FuelSurcharge" onclick="toggleFields('shServiceRates_FuelSurcharge', 'divServiceRates_FuelSurcharge');">
                Fuel Surcharges
            </div>
            <div id="divServiceRates_FuelSurcharge" style="@(!Model.IsFuelSurchargeEnabled ? "display: none" : "")">
                <div style="padding: 10px">
                    Fuel Surcharge Rate: <span style="border: solid 1px #afafaf; padding: 3px 5px; margin-top: 5px" onclick="$('#fuelSurcharge').focus()">
                        <input asp-for="FuelSurcharge" style="border: none; color: gray; width: 50px; margin: 0; text-align: right" 
                               maxlength="5" class="NumbersDecOnly" />%
                    </span><br /><br />
                    <span>@Html.Raw(Model.FuelSurchargeNoteText)</span>
                </div>
            </div>
        </div>

        <div id="tabNotes" class="TabContainer" style="display:none">
            <div class="Overview">
                Record any notes that you want to keep regarding this account. These notes are only visible to your employees.
            </div>
            <div class="Padded">
                <textarea asp-for="txtNotes" style="width: 600px; height: 300px"></textarea>
            </div>
        </div>

        <div id="tabStatements" class="TabContainer" style="display:none">
            <div class="Overview">
                Create a disclaimer that will be shown on statements created for this account.
            </div>
            <div class="Padded">
                <strong>Disclaimer</strong>
                <textarea asp-for="StatementDisclaimer" class="tb-textarea" style="width: 800px; height: 200px" 
                          data-max-length="6000"></textarea>
            </div>
            @if (Model.CompanyHasAdvancedBillingFeature) {
                <div class="Overview">
                    Specify the email text to use when sending a statement to your customer.
                </div>
                <strong>Email</strong>
                <table class="editor">
                    <tbody>
                        <tr>
                            <td class="Title">
                                <label for="StatementSubject">Subject</label>
                                <input asp-for="StatementSubject" style="width: 800px" />
                            </td>
                        </tr>
                        <tr>
                            <td class="Title">
                                <label for="StatementMessage">Message</label>
                                <textarea asp-for="StatementMessage" style="width: 800px; height: 150px"></textarea>
                            </td>
                        </tr>
                    </tbody>
                </table>
            }
        </div>

        <div id="tabInvoices" class="TabContainer" style="display:none">
            <div class="Overview">
                Create a disclaimer that will be shown on invoices belonging to this account.
            </div>
            <div class="Padded">
                <strong>Disclaimer</strong>
                <textarea asp-for="AccountDisclaimer" class="tb-textarea" style="width: 600px; height: 200px" 
                          data-max-length="6000"></textarea>
            </div>
            
            @if (Model.CompanyHasAdvancedBillingFeature) {
                <div class="Overview">
                    Specify the email text to use when sending an invoice to your customer.
                </div>
                <strong>Email</strong>
                <table class="editor">
                    <tbody>
                        <tr>
                            <td class="Title">
                                <label for="InvoiceSubject">Subject</label>
                                <input asp-for="InvoiceSubject" style="width: 800px"/>
                            </td>
                        </tr>
                        <tr>
                            <td class="Title">
                                <label for="InvoiceMessage">Message</label>
                                <textarea asp-for="InvoiceMessage" style="width: 800px; height: 150px"></textarea>
                            </td>
                        </tr>
                    </tbody>
                </table>
            }
        </div>

        @if (Model.ShowAutoAccept) {
            <div id="tabDigitalDispatch" class="TabContainer" style="display:none">
                <strong>Respond to Digital Dispatches automatically:</strong>
                <ul id="autoRespondRules"></ul>
                <a href="#" onclick="doCopy()">Copy ETA's across all zip codes</a>
                <input type="hidden" name="autoAcceptJson" id="autoAcceptJson" value="@System.Net.WebUtility.HtmlEncode(Model.AutoAcceptJson)" />
            </div>
        }

        <div class="TabContainerFooter">
            <div style="clear:both"></div>
            <div style="float:left">
                <button type="submit" name="handler" value="Save" class="button">Save Changes</button>
                <button type="submit" name="handler" value="Cancel" class="button">Cancel</button>
            </div>
            @if(Model.AccountId > 0)
            {
                <div style="float:right">
                    @if(!Model.AccountDetail.Deleted)
                    {
                        <button type="submit" name="handler" value="Delete" class="sm"
                                onclick="return confirm('Are you sure you want to delete this account?')">
                            Delete
                        </button>
                    }
                    else
                    {
                        <button type="submit" name="handler" value="Undelete" class="sm"
                                onclick="return confirm('Are you sure you want to undelete this account?')">
                            Undelete
                        </button>
                    }
                </div>
            }
            &nbsp;
            <div style="clear:both"></div>
        </div>
    </div>

    <!-- Add validation summary -->
    <div asp-validation-summary="All" class="text-danger"></div>
</form>
</body>
</html>
