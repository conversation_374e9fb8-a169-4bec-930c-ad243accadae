using System.Configuration;
using Extric.Towbook.App;
using Extric.Towbook.Configuration;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc.ViewFeatures;

internal class Program
{
    private static void Main(string[] args)
    {
        var builder = WebApplication.CreateBuilder(args);
        builder.Services.AddSingleton<IHtmlGenerator, DefaultHtmlGenerator>();
        builder.Services.AddSystemWebAdapters();
        builder.Services.AddHttpForwarder();

        // Add services to the container.
        builder.Services.AddControllersWithViews();
        builder.Services.AddRazorPages();
        builder.Services.ConfigureCore();
        builder.Services.AddSingleton(config =>
        {
            return new HttpClient(
                new SocketsHttpHandler
                {
                    PooledConnectionLifetime = TimeSpan.FromMinutes(5),
                    PooledConnectionIdleTimeout = TimeSpan.FromSeconds(30)
                }
            );
        });
        builder.Configuration.ConfigureHelper();

        var app = builder.Build();
        
        IHttpContextAccessor httpContextAccessor = app.Services.GetRequiredService<IHttpContextAccessor>();
        IWebHostEnvironment webHostEnvironment = app.Services.GetRequiredService<IWebHostEnvironment>();
        Extric.Towbook.Web.HttpContext.Configure(httpContextAccessor, webHostEnvironment, app.Services);

        

        if (!app.Environment.IsDevelopment())
        {
            //    app.UseHsts();
        }
        app.MapRazorPages();
        //app.UseHttpsRedirection();
        app.UseStaticFiles();
        app.UseRouting();
        app.UseAuthorization();
        app.UseSystemWebAdapters();
        app.UseAppAuthentication();
        app.MapDefaultControllerRoute();
        app.MapForwarder("/{**catch-all}", app.Configuration["ProxyTo"]!).Add(static builder => ((RouteEndpointBuilder)builder).Order = int.MaxValue);

        app.Run();
    }
}
