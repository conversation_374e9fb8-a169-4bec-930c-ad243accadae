diff --git a/Extric.Towbook.API/Controllers/Accounts/ParkingPermitsController.cs b/Extric.Towbook.API/Controllers/Accounts/ParkingPermitsController.cs
index 600f72e6ba..33a98e38c9 100644
--- a/Extric.Towbook.API/Controllers/Accounts/ParkingPermitsController.cs
+++ b/Extric.Towbook.API/Controllers/Accounts/ParkingPermitsController.cs
@@ -33,7 +33,7 @@ namespace Extric.Towbook.API.Accounts.Controllers
             ThrowIfFeatureNotAssigned(Generated.Features.ParkingPermits);
 
             var acc = Account.GetById(accountId);
-            ThrowIfNoCompanyAccess(acc?.CompanyId, "Account");
+            await ThrowIfNoCompanyAccessAsync(acc?.CompanyId, "Account");
 
             Collection<ParkingPermit> ret = null;
 
@@ -96,7 +96,7 @@ namespace Extric.Towbook.API.Accounts.Controllers
             var x = Account.GetById(accountId);
             var d = ParkingPermit.GetById(id);
 
-            ThrowIfNoCompanyAccess(x?.Companies);
+            await ThrowIfNoCompanyAccessAsync(x?.Companies);
             if (d?.AccountId != x.Id)
                 d = null;
 
@@ -122,7 +122,7 @@ namespace Extric.Towbook.API.Accounts.Controllers
                 return null;
                         
             var x = Account.GetById(accountId);
-            ThrowIfNoCompanyAccess(x?.Companies);
+            await ThrowIfNoCompanyAccessAsync(x?.Companies);
             
             var d = ParkingPermit.GetByLicensePlate(accountId, plate); //.Where(o => o.StatusId == PermitStatus.Valid.Id); -- not all valid permits have a status of Valid...
 
@@ -187,7 +187,7 @@ namespace Extric.Towbook.API.Accounts.Controllers
             ThrowIfNotFound(q, "Parking Permit Search Query");
 
             var x = Account.GetById(accountId);
-            ThrowIfNoCompanyAccess(x?.Companies);
+            await ThrowIfNoCompanyAccessAsync(x?.Companies);
 
             var list = ParkingPermit.Find(
                 new int[] { accountId }.ToArray(),
@@ -232,7 +232,7 @@ namespace Extric.Towbook.API.Accounts.Controllers
                 return new Collection<ParkingPermitModel>();
 
             var x = Account.GetById(accountId);
-            ThrowIfNoCompanyAccess(x?.Companies);
+            await ThrowIfNoCompanyAccessAsync(x?.Companies);
 
             if(!Web.HttpContext.Current.Request.Query.ContainsKey("qs") ||
                 string.IsNullOrEmpty(Web.HttpContext.Current.Request.Query["qs"]))
@@ -290,7 +290,7 @@ namespace Extric.Towbook.API.Accounts.Controllers
         {
             var act = Account.GetById(accountId);
 
-            ThrowIfNoCompanyAccess(act?.CompanyId);
+            await ThrowIfNoCompanyAccessAsync(act?.CompanyId);
 
             var pp = ParkingPermitModel.Map(model);
 
@@ -393,7 +393,7 @@ namespace Extric.Towbook.API.Accounts.Controllers
         public async Task<HttpResponseMessage> Revoke(int accountId, int id)
         {
             var a = Account.GetById(accountId);
-            ThrowIfNoCompanyAccess(a?.CompanyId, "permits");
+            await ThrowIfNoCompanyAccessAsync(a?.CompanyId, "permits");
 
             var pp = ParkingPermit.GetById(id);
             if (pp?.AccountId != pp.AccountId)
@@ -417,7 +417,7 @@ namespace Extric.Towbook.API.Accounts.Controllers
         public async Task<HttpResponseMessage> Unrevoke(int accountId, int id)
         {
             var a = Account.GetById(accountId);
-            ThrowIfNoCompanyAccess(a?.CompanyId, "permits");
+            await ThrowIfNoCompanyAccessAsync(a?.CompanyId, "permits");
 
             var pp = ParkingPermit.GetById(id);
 
@@ -442,7 +442,7 @@ namespace Extric.Towbook.API.Accounts.Controllers
         public async Task<HttpResponseMessage> Extend(int accountId, int id, ExtendPermitModel model)
         {
             var a = Account.GetById(accountId);
-            ThrowIfNoCompanyAccess(a?.CompanyId, "permits");
+            await ThrowIfNoCompanyAccessAsync(a?.CompanyId, "permits");
 
             var pp = ParkingPermit.GetById(id);
 
@@ -526,7 +526,7 @@ namespace Extric.Towbook.API.Accounts.Controllers
         public async Task<IEnumerable<AccountParkingPermitHistoryModel>> History(int accountId, int id)
         {
             var acc = Account.GetById(accountId);
-            ThrowIfNoCompanyAccess(acc?.CompanyId, "Account");
+            await ThrowIfNoCompanyAccessAsync(acc?.CompanyId, "Account");
 
             var permit = ParkingPermit.GetById(id);
 
diff --git a/Extric.Towbook.API/Controllers/Auctions/AuctionItemPhotosController.cs b/Extric.Towbook.API/Controllers/Auctions/AuctionItemPhotosController.cs
index abdbb12a5d..0d1fc1fc6f 100644
--- a/Extric.Towbook.API/Controllers/Auctions/AuctionItemPhotosController.cs
+++ b/Extric.Towbook.API/Controllers/Auctions/AuctionItemPhotosController.cs
@@ -36,7 +36,7 @@ namespace Extric.Towbook.API.Auctions.Controllers
 
             var e = await Dispatch.Entry.GetByIdAsync(detail.DispatchEntryId);
 
-            ThrowIfNoCompanyAccess(e?.CompanyId, "Call");
+            await ThrowIfNoCompanyAccessAsync(e?.CompanyId, "Call");
 
             return InternalGetPhotosByAuctionDetails(new EntryAuctionDetail[] { detail }, false);
         }
@@ -124,7 +124,7 @@ namespace Extric.Towbook.API.Auctions.Controllers
             ThrowIfNotFound(detail?.Id, "Auction Details");
 
             var e = await Dispatch.Entry.GetByIdAsync(detail.DispatchEntryId);
-            ThrowIfNoCompanyAccess(e?.CompanyId, "Call");
+            await ThrowIfNoCompanyAccessAsync(e?.CompanyId, "Call");
 
             var auction = Auction.GetById(detail.AuctionId.GetValueOrDefault());
 
@@ -176,7 +176,7 @@ namespace Extric.Towbook.API.Auctions.Controllers
             ThrowIfNotFound(detail?.Id, "Auction Details");
 
             var e = await Dispatch.Entry.GetByIdAsync(detail.DispatchEntryId);
-            ThrowIfNoCompanyAccess(e?.CompanyId, "Call");
+            await ThrowIfNoCompanyAccessAsync(e?.CompanyId, "Call");
 
             var auction = Auction.GetById(detail.AuctionId.GetValueOrDefault());
             ThrowIfNotFound(auction, "Auction");
diff --git a/Extric.Towbook.API/Controllers/Auctions/AuctionItemsController.cs b/Extric.Towbook.API/Controllers/Auctions/AuctionItemsController.cs
index a5d490bcd8..b352f089db 100644
--- a/Extric.Towbook.API/Controllers/Auctions/AuctionItemsController.cs
+++ b/Extric.Towbook.API/Controllers/Auctions/AuctionItemsController.cs
@@ -60,7 +60,7 @@ namespace Extric.Towbook.API.Auctions.Controllers
             ThrowIfFeatureNotAssigned(Generated.Features.Impounds_ImpoundAuctions);
 
             var auction = Auction.GetById(auctionId);
-            ThrowIfNoCompanyAccess(auction?.CompanyId, "Auction");
+            await ThrowIfNoCompanyAccessAsync(auction?.CompanyId, "Auction");
 
             var items = EntryAuctionDetail.GetByAuctionId(auctionId);
 
@@ -90,7 +90,7 @@ namespace Extric.Towbook.API.Auctions.Controllers
             ThrowIfFeatureNotAssigned(Generated.Features.Impounds_ImpoundAuctions);
 
             var auction = Auction.GetById(auctionId);
-            ThrowIfNoCompanyAccess(auction?.CompanyId, "Auction");
+            await ThrowIfNoCompanyAccessAsync(auction?.CompanyId, "Auction");
 
             var detail = EntryAuctionDetail.GetById(id);
             ThrowIfNotFound(detail, "Auction Details");
@@ -105,7 +105,7 @@ namespace Extric.Towbook.API.Auctions.Controllers
             }
 
             var e = await Dispatch.Entry.GetByIdAsync(detail.DispatchEntryId);
-            ThrowIfNoCompanyAccess(e?.CompanyId);
+            await ThrowIfNoCompanyAccessAsync(e?.CompanyId);
 
             var calls = await API.Controllers.CallsController.GetByIds(WebGlobal.Companies.Select(s => s.Id).ToArray(), new int[] { detail.DispatchEntryId }.ToArray());
             var photos = AuctionItemPhotosController.InternalGetPhotosByAuctionDetails(new EntryAuctionDetail[] { detail }.ToArray(), true);
@@ -131,7 +131,7 @@ namespace Extric.Towbook.API.Auctions.Controllers
             ThrowIfNotFound(detail?.Id, "Auction Details");
 
             var e = Dispatch.Entry.GetByIdNoCache(detail.DispatchEntryId);
-            ThrowIfNoCompanyAccess(e?.CompanyId);
+            await ThrowIfNoCompanyAccessAsync(e?.CompanyId);
 
             var imp = Impound.GetByDispatchEntry(e);
             ThrowIfNotFound(imp, "Impound");
@@ -188,7 +188,7 @@ namespace Extric.Towbook.API.Auctions.Controllers
 
             var imp = Impound.GetById(auctionItemModel.StockNumber);
             ThrowIfNotFound(imp?.DispatchEntry?.Id, "Impound");
-            ThrowIfNoCompanyAccess(imp.Company.Id);
+            await ThrowIfNoCompanyAccessAsync(imp.Company.Id);
 
             var detail = AuctionItemModel.Map(auctionItemModel, EntryAuctionDetail.GetByDispatchEntryId(imp.DispatchEntry.Id));
             detail.DispatchEntryId = imp.DispatchEntry.Id;
@@ -292,7 +292,7 @@ namespace Extric.Towbook.API.Auctions.Controllers
             ThrowIfNotFound(detail?.Id, "Auction Details");
 
             var e = Dispatch.Entry.GetByIdNoCache(detail.DispatchEntryId);
-            ThrowIfNoCompanyAccess(e?.CompanyId);
+            await ThrowIfNoCompanyAccessAsync(e?.CompanyId);
 
             var imp = Impound.GetByDispatchEntry(e);
             ThrowIfNotFound(imp, "Impound");
@@ -355,7 +355,7 @@ namespace Extric.Towbook.API.Auctions.Controllers
         private async Task<IEnumerable<AuctionItemModel>> InternalInsertOrUpdateAuctionItems(int auctionId, int[] impoundIds, bool assign = true)
         {
             var auction = Auction.GetById(auctionId);
-            ThrowIfNoCompanyAccess(auction?.CompanyId, "Auction");
+            await ThrowIfNoCompanyAccessAsync(auction?.CompanyId, "Auction");
 
             if (auction.EndDate.HasValue && auction.EndDate < DateTime.Now)
             {
@@ -373,7 +373,7 @@ namespace Extric.Towbook.API.Auctions.Controllers
                 foreach (var iId in impoundIds)
                 {
                     var imp = Impound.GetById(iId);
-                    ThrowIfNoCompanyAccess(imp?.Company?.Id, "Impound");
+                    await ThrowIfNoCompanyAccessAsync(imp?.Company?.Id, "Impound");
 
                     // Don't allow assignment to an auctionId if the impound is a police hold.
                     if (assign && auctionId > 0 && imp.Hold)
diff --git a/Extric.Towbook.API/Controllers/Chat/ChatMessagesController.cs b/Extric.Towbook.API/Controllers/Chat/ChatMessagesController.cs
index 265cf7f53e..47399088fa 100644
--- a/Extric.Towbook.API/Controllers/Chat/ChatMessagesController.cs
+++ b/Extric.Towbook.API/Controllers/Chat/ChatMessagesController.cs
@@ -22,7 +22,7 @@ namespace Extric.Towbook.API.Controllers
         {
             var chat = await Chat.Chat.GetByIdAsync(chatId);
             
-            ThrowIfNoCompanyAccess(chat?.CompanyId, "chat");
+            await ThrowIfNoCompanyAccessAsync(chat?.CompanyId, "chat");
 
             var cms = await ChatMember.GetByChatIdAsync(chat.Id);
 
@@ -64,7 +64,7 @@ namespace Extric.Towbook.API.Controllers
             
             var chat = await Chat.Chat.GetByIdAsync(cm.ChatId);
             
-            ThrowIfNoCompanyAccess(chat.CompanyId);
+            await ThrowIfNoCompanyAccessAsync(chat.CompanyId);
 
 
             return ChatMessageModel.Map(cm);
@@ -90,7 +90,7 @@ namespace Extric.Towbook.API.Controllers
         {
             var chat = await Chat.Chat.GetByIdAsync(chatId);
 
-            ThrowIfNoCompanyAccess(chat?.CompanyId, "chat");
+            await ThrowIfNoCompanyAccessAsync(chat?.CompanyId, "chat");
 
             if (chat == null || chat.Deleted)
             {
diff --git a/Extric.Towbook.API/Controllers/Chat/ChatsController.cs b/Extric.Towbook.API/Controllers/Chat/ChatsController.cs
index 1838eaccaf..62c63c6201 100644
--- a/Extric.Towbook.API/Controllers/Chat/ChatsController.cs
+++ b/Extric.Towbook.API/Controllers/Chat/ChatsController.cs
@@ -53,7 +53,7 @@ namespace Extric.Towbook.API.Controllers
             if (callId != null)
             {
                 var entry = await Dispatch.Entry.GetByIdAsync(callId.Value);
-                ThrowIfNoCompanyAccess(entry?.CompanyId, "chat");
+                await ThrowIfNoCompanyAccessAsync(entry?.CompanyId, "chat");
 
                 var chats = await Chat.Chat.FindByDispatchEntryId(callId.Value);
                 var rv = chats.Select(o => ChatModel.Map(o, 
@@ -161,7 +161,7 @@ namespace Extric.Towbook.API.Controllers
         {
             var chat = await Chat.Chat.GetByIdAsync(id);
 
-            ThrowIfNoCompanyAccess(chat?.CompanyId, "chat");
+            await ThrowIfNoCompanyAccessAsync(chat?.CompanyId, "chat");
 
             var cms = await ChatMember.GetByChatIdAsync(chat.Id);
 
@@ -258,7 +258,7 @@ namespace Extric.Towbook.API.Controllers
         {
             var chat = await Chat.Chat.GetByIdAsync(id);
 
-            ThrowIfNoCompanyAccess(chat?.CompanyId, "chat");
+            await ThrowIfNoCompanyAccessAsync(chat?.CompanyId, "chat");
 
             // return empty list of members if the chat is a Company type chat.  We may want to do something different here....
             if (chat.ChatTypeId == (int)ChatTypes.Company)
@@ -386,7 +386,7 @@ namespace Extric.Towbook.API.Controllers
             }
 
             if (WebGlobal.CurrentUser.Type != Towbook.User.TypeEnum.SystemAdministrator)
-                ThrowIfNoCompanyAccess(chat.CompanyId, "chat");
+                await ThrowIfNoCompanyAccessAsync(chat.CompanyId, "chat");
 
             var deletePermission = CompanyKeyValue.GetFirstValueOrNull(chat.CompanyId, Provider.Towbook.ProviderId, "Permission_Chats_Delete") ?? "0";
             if (deletePermission == "2")
diff --git a/Extric.Towbook.API/Controllers/Dispatch/ActivityLogController.cs b/Extric.Towbook.API/Controllers/Dispatch/ActivityLogController.cs
index 101e426f4b..a81f30772e 100644
--- a/Extric.Towbook.API/Controllers/Dispatch/ActivityLogController.cs
+++ b/Extric.Towbook.API/Controllers/Dispatch/ActivityLogController.cs
@@ -23,7 +23,7 @@ namespace Extric.Towbook.API.Controllers
         {
             var e = await Entry.GetByIdAsync(callId);
 
-            ThrowIfNoCompanyAccess(e?.CompanyId);
+            await ThrowIfNoCompanyAccessAsync(e?.CompanyId);
 
             var r = await ActivityLogCloudService.GetByObject(ActivityLogType.DispatchEntry, callId);
 
diff --git a/Extric.Towbook.API/Controllers/Dispatch/BatchController.cs b/Extric.Towbook.API/Controllers/Dispatch/BatchController.cs
index 1ce8a330e2..********** 100644
--- a/Extric.Towbook.API/Controllers/Dispatch/BatchController.cs
+++ b/Extric.Towbook.API/Controllers/Dispatch/BatchController.cs
@@ -33,7 +33,7 @@ namespace Extric.Towbook.API.Controllers
             var calls = Entry.GetByIds(model.Calls, null, false);
 
             foreach (var x in calls)
-                ThrowIfNoCompanyAccess(x?.CompanyId, "call");
+                await ThrowIfNoCompanyAccessAsync(x?.CompanyId, "call");
 
             if (model.Calls.Where(o => !calls.Where(c => c.Id == o).Any()).Any())
                 throw new Extric.Towbook.Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest)
diff --git a/Extric.Towbook.API/Controllers/Dispatch/CallValidationController.cs b/Extric.Towbook.API/Controllers/Dispatch/CallValidationController.cs
index 4a6e69d750..cd9b6b1925 100644
--- a/Extric.Towbook.API/Controllers/Dispatch/CallValidationController.cs
+++ b/Extric.Towbook.API/Controllers/Dispatch/CallValidationController.cs
@@ -26,7 +26,7 @@ namespace Extric.Towbook.API.Controllers
         {
             var d = await Entry.GetByIdAsync(callId);
 
-            ThrowIfNoCompanyAccess(d?.CompanyId, "call");
+            await ThrowIfNoCompanyAccessAsync(d?.CompanyId, "call");
 
             if (statusId == null)
                 statusId = Status.Completed.Id;
diff --git a/Extric.Towbook.API/Controllers/Dispatch/CallsController.cs b/Extric.Towbook.API/Controllers/Dispatch/CallsController.cs
index b8bac750a5..e6970e130f 100644
--- a/Extric.Towbook.API/Controllers/Dispatch/CallsController.cs
+++ b/Extric.Towbook.API/Controllers/Dispatch/CallsController.cs
@@ -1175,7 +1175,7 @@ namespace Extric.Towbook.API.Controllers
                 drivers = Driver.GetByUserId(WebGlobal.CurrentUser.Id);
 
             foreach (var c in companyIds)
-                ThrowIfNoCompanyAccess(c, "company");
+                await ThrowIfNoCompanyAccessAsync(c, "company");
 
             var newClient = CosmosDB.Get().Client;
             var rx = newClient.GetDatabase(Core.CosmosDatabase ?? "towbook");
@@ -1309,7 +1309,7 @@ namespace Extric.Towbook.API.Controllers
                 drivers = Driver.GetByUserId(WebGlobal.CurrentUser.Id);
 
             foreach (var c in companyIds)
-                ThrowIfNoCompanyAccess(c, "company");
+                await ThrowIfNoCompanyAccessAsync(c, "company");
 
             var newClient = CosmosDB.Get().Client;
             var rx = newClient.GetDatabase(Core.CosmosDatabase ?? "towbook");
@@ -6808,7 +6808,7 @@ namespace Extric.Towbook.API.Controllers
         {
             var c = await Entry.GetByIdAsync(id);
 
-            ThrowIfNoCompanyAccess(c?.CompanyId, "call");
+            await ThrowIfNoCompanyAccessAsync(c?.CompanyId, "call");
 
             if (CompanyKeyValue.GetFirstValueOrNull(c.CompanyId,
                 Provider.Towbook.ProviderId, "PreventDriversFromCompletingCalls") == "1" &&
@@ -7899,7 +7899,7 @@ namespace Extric.Towbook.API.Controllers
             Entry.CacheClearById(id);
 
             var e = await Entry.GetByIdAsync(id);
-            ThrowIfNoCompanyAccess(e?.CompanyId, "call");
+            await ThrowIfNoCompanyAccessAsync(e?.CompanyId, "call");
 
             if (e.Status.Id == Status.Cancelled.Id)
             {
@@ -8121,7 +8121,7 @@ namespace Extric.Towbook.API.Controllers
         public async Task<HttpResponseMessage> AcknowledgeCancel(int id)
         {
             var e = await Entry.GetByIdAsync(id);
-            ThrowIfNoCompanyAccess(e?.CompanyId, "call");
+            await ThrowIfNoCompanyAccessAsync(e?.CompanyId, "call");
 
             if (e.Attributes.ContainsKey(Dispatch.AttributeValue.BUILTIN_DISPATCH_CANCEL_ACKNOWLEDGEMENT_JSON))
             {
@@ -8161,7 +8161,7 @@ namespace Extric.Towbook.API.Controllers
         public async Task<HttpResponseMessage> AcknowledgeCompletion(int id)
         {
             var e = await Entry.GetByIdAsync(id);
-            ThrowIfNoCompanyAccess(e?.CompanyId, "call");
+            await ThrowIfNoCompanyAccessAsync(e?.CompanyId, "call");
 
             if (e.Attributes.ContainsKey(Dispatch.AttributeValue.BUILTIN_DISPATCH_COMPLETION_ACKNOWLEDGEMENT_JSON))
             {
@@ -8201,7 +8201,7 @@ namespace Extric.Towbook.API.Controllers
         public async Task<HttpResponseMessage> Uncancel(int id)
         {
             var e = await Entry.GetByIdAsync(id);
-            ThrowIfNoCompanyAccess(e?.CompanyId, "call");
+            await ThrowIfNoCompanyAccessAsync(e?.CompanyId, "call");
 
             if (WebGlobal.CurrentUser.Type == Towbook.User.TypeEnum.Dispatcher &&
                 WebGlobal.CurrentUser.Notes != null &&
@@ -8239,7 +8239,7 @@ namespace Extric.Towbook.API.Controllers
         public async Task<HttpResponseMessage> Uncomplete(int id)
         {
             var e = await Entry.GetByIdAsync(id);
-            ThrowIfNoCompanyAccess(e?.CompanyId, "call");
+            await ThrowIfNoCompanyAccessAsync(e?.CompanyId, "call");
 
             if (WebGlobal.CurrentUser.Type == Towbook.User.TypeEnum.AccountUser ||
                 WebGlobal.CurrentUser.Type == Towbook.User.TypeEnum.AccountManager)
@@ -8357,7 +8357,7 @@ namespace Extric.Towbook.API.Controllers
         public async Task<object> ExtendEta(int id, ExtendEtaModel model)
         {
             var en = await Entry.GetByIdAsync(id);
-            ThrowIfNoCompanyAccess(en?.CompanyId, "call");
+            await ThrowIfNoCompanyAccessAsync(en?.CompanyId, "call");
 
             var cr = await CallRequest.GetByDispatchEntryId(en.Id);
             ThrowIfNotFound(cr, "Digital Call Request");
@@ -8477,7 +8477,7 @@ namespace Extric.Towbook.API.Controllers
         public async Task<object> RequestGoa(int id, RequestGoaModel model)
         {
             var en = await Entry.GetByIdAsync(id);
-            ThrowIfNoCompanyAccess(en?.CompanyId, "call");
+            await ThrowIfNoCompanyAccessAsync(en?.CompanyId, "call");
 
             if (WebGlobal.CurrentUser.Type == Towbook.User.TypeEnum.Driver)
             {
@@ -8703,7 +8703,7 @@ namespace Extric.Towbook.API.Controllers
         public async Task<object> ServiceFailure(int id, ServiceFailureModel model)
         {
             var en = await Entry.GetByIdAsync(id);
-            ThrowIfNoCompanyAccess(en?.CompanyId, "call");
+            await ThrowIfNoCompanyAccessAsync(en?.CompanyId, "call");
 
             var cr = await CallRequest.GetByDispatchEntryId(en.Id);
 
@@ -8806,7 +8806,7 @@ namespace Extric.Towbook.API.Controllers
         public async Task<object> DigitalCancel(int id, DigitalCancelModel model)
         {
             var en = await Entry.GetByIdAsync(id);
-            ThrowIfNoCompanyAccess(en?.CompanyId, "call");
+            await ThrowIfNoCompanyAccessAsync(en?.CompanyId, "call");
 
             if (WebGlobal.CurrentUser.Type == Towbook.User.TypeEnum.Driver)
             {
@@ -8951,7 +8951,7 @@ namespace Extric.Towbook.API.Controllers
         public async Task<object> RequestAdditionalService(int id, AdditionalServiceModel model)
         {
             var en = await Entry.GetByIdAsync(id);
-            ThrowIfNoCompanyAccess(en?.CompanyId, "call");
+            await ThrowIfNoCompanyAccessAsync(en?.CompanyId, "call");
 
             var cr = await CallRequest.GetByDispatchEntryId(en.Id);
 
@@ -9028,7 +9028,7 @@ namespace Extric.Towbook.API.Controllers
         public async Task<object> RequestInfo(int id, RequestInfoModel model)
         {
             var en = await Entry.GetByIdAsync(id);
-            ThrowIfNoCompanyAccess(en?.CompanyId, "call");
+            await ThrowIfNoCompanyAccessAsync(en?.CompanyId, "call");
 
             var cr = await CallRequest.GetByDispatchEntryId(en.Id);
 
@@ -9701,7 +9701,7 @@ namespace Extric.Towbook.API.Controllers
 
             var call = await Entry.GetByIdAsync(id);
 
-            ThrowIfNoCompanyAccess(call?.CompanyId, "call");
+            await ThrowIfNoCompanyAccessAsync(call?.CompanyId, "call");
 
             if (!WebGlobal.CurrentUser.HasPermissionToEmailCall(call))
             {
@@ -9795,7 +9795,7 @@ namespace Extric.Towbook.API.Controllers
             var calls = Entry.GetByIds(ids.Calls).ToCollection();
 
             foreach (var call in calls)
-                ThrowIfNoCompanyAccess(call?.CompanyId, "call");
+                await ThrowIfNoCompanyAccessAsync(call?.CompanyId, "call");
 
             var statusCode = HttpStatusCode.OK;
             var token = this.GetCurrentToken();
@@ -9838,7 +9838,7 @@ namespace Extric.Towbook.API.Controllers
         public async Task<HttpResponseMessage> Unlock(int id)
         {
             var call = await Entry.GetByIdAsync(id);
-            ThrowIfNoCompanyAccess(call?.CompanyId, "call");
+            await ThrowIfNoCompanyAccessAsync(call?.CompanyId, "call");
 
             if (!WebGlobal.CurrentUser.HasAccessToUnlockCalls())
                 return new HttpResponseMessage(Forbidden());
@@ -9903,7 +9903,7 @@ namespace Extric.Towbook.API.Controllers
             var calls = Entry.GetByIds(ids.Calls).ToCollection();
 
             foreach (var call in calls)
-                ThrowIfNoCompanyAccess(call?.CompanyId, "call");
+                await ThrowIfNoCompanyAccessAsync(call?.CompanyId, "call");
 
             HttpStatusCode statusCode = HttpStatusCode.OK;
             var token = this.GetCurrentToken();
@@ -9984,7 +9984,7 @@ namespace Extric.Towbook.API.Controllers
         public async Task<HttpResponseMessage> Unaudit(int id)
         {
             var call = await Entry.GetByIdAsync(id);
-            ThrowIfNoCompanyAccess(call?.CompanyId, "call");
+            await ThrowIfNoCompanyAccessAsync(call?.CompanyId, "call");
 
             if (!WebGlobal.CurrentUser.HasAccessToUnauditCalls())
                 return new HttpResponseMessage(Forbidden());
@@ -11784,7 +11784,7 @@ Sent using Towbook
             {
                 var call = await Entry.GetByIdAsync(id);
 
-                ThrowIfNoCompanyAccess(call?.CompanyId, "call");
+                await ThrowIfNoCompanyAccessAsync(call?.CompanyId, "call");
 
                 var nvc = HttpUtility.ParseQueryString(Web.HttpContext.Current.Request.QueryString.Value);
 
@@ -11914,7 +11914,7 @@ Sent using Towbook
         {
             var call = await Entry.GetByIdAsync(id);
 
-            ThrowIfNoCompanyAccess(call?.CompanyId);
+            await ThrowIfNoCompanyAccessAsync(call?.CompanyId);
 
             try
             {
@@ -12084,7 +12084,7 @@ Sent using Towbook
 
             foreach (var en in entries)
             {
-                ThrowIfNoCompanyAccess(en.CompanyId, "call");
+                await ThrowIfNoCompanyAccessAsync(en.CompanyId, "call");
             }
 
             DateTime when = DateTime.UtcNow;
@@ -12243,7 +12243,7 @@ Sent using Towbook
             var x = (await Dispatch.Photo.GetByDispatchEntryIdAsync(id)).LastOrDefault(o => o.Description == "__INVOICE_SIGNATURE");
             var d = await Entry.GetByIdAsync(id);
 
-            ThrowIfNoCompanyAccess(d?.CompanyId, "invoice signature");
+            await ThrowIfNoCompanyAccessAsync(d?.CompanyId, "invoice signature");
 
             if (x == null || d == null)
                 throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound)
@@ -12672,7 +12672,7 @@ Sent using Towbook
         public async Task<object> RequestPickupLocation(int id, [FromQuery]string mobileNumber)
         {
             var d = await Entry.GetByIdAsync(id);
-            ThrowIfNoCompanyAccess(d?.CompanyId, "call");
+            await ThrowIfNoCompanyAccessAsync(d?.CompanyId, "call");
 
             mobileNumber = Core.FormatPhoneWithNumbersOnly(mobileNumber);
 
@@ -12717,7 +12717,7 @@ Sent using Towbook
             ThrowIfFeatureNotAssigned(Features.Impounds_TowOuts);
 
             var d = await Entry.GetByIdAsync(id);
-            ThrowIfNoCompanyAccess(d?.CompanyId, "call");
+            await ThrowIfNoCompanyAccessAsync(d?.CompanyId, "call");
 
             if (WebGlobal.CurrentUser.Type == Towbook.User.TypeEnum.Driver &&
                 CompanyKeyValue.GetFirstValueOrNull(d.CompanyId, Provider.Towbook.ProviderId, "Towouts_PreventDriversFromReleasingFromStorage") == "1")
@@ -12951,7 +12951,7 @@ Sent using Towbook
             ThrowIfFeatureNotAssigned(Features.Impounds_TowOuts);
 
             var d = await Entry.GetByIdAsync(id);
-            ThrowIfNoCompanyAccess(d?.CompanyId, "call");
+            await ThrowIfNoCompanyAccessAsync(d?.CompanyId, "call");
 
             if (await d.IsLockedAsync())
                 throw new TowbookException($"Call #{d.CallNumber} is locked. It must be unlocked before it can be modified.");
@@ -13081,7 +13081,7 @@ Sent using Towbook
                 permissionAllowed = true;
 
             if (!permissionAllowed)
-                ThrowIfNoCompanyAccess(e?.CompanyId, "call");
+                await ThrowIfNoCompanyAccessAsync(e?.CompanyId, "call");
 
             if (e.Attributes.ContainsKey(Dispatch.AttributeValue.BUILTIN_DISPATCH_MILES_CALCULATED))
                 autoFillMiles = false;
diff --git a/Extric.Towbook.API/Controllers/Dispatch/DamageFormDamagePhotosController.cs b/Extric.Towbook.API/Controllers/Dispatch/DamageFormDamagePhotosController.cs
index 71133e4dc7..bd2193d53f 100644
--- a/Extric.Towbook.API/Controllers/Dispatch/DamageFormDamagePhotosController.cs
+++ b/Extric.Towbook.API/Controllers/Dispatch/DamageFormDamagePhotosController.cs
@@ -22,7 +22,7 @@ namespace Extric.Towbook.API.Controllers
         public async Task<object> GetAllAsync(int callId, int damageFormId, int damageId)
         {
             var e = await Entry.GetByIdAsync(callId);
-            ThrowIfNoCompanyAccess(e?.CompanyId, "call");
+            await ThrowIfNoCompanyAccessAsync(e?.CompanyId, "call");
 
             #region Validation
             var df = EntryDamage.GetById(damageFormId);
@@ -68,7 +68,7 @@ namespace Extric.Towbook.API.Controllers
         public async Task<HttpResponseMessage> Get(int callId, int damageFormId, int damageId, int photoId)
         {
             var e = await Entry.GetByIdAsync(callId);
-            ThrowIfNoCompanyAccess(e?.CompanyId, "call");
+            await ThrowIfNoCompanyAccessAsync(e?.CompanyId, "call");
 
             #region Validations
             var df = EntryDamage.GetById(damageFormId);
@@ -136,7 +136,7 @@ namespace Extric.Towbook.API.Controllers
             EntryDamageRegion edr;
 
             #region Validations 
-            ThrowIfNoCompanyAccess(e?.CompanyId, "call");
+            await ThrowIfNoCompanyAccessAsync(e?.CompanyId, "call");
 
             //if (!Request.Content.IsMimeMultipartContent())
             if (!Web.HttpContext.Current.Request.HasFormContentType)
diff --git a/Extric.Towbook.API/Controllers/Dispatch/DamageFormDamageVideosController.cs b/Extric.Towbook.API/Controllers/Dispatch/DamageFormDamageVideosController.cs
index 04684a6b0a..a44d73d173 100644
--- a/Extric.Towbook.API/Controllers/Dispatch/DamageFormDamageVideosController.cs
+++ b/Extric.Towbook.API/Controllers/Dispatch/DamageFormDamageVideosController.cs
@@ -34,7 +34,7 @@ namespace Extric.Towbook.API.Controllers
             var returns = new List<DamageVideoModel>();
 
             var e = await Entry.GetByIdAsync(callId);
-            ThrowIfNoCompanyAccess(e?.CompanyId, "Video");
+            await ThrowIfNoCompanyAccessAsync(e?.CompanyId, "Video");
 
             var d = EntryDamage.GetById(damageId);
             if (d == null)
@@ -79,7 +79,7 @@ namespace Extric.Towbook.API.Controllers
             List<DamageVideoModel> returns = new List<DamageVideoModel>();
 
             var e = await Entry.GetByIdAsync(callId);
-            ThrowIfNoCompanyAccess(e?.CompanyId, "Video");
+            await ThrowIfNoCompanyAccessAsync(e?.CompanyId, "Video");
 
             var d = Dispatch.EntryDamage.GetById(damageId);
             if (d == null)
diff --git a/Extric.Towbook.API/Controllers/Dispatch/DamageFormPhotosController.cs b/Extric.Towbook.API/Controllers/Dispatch/DamageFormPhotosController.cs
index e86c46cff8..a30a6fc45b 100644
--- a/Extric.Towbook.API/Controllers/Dispatch/DamageFormPhotosController.cs
+++ b/Extric.Towbook.API/Controllers/Dispatch/DamageFormPhotosController.cs
@@ -25,7 +25,7 @@ namespace Extric.Towbook.API.Controllers
         public async Task<object> GetAllAsync(int callId, int damageFormId)
         {
             var e = await Entry.GetByIdAsync(callId);
-            ThrowIfNoCompanyAccess(e?.CompanyId, "call");
+            await ThrowIfNoCompanyAccessAsync(e?.CompanyId, "call");
 
             #region Validation
             var damage = EntryDamage.GetById(damageFormId);
@@ -54,7 +54,7 @@ namespace Extric.Towbook.API.Controllers
             var e = await Entry.GetByIdAsync(callId);
 
             if (damageFormId != 1) // sample version
-                ThrowIfNoCompanyAccess(e?.CompanyId, "call");
+                await ThrowIfNoCompanyAccessAsync(e?.CompanyId, "call");
 
             #region Validations
             var df = EntryDamage.GetById(damageFormId);
diff --git a/Extric.Towbook.API/Controllers/Dispatch/DamageFormSignatureController.cs b/Extric.Towbook.API/Controllers/Dispatch/DamageFormSignatureController.cs
index 16fbd15f61..4d858ad424 100644
--- a/Extric.Towbook.API/Controllers/Dispatch/DamageFormSignatureController.cs
+++ b/Extric.Towbook.API/Controllers/Dispatch/DamageFormSignatureController.cs
@@ -22,7 +22,7 @@ namespace Extric.Towbook.API.Controllers
             var e = await Entry.GetByIdAsync(callId);
 
             if (damageFormId != 1) // sample version
-                ThrowIfNoCompanyAccess(e?.CompanyId, "call");
+                await ThrowIfNoCompanyAccessAsync(e?.CompanyId, "call");
 
             var p = EntryDamagePhoto.GetSignature(damageFormId);
 
@@ -40,7 +40,7 @@ namespace Extric.Towbook.API.Controllers
         public async Task<ObjectResult> Post(int callId, int damageFormId)
         {
             var e = await Entry.GetByIdAsync(callId);
-            ThrowIfNoCompanyAccess(e?.CompanyId, "call");
+            await ThrowIfNoCompanyAccessAsync(e?.CompanyId, "call");
 
             #region Validations
             var df = EntryDamage.GetById(damageFormId);
diff --git a/Extric.Towbook.API/Controllers/Dispatch/DamageFormsController.cs b/Extric.Towbook.API/Controllers/Dispatch/DamageFormsController.cs
index 9f72b60fed..5118523a78 100644
--- a/Extric.Towbook.API/Controllers/Dispatch/DamageFormsController.cs
+++ b/Extric.Towbook.API/Controllers/Dispatch/DamageFormsController.cs
@@ -33,7 +33,7 @@ namespace Extric.Towbook.API.Controllers
             }
 
             var e = await Entry.GetByIdAsync(callId);
-            ThrowIfNoCompanyAccess(e?.CompanyId, "call");
+            await ThrowIfNoCompanyAccessAsync(e?.CompanyId, "call");
 
             var dms = await EntryDamage.GetByDispatchEntryIdAsync(callId, assetId, damageRegionsOwner);
 
@@ -56,7 +56,7 @@ namespace Extric.Towbook.API.Controllers
         public async Task<object> GetAsync(int callId, int id, [FromQuery] bool? damageRegionsOwner = null)
         {
             var e = await Entry.GetByIdAsync(callId);
-            ThrowIfNoCompanyAccess(e?.CompanyId, "call");
+            await ThrowIfNoCompanyAccessAsync(e?.CompanyId, "call");
 
             var ed = EntryDamage.GetById(id, damageRegionsOwner);
 
@@ -89,7 +89,7 @@ namespace Extric.Towbook.API.Controllers
             var call = await Entry.GetByIdAsync(callId);
 
             #region Validations
-            ThrowIfNoCompanyAccess(call?.CompanyId, "call");
+            await ThrowIfNoCompanyAccessAsync(call?.CompanyId, "call");
 
             if (model.AssetId == null)
             {
diff --git a/Extric.Towbook.API/Controllers/Dispatch/DisclaimerController.cs b/Extric.Towbook.API/Controllers/Dispatch/DisclaimerController.cs
index 3e4b8f7509..148bff7749 100644
--- a/Extric.Towbook.API/Controllers/Dispatch/DisclaimerController.cs
+++ b/Extric.Towbook.API/Controllers/Dispatch/DisclaimerController.cs
@@ -19,7 +19,7 @@ namespace Extric.Towbook.API.Controllers
         {
             var e = await Entry.GetByIdAsync(callId);
 
-            ThrowIfNoCompanyAccess(e?.CompanyId, "call");
+            await ThrowIfNoCompanyAccessAsync(e?.CompanyId, "call");
 
             var disclaimer = Company.InvoiceDisclaimer.GetByCompanyId(e.CompanyId, 
                 e.ReasonId, 
diff --git a/Extric.Towbook.API/Controllers/Dispatch/DriverReplayController.cs b/Extric.Towbook.API/Controllers/Dispatch/DriverReplayController.cs
index ce0966edf2..b007666d36 100644
--- a/Extric.Towbook.API/Controllers/Dispatch/DriverReplayController.cs
+++ b/Extric.Towbook.API/Controllers/Dispatch/DriverReplayController.cs
@@ -15,9 +15,9 @@ namespace Extric.Towbook.API.Controllers
         public async Task<DriverReplayModel> GetAsync([FromRoute]int callId, [FromRoute] int driverId)
         {
             var call = await Dispatch.Entry.GetByIdAsync(callId);
-            ThrowIfNoCompanyAccess(call?.CompanyId);
+            await ThrowIfNoCompanyAccessAsync(call?.CompanyId);
             var driver = await Driver.GetByIdAsync(driverId);
-            ThrowIfNoCompanyAccess(driver?.CompanyId);
+            await ThrowIfNoCompanyAccessAsync(driver?.CompanyId);
             var replay = DriverReplay.GetByCallAndDriverId(callId, driverId);
             return DriverReplayModel.Map(replay);
         }
@@ -27,9 +27,9 @@ namespace Extric.Towbook.API.Controllers
         public async Task<DriverReplayModel> Post([FromRoute] int callId, [FromRoute] int driverId)
         {
             var call = await Dispatch.Entry.GetByIdAsync(callId);
-            ThrowIfNoCompanyAccess(call?.CompanyId);
+            await ThrowIfNoCompanyAccessAsync(call?.CompanyId);
             var driver = await Driver.GetByIdAsync(driverId);
-            ThrowIfNoCompanyAccess(driver?.CompanyId);
+            await ThrowIfNoCompanyAccessAsync(driver?.CompanyId);
 
             if (call.DriverId != driver.Id && !call.Assets.Any(a => a.Drivers.Any(d => d.DriverId == driver.Id)))
             {
diff --git a/Extric.Towbook.API/Controllers/Dispatch/EmailHistoryController.cs b/Extric.Towbook.API/Controllers/Dispatch/EmailHistoryController.cs
index 44f06e7508..18ac816d9e 100644
--- a/Extric.Towbook.API/Controllers/Dispatch/EmailHistoryController.cs
+++ b/Extric.Towbook.API/Controllers/Dispatch/EmailHistoryController.cs
@@ -19,7 +19,7 @@ namespace Extric.Towbook.API.Controllers
         public async Task<IEnumerable<EmailTransaction>> GetAsync(int callId)
         {
             var entry = await Entry.GetByIdAsync(callId);
-            ThrowIfNoCompanyAccess(entry?.CompanyId, "call");
+            await ThrowIfNoCompanyAccessAsync(entry?.CompanyId, "call");
 
             var r = EmailTransaction.GetByDispatchEntryId(callId);
 
diff --git a/Extric.Towbook.API/Controllers/Dispatch/NotesController.cs b/Extric.Towbook.API/Controllers/Dispatch/NotesController.cs
index b66f871e52..d018bbac81 100644
--- a/Extric.Towbook.API/Controllers/Dispatch/NotesController.cs
+++ b/Extric.Towbook.API/Controllers/Dispatch/NotesController.cs
@@ -22,7 +22,7 @@ namespace Extric.Towbook.API.Controllers
         public async Task<IEnumerable<NoteModel>> GetAsync(int callId)
         {
             var e = await Dispatch.Entry.GetByIdAsync(callId);
-            ThrowIfNoCompanyAccess(e?.CompanyId, "Call");
+            await ThrowIfNoCompanyAccessAsync(e?.CompanyId, "Call");
 
             if (!WebGlobal.CurrentUser.HasPermissionToViewInternalNotes())
             {
@@ -57,7 +57,7 @@ namespace Extric.Towbook.API.Controllers
         public async Task<NoteModel> GetAsync(int callId, int id)
         {
             var e = await Dispatch.Entry.GetByIdAsync(callId);
-            ThrowIfNoCompanyAccess(e?.CompanyId, "Call");
+            await ThrowIfNoCompanyAccessAsync(e?.CompanyId, "Call");
 
             NoteModel result = NoteModel.Map(await Dispatch.EntryNote.GetByIdAsync(id));
             result.ShowDelete = result.OwnerUserId == WebGlobal.CurrentUser.Id;
@@ -84,7 +84,7 @@ namespace Extric.Towbook.API.Controllers
             }
 
             var e = await Dispatch.Entry.GetByIdAsync(callId);
-            ThrowIfNoCompanyAccess(e?.CompanyId, "Call");
+            await ThrowIfNoCompanyAccessAsync(e?.CompanyId, "Call");
 
             var n = NoteModel.Map(model);
             n.OwnerUserId = WebGlobal.CurrentUser.Id;
diff --git a/Extric.Towbook.API/Controllers/Dispatch/PaymentActionsController.cs b/Extric.Towbook.API/Controllers/Dispatch/PaymentActionsController.cs
index a96a2ff647..82b188cb0a 100644
--- a/Extric.Towbook.API/Controllers/Dispatch/PaymentActionsController.cs
+++ b/Extric.Towbook.API/Controllers/Dispatch/PaymentActionsController.cs
@@ -18,7 +18,7 @@ namespace Extric.Towbook.API.Controllers
         {
             var entry = await Entry.GetByIdAsync(callId);
 
-            ThrowIfNoCompanyAccess(entry?.CompanyId);
+            await ThrowIfNoCompanyAccessAsync(entry?.CompanyId);
 
             try
             {
@@ -61,7 +61,7 @@ namespace Extric.Towbook.API.Controllers
             var call = Entry.GetById(callId);
             if (call?.Invoice?.Id != ip.InvoiceId)
                 call = null;
-            ThrowIfNoCompanyAccess(call?.CompanyId);
+            await ThrowIfNoCompanyAccessAsync(call?.CompanyId);
 
             if (ip.PaymentType == PaymentType.Square.Id)
             {
diff --git a/Extric.Towbook.API/Controllers/Dispatch/PhotoActionsController.cs b/Extric.Towbook.API/Controllers/Dispatch/PhotoActionsController.cs
index 0898b8c454..d7b9c19a6e 100644
--- a/Extric.Towbook.API/Controllers/Dispatch/PhotoActionsController.cs
+++ b/Extric.Towbook.API/Controllers/Dispatch/PhotoActionsController.cs
@@ -31,7 +31,7 @@ namespace Extric.Towbook.API.Controllers
             }
             
             var d = await Dispatch.Entry.GetByIdAsync(callId);
-            ThrowIfNoCompanyAccess(d?.CompanyId);
+            await ThrowIfNoCompanyAccessAsync(d?.CompanyId);
 
             // Look for a call photo with this id
             var x = Dispatch.Photo.GetById(id);
diff --git a/Extric.Towbook.API/Controllers/Dispatch/PhotosController.cs b/Extric.Towbook.API/Controllers/Dispatch/PhotosController.cs
index 1d075ba5e3..66e59cf376 100644
--- a/Extric.Towbook.API/Controllers/Dispatch/PhotosController.cs
+++ b/Extric.Towbook.API/Controllers/Dispatch/PhotosController.cs
@@ -49,7 +49,7 @@ namespace Extric.Towbook.API.Controllers
 
             var d = await Dispatch.Entry.GetByIdAsync(callId);
 
-            ThrowIfNoCompanyAccess(d?.CompanyId, "photo");
+            await ThrowIfNoCompanyAccessAsync(d?.CompanyId, "photo");
 
             if (WebGlobal.CurrentUser?.Type == Towbook.User.TypeEnum.PoliceDispatcher ||
                 WebGlobal.CurrentUser?.Type == Towbook.User.TypeEnum.PoliceOfficer)
@@ -101,7 +101,7 @@ namespace Extric.Towbook.API.Controllers
         public async Task<HttpResponseMessage> Head(int callId)
         {
             var d = await Dispatch.Entry.GetByIdAsync(callId);
-            ThrowIfNoCompanyAccess(d?.CompanyId);
+            await ThrowIfNoCompanyAccessAsync(d?.CompanyId);
 
             IEnumerable<string> values = new List<string>();
 
@@ -127,7 +127,7 @@ namespace Extric.Towbook.API.Controllers
         public async Task<HttpResponseMessage> Get(int callId, int id)
         {
             var d = await Dispatch.Entry.GetByIdAsync(callId);
-            ThrowIfNoCompanyAccess(d?.CompanyId);
+            await ThrowIfNoCompanyAccessAsync(d?.CompanyId);
 
             // Look for a call photo with this id
             var x = Dispatch.Photo.GetById(id);
@@ -191,7 +191,7 @@ namespace Extric.Towbook.API.Controllers
 
             var u = WebGlobal.CurrentUser;
             var d = await Dispatch.Entry.GetByIdAsync(callId);
-            ThrowIfNoCompanyAccess(d?.CompanyId);
+            await ThrowIfNoCompanyAccessAsync(d?.CompanyId);
 
             if (await Core.GetRedisValueAsync("maintenance_block_call_creation") == "1")
             {
@@ -511,7 +511,7 @@ namespace Extric.Towbook.API.Controllers
         {
             var d = await Dispatch.Entry.GetByIdAsync(callId);
 
-            ThrowIfNoCompanyAccess(d?.CompanyId);
+            await ThrowIfNoCompanyAccessAsync(d?.CompanyId);
 
             // Look for a call photo with this id
             var x = Dispatch.Photo.GetById(id);
diff --git a/Extric.Towbook.API/Controllers/Dispatch/RoadsideController.cs b/Extric.Towbook.API/Controllers/Dispatch/RoadsideController.cs
index c07f465acf..dc46bc8be1 100644
--- a/Extric.Towbook.API/Controllers/Dispatch/RoadsideController.cs
+++ b/Extric.Towbook.API/Controllers/Dispatch/RoadsideController.cs
@@ -36,7 +36,7 @@ namespace Extric.Towbook.API.Controllers
 
             var entry = await Dispatch.Entry.GetByIdAsync(callId);
 
-            ThrowIfNoCompanyAccess(entry?.CompanyId, "Roadside.io");
+            await ThrowIfNoCompanyAccessAsync(entry?.CompanyId, "Roadside.io");
 
             var dispatch = await RoadsideDispatch.GetByDispatchEntryIdAsync(entry.Id);
             if (dispatch == null)
@@ -91,7 +91,7 @@ namespace Extric.Towbook.API.Controllers
 
             var entry = await Dispatch.Entry.GetByIdAsync(callId);
 
-            ThrowIfNoCompanyAccess(entry?.CompanyId, "Roadside.io");
+            await ThrowIfNoCompanyAccessAsync(entry?.CompanyId, "Roadside.io");
 
             ThrowIfNoAccountAccess(entry?.Account);
 
@@ -179,7 +179,7 @@ namespace Extric.Towbook.API.Controllers
 
             var entry = await Dispatch.Entry.GetByIdAsync(callId);
 
-            ThrowIfNoCompanyAccess(entry?.CompanyId, "Roadside.io");
+            await ThrowIfNoCompanyAccessAsync(entry?.CompanyId, "Roadside.io");
 
             ThrowIfNoAccountAccess(entry?.Account);
 
@@ -261,7 +261,7 @@ namespace Extric.Towbook.API.Controllers
             {
                 var entry = await Dispatch.Entry.GetByIdAsync(callId);
 
-                ThrowIfNoCompanyAccess(entry?.CompanyId, "Roadside.io");
+                await ThrowIfNoCompanyAccessAsync(entry?.CompanyId, "Roadside.io");
 
                 var dispatch = await RoadsideDispatch.GetByDispatchEntryIdAsync(entry.Id);
                 if (dispatch == null)
@@ -311,7 +311,7 @@ namespace Extric.Towbook.API.Controllers
         public async Task<HttpResponseMessage> DeleteAsync(int callId, int id, int userId)
         {
             var dispatch = await RoadsideDispatch.GetByIdAsync(id);
-            ThrowIfNoCompanyAccess(dispatch?.CompanyId, "Roadside.io");
+            await ThrowIfNoCompanyAccessAsync(dispatch?.CompanyId, "Roadside.io");
 
             var user = (await RoadsideDispatchUser.GetByRoadsideDispatchIdAsync(dispatch.Id, true)).Where(w => w.Id == userId).FirstOrDefault();
             if (user != null)
diff --git a/Extric.Towbook.API/Controllers/Dispatch/SignaturesController.cs b/Extric.Towbook.API/Controllers/Dispatch/SignaturesController.cs
index 64cc7e1283..558c24a874 100644
--- a/Extric.Towbook.API/Controllers/Dispatch/SignaturesController.cs
+++ b/Extric.Towbook.API/Controllers/Dispatch/SignaturesController.cs
@@ -36,7 +36,7 @@ namespace Extric.Towbook.API.Controllers
         public async Task<IEnumerable<SignatureModel>> Get(int callId)
         {
             var d = await Entry.GetByIdAsync(callId);
-            ThrowIfNoCompanyAccess(d?.CompanyId);
+            await ThrowIfNoCompanyAccessAsync(d?.CompanyId);
 
             var signatures = await Signature.GetByDispatchEntryIdAsync(callId);
             var result = new Collection<SignatureModel>();
@@ -57,11 +57,11 @@ namespace Extric.Towbook.API.Controllers
             var x = await Signature.GetByIdAsync(id);
 
             if (x?.DispatchEntryId != callId)
-                ThrowIfNoCompanyAccess((int?)null, "call");
+                await ThrowIfNoCompanyAccessAsync((int?)null, "call");
 
             var d = await Entry.GetByIdAsync(x?.DispatchEntryId ?? 0);
 
-            ThrowIfNoCompanyAccess(d?.CompanyId, "call");
+            await ThrowIfNoCompanyAccessAsync(d?.CompanyId, "call");
             if (x?.DispatchEntryId != d.Id)
                 x = null;
 
@@ -128,7 +128,7 @@ namespace Extric.Towbook.API.Controllers
 
             var u = WebGlobal.CurrentUser;
             var d = await Entry.GetByIdAsync(callId);
-            ThrowIfNoCompanyAccess(d?.CompanyId, "call");
+            await ThrowIfNoCompanyAccessAsync(d?.CompanyId, "call");
 
             // validates the type of signature if its supported by this company calls
 
@@ -252,7 +252,7 @@ namespace Extric.Towbook.API.Controllers
             var e = await Entry.GetByIdAsync(callId);
             var s = await Signature.GetByIdAsync(id);
 
-            ThrowIfNoCompanyAccess(e?.CompanyId, "call");
+            await ThrowIfNoCompanyAccessAsync(e?.CompanyId, "call");
             if (s.DispatchEntryId != e.Id)
                 s = null;
 
diff --git a/Extric.Towbook.API/Controllers/Dispatch/VideosController.cs b/Extric.Towbook.API/Controllers/Dispatch/VideosController.cs
index 3745f275ac..f0bc2fa246 100644
--- a/Extric.Towbook.API/Controllers/Dispatch/VideosController.cs
+++ b/Extric.Towbook.API/Controllers/Dispatch/VideosController.cs
@@ -28,7 +28,7 @@ namespace Extric.Towbook.API.Controllers
 
             var d = await Dispatch.Entry.GetByIdAsync(callId);
 
-            ThrowIfNoCompanyAccess(d?.CompanyId, "video");
+            await ThrowIfNoCompanyAccessAsync(d?.CompanyId, "video");
 
             foreach (var video in await Dispatch.Video.GetByDispatchEntryIdAsync(callId))
             {
@@ -49,7 +49,7 @@ namespace Extric.Towbook.API.Controllers
             var d = await Dispatch.Entry.GetByIdAsync(callId);
             var x = Dispatch.Video.GetById(id);
 
-            ThrowIfNoCompanyAccess(d?.CompanyId, "call");
+            await ThrowIfNoCompanyAccessAsync(d?.CompanyId, "call");
             if (x?.DispatchEntryId != d.Id)
                 x = null;
 
@@ -93,7 +93,7 @@ namespace Extric.Towbook.API.Controllers
         {
             var u = WebGlobal.CurrentUser;
             var d = await Dispatch.Entry.GetByIdAsync(callId);
-            ThrowIfNoCompanyAccess(d?.CompanyId);
+            await ThrowIfNoCompanyAccessAsync(d?.CompanyId);
 
             if (model.ContentType != "video/mp4")
                 throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest)
@@ -138,7 +138,7 @@ namespace Extric.Towbook.API.Controllers
         public async Task<HttpResponseMessage> Delete(int callId, int id)
         {
             var e = await Dispatch.Entry.GetByIdAsync(callId);
-            ThrowIfNoCompanyAccess(e?.CompanyId, "call");
+            await ThrowIfNoCompanyAccessAsync(e?.CompanyId, "call");
 
             var p = Dispatch.Video.GetById(id);
             if (p?.DispatchEntryId != e.Id)
diff --git a/Extric.Towbook.API/Controllers/Drivers/AbsencesController.cs b/Extric.Towbook.API/Controllers/Drivers/AbsencesController.cs
index aeabb1d49f..0ae230955c 100644
--- a/Extric.Towbook.API/Controllers/Drivers/AbsencesController.cs
+++ b/Extric.Towbook.API/Controllers/Drivers/AbsencesController.cs
@@ -89,7 +89,7 @@ namespace Extric.Towbook.API.Drivers.Controllers
                 });
             }
 
-            ThrowIfNoCompanyAccess(t?.Companies);
+            await ThrowIfNoCompanyAccessAsync(t?.Companies);
 
             return (await ScheduleAbsence.GetByDriverId(driverId)).Select(o => ScheduleAbsenceModel.Map(o));
         }
@@ -121,7 +121,7 @@ namespace Extric.Towbook.API.Drivers.Controllers
                 });
             }
 
-            ThrowIfNoCompanyAccess(t?.Companies);
+            await ThrowIfNoCompanyAccessAsync(t?.Companies);
 
             return ScheduleAbsenceModel.Map(r);
         }
@@ -153,7 +153,7 @@ namespace Extric.Towbook.API.Drivers.Controllers
                 });
             }
 
-            ThrowIfNoCompanyAccess(t?.Companies);
+            await ThrowIfNoCompanyAccessAsync(t?.Companies);
 
             var tr = ScheduleAbsenceModel.Map(model, r);
 
@@ -198,7 +198,7 @@ namespace Extric.Towbook.API.Drivers.Controllers
                 });
             }
 
-            ThrowIfNoCompanyAccess(t?.Companies);
+            await ThrowIfNoCompanyAccessAsync(t?.Companies);
 
             var tr = ScheduleAbsenceModel.Map(model, new ScheduleAbsence());
 
@@ -239,7 +239,7 @@ namespace Extric.Towbook.API.Drivers.Controllers
                 });
             }
 
-            ThrowIfNoCompanyAccess(t?.Companies);
+            await ThrowIfNoCompanyAccessAsync(t?.Companies);
 
             if (r != null)
             {
diff --git a/Extric.Towbook.API/Controllers/Email/ReplayEmailController.cs b/Extric.Towbook.API/Controllers/Email/ReplayEmailController.cs
index a9879bbb3b..c6e24199fa 100644
--- a/Extric.Towbook.API/Controllers/Email/ReplayEmailController.cs
+++ b/Extric.Towbook.API/Controllers/Email/ReplayEmailController.cs
@@ -45,7 +45,7 @@ namespace Extric.Towbook.API.Controllers
 
             var emailTransaction = Utility.EmailTransaction.GetByIds(new[] { model.EmailTransactionId }).FirstOrDefault();
 
-            ThrowIfNoCompanyAccess(emailTransaction?.CompanyId, "Email transaction not found or you don't have permission to access it");
+            await ThrowIfNoCompanyAccessAsync(emailTransaction?.CompanyId, "Email transaction not found or you don't have permission to access it");
 
             if (WebGlobal.CurrentUser.Type == Extric.Towbook.User.TypeEnum.Driver)
             {
@@ -83,10 +83,10 @@ namespace Extric.Towbook.API.Controllers
                         ThrowIfPropertyMissing("AccountId");
 
                     entry = await Entry.GetByIdAsync(model.CallId.Value);
-                    ThrowIfNoCompanyAccess(entry?.CompanyId, "Call");
+                    await ThrowIfNoCompanyAccessAsync(entry?.CompanyId, "Call");
 
                     var account = Account.GetById(model.AccountId.Value);
-                    ThrowIfNoCompanyAccess(account?.CompanyId, "Account");
+                    await ThrowIfNoCompanyAccessAsync(account?.CompanyId, "Account");
 
                     if (entry.AccountId != account.Id)
                         return new HttpResponseMessage() { StatusCode = System.Net.HttpStatusCode.Forbidden, Content = new StringContent("Cannot continue. The account specified does not match the account on record.") };
@@ -101,7 +101,7 @@ namespace Extric.Towbook.API.Controllers
                         ThrowIfPropertyMissing("CallId");
 
                     entry = await Entry.GetByIdAsync(model.CallId.Value);
-                    ThrowIfNoCompanyAccess(entry?.CompanyId, "Call");
+                    await ThrowIfNoCompanyAccessAsync(entry?.CompanyId, "Call");
 
                     company = entry.Company;
                     accountId = entry.AccountId;
@@ -113,7 +113,7 @@ namespace Extric.Towbook.API.Controllers
 
                     st = Statement.GetById(model.StatementId.Value);
 
-                    ThrowIfNoCompanyAccess(st?.Company?.Id, "Statement");
+                    await ThrowIfNoCompanyAccessAsync(st?.Company?.Id, "Statement");
                     company = st.Company;
                     accountId = st.AccountId;
                     break;
diff --git a/Extric.Towbook.API/Controllers/EventNotifications/EventNotificationSoundsController.cs b/Extric.Towbook.API/Controllers/EventNotifications/EventNotificationSoundsController.cs
index d9d931c889..47aabff69a 100644
--- a/Extric.Towbook.API/Controllers/EventNotifications/EventNotificationSoundsController.cs
+++ b/Extric.Towbook.API/Controllers/EventNotifications/EventNotificationSoundsController.cs
@@ -113,7 +113,7 @@ namespace Extric.Towbook.API.Controllers
 
             var user = Towbook.User.GetById(model.UserId ?? WebGlobal.CurrentUser.Id);
 
-            ThrowIfNoCompanyAccess(user?.CompanyId);
+            await ThrowIfNoCompanyAccessAsync(user?.CompanyId);
 
             var companyId = user.CompanyId;
 
