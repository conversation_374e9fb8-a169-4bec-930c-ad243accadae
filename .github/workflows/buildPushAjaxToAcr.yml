name: Build and Deploy AJAX to ACR
run-name: Build and Deploy AJAX by ${{ github.actor }}
on:
  workflow_call:
    secrets:
      ACR_MOJIX_USERNAME:
        required: true
      ACR_MOJIX_PASSWORD:
        required: true 
      AKS_MOJIX_KUBECONFIG:
        required: true  
        
  push:
    paths:
    - 'Ajax/**'
    - '.github/workflows/buildPushAjaxToAcr.yml'
    - 'Extric.Towbook/**'

  workflow_dispatch:
    inputs:
      environment:
        description: 'Deployment Environment'     
        required: true
        default: 'Development'
        type: choice
        options:
        - Development
        - Staging
        - Production
      newRelicEnabled:
        description: 'NewRelic profiling monitor enabled'     
        required: false
        default: '1'
        type: choice
        options:
        - 1
        - 0

jobs:
  avoid_reduncy:
    permissions: read-all
    if: ${{ github.event_name == 'workflow_dispatch' }}
    runs-on: ubuntu-latest
    steps:
      - name: Cancel Previous Redundant Action
        uses: styfle/cancel-workflow-action@0.11.0
        with:
          access_token: ${{ secrets.GITHUB_TOKEN }}

  build:
    if: ${{ github.event_name != 'workflow_dispatch' }}
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - uses: actions/setup-dotnet@v3
      with:
        dotnet-version: '8.0.x'
    - run: dotnet build Ajax/
  
  build_push_deploy:
    permissions: read-all
    if: ${{ github.event_name == 'workflow_dispatch' }}
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@master

    - name: Set output (short commit sha)
      id: vars
      uses: ./.github/actions/short-sha
      # run: echo "::set-output name=sha_short::$(git rev-parse --short HEAD)"

    - name: Replacement of the environment values
      uses: nowactions/envsubst@v1
      with:
        input: ./Ajax/deployment.yml
        output: ./Ajax/ajax-deployment.yml
      env:
        DEPLOYMENT_ENVIRONMENT: ${{ github.event.inputs.environment }}
        CORECLR_ENABLE_PROFILING: ${{ github.event.inputs.newRelicEnabled }}
      
    - uses: azure/docker-login@v1
      with:
        login-server: towbookapiregistry.azurecr.io
        username: ${{ secrets.ACR_MOJIX_USERNAME }}
        password: ${{ secrets.ACR_MOJIX_PASSWORD }}
    
    - name: Build image
      id: build-image
      run: docker build "$GITHUB_WORKSPACE/." --build-arg COMMIT=${{ steps.vars.outputs.sha_short }} -f "Ajax/Dockerfile" -t towbookapiregistry.azurecr.io/ajax:${{ steps.vars.outputs.sha_short }} --label dockerfile-path=Ajax/Dockerfile

    - name: Push image to ACR
      id: push-image
      run: docker push towbookapiregistry.azurecr.io/ajax:${{ steps.vars.outputs.sha_short }}

    - name: Azure Set Context
      id: azure-login
      uses: azure/k8s-set-context@v1
      with:
         kubeconfig: ${{ secrets.AKS_MOJIX_KUBECONFIG }}

    - name: Azure Create Secrets
      uses: azure/k8s-create-secret@v4
      with:
        container-registry-url: towbookapiregistry.azurecr.io
        container-registry-username: ${{ secrets.ACR_MOJIX_USERNAME }}
        container-registry-password: ${{ secrets.ACR_MOJIX_PASSWORD }}
        secret-name: devmojixtowbookdockerauth
       
    - name: Azure Deploy Ajax
      uses: azure/k8s-deploy@v3.1
      with:
        manifests: |
          Ajax/ajax-deployment.yml
        images: |
          towbookapiregistry.azurecr.io/ajax:${{ steps.vars.outputs.sha_short }}
        imagepullsecrets: |
          devmojixtowbookdockerauth
