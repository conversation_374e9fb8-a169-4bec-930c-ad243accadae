name: Build and Deploy API Utils
run-name: Build and Deploy API Utils by ${{ github.actor }}
on:
  workflow_call:
    secrets:
      ACR_MOJIX_USERNAME:
        required: true
      ACR_MOJIX_PASSWORD:
        required: true
      AKS_MOJIX_KUBECONFIG:
        required: true
      ACCESS_REPO_FILES:
        required: true
        
  push:
    paths:
    - '.github/workflows/BuildPushApiUtils.yml'
    - 'Extric.Towbook.API.Utils/**'
    - 'Extric.Towbook/**'
  workflow_dispatch:
    inputs:
      environment:
        description: 'Deployment Environment'     
        required: true
        default: 'Development'
        type: choice
        options:
        - Development
        - Staging
        - Production

jobs:
  avoid_reduncy:
    permissions: read-all
    if: ${{ github.event_name == 'workflow_dispatch' }}
    runs-on: ubuntu-latest
    steps:
      - name: Cancel Previous Redundant Action
        uses: styfle/cancel-workflow-action@0.11.0
        with:
          access_token: ${{ secrets.GITHUB_TOKEN }}

  build:
    if: ${{ github.event_name != 'workflow_dispatch' }}
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - uses: actions/setup-dotnet@v3
      with:
        dotnet-version: '8.0.x'
    - run: dotnet build Extric.Towbook.API.Utils/
  
  build_push_deploy:
    permissions: read-all
    if: ${{ github.event_name == 'workflow_dispatch' }}
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@master

    - name: Checkout files repo
      uses: actions/checkout@v3
      with:
        repository: ricardo-flores-mojix-com/test-files
        # token: ${{ secrets.ACCESS_REPO_FILES }}
        path: Fonts

    - name: Extract font files
      run: |
        7z x Fonts/Fonts.zip -o"Extric.Towbook.API.Utils/Fonts"
        dir Extric.Towbook.API.Utils/Fonts

    - name: Set output (short commit sha)
      id: vars
      uses: ./.github/actions/short-sha
      #run: echo "::set-output name=sha_short::$(git rev-parse --short HEAD)"
      
    # - name: Replacement of the environment values
    #   uses: nowactions/envsubst@v1
    #   env:
    #     DEPLOYMENT_ENVIRONMENT: ${{ github.event.inputs.environment }}

    - uses: azure/docker-login@v1
      with:
        login-server: towbookapiregistry.azurecr.io
        username: ${{ secrets.ACR_MOJIX_USERNAME }}
        password: ${{ secrets.ACR_MOJIX_PASSWORD }}
    
    - name: Build image
      id: build-image
      run: docker build --build-arg COMMIT=${{ steps.vars.outputs.sha_short }} -f "Extric.Towbook.API.Utils/Dockerfile" -t towbookapiregistry.azurecr.io/api-utils:${{ steps.vars.outputs.sha_short }} --label dockerfile-path=Extric.Towbook.API.Utils/Dockerfile .

    - name: Push image to ACR
      id: push-image
      run: docker push towbookapiregistry.azurecr.io/api-utils:${{ steps.vars.outputs.sha_short }}

    - name: Azure Set Context
      id: azure-login
      uses: azure/k8s-set-context@v1
      with:
         kubeconfig: ${{ secrets.AKS_MOJIX_KUBECONFIG }}

    - name: Azure Create Secrets
      uses: azure/k8s-create-secret@v4
      with:
        container-registry-url: towbookapiregistry.azurecr.io
        container-registry-username: ${{ secrets.ACR_MOJIX_USERNAME }}
        container-registry-password: ${{ secrets.ACR_MOJIX_PASSWORD }}
        secret-name: devmojixtowbookdockerauth
       
    - name: Azure Deploy API Utils
      uses: azure/k8s-deploy@v3.1
      with:
        manifests: |
          Extric.Towbook.API.Utils/k8s/api-utils-deployment-linux.yml
        images: |
          towbookapiregistry.azurecr.io/api-utils:${{ steps.vars.outputs.sha_short }}
        imagepullsecrets: |
          devmojixtowbookdockerauth
