name: Deploy Node App

on:
  push:
    branches: [milestone/reports]

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

jobs:
  build-deploy:
    runs-on: ubuntu-latest
    env:
      GITHUB_TOKEN: ${{ github.token }}
      FONTAWESOME_NPM_AUTH_TOKEN: ${{ secrets.FONTAWESOME_NPM_AUTH_TOKEN }}

    steps:
      - name: Checkout Code
        uses: actions/checkout@v4

      - name: Get Version Number
        id: version
        run: echo "version=$(echo ${GITHUB_SHA::7})" >> $GITHUB_OUTPUT

      - name: Docker Login
        uses: azure/docker-login@v2
        with:
          login-server: ${{ secrets.ACR_GARAND_REGISTRY_SERVER }}
          username: ${{ secrets.ACR_GARAND_REGISTRY_USERNAME }}
          password: ${{ secrets.ACR_GARAND_REGISTRY_PASSWORD }}

      - name: Build NodeApp
        id: build-nodeapp
        run: |
          cd NodeApp
          npm install
          npm run build
          npm run build-remix

      - name: Build Container
        id: build-container
        run: >
          docker build ./NodeApp/remix -t ${{ secrets.ACR_GARAND_REGISTRY_SERVER }}/node-app:${{ steps.version.outputs.version }}
          --build-arg VERSION=${{ steps.version.outputs.version }}
          --build-arg GITHUB_TOKEN
          --build-arg FONTAWESOME_NPM_AUTH_TOKEN

      - name: Publish Image
        id: publish
        run: docker push ${{ secrets.ACR_GARAND_REGISTRY_SERVER }}/node-app:${{ steps.version.outputs.version }}

      - name: Deploy
        id: deploy
        uses: azure/webapps-deploy@v3
        with:
          publish-profile: ${{ secrets.AZURE_NODEAPP_WEBAPP_PUBLISH_PROFILE }}
          images: "${{ secrets.ACR_GARAND_REGISTRY_SERVER }}/node-app:${{ steps.version.outputs.version }}"
