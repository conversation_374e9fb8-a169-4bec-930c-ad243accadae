# My Pull Request [Example]

## Checklist
The checklist enumerates the tasks you set out to do before the PR becomes `ready for review`; The label should be applied once all of these tasks are checked.
+ [ ] write tests
+ [ ] update README
+ [ ] write up an awesome PR message

## Purpose for Pull Request
We've discovered through support tickets that the things and the whatzits caused the spinners and the pointers to fail.

## What are the acceptance criteria for the change?
+ [ ] Engineering Manager gives a :+1:
+ [ ] @person provides a :+1: that this PR yields the desired results

## How can the change be tested
+ Add a *list* of steps that the reviewer may step through to fully experience the changeset being introduced
+ i.e. you'll need to reinstall `node_modules` since I've upgraded a few dependencies etc.
+ run both the server and the client
+ open localhost:8000

## Demonstration
+ Please include before and after screenshots and if the changset modifies a workflow, please a screencast/gif illustrating the interaction.

## Who's Affected
+ How many users will be affected by that change?
  - Does it affect to 100% of our users, or just a subset of them?

## Blockers; upstream dependencies
+ [ ] Merge #1337
+ [ ] Merge API#42
