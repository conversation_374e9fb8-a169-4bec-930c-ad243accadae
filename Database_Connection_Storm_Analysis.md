# Database Connection Storm Analysis
## TowbookNET8 - CallsController Async Issues

### Executive Summary
The application is experiencing SQL Server connection failures due to an **async connection storm** caused by unlimited parallel database operations in the CallsController.InternalList() method.

---

## The Problem Chain

### 1. **Root Cause: Unlimited Parallelism**
**File:** `Extric.Towbook.API/Controllers/Dispatch/CallsController.cs` (Line 269)
```csharp
result = (await Task.WhenAll(result.Select(o => o.FinishMapAsync()))).ToCollection();
```

This line executes `FinishMapAsync()` on **all calls in parallel** without any concurrency limits.

### 2. **FinishMapAsync() Database Operations**
**File:** `Extric.Towbook.API.Models/Calls/Extensions/CallModelExtensions.cs` (Line 733)
```csharp
var blockCharges = await ShouldBlockChargesAsync(r);
```

Each `FinishMapAsync()` call triggers `ShouldBlockChargesAsync()` which performs multiple database operations.

### 3. **ShouldBlockChargesAsync() Multiple DB Calls**
**File:** `Extric.Towbook.API.Models/Calls/Extensions/CallModelExtensions.cs` (Lines 911-934)
```csharp
// Database Call #1
var hideChargesFromAccountUsers = await CompanyKeyValue.GetFirstValueOrNullAsync(...)

// Database Call #2  
var preventDriversFromViewingCharges = await CompanyKeyValue.GetByCompanyIdAsync(...)

// Database Call #3
var preventDispatchersFromViewingCharges = await CompanyKeyValue.GetByCompanyIdAsync(...)

// Database Call #4
var drivers = await Driver.GetByUserIdAsync(CurrentUser.Id);

// Database Call #5 (for EACH driver)
var hidePricing = await DriverKeyValue.GetByDriverAsync(...)
```

### 4. **DriverKeyValue Chain Reaction**
**File:** `Extric.Towbook/Integration/DriverKeyValue.cs` (Line 112)
```csharp
var k = await DriverKey.GetByProviderIdAsync(providerId, keyName);
```

### 5. **DriverKey Cache Miss Storm**
**File:** `Extric.Towbook/Integration/DriverKey.cs` (Line 45)
```csharp
var queryResult = await SqlMapper.QuerySpAsync<dynamic>("Integration.ProviderDriverKeysGetAll");
```

---

## The Multiplication Effect

### Scenario: 100 Calls in Result Set
- **100 parallel FinishMapAsync()** calls
- Each makes **4-5 database calls** in ShouldBlockChargesAsync()
- Each DriverKeyValue.GetByDriverAsync() can trigger **DriverKey.GetAllAsync()**
- **Result: 400-500+ concurrent database connections**

### Why This Causes Server Resets
1. **Connection Pool Exhaustion**: SQL Server default pool = 100 connections
2. **Memory Pressure**: Each connection consumes significant memory
3. **Lock Contention**: Concurrent reads cause blocking and deadlocks
4. **Timeout Cascades**: Failed connections trigger retries, amplifying the problem
5. **Cache Stampede**: Multiple threads hit cache miss simultaneously

---

## Stack Trace Analysis

The error originates from:
```
SqlMapper.QuerySpAsync<T> (SqlMapperExtensions.cs:253)
↓
DriverKey.GetAllAsync() (DriverKey.cs:45)
↓
DriverKeyValue.GetByDriverAsync() (DriverKeyValue.cs:112)
↓
ShouldBlockChargesAsync() (CallModelExtensions.cs:934)
↓
FinishMapAsync() (CallModelExtensions.cs:733)
↓
CallsController.InternalList() (CallsController.cs:269)
```

---

## Solutions

### 1. **Immediate Fix: Limit Concurrency**
```csharp
// Replace line 269 in CallsController.cs
var semaphore = new SemaphoreSlim(10); // Limit to 10 concurrent operations
var tasks = result.Select(async call => {
    await semaphore.WaitAsync();
    try { 
        return await call.FinishMapAsync(); 
    }
    finally { 
        semaphore.Release(); 
    }
});
result = (await Task.WhenAll(tasks)).ToCollection();
```

### 2. **Better Fix: Batch Processing**
```csharp
// Process in smaller batches
const int batchSize = 20;
var batches = result.Batch(batchSize);
var processedResults = new List<CallModel>();

foreach (var batch in batches)
{
    var batchResults = await Task.WhenAll(batch.Select(o => o.FinishMapAsync()));
    processedResults.AddRange(batchResults);
}
result = processedResults.ToCollection();
```

### 3. **Best Fix: Pre-fetch Configuration Data**
Instead of each call doing individual database lookups, fetch all configuration data once and pass it to FinishMapAsync():

```csharp
// Pre-fetch all configuration data
var companyConfigs = await GetCompanyConfigurationsAsync(result.Select(r => r.CompanyId).Distinct());
var driverConfigs = await GetDriverConfigurationsAsync(CurrentUser.Id);

// Pass pre-fetched data to avoid individual DB calls
result = (await Task.WhenAll(result.Select(o => o.FinishMapAsync(companyConfigs, driverConfigs)))).ToCollection();
```

### 4. **Cache Stampede Protection**
Add locking around cache misses in DriverKey.GetAllAsync():

```csharp
private static readonly SemaphoreSlim _cacheLock = new SemaphoreSlim(1, 1);

public static async Task<Collection<DriverKey>> GetAllAsync()
{
    return await AppServices.Cache.GetAsync(CacheKeyAll, TimeSpan.FromDays(30), async () =>
    {
        await _cacheLock.WaitAsync();
        try
        {
            // Check cache again after acquiring lock
            var cached = AppServices.Cache.Get<DriverKeyCollection>(CacheKeyAll);
            if (cached != null) return cached;
            
            var queryResult = await SqlMapper.QuerySpAsync<dynamic>("Integration.ProviderDriverKeysGetAll");
            return new DriverKeyCollection(Map(queryResult));
        }
        finally
        {
            _cacheLock.Release();
        }
    });
}
```

---

## Recommendation

**Implement Solution #1 immediately** as it requires minimal code changes and will resolve the connection storm. Then plan for Solution #3 as a longer-term architectural improvement.

This is a classic case of **async amplification** where innocent-looking parallel processing creates a database connection storm that can bring down the entire application.

---

*Analysis Date: 2025-01-17*  
*File: CallsController.cs - Line 269*  
*Impact: Critical - Application Stability*
