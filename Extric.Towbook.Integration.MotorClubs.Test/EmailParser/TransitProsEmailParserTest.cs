using System;
using System.Text;
using System.Collections.Generic;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System.IO;
using MimeKit;
using System.Linq;
using Extric.Towbook.Utility;

namespace Extric.Towbook.Integration.MotorClubs.Test.EmailParser
{
    /// <summary>
    /// Summary description for TransitProsEmailParserTest
    /// </summary>
    [TestClass]
    public class TransitProsEmailParserTest
    {
        [TestMethod]
        public void FromTextTest()
        {
            var text = Util.GetPdfAttachmentFileContents("TransitPros\\Last_TransitPros_Job_Email.eml", "187434.pdf");
            Console.WriteLine(text);

            var result = TransitProsEmailParser.FromText(text);

            var notes = "Pickup Date: 11/30/2016  Lot #: 41348066 Zone #: 17 \n" +
                "Pickup Location: FAIRFIELD COLLISION CENTER (*************\n" +
                "Delivery Location: COPART - 149 MA - WEST WARREN (*************\n\n" +
                "Pickup Notes:\n" +
                "******* TO START THE PAYMENT PROCESS PLEASE MAKE SURE YOU FILL OUT PAPERWORK COMPLETELY AND ACCURATELY, HAVE IT SIGNED BY THE PICKUP LOCATION, AND THAT YOU TURN IT INTO THE DISPATCHER AT COPART BEFORE YOU DROP THE UNIT******* PER ASHLEY. UNIT IS A 2014 MERCEDES SPRINTER. DAMAGE TO FRONT. TOWABLE. WHEELS/TIRES UP AND ROLLING. NO DEBRIS. NO DAMAGE. AVAILABLE 8 AM TO 5 PM, MON - FRI. CHARGES DUE $239.55 NO STORAGE. ******* TO START THE PAYMENT PROCESS PLEASE MAKE SURE YOU FILL OUT PAPERWORK COMPLETELY AND ACCURATELY, HAVE IT SIGNED BY THE PICKUP LOCATION, AND THAT YOU TURN IT INTO THE DISPATCHER AT COPART BEFORE YOU DROP THE UNIT******* PER . UNIT IS A . DAMAGE AVAILABLE 8 AM TO 5 PM, MON - FRI. CHARGES DUE $239.55 NO STORAGE.. TAKES CHECK. COMPLETELY ACCESSIBLE. ADDRESS VERIFIED. PERSONALS REMOVED.\n\n" +
                "ADVANCE CHARGES DUE ($239.55)\nPlease pay for advance charges by CHECK and take the receipt to dispatcher";

            Assert.AreEqual("600 OLD WEST WARREN RD WEST WARREN, MA 01092", result.DestinationAddress);
            Assert.AreEqual("102 LINWOOD AVE FAIRFIELD, CT 06824", result.PickupAddress);
            Assert.AreEqual("187434", result.PurchaseOrderNumber);
            Assert.AreEqual("17", result.TowingProviderId);
            Assert.AreEqual("Mercedes", result.VehicleMake);
            Assert.AreEqual("SPRINTER", result.VehicleModel);
            Assert.AreEqual(notes, result.VehicleNotes);
            Assert.AreEqual("WD3PE7DC9E5849898", result.VehicleVIN);
            Assert.AreEqual("2014", result.VehicleYear);

            Console.WriteLine(result.ToJson(true));
        }
    }
}
