using System;
using System.Text;
using System.Collections.Generic;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System.IO;
using System.Reflection;
using Extric.Towbook.Utility;

namespace Extric.Towbook.Integration.MotorClubs.Test.TestData.EmailParser
{
    /// <summary>
    /// Summary description for NacEmailParserTest
    /// </summary>
    [TestClass]
    public class NacEmailParserTest
    {
        [TestMethod]
        public void TestFromText()
        {
            var filePath = Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location) + "\\TestData\\NAC\\NAC_Job_Email.eml";
            var text = MimeKit.MimeMessage.Load(filePath).TextBody;
            var result = NacEmailParser.FromText(text);
            Console.WriteLine(text);

            Assert.AreEqual("ADVANTAGE/EZ RENT A CAR - COD", result.CustomerName);
            Assert.AreEqual("(*************", result.CustomerPhone);
            Assert.AreEqual("5800 LONE TREE LN, SHINGLE SPRINGS, CA 95682", result.PickupAddress);
            Assert.AreEqual("**********", result.PurchaseOrderNumber);
            Assert.AreEqual("Lockout - Coverage: Covered as Dispatched\nPlease call if Customer requests a change to service as dispatched or if your requirement to provide the service changes.  (i.e. Need Special Equipment, etc.)", result.ServiceNeeded);
            Assert.AreEqual("7KQT487", result.VehicleLicense);
            Assert.AreEqual("VOLKSWAGEN", result.VehicleMake);
            Assert.AreEqual("BUG", result.VehicleModel);
            Assert.AreEqual("2015", result.VehicleYear);
            Assert.AreEqual("BL: RESIDENCE\r\n-MAJOR CROSS STREET IS SOUTH SHINGLE ROAD.\r\n-PLS CALL CUST UPON ARRIVAL\r\n\r\nDetails: / Electric Locks / Side Airbags / Keys In Trunk Vehicle Has Trunk Release / Parked On Street / Customer With Vehicle\r\nRt#'s: Lat: 38.6081657409668 Long: -120.94556427001953", result.VehicleNotes);

            Assert.AreEqual("Passenger/Light Util", result.VehicleType);
            Assert.AreEqual("COYOTE PASS", result.PickupCrossStreet);

            Assert.AreEqual("CA6489", result.TowingProviderId);

            Console.WriteLine(result.ToJson(true));
        }

        [TestMethod]
        public void TestFromText6324688()
        {
            var filePath = Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location) + "\\TestData\\NAC\\6324688_Job_Email.eml";
            var text = MimeKit.MimeMessage.Load(filePath).TextBody;
            Console.WriteLine(text);

            var result = NacEmailParser.FromText(text);
            var notes = "lifted 4wd truck 35\" tires and 6' lift\njump poss tow\ncov 5 miles\n / @ res\r\n\r\nDetails: / Engine Does Not Turn Over / Parked On Street / Customer With Vehicle\r\nRt#'s: Lat: 39.142929077148438 Long: -121.58961486816406";

            Assert.AreEqual("LANEY, PETER", result.CustomerName);
            Assert.AreEqual("(*************", result.CustomerPhone);
            Assert.AreEqual("609 GARDEN HWY, YUBA CITY, CA 95991 Lat: 39.118583 Long: -121.617301", result.DestinationAddress);
            Assert.AreEqual("GARDEN HIGHWAY AUTO  Auto Shop", result.DestinationName);
            Assert.AreEqual("710 D ST, MARYSVILLE, CA 95901", result.PickupAddress);
            Assert.AreEqual("Roadside - Coverage: Miles to collect from Customer = 0\nMiles Billable to NAC = 0.  Please call if Customer requests a change to service as dispatched or if your requirement to provide the service changes.  (i.e. Need Special Equipment, etc.)", result.ServiceNeeded);
            Assert.AreEqual("CA3921", result.TowingProviderId);
            Assert.AreEqual("FORD", result.VehicleMake);
            Assert.AreEqual("BRONCO", result.VehicleModel);
            Assert.AreEqual(notes, result.VehicleNotes);
            Assert.AreEqual("Passenger/Light Util", result.VehicleType);
            Assert.AreEqual("1996", result.VehicleYear);

            Assert.AreEqual("GARDEN HIGHWAY AUTO  Auto Shop", result.DestinationName);
            Assert.AreEqual("609 GARDEN HWY, YUBA CITY, CA 95991 Lat: 39.118583 Long: -121.617301", result.DestinationAddress);

            Console.WriteLine(result.ToJson(true));
        }

        [TestMethod]
        public void TestFromText6327709()
        {
            var filePath = Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location) + "\\TestData\\NAC\\6327709_Job_Email.eml";
            var text = MimeKit.MimeMessage.Load(filePath).TextBody;
            Console.WriteLine(text);

            var result = NacEmailParser.FromText(text);
            var notes = "power steering issues\nat res\ntd:vacaville honda\ncovered for 5mi\r\n\r\nDetails: N/A / Unk Mechanical Problem / Front Wheel / Tow Dest Open / Drop Box Available / Parked On Street / 1 Passenger / Customer With Vehicle\r\nRt#'s: Lat: 38.343601 Long: -121.939529";

            Assert.AreEqual("CCMC", result.CustomerName);
            Assert.AreEqual("(*************", result.CustomerPhone);
            Assert.AreEqual("751 ORANGE DR, VACAVILLE, CA 95687 Lat: 38.383707851171494 Long: -121.93767093122005", result.DestinationAddress);
            Assert.AreEqual("Auto Dealership", result.DestinationName);
            Assert.AreEqual("2243 MARSHALL RD, VACAVILLE, CA 95687", result.PickupAddress);
            Assert.AreEqual("**********", result.PurchaseOrderNumber); 
            Assert.AreEqual("Tow - Coverage: Miles to collect from Customer = 0\nMiles Billable to NAC = 3.  Please call if Customer requests a change to service as dispatched or if your requirement to provide the service changes.  (i.e. Need Special Equipment, etc.)", result.ServiceNeeded);
            Assert.AreEqual("CA3148", result.TowingProviderId);
            Assert.AreEqual("HONDA", result.VehicleMake);
            Assert.AreEqual("ACCORD", result.VehicleModel);
            Assert.AreEqual(notes, result.VehicleNotes);
            Assert.AreEqual("Passenger/Light Util", result.VehicleType);
            Assert.AreEqual("2006", result.VehicleYear);

            Assert.AreEqual("Auto Dealership", result.DestinationName);
            Assert.AreEqual("751 ORANGE DR, VACAVILLE, CA 95687 Lat: 38.383707851171494 Long: -121.93767093122005", result.DestinationAddress);

            Console.WriteLine(result.ToJson(true));
        }

        [TestMethod]
        public void TestFromText6349936()
        {
            var filePath = Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location) + "\\TestData\\NAC\\6349936_Job_Email.eml";
            var text = MimeKit.MimeMessage.Load(filePath).TextBody;

            var result = NacEmailParser.FromText(text);
            Console.WriteLine(result.ToJson(true));
        }

        [TestMethod]
        public void TestFromText6350299()
        {
            var filePath = Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location) + "\\TestData\\NAC\\6350299_Job_Email.eml";
            var text = MimeKit.MimeMessage.Load(filePath).HtmlBody;
            Console.WriteLine(text);

            var result = NacEmailParser.FromHtml(text);


            Assert.AreEqual("CCMC", result.CustomerName);
            Assert.AreEqual("(*************", result.CustomerPhone);
            Assert.AreEqual("522 W ROOSEVELT RD, LITTLE ROCK, AR 72206 Lat: 34.7244867682457 Long: -92.279215827584267", result.DestinationAddress);
            Assert.AreEqual("TODD'S AUTO CLINIC Auto Shop", result.DestinationName);
            Assert.AreEqual("2819 MARSHALL ST, LITTLE ROCK, AR 72206", result.PickupAddress);
            Assert.AreEqual("W 28TH ST", result.PickupCrossStreet);
            Assert.AreEqual("**********", result.PurchaseOrderNumber);
            Assert.AreEqual("Tow - Coverage: Miles to collect from Customer = 0\r\n\nMiles Billable to NAC = 0.  Please call if Customer requests a change to service as dispatched or if your requirement to provide the service changes.  (i.e. Need Special Equipment, etc.)", result.ServiceNeeded);
            Assert.AreEqual("AR3087", result.TowingProviderId);
            Assert.AreEqual("NISSA", result.VehicleMake);
            Assert.AreEqual("MAXIMA", result.VehicleModel);
            Assert.AreEqual("@ res.\r\n\nCar in drvway\r\n\r\nDetails: N/A / Unk Mechanical Problem / Rear Wheel / Tow Dest Open / Drop Box Available / Parked On Driveway / NO Passengers / Customer With Vehicle\r\nRt#'s: Lat: 34.7212028503418 Long: -92.29193115234375", result.VehicleNotes);
            Assert.AreEqual("Passenger/Light Util", result.VehicleType);
            Assert.AreEqual("2001", result.VehicleYear);

            Console.WriteLine(result.ToJson(true));
        }

    }
}
