using System.Text;
using ForceDotNetJwtCompanion.Models;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Org.BouncyCastle.Math;

namespace ForceDotNetJwtCompanion.Util.Tests;

[TestClass()]
public class KeyHelpersTests
{
    [TestMethod()]
    public void CreatePrivateKeyWrapperTest()
    {
        // Arrange
        var pemKey = @"
*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************";
        string expectedModulus = "19271401833479256875714882464380703216276407892988446480935197002496628534011233121608719111178329232969449200702016863594230454973940811791219676127673436346057788053982267349170039399573357160907878727274221870794009058942862398271738323425300725435524422833459375635225764274256911595470725788927383827931297169006110674472609934326804425385883039370387972160605478125778273110264946860415169829837562118003508237911959334118050842918987096602495172330727458011470718952196718814122881323363783803998837444706102191659640699910974061355333792811766902086997960612990203854288057607213759338625278417349596964190603";
        string expectedExponent = "7759931926768098163200602572497590178462948656362338959803460355179284659021460189857535330949547878197770414909530097835115195866988282233919642998172607053333109508377939853989696732151931104532988474806644766457398142438205399380381135633794245905073038991158829261726815483093561935222465450156387926319264103332676612704788829618017002689995320986404432009162276646593670363801858665742619437638182972639234898140622174754935674382831959819853325886186064149258273488959583980773175699132295710302164007388664533434621326307313976083472128589710345559892729020311498453773729871244020713254180217833779232727641";

        // Act
        var result = KeyHelpers.CreatePrivateKeyWrapper(pemKey);

        // Assert
        Assert.IsNotNull(result);
        Assert.AreEqual(expectedModulus, result.Modulus.ToString());
        Assert.AreEqual(expectedExponent, result.Exponent.ToString());
    }

    [TestMethod()]
    public void CreateSignature_ValidInput_ReturnsSignature()
    {
        // Arrange
        var modulus = new BigInteger("19271401833479256875714882464380703216276407892988446480935197002496628534011233121608719111178329232969449200702016863594230454973940811791219676127673436346057788053982267349170039399573357160907878727274221870794009058942862398271738323425300725435524422833459375635225764274256911595470725788927383827931297169006110674472609934326804425385883039370387972160605478125778273110264946860415169829837562118003508237911959334118050842918987096602495172330727458011470718952196718814122881323363783803998837444706102191659640699910974061355333792811766902086997960612990203854288057607213759338625278417349596964190603");
        var exponent = new BigInteger("7759931926768098163200602572497590178462948656362338959803460355179284659021460189857535330949547878197770414909530097835115195866988282233919642998172607053333109508377939853989696732151931104532988474806644766457398142438205399380381135633794245905073038991158829261726815483093561935222465450156387926319264103332676612704788829618017002689995320986404432009162276646593670363801858665742619437638182972639234898140622174754935674382831959819853325886186064149258273488959583980773175699132295710302164007388664533434621326307313976083472128589710345559892729020311498453773729871244020713254180217833779232727641");
        var privateKeyWrapper = new PrivateKeyWrapper(modulus, exponent);
        byte[] bytesToSign = Encoding.UTF8.GetBytes("This a text to sign with libraries!!!!");

        // Act
        var signature = KeyHelpers.CreateSignature(privateKeyWrapper, bytesToSign);

        // Assert
        Assert.IsNotNull(signature);
    }

    [TestMethod()]
    public void CreatePrivateKeyWrapperWithPassPhrase_ValidKeyAndPassphrase_ReturnsPrivateKeyWrapper()
    {
        // Arrange
        string pemKey = @"
-----BEGIN RSA PRIVATE KEY-----
Proc-Type: 4,ENCRYPTED
DEK-Info: AES-256-CBC,987664C874DD282539E4212955745739

+maj+FVWXyFIF93eIyd5UGL6DLbq7qSBtk+p/wl+a+ikZMUZ0hyoM+OID+ycrXjS
jYw5f/Z/uMnphVstHBqYEsQzkbH7XRnQU/whmMcr0IRimuIjzgBpYyu4Sfd/RC2Y
L1sgfwOzhzs+K5uThEoBHqyj3FR1uQTiK+ccWsw/vJdiQZcTG2C4OsLPHIkmPvtY
fvw93IQHwPdBbOixF/21l05V3ZI/6ahxqFZ4R1xKASKXxvLyoIPbxCog+r0WbD42
LFCkXgFqFyXz8lrIXDliw6ySGwBErMTsR+e3VlpuNqaSOIisl6ntMD8WSv16WvFb
Arva0ytDceUH5eIkmw3y3BGCEgOc7LfX+b0xRy7Nqb6HrqE8R3X2sp5xZ5MdGicP
AgiauwnslvPbQF5h6Hv9xWLjN67Qx6+T9EpaCZIh651mIR0Vt6CMA9wQ+8EjasZE
xjFZ5lL6UNEMumfqZ9vyhsJH9PfcfiFY6Ndnsx1MkZ5s/9qQ1oqO+onOPmQr7BDw
h3+0lKxnJPgKb0BU7pFMqEDNjbuORtJh4GKTdnM19IhP7p1i6898JZTHtUY22OUq
PoMsnfFmW95wieR3jK/7C8tp/d4E5OtXURsyZDMgqo4UJPz1o8AnumFJDx9p/hSl
vvbVsybXueboRhyghJE41OYQzOsnWsBR1a6a68jPYuxhIKxn/olXetSK087R4tK0
FX1MtO38Tj0Nu8treTfy9cMI5NQ/o8kktRi/5bXZr/qW9PoGi7tigu3U6c80eCfa
txcrsaFYJTyoYKXx+KnjJf/WKRiHB+3cbe4twjCeUaKV344RGajYipzflmEUFE1c
Y/TmebHV52BJmvyAXmzxgebZo4WU0tm/3/ZYiHYNfVP/Q/nzBfHEP8pOjs2mebYl
bSEVB9y1X49yadE6nAnNHJROzCUa67R4Hk12/aSLurG3QLXwO+4XvbwuPlcFkoeZ
VjzXYmknaQ9odCi6aXhwCmzuXNuM6LxbgAgJi9UDMyq0y8XId8LOzeeYd5f+q7XE
L4sHGYS+T39J9ZyH6+9xsfkwm2MWgS3TH3EBHC0MRWz1UaMUWgx5Fgex9AhwtfPs
lCxoT8mYhX59pKtKoHiI7ma8SIDvmQPIaLkq43UisLwxoX4s+917ha63kabL+G20
CvrOGTKnOH5I2TMc123cbqFKHZgcojPRu4OMtarhLvBGLiL0xXB2UI68RC4UH7N4
iM57FnyTKAgK4dc5+Dpv9957JYeMMLtj8FLrX1RZX9jCcFZUWrAmcW73AplXkpGP
T4VkwUmMDifvzWJ2aNZAJhzl2xobS5t0ru4kuiwyT9p+1833YTToE/ggQ58Evxi8
0sLq1Ska9bMlhxs3/ZsS7AuXmlrDV1DZ3JgFECcMCRmH5KDdSFIbv4ugILSRbpPI
n0kGQ5gkMvQF1dcHPs8t3xUDkxzL1lbmy3f8VgQikk2Fv+DKJlEN00wNtoKYOGmV
5CdcEfOFZ68S2NzHhjq2WnYN9xRURw4QqdQPG0kdbDpPL3pwMhmjdPOGqgAbE7fk
JDpnQrQRldi2rUq4JkI8ypPG1LUymTqfuh2WwDWtCrmKF4c4T16TCTSpg1x5uDXx
-----END RSA PRIVATE KEY-----
        ";
        string passphrase = "Private_passphrase_to_Generate_pem";
        string expectedModulus = "22641399824818642750551508154127447036682381378321669356176380728167292898796385839393084570150284340617897627730772633003499673989604725752142094341340431573426625001115922730000399963623951774364126713491880861463415778646689153681396261878160515929198144175513973904838254235129930595649034403224670131525758948858806707653492728768086089489581416794190904567346862377909906566856638774383311427851015172228904845623078745687449001129109858876382736394931368397438878681853516712151389603150692254752379966282322297226399427150105703755431473529465274933402754737519217195671741078569598908079733301041302578587241";
        string expectedExponent = "849730488260432026324923164867050196551932270902238093724940507484326008420885768505900953793149508890913239928080448725687898410742508559439228149624806223934573246452305164208141717663754394986435236042306333320954841527067101673783698456070976776163934196165435863959903109948968938051152250151386219318858195914447895239641052390275377003216821487617970445661491904959055962215652526094676433349987087425626098999062529860424376820381803445414665758820708698472532775269812803204119818162808483967837137396729470664808728023017408853598181807179319696441231957095416212458255936472116741799364028148138462286113";

        // Act
        var result = KeyHelpers.CreatePrivateKeyWrapperWithPassPhrase(pemKey, passphrase);

        // Assert
        Assert.IsNotNull(result);
        Assert.AreEqual(expectedModulus, result.Modulus.ToString());
        Assert.AreEqual(expectedExponent, result.Exponent.ToString());
    }
}
