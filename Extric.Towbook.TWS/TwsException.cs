using System;

[Serializable]
public class TwsException : Exception
{
    public TwsException() { }
    public TwsException(string message) : base(message) { }
    public TwsException(string message, Exception inner) : base(message, inner) { }
    protected TwsException(
      System.Runtime.Serialization.SerializationInfo info,
      System.Runtime.Serialization.StreamingContext context)
        : base(info, context) { }
}
