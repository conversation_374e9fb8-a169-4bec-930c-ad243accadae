{"id": "fc1f02e0-c1db-41eb-9f04-52e37cb7f2ca", "name": "Motorclubs", "values": [{"key": "protocol", "value": "http", "type": "default", "enabled": true}, {"key": "auth", "value": "cee7217e1e194e808698e504e0a4f8109b5a180271bc418bad384a6ee5b0ab34", "type": "default", "enabled": true}, {"key": "host", "value": "localhost", "type": "default", "enabled": true}, {"key": "timestamp", "value": "Thu Mar 21 2024 10:35:04 GMT-0400 ", "type": "default", "enabled": true}, {"key": "extended", "value": "true", "type": "default", "enabled": true}, {"key": "page", "value": "1", "type": "default", "enabled": true}, {"key": "time<PERSON><PERSON>y", "value": "5000", "type": "default", "enabled": true}, {"key": "allstateContractorId", "value": "MI7124", "type": "default", "enabled": true}, {"key": "pageSize", "value": "5", "type": "default", "enabled": true}, {"key": "masterAccountReasonId", "value": "0", "type": "any", "enabled": true}, {"key": "masterAccountId", "value": "0", "type": "any", "enabled": true}, {"key": "purchaseOrderNumber", "value": "**********", "type": "default", "enabled": true}, {"key": "responseId", "value": "4603833", "type": "default", "enabled": true}, {"key": "callRequestId", "value": "0", "type": "default", "enabled": true}, {"key": "callId", "value": "0", "type": "default", "enabled": true}, {"key": "anotherCallRequestId", "value": "0", "type": "default", "enabled": true}, {"key": "assetsId", "value": "0", "type": "any", "enabled": true}, {"key": "driverId", "value": "0", "type": "default", "enabled": true}, {"key": "truckId", "value": "0", "type": "default", "enabled": true}, {"key": "waypointPickupId", "value": "0", "type": "any", "enabled": true}, {"key": "waypointTowingId", "value": "0", "type": "any", "enabled": true}, {"key": "waypointsDestinationId", "value": "0", "type": "any", "enabled": true}, {"key": "allstateLoginUser", "value": "j<PERSON><PERSON><PERSON><PERSON>", "type": "default", "enabled": true}, {"key": "allstateLoginPassword", "value": "44dev27", "type": "default", "enabled": true}, {"key": "swoopLoginUser", "value": "j<PERSON><PERSON><PERSON><PERSON>", "type": "default", "enabled": true}, {"key": "swoopLoginPassword", "value": "44dev27", "type": "default", "enabled": true}, {"key": "swoopDriverId", "value": "0", "type": "default", "enabled": true}, {"key": "swoopTruckId", "value": "0", "type": "default", "enabled": true}, {"key": "sykesLoginUser", "value": "j<PERSON><PERSON><PERSON><PERSON>", "type": "default", "enabled": true}, {"key": "sykesL<PERSON>in<PERSON>assword", "value": "44dev27", "type": "default", "enabled": true}, {"key": "sykesDriverId", "value": "0", "type": "default", "enabled": true}, {"key": "sykesTruckId", "value": "0", "type": "default", "enabled": true}, {"key": "sykesContractorId", "value": "FA262CAE-BB54-4FDE-B130-1C3C0438C6A1", "type": "default", "enabled": true}, {"key": "sykesReferenceId", "value": "2c2ccdc7-e810-40cf-92b1-63a1f719d781", "type": "default", "enabled": true}, {"key": "sykesAuthorization", "value": "Bearer f3666d70c70945a6997016271e73d22b", "type": "default", "enabled": true}, {"key": "sykesDistpatchId", "value": "u0230k0tp8", "type": "default", "enabled": true}, {"key": "urgentlyLogin<PERSON>ser", "value": "j<PERSON><PERSON><PERSON><PERSON>", "type": "default", "enabled": true}, {"key": "urgentlyLogin<PERSON><PERSON><PERSON>", "value": "44dev27", "type": "default", "enabled": true}, {"key": "urgentlyCaseId", "value": "0", "type": "default", "enabled": true}, {"key": "urgentlyProviderId", "value": "<EMAIL>", "type": "default", "enabled": true}, {"key": "anotherUrgentlyProviderId", "value": "<EMAIL>", "type": "default", "enabled": true}, {"key": "urgentlyOfferExpires", "value": "0", "type": "default", "enabled": true}, {"key": "completionTime", "value": "0", "type": "default", "enabled": true}, {"key": "swoopWebhookSecret", "value": "ufkSSu2vMdlQcNAucX3UaDLRDZv2UBFJGGqPjwTmuhj", "type": "default", "enabled": true}, {"key": "retriesCount", "value": "0", "type": "default", "enabled": true}, {"key": "maxRetries", "value": "3", "type": "default", "enabled": true}, {"key": "swoopDriver2Id", "value": "", "type": "any", "enabled": true}, {"key": "etaDate", "value": "2024-04-26T23:42:04Z", "type": "default", "enabled": true}, {"key": "honkLoginUser", "value": "j<PERSON><PERSON><PERSON><PERSON>", "type": "default", "enabled": true}, {"key": "honkLogin<PERSON>assword", "value": "44dev27", "type": "default", "enabled": true}, {"key": "honkAuthorization", "value": "Bearer aa143986787f459c92c9b1b65393d7d5", "type": "default", "enabled": true}, {"key": "honkProviderId", "value": "101159911", "type": "default", "enabled": true}, {"key": "honkDriverId", "value": "0", "type": "default", "enabled": true}, {"key": "honkTruckId", "value": "0", "type": "default", "enabled": true}, {"key": "honkReferenceId", "value": "0", "type": "default", "enabled": true}, {"key": "honkDispatchId", "value": "0", "type": "default", "enabled": true}, {"key": "aaaAceLoginUser", "value": "j<PERSON><PERSON><PERSON><PERSON>", "type": "default", "enabled": true}, {"key": "aaaAceLoginPassword", "value": "44dev27", "type": "default", "enabled": true}, {"key": "aaaAceDriverId", "value": "0", "type": "default", "enabled": true}, {"key": "aaaAceTruckId", "value": "0", "type": "default", "enabled": true}, {"key": "aaaAceAuthorization", "value": "Bearer Gv0sGl810pOtyjJdmJNidw==", "type": "default", "enabled": true}, {"key": "aaaAceDispatchId", "value": "0", "type": "default", "enabled": true}, {"key": "aaaAceContractorId", "value": "353858", "type": "default", "enabled": true}], "_postman_variable_scope": "environment", "_postman_exported_at": "2024-06-06T15:48:47.007Z", "_postman_exported_using": "Postman/10.24.25"}