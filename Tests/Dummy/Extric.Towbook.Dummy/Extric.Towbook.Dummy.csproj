<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>9d92ffcc-16da-450a-b1cb-617b1549ccc9</UserSecretsId>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    <DockerfileContext>..\..\..</DockerfileContext>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Azure.Identity" Version="1.12.0" />
    <PackageReference Include="Azure.Security.KeyVault.Secrets" Version="4.6.0" />
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.21.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\..\Integrations\MotorClubs\Swoop\Swoop.csproj" />
    <ProjectReference Include="..\..\..\Integrations\MotorClubs\Urgently\Urgently.csproj" />
  </ItemGroup>

</Project>
