using Newtonsoft.Json;
using Extric.Towbook.Integrations.MotorClubs.Swoop.Model;
using Extric.Towbook.Integrations.MotorClubs.Swoop;
using Extric.Towbook.Utility;
using Microsoft.AspNetCore.Http.Extensions;
using static Extric.Towbook.Integrations.MotorClubs.Swoop.SwoopRestClient;

namespace Extric.Towbook.Dummy.MotorClubs.Controllers;

public static class SwoopDummy
{
    public static void AddSwoopDummyEndpoints(this IEndpointRouteBuilder app)
    {
        app.MapPost(
            "/dummy/swoop/graphql",
            async (HttpRequest request) => 
            {
                using var reader = new StreamReader(
                    request.Body,
                    Encoding.UTF8
                );
                var rawJson = await reader.ReadToEndAsync();

                Console.WriteLine("Requested path : " + request.GetEncodedPathAndQuery());
                Console.WriteLine("Body request: " + rawJson);

                rawJson = rawJson.Replace("\\r", string.Empty).
                    Replace("\\n", string.Empty).
                    Replace("\r", string.Empty).
                    Replace("\n", string.Empty).
                    Replace("\t", string.Empty).
                    Replace("\\\"", "\"");

                if (rawJson.StartsWith("{\"query\":\"mutation updateJobEta {  updateJob(input: {"))
                {
                    return UpdateGps(rawJson);
                }

                if (rawJson.StartsWith("{\"query\":\"mutation UpdateDriverLocation {  updateDriverLocation(input:"))
                {
                    return UpdateDriverLocation(rawJson);
                }

                if (rawJson.StartsWith("{\"query\":\"mutation UpdatePartnerVehicle {              updatePartnerVehicle(input:"))
                {
                    return UpdateGps(rawJson);
                }

                if (rawJson.StartsWith("{\"query\":\"mutation UpdateJobDriver {  updateJob(input:"))
                {
                    return AssignDriverToJob(rawJson);
                }

                if (rawJson.StartsWith("{\"query\":\"mutation UpdateUserVehicle {  updateUser(input:"))
                {
                    return UpdateUserVehicle(rawJson);
                }

                if (rawJson.StartsWith("{\"query\":\"mutation CreatePartnerVehicle {"))
                {
                    return AddVehicle(rawJson);
                }

                if (rawJson.StartsWith("{\"query\":\"query {                partnerVehicles {"))
                {
                    return SearchVehicles(rawJson);
                }

                if (rawJson.StartsWith("{\"query\":\"mutation RejectJob {  updateJobStatus(input: {"))
                {
                    return await SearchRejectJob(rawJson);
                }

                if (rawJson.StartsWith("{\"query\":\"mutation AcceptJob {  updateJobStatus(input: {    job: {      id: \""))
                {
                    return SearchAcceptJob(rawJson);
                }

                if (rawJson.StartsWith("{\"query\":\"mutation UpdateJobStatus {              updateJobStatus(input: {                job: {"))
                {
                    return await SearchUpdateStatus(rawJson);
                }

                if (rawJson.StartsWith("{\"query\":\"query JobStatusReasonsAvailable"))
                {
                    return SearchJobStatusReasons();
                }

                if (rawJson.StartsWith("{\"query\":\"query {                users {"))
                {
                    return SearchUsers();
                }

                if (rawJson.StartsWith("{\"query\":\"mutation {  createUser ("))
                {
                    return CreateUser(rawJson);
                }

                if (rawJson.StartsWith("{\"query\":\"mutation {  updateUser ("))
                {
                    return UpdateUser(rawJson);
                }

                if (rawJson.StartsWith("{\"query\":\"query {  job(id:"))
                {
                    return await SearchJobId(rawJson);
                }

                return Results.NotFound();
            }
            )
            .WithName("PostSwoopGraphql")
            .Produces(StatusCodes.Status200OK)
            .WithSummary("Takes the request param and returns a graphql request")
            .WithDescription("Endpoint to simulate a post for getting graphql request");
    }

    public class dataClassEdge
    {
        public IEnumerable<object> edges { get; set; }
        public object pageInfo { get; set; }
    }

    private static IResult UpdateDriverLocation(string rawJson)
    {
        var data = new
        {
        };
        return Results.Ok(new GraphData<dynamic>()
        {
            Data = data,
            Errors = null
        });
    }

    private static IResult UpdateGps(string rawJson)
    {
        var data = new
        {
        };
        return Results.Ok(new GraphData<dynamic>()
        {
            Data = data,
            Errors = null
        });    
    }

    private static IResult AssignDriverToJob(string rawJson)
    {
        var strTmp = rawJson.Substring(rawJson.IndexOf("updateJob(input:") + 17);
        strTmp = strTmp.Substring(strTmp.IndexOf("job: {") + 6).Trim();
        strTmp = strTmp.Substring(strTmp.IndexOf("id: \"") + 5).Trim();
        var strTmp2 = strTmp.Substring(strTmp.IndexOf("name: \"") + 7).Trim();

        var jobId = strTmp.Substring(0, strTmp.IndexOf("\""));
        var driverName = strTmp2.Substring(0, strTmp2.IndexOf("\"") - 1).Trim();

        var data = new
        {
            job = new
            {
                id = jobId
            },
            driver = new
            {
                name = driverName
            }
        };
        return Results.Ok(new GraphData<dynamic>()
        {
            Data = data,
            Errors = null
        });
    }

    private static IResult UpdateUserVehicle(string rawJson)
    {
        var strQuery = rawJson.Substring(rawJson.IndexOf("updateJob(input:") + 17);
        strQuery = strQuery.Substring(strQuery.IndexOf("job: {") + 6).Trim();
        strQuery = strQuery.Substring(strQuery.IndexOf("id: \"") + 5).Trim();
        var strDriver = strQuery.Substring(strQuery.IndexOf("name: \"") + 7).Trim();

        var jobId = strQuery.Substring(0, strQuery.IndexOf("\""));
        var driverName = strDriver.Substring(0, strDriver.IndexOf("\"")).Trim();

        var data = new
        {
            job = new
            {
                id = jobId
            },
            driver = new
            {
                name = driverName
            }
        };
        return Results.Ok(new GraphData<dynamic>()
        {
            Data = data,
            Errors = null
        });
    }

    private static IResult AddVehicle(string rawJson)
    {
        var data = new
        {
            createPartnerVehicle = new
            {
                partnerVehicle = new
                {
                    id = "10",
                    name = "truck1"
                }
            }
        };
        return Results.Ok(new GraphData<dynamic>()
        {
            Data = data,
            Errors = null
        });
    }

    private static IResult SearchVehicles(string rawJson)
    {
        var edges = new List<object>();
        edges.Add(new
        {
            node = new
            {
                id = "10",
                name = "truck1"
            }
        });

        var data = new
        {
            partnerVehicles = new
            {
                edges = edges,
                pageInfo = new
                {
                    hasNextPage = false
                }
            }
        };
        return Results.Ok(new GraphData<dynamic>()
        {
            Data = data,
            Errors = null
        });
    }

    private async static Task<IResult> SearchRejectJob(string rawJson)
    {
        var strQuery = rawJson.Substring(rawJson.IndexOf("updateJobStatus(input:") + 23);
        strQuery = strQuery.Substring(strQuery.IndexOf("job: {") + 6).Trim();
        strQuery = strQuery.Substring(strQuery.IndexOf("id: \"") + 5).Trim();
        var strStatus = strQuery.Substring(strQuery.IndexOf("status: ") + 8).Trim();

        var jobId = strQuery.Substring(0, strQuery.IndexOf("\" "));
        var status = strStatus.Substring(0, strStatus.IndexOf("reason:") - 1).Trim();

        await Core.SetRedisValueAsync("swoop_dummy_status:" + jobId, status, TimeSpan.FromMinutes(5));

        var data = new
        {
            updateJobStatus = new
            {
                job = new
                {
                    id = jobId,
                    status = status
                }
            }
        };
        return Results.Ok(new GraphData<dynamic>()
        {
            Data = data,
            Errors = null
        });
    }

    private async static Task<IResult> SearchUpdateStatus(string rawJson)
    {
        var strQuery = rawJson.Substring(rawJson.IndexOf("updateJobStatus(input:") + 23);
        strQuery = strQuery.Substring(strQuery.IndexOf("job: {") + 6).Trim();
        strQuery = strQuery.Substring(strQuery.IndexOf("id: \"") + 5).Trim();
        var strStatus = strQuery.Substring(strQuery.IndexOf("status: ") + 8).Trim();

        var jobId = strQuery.Substring(0, strQuery.IndexOf("\" "));
        var status = strStatus.Substring(0, strStatus.IndexOf("}") - 1).Trim();

        await Core.SetRedisValueAsync("swoop_dummy_status:" + jobId, status, TimeSpan.FromMinutes(5));

        var data = new
        {
            updateJobStatus = new
            {
                job = new
                {
                    id = jobId,
                    status = status
                }
            }
        };
        return Results.Ok(new GraphData<dynamic>()
        {
            Data = data,
            Errors = null
        });
    }

    private static IResult SearchAcceptJob(string rawJson)
    {
        var strQuery = rawJson.Substring(rawJson.IndexOf("updateJobStatus(input:") + 23);
        strQuery = strQuery.Substring(strQuery.IndexOf("job: {      id: \"") + 17);
        var jobId = strQuery.Substring(0, strQuery.IndexOf("\" "));

        var data = new
        {
            updateJobStatus = new
            {
                job = new
                {
                    id = jobId,
                    status = "active"
                }
            }
        };
        return Results.Ok(new GraphData<dynamic>()
        {
            Data = data,
            Errors = null
        });
    }

    private static IResult SearchJobStatusReasons()
    {
        return Results.Ok(new GraphData<dynamic>()
        {
            Data = new { },
            Errors = null
        });
    }

    private static IResult CreateUser(string rawJson)
    {
        var strQuery = rawJson.Substring(rawJson.IndexOf("name: \"") + 7);
        var name = strQuery.Substring(0, strQuery.IndexOf("\" "));

        var roles = new List<object>();
        roles.Add(new
        {
            node = new
            {
                name = "admin"
            }
        });

        var swoopUser = new
        {
            Id = 1,
            Name = name,
            Phone = "1854521269",
            Email = "<EMAIL>",
            Roles = (new { edges = roles })
        };

        var edges = new List<object>();
        edges.Add(new
        {
            node = swoopUser
        });

        var data = new
        {
            createUser = new
            {
                user = swoopUser

            }
        };

        return Results.Ok(new GraphData<object>()
        {
            Data = data,
            Errors = null
        });
    }

    private static IResult UpdateUser(string rawJson)
    {
        var strQuery = rawJson.Substring(rawJson.IndexOf("id: \"") + 5);
        var id = Int32.Parse(strQuery.Substring(0, strQuery.IndexOf("\" ")));

        var roles = new List<object>();
        roles.Add(new
        {
            node = new
            {
                name = "admin"
            }
        });

        var swoopUser = new
        {
            Id = id,
            Roles = (new { edges = roles })
        };

        var edges = new List<object>();
        edges.Add(new
        {
            node = swoopUser
        });

        var data = new
        {
            updateUser = new
            {
                user = swoopUser

            }
        };

        return Results.Ok(new GraphData<object>()
        {
            Data = data,
            Errors = null
        });
    }

    private static IResult SearchUsers()
    {
        var roles = new List<object>();
        roles.Add(new
        {
            node = new
            {
                name = "admin"
            }
        });

        var swoopUser = new
        {
            Id = 1,
            Name = "Test Admin",
            Phone = "1854521269",
            Email = "<EMAIL>",
            Roles = (new { edges = roles })
        };

        var edges = new List<object>();
        edges.Add(new
        {
            node = swoopUser
        });

        var data = new
        {
            users = new dataClassEdge
            {
                edges = edges,
                pageInfo = new
                {
                    hasNextPage = false
                }
            }
        };

        return Results.Ok(new GraphData<object>()
        {
            Data = data,
            Errors = null
        });
    }

    private async static Task<IResult> SearchJobId(string rawJson)
    {
        var strQuery = rawJson.Substring(rawJson.IndexOf("job(id: \"") + 9);
        var jobId = strQuery.Substring(0, strQuery.IndexOf("\") {"));

        var message = SwoopMessageByRequestId(jobId);

        if (message == null)
        {
            return Results.BadRequest();
        }

        var swoopJob = JsonConvert.DeserializeObject<SwoopJob>(message.JsonData);
        if (swoopJob.Eta == null)
        {
            swoopJob.Eta = new JobEta()
            {
                Current = DateTime.Now.AddMinutes(5)
            };
        }
        if (swoopJob.Notes == null)
        {
            swoopJob.Notes = new Notes();
        }

        var status = await Core.GetRedisValueAsync("swoop_dummy_status:" + jobId);
        if (!string.IsNullOrEmpty(status))
        {
            swoopJob.Status = status;
        }

        var swoopjobContainer = new
        {
            Job = swoopJob
        };

        var graphData = new GraphData<object>()
        {
            Data = swoopjobContainer,
            Errors = null
        };

        return Results.Ok(graphData);
    }

    private static SwoopMessage SwoopMessageByRequestId(string requestId)
    {
        return SqlMapper.Query<SwoopMessage>("SELECT * FROM MCDispatch.SwoopMessages WITH (nolock) WHERE RequestId=@requestId ORDER BY CreateDate DESC",
            new { @RequestId = requestId }).FirstOrDefault();
    }
}



