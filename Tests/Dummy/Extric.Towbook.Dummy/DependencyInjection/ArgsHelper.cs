namespace Extric.Towbook.Dummy.DependencyInjection;

public record ArgumentConfig(
    int Port,
    string BasePath,
    bool IsHttps
);

public static class ArgsHelper
{
    public const int DEFAULT_PORT = 9999;
    public const int DEFAULT_HTTPS_PORT = 9009;
    public const string DEFAULT_BASE_PATH = "/dummy";

    public static int CalculatePort(string[] args, bool isHttps)
    {
        var pIndex = Array.IndexOf(args, "-p");
        var defaultPort = isHttps ? DEFAULT_HTTPS_PORT : DEFAULT_PORT;

        if (pIndex == -1)
        {
            return DEFAULT_PORT;
        }

        var portProvided = int.TryParse(args[pIndex + 1], out var port);

        return portProvided ? port : defaultPort;
    }

    public static string CalculateBasePath(string[] args)
    {
        var pIndex = Array.IndexOf(args, "-b");

        if (pIndex == -1)
        {
            return DEFAULT_BASE_PATH;
        }

        var basePath = args[pIndex + 1];
        var basePathProvided = string.IsNullOrWhiteSpace(basePath);

        return basePathProvided ? basePath : DEFAULT_BASE_PATH;
    }

    public static bool CalculateIsHttps(string[] args)
    {
        return Array.IndexOf(args, "-s") != -1;
    }

    public static ArgumentConfig GetArgumentConfig(string[] args)
    {
        var isHttps = CalculateIsHttps(args);

        return new(
            CalculatePort(args, isHttps),
            CalculateBasePath(args),
            isHttps
        );
    }
}
