using Extric.Towbook.Dispatch;

namespace Extric.Towbook.Tests.StorageTests;

public class SignatureTests : IDisposable
{
    public void Dispose() { }

    [Theory]
    [InlineData("image/jpg", ".jpg")]
    [InlineData("image/jpeg", ".jpg")]
    [InlineData("image/png", ".png")]
    [InlineData("image/bmp", ".bmp")]
    [InlineData("image/gif", ".gif")]
    [InlineData("image/tiff", ".tif")]
    [InlineData("image/mp4", "")]
    public async Task Location_WhenSavingAndRetrievingFilePath_ShouldResolveCorrectlyAsync(
        string contentType,
        string expectedExtension
    )
    {
        // Arrange
        var companyId = 10;
        var signature = new Signature
        {
            ContentType = contentType,
            DispatchEntryId = 1,
            DispatchEntrySignatureId = 1,
        };
        var separator = Path.DirectorySeparatorChar;
        var expectedFullPath =
            $"{Path.GetTempPath()}Towbook{separator}Storage{separator}dispatchEntries{separator}Signatures{separator}{companyId}{separator}{signature.DispatchEntryId}{separator}{signature.DispatchEntrySignatureId}{expectedExtension}";

        // Act
        var savePath = signature.Location.Replace("%1", companyId.ToString());

        // Assert
        Assert.NotNull(signature);
        Assert.Equal(expectedFullPath, savePath);

        await FileStubHelper.CreateFileAsync(savePath);

        Assert.True(File.Exists(savePath));
        FileStubHelper.CleanupPathAsync(savePath);
    }

    /// <summary>
    /// Not exactly a UT, but will have to do due to the nature of the hard-coded 
    /// and coupled code-base. Will connect to S3 to get a URL for an S3 instance. This 
    /// specifically tests out if the resolved URL makes sense, mainly done to make sure
    /// Signature.Location didn't cause regression. This specifically is code that
    /// is called by <see cref="SignaturesController.GetSignatureFile"/>
    /// </summary>
    [Fact]
    public void GetPresignedUrl_WhenTryingToGetS3Url_ShouldResolveCorrectly()
    {
        // Arrange
        var companyId = -10;
        var signature = new Signature
        {
            ContentType = "image/jpg",
            DispatchEntryId = 1,
            DispatchEntrySignatureId = 1,
        };
        var savePath = signature.Location.Replace("%1", companyId.ToString());
        var expectedS3Path = $"https://s3.amazonaws.com/storage.towbook.net/dispatchEntries/Signatures/{companyId}/{signature.DispatchEntryId}/{signature.DispatchEntrySignatureId}.jpg?";

        // Act
        var result = FileAccesor.GetPresignedUrl(
            new Storage.FileUtility(),
            savePath,
            contentType: "image/jpeg",
            expiresInMinutes: 30,
            verb: Amazon.S3.HttpVerb.GET
        );

        // Assert
        Assert.NotNull(result);
        Assert.StartsWith(expectedS3Path, result);
    }
}
