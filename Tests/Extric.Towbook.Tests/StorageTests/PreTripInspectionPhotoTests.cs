using Extric.Towbook.PreTripInspections;

namespace Extric.Towbook.Tests.StorageTests;

public class PreTripInspectionPhotoTests
{
    [Fact]
    public async Task Location_WhenSavingAndRetrievingSampleFilePath_ShouldResolveCorrectlyAsync()
    {
        // Arrange
        var companyId = 1;
        var expectedExtension = ".jpg";
        var preTripInspectionPhoto = new PreTripInspectionPhoto
        {
            PreTripInspectionPhotoId = 1,
            PreTripInspectionId = 1,
        };

        var separator = Path.DirectorySeparatorChar;
        var expectedFullPath =
            $"{Path.GetTempPath()}Towbook{separator}Storage{separator}PreTripInspections{separator}Photos{separator}Sample{separator}{preTripInspectionPhoto.PreTripInspectionPhotoId}{expectedExtension}";

        // Act
        var savePath = preTripInspectionPhoto.Location.Replace(
            "%1",
            companyId.ToString()
        );

        // Assert
        Assert.NotNull(preTripInspectionPhoto);
        Assert.Equal(expectedFullPath, savePath);

        await FileStubHelper.CreateFileAsync(savePath);

        Assert.True(File.Exists(savePath));
        FileStubHelper.CleanupPathAsync(savePath);
    }

    [Fact]
    public async Task Location_WhenSavingAndRetrievingFilePath_ShouldResolveCorrectlyAsync()
    {
        // Arrange
        var companyId = 1;
        var expectedExtension = ".jpg";
        var preTripInspectionPhoto = new PreTripInspectionPhoto
        {
            PreTripInspectionId = 2,
            PreTripInspectionPhotoId = 1,
        };

        var separator = Path.DirectorySeparatorChar;
        var expectedFullPath =
            $"{Path.GetTempPath()}Towbook{separator}Storage{separator}PreTripInspections{separator}Photos{separator}{companyId}{separator}{preTripInspectionPhoto.PreTripInspectionId}{separator}{preTripInspectionPhoto.PreTripInspectionPhotoId}{expectedExtension}";

        // Act
        var savePath = preTripInspectionPhoto.Location.Replace(
            "%1",
            companyId.ToString()
        );

        // Assert
        Assert.NotNull(preTripInspectionPhoto);
        Assert.Equal(expectedFullPath, savePath);

        await FileStubHelper.CreateFileAsync(savePath);

        Assert.True(File.Exists(savePath));
        FileStubHelper.CleanupPathAsync(savePath);
    }
}
