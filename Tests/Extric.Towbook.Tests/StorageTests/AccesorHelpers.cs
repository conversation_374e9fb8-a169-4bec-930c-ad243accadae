using System.Runtime.CompilerServices;
using Amazon.S3;
using Extric.Towbook.Accounts;
using Extric.Towbook.Dispatch;
using Extric.Towbook.Storage;
using Photo = Extric.Towbook.Stickering.Photo;

namespace Extric.Towbook.Tests.StorageTests;

public static class FileAccesor
{
    [UnsafeAccessor(UnsafeAccessorKind.StaticMethod, Name = "GetPresignedUrl")]
    public static extern string GetPresignedUrl(
        FileUtility fileUtility,
        string path,
        string contentType,
        int expiresInMinutes,
        HttpVerb verb
    );
}

public static class ParkingPermitPhotoAccesor
{
    [UnsafeAccessor(UnsafeAccessorKind.Method, Name = "set_Id")]
    public static extern void SetId(ParkingPermitPhoto photo, int id);
}

public static class EntryDamageVideoAccesor
{
    [UnsafeAccessor(UnsafeAccessorKind.Method, Name = "set_Id")]
    public static extern void SetId(EntryDamageVideo video, int id);
}

public static class PhotoAccesor
{
    [UnsafeAccessor(UnsafeAccessorKind.Method, Name = "set_Id")]
    public static extern void SetId(Photo photo, int id);
}
