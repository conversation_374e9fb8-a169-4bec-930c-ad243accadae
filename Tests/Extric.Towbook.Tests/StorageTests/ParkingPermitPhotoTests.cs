using Extric.Towbook.Accounts;

namespace Extric.Towbook.Tests.StorageTests;

public class ParkingPermitPhotoTests
{
    [Theory]
    [InlineData("image/jpg", ".jpg")]
    [InlineData("image/jpeg", ".jpg")]
    [InlineData("image/png", ".png")]
    [InlineData("image/bmp", ".bmp")]
    [InlineData("image/gif", ".gif")]
    [InlineData("image/tiff", ".tif")]
    [InlineData("image/mp4", "")]
    public async Task Location_WhenSavingAndRetrievingFilePath_ShouldResolveCorrectlyAsync(
        string contentType,
        string expectedExtension
    )
    {
        // Arrange
        var companyId = 10;
        var parkingPermitPhotoId = 1;
        var parkingPermitPhoto = new ParkingPermitPhoto
        {
            ContentType = contentType,
            ParkingPermitId = 1,
        };

        ParkingPermitPhotoAccesor.SetId(parkingPermitPhoto, id: parkingPermitPhotoId);

        var separator = Path.DirectorySeparatorChar;
        var expectedFullPath =
            $"{Path.GetTempPath()}Towbook{separator}Storage{separator}permits{separator}Photos{separator}{companyId}{separator}{parkingPermitPhoto.ParkingPermitId}{separator}{parkingPermitPhotoId}{expectedExtension}";

        // Act
        var savePath = parkingPermitPhoto.Location.Replace("%1", companyId.ToString());

        // Assert
        Assert.NotNull(parkingPermitPhoto);
        Assert.Equal(expectedFullPath, savePath);

        await FileStubHelper.CreateFileAsync(savePath);

        Assert.True(File.Exists(savePath));
        FileStubHelper.CleanupPathAsync(savePath);
    }
}
