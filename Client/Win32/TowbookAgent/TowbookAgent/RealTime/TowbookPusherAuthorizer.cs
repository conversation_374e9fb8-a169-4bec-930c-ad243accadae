using Extric.Towbook.AgentClient.ApiClient;
using PusherClient;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Extric.Towbook.AgentClient.RealTime
{
    class TowbookPusherAuthorizer : IAuthorizer
    {
        private TowbookClient _client;

        public TowbookPusherAuthorizer(TowbookClient client)
        {
            _client = client;
        }

        public string Authorize(string channelName, string socketId)
        {
            return _client.Pusher(channelName, socketId);
        }
    }
}
