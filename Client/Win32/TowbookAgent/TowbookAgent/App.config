<?xml version="1.0" encoding="utf-8"?>
<configuration>
    <configSections>
        <sectionGroup name="userSettings" type="System.Configuration.UserSettingsGroup, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
            <section name="Extric.Towbook.AgentClient.Properties.Settings" type="System.Configuration.ClientSettingsSection, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" allowExeDefinition="MachineToLocalUser" requirePermission="false"/>
        </sectionGroup>
    </configSections>
    <startup> 
        <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.8"/>
    </startup>
    <userSettings>
        <Extric.Towbook.AgentClient.Properties.Settings>
            <setting name="LastSync" serializeAs="String">
                <value>2000-01-01</value>
            </setting>
            <setting name="MinimizeToTray" serializeAs="String">
                <value>True</value>
            </setting>
            <setting name="CloseToTray" serializeAs="String">
                <value>True</value>
            </setting>
            <setting name="IsUpgrade" serializeAs="String">
                <value>True</value>
            </setting>
            <setting name="SavedUsername" serializeAs="String">
                <value/>
            </setting>
            <setting name="SavedPassword" serializeAs="String">
                <value/>
            </setting>
            <setting name="UseAsProxy" serializeAs="String">
                <value>False</value>
            </setting>
        </Extric.Towbook.AgentClient.Properties.Settings>
    </userSettings>
</configuration>
