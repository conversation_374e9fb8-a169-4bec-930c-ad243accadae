using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Extric.Towbook.AgentClient.ApiClient.Model
{
    /// <summary>
    /// What type of object are we sync'ing?
    /// </summary>
    public enum SyncEventType
    {
        Class = 1,
        Account = 2,
        Customer = 3,
        PaymentMethod = 4,
        Item = 5,
        TaxCode = 6,
        TaxItem = 7,
        Invoice = 8,
        Payment = 9
    }

    public enum SyncEventStatus
    {
        Created = 0,
        SentToAgent = 1,
        InProgress = 10,
        Success = 10,
        Failed = 20,
        Delayed = 50,
    }

    public class SyncEvent
    {
        public long Id { get; set; }
        public int AgentSessionId { get; set; }
        public SyncEventType Type { get; set; }
        public DateTime CreateDate { get; set; }
        public DateTime? LastUpdated { get; set; }
        public SyncEventStatus Status { get; set; }

        public int SourceIdType { get; set; }

        /// <summary>
        /// ID of the object in Towbook, such as a DispatchEntryId, PaymentId, etc.
        /// </summary>
        public int SourceId { get; set; }

        /// <summary>
        /// ID of the Agent.QBObject 
        /// </summary>
        public int ObjectId { get; set; }
    }
}
