using Extric.Towbook.AgentClient.Connectors.QuickBooks.Models;
using Extric.Towbook.AgentClient.Utility;
using QBFC13Lib;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Xml.Linq;

namespace Extric.Towbook.AgentClient.Connectors.QuickBooks
{
    public class LocalQuickBooksConnector : IDisposable
    {
        private QBSessionManager _sessionManager = new QBSessionManager();
        private bool _sessionBegun = true;

        public string LastXmlResponse { get; private set; }

        public string Filename
        {
            get
            {
                if (_sessionBegun)
                {
                    return _sessionManager?.GetCurrentCompanyFileName();
                }
                else
                {
                    return null;
                }
            }
        }

        private LocalQuickBooksConnector()
        {
            
        }

        public enum SessionResult
        {
            Success = 0,
            FailedQuickBooksNotOpen = 1,
            FailedQuickBooksOpenNoFile = 2,
            FailedQuickBooksUserDeniedAccess = 3,
            FailedQuickBooksNotInstalled = 4,
        }

        private SessionResult BeginSession()
        {
            try
            {
                _sessionManager.OpenConnection("", "Towbook");
                _sessionManager.BeginSession("", ENOpenMode.omDontCare);
                _sessionBegun = true;

            }
            catch(COMException e)
            {
                switch (e.Message)
                {
                    case "Could not start QuickBooks.":
                        return SessionResult.FailedQuickBooksNotOpen;

                    case "The QuickBooks user has denied access.":
                        return SessionResult.FailedQuickBooksUserDeniedAccess;

                    case "If the QuickBooks company data file is not open, a call to the \"BeginSession\" method must include the name of the data file.":
                        return SessionResult.FailedQuickBooksOpenNoFile;

                    case "Cannot create QBXMLRP2 COM component. ":
                        return SessionResult.FailedQuickBooksNotInstalled;
                }
                MessageBox.Show(e.Message);
            }

            return SessionResult.Success;    
        }

        public static LocalQuickBooksConnector Create()
        {
            SessionResult sr = SessionResult.Success;
            return Create(out sr);
        }
        public static LocalQuickBooksConnector Create(out SessionResult status)
        {
            try
            {
                var lqbc = new LocalQuickBooksConnector();
                status = lqbc.BeginSession();

                if (status != SessionResult.Success)
                    return null;

                return lqbc;
            }
            catch
            {
                status = SessionResult.FailedQuickBooksNotInstalled;
                return null;
            }
        }

        public void Dispose()
        {
            if (_sessionBegun)
            {
                if (_sessionManager != null)
                {
                    try
                    {
                        _sessionManager.EndSession();
                    }
                    catch
                    {

                    }
                    try
                    {
                        _sessionManager.CloseConnection();
                    }
                    catch
                    {

                    }
                }
            }
        }

        public IEnumerable<CustomerModel> GetCustomers(DateTime? modifiedFrom = null)
        {
            var msr = _sessionManager.GetMsgSetRequest();
            var cqr = msr.AppendCustomerQueryRq();
            cqr.ORCustomerListQuery.CustomerListFilter.ActiveStatus.SetValue(ENActiveStatus.asAll);

            if (modifiedFrom != null)
                cqr
                    .ORCustomerListQuery
                    .CustomerListFilter
                    .FromModifiedDate
                    .SetValue(modifiedFrom.Value, false);

            var results = _sessionManager.DoRequests(msr);

            var list = results.ResponseList.ToCustomerRetList()
                .Select(o => CustomerModel.Map(o));

            return list;
        }

        public CustomerModel GetCustomer(string name)
        {
            var msr = _sessionManager.GetMsgSetRequest();
            var cqr = msr.AppendCustomerQueryRq();
            //cqr.ORCustomerListQuery.CustomerListFilter.ActiveStatus.SetValue(ENActiveStatus.asAll);
            cqr.ORCustomerListQuery.FullNameList.Add(name);

            var results = _sessionManager.DoRequests(msr);

            var list = results.ResponseList.ToCustomerRetList()
                .Select(o => CustomerModel.Map(o));

            return list.FirstOrDefault();
        }

        public IEnumerable<ItemModel> GetItems(DateTime? modifiedFrom = null)
        {
            var msr = _sessionManager.GetMsgSetRequest();
            var cqr = msr.AppendItemServiceQueryRq();

            if (modifiedFrom != null)
                cqr
                    .ORListQueryWithOwnerIDAndClass
                    .ListWithClassFilter
                    .FromModifiedDate
                    .SetValue(modifiedFrom.Value, false);

            var results = _sessionManager.DoRequests(msr);

            var list = results.ResponseList.ToItemServiceRetList()
                .Select(o => ItemModel.Map(o));

            return list;
        }

        public IEnumerable<AccountModel> GetAccounts(DateTime? modifiedFrom = null)
        {
            var msr = _sessionManager.GetMsgSetRequest();
            var cqr = msr.AppendAccountQueryRq();
            if (modifiedFrom != null)
                cqr
                    .ORAccountListQuery
                    .AccountListFilter
                    .FromModifiedDate
                    .SetValue(modifiedFrom.Value, false);
            
            var results = _sessionManager.DoRequests(msr);

            var list = results.ResponseList.ToAccountRetList()
                .Select(o => AccountModel.Map(o));

            return list;
        }


        public IEnumerable<ClassModel> GetClasses(DateTime? modifiedFrom = null)
        {
            var msr = _sessionManager.GetMsgSetRequest();
            var cqr = msr.AppendClassQueryRq();
            cqr.ORListQuery.ListFilter.ActiveStatus.SetValue(ENActiveStatus.asAll);

            if (modifiedFrom != null)
                cqr.ORListQuery.ListFilter.FromModifiedDate.SetValue(modifiedFrom.Value, false);

            var results = _sessionManager.DoRequests(msr);

            var list = results.ResponseList.ToClassRetList()
                .Select(o => ClassModel.Map(o));

            return list;
        }

        public IEnumerable<InvoiceModel> GetInvoices(DateTime? modifiedFrom = null)
        {
            var msr = _sessionManager.GetMsgSetRequest();
            var cqr = msr.AppendInvoiceQueryRq();

            if (modifiedFrom != null)
                cqr
                    .ORInvoiceQuery
                    .InvoiceFilter
                    .ORDateRangeFilter
                    .ModifiedDateRangeFilter
                    .FromModifiedDate
                    .SetValue(modifiedFrom.Value, false);

            //cqr.IncludeRetElementList.Add("PONumber");
            cqr.IncludeLineItems.SetValue(true);
            cqr.IncludeLinkedTxns.SetValue(true);
            var results = _sessionManager.DoRequests(msr);

            var list = results.ResponseList.ToInvoiceRetList()
                .Select(o => InvoiceModel.Map(o));

            return list;
        }


        public enum InvoiceSelectType
        {
            TxnId = 0,
            RefNumber = 1
        }

        public InvoiceModel GetInvoice(string id, InvoiceSelectType type)
        {
            var msr = _sessionManager.GetMsgSetRequest();
            var cqr = msr.AppendInvoiceQueryRq();
            if (type == InvoiceSelectType.TxnId)
                cqr.ORInvoiceQuery.TxnIDList.Add(id);
            else if (type == InvoiceSelectType.RefNumber)
            {
                cqr.ORInvoiceQuery.RefNumberList.Add(id);
            }

            cqr.IncludeLineItems.SetValue(true);
            cqr.IncludeLinkedTxns.SetValue(true);

            var results = _sessionManager.DoRequests(msr);
            try
            {
                var list = results.ResponseList.ToInvoiceRetList()
                    .Select(o => InvoiceModel.Map(o)).ToList();

                if (type == InvoiceSelectType.RefNumber)
                {
                    return list.Where(o => o.RefNumber.ToLowerInvariant() == id.ToLowerInvariant()).FirstOrDefault();
                }

                return list.FirstOrDefault();
            }
            catch (Exception e)
            {
                throw new Exception("GetInvoice error finding " + id,
                    e);
            }
        }

        public ItemModel GetItem(string name)
        {
            var msr = _sessionManager.GetMsgSetRequest();
            var cqr = msr.AppendItemServiceQueryRq();
            cqr.ORListQueryWithOwnerIDAndClass.FullNameList.Add(name);
            var results = _sessionManager.DoRequests(msr);

            var list = results.ResponseList.ToItemServiceRetList()
                .Select(o => ItemModel.Map(o));

            return list.FirstOrDefault();
        }

        public ItemModel GetDiscountItem(string name)
        {
            var msr = _sessionManager.GetMsgSetRequest();
            var cqr = msr.AppendItemDiscountQueryRq();
            cqr.ORListQueryWithOwnerIDAndClass.FullNameList.Add(name);
            var results = _sessionManager.DoRequests(msr);

            var list = results.ResponseList.ToItemDiscountRetList()
                .Select(o => ItemModel.Map(o));

            return list.FirstOrDefault();
        }



        public enum PaymentSelectType
        {
            TxnId = 0,
            RefNumber = 1,
            InvoiceTxnId = 2,
        }

        public List<PaymentModel> GetPayment(string refNumber, PaymentSelectType type)
        {
            if (string.IsNullOrWhiteSpace(refNumber))
                return new List<PaymentModel>();
            var msr = _sessionManager.GetMsgSetRequest();
            var cqr = msr.AppendReceivePaymentQueryRq();

            if (type == PaymentSelectType.RefNumber)
            {
                cqr.ORTxnQuery.TxnFilter.ORRefNumberFilter.RefNumberFilter.RefNumber.SetValue(refNumber);
                cqr.ORTxnQuery.TxnFilter.ORRefNumberFilter.RefNumberFilter.MatchCriterion.SetValue(ENMatchCriterion.mcEndsWith);
                cqr.ORTxnQuery.TxnFilter.MaxReturned.SetValue(10);
            }
            else if (type == PaymentSelectType.TxnId)
                cqr.ORTxnQuery.TxnIDList.Add(refNumber);
            else if (type == PaymentSelectType.InvoiceTxnId)
            {
                return GetPaymentsAppliedTo(refNumber).ToList();
            }

            var results = _sessionManager.DoRequests(msr);
            var response = results.ResponseList.ToIResponse();

            if (response.StatusCode == 0)
            {

                try
                {
                    var list = results.ResponseList.ToReceivePaymentRetList()
                        .Select(o => PaymentModel.Map(o)).Where(o => o != null).ToList();

                    return list;
                }
                catch (Exception e)
                {
                    MessageBox.Show(e.ToString() + "\n\n\n" +
                        results.ToXMLString());

                    return new List<PaymentModel>();
                }
            }
            else if (response.StatusCode == 500 && response.StatusMessage.Contains("could not be found"))
            {
                return new List<PaymentModel>();
            }
            else
            {
                throw new Exception(response.StatusCode + ", " + response.StatusMessage);
            }
        }


        public IEnumerable<PaymentMethodModel> GetPaymentMethods(DateTime? modifiedFrom = null)
        {
            var msr = _sessionManager.GetMsgSetRequest();
            var cqr = msr.AppendPaymentMethodQueryRq();
            cqr.ORPaymentMethodListQuery
                .PaymentMethodListFilter
                .ActiveStatus
                .SetValue(ENActiveStatus.asAll);

            if (modifiedFrom != null)
                cqr
                    .ORPaymentMethodListQuery
                    .PaymentMethodListFilter
                    .FromModifiedDate
                    .SetValue(modifiedFrom.Value, false);

            var results = _sessionManager.DoRequests(msr);

            var list = results.ResponseList.ToPaymentMethodRetList()
                .Select(o => PaymentMethodModel.Map(o));

            return list;
        }

        public IEnumerable<SalesTaxCodeModel> GetSalesTaxCodes(DateTime? modifiedFrom = null)
        {
            var msr = _sessionManager.GetMsgSetRequest();
            var cqr = msr.AppendSalesTaxCodeQueryRq();
            cqr.ORListQuery
                .ListFilter
                .ActiveStatus
                .SetValue(ENActiveStatus.asAll);

            if (modifiedFrom != null)
                cqr.ORListQuery
                    .ListFilter
                    .FromModifiedDate
                    .SetValue(modifiedFrom.Value, false);

            var results = _sessionManager.DoRequests(msr);

            var list = results.ResponseList.ToSalesTaxCodeRetList()
                .Select(o => SalesTaxCodeModel.Map(o));

            return list;
        }

        public IEnumerable<ItemSalesTaxModel> GetItemSalesTaxes(DateTime? modifiedFrom = null)
        {
            // there is no modified date filter for this from what i found... if it does exist
            // implement it, but when i wrote this i couldnt find it.
            var msr = _sessionManager.GetMsgSetRequest();
            var cqr = msr.AppendItemSalesTaxQueryRq();
            
            if (modifiedFrom != null)
                cqr
                    .ORListQueryWithOwnerIDAndClass
                    .ListWithClassFilter
                    .FromModifiedDate
                    .SetValue(modifiedFrom.Value, false);

            var results = _sessionManager.DoRequests(msr);

            var list = results.ResponseList.ToItemSalesTaxRetList()
                .Select(o => ItemSalesTaxModel.Map(o));

            return list;
        }

        public ReturnData InsertOrUpdateCustomer(CustomerModel cm)
        {
            var msr = _sessionManager.GetMsgSetRequest();
            if (!string.IsNullOrWhiteSpace(cm.Id))
            {
                var cmr = msr.AppendCustomerModRq();
                CustomerModel.Map(cm, cmr);
            }
            else
            {
                var existingItem = GetCustomer(cm.Name);
                if (existingItem != null)
                {
                    var markedActive = false;

                    if (!existingItem.IsActive)
                    {
                        var cmrx = msr.AppendCustomerModRq();
                        existingItem.IsActive = true;
                        CustomerModel.Map(existingItem, cmrx);

                        var rsp = _sessionManager.DoRequests(msr);
                        LastXmlResponse = rsp.ToXMLString();

                        var rv = GenerateReturnValue("CustomerRet", rsp);
                        if (rv.Status == 0)
                        {
                            markedActive = true;
                        }
                        else
                        {
                            return rv;
                        }
                    }

                    return new ReturnData()
                    {
                        TxnId = existingItem.Id,
                        EditSequence = existingItem.EditSequence,
                        Status = 0,
                        StatusMessage = !markedActive ? $"Customer with name {existingItem.Name} already existed; Returned existing customer." :
                                        $"Customer with name {existingItem.Name} already existed but was Inactive; Changed to Active and returned existing customer"
                    };
                }

                var cma = msr.AppendCustomerAddRq();
                CustomerModel.Map(cm, cma);
            }

            var response = _sessionManager.DoRequests(msr);

            LastXmlResponse = response.ToXMLString();
            return GenerateReturnValue("CustomerRet", response);
        }

        internal IEnumerable<PaymentModel> GetPaymentsAppliedTo(string id)
        {
            var msr = _sessionManager.GetMsgSetRequest();
            var cqr = msr.AppendInvoiceQueryRq();
            cqr.ORInvoiceQuery.TxnIDList.Add(id);

            cqr.IncludeLinkedTxns.SetValue(true);

            var results = _sessionManager.DoRequests(msr);

            var list = results?.ResponseList.ToInvoiceRetList();
            var item = list?.FirstOrDefault();

            if (item != null && item.LinkedTxnList.Count > 0)
            {
                var l = new List<PaymentModel>();

                for (int i = 0; i < item.LinkedTxnList.Count; i++)
                {
                    var linkedItem = item.LinkedTxnList?.GetAt(i);

                    if (linkedItem != null && 
                        linkedItem.TxnType.GetValue() == ENTxnType.ttReceivePayment)
                    {
                        var p = GetPayment(linkedItem.TxnID.GetValue(), PaymentSelectType.TxnId);
                        if (p.Any())
                            l.Add(p.First());
                    }
                }

                return l; 
            }

            return Array.Empty<PaymentModel>();
        }

        public ReturnData InsertOrUpdateItem(ItemModel service)
        {
            var msr = _sessionManager.GetMsgSetRequest();
            if (!string.IsNullOrWhiteSpace(service.Id))
            {
                var cmr = msr.AppendItemServiceModRq();
                ItemModel.Map(service, cmr);
            }
            else
            {
                var existingItem = GetItem(service.Name);
                if (existingItem != null)
                {
                    var markedActive = false;

                    if (!existingItem.IsActive)
                    {
                        var cmrx = msr.AppendItemServiceModRq();
                        existingItem.IsActive = true;
                        ItemModel.Map(existingItem, cmrx);

                        var rsp = _sessionManager.DoRequests(msr);
                        LastXmlResponse = rsp.ToXMLString();

                        var rv = GenerateReturnValue("ItemRet", rsp);
                        if (rv.Status == 0)
                        {
                            markedActive = true;
                        }
                        else
                        {
                            return rv;
                        }
                    }

                    return new ReturnData()
                    {
                        TxnId = existingItem.Id,
                        EditSequence = existingItem.EditSequence,
                        Status = 0,
                        StatusMessage = !markedActive ? $"Item with name {existingItem.Name} already existed; Returned existing item." :
                                        $"Item with name {existingItem.Name} already existed but was Inactive; Changed to Active and returned existing item"
                    };
                }

                var cma = msr.AppendItemServiceAddRq();
                ItemModel.Map(service, cma);
            }

            var response = _sessionManager.DoRequests(msr);
            LastXmlResponse = response.ToXMLString();

            return GenerateReturnValue("ItemRet", response);
        }
        
        public class ReturnData
        {
            public string TxnId { get; set; }
            public string EditSequence { get; set; }
            public string RawXml { get; set; }
            public int Status { get; set; }
            public string StatusMessage { get; set; }
        }

        public ReturnData InsertOrUpdateInvoice(InvoiceModel service)
        {
            var msr = _sessionManager.GetMsgSetRequest();
            if (!string.IsNullOrWhiteSpace(service.Id) && 
                !string.IsNullOrWhiteSpace(service.EditSequence))
            {
                var cmr = msr.AppendInvoiceModRq();
                InvoiceModel.Map(service, cmr);
            }
            else
            {
                var cma = msr.AppendInvoiceAddRq();
                InvoiceModel.Map(service, cma);
            }
            System.Diagnostics.Debug.WriteLine(msr.ToXMLString());
            var response = _sessionManager.DoRequests(msr);

            LastXmlResponse = response.ToXMLString();
            return GenerateReturnValue("InvoiceRet", response);
        }

        public ReturnData InsertOrUpdatePayment(PaymentModel payment)
        {
            var msr = _sessionManager.GetMsgSetRequest();
            if (!string.IsNullOrWhiteSpace(payment.Id) && !string.IsNullOrWhiteSpace(payment.EditSequence))
            {
                var cmr = msr.AppendReceivePaymentModRq();
                PaymentModel.Map(payment, cmr);
            }
            else
            {
                var cma = msr.AppendReceivePaymentAddRq();
                PaymentModel.Map(payment, cma);

                var rq = cma.ORApplyPayment.AppliedToTxnAddList.Append();

                rq.TxnID.SetValue(payment.TxnID);
                rq.PaymentAmount.SetValue(payment.Amount);
            }

            var response = _sessionManager.DoRequests(msr);

            LastXmlResponse = response.ToXMLString();
            return GenerateReturnValue("ReceivePaymentRet", response);
        }

        private ReturnData GenerateReturnValue(string type, IMsgSetResponse response)
        {
            XDocument xdoc = XDocument.Parse(response.ToXMLString());

            //Run query
            var lv1s = (from lv1 in xdoc.Descendants(type)
                        select new
                        {
                            TxnId = lv1.Descendants("TxnID").FirstOrDefault()?.Value,
                            EditSequence = lv1.Descendants("EditSequence").FirstOrDefault()?.Value,
                        }).FirstOrDefault();

            LastXmlResponse = response.ToXMLString();

            return new ReturnData()
            {
                RawXml = response.ToXMLString(),
                TxnId = lv1s?.TxnId,
                EditSequence = lv1s?.EditSequence,
                Status = response.ResponseList.ToIResponse().StatusCode,
                StatusMessage = response.ResponseList.ToIResponse().StatusMessage,
            };
        }

        public ReturnData InsertOrUpdateClass(ClassModel cm)
        {
            var msr = _sessionManager.GetMsgSetRequest();
            if (!string.IsNullOrWhiteSpace(cm.Id))
            {
                LastXmlResponse = "<Ignored Reason=\"Already Exists\"/>";
                return new ReturnData() { Status = 500, StatusMessage = "Towbook: Already Exists", TxnId = cm.Id };

                //var cmr = msr.AppendClassModRq();
                //ClassModel.Map(cm, cmr);
            }
            else
            {
                var cma = msr.AppendClassAddRq();
                ClassModel.Map(cm, cma);
            }

            var response = _sessionManager.DoRequests(msr);

            LastXmlResponse = response.ToXMLString();
            return GenerateReturnValue("ClassRet", response);
        }
    }
}
