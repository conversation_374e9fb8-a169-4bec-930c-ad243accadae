using QBFC13Lib;

namespace Extric.Towbook.AgentClient.Connectors.QuickBooks.Models
{
    public class AddressModel
    {
        public string Line1 { get; set; }
        public string Line2 { get; set; }
        public string Line3 { get; set; }
        public string Line4 { get; set; }
        public string Line5 { get; set; }
        public string City { get; set; }
        public string State { get; set; }
        public string PostalCode { get; set; }
        public string Country { get; set; }
        public string Note { get; set; }

        internal static AddressModel Map(IAddress a)
        {
            var r = new AddressModel();

            if (a == null)
                return r;

            r.City = a.City.GetValueOrNull();
            r.State = a.State.GetValueOrNull();
            r.PostalCode = a.PostalCode.GetValueOrNull();
            r.Line1 = a.Addr1.GetValueOrNull();
            r.Line2 = a.Addr2.GetValueOrNull();
            r.Line3 = a.Addr3.GetValueOrNull();
            r.Line4 = a.Addr4.GetValueOrNull();
            r.Line5 = a.Addr5.GetValueOrNull();
            r.Country = a.Country.GetValueOrNull();
            r.Note = a.Note.GetValueOrNull();

            return r;
        }

        public static void Map(AddressModel a, IAddress r)
        {
            if (a == null)
                return;
            r.Addr1.SetValueOrEmpty(a.Line1);
            r.Addr2.SetValueOrEmpty(a.Line2);
            r.Addr3.SetValueOrEmpty(a.Line3);
            r.Addr4.SetValueOrEmpty(a.Line4);
            r.Addr5.SetValueOrEmpty(a.Line5);
            r.City.SetValueOrEmpty(a.City);
            r.State.SetValueOrEmpty(a.State);
            r.PostalCode.SetValueOrEmpty(a.PostalCode);
            r.Country.SetValueOrEmpty(a.Country);
            r.Note.SetValueOrEmpty(a.Note);
        }
    }
}