using Newtonsoft.Json;
using QBFC13Lib;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Extric.Towbook.AgentClient.Connectors.QuickBooks.Models
{
    public class ItemModel
    {
        private string _id;
        [JsonProperty("ObjectId")]
        public string Id
        {
            get
            {
                if (_id == "0")
                    return null;

                return _id;
            }
            set
            {
                _id = value;
            }
        }

        public string EditSequence { get; set; }
        public string Name { get; set; }
        public double Price { get; set; }
        public string Description { get; set; }
        public string AccountName { get; set; }
        public bool IsActive { get; set; }

        public static ItemModel Map(IItemServiceRet item)
        {
            if (item == null)
                return null;

            var cm = new ItemModel();

            cm.Id = item?.ListID?.GetValue();
            cm.EditSequence = item?.EditSequence?.GetValueOrNull();
            cm.Name = item?.Name?.GetValueOrNull();
            cm.Description = item?.ORSalesPurchase?.SalesOrPurchase?.Desc?.GetValueOrNull();
            cm.Price = item?.ORSalesPurchase?.SalesOrPurchase?.ORPrice?.Price?.GetValue() ?? 0;
            cm.IsActive = item?.IsActive?.GetValue() ?? false;

            return cm;
        }

        public static ItemModel Map(IItemDiscountRet item)
        {
            if (item == null)
                return null;

            var cm = new ItemModel();

            cm.Id = item?.ListID?.GetValue();
            cm.EditSequence = item?.EditSequence?.GetValueOrNull();
            cm.Name = item?.Name?.GetValueOrNull();
            cm.IsActive = item?.IsActive?.GetValue() ?? false;

            return cm;
        }

        internal static IItemServiceMod Map(ItemModel service, IItemServiceMod cmr)
        {
            cmr.ListID.SetValue(service.Id);
            cmr.EditSequence.SetValue(service.EditSequence);
            cmr.Name.SetValue(service.Name);

            cmr.ORSalesPurchaseMod.SalesOrPurchaseMod.Desc.SetValue(service.Description);
            cmr.ORSalesPurchaseMod.SalesOrPurchaseMod.ORPrice.Price.SetValue(service.Price);
            cmr.IsActive.SetValue(service.IsActive);

            return cmr;
        }


        internal static IItemServiceAdd Map(ItemModel service, IItemServiceAdd cmr)
        {
            cmr.Name.SetValue(service.Name);

            cmr.ORSalesPurchase.SalesOrPurchase.Desc.SetValue(service.Description);
            cmr.ORSalesPurchase.SalesOrPurchase.ORPrice.Price.SetValue(service.Price);
            cmr.ORSalesPurchase.SalesOrPurchase.AccountRef.FullName.SetValue(service.AccountName);
            cmr.IsActive.SetValue(service.IsActive);

            return cmr;
        }
    }
}