using System;
using QBFC13Lib;
using System.Collections.ObjectModel;
using Newtonsoft.Json;

namespace Extric.Towbook.AgentClient.Connectors.QuickBooks.Models
{

    public class PaymentLine
    {
        public string RefNumber { get; set; }
        public decimal Amount { get; set; }
    }

    public class PaymentModel
    {
        private string _id;
        [JsonProperty("ObjectId")]
        public string Id
        {
            get
            {
                if (_id == "0")
                    return null;

                return _id;
            }
            set
            {
                _id = value;
            }
        }
        public string EditSequence { get; set; }
        public DateTime Date { get; set; }
        public string CustomerRefName { get; set; }
        public string PaymentMethodRefName { get; set; }
        public string ArAccountRefName { get; set; }
        public string Memo { get; set; }
        public string RefNumber { get; set; }
        public double Amount { get; set; }
        
        /// <summary>
        /// Apply to this Invoice TxnID.
        /// </summary>
        public string TxnID { get; set; }

        public Collection<PaymentLine> Invoices { get; set; }


        internal static IReceivePaymentMod Map(PaymentModel payment, IReceivePaymentMod r)
        {
            r.TxnID.SetValue(payment.Id);
            r.EditSequence.SetValue(payment.EditSequence);
            r.CustomerRef.FullName.SetValue(payment.CustomerRefName);
            r.TotalAmount.SetValue(payment.Amount);
            r.TxnDate.SetValue(payment.Date);
            r.PaymentMethodRef.FullName.SetValue(payment.PaymentMethodRefName);
            r.Memo.SetValue(payment.Memo);
            r.RefNumber.SetValue(payment.RefNumber);
            r.ARAccountRef.FullName.SetValueOrEmpty(payment.ArAccountRefName);
            return r;
        }

        internal static IReceivePaymentAdd Map(PaymentModel payment, IReceivePaymentAdd r)
        {
            r.CustomerRef.FullName.SetValue(payment.CustomerRefName);
            r.TotalAmount.SetValue(payment.Amount);
            r.TxnDate.SetValue(payment.Date);
            r.PaymentMethodRef.FullName.SetValue(payment.PaymentMethodRefName);
            r.Memo.SetValue(payment.Memo);
            r.RefNumber.SetValue(payment.RefNumber);
            r.ARAccountRef.FullName.SetValueOrEmpty(payment.ArAccountRefName);

            return r;
        }

        internal static PaymentModel Map(IReceivePaymentRet payment)
        {
            var r = new PaymentModel();
            if (payment == null || 
                payment.TotalAmount == null)
                return null;
            
            r.CustomerRefName = payment.CustomerRef.FullName.GetValueOrNull();
            r.Amount = payment.TotalAmount.GetValue();
            r.Date = payment.TxnDate.GetValue();
            r.PaymentMethodRefName = payment.PaymentMethodRef.FullName.GetValue();
            r.ArAccountRefName = payment.ARAccountRef.FullName.GetValueOrNull();
            r.Memo = payment.Memo.GetValueOrNull();
            r.RefNumber = payment.RefNumber.GetValueOrNull();
            r.Id = payment.TxnID.GetValue();
            r.EditSequence = payment.EditSequence.GetValueOrNull();

            return r;
        }

    }
}
