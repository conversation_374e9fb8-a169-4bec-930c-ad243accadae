using Newtonsoft.Json;
using QBFC13Lib;
using System;

namespace Extric.Towbook.AgentClient.Connectors.QuickBooks.Models
{
    public enum AccountType
    {
        atAccountsPayable = 0,
        atAccountsReceivable = 1,
        atBank = 2,
        atCostOfGoodsSold = 3,
        atCreditCard = 4,
        atEquity = 5,
        atExpense = 6,
        atFixedAsset = 7,
        atIncome = 8,
        atLongTermLiability = 9,
        atNonPosting = 10,
        atOtherAsset = 11,
        atOtherCurrentAsset = 12,
        atOtherCurrentLiability = 13,
        atOtherExpense = 14,
        atOtherIncome = 15
    }

    public class AccountModel
    {
        [JsonProperty("ObjectId")]
        public string Id { get; set; }
        public string Name { get; set; }
        public string AccountNumber { get; set; }
        public string FullName { get; set; }
        public AccountType Type { get; set; }
        public DateTime? LastModified { get; set; }
        public bool IsActive { get; set; }

        public static AccountModel Map(IAccountRet input)
        {
            var r = new AccountModel();

            r.Id = input.ListID.GetValue();
            r.Name = input.Name.GetValueOrNull();
            r.FullName = input.FullName.GetValueOrNull();
            r.AccountNumber = input.AccountNumber.GetValueOrNull();
            r.Type = (AccountType)input.AccountType.GetValue();
            r.IsActive = input.IsActive.GetValue();
            r.LastModified = input.TimeModified.GetValue();

            return r;
        }
    }
}
