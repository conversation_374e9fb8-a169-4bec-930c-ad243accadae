using System.Net;
using Extric.Towbook.API.Utils;
using Extric.Towbook.API.Utils.Middlewares;
using Extric.Towbook.API.Utils.Services;
using Extric.Towbook.Configuration;
using Extric.Towbook.Configuration.Logging;
using Extric.Towbook.WebWrapper;

var builder = WebApplication.CreateBuilder(args);

builder.Configuration.AddUserSecrets<Program>();

builder.Services.ConfigureCore();

// Add services to the container.
builder.Services.AddScoped<IRequestContext, NullSafeConcurrentDictionary>();
builder.Services.AddSingleton<PdfService>();
builder.Services.AddHostedService<CronService>();
builder.Services.AddControllers();

    // Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

// Logging configuration
builder.Host.ConfigureLogglyWithNLog();

builder.WebHost.ConfigureKestrel(options =>
{
    options.Listen(IPAddress.Any, 5068);
    options.Limits.MaxRequestBodySize = null;
});

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseCors(builder =>
        builder.AllowAnyOrigin().AllowAnyMethod().AllowAnyHeader()
    );
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseMiddleware<AuthenticationMiddleware>();
//app.UseMiddleware<CookieAuthenticationMiddleware>();
//app.UseMiddleware<ApiAuthenticationMiddleware>();
app.UseMiddleware<ExceptionHandlerMiddleware>();

app.UseAuthorization();

app.MapControllers();

app.ConfigureIronPdf(builder.Configuration);

NewRelic.Api.Agent.NewRelic.StartAgent();

app.Run();
