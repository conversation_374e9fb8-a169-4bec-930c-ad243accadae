apiVersion: v1
kind: Service
metadata:
  name: api-utils-service
spec:
  selector:
    app: api-utils-pod
  ports:
  - port: 80
    targetPort: 5068
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: api-utils
spec:
  selector:
    matchLabels:
      app: api-utils-pod
  template:
    metadata:
      labels:
        app: api-utils-pod
    spec:
      containers:
      - name: api-utils-container
        image: towbookapiregistry.azurecr.io/api-utils:latest
        resources:
         requests:
           memory: "512Mi"
           cpu: "400m"
         limits:
           memory: "2Gi"
           cpu: "800m"
        ports:
        - containerPort: 5068
        env:
        - name: ASPNETCORE_ENVIRONMENT
          value: "${DEPLOYMENT_ENVIRONMENT}"
        - name: CORECLR_ENABLE_PROFILING
          value: "1"
        - name: CORECLR_PROFILER
          value: "{36032161-FFC0-4B61-B559-F6C5D41BAE5A}"
        - name: NEW_RELIC_APP_NAME
          value: "NewRelic-PdfUtils"
        - name: NEW_RELIC_LICENSE_KEY
          value: "ba6a3e6ef84b36cc9a86e7ed156ca2e1FFFFNRAL"
        - name: ConnectionStrings__Database
          valueFrom:
            secretKeyRef:
              name: kvtowbook-secrets
              key: ConnectionStrings-Database
        - name: ConnectionStrings__Microsoft.ServiceBus
          valueFrom:
            secretKeyRef:
              name: kvtowbook-secrets
              key: ConnectionStrings-MicrosoftServiceBus
        - name: Redis__Credentials
          valueFrom:
            secretKeyRef:
              name: kvtowbook-secrets
              key: Redis-Credentials
        - name: CosmosDb__Url
          valueFrom:
            secretKeyRef:
              name: kvtowbook-secrets
              key: CosmosDb-Url
        - name: CosmosDb__AuthKey
          valueFrom:
            secretKeyRef:
              name: kvtowbook-secrets
              key: CosmosDb-AuthKey
        - name: CosmosDb__Database
          valueFrom:
            secretKeyRef:
              name: kvtowbook-secrets
              key: CosmosDb-Database
      nodeSelector:
        selector: nplin
