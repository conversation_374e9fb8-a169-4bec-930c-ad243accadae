Timestamp,Provider,Counter Name,Counter Type,Mean/Increment
05/08/2025 15:26:05,System.Runtime,CPU Usage (%),Metric,0
05/08/2025 15:26:05,System.Runtime,Working Set (MB),Metric,110.669824
05/08/2025 15:26:05,System.Runtime,G<PERSON> Heap Size (MB),Metric,8.274608
05/08/2025 15:26:05,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:05,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:05,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:05,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:26:05,System.Runtime,ThreadPool Thread Count,Metric,0
05/08/2025 15:26:05,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:26:05,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:26:05,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:26:05,System.Runtime,Allocation Rate (B / 1 sec),Rate,267208
05/08/2025 15:26:05,System.Runtime,Number of Active Timers,Metric,3
05/08/2025 15:26:05,System.Runtime,GC Fragmentation (%),Metric,0
05/08/2025 15:26:05,System.Runtime,GC Committed Bytes (MB),Metric,0
05/08/2025 15:26:05,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:26:05,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:26:05,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:26:05,System.Runtime,Gen 0 Size (B),Metric,0
05/08/2025 15:26:05,System.Runtime,Gen 1 Size (B),Metric,0
05/08/2025 15:26:05,System.Runtime,Gen 2 Size (B),Metric,0
05/08/2025 15:26:05,System.Runtime,LOH Size (B),Metric,0
05/08/2025 15:26:05,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,0
05/08/2025 15:26:05,System.Runtime,Number of Assemblies Loaded,Metric,144
05/08/2025 15:26:05,System.Runtime,IL Bytes Jitted (B),Metric,329499
05/08/2025 15:26:05,System.Runtime,Number of Methods Jitted,Metric,4570
05/08/2025 15:26:05,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,184.10950000000003
05/08/2025 15:26:06,System.Runtime,CPU Usage (%),Metric,1.3565891472868217
05/08/2025 15:26:06,System.Runtime,Working Set (MB),Metric,110.915584
05/08/2025 15:26:06,System.Runtime,GC Heap Size (MB),Metric,8.299224
05/08/2025 15:26:06,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:06,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:06,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:06,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:26:06,System.Runtime,ThreadPool Thread Count,Metric,0
05/08/2025 15:26:06,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:26:06,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:26:06,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:26:06,System.Runtime,Allocation Rate (B / 1 sec),Rate,0
05/08/2025 15:26:06,System.Runtime,Number of Active Timers,Metric,3
05/08/2025 15:26:06,System.Runtime,GC Fragmentation (%),Metric,0
05/08/2025 15:26:06,System.Runtime,GC Committed Bytes (MB),Metric,0
05/08/2025 15:26:06,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:26:06,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:26:06,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:26:06,System.Runtime,Gen 0 Size (B),Metric,0
05/08/2025 15:26:06,System.Runtime,Gen 1 Size (B),Metric,0
05/08/2025 15:26:06,System.Runtime,Gen 2 Size (B),Metric,0
05/08/2025 15:26:06,System.Runtime,LOH Size (B),Metric,0
05/08/2025 15:26:06,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,0
05/08/2025 15:26:06,System.Runtime,Number of Assemblies Loaded,Metric,144
05/08/2025 15:26:06,System.Runtime,IL Bytes Jitted (B),Metric,337218
05/08/2025 15:26:06,System.Runtime,Number of Methods Jitted,Metric,4623
05/08/2025 15:26:06,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,93.03559999999993
05/08/2025 15:26:07,System.Runtime,CPU Usage (%),Metric,0.1953125
05/08/2025 15:26:07,System.Runtime,Working Set (MB),Metric,111.034368
05/08/2025 15:26:07,System.Runtime,GC Heap Size (MB),Metric,8.323896
05/08/2025 15:26:07,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:07,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:07,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:07,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:26:07,System.Runtime,ThreadPool Thread Count,Metric,2
05/08/2025 15:26:07,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:26:07,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:26:07,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,2
05/08/2025 15:26:07,System.Runtime,Allocation Rate (B / 1 sec),Rate,24600
05/08/2025 15:26:07,System.Runtime,Number of Active Timers,Metric,3
05/08/2025 15:26:07,System.Runtime,GC Fragmentation (%),Metric,0
05/08/2025 15:26:07,System.Runtime,GC Committed Bytes (MB),Metric,0
05/08/2025 15:26:07,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:26:07,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:26:07,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:26:07,System.Runtime,Gen 0 Size (B),Metric,0
05/08/2025 15:26:07,System.Runtime,Gen 1 Size (B),Metric,0
05/08/2025 15:26:07,System.Runtime,Gen 2 Size (B),Metric,0
05/08/2025 15:26:07,System.Runtime,LOH Size (B),Metric,0
05/08/2025 15:26:07,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,0
05/08/2025 15:26:07,System.Runtime,Number of Assemblies Loaded,Metric,144
05/08/2025 15:26:07,System.Runtime,IL Bytes Jitted (B),Metric,340973
05/08/2025 15:26:07,System.Runtime,Number of Methods Jitted,Metric,4655
05/08/2025 15:26:07,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,35.787700000000086
05/08/2025 15:26:08,System.Runtime,CPU Usage (%),Metric,1.171875
05/08/2025 15:26:08,System.Runtime,Working Set (MB),Metric,111.128576
05/08/2025 15:26:08,System.Runtime,GC Heap Size (MB),Metric,8.332072
05/08/2025 15:26:08,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:08,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:08,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:08,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:26:08,System.Runtime,ThreadPool Thread Count,Metric,2
05/08/2025 15:26:08,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:26:08,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:26:08,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:26:08,System.Runtime,Allocation Rate (B / 1 sec),Rate,8176
05/08/2025 15:26:08,System.Runtime,Number of Active Timers,Metric,3
05/08/2025 15:26:08,System.Runtime,GC Fragmentation (%),Metric,0
05/08/2025 15:26:08,System.Runtime,GC Committed Bytes (MB),Metric,0
05/08/2025 15:26:08,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:26:08,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:26:08,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:26:08,System.Runtime,Gen 0 Size (B),Metric,0
05/08/2025 15:26:08,System.Runtime,Gen 1 Size (B),Metric,0
05/08/2025 15:26:08,System.Runtime,Gen 2 Size (B),Metric,0
05/08/2025 15:26:08,System.Runtime,LOH Size (B),Metric,0
05/08/2025 15:26:08,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,0
05/08/2025 15:26:08,System.Runtime,Number of Assemblies Loaded,Metric,144
05/08/2025 15:26:08,System.Runtime,IL Bytes Jitted (B),Metric,344968
05/08/2025 15:26:08,System.Runtime,Number of Methods Jitted,Metric,4706
05/08/2025 15:26:08,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,49.210399999999936
05/08/2025 15:26:09,System.Runtime,CPU Usage (%),Metric,0.1968503937007874
05/08/2025 15:26:09,System.Runtime,Working Set (MB),Metric,111.136768
05/08/2025 15:26:09,System.Runtime,GC Heap Size (MB),Metric,8.332072
05/08/2025 15:26:09,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:09,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:09,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:09,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:26:09,System.Runtime,ThreadPool Thread Count,Metric,2
05/08/2025 15:26:09,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:26:09,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:26:09,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:26:09,System.Runtime,Allocation Rate (B / 1 sec),Rate,8200
05/08/2025 15:26:09,System.Runtime,Number of Active Timers,Metric,3
05/08/2025 15:26:09,System.Runtime,GC Fragmentation (%),Metric,0
05/08/2025 15:26:09,System.Runtime,GC Committed Bytes (MB),Metric,0
05/08/2025 15:26:09,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:26:09,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:26:09,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:26:09,System.Runtime,Gen 0 Size (B),Metric,0
05/08/2025 15:26:09,System.Runtime,Gen 1 Size (B),Metric,0
05/08/2025 15:26:09,System.Runtime,Gen 2 Size (B),Metric,0
05/08/2025 15:26:09,System.Runtime,LOH Size (B),Metric,0
05/08/2025 15:26:09,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,0
05/08/2025 15:26:09,System.Runtime,Number of Assemblies Loaded,Metric,144
05/08/2025 15:26:09,System.Runtime,IL Bytes Jitted (B),Metric,345546
05/08/2025 15:26:09,System.Runtime,Number of Methods Jitted,Metric,4714
05/08/2025 15:26:09,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,13.68139999999994
05/08/2025 15:26:10,System.Runtime,CPU Usage (%),Metric,0.3875968992248062
05/08/2025 15:26:10,System.Runtime,Working Set (MB),Metric,111.259648
05/08/2025 15:26:10,System.Runtime,GC Heap Size (MB),Metric,8.340296
05/08/2025 15:26:10,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:10,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:10,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:10,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:26:10,System.Runtime,ThreadPool Thread Count,Metric,2
05/08/2025 15:26:10,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:26:10,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:26:10,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:26:10,System.Runtime,Allocation Rate (B / 1 sec),Rate,0
05/08/2025 15:26:10,System.Runtime,Number of Active Timers,Metric,3
05/08/2025 15:26:10,System.Runtime,GC Fragmentation (%),Metric,0
05/08/2025 15:26:10,System.Runtime,GC Committed Bytes (MB),Metric,0
05/08/2025 15:26:10,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:26:10,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:26:10,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:26:10,System.Runtime,Gen 0 Size (B),Metric,0
05/08/2025 15:26:10,System.Runtime,Gen 1 Size (B),Metric,0
05/08/2025 15:26:10,System.Runtime,Gen 2 Size (B),Metric,0
05/08/2025 15:26:10,System.Runtime,LOH Size (B),Metric,0
05/08/2025 15:26:10,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,0
05/08/2025 15:26:10,System.Runtime,Number of Assemblies Loaded,Metric,144
05/08/2025 15:26:10,System.Runtime,IL Bytes Jitted (B),Metric,349295
05/08/2025 15:26:10,System.Runtime,Number of Methods Jitted,Metric,4758
05/08/2025 15:26:10,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,49.302400000000034
05/08/2025 15:26:11,System.Runtime,CPU Usage (%),Metric,0
05/08/2025 15:26:11,System.Runtime,Working Set (MB),Metric,111.280128
05/08/2025 15:26:11,System.Runtime,GC Heap Size (MB),Metric,8.348432
05/08/2025 15:26:11,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:11,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:11,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:11,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:26:11,System.Runtime,ThreadPool Thread Count,Metric,2
05/08/2025 15:26:11,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:26:11,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:26:11,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:26:11,System.Runtime,Allocation Rate (B / 1 sec),Rate,8136
05/08/2025 15:26:11,System.Runtime,Number of Active Timers,Metric,3
05/08/2025 15:26:11,System.Runtime,GC Fragmentation (%),Metric,0
05/08/2025 15:26:11,System.Runtime,GC Committed Bytes (MB),Metric,0
05/08/2025 15:26:11,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:26:11,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:26:11,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:26:11,System.Runtime,Gen 0 Size (B),Metric,0
05/08/2025 15:26:11,System.Runtime,Gen 1 Size (B),Metric,0
05/08/2025 15:26:11,System.Runtime,Gen 2 Size (B),Metric,0
05/08/2025 15:26:11,System.Runtime,LOH Size (B),Metric,0
05/08/2025 15:26:11,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,0
05/08/2025 15:26:11,System.Runtime,Number of Assemblies Loaded,Metric,144
05/08/2025 15:26:11,System.Runtime,IL Bytes Jitted (B),Metric,350409
05/08/2025 15:26:11,System.Runtime,Number of Methods Jitted,Metric,4759
05/08/2025 15:26:11,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,8.050799999999981
05/08/2025 15:26:12,System.Runtime,CPU Usage (%),Metric,0.1953125
05/08/2025 15:26:12,System.Runtime,Working Set (MB),Metric,111.28832
05/08/2025 15:26:12,System.Runtime,GC Heap Size (MB),Metric,8.348432
05/08/2025 15:26:12,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:12,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:12,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:12,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:26:12,System.Runtime,ThreadPool Thread Count,Metric,2
05/08/2025 15:26:12,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:26:12,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:26:12,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:26:12,System.Runtime,Allocation Rate (B / 1 sec),Rate,8200
05/08/2025 15:26:12,System.Runtime,Number of Active Timers,Metric,3
05/08/2025 15:26:12,System.Runtime,GC Fragmentation (%),Metric,0
05/08/2025 15:26:12,System.Runtime,GC Committed Bytes (MB),Metric,0
05/08/2025 15:26:12,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:26:12,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:26:12,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:26:12,System.Runtime,Gen 0 Size (B),Metric,0
05/08/2025 15:26:12,System.Runtime,Gen 1 Size (B),Metric,0
05/08/2025 15:26:12,System.Runtime,Gen 2 Size (B),Metric,0
05/08/2025 15:26:12,System.Runtime,LOH Size (B),Metric,0
05/08/2025 15:26:12,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,0
05/08/2025 15:26:12,System.Runtime,Number of Assemblies Loaded,Metric,144
05/08/2025 15:26:12,System.Runtime,IL Bytes Jitted (B),Metric,350838
05/08/2025 15:26:12,System.Runtime,Number of Methods Jitted,Metric,4761
05/08/2025 15:26:12,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,2.875099999999975
05/08/2025 15:26:13,System.Runtime,CPU Usage (%),Metric,0.1953125
05/08/2025 15:26:13,System.Runtime,Working Set (MB),Metric,111.300608
05/08/2025 15:26:13,System.Runtime,GC Heap Size (MB),Metric,8.356656
05/08/2025 15:26:13,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:13,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:13,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:13,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:26:13,System.Runtime,ThreadPool Thread Count,Metric,2
05/08/2025 15:26:13,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:26:13,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:26:13,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:26:13,System.Runtime,Allocation Rate (B / 1 sec),Rate,0
05/08/2025 15:26:13,System.Runtime,Number of Active Timers,Metric,3
05/08/2025 15:26:13,System.Runtime,GC Fragmentation (%),Metric,0
05/08/2025 15:26:13,System.Runtime,GC Committed Bytes (MB),Metric,0
05/08/2025 15:26:13,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:26:13,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:26:13,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:26:13,System.Runtime,Gen 0 Size (B),Metric,0
05/08/2025 15:26:13,System.Runtime,Gen 1 Size (B),Metric,0
05/08/2025 15:26:13,System.Runtime,Gen 2 Size (B),Metric,0
05/08/2025 15:26:13,System.Runtime,LOH Size (B),Metric,0
05/08/2025 15:26:13,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,0
05/08/2025 15:26:13,System.Runtime,Number of Assemblies Loaded,Metric,144
05/08/2025 15:26:13,System.Runtime,IL Bytes Jitted (B),Metric,351268
05/08/2025 15:26:13,System.Runtime,Number of Methods Jitted,Metric,4766
05/08/2025 15:26:13,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,3.8080999999999676
05/08/2025 15:26:14,System.Runtime,CPU Usage (%),Metric,0.390625
05/08/2025 15:26:14,System.Runtime,Working Set (MB),Metric,111.742976
05/08/2025 15:26:14,System.Runtime,GC Heap Size (MB),Metric,8.36488
05/08/2025 15:26:14,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:14,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:14,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:14,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:26:14,System.Runtime,ThreadPool Thread Count,Metric,2
05/08/2025 15:26:14,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:26:14,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:26:14,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:26:14,System.Runtime,Allocation Rate (B / 1 sec),Rate,8200
05/08/2025 15:26:14,System.Runtime,Number of Active Timers,Metric,3
05/08/2025 15:26:14,System.Runtime,GC Fragmentation (%),Metric,0
05/08/2025 15:26:14,System.Runtime,GC Committed Bytes (MB),Metric,0
05/08/2025 15:26:14,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:26:14,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:26:14,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:26:14,System.Runtime,Gen 0 Size (B),Metric,0
05/08/2025 15:26:14,System.Runtime,Gen 1 Size (B),Metric,0
05/08/2025 15:26:14,System.Runtime,Gen 2 Size (B),Metric,0
05/08/2025 15:26:14,System.Runtime,LOH Size (B),Metric,0
05/08/2025 15:26:14,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,0
05/08/2025 15:26:14,System.Runtime,Number of Assemblies Loaded,Metric,144
05/08/2025 15:26:14,System.Runtime,IL Bytes Jitted (B),Metric,354200
05/08/2025 15:26:14,System.Runtime,Number of Methods Jitted,Metric,4784
05/08/2025 15:26:14,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,32.47750000000019
05/08/2025 15:26:15,System.Runtime,CPU Usage (%),Metric,0
05/08/2025 15:26:15,System.Runtime,Working Set (MB),Metric,111.747072
05/08/2025 15:26:15,System.Runtime,GC Heap Size (MB),Metric,8.373024
05/08/2025 15:26:15,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:15,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:15,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:15,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:26:15,System.Runtime,ThreadPool Thread Count,Metric,2
05/08/2025 15:26:15,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:26:15,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:26:15,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:26:15,System.Runtime,Allocation Rate (B / 1 sec),Rate,8144
05/08/2025 15:26:15,System.Runtime,Number of Active Timers,Metric,3
05/08/2025 15:26:15,System.Runtime,GC Fragmentation (%),Metric,0
05/08/2025 15:26:15,System.Runtime,GC Committed Bytes (MB),Metric,0
05/08/2025 15:26:15,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:26:15,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:26:15,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:26:15,System.Runtime,Gen 0 Size (B),Metric,0
05/08/2025 15:26:15,System.Runtime,Gen 1 Size (B),Metric,0
05/08/2025 15:26:15,System.Runtime,Gen 2 Size (B),Metric,0
05/08/2025 15:26:15,System.Runtime,LOH Size (B),Metric,0
05/08/2025 15:26:15,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,0
05/08/2025 15:26:15,System.Runtime,Number of Assemblies Loaded,Metric,144
05/08/2025 15:26:15,System.Runtime,IL Bytes Jitted (B),Metric,354219
05/08/2025 15:26:15,System.Runtime,Number of Methods Jitted,Metric,4785
05/08/2025 15:26:15,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,0.5881999999999152
05/08/2025 15:26:16,System.Runtime,CPU Usage (%),Metric,0.3853564547206166
05/08/2025 15:26:16,System.Runtime,Working Set (MB),Metric,111.755264
05/08/2025 15:26:16,System.Runtime,GC Heap Size (MB),Metric,8.373024
05/08/2025 15:26:16,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:16,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:16,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:16,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:26:16,System.Runtime,ThreadPool Thread Count,Metric,2
05/08/2025 15:26:16,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:26:16,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:26:16,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:26:16,System.Runtime,Allocation Rate (B / 1 sec),Rate,8200
05/08/2025 15:26:16,System.Runtime,Number of Active Timers,Metric,3
05/08/2025 15:26:16,System.Runtime,GC Fragmentation (%),Metric,0
05/08/2025 15:26:16,System.Runtime,GC Committed Bytes (MB),Metric,0
05/08/2025 15:26:16,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:26:16,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:26:16,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:26:16,System.Runtime,Gen 0 Size (B),Metric,0
05/08/2025 15:26:16,System.Runtime,Gen 1 Size (B),Metric,0
05/08/2025 15:26:16,System.Runtime,Gen 2 Size (B),Metric,0
05/08/2025 15:26:16,System.Runtime,LOH Size (B),Metric,0
05/08/2025 15:26:16,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,0
05/08/2025 15:26:16,System.Runtime,Number of Assemblies Loaded,Metric,144
05/08/2025 15:26:16,System.Runtime,IL Bytes Jitted (B),Metric,355333
05/08/2025 15:26:16,System.Runtime,Number of Methods Jitted,Metric,4786
05/08/2025 15:26:16,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,7.091499999999996
05/08/2025 15:26:17,System.Runtime,CPU Usage (%),Metric,0
05/08/2025 15:26:17,System.Runtime,Working Set (MB),Metric,111.771648
05/08/2025 15:26:17,System.Runtime,GC Heap Size (MB),Metric,8.381248
05/08/2025 15:26:17,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:17,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:17,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:17,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:26:17,System.Runtime,ThreadPool Thread Count,Metric,2
05/08/2025 15:26:17,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:26:17,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:26:17,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:26:17,System.Runtime,Allocation Rate (B / 1 sec),Rate,0
05/08/2025 15:26:17,System.Runtime,Number of Active Timers,Metric,3
05/08/2025 15:26:17,System.Runtime,GC Fragmentation (%),Metric,0
05/08/2025 15:26:17,System.Runtime,GC Committed Bytes (MB),Metric,0
05/08/2025 15:26:17,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:26:17,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:26:17,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:26:17,System.Runtime,Gen 0 Size (B),Metric,0
05/08/2025 15:26:17,System.Runtime,Gen 1 Size (B),Metric,0
05/08/2025 15:26:17,System.Runtime,Gen 2 Size (B),Metric,0
05/08/2025 15:26:17,System.Runtime,LOH Size (B),Metric,0
05/08/2025 15:26:17,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,0
05/08/2025 15:26:17,System.Runtime,Number of Assemblies Loaded,Metric,144
05/08/2025 15:26:17,System.Runtime,IL Bytes Jitted (B),Metric,355396
05/08/2025 15:26:17,System.Runtime,Number of Methods Jitted,Metric,4787
05/08/2025 15:26:17,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,0.5091999999999643
05/08/2025 15:26:18,System.Runtime,CPU Usage (%),Metric,0
05/08/2025 15:26:18,System.Runtime,Working Set (MB),Metric,111.77984
05/08/2025 15:26:18,System.Runtime,GC Heap Size (MB),Metric,8.390744
05/08/2025 15:26:18,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:18,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:18,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:18,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:26:18,System.Runtime,ThreadPool Thread Count,Metric,2
05/08/2025 15:26:18,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:26:18,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:26:18,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:26:18,System.Runtime,Allocation Rate (B / 1 sec),Rate,9472
05/08/2025 15:26:18,System.Runtime,Number of Active Timers,Metric,3
05/08/2025 15:26:18,System.Runtime,GC Fragmentation (%),Metric,0
05/08/2025 15:26:18,System.Runtime,GC Committed Bytes (MB),Metric,0
05/08/2025 15:26:18,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:26:18,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:26:18,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:26:18,System.Runtime,Gen 0 Size (B),Metric,0
05/08/2025 15:26:18,System.Runtime,Gen 1 Size (B),Metric,0
05/08/2025 15:26:18,System.Runtime,Gen 2 Size (B),Metric,0
05/08/2025 15:26:18,System.Runtime,LOH Size (B),Metric,0
05/08/2025 15:26:18,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,0
05/08/2025 15:26:18,System.Runtime,Number of Assemblies Loaded,Metric,144
05/08/2025 15:26:18,System.Runtime,IL Bytes Jitted (B),Metric,355396
05/08/2025 15:26:18,System.Runtime,Number of Methods Jitted,Metric,4787
05/08/2025 15:26:18,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,0
05/08/2025 15:26:19,System.Runtime,CPU Usage (%),Metric,0.3831417624521073
05/08/2025 15:26:19,System.Runtime,Working Set (MB),Metric,111.783936
05/08/2025 15:26:19,System.Runtime,GC Heap Size (MB),Metric,8.390744
05/08/2025 15:26:19,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:19,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:19,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:19,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:26:19,System.Runtime,ThreadPool Thread Count,Metric,2
05/08/2025 15:26:19,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:26:19,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:26:19,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:26:19,System.Runtime,Allocation Rate (B / 1 sec),Rate,0
05/08/2025 15:26:19,System.Runtime,Number of Active Timers,Metric,3
05/08/2025 15:26:19,System.Runtime,GC Fragmentation (%),Metric,0
05/08/2025 15:26:19,System.Runtime,GC Committed Bytes (MB),Metric,0
05/08/2025 15:26:19,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:26:19,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:26:19,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:26:19,System.Runtime,Gen 0 Size (B),Metric,0
05/08/2025 15:26:19,System.Runtime,Gen 1 Size (B),Metric,0
05/08/2025 15:26:19,System.Runtime,Gen 2 Size (B),Metric,0
05/08/2025 15:26:19,System.Runtime,LOH Size (B),Metric,0
05/08/2025 15:26:19,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,0
05/08/2025 15:26:19,System.Runtime,Number of Assemblies Loaded,Metric,144
05/08/2025 15:26:19,System.Runtime,IL Bytes Jitted (B),Metric,355444
05/08/2025 15:26:19,System.Runtime,Number of Methods Jitted,Metric,4789
05/08/2025 15:26:19,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,0.8070000000000164
05/08/2025 15:26:20,System.Runtime,CPU Usage (%),Metric,0.1996007984031936
05/08/2025 15:26:20,System.Runtime,Working Set (MB),Metric,111.792128
05/08/2025 15:26:20,System.Runtime,GC Heap Size (MB),Metric,8.398968
05/08/2025 15:26:20,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:20,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:20,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:20,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:26:20,System.Runtime,ThreadPool Thread Count,Metric,2
05/08/2025 15:26:20,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:26:20,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:26:20,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:26:20,System.Runtime,Allocation Rate (B / 1 sec),Rate,8200
05/08/2025 15:26:20,System.Runtime,Number of Active Timers,Metric,3
05/08/2025 15:26:20,System.Runtime,GC Fragmentation (%),Metric,0
05/08/2025 15:26:20,System.Runtime,GC Committed Bytes (MB),Metric,0
05/08/2025 15:26:20,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:26:20,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:26:20,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:26:20,System.Runtime,Gen 0 Size (B),Metric,0
05/08/2025 15:26:20,System.Runtime,Gen 1 Size (B),Metric,0
05/08/2025 15:26:20,System.Runtime,Gen 2 Size (B),Metric,0
05/08/2025 15:26:20,System.Runtime,LOH Size (B),Metric,0
05/08/2025 15:26:20,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,0
05/08/2025 15:26:20,System.Runtime,Number of Assemblies Loaded,Metric,144
05/08/2025 15:26:20,System.Runtime,IL Bytes Jitted (B),Metric,356338
05/08/2025 15:26:20,System.Runtime,Number of Methods Jitted,Metric,4795
05/08/2025 15:26:20,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,7.462600000000066
05/08/2025 15:26:21,System.Runtime,CPU Usage (%),Metric,0.19193857965451055
05/08/2025 15:26:21,System.Runtime,Working Set (MB),Metric,111.808512
05/08/2025 15:26:21,System.Runtime,GC Heap Size (MB),Metric,8.415104
05/08/2025 15:26:21,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:21,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:21,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:21,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:26:21,System.Runtime,ThreadPool Thread Count,Metric,2
05/08/2025 15:26:21,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:26:21,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:26:21,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:26:21,System.Runtime,Allocation Rate (B / 1 sec),Rate,16112
05/08/2025 15:26:21,System.Runtime,Number of Active Timers,Metric,3
05/08/2025 15:26:21,System.Runtime,GC Fragmentation (%),Metric,0
05/08/2025 15:26:21,System.Runtime,GC Committed Bytes (MB),Metric,0
05/08/2025 15:26:21,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:26:21,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:26:21,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:26:21,System.Runtime,Gen 0 Size (B),Metric,0
05/08/2025 15:26:21,System.Runtime,Gen 1 Size (B),Metric,0
05/08/2025 15:26:21,System.Runtime,Gen 2 Size (B),Metric,0
05/08/2025 15:26:21,System.Runtime,LOH Size (B),Metric,0
05/08/2025 15:26:21,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,0
05/08/2025 15:26:21,System.Runtime,Number of Assemblies Loaded,Metric,144
05/08/2025 15:26:21,System.Runtime,IL Bytes Jitted (B),Metric,357168
05/08/2025 15:26:21,System.Runtime,Number of Methods Jitted,Metric,4799
05/08/2025 15:26:21,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,4.808499999999867
05/08/2025 15:26:22,System.Runtime,CPU Usage (%),Metric,0.5882352941176471
05/08/2025 15:26:22,System.Runtime,Working Set (MB),Metric,112.218112
05/08/2025 15:26:22,System.Runtime,GC Heap Size (MB),Metric,8.439808
05/08/2025 15:26:22,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:22,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:22,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:22,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:26:22,System.Runtime,ThreadPool Thread Count,Metric,3
05/08/2025 15:26:22,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:26:22,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:26:22,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,3
05/08/2025 15:26:22,System.Runtime,Allocation Rate (B / 1 sec),Rate,32880
05/08/2025 15:26:22,System.Runtime,Number of Active Timers,Metric,3
05/08/2025 15:26:22,System.Runtime,GC Fragmentation (%),Metric,0
05/08/2025 15:26:22,System.Runtime,GC Committed Bytes (MB),Metric,0
05/08/2025 15:26:22,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:26:22,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:26:22,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:26:22,System.Runtime,Gen 0 Size (B),Metric,0
05/08/2025 15:26:22,System.Runtime,Gen 1 Size (B),Metric,0
05/08/2025 15:26:22,System.Runtime,Gen 2 Size (B),Metric,0
05/08/2025 15:26:22,System.Runtime,LOH Size (B),Metric,0
05/08/2025 15:26:22,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,0
05/08/2025 15:26:22,System.Runtime,Number of Assemblies Loaded,Metric,144
05/08/2025 15:26:22,System.Runtime,IL Bytes Jitted (B),Metric,364383
05/08/2025 15:26:22,System.Runtime,Number of Methods Jitted,Metric,4933
05/08/2025 15:26:22,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,31.941299999999956
05/08/2025 15:26:23,System.Runtime,CPU Usage (%),Metric,14.34108527131783
05/08/2025 15:26:23,System.Runtime,Working Set (MB),Metric,128.909312
05/08/2025 15:26:23,System.Runtime,GC Heap Size (MB),Metric,9.742704
05/08/2025 15:26:23,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:23,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:23,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:23,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:26:23,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:26:23,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:26:23,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:26:23,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,5
05/08/2025 15:26:23,System.Runtime,Allocation Rate (B / 1 sec),Rate,1291144
05/08/2025 15:26:23,System.Runtime,Number of Active Timers,Metric,3
05/08/2025 15:26:23,System.Runtime,GC Fragmentation (%),Metric,0
05/08/2025 15:26:23,System.Runtime,GC Committed Bytes (MB),Metric,0
05/08/2025 15:26:23,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:26:23,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:26:23,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:26:23,System.Runtime,Gen 0 Size (B),Metric,0
05/08/2025 15:26:23,System.Runtime,Gen 1 Size (B),Metric,0
05/08/2025 15:26:23,System.Runtime,Gen 2 Size (B),Metric,0
05/08/2025 15:26:23,System.Runtime,LOH Size (B),Metric,0
05/08/2025 15:26:23,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,0
05/08/2025 15:26:23,System.Runtime,Number of Assemblies Loaded,Metric,149
05/08/2025 15:26:23,System.Runtime,IL Bytes Jitted (B),Metric,473044
05/08/2025 15:26:23,System.Runtime,Number of Methods Jitted,Metric,6641
05/08/2025 15:26:23,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,576.608
05/08/2025 15:26:24,System.Runtime,CPU Usage (%),Metric,13.16793893129771
05/08/2025 15:26:24,System.Runtime,Working Set (MB),Metric,158.998528
05/08/2025 15:26:24,System.Runtime,GC Heap Size (MB),Metric,10.971448
05/08/2025 15:26:24,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:24,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:24,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:24,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:26:24,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:26:24,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:26:24,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:26:24,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,3
05/08/2025 15:26:24,System.Runtime,Allocation Rate (B / 1 sec),Rate,1225384
05/08/2025 15:26:24,System.Runtime,Number of Active Timers,Metric,3
05/08/2025 15:26:24,System.Runtime,GC Fragmentation (%),Metric,0
05/08/2025 15:26:24,System.Runtime,GC Committed Bytes (MB),Metric,0
05/08/2025 15:26:24,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:26:24,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:26:24,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:26:24,System.Runtime,Gen 0 Size (B),Metric,0
05/08/2025 15:26:24,System.Runtime,Gen 1 Size (B),Metric,0
05/08/2025 15:26:24,System.Runtime,Gen 2 Size (B),Metric,0
05/08/2025 15:26:24,System.Runtime,LOH Size (B),Metric,0
05/08/2025 15:26:24,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,0
05/08/2025 15:26:24,System.Runtime,Number of Assemblies Loaded,Metric,149
05/08/2025 15:26:24,System.Runtime,IL Bytes Jitted (B),Metric,542206
05/08/2025 15:26:24,System.Runtime,Number of Methods Jitted,Metric,7363
05/08/2025 15:26:24,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,544.9846000000002
05/08/2025 15:26:25,System.Runtime,CPU Usage (%),Metric,8.8
05/08/2025 15:26:25,System.Runtime,Working Set (MB),Metric,164.089856
05/08/2025 15:26:25,System.Runtime,GC Heap Size (MB),Metric,11.045208
05/08/2025 15:26:25,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:25,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:25,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:25,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:26:25,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:26:25,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:26:25,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:26:25,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:26:25,System.Runtime,Allocation Rate (B / 1 sec),Rate,81736
05/08/2025 15:26:25,System.Runtime,Number of Active Timers,Metric,3
05/08/2025 15:26:25,System.Runtime,GC Fragmentation (%),Metric,0
05/08/2025 15:26:25,System.Runtime,GC Committed Bytes (MB),Metric,0
05/08/2025 15:26:25,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:26:25,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:26:25,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:26:25,System.Runtime,Gen 0 Size (B),Metric,0
05/08/2025 15:26:25,System.Runtime,Gen 1 Size (B),Metric,0
05/08/2025 15:26:25,System.Runtime,Gen 2 Size (B),Metric,0
05/08/2025 15:26:25,System.Runtime,LOH Size (B),Metric,0
05/08/2025 15:26:25,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,0
05/08/2025 15:26:25,System.Runtime,Number of Assemblies Loaded,Metric,149
05/08/2025 15:26:25,System.Runtime,IL Bytes Jitted (B),Metric,601579
05/08/2025 15:26:25,System.Runtime,Number of Methods Jitted,Metric,7737
05/08/2025 15:26:25,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,656.8289
05/08/2025 15:26:26,System.Runtime,CPU Usage (%),Metric,0.78125
05/08/2025 15:26:26,System.Runtime,Working Set (MB),Metric,165.39648
05/08/2025 15:26:26,System.Runtime,GC Heap Size (MB),Metric,11.069752
05/08/2025 15:26:26,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:26,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:26,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:26,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:26:26,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:26:26,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:26:26,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:26:26,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:26:26,System.Runtime,Allocation Rate (B / 1 sec),Rate,16328
05/08/2025 15:26:26,System.Runtime,Number of Active Timers,Metric,3
05/08/2025 15:26:26,System.Runtime,GC Fragmentation (%),Metric,0
05/08/2025 15:26:26,System.Runtime,GC Committed Bytes (MB),Metric,0
05/08/2025 15:26:26,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:26:26,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:26:26,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:26:26,System.Runtime,Gen 0 Size (B),Metric,0
05/08/2025 15:26:26,System.Runtime,Gen 1 Size (B),Metric,0
05/08/2025 15:26:26,System.Runtime,Gen 2 Size (B),Metric,0
05/08/2025 15:26:26,System.Runtime,LOH Size (B),Metric,0
05/08/2025 15:26:26,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,0
05/08/2025 15:26:26,System.Runtime,Number of Assemblies Loaded,Metric,149
05/08/2025 15:26:26,System.Runtime,IL Bytes Jitted (B),Metric,601585
05/08/2025 15:26:26,System.Runtime,Number of Methods Jitted,Metric,7738
05/08/2025 15:26:26,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,0.15760000000000218
05/08/2025 15:26:27,System.Runtime,CPU Usage (%),Metric,9.375
05/08/2025 15:26:27,System.Runtime,Working Set (MB),Metric,203.743232
05/08/2025 15:26:27,System.Runtime,GC Heap Size (MB),Metric,45.864248
05/08/2025 15:26:27,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:27,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:27,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:27,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:26:27,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:26:27,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:26:27,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:26:27,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,3
05/08/2025 15:26:27,System.Runtime,Allocation Rate (B / 1 sec),Rate,34693992
05/08/2025 15:26:27,System.Runtime,Number of Active Timers,Metric,3
05/08/2025 15:26:27,System.Runtime,GC Fragmentation (%),Metric,0
05/08/2025 15:26:27,System.Runtime,GC Committed Bytes (MB),Metric,0
05/08/2025 15:26:27,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:26:27,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:26:27,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:26:27,System.Runtime,Gen 0 Size (B),Metric,0
05/08/2025 15:26:27,System.Runtime,Gen 1 Size (B),Metric,0
05/08/2025 15:26:27,System.Runtime,Gen 2 Size (B),Metric,0
05/08/2025 15:26:27,System.Runtime,LOH Size (B),Metric,0
05/08/2025 15:26:27,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,0
05/08/2025 15:26:27,System.Runtime,Number of Assemblies Loaded,Metric,152
05/08/2025 15:26:27,System.Runtime,IL Bytes Jitted (B),Metric,666802
05/08/2025 15:26:27,System.Runtime,Number of Methods Jitted,Metric,8428
05/08/2025 15:26:27,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,308.90340000000015
05/08/2025 15:26:28,System.Runtime,CPU Usage (%),Metric,14.285714285714286
05/08/2025 15:26:28,System.Runtime,Working Set (MB),Metric,303.640576
05/08/2025 15:26:28,System.Runtime,GC Heap Size (MB),Metric,117.283456
05/08/2025 15:26:28,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:28,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:28,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:28,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:26:28,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:26:28,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:26:28,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:26:28,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:26:28,System.Runtime,Allocation Rate (B / 1 sec),Rate,71377248
05/08/2025 15:26:28,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:26:28,System.Runtime,GC Fragmentation (%),Metric,0
05/08/2025 15:26:28,System.Runtime,GC Committed Bytes (MB),Metric,0
05/08/2025 15:26:28,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:26:28,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:26:28,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:26:28,System.Runtime,Gen 0 Size (B),Metric,0
05/08/2025 15:26:28,System.Runtime,Gen 1 Size (B),Metric,0
05/08/2025 15:26:28,System.Runtime,Gen 2 Size (B),Metric,0
05/08/2025 15:26:28,System.Runtime,LOH Size (B),Metric,0
05/08/2025 15:26:28,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,0
05/08/2025 15:26:28,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:26:28,System.Runtime,IL Bytes Jitted (B),Metric,745415
05/08/2025 15:26:28,System.Runtime,Number of Methods Jitted,Metric,9568
05/08/2025 15:26:28,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,502.7379000000001
05/08/2025 15:26:29,System.Runtime,CPU Usage (%),Metric,17.884615384615383
05/08/2025 15:26:29,System.Runtime,Working Set (MB),Metric,323.231744
05/08/2025 15:26:29,System.Runtime,GC Heap Size (MB),Metric,121.959976
05/08/2025 15:26:29,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:29,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:29,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:29,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:26:29,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:26:29,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:26:29,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:26:29,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:26:29,System.Runtime,Allocation Rate (B / 1 sec),Rate,4676448
05/08/2025 15:26:29,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:26:29,System.Runtime,GC Fragmentation (%),Metric,0
05/08/2025 15:26:29,System.Runtime,GC Committed Bytes (MB),Metric,0
05/08/2025 15:26:29,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:26:29,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:26:29,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:26:29,System.Runtime,Gen 0 Size (B),Metric,0
05/08/2025 15:26:29,System.Runtime,Gen 1 Size (B),Metric,0
05/08/2025 15:26:29,System.Runtime,Gen 2 Size (B),Metric,0
05/08/2025 15:26:29,System.Runtime,LOH Size (B),Metric,0
05/08/2025 15:26:29,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,0
05/08/2025 15:26:29,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:26:29,System.Runtime,IL Bytes Jitted (B),Metric,795916
05/08/2025 15:26:29,System.Runtime,Number of Methods Jitted,Metric,10299
05/08/2025 15:26:29,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,539.5027999999993
05/08/2025 15:26:30,System.Runtime,CPU Usage (%),Metric,12.6953125
05/08/2025 15:26:30,System.Runtime,Working Set (MB),Metric,323.268608
05/08/2025 15:26:30,System.Runtime,GC Heap Size (MB),Metric,121.968176
05/08/2025 15:26:30,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:30,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:30,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:30,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:26:30,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:26:30,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:26:30,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:26:30,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:26:30,System.Runtime,Allocation Rate (B / 1 sec),Rate,8200
05/08/2025 15:26:30,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:26:30,System.Runtime,GC Fragmentation (%),Metric,0
05/08/2025 15:26:30,System.Runtime,GC Committed Bytes (MB),Metric,0
05/08/2025 15:26:30,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:26:30,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:26:30,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:26:30,System.Runtime,Gen 0 Size (B),Metric,0
05/08/2025 15:26:30,System.Runtime,Gen 1 Size (B),Metric,0
05/08/2025 15:26:30,System.Runtime,Gen 2 Size (B),Metric,0
05/08/2025 15:26:30,System.Runtime,LOH Size (B),Metric,0
05/08/2025 15:26:30,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,0
05/08/2025 15:26:30,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:26:30,System.Runtime,IL Bytes Jitted (B),Metric,799355
05/08/2025 15:26:30,System.Runtime,Number of Methods Jitted,Metric,10386
05/08/2025 15:26:30,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,35.21590000000015
05/08/2025 15:26:31,System.Runtime,CPU Usage (%),Metric,12.5
05/08/2025 15:26:31,System.Runtime,Working Set (MB),Metric,340.135936
05/08/2025 15:26:31,System.Runtime,GC Heap Size (MB),Metric,69.844072
05/08/2025 15:26:31,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,1
05/08/2025 15:26:31,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,1
05/08/2025 15:26:31,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,1
05/08/2025 15:26:31,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:26:31,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:26:31,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:26:31,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:26:31,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:26:31,System.Runtime,Allocation Rate (B / 1 sec),Rate,2040088
05/08/2025 15:26:31,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:26:31,System.Runtime,GC Fragmentation (%),Metric,0
05/08/2025 15:26:31,System.Runtime,GC Committed Bytes (MB),Metric,0
05/08/2025 15:26:31,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:26:31,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:26:31,System.Runtime,Time paused by GC (ms / 1 sec),Rate,31.663
05/08/2025 15:26:31,System.Runtime,Gen 0 Size (B),Metric,0
05/08/2025 15:26:31,System.Runtime,Gen 1 Size (B),Metric,0
05/08/2025 15:26:31,System.Runtime,Gen 2 Size (B),Metric,0
05/08/2025 15:26:31,System.Runtime,LOH Size (B),Metric,0
05/08/2025 15:26:31,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,0
05/08/2025 15:26:31,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:26:31,System.Runtime,IL Bytes Jitted (B),Metric,801636
05/08/2025 15:26:31,System.Runtime,Number of Methods Jitted,Metric,10448
05/08/2025 15:26:31,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,35.64980000000014
05/08/2025 15:26:32,System.Runtime,CPU Usage (%),Metric,6.349206349206349
05/08/2025 15:26:32,System.Runtime,Working Set (MB),Metric,272.142336
05/08/2025 15:26:32,System.Runtime,GC Heap Size (MB),Metric,42.382448
05/08/2025 15:26:32,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:32,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:32,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:32,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:26:32,System.Runtime,ThreadPool Thread Count,Metric,5
05/08/2025 15:26:32,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,1
05/08/2025 15:26:32,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:26:32,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,45
05/08/2025 15:26:32,System.Runtime,Allocation Rate (B / 1 sec),Rate,230080
05/08/2025 15:26:32,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:26:32,System.Runtime,GC Fragmentation (%),Metric,44.80787576566392
05/08/2025 15:26:32,System.Runtime,GC Committed Bytes (MB),Metric,77.385728
05/08/2025 15:26:32,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:26:32,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:26:32,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:26:32,System.Runtime,Gen 0 Size (B),Metric,52756616
05/08/2025 15:26:32,System.Runtime,Gen 1 Size (B),Metric,0
05/08/2025 15:26:32,System.Runtime,Gen 2 Size (B),Metric,0
05/08/2025 15:26:32,System.Runtime,LOH Size (B),Metric,23156672
05/08/2025 15:26:32,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,415240
05/08/2025 15:26:32,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:26:32,System.Runtime,IL Bytes Jitted (B),Metric,825384
05/08/2025 15:26:32,System.Runtime,Number of Methods Jitted,Metric,10908
05/08/2025 15:26:32,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,162.77019999999993
05/08/2025 15:26:33,System.Runtime,CPU Usage (%),Metric,3.515625
05/08/2025 15:26:33,System.Runtime,Working Set (MB),Metric,273.006592
05/08/2025 15:26:33,System.Runtime,GC Heap Size (MB),Metric,43.577016
05/08/2025 15:26:33,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:33,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:33,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:33,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:26:33,System.Runtime,ThreadPool Thread Count,Metric,5
05/08/2025 15:26:33,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:26:33,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:26:33,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,4
05/08/2025 15:26:33,System.Runtime,Allocation Rate (B / 1 sec),Rate,1194592
05/08/2025 15:26:33,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:26:33,System.Runtime,GC Fragmentation (%),Metric,44.80787576566392
05/08/2025 15:26:33,System.Runtime,GC Committed Bytes (MB),Metric,77.385728
05/08/2025 15:26:33,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:26:33,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:26:33,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:26:33,System.Runtime,Gen 0 Size (B),Metric,52756616
05/08/2025 15:26:33,System.Runtime,Gen 1 Size (B),Metric,0
05/08/2025 15:26:33,System.Runtime,Gen 2 Size (B),Metric,0
05/08/2025 15:26:33,System.Runtime,LOH Size (B),Metric,23156672
05/08/2025 15:26:33,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,415240
05/08/2025 15:26:33,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:26:33,System.Runtime,IL Bytes Jitted (B),Metric,848415
05/08/2025 15:26:33,System.Runtime,Number of Methods Jitted,Metric,11065
05/08/2025 15:26:33,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,183.3154000000004
05/08/2025 15:26:34,System.Runtime,CPU Usage (%),Metric,2.6923076923076925
05/08/2025 15:26:34,System.Runtime,Working Set (MB),Metric,270.237696
05/08/2025 15:26:34,System.Runtime,GC Heap Size (MB),Metric,43.59392
05/08/2025 15:26:34,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:34,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:34,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:34,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:26:34,System.Runtime,ThreadPool Thread Count,Metric,5
05/08/2025 15:26:34,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:26:34,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:26:34,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:26:34,System.Runtime,Allocation Rate (B / 1 sec),Rate,9512
05/08/2025 15:26:34,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:26:34,System.Runtime,GC Fragmentation (%),Metric,44.80787576566392
05/08/2025 15:26:34,System.Runtime,GC Committed Bytes (MB),Metric,77.385728
05/08/2025 15:26:34,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:26:34,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:26:34,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:26:34,System.Runtime,Gen 0 Size (B),Metric,52756616
05/08/2025 15:26:34,System.Runtime,Gen 1 Size (B),Metric,0
05/08/2025 15:26:34,System.Runtime,Gen 2 Size (B),Metric,0
05/08/2025 15:26:34,System.Runtime,LOH Size (B),Metric,23156672
05/08/2025 15:26:34,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,415240
05/08/2025 15:26:34,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:26:34,System.Runtime,IL Bytes Jitted (B),Metric,867347
05/08/2025 15:26:34,System.Runtime,Number of Methods Jitted,Metric,11125
05/08/2025 15:26:34,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,148.78629999999976
05/08/2025 15:26:35,System.Runtime,CPU Usage (%),Metric,14.0625
05/08/2025 15:26:35,System.Runtime,Working Set (MB),Metric,334.364672
05/08/2025 15:26:35,System.Runtime,GC Heap Size (MB),Metric,119.124968
05/08/2025 15:26:35,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,1
05/08/2025 15:26:35,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:35,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:35,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:26:35,System.Runtime,ThreadPool Thread Count,Metric,5
05/08/2025 15:26:35,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:26:35,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:26:35,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:26:35,System.Runtime,Allocation Rate (B / 1 sec),Rate,105669800
05/08/2025 15:26:35,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:26:35,System.Runtime,GC Fragmentation (%),Metric,23.60264993910209
05/08/2025 15:26:35,System.Runtime,GC Committed Bytes (MB),Metric,82.108416
05/08/2025 15:26:35,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:26:35,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:26:35,System.Runtime,Time paused by GC (ms / 1 sec),Rate,7.346999999999998
05/08/2025 15:26:35,System.Runtime,Gen 0 Size (B),Metric,183672
05/08/2025 15:26:35,System.Runtime,Gen 1 Size (B),Metric,3939672
05/08/2025 15:26:35,System.Runtime,Gen 2 Size (B),Metric,0
05/08/2025 15:26:35,System.Runtime,LOH Size (B),Metric,23815760
05/08/2025 15:26:35,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:26:35,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:26:35,System.Runtime,IL Bytes Jitted (B),Metric,881822
05/08/2025 15:26:35,System.Runtime,Number of Methods Jitted,Metric,11319
05/08/2025 15:26:35,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,237.53179999999975
05/08/2025 15:26:36,System.Runtime,CPU Usage (%),Metric,11.71875
05/08/2025 15:26:36,System.Runtime,Working Set (MB),Metric,350.969856
05/08/2025 15:26:36,System.Runtime,GC Heap Size (MB),Metric,123.382496
05/08/2025 15:26:36,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:36,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:36,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:36,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:26:36,System.Runtime,ThreadPool Thread Count,Metric,5
05/08/2025 15:26:36,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:26:36,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:26:36,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:26:36,System.Runtime,Allocation Rate (B / 1 sec),Rate,4257408
05/08/2025 15:26:36,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:26:36,System.Runtime,GC Fragmentation (%),Metric,23.60264993910209
05/08/2025 15:26:36,System.Runtime,GC Committed Bytes (MB),Metric,82.108416
05/08/2025 15:26:36,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:26:36,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:26:36,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:26:36,System.Runtime,Gen 0 Size (B),Metric,183672
05/08/2025 15:26:36,System.Runtime,Gen 1 Size (B),Metric,3939672
05/08/2025 15:26:36,System.Runtime,Gen 2 Size (B),Metric,0
05/08/2025 15:26:36,System.Runtime,LOH Size (B),Metric,23815760
05/08/2025 15:26:36,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:26:36,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:26:36,System.Runtime,IL Bytes Jitted (B),Metric,887572
05/08/2025 15:26:36,System.Runtime,Number of Methods Jitted,Metric,11388
05/08/2025 15:26:36,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,89.23940000000039
05/08/2025 15:26:37,System.Runtime,CPU Usage (%),Metric,13.0859375
05/08/2025 15:26:37,System.Runtime,Working Set (MB),Metric,351.166464
05/08/2025 15:26:37,System.Runtime,GC Heap Size (MB),Metric,123.464848
05/08/2025 15:26:37,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:37,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:37,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:37,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:26:37,System.Runtime,ThreadPool Thread Count,Metric,5
05/08/2025 15:26:37,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:26:37,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:26:37,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,2
05/08/2025 15:26:37,System.Runtime,Allocation Rate (B / 1 sec),Rate,82136
05/08/2025 15:26:37,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:26:37,System.Runtime,GC Fragmentation (%),Metric,23.60264993910209
05/08/2025 15:26:37,System.Runtime,GC Committed Bytes (MB),Metric,82.108416
05/08/2025 15:26:37,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:26:37,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:26:37,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:26:37,System.Runtime,Gen 0 Size (B),Metric,183672
05/08/2025 15:26:37,System.Runtime,Gen 1 Size (B),Metric,3939672
05/08/2025 15:26:37,System.Runtime,Gen 2 Size (B),Metric,0
05/08/2025 15:26:37,System.Runtime,LOH Size (B),Metric,23815760
05/08/2025 15:26:37,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:26:37,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:26:37,System.Runtime,IL Bytes Jitted (B),Metric,893041
05/08/2025 15:26:37,System.Runtime,Number of Methods Jitted,Metric,11466
05/08/2025 15:26:37,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,65.73639999999978
05/08/2025 15:26:38,System.Runtime,CPU Usage (%),Metric,11.5234375
05/08/2025 15:26:38,System.Runtime,Working Set (MB),Metric,365.469696
05/08/2025 15:26:38,System.Runtime,GC Heap Size (MB),Metric,125.56968
05/08/2025 15:26:38,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:38,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:38,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:38,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:26:38,System.Runtime,ThreadPool Thread Count,Metric,5
05/08/2025 15:26:38,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:26:38,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:26:38,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:26:38,System.Runtime,Allocation Rate (B / 1 sec),Rate,2104784
05/08/2025 15:26:38,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:26:38,System.Runtime,GC Fragmentation (%),Metric,23.60264993910209
05/08/2025 15:26:38,System.Runtime,GC Committed Bytes (MB),Metric,82.108416
05/08/2025 15:26:38,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:26:38,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:26:38,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:26:38,System.Runtime,Gen 0 Size (B),Metric,183672
05/08/2025 15:26:38,System.Runtime,Gen 1 Size (B),Metric,3939672
05/08/2025 15:26:38,System.Runtime,Gen 2 Size (B),Metric,0
05/08/2025 15:26:38,System.Runtime,LOH Size (B),Metric,23815760
05/08/2025 15:26:38,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:26:38,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:26:38,System.Runtime,IL Bytes Jitted (B),Metric,893122
05/08/2025 15:26:38,System.Runtime,Number of Methods Jitted,Metric,11467
05/08/2025 15:26:38,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,0.9537000000000262
05/08/2025 15:26:39,System.Runtime,CPU Usage (%),Metric,2.9761904761904763
05/08/2025 15:26:39,System.Runtime,Working Set (MB),Metric,339.423232
05/08/2025 15:26:39,System.Runtime,GC Heap Size (MB),Metric,125.594352
05/08/2025 15:26:39,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:39,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:39,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:39,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:26:39,System.Runtime,ThreadPool Thread Count,Metric,5
05/08/2025 15:26:39,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:26:39,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:26:39,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,42
05/08/2025 15:26:39,System.Runtime,Allocation Rate (B / 1 sec),Rate,32800
05/08/2025 15:26:39,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:26:39,System.Runtime,GC Fragmentation (%),Metric,23.60264993910209
05/08/2025 15:26:39,System.Runtime,GC Committed Bytes (MB),Metric,82.108416
05/08/2025 15:26:39,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:26:39,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:26:39,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:26:39,System.Runtime,Gen 0 Size (B),Metric,183672
05/08/2025 15:26:39,System.Runtime,Gen 1 Size (B),Metric,3939672
05/08/2025 15:26:39,System.Runtime,Gen 2 Size (B),Metric,0
05/08/2025 15:26:39,System.Runtime,LOH Size (B),Metric,23815760
05/08/2025 15:26:39,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:26:39,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:26:39,System.Runtime,IL Bytes Jitted (B),Metric,900365
05/08/2025 15:26:39,System.Runtime,Number of Methods Jitted,Metric,11632
05/08/2025 15:26:39,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,157.3064999999997
05/08/2025 15:26:40,System.Runtime,CPU Usage (%),Metric,0
05/08/2025 15:26:40,System.Runtime,Working Set (MB),Metric,339.423232
05/08/2025 15:26:40,System.Runtime,GC Heap Size (MB),Metric,125.602576
05/08/2025 15:26:40,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:40,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:40,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:40,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:26:40,System.Runtime,ThreadPool Thread Count,Metric,5
05/08/2025 15:26:40,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:26:40,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:26:40,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:26:40,System.Runtime,Allocation Rate (B / 1 sec),Rate,0
05/08/2025 15:26:40,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:26:40,System.Runtime,GC Fragmentation (%),Metric,23.60264993910209
05/08/2025 15:26:40,System.Runtime,GC Committed Bytes (MB),Metric,82.108416
05/08/2025 15:26:40,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:26:40,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:26:40,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:26:40,System.Runtime,Gen 0 Size (B),Metric,183672
05/08/2025 15:26:40,System.Runtime,Gen 1 Size (B),Metric,3939672
05/08/2025 15:26:40,System.Runtime,Gen 2 Size (B),Metric,0
05/08/2025 15:26:40,System.Runtime,LOH Size (B),Metric,23815760
05/08/2025 15:26:40,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:26:40,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:26:40,System.Runtime,IL Bytes Jitted (B),Metric,900365
05/08/2025 15:26:40,System.Runtime,Number of Methods Jitted,Metric,11632
05/08/2025 15:26:40,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,0
05/08/2025 15:26:41,System.Runtime,CPU Usage (%),Metric,2.9469548133595285
05/08/2025 15:26:41,System.Runtime,Working Set (MB),Metric,340.058112
05/08/2025 15:26:41,System.Runtime,GC Heap Size (MB),Metric,126.851016
05/08/2025 15:26:41,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:41,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:41,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:41,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:26:41,System.Runtime,ThreadPool Thread Count,Metric,5
05/08/2025 15:26:41,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:26:41,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:26:41,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,3
05/08/2025 15:26:41,System.Runtime,Allocation Rate (B / 1 sec),Rate,1245152
05/08/2025 15:26:41,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:26:41,System.Runtime,GC Fragmentation (%),Metric,23.60264993910209
05/08/2025 15:26:41,System.Runtime,GC Committed Bytes (MB),Metric,82.108416
05/08/2025 15:26:41,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:26:41,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:26:41,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:26:41,System.Runtime,Gen 0 Size (B),Metric,183672
05/08/2025 15:26:41,System.Runtime,Gen 1 Size (B),Metric,3939672
05/08/2025 15:26:41,System.Runtime,Gen 2 Size (B),Metric,0
05/08/2025 15:26:41,System.Runtime,LOH Size (B),Metric,23815760
05/08/2025 15:26:41,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:26:41,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:26:41,System.Runtime,IL Bytes Jitted (B),Metric,913005
05/08/2025 15:26:41,System.Runtime,Number of Methods Jitted,Metric,11751
05/08/2025 15:26:41,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,96.47310000000016
05/08/2025 15:26:42,System.Runtime,CPU Usage (%),Metric,0
05/08/2025 15:26:42,System.Runtime,Working Set (MB),Metric,337.887232
05/08/2025 15:26:42,System.Runtime,GC Heap Size (MB),Metric,126.867416
05/08/2025 15:26:42,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:42,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:42,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:42,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:26:42,System.Runtime,ThreadPool Thread Count,Metric,5
05/08/2025 15:26:42,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:26:42,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:26:42,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:26:42,System.Runtime,Allocation Rate (B / 1 sec),Rate,16992
05/08/2025 15:26:42,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:26:42,System.Runtime,GC Fragmentation (%),Metric,23.60264993910209
05/08/2025 15:26:42,System.Runtime,GC Committed Bytes (MB),Metric,82.108416
05/08/2025 15:26:42,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:26:42,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:26:42,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:26:42,System.Runtime,Gen 0 Size (B),Metric,183672
05/08/2025 15:26:42,System.Runtime,Gen 1 Size (B),Metric,3939672
05/08/2025 15:26:42,System.Runtime,Gen 2 Size (B),Metric,0
05/08/2025 15:26:42,System.Runtime,LOH Size (B),Metric,23815760
05/08/2025 15:26:42,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:26:42,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:26:42,System.Runtime,IL Bytes Jitted (B),Metric,913005
05/08/2025 15:26:42,System.Runtime,Number of Methods Jitted,Metric,11751
05/08/2025 15:26:42,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,0
05/08/2025 15:26:43,System.Runtime,CPU Usage (%),Metric,14.368932038834952
05/08/2025 15:26:43,System.Runtime,Working Set (MB),Metric,325.988352
05/08/2025 15:26:43,System.Runtime,GC Heap Size (MB),Metric,86.905992
05/08/2025 15:26:43,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,2
05/08/2025 15:26:43,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,2
05/08/2025 15:26:43,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,1
05/08/2025 15:26:43,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:26:43,System.Runtime,ThreadPool Thread Count,Metric,5
05/08/2025 15:26:43,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:26:43,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:26:43,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,1
05/08/2025 15:26:43,System.Runtime,Allocation Rate (B / 1 sec),Rate,106246032
05/08/2025 15:26:43,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:26:43,System.Runtime,GC Fragmentation (%),Metric,12.067805407256495
05/08/2025 15:26:43,System.Runtime,GC Committed Bytes (MB),Metric,71.610368
05/08/2025 15:26:43,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:26:43,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:26:43,System.Runtime,Time paused by GC (ms / 1 sec),Rate,13.030999999999999
05/08/2025 15:26:43,System.Runtime,Gen 0 Size (B),Metric,488320
05/08/2025 15:26:43,System.Runtime,Gen 1 Size (B),Metric,172968
05/08/2025 15:26:43,System.Runtime,Gen 2 Size (B),Metric,3829792
05/08/2025 15:26:43,System.Runtime,LOH Size (B),Metric,1020232
05/08/2025 15:26:43,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:26:43,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:26:43,System.Runtime,IL Bytes Jitted (B),Metric,920284
05/08/2025 15:26:43,System.Runtime,Number of Methods Jitted,Metric,11830
05/08/2025 15:26:43,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,99.21700000000055
05/08/2025 15:26:44,System.Runtime,CPU Usage (%),Metric,13.137254901960784
05/08/2025 15:26:44,System.Runtime,Working Set (MB),Metric,345.673728
05/08/2025 15:26:44,System.Runtime,GC Heap Size (MB),Metric,90.622544
05/08/2025 15:26:44,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:44,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:44,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:44,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:26:44,System.Runtime,ThreadPool Thread Count,Metric,5
05/08/2025 15:26:44,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:26:44,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:26:44,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:26:44,System.Runtime,Allocation Rate (B / 1 sec),Rate,3716456
05/08/2025 15:26:44,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:26:44,System.Runtime,GC Fragmentation (%),Metric,12.067805407256495
05/08/2025 15:26:44,System.Runtime,GC Committed Bytes (MB),Metric,71.610368
05/08/2025 15:26:44,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:26:44,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:26:44,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:26:44,System.Runtime,Gen 0 Size (B),Metric,488320
05/08/2025 15:26:44,System.Runtime,Gen 1 Size (B),Metric,172968
05/08/2025 15:26:44,System.Runtime,Gen 2 Size (B),Metric,3829792
05/08/2025 15:26:44,System.Runtime,LOH Size (B),Metric,1020232
05/08/2025 15:26:44,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:26:44,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:26:44,System.Runtime,IL Bytes Jitted (B),Metric,921641
05/08/2025 15:26:44,System.Runtime,Number of Methods Jitted,Metric,11844
05/08/2025 15:26:44,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,54.86110000000008
05/08/2025 15:26:45,System.Runtime,CPU Usage (%),Metric,12.25296442687747
05/08/2025 15:26:45,System.Runtime,Working Set (MB),Metric,360.075264
05/08/2025 15:26:45,System.Runtime,GC Heap Size (MB),Metric,92.734568
05/08/2025 15:26:45,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:45,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:45,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:45,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:26:45,System.Runtime,ThreadPool Thread Count,Metric,5
05/08/2025 15:26:45,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:26:45,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:26:45,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:26:45,System.Runtime,Allocation Rate (B / 1 sec),Rate,2111904
05/08/2025 15:26:45,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:26:45,System.Runtime,GC Fragmentation (%),Metric,12.067805407256495
05/08/2025 15:26:45,System.Runtime,GC Committed Bytes (MB),Metric,71.610368
05/08/2025 15:26:45,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:26:45,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:26:45,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:26:45,System.Runtime,Gen 0 Size (B),Metric,488320
05/08/2025 15:26:45,System.Runtime,Gen 1 Size (B),Metric,172968
05/08/2025 15:26:45,System.Runtime,Gen 2 Size (B),Metric,3829792
05/08/2025 15:26:45,System.Runtime,LOH Size (B),Metric,1020232
05/08/2025 15:26:45,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:26:45,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:26:45,System.Runtime,IL Bytes Jitted (B),Metric,921650
05/08/2025 15:26:45,System.Runtime,Number of Methods Jitted,Metric,11845
05/08/2025 15:26:45,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,1.022399999999834
05/08/2025 15:26:46,System.Runtime,CPU Usage (%),Metric,2.7079303675048356
05/08/2025 15:26:46,System.Runtime,Working Set (MB),Metric,333.98784
05/08/2025 15:26:46,System.Runtime,GC Heap Size (MB),Metric,92.75924
05/08/2025 15:26:46,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:46,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:46,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:46,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:26:46,System.Runtime,ThreadPool Thread Count,Metric,5
05/08/2025 15:26:46,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:26:46,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:26:46,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,42
05/08/2025 15:26:46,System.Runtime,Allocation Rate (B / 1 sec),Rate,24600
05/08/2025 15:26:46,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:26:46,System.Runtime,GC Fragmentation (%),Metric,12.067805407256495
05/08/2025 15:26:46,System.Runtime,GC Committed Bytes (MB),Metric,71.610368
05/08/2025 15:26:46,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:26:46,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:26:46,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:26:46,System.Runtime,Gen 0 Size (B),Metric,488320
05/08/2025 15:26:46,System.Runtime,Gen 1 Size (B),Metric,172968
05/08/2025 15:26:46,System.Runtime,Gen 2 Size (B),Metric,3829792
05/08/2025 15:26:46,System.Runtime,LOH Size (B),Metric,1020232
05/08/2025 15:26:46,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:26:46,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:26:46,System.Runtime,IL Bytes Jitted (B),Metric,927532
05/08/2025 15:26:46,System.Runtime,Number of Methods Jitted,Metric,11944
05/08/2025 15:26:46,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,106.86869999999999
05/08/2025 15:26:47,System.Runtime,CPU Usage (%),Metric,3.5502958579881656
05/08/2025 15:26:47,System.Runtime,Working Set (MB),Metric,334.876672
05/08/2025 15:26:47,System.Runtime,GC Heap Size (MB),Metric,93.938816
05/08/2025 15:26:47,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:47,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:47,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:47,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:26:47,System.Runtime,ThreadPool Thread Count,Metric,5
05/08/2025 15:26:47,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:26:47,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:26:47,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,3
05/08/2025 15:26:47,System.Runtime,Allocation Rate (B / 1 sec),Rate,1184560
05/08/2025 15:26:47,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:26:47,System.Runtime,GC Fragmentation (%),Metric,12.067805407256495
05/08/2025 15:26:47,System.Runtime,GC Committed Bytes (MB),Metric,71.610368
05/08/2025 15:26:47,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:26:47,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:26:47,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:26:47,System.Runtime,Gen 0 Size (B),Metric,488320
05/08/2025 15:26:47,System.Runtime,Gen 1 Size (B),Metric,172968
05/08/2025 15:26:47,System.Runtime,Gen 2 Size (B),Metric,3829792
05/08/2025 15:26:47,System.Runtime,LOH Size (B),Metric,1020232
05/08/2025 15:26:47,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:26:47,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:26:47,System.Runtime,IL Bytes Jitted (B),Metric,939004
05/08/2025 15:26:47,System.Runtime,Number of Methods Jitted,Metric,12035
05/08/2025 15:26:47,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,126.59540000000015
05/08/2025 15:26:48,System.Runtime,CPU Usage (%),Metric,0.5791505791505791
05/08/2025 15:26:48,System.Runtime,Working Set (MB),Metric,334.708736
05/08/2025 15:26:48,System.Runtime,GC Heap Size (MB),Metric,93.963488
05/08/2025 15:26:48,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:48,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:48,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:48,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:26:48,System.Runtime,ThreadPool Thread Count,Metric,5
05/08/2025 15:26:48,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:26:48,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:26:48,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:26:48,System.Runtime,Allocation Rate (B / 1 sec),Rate,16400
05/08/2025 15:26:48,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:26:48,System.Runtime,GC Fragmentation (%),Metric,12.067805407256495
05/08/2025 15:26:48,System.Runtime,GC Committed Bytes (MB),Metric,71.610368
05/08/2025 15:26:48,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:26:48,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:26:48,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:26:48,System.Runtime,Gen 0 Size (B),Metric,488320
05/08/2025 15:26:48,System.Runtime,Gen 1 Size (B),Metric,172968
05/08/2025 15:26:48,System.Runtime,Gen 2 Size (B),Metric,3829792
05/08/2025 15:26:48,System.Runtime,LOH Size (B),Metric,1020232
05/08/2025 15:26:48,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:26:48,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:26:48,System.Runtime,IL Bytes Jitted (B),Metric,939004
05/08/2025 15:26:48,System.Runtime,Number of Methods Jitted,Metric,12035
05/08/2025 15:26:48,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,0
05/08/2025 15:26:49,System.Runtime,CPU Usage (%),Metric,12.84046692607004
05/08/2025 15:26:49,System.Runtime,Working Set (MB),Metric,331.313152
05/08/2025 15:26:49,System.Runtime,GC Heap Size (MB),Metric,68.590408
05/08/2025 15:26:49,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,2
05/08/2025 15:26:49,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,1
05/08/2025 15:26:49,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,1
05/08/2025 15:26:49,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:26:49,System.Runtime,ThreadPool Thread Count,Metric,5
05/08/2025 15:26:49,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:26:49,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:26:49,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:26:49,System.Runtime,Allocation Rate (B / 1 sec),Rate,105035528
05/08/2025 15:26:49,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:26:49,System.Runtime,GC Fragmentation (%),Metric,18.197941683242746
05/08/2025 15:26:49,System.Runtime,GC Committed Bytes (MB),Metric,74.223616
05/08/2025 15:26:49,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:26:49,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:26:49,System.Runtime,Time paused by GC (ms / 1 sec),Rate,8.719000000000001
05/08/2025 15:26:49,System.Runtime,Gen 0 Size (B),Metric,356536
05/08/2025 15:26:49,System.Runtime,Gen 1 Size (B),Metric,337120
05/08/2025 15:26:49,System.Runtime,Gen 2 Size (B),Metric,3829696
05/08/2025 15:26:49,System.Runtime,LOH Size (B),Metric,1020232
05/08/2025 15:26:49,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:26:49,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:26:49,System.Runtime,IL Bytes Jitted (B),Metric,950255
05/08/2025 15:26:49,System.Runtime,Number of Methods Jitted,Metric,12085
05/08/2025 15:26:49,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,219.10959999999977
05/08/2025 15:26:50,System.Runtime,CPU Usage (%),Metric,12.524461839530332
05/08/2025 15:26:50,System.Runtime,Working Set (MB),Metric,348.667904
05/08/2025 15:26:50,System.Runtime,GC Heap Size (MB),Metric,73.39024
05/08/2025 15:26:50,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:50,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:50,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:50,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:26:50,System.Runtime,ThreadPool Thread Count,Metric,5
05/08/2025 15:26:50,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:26:50,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:26:50,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:26:50,System.Runtime,Allocation Rate (B / 1 sec),Rate,4799592
05/08/2025 15:26:50,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:26:50,System.Runtime,GC Fragmentation (%),Metric,18.197941683242746
05/08/2025 15:26:50,System.Runtime,GC Committed Bytes (MB),Metric,74.223616
05/08/2025 15:26:50,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:26:50,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:26:50,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:26:50,System.Runtime,Gen 0 Size (B),Metric,356536
05/08/2025 15:26:50,System.Runtime,Gen 1 Size (B),Metric,337120
05/08/2025 15:26:50,System.Runtime,Gen 2 Size (B),Metric,3829696
05/08/2025 15:26:50,System.Runtime,LOH Size (B),Metric,1020232
05/08/2025 15:26:50,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:26:50,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:26:50,System.Runtime,IL Bytes Jitted (B),Metric,951671
05/08/2025 15:26:50,System.Runtime,Number of Methods Jitted,Metric,12115
05/08/2025 15:26:50,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,21.925900000000183
05/08/2025 15:26:51,System.Runtime,CPU Usage (%),Metric,12.475633528265107
05/08/2025 15:26:51,System.Runtime,Working Set (MB),Metric,348.647424
05/08/2025 15:26:51,System.Runtime,GC Heap Size (MB),Metric,73.398408
05/08/2025 15:26:51,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:51,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:51,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:51,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:26:51,System.Runtime,ThreadPool Thread Count,Metric,5
05/08/2025 15:26:51,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:26:51,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:26:51,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:26:51,System.Runtime,Allocation Rate (B / 1 sec),Rate,8168
05/08/2025 15:26:51,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:26:51,System.Runtime,GC Fragmentation (%),Metric,18.197941683242746
05/08/2025 15:26:51,System.Runtime,GC Committed Bytes (MB),Metric,74.223616
05/08/2025 15:26:51,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:26:51,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:26:51,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:26:51,System.Runtime,Gen 0 Size (B),Metric,356536
05/08/2025 15:26:51,System.Runtime,Gen 1 Size (B),Metric,337120
05/08/2025 15:26:51,System.Runtime,Gen 2 Size (B),Metric,3829696
05/08/2025 15:26:51,System.Runtime,LOH Size (B),Metric,1020232
05/08/2025 15:26:51,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:26:51,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:26:51,System.Runtime,IL Bytes Jitted (B),Metric,951671
05/08/2025 15:26:51,System.Runtime,Number of Methods Jitted,Metric,12115
05/08/2025 15:26:51,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,0
05/08/2025 15:26:52,System.Runtime,CPU Usage (%),Metric,8.984375
05/08/2025 15:26:52,System.Runtime,Working Set (MB),Metric,336.986112
05/08/2025 15:26:52,System.Runtime,GC Heap Size (MB),Metric,75.543648
05/08/2025 15:26:52,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:52,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:52,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:52,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:26:52,System.Runtime,ThreadPool Thread Count,Metric,5
05/08/2025 15:26:52,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:26:52,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:26:52,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,42
05/08/2025 15:26:52,System.Runtime,Allocation Rate (B / 1 sec),Rate,2145024
05/08/2025 15:26:52,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:26:52,System.Runtime,GC Fragmentation (%),Metric,18.197941683242746
05/08/2025 15:26:52,System.Runtime,GC Committed Bytes (MB),Metric,74.223616
05/08/2025 15:26:52,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:26:52,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:26:52,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:26:52,System.Runtime,Gen 0 Size (B),Metric,356536
05/08/2025 15:26:52,System.Runtime,Gen 1 Size (B),Metric,337120
05/08/2025 15:26:52,System.Runtime,Gen 2 Size (B),Metric,3829696
05/08/2025 15:26:52,System.Runtime,LOH Size (B),Metric,1020232
05/08/2025 15:26:52,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:26:52,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:26:52,System.Runtime,IL Bytes Jitted (B),Metric,952912
05/08/2025 15:26:52,System.Runtime,Number of Methods Jitted,Metric,12148
05/08/2025 15:26:52,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,16.516099999999824
05/08/2025 15:26:53,System.Runtime,CPU Usage (%),Metric,1.3671875
05/08/2025 15:26:53,System.Runtime,Working Set (MB),Metric,337.108992
05/08/2025 15:26:53,System.Runtime,GC Heap Size (MB),Metric,75.543648
05/08/2025 15:26:53,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:53,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:53,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:53,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:26:53,System.Runtime,ThreadPool Thread Count,Metric,5
05/08/2025 15:26:53,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:26:53,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:26:53,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:26:53,System.Runtime,Allocation Rate (B / 1 sec),Rate,8200
05/08/2025 15:26:53,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:26:53,System.Runtime,GC Fragmentation (%),Metric,18.197941683242746
05/08/2025 15:26:53,System.Runtime,GC Committed Bytes (MB),Metric,74.223616
05/08/2025 15:26:53,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:26:53,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:26:53,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:26:53,System.Runtime,Gen 0 Size (B),Metric,356536
05/08/2025 15:26:53,System.Runtime,Gen 1 Size (B),Metric,337120
05/08/2025 15:26:53,System.Runtime,Gen 2 Size (B),Metric,3829696
05/08/2025 15:26:53,System.Runtime,LOH Size (B),Metric,1020232
05/08/2025 15:26:53,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:26:53,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:26:53,System.Runtime,IL Bytes Jitted (B),Metric,959630
05/08/2025 15:26:53,System.Runtime,Number of Methods Jitted,Metric,12269
05/08/2025 15:26:53,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,102.28639999999996
05/08/2025 15:26:54,System.Runtime,CPU Usage (%),Metric,3.1746031746031744
05/08/2025 15:26:54,System.Runtime,Working Set (MB),Metric,337.42848
05/08/2025 15:26:54,System.Runtime,GC Heap Size (MB),Metric,76.732696
05/08/2025 15:26:54,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:54,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:54,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:54,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:26:54,System.Runtime,ThreadPool Thread Count,Metric,5
05/08/2025 15:26:54,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:26:54,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:26:54,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,3
05/08/2025 15:26:54,System.Runtime,Allocation Rate (B / 1 sec),Rate,1177752
05/08/2025 15:26:54,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:26:54,System.Runtime,GC Fragmentation (%),Metric,18.197941683242746
05/08/2025 15:26:54,System.Runtime,GC Committed Bytes (MB),Metric,74.223616
05/08/2025 15:26:54,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:26:54,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:26:54,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:26:54,System.Runtime,Gen 0 Size (B),Metric,356536
05/08/2025 15:26:54,System.Runtime,Gen 1 Size (B),Metric,337120
05/08/2025 15:26:54,System.Runtime,Gen 2 Size (B),Metric,3829696
05/08/2025 15:26:54,System.Runtime,LOH Size (B),Metric,1020232
05/08/2025 15:26:54,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:26:54,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:26:54,System.Runtime,IL Bytes Jitted (B),Metric,972828
05/08/2025 15:26:54,System.Runtime,Number of Methods Jitted,Metric,12343
05/08/2025 15:26:54,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,66.53189999999995
05/08/2025 15:26:55,System.Runtime,CPU Usage (%),Metric,1.3461538461538463
05/08/2025 15:26:55,System.Runtime,Working Set (MB),Metric,335.867904
05/08/2025 15:26:55,System.Runtime,GC Heap Size (MB),Metric,81.80928
05/08/2025 15:26:55,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:55,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:55,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:55,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:26:55,System.Runtime,ThreadPool Thread Count,Metric,5
05/08/2025 15:26:55,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:26:55,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:26:55,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:26:55,System.Runtime,Allocation Rate (B / 1 sec),Rate,5055032
05/08/2025 15:26:55,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:26:55,System.Runtime,GC Fragmentation (%),Metric,18.197941683242746
05/08/2025 15:26:55,System.Runtime,GC Committed Bytes (MB),Metric,74.223616
05/08/2025 15:26:55,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:26:55,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:26:55,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:26:55,System.Runtime,Gen 0 Size (B),Metric,356536
05/08/2025 15:26:55,System.Runtime,Gen 1 Size (B),Metric,337120
05/08/2025 15:26:55,System.Runtime,Gen 2 Size (B),Metric,3829696
05/08/2025 15:26:55,System.Runtime,LOH Size (B),Metric,1020232
05/08/2025 15:26:55,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:26:55,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:26:55,System.Runtime,IL Bytes Jitted (B),Metric,972846
05/08/2025 15:26:55,System.Runtime,Number of Methods Jitted,Metric,12344
05/08/2025 15:26:55,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,0.5833999999995285
05/08/2025 15:26:56,System.Runtime,CPU Usage (%),Metric,13.450292397660819
05/08/2025 15:26:56,System.Runtime,Working Set (MB),Metric,334.696448
05/08/2025 15:26:56,System.Runtime,GC Heap Size (MB),Metric,88.279088
05/08/2025 15:26:56,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,1
05/08/2025 15:26:56,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,1
05/08/2025 15:26:56,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,1
05/08/2025 15:26:56,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:26:56,System.Runtime,ThreadPool Thread Count,Metric,5
05/08/2025 15:26:56,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:26:56,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:26:56,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:26:56,System.Runtime,Allocation Rate (B / 1 sec),Rate,103730424
05/08/2025 15:26:56,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:26:56,System.Runtime,GC Fragmentation (%),Metric,26.20162747721754
05/08/2025 15:26:56,System.Runtime,GC Committed Bytes (MB),Metric,139.157504
05/08/2025 15:26:56,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:26:56,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:26:56,System.Runtime,Time paused by GC (ms / 1 sec),Rate,4.4709999999999965
05/08/2025 15:26:56,System.Runtime,Gen 0 Size (B),Metric,44423520
05/08/2025 15:26:56,System.Runtime,Gen 1 Size (B),Metric,337120
05/08/2025 15:26:56,System.Runtime,Gen 2 Size (B),Metric,3829696
05/08/2025 15:26:56,System.Runtime,LOH Size (B),Metric,65479560
05/08/2025 15:26:56,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:26:56,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:26:56,System.Runtime,IL Bytes Jitted (B),Metric,979985
05/08/2025 15:26:56,System.Runtime,Number of Methods Jitted,Metric,12399
05/08/2025 15:26:56,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,60.01720000000023
05/08/2025 15:26:57,System.Runtime,CPU Usage (%),Metric,12.156862745098039
05/08/2025 15:26:57,System.Runtime,Working Set (MB),Metric,349.495296
05/08/2025 15:26:57,System.Runtime,GC Heap Size (MB),Metric,89.345112
05/08/2025 15:26:57,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:57,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:57,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:57,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:26:57,System.Runtime,ThreadPool Thread Count,Metric,5
05/08/2025 15:26:57,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:26:57,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:26:57,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:26:57,System.Runtime,Allocation Rate (B / 1 sec),Rate,1065976
05/08/2025 15:26:57,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:26:57,System.Runtime,GC Fragmentation (%),Metric,26.20162747721754
05/08/2025 15:26:57,System.Runtime,GC Committed Bytes (MB),Metric,139.157504
05/08/2025 15:26:57,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:26:57,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:26:57,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:26:57,System.Runtime,Gen 0 Size (B),Metric,44423520
05/08/2025 15:26:57,System.Runtime,Gen 1 Size (B),Metric,337120
05/08/2025 15:26:57,System.Runtime,Gen 2 Size (B),Metric,3829696
05/08/2025 15:26:57,System.Runtime,LOH Size (B),Metric,65479560
05/08/2025 15:26:57,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:26:57,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:26:57,System.Runtime,IL Bytes Jitted (B),Metric,980306
05/08/2025 15:26:57,System.Runtime,Number of Methods Jitted,Metric,12404
05/08/2025 15:26:57,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,2.8038000000005923
05/08/2025 15:26:58,System.Runtime,CPU Usage (%),Metric,11.937377690802348
05/08/2025 15:26:58,System.Runtime,Working Set (MB),Metric,363.266048
05/08/2025 15:26:58,System.Runtime,GC Heap Size (MB),Metric,91.459416
05/08/2025 15:26:58,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:58,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:58,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:58,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:26:58,System.Runtime,ThreadPool Thread Count,Metric,5
05/08/2025 15:26:58,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:26:58,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:26:58,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,1
05/08/2025 15:26:58,System.Runtime,Allocation Rate (B / 1 sec),Rate,2114208
05/08/2025 15:26:58,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:26:58,System.Runtime,GC Fragmentation (%),Metric,26.20162747721754
05/08/2025 15:26:58,System.Runtime,GC Committed Bytes (MB),Metric,139.157504
05/08/2025 15:26:58,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:26:58,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:26:58,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:26:58,System.Runtime,Gen 0 Size (B),Metric,44423520
05/08/2025 15:26:58,System.Runtime,Gen 1 Size (B),Metric,337120
05/08/2025 15:26:58,System.Runtime,Gen 2 Size (B),Metric,3829696
05/08/2025 15:26:58,System.Runtime,LOH Size (B),Metric,65479560
05/08/2025 15:26:58,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:26:58,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:26:58,System.Runtime,IL Bytes Jitted (B),Metric,980329
05/08/2025 15:26:58,System.Runtime,Number of Methods Jitted,Metric,12406
05/08/2025 15:26:58,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,0.30089999999927386
05/08/2025 15:26:59,System.Runtime,CPU Usage (%),Metric,2.140077821011673
05/08/2025 15:26:59,System.Runtime,Working Set (MB),Metric,336.007168
05/08/2025 15:26:59,System.Runtime,GC Heap Size (MB),Metric,91.492312
05/08/2025 15:26:59,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:59,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:59,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:26:59,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:26:59,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:26:59,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:26:59,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:26:59,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,41
05/08/2025 15:26:59,System.Runtime,Allocation Rate (B / 1 sec),Rate,32800
05/08/2025 15:26:59,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:26:59,System.Runtime,GC Fragmentation (%),Metric,26.20162747721754
05/08/2025 15:26:59,System.Runtime,GC Committed Bytes (MB),Metric,139.157504
05/08/2025 15:26:59,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:26:59,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:26:59,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:26:59,System.Runtime,Gen 0 Size (B),Metric,44423520
05/08/2025 15:26:59,System.Runtime,Gen 1 Size (B),Metric,337120
05/08/2025 15:26:59,System.Runtime,Gen 2 Size (B),Metric,3829696
05/08/2025 15:26:59,System.Runtime,LOH Size (B),Metric,65479560
05/08/2025 15:26:59,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:26:59,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:26:59,System.Runtime,IL Bytes Jitted (B),Metric,984638
05/08/2025 15:26:59,System.Runtime,Number of Methods Jitted,Metric,12452
05/08/2025 15:26:59,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,59.13310000000001
05/08/2025 15:27:00,System.Runtime,CPU Usage (%),Metric,2.75049115913556
05/08/2025 15:27:00,System.Runtime,Working Set (MB),Metric,336.539648
05/08/2025 15:27:00,System.Runtime,GC Heap Size (MB),Metric,92.673656
05/08/2025 15:27:00,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:00,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:00,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:00,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:27:00,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:27:00,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:27:00,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:27:00,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,3
05/08/2025 15:27:00,System.Runtime,Allocation Rate (B / 1 sec),Rate,1186400
05/08/2025 15:27:00,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:27:00,System.Runtime,GC Fragmentation (%),Metric,26.20162747721754
05/08/2025 15:27:00,System.Runtime,GC Committed Bytes (MB),Metric,139.157504
05/08/2025 15:27:00,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:27:00,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:27:00,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:27:00,System.Runtime,Gen 0 Size (B),Metric,44423520
05/08/2025 15:27:00,System.Runtime,Gen 1 Size (B),Metric,337120
05/08/2025 15:27:00,System.Runtime,Gen 2 Size (B),Metric,3829696
05/08/2025 15:27:00,System.Runtime,LOH Size (B),Metric,65479560
05/08/2025 15:27:00,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:27:00,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:27:00,System.Runtime,IL Bytes Jitted (B),Metric,994606
05/08/2025 15:27:00,System.Runtime,Number of Methods Jitted,Metric,12523
05/08/2025 15:27:00,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,85.98100000000068
05/08/2025 15:27:01,System.Runtime,CPU Usage (%),Metric,0.19723865877712032
05/08/2025 15:27:01,System.Runtime,Working Set (MB),Metric,336.560128
05/08/2025 15:27:01,System.Runtime,GC Heap Size (MB),Metric,92.698208
05/08/2025 15:27:01,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:01,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:01,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:01,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:27:01,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:27:01,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:27:01,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:27:01,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:27:01,System.Runtime,Allocation Rate (B / 1 sec),Rate,16376
05/08/2025 15:27:01,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:27:01,System.Runtime,GC Fragmentation (%),Metric,26.20162747721754
05/08/2025 15:27:01,System.Runtime,GC Committed Bytes (MB),Metric,139.157504
05/08/2025 15:27:01,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:27:01,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:27:01,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:27:01,System.Runtime,Gen 0 Size (B),Metric,44423520
05/08/2025 15:27:01,System.Runtime,Gen 1 Size (B),Metric,337120
05/08/2025 15:27:01,System.Runtime,Gen 2 Size (B),Metric,3829696
05/08/2025 15:27:01,System.Runtime,LOH Size (B),Metric,65479560
05/08/2025 15:27:01,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:27:01,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:27:01,System.Runtime,IL Bytes Jitted (B),Metric,994606
05/08/2025 15:27:01,System.Runtime,Number of Methods Jitted,Metric,12523
05/08/2025 15:27:01,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,0
05/08/2025 15:27:02,System.Runtime,CPU Usage (%),Metric,5.639097744360902
05/08/2025 15:27:02,System.Runtime,Working Set (MB),Metric,335.917056
05/08/2025 15:27:02,System.Runtime,GC Heap Size (MB),Metric,86.26644
05/08/2025 15:27:02,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,1
05/08/2025 15:27:02,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:02,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:02,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:27:02,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:27:02,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:27:02,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:27:02,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:27:02,System.Runtime,Allocation Rate (B / 1 sec),Rate,42083008
05/08/2025 15:27:02,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:27:02,System.Runtime,GC Fragmentation (%),Metric,24.990076637281497
05/08/2025 15:27:02,System.Runtime,GC Committed Bytes (MB),Metric,144.605184
05/08/2025 15:27:02,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:27:02,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:27:02,System.Runtime,Time paused by GC (ms / 1 sec),Rate,1.088000000000008
05/08/2025 15:27:02,System.Runtime,Gen 0 Size (B),Metric,183672
05/08/2025 15:27:02,System.Runtime,Gen 1 Size (B),Metric,376256
05/08/2025 15:27:02,System.Runtime,Gen 2 Size (B),Metric,3829696
05/08/2025 15:27:02,System.Runtime,LOH Size (B),Metric,71486368
05/08/2025 15:27:02,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:27:02,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:27:02,System.Runtime,IL Bytes Jitted (B),Metric,997549
05/08/2025 15:27:02,System.Runtime,Number of Methods Jitted,Metric,12553
05/08/2025 15:27:02,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,87.26749999999993
05/08/2025 15:27:03,System.Runtime,CPU Usage (%),Metric,12.601626016260163
05/08/2025 15:27:03,System.Runtime,Working Set (MB),Metric,408.92416
05/08/2025 15:27:03,System.Runtime,GC Heap Size (MB),Metric,154.070208
05/08/2025 15:27:03,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:03,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:03,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:03,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:27:03,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:27:03,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:27:03,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:27:03,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:27:03,System.Runtime,Allocation Rate (B / 1 sec),Rate,67803408
05/08/2025 15:27:03,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:27:03,System.Runtime,GC Fragmentation (%),Metric,24.990076637281497
05/08/2025 15:27:03,System.Runtime,GC Committed Bytes (MB),Metric,144.605184
05/08/2025 15:27:03,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:27:03,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:27:03,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:27:03,System.Runtime,Gen 0 Size (B),Metric,183672
05/08/2025 15:27:03,System.Runtime,Gen 1 Size (B),Metric,376256
05/08/2025 15:27:03,System.Runtime,Gen 2 Size (B),Metric,3829696
05/08/2025 15:27:03,System.Runtime,LOH Size (B),Metric,71486368
05/08/2025 15:27:03,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:27:03,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:27:03,System.Runtime,IL Bytes Jitted (B),Metric,999702
05/08/2025 15:27:03,System.Runtime,Number of Methods Jitted,Metric,12596
05/08/2025 15:27:03,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,35.4411999999993
05/08/2025 15:27:04,System.Runtime,CPU Usage (%),Metric,12.062256809338521
05/08/2025 15:27:04,System.Runtime,Working Set (MB),Metric,408.92416
05/08/2025 15:27:04,System.Runtime,GC Heap Size (MB),Metric,154.078432
05/08/2025 15:27:04,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:04,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:04,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:04,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:27:04,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:27:04,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:27:04,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:27:04,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:27:04,System.Runtime,Allocation Rate (B / 1 sec),Rate,8200
05/08/2025 15:27:04,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:27:04,System.Runtime,GC Fragmentation (%),Metric,24.990076637281497
05/08/2025 15:27:04,System.Runtime,GC Committed Bytes (MB),Metric,144.605184
05/08/2025 15:27:04,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:27:04,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:27:04,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:27:04,System.Runtime,Gen 0 Size (B),Metric,183672
05/08/2025 15:27:04,System.Runtime,Gen 1 Size (B),Metric,376256
05/08/2025 15:27:04,System.Runtime,Gen 2 Size (B),Metric,3829696
05/08/2025 15:27:04,System.Runtime,LOH Size (B),Metric,71486368
05/08/2025 15:27:04,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:27:04,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:27:04,System.Runtime,IL Bytes Jitted (B),Metric,999702
05/08/2025 15:27:04,System.Runtime,Number of Methods Jitted,Metric,12596
05/08/2025 15:27:04,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,0
05/08/2025 15:27:05,System.Runtime,CPU Usage (%),Metric,12.572533849129593
05/08/2025 15:27:05,System.Runtime,Working Set (MB),Metric,422.576128
05/08/2025 15:27:05,System.Runtime,GC Heap Size (MB),Metric,155.1304
05/08/2025 15:27:05,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:05,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:05,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:05,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:27:05,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:27:05,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:27:05,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:27:05,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:27:05,System.Runtime,Allocation Rate (B / 1 sec),Rate,1051872
05/08/2025 15:27:05,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:27:05,System.Runtime,GC Fragmentation (%),Metric,24.990076637281497
05/08/2025 15:27:05,System.Runtime,GC Committed Bytes (MB),Metric,144.605184
05/08/2025 15:27:05,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:27:05,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:27:05,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:27:05,System.Runtime,Gen 0 Size (B),Metric,183672
05/08/2025 15:27:05,System.Runtime,Gen 1 Size (B),Metric,376256
05/08/2025 15:27:05,System.Runtime,Gen 2 Size (B),Metric,3829696
05/08/2025 15:27:05,System.Runtime,LOH Size (B),Metric,71486368
05/08/2025 15:27:05,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:27:05,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:27:05,System.Runtime,IL Bytes Jitted (B),Metric,999982
05/08/2025 15:27:05,System.Runtime,Number of Methods Jitted,Metric,12610
05/08/2025 15:27:05,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,5.412300000000869
05/08/2025 15:27:06,System.Runtime,CPU Usage (%),Metric,4.356435643564357
05/08/2025 15:27:06,System.Runtime,Working Set (MB),Metric,397.266944
05/08/2025 15:27:06,System.Runtime,GC Heap Size (MB),Metric,156.219192
05/08/2025 15:27:06,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:06,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:06,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:06,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:27:06,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:27:06,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:27:06,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:27:06,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,42
05/08/2025 15:27:06,System.Runtime,Allocation Rate (B / 1 sec),Rate,1096896
05/08/2025 15:27:06,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:27:06,System.Runtime,GC Fragmentation (%),Metric,24.990076637281497
05/08/2025 15:27:06,System.Runtime,GC Committed Bytes (MB),Metric,144.605184
05/08/2025 15:27:06,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:27:06,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:27:06,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:27:06,System.Runtime,Gen 0 Size (B),Metric,183672
05/08/2025 15:27:06,System.Runtime,Gen 1 Size (B),Metric,376256
05/08/2025 15:27:06,System.Runtime,Gen 2 Size (B),Metric,3829696
05/08/2025 15:27:06,System.Runtime,LOH Size (B),Metric,71486368
05/08/2025 15:27:06,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:27:06,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:27:06,System.Runtime,IL Bytes Jitted (B),Metric,1008140
05/08/2025 15:27:06,System.Runtime,Number of Methods Jitted,Metric,12694
05/08/2025 15:27:06,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,127.79579999999987
05/08/2025 15:27:07,System.Runtime,CPU Usage (%),Metric,2.6923076923076925
05/08/2025 15:27:07,System.Runtime,Working Set (MB),Metric,397.574144
05/08/2025 15:27:07,System.Runtime,GC Heap Size (MB),Metric,157.466488
05/08/2025 15:27:07,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:07,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:07,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:07,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:27:07,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:27:07,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:27:07,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:27:07,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,8
05/08/2025 15:27:07,System.Runtime,Allocation Rate (B / 1 sec),Rate,1228120
05/08/2025 15:27:07,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:27:07,System.Runtime,GC Fragmentation (%),Metric,24.990076637281497
05/08/2025 15:27:07,System.Runtime,GC Committed Bytes (MB),Metric,144.605184
05/08/2025 15:27:07,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:27:07,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:27:07,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:27:07,System.Runtime,Gen 0 Size (B),Metric,183672
05/08/2025 15:27:07,System.Runtime,Gen 1 Size (B),Metric,376256
05/08/2025 15:27:07,System.Runtime,Gen 2 Size (B),Metric,3829696
05/08/2025 15:27:07,System.Runtime,LOH Size (B),Metric,71486368
05/08/2025 15:27:07,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:27:07,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:27:07,System.Runtime,IL Bytes Jitted (B),Metric,1015633
05/08/2025 15:27:07,System.Runtime,Number of Methods Jitted,Metric,12813
05/08/2025 15:27:07,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,196.1993999999995
05/08/2025 15:27:08,System.Runtime,CPU Usage (%),Metric,0
05/08/2025 15:27:08,System.Runtime,Working Set (MB),Metric,397.570048
05/08/2025 15:27:08,System.Runtime,GC Heap Size (MB),Metric,157.474712
05/08/2025 15:27:08,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:08,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:08,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:08,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:27:08,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:27:08,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:27:08,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:27:08,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:27:08,System.Runtime,Allocation Rate (B / 1 sec),Rate,8200
05/08/2025 15:27:08,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:27:08,System.Runtime,GC Fragmentation (%),Metric,24.990076637281497
05/08/2025 15:27:08,System.Runtime,GC Committed Bytes (MB),Metric,144.605184
05/08/2025 15:27:08,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:27:08,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:27:08,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:27:08,System.Runtime,Gen 0 Size (B),Metric,183672
05/08/2025 15:27:08,System.Runtime,Gen 1 Size (B),Metric,376256
05/08/2025 15:27:08,System.Runtime,Gen 2 Size (B),Metric,3829696
05/08/2025 15:27:08,System.Runtime,LOH Size (B),Metric,71486368
05/08/2025 15:27:08,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:27:08,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:27:08,System.Runtime,IL Bytes Jitted (B),Metric,1015633
05/08/2025 15:27:08,System.Runtime,Number of Methods Jitted,Metric,12813
05/08/2025 15:27:08,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,0
05/08/2025 15:27:09,System.Runtime,CPU Usage (%),Metric,3.515625
05/08/2025 15:27:09,System.Runtime,Working Set (MB),Metric,398.82752
05/08/2025 15:27:09,System.Runtime,GC Heap Size (MB),Metric,128.219464
05/08/2025 15:27:09,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,2
05/08/2025 15:27:09,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,1
05/08/2025 15:27:09,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,1
05/08/2025 15:27:09,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:27:09,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:27:09,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:27:09,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:27:09,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:27:09,System.Runtime,Allocation Rate (B / 1 sec),Rate,31112376
05/08/2025 15:27:09,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:27:09,System.Runtime,GC Fragmentation (%),Metric,10.153608517189605
05/08/2025 15:27:09,System.Runtime,GC Committed Bytes (MB),Metric,210.313216
05/08/2025 15:27:09,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:27:09,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:27:09,System.Runtime,Time paused by GC (ms / 1 sec),Rate,10.966999999999999
05/08/2025 15:27:09,System.Runtime,Gen 0 Size (B),Metric,183672
05/08/2025 15:27:09,System.Runtime,Gen 1 Size (B),Metric,485888
05/08/2025 15:27:09,System.Runtime,Gen 2 Size (B),Metric,3829696
05/08/2025 15:27:09,System.Runtime,LOH Size (B),Metric,136734728
05/08/2025 15:27:09,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:27:09,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:27:09,System.Runtime,IL Bytes Jitted (B),Metric,1017786
05/08/2025 15:27:09,System.Runtime,Number of Methods Jitted,Metric,12825
05/08/2025 15:27:09,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,20.928600000000188
05/08/2025 15:27:10,System.Runtime,CPU Usage (%),Metric,14.37007874015748
05/08/2025 15:27:10,System.Runtime,Working Set (MB),Metric,363.831296
05/08/2025 15:27:10,System.Runtime,GC Heap Size (MB),Metric,83.71524
05/08/2025 15:27:10,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:10,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:10,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:10,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:27:10,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:27:10,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:27:10,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:27:10,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:27:10,System.Runtime,Allocation Rate (B / 1 sec),Rate,78741048
05/08/2025 15:27:10,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:27:10,System.Runtime,GC Fragmentation (%),Metric,21.974937789968752
05/08/2025 15:27:10,System.Runtime,GC Committed Bytes (MB),Metric,93.26592
05/08/2025 15:27:10,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:27:10,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:27:10,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:27:10,System.Runtime,Gen 0 Size (B),Metric,480248
05/08/2025 15:27:10,System.Runtime,Gen 1 Size (B),Metric,485888
05/08/2025 15:27:10,System.Runtime,Gen 2 Size (B),Metric,3829696
05/08/2025 15:27:10,System.Runtime,LOH Size (B),Metric,1020232
05/08/2025 15:27:10,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:27:10,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:27:10,System.Runtime,IL Bytes Jitted (B),Metric,1020839
05/08/2025 15:27:10,System.Runtime,Number of Methods Jitted,Metric,12867
05/08/2025 15:27:10,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,42.44539999999961
05/08/2025 15:27:11,System.Runtime,CPU Usage (%),Metric,12.301587301587302
05/08/2025 15:27:11,System.Runtime,Working Set (MB),Metric,363.8272
05/08/2025 15:27:11,System.Runtime,GC Heap Size (MB),Metric,83.722824
05/08/2025 15:27:11,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:11,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:11,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:11,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:27:11,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:27:11,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:27:11,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:27:11,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:27:11,System.Runtime,Allocation Rate (B / 1 sec),Rate,7584
05/08/2025 15:27:11,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:27:11,System.Runtime,GC Fragmentation (%),Metric,21.974937789968752
05/08/2025 15:27:11,System.Runtime,GC Committed Bytes (MB),Metric,93.26592
05/08/2025 15:27:11,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:27:11,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:27:11,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:27:11,System.Runtime,Gen 0 Size (B),Metric,480248
05/08/2025 15:27:11,System.Runtime,Gen 1 Size (B),Metric,485888
05/08/2025 15:27:11,System.Runtime,Gen 2 Size (B),Metric,3829696
05/08/2025 15:27:11,System.Runtime,LOH Size (B),Metric,1020232
05/08/2025 15:27:11,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:27:11,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:27:11,System.Runtime,IL Bytes Jitted (B),Metric,1020839
05/08/2025 15:27:11,System.Runtime,Number of Methods Jitted,Metric,12867
05/08/2025 15:27:11,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,0
05/08/2025 15:27:12,System.Runtime,CPU Usage (%),Metric,12.595419847328245
05/08/2025 15:27:12,System.Runtime,Working Set (MB),Metric,350.199808
05/08/2025 15:27:12,System.Runtime,GC Heap Size (MB),Metric,85.861344
05/08/2025 15:27:12,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:12,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:12,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:12,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:27:12,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:27:12,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:27:12,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:27:12,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,39
05/08/2025 15:27:12,System.Runtime,Allocation Rate (B / 1 sec),Rate,2138352
05/08/2025 15:27:12,System.Runtime,Number of Active Timers,Metric,5
05/08/2025 15:27:12,System.Runtime,GC Fragmentation (%),Metric,21.974937789968752
05/08/2025 15:27:12,System.Runtime,GC Committed Bytes (MB),Metric,93.26592
05/08/2025 15:27:12,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:27:12,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:27:12,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:27:12,System.Runtime,Gen 0 Size (B),Metric,480248
05/08/2025 15:27:12,System.Runtime,Gen 1 Size (B),Metric,485888
05/08/2025 15:27:12,System.Runtime,Gen 2 Size (B),Metric,3829696
05/08/2025 15:27:12,System.Runtime,LOH Size (B),Metric,1020232
05/08/2025 15:27:12,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:27:12,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:27:12,System.Runtime,IL Bytes Jitted (B),Metric,1024508
05/08/2025 15:27:12,System.Runtime,Number of Methods Jitted,Metric,12911
05/08/2025 15:27:12,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,80.56810000000041
05/08/2025 15:27:13,System.Runtime,CPU Usage (%),Metric,0.1984126984126984
05/08/2025 15:27:13,System.Runtime,Working Set (MB),Metric,350.236672
05/08/2025 15:27:13,System.Runtime,GC Heap Size (MB),Metric,85.861344
05/08/2025 15:27:13,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:13,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:13,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:13,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:27:13,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:27:13,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:27:13,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:27:13,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,4
05/08/2025 15:27:13,System.Runtime,Allocation Rate (B / 1 sec),Rate,8192
05/08/2025 15:27:13,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:27:13,System.Runtime,GC Fragmentation (%),Metric,21.974937789968752
05/08/2025 15:27:13,System.Runtime,GC Committed Bytes (MB),Metric,93.26592
05/08/2025 15:27:13,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:27:13,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:27:13,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:27:13,System.Runtime,Gen 0 Size (B),Metric,480248
05/08/2025 15:27:13,System.Runtime,Gen 1 Size (B),Metric,485888
05/08/2025 15:27:13,System.Runtime,Gen 2 Size (B),Metric,3829696
05/08/2025 15:27:13,System.Runtime,LOH Size (B),Metric,1020232
05/08/2025 15:27:13,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:27:13,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:27:13,System.Runtime,IL Bytes Jitted (B),Metric,1026014
05/08/2025 15:27:13,System.Runtime,Number of Methods Jitted,Metric,12935
05/08/2025 15:27:13,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,31.63000000000011
05/08/2025 15:27:14,System.Runtime,CPU Usage (%),Metric,1.9342359767891684
05/08/2025 15:27:14,System.Runtime,Working Set (MB),Metric,350.728192
05/08/2025 15:27:14,System.Runtime,GC Heap Size (MB),Metric,87.108176
05/08/2025 15:27:14,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:14,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:14,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:14,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:27:14,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:27:14,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:27:14,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:27:14,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,3
05/08/2025 15:27:14,System.Runtime,Allocation Rate (B / 1 sec),Rate,1235304
05/08/2025 15:27:14,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:27:14,System.Runtime,GC Fragmentation (%),Metric,21.974937789968752
05/08/2025 15:27:14,System.Runtime,GC Committed Bytes (MB),Metric,93.26592
05/08/2025 15:27:14,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:27:14,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:27:14,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:27:14,System.Runtime,Gen 0 Size (B),Metric,480248
05/08/2025 15:27:14,System.Runtime,Gen 1 Size (B),Metric,485888
05/08/2025 15:27:14,System.Runtime,Gen 2 Size (B),Metric,3829696
05/08/2025 15:27:14,System.Runtime,LOH Size (B),Metric,1020232
05/08/2025 15:27:14,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:27:14,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:27:14,System.Runtime,IL Bytes Jitted (B),Metric,1028020
05/08/2025 15:27:14,System.Runtime,Number of Methods Jitted,Metric,12960
05/08/2025 15:27:14,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,25.726399999999558
05/08/2025 15:27:15,System.Runtime,CPU Usage (%),Metric,0.1988071570576541
05/08/2025 15:27:15,System.Runtime,Working Set (MB),Metric,350.732288
05/08/2025 15:27:15,System.Runtime,GC Heap Size (MB),Metric,87.132784
05/08/2025 15:27:15,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:15,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:15,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:15,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:27:15,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:27:15,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:27:15,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:27:15,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:27:15,System.Runtime,Allocation Rate (B / 1 sec),Rate,24560
05/08/2025 15:27:15,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:27:15,System.Runtime,GC Fragmentation (%),Metric,21.974937789968752
05/08/2025 15:27:15,System.Runtime,GC Committed Bytes (MB),Metric,93.26592
05/08/2025 15:27:15,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:27:15,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:27:15,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:27:15,System.Runtime,Gen 0 Size (B),Metric,480248
05/08/2025 15:27:15,System.Runtime,Gen 1 Size (B),Metric,485888
05/08/2025 15:27:15,System.Runtime,Gen 2 Size (B),Metric,3829696
05/08/2025 15:27:15,System.Runtime,LOH Size (B),Metric,1020232
05/08/2025 15:27:15,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:27:15,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:27:15,System.Runtime,IL Bytes Jitted (B),Metric,1028020
05/08/2025 15:27:15,System.Runtime,Number of Methods Jitted,Metric,12960
05/08/2025 15:27:15,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,0
05/08/2025 15:27:16,System.Runtime,CPU Usage (%),Metric,11.450381679389313
05/08/2025 15:27:16,System.Runtime,Working Set (MB),Metric,348.438528
05/08/2025 15:27:16,System.Runtime,GC Heap Size (MB),Metric,89.038512
05/08/2025 15:27:16,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,1
05/08/2025 15:27:16,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,1
05/08/2025 15:27:16,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,1
05/08/2025 15:27:16,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:27:16,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:27:16,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:27:16,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:27:16,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:27:16,System.Runtime,Allocation Rate (B / 1 sec),Rate,105350184
05/08/2025 15:27:16,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:27:16,System.Runtime,GC Fragmentation (%),Metric,29.605744293428465
05/08/2025 15:27:16,System.Runtime,GC Committed Bytes (MB),Metric,158.728192
05/08/2025 15:27:16,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:27:16,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:27:16,System.Runtime,Time paused by GC (ms / 1 sec),Rate,8.216999999999999
05/08/2025 15:27:16,System.Runtime,Gen 0 Size (B),Metric,54816808
05/08/2025 15:27:16,System.Runtime,Gen 1 Size (B),Metric,485888
05/08/2025 15:27:16,System.Runtime,Gen 2 Size (B),Metric,3829696
05/08/2025 15:27:16,System.Runtime,LOH Size (B),Metric,66394304
05/08/2025 15:27:16,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:27:16,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:27:16,System.Runtime,IL Bytes Jitted (B),Metric,1032230
05/08/2025 15:27:16,System.Runtime,Number of Methods Jitted,Metric,12994
05/08/2025 15:27:16,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,41.3713000000007
05/08/2025 15:27:17,System.Runtime,CPU Usage (%),Metric,12.3046875
05/08/2025 15:27:17,System.Runtime,Working Set (MB),Metric,363.74528
05/08/2025 15:27:17,System.Runtime,GC Heap Size (MB),Metric,93.55888
05/08/2025 15:27:17,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:17,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:17,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:17,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:27:17,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:27:17,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:27:17,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:27:17,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:27:17,System.Runtime,Allocation Rate (B / 1 sec),Rate,4520296
05/08/2025 15:27:17,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:27:17,System.Runtime,GC Fragmentation (%),Metric,29.605744293428465
05/08/2025 15:27:17,System.Runtime,GC Committed Bytes (MB),Metric,158.728192
05/08/2025 15:27:17,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:27:17,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:27:17,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:27:17,System.Runtime,Gen 0 Size (B),Metric,54816808
05/08/2025 15:27:17,System.Runtime,Gen 1 Size (B),Metric,485888
05/08/2025 15:27:17,System.Runtime,Gen 2 Size (B),Metric,3829696
05/08/2025 15:27:17,System.Runtime,LOH Size (B),Metric,66394304
05/08/2025 15:27:17,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:27:17,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:27:17,System.Runtime,IL Bytes Jitted (B),Metric,1032386
05/08/2025 15:27:17,System.Runtime,Number of Methods Jitted,Metric,12999
05/08/2025 15:27:17,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,3.261099999999715
05/08/2025 15:27:18,System.Runtime,CPU Usage (%),Metric,11.71875
05/08/2025 15:27:18,System.Runtime,Working Set (MB),Metric,376.528896
05/08/2025 15:27:18,System.Runtime,GC Heap Size (MB),Metric,93.586432
05/08/2025 15:27:18,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:18,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:18,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:18,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:27:18,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:27:18,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:27:18,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:27:18,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:27:18,System.Runtime,Allocation Rate (B / 1 sec),Rate,27528
05/08/2025 15:27:18,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:27:18,System.Runtime,GC Fragmentation (%),Metric,29.605744293428465
05/08/2025 15:27:18,System.Runtime,GC Committed Bytes (MB),Metric,158.728192
05/08/2025 15:27:18,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:27:18,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:27:18,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:27:18,System.Runtime,Gen 0 Size (B),Metric,54816808
05/08/2025 15:27:18,System.Runtime,Gen 1 Size (B),Metric,485888
05/08/2025 15:27:18,System.Runtime,Gen 2 Size (B),Metric,3829696
05/08/2025 15:27:18,System.Runtime,LOH Size (B),Metric,66394304
05/08/2025 15:27:18,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:27:18,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:27:18,System.Runtime,IL Bytes Jitted (B),Metric,1032386
05/08/2025 15:27:18,System.Runtime,Number of Methods Jitted,Metric,12999
05/08/2025 15:27:18,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,0
05/08/2025 15:27:19,System.Runtime,CPU Usage (%),Metric,7.421875
05/08/2025 15:27:19,System.Runtime,Working Set (MB),Metric,349.351936
05/08/2025 15:27:19,System.Runtime,GC Heap Size (MB),Metric,95.706496
05/08/2025 15:27:19,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:19,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:19,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:19,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:27:19,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:27:19,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:27:19,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:27:19,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,42
05/08/2025 15:27:19,System.Runtime,Allocation Rate (B / 1 sec),Rate,2119896
05/08/2025 15:27:19,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:27:19,System.Runtime,GC Fragmentation (%),Metric,29.605744293428465
05/08/2025 15:27:19,System.Runtime,GC Committed Bytes (MB),Metric,158.728192
05/08/2025 15:27:19,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:27:19,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:27:19,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:27:19,System.Runtime,Gen 0 Size (B),Metric,54816808
05/08/2025 15:27:19,System.Runtime,Gen 1 Size (B),Metric,485888
05/08/2025 15:27:19,System.Runtime,Gen 2 Size (B),Metric,3829696
05/08/2025 15:27:19,System.Runtime,LOH Size (B),Metric,66394304
05/08/2025 15:27:19,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:27:19,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:27:19,System.Runtime,IL Bytes Jitted (B),Metric,1036282
05/08/2025 15:27:19,System.Runtime,Number of Methods Jitted,Metric,13067
05/08/2025 15:27:19,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,46.83050000000003
05/08/2025 15:27:20,System.Runtime,CPU Usage (%),Metric,1.7578125
05/08/2025 15:27:20,System.Runtime,Working Set (MB),Metric,350.486528
05/08/2025 15:27:20,System.Runtime,GC Heap Size (MB),Metric,96.935584
05/08/2025 15:27:20,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:20,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:20,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:20,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:27:20,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:27:20,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:27:20,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:27:20,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,3
05/08/2025 15:27:20,System.Runtime,Allocation Rate (B / 1 sec),Rate,1234120
05/08/2025 15:27:20,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:27:20,System.Runtime,GC Fragmentation (%),Metric,29.605744293428465
05/08/2025 15:27:20,System.Runtime,GC Committed Bytes (MB),Metric,158.728192
05/08/2025 15:27:20,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:27:20,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:27:20,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:27:20,System.Runtime,Gen 0 Size (B),Metric,54816808
05/08/2025 15:27:20,System.Runtime,Gen 1 Size (B),Metric,485888
05/08/2025 15:27:20,System.Runtime,Gen 2 Size (B),Metric,3829696
05/08/2025 15:27:20,System.Runtime,LOH Size (B),Metric,66394304
05/08/2025 15:27:20,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:27:20,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:27:20,System.Runtime,IL Bytes Jitted (B),Metric,1046652
05/08/2025 15:27:20,System.Runtime,Number of Methods Jitted,Metric,13127
05/08/2025 15:27:20,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,59.47109999999975
05/08/2025 15:27:21,System.Runtime,CPU Usage (%),Metric,1.6
05/08/2025 15:27:21,System.Runtime,Working Set (MB),Metric,350.580736
05/08/2025 15:27:21,System.Runtime,GC Heap Size (MB),Metric,96.95224
05/08/2025 15:27:21,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:21,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:21,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:21,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:27:21,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:27:21,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:27:21,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:27:21,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:27:21,System.Runtime,Allocation Rate (B / 1 sec),Rate,8480
05/08/2025 15:27:21,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:27:21,System.Runtime,GC Fragmentation (%),Metric,29.605744293428465
05/08/2025 15:27:21,System.Runtime,GC Committed Bytes (MB),Metric,158.728192
05/08/2025 15:27:21,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:27:21,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:27:21,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:27:21,System.Runtime,Gen 0 Size (B),Metric,54816808
05/08/2025 15:27:21,System.Runtime,Gen 1 Size (B),Metric,485888
05/08/2025 15:27:21,System.Runtime,Gen 2 Size (B),Metric,3829696
05/08/2025 15:27:21,System.Runtime,LOH Size (B),Metric,66394304
05/08/2025 15:27:21,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:27:21,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:27:21,System.Runtime,IL Bytes Jitted (B),Metric,1049240
05/08/2025 15:27:21,System.Runtime,Number of Methods Jitted,Metric,13142
05/08/2025 15:27:21,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,21.75709999999981
05/08/2025 15:27:22,System.Runtime,CPU Usage (%),Metric,2.3166023166023164
05/08/2025 15:27:22,System.Runtime,Working Set (MB),Metric,337.424384
05/08/2025 15:27:22,System.Runtime,GC Heap Size (MB),Metric,70.84884
05/08/2025 15:27:22,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,1
05/08/2025 15:27:22,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:22,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:22,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:27:22,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:27:22,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:27:22,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:27:22,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:27:22,System.Runtime,Allocation Rate (B / 1 sec),Rate,17797328
05/08/2025 15:27:22,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:27:22,System.Runtime,GC Fragmentation (%),Metric,23.407623344658774
05/08/2025 15:27:22,System.Runtime,GC Committed Bytes (MB),Metric,143.876096
05/08/2025 15:27:22,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:27:22,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:27:22,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0.9429999999999978
05/08/2025 15:27:22,System.Runtime,Gen 0 Size (B),Metric,183672
05/08/2025 15:27:22,System.Runtime,Gen 1 Size (B),Metric,667992
05/08/2025 15:27:22,System.Runtime,Gen 2 Size (B),Metric,3829696
05/08/2025 15:27:22,System.Runtime,LOH Size (B),Metric,69812336
05/08/2025 15:27:22,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:27:22,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:27:22,System.Runtime,IL Bytes Jitted (B),Metric,1049963
05/08/2025 15:27:22,System.Runtime,Number of Methods Jitted,Metric,13146
05/08/2025 15:27:22,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,6.879300000000512
05/08/2025 15:27:23,System.Runtime,CPU Usage (%),Metric,11.787819253438114
05/08/2025 15:27:23,System.Runtime,Working Set (MB),Metric,397.279232
05/08/2025 15:27:23,System.Runtime,GC Heap Size (MB),Metric,160.439368
05/08/2025 15:27:23,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:23,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:23,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:23,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:27:23,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:27:23,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:27:23,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:27:23,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:27:23,System.Runtime,Allocation Rate (B / 1 sec),Rate,89495824
05/08/2025 15:27:23,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:27:23,System.Runtime,GC Fragmentation (%),Metric,23.407623344658774
05/08/2025 15:27:23,System.Runtime,GC Committed Bytes (MB),Metric,143.876096
05/08/2025 15:27:23,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:27:23,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:27:23,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:27:23,System.Runtime,Gen 0 Size (B),Metric,183672
05/08/2025 15:27:23,System.Runtime,Gen 1 Size (B),Metric,667992
05/08/2025 15:27:23,System.Runtime,Gen 2 Size (B),Metric,3829696
05/08/2025 15:27:23,System.Runtime,LOH Size (B),Metric,69812336
05/08/2025 15:27:23,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:27:23,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:27:23,System.Runtime,IL Bytes Jitted (B),Metric,1056972
05/08/2025 15:27:23,System.Runtime,Number of Methods Jitted,Metric,13200
05/08/2025 15:27:23,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,71.14919999999984
05/08/2025 15:27:24,System.Runtime,CPU Usage (%),Metric,12.307692307692308
05/08/2025 15:27:24,System.Runtime,Working Set (MB),Metric,411.131904
05/08/2025 15:27:24,System.Runtime,GC Heap Size (MB),Metric,163.101152
05/08/2025 15:27:24,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:24,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:24,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:24,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:27:24,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:27:24,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:27:24,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:27:24,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:27:24,System.Runtime,Allocation Rate (B / 1 sec),Rate,2661760
05/08/2025 15:27:24,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:27:24,System.Runtime,GC Fragmentation (%),Metric,23.407623344658774
05/08/2025 15:27:24,System.Runtime,GC Committed Bytes (MB),Metric,143.876096
05/08/2025 15:27:24,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:27:24,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:27:24,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:27:24,System.Runtime,Gen 0 Size (B),Metric,183672
05/08/2025 15:27:24,System.Runtime,Gen 1 Size (B),Metric,667992
05/08/2025 15:27:24,System.Runtime,Gen 2 Size (B),Metric,3829696
05/08/2025 15:27:24,System.Runtime,LOH Size (B),Metric,69812336
05/08/2025 15:27:24,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:27:24,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:27:24,System.Runtime,IL Bytes Jitted (B),Metric,1057313
05/08/2025 15:27:24,System.Runtime,Number of Methods Jitted,Metric,13205
05/08/2025 15:27:24,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,4.496399999999994
05/08/2025 15:27:25,System.Runtime,CPU Usage (%),Metric,12.085769980506823
05/08/2025 15:27:25,System.Runtime,Working Set (MB),Metric,398.880768
05/08/2025 15:27:25,System.Runtime,GC Heap Size (MB),Metric,165.230952
05/08/2025 15:27:25,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:25,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:25,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:25,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:27:25,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:27:25,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:27:25,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:27:25,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,39
05/08/2025 15:27:25,System.Runtime,Allocation Rate (B / 1 sec),Rate,2129656
05/08/2025 15:27:25,System.Runtime,Number of Active Timers,Metric,5
05/08/2025 15:27:25,System.Runtime,GC Fragmentation (%),Metric,23.407623344658774
05/08/2025 15:27:25,System.Runtime,GC Committed Bytes (MB),Metric,143.876096
05/08/2025 15:27:25,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:27:25,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:27:25,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:27:25,System.Runtime,Gen 0 Size (B),Metric,183672
05/08/2025 15:27:25,System.Runtime,Gen 1 Size (B),Metric,667992
05/08/2025 15:27:25,System.Runtime,Gen 2 Size (B),Metric,3829696
05/08/2025 15:27:25,System.Runtime,LOH Size (B),Metric,69812336
05/08/2025 15:27:25,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:27:25,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:27:25,System.Runtime,IL Bytes Jitted (B),Metric,1058610
05/08/2025 15:27:25,System.Runtime,Number of Methods Jitted,Metric,13218
05/08/2025 15:27:25,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,18.50460000000021
05/08/2025 15:27:26,System.Runtime,CPU Usage (%),Metric,0.5952380952380952
05/08/2025 15:27:26,System.Runtime,Working Set (MB),Metric,398.88896
05/08/2025 15:27:26,System.Runtime,GC Heap Size (MB),Metric,165.230952
05/08/2025 15:27:26,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:26,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:26,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:26,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:27:26,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:27:26,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:27:26,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:27:26,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,3
05/08/2025 15:27:26,System.Runtime,Allocation Rate (B / 1 sec),Rate,8200
05/08/2025 15:27:26,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:27:26,System.Runtime,GC Fragmentation (%),Metric,23.407623344658774
05/08/2025 15:27:26,System.Runtime,GC Committed Bytes (MB),Metric,143.876096
05/08/2025 15:27:26,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:27:26,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:27:26,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:27:26,System.Runtime,Gen 0 Size (B),Metric,183672
05/08/2025 15:27:26,System.Runtime,Gen 1 Size (B),Metric,667992
05/08/2025 15:27:26,System.Runtime,Gen 2 Size (B),Metric,3829696
05/08/2025 15:27:26,System.Runtime,LOH Size (B),Metric,69812336
05/08/2025 15:27:26,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:27:26,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:27:26,System.Runtime,IL Bytes Jitted (B),Metric,1059336
05/08/2025 15:27:26,System.Runtime,Number of Methods Jitted,Metric,13230
05/08/2025 15:27:26,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,8.002999999999702
05/08/2025 15:27:27,System.Runtime,CPU Usage (%),Metric,2.5390625
05/08/2025 15:27:27,System.Runtime,Working Set (MB),Metric,399.323136
05/08/2025 15:27:27,System.Runtime,GC Heap Size (MB),Metric,166.420648
05/08/2025 15:27:27,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:27,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:27,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:27,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:27:27,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:27:27,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:27:27,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:27:27,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,3
05/08/2025 15:27:27,System.Runtime,Allocation Rate (B / 1 sec),Rate,1178304
05/08/2025 15:27:27,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:27:27,System.Runtime,GC Fragmentation (%),Metric,23.407623344658774
05/08/2025 15:27:27,System.Runtime,GC Committed Bytes (MB),Metric,143.876096
05/08/2025 15:27:27,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:27:27,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:27:27,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:27:27,System.Runtime,Gen 0 Size (B),Metric,183672
05/08/2025 15:27:27,System.Runtime,Gen 1 Size (B),Metric,667992
05/08/2025 15:27:27,System.Runtime,Gen 2 Size (B),Metric,3829696
05/08/2025 15:27:27,System.Runtime,LOH Size (B),Metric,69812336
05/08/2025 15:27:27,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:27:27,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:27:27,System.Runtime,IL Bytes Jitted (B),Metric,1063490
05/08/2025 15:27:27,System.Runtime,Number of Methods Jitted,Metric,13246
05/08/2025 15:27:27,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,29.10990000000038
05/08/2025 15:27:28,System.Runtime,CPU Usage (%),Metric,0
05/08/2025 15:27:28,System.Runtime,Working Set (MB),Metric,393.961472
05/08/2025 15:27:28,System.Runtime,GC Heap Size (MB),Metric,166.428872
05/08/2025 15:27:28,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:28,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:28,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:28,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:27:28,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:27:28,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:27:28,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:27:28,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,1
05/08/2025 15:27:28,System.Runtime,Allocation Rate (B / 1 sec),Rate,8200
05/08/2025 15:27:28,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:27:28,System.Runtime,GC Fragmentation (%),Metric,23.407623344658774
05/08/2025 15:27:28,System.Runtime,GC Committed Bytes (MB),Metric,143.876096
05/08/2025 15:27:28,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:27:28,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:27:28,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:27:28,System.Runtime,Gen 0 Size (B),Metric,183672
05/08/2025 15:27:28,System.Runtime,Gen 1 Size (B),Metric,667992
05/08/2025 15:27:28,System.Runtime,Gen 2 Size (B),Metric,3829696
05/08/2025 15:27:28,System.Runtime,LOH Size (B),Metric,69812336
05/08/2025 15:27:28,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:27:28,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:27:28,System.Runtime,IL Bytes Jitted (B),Metric,1063795
05/08/2025 15:27:28,System.Runtime,Number of Methods Jitted,Metric,13250
05/08/2025 15:27:28,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,1.771099999999933
05/08/2025 15:27:29,System.Runtime,CPU Usage (%),Metric,0.1984126984126984
05/08/2025 15:27:29,System.Runtime,Working Set (MB),Metric,393.981952
05/08/2025 15:27:29,System.Runtime,GC Heap Size (MB),Metric,166.44532
05/08/2025 15:27:29,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:29,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:29,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:29,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:27:29,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:27:29,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:27:29,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:27:29,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:27:29,System.Runtime,Allocation Rate (B / 1 sec),Rate,24600
05/08/2025 15:27:29,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:27:29,System.Runtime,GC Fragmentation (%),Metric,23.407623344658774
05/08/2025 15:27:29,System.Runtime,GC Committed Bytes (MB),Metric,143.876096
05/08/2025 15:27:29,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:27:29,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:27:29,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:27:29,System.Runtime,Gen 0 Size (B),Metric,183672
05/08/2025 15:27:29,System.Runtime,Gen 1 Size (B),Metric,667992
05/08/2025 15:27:29,System.Runtime,Gen 2 Size (B),Metric,3829696
05/08/2025 15:27:29,System.Runtime,LOH Size (B),Metric,69812336
05/08/2025 15:27:29,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:27:29,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:27:29,System.Runtime,IL Bytes Jitted (B),Metric,1063795
05/08/2025 15:27:29,System.Runtime,Number of Methods Jitted,Metric,13250
05/08/2025 15:27:29,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,0
05/08/2025 15:27:30,System.Runtime,CPU Usage (%),Metric,10.898661567877628
05/08/2025 15:27:30,System.Runtime,Working Set (MB),Metric,349.011968
05/08/2025 15:27:30,System.Runtime,GC Heap Size (MB),Metric,87.670296
05/08/2025 15:27:30,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,2
05/08/2025 15:27:30,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,1
05/08/2025 15:27:30,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,1
05/08/2025 15:27:30,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:27:30,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:27:30,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:27:30,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:27:30,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:27:30,System.Runtime,Allocation Rate (B / 1 sec),Rate,105575664
05/08/2025 15:27:30,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:27:30,System.Runtime,GC Fragmentation (%),Metric,15.485045526331183
05/08/2025 15:27:30,System.Runtime,GC Committed Bytes (MB),Metric,91.189248
05/08/2025 15:27:30,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:27:30,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:27:30,System.Runtime,Time paused by GC (ms / 1 sec),Rate,7.853000000000009
05/08/2025 15:27:30,System.Runtime,Gen 0 Size (B),Metric,471752
05/08/2025 15:27:30,System.Runtime,Gen 1 Size (B),Metric,774216
05/08/2025 15:27:30,System.Runtime,Gen 2 Size (B),Metric,3829696
05/08/2025 15:27:30,System.Runtime,LOH Size (B),Metric,1020232
05/08/2025 15:27:30,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:27:30,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:27:30,System.Runtime,IL Bytes Jitted (B),Metric,1067230
05/08/2025 15:27:30,System.Runtime,Number of Methods Jitted,Metric,13286
05/08/2025 15:27:30,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,52.1338999999989
05/08/2025 15:27:31,System.Runtime,CPU Usage (%),Metric,12.25296442687747
05/08/2025 15:27:31,System.Runtime,Working Set (MB),Metric,366.1824
05/08/2025 15:27:31,System.Runtime,GC Heap Size (MB),Metric,91.93444
05/08/2025 15:27:31,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:31,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:31,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:31,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:27:31,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:27:31,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:27:31,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:27:31,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:27:31,System.Runtime,Allocation Rate (B / 1 sec),Rate,4264048
05/08/2025 15:27:31,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:27:31,System.Runtime,GC Fragmentation (%),Metric,15.485045526331183
05/08/2025 15:27:31,System.Runtime,GC Committed Bytes (MB),Metric,91.189248
05/08/2025 15:27:31,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:27:31,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:27:31,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:27:31,System.Runtime,Gen 0 Size (B),Metric,471752
05/08/2025 15:27:31,System.Runtime,Gen 1 Size (B),Metric,774216
05/08/2025 15:27:31,System.Runtime,Gen 2 Size (B),Metric,3829696
05/08/2025 15:27:31,System.Runtime,LOH Size (B),Metric,1020232
05/08/2025 15:27:31,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:27:31,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:27:31,System.Runtime,IL Bytes Jitted (B),Metric,1067390
05/08/2025 15:27:31,System.Runtime,Number of Methods Jitted,Metric,13289
05/08/2025 15:27:31,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,1.924300000000585
05/08/2025 15:27:32,System.Runtime,CPU Usage (%),Metric,12.307692307692308
05/08/2025 15:27:32,System.Runtime,Working Set (MB),Metric,379.86304
05/08/2025 15:27:32,System.Runtime,GC Heap Size (MB),Metric,92.984976
05/08/2025 15:27:32,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:32,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:32,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:32,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:27:32,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:27:32,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:27:32,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:27:32,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:27:32,System.Runtime,Allocation Rate (B / 1 sec),Rate,1050488
05/08/2025 15:27:32,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:27:32,System.Runtime,GC Fragmentation (%),Metric,15.485045526331183
05/08/2025 15:27:32,System.Runtime,GC Committed Bytes (MB),Metric,91.189248
05/08/2025 15:27:32,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:27:32,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:27:32,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:27:32,System.Runtime,Gen 0 Size (B),Metric,471752
05/08/2025 15:27:32,System.Runtime,Gen 1 Size (B),Metric,774216
05/08/2025 15:27:32,System.Runtime,Gen 2 Size (B),Metric,3829696
05/08/2025 15:27:32,System.Runtime,LOH Size (B),Metric,1020232
05/08/2025 15:27:32,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:27:32,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:27:32,System.Runtime,IL Bytes Jitted (B),Metric,1067390
05/08/2025 15:27:32,System.Runtime,Number of Methods Jitted,Metric,13289
05/08/2025 15:27:32,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,0
05/08/2025 15:27:33,System.Runtime,CPU Usage (%),Metric,3.5643564356435644
05/08/2025 15:27:33,System.Runtime,Working Set (MB),Metric,353.304576
05/08/2025 15:27:33,System.Runtime,GC Heap Size (MB),Metric,94.081968
05/08/2025 15:27:33,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:33,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:33,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:33,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:27:33,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:27:33,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:27:33,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:27:33,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,42
05/08/2025 15:27:33,System.Runtime,Allocation Rate (B / 1 sec),Rate,1096896
05/08/2025 15:27:33,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:27:33,System.Runtime,GC Fragmentation (%),Metric,15.485045526331183
05/08/2025 15:27:33,System.Runtime,GC Committed Bytes (MB),Metric,91.189248
05/08/2025 15:27:33,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:27:33,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:27:33,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:27:33,System.Runtime,Gen 0 Size (B),Metric,471752
05/08/2025 15:27:33,System.Runtime,Gen 1 Size (B),Metric,774216
05/08/2025 15:27:33,System.Runtime,Gen 2 Size (B),Metric,3829696
05/08/2025 15:27:33,System.Runtime,LOH Size (B),Metric,1020232
05/08/2025 15:27:33,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:27:33,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:27:33,System.Runtime,IL Bytes Jitted (B),Metric,1069456
05/08/2025 15:27:33,System.Runtime,Number of Methods Jitted,Metric,13338
05/08/2025 15:27:33,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,19.496900000000096
05/08/2025 15:27:34,System.Runtime,CPU Usage (%),Metric,2.8901734104046244
05/08/2025 15:27:34,System.Runtime,Working Set (MB),Metric,354.443264
05/08/2025 15:27:34,System.Runtime,GC Heap Size (MB),Metric,95.265136
05/08/2025 15:27:34,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:34,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:34,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:34,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:27:34,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:27:34,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:27:34,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:27:34,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,3
05/08/2025 15:27:34,System.Runtime,Allocation Rate (B / 1 sec),Rate,1188104
05/08/2025 15:27:34,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:27:34,System.Runtime,GC Fragmentation (%),Metric,15.485045526331183
05/08/2025 15:27:34,System.Runtime,GC Committed Bytes (MB),Metric,91.189248
05/08/2025 15:27:34,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:27:34,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:27:34,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:27:34,System.Runtime,Gen 0 Size (B),Metric,471752
05/08/2025 15:27:34,System.Runtime,Gen 1 Size (B),Metric,774216
05/08/2025 15:27:34,System.Runtime,Gen 2 Size (B),Metric,3829696
05/08/2025 15:27:34,System.Runtime,LOH Size (B),Metric,1020232
05/08/2025 15:27:34,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:27:34,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:27:34,System.Runtime,IL Bytes Jitted (B),Metric,1081356
05/08/2025 15:27:34,System.Runtime,Number of Methods Jitted,Metric,13435
05/08/2025 15:27:34,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,152.04370000000017
05/08/2025 15:27:35,System.Runtime,CPU Usage (%),Metric,0.3937007874015748
05/08/2025 15:27:35,System.Runtime,Working Set (MB),Metric,354.44736
05/08/2025 15:27:35,System.Runtime,GC Heap Size (MB),Metric,95.273288
05/08/2025 15:27:35,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:35,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:35,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:35,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:27:35,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:27:35,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:27:35,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:27:35,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:27:35,System.Runtime,Allocation Rate (B / 1 sec),Rate,0
05/08/2025 15:27:35,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:27:35,System.Runtime,GC Fragmentation (%),Metric,15.485045526331183
05/08/2025 15:27:35,System.Runtime,GC Committed Bytes (MB),Metric,91.189248
05/08/2025 15:27:35,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:27:35,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:27:35,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:27:35,System.Runtime,Gen 0 Size (B),Metric,471752
05/08/2025 15:27:35,System.Runtime,Gen 1 Size (B),Metric,774216
05/08/2025 15:27:35,System.Runtime,Gen 2 Size (B),Metric,3829696
05/08/2025 15:27:35,System.Runtime,LOH Size (B),Metric,1020232
05/08/2025 15:27:35,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:27:35,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:27:35,System.Runtime,IL Bytes Jitted (B),Metric,1081356
05/08/2025 15:27:35,System.Runtime,Number of Methods Jitted,Metric,13435
05/08/2025 15:27:35,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,0
05/08/2025 15:27:36,System.Runtime,CPU Usage (%),Metric,3.3009708737864076
05/08/2025 15:27:36,System.Runtime,Working Set (MB),Metric,355.065856
05/08/2025 15:27:36,System.Runtime,GC Heap Size (MB),Metric,118.025296
05/08/2025 15:27:36,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:36,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:36,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:36,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:27:36,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:27:36,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:27:36,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:27:36,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:27:36,System.Runtime,Allocation Rate (B / 1 sec),Rate,22690408
05/08/2025 15:27:36,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:27:36,System.Runtime,GC Fragmentation (%),Metric,15.485045526331183
05/08/2025 15:27:36,System.Runtime,GC Committed Bytes (MB),Metric,91.189248
05/08/2025 15:27:36,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:27:36,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:27:36,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:27:36,System.Runtime,Gen 0 Size (B),Metric,471752
05/08/2025 15:27:36,System.Runtime,Gen 1 Size (B),Metric,774216
05/08/2025 15:27:36,System.Runtime,Gen 2 Size (B),Metric,3829696
05/08/2025 15:27:36,System.Runtime,LOH Size (B),Metric,1020232
05/08/2025 15:27:36,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:27:36,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:27:36,System.Runtime,IL Bytes Jitted (B),Metric,1084535
05/08/2025 15:27:36,System.Runtime,Number of Methods Jitted,Metric,13456
05/08/2025 15:27:36,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,26.935100000000602
05/08/2025 15:27:37,System.Runtime,CPU Usage (%),Metric,14.814814814814815
05/08/2025 15:27:37,System.Runtime,Working Set (MB),Metric,352.444416
05/08/2025 15:27:37,System.Runtime,GC Heap Size (MB),Metric,75.070424
05/08/2025 15:27:37,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,2
05/08/2025 15:27:37,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,1
05/08/2025 15:27:37,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,1
05/08/2025 15:27:37,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:27:37,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:27:37,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:27:37,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:27:37,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,2
05/08/2025 15:27:37,System.Runtime,Allocation Rate (B / 1 sec),Rate,87263512
05/08/2025 15:27:37,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:27:37,System.Runtime,GC Fragmentation (%),Metric,21.095858147011786
05/08/2025 15:27:37,System.Runtime,GC Committed Bytes (MB),Metric,77.746176
05/08/2025 15:27:37,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:27:37,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:27:37,System.Runtime,Time paused by GC (ms / 1 sec),Rate,9.313999999999993
05/08/2025 15:27:37,System.Runtime,Gen 0 Size (B),Metric,191896
05/08/2025 15:27:37,System.Runtime,Gen 1 Size (B),Metric,806832
05/08/2025 15:27:37,System.Runtime,Gen 2 Size (B),Metric,3829696
05/08/2025 15:27:37,System.Runtime,LOH Size (B),Metric,1020232
05/08/2025 15:27:37,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:27:37,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:27:37,System.Runtime,IL Bytes Jitted (B),Metric,1090262
05/08/2025 15:27:37,System.Runtime,Number of Methods Jitted,Metric,13579
05/08/2025 15:27:37,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,48.745600000000195
05/08/2025 15:27:38,System.Runtime,CPU Usage (%),Metric,12.32876712328767
05/08/2025 15:27:38,System.Runtime,Working Set (MB),Metric,352.448512
05/08/2025 15:27:38,System.Runtime,GC Heap Size (MB),Metric,75.086872
05/08/2025 15:27:38,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:38,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:38,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:38,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:27:38,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:27:38,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:27:38,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:27:38,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:27:38,System.Runtime,Allocation Rate (B / 1 sec),Rate,16400
05/08/2025 15:27:38,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:27:38,System.Runtime,GC Fragmentation (%),Metric,21.095858147011786
05/08/2025 15:27:38,System.Runtime,GC Committed Bytes (MB),Metric,77.746176
05/08/2025 15:27:38,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:27:38,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:27:38,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:27:38,System.Runtime,Gen 0 Size (B),Metric,191896
05/08/2025 15:27:38,System.Runtime,Gen 1 Size (B),Metric,806832
05/08/2025 15:27:38,System.Runtime,Gen 2 Size (B),Metric,3829696
05/08/2025 15:27:38,System.Runtime,LOH Size (B),Metric,1020232
05/08/2025 15:27:38,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:27:38,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:27:38,System.Runtime,IL Bytes Jitted (B),Metric,1090262
05/08/2025 15:27:38,System.Runtime,Number of Methods Jitted,Metric,13579
05/08/2025 15:27:38,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,0
05/08/2025 15:27:39,System.Runtime,CPU Usage (%),Metric,12.085769980506823
05/08/2025 15:27:39,System.Runtime,Working Set (MB),Metric,366.108672
05/08/2025 15:27:39,System.Runtime,GC Heap Size (MB),Metric,76.14128
05/08/2025 15:27:39,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:39,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:39,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:39,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:27:39,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:27:39,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:27:39,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:27:39,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:27:39,System.Runtime,Allocation Rate (B / 1 sec),Rate,1054312
05/08/2025 15:27:39,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:27:39,System.Runtime,GC Fragmentation (%),Metric,21.095858147011786
05/08/2025 15:27:39,System.Runtime,GC Committed Bytes (MB),Metric,77.746176
05/08/2025 15:27:39,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:27:39,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:27:39,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:27:39,System.Runtime,Gen 0 Size (B),Metric,191896
05/08/2025 15:27:39,System.Runtime,Gen 1 Size (B),Metric,806832
05/08/2025 15:27:39,System.Runtime,Gen 2 Size (B),Metric,3829696
05/08/2025 15:27:39,System.Runtime,LOH Size (B),Metric,1020232
05/08/2025 15:27:39,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:27:39,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:27:39,System.Runtime,IL Bytes Jitted (B),Metric,1090262
05/08/2025 15:27:39,System.Runtime,Number of Methods Jitted,Metric,13579
05/08/2025 15:27:39,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,0
05/08/2025 15:27:40,System.Runtime,CPU Usage (%),Metric,2.34375
05/08/2025 15:27:40,System.Runtime,Working Set (MB),Metric,340.590592
05/08/2025 15:27:40,System.Runtime,GC Heap Size (MB),Metric,77.221848
05/08/2025 15:27:40,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:40,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:40,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:40,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:27:40,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:27:40,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:27:40,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:27:40,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,45
05/08/2025 15:27:40,System.Runtime,Allocation Rate (B / 1 sec),Rate,1080496
05/08/2025 15:27:40,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:27:40,System.Runtime,GC Fragmentation (%),Metric,21.095858147011786
05/08/2025 15:27:40,System.Runtime,GC Committed Bytes (MB),Metric,77.746176
05/08/2025 15:27:40,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:27:40,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:27:40,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:27:40,System.Runtime,Gen 0 Size (B),Metric,191896
05/08/2025 15:27:40,System.Runtime,Gen 1 Size (B),Metric,806832
05/08/2025 15:27:40,System.Runtime,Gen 2 Size (B),Metric,3829696
05/08/2025 15:27:40,System.Runtime,LOH Size (B),Metric,1020232
05/08/2025 15:27:40,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:27:40,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:27:40,System.Runtime,IL Bytes Jitted (B),Metric,1090784
05/08/2025 15:27:40,System.Runtime,Number of Methods Jitted,Metric,13600
05/08/2025 15:27:40,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,5.165399999999863
05/08/2025 15:27:41,System.Runtime,CPU Usage (%),Metric,0.1953125
05/08/2025 15:27:41,System.Runtime,Working Set (MB),Metric,340.590592
05/08/2025 15:27:41,System.Runtime,GC Heap Size (MB),Metric,77.221848
05/08/2025 15:27:41,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:41,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:41,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:41,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:27:41,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:27:41,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:27:41,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:27:41,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:27:41,System.Runtime,Allocation Rate (B / 1 sec),Rate,8200
05/08/2025 15:27:41,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:27:41,System.Runtime,GC Fragmentation (%),Metric,21.095858147011786
05/08/2025 15:27:41,System.Runtime,GC Committed Bytes (MB),Metric,77.746176
05/08/2025 15:27:41,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:27:41,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:27:41,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:27:41,System.Runtime,Gen 0 Size (B),Metric,191896
05/08/2025 15:27:41,System.Runtime,Gen 1 Size (B),Metric,806832
05/08/2025 15:27:41,System.Runtime,Gen 2 Size (B),Metric,3829696
05/08/2025 15:27:41,System.Runtime,LOH Size (B),Metric,1020232
05/08/2025 15:27:41,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:27:41,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:27:41,System.Runtime,IL Bytes Jitted (B),Metric,1090784
05/08/2025 15:27:41,System.Runtime,Number of Methods Jitted,Metric,13600
05/08/2025 15:27:41,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,0
05/08/2025 15:27:42,System.Runtime,CPU Usage (%),Metric,0.1953125
05/08/2025 15:27:42,System.Runtime,Working Set (MB),Metric,340.594688
05/08/2025 15:27:42,System.Runtime,GC Heap Size (MB),Metric,77.230072
05/08/2025 15:27:42,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:42,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:42,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:42,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:27:42,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:27:42,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:27:42,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:27:42,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:27:42,System.Runtime,Allocation Rate (B / 1 sec),Rate,0
05/08/2025 15:27:42,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:27:42,System.Runtime,GC Fragmentation (%),Metric,21.095858147011786
05/08/2025 15:27:42,System.Runtime,GC Committed Bytes (MB),Metric,77.746176
05/08/2025 15:27:42,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:27:42,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:27:42,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:27:42,System.Runtime,Gen 0 Size (B),Metric,191896
05/08/2025 15:27:42,System.Runtime,Gen 1 Size (B),Metric,806832
05/08/2025 15:27:42,System.Runtime,Gen 2 Size (B),Metric,3829696
05/08/2025 15:27:42,System.Runtime,LOH Size (B),Metric,1020232
05/08/2025 15:27:42,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:27:42,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:27:42,System.Runtime,IL Bytes Jitted (B),Metric,1090784
05/08/2025 15:27:42,System.Runtime,Number of Methods Jitted,Metric,13600
05/08/2025 15:27:42,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,0
05/08/2025 15:27:43,System.Runtime,CPU Usage (%),Metric,0
05/08/2025 15:27:43,System.Runtime,Working Set (MB),Metric,339.472384
05/08/2025 15:27:43,System.Runtime,GC Heap Size (MB),Metric,77.238296
05/08/2025 15:27:43,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:43,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:43,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:43,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:27:43,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:27:43,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:27:43,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:27:43,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,1
05/08/2025 15:27:43,System.Runtime,Allocation Rate (B / 1 sec),Rate,8200
05/08/2025 15:27:43,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:27:43,System.Runtime,GC Fragmentation (%),Metric,21.095858147011786
05/08/2025 15:27:43,System.Runtime,GC Committed Bytes (MB),Metric,77.746176
05/08/2025 15:27:43,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:27:43,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:27:43,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:27:43,System.Runtime,Gen 0 Size (B),Metric,191896
05/08/2025 15:27:43,System.Runtime,Gen 1 Size (B),Metric,806832
05/08/2025 15:27:43,System.Runtime,Gen 2 Size (B),Metric,3829696
05/08/2025 15:27:43,System.Runtime,LOH Size (B),Metric,1020232
05/08/2025 15:27:43,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:27:43,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:27:43,System.Runtime,IL Bytes Jitted (B),Metric,1090784
05/08/2025 15:27:43,System.Runtime,Number of Methods Jitted,Metric,13600
05/08/2025 15:27:43,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,0
05/08/2025 15:27:44,System.Runtime,CPU Usage (%),Metric,0
05/08/2025 15:27:44,System.Runtime,Working Set (MB),Metric,339.410944
05/08/2025 15:27:44,System.Runtime,GC Heap Size (MB),Metric,77.238296
05/08/2025 15:27:44,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:44,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:44,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:44,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:27:44,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:27:44,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:27:44,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:27:44,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:27:44,System.Runtime,Allocation Rate (B / 1 sec),Rate,8176
05/08/2025 15:27:44,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:27:44,System.Runtime,GC Fragmentation (%),Metric,21.095858147011786
05/08/2025 15:27:44,System.Runtime,GC Committed Bytes (MB),Metric,77.746176
05/08/2025 15:27:44,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:27:44,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:27:44,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:27:44,System.Runtime,Gen 0 Size (B),Metric,191896
05/08/2025 15:27:44,System.Runtime,Gen 1 Size (B),Metric,806832
05/08/2025 15:27:44,System.Runtime,Gen 2 Size (B),Metric,3829696
05/08/2025 15:27:44,System.Runtime,LOH Size (B),Metric,1020232
05/08/2025 15:27:44,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:27:44,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:27:44,System.Runtime,IL Bytes Jitted (B),Metric,1090784
05/08/2025 15:27:44,System.Runtime,Number of Methods Jitted,Metric,13600
05/08/2025 15:27:44,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,0
05/08/2025 15:27:45,System.Runtime,CPU Usage (%),Metric,0
05/08/2025 15:27:45,System.Runtime,Working Set (MB),Metric,339.410944
05/08/2025 15:27:45,System.Runtime,GC Heap Size (MB),Metric,77.246472
05/08/2025 15:27:45,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:45,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:45,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:45,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:27:45,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:27:45,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:27:45,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:27:45,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:27:45,System.Runtime,Allocation Rate (B / 1 sec),Rate,0
05/08/2025 15:27:45,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:27:45,System.Runtime,GC Fragmentation (%),Metric,21.095858147011786
05/08/2025 15:27:45,System.Runtime,GC Committed Bytes (MB),Metric,77.746176
05/08/2025 15:27:45,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:27:45,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:27:45,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:27:45,System.Runtime,Gen 0 Size (B),Metric,191896
05/08/2025 15:27:45,System.Runtime,Gen 1 Size (B),Metric,806832
05/08/2025 15:27:45,System.Runtime,Gen 2 Size (B),Metric,3829696
05/08/2025 15:27:45,System.Runtime,LOH Size (B),Metric,1020232
05/08/2025 15:27:45,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:27:45,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:27:45,System.Runtime,IL Bytes Jitted (B),Metric,1090784
05/08/2025 15:27:45,System.Runtime,Number of Methods Jitted,Metric,13600
05/08/2025 15:27:45,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,0
05/08/2025 15:27:46,System.Runtime,CPU Usage (%),Metric,2.584493041749503
05/08/2025 15:27:46,System.Runtime,Working Set (MB),Metric,339.816448
05/08/2025 15:27:46,System.Runtime,GC Heap Size (MB),Metric,78.440216
05/08/2025 15:27:46,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:46,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:46,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:46,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:27:46,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:27:46,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:27:46,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:27:46,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,3
05/08/2025 15:27:46,System.Runtime,Allocation Rate (B / 1 sec),Rate,1190480
05/08/2025 15:27:46,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:27:46,System.Runtime,GC Fragmentation (%),Metric,21.095858147011786
05/08/2025 15:27:46,System.Runtime,GC Committed Bytes (MB),Metric,77.746176
05/08/2025 15:27:46,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:27:46,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:27:46,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:27:46,System.Runtime,Gen 0 Size (B),Metric,191896
05/08/2025 15:27:46,System.Runtime,Gen 1 Size (B),Metric,806832
05/08/2025 15:27:46,System.Runtime,Gen 2 Size (B),Metric,3829696
05/08/2025 15:27:46,System.Runtime,LOH Size (B),Metric,1020232
05/08/2025 15:27:46,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:27:46,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:27:46,System.Runtime,IL Bytes Jitted (B),Metric,1094015
05/08/2025 15:27:46,System.Runtime,Number of Methods Jitted,Metric,13614
05/08/2025 15:27:46,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,16.995999999999185
05/08/2025 15:27:47,System.Runtime,CPU Usage (%),Metric,0.19193857965451055
05/08/2025 15:27:47,System.Runtime,Working Set (MB),Metric,339.849216
05/08/2025 15:27:47,System.Runtime,GC Heap Size (MB),Metric,78.464856
05/08/2025 15:27:47,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:47,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:47,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:47,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:27:47,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:27:47,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:27:47,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:27:47,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:27:47,System.Runtime,Allocation Rate (B / 1 sec),Rate,17008
05/08/2025 15:27:47,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:27:47,System.Runtime,GC Fragmentation (%),Metric,21.095858147011786
05/08/2025 15:27:47,System.Runtime,GC Committed Bytes (MB),Metric,77.746176
05/08/2025 15:27:47,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:27:47,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:27:47,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:27:47,System.Runtime,Gen 0 Size (B),Metric,191896
05/08/2025 15:27:47,System.Runtime,Gen 1 Size (B),Metric,806832
05/08/2025 15:27:47,System.Runtime,Gen 2 Size (B),Metric,3829696
05/08/2025 15:27:47,System.Runtime,LOH Size (B),Metric,1020232
05/08/2025 15:27:47,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:27:47,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:27:47,System.Runtime,IL Bytes Jitted (B),Metric,1094015
05/08/2025 15:27:47,System.Runtime,Number of Methods Jitted,Metric,13614
05/08/2025 15:27:47,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,0
05/08/2025 15:27:48,System.Runtime,CPU Usage (%),Metric,13.745019920318725
05/08/2025 15:27:48,System.Runtime,Working Set (MB),Metric,336.658432
05/08/2025 15:27:48,System.Runtime,GC Heap Size (MB),Metric,85.894912
05/08/2025 15:27:48,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,1
05/08/2025 15:27:48,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,1
05/08/2025 15:27:48,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,1
05/08/2025 15:27:48,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:27:48,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:27:48,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:27:48,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:27:48,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:27:48,System.Runtime,Allocation Rate (B / 1 sec),Rate,106210184
05/08/2025 15:27:48,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:27:48,System.Runtime,GC Fragmentation (%),Metric,27.15970762538958
05/08/2025 15:27:48,System.Runtime,GC Committed Bytes (MB),Metric,141.983744
05/08/2025 15:27:48,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:27:48,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:27:48,System.Runtime,Time paused by GC (ms / 1 sec),Rate,10.164000000000001
05/08/2025 15:27:48,System.Runtime,Gen 0 Size (B),Metric,46067336
05/08/2025 15:27:48,System.Runtime,Gen 1 Size (B),Metric,806832
05/08/2025 15:27:48,System.Runtime,Gen 2 Size (B),Metric,3829696
05/08/2025 15:27:48,System.Runtime,LOH Size (B),Metric,65104168
05/08/2025 15:27:48,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:27:48,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:27:48,System.Runtime,IL Bytes Jitted (B),Metric,1096265
05/08/2025 15:27:48,System.Runtime,Number of Methods Jitted,Metric,13628
05/08/2025 15:27:48,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,18.47270000000026
05/08/2025 15:27:49,System.Runtime,CPU Usage (%),Metric,12.524461839530332
05/08/2025 15:27:49,System.Runtime,Working Set (MB),Metric,353.181696
05/08/2025 15:27:49,System.Runtime,GC Heap Size (MB),Metric,89.625664
05/08/2025 15:27:49,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:49,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:49,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:49,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:27:49,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:27:49,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:27:49,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:27:49,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:27:49,System.Runtime,Allocation Rate (B / 1 sec),Rate,3730680
05/08/2025 15:27:49,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:27:49,System.Runtime,GC Fragmentation (%),Metric,27.15970762538958
05/08/2025 15:27:49,System.Runtime,GC Committed Bytes (MB),Metric,141.983744
05/08/2025 15:27:49,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:27:49,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:27:49,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:27:49,System.Runtime,Gen 0 Size (B),Metric,46067336
05/08/2025 15:27:49,System.Runtime,Gen 1 Size (B),Metric,806832
05/08/2025 15:27:49,System.Runtime,Gen 2 Size (B),Metric,3829696
05/08/2025 15:27:49,System.Runtime,LOH Size (B),Metric,65104168
05/08/2025 15:27:49,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:27:49,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:27:49,System.Runtime,IL Bytes Jitted (B),Metric,1096358
05/08/2025 15:27:49,System.Runtime,Number of Methods Jitted,Metric,13629
05/08/2025 15:27:49,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,0.9306999999989785
05/08/2025 15:27:50,System.Runtime,CPU Usage (%),Metric,12.619502868068833
05/08/2025 15:27:50,System.Runtime,Working Set (MB),Metric,366.7968
05/08/2025 15:27:50,System.Runtime,GC Heap Size (MB),Metric,91.732216
05/08/2025 15:27:50,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:50,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:50,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:50,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:27:50,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:27:50,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:27:50,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:27:50,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:27:50,System.Runtime,Allocation Rate (B / 1 sec),Rate,2106456
05/08/2025 15:27:50,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:27:50,System.Runtime,GC Fragmentation (%),Metric,27.15970762538958
05/08/2025 15:27:50,System.Runtime,GC Committed Bytes (MB),Metric,141.983744
05/08/2025 15:27:50,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:27:50,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:27:50,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:27:50,System.Runtime,Gen 0 Size (B),Metric,46067336
05/08/2025 15:27:50,System.Runtime,Gen 1 Size (B),Metric,806832
05/08/2025 15:27:50,System.Runtime,Gen 2 Size (B),Metric,3829696
05/08/2025 15:27:50,System.Runtime,LOH Size (B),Metric,65104168
05/08/2025 15:27:50,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:27:50,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:27:50,System.Runtime,IL Bytes Jitted (B),Metric,1096358
05/08/2025 15:27:50,System.Runtime,Number of Methods Jitted,Metric,13629
05/08/2025 15:27:50,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,0
05/08/2025 15:27:51,System.Runtime,CPU Usage (%),Metric,1.7857142857142858
05/08/2025 15:27:51,System.Runtime,Working Set (MB),Metric,340.852736
05/08/2025 15:27:51,System.Runtime,GC Heap Size (MB),Metric,91.764496
05/08/2025 15:27:51,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:51,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:51,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:51,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:27:51,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:27:51,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:27:51,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:27:51,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,42
05/08/2025 15:27:51,System.Runtime,Allocation Rate (B / 1 sec),Rate,32208
05/08/2025 15:27:51,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:27:51,System.Runtime,GC Fragmentation (%),Metric,27.15970762538958
05/08/2025 15:27:51,System.Runtime,GC Committed Bytes (MB),Metric,141.983744
05/08/2025 15:27:51,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:27:51,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:27:51,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:27:51,System.Runtime,Gen 0 Size (B),Metric,46067336
05/08/2025 15:27:51,System.Runtime,Gen 1 Size (B),Metric,806832
05/08/2025 15:27:51,System.Runtime,Gen 2 Size (B),Metric,3829696
05/08/2025 15:27:51,System.Runtime,LOH Size (B),Metric,65104168
05/08/2025 15:27:51,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:27:51,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:27:51,System.Runtime,IL Bytes Jitted (B),Metric,1097569
05/08/2025 15:27:51,System.Runtime,Number of Methods Jitted,Metric,13649
05/08/2025 15:27:51,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,23.694800000001123
05/08/2025 15:27:52,System.Runtime,CPU Usage (%),Metric,2.1153846153846154
05/08/2025 15:27:52,System.Runtime,Working Set (MB),Metric,341.147648
05/08/2025 15:27:52,System.Runtime,GC Heap Size (MB),Metric,92.997032
05/08/2025 15:27:52,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:52,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:52,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:52,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:27:52,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:27:52,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:27:52,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:27:52,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,3
05/08/2025 15:27:52,System.Runtime,Allocation Rate (B / 1 sec),Rate,1237488
05/08/2025 15:27:52,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:27:52,System.Runtime,GC Fragmentation (%),Metric,27.15970762538958
05/08/2025 15:27:52,System.Runtime,GC Committed Bytes (MB),Metric,141.983744
05/08/2025 15:27:52,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:27:52,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:27:52,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:27:52,System.Runtime,Gen 0 Size (B),Metric,46067336
05/08/2025 15:27:52,System.Runtime,Gen 1 Size (B),Metric,806832
05/08/2025 15:27:52,System.Runtime,Gen 2 Size (B),Metric,3829696
05/08/2025 15:27:52,System.Runtime,LOH Size (B),Metric,65104168
05/08/2025 15:27:52,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:27:52,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:27:52,System.Runtime,IL Bytes Jitted (B),Metric,1098900
05/08/2025 15:27:52,System.Runtime,Number of Methods Jitted,Metric,13683
05/08/2025 15:27:52,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,30.20310000000063
05/08/2025 15:27:53,System.Runtime,CPU Usage (%),Metric,0.1953125
05/08/2025 15:27:53,System.Runtime,Working Set (MB),Metric,341.151744
05/08/2025 15:27:53,System.Runtime,GC Heap Size (MB),Metric,93.02156
05/08/2025 15:27:53,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:53,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:53,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:53,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:27:53,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:27:53,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:27:53,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:27:53,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:27:53,System.Runtime,Allocation Rate (B / 1 sec),Rate,8800
05/08/2025 15:27:53,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:27:53,System.Runtime,GC Fragmentation (%),Metric,27.15970762538958
05/08/2025 15:27:53,System.Runtime,GC Committed Bytes (MB),Metric,141.983744
05/08/2025 15:27:53,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:27:53,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:27:53,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:27:53,System.Runtime,Gen 0 Size (B),Metric,46067336
05/08/2025 15:27:53,System.Runtime,Gen 1 Size (B),Metric,806832
05/08/2025 15:27:53,System.Runtime,Gen 2 Size (B),Metric,3829696
05/08/2025 15:27:53,System.Runtime,LOH Size (B),Metric,65104168
05/08/2025 15:27:53,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:27:53,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:27:53,System.Runtime,IL Bytes Jitted (B),Metric,1098900
05/08/2025 15:27:53,System.Runtime,Number of Methods Jitted,Metric,13683
05/08/2025 15:27:53,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,0
05/08/2025 15:27:54,System.Runtime,CPU Usage (%),Metric,12.941176470588236
05/08/2025 15:27:54,System.Runtime,Working Set (MB),Metric,398.168064
05/08/2025 15:27:54,System.Runtime,GC Heap Size (MB),Metric,152.582664
05/08/2025 15:27:54,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,1
05/08/2025 15:27:54,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:54,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:54,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:27:54,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:27:54,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:27:54,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:27:54,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:27:54,System.Runtime,Allocation Rate (B / 1 sec),Rate,106228512
05/08/2025 15:27:54,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:27:54,System.Runtime,GC Fragmentation (%),Metric,24.95177952302428
05/08/2025 15:27:54,System.Runtime,GC Committed Bytes (MB),Metric,147.156992
05/08/2025 15:27:54,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:27:54,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:27:54,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0.9869999999999948
05/08/2025 15:27:54,System.Runtime,Gen 0 Size (B),Metric,183672
05/08/2025 15:27:54,System.Runtime,Gen 1 Size (B),Metric,1049264
05/08/2025 15:27:54,System.Runtime,Gen 2 Size (B),Metric,3829696
05/08/2025 15:27:54,System.Runtime,LOH Size (B),Metric,70979336
05/08/2025 15:27:54,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:27:54,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:27:54,System.Runtime,IL Bytes Jitted (B),Metric,1099697
05/08/2025 15:27:54,System.Runtime,Number of Methods Jitted,Metric,13700
05/08/2025 15:27:54,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,13.239399999998568
05/08/2025 15:27:55,System.Runtime,CPU Usage (%),Metric,12.770137524557956
05/08/2025 15:27:55,System.Runtime,Working Set (MB),Metric,414.912512
05/08/2025 15:27:55,System.Runtime,GC Heap Size (MB),Metric,156.304816
05/08/2025 15:27:55,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:55,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:55,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:55,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:27:55,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:27:55,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:27:55,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:27:55,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:27:55,System.Runtime,Allocation Rate (B / 1 sec),Rate,3722104
05/08/2025 15:27:55,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:27:55,System.Runtime,GC Fragmentation (%),Metric,24.95177952302428
05/08/2025 15:27:55,System.Runtime,GC Committed Bytes (MB),Metric,147.156992
05/08/2025 15:27:55,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:27:55,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:27:55,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:27:55,System.Runtime,Gen 0 Size (B),Metric,183672
05/08/2025 15:27:55,System.Runtime,Gen 1 Size (B),Metric,1049264
05/08/2025 15:27:55,System.Runtime,Gen 2 Size (B),Metric,3829696
05/08/2025 15:27:55,System.Runtime,LOH Size (B),Metric,70979336
05/08/2025 15:27:55,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:27:55,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:27:55,System.Runtime,IL Bytes Jitted (B),Metric,1100292
05/08/2025 15:27:55,System.Runtime,Number of Methods Jitted,Metric,13703
05/08/2025 15:27:55,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,4.3145999999997
05/08/2025 15:27:56,System.Runtime,CPU Usage (%),Metric,12.3046875
05/08/2025 15:27:56,System.Runtime,Working Set (MB),Metric,428.847104
05/08/2025 15:27:56,System.Runtime,GC Heap Size (MB),Metric,158.41132
05/08/2025 15:27:56,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:56,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:56,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:56,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:27:56,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:27:56,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:27:56,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:27:56,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:27:56,System.Runtime,Allocation Rate (B / 1 sec),Rate,2106456
05/08/2025 15:27:56,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:27:56,System.Runtime,GC Fragmentation (%),Metric,24.95177952302428
05/08/2025 15:27:56,System.Runtime,GC Committed Bytes (MB),Metric,147.156992
05/08/2025 15:27:56,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:27:56,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:27:56,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:27:56,System.Runtime,Gen 0 Size (B),Metric,183672
05/08/2025 15:27:56,System.Runtime,Gen 1 Size (B),Metric,1049264
05/08/2025 15:27:56,System.Runtime,Gen 2 Size (B),Metric,3829696
05/08/2025 15:27:56,System.Runtime,LOH Size (B),Metric,70979336
05/08/2025 15:27:56,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:27:56,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:27:56,System.Runtime,IL Bytes Jitted (B),Metric,1100292
05/08/2025 15:27:56,System.Runtime,Number of Methods Jitted,Metric,13703
05/08/2025 15:27:56,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,0
05/08/2025 15:27:57,System.Runtime,CPU Usage (%),Metric,1.5533980582524272
05/08/2025 15:27:57,System.Runtime,Working Set (MB),Metric,404.299776
05/08/2025 15:27:57,System.Runtime,GC Heap Size (MB),Metric,158.444216
05/08/2025 15:27:57,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:57,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:57,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:57,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:27:57,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:27:57,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:27:57,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:27:57,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,42
05/08/2025 15:27:57,System.Runtime,Allocation Rate (B / 1 sec),Rate,32800
05/08/2025 15:27:57,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:27:57,System.Runtime,GC Fragmentation (%),Metric,24.95177952302428
05/08/2025 15:27:57,System.Runtime,GC Committed Bytes (MB),Metric,147.156992
05/08/2025 15:27:57,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:27:57,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:27:57,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:27:57,System.Runtime,Gen 0 Size (B),Metric,183672
05/08/2025 15:27:57,System.Runtime,Gen 1 Size (B),Metric,1049264
05/08/2025 15:27:57,System.Runtime,Gen 2 Size (B),Metric,3829696
05/08/2025 15:27:57,System.Runtime,LOH Size (B),Metric,70979336
05/08/2025 15:27:57,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:27:57,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:27:57,System.Runtime,IL Bytes Jitted (B),Metric,1104223
05/08/2025 15:27:57,System.Runtime,Number of Methods Jitted,Metric,13730
05/08/2025 15:27:57,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,77.53950000000077
05/08/2025 15:27:58,System.Runtime,CPU Usage (%),Metric,1.596806387225549
05/08/2025 15:27:58,System.Runtime,Working Set (MB),Metric,404.660224
05/08/2025 15:27:58,System.Runtime,GC Heap Size (MB),Metric,159.641672
05/08/2025 15:27:58,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:58,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:58,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:58,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:27:58,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:27:58,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:27:58,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:27:58,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,4
05/08/2025 15:27:58,System.Runtime,Allocation Rate (B / 1 sec),Rate,1202360
05/08/2025 15:27:58,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:27:58,System.Runtime,GC Fragmentation (%),Metric,24.95177952302428
05/08/2025 15:27:58,System.Runtime,GC Committed Bytes (MB),Metric,147.156992
05/08/2025 15:27:58,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:27:58,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:27:58,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:27:58,System.Runtime,Gen 0 Size (B),Metric,183672
05/08/2025 15:27:58,System.Runtime,Gen 1 Size (B),Metric,1049264
05/08/2025 15:27:58,System.Runtime,Gen 2 Size (B),Metric,3829696
05/08/2025 15:27:58,System.Runtime,LOH Size (B),Metric,70979336
05/08/2025 15:27:58,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:27:58,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:27:58,System.Runtime,IL Bytes Jitted (B),Metric,1104822
05/08/2025 15:27:58,System.Runtime,Number of Methods Jitted,Metric,13743
05/08/2025 15:27:58,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,22.937799999999697
05/08/2025 15:27:59,System.Runtime,CPU Usage (%),Metric,0
05/08/2025 15:27:59,System.Runtime,Working Set (MB),Metric,404.688896
05/08/2025 15:27:59,System.Runtime,GC Heap Size (MB),Metric,159.6662
05/08/2025 15:27:59,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:59,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:59,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:27:59,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:27:59,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:27:59,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:27:59,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:27:59,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:27:59,System.Runtime,Allocation Rate (B / 1 sec),Rate,16384
05/08/2025 15:27:59,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:27:59,System.Runtime,GC Fragmentation (%),Metric,24.95177952302428
05/08/2025 15:27:59,System.Runtime,GC Committed Bytes (MB),Metric,147.156992
05/08/2025 15:27:59,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:27:59,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:27:59,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:27:59,System.Runtime,Gen 0 Size (B),Metric,183672
05/08/2025 15:27:59,System.Runtime,Gen 1 Size (B),Metric,1049264
05/08/2025 15:27:59,System.Runtime,Gen 2 Size (B),Metric,3829696
05/08/2025 15:27:59,System.Runtime,LOH Size (B),Metric,70979336
05/08/2025 15:27:59,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:27:59,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:27:59,System.Runtime,IL Bytes Jitted (B),Metric,1104822
05/08/2025 15:27:59,System.Runtime,Number of Methods Jitted,Metric,13743
05/08/2025 15:27:59,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,0
05/08/2025 15:28:00,System.Runtime,CPU Usage (%),Metric,10
05/08/2025 15:28:00,System.Runtime,Working Set (MB),Metric,351.997952
05/08/2025 15:28:00,System.Runtime,GC Heap Size (MB),Metric,81.883016
05/08/2025 15:28:00,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,2
05/08/2025 15:28:00,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,1
05/08/2025 15:28:00,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,1
05/08/2025 15:28:00,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:28:00,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:28:00,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:28:00,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:28:00,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:28:00,System.Runtime,Allocation Rate (B / 1 sec),Rate,105099576
05/08/2025 15:28:00,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:28:00,System.Runtime,GC Fragmentation (%),Metric,20.98139539234302
05/08/2025 15:28:00,System.Runtime,GC Committed Bytes (MB),Metric,92.008448
05/08/2025 15:28:00,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:28:00,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:28:00,System.Runtime,Time paused by GC (ms / 1 sec),Rate,9.766000000000005
05/08/2025 15:28:00,System.Runtime,Gen 0 Size (B),Metric,364648
05/08/2025 15:28:00,System.Runtime,Gen 1 Size (B),Metric,1158504
05/08/2025 15:28:00,System.Runtime,Gen 2 Size (B),Metric,3829696
05/08/2025 15:28:00,System.Runtime,LOH Size (B),Metric,1020232
05/08/2025 15:28:00,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:28:00,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:28:00,System.Runtime,IL Bytes Jitted (B),Metric,1105447
05/08/2025 15:28:00,System.Runtime,Number of Methods Jitted,Metric,13756
05/08/2025 15:28:00,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,8.934699999999793
05/08/2025 15:28:01,System.Runtime,CPU Usage (%),Metric,12.233009708737864
05/08/2025 15:28:01,System.Runtime,Working Set (MB),Metric,369.33632
05/08/2025 15:28:01,System.Runtime,GC Heap Size (MB),Metric,86.613112
05/08/2025 15:28:01,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:28:01,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:28:01,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:28:01,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:28:01,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:28:01,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:28:01,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:28:01,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:28:01,System.Runtime,Allocation Rate (B / 1 sec),Rate,4729952
05/08/2025 15:28:01,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:28:01,System.Runtime,GC Fragmentation (%),Metric,20.98139539234302
05/08/2025 15:28:01,System.Runtime,GC Committed Bytes (MB),Metric,92.008448
05/08/2025 15:28:01,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:28:01,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:28:01,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:28:01,System.Runtime,Gen 0 Size (B),Metric,364648
05/08/2025 15:28:01,System.Runtime,Gen 1 Size (B),Metric,1158504
05/08/2025 15:28:01,System.Runtime,Gen 2 Size (B),Metric,3829696
05/08/2025 15:28:01,System.Runtime,LOH Size (B),Metric,1020232
05/08/2025 15:28:01,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:28:01,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:28:01,System.Runtime,IL Bytes Jitted (B),Metric,1105447
05/08/2025 15:28:01,System.Runtime,Number of Methods Jitted,Metric,13756
05/08/2025 15:28:01,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,0
05/08/2025 15:28:02,System.Runtime,CPU Usage (%),Metric,12.301587301587302
05/08/2025 15:28:02,System.Runtime,Working Set (MB),Metric,369.33632
05/08/2025 15:28:02,System.Runtime,GC Heap Size (MB),Metric,86.62128
05/08/2025 15:28:02,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:28:02,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:28:02,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:28:02,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:28:02,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:28:02,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:28:02,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:28:02,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:28:02,System.Runtime,Allocation Rate (B / 1 sec),Rate,8168
05/08/2025 15:28:02,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:28:02,System.Runtime,GC Fragmentation (%),Metric,20.98139539234302
05/08/2025 15:28:02,System.Runtime,GC Committed Bytes (MB),Metric,92.008448
05/08/2025 15:28:02,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:28:02,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:28:02,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:28:02,System.Runtime,Gen 0 Size (B),Metric,364648
05/08/2025 15:28:02,System.Runtime,Gen 1 Size (B),Metric,1158504
05/08/2025 15:28:02,System.Runtime,Gen 2 Size (B),Metric,3829696
05/08/2025 15:28:02,System.Runtime,LOH Size (B),Metric,1020232
05/08/2025 15:28:02,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:28:02,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:28:02,System.Runtime,IL Bytes Jitted (B),Metric,1105447
05/08/2025 15:28:02,System.Runtime,Number of Methods Jitted,Metric,13756
05/08/2025 15:28:02,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,0
05/08/2025 15:28:03,System.Runtime,CPU Usage (%),Metric,9.038461538461538
05/08/2025 15:28:03,System.Runtime,Working Set (MB),Metric,354.574336
05/08/2025 15:28:03,System.Runtime,GC Heap Size (MB),Metric,88.760688
05/08/2025 15:28:03,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:28:03,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:28:03,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:28:03,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:28:03,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:28:03,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:28:03,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:28:03,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,42
05/08/2025 15:28:03,System.Runtime,Allocation Rate (B / 1 sec),Rate,2139216
05/08/2025 15:28:03,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:28:03,System.Runtime,GC Fragmentation (%),Metric,20.98139539234302
05/08/2025 15:28:03,System.Runtime,GC Committed Bytes (MB),Metric,92.008448
05/08/2025 15:28:03,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:28:03,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:28:03,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:28:03,System.Runtime,Gen 0 Size (B),Metric,364648
05/08/2025 15:28:03,System.Runtime,Gen 1 Size (B),Metric,1158504
05/08/2025 15:28:03,System.Runtime,Gen 2 Size (B),Metric,3829696
05/08/2025 15:28:03,System.Runtime,LOH Size (B),Metric,1020232
05/08/2025 15:28:03,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:28:03,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:28:03,System.Runtime,IL Bytes Jitted (B),Metric,1106930
05/08/2025 15:28:03,System.Runtime,Number of Methods Jitted,Metric,13780
05/08/2025 15:28:03,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,20.780000000000655
05/08/2025 15:28:04,System.Runtime,CPU Usage (%),Metric,0
05/08/2025 15:28:04,System.Runtime,Working Set (MB),Metric,354.574336
05/08/2025 15:28:04,System.Runtime,GC Heap Size (MB),Metric,88.760688
05/08/2025 15:28:04,System.Runtime,Gen 0 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:28:04,System.Runtime,Gen 1 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:28:04,System.Runtime,Gen 2 GC Count (Count / 1 sec),Rate,0
05/08/2025 15:28:04,System.Runtime,Gen 0 GC Budget (MB),Metric,62
05/08/2025 15:28:04,System.Runtime,ThreadPool Thread Count,Metric,4
05/08/2025 15:28:04,System.Runtime,Monitor Lock Contention Count (Count / 1 sec),Rate,0
05/08/2025 15:28:04,System.Runtime,ThreadPool Queue Length,Metric,0
05/08/2025 15:28:04,System.Runtime,ThreadPool Completed Work Item Count (Count / 1 sec),Rate,0
05/08/2025 15:28:04,System.Runtime,Allocation Rate (B / 1 sec),Rate,8200
05/08/2025 15:28:04,System.Runtime,Number of Active Timers,Metric,4
05/08/2025 15:28:04,System.Runtime,GC Fragmentation (%),Metric,20.98139539234302
05/08/2025 15:28:04,System.Runtime,GC Committed Bytes (MB),Metric,92.008448
05/08/2025 15:28:04,System.Runtime,Exception Count (Count / 1 sec),Rate,0
05/08/2025 15:28:04,System.Runtime,% Time in GC since last GC (%),Metric,0
05/08/2025 15:28:04,System.Runtime,Time paused by GC (ms / 1 sec),Rate,0
05/08/2025 15:28:04,System.Runtime,Gen 0 Size (B),Metric,364648
05/08/2025 15:28:04,System.Runtime,Gen 1 Size (B),Metric,1158504
05/08/2025 15:28:04,System.Runtime,Gen 2 Size (B),Metric,3829696
05/08/2025 15:28:04,System.Runtime,LOH Size (B),Metric,1020232
05/08/2025 15:28:04,System.Runtime,POH (Pinned Object Heap) Size (B),Metric,469096
05/08/2025 15:28:04,System.Runtime,Number of Assemblies Loaded,Metric,153
05/08/2025 15:28:04,System.Runtime,IL Bytes Jitted (B),Metric,1106930
05/08/2025 15:28:04,System.Runtime,Number of Methods Jitted,Metric,13780
05/08/2025 15:28:04,System.Runtime,Time spent in JIT (ms / 1 sec),Rate,0
