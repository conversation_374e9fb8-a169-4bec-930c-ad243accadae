{
  "ConnectionStrings": {
    "Database": "Data Source=localhost;Initial Catalog=TowbookDev;User Id=sa;Password=StrongPassword1234#;Pooling=true;TrustServerCertificate=True;Encrypt=false",
    "Database.Azure": "Data Source=localhost;Initial Catalog=TowbookDev;User Id=sa;Password=StrongPassword1234#;Pooling=true;TrustServerCertificate=True;Encrypt=false",
    "Microsoft.ServiceBus": "Endpoint=sb://towbook-mj-pablo.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=eo/aRu73gRq3N3TpuhZMsUcktQrv3PStrF2cQaua25U=",
    "Microsoft.ServiceBusDev": "Endpoint=sb://towbook-mj-pablo.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=eo/aRu73gRq3N3TpuhZMsUcktQrv3PStrF2cQaua25U="
  },
  "Redis": {
    "Servers": "localhost",
    "Credentials": "",
    "SentinelServiceName": "!disable",
    "ConnectionPoolSize": 1
  },
  "CosmosDb": {
    "Url": "https://127.0.0.1:8081",
    "AuthKey": "C2y6yDjf5/R+ob0N8A7Cgv30VRDJIWEHLM+4QDU5DE2nQ9nDuVTqobD4b8mGGyPMbIZnqyMsEcaGQy67XIw/Jw==",
    "Database": "towbook-dev"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning",
      "IronPdf": "Warning"
    }
  },
  "Loggly": {
    "CustomerToken": "************************************",
    "ApplicationName": "Extric.Towbook.Pdf.Utils",
    "Transport": {
      "EndpointHostname": "logs-01.loggly.com",
      "EndpointPort": 6514,
      "LogTransport": "SyslogSecure"
    },
    "SimpleTagValue": "mojix-dev"
  },
  "AllowedHosts": "*",
  "IronPdf": {
    "LicenseKey": "IRONPDF.TOWBOOK.IRO250303.9958.87115-3C5F8EFAAD-AVLT75TU45SSV-JOSQ53IBG5IW-CAOQLB3BJ65N-DB5DQJBQFMQD-AZBJGZR2F63C-6EEIUF-LASLO4GJFAKWUA-IRONPDF.DOTNET.PROFESSIONAL.10DEV.SAAS.OEM.5YR-PWG4IN.RENEW.SUPPORT.02.MAR.2030",
    "LogFilePath": "Default.log",
    "LoggingMode": "Custom", // None=0, DebugOutputWindow=1, Console=2, Custom=4, File=8, All=15
    "LinuxAndDockerDependenciesAutoConfig": false,
    "EnableUnixSupport": true
  },
  "Authentication": {
    "CookieName": ".XTBAUTHSEC",
    "DecryptionKey": "8A9BE8FD67AF6979E7D20198CFEA50DD3D3799C77AF2B72F",
    "ValidationKey": "C50B3C89CB21F4F1422FF158A5B42D0E8DB8CB5CDA1742572A487D9401E3400267682B202B746511891C1BAF47F8D25C07F6C39A104696DB51F17C529AD3CABE"
  }
}
