using System;

namespace Extric.Towbook.API.Models
{
    public class ImpoundNoteModel
    {
        public int Id { get; set; }
        public string Content { get; set; }
        // Obsolete, but apps refer to it.
        public string Note { get; set; }

        public int AuthorId { get; set; }
        public string AuthorName { get; set; }
        public DateTime CreateDate { get; set; }

        public static ImpoundNoteModel Map(Towbook.Impounds.Note note)
        {
            if (note == null)
                return null;

            return new ImpoundNoteModel()
            {
                Id = note.Id,
                Content = note.Content,
                AuthorId = note.User?.Id ?? 0,
                AuthorName = note.User?.FullName,
                CreateDate = note.CreateDate,
            };
        }
    }
}