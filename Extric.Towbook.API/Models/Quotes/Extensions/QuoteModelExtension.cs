using Extric.Towbook.API.Models.Calls;
using Extric.Towbook.Dispatch;
using Extric.Towbook.Dispatch.CallModels;
using Extric.Towbook.Dispatch.QuoteModels;
using Extric.Towbook.Generated;
using Extric.Towbook.Integration;
using Extric.Towbook.Utility;
using Extric.Towbook.WebShared;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;

namespace Extric.Towbook.API.Models.Quotes.Extensions
{
    public static class QuoteModelExtension
    {
        // MOVED TO PROJECT Extric.Towbook.API.Models
        //public static Collection<QuoteModel> Map(Collection<QuoteModel> inputList)
        //{
            //var o = new Collection<QuoteModel>();

            //if (inputList == null)
            //    return o;

            //foreach (var x in inputList)
            //{
            //    o.Add(Map(x));
            //}

            //return o;
        //}

        public static async Task<Entry> MapToEntry(this QuoteModel x, Entry e = null)
        {
            if (e == null)
            {
                e = new Entry()
                {
                    CompanyId = x.CompanyId,
                    AccountId = x.AccountId
                };
            }

            e.Assets = new Collection<EntryAsset>();

            foreach (var asset in x.Call.Assets)
            {
                e.Assets.Add(await Models.Calls.CallAssetModelExtensions.TranslateAsync(asset));
            }

            var uniqueId = -1000;
            foreach (var ii in x.Call.InvoiceItems)
            {
                var i = await CallInvoiceItemModel.MapAsync(ii, await InvoiceItem.GetByIdAsync(uniqueId++));

                // set taxable by rateItem
                if (i.RateItem != null)
                {
                    if (!x.Call.InvoiceTaxExempt.GetValueOrDefault())
                    {
                        if (i.RateItem.RateItemId == RateItem.BUILTIN_FUEL_SURCHARGE)
                        {
                            var s = await Surcharges.SurchargeRate.GetBySurchargeAsync(Surcharges.Surcharge.SURCHARGE_FUEL, x.CompanyId);
                            if (s != null)
                            {
                                if (s.Taxable)
                                    i.Taxable = true;
                            }
                        }
                        else
                        {
                            i.Taxable = i.RateItem.Taxable;
                        }
                    }
                }
                e.Invoice.InvoiceItems.Add(i);
            }

            e = await new Controllers.CallsController().InternalCalculateMiles(0, e);
            e = await Quotes.QuoteModelExtension.InternalApplyFreeQuantities(e);

            e.Invoice.DispatchEntry = e;
            e.Invoice.ForceRecalculate(false);
            e.Invoice.IsTaxExempt = x.Call.InvoiceTaxExempt ?? e.Invoice.IsTaxExempt;
            e.Invoice.AccountId = x.Call.BillToAccountId == null ? x.AccountId : x.Call.BillToAccountId.Value;

            // Contacts
            foreach (var c in x.Call.Contacts)
                e.Contacts.Add(CallContactModel.Map(c));

            // Attributes
            foreach (var a in x.Call.Attributes)
            {
                if (e.Attributes.ContainsKey(a.AttributeId))
                    e.Attributes[a.AttributeId] = CallAttributeValueModel.Map(a);
                else
                    e.Attributes.Add(a.AttributeId, CallAttributeValueModel.Map(a));
            }

            return e;
        }

        // MOVED TO PROJECT Extric.Towbook.API.Models
        //public static QuoteModel Map(QuoteModel x)
        //{
        //    // add available actions based on current user context
        //    x = GetAvailableActions(x);

        //    Calls.CallModelExtensions.CheckPreventByUserTypeKeyValue(x.Call, "PreventDriversFromViewingNotes", "1", (o) =>
        //    {
        //        o.Notes = null;
        //    });

        //    CheckPreventDriverKeyValue(x, "HideAccountDetailsFromDrivers", "1", (o) => {
        //        if (o.Call.Account != null)
        //        {
        //            o.Call.Account.Address = "";
        //            o.Call.Account.City = "";
        //            o.Call.Account.Email = "";
        //            o.Call.Account.Phone = "";
        //            o.Call.Account.State = "";
        //            o.Call.Account.Zip = "";
        //        }
        //    });

        //    CheckPreventDriverKeyValue(x, "HideAccountDetailsFromDrivers", "2", (o) => {
        //        if (o.Call.Account != null)
        //        {
        //            o.Call.Account.Address = "";
        //            o.Call.Account.City = "";
        //            o.Call.Account.Email = "";
        //            o.Call.Account.Phone = "";
        //            o.Call.Account.State = "";
        //            o.Call.Account.Zip = "";

        //            // show the Type as the Name.
        //            o.Call.Account.Company = ((Towbook.Accounts.AccountType)o.Call.Account.TypeId).ToString();
        //        }
        //    });

        //    CheckPreventDriverKeyValue(x, "HideAccountDetailsFromDrivers", "3", (o) => {
        //        if (o.Call.Account != null)
        //        {
        //            o.Call.Account.Address = "";
        //            o.Call.Account.City = "";
        //            o.Call.Account.Email = "";
        //            o.Call.Account.Phone = "";
        //            o.Call.Account.State = "";
        //            o.Call.Account.Zip = "";
        //            o.Call.Account.Company = "(Hidden)";
        //        }
        //    });

        //    CheckPreventDriverKeyValue(x, "HideAccountDetailsFromDrivers", "4", (o) =>
        //    {
        //        if (x.Call.Account != null)
        //        {
        //            o.Call.Attributes = o.Call.Attributes.Concat(new Collection<CallAttributeValueModel>()
        //            {
        //                new CallAttributeValueModel()
        //                {
        //                     Id = -56,
        //                     AttributeId = AttributeValue.BUILTIN_DISPATCH_ACCOUNT_NAME_READONLY,
        //                     Value = x.Call.Account.Company
        //                }

        //            }).ToArray();

        //            o.Call.Account.Company = "(Not Available)";
        //        }
        //    });

        //    Calls.CallModelExtensions.CheckPreventByUserTypeKeyValue(x.Call, "PreventDriversFromViewingPONumber", "1", (o) =>
        //    {
        //        o.PurchaseOrderNumber = null;
        //        o.Attributes = o.Attributes?.Where(ro => ro.AttributeId != AttributeValue.BUILTIN_ACCOUNT_PURCHASEORDER).ToArray();
        //    });

        //    CheckPreventDriverKeyValue(x, "PreventDriversFromCreatingCalls", "1", (o) =>
        //    {
        //        o.AvailableActions = o.AvailableActions.Where(w => w != "CONVERT").ToArray();
        //    });

        //    CheckPreventDriverKeyValue(x, "PreventDriversFromViewingQuotes", "1", (o) =>
        //    {
        //        o.AvailableActions = o.AvailableActions.Where(w => w != "VIEW").ToArray();
        //    });

        //    CheckPreventDriverKeyValue(x, "PreventDriversFromEmailingCalls", "1", (o) =>
        //    {
        //        o.AvailableActions = o.AvailableActions.Where(w => w != "EMAIL").ToArray();
        //    });

        //    if (x.Call != null)
        //    {
        //        x.Call.BalanceByClass = x.Call.BalanceByClass ?? Array.Empty<ClassBalanceModel>();

        //        x.Call = Calls.CallModelExtensions.BlockInvoicePricingWhenApplicable(x.Call);
        //        x.Call = Calls.CallModelExtensions.BlockContactDetails(x.Call);

        //        if (x.Call.ArrivalETA?.Date == DateTime.MinValue.Date)
        //            x.Call.ArrivalETA = null;

        //        if (string.IsNullOrEmpty(x.Call.TowSource))
        //            x.Call.TowSource = x.Call.Waypoints?.FirstOrDefault(f => f.Title == "Pickup")?.Address;
        //        if (string.IsNullOrEmpty(x.Call.TowDestination))
        //            x.Call.TowDestination = x.Call.Waypoints?.FirstOrDefault(f => f.Title == "Destination")?.Address;
        //    }

        //    return x;
        //}

        // MOVED TO PROJECT Extric.Towbook.API.Models
        //internal static QuoteModel GetAvailableActions(this QuoteModel x)
        //{
        //    var actions = new string[] { "CONVERT", "VIEW", "EMAIL" }.ToList();

        //    if (WebGlobal.CurrentUser.Type == User.TypeEnum.Manager || 
        //        WebGlobal.CurrentUser.Type == User.TypeEnum.SystemAdministrator ||
        //        x.Owner?.Id == WebGlobal.CurrentUser.Id) {
        //        actions.Add("DELETE");
        //        actions.Add("MODIFY");
        //    }

        //    if (WebGlobal.CurrentUser.Type == User.TypeEnum.Dispatcher)
        //    {
        //        actions.Add("MODIFY");
        //    }

        //    if (WebGlobal.CurrentUser.Type == User.TypeEnum.Manager ||
        //        WebGlobal.CurrentUser.Type == User.TypeEnum.SystemAdministrator ||
        //        WebGlobal.CurrentUser.Type == User.TypeEnum.Accountant ||
        //        WebGlobal.CurrentUser.Type == User.TypeEnum.Dispatcher)
        //    {
        //        actions.Add("CREATE");
        //        actions.Add("DUPLICATE");
        //    }

        //    x.AvailableActions = actions.Distinct().OrderBy(a => a).ToArray();

        //    return x;
        //}

        // MOVED TO PROJECT Extric.Towbook.API.Models
        //internal static QuoteModel CheckPreventDriverKeyValue(QuoteModel quote, string keyName, string trueValue, Action<QuoteModel> whenTrue)
        //{
        //    if (quote == null || quote.Call == null || keyName == null || trueValue == null || whenTrue == null)
        //        return quote;

        //    if (WebGlobal.CurrentUser?.Type == User.TypeEnum.Driver)
        //    {
        //        var kv = CompanyKeyValue.GetByCompanyId(quote.CompanyId,
        //            Provider.Towbook.ProviderId, keyName).FirstOrDefault();

        //        if (kv?.Value == trueValue)
        //        {
        //            whenTrue(quote);
        //            return quote;
        //        }
        //    }

        //    return quote;
        //}


        //internal static Entry InternalApplyFreeQuantities(Entry e)
        //{
        //    #region free miles

        //    var bucket = new List<InvoiceItem>();

        //    foreach (var itemToHandle in
        //                e.InvoiceItems.Where(o => o.RateItem?.ParentRateItemId == 0 &&
        //                    (o.CustomName == null || !o.CustomName.Contains("FreeQuantity")) &&
        //                    o.RelatedInvoiceItemId == null))
        //    {
        //        decimal freeQuantity = itemToHandle.RateItem.FreeQuantity;

        //        if (e.Account != null)
        //        {
        //            var ari = Extric.Towbook.Accounts.RateItem.GetByRateItem(e.Account, itemToHandle.RateItem);
        //            if (ari != null)
        //            {
        //                if (ari.FreeQuantity > 0)
        //                    freeQuantity = ari.FreeQuantity;
        //            }
        //        }

        //        var allFreeItems = e.InvoiceItems.Where(o =>
        //            o.RelatedInvoiceItemId == itemToHandle.Id &&
        //            (o.RateItem != null && o.RateItem.ParentRateItemId == 0)
        //        ).ToCollection();

        //        var freeItem = allFreeItems.FirstOrDefault();

        //        if (freeItem == null)
        //        {
        //            if (freeQuantity > 0)
        //            {
        //                /* Don't add free credit for time based rate items (free minutes has been calculated into the quantity for rounding purposes) */
        //                if (itemToHandle.RateItem?.TimeRound > 0)
        //                    continue;

        //                freeItem = new InvoiceItem()
        //                {
        //                    RelatedInvoiceItemId = itemToHandle.Id,
        //                    InvoiceId = 0,
        //                    CustomName = "FreeQuantity Credit " + itemToHandle.RateItem.RateItemId,
        //                    RateItem = itemToHandle.RateItem,
        //                    AssetId = itemToHandle.AssetId,
        //                    ClassId = itemToHandle.ClassId
        //                };
        //            }
        //            else
        //            {
        //                continue;
        //            }
        //        }

        //        freeItem.CustomPrice = -itemToHandle.Price;
        //        // free=5, item=1
        //        if (freeQuantity > itemToHandle.Quantity)
        //            freeItem.Quantity = itemToHandle.Quantity;
        //        else
        //            freeItem.Quantity = freeQuantity;

        //        freeItem.Taxable = itemToHandle.Taxable;


        //        // add or update free invoice item
        //        if (freeItem != null)
        //        {
        //            var fi = e.InvoiceItems.FirstOrDefault(f => f.Id == freeItem.Id);
        //            if (fi == null)
        //                bucket.Add(freeItem);
        //        }
        //    }

        //    foreach (var b in bucket)
        //        e.InvoiceItems.Add(b);
        //    #endregion


        //    #region time based free qty
        //    if (e.Company.HasFeature(Features.TimeBasedRates) && e.InvoiceItems != null)
        //    {
        //        // consider invoice items that have rate items that are time based and have a quantity of zero (not valid)
        //        foreach (var ii in e.InvoiceItems.Where(w => w.RateItem != null &&
        //                                w.RateItem.IsTimeBasedItem() &&
        //                                w.Quantity == 0)
        //                                .ToCollection())
        //        {
        //            RateItem ri = ii.RateItem as RateItem;

        //            // only consider completion end times
        //            if (ri == null || ri.TimeStopAtStatusId != Status.Completed.Id)
        //                continue;

        //            // consider account free quantity
        //            var ari = Towbook.Accounts.RateItem.GetByRateItem(e.Invoice.AccountId ?? e.AccountId, ii.RateItem.RateItemId);
        //            if (ari != null && ari.FreeQuantity > 0)
        //                ri.FreeQuantity = ari.FreeQuantity;

        //            // get qty based on time based settings for rate item
        //            var qty = ri.GetTimeBasedQuantity(e);
        //            if (qty != null)
        //            {
        //                // update quantity for save to entry/invoice/invoiceitems
        //                ii.Quantity = qty.Value;
        //            }

        //            // apply quantity, if zero
        //            if (ii.Quantity == 0)
        //            {
        //                ii.Quantity = RateItem.GetTimedBasedTransformedCost(ii.RateItem as RateItem, e);
        //            }
        //        }
        //    }
        //    #endregion

        //    return e;
        //}

    }

    // MOVED TO PROJECT Extric.Towbook.API.Models
    //class QuoteEventJson
    //{
    //    public int QuoteNumber { get; set; }
    //    public string QuoteName { get; set; }
    //    public DateTime ConvertDate { get; set; }
    //    public int? PerformerId { get; set; }
    //    public string Performer { get; set; }

    //}
}
