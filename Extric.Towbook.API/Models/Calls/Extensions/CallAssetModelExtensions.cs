using Extric.Towbook.Dispatch;
using System;
using Extric.Towbook.Dispatch.CallModels;
using Extric.Towbook.WebShared;

namespace Extric.Towbook.API.Models.Calls
{
    public static class CallAssetModelExtensions
    {
        public static EntryAsset Translate(this CallAssetModel thisAsset, EntryAsset original = null)
        {
            if (original == null)
                original = new EntryAsset();

            if (thisAsset.Id > 0 && original.Id != thisAsset.Id)
                throw new TowbookException("AssetId doesn't match; cannot process.");

            original.Id = thisAsset.Id;

            // don't allow drivers to remove themselves.
            if (thisAsset.Drivers != null && thisAsset.Drivers.Length == 0 && WebGlobal.CurrentUser?.Type == User.TypeEnum.Driver)
                thisAsset.Drivers = null;

            if (thisAsset.Drivers != null)
            {
                if (WebShared.Net5.WebGlobal.CurrentUser?.Type == User.TypeEnum.Driver &&
                    original.Drivers.Count > 1)
                {
                    // do nothing if there are multiple drivers on the call. ignore whatever the app is sending, because it's going to overwrite
                    // the multiple drivers. 
                }
                else
                {
                    var drivers = thisAsset.Drivers.Translate(original);

                    original.Drivers = drivers;
                    original.MarkAsDirty();
                }
            }

            if (thisAsset.Drivable != null)
            {
                original.Drivable = (thisAsset.Drivable == true ? Drivable.Drivable :
                                     thisAsset.Drivable == false ? Drivable.NotDrivable :
                                     Drivable.Unknown);
            }
            else if(thisAsset.DrivableId != null)
            {
                original.Drivable = (Drivable)thisAsset.DrivableId;
            }

            if (thisAsset.Keys != null)
                original.Keys = thisAsset.Keys;

            if (thisAsset.KeysLocation != null)
                original.KeysLocation = thisAsset.KeysLocation;

            if (thisAsset.BodyType != null)
                original.BodyTypeId = thisAsset.BodyType?.Id ?? 0;

            if (original.BodyTypeId == 0)
            {
                // if a body type isn't set, default it to 1. 
                // due to bug in android app.
                original.BodyTypeId = 1;
            }

            if (thisAsset.Color != null)
            {
                if(thisAsset.Color.Id == -1 )
                {
                    original.ColorId = 0;
                }
                else if (thisAsset.Color.Id != 0)
                {
                    original.ColorId = thisAsset.Color?.Id ?? 0;
                }
                else if (thisAsset.Color.Name != null)
                {
                    var tempColorId = Vehicle.VehicleUtility.GetColorIdByName(thisAsset.Color.Name);
                    if (tempColorId > 0)
                        original.ColorId = tempColorId;
                }
            }

            if (thisAsset.Make != null)
                original.Make = thisAsset.Make;

            if (thisAsset.Model != null)
                original.Model = thisAsset.Model;

            if (thisAsset.Odometer != null)
                original.Odometer = thisAsset.Odometer.GetValueOrDefault();

            if (thisAsset.Year != null)
                original.Year = thisAsset.Year.GetValueOrDefault();

            if (thisAsset.LicenseYear != null)
                original.LicenseYear = thisAsset.LicenseYear != null ? Convert.ToInt32(thisAsset.LicenseYear) : 0;

            if (thisAsset.LicenseNumber != null)
                original.LicenseNumber = thisAsset.LicenseNumber;

            if (thisAsset.LicenseState != null)
                original.LicenseState = thisAsset.LicenseState;

            if (thisAsset.Vin != null)
                original.Vin = thisAsset.Vin;

            if (thisAsset.DriveType != null)
                original.DriveType = thisAsset.DriveType;

            if (thisAsset.Notes != null)
                original.Notes = thisAsset.Notes;

            if (thisAsset.UnitNumber != null)
                original.UnitNumber = thisAsset.UnitNumber;

            if (thisAsset.AirbagStatus.HasValue)
            {
                switch (thisAsset.AirbagStatus)
                {
                    case 1:
                        original.AirbagStatus = AirbagStatus.NotDeployed;
                        break;

                    case 2:
                        original.AirbagStatus = AirbagStatus.Deployed;
                        break;

                    default:
                        original.AirbagStatus = AirbagStatus.Unknown;
                        break;
                }
            }

            return original;
        }

    }
}
