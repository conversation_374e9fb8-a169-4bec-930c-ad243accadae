using System;
using System.Collections.Generic;
using System.Linq;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel;
using System.Collections.ObjectModel;
using Extric.Towbook.Licenses;
using Extric.Towbook.Utility;
using Extric.Towbook.WebShared;
using Extric.Towbook.Integration;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.AspNetCore.Html;
using System.Threading.Tasks;


namespace Extric.Towbook.API.Models
{
    public class DriverModel
    {
        public int Id { get; set; }
        private int CompanyId { get; set; }

        [Required]
        public string Name { get; set; }

        public string Address { get; set; }
        public string City { get; set; }
        public string State { get; set; }
        public string Zip { get; set; }

        [DataType(DataType.PhoneNumber)]
        [DisplayName("Work Phone")]
        [RegularExpression(@"^\(?([0-9]{3})\)?[-. ]?([0-9]{3})[-. ]?([0-9]{4})|([0-9]{4}?[-. ]+[0-9]{3}?[-. ]+[0-9]{3})$", ErrorMessage = "Enter a 10 digit work phone number (Example: ************)")]
        public string WorkPhone { get; set; }

        [DataType(DataType.PhoneNumber)]
        [DisplayName("Mobile Phone for Text Messaging")]
        [RegularExpression(@"^\(?([0-9]{3})\)?[-. ]?([0-9]{3})[-. ]?([0-9]{4})|([0-9]{4}?[-. ]+[0-9]{3}?[-. ]+[0-9]{3})$", ErrorMessage = "Enter a 10 digit mobile phone number (Example: ************)")]
        public string MobilePhone { get; set; }

        [DataType(DataType.PhoneNumber)]
        [DisplayName("Home Phone")]
        [RegularExpression(@"^\(?([0-9]{3})\)?[-. ]?([0-9]{3})[-. ]?([0-9]{4})|([0-9]{4}?[-. ]+[0-9]{3}?[-. ]+[0-9]{3})$", ErrorMessage = "Enter a 10 digit home phone number (Example: ************)")]
        public string HomePhone { get; set; }

        [DataType(DataType.EmailAddress)]
        [EmailAddress]
        public string Email { get; set; }
        public DateTime CreateDate { get; set; }

        [DisplayName("License Number")]
        public string LicenseNumber { get; set; }

        [DisplayName("License Class")]
        public string LicenseClass { get; set; }

        [DisplayName("License Expiration Date")]
        public DateTime? LicenseExpirationDate { get; set; }

        [DisplayName("Licensed to Operate Heavy Equipment")]
        public bool OperateHeavyEquipment { get; set; }
        public bool Active { get; set; }
        public bool Deleted { get; set; }

        [DisplayName("Start Date")]
        public DateTime? StartDate { get; set; }

        [DisplayName("Last Day of Employment")]
        public DateTime? EndDate { get; set; }

        public bool IsActive() => EndDate == null || EndDate >= DateTime.Now;

        [DisplayName("Standard Commission Rate")]
        public decimal? CommissionRate { get; set; }

        public DriverDispatchingNotificationType? DispatchingNotificationType { get; set; }
        public string DispatchingNotificationValue { get; set; }

        public int UserId { get; set; }

        public string Notes { get; set; }

        public Collection<LicenseKeyValueModel> Licenses { get; set; }
        public string ScheduleJson { get; set; }

        public HtmlString DriverDetails { get; set; }

        /// <summary>
        /// If set to true, then the schedule is ignored and calls will always be auto-dispatched regardless of scheduleJson.
        /// </summary>
        [DisplayName("Make this driver always available (Ignore Schedule)")]
        public bool ScheduleAlwaysOn { get; set; }

        [DisplayName("Make this driver unavailable (Ignore Schedule)")]
        public bool ScheduleForceOff { get; set; }

        [DisplayName("Don't automatically dispatch drivers to this list of zip codes")]
        public string BlockedZips { get; set; }

        [DisplayName("Driver Rating")]
        public int Rating { get; set; }

        [UsernameExists(ErrorMessage = "Username is already taken.")]
        [StringLength(100, ErrorMessage = "Username must be at least 5 characters.", MinimumLength = 5)]
        public string Username { get; set; }

        [PasswordNotTheSame(ErrorMessage = "Username and Password cannot be the same.")]
        [PasswordNotPassword(ErrorMessage = "Password cannot be 'password'.")]
        [PasswordLength(ErrorMessage = "Password must be at least 8 characters.")]
        public string Password { get; set; }

        public int[] Companies { get; set; }

        [DisplayName("Date of Birth")]
        public DateTime? BirthDate { get; set; }

        [DisplayName("Emergency Contact Name")]
        public string EmergencyContactName { get; set; }

        [DataType(DataType.PhoneNumber)]
        [DisplayName("Emergency Contact Phone")]
        [RegularExpression(@"^\(?([0-9]{3})\)?[-. ]?([0-9]{3})[-. ]?([0-9]{4})|([0-9]{4}?[-. ]+[0-9]{3}?[-. ]+[0-9]{3})$", ErrorMessage = "Enter a 10 digit emergency contact phone number (Example: ************)")]
        public string EmergencyContactPhone { get; set; }

        [DisplayName("Medical Exam Expiration Date")]
        public DateTime? MedicalExamExpirationDate { get; set; }

        public bool HasProfilePhoto { get; set; }

        public static DriverModel Map(Driver input) {
            if (input == null)
                throw new TowbookException("Cannot map null driver object");

            var licenses = DriverLicenseKeyValue.GetByDriverId(input.Id);

            return Map(input, licenses);
        }

        public static DriverModel Map(Driver input, IEnumerable<DriverLicenseKeyValue> licenses)
        {
            if (input == null)
                throw new TowbookException("Cannot map null driver object");

            DriverModel d = new DriverModel();

            var driverLicenseKeyValues = licenses != null
                ? licenses.Where(w => w.DriverId == input.Id).ToCollection()
                : DriverLicenseKeyValue.GetByDriverId(input.Id);

            var driverLicenseKeysFiltered = DriverLicenseKey.GetLicenceKeyData(WebGlobal.CurrentUser.Company);

            d.Id = input.Id;
            d.CompanyId = input.CompanyId;
            d.Name = input.Name;
            d.Address = input.Address;
            d.City = input.City;
            d.State = input.State;
            d.Zip = input.Zip;
            d.WorkPhone = input.WorkPhone;
            d.MobilePhone = input.MobilePhone;
            d.HomePhone = input.HomePhone;
            d.Email = input.Email;
            d.CreateDate = input.CreateDate.ToUniversalTime();
            d.LicenseNumber = input.LicenseNumber;
            d.LicenseClass = input.LicenseClass;
            d.LicenseExpirationDate = input.LicenseExpirationDate;
            d.OperateHeavyEquipment = input.OperateHeavyEquipment;
            d.StartDate = input.StartDate;
            d.EndDate = input.EndDate;
            d.CommissionRate = input.CommissionRate;
            d.DispatchingNotificationValue = input.DispatchingNotificationValue;
            d.DispatchingNotificationType = input.DispatchingNotificationType;
            d.UserId = input.UserId;
            d.Active = input.Active;
            d.Deleted = input.Deleted;

            d.Notes = input.Notes;
            d.Licenses = GetAllMatchedKeyValues(driverLicenseKeysFiltered, driverLicenseKeyValues);
            d.Companies = input.Companies;

            d.BirthDate = input.BirthDate;
            d.EmergencyContactName = input.EmergencyContactName;
            d.EmergencyContactPhone = input.EmergencyContactPhone;
            d.MedicalExamExpirationDate = input.MedicalExamExpirationDate;

            if (d.UserId > 0)
            {
                var user = User.GetById(d.UserId);
                d.HasProfilePhoto = user is not null && user.HasProfilePhoto;
            }

            return d;
        }

        public static async Task<DriverModel> MapAsync(Driver input, IEnumerable<DriverLicenseKeyValue> licenses)
        {
            if (input == null)
                throw new TowbookException("Cannot map null driver object");

            DriverModel d = new DriverModel();

            var driverLicenseKeyValues = licenses != null
                ? licenses.Where(w => w.DriverId == input.Id).ToCollection()
                : await DriverLicenseKeyValue.GetByDriverIdAsync(input.Id); // Make this method async

            var driverLicenseKeysFiltered = await DriverLicenseKey.GetLicenceKeyDataAsync(WebGlobal.CurrentUser.Company); // Use async version

            d.Id = input.Id;
            d.CompanyId = input.CompanyId;
            d.Name = input.Name;
            d.Address = input.Address;
            d.City = input.City;
            d.State = input.State;
            d.Zip = input.Zip;
            d.WorkPhone = input.WorkPhone;
            d.MobilePhone = input.MobilePhone;
            d.HomePhone = input.HomePhone;
            d.Email = input.Email;
            d.CreateDate = input.CreateDate.ToUniversalTime();
            d.LicenseNumber = input.LicenseNumber;
            d.LicenseClass = input.LicenseClass;
            d.LicenseExpirationDate = input.LicenseExpirationDate;
            d.OperateHeavyEquipment = input.OperateHeavyEquipment;
            d.StartDate = input.StartDate;
            d.EndDate = input.EndDate;
            d.CommissionRate = input.CommissionRate;
            d.DispatchingNotificationValue = input.DispatchingNotificationValue;
            d.DispatchingNotificationType = input.DispatchingNotificationType;
            d.UserId = input.UserId;
            d.Active = input.Active;
            d.Deleted = input.Deleted;

            d.Notes = input.Notes;
            d.Licenses = GetAllMatchedKeyValues(driverLicenseKeysFiltered, driverLicenseKeyValues);
            d.Companies = input.Companies;

            d.BirthDate = input.BirthDate;
            d.EmergencyContactName = input.EmergencyContactName;
            d.EmergencyContactPhone = input.EmergencyContactPhone;
            d.MedicalExamExpirationDate = input.MedicalExamExpirationDate;

            if (d.UserId > 0)
            {
                var user = await User.GetByIdAsync(d.UserId); // Make this async if possible
                d.HasProfilePhoto = user is not null && user.HasProfilePhoto;
            }

            return d;
        }


        public static async Task<Driver> MapAsync(DriverModel model, Driver input)
        {
            input.Name = model.Name;
            input.Address = model.Address;
            input.City = model.City;
            input.State = model.State;
            input.Zip = model.Zip;
            input.WorkPhone = model.WorkPhone;

            if (model.DispatchingNotificationValue != null)
                input.DispatchingNotificationValue = model.DispatchingNotificationValue;

            if (model.DispatchingNotificationType != null) 
                input.DispatchingNotificationType = model.DispatchingNotificationType;

            if (input.MobilePhone != model.MobilePhone)
            {
                if (!String.IsNullOrWhiteSpace(model.MobilePhone))
                {
                    var mms = CompanyKeyValue.GetFirstValueOrNull(WebGlobal.CurrentUser.CompanyId, 
                        Provider.Towbook.ProviderId, "FreeTexting_UseMmsForLookups") == "1";

                    var x = await SmsEmailHelper.GetEmailFromPhone(model.MobilePhone, mms);

                    if (x != null)
                    {
                        // turn on text messaging
                        input.DispatchingNotificationType = DriverDispatchingNotificationType.Email;
                        // apply the sms text message value
                        input.DispatchingNotificationValue = x;
                    }
                    else
                    {
                        input.DispatchingNotificationType = DriverDispatchingNotificationType.None;
                        input.DispatchingNotificationValue = null;
                    }
                }
                else
                {
                    input.DispatchingNotificationType = DriverDispatchingNotificationType.None;
                    input.DispatchingNotificationValue = null;
                }
            }

            input.HomePhone = model.HomePhone;
            input.MobilePhone = model.MobilePhone;
            input.Email = model.Email;
            input.LicenseNumber = model.LicenseNumber;
            input.LicenseClass = model.LicenseClass;

            //Prevent from saving with a time of 00:00:00. This will not push the date forward 1 day.
            input.LicenseExpirationDate = model.LicenseExpirationDate?.Date.AddHours(23).AddMinutes(59).AddSeconds(00);
            input.OperateHeavyEquipment = model.OperateHeavyEquipment;
            input.StartDate = model.StartDate;
            input.EndDate = model.EndDate;
            input.Active = model.Active;
            
            input.UserId = model.UserId;
            input.Notes = model.Notes;

            // Update if not null (protect from mobile updates until properties are handled)
            if (model.BirthDate != null)
                input.BirthDate = model.BirthDate;

            if (model.EmergencyContactName != null)
                input.EmergencyContactName = model.EmergencyContactName;

            if (model.EmergencyContactPhone != null)
                input.EmergencyContactPhone = Core.FormatPhone(model.EmergencyContactPhone, true);

            if (model.MedicalExamExpirationDate != null)
                input.MedicalExamExpirationDate = model.MedicalExamExpirationDate;

            return input;
        }

        public static IEnumerable<DriverModel> Map(IEnumerable<Driver> input)
        {
            Collection<DriverModel> output = new Collection<DriverModel>();

            foreach (Driver t in input)
            {
                output.Add(Map(t));
            }

            return output;
        }

        public List<CommissionRateModel> CommissionItems { get; set; }

        public SelectList Users { get; set; }

        public List<CompanyFile> Files { get; set; }
        
        public static Collection<LicenseKeyValueModel> GetAllMatchedKeyValues(Collection<DriverLicenseKey> filteredDriverLicenseKeys, Collection<DriverLicenseKeyValue> allDriverLicenseKeyValues)
        {
            Collection<LicenseKeyValueModel> driverLicenseKeyValues = LicenseKeyValueModel.Map(allDriverLicenseKeyValues);// CompanyLicenseValues.MapDomainObjectToModel(allCompanyLicenseKeyValues);
            Collection<LicenseKeyValueModel> licenseData = new Collection<LicenseKeyValueModel>();

            if (driverLicenseKeyValues.Count() != 0)
            {
                foreach (var clk in filteredDriverLicenseKeys)
                {
                    var license = driverLicenseKeyValues.Where(w => w.KeyId == clk.Id).FirstOrDefault();

                    if (license != null)
                    {
                        license.KeyId = clk.Id;
                        license.KeyName = clk.Name;
                        licenseData.Add(license);
                    }
                    else
                    {
                        var license_null = new LicenseKeyValueModel();
                        license_null.KeyId = clk.Id;
                        license_null.KeyName = clk.Name;
                        licenseData.Add(license_null);
                    }
                }
            }
            else
            {
                foreach (var clk in filteredDriverLicenseKeys)
                {
                    var license = new LicenseKeyValueModel();
                    license.KeyId = clk.Id;
                    license.KeyName = clk.Name;
                    licenseData.Add(license);
                }
            }

            return licenseData;
        }

        public string GpsDriverId { get; set; }

        public class ScheduleContainer
        {
            public int DriverId { get; set; }
            public string Json { get; set; }
        }

        public class ScheduleItem
        {
            public string DayName { get; set; }
            public TimeSpan Start { get; set; }
            public TimeSpan Stop { get; set; }

            public static List<ScheduleItem> OffsetDateTimes(IEnumerable<ScheduleItem> items, bool reverse = false)
            {
                DateTime standardize(DateTime input) => (reverse ? WebGlobal.OffsetDateTime(input, true) : WebGlobal.OffsetDateTime(input));

                var scheduleItems = new List<ScheduleItem>();

                var sow = Convert.ToDateTime("4/23/2023");
                foreach (var ss in items)
                {
                    DateTime result = sow;
                    while (!string.Equals(result.DayOfWeek.ToString(), ss.DayName, StringComparison.OrdinalIgnoreCase))
                        result = result.AddDays(1);

                    DateTime start = result.Add(ss.Start);
                    DateTime end = result.Add(ss.Stop);
                    // if the end date is before the start date, add a day to the end date,
                    // like if the start is 8:00PM, and the end is 2:00AM. then, 2:00AM must be the next day.
                    if (end < start)
                        end = end.AddDays(1);


                    // Check if the start and end dates are on different days in offset time zone
                    bool isUTCBleed = standardize(start).Date != standardize(end).Date;

                    if (isUTCBleed)
                    {
                        // Calculate the timespan from the start to the midnight of the offset date
                        DateTime utcMidnight = standardize(start).Date.AddDays(1);
                        TimeSpan beforeMidnight = utcMidnight - standardize(start);

                        // Calculate the timespan from the end of the offset date to the end of the input end date
                        DateTime endOfDay = standardize(end).Date;
                        TimeSpan afterMidnight = standardize(end) - endOfDay;

                        Console.WriteLine("Before midnight: " + beforeMidnight);
                        Console.WriteLine("After midnight: " + afterMidnight);
                        scheduleItems.Add(new ScheduleItem()
                        {
                            DayName = standardize(start).Date.DayOfWeek.ToString().ToUpperInvariant(),
                            Start = standardize(start).TimeOfDay,
                            Stop = TimeSpan.FromDays(1).Add(TimeSpan.FromSeconds(-1)),
                        });

                        if (afterMidnight != TimeSpan.FromTicks(0))
                            scheduleItems.Add(new ScheduleItem()
                            {
                                DayName = endOfDay.DayOfWeek.ToString().ToUpperInvariant(),
                                Start = TimeSpan.FromSeconds(0),
                                Stop = (afterMidnight),
                            });
                    }
                    else
                    {
                        if (standardize(start).DayOfWeek != start.DayOfWeek)
                        {
                            var existingDay = scheduleItems.FirstOrDefault(o =>
                                string.Equals(o.DayName, standardize(start).DayOfWeek.ToString(), StringComparison.InvariantCultureIgnoreCase) &&
                                o.Stop == standardize(start).TimeOfDay.Add(TimeSpan.FromSeconds(-1)));

                            if (existingDay != null)
                            {
                                existingDay.Stop = standardize(end).TimeOfDay;
                                continue;
                            }
                        }

                        scheduleItems.Add(new ScheduleItem()
                        {
                            DayName = standardize(start).DayOfWeek.ToString().ToUpperInvariant(),
                            Start = standardize(start).TimeOfDay,
                            Stop = standardize(end).TimeOfDay
                        });
                    }
                }

                return scheduleItems;
            }
        }
    }
}
