using Extric.Roadside.API.Models;
using Extric.Roadside.Surveys;
using Extric.Towbook.Utility;
using Newtonsoft.Json;
using System;
using System.Collections.ObjectModel;
using System.Linq;

namespace Extric.Towbook.API.Models
{
    public class SurveyReviewModel
    {
        public int ResponseId { get; set; }
        public int DispatchId { get; set; }
        public int DispatchUserId { get; set; }
        public int CallId { get; set; }
        public int CallNumber { get; set; }
        public int AccountId { get; set; }
        public string AccountName { get; set; }
        public int CompanyId { get; set; }
        public string CompanyName { get; set; }
        public string PurchaseOrderNumber { get; set; }

        public int? DriverId { get; set; }
        public string DriverName { get; set; }
        public int? TruckId { get; set; }
        public string TruckName { get; set; }
        public int? CallContactId { get; set; }
        public DateTime SubmittedDate { get; set; }

        public string Name { get; set; }
        public string MobileNumber { get; set; }
        public string Email { get; set; }

        [JsonConverter(typeof(PlainJsonStringConverter))]
        public string Answers {get;set;}

        public string Review { get; set; }
        public decimal? AverageRating { get; set; }

        public object WebsiteConnectItem {get;set;}
        public object FacebookConnectItem { get; set; }
        public object AvailableActions { get; set; }

        public static SurveyReviewModel Map(Survey item)
        {
            return new SurveyReviewModel()
            {
                ResponseId = item.SurveyResponseId,
                DispatchId = item.RoadsideDispatchId,
                DispatchUserId = item.RoadsideUserId,
                CallId = item.DispatchEntryId,
                CallNumber = item.CallNumber,
                AccountId = item.AccountId,
                AccountName = item.AccountName,
                CompanyId = item.CompanyId,
                CompanyName = item.CompanyName,
                PurchaseOrderNumber = item.PurchaseOrderNumber,
                DriverId = item.DriverId,
                DriverName = item.DriverName,
                TruckId = item.TruckId,
                TruckName = item.TruckName,
                CallContactId = item.DispatchContactId,
                SubmittedDate = item.SurveySubmittedDate,
                Name = item.ContactName,
                MobileNumber = item.ContactPhone,
                Email = item.ContactEmail,
                Answers = item.AnswersJson,
                Review = item.Review ?? string.Empty,
                AverageRating = item.Rating
            };
        }
    }
}