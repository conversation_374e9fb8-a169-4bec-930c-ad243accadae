using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Collections;
using System.ComponentModel.DataAnnotations;
using Extric.Towbook.WebShared;
using System.Linq;
using Microsoft.AspNetCore.Mvc.Rendering;
using System.Threading.Tasks;
using Geotab.Checkmate.ObjectModel;

namespace Extric.Towbook.API.Models
{

    public class RateItemModel
    {
        public int Id { get; set; }
        [Required]
        public string Name { get; set; }

        public string Cost { get; set; }
        public List<ExtendedRateModel> ExtendedRates { get; set; }

        [DisplayName("Category")]
        public int? CategoryId { get; set; }
        [DisplayName("Charge Tax")]
        public bool? Taxable { get; set; }
        public bool? Enable { get; set; }

        [DisplayName("Prevent fuel surcharges")]
        public bool ExcludeFuelSurcharge { get; set; }

        public bool Predefined { get; set; }
        public int PredefinedId { get; set; }

        [DisplayName("Regulated Charge")]
        public bool? RegulatedCharge { get; set; }

        // TODO: replace this with a Model representation. this shouldn't be in the model...
        public Collection<Extric.Towbook.Accounts.RateItem> AccountRateItems { get; set; }

        // Tiered Storage
        public Collection<RateItemModel> Children { get; set; }

        [DisplayName("Tiered Storage Pricing")]
        public bool? TieredStorageRates { get; set; }

        /// <summary>
        /// If this is a child item, then this holds the parent RateItemId, such as Inside Storage. 
        /// </summary>
        public int ParentRateItemId { get; set; }
        public decimal? FreeQuantity { get; set; }
        public IEnumerable Categories { get; set; }
        public int? DefaultClassId { get; set; }

        [DisplayName("Available To All Accounts")]
        public bool InclusionType { get; set; }

        [DisplayName("Hourly Based Rate")]
        public bool? TimeBasedItem { get; set; }

        [DisplayName("Start Calculation At")]
        public int? TimeStartAtStatusId { get; set; }
        [DisplayName("Stop Calculation At")]
        public int? TimeStopAtStatusId { get; set; }

        [DisplayName("Round to")]
        public int? TimeRound { get; set; }
        public SelectList Statuses { get; set; }

        public RateItem.MeasurementEnum? Measurement { get; set; }

        public bool Deleted { get; set; }
        public int? LedgerAccountId { get; set; }

        public SelectList TimeRoundValues
        {
            get
            {
                return new SelectList(new[]
                {
                   new {Name = "Actual Time", Value = (int)RateItem.TimeRoundEnum.Actual },
                   new {Name = "Nearest Quarter Hour", Value = (int)RateItem.TimeRoundEnum.NearestQuarterHour },
                   new {Name = "Nearest Half Hour", Value = (int)RateItem.TimeRoundEnum.NearestHalfHour },
                   new {Name = "Nearest Hour", Value = (int)RateItem.TimeRoundEnum.NearestHour},
                }, "Value", "Name", TimeRound.GetValueOrDefault());
            }
        }

        public static RateItemModel Map(IRateItem input)
        {
            if (input == null)
                throw new TowbookException("Cannot map null IRateItem to RateItemModel.");

            RateItemModel d = new RateItemModel();

            d.Id = input.RateItemId;
            d.Name = input.Name;
            d.CategoryId = input.CategoryId;
            d.Taxable = input.Taxable;
            d.Predefined = input.Predefined != null;
            d.PredefinedId = (d.Predefined ? input.Predefined.Id : 0);
            d.TieredStorageRates = null; // dangerous - causes R/T to database/redis for each rateitem .. // RateItem.GetByParentId(input.RateItemId).Count > 0;
            d.ParentRateItemId = input.ParentRateItemId;
            d.FreeQuantity = input.FreeQuantity;
            d.DefaultClassId = input.DefaultClassId;
            d.ExtendedRates = new List<ExtendedRateModel>();
            d.Deleted = input.Deleted;

            foreach (var t in Vehicle.BodyType.GetByCompanyId(WebGlobal.CurrentUser.CompanyId))
            {
                var rrm = new ExtendedRateModel();

                rrm.BodyTypeId = t.Id;
                rrm.Name = t.Name;

                if (input.ExtendedRateItems.ContainsKey(t.Id))
                {
                    rrm.Cost = input.ExtendedRateItems[t.Id].Amount.ToString("0.00");
                    rrm.FreeQuantity = input.ExtendedRateItems[t.Id].FreeQuantity;
                }
                else
                {
                    rrm.Cost = string.Empty;
                    rrm.FreeQuantity = d.FreeQuantity.Value;
                }
                d.ExtendedRates.Add(rrm);
            }

            if (input.Cost != 0)
            {
                d.Cost = input.Cost.ToString("0.00");
            }
            else
            {
                d.Cost = string.Empty;
            }
            
            if (input is RateItem)
            {
                d.TimeBasedItem = (input as RateItem).RateType == RateItem.RateTypeEnum.FixedCalculatedRate &&
                    (input as RateItem).Measurement == RateItem.MeasurementEnum.Hours;

                d.TimeStartAtStatusId = (input as RateItem).TimeStartAtStatusId;
                d.TimeStopAtStatusId = (input as RateItem).TimeStopAtStatusId;
                d.TimeRound = (int?)(input as RateItem).TimeRound;

                if ((input as RateItem).Measurement != RateItem.MeasurementEnum.Hours)
                    d.Measurement = (input as RateItem).Measurement;
                else
                    d.Measurement = null;
            }
            else
                d.TimeBasedItem = false;

            d.InclusionType = (input as RateItem).InclusionType == RateItem.InclusionTypeEnum.Corporate;
            d.RegulatedCharge = false;
            d.LedgerAccountId = input.LedgerAccountId;
            return d;
        }

        public static async Task<RateItemModel> MapAsync(IRateItem input)
        {
            if (input == null)
                throw new TowbookException("Cannot map null IRateItem to RateItemModel.");

            RateItemModel d = new RateItemModel();

            d.Id = input.RateItemId;
            d.Name = input.Name;
            d.CategoryId = input.CategoryId;
            d.Taxable = input.Taxable;
            d.Predefined = input.Predefined != null;
            d.PredefinedId = (d.Predefined ? input.Predefined.Id : 0);
            d.TieredStorageRates = null; // dangerous - causes R/T to database/redis for each rateitem .. // RateItem.GetByParentId(input.RateItemId).Count > 0;
            d.ParentRateItemId = input.ParentRateItemId;
            d.FreeQuantity = input.FreeQuantity;
            d.DefaultClassId = input.DefaultClassId;
            d.ExtendedRates = new List<ExtendedRateModel>();
            d.Deleted = input.Deleted;

            var bodyTypes = await Vehicle.BodyType.GetByCompanyIdAsync(WebGlobal.CurrentUser.CompanyId);

            foreach (var t in bodyTypes)
            {
                var rrm = new ExtendedRateModel();

                rrm.BodyTypeId = t.Id;
                rrm.Name = t.Name;

                if (input.ExtendedRateItems.ContainsKey(t.Id))
                {
                    rrm.Cost = input.ExtendedRateItems[t.Id].Amount.ToString("0.00");
                    rrm.FreeQuantity = input.ExtendedRateItems[t.Id].FreeQuantity;
                }
                else
                {
                    rrm.Cost = string.Empty;
                    rrm.FreeQuantity = d.FreeQuantity.Value;
                }
                d.ExtendedRates.Add(rrm);
            }

            if (input.Cost != 0)
            {
                d.Cost = input.Cost.ToString("0.00");
            }
            else
            {
                d.Cost = string.Empty;
            }

            if (input is RateItem)
            {
                d.TimeBasedItem = (input as RateItem).RateType == RateItem.RateTypeEnum.FixedCalculatedRate &&
                    (input as RateItem).Measurement == RateItem.MeasurementEnum.Hours;

                d.TimeStartAtStatusId = (input as RateItem).TimeStartAtStatusId;
                d.TimeStopAtStatusId = (input as RateItem).TimeStopAtStatusId;
                d.TimeRound = (int?)(input as RateItem).TimeRound;

                if ((input as RateItem).Measurement != RateItem.MeasurementEnum.Hours)
                    d.Measurement = (input as RateItem).Measurement;
                else
                    d.Measurement = null;
            }
            else
                d.TimeBasedItem = false;

            d.InclusionType = (input as RateItem).InclusionType == RateItem.InclusionTypeEnum.Corporate;
            d.RegulatedCharge = false;
            d.LedgerAccountId = input.LedgerAccountId;
            return d;
        }

        public static async Task<RateItem> MapAsync(RateItemModel model, RateItem input)
        {
            if (input.Predefined == null && model.PredefinedId < 1)
            {
                input.Name = model.Name;
                input.CategoryId = model.CategoryId;
            }

            if (model.Taxable != null)
                input.Taxable = model.Taxable.Value;

            if (model.Cost != null)
            {
                string cost = model.Cost.Replace("$", "").Replace(" ", "");
                if (cost.Length != 0)
                    input.Cost = Math.Round(Convert.ToDecimal(cost), 2, MidpointRounding.AwayFromZero);
                else
                    input.Cost = 0;
            }
            else
            {
                input.Cost = 0;
            }

            if (model.Predefined || model.PredefinedId > 0)
                input.Predefined = await PredefinedRateItem.GetByIdAsync(model.PredefinedId);

            if (model.FreeQuantity != null)
                input.FreeQuantity = model.FreeQuantity.Value;
            input.ParentRateItemId = model.ParentRateItemId;

            if (input.ParentRateItemId > 0)
            {
                var r = await RateItem.GetByIdAsync(input.ParentRateItemId);

                input.CategoryId = r.CategoryId;
            }

            if (model.DefaultClassId != null)
                input.DefaultClassId = model.DefaultClassId.Value;

            if (model.TimeBasedItem != null && model.TimeBasedItem.Value)
            {
                input.RateType = RateItem.RateTypeEnum.FixedCalculatedRate;
                input.Measurement = RateItem.MeasurementEnum.Hours;

                if (model.TimeStartAtStatusId != null)
                    input.TimeStartAtStatusId = model.TimeStartAtStatusId;

                if (model.TimeStopAtStatusId != null)
                    input.TimeStopAtStatusId = model.TimeStopAtStatusId;

                if (model.TimeRound != null)
                    input.TimeRound = (RateItem.TimeRoundEnum)model.TimeRound;

            }
            else
            {
                input.RateType = RateItem.RateTypeEnum.FixedRate;

                input.TimeRound = null;
                input.TimeStartAtStatusId = null;
                input.TimeStopAtStatusId = null;
            }


            if (model.InclusionType)
                input.InclusionType = RateItem.InclusionTypeEnum.Corporate;
            else
                input.InclusionType = RateItem.InclusionTypeEnum.Default;

            if (model.Measurement != null)
                input.Measurement = model.Measurement.Value;

            if (model.LedgerAccountId != null)
                input.LedgerAccountId = model.LedgerAccountId.Value;

            input.Deleted = model.Deleted;

            return input;
        }

        internal static List<RateItemModel> Map(IEnumerable<IRateItem> input)
        {
            List<RateItemModel> output = new List<RateItemModel>();

            foreach (RateItem t in input)
                output.Add(Map(t));

            return output;
        }

        public static void SaveExtendedRateItems(RateItemModel model, RateItem input, User currentUser)
        {
            if (model.ExtendedRates == null) return;
            foreach (ExtendedRateModel rm in model.ExtendedRates)
            {
                decimal rAmount = 0;

                if (!string.IsNullOrWhiteSpace(rm.Cost))
                    rAmount = Math.Round(Convert.ToDecimal(rm.Cost.Replace("$", "")), 2,
                        MidpointRounding.AwayFromZero);

                if (rAmount == 0)
                {
                    if (input.ExtendedRateItems.ContainsKey(rm.BodyTypeId))
                        input.ExtendedRateItems[rm.BodyTypeId].Delete(currentUser);
                }
                else
                {
                    if (input.ExtendedRateItems.ContainsKey(rm.BodyTypeId))
                    {
                        input.ExtendedRateItems[rm.BodyTypeId].Amount = rAmount;
                    }
                    else
                    {
                        input.ExtendedRateItems.Add(rm.BodyTypeId, new ExtendedRateItem());
                        input.ExtendedRateItems[rm.BodyTypeId].RateItemId = input.RateItemId;
                        input.ExtendedRateItems[rm.BodyTypeId].BodyTypeId = rm.BodyTypeId;
                        input.ExtendedRateItems[rm.BodyTypeId].Amount = rAmount;
                    }

                    input.ExtendedRateItems[rm.BodyTypeId].Save();
                }
            }
        }
    }

    public class ExtendedRateModel
    {
        public int BodyTypeId { get; set; }
        public string Name { get; set; }
        public string Cost { get; set; }
        public decimal FreeQuantity { get; set; }
    }

    public class RateItemConfigModel
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public decimal Cost { get; set; }
        public bool Taxable { get; set; }
        public bool LockCost { get; set; }
        public int? CategoryId { get; set; }
        public bool LockQuantity { get; set; }
        public int? DefaultQuantity { get; set; }
        public int? MaxQuantity { get; set; }
        public int? MinQuantity { get; set; }
        public decimal FreeQuantity { get; set; }
        public RateItemPredefinedConfigModel Predefined { get; set; }
        public IEnumerable<RateItemExtendedConfigModel> ExtendedRates { get; set; }
        public bool DiscountExempt { get; set; }
        public bool Hidable { get; set; }
        public int[] Companies { get; set; }
        public int ClassId { get; set; }
        public int? TimeStartAtStatusId { get; set; }
        public int? TimeStopAtStatusId { get; set; }
        public int? TimeRound { get; set; }
        public int InclusionType { get; set; }
        public RateItem.MeasurementEnum? Measurement {get;set;}
        public int? LedgerAccountId { get; set; }

        public static RateItemConfigModel Map(IRateItem o, bool blockCharges)
        {
            if (o == null)
                return null;

            var r = new RateItemConfigModel()
            {
                Id = o.RateItemId,
                Name = o.Name,
                Cost = (o.Cost == 0 ? o.BaseCost : o.Cost),
                Taxable = o.Taxable,
                LockCost = o.LockCost,
                CategoryId = o.CategoryId,
                LockQuantity = o.LockQuantity,
                DefaultQuantity = o.DefaultQuantity,
                MaxQuantity = o.MaximumQuantity,
                MinQuantity = o.MinimumQuantity,
                FreeQuantity = o.FreeQuantity,
                Predefined = RateItemPredefinedConfigModel.Map(o.Predefined),
                ExtendedRates = (o.ExtendedRateItems?.Values.Select(e => new RateItemExtendedConfigModel(e.BodyTypeId, e.Amount, e.FreeQuantity))),
                DiscountExempt = o.DiscountExempt,
                Hidable = (o.Predefined != null ? false : true),
                Companies = o.Companies,
                ClassId = o.DefaultClassId,
                TimeStartAtStatusId = (o as RateItem)?.TimeStartAtStatusId,
                TimeStopAtStatusId = (o as RateItem)?.TimeStopAtStatusId,
                TimeRound = (int?)(o as RateItem)?.TimeRound,
                InclusionType = (o as RateItem)?.InclusionType == RateItem.InclusionTypeEnum.Corporate ? 1 : 0,
                Measurement = (o as RateItem)?.Measurement > 0 ? (o as RateItem)?.Measurement : null,
                LedgerAccountId = o.LedgerAccountId
            };

            if (blockCharges)
            {
                // dont hide cash or overage pricing when blocking charges from drivers/dispatchers.
                // they need it to know how much money to collect.
                if (!(r.Name != null && (
                    r.ClassId == ChargeClass.Cash ||
                    r.Name.ToLowerInvariant().Contains("cash") ||
                    r.Name.ToLowerInvariant().Contains("overage"))))
                {
                    r.Cost = 0;
                    r.FreeQuantity = 0;
                    r.ExtendedRates = Array.Empty<RateItemExtendedConfigModel>();
                }
            }

            return r;
        }
    }

    public class RateItemExtendedConfigModel
    {
        public int Id { get; }
        public decimal Price { get; }
        public decimal FreeQuantity { get; }

        public RateItemExtendedConfigModel(int id, decimal price, decimal freeQuantity)
        {
            Id = id;
            Price = price;
            FreeQuantity = freeQuantity;
        }

        public override bool Equals(object obj)
        {
            return obj is RateItemExtendedConfigModel other &&
                   Id == other.Id &&
                   Price == other.Price &&
                   FreeQuantity == other.FreeQuantity;
        }

        public override int GetHashCode()
        {
            var hashCode = 996299460;
            hashCode = hashCode * -1521134295 + Id.GetHashCode();
            hashCode = hashCode * -1521134295 + Price.GetHashCode();
            hashCode = hashCode * -1521134295 + FreeQuantity.GetHashCode();
            return hashCode;
        }
    }

    public class RateItemPredefinedConfigModel
    {
        public int Id { get; set; }
        public bool Locked { get; set; }
        public bool Hidden { get; set; }

        public static RateItemPredefinedConfigModel Map(PredefinedRateItem obj)
        {
            if (obj == null)
                return null;
            return new RateItemPredefinedConfigModel()
            {
                Id = obj.Id,
                Locked = obj.Locked,
                Hidden = obj.Hidden
            };
        }
    }

}
