//using System;
//using System.Collections.Generic;
//using System.Collections.ObjectModel;
//using System.Linq;
//using System.Web;
//using Extric.Towbook.Accounts;
//using Extric.Towbook.API.Models.Accounts;
//using Extric.Towbook.Dispatch;
//using Extric.Towbook.Integration;
//using Extric.Towbook.Utility;
//using Newtonsoft.Json;
//using Newtonsoft.Json.Converters;

//namespace Extric.Towbook.API.Models
//{
//    public class IdModel
//    {
//        public int Id { get; set; }
//    }

//    public class AccountMinimalModel
//    {
//        public int Id { get; set; }
//        public string Name { get; set; }
//        public AccountType? Type { get; set; }
//        public int? MasterAccountId { get; set; }
//        public bool IsFavorite { get; set; }
//        public string Phone { get; set; }
//        public string ContactName { get; set; }
//        public string Email { get; set; }
//        public string Fax { get; set; }
//        public AddressMinimalModel Address { get; set; }
//        public IEnumerable<int> Tags { get; set; }
//        public IEnumerable<IdModel> Attributes { get; set; }
//        public AccountImpoundDestination? ImpoundDestinationType { get; set; }
//        public int? ImpoundDestinationStorageLotId { get; set; }
//        public IEnumerable<InternalGetForAccountObject> RateItems { get; set; }
//        public AccountStatus? Status { get; set; }
//        public bool Deleted { get; set; }
//        public string DefaultTowFrom { get; set; }
//        public string DefaultTowTo { get; set; }
//        public bool? TaxExempt { get; set; }
//        public decimal? DiscountRate { get; set; }
//        public string AccountNotes { get; set; }
//        public int[] Companies { get; set; }
//        public decimal? CreditLimit { get; set; }
//        public decimal? CreditUtilized { get; set; }
//        public bool? CreditHold { get; set; }
//        public decimal? Balance { get; set; }
//        public decimal? CreditBalance { get; set; }
//        public DateTime? LastActivity { get; set; }
//        public string PropertyGateCode { get; set; }
//        public bool? AutomaticallyAddMileage { get; set; }
        
//        public bool? AutomaticallyAddDeadheadMileage { get; set; }
//        public AccountPropertyManagementModel PropertyManagementDetails { get; set; }
//        public string ContractorId { get; set; }
//        public bool InvoiceHideChargesByDefault { get; set; }
//        public bool InvoiceHideDiscountsByDefault { get; set; }
//        public bool InvoiceIncludePaymentLinkByDefault { get; set; }
//        public bool RequireInvoicePaid { get; set; }
//        public int? DefaultPriority { get; set; }
//        public string DefaultPO { get; set; }
//        public int? DefaultStorageRateItemId { get; set; }
//        public int? DefaultBodyTypeId { get; set; }
//        public int? DefaultSubcontractorAccountId { get; set; }
//        public int? DefaultBillToAccountId { get; set; }
//        public IEnumerable<AccountRuleSetModel> CallValidationRuleSet { get; set; }
//        public AccountManagerMinimalModel Manager { get; set; }
//        public StatementPreferenceModel StatementPreferences { get; set; }

//        public static AccountMinimalModel Map(AccountModel o,
//            IEnumerable<InternalGetForAccountObject> rateItems,
//            IEnumerable<AccountKeyValue> customFields,
//            IEnumerable<EntryValidationRuleSet> ruleSets,
//            IEnumerable<Dispatch.Attribute> attributes,
//            User currentUser,
//            IEnumerable<CompanyKeyDefaultValue> customFieldDefaultValues = null,
//            IEnumerable<AccountManager> managers = null)
//        {
//            o = AccountModel.ReduceDetails(o, currentUser);

//            var ruleSet = new Collection<AccountRuleSetModel>();

//            if (ruleSets != null && ruleSets.Any(rs => rs.AccountId == o.Id))
//            {
//                ruleSet = AccountValidationRuleSetModel.Map(
//                    CallValidationRuleSetModel.Map(
//                        ruleSets.First(rs => rs.AccountId == o.Id ||
//                        (rs.AccountId.GetValueOrDefault() == 0 && rs.MasterAccountId == o.MasterAccountId))), currentUser.Type)
//                    .Where(ro => ro.IsRequired || ro.ShowWarning).ToCollection();
//            }

//            o = AccountModel.FillKeyValues(o, customFields, currentUser);

//            // AC 2/25/2020 - Hot fix for mobile apps. Apps will force impound if ImpoundDestinationStorageLotId has any value.
//            if (HttpContext.Current.IsAndroidDevice() || HttpContext.Current.IsAppleDevice()) {
//                if(o.ImpoundDestinationType == AccountImpoundDestination.None)
//                    o.ImpoundDestinationStorageLotId = null;
//            }


//            if (o.Type == AccountType.Subcontractor &&
//                !string.IsNullOrEmpty(o.City) &&
//                !string.IsNullOrEmpty(o.State))
//            {
//                var t = $" ({o.City}, {o.State})";
//                if (o.Company != null && !o.Company.Contains(t))
//                    o.Company += t;
//            }

//            var includePaymentLinkDefaultValue = 
//                customFieldDefaultValues?.FirstOrDefault(f => f.Key == "Square_AlwaysIncludePaymentLinkOnInvoices" && o.Companies.Contains(f.CompanyId))?.DefaultValue == "1";

//            var manager = AccountManagerMinimalModel.Map(managers?.LastOrDefault(l => l.AccountId == o.Id));

//            return new AccountMinimalModel
//            {
//                Id = o.Id,
//                Name = o.Company,
//                Type = o.Type,
//                MasterAccountId = o.MasterAccountId,

//                // TODO: make IsFavorite come from db so people can customize what is a fav/pin to top of list.
//                IsFavorite = o.MasterAccountId == MasterAccountTypes.SafeClear || o.Type == AccountType.PoliceDepartment,
//                Phone = o.Phone,
//                ContactName = o.FullName,
//                Email = o.Email,
//                Fax = o.Fax,
//                Address = new AddressMinimalModel
//                {
//                    Address = o.Address,
//                    City = o.City,
//                    State = o.State,
//                    Zip = o.Zip,
//                    Latitude = o.Latitude,
//                    Longitude = o.Longitude
//                },
//                Tags = o.Tags,
//                Attributes = attributes?.Select(ro => new IdModel() { Id = ro.Id }).ToArray() ??
//                Dispatch.Attribute.GetByAccount(o.Companies.FirstOrDefault(), o.Id).Select(a => new IdModel { Id = a.Id }),
//                ImpoundDestinationType = o.ImpoundDestinationType,
//                ImpoundDestinationStorageLotId = o.ImpoundDestinationStorageLotId,
//                RateItems = rateItems ?? Controllers.AccountRateItemsController.InternalGetForAccount(o.Id, currentUser),
//                Status = o.Status,
//                Deleted = o.Deleted,
//                DefaultTowFrom = o.DefaultTowFrom,
//                DefaultTowTo = o.DefaultTowTo,
//                TaxExempt = o.TaxExempt,
//                DiscountRate = o.DiscountRate,
//                AccountNotes = GetAccountNotes(o, 
//                    ruleSets?.FirstOrDefault(rs => rs.AccountId == o.Id || 
//                        (rs.AccountId.GetValueOrDefault() == 0 && rs.MasterAccountId == o.MasterAccountId))),
//                Companies = o.Companies,
//                CreditLimit = o.CreditLimit,
//                CreditUtilized = o.CreditUtilized,
//                CreditHold = o.CreditHold,
//                Balance = o.Balance,
//                CreditBalance = o.CreditBalance,
//                LastActivity = o.LatestActivity,
//                PropertyGateCode = o.PropertyGateCode,
//                AutomaticallyAddMileage = o.AutomaticallyAddMileage,
//                AutomaticallyAddDeadheadMileage = o.AutomaticallyAddDeadheadMileage,
//                PropertyManagementDetails = o.PropertyManagementDetails,
//                ContractorId = o.ContractorId,
//                InvoiceHideChargesByDefault =  o.InvoiceHideChargesByDefault.GetValueOrDefault(),
//                InvoiceHideDiscountsByDefault = o.InvoiceHideChargesByDefault.GetValueOrDefault(),
//                InvoiceIncludePaymentLinkByDefault = o.InvoiceIncludePaymentLinkByDefault.GetValueOrDefault(includePaymentLinkDefaultValue),
//                RequireInvoicePaid = ruleSets?.FirstOrDefault(rs => rs.AccountId == o.Id)?.RequireInvoicePaid == EntryValidationType.Required,
//                DefaultPriority = o.DefaultPriority,
//                DefaultPO = !string.IsNullOrWhiteSpace(o.DefaultPO) ? o.DefaultPO : null,
//                DefaultStorageRateItemId = o.DefaultStorageRateItemId,
//                DefaultBodyTypeId = o.DefaultBodyTypeId,
//                DefaultSubcontractorAccountId = o.DefaultSubcontractorAccountId,
//                DefaultBillToAccountId = o.DefaultBillToAccountId,
//                CallValidationRuleSet = ruleSet,
//                Manager = manager,
//                StatementPreferences = o.StatementPreferences
//            };
//        }

//        private static string GetAccountNotes(AccountModel o, EntryValidationRuleSet rs = null)
//        {
//            string notes = string.Empty;

//            if (o.CreditHold.GetValueOrDefault())
//                notes += "***THIS ACCOUNT HAS A CREDIT HOLD ON IT - CASH OR CREDIT PAYMENT IS REQUIRED. ***\n\n";

//            if (o.CreditLimit.GetValueOrDefault() > 0 &&
//                o.CreditUtilized.GetValueOrDefault() >= o.CreditLimit.GetValueOrDefault())
//                    notes += "*** THIS ACCOUNT HAS EXCEEDED ITS CREDIT LIMIT. ***\n\n";

//            if(rs != null && 
//                (rs.RequireInvoicePaid == EntryValidationType.Required || 
//                 rs.RequireInvoicePaid == EntryValidationType.RequiredIncludingDispatchers))
//                    notes += "***THIS ACCOUNT IS A COD ACCOUNT - CASH OR CREDIT PAYMENT IS REQUIRED. ***\n\n";

//            notes += o.Notes;

//            return notes;
//        }
//    }
//}