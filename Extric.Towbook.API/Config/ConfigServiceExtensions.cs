using Extric.Towbook.API.Config;
using Microsoft.AspNetCore.Builder;

namespace Extric.Towbook.API
{
    public static class ConfigServiceExtensions
    {
        public static void ConfigureExceptionHandlerMiddleware(this IApplicationBuilder app)
        {
            app.UseMiddleware<ExceptionHandlerMiddleware>();
        }

        public static void ConfigureApiAuthentication(this IApplicationBuilder app)
        {
            app.UseMiddleware<ApiAuthenticationMiddleware>();
        }
        
        public static void ConfigureLoggingMiddleware(this IApplicationBuilder app)
        {
            app.UseMiddleware<LoggerMiddleware>();
        }

        public static void ConfigurePreSendRequestHeadersMiddleware(this IApplicationBuilder app)
        {
            app.UseMiddleware<PreSendRequestHeadersMiddleware>();
        }
    }
}
