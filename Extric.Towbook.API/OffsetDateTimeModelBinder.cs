using System;
using System.Linq;
using System.Threading.Tasks;
using Extric.Towbook.WebShared;
using Microsoft.AspNetCore.Mvc.ModelBinding;

namespace Extric.Towbook.API
{
    internal class OffsetDateTimeModelBinder : IModelBinder
    {
        public Task BindModelAsync(ModelBindingContext bindingContext)
        {
            if (bindingContext == null)
                throw new ArgumentNullException(nameof(bindingContext));

            var values = bindingContext.ValueProvider.GetValue(bindingContext.ModelName);
            if (values.Length == 0)
                return Task.CompletedTask;

            var value = values.FirstValue;

            if (value == null)
                return Task.CompletedTask;

            if (value.Contains(","))
            {
                DateTime[] dates = ArrayCommaDelimitedModelBinder.ToArray(typeof(DateTime), value) as DateTime[];

                if (dates != null)
                {
                    bindingContext.Result = ModelBindingResult.Success(dates.Select(d => Core.OffsetDateTime(WebGlobal.CurrentUser?.Company, d, true)).ToArray());
                    return Task.CompletedTask;
                }
                else
                    return Task.CompletedTask;
            }

            DateTime result = DateTime.MinValue;
            if (DateTime.TryParse(value, out result))
            {
                // offset the date from the users local time (like pacific) to users
                bindingContext.Result = ModelBindingResult.Success(Core.OffsetDateTime(WebGlobal.CurrentUser?.Company, result, true));

                return Task.CompletedTask;
            }

            return Task.CompletedTask;
        }

        //public bool BindModel(HttpActionContext actionContext, ModelBindingContext bindingContext)
        //{
        //    var value = bindingContext.ValueProvider.GetValue(bindingContext.ModelName)?.RawValue as string;

        //    if (value == null)
        //        return false;

        //    if (value.Contains(","))
        //    {
        //        DateTime[] dates = ArrayCommaDelimitedModelBinder.ToArray(typeof(DateTime), value) as DateTime[];

        //        if (dates != null)
        //        {
        //            bindingContext.Model = dates.Select(d => Core.OffsetDateTime(WebGlobal.CurrentUser?.Company, d, true)).ToArray();
        //            return true;
        //        }
        //        else
        //            return false;
        //    }

        //    DateTime result = DateTime.MinValue;
        //    if (DateTime.TryParse(value, out result))
        //    {
        //        // offset the date from the users local time (like pacific) to users
        //        bindingContext.Model = Core.OffsetDateTime(WebGlobal.CurrentUser?.Company, result, true); 

        //        return true;
        //    }

        //    return false;
        //}
    }
}