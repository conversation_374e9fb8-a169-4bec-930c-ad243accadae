using Extric.Towbook.WebShared;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Extric.Towbook.API
{
    public class ApiPermissionConstraint// : IRouteConstraint
    {
        /*private User.TypeEnum type;

        /// <summary>
        /// Limit requests to specified user type.
        /// </summary>
        /// <param name="type"></param>
        public ApiPermissionConstraint(User.TypeEnum type)
        {
            this.type = type;
        }

        public bool Match(HttpContextBase httpContext,
               Route route,
               string parameterName,
               RouteValueDictionary values,
               RouteDirection routeDirection)
        {
            if (WebGlobal.CurrentUser != null)
            {
                if (WebGlobal.CurrentUser.Type != this.type)
                {
                    return false;
                }
            }

            return true;
        }*/
    }
}