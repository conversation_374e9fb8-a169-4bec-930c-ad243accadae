using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.AspNetCore.Mvc;

namespace Extric.Towbook.API
{

    public class ValidateFilterAttribute : ActionFilterAttribute
    {
        public override void OnActionExecuting(ActionExecutingContext actionContext)
        {
            //System.Diagnostics.Debug.WriteLine("ON ACTION EXECUTING");

            var modelState = actionContext.ModelState;
            if (!modelState.IsValid)
            {
                var errors = modelState
                    .Where(s => s.Value.Errors.Count > 0)
                    .Select(s => new KeyValuePair<string, string>(s.Key, s.Value.Errors.First().ErrorMessage))
                    .ToArray();

                actionContext.Result = new ObjectResult(errors) { StatusCode = HttpStatusCode.BadRequest.IntValue() };
            }
            base.OnActionExecuting(actionContext);
        }
    }
}