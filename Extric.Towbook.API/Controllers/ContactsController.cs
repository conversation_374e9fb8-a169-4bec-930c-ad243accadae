using Extric.Towbook.Dispatch;
using Extric.Towbook.WebShared;
using System.Collections.ObjectModel;
using System.Linq;
using System.Net;
using System.Net.Http;
using Extric.Towbook.Dispatch.CallModels;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;

namespace Extric.Towbook.API.Controllers
{
    [Route("Contacts")]
    [ApiController]
    public class ContactsController : ControllerBase
    {
        /// <summary>
        /// Get all the contacts or search for a specific contact by url passing.
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("")]
        public async Task<CallContactModel> Get([FromQuery]string phone, [FromQuery] int? companyId = null)
        {
            var entries = new Collection<EntryContact>();

            var response = CallContactModel.MapDomainToModel(
                    await EntryContact.GetByPhoneAsync((await WebGlobal.GetCompaniesAsync()).Select(y => y.Id).ToArray(), 
                    Core.FormatPhoneWithDashesOnly(phone)));

            if (companyId > 0)
            {
                var abe = (await Company.AddressBookEntry.GetByPhone(phone, companyId.Value, false)).FirstOrDefault();

                if (abe != null)
                    response = CallContactModel.MapAddressBookToModel(abe, response?.CallId);
            }

            if (response == null)
                throw new Extric.Towbook.Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound) { Content = new StringContent("No results found.") });

            return response;
        }
    }
}
