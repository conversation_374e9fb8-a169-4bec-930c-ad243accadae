using Microsoft.AspNetCore.Mvc;
using Extric.Towbook.API.Models;
using Extric.Towbook.Company;
using Extric.Towbook.Dispatch;
using Extric.Towbook.WebShared;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Web.Http;
using System;

namespace Extric.Towbook.API.Controllers
{

    [Route("reasonGroups")]
    public class ReasonGroupsController : ControllerBase
    {
        /// <summary>
        /// Retrieve a list of reason groups available to the current company (Private Property, Towing, Transport, etc)
        /// </summary>
        /// <returns></returns>
        //routes.MapHttpRoute(
        //    name: "DefaultApiGetSingle",
        //    routeTemplate: "{controller}/{action}",
        //    defaults: new { action = "Get" },
        //    constraints: new
        //    {
        //        httpMethod = new HttpMethodConstraint(new string[] { "GET" }),
        //    }
        //    ).DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };
        [Route("")]
        [HttpGet]
        public IEnumerable<ReasonGroupModel> Get()
        {
            var crg = CompanyReasonGroup.GetByCompanyId(WebGlobal.CurrentUser.CompanyId);

            return ReasonGroup.GetAll().Select(o => ReasonGroupModel.Map(o, crg));
        }

        //routes.MapHttpRoute(
        //    name: "DefaultApiGet",
        //    routeTemplate: "{controller}/{id}",
        //    defaults: new { id = RouteParameter.Optional, action = "Get" },
        //    constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "GET" }),
        //                       id = new RestStyleConstraint() }
        //    ).DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };
        [Route("{id}")]
        [HttpGet]
        /// <summary>
        /// Return reason group for id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<IEnumerable<ReasonModel>> Get(int id)
        {
            var reasonObjs = await Task.WhenAll(ReasonGroup.GetById(id)?.Reasons.Select(Reason.GetByIdAsync));
            return reasonObjs.Select(ReasonModel.Map);
        }

        public class ReasonGroupModel
        {
            public int Id { get; set; }
            public string Name { get; set; }
            public bool Enabled { get; set; }

            public static ReasonGroupModel Map(ReasonGroup domain,
                IEnumerable<CompanyReasonGroup> selectedGroups)
            {
                return new ReasonGroupModel()
                {
                    Id = domain.Id,
                    Name = domain.Name,
                    Enabled = selectedGroups.Where(o => o.DispatchReasonGroupId == domain.Id).Any()
                };
            }
        }

        //routes.MapHttpRoute(
        //    name: "DefaultApiPut",
        //    routeTemplate: "{controller}/{id}",
        //    defaults: new { id = RouteParameter.Optional, action = "Put" },
        //    constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "PUT" }),
        //                       id = new RestStyleConstraint() })
        //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };
        [Route("{id}")]
        [HttpPut]
        public async Task Put(int id, ReasonGroupModel model)
        {
            if (model.Enabled)
            {
                var f = CompanyReasonGroup.GetByCompanyId(WebGlobal.CurrentUser.CompanyId).Where(o => o.DispatchReasonGroupId == model.Id).FirstOrDefault();
                if (f == null)
                    await new CompanyReasonGroup() { CompanyId = WebGlobal.CurrentUser.CompanyId, DispatchReasonGroupId = model.Id }.Save();
            }
            else
            {
                var f = CompanyReasonGroup.GetByCompanyId(WebGlobal.CurrentUser.CompanyId).Where(o => o.DispatchReasonGroupId == model.Id).FirstOrDefault();
                if (f != null)
                    await f.Delete();
            }
            return;
        }



    }
}
