using System.Linq;
using System.Threading.Tasks;
using Extric.Towbook.Integration;
using Extric.Towbook.WebShared;
using Microsoft.AspNetCore.Mvc;

namespace Extric.Towbook.API.Integration.Controllers
{
    [Route("integration/trucks")]
    public class IntegrationTrucksController : ControllerBase
    {
        /// <summary>
        /// Returns KeyValue pairs for specified Truck.
        /// </summary>
        /// <param name="id">Truck Id. Optional.</param>
        /// <returns></returns>
        [HttpGet]
        [Route("")]
        [Route("get/{id}")]
        public object Get(int? id = null)
        {
            if (id == null)
                return TruckKeyValue.GetByCompany(WebGlobal.CurrentUser.CompanyId);
            else
                return TruckKeyValue.GetByTruck(WebGlobal.CurrentUser.CompanyId, id.Value);
        }


        [HttpDelete]
        [Route("delete/{id}")]
        [Route("")]
        public async Task<object> Delete(int id)
        {
            var currentGpsProvider = CompanyKeyValue.GetFirstValueOrNull(
                WebGlobal.CurrentUser.CompanyId,
                Provider.Towbook.ProviderId,
                "CompanyGpsProvider"
            );

            var provider = Provider.GetByName(currentGpsProvider);

            var data = TruckKeyValue.GetByTruck(WebGlobal.CurrentUser.CompanyId, id).Where(w => w.KeyId == provider.GetKey(KeyType.Truck, "TruckId").Id);

            foreach (var akv in data)
            {
                await akv.DeleteAsync();
            }

            return data;
        }
    }
}
