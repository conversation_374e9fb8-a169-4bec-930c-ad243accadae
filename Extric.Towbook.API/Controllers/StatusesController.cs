using Extric.Towbook.Dispatch;
using Extric.Towbook.WebShared;
using System.Collections.Generic;
using System.Linq;
using System.Collections.ObjectModel;
using Extric.Towbook.Integration;
using Microsoft.AspNetCore.Mvc;
using Extric.Towbook.API.Models;
using System.Threading.Tasks;

namespace Extric.Towbook.API.Controllers
{
    [Route("statuses")]
    public class StatusesController : ControllerBase
    {
        /// <summary>
        /// Returns a list of statuses available to this company, for dispatching.
        /// </summary>
        /// <returns>List of Status object, containing Id, Name, FullName, Color, and DisplayOrder</returns>
        [Route("")]
        [HttpGet]
        public StatusModel[] Get()
        {
            return StatusModel.InternalGet();
        }

        //public class StatusModel
        //{
        //    public int Id { get; set; }
        //    public string Name { get; set; }
        //    public string FullName { get; set; }
        //    public string Color { get; set; }
        //    public string HexColor { get; set; }
        //    public int DisplayOrder { get; set; }
        //    public int CompanyId { get; set; }

        //    public StatusModel()
        //    {

        //    }

        //    public static StatusModel Map(Status s)
        //    {
        //        return new StatusModel()
        //        {
        //            Id = s.Id,
        //            Name = s.Name,
        //            FullName = s.ExtendedName,
        //            Color = s.HtmlColor,
        //            HexColor = s.HexColor,
        //            DisplayOrder = s.DisplayOrder,
        //            CompanyId = s.CompanyId
        //        };
        //    }

        //}
        /// <summary>
        /// Returns a list of statuses for the current user/company
        /// </summary>
        /// <returns></returns>
        public static StatusModel[] InternalGet(Company.Company[] companies = null)
        {
            return StatusModel.InternalGet(companies);
        }

        /// <summary>
        /// Get an individual status by specifying its Id.
        /// </summary>
        /// <param name="id"></param>
        /// <returns>Status object, containnig Id, Name, FullName, Color, and DisplayOrder.</returns>
        [HttpGet("{id}")]
        public async Task<object> Get(int id)
        {
            var o = await Status.GetByIdAsync(id, WebGlobal.CurrentUser.CompanyId);

            return new
            {
                Id = o.Id,
                Name = o.Name,
                FullName = o.ExtendedName,
                Color = o.HtmlColor,
                DisplayOrder = o.DisplayOrder
            };
        }
    }
}
