using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Web.Http;
using Microsoft.AspNetCore.Mvc;

namespace Extric.Towbook.API.Controllers
{
    public sealed class LedgerAccountModel
    {
        public int Id { get; set; }
        public string Name { get; set; }
    }

    [Route("ledgerAccounts")]
    public class LedgerAccountsController : ControllerBase
    {
        [HttpGet]
        [Route("")]
        public async Task<IEnumerable<LedgerAccountModel>> Get()
        {
            return LedgerAccount.GetByCompany((await this.GetCompaniesForRequestAsync()).FirstOrDefault().Id).Select(o => new LedgerAccountModel()
            {
                Id = o.Id,
                Name = o.Name
            });
        }
    }
}
