using Extric.Towbook.Accounts;
using Extric.Towbook.Impounds;
using Extric.Towbook.Integration.MotorClubs.Services;
using Extric.Towbook.Utility;
using Extric.Towbook.Web;
using static Extric.Towbook.API.ApiUtility;
using Extric.Towbook.WebShared;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;

namespace Extric.Towbook.API.Drivers.Controllers
{
    [Route("drivers")]
    [ApiController]
    public class AbsencesController : ControllerBase
    {
        public sealed class ScheduleAbsenceModel
        {
            public int Id { get; set; }
            public int DriverId { get; set; }
            public DateTime? StartDate { get; set; }
            public DateTime? EndDate { get; set; }
            public ScheduleAbsenceType Type { get; set; }
            public DateTime CreateDate { get; set; }
            public int OwnerUserId { get; set; }
            public string ReferenceId { get; set; }

            public static ScheduleAbsenceModel Map(ScheduleAbsence r)
            {
                if (r == null)
                    return null;
                return new ScheduleAbsenceModel()
                {
                    Id = r.ScheduleAbsenceId,
                    DriverId = r.DriverId,
                    StartDate = r.StartDate,
                    EndDate = r.EndDate,
                    Type = r.Type,
                    CreateDate = r.CreateDate,
                    OwnerUserId = r.OwnerUserId,
                    ReferenceId = r.ReferenceId
                };
            }

            public static ScheduleAbsence Map(ScheduleAbsenceModel model, ScheduleAbsence original)
            {
                if (original == null)
                    original = new ScheduleAbsence();
               
                original.ScheduleAbsenceId = model.Id;
                original.DriverId = model.DriverId;

                if (model.StartDate != null)
                    original.StartDate = model.StartDate.Value;

                if (model.EndDate != null) 
                    original.EndDate = model.EndDate.Value;    

                original.Type = model.Type;

                if (model.ReferenceId != null) 
                    original.ReferenceId = model.ReferenceId;


                return original;
            }
        }
        /// <summary>
        /// Retrieve all absences readings for the specified truck
        /// </summary>
        /// <param name="driverId"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("{driverId}/absences")]
        public async Task<IEnumerable<ScheduleAbsenceModel>> Get(int driverId)
        {
            var t = await Driver.GetByIdAsync(driverId);
          
            if (t == null)
            {
                throw new HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound)
                {
                    Content = new StringContent("The specified truck doesn't exist")
                });
            }

            await ThrowIfNoCompanyAccessAsync(t?.Companies);

            return (await ScheduleAbsence.GetByDriverId(driverId)).Select(o => ScheduleAbsenceModel.Map(o));
        }

        /// <summary>
        /// Retrieve an individual absence entry based on id for the specified truck.
        /// </summary>
        /// <param name="driverId"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        /// 
        [HttpGet]
        [Route("{driverId}/absences/{absenceId}")]
        public async Task<ScheduleAbsenceModel> Get(int driverId, int id)
        {
            var t = await Driver.GetByIdAsync(driverId);
            var r = await ScheduleAbsence.GetById(id);

            if (t == null || r == null || r.DriverId != t.Id)
            {
                string msg = "The specified driver doesn't exist or you don't have access to it.";

                if (t != null)
                    msg = "The absence entry you requested doesn't exist, or you don't have access to it.";

                throw new HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound)
                {
                    Content = new StringContent(msg)
                });
            }

            await ThrowIfNoCompanyAccessAsync(t?.Companies);

            return ScheduleAbsenceModel.Map(r);
        }

        /// <summary>
        /// Modify an existing odometer entry for the specified truck.
        /// </summary>
        /// <param name="driverId"></param>
        /// <param name="absenceId"></param>
        /// <param name="model"></param>
        /// <returns></returns>
        [ApiPermission(Extric.Towbook.User.TypeEnum.Manager)]
        [HttpPut]
        [Route("{driverId}/absences/{absenceId}")]
        public async Task<ScheduleAbsenceModel> Put(int driverId, int absenceId, ScheduleAbsenceModel model)
        {
            var t = await Driver.GetByIdAsync(driverId);
            var r = await ScheduleAbsence.GetById(absenceId);

            if (t == null || r == null || r.DriverId != t.Id)
            {
                string msg = "The specified driver doesn't exist or you don't have access to it.";
                if (t != null)
                    msg = "The specified absence doesn't exist, or you don't have access to the requested resource.";

                throw new HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound)
                {
                    Content = new StringContent(msg)
                });
            }

            await ThrowIfNoCompanyAccessAsync(t?.Companies);

            var tr = ScheduleAbsenceModel.Map(model, r);

            tr.OwnerUserId = WebGlobal.CurrentUser.Id;

            await tr.Save();

            // emit event to service bus to notify motor club of change

            // emit event to service bus to notify motor club of new absence
            var acc = (await Account.GetByCompanyAsync(await Company.Company.GetByIdAsync(t.CompanyId), AccountType.MotorClub))
                .FirstOrDefault(o => o.MasterAccountId == MasterAccountTypes.AaaAcg ||
                 o.MasterAccountId == MasterAccountTypes.AaaNationalFsl);

            if (acc != null)
            {
                await DigitalDispatchService.HandleOutgoingEventAsync(t.CompanyId, acc.Id,
                    tr.ToJson(),
                    DigitalDispatchService.CallEventType.ShareAbsence, WebGlobal.CurrentUser.Id);
            }

            return ScheduleAbsenceModel.Map(tr);
        }

        /// <summary>
        /// Create a new odometer entry for the specified truck.
        /// </summary>
        /// <param name="driverId"></param>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("{driverId}/absences")]
        public async Task<ScheduleAbsenceModel> Post(int driverId, ScheduleAbsenceModel model)
        {
            var t = await Driver.GetByIdAsync(driverId);

            if (t == null)
            {
                throw new HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound)
                {
                    Content = new StringContent("The specified driver doesn't exist or you don't have access to it. No changes committed.")
                });
            }

            await ThrowIfNoCompanyAccessAsync(t?.Companies);

            var tr = ScheduleAbsenceModel.Map(model, new ScheduleAbsence());

            tr.DriverId = driverId;
            tr.OwnerUserId = WebGlobal.CurrentUser.Id;

            await tr.Save();

            // emit event to service bus to notify motor club of new absence
            var acc = (await Account.GetByCompanyAsync(await Company.Company.GetByIdAsync(t.CompanyId), AccountType.MotorClub))
                .FirstOrDefault(o => o.MasterAccountId == MasterAccountTypes.AaaAcg ||
                    o.MasterAccountId == MasterAccountTypes.AaaNationalFsl);

            if (acc != null)
            {
                await DigitalDispatchService.HandleOutgoingEventAsync(t.CompanyId, acc.Id,
                    tr.ToJson(),
                    DigitalDispatchService.CallEventType.ShareAbsence, WebGlobal.CurrentUser.Id);
            }

            return ScheduleAbsenceModel.Map(tr);
        }

        [HttpDelete]
        [Route("{driverId}/absences/{absenceId}")]
        public async Task<HttpResponseMessage> Delete(int driverId, int absenceId)
        {
            var t = await Driver.GetByIdAsync(driverId);
            var r = await ScheduleAbsence.GetById(absenceId);

            if ((r != null && t != null && r.DriverId != t.Id) || t == null)
            {
                string msg = "The specified driver doesn't exist or you don't have access to it.";
                
                throw new HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound)
                {
                    Content = new StringContent(msg)
                });
            }

            await ThrowIfNoCompanyAccessAsync(t?.Companies);

            if (r != null)
            {
                await r.Delete(WebGlobal.CurrentUser.Id);

                if (r.StartDate > DateTime.Now)
                    r.EndDate = r.StartDate.AddMinutes(1); // DateTime.Now;
                else
                    r.EndDate = DateTime.Now;

                // emit event to service bus to notify motor club of change

                // emit event to service bus to notify motor club of new absence
                var acc = (await Account.GetByCompanyAsync(await Company.Company.GetByIdAsync(t.CompanyId), AccountType.MotorClub))
                    .FirstOrDefault(o => o.MasterAccountId == MasterAccountTypes.AaaAcg ||
                        o.MasterAccountId == MasterAccountTypes.AaaNationalFsl);

                if (acc != null)
                {
                    await DigitalDispatchService.HandleOutgoingEventAsync(t.CompanyId, acc.Id,
                        r.ToJson(),
                        DigitalDispatchService.CallEventType.ShareAbsence, WebGlobal.CurrentUser.Id);
                }


            }

            return new HttpResponseMessage(HttpStatusCode.OK);
        }
    }
}
