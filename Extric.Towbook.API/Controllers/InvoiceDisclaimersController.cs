using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using Extric.Towbook.API.Models;
using Extric.Towbook.Company;
using Extric.Towbook.WebShared;
using Microsoft.AspNetCore.Mvc;

namespace Extric.Towbook.API.Controllers
{

    [Route("invoiceDisclaimers")]
    public class InvoiceDisclaimersController : ControllerBase
    {
        /// <summary>
        /// Retrieve invoice disclaimers (Also used for signature capture text) for the current user. Optionally specify the <paramref name="reasonId"/>. 
        /// </summary>
        /// <param name="reasonId"></param>
        /// <param name="accountId"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("")]
        public async Task<IEnumerable<InvoiceDisclaimerModel>> Get([FromQuery] int? reasonId = null, [FromQuery] int? accountId = null)
        {
            return InvoiceDisclaimer.GetByCompanyId(
                await this.GetCompaniesForRequestAsync(), reasonId.GetValueOrDefault(), accountId, null)
                .Select(o => InvoiceDisclaimerModel.Map(o));
        }

        /// <summary>
        /// Retrieve invoice disclaimers - specify whether to get impound disclaimers.
        /// </summary>
        /// <param name="impoundDisclaimers"></param>
        /// <returns></returns>
        [NonAction]
        public IEnumerable<InvoiceDisclaimerModel> Get(bool impoundDisclaimers)
        {
            return InvoiceDisclaimer.GetByCompanyId(new[] { WebGlobal.CurrentUser.Company }.ToArray(), impoundDisclaimers)
                 .Select(o => InvoiceDisclaimerModel.Map(o));
        }

        /// <summary>
        /// Retrieve the specified Disclaimer. Throws a 404 exception if it doesn't exist or the user doesn't have access to it.
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        //routes.MapHttpRoute(
        //    name: "DefaultApiGet",
        //    routeTemplate: "{controller}/{id}",
        //    defaults: new { id = RouteParameter.Optional, action = "Get" },
        //    constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "GET" }),
        //                       id = new RestStyleConstraint() }
        //    ).DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };
        [HttpGet]
        [Route("{id}")]
        public InvoiceDisclaimerModel Get(int id)
        {
            var disclaimer = InvoiceDisclaimer.GetById(id);
            
            if (disclaimer == null || !WebGlobal.CurrentUser.HasAccessToCompany(disclaimer.CompanyId))
                throw new Extric.Towbook.Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound)
                {
                    Content = new StringContent("The specified disclaimer either doesn't exist or you don't have access to it.")
                });

            return InvoiceDisclaimerModel.Map(disclaimer);
        }

        //routes.MapHttpRoute(
        //    name: "DefaultApiPostSingle",
        //    routeTemplate: "{controller}/{action}",
        //    defaults: new { action = "Post" },
        //    constraints: new
        //    {
        //        httpMethod = new HttpMethodConstraint(new string[] { "POST" }),
        //    }
        //    ).DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };
        [Route("")]
        [HttpPost]
        public async Task<object> Post(InvoiceDisclaimerModel model)
        {
            var disc = new InvoiceDisclaimer() { CompanyId = WebGlobal.CurrentUser.CompanyId };

            if (model.Disclaimer != null)
                disc.Disclaimer = model.Disclaimer;
            else
                throw new Exception("You must specify a disclaimer value when POST'ing.");

            if (model.ReasonId != null)
                disc.ReasonId = model.ReasonId;

            if (model.Impound != null)
                disc.Impound = model.Impound.Value;

            await disc.Save();

            return new { Id = disc.Id };
        }

        //routes.MapHttpRoute(
        //    name: "DefaultApiPut",
        //    routeTemplate: "{controller}/{id}",
        //    defaults: new { id = RouteParameter.Optional, action = "Put" },
        //    constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "PUT" }),
        //                       id = new RestStyleConstraint() })
        //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };
        [Route("{id}")]
        [HttpPut]
        public async Task<object> Put(int id, InvoiceDisclaimerModel model)
        {
            var disc = InvoiceDisclaimer.GetById(id);

            if (disc == null || !WebGlobal.CurrentUser.HasAccessToCompany(disc.CompanyId))
                throw new Extric.Towbook.Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound)
                {
                    Content = new StringContent("The specified disclaimer either doesn't exist or you don't have access to it.")
                });


            if (model.Disclaimer != null)
                disc.Disclaimer = model.Disclaimer;

            if (model.ReasonId != null)
                disc.ReasonId = model.ReasonId;

            if (model.Impound != null)
                disc.Impound = model.Impound.Value;

            await disc.Save();

            return new { Id = disc.Id };
        }

        //routes.MapHttpRoute(
        //    name: "DefaultApiDelete",
        //    routeTemplate: "{controller}/{id}",
        //    defaults: new { id = RouteParameter.Optional, action = "Delete" },
        //    constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "DELETE" }),
        //                       id = new RestStyleConstraint(true) })
        //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };
        [Route("{id}")]
        [HttpDelete]
        public async Task<HttpResponseMessage> Delete(int id)
        {
            var disc = InvoiceDisclaimer.GetById(id);

            if (disc != null && !WebGlobal.CurrentUser.HasAccessToCompany(disc.CompanyId))
                throw new Extric.Towbook.Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound)
                {
                    Content = new StringContent("The specified disclaimer either doesn't exist or you don't have access to it.")
                });

            if (disc == null)
                return null;


            await disc.Delete();

            return new HttpResponseMessage() { 
                StatusCode = HttpStatusCode.OK, 
                Content = new StringContent("Disclaimer successfully deleted")
            };
        }

    }
}
