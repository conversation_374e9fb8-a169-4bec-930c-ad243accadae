using Extric.Towbook.Chat;
using Extric.Towbook.Integration;
using Extric.Towbook.WebShared;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Extric.Towbook.API
{
    internal static class ChatUtility
    {

        public static bool HasAccessRoleToCallChats()
        {
            if (WebGlobal.CurrentUser == null)
                return false;

            var hasAccess = false;

            if (WebGlobal.CurrentUser.Type == User.TypeEnum.Manager)
                hasAccess = true;

            if (WebGlobal.CurrentUser.Type == User.TypeEnum.Dispatcher)
                hasAccess = true;

            var kv = CompanyKeyValue.GetByCompanyId(WebGlobal.CurrentUser.CompanyId, Provider.Towbook.ProviderId, "Chat_IncludeAccountantsAsManagers").FirstOrDefault();
            if (kv != null && kv.Value == "1" && WebGlobal.CurrentUser.Type == User.TypeEnum.Accountant)
                hasAccess = true;

            return hasAccess;
        }

        public static async Task<bool> HasAccessRoleToCallChatsAsync()
        {
            if (WebGlobal.CurrentUser == null)
                return false;

            var hasAccess = WebGlobal.CurrentUser.Type == User.TypeEnum.Manager
                || WebGlobal.CurrentUser.Type == User.TypeEnum.Dispatcher;

            if (!hasAccess)
            {
                var kv = (await CompanyKeyValue.GetByCompanyIdAsync(
                    WebGlobal.CurrentUser.CompanyId,
                    Provider.Towbook.ProviderId,
                    "Chat_IncludeAccountantsAsManagers"
                )).FirstOrDefault();

                if (kv?.Value == "1" && WebGlobal.CurrentUser.Type == User.TypeEnum.Accountant)
                    hasAccess = true;
            }

            return hasAccess;
        }

    }
}
