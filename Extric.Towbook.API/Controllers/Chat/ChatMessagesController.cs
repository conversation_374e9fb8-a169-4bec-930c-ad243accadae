using Microsoft.AspNetCore.Mvc;
using Extric.Towbook.API.Models.Chats;
using Extric.Towbook.Chat;
using Extric.Towbook.WebShared;
using System.Collections.Generic;
using System.Linq;
using static Extric.Towbook.API.ChatUtility;

namespace Extric.Towbook.API.Controllers
{
    using System.Net;
    using System.Net.Http;
    using System.Threading.Tasks;
    using static ApiUtility;

    [Route("chats")]
    public class ChatMessagesController : ControllerBase
    {

        [HttpGet("{chatId}/messages")]        
        public async Task<IEnumerable<ChatMessageModel>> GetAsync(int chatId, [FromQuery] int? page = 1, [FromQuery] int? pageSize = 15)
        {
            var chat = await Chat.Chat.GetByIdAsync(chatId);
            
            await ThrowIfNoCompanyAccessAsync(chat?.CompanyId, "chat");

            var cms = await ChatMember.GetByChatIdAsync(chat.Id);

            if (cms.All(o => o.UserId != CurrentUser.Id) && chat.ChatTypeId != (int)ChatTypes.Company)
            {
                // Allow the owner of the chat who is not a participant to see the list of members (and dispatchers and managers)
                if (chat.OwnerUserId != CurrentUser.Id && cms.All(w => w.OwnerUserId != CurrentUser.Id) &&
                    !(chat.DispatchEntries.Any() && await HasAccessRoleToCallChatsAsync()))
                {
                    throw new Extric.Towbook.Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound)
                    {
                        Content = new StringContent($"The chat you tried to access cannot be retrieved; you're not a member of this chat.")
                    });
                }
            }

            return ChatMessage.GetByChatId(chatId, page ?? 1, pageSize ?? 15).Select(o => ChatMessageModel.Map(o));
        }

        /// <summary>
        /// Retrieve a message by the specified ID. 
        /// 
        /// When a user retrieves the message, we mark it as delivered to that user internally.
        /// </summary>
        /// <param name="chatId"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        /// 
        //routes.MapHttpRoute(
        //    name: "ChatMessages",
        //    routeTemplate: "chats/{chatId}/messages/{id}/{action}",
        //    defaults: new { controller = "ChatMessages", id = RouteParameter.Optional })
        //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace} } };
        [HttpGet]
        [Route("{chatId}/messages/{id}/get")]
        public async Task<ChatMessageModel> GetAsync(long chatId, long id)
        {
            var cm = ChatMessage.GetById(chatId, id);
            
            var chat = await Chat.Chat.GetByIdAsync(cm.ChatId);
            
            await ThrowIfNoCompanyAccessAsync(chat.CompanyId);


            return ChatMessageModel.Map(cm);
        }

        /// <summary>
        /// Used to send a message from the current user 
        /// </summary>
        /// <param name="chatId"></param>
        /// <param name="model"></param>
        /// <returns></returns>
        /// 
        //routes.MapHttpRoute(
        //    name: "ChatMessagesActionOnly",
        //    routeTemplate: "chats/{chatId}/messages",
        //    defaults: new { controller = "ChatMessages" })
        //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };
        [HttpPost]
        [Route("{chatId}/messages")]
        public async Task<object> Post(
            int chatId,
            ChatMessageModel model)
        {
            var chat = await Chat.Chat.GetByIdAsync(chatId);

            await ThrowIfNoCompanyAccessAsync(chat?.CompanyId, "chat");

            if (chat == null || chat.Deleted)
            {
                return new HttpResponseMessage(System.Net.HttpStatusCode.Forbidden)
                {
                    Content = new StringContent(
                        "This conversation is deleted. You cannot send a message to a deleted conversation.")
                };
            }

            if (string.IsNullOrWhiteSpace(model.Message))
                return NoContent();

            var membership = (await ChatMember
                .GetByChatIdAsync(chat.Id))
                .FirstOrDefault(o => o.UserId == WebGlobal.CurrentUser.Id);

            if ((membership == null || membership.Muted) && WebGlobal.CurrentUser.Type == Towbook.User.TypeEnum.Driver)
            {
                return new HttpResponseMessage(System.Net.HttpStatusCode.Forbidden)
                {
                    Content = new StringContent("You are not a part of this chat.")
                };
            }

            // since we have a callId passed...we can assume the first chat in the list is the call chat

            int roleId = ChatMember.BUILTIN_CHATMEMBER_ROLE_PARTICIPANT;
            var chatMembers = await ChatMember.GetByChatIdAsync(chat.Id);
            if (chatMembers.Any())
                roleId = ChatMember.BUILTIN_CHATMEMBER_ROLE_DISPATCHER;

            // check and add this user as participant
            if (chatMembers.All(o => o.UserId != WebGlobal.CurrentUser.Id))
            {
                // Add new participant
                var participant = new ChatMember();
                participant.ChatId = chat.Id;
                participant.OwnerUserId = WebGlobal.CurrentUser.Id;
                participant.Muted = false;
                participant.RoleId = roleId;
                participant.UserId = WebGlobal.CurrentUser.Id;
                participant.Save();
            }

            var cm = ChatMessageModel.Map(model);
            cm.ChatId = chatId;
            cm.SenderUserId = WebGlobal.CurrentUser.Id;
            await cm.Save();

            return Created(cm.Id.ToString(), new {cm.Id});
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="chatId"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        /// 
        [HttpPost("{chatId}/messages/{id}/delivered")]
        public StatusCodeResult Delivered(int chatId, int id)
        {
            ChatMessage.MarkDelivered(chatId, id, WebGlobal.CurrentUser.Id);

            return NoContent();
        }


        [HttpPut("{chatId}/messages/{id}/read")]
        public async Task<StatusCodeResult> Read(int chatId, int id)
        {
            await ChatMessage.MarkRead(chatId, id, WebGlobal.CurrentUser.Id);

            if (WebGlobal.CurrentUser.Type == Towbook.User.TypeEnum.Manager ||
                WebGlobal.CurrentUser.Type == Towbook.User.TypeEnum.Dispatcher)
                ChatMessage.SetReadStateByChatId(chatId);

            return NoContent();
        }
    }
}
