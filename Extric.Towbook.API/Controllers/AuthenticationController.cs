using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Mail;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using Extric.Towbook.Company.Security;
using Extric.Towbook.Integration;
using Extric.Towbook.Platform;
using Extric.Towbook.SSO;
using Extric.Towbook.Utility;
using Extric.Towbook.WebShared;
using ITfoxtec.Identity.Saml2;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.NotificationHubs;
using Newtonsoft.Json;
using NLog;

namespace Extric.Towbook.API.Controllers
{
    public class Notifications
    {
        public static Notifications Instance = new Notifications();

        public NotificationHubClient Hub { get; set; }

        private Notifications()
        {
            Hub = NotificationHubClient.CreateClientFromConnectionString(
                "Endpoint=sb://towbook.servicebus.windows.net/;SharedAccessKeyName=DefaultFullSharedAccessSignature;SharedAccessKey=83bZjnY/O89ZFw942Rb7tJYypzvtvKxsL2LNIp/7wrw=",
                "general");
        }
    }

    [Route("authentication")]
    public class AuthenticationController : ControllerBase
    {
        private static readonly Logger logger = LogManager.GetCurrentClassLogger();
        private static readonly NotificationHubClient hub = NotificationHubHelper.GeneralChannelInstance.Hub;
        private static readonly NotificationHubClient hubFirebase = NotificationHubHelper.GeneralChannelInstanceFirebase.Hub;

        private static string GetSsoApiTokenKey(string apiToken) => $"sso:api-token:{apiToken}";

        public AuthenticationController()
        {

        }

        /// <summary>
        /// Access this method to login from an API client. 
        /// </summary>
        /// <param name="androidId">ANDROID_ID of device. Different from urban airship. For error tracking. Optional.</param>
        /// <param name="registrationHandle">Notification Hub registration handle - for either iOS or Android.</param>
        /// <param name="androidClientVersion">The git SHA-1 of the android client executing the login request. Optional.</param>
        /// <param name="iosClientVersion">The git SHA-1 of the iOS client executing the login request. Optional.</param>
        /// <param name="returnUrl">The original Return URL. Optional.</param>
        /// <returns>Headers: 
        ///     X-Towbook-Token-Expires - DateTime representing when the token will 
        ///         expire and will need to be renewed
        ///     X-Towbook-Registered-iOS - informational string letting you know that 
        ///         an iOS key was registered in this request. (includes last 4 chars 
        ///         of token received)
        ///     X-Towbook-Registered-Android - informational string letting you know 
        ///     that an iOS key was registered in this request. (includes last 4 chars 
        ///     of token received)
        ///     
        ///     Body: Returns a token string that can be used to access API methods. Pass it to subsequent requests in the X-Api-Token header.
        /// </returns>
        /// <remarks>
        /// You don't need to specify the iOS or Android token when reauthenticating, if
        /// you do it will have no negative effect though, the token will simply be ignored. 
        /// </remarks>
        [HttpGet]
        [Route("")]
        public async Task<HttpResponseMessage> Get(
            [FromQuery] string androidId = null,
            [FromQuery] string registrationHandle = null,
            [FromQuery] string androidClientVersion = null,
            [FromQuery] string iosClientVersion = null,
            [FromQuery] string returnUrl = null)
        {
            HttpResponseMessage response;

            if (this.Request.Headers.HasAuthorization())
            {
                await EnsureRateLimit(this.GetRequestingIp());

                string[] values = ParseAuthorizationHeader(this.Request.Headers.AuthorizationParameter());

                if (values != null)
                {
                    if (values[0].Length > 64)
                    {
                        response = new HttpResponseMessage(HttpStatusCode.Unauthorized);
                        response.Headers.Add("WWW-Authenticate",
                                                       "Basic Realm=\"" + "Towbook" + "\"");
                        return response;
                    }

                    var isEmail = Core.IsEmailValid(values[0]);
                    if (isEmail)
                    {
                        var relayState = (!string.IsNullOrEmpty(androidClientVersion) || !string.IsNullOrEmpty(iosClientVersion))
                            ? new SSORelayState(returnUrl, true)
                            : new SSORelayState(returnUrl, false);

                        response = await SsoResponseAsync(values[0], relayState.GetRelayStateEncoded());
                        if (response != null)
                        {
                            await EnsureRateLimit(this.GetRequestingIp(), true);
                            return response;
                        }
                    }
                    if (!string.IsNullOrEmpty(values[1]))
                    {
                        var x = Towbook.User.GetByUsernamePassword(values[0], values[1]);
                        response = (await InternalLogin(x, values, androidId, registrationHandle, androidClientVersion, iosClientVersion)).Response;

                        if (response != null)
                        {
                            await EnsureRateLimit(this.GetRequestingIp(), true);
                            return response;
                        }
                    }
                }
            }

            response = new HttpResponseMessage(HttpStatusCode.Unauthorized);

            // not authorized:
            response.Headers.Add("WWW-Authenticate",
                "Basic Realm=\"" + "Towbook" + "\"");

            return response;
        }

        private async Task<(HttpResponseMessage Response, string Token)> InternalLogin(User x, string[] values,
             string androidId = null,
             string registrationHandle = null,
             string androidClientVersion = null,
             string iosClientVersion = null,
             int minsExpiration = 0)
        {
            var logEvent = new LogEventInfo();

            logEvent.LoggerName = logger.Name;
            logEvent.Message = "Authentication Request";
            logEvent.Level = LogLevel.Info;
            logEvent.TimeStamp = DateTime.Now;
            logEvent.Properties["headers"] = Request.Headers;

            HttpResponseMessage response = null;
            try
            {
                // workaround to fix problem of android devices DDOS'ing us because of a bug that ignored
                // bad passwords. this way, we return an HTTP 200 so the client stops retrying.
                if (x == null || x.Disabled || x.Deleted || x.Company == null)
                {
                    if (HttpContext.IsAndroidDevice() || androidId != null)
                    {
                        var tempUser = Towbook.User.GetByUsername(values[0]);
                        if (tempUser != null)
                        {
                            if (tempUser.Deleted || tempUser.Disabled)
                            {
                                response = new HttpResponseMessage(HttpStatusCode.OK);

                                response.Content = new StringContent("WORKAROUND:DISABLED_OR_DELETED_USER");
                                response.Headers.Add("X-Towbook-Token-Expires", DateTime.Now.AddDays(7).ToString());
                                response.Headers.Add("X-Towbook-Company-Id", "0");
                                response.Headers.Add("X-Towbook-Company-Name", "");
                                response.Headers.Add("X-Towbook-Company-Type", "");
                                response.Headers.Add("X-Towbook-Username", "");
                                response.Headers.Add("X-Towbook-User-Type", "Disabled");

                                return (response, string.Empty);
                            }

                            if (tempUser.Password != values[1])
                            {
                                response = new HttpResponseMessage(HttpStatusCode.BadRequest);

                                response.Content = new StringContent("");
                                response.Headers.Add("X-Towbook-Token-Expires", DateTime.Now.AddDays(7).ToString());
                                response.Headers.Add("X-Towbook-Company-Id", "0");
                                response.Headers.Add("X-Towbook-Company-Name", "");
                                response.Headers.Add("X-Towbook-Company-Type", "");
                                response.Headers.Add("X-Towbook-Username", "");
                                response.Headers.Add("X-Towbook-User-Type", "BadPassword");

                                return (response, string.Empty);
                            }
                        }
                    }
                }


                if (x != null && x.Id > 0 && x.Deleted == false && x.Disabled == false && x.Company != null)
                {
                    ClientVersion cv = null;

                    var utc = false;
                    if (Request.Headers.Where(o =>
                        o.Key.ToLowerInvariant() == "x-timezone-request" &&
                        o.Value.FirstOrDefault()?.ToLowerInvariant() == "utc").Any())
                    {
                        if (!string.IsNullOrEmpty(androidClientVersion))
                        {
                            androidClientVersion += "|UTC";
                            utc = true;
                        }
                        else if (!string.IsNullOrEmpty(iosClientVersion))
                        {
                            iosClientVersion += "|UTC";
                            utc = true;
                        }
                    }

                    if (!string.IsNullOrEmpty(androidClientVersion))
                    {
                        cv = ClientVersion.GetByGitHash(androidClientVersion, ClientVersionType.Android);
                        // workaround to force android to use UTC -- there is a bug in it that causes it not to send UTC header
                        // when requesting a token inside the photo upload process.
                        if ((cv == null || cv.Id > 348) && !utc)
                        {
                            androidClientVersion += "|UTC";
                            utc = true;
                            cv = ClientVersion.GetByGitHash(androidClientVersion, ClientVersionType.Android);
                        }
                    }
                    else if (!string.IsNullOrEmpty(iosClientVersion))
                        cv = ClientVersion.GetByGitHash(iosClientVersion, ClientVersionType.Iphone);
                    else if (Request.Headers.Where(o => o.Key == "client" && o.Value.FirstOrDefault() == "towbook-agent").Any())
                        cv = ClientVersion.GetByGitHash("towbook-agent", ClientVersionType.Agent);
                    else
                        cv = ClientVersion.GetByGitHash(null, ClientVersionType.Internal);

                    var primaryCompany = await Company.Company.GetByIdAsync(x.PrimaryCompanyId);
                    var securityToolSettings = await primaryCompany.HasFeatureAsync(Generated.Features.SecurityTools)
                        ? SecurityToolSettings.GetByCompanyId(x.PrimaryCompanyId)
                        : null;

                    if (securityToolSettings != null)
                    {
                        // If mobile
                        if (iosClientVersion != null || androidClientVersion != null)
                        {
                            if (securityToolSettings.PreventDispatchersSigningInOnMobile 
                                && x.Type == Extric.Towbook.User.TypeEnum.Dispatcher)
                            {
                                StringContent content = new StringContent(JsonConvert.SerializeObject(new
                                {
                                    title = "SECURITY_TOOLS_BLOCK",
                                    message = "Dispatchers are blocked from signing in on mobile by your company's settings",
                                }));

                                response = new HttpResponseMessage(HttpStatusCode.Forbidden)
                                {
                                    Content = content
                                };
                                return (response, string.Empty);
                            }
                        }
                    }

                    var iosV2 = false;

                    try
                    {
                        if (iosClientVersion != null)
                        {
                            if (iosClientVersion.Contains("|"))
                                iosClientVersion = iosClientVersion.Substring(0, iosClientVersion.IndexOf("|"));

                            if (iosClientVersion.Contains("("))
                                iosClientVersion = iosClientVersion.Substring(0, iosClientVersion.IndexOf('('));

                            if (Version.TryParse(iosClientVersion, out var vp))
                            {
                                if (vp.Major > 1)
                                    iosV2 = true;
                            }
                        }
                    }
                    catch
                    {

                    }

                    string platform = "";
                    if (androidClientVersion != null)
                        platform = "gcm";
                    else if (iosClientVersion != null)
                        platform = "apns";

                    logEvent.Properties["version"] = cv.Id + "|" + cv.ReleaseDate.ToJson().Replace("\"", "").Replace("'", "");
                    logEvent.Properties["type"] = cv.Type;
                    logEvent.Properties["commitId"] = cv.GitHash;

                    if (cv.Id < 3500 && cv.Type == ClientVersionType.Android && x.Company.Id > 187000)
                    {
                        response = new HttpResponseMessage(HttpStatusCode.OK);

                        response.Content = new StringContent("WORKAROUND:DISABLED_OR_DELETED_USER");
                        response.Headers.Add("X-Towbook-Token-Expires", DateTime.Now.AddDays(7).ToString());
                        response.Headers.Add("X-Towbook-Company-Id", "0");
                        response.Headers.Add("X-Towbook-Company-Name", "");
                        response.Headers.Add("X-Towbook-Company-Type", "");
                        response.Headers.Add("X-Towbook-Username", "");
                        response.Headers.Add("X-Towbook-User-Type", "Disabled");

                        return (response, string.Empty);
                    }

                    var t = await x.GetTokenAsync(cv.Id, registrationHandle, minsExpiration);

                    logEvent.Properties["token"] = t;



                    response = new HttpResponseMessage(HttpStatusCode.OK);
                    response.Content = new StringContent(t.Token);
                    response.Headers.Add("X-Towbook-Token-Expires", t.ExpirationDate.ToUniversalTime().ToString());
                    response.Headers.Add("x-towbook-token-expires-utc", DateTime.SpecifyKind(t.ExpirationDate.ToUniversalTime(), DateTimeKind.Utc).ToString("o"));

                    if (!response.Headers.Contains("X-Towbook-Company-Id"))
                        response.Headers.Add("X-Towbook-Company-Id", x.CompanyId.ToString());

                    if (!response.Headers.Contains("X-Towbook-Company-Type"))
                        response.Headers.Add("X-Towbook-Company-Type", x.Company.Type.ToString());

                    if (!response.Headers.Contains("X-Towbook-Company-Name"))
                        response.Headers.Add("X-Towbook-Company-Name", x.Company.Name.ToString());

                    if (utc)
                        response.Headers.Add("X-Timezone-Response", "UTC");

                    if (!string.IsNullOrWhiteSpace(androidId))
                    {
                        x.AddKey("android_id", androidId, "rest login");
                        response.Headers.Add("X-Towbook-Registered-Android-Device-Id", "confirmed,**" + (androidId.Length > 4 ? androidId.Substring(androidId.Length - 4) : androidId));
                    }

                    if (Request.Headers.Any(o => o.Key == "client" && o.Value.FirstOrDefault() == "towbook-agent"))
                    {
                        var ags = await Agent.Session.GetByCompanyIdAsync(x.CompanyId);
                        if (ags != null)
                        {
                            response.Headers.Add("X-Towbook-Agent-Session-Id", ags.Id.ToString());
                            if (ags.QuickBooksFilename != null)
                                response.Headers.Add("X-Towbook-Agent-QuickBooks-Filename", ags.QuickBooksFilename);
                        }
                        else
                        {
                            if (await x.Company.HasFeatureAsync(Generated.Features.QuickBooks))
                            {
                                if (CompanyKeyValue.GetFirstValueOrNull(
                                    x.CompanyId, Provider.QuickBooks.ProviderId, "DataSource") != "QBO")
                                {
                                    ags = new Agent.Session();
                                    ags.CompanyId = x.CompanyId;
                                    ags.OwnerUserId = x.Id;
                                    ags.Save();
                                    response.Headers.Add("X-Towbook-Agent-Session-Id", ags.Id.ToString());
                                    if (ags.QuickBooksFilename != null)
                                        response.Headers.Add("X-Towbook-Agent-QuickBooks-Filename", ags.QuickBooksFilename);
                                }

                            }
                        }
                    }

                    var serverUrl = UserKeyValue.GetByUser(x.CompanyId, x.Id, Provider.Towbook.ProviderId, "ServerUrl").FirstOrDefault()?.Value;

                    // AT&T users in GA constantly report problems connecting when they're pointed at a Frontdoor instance. 
                    // Need to figure out why, but in mean time we won't direct users in GA to the frontdoor address (api.tow-book.com).
                    // Verified it is not a DNS issue. 
                    if (string.IsNullOrWhiteSpace(serverUrl)
                        && x.Company?.State != "GA"
                        && x.Company?.State != "AL")
                        serverUrl = "https://api.tow-book.com";

                    response.Headers.Add("X-Towbook-Server-Url", serverUrl);
                    //response.Headers.Add("X-Towbook-Server-Url", "https://api.towbook.com");

                    if (Core.GetAppSetting("PushNotifications:Disable") != "1")
                    {
                        bool disablePush = false;

                        if (platform == "apns" &&
                            Core.GetAppSetting("PushNotifications:DisableIOS") == "1")
                            disablePush = true;

                        if (!disablePush && !string.IsNullOrWhiteSpace(registrationHandle) && registrationHandle != "(null)")
                        {
                            x.AddKey("registration", registrationHandle, "Notification Hub via Login");
                            response.Headers.Add("X-Towbook-Registered-Android-Device-Id", "confirmed,**" + registrationHandle.Substring(registrationHandle.Length - 4));

                            string newRegistrationId = null;

                            if (Core.GetAppSetting("PushNotifications:DisableGetRegistrations") != "1")
                            {
                                var registrations = await hub.GetRegistrationsByChannelAsync(registrationHandle, 100);

                                foreach (RegistrationDescription r in registrations)
                                {
                                    if (r.RegistrationId == registrationHandle)
                                    {
                                        newRegistrationId = r.RegistrationId;
                                    }
                                    else
                                    {
                                        await hub.DeleteRegistrationAsync(r);
                                    }
                                }
                            }

                            x.AddKey("notificationhub_iphone_new", iosV2.ToString(), "Notification Hub via Login");

                            // Important: The client (iOS/Android/Windows Phone) needs this to finish the registration.
                            response.Headers.Add("X-Registration-Id", newRegistrationId);

                            RegistrationDescription registration = null;
                            switch (platform)
                            {
                                case "apns":
                                    registration = new AppleRegistrationDescription(registrationHandle);
                                    break;
                                case "fcm":
                                case "gcm":
                                    registration = new FcmV1RegistrationDescription(registrationHandle);
                                    break;
                                default:
                                    throw new Extric.Towbook.Web.HttpResponseException(HttpStatusCode.BadRequest);
                            }

                            var username = Core.NormalizeString(x.Username);

                            // add check if user is allowed to add these tags
                            registration.Tags = new HashSet<string>
                            {
                                "user.username:" + username,
                                "user.id:" + x.Id
                            };

                            if (platform == "apns")
                            {
                                if (newRegistrationId == null)
                                    newRegistrationId = await hub.CreateRegistrationIdAsync();

                                registration.RegistrationId = newRegistrationId;
                                await hub.CreateOrUpdateRegistrationAsync(registration);
                            }
                            else if (platform == "fcm" || platform == "gcm")
                            {
                                if (newRegistrationId == null)
                                    newRegistrationId = await hubFirebase.CreateRegistrationIdAsync();
                                registration.RegistrationId = newRegistrationId;
                                await hubFirebase.CreateOrUpdateRegistrationAsync(registration);
                            }

                            x.AddKey("notificationhub_registration_id", newRegistrationId, "Notification Hub via Login");
                        }
                    }

                    string ip = this.GetRequestingIp();

                    if (!string.IsNullOrWhiteSpace(androidClientVersion))
                    {
                        if (string.IsNullOrWhiteSpace(ip))
                            ip = "0.0.0.0|" + androidClientVersion;
                        else
                            ip += "|" + androidClientVersion;
                    }

                    if (!string.IsNullOrWhiteSpace(iosClientVersion))
                    {
                        if (string.IsNullOrWhiteSpace(ip))
                            ip = "0.0.0.0|" + iosClientVersion;
                        else
                            ip += "|" + iosClientVersion;
                    }

                    Extric.Towbook.User.HistoryItem.RecordAction(
                        Extric.Towbook.User.HistoryItem.TypeEnum.ApiLogin,
                        x.Id, x.Id, ip);

                    return (response, t.Token);
                }

                return (response, string.Empty);
            }
            catch (Exception y)
            {
                logEvent.Exception = y;
                throw;
            }
            finally
            {
                logger.Log(logEvent);
            }
        }

        private const int requestsPerMinuteThreshold = 5;
        protected async Task<bool> EnsureRateLimit(string id, bool cancel = false)
        {
            try
            {

                var cacheKey = "ratelimit:" + id;

                if (cancel)
                {
                    await Core.GetRedisDatabase().KeyDeleteAsync(cacheKey);
                    return true;
                }

                var result = await Core.GetRedisDatabase().HashIncrementAsync(cacheKey, 1);

                if (result == 1)
                {
                    await Core.GetRedisDatabase().KeyExpireAsync(
                       cacheKey,
                       TimeSpan.FromSeconds(60),
                       StackExchange.Redis.CommandFlags.FireAndForget);
                }
                else if (result > requestsPerMinuteThreshold)
                {
                    throw new Web.HttpResponseException(new HttpResponseMessage((HttpStatusCode)429)
                    {
                        Content = new StringContent("You're trying to login too quickly. Please try again in a few minutes.")
                    });
                }
            }
            catch
            {
                // silently ignore redis errors for rate limiting
            }
            return true;
        }

        private async Task<HttpResponseMessage> SsoResponseAsync(string username, string relayState = "")
        {
            HttpResponseMessage response = null;

            var companySecuritySetting = CompanySecuritySetting.GetByCompanyDomain(CompanySecuritySetting.GetDomain(username));
            if (companySecuritySetting != null && companySecuritySetting.SsoEnable
                && await (await Company.Company.GetByIdAsync(companySecuritySetting.CompanyId)).HasFeatureAsync(Generated.Features.Settings_SingleSignOn))
            {
                if (companySecuritySetting.SsoProvider.SsoType.Equals("SAML"))
                {
                    var binding = new Saml2RedirectBinding();
                    var config = SSOConfiguration.GetSamlConfiguration(companySecuritySetting);
                    binding.SetRelayStateQuery(new Dictionary<string, string> { { "ReturnUrl", relayState } });
                    binding.Bind(new Saml2AuthnRequest(config));
                    response = new HttpResponseMessage(HttpStatusCode.Moved);
                    response.Headers.Add("Location", binding.RedirectLocation.OriginalString);
                }
                else
                {

                    var authUrl = SSOConfiguration.GetOIDCAuthEndpoint(companySecuritySetting);
                    var clientId = companySecuritySetting.Idp;
                    var nonce = SSOConfiguration.GenerateNonce(username);

                    response = new HttpResponseMessage(HttpStatusCode.Moved);
                    var parameters = new Dictionary<string, string>
                    {
                        { "client_id", clientId },
                        { "response_type", "id_token code" },
                        { "redirect_uri", WebGlobal.GetDomain() + "/Security/SsoCallback.aspx" },
                        { "response_mode", "form_post" },
                        { "scope", "openid profile email offline_access" },
                        { "state", relayState },
                        { "nonce", nonce },
                        { "sso_reload", "true" }
                    };
                    var encodedParameters = string.Join("&", parameters.Select(keyVal => $"{keyVal.Key}={HttpUtility.UrlEncode(keyVal.Value)}"));
                    var uriEncoded = $"{authUrl}?{encodedParameters}";
                    response.Headers.Add("Location", uriEncoded);
                }
            }
            return response;
        }

        [HttpGet]
        [Route("ssoLogin")]
        public async Task<HttpResponseMessage> SSOLogin(
            [FromQuery] string androidId = null,
            [FromQuery] string registrationHandle = null,
            [FromQuery] string androidClientVersion = null,
            [FromQuery] string iosClientVersion = null)
        {
            if (this.Request.Headers.ContainsKey("X-Towbook-SsoUser")) // && !(string.IsNullOrEmpty(androidClientVersion) && string.IsNullOrEmpty(iosClientVersion)))
            {
                var ssoHeader = this.Request.Headers["X-Towbook-SsoUser"];

                if (!string.IsNullOrEmpty(ssoHeader))
                {
                    var codeToken = SSOTokens.GetFromCache(ssoHeader);
                    if (codeToken == null) return UnauthorizedResponse();

                    var newToken = GetAccessToken(codeToken);
                    if (newToken == null) return UnauthorizedResponse();

                    newToken.Username = codeToken.Username;
                    newToken.TokenHint = codeToken.TokenHint;

                    var x = Towbook.User.GetByUsername(newToken.Username);

                    var values = x != null ? new string[] {
                            x.Username,
                            x.Password
                        } : new string[] { };

                    var minsExpiration = newToken.ExpiresIn / 60;
                    if (minsExpiration == 0 && !default(DateTimeOffset).Equals(codeToken.ValidTo))
                    {
                        TimeSpan span = codeToken.ValidTo.Subtract(DateTime.Now);
                        minsExpiration = span.Minutes;
                        newToken.ExpiresIn = minsExpiration * 60;
                    }
                    var (response, token) = await InternalLogin(x, values, androidId, registrationHandle, androidClientVersion, iosClientVersion, minsExpiration);
                    newToken.ApiToken = token;

                    Core.SetRedisValue(newToken.GetKey(), newToken.ToJson(), TimeSpan.FromDays(7));
                    Core.SetRedisValue(GetSsoApiTokenKey(token), newToken.RefreshToken, TimeSpan.FromDays(7));

                    response.Headers.Add("X-Offline-Access", newToken.RefreshToken);

                    if (response != null)
                        return response;
                }
            }

            return UnauthorizedResponse();
        }

        [HttpGet]
        [Route("ssoRefresh")]
        public async Task<HttpResponseMessage> SSORefresh(
            [FromQuery] string androidId = null,
            [FromQuery] string registrationHandle = null,
            [FromQuery] string androidClientVersion = null,
            [FromQuery] string iosClientVersion = null)
        {
            if (this.Request.Headers.ContainsKey("X-Offline-Access") && !(string.IsNullOrEmpty(androidClientVersion) && string.IsNullOrEmpty(iosClientVersion)))
            {
                var ssoHeader = Request.Headers["X-Offline-Access"];

                if (!string.IsNullOrEmpty(ssoHeader))
                {
                    var previousToken = AccessToken.GetFromCache(ssoHeader);
                    if (previousToken == null) return UnauthorizedResponse();

                    var newToken = GetTokensByRefreshToken(previousToken);
                    if (newToken == null)
                        return UnauthorizedResponse();
                    else
                    {
                        await Core.DeleteRedisKeyAsync(GetSsoApiTokenKey(previousToken.ApiToken));
                        await Core.DeleteRedisKeyAsync(previousToken.GetKey());
                    }

                    newToken.Username = previousToken.Username;
                    newToken.TokenHint = previousToken.TokenHint;

                    var x = Towbook.User.GetByUsername(newToken.Username);

                    var values = x != null ? new string[] {
                            x.Username,
                            x.Password
                        } : new string[] { };

                    var minsExpiration = newToken.ExpiresIn / 60;
                    var (response, token) = await InternalLogin(x, values, androidId, registrationHandle, androidClientVersion, iosClientVersion, minsExpiration);
                    newToken.ApiToken = token;

                    Core.SetRedisValue(newToken.GetKey(), newToken.ToJson(), TimeSpan.FromDays(7));
                    Core.SetRedisValue(GetSsoApiTokenKey(token), newToken.RefreshToken, TimeSpan.FromDays(7));

                    response.Headers.Add("X-Offline-Access", newToken.RefreshToken);

                    if (response != null)
                        return response;
                }
            }

            return UnauthorizedResponse();
        }

        private static HttpResponseMessage UnauthorizedResponse()
        {
            var response = new HttpResponseMessage(HttpStatusCode.Unauthorized);
            response.Headers.Add("WWW-Authenticate", "Basic Realm=\"" + "Towbook" + "\"");

            return response;
        }

        public class LoginModel
        {
            public string Username { get; set; }

            public string Password { get; set; }

            public string Version { get; set; }
        }

        [HttpPost]
        [Route("")]
        public async Task<HttpResponseMessage> Post(LoginModel model)
        {
            HttpResponseMessage response = null;

            if (model != null)
            {
                await EnsureRateLimit(this.GetRequestingIp());

                var x = Towbook.User.GetByUsernamePassword(model.Username, model.Password);
                var cv = ClientVersion.GetByGitHash(model.Version);

                if (x != null && x.Id > 0 && x.Deleted == false && x.Disabled == false)
                {
                    var t = await x.GetTokenAsync(cv.Id);

                    response = new HttpResponseMessage(HttpStatusCode.OK);
                    response.Content = new StringContent(t.Token);
                    response.Headers.Add("X-Towbook-Token-Expires", t.ExpirationDate.ToUniversalTime().ToString());
                    //response.Headers.Add("X-Towbook-Company-Id", x.CompanyId.ToString());
                    response.Headers.Add("X-Towbook-Company-Name", x.Company.Name.ToString());
//                    response.Headers.Add("X-Towbook-Company-Type", x.Company.Type.ToString());
                    //response.Headers.Add("X-Towbook-Username", x.Username);
                    //response.Headers.Add("X-Towbook-User-Type", x.Type.ToString());
                    response.Headers.Add("x-towbook-token-expires-utc", DateTime.SpecifyKind(t.ExpirationDate.ToUniversalTime(), DateTimeKind.Utc).ToString("o"));

                    var ip = this.GetRequestingIp();

                    Towbook.User.HistoryItem.RecordAction(
                        Towbook.User.HistoryItem.TypeEnum.ApiLogin,
                        x.Id, x.Id, ip);

                    return response;
                }
                else
                {
                    response = new HttpResponseMessage(HttpStatusCode.Forbidden);

                    if (x == null || x.Id < 1 || x.Deleted)
                        response.Content = new StringContent("Invalid username or password");
                    else if (x.Disabled)
                        response.Content = new StringContent("Your account has been disabled");

                    return response;
                }
            }

            response = new HttpResponseMessage(HttpStatusCode.Forbidden);
            await EnsureRateLimit(this.GetRequestingIp(), true);

            return response;
        }

        /// <summary>
        /// Access this method to invalidate your API token/log out.
        /// </summary>
        /// <returns>Should return a 401 Unauthorized request if the request is successful and will send a WWW-Authenticate header notifying you to login again</returns>
        [HttpDelete]
        [Route("")]
        public async Task<ActionResult> Delete()
        {
            var token = this.GetCurrentToken();

            if (token != null)
            {
                string registrationHandle = token.RegistrationHandle;

                // Revoke it!
                AuthenticationToken.RevokeByToken(token.Token);

                foreach (var hb in new[] { hub, hubFirebase })
                {
                    // delete the registration from the notification hub
                    if (!string.IsNullOrEmpty(registrationHandle) &&
                        Core.GetAppSetting("PushNotifications:Disable") != "1")
                    {
                        var registrations = await hb.GetRegistrationsByChannelAsync(registrationHandle, 100);

                        foreach (var r in registrations)
                        {
                            if (r?.Tags != null)
                            {
                                r.Tags.Clear();
                                await hb.UpdateRegistrationAsync(r);
                            }
                        }
                    }
                }

                ActionResult response = Unauthorized();

                #region Remove Refresh Token

                var key = Core.GetRedisValue($"sso:api-token:{token.Token}");
                var previousToken = AccessToken.GetFromCache(key);
                if (!(previousToken is null))
                {
                    var companySecuritySetting = CompanySecuritySetting.GetByCompanyDomain(CompanySecuritySetting.GetDomain(previousToken.Username));
                    if (companySecuritySetting.SsoProvider.SsoType.Equals("OIDC"))
                    {
                        var oktaLogoutUrl = SSOConfiguration.GetOIDCLogoutEndpoint(companySecuritySetting);

                        var hintQueryParam = companySecuritySetting.SsoProvider.Name.ToUpper().Contains("OKTA") ? "id_token_hint" : "logout_hint";
                        var redirectUrl = oktaLogoutUrl + "?" + (hintQueryParam + "=" + previousToken.TokenHint)
                            + "&post_logout_redirect_uri=" + WebGlobal.GetDomain() + "/Security/SloCallback.aspx";
                        Response.Headers.Add("Location", redirectUrl);
                    }
                    Core.DeleteRedisKey(previousToken.GetKey());
                    Core.DeleteRedisKey($"sso:api-token:{token.Token}");
                }

                #endregion Remove Refresh Token

                // not authorized:
                Response.Headers.Add("WWW-Authenticate",
                    "Basic Realm=\"" + "Towbook" + "\"");

                return response;
            }

            return StatusCode((int)HttpStatusCode.BadRequest, "");
        }

        private SSO.AccessToken GetAccessToken(SSOTokens token)
        {
            var companySecuritySetting = CompanySecuritySetting.GetByCompanyDomain(CompanySecuritySetting.GetDomain(token.Username));
            if (companySecuritySetting.SsoProvider.SsoType.Equals("SAML"))
            {
                return new AccessToken
                {
                    Username = token.Username
                };
            }

            var url = SSOConfiguration.GetOIDCTokenEndpoint(companySecuritySetting);
            var clientId = companySecuritySetting.Idp;
            var scope = "openid+profile+email+offline_access";
            var redirectUri = WebGlobal.GetDomain() + "/Security/SsoCallback.aspx"; // "http://localhost/Security/SsoCallback.aspx";
            var grantType = "authorization_code";
            var clientSecret = companySecuritySetting.Secret;

            var postData = "client_id=" + clientId +
                "&scope=" + scope +
                "&code=" + token.Code +
                "&redirect_uri=" + redirectUri +
                "&grant_type=" + grantType +
                "&client_secret=" + clientSecret;

            var accessToken = ExecutePostRequest<SSO.AccessToken>(url, postData);
            return accessToken;
        }

        private SSO.AccessToken GetTokensByRefreshToken(SSO.AccessToken token)
        {
            var companySecuritySetting = CompanySecuritySetting.GetByCompanyDomain(CompanySecuritySetting.GetDomain(token.Username));
            if (companySecuritySetting.SsoProvider.SsoType.Equals("SAML"))
            {
                return new AccessToken
                {
                    Username = token.Username,
                    ExpiresIn = token.ExpiresIn
                };
            }

            var url = SSOConfiguration.GetOIDCTokenEndpoint(companySecuritySetting);
            var clientId = companySecuritySetting.Idp;
            var scope = "openid+profile+email+offline_access";
            var redirectUri = WebGlobal.GetDomain() + "/Security/SsoCallback.aspx"; // "http://localhost/Security/SsoCallback.aspx";
            var grantType = "refresh_token";
            var clientSecret = companySecuritySetting.Secret;

            var postData = "client_id=" + clientId +
                "&scope=" + scope +
                "&refresh_token=" + token.RefreshTokenProvider +
                "&redirect_uri=" + redirectUri +
                "&grant_type=" + grantType +
                "&client_secret=" + clientSecret;

            var accessToken = ExecutePostRequest<SSO.AccessToken>(url, postData);
            return accessToken;
        }

        private static T ExecutePostRequest<T>(string urlExecute, string dataString)
        {
            var request = WebRequest.Create(urlExecute) as HttpWebRequest;
            request.Method = "POST";

            var encoding = new ASCIIEncoding();
            var data = encoding.GetBytes(dataString);

            request.ContentType = "application/x-www-form-urlencoded";
            request.ContentLength = data.Length;

            var requestStream = request.GetRequestStream();
            requestStream.Write(data, 0, data.Length);
            requestStream.Close();

            string jsonResponse = string.Empty;
            try
            {
                using (var response = request.GetResponse() as HttpWebResponse)
                {
                    var statusCode = (int)response.StatusCode;
                    if (!statusCode.Equals(200))
                    {
                        return default;
                    }
                    using (var s = response.GetResponseStream())
                    {
                        using (StreamReader sr = new StreamReader(s, Encoding.GetEncoding("utf-8")))
                        {
                            jsonResponse = sr.ReadToEnd();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                logger.Error(ex, "Error on POST request invocation ({0})", urlExecute);
            }

            return JsonConvert.DeserializeObject<T>(jsonResponse);
        }

        private string[] ParseAuthorizationHeader(string authHeader)
        {
            string[] credentials = Encoding.ASCII.GetString(Convert
                                                            .FromBase64String(authHeader))
                                                            .Split(
                                                            new[] { ':' });

            if (credentials.Length != 2 || string.IsNullOrEmpty(credentials[0]))
                return null;

            return credentials;
        }

        #region Password Recovery Methods

        public class QuestionsModel
        {
            public string Username { get; set; }
            public string EmailAddress { get; set; }
        }

        public class AnswersModel
        {
            public int Id { get; set; }
            public string Answer { get; set; }
            public string Username { get; set; }
        }

        public class ResetPasswordModel
        {
            public int UserId { get; set; }
            public string Token { get; set; }
            public string Password { get; set; }
        }

        /// <summary>
        /// Retrieves the security question available for that user, this will return the question that has LastAttempt = NULL
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("Questions")]
        public dynamic Questions(QuestionsModel model)
        {
            var user = Towbook.User.GetByUsername(model.Username);

            if (user == null)
                throw new Exception("No users were found with the given username.");

            // finds the next question registered for that user
            var question = UserSecurityQuestion.GetByUserId(user.Id);

            if (question == null)
                throw new Exception("There are no more security questions available for this user, please contact the support.");

            return new
            {
                Id = question.SecurityQuestionId,
                Question = question.Question
            };
        }

        /// <summary>
        /// Use to supply the answer to a security question. If the answer is correct, an email will be sent
        /// to the user with a link to reset their password.
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("Answers")]
        public async Task<HttpResponseMessage> Answers(AnswersModel model)
        {
            // gets the user based on username
            var user = Towbook.User.GetByUsername(model.Username);
            if (user == null)
                throw new Exception("No users were found with the given username.");

            // validate all answers provided by the user
            var sq = UserSecurityQuestion.GetBySecurityQuestionId(model.Id, user.Id);
            var isValid = sq.ValidateAnswer(model.Answer);

            // if the answer is invalid, mark the question with a last attempt and returns the error
            if (!isValid)
            {
                sq.LastAttempt = DateTime.Now;
                sq.Save();

                throw new Exception("The answer don't match the one in the database.");
            }

            // if the answer is valid, resets the LastAttempt for all security questions of that user
            UserSecurityQuestion.ResetAttempts(user.Id);

            var newToken = Guid.NewGuid();

            // creates the link between userId and the token (GUID)
            user.SetPasswordResetToken(newToken);

            // link (userId helps to prevent someone guessing the GUID)
            var url = "";
            var host = Request.Host.Value == "api.towbook.com" ? "app.towbook.com" : Request.Host.Value;

            if (Request.Host.Port != 80)
                url = string.Format("{0}://{1}:{2}/Security/ResetPassword?t={3}", Request.Scheme, host, Request.Host.Port, newToken.ToString("N"));
            else
                url = string.Format("{0}://{1}/Security/ResetPassword?t={2}", Request.Scheme, host, newToken.ToString("N"));

            // sends email with the reset password link
            // ToDo: change the subject of the e-mail to something standard
            // ToDo: change the body of the e-mail to something better
            // ToDo: verify the reply list and sender with Dan

            var body = @"
              <div>
                <p>
                    Hi {{name}},<br /><br />
                    We received a request to reset your Towbook account password.<br />
                    Please click the button below to reset it. <br/>
                </p>
                <table class=""body-action"" align=""center"" width=""100%"" cellpadding=""0"" cellspacing=""0"">
                    <tr>
                    <td align=""center"">
                        <div>
                          <a href=""{{url}}"" style=""display: inline-block;width: 200px;background-color: #3869D4;border-radius: 3px;color: #ffffff;font-size: 15px;line-height: 45px;text-align: center;text-decoration: none;-webkit-text-size-adjust: none;mso-hide: all;"">
                            Reset your password
                          </a>
                        </div>
                    </td>
                    </tr>
                </table>
                <br /><br />
                <p>
                    This link takes you to a secure page where you can change your password.<br />
                    If you don't want to reset your password, please ignore this message. Your password will not be reset.
                </p>
                <p>
                    <b>P.S.</b> We also love hearing from you and helping you with any issues you have. Please reply to this email if you want to ask a question.<br /><br />
                    Towbook Support Team | <a href=""mailto:<EMAIL>?Subject=support%20request"" target=""_top""><EMAIL></a> | (810) 320-5063<br />
                </p>
              </div>";

            body = body.Replace("{{name}}", user.FullName);
            body = body.Replace("{{url}}", url);

            using (MailMessage mm = new MailMessage())
            {

                mm.Subject = "[Towbook] Reset your password";
                mm.IsBodyHtml = true;
                //mm.Body = $@"Hi {user.FullName},\r\n\r\n<br /><br />We received a request to reset your Towbook account password. Please click the following link to change your password:\r\n\r\n<a href='{url}'>Reset Password</a>\r\n\r\n<br /><br />" + 
                //    "If you have any questions, please let us know.<br /><br />Towbook Support Team<br />(810) 320-5063";
                mm.Body = body;

                mm.From = new MailAddress("<EMAIL>", "Towbook");
                mm.To.Add(new MailAddress(user.Email, user.FullName));

                using (SmtpClient sc = new SmtpClient().Get())
                {
                    await sc.Send(mm, user, "Password Reset", EmailType.PasswordReset, user.Id);
                }

            }
            return new HttpResponseMessage(HttpStatusCode.OK);
        }

        [HttpPost]
        [Route("ResetPassword")]
        public async Task<HttpResponseMessage> ResetPassword(ResetPasswordModel model)
        {
            var wa = await Towbook.User.GetByResetPasswordTokenAsync(new Guid(model.Token));

            if (wa == null)
                throw new Exception("No users found for the given id.");

            wa.Password = model.Password;
            await wa.Save();

            return new HttpResponseMessage(HttpStatusCode.OK);
        }

        #endregion
    }
}
