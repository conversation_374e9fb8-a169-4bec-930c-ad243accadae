using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using Extric.Towbook.WebShared;
using Extric.Towbook.API.Models;
using Extric.Towbook.Licenses;
using System.Collections.ObjectModel;
using System.Threading.Tasks;
using Extric.Towbook.API.PreTripInspections.Models;
using Extric.Towbook.PreTripInspections;
using static Extric.Towbook.API.ApiUtility;
using Extric.Towbook.Utility;
using Microsoft.AspNetCore.Mvc;

namespace Extric.Towbook.API.Controllers
{
    [Route("trucks")]
    public class TrucksController : ControllerBase
    {
        /// <summary>
        /// Retrieve a list of trucks with minimal information by default. Specify full=true to return full details.
        /// </summary>
        /// <param name="full"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<IActionResult> Get(bool full = false, bool? includeDeleted = false)
        {

            if (full == true)
            {
                IEnumerable<Truck> trucks = new List<Truck>();

                foreach (var c in await this.GetCompaniesForRequestAsync())
                    trucks = trucks.Union(await Truck.GetByCompanyAsync(c, includeDeleted));

                return Ok(TruckModel.Map(trucks));
            }
            else
            {
                return Ok(await InternalGetTrucks(await this.GetCompaniesForRequestAsync(), includeDeleted));
            }
        }

        public static async Task<IEnumerable<InternalTruckModel>> InternalGetTrucks(Company.Company[] companies = null, bool? includeDeleted = false)
        {
            IEnumerable<Truck> x = new Collection<Truck>();

            foreach (var company in (companies ?? new Company.Company[] { WebGlobal.CurrentUser.Company }))
            {
                x = x.Union(await Truck.GetByCompanyAsync(company, includeDeleted));
            }

            x = x.Where(o => o.IsActive);

            return x.Select(InternalTruckModel.Map)
                .OrderBy(o => o.Name);
        }

        public class InternalTruckModel
        {
            public int Id { get; set; }
            public string Name { get; set; }
            public Truck.TruckType Type { get; set; }
            public Truck.DutyType Duty { get; set; }
            public string Make { get; set; }
            public string Model { get; set; }
            public int CompanyId { get; set; }
            public int[] Companies { get; set; }

            public static InternalTruckModel Map(Truck o)
            {
                return new InternalTruckModel()
                {
                    Id = o.Id,
                    Name = o.Name,
                    Type = o.Type,
                    Duty = o.Duty,
                    Make = o.Manufacturer,
                    Model = o.Model,
                    CompanyId = o.CompanyId,
                    Companies = o.Companies ?? new int[] { o.CompanyId }
                };
            }
        }

        /// <summary>
        /// Retrieve a specific truck by it's unique ID.
        /// </summary>
        /// <param name="id"></param>
        /// <returns>TruckModel representing the driver</returns>
        [HttpGet("{id}")]
        public async Task<IActionResult> Get(int id)
        {
            var t = await Truck.GetByIdAsync(id);

            if (t != null)
            {
                if (!await WebGlobal.CurrentUser.HasAccessToCompanyAsync(t.CompanyId))
                {
                    t = null;
                }
            }

            if (t != null)
            {
                return Ok(TruckModel.Map(t));
                //return this.Request.CreateResponse<TruckModel>(HttpStatusCode.OK, TruckModel.Map(t), PerRequestJsonSettingsFormatter.Instance);
            }
            else
            {
                return NotFound("Truck " + id + " does not exist.");
                //return this.Request.CreateResponse<string>(HttpStatusCode.NotFound, "Truck " + id + " does not exist.", PerRequestJsonSettingsFormatter.Instance);
            }
        }

        /// <summary>
        /// Create a new driver
        /// </summary>
        /// <param name="truckModel"></param>
        /// <returns></returns>
        [ApiPermission(Extric.Towbook.User.TypeEnum.Manager)]
        [HttpPost]
        public async Task<TruckModel> Post([FromBody] TruckModel truckModel)
        {
            if (truckModel == null)
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.Forbidden)
                {
                    Content = new StringContent("Your request body is empty. Did you forget to include the JSON or set the content-type to application/json?")
                });
            }

            if (truckModel.Id > 0)
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.Forbidden)
                {
                    Content = new StringContent("You shouldn't set the ID in a POST request. If you're trying to update an existing object, use PUT method instead")
                });
            }

            var truck = new Truck();

            TruckModel.Map(truckModel, truck);

            truck.CompanyId = WebGlobal.CurrentUser.CompanyId;
            await truck.Save();

            saveLicenseInformation(truck, truckModel);

            var up = Extric.Towbook.Integrations.MotorClubs.Urgently.UrgentlyProvider.GetByCompanyId(truck.CompanyId).FirstOrDefault();
            if (up != null && up.IsEnabled == true)
            {
                var queueItemId = await Towbook.Integration.MotorClubs.Services.DigitalDispatchService.SyncCompanyResources(up.CompanyId, up.AccountId, WebGlobal.CurrentUser.Id);
            }

            return TruckModel.Map(truck);
        }

        /// <summary>
        /// Update an existing driver
        /// </summary>
        /// <param name="id"></param>
        /// <param name="truckModel"></param>
        /// <returns></returns>
        [ApiPermission(Extric.Towbook.User.TypeEnum.Manager)]
        [HttpPut("{id}")]
        public async Task<TruckModel> Put(int id, TruckModel truckModel)
        {
            if (truckModel == null)
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.Forbidden)
                {
                    Content = new StringContent("Your request body is empty. Did you forget to include the JSON or set the content-type to application/json?")
                });
            }

            if (truckModel.Id < 1)
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.InternalServerError)
                {
                    Content = new StringContent("You must set the Id in a POST request. If you're trying to create a new truck, use the POST method instead.")
                });
            }

            if (truckModel.Id != id)
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.InternalServerError)
                {
                    Content = new StringContent("The ID in the body of the request doesn't match the ID passed via the incoming URL.")
                });
            }

            var truck = await Truck.GetByIdAsync(id);

            if (truck == null || !await WebGlobal.CurrentUser.HasAccessToCompanyAsync(truck.CompanyId))
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound) { Content = new StringContent("The specified truck either doesn't exist or you don't have access to it.") });

            TruckModel.Map(truckModel, truck);

            await truck.Save();

            saveLicenseInformation(truck, truckModel);

            var up = Extric.Towbook.Integrations.MotorClubs.Urgently.UrgentlyProvider.GetByCompanyId(truck.CompanyId).FirstOrDefault();
            if (up != null && up.IsEnabled == true)
            {
                var queueItemId = await Towbook.Integration.MotorClubs.Services.DigitalDispatchService.SyncCompanyResources(up.CompanyId, up.AccountId, WebGlobal.CurrentUser.Id);
            }

            return TruckModel.Map(truck);
        }

        [ApiPermission(Extric.Towbook.User.TypeEnum.Manager)]
        [HttpDelete("{id}")]
        public async Task<HttpResponseMessage> Delete(int id)
        {
            var truck = await Truck.GetByIdAsync(id);

            if (truck == null)
                return new HttpResponseMessage(HttpStatusCode.OK);

            if (!await WebGlobal.CurrentUser.HasAccessToCompanyAsync(truck.CompanyId))
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound) { Content = new StringContent("The specified truck either doesn't exist or you don't have access to it.") });

            await truck.Delete();

            return new HttpResponseMessage(HttpStatusCode.Accepted);
        }

        [HttpPost("{id}/undelete")]
        [HttpPut("{id}/undelete")]
        public async Task<HttpResponseMessage> Undelete(int id)
        {
            var truck = await Truck.GetByIdAsync(id);

            if (truck == null)
                return new HttpResponseMessage(HttpStatusCode.NotFound);

            if (!await WebGlobal.CurrentUser.HasAccessToCompanyAsync(truck.CompanyId))
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound) { Content = new StringContent("The specified truck either doesn't exist or you don't have access to it.") });

            await truck.Undelete();

            return new HttpResponseMessage(HttpStatusCode.Accepted);
        }

        private static void saveLicenseInformation(Truck Truck, TruckModel model)
        {
            if (model == null || model.Licenses == null || Truck == null)
                return;

            if (Truck.Id < 1)
                throw new TowbookException("Cannot save license information when Truck.Id is less than 1.");

            foreach (var x in model.Licenses)
            {
                var tlkv = new TruckLicenseKeyValue();

                if (x.KeyId > 0)
                {
                    var c = TruckLicenseKeyValue.GetByTruckId(model.Id, x.KeyId);

                    if (c != null)
                        tlkv = c;
                }
                tlkv.TruckId = Truck.Id;
                tlkv.KeyId = x.KeyId;
                tlkv.Value = x.Value;

                if (string.IsNullOrWhiteSpace(tlkv.Value))
                    tlkv.Delete(WebGlobal.CurrentUser);
                else
                    tlkv.Save(WebGlobal.CurrentUser);
            }
        }

        [HttpGet("{id}/inspections")]
        public async Task<IEnumerable<PreTripInspectionItemTypeDetailModel>> Inspections(int id)
        {
            var truck = await Truck.GetByIdAsync(id);

            await ThrowIfNoCompanyAccessAsync(truck?.CompanyId, "Truck");

            var companies = await WebGlobal.GetCompaniesAsync();

            var preTripItems = PreTripInspectionItemType
                .GetAllItemsByCompany(companies.Select(s => s.Id).ToArray(), WebGlobal.CurrentUser.PrimaryCompanyId);

            var customInspections = PreTripInspectionItemTypeDetail
                .GetByCompanyIds(companies.Select(s => s.Id).ToArray())
                .Select(s => new PreTripInspectionItemTypeDetailModel()
                {
                    TruckTypeId = (int)truck.Type,
                    DetailId = s.Id,
                    Name = s.Name,
                    QuestionCount = preTripItems?.Where(w => w.PreTripInspectionItemTypeDetailId == s.Id).Count()
                }).ToCollection();

            if (preTripItems.Any(a => a.TruckTypeId == (int)truck.Type))
            {
                // keep matched truck type items
                preTripItems = preTripItems.Where(w => w.TruckTypeId == (int)truck.Type).ToCollection();
            }
            else
            {
                // keep custome (truckless) or default items
                preTripItems = preTripItems.Where(w => w.TruckTypeId == 0).ToCollection();
            }

            var truckTypeInspections = preTripItems
                .OrderBy(w => w.TruckTypeId > 0)
                .GroupBy(g => g.TruckTypeId)
                .Select(s =>  {

                    var name = truck?.Type.ToString() ?? "Truck";
                    if (s.Key == 0)
                        name = "Standard Pre/Post Trip";

                    name += " Inspection";

                    return new PreTripInspectionItemTypeDetailModel()
                    {
                        TruckTypeId = (int)truck.Type,
                        DetailId = (int?)null,
                        Name = name,
                        QuestionCount = s.Count(c => c.TruckTypeId == s.Key && c.PreTripInspectionItemTypeDetailId.GetValueOrDefault() == 0)
                    };
                });
            
            return truckTypeInspections.Union(customInspections.Where(w => w.QuestionCount > 0));
        }
    }
}
