using Extric.Towbook.API.PreTripInspections.Models;
using Extric.Towbook.PreTripInspections;
using Extric.Towbook.Utility;
using Extric.Towbook.Web;
using Extric.Towbook.WebShared;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using static Extric.Towbook.API.ApiUtility;

namespace Extric.Towbook.API.Controllers
{
    [Route("pretripinspectionitemtypedetails")]
    [ApiController]
    public class PreTripInspectionItemTypeDetailsController : ControllerBase
    {
        [HttpGet]
        [Route("")]
        public async Task<PreTripInspectionReportsModel> Get()
        {
            var truckTypes = EnumExtensionMethods.Enum<Truck.TruckType>.GetAllValuesAsIEnumerable()
                                .Select(d => new EnumExtensionMethods.EnumTypeDefinitionModel(d))
                                .ToCollection();

            var companies = await WebGlobal.GetCompaniesAsync();

            var pretripsItems = PreTripInspectionItemType
                // TODO: Can be made async
                .GetAllItemsByCompany(companies.Select(s => s.Id).ToArray(), WebGlobal.CurrentUser.PrimaryCompanyId)
                .ToCollection();

            var customInspections = PreTripInspectionItemTypeDetail
                // TODO: Can be made async
                .GetByCompanyIds(companies.Select(s => s.Id).ToArray())
                .Select(s => new PreTripInspectionItemTypeDetailModel()
                {
                    DetailId = s.Id,
                    Name = s.Name,
                    QuestionCount = pretripsItems?.Where(w => w.PreTripInspectionItemTypeDetailId == s.Id).Count()
                })
                .ToCollection();

            
            var truckTypeItems = pretripsItems
                .OrderBy(w => w.TruckTypeId > 0)
                .GroupBy(g => g.TruckTypeId)
                .Select(s =>
                {
                    var truck = truckTypes.FirstOrDefault(f => f.Id == s.Key);
                    var item = pretripsItems.FirstOrDefault(f => f.Id == s.Key);

                    var name = truck?.Name ?? "Truck";
                    if (s.Key == 0)
                        name = "Standard Pre/Post Trip";

                    name += " Inspections";

                    return new PreTripInspectionItemTypeDetailModel()
                    {
                        Name = name,
                        TruckTypeId = item?.TruckTypeId ?? truck?.Id ?? 0,
                        QuestionCount = s.Count(c => c.TruckTypeId == s.Key && c.PreTripInspectionItemTypeDetailId.GetValueOrDefault() == 0)
                    };
                })
                .ToCollection();

            return new PreTripInspectionReportsModel()
            {
                Inspections = truckTypeItems
                    .Union(customInspections)
                    .ToArray(),
            };
        }

        [HttpGet]
        [Route("{id}")]
        public async Task<IEnumerable<PreTripInspectionItemTypeDetailModel>> GetAsync(int id)
        {
            var customInspections = PreTripInspectionItemTypeDetail
                // TODO: Can be made async
                .GetByCompanyIds((await WebGlobal.GetCompaniesAsync()).Select(s => s.Id).ToArray())
                .Where(w => w.Id == id)
                .Select(s => new PreTripInspectionItemTypeDetailModel()
                {
                    DetailId = s.Id,
                    Name = s.Name
                }).ToCollection();

            if (customInspections.Any())
            {
                var questions = await PreTripInspectionItemType.GetByItemTypeDetailIdsAsync([id]);
                foreach (var ci in customInspections)
                    ci.QuestionCount = questions.Count();
            }

            return customInspections;
        }

        [HttpPost]
        [Route("")]
        public async Task<PreTripInspectionItemTypeDetailModel> PostAsync(PreTripInspectionItemTypeDetailModel model)
        {
            ThrowIfNotFound(model.Name, "Inspection Name");

            var sanitizedName = Core.HtmlEncode(model.Name);

            // check for duplicate names
            // TODO: Can be made async
            var all = await Get();

            if (all != null && 
                all.Inspections.Any(s => s.Name.Equals(sanitizedName)))
            {
                throw new HttpResponseException(new HttpResponseMessage(Forbidden())
                {
                    Content = new StringContent($"An inspection report with the name {sanitizedName} is already created.  Please change your inspection report name to something unique.")
                });
            }

            var m = new PreTripInspectionItemTypeDetail()
            {
                Name = sanitizedName
            };
            m.CompanyId = WebGlobal.CurrentUser.PrimaryCompanyId;
            await m.SaveAsync(WebGlobal.CurrentUser);

            return new PreTripInspectionItemTypeDetailModel()
            {
                DetailId = m.Id,
                Name = m.Name
            };
        }

        [HttpPut]
        [Route("{id}")]
        public async Task<PreTripInspectionItemTypeDetailModel> Put(int id, PreTripInspectionItemTypeDetailModel model)
        {
            var detail = await PreTripInspectionItemTypeDetail.GetByIdAsync(id);
            await ThrowIfNoCompanyAccessAsync(detail?.CompanyId, "Inspection");
            ThrowIfNotFound(model.Name, "Inspection Name");

            detail.Name = Core.HtmlEncode(model.Name);
            await detail.SaveAsync(WebGlobal.CurrentUser);

            return new PreTripInspectionItemTypeDetailModel()
            {
                DetailId = detail.Id,
                Name = detail.Name
            };
        }

        /// Since a pre-trip inspection report is simply a grouping of pre-trip inspection items, we don't
        /// necessarily have an id to use for a traditional DELETE request. We must gather the items and
        /// mark them deleted.
        /// Should the request body contain a DetailId value, then we must also delete the report details
        /// since the grouping has an associated custom name (and is truck type less).
        [HttpDelete]
        [Route("")]
        public async Task<HttpResponseMessage> Delete([FromBody] PreTripInspectionItemTypeDetailModel model)
        {
            if (model == null || 
                (model?.TruckTypeId.GetValueOrDefault() == 0 && model?.DetailId.GetValueOrDefault() == 0))
            {
                // don't allow default pre trip inspection items to be deleted
                throw new HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest)
                {
                    Content = new StringContent("The request body is not valid or you don't have permission to perform this action.")
                });
            }

            var items = new List<PreTripInspectionItemType>();

            if (model.DetailId > 0) 
            {
                var detail = await PreTripInspectionItemTypeDetail.GetByIdAsync(model.DetailId.GetValueOrDefault());

                await ThrowIfNoCompanyAccessAsync(detail?.CompanyId, "Inspection");

                items = (await PreTripInspectionItemType.GetByItemTypeDetailAsync(detail.CompanyId, detail.Id)).ToList();

                await detail.DeleteAsync(WebGlobal.CurrentUser);
            }

            if(model.TruckTypeId > 0)
            {
                items = PreTripInspectionItemType.GetByCompany((await WebGlobal.GetCompaniesAsync()).Select(s => s.Id).ToArray(), WebGlobal.CurrentUser.PrimaryCompanyId, model.TruckTypeId).ToList();
            }

            // delete the individual items
            foreach (var item in items)
                // TODO: Can be made async, also this is not performant (n database/network calls on a loop)
                item.Delete();

            return new HttpResponseMessage(HttpStatusCode.OK);
        }
    }
}
