using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using Extric.Towbook.WebShared;
using Extric.Towbook.API.Controllers;
using Extric.Towbook.Accounts;
using Extric.Towbook.Utility;
using System;
using System.Collections.ObjectModel;
using Stripe;
using static Extric.Towbook.API.ApiUtility;
using Extric.Towbook.API.Models;
using NLog;
using Extric.Towbook.Stripe;
using Extric.Towbook.PermitRequests.API;
using System.Threading.Tasks;
using System.Linq.Expressions;

namespace Extric.Towbook.API.Accounts.Controllers
{

    [Route("accounts/{accountId}/parkingPermitApprovalSessions")]
    public class ParkingPermitApprovalSessionsController : ControllerBase
    {

        private static readonly Logger logger = LogManager.GetCurrentClassLogger();

        /// <summary>
        /// Retrieve a list of parking permits approval sessions associated with <paramref name="accountId"/>.
        /// </summary>
        /// <param name="accountId">The accountId (required).</param>
        /// <param name="sessionStatusId">Optional. The session status to retreive (rejected, ready for approvals, change request)</param>
        /// <param name="page">Used for pagination. This is the offset value to begin collecting data.</param>
        /// <param name="pageSize">The maximum number of results to return.</param>
        //routes.MapHttpRoute(
        //    name: "AccountParkingPermitApprovalSessions",
        //    routeTemplate: "accounts/{accountId}/parkingPermitApprovalSessions/{id}",
        //    defaults: new { controller = "ParkingPermitApprovalSessions", id = RouteParameter.Optional })
        //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Accounts.Controllers" } } };
        [Route("")]
        [HttpGet]
        public async Task<IEnumerable<ParkingPermitApprovalSessionModel>> Get(int accountId, [FromQuery] int? sessionStatusId = null, [FromQuery] int? page = 1, [FromQuery] int? pageSize = 25)
        {
            await ThrowIfFeatureNotAssignedAsync(Generated.Features.ParkingPermits);

            var account = await Towbook.Accounts.Account.GetByIdAsync(accountId);
            await ThrowIfNoCompanyAccessAsync(account?.Companies, "account");

            int[] sessionStatusIds = new int[] { (int)ApprovalSessionStatuses.ReadyForApproval, (int)ApprovalSessionStatuses.ChangeRequest };
            if (sessionStatusId != null)
                sessionStatusIds = new int[] { sessionStatusId.Value };

            var sessions = ParkingPermitApprovalSession.GetByAccountId(accountId, sessionStatusIds, page.Value, pageSize.Value);

            var requests = ParkingPermitRequest.GetByIds(sessions.Select(s => s.RequestId).Distinct().ToArray(), true);
            var orders = ParkingPermitApprovalSessionOrder.GetByIds(sessions.Where(w => w.SessionOrderId != null).Select(s => s.SessionOrderId.Value).ToArray());

            var ret = new Collection<ParkingPermitApprovalSessionModel>();

            var addresses = AccountParkingPermitAddress.GetByParkingPermitRequestIds(sessions.Select(s => s.RequestId).ToArray()).ToCollection();

            // Get session items
            foreach (var session in sessions)
            {
                if (session.Items == null)
                    session.Items = ParkingPermitApprovalSessionItem.GetBySessionId(session.Id);

                var request = requests.Where(w => w.Id == session.RequestId).FirstOrDefault();
                var matchedAddresses = addresses.Where(w => session.Items.Where(a => a.ParkingPermitId.GetValueOrDefault() == w.ParkingPermitId.GetValueOrDefault()).Any()).ToCollection();

                var model = ParkingPermitApprovalSessionModel.Map(session, WebGlobal.CurrentUser, request, matchedAddresses);

                var order = orders.Where(w => w.ApprovalSessionId == session.Id).FirstOrDefault();
                if (order != null)
                {
                    model.SessionOrderId = order.Id;
                    model.PreAuthTotal = (decimal?)order.PreAuthTotal / 100; // convert from cents to dollar 
                    model.ApprovedTotal = (decimal?)order.ApprovedTotal / 100; // convert from cents to dollar
                    model.Last4 = order.Last4;
                    model.Brand = order.Brand;
                }

                ret.Add(model);
            }

            return ret;

        }


        //routes.MapHttpRoute(
        //    name: "AccountParkingPermitApprovalSessions",
        //    routeTemplate: "accounts/{accountId}/parkingPermitApprovalSessions/{id}",
        //    defaults: new { controller = "ParkingPermitApprovalSessions", id = RouteParameter.Optional })
        //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Accounts.Controllers" } } };
        [Route("{id}")]
        [HttpGet]
        public async Task<object> GetAsync(int accountId, int id)
        {
            if (!await WebGlobal.CurrentUser.Company.HasFeatureAsync(Generated.Features.ParkingPermits))
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.Forbidden) { Content = new StringContent("Parking permits feature is not enabled.") });
            }

            var account = await Towbook.Accounts.Account.GetByIdAsync(accountId);
            await ThrowIfNoCompanyAccessAsync(account?.Companies, "account");

            var session = ParkingPermitApprovalSession.GetByIds(new int[] { id }).FirstOrDefault();

            if(session == null)
                return StatusCode((int)HttpStatusCode.NotFound, "Session " + id + " does not exist.");

            if (session.AccountId != account.Id)
                session = null;

            await ThrowIfNoCompanyAccessAsync(session?.CompanyId, "parking permit approval session");

            session.Items = ParkingPermitApprovalSessionItem.GetBySessionId(id);
            var request = ParkingPermitRequest.GetById(session.RequestId);

            var model = ParkingPermitApprovalSessionModel.Map(session, WebGlobal.CurrentUser, request, null);

            var order = ParkingPermitApprovalSessionOrder.GetBySessionId(session.Id);
            if (order!= null)
            {
                model.PreAuthTotal = (decimal?)order.PreAuthTotal / 100; // convert from cents to dollar 
                model.ApprovedTotal = (decimal?)order.ApprovedTotal / 100; // convert from cents to dollar
                model.Last4 = order.Last4;
                model.Brand = order.Brand;
            }

            return model;
        }

        /// <summary>
        /// Approve or Revoke the approval request
        /// </summary>
        /// <returns>A collection of parking permits that were created (if any).</returns>
        //routes.MapHttpRoute(
        //    name: "AccountParkingPermitApprovalSessions",
        //    routeTemplate: "accounts/{accountId}/parkingPermitApprovalSessions/{id}",
        //    defaults: new { controller = "ParkingPermitApprovalSessions", id = RouteParameter.Optional })
        //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Accounts.Controllers" } } };
        [Route("{id}")]
        [HttpPost]
        public async Task<object> Post(int accountId, int id, ParkingPermitApprovalModel model)
        {
            await ThrowIfFeatureNotAssignedAsync(Generated.Features.ParkingPermits);

            var account = await Towbook.Accounts.Account.GetByIdAsync(accountId);
            await ThrowIfNoCompanyAccessAsync(account?.Companies);

            if (model == null || id <= 0)
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound) { Content = new StringContent("ApprovalSessionId was not provided or is invalid.") });

            var session = ParkingPermitApprovalSession.GetById(id);
            if (session?.AccountId != accountId)
                session = null;
            await ThrowIfNoCompanyAccessAsync(session?.CompanyId);

            var request = ParkingPermitRequest.GetById(session.RequestId);
            if (request == null)
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound) { Content = new StringContent("Request not found or you don't have permission to access this information.") });

            
            var permits = new Collection<ParkingPermitModel>();

            var items = ParkingPermitApprovalSessionItem.GetBySessionId(session.Id);

            // Check for rejected condition... if so, delete session.
            if (!model.ApprovedItemIds.Any())
            {
                await ParkingPermitApprovalSession.UpdateStatus(
                    session, 
                    ApprovalSessionStatuses.Rejected,
                    "Rejected by " + WebGlobal.CurrentUser.FullName + " on " + DateTime.Now.ToShortDateString() + ".");

                // record permit status event of rejected
                foreach (var item in items)
                {
                    if (item.ParkingPermitId != null)
                    {
                        var pp = ParkingPermit.GetById(item.ParkingPermitId.Value);
                        if (pp != null)
                        {
                            await pp.Reject(WebGlobal.CurrentUser);
                        }
                    }
                }

                // Delete approval session
                session.Delete(WebGlobal.CurrentUser);
                return null;
            }

            // Capture payment before converting to permits. We want to return any possible issues now.
            if (session.SessionOrderId != null)
            {
                var setting = ParkingPermitSetting.GetByAccountId(account.CompanyId, account.Id);
                var cSetting = ParkingPermitSetting.GetByCompanyId(account.CompanyId);
                if (setting == null)
                    setting = cSetting;

                // company overrides
                if (setting.TaxRateId == null && cSetting.TaxRateId != null)
                    setting.TaxRateId = cSetting.TaxRateId;

                // recalculate authorized total (consider tax)
                var subtotal = items.Where(w => model.ApprovedItemIds.Contains(w.Id)).Sum(s => s.Amount) ?? 0.0M;
                var tax = 0.0M;
                if (setting.TaxRateId != null && setting.TaxRateId.Value != 0)
                {
                    var tr = await TaxRate.GetByIdAsync(setting.TaxRateId.Value);
                    if (tr != null)
                        tax = subtotal * tr.Rate / 100;
                }
                var total = subtotal + tax;

                // count the number of permits to be approved
                int numberOfPermits = model.ApprovedItemIds.Count();

                // convert dollar to cents
                var approvedTotal = Convert.ToInt32(total * 100);

                var order = ParkingPermitApprovalSessionOrder.GetById(session.SessionOrderId.Value);
                if (order != null)
                {
                    CaptureModel capture = null;
                    var stripeTransaction = StripeTransaction.GetByPaymentIntent(StripeUtility.GetPaymentIntentFromClientSecret(order.PaymentIntentClientSecret));

                    try
                    {
                        if (total > 0)
                        {

                            if (stripeTransaction?.CaptureMethod == "automatic")
                            {
                                capture = StripeUtility.ChargeAmount(account.CompanyId,
                                            account.Company,
                                            order.PaymentMethodId,
                                            order.PaymentIntentClientSecret,
                                            approvedTotal);
                            }
                            else
                            {
                                capture = StripeUtility.CaptureAuthAmount(
                                                account.CompanyId,
                                                StripeUtility.GetPaymentIntentFromClientSecret(order.PaymentIntentClientSecret),
                                                order.PaymentMethodId,
                                                approvedTotal,
                                                numberOfPermits);
                            }

                            if (capture != null)
                            {
                                order.ApprovedTotal = capture.Charge?.Amount;
                                order.ApprovedAuth = capture.Charge?.Id;
                                order.StripeTransferId = capture.Transfer?.Id;
                                order.StripePaymentId = capture.Transfer?.DestinationPaymentId;
                                order.ApprovedFee = capture.ApprovedFee;
                                order.ApplicationFeeId = capture.Charge?.ApplicationFee?.Id;
                                order.PaymentIntentId = capture.Intent?.Id;
                                order.PaymentIntentClientSecret = capture.Intent?.ClientSecret;
                                order.Save();
                            }
                        }
                    }
                    catch(StripeException e)
                    {
                        var ex = new StripeApiException()
                        {
                            AccountId = account.CompanyId,
                            CompanyId = account.CompanyId,
                            SessionOrderId = order?.Id,
                            PreAuthTotal = order?.PreAuthTotal,
                            ApprovalTotal = order?.ApprovedTotal,
                            StripeChargeReference = order?.PreAuth,
                            StripeIntentClientSecret = order?.PaymentIntentClientSecret,
                            StripeTokenReference = order?.Token,
                            RequestId = request.Id,
                            UserId = request.UserId,
                            ApprovalSessionId = session?.Id,
                            AttemptedApprovalDate = DateTime.Now,
                            AttemptedApprovalByUserId = WebGlobal.CurrentUser.Id,
                            HttpStatusCode = e.HttpStatusCode,
                            Type = e.StripeError?.Type,
                            Code = e.StripeError?.Code,
                            DeclineCode = e.StripeError?.DeclineCode,
                            StripeMessage = e.StripeError.Message,
                            Param = e.StripeError.Param
                        };

                        var msg = StripeApiException.HandleExceptionMessage(ex);

                        // Log activity
                        await HelperUtility.LogErrorAsync(new Dictionary<string, object> {
                            ["type"] = "Permit Approval Error",
                            ["session"] = session.ToJson(),
                            ["request"] = request.ToJson(),
                            ["requestId"] = request.Id,
                            ["order"] = order.ToJson(),
                            ["orderId"] = order?.Id,
                            ["account"] = account.Company,
                            ["accountId"] = account.Id,
                        }, account.CompanyId, e);

                        await ParkingPermitApprovalSession.UpdateStatus(session, ApprovalSessionStatuses.Error, ex.ToJson());


                        if (stripeTransaction != null &&
                            !stripeTransaction.Deleted &&
                            ex.Type == "invalid_request_error")
                        {
                            var paymentIntent = StripeUtility.GetPaymentIntentById(stripeTransaction.StripePaymentIntentId);
                            if (new[] { "succeeded", "canceled", "requires_confirmation" }.Contains(paymentIntent?.Status))
                            {
                                // delete the transaction
                                stripeTransaction.Deleted = true;
                            }
                        }

                        return new HttpResponseMessage(HttpStatusCode.InternalServerError) { Content = new StringContent(msg) };
                    }
                    finally 
                    {
                        if(stripeTransaction != null)
                        {
                            if(new[] { "succeeded", "canceled" }.Contains(capture?.Intent?.Status))
                            {
                                stripeTransaction.Deleted = true;
                                
                                stripeTransaction.CaptureAmount = (int?)capture?.Charge?.AmountCaptured;
                            }

                            if (order?.Id > 0)
                                stripeTransaction.AccountParkingPermitApprovalSessionOrderId = order.Id;

                            stripeTransaction.Save();
                        }
                    }

                }
            }


            foreach (var itemId in model.ApprovedItemIds)
            {
                var item = items.Where(w => w.Id == itemId).FirstOrDefault();
                if(item != null)
                {
                    var permitId = item.ParkingPermitId;

                    if (item.ParkingPermitId != null)
                    {
                        // mark permit valid
                        var permit = ParkingPermit.GetById(item.ParkingPermitId.Value);
                        if (permit != null)
                        {
                            // record permit status approval event
                            await permit.Approve(WebGlobal.CurrentUser, this.GetCurrentToken(), this.GetRequestingIp());

                            permits.Add(await ParkingPermitModel.Map(permit));
                        }
                    }
                    else
                    {

                        var lp = item.LicensePlate;
                        if (!string.IsNullOrWhiteSpace(lp))
                            lp = lp.Replace(" ", "");

                        // Create permit
                        var permit = new ParkingPermit()
                        {
                            AccountId = account.Id,
                            ParkingPermitListId = item.ParkingPermitListId,
                            LicensePlate = lp,
                            LicensePlateState = item.LicensePlateState,
                            VehicleYear = item.VehicleYear,
                            VehicleMake = item.VehicleMake,
                            VehicleModel = item.VehicleModel,
                            VehicleColor = item.VehicleColor,
                            VIN = item.VIN,
                            FullName = request.FullName,
                            Email = request.Email,
                            CellPhone = request.CellPhone,
                            CustomPermitNumber = request.CustomPermitNumber,
                            UnitNumber = request.UnitNumber,
                            CreateDate = DateTime.Now
                        };

                        await permit.Save(WebGlobal.CurrentUser, this.GetCurrentToken(), this.GetRequestingIp());
                        permits.Add(await ParkingPermitModel.Map(permit));
                        permitId = permit.Id;
                    }

                    item.ParkingPermitId = permitId;
                    item.Approved = true;
                    item.Save();
                }
            }

            foreach (var itemId in model.RevokedItemIds)
            {
                var item = items.Where(w => w.Id == itemId).FirstOrDefault();
                if (item != null)
                {
                    item.Approved = false;
                    item.Save();
                }
            }

            // copy request addresses to each permit being created/approved
            var addresses = AccountParkingPermitAddress.GetByParkingPermitRequestId(request.Id);
            if (addresses != null)
            {
                foreach (var addr in addresses)
                {
                    if (addr.ParkingPermitId == null)
                    {
                        // make a copy of the address and apply it to each permit
                        foreach (var p in permits)
                        {
                            var a = new AccountParkingPermitAddress() {
                                ParkingPermitId = p.Id,
                                ParkingPermitRequestId = request.Id,
                                Address = addr.Address,
                                City = addr.City,
                                State = addr.State,
                                Zip = addr.Zip,
                                Type = addr.Type
                            };

                            a.Save(WebGlobal.CurrentUser);
                        }

                        // Mark address as deleted since the address is now connected
                        // to the permit(s)
                        addr.Delete(WebGlobal.CurrentUser);
                    }
                }
            }

            // Update status and delete approval session
            await ParkingPermitApprovalSession.UpdateStatus(
                session, 
                ApprovalSessionStatuses.Approved, 
                "Approved by " + WebGlobal.CurrentUser.FullName + " on " + DateTime.Now.ToShortDateString() + ".");

            session.Delete(WebGlobal.CurrentUser);

            return permits.ToCollection();
        }
        
    }
}
