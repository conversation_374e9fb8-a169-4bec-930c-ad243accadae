using Extric.Towbook.Accounts;
using Extric.Towbook.API.Accounts.Models;
using Extric.Towbook.Storage;
using Extric.Towbook.Utility;
using SkiaSharp;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using static Extric.Towbook.API.ApiUtility;
using Extric.Towbook.WebShared;
using Extric.Towbook.WebShared.Multipart;
using Microsoft.AspNetCore.Http.Features;
using Microsoft.AspNetCore.WebUtilities;
using Microsoft.Net.Http.Headers;
using Extric.Towbook.Web;
using Plivo.XML;

namespace Extric.Towbook.API.Accounts.Controllers
{
    [Route("accounts/{accountId}/photos")]
    public class AccountPhotosController: ControllerBase
    {

        [HttpGet]
        [Route("")]
        public async Task<object> GetSolver(int accountId, [FromQuery] int? id) =>
            id == null || id == 0 ? Get(accountId) : await Get(accountId, (int)id);

        private IEnumerable<PhotoModel> Get(int accountId)
        {
            return Towbook.Accounts.Photo.GetByAccountId(accountId).Select(o => PhotoModel.Map(o));
        }

        private async Task<HttpResponseMessage> Get(int accountId, [FromQuery] int id)
        {
            var account = await Extric.Towbook.Accounts.Account.GetByIdAsync(accountId);
            var ap = Extric.Towbook.Accounts.Photo.GetById(id);

            await ThrowIfNoCompanyAccessAsync(account?.CompanyId);

            if (ap == null || account == null || account.Id != ap.AccountId)
                throw new HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound));

            var result = new HttpResponseMessage(HttpStatusCode.OK);
            result.Content = new StreamContent(new FileStream(await FileUtility.GetFileAsync(ap.Location.Replace("%1", account.CompanyId.ToString())), FileMode.Open, FileAccess.Read, FileShare.ReadWrite));
            result.Content.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue(ap.ContentType);

            return result;
        }

        [HttpPost]
        [Route("")]
        [DisableFormValueModelBinding]
        public async Task<ObjectResult> Post([FromQuery] int accountId, [FromQuery] string description = "")

        {
            //if (!Request.Content.IsMimeMultipartContent())
            if (!Web.HttpContext.Current.Request.HasFormContentType)
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.UnsupportedMediaType));
            }

            var u = await Extric.Towbook.User.GetByIdAsync(WebGlobal.CurrentUser.Id);
            var s = await Extric.Towbook.Accounts.Account.GetByIdAsync(accountId);
            var p = new Extric.Towbook.Accounts.Photo();

            // Save file
            string path = Path.GetTempPath();

            var provider = new MultipartFormDataStreamProvider(path);

            FileInfo fileInfo = null;

            FormOptions _defaultFormOptions = new FormOptions();

            var boundary = MultipartRequestHelper.GetBoundary(
                MediaTypeHeaderValue.Parse(Web.HttpContext.Current.Request.ContentType),
                _defaultFormOptions.MultipartBoundaryLengthLimit);

            var reader = new MultipartReader(boundary, Web.HttpContext.Current.Request.Body);

            var section = await reader.ReadNextSectionAsync();

            string targetFilePath = "";

            if (section != null)
            {
                var hasContentDispositionHeader =
                    ContentDispositionHeaderValue.TryParse(
                        section.ContentDisposition, out var contentDisposition);

                if (hasContentDispositionHeader)
                {
                    if (MultipartRequestHelper.HasFileContentDisposition(contentDisposition))
                    {
                        targetFilePath = Path.GetTempFileName();
                        using (var targetStream = System.IO.File.Create(targetFilePath))
                        {
                            await section.Body.CopyToAsync(targetStream);
                            fileInfo = new FileInfo(targetFilePath);
                            Console.WriteLine($"Copied the uploaded file '{targetFilePath}'");
                        }
                    }
                }
            }
            if (fileInfo == null || !fileInfo.Exists)
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.InternalServerError)
                {
                    Content = new StringContent("Request is missing file data.")
                });
            }
            string savedFile = targetFilePath;
            string originalFile = section.GetContentDispositionHeader().FileName.Value.TrimStart('"').TrimEnd('"');

            // Copy file and rename with new file name and correct extension
            var file = new FileInfo(savedFile);

            try
            {
                string fn = "";
                int photoId = -1;

                using (var i = SKBitmap.Decode(file.FullName))
                {
                    p.AccountId = Convert.ToInt32(accountId);
                    p.ContentType = "image/jpg";
                    p.Description = description;
                    p.OwnerUserId = u.Id;
                    p.Save(u);

                    fn = p.Location.Replace("%1", s.CompanyId.ToString());

                    if (!Directory.Exists(Path.GetDirectoryName(fn)))
                    {
                        Directory.CreateDirectory(Path.GetDirectoryName(fn));
                    }

                    int width = 640;
                    int height = 480;

                    using (var resizeImg = i.Resize(new SKImageInfo(width, height), SKFilterQuality.High))
                    {
                        resizeImg.Save(fn, SKEncodedImageFormat.Jpeg);
                        photoId = p.Id;
                    }
                }
                if ((await FileUtility.SendFileAsync(fn)).IsHttpSuccess())
                    System.IO.File.Delete(fn);

                return StatusCode((int)HttpStatusCode.Created, PhotoModel.Map(p));
                //return this.Request.CreateResponse(HttpStatusCode.Created, PhotoModel.Map(p), PerRequestJsonSettingsFormatter.Instance);
            }
            catch
            {
                throw;
                //throw new HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotImplemented) { Content = new StringContent("Invalid Image File. Only JPG, PNG, BMP and GIF files are supported.") });
            }
            finally
            {
                file.Delete();
            }
        }

        [HttpDelete]
        [Route("")]
        public async Task<HttpResponseMessage> Delete(int accountId, [FromQuery] int id)
        {
            if (WebGlobal.CurrentUser.Type == Towbook.User.TypeEnum.Manager ||
                WebGlobal.CurrentUser.Type == Towbook.User.TypeEnum.SystemAdministrator)
            {
                var p = Extric.Towbook.Accounts.Photo.GetById(id);
                var a = await Account.GetByIdAsync(accountId);

                await ThrowIfNoCompanyAccessAsync(a?.CompanyId);

                if (p == null || p.AccountId != accountId)
                    throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound));

                p.Delete();
                return new HttpResponseMessage(HttpStatusCode.NoContent);
            }
            else
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.Forbidden));
            }
        }
    }
}
