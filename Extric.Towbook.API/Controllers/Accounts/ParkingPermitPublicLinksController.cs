using Microsoft.AspNetCore.Mvc;
using Extric.Towbook.Accounts;
using Extric.Towbook.API.Models;
using Extric.Towbook.WebShared;
using System.Net;
using System.Net.Http;
using static Extric.Towbook.API.ApiUtility;
using System.Threading.Tasks;

namespace Extric.Towbook.API.Accounts.Controllers
{

    [Route("accounts/{accountId}/parkingPermitPublicLinks")]
    public class ParkingPermitPublicLinksController : ControllerBase
    {

        [Route("{id?}")]
        [HttpGet]
        public async Task<object> GetSolver(int accountId, [FromQuery] string propertyCode) =>
            string.IsNullOrEmpty(propertyCode) ? await Get(accountId) : await Get(accountId, propertyCode);


        //routes.MapHttpRoute(
        //    name: "AccountParkingPermitPublicLinks",
        //    routeTemplate: "accounts/{accountId}/parkingPermitPublicLinks/{id}",
        //    defaults: new { controller = "ParkingPermitPublicLinks", id = RouteParameter.Optional })
        //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Accounts.Controllers" } } };
        //[Route("{id?}")]
        //[HttpGet]
        private async Task<ParkingPermitPublicLinkModel> Get(int accountId)
        {
            await ThrowIfFeatureNotAssignedAsync(Generated.Features.ParkingPermits);

            var acc = await Account.GetByIdAsync(accountId);
            //TODO change reference ParkingPermitPublicLinkModel first
            await ThrowIfNoCompanyAccessAsync(acc?.Companies);

            return ParkingPermitPublicLinkModel.Map(ParkingPermitPublicLink.GetByAccountId(accountId));
        }

        //routes.MapHttpRoute(
        //    name: "AccountParkingPermitPublicLinks",
        //    routeTemplate: "accounts/{accountId}/parkingPermitPublicLinks/{id}",
        //    defaults: new { controller = "ParkingPermitPublicLinks", id = RouteParameter.Optional })
        //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Accounts.Controllers" } } };
        //[Route("{id?}")]
        //[HttpGet]
        private async Task <HttpResponseMessage> Get(int accountId, [FromQuery] string propertyCode)
        {
            await ThrowIfFeatureNotAssignedAsync(Generated.Features.ParkingPermits);

            var acc = await Account.GetByIdAsync(accountId);
            //TODO change reference HttpResponseMessage first
            await ThrowIfNoCompanyAccessAsync(acc?.Companies);

            var result = ParkingPermitPublicLink.FindByPropertyCode(propertyCode);
            if (result == null || (result != null && result.AccountId == accountId))
                return new HttpResponseMessage(HttpStatusCode.OK);
            else
                return new HttpResponseMessage(HttpStatusCode.Conflict);
        }

        //routes.MapHttpRoute(
        //    name: "AccountParkingPermitPublicLinks",
        //    routeTemplate: "accounts/{accountId}/parkingPermitPublicLinks/{id}",
        //    defaults: new { controller = "ParkingPermitPublicLinks", id = RouteParameter.Optional })
        //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Accounts.Controllers" } } };
        [Route("")]
        [HttpPost]
        public async Task<ParkingPermitPublicLinkModel> Post(int accountId)
        {
            //ThrowIfFeatureNotAssigned(Generated.Features.ParkingPermits);

            var acc = await Account.GetByIdAsync(accountId);
            await ThrowIfNoCompanyAccessAsync(acc?.Companies);

            var pl = ParkingPermitPublicLink.GetByAccountId(accountId);
            if (pl == null)
            {
                pl = new ParkingPermitPublicLink();
                pl.AccountId = accountId;
                pl.Disabled = false;
            }

            pl.Save(WebGlobal.CurrentUser);

            return ParkingPermitPublicLinkModel.Map(pl);
        }
    }
}
