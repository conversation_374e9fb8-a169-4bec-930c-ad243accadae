using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using Extric.Towbook.Accounts;
using Extric.Towbook.API.Models;
using Extric.Towbook.Utility;
using Extric.Towbook.WebShared;
using static Extric.Towbook.API.ApiUtility;
using System.Threading.Tasks;
using Extric.Towbook.EventNotifications;
using Extric.Towbook.API.Models.Accounts;
using Extric.Towbook.Web;

namespace Extric.Towbook.API.Controllers
{
    public class StatementsController: ControllerBase
	  {

        [HttpGet("accounts/{accountId}/statements")]
        public async Task<IEnumerable<StatementModel>> AccountsGetAsync(int accountId,
            [FromQuery] DateTime? start = null,
            [FromQuery] DateTime? end = null,
            [FromQuery] bool? paidInFull = null,
            [FromQuery] bool? includePastDue = null) => await GetAsync(accountId, start, end, paidInFull, includePastDue);

        [HttpGet]
        [Route("/statements")]
        public async Task<IEnumerable<StatementModel>> GetAsync([FromQuery]int accountId,
            [FromQuery] DateTime? start = null,
            [FromQuery] DateTime? end = null,
            [FromQuery] bool? paidInFull = null,
            [FromQuery] bool? includePastDue = null)
        {
            if (WebGlobal.CurrentUser.Type == Towbook.User.TypeEnum.Driver)
                return Array.Empty<StatementModel>();

            var a = await Account.GetByIdAsync(accountId);
            await ThrowIfNoCompanyAccessAsync(a?.Companies, "Account");
            
            var statements = (await Statement.GetByCompanyAsync(
                WebGlobal.CurrentUser.CompanyId, accountId, start, end, paidInFull, includePastDue))
                .Select(d =>
                {
                    d.DispatchEntries = new System.Collections.ObjectModel.Collection<Dispatch.Entry>();
                    d.Invoices = new System.Collections.ObjectModel.Collection<Invoice>();

                    var ret =  StatementModel.Map(d);
                    // return null to the client so the client can know these properties weren't filled in. an empty array would indicate
                    // no calls.
                    ret.DispatchEntries = null;
                    ret.Invoices = null;
                    return ret;
                }).ToArray();

            // Get the email history for all statements
            var emailHistory = (await EmailTransaction.GetByStatementIdsAsync(statements.Select(s => s.Id.GetValueOrDefault()).ToArray()))
                .ToArray();

            // Apply them to each statement
            foreach (var s in statements)
            {
                s.EmailHistory = emailHistory.Where(h => h.StatementId == s.Id).ToArray();
            }

            return statements.OrderByDescending(o => o.Id);
        }

        [HttpGet("accounts/{accountId}/statements/{statementId}")]
        public async Task<StatementModel> Get(int accountId, int statementId)
        {
            if (WebGlobal.CurrentUser.Type == Towbook.User.TypeEnum.Driver)
               await ThrowIfNoCompanyAccessAsync(0, "Statement");

            var a = await Account.GetByIdAsync(accountId);
            var s = await Statement.GetByIdAsync(statementId);

            if (s?.AccountId != a.Id)
                await ThrowIfNoCompanyAccessAsync(0, "Statement");
            else
                await ThrowIfNoCompanyAccessAsync(a?.CompanyId, "Statement");

            // Get email history
            var model = StatementModel.Map(s);
            model.EmailHistory = await EmailTransaction.GetByStatementIdAsync(model.Id.Value);

            return model;
        }

        [HttpPost("accounts/{accountId}/statements")]
        public async Task<StatementModel> Post(int accountId, [FromForm] StatementModel model)
        {
            var a = await Account.GetByIdAsync(accountId);
            await ThrowIfNoCompanyAccessAsync(a?.Companies, "statement");

            if (!model.DispatchEntries.Any())
            {
                throw new Extric.Towbook.Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest)
                {
                    Content = new StringContent($"Your request doesn't contain any CallId's")
                });
            }

            if (!model.StatementDate.HasValue)
                model.StatementDate = DateTime.Now;

            if (!model.DueDate.HasValue)
                model.DueDate = model.StatementDate.Value.AddDays(30);

            Statement s = StatementModel.Map(model);
            s.SourceId = StatementSource.AccountStatementApi;
            s.OwnerUserId = WebGlobal.CurrentUser.Id;

            var entries = (await Dispatch.Entry.GetByIdsAsync(model.DispatchEntries.ToArray())).ToCollection();

            // TODO: This is not performant, (n database/network calls per record)
            var accessChecks = entries.Select(async o => new
            {
                Entry = o,
                HasAccess = await WebGlobal.CurrentUser.HasAccessToCompanyAsync(o.CompanyId)
            });

            var accessResults = await Task.WhenAll(accessChecks);
            var forbiddenCalls = accessResults
                .Where(result => !result.HasAccess)
                .Select(result => result.Entry);

            if (forbiddenCalls.Any())
                throw new Extric.Towbook.Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest)
                {
                    Content = new StringContent($"Your request contained invalid ID's")
                });


            if (entries != null)
            {
                s.Company = await Company.Company.GetByIdAsync(a.CompanyId);
                s.DispatchEntries = entries.ToCollection();
                await s.RecalculateSafeAsync();
                await s.Save();
            }

            if (s.Id < 1)
                throw new ApplicationException("Couldn't create a new statement. Something went wrong.");

            return StatementModel.Map(await Statement.GetByIdAsync(s.Id));
        }

        [HttpPut("accounts/{accountId}/statements/{statementId}")]
        public StatementModel Put(int accountId, int statementId, StatementModel model)
        {
            throw new NotImplementedException();
        }

        [HttpDelete("accounts/{accountId}/statements/{statementId}")]
        public async Task<HttpResponseMessage> Delete(int accountId, int statementId)
        {
            var st = await Statement.GetByIdAsync(statementId);
            await ThrowIfNoCompanyAccessAsync(st?.Company?.Id);

            if (st != null)
                await st.Delete();


            return new HttpResponseMessage(HttpStatusCode.NoContent);
        }

        [HttpPost]
        [Route("~/statements/Email")]
        public async Task<HttpResponseMessage> Email(StatementEmailJobModel model)
        {
            await ThrowIfFeatureNotAssignedAsync(Generated.Features.AdvancedBilling);

            if (model == null || !model.StatementIds.Any())
                throw new HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest)
                {
                    Content = new StringContent($"Your request does not contain valid ID's")
                });


            var statements = await Statement.GetByIdsAsync(model.StatementIds);

            // user access check
            var companyIds = (await WebGlobal.GetCompaniesAsync()).Select(s => s.Id);
            statements = statements.Where(w => companyIds.Contains(w.Company?.Id ?? 0));

            if (!statements.Any())
                throw new HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest)
                {
                    Content = new StringContent($"Your request does not contain any valid statements.")
                });


            await ENServiceBusHelper.SendMessageObjectAsync("batch-email-statements",
                "batch-statement-" + WebGlobal.CurrentUser.Id + "-" + DateTime.Now.Ticks,
                new StatementEmailJobModel
                {
                    SendingUserId = WebGlobal.CurrentUser.Id,
                    StatementIds = statements.Select(s => s.Id).ToArray()
                }, "Statement Batch Push");

            return new HttpResponseMessage(HttpStatusCode.OK)
            {
                Content = new StringContent(
                    $"Your request to send {(statements.Count() > 1 ? "statement emails" : "a statement email")} is complete.")       
            };
        }
    }
}
