using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using Extric.Towbook.Utility;
using Extric.Towbook.WebShared;
using Microsoft.AspNetCore.Mvc;

namespace Extric.Towbook.API.Controllers
{
    public class BodyTypeModel
    {
        public int Id { get; set; }
        public string Name { get; set; }
    }

    [Route("bodytypes")]
    [ApiController]
    public class BodyTypesController : ControllerBase
    {
        [HttpGet]
        public async Task<Collection<BodyTypeModel>> Get()
        {
            return (await Vehicle.BodyType.GetByCompanyIdAsync(WebGlobal.CurrentUser.CompanyId))
                .Select(d => new BodyTypeModel()
                {
                    Id = d.Id,
                    Name = d.Name
                }).ToCollection();
        }
    }
}
