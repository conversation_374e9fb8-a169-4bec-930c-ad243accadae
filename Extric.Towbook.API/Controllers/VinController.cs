using System;
using System.Net;
using System.Net.Http;
using Extric.Towbook.Accounts;
using Extric.Towbook.Utility;
using Extric.Towbook.Web;
using Extric.Towbook.WebShared;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System.Threading.Tasks;
using System.Linq;

namespace Extric.Towbook.API.Controllers
{
    [Route("vin")]
    public class VinController : ControllerBase
    {
        //routes.MapHttpRoute(
        //    name: "VinDecoderGet",
        //    routeTemplate: "vin/{id}",
        //    defaults: new { action = "Get", controller = "Vin" }
        //    ).DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };
        [Route("")]
        [HttpGet]
        public async Task<object> Get(
            [FromQuery]string plate, 
            [FromQuery] string state, 
            [FromQuery] int? accountId = null)
        {
            string key = "plate:" + plate.ToUpperInvariant() + "/" + state.ToUpperInvariant();

            if ((accountId.GetValueOrDefault() > 0 ||
                 WebGlobal.CurrentUser.CompanyId == 49039) &&
                !Web.HttpContext.Current.IsAndroidDevice() && 
                !Web.HttpContext.Current.IsAppleDevice())
            {
                // disable web web app plate-to-vin on dispatch-to-subcontractor setups.  CTS needed this. we need a better
                // way to disable it.
                // disable web web app plate-to-vin on dispatch-to-subcontractor setups.
                // CTS needed this. we need a better way to disable it.
                var ac = await Account.GetByIdAsync(accountId.Value);
                if ((ac != null && ac.MasterAccountId == MasterAccountTypes.Towbook) ||
                    ac.CompanyId == 49039)
                    return new CarfaxUtility.CarfaxVin();
            }

            try
            {
                var cached = Core.GetRedisValue(key);
                if (cached != null)
                {
                    return JsonConvert.DeserializeObject<CarfaxUtility.CarfaxVin>(cached);
                }
                else
                {

                    var eu = ExperianUtility.FromLicensePlate(plate, state);
                    if (eu != null)
                    {
                        var vin = eu.Vehicle.FirstOrDefault().Vin;
                        var r = VinDecoder.Decode(vin);

                        if (r != null)
                        {
                            return r;
                        }
                        else
                        {
                            return new CarfaxUtility.CarfaxVin()
                            {
                                Vin = vin,
                                Year = eu.Vehicle.First().Vinspec.Year,
                                Make = eu.Vehicle.First().Vinspec.Make,
                                Model = eu.Vehicle.First().Vinspec.Model,
                                Body = eu.Vehicle.First().Vinspec.Body
                            };
                        }
                    }

                    /*
                    CarfaxUtility.CarfaxVin cv = CarfaxUtility.FromLicensePlate(plate, state);
                    if (cv != null)
                    {
                        cv.Body = ParseBodyType(cv.Body);

                        Core.SetRedisValue(key, cv.ToJson(), TimeSpan.FromDays(30));
                        return cv;
                    }
                    */
                    return new CarfaxUtility.CarfaxVin();
                }
            }
            catch (TowbookException)
            {
                return new HttpResponseMessage(HttpStatusCode.ServiceUnavailable)
                {
                    Content = new StringContent("Towbook is currently unable to process License Plate to VIN requests. Please try again later.")
                };
            }
        }

        /// <summary>
        /// Decodes a VIN number and returns an object containing information such as year, make, model, transmission, fuel, fuel capacity, etc. 
        /// </summary>
        /// <param name="id">17-digit VIN number</param>
        /// <returns>Dynamic object containing various vehicle details</returns>
        [HttpGet("{id}")]
        public async Task<object> Get(string id)
        {
            if (string.IsNullOrWhiteSpace(id))
                return null;

            string vin = id.ToUpperInvariant();

            if (vin.Length != 17 || vin == "11111111111111111")
            if (vin.Length != 17 || 
                vin == "11111111111111111")
                return null;

            string key = "VIN:" + vin;

            var cached = Core.GetRedisValue(key);

            if (cached != null)
            {
                if (Web.HttpContext.Current.IsAndroidDevice())
                {
                    var floodkey = key + "_" + WebGlobal.CurrentUser.Id;

                    var count = await Core.GetRedisDatabase().StringIncrementAsync(
                        floodkey);

                    if (count > 2)
                        throw new HttpResponseException(new HttpResponseMessage((System.Net.HttpStatusCode)429)
                        {
                            Content = new StringContent("Rate limited: VIN requested too many times.")
                        });

                    await Core.GetRedisDatabase().KeyExpireAsync(floodkey, DateTime.Now.AddMinutes(5));
                }

                return JsonConvert.DeserializeObject<CarfaxUtility.CarfaxVin>(cached);
            }

            var cv = VinDecoder.Decode(vin);

            if (cv != null)
            {
                Core.SetRedisValue(key, cv.ToJson(), TimeSpan.FromDays(30));

                cv.Body = ParseBodyType(cv.Body);

                if (cv.Make != null)
                {
                    Core.SetRedisValue(key, cv.ToJson(), TimeSpan.FromDays(30));

                    return cv;
                }
            }

            return new CarfaxUtility.CarfaxVin();
        }

        [ApiExplorerSettings(IgnoreApi = true)]
        private static string ParseBodyType(string body)
        {
            if (string.IsNullOrWhiteSpace(body))
            {
                return body;
            }
            else if (body.ToLowerInvariant().Contains("truck") || body.ToLowerInvariant().Contains("pickup"))
            {
                // even if the value is: "Pickup, Crew Cab" or "Truck, Crew Cab" -> we will return only "truck"
                return "truck";
            }
            else if (body.ToLowerInvariant().Contains("sedan"))
            {
                return "sedan";
            }
            else if (body.ToLowerInvariant().Contains("suv"))
            {
                return "suv";
            }

            return body;
        }
    }
}
