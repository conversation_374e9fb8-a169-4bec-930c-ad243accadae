using Extric.Towbook.API.Stickering.Models;
using Extric.Towbook.WebShared;
using Extric.Towbook.WebShared.Multipart;
using Extric.Towbook.Integration;
using Extric.Towbook.Storage;
using Extric.Towbook.Utility;
using SkiaSharp;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Http.Features;
using Microsoft.AspNetCore.WebUtilities;
using Microsoft.Net.Http.Headers;
using static Extric.Towbook.API.ApiUtility;

namespace Extric.Towbook.API.Stickering.Controllers
{
    [Route("stickering/stickers")]
    public class StickeringPhotosController : ControllerBase
    {
        [Route("{stickerId}/photos")]
        [HttpGet]
        public IEnumerable<PhotoModel> Get(int stickerId)
        {
            // TODO: Can be made async
            return Towbook.Stickering.Photo.GetByStickerId(stickerId).Select(o => PhotoModel.Map(o));
        }

        [Route("{stickerId}/photos/{id}")]
        [HttpGet]
        public async Task<HttpResponseMessage> Get(int stickerId, int id)
        {
            // TODO: Can be made async
            var s = Towbook.Stickering.Sticker.GetById(stickerId);
            var p = await Towbook.Stickering.Photo.GetByIdAsync(id);

            if (p == null || s == null)
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound));

            HttpResponseMessage result = new HttpResponseMessage(HttpStatusCode.OK);
            result.Content = new StreamContent(new FileStream(await FileUtility.GetFileAsync(p.Location.Replace("%1", s.CompanyId.ToString())), FileMode.Open, FileAccess.Read, FileShare.ReadWrite));
            result.Content.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue(p.ContentType);

            return result;
        }

        [Route("{stickerId}/photos")]
        [HttpPost]
        [DisableFormValueModelBinding]
        public async Task<ObjectResult> Post([FromRoute] int stickerId, [FromQuery] string description = "")
        {
            //if (!Request.Content.IsMimeMultipartContent())
            if (!Web.HttpContext.Current.Request.HasFormContentType)
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.UnsupportedMediaType));
            }

            var u = await Towbook.User.GetByIdAsync(WebGlobal.CurrentUser.Id);
            // TODO: Can be made async
            var s = Towbook.Stickering.Sticker.GetById(stickerId);


            await ThrowIfNoCompanyAccessAsync(s?.CompanyId);


            var p = new Towbook.Stickering.Photo();

            // Save file
            string path = Path.GetTempPath();

            var provider = new MultipartFormDataStreamProvider(path);

            FileInfo fileInfo = null;

            FormOptions _defaultFormOptions = new FormOptions();

            var boundary = MultipartRequestHelper.GetBoundary(
                MediaTypeHeaderValue.Parse(Web.HttpContext.Current.Request.ContentType),
                _defaultFormOptions.MultipartBoundaryLengthLimit);

            var reader = new MultipartReader(boundary, Web.HttpContext.Current.Request.Body);

            var section = await reader.ReadNextSectionAsync();
            
            string targetFilePath = "";

            if (section != null)
            {
                var hasContentDispositionHeader =
                    ContentDispositionHeaderValue.TryParse(
                        section.ContentDisposition, out var contentDisposition);

                if (hasContentDispositionHeader)
                {
                    if (MultipartRequestHelper.HasFileContentDisposition(contentDisposition))
                    {
                        targetFilePath = Path.GetTempFileName();
                        using (var targetStream = System.IO.File.Create(targetFilePath))
                        {
                            await section.Body.CopyToAsync(targetStream);
                            fileInfo = new FileInfo(targetFilePath);
                            Console.WriteLine($"Copied the uploaded file '{targetFilePath}'");
                        }
                    }
                }
            }

            if (fileInfo == null || !fileInfo.Exists)
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.InternalServerError)
                {
                    Content = new StringContent("Request is missing file data.")
                });
            }

            string savedFile = targetFilePath;
            string originalFile = section.GetContentDispositionHeader().FileName.Value.TrimStart('"').TrimEnd('"');

            // Copy file and rename with new file name and correct extension
            var file = new FileInfo(savedFile);

            try
            {
                string fn = "";
                int photoId = -1;

                using (var i = SKBitmap.Decode(file.FullName))
                {
                    p.StickerId = Convert.ToInt32(stickerId);
                    p.ContentType = "image/jpg";
                    p.Description = description;
                    p.OwnerUserId = u.Id;
                    await p.SaveAsync(u);

                    fn = p.Location.Replace("%1", s.CompanyId.ToString());

                    if (!Directory.Exists(Path.GetDirectoryName(fn)))
                    {
                        Directory.CreateDirectory(Path.GetDirectoryName(fn));
                    }

                    // keep it proportionately correct, don't allow any dimension to exceed 1920. 
                    using (var resized = i.ResizeProportionately(1920))
                        resized.Save(fn, SKEncodedImageFormat.Jpeg);

                    photoId = p.Id;

                }
                var result = await FileUtility.SendFileAsync(fn);
                if (result.IsHttpSuccess())
                    System.IO.File.Delete(fn);

                await PushNotificationProvider.UpdateSticker(s.CompanyId, s.Id);

                return StatusCode((int)HttpStatusCode.Created, PhotoModel.Map(p));
               
                //return this.Request.CreateResponse(HttpStatusCode.Created, PhotoModel.Map(p), PerRequestJsonSettingsFormatter.Instance);
            }
            catch
            {
                throw;
                //throw new HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotImplemented) { Content = new StringContent("Invalid Image File. Only JPG, PNG, BMP and GIF files are supported.") });
            }
            finally
            {
                file.Delete();
            }
        }

        [Route("{stickerId}/photos/{id}")]
        [HttpDelete]
        public async Task<HttpResponseMessage> DeleteAsync(int stickerId, int id)
        {
            if (WebGlobal.CurrentUser.Type == Towbook.User.TypeEnum.Manager ||
                WebGlobal.CurrentUser.Type == Towbook.User.TypeEnum.SystemAdministrator)
            {
                var p = await Towbook.Stickering.Photo.GetByIdAsync(id);

                if (p == null)
                    throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound));

                // TODO: Can be made async
                var ss = Towbook.Stickering.Sticker.GetById(p.StickerId);

                await ThrowIfNoCompanyAccessAsync(ss?.CompanyId);
                
                await p.DeleteAsync();

                return new HttpResponseMessage(HttpStatusCode.NoContent);
            }
            else
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.Forbidden));
            }
        }
    }
}
