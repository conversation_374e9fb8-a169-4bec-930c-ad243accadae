using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using Extric.Towbook.Utility;
using Extric.Towbook.WebShared;
using System.Data.SqlClient;
using static Extric.Towbook.API.ApiUtility;
using Newtonsoft.Json;
using System.Collections.ObjectModel;
using NLog;
using Extric.Towbook.Accounts;
using Extric.Towbook.Dispatch;
using Extric.Towbook.Integration.MotorClubs.Services;
using Extric.Towbook.Integration;
using System.Threading.Tasks;
using Extric.Towbook.Queues;
using GeoCoordinatePortable;

namespace Extric.Towbook.API.Controllers
{
    [Route("location")]
    public sealed class LocationController : ControllerBase
    {
        private static readonly Logger logger = LogManager.GetCurrentClassLogger();

        public sealed class ReportLocationModel
        {
            public decimal Longitude { get; set; }
            public decimal Latitude { get; set; }
            public decimal Altitude { get; set; }
            public string Speed { get; set; }
            /// <summary>
            /// 1-100 - battery percentage remaining on device.
            /// </summary>
            public string Bearing { get; set; }
            public int Battery { get; set; }
            public string Extra { get; set; }
            public bool IsMock { get; set; }
            public long Id { get; set; }
        }

        public sealed class Geofence
        {
            public decimal Lat { get; set; }
            public decimal Lng { get; set; }
            public int CallId { get; set; }
            public int Status { get; set; }
            public int? RemoteCallId { get; set; }
            public int? RemoteCompanyId { get; set; }
        }

        public static async Task HandleGeofence(UserLocationHistoryItem h, string remoteIp, int companyId)
        {
            try
            {
                var sw = System.Diagnostics.Stopwatch.StartNew();
                var rdb = Core.GetRedisDatabase();
                var userId = h.UserId;
                var userGeo = new GeoCoordinate((double)h.Latitude,
                                (double)h.Longitude);

                var value = CompanyKeyValue.GetFirstValueOrNull(companyId, Provider.Towbook.ProviderId, 
                    "Towbook_Calls_GeofencedOnScene");

                var geofences = await Core.GetRedisValueAsync("geofences:" + userId);

                var enableGeofence = (value == null || value == "1" || geofences != null);

                if (!enableGeofence)
                    return;

                if (geofences != null)
                {
                    var geoList = JsonConvert.DeserializeObject<IEnumerable<Geofence>>(geofences);
                    Collection<int> toRemove = new Collection<int>();

                    foreach (var call in geoList)
                    {
                        // share gps to sending/remote company
                        if (call.RemoteCallId != null &&
                            call.RemoteCompanyId != null)
                        {
                            await PushNotificationProvider.ReportSubcontractorCallGps(
                                call.RemoteCompanyId.Value,
                                call.RemoteCallId.Value,
                                companyId,
                                h);
                        }

                        var pickupGeo = new GeoCoordinate((double)call.Lat,
                            (double)call.Lng);

                        var distance = userGeo.GetDistanceTo(pickupGeo);

                        string gpsSource = "";
                        var cv = Platform.ClientVersion.GetById(h.ClientVersionId);

                        if (cv?.Type == Platform.ClientVersionType.Android || Web.HttpContext.Current.IsAndroidDevice())
                            gpsSource = "android";
                        else if (cv?.Type == Platform.ClientVersionType.Iphone || Web.HttpContext.Current.IsAppleDevice())
                            gpsSource = "iOS";

                        if (distance < 400)
                        {
                            var key = "geofence_hit:" + call.CallId;
                            await rdb.StringIncrementAsync(key);
                            await rdb.KeyExpireAsync(key, TimeSpan.FromMinutes(3));

                            int counter = (int)await rdb.StringGetAsync(key);
                            
                            var en = await Entry.GetByIdNoCacheAsync(call.CallId);
                            
                            var highLimit = gpsSource == "iOS" ? 5 : 3;
                            var highDistanceRange = gpsSource == "iOS" ? 150 : 200;

                            if ((counter >= 2 && distance < 100) || 
                                (counter >= highLimit && distance < highDistanceRange))
                            {
                                // we have at least two breadcrumbs within 500 meters.

                                if (en != null && en.Status.Id < call.Status)
                                {
                                    en.Status = await Status.GetByIdAsync(call.Status, en.CompanyId);

                                    LogEventInfo lei = new LogEventInfo();
                                    lei.Level = LogLevel.Info;
                                    lei.Message = "Updated Status to on scene automatically";

                                    await en.Save(false, new AuthenticationToken() { UserId = h.UserId, ClientVersionId = h.ClientVersionId }, remoteIp);

                                    lei.Properties.Add("callId", en.Id);
                                    lei.Properties.Add("callNumber", en.CallNumber);
                                    lei.Properties.Add("count", counter);
                                    lei.Properties.Add("callGeoLocation", call.Lat + "," + call.Lng);
                                    lei.Properties.Add("userLocation", h.Latitude + "," + h.Longitude);
                                    lei.Properties.Add("distanceMeters", distance);
                                    lei.Properties.Add("companyId", en.CompanyId);
                                    lei.Properties.Add("companyName", en.Company?.Name);
                                    lei.Properties.Add("where", "LocationPost");
                                    lei.Properties.Add("userId", userId);

                                    if ((en.Account?.MasterAccountId).GetValueOrDefault() > 0)
                                        lei.Properties.Add("masterAccountName",
                                            MasterAccountTypes.GetName(en.Account.MasterAccountId));

                                    lei.Properties.Add("platform", gpsSource);

                                    var cr = await CallRequest.GetByDispatchEntryId(en.Id);
                                    if (cr != null)
                                    {

                                        var eventId = await DigitalDispatchService.NotifyCallUpdateEventAsync(en.CompanyId, en.AccountId,
                                            new
                                            {
                                                CallRequestId = cr.CallRequestId,
                                                NewStatusId = en.Status.Id,
                                                Latitude = h.Latitude,
                                                Longitude = h.Longitude,
                                                Source = gpsSource + "-geofence-ss",
                                                DriverId = en.DriverId,
                                                DriverName = en.Driver?.Name,
                                                TruckId = (en.Assets?.FirstOrDefault()?.Drivers?.FirstOrDefault()?.TruckId ?? en.TruckId),
                                                CurrentWaypointId = en?.Assets?.FirstOrDefault()?.Drivers?.FirstOrDefault()?.CurrentWaypointId ?? 0
                                            }.ToJson(null), DigitalDispatchService.CallUpdateType.StatusUpdate,
                                            ownerUserId: userId);

                                        lei.Properties.Add("queueItemId", eventId);
                                    }

                                    lei.Properties.Add("totalTime", sw.ElapsedMilliseconds);
                                    logger.Log(lei);
                                }

                                toRemove.Add(call.CallId);
                            }
                            else
                            {
                                var log = new LogEventInfo();
                                log.Message = "Geofence Hit";
                                log.Level = LogLevel.Info;
                                log.Properties.Add("count", counter);
                                log.Properties.Add("callId", call.CallId);
                                log.Properties.Add("userId", userId);
                                log.Properties.Add("callGeoLocation", call.Lat + "," + call.Lng);
                                log.Properties.Add("userLocation", h.Latitude + "," + h.Longitude);
                                log.Properties.Add("distanceMeters", distance);

                                if (en != null)
                                {
                                    log.Properties.Add("companyId", en.CompanyId);

                                    if (en.Company != null)
                                        log.Properties.Add("companyName", en.Company?.Name);
                                }

                                log.Properties.Add("where", "LocationPost");
                                log.Properties.Add("platform", gpsSource);

                                logger.Log(log);
                            }
                        }
                    }

                    if (toRemove.Any())
                    {
                        geoList = geoList.Where(o => !toRemove.Contains(o.CallId));
                        await Core.SetRedisValueAsync("geofences:" + userId, geoList.ToJson());
                    }

                }
            }
            catch (Exception exc)
            {
                logger.Log(LogLevel.Error, exc, "GPS Post Geofence Error");
            }
        }

        public static async Task AddGeofence(int userId, Geofence gf)
        {
            try
            {
                if (userId == 0 ||
                    gf.CallId < 1 ||
                    gf.Lat == 0 ||
                    gf.Lng == 0)
                    return;

                var enableGeofence = CompanyKeyValue.GetFirstValueOrNull(WebGlobal.CurrentUser?.CompanyId ?? 0,
                                      Provider.Towbook.ProviderId, "Towbook_Calls_GeofencedOnScene") == "1";

                var en = await Entry.GetByIdNoCacheAsync(gf.CallId);
                if (!enableGeofence)
                {
                    if (en == null ||
                        (en?.Account?.MasterAccountId != MasterAccountTypes.Allstate &&
                        en?.Account?.MasterAccountId != MasterAccountTypes.Swoop &&
                        en?.Account?.MasterAccountId != MasterAccountTypes.Agero &&
                        en?.Account?.MasterAccountId != MasterAccountTypes.Nsd &&
                        en?.Account?.MasterAccountId != MasterAccountTypes.Quest &&
                        en?.Account?.MasterAccountId != MasterAccountTypes.Urgently &&
                        en?.Account?.MasterAccountId != MasterAccountTypes.OonUrgently &&
                        en?.Account?.MasterAccountId != MasterAccountTypes.Towbook))
                        return;
                }
               

                string key = "geofences:" + userId;

                var json = await Core.GetRedisValueAsync(key);

                var geofences = new Collection<Geofence>();

                if (json != null)
                {
                    geofences = JsonConvert.DeserializeObject<Collection<Geofence>>(json)
                        .Where(o => o.CallId != gf.CallId && o.Status != o.Status).ToCollection();
                }

                await Core.SetRedisValueAsync(key,
                    geofences.Union(new Geofence[] { gf }).ToJson()
                );

                LogEventInfo lei = new LogEventInfo();
                lei.Level = LogLevel.Info;
                lei.Message = "Created Geofence";

                lei.Properties.Add("callGeoLocation", gf.Lat + "," + gf.Lng);
                lei.Properties.Add("callId", gf.CallId);

                if (en != null)
                {
                    lei.Properties.Add("callNumber", en.CallNumber);
                    lei.Properties.Add("poNumber", en.PurchaseOrderNumber);
                    lei.Properties.Add("masterAccountId", en.Account?.MasterAccountId);
                    lei.Properties.Add("masterAccountName", MasterAccountTypes.GetName(en.Account?.MasterAccountId ?? 0));
                    lei.Properties.Add("companyId", en.CompanyId);
                    lei.Properties.Add("companyName", en.Company?.Name);
                }

                logger.Log(lei);
            }
            catch (Exception r)
            {
                logger.Log(LogLevel.Error, r, "Error adding geofence");
            }
        }

        public sealed class Container
        {
            public string RemoteIp { get; set; }
            public int UserId { get; set; }
            public int CompanyId { get; set; }
            public UserLocationHistoryItem Item { get; set; }
        }

        private async Task<bool> SendToGpsWriteQueue(int companyId, int userId, string remoteIp, UserLocationHistoryItem loc)
        {
            var container = new Container()
            {
                UserId = loc.UserId,
                CompanyId = companyId,
                RemoteIp = remoteIp,
                Item = loc
            };

            try
            {
                var rjq = new RedisJobQueue(Core.GetDedicatedGpsRedisConnection(), "gps_inbound");

                await rjq.AddJobAsync(container.ToJson());

                return true;
            }
            catch(Exception ex)
            {
                logger.Log(LogLevel.Error, ex, "Error sending to gps write queue: " + ex.Message);
                await Process(container);
                return false;
            }
        }

        /// <summary>
        /// Use to report your location to the server.
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        //routes.MapHttpRoute(
        //    name: "Location Services",
        //    routeTemplate: "locaton/{action}",
        //    defaults: new { action = "Get", controller = "Location" }
        //    ).DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };
        [HttpPost]
        [Route("{id?}")]
        [Route("Post")]
        public async Task<object> Post(ReportLocationModel model)
        {
            if (WebGlobal.CurrentUser == null)
                return new HttpResponseMessage(HttpStatusCode.NoContent);

            var currentUserId = WebGlobal.CurrentUser.Id;
            var primaryCompanyId = WebGlobal.CurrentUser.PrimaryCompanyId;

            decimal speed = 0;
            decimal bearing = 0;

            if (model.IsMock)
                return new HttpResponseMessage(HttpStatusCode.NoContent);

            decimal.TryParse(model.Speed, out speed);
            decimal.TryParse(model.Bearing, out bearing);

            var ulhi = new UserLocationHistoryItem()
            {
                Latitude = decimal.Round(model.Latitude, 6),
                Longitude = decimal.Round(model.Longitude, 6),
                UserId = currentUserId,
                Timestamp = DateTime.Now,
                GpsSource = Web.HttpContext.Current.IsAndroidDevice() ? "android" :
                    Web.HttpContext.Current.IsAppleDevice() ? "iOS" : null,
                ClientVersionId = (await this.GetCurrentTokenAsync())?.ClientVersionId ?? 0,
                Battery = model.Battery,
                Speed = speed == 0 ? null : (decimal?) decimal.Round(speed, 2),
                Bearing = bearing == 0 ? null : (decimal?) decimal.Round(bearing,2)
            };

            var result = await SendToGpsWriteQueue(primaryCompanyId, currentUserId, this.GetRequestingIp(), ulhi);

            if (result)
                return new HttpResponseMessage(HttpStatusCode.Accepted);
            else
                return new HttpResponseMessage(HttpStatusCode.Created);
        }

        public static async Task Process(Container container)
        {
            if (await Core.GetRedisValueAsync("gps:" + container.UserId) != null)
            {
                await HandleGeofence(container.Item, container.RemoteIp, container.CompanyId);
                return;
            }

            try
            {
                await container.Item.Save(container.CompanyId);
                await Core.SetRedisValueAsync("gps:" + container.UserId, "1", TimeSpan.FromSeconds(29));

                await HandleGeofence(container.Item, container.RemoteIp, container.CompanyId);
            }
            catch (SqlException timedOut)
            {
                logger.Log(LogLevel.Error, timedOut, "LocationController Error: " + timedOut.Message);
            }
        }

        /// <summary>
        /// Use to retrieve location information so you can know whether to send GPS updates.
        /// </summary>
        /// <returns></returns>
        //routes.MapHttpRoute(
        //    name: "Location Services",
        //    routeTemplate: "locaton/{action}",
        //    defaults: new { action = "Get", controller = "Location" }
        //    ).DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };
        [HttpGet]
        [Route("{id?}")]
        [Route("Get")]
        public dynamic Get(int? id=null)
        {
            return new
            {
                UserConsentDate = DateTime.Now,
                ContinuousUpdatesEnabled = true,
                OnDemandUpdatesEnabled = true,
                ReportLocationWhenUpdating = true
            };
        }

        //routes.MapHttpRoute(
        //    name: "Location Services",
        //    routeTemplate: "locaton/{action}",
        //    defaults: new { action = "Get", controller = "Location" }
        //    ).DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };
        [HttpGet]
        [ApiPermission(Extric.Towbook.User.TypeEnum.Manager)]
        [Route("Push")]
        public async Task<HttpResponseMessage> Push()
        {
            await PushNotificationProvider.RequestLocationUpdate(WebGlobal.CurrentUser.PrimaryCompanyId);

            return new HttpResponseMessage(HttpStatusCode.NoContent);
        }

        //routes.MapHttpRoute(
        //    name: "Location Services",
        //    routeTemplate: "locaton/{action}",
        //    defaults: new { action = "Get", controller = "Location" }
        //    ).DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };
        [HttpGet]
        [ApiPermission(Extric.Towbook.User.TypeEnum.Manager, Extric.Towbook.User.TypeEnum.Dispatcher)]
        [Route("History")]
        public async Task<object> History([FromQuery]int userId, [FromQuery]DateTime start, [FromQuery] DateTime end)
        {
            if (!(await WebGlobal.CurrentUser.Company.HasFeatureAsync(Generated.Features.Mapping_Replay)))
                return new HttpResponseMessage(HttpStatusCode.BadRequest)
                {
                    Content = new StringContent("Your account doesn't have the feature enabled to query location history.")
                };

            // limit to 24 hrs.
            if ((end - start).TotalHours > 24)
                end = start.AddHours(24);

            var user = await Towbook.User.GetByIdAsync(userId);

            await ThrowIfNoCompanyAccessAsync(user?.CompanyId, "user");
            
            var list = UserLocationHistoryItem.GetByUserId(userId, start, end);

            return list;
        }

        public class UserLocationHistoryItemWithTruckId : UserLocationHistoryItem
        {
            public UserLocationHistoryItemWithTruckId() { }

            public UserLocationHistoryItemWithTruckId(UserLocationHistoryItem i)
            {
                this.Id = i.Id;
                this.Latitude = i.Latitude;
                this.Longitude = i.Longitude;
                this.UserId = i.UserId;
                this.Timestamp = i.Timestamp;
                this.GpsSource = i.GpsSource;
                
                this.ClientVersionId = i.ClientVersionId;

                this.Bearing = i.Bearing;
                this.Speed = i.Speed;
                this.Battery = i.Battery;

                if (this.Speed.GetValueOrDefault() > 0)
                    this.Speed = this.Speed * 2.2369M;
            }

            public int TruckId { get; set; }
        }
        
        [HttpGet("CurrentLocations")]
        public async Task<IEnumerable<UserLocationHistoryItemWithTruckId>> CurrentLocations()
        {
            return await GetCurrentLocations(await WebGlobal.GetCompaniesAsync());
        }

        internal static async Task<IEnumerable<UserLocationHistoryItemWithTruckId>> GetCurrentLocations(Company.Company[] companies)
        {
            if (WebGlobal.CurrentUser.Type != Towbook.User.TypeEnum.Manager &&
                WebGlobal.CurrentUser.Type != Towbook.User.TypeEnum.Dispatcher)
                return Array.Empty<UserLocationHistoryItemWithTruckId>();

            if (!await WebGlobal.CurrentUser.Company.HasFeatureAsync(Generated.Features.Mapping))
                return Array.Empty<UserLocationHistoryItemWithTruckId>();

            var list = new List<UserLocationHistoryItemWithTruckId>();

            foreach (var group in (await UserLocationHistoryItem.GetRecentByCompanies(companies)).GroupBy(o => o.UserId))
            {
                list.Add(group.OrderByDescending(o => o.Timestamp).Select(o => new UserLocationHistoryItemWithTruckId(o)).FirstOrDefault());
            }

            var defaults = DriverTruckDefault.GetByCompanyId(companies.Select(o => o.Id).ToArray());

            foreach (var group in TruckLocationHistoryItem.GetRecentByCompanies(companies).GroupBy(o => o.TruckId))
            {
                var f = group.OrderByDescending(o => o.Timestamp).FirstOrDefault();

                if (f != null)
                {
                    var defaultTruck = defaults.Where(o => o.TruckId == f.TruckId).FirstOrDefault();

                    if (defaultTruck != null)
                    {
                        var userId = (await Driver.GetByIdAsync(defaultTruck.DriverId))?.UserId;
                        if (userId != null)
                        {
                            list = list.Where(o => o.UserId != userId.Value).ToList();

                            list.Add(new UserLocationHistoryItemWithTruckId()
                            {
                                Id = (long)-f.Id,
                                TruckId = f.TruckId,
                                UserId = userId.Value,
                                Latitude = f.Latitude,
                                Longitude = f.Longitude,
                                Timestamp = f.Timestamp,
                                GpsSource =  f.GetGpsSourceName()
                            });
                        }
                    }
                }
            }

            return list;
        }

    }
}
