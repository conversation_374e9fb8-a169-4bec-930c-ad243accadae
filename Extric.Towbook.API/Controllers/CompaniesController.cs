using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web;
using Extric.Towbook.Web;
using Extric.Towbook.Utility;
using Extric.Towbook.Company;
using Extric.Towbook.WebShared;
using Extric.Towbook.Licenses;
using Extric.Towbook.API.Models;
using Extric.Towbook.Integrations.Email;
using Extric.Towbook.Integration;
using Microsoft.AspNetCore.Mvc;

namespace Extric.Towbook.API.Controllers
{
    [Route("companies")]
    public class CompaniesController : ControllerBase
    {
        private sealed class SourceModel
        {
            public int CompanyId { get; set; }
            public string Source { get; set; }
        }

        /// <summary>
        /// Returns the list of companies, for the Accounts Report, based on the CompaniesGetByStatusTypeStateDate StoreProcedure
        /// </summary>
        /// <returns>A list, with Company data</returns>
        [HttpGet]
        [Route("")]
        public async Task<object> GetAsync()
        {
            if (WebGlobal.CurrentUser.Type == Towbook.User.TypeEnum.SystemAdministrator)
                return await AdminCompaniesAsync();
            else
                return await ClientCompaniesAsync();
        }

        /// <summary>
        /// Get User By CompanyId for the current user
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("{id}/users")] //TODO is this correct
        [ApiPermission(true, Extric.Towbook.User.TypeEnum.SystemAdministrator)]
        public async Task<List<UserModel>> Users(int id)
        {
            if (!WebGlobal.CurrentUser.HasAccessToCompany(id))
            {
                throw new TowbookException("Invalid companyId or you don't have access to it.");
            }
        
            return await UserModel.Map(Towbook.User.GetByCompanyId(id));
        }

        private async Task<IEnumerable<CompanyConfigModel>> ClientCompaniesAsync()
        {
            return (await WebGlobal.GetCompaniesAsync()).Select(o => CompanyConfigModel.Map(o)).OrderBy(o => o.Name);
        }

        private async Task<object> AdminCompaniesAsync()
        {
            if (WebGlobal.CurrentUser.Type != Towbook.User.TypeEnum.SystemAdministrator)
                return null;

            var nvc = HttpUtility.ParseQueryString(Web.HttpContext.Current.Request.QueryString.Value);

            int? status = (!String.IsNullOrEmpty(nvc["status"])) ? (int?)Convert.ToInt32(nvc["status"]) : null;
            int? type = (!String.IsNullOrEmpty(nvc["type"])) ? (int?)Convert.ToInt32(nvc["type"]) : null;
            string state = string.IsNullOrEmpty(nvc["state"]) ? null : nvc["state"];
            DateTime? dateStart = (!string.IsNullOrEmpty(nvc["dateStart"]) ? (DateTime?)Convert.ToDateTime(nvc["dateStart"]) : null);
            DateTime? dateEnd = (!string.IsNullOrEmpty(nvc["dateEnd"]) ? (DateTime?)Convert.ToDateTime(nvc["dateEnd"]) : null);

            // new admin fields to filter by 
            int? classId = !string.IsNullOrEmpty(nvc["classId"]) ? (int?) Convert.ToInt32(nvc["classId"]) : null;
            int? statusId = !string.IsNullOrEmpty(nvc["statusId"]) ? (int?)Convert.ToInt32(nvc["statusId"]) : null;
            int? accountManagerId = !string.IsNullOrEmpty(nvc["accountManagerId"]) ? (int?)Convert.ToInt32(nvc["accountManagerId"]) : null;
            int? tagId = !string.IsNullOrEmpty(nvc["tagId"]) ? (int?)Convert.ToInt32(nvc["tagId"]) : null;

            dateStart = WebGlobal.OffsetDateTimeNullable(dateStart, true);
            dateEnd = WebGlobal.OffsetDateTimeNullable(dateEnd, true);

            IEnumerable<Company.Company> result = null;

            if (nvc["page"] != null)
            {
                int pageSize = 50;
                if (nvc["size"] != null)
                {
                    pageSize = Convert.ToInt32(nvc["size"]);
                }

                result = await Company.Company.GetAsync(Convert.ToInt32(nvc["page"]), pageSize, Company.Company.CompanySortEnum.CompanyId, Company.Company.SortOrder.Descending);
            }
            else
            {
                result = await Company.Company.GetByStatusTypeStateDateAsync(status, type, state, dateStart, dateEnd);

                if (status.HasValue && (status.Value == 2 || status.Value == 3) || type.HasValue && (type.Value == 1))
                {
                    result = result.OrderByDescending(o => o.CreateDate);
                }
                else
                {
                    result = result.OrderByDescending(o => o.Id);
                }

                bool csv = nvc["csv"] == "1" ? true : false;
                string exportName = nvc["exportName"];

                if (csv)
                {
                    var emailAddresses = await EmailAddress.GetAllAsync();

                    if (exportName == "funnel")
                    {
                        
                        using var conn = Core.GetConnection();
                            var sources = (await Dapper.SqlMapper.QueryAsync<SourceModel>(conn,
                                @"
SELECT c.CompanyId,
       Source
FROM vwcompanies c WITH (NOLOCK)
CROSS APPLY (
    SELECT TOP 1 JSON_VALUE(Content, '$.source') AS Source
    FROM CompanyNotes cn WITH (NOLOCK)
    WHERE cn.CompanyId = c.CompanyId
    AND cn.Content LIKE '{%'
) cn where cn.Source is not null
", null)).Where(o => !string.IsNullOrEmpty(o.Source));

                        return result.Select(c => new
                        {
                            No = c.Id,
                            Name = c.Name,
                            State = c.State,
                            Phone = c.Phone,
                            Email = (c.FirstUser != null) ? c.FirstUser.Email : string.Empty,
                            Contact = (c.FirstUser != null) ? c.FirstUser.FullName : string.Empty,
                            TowbookNetEmail = emailAddresses.FirstOrDefault(f => f.CompanyId == c.Id)?.Address ?? string.Empty,
                            Trks = c.TruckCount,
                            Drvr = c.DriverCount,
                            All = c.CallCountForever,
                            D365 = c.CallCount365,
                            D180 = c.CallCount180,
                            D90 = c.CallCount90,
                            D60 = c.CallCount60,
                            D30 = c.CallCount30,
                            LastLogin = (c.LastLogin.HasValue) ? c.LastLogin.Value.ToUniversalTime() : (DateTime?)null,
                            CreateDate = c.CreateDate.ToUniversalTime(),
                            Source = sources.FirstOrDefault(r => r.CompanyId == c.Id)?.Source ?? ""
                        }).ToList();
                    }
                    else
                    {
                        var list = result.Select(s => new
                        {
                            CompanyId = s.Id,
                            CompanyType = s.Type.ToString(),
                            CompanyCountry = s.CountryFullName,
                            CompanyName = s.Name ?? string.Empty,
                            CompanyAddress = s.Address ?? string.Empty,
                            CompanyState = s.State ?? string.Empty,
                            CompanyCity = s.City ?? string.Empty,
                            CompanyZip = s.Zip ?? string.Empty,
                            CompanyPhone = s.Phone ?? string.Empty,
                            CompanyFax = s.Fax ?? string.Empty,
                            CompanyEmail = s.FirstUser?.Email ?? string.Empty,
                            TowbookNetEmail = emailAddresses.FirstOrDefault(f => f.CompanyId == s.Id)?.Address ?? string.Empty,
                            CompanyWebsite = s.Website ?? string.Empty,
                            CompanyFirstUser = s.FirstUser?.FullName ?? string.Empty,
                            CompanyStatus = s.Status,
                            CompanyTruckCount = s.TruckCount,
                            CompanyUserCount = s.UserCount,
                            CompanyDriverCount = s.DriverCount,
                            CompanyRateItemCount = s.RateItemCount,
                            CompanyCallCountForever = s.CallCountForever,
                            CompanyCallCount365Days = s.CallCount365,
                            CompanyCallCount180Days = s.CallCount180,
                            CompanyCallCount90Days = s.CallCount90,
                            CompanyCallCount60Days = s.CallCount60,
                            CompanyCallCount30Days = s.CallCount30,
                            CompanyFirstLoginDate = s.FirstLogin,
                            CompanyLastLoginDate = s.LastLogin,
                            CompanyDaysLeftInTrial = s.DaysLeft,
                            CompanyMonthlyFee = s.MonthlyFee,
                            CompanyYearAverage = s.YearAverage
                        }).ToList();

                        return list;
                    }
                }
            }

            return result.Select(c => new
            {
                Id = c.Id,
                Company = c.Name,
                Phone = c.Phone,
                Fax = c.Fax,
                Email = c.Email,
                Address = c.Address,
                City = c.City,
                State = c.State,
                Zip = c.Zip,
                Latitude = c.Latitude,
                Longitude = c.Longitude,
                Country = c.Country,
                //CountryName = c.CountryName,
                CreateDate = c.CreateDate.ToUniversalTime(),
                AccountingMethod = c.AccountingMethod,
                //AccountingSystemId = c.AccountingSystemId,
                TimezoneOffset = c.TimezoneOffset,
                TimezoneUseDST = c.TimezoneUseDST,
                TaxMode = c.TaxMode,
                InvoicingTagline = c.InvoicingTagline,
                InvoicingLateAPR = c.InvoicingLateAPR,
                InvoicingLateGracePeriod = c.InvoicingLateGracePeriod,
                Type = c.Type,
                News = c.News,
                Status = c.Status,
                TruckCount = c.TruckCount,
                FirstUser = c.FirstUser?.FullName ?? string.Empty,
                FirstUserEmail = c.FirstUser?.Email ?? string.Empty,
                UserCount = c.UserCount,
                DriverCount = c.DriverCount,
                RateItemCount = c.RateItemCount,
                CallCountForever = c.CallCountForever,
                CallCount365 = c.CallCount365,
                CallCount180 = c.CallCount180,
                CallCount90 = c.CallCount90,
                CallCount60 = c.CallCount60,
                CallCount30 = c.CallCount30,
                YearAverage = c.YearAverage,
                FirstLogin = c.FirstLogin?.ToUniversalTime(),
                LastLogin = c.LastLogin?.ToUniversalTime(),
                DaysLeft = c.DaysLeft,
                MonthlyFee = c.MonthlyFee,
                Notes = "",
                OpenItems = "",
                InterimNotes = ""
            }).ToCollection();
        }

        /// <summary>
        /// Internal Get (used by AJAX)
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [NonAction]
        public async Task<CompanyModel> InternalGetCompanyAsync(int id)
        {
            var c = await Company.Company.GetByIdAsync(id);

            if (c != null)
            {
                return CompanyModel.Map(c, CompanyLicenseKeyValue.GetByCompanyId(c.Id));
            }
            else
            {
                return null;
            }
        }

        /// <summary>
        /// Retrieve details for the specified company
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("{id}")]
        public async Task<CompanyModel> GetAsync(int id)
        {
            var companyModel = await InternalGetCompanyAsync(id);

            if (companyModel == null)
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound)
                {
                    Content = new StringContent("The specified companyId doesn't exist.")
                });
            }

            if (!WebGlobal.CurrentUser.HasAccessToCompany(companyModel.Id))
            {
                throw new HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound)
                {
                    Content = new StringContent("The specified companyId doesn't exist or you don't have access to it.")
                });
            }  

            return companyModel;
        }

        [HttpPut("{id}")]
        [ApiPermission(true, Extric.Towbook.User.TypeEnum.SystemAdministrator, Extric.Towbook.User.TypeEnum.Manager)]
        public async Task Put(int id, CompanyModel model)
        {
            model.Id = id;

            if (!WebGlobal.CurrentUser.HasAccessToCompany(id))
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound)
                {
                    Content = new StringContent("The specified companyId doesn't exist or you don't have access to it.")
                });

            var c = await Company.Company.GetByIdAsync(id);

            if (c != null)
            {
                c = CompanyModel.Map(model, c);

                if (model.ReferenceCode != null)
                {
                    CompanyKeyValue.InsertOrUpdate(model.Id, Provider.Towbook.ProviderId, "ReferenceCode", model.ReferenceCode);
                }

                await c.Save();
            }
            else
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound)
                {
                    Content = new StringContent("The specified companyId doesn't exist.")
                });
        }

        [HttpPost]
        [Route("")]
        [ApiPermission(true, Extric.Towbook.User.TypeEnum.SystemAdministrator)]
        public void Post([FromBody] CompanyInterimNotes data)
        {
            data.Save();
        }

        private static object GenerateCSVReport<T>(string reportName, DateTime? startDate, DateTime? endDate, List<T> result)
        {
            CsvExporter<T> x = new CsvExporter<T>();

            var r = new HttpResponseMessage()
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(x.ExportCollection(result.ToCollection()))
            };

            var sDate = startDate.HasValue ? startDate : DateTime.Now;
            var eDate = endDate.HasValue ? endDate : DateTime.Now;

            r.Content.Headers.Add("Content-Disposition", string.Format("attachment;filename=Towbook-" + reportName + "-{0}-to-{1}.csv", sDate.Value.ToShortDateString().Replace("/", "-"),
                eDate.Value.ToShortDateString().Replace("/", "-")));

            return r;
        }
    }
}
