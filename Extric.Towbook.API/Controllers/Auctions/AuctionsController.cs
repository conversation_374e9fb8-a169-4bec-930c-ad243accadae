using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using static Extric.Towbook.API.ApiUtility;
using Extric.Towbook.API.Models.Auctions;
using Extric.Towbook.WebShared;
using Extric.Towbook.Impounds;
using Extric.Towbook.Auctions;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Extric.Towbook.Web;

namespace Extric.Towbook.API.Auctions.Controllers
{
 
    [Route("auctions")]
    public class AuctionsController : ControllerBase
    {
        //routes.MapHttpRoute(
        //    name: "Auction_default_POST",
        //    routeTemplate: "auctions/{action}",
        //    defaults: new { controller = "Auctions", id = RouteParameter.Optional, action = "Post" },
        //        constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "POST" }) })
        //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Auctions.Controllers" } } };
        [HttpGet]
        [Route("")]
        [Route("get")]
        public async Task<IEnumerable<AuctionModel>> Get(
            [FromQuery] int? companyId = null,
            [FromQuery] int? lotId = null,
            [FromQuery] string include = "current",
            [FromQuery] int? page = null,
            [FromQuery] int? size = null)
        {
            var companies = await WebGlobal.GetCompaniesAsync();

            if (companyId != null)
                companies = companies.Where(w => w.Id == companyId.Value).ToArray();

            await ThrowIfNoCompanyAccessAsync(companies.Select(s => s.Id).ToArray(), "company");

            var lots = Lot.GetByCompany(companies, false);

            if (lotId != null)
                lots = lots.Where(w => w.Id == lotId.Value).ToList();

            await ThrowIfNoCompanyAccessAsync(lots.Select(s => s.CompanyId).ToArray(), "lot location");

            if (WebGlobal.CurrentUser.IsAccountTypeUser())
                return new List<AuctionModel>();


            IncludeType includeType = IncludeType.Current;
            if(include != null)
            {
                if (include.ToLowerInvariant() == "all")
                    includeType = IncludeType.All;

                if (include.ToLowerInvariant() == "completed")
                    includeType = IncludeType.Completed;
            }

            var auctions = Auction.GetByCompanyIds(companies.Select(s => s.Id).ToArray(), lots.Select(s => s.Id), includeType, page, size);

            var ret = await InternalGet(companies.ToList(), lots, auctions, null, null);

            return ret;
        }

        [ApiExplorerSettings(IgnoreApi = true)]
        public async Task<IEnumerable<AuctionModel>> InternalGet(
            IEnumerable<Company.Company> companies,
            IEnumerable<Lot> lots,
            IEnumerable<Auction> auctions,
            IEnumerable<EntryAuctionDetail> details = null,
            IEnumerable<AuctionPhotoModel> photos = null)
        {

            if (details != null && details.Count() > 0)
            {
                var calls = (await API.Controllers.CallsController.GetByIds(companies.Select(s => s.Id).ToArray(), details.Select(s => s.DispatchEntryId).ToArray())).ToList();

                var callIdsNotFound = details.Where(w => calls.FirstOrDefault(f => f.AuctionDetails?.Id == w.Id) == null).Select(s => s.DispatchEntryId).ToArray();

                if (callIdsNotFound.Any())
                {
                    var foundCalls = (await Dispatch.Entry.GetByIdsAsync(callIdsNotFound)).ToArray();
                    if (foundCalls.Any())
                        calls.AddRange(await Task.WhenAll(foundCalls.Select(Dispatch.CallModels.CallModel.MapAsync)));
                }
                if (calls != null && calls.Count() > 0)
                {
                    return auctions.Select(s =>
                    {
                        var model = AuctionModel.Map(
                                        companies.FirstOrDefault(f => f.Id == s.CompanyId),
                                        s,
                                        lots?.FirstOrDefault(f => f.Id == s.ImpoundLotId.GetValueOrDefault()));


                        var items = new List<AuctionItemModel>();
                        foreach(var d in details.Where(w => w.AuctionId == s.Id))
                        {
                            var auctionItemModel = AuctionItemModel.Map(d,
                                                calls.FirstOrDefault(w => w.Id == d.DispatchEntryId),
                                                photos?.Where(w => w.ItemId == d.Id));
                            
                            if (auctionItemModel != null)
                                items.Add(auctionItemModel);
                        }
                        model.Items = items;

                        model.ItemCount = s.ItemCount;
                        model.PhotoCount = s.PhotoCount;

                        return model;
                    });
                }
            }

            return auctions.Select(s =>
            {

                var model = AuctionModel.Map(
                    companies.FirstOrDefault(f => f.Id == s.CompanyId),
                    s,
                    lots?.FirstOrDefault(f => f.Id == s.ImpoundLotId.GetValueOrDefault()));

                return model;
            });
        }

        //routes.MapHttpRoute(
        //    name: "Auctions_Default_actions",
        //    routeTemplate: "auctions/{id}/{action}",
        //    defaults: new { controller = "Auctions", id = RouteParameter.Optional, action = RouteParameter.Optional })
        //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Auctions.Controllers" } } };
        [HttpGet]
        [Route("{id}")]
        [Route("{id}/get")]
        public async Task<AuctionModel> Get(int id)
        {

            var auction = Auction.GetById(id);
            await ThrowIfNoCompanyAccessAsync(auction?.CompanyId, "Auction");
            
            var company = (await WebGlobal.GetCompaniesAsync()).FirstOrDefault(f => f.Id == auction.CompanyId);
            ThrowIfNotFound(company, "Company");

            var lot = await Lot.GetByIdAsync(auction.CompanyId, auction.ImpoundLotId ?? 0);

            var details = EntryAuctionDetail.GetByAuctionIds(new int[] { auction.Id });
            var auctionPhotos = await AuctionItemPhotosController.InternalGetPhotosByAuctionDetailsAsync(details.ToArray(), true);

            var ret = await InternalGet(
                new Company.Company[] { company }, 
                lot != null ? new Lot[] {lot} : null,
                new Auction[] { auction }, 
                details,
                auctionPhotos);

            return ret.FirstOrDefault();
        }

        //routes.MapHttpRoute(
        //    name: "Auction_default_POST",
        //    routeTemplate: "auctions/{action}",
        //    defaults: new { controller = "Auctions", id = RouteParameter.Optional, action = "Post" },
        //        constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "POST" }) })
        //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Auctions.Controllers" } } };

        [HttpPost]
        [Route("")]
        [Route("post")]
        public async Task<AuctionModel> Post(AuctionModel model)
        {
            ThrowIfNotFound(model);

            Lot lot = null;
            if (model.Location?.Id > 0)
                lot = Lot.GetByCompany(await WebGlobal.GetCompaniesAsync(), false).FirstOrDefault(f => f.Id == model.Location.Id.Value);

            var company = await Company.Company.GetByIdAsync(lot?.CompanyId ?? (model.CompanyId == 0 ? WebGlobal.CurrentUser.CompanyId : model.CompanyId));
            await ThrowIfNoCompanyAccessAsync(company.Id, "Company");

            var auction = new Auction()
            {
                CompanyId = company.Id,
                Name = model.Name,
                StartDate = model.StartDate,
                EndDate = model.EndDate,
                ImpoundLotId = lot?.Id,
                Description = Core.HtmlEncode(model.Description),
                RemoteId = model.RemoteId
            };

            if (!string.IsNullOrEmpty(model.Name))
                auction.Name = Core.HtmlEncode(model.Name);

            if (auction.RemoteId != null)
                auction.RemoteSystem = "Joyride";

            auction.Save(WebGlobal.CurrentUser);

            return await AuctionModel.MapAsync(company, auction, lot);
        }

        //routes.MapHttpRoute(
        //    name: "Auctions_Default_actions",
        //    routeTemplate: "auctions/{id}/{action}",
        //    defaults: new { controller = "Auctions", id = RouteParameter.Optional, action = RouteParameter.Optional })
        //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Auctions.Controllers" } } };
        [HttpPut]
        [Route("{id}")]
        [Route("{id}/put")]
        public async Task<AuctionModel> Put(int id, AuctionModel model)
        {
            var auction = Auction.GetById(id);
            await ThrowIfNoCompanyAccessAsync(auction.CompanyId, "Auction");

            var company = await Company.Company.GetByIdAsync(auction.CompanyId);

            Lot lot = null;
            if (model.Location?.Id > 0)
                lot = Lot.GetByCompany(await WebGlobal.GetCompaniesAsync(), false).FirstOrDefault(f => f.Id == model.Location.Id.Value);

            if (lot != null)
                await ThrowIfNoCompanyAccessAsync(lot.CompanyId, "Lot");

            auction.StartDate = model.StartDate;
            auction.EndDate = model.EndDate;
            auction.ImpoundLotId = lot?.Id;
            auction.Description = Core.HtmlEncode(model.Description);

            if (!string.IsNullOrEmpty(model.Name))
                auction.Name = Core.HtmlEncode(model.Name);

            auction.Save(WebGlobal.CurrentUser);

            return await AuctionModel.MapAsync(company, auction, lot);
        }

        [ApiPermission(Towbook.User.TypeEnum.Manager, Towbook.User.TypeEnum.SystemAdministrator)]
        [HttpDelete]
        [Route("{id}")]
        [Route("{id}/delete")]
        public async Task<HttpResponseMessage> DeleteAsync(int id)
        {
            var auction = Auction.GetById(id);
            await ThrowIfNoCompanyAccessAsync(auction?.CompanyId, "Auction");

            var details = EntryAuctionDetail.GetByAuctionId(auction.Id);
            if(details != null && details.Count() > 0)
            {
                foreach (var detail in details)
                {
                    detail.AuctionId = (int?)null;

                    await detail.SaveAsync(this.GetCurrentToken(), this.GetRequestingIp());
                }

                var auctionPhotos = AuctionPhoto.GetByAuctionId(auction.Id);
                if(auctionPhotos != null && auctionPhotos.Count() > 0)
                {
                    foreach(var ap in auctionPhotos)
                    {
                        ap.Delete();
                    }
                }
            }

            auction.Deleted = true;
            auction.DeletedByUserId = WebGlobal.CurrentUser.Id;
            auction.DeleteDate = DateTime.Now;

            auction.Save();

            return new HttpResponseMessage()
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent("Auction was successfully deleted")
            };
        }

        [HttpGet("{auctionId}/items")]
        public async Task<IEnumerable<AuctionItemModel>> ItemsAsync(int auctionId)
        {
            await ThrowIfFeatureNotAssignedAsync(Generated.Features.Impounds_ImpoundAuctions);

            var auction = Auction.GetById(auctionId);
            await ThrowIfNoCompanyAccessAsync(auction?.CompanyId, "Auction");

            var items = EntryAuctionDetail.GetByAuctionId(auctionId);

            var companyIds = (await WebGlobal.GetCompaniesAsync()).Select(s => s.Id).ToArray();
            var calls = await API.Controllers.CallsController.GetByIds(companyIds, items.Select(s => s.DispatchEntryId).ToArray());
            var photos = await AuctionItemPhotosController.InternalGetPhotosByAuctionDetailsAsync(items.ToArray(), true);

            return items
                .OrderByDescending(o => o.Id)
                .Select(d =>
                    AuctionItemModel.Map(
                        d,
                        calls.FirstOrDefault(w => w.Id == d.DispatchEntryId),
                        photos?.Where(w => w.ItemId == d.Id)));
        }

        [HttpGet]
        [Route("{auctionId}/items/{id}")]
        [Route("{auctionId}/items/{id}/get")]
        public async Task<AuctionItemModel> Get(int auctionId, int id)
        {
            await ThrowIfFeatureNotAssignedAsync(Generated.Features.Impounds_ImpoundAuctions);

            var auction = Auction.GetById(auctionId);
            await ThrowIfNoCompanyAccessAsync(auction?.CompanyId, "Auction");

            var detail = EntryAuctionDetail.GetById(id);
            ThrowIfNotFound(detail, "Auction Details");

            if (auction.Id != detail.AuctionId)
            {
                throw new HttpResponseException(new HttpResponseMessage()
                {
                    StatusCode = HttpStatusCode.BadRequest,
                    Content = new StringContent("The auction does not contain the id specified in the URL.")
                });
            }

            var e = await Dispatch.Entry.GetByIdAsync(detail.DispatchEntryId);
            await ThrowIfNoCompanyAccessAsync(e?.CompanyId);

            var calls = await API.Controllers.CallsController.GetByIds((await WebGlobal.GetCompaniesAsync()).Select(s => s.Id).ToArray(), new int[] { detail.DispatchEntryId }.ToArray());
            var photos = await AuctionItemPhotosController.InternalGetPhotosByAuctionDetailsAsync(new EntryAuctionDetail[] { detail }.ToArray(), true);
            return AuctionItemModel.Map(
                            detail,
                            calls.FirstOrDefault(f => f.Id == detail.DispatchEntryId),
                            photos?.Where(w => w.ItemId == detail.Id));
        }

        public class AuctionBatchItemRequest
        {
            /// <summary>
            /// Equivalent to an array of Impound Ids
            /// </summary>
            public int[] StockNumbers { get; set; }
        }

        [HttpPut]
        [Route("{auctionId}/items")]
        [Route("{auctionId}/items/put")]
        public async Task<IEnumerable<AuctionItemModel>> Put(int auctionId, AuctionBatchItemRequest model)
        {
            ThrowIfNotFound(model?.StockNumbers, "Stock Numbers");

            var ret = await InternalInsertOrUpdateAuctionItemsAsync(auctionId, model.StockNumbers, true);

            return ret;
        }

        [HttpDelete]
        [Route("{auctionId}/items/{id}")]
        [Route("{auctionId}/items/{id}/delete")]
        public async Task<IEnumerable<AuctionItemModel>> Delete(int auctionId, AuctionBatchItemRequest model)
        {
            ThrowIfNotFound(model?.StockNumbers, "Stock Numbers");

            var ret = await InternalInsertOrUpdateAuctionItemsAsync(auctionId, model.StockNumbers, false);

            return ret;
        }

        private async Task<IEnumerable<AuctionItemModel>> InternalInsertOrUpdateAuctionItemsAsync(int auctionId, int[] impoundIds, bool assign = true)
        {
            var auction = Auction.GetById(auctionId);
            await ThrowIfNoCompanyAccessAsync(auction?.CompanyId, "Auction");

            if (auction.EndDate.HasValue && auction.EndDate < DateTime.Now)
            {
                throw new Extric.Towbook.Web.HttpResponseException(
                    new HttpResponseMessage(HttpStatusCode.BadRequest)
                    {
                        Content = new StringContent($"You cannot assign vehicles to an auction that has ended.")
                    });
            }

            List<Impound> impounds = new List<Impound>();

            if (impoundIds.Length > 0)
            {
                foreach (var iId in impoundIds)
                {
                    var imp = await Impound.GetByIdAsync(iId);
                    await ThrowIfNoCompanyAccessAsync(imp?.Company?.Id, "Impound");

                    // Don't allow assignment to an auctionId if the impound is a police hold.
                    if (assign && auctionId > 0 && imp.Hold)
                        continue;

                    impounds.Add(imp);
                }
            }

            if (impounds.Count == 0)
            {
                throw new Extric.Towbook.Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest)
                {
                    Content = new StringContent(
                        $"There were no impound vehicles to move to auction.")
                });
            }

            // Fill entry properties all at once (performance gain)
            impounds = await Impound.InitAsync(impounds);


            var currentAuctionItems = EntryAuctionDetail.GetByAuctionIds(new int[] { auctionId }).ToList();

            List<int> assignCallIds = assign
                ? impounds.Where(w => w.DispatchEntry != null && !currentAuctionItems.Select(x => x.DispatchEntryId).Contains(w.DispatchEntry.Id)).Select(s => s.DispatchEntry.Id).ToList()
                : new List<int>();

            List<int> removeCallIds = assign
                ? new List<int>()
                : impounds.Where(w => w.DispatchEntry != null && currentAuctionItems.Select(x => x.DispatchEntryId).Contains(w.DispatchEntry.Id)).Select(s => s.DispatchEntry.Id).ToList();


            // assign calls to auction
            foreach (var id in assignCallIds)
            {
                var i = impounds.FirstOrDefault(f => f.DispatchEntry?.Id == id);
                if (i != null)
                {
                    var ad = currentAuctionItems.FirstOrDefault(f => f.DispatchEntryId == id)
                        ?? EntryAuctionDetail.GetByDispatchEntryId(id);

                    if (ad == null)
                    {
                        ad = new EntryAuctionDetail()
                        {
                            DispatchEntryId = id
                        };
                    }

                    ad.AuctionId = auctionId;

                    var token = await this.GetCurrentTokenAsync();
                    await ad.SaveAsync(token, this.GetRequestingIp());
                    // set impound as auction (most likely assigned already)
                    i.Auction = true;

                    // save and update in Azure with new Auction Details
                    await i.DispatchEntry.Save(true, token, this.GetRequestingIp(), true);

                    if (currentAuctionItems.FirstOrDefault(f => f.DispatchEntryId == ad.DispatchEntryId) == null)
                        currentAuctionItems.Add(ad);
                }
            }



            // remove calls from auction
            foreach (var id in removeCallIds)
            {
                var e = impounds.FirstOrDefault(f => f.DispatchEntry?.Id == id)?.DispatchEntry
                    ?? await Dispatch.Entry.GetByIdAsync(id);

                if (e != null)
                {
                    var ad = currentAuctionItems.FirstOrDefault(f => f.DispatchEntryId == e.Id)
                        ?? EntryAuctionDetail.GetByDispatchEntryId(id);

                    if (ad != null)
                    {
                        ad.AuctionId = (int?)null;

                        await ad.SaveAsync(this.GetCurrentToken(), this.GetRequestingIp());

                        // TODO: keep AuctionItems table in sync here.  ? What conditions to we remove the flattened item ?

                        // save and update in Azure with new Auction Details
                        await e.Save(true, this.GetCurrentToken(), this.GetRequestingIp(), true);

                        if (currentAuctionItems.FirstOrDefault(f => f.DispatchEntryId == ad.DispatchEntryId) != null)
                            currentAuctionItems.Remove(ad);
                    }
                }
            }

            // prepare return of all auction items
            var calls = await API.Controllers.CallsController.GetByIds((await WebGlobal.GetCompaniesAsync()).Select(s => s.Id).ToArray(), currentAuctionItems.Select(s => s.DispatchEntryId).ToArray());
            var photos = await AuctionItemPhotosController.InternalGetPhotosByAuctionDetailsAsync(currentAuctionItems.ToArray(), true);

            return currentAuctionItems.Select(d => AuctionItemModel.Map(
                            d,
                            calls.FirstOrDefault(w => w.Id == d.DispatchEntryId),
                            photos?.Where(w => w.ItemId == d.Id)));
        }

    }
}
