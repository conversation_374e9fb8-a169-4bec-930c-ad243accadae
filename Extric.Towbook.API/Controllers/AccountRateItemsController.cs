using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Linq;
using System.Net;
using System.Net.Http;
using Extric.Towbook.WebShared;
using Extric.Towbook.API.Models;
using Extric.Towbook.Accounts;
using static Extric.Towbook.API.ApiUtility;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using Async = System.Threading.Tasks;
using Microsoft.AspNetCore.WebUtilities;
using System.Web;

namespace Extric.Towbook.API.Controllers
{
    // where 1=rateItemId.
    // /api/accountrateitems/1
    // /api/accountrateitems/1/update
    // /api/accountrateitems/1/delete

    // /api/accounts/1/ -- return account detail with accountRate stuff. 

    [Route("accountRateItems")]
    public class AccountRateItemsController : ControllerBase
    {
        public static async Task<IEnumerable<object>> InternalGetAccountRateItemsByAccountId(int accountId, bool includeAll = true, User currentUser = null)
        {
            if (currentUser == null)
                currentUser = WebGlobal.CurrentUser;

            return (await RateItemHelper.GetAllRateItems(await Account.GetByIdAsync(accountId), includeAll))
                .Select(o => o.AccountIfNotNullOtherwiseBaseRate).Select(o => new
                {
                    Id = o.RateItemId,
                    Name = o.Name,
                    Cost = (o.Cost == 0 ? (o.BaseCost != 0 ? o.BaseCost : 0) : o.Cost),
                    Taxable = o.Taxable,
                    LockCost = o.LockCost,
                    CategoryId = o.CategoryId,
                    LockQuantity = o.LockQuantity,
                    DefaultQuantity = o.DefaultQuantity,
                    MaxQuantity = o.MaximumQuantity,
                    MinQuantity = o.MinimumQuantity,
                    FreeQuantity = o.FreeQuantity,
                    Predefined = (o.Predefined != null ?
                   new
                   {
                       Id = o.Predefined.Id,
                       Locked = o.Predefined.Locked,
                       Hidden = o.Predefined.Hidden
                   } : null),
                    ExtendedRates = InternalGetForAccountObject.BlockChargesExtended(o, InternalGetForAccountObject.ShouldBlockCharges(currentUser), currentUser)
                }).OrderBy(o => (o.Predefined != null ? 1 : 0)).ThenBy(o => o.Name).AsEnumerable();
        }

        // THIS METHOD WAS MOVED TO AccountRateItemModel class
        //public static IEnumerable<InternalGetForAccountObject> InternalGetForAccount(int accountId, User currentUser)
        //{
        //    return InternalGetForAccount(new Account[] { await Account.GetByIdAsync(accountId) }, currentUser);
        //}

        // THIS METHOD WAS MOVED TO AccountRateItemModel class
        /// <summary>
        /// Returns a list of rates with Cost, FreeQuantity, Discount, DiscountExempt, and ExtendedRates, for 
        /// </summary>
        /// <param name="accounts"></param>
        /// <param name="currentUser"></param>
        /// <returns></returns>
        //public static IEnumerable<InternalGetForAccountObject> InternalGetForAccount(IEnumerable<Account> accounts, User currentUser)
        //{
        //    bool blockCharges = InternalGetForAccountObject.ShouldBlockCharges(currentUser);

        //    return Extric.Towbook.Accounts.RateItem.GetByAccount(accounts, true, false)
        //        .Select(o => InternalGetForAccountObject.Map(o, currentUser))
        //        .Where(o => !blockCharges || o.Cost > 0);
        //}

        /// <summary>
        /// GET /api/accountRateItems/?accountId=[accountId]
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("")]
        public async Task<IEnumerable<object>> Get() //[FromUri]int accountId)
        {
            // 2/13/2019: This method isn't used, according to research done in our logs (loggly)

            NameValueCollection nvc = HttpUtility.ParseQueryString(Web.HttpContext.Current.Request.QueryString.Value);

            int? accountId = (!String.IsNullOrEmpty(nvc["accountId"])) ? (int?)Convert.ToInt32(nvc["accountId"]) : null;
            if (accountId == null)
                return null;

            return await InternalGetAccountRateItemsByAccountId(accountId.Value,
                currentUser: WebGlobal.CurrentUser);
        }

        [HttpGet]
        [Route("{id}")]
        public async Task<object> GetSolver(int id, [FromQuery] int? accountId) =>
            accountId == null || accountId == 0 ? await GetAsync(id) :await GetAsync(id, (int)accountId);



        /// <summary>
        /// GET /api/accountRateItems/[rateItemId]
        /// </summary>
        /// <param name="id"></param>
        /// <returns>array of accountRateItemModel's for any accounts that have rates assigned to this rateItem.</returns>
        [ApiExplorerSettings(IgnoreApi = true)]
        public async Task<AccountRateItemModel[]> GetAsync(int id)
        {
            var x = await Extric.Towbook.Accounts.RateItem.GetByRateItem(id);

            if (x.Count > 0 && !WebGlobal.CurrentUser.HasAccessToCompany((await Extric.Towbook.Accounts.Account.GetByIdAsync(x[0].AccountId)).CompanyId))
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.Forbidden) { Content = new StringContent("You don't have access to that company.") });
            }

            return AccountRateItemModel.TranslateDomainToModel(x);
        }

        /// <summary>
        /// GET /api/accountRateItems/[rateItemId]/?accountId=[accountId]
        /// </summary>
        /// <param name="id"></param>
        /// <param name="accountId"></param>
        /// <returns></returns>
        private async Task<AccountRateItemModel> GetAsync(int id, int accountId)
        {
            var ret = Towbook.Accounts.RateItem.GetByRateItem(accountId, id);

            if (!WebGlobal.CurrentUser.HasAccessToCompany((await Account.GetByIdAsync(ret.AccountId)).CompanyId))
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.Forbidden) { Content = new StringContent("You don't have access to that company.") });
            }

            AccountRateItemModel arim = new AccountRateItemModel();
            arim.AccountId = ret.AccountId;
            arim.Id = ret.Id;
            arim.Value = ret.Cost;


            List<Models.AccountRateItemExtendedModel> list = new List<Models.AccountRateItemExtendedModel>();
            foreach (var e in ret.ExtendedRateItems.Values)
            {
                list.Add(new Models.AccountRateItemExtendedModel() { AccountRateItemId = e.Id, BodyType = e.BodyTypeId, Value = e.Amount });
            }
            arim.ExtendedItems = list.ToArray();

            return arim;
        }

        [HttpPost]
        [Route("{id}")]
        public async Task<object> PostSolver(int id, [FromQuery] int? accountId, object model) =>
            accountId == null || accountId == 0 ? await Post(id, model != null ? ((JArray)model).ToObject<AccountRateItemUpdateModel[]>() : null) :
            await Post(id, (int)accountId, model != null ? ((JObject)model).ToObject<AccountRateItemUpdateModel>() : null);

        private async Task<object> Post(int id, [FromQuery] int accountId, AccountRateItemModel model)
        {
            await Put(id, accountId, model);
            return null;
        }

        [HttpPut]
        [Route("{id}")]
        public async Task Put(int id, [FromQuery] int accountId, [FromBody] AccountRateItemModel model)
        {
            Extric.Towbook.Accounts.RateItem ri = Extric.Towbook.Accounts.RateItem.GetByRateItem(accountId, id);

            if (ri == null)
            {
                ri = new Extric.Towbook.Accounts.RateItem();
                ri.BaseRateItem = await RateItem.GetByIdAsync(id);
                ri.AccountId = accountId;
            }

            ri = AccountRateItemModel.TranslateModelToDomain(model, ri);

            if (!await WebGlobal.CurrentUser.HasAccessToCompanyAsync((await Extric.Towbook.Accounts.Account.GetByIdAsync(ri.AccountId)).CompanyId))
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.Forbidden) { Content = new StringContent("You don't have access to that company.") });
            }

            //ri.Save();
        }

        private async Task<object> Post(int id, AccountRateItemUpdateModel[] model)
        {
            // get all of them.
            var accountRateItems = await Towbook.Accounts.RateItem.GetByRateItem(id);
            var updates = new List<Towbook.Accounts.RateItem>();
            var deletes = new List<Towbook.Accounts.RateItem>();
            var companyRate = await RateItem.GetByIdAsync(id);

            await ThrowIfNoCompanyAccessAsync(companyRate?.CompanyId);

            var accounts = await Account.GetByIdsAsync(model.Select(o => o.AccountId).ToArray());

            foreach (var rm in model.OrderBy(o => o.AccountId))
            {
                var ri = accountRateItems.FirstOrDefault(o => o.AccountId == rm.AccountId);

                if (ri == null)
                {
                    ri = new Towbook.Accounts.RateItem();
                    ri.BaseRateItem = companyRate;
                    ri.AccountId = rm.AccountId;
                }

                await ThrowIfNoCompanyAccessAsync(accounts.FirstOrDefault(o => o.Id == ri.AccountId)?.CompanyId);

                if (!rm.Delete)
                {
                    updates.Add(AccountRateItemModel.TranslateModelToDomain(rm, ri));
                }
                else
                {
                    deletes.Add(ri);
                }
            }
            var x = System.Diagnostics.Stopwatch.StartNew();

            updates = updates.Where(o => o.IsDirty || o.ExtendedRateItems.Values.Any(eo => (eo as Towbook.Accounts.ExtendedRateItem).IsDirty)).ToList();

            foreach (var s in updates)
            {
                await s.Save();
            }

            foreach (var dx in deletes)
            {
                // remove any account extended rates items before removing the account rate item
                if (dx.ExtendedRateItems != null)
                {
                    foreach (var eri in dx.ExtendedRateItems)
                        if (eri.Value != null)
                            eri.Value.Delete(WebGlobal.CurrentUser);
                }

                await dx.Delete();
            }

            // update config.

            // fire off batch update.

            await Caching.CacheWorkerUtility.UpdateAccountRateItems(
                WebGlobal.CurrentUser.Id,
                WebGlobal.CurrentUser.CompanyId,
                id,
                updates.Select(o => o.AccountId).Distinct().ToArray(),
                deletes.Select(o => o.AccountId).Distinct().ToArray());

            return
                new
                {
                    updated = updates.Select(o => new { o.Id, o.RateItemId, o.AccountId, o.Cost }),
                    deleted = deletes.Select(o => o.Id),
                    time = x.ElapsedMilliseconds
                };
        }


        [HttpDelete]
        [Route("{id}")]
        public async Async.Task Delete(int id, [FromQuery] int accountId)
        {
            await ThrowIfNoCompanyAccessAsync((await Account.GetByIdAsync(accountId)).CompanyId, "Account");

            Towbook.Accounts.RateItem ri = Extric.Towbook.Accounts.RateItem.GetByRateItem(accountId, id);
            if (ri == null || ri.AccountId != accountId)
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.Forbidden) { Content = new StringContent("You don't have permission to access this item.") });

            await ri.Delete();
        }
    }
    //public class AccountRateItemExtendedModel
    //{
    //    public int Id { get; }
    //    public decimal Price { get; }
    //    public decimal FreeQuantity { get; }

    //    public AccountRateItemExtendedModel(int id, decimal price, decimal freeQuantity)
    //    {
    //        Id = id;
    //        Price = price;
    //        FreeQuantity = freeQuantity;
    //    }

    //    public override bool Equals(object obj)
    //    {
    //        return obj is AccountRateItemExtendedModel other &&
    //               Id == other.Id &&
    //               Price == other.Price &&
    //               FreeQuantity == other.FreeQuantity;
    //    }

    //    public override int GetHashCode()
    //    {
    //        var hashCode = *********;
    //        hashCode = hashCode * -********** + Id.GetHashCode();
    //        hashCode = hashCode * -********** + Price.GetHashCode();
    //        hashCode = hashCode * -********** + FreeQuantity.GetHashCode();
    //        return hashCode;
    //    }
    //}
}
