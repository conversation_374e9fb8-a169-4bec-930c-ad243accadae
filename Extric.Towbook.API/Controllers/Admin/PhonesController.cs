using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using Microsoft.AspNetCore.Mvc;
using Extric.Towbook.WebShared;

namespace Extric.Towbook.API.Admin.Controllers
{
    [Route("admin/phones")]
    public class PhonesController : ControllerBase
    {
        public class PhoneModel
        {
            public string Destination { get; set; }
            public int? CompanyId { get; set; }
        }



        //routes.MapHttpRoute(
        //    name: "Admin_Api_Post",
        //    routeTemplate: "admin/{controller}/{action}")
        //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Admin.Controllers" } } };
        [ApiPermission(Towbook.User.TypeEnum.SystemAdministrator)]
        [HttpGet]
        [Route("{id}")]
        public object ByCompany(int id)
        {
            return Towbook.Management.InternalVoice.PhoneCall.GetByCompanyId(id).Select(o => new
            {
                Id = o.PhoneCallId,
                Direction = o.Direction,
                CreateDate = o.CreateDate,
                CallerName = Core.FormatName(o.CallerName),
                CallerNumber = Core.FormatPhone(o.CallerNumber),
                Duration = o.EndTime - o.StartTime,
                clientNumber = o.Direction == 2 ? Core.FormatPhone(o.DestinationNumber) : Core.FormatPhone(o.CallerNumber),
                EmployeeName = Extric.Towbook.User.GetById(o.AnsweredByUserId)?.FullName ?? 
                    (o.Direction == 2 ? o.CallerName : Core.FormatPhone(o.AnsweredByNumber))
            });
        }

        //routes.MapHttpRoute(
        //    name: "Admin_Api_Post",
        //    routeTemplate: "admin/{controller}/{action}")
        //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Admin.Controllers" } } };
        [ApiPermission(Towbook.User.TypeEnum.SystemAdministrator)]
        [HttpPost]
        [Route("")]
        public object MakeCall(PhoneModel o)
        {

            var plivo = new Plivo.PlivoApi("MAYJEYZDYZOTNHZGFKZD", "****************************************");

            var user = WebGlobal.CurrentUser;

            var outbound = user.MobilePhone;
            var destination = Core.FormatPhone(o.Destination, true);

            if (destination.Length == 10)
                destination = "1" + destination;

            if (destination.Length != 11)
            {
                throw new Extric.Towbook.Web.HttpResponseException(new HttpResponseMessage()
                {
                    StatusCode = HttpStatusCode.BadRequest,
                    Content = new StringContent("Invalid Phone number: " + destination)
                });
            }
            
            // Make an outbound call
            var resp = plivo.Call.Create(from: "18103205063",
                to: new List<string> { outbound },
                answerUrl: "http://voice.towbook.com/outboundCall/" + destination + "?callerName=" +
                    WebUtility.UrlEncode(user.FullName) + 
                    "&callerNumber=" + outbound + 
                    "&userId=" + WebGlobal.CurrentUser.Id + 
                    "&companyId=" + o.CompanyId.GetValueOrDefault(),
                answerMethod: "POST",
                hangupUrl: "http://voice.towbook.com/callback/",
                hangupMethod: "POST");
            
            return resp;
        }
    }
}