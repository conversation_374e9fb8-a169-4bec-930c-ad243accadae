using Azure.Messaging.ServiceBus.Administration;
using Extric.Towbook.Integration.MotorClubs.Services;
using Extric.Towbook.WebShared;
using Microsoft.AspNetCore.Mvc;
using NLog;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Extric.Towbook.API.Controllers.Admin.Statistics
{
    [Route("admin/statistics/queues")]
    public class QueuesController : ControllerBase
    {
        private static readonly Logger logger = LogManager.GetCurrentClassLogger();
        public class StatJar
        {
            public string Service { get; set; }
            public string Name { get; set; }
            public string Description { get; set; }
            public int WarningThreshold { get; set; }
            public int CriticalThreshold { get; set; }
            public Dictionary<string, long> Namespaces { get; set; }
        }

        private async Task<long> SafeGetCount(ServiceBusAdministrationClient nm, string name)
        {
            try
            {
                var props = await nm.GetQueueRuntimePropertiesAsync(name);
                return props.Value.ActiveMessageCount;
            }
            catch
            {
                return -1;
            }
        }

        [HttpGet]
        [ApiPermission(Towbook.User.TypeEnum.SystemAdministrator)]
        public async Task<object> Get()
        {
            var namespaceManagerNC = new ServiceBusAdministrationClient(Core.GetConnectionString("Microsoft.ServiceBus.NC"));
            var namespaceManagerPrem = new ServiceBusAdministrationClient(Core.GetConnectionString("Microsoft.ServiceBus.WC"));

            var list = new List<StatJar>();

            foreach (var queue in new string[] {
                DigitalDispatchService.IncomingQueueName,
                DigitalDispatchService.OutgoingQueueName,
                "mc-dispatch-inbound",
                "mc-photos-outbound",
                "quickbooks-invoices",
                "mcb-dispatchentryqueue",
                "email-statements"})
            {
                list.Add(new StatJar()
                {
                    Service = "ServiceBus",
                    Name = queue,
                    WarningThreshold = (queue == "quickbooks-invoices" || queue == "mcb-dispatchentryqueue") ? 500 : 100,
                    CriticalThreshold = (queue == "quickbooks-invoices" || queue == "mcb-dispatchentryqueue") ? 1000 : 250,
                    Namespaces = new Dictionary<string, long>
                    {
                        ["north central standard"] = await SafeGetCount(namespaceManagerNC, queue),
                        ["west central premium"] = await SafeGetCount(namespaceManagerPrem, queue),
                    }
                });
            }

            list.Add(new StatJar()
            {
                Service = "Redis",
                WarningThreshold = 500,
                CriticalThreshold = 7500,
                Namespaces =
                    new Dictionary<string, long>
                    {
                        ["search"] = Queues.RedisJobQueue.InfoDump("search_calls_update").TotalMessages,
                        ["gps"] = Queues.RedisJobQueue.InfoDump("gps_inbound").TotalMessages
                    }
            });

            return list;
        }


        [HttpGet("resetGps")]
        [ApiPermission(Towbook.User.TypeEnum.SystemAdministrator)]
        public object ResetGps()
        {
            Queues.RedisJobQueue.DeleteQueue("gps_inbound");

            var lei = new LogEventInfo();
            lei.Level = LogLevel.Warn;
            var currentUser = WebGlobal.CurrentUser;
            if (currentUser != null)
            {
                lei.Properties["companyId"] = currentUser.CompanyId;
                lei.Properties["userId"] = currentUser.Id;
            }

            lei.Message = "Cleared gps_inbound";

            logger.Log(lei);

            return new
            {
                success = true,
                message = "GPS queue reset successfully."
            };
        }
    }
}
