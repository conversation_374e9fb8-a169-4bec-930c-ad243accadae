using Microsoft.AspNetCore.Mvc;
using System.Linq;
using System.Threading.Tasks;

namespace Extric.Towbook.API.Controllers.Admin.Statistics
{
    [Route("admin/statistics/global")]
    public class GlobalController : ControllerBase
    {
        public class GlobalStatsModel
        {
            public int DispatchEntries { get; set; }
            public int CallRequests { get; set; }
            public int Companies { get; set; }
            public int Users { get; set; }
        }

        [ApiPermission(Towbook.User.TypeEnum.SystemAdministrator)]
        [HttpGet]
        public async Task<GlobalStatsModel> GetAsync()
        {
            using var conn = Core.GetConnection();

            var stats = (await Dapper.SqlMapper.QueryAsync<GlobalStatsModel>(conn,
                "SELECT cast(IDENT_CURRENT(@Table1) as int) as DispatchEntries, " +
                "cast(IDENT_CURRENT(@Table2) as int) as CallRequests, " +
                "cast(IDENT_CURRENT(@Table3) as int) as Companies, " +
                "cast(IDENT_CURRENT(@Table4) as int) as Users", new
                {
                    Table1 = "DispatchEntries",
                    Table2 = "DispatchEntryRequests",
                    Table3 = "Companies",
                    Table4 = "Users",
                })).FirstOrDefault();

            return stats;
        }
    }
}
