using System.Collections.ObjectModel;
using System.Linq;
using Extric.Towbook.Accounts;
using Extric.Towbook.WebShared;
using System;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace Extric.Towbook.API.Controllers
{
    [Route("rotationStatus")]
    public class RotationStatusController : ControllerBase 
    {
        [HttpGet]
        public async Task<object> GetAsync()
        {
            Collection<RotationSubcontractor.RotationSummaryModel> list = null;

            if (await WebGlobal.CurrentUser.Company.HasFeatureAsync(Generated.Features.DispatchToSubcontractors_SubcontractorRotation))
                list = RotationSubcontractor.GetBySenderCompanyId(WebGlobal.CurrentUser.CompanyId);
            else
                list = RotationSubcontractor.GetByMemberCompanyId(WebGlobal.CurrentUser.CompanyId);

            if (list == null)
                return Array.Empty<object>();

            var rs = list.GroupBy(o => new { o.RotationName, o.CompanyName });

            return rs.Select(o => new
            {
                Name = o.Key.RotationName,
                Company = o.Key.CompanyName,
                Subcontractors = o.Select(s => new
                {
                    s.SubCompanyId,
                    s.SubCompanyName,
                    s.LastDispatched,
                    s.RotationSubContractorId
                }).OrderBy(r => r.LastDispatched)
            });
        }

        public sealed class RotationPositionModel
        {
            public int RotationSubcontractorId { get; set; }
            
            /// MOVE_FIRST, MOVE_LAST
            public string Action { get; set; }
        }

        [HttpPut]
        public async Task<object> PutAsync(RotationPositionModel model)
        {
            if (!await WebGlobal.CurrentUser.Company.HasFeatureAsync(Generated.Features.DispatchToSubcontractors_SubcontractorRotation))
                throw new Exception("no access");

            var list = RotationSubcontractor.GetBySenderCompanyId(WebGlobal.CurrentUser.CompanyId);

            var sub = list.FirstOrDefault(o => o.RotationSubContractorId == model.RotationSubcontractorId);
            if (sub == null)
                throw new Exception("invalid id");

            var rs = RotationSubcontractor.GetById(sub.RotationSubContractorId);

            if (model.Action == "MOVE_FIRST")
                rs.LastDispatched = list.OrderBy(o => o.LastDispatched).First().LastDispatched.AddDays(-1);
            if (model.Action == "MOVE_LAST")
                rs.LastDispatched = list.OrderBy(o => o.LastDispatched).Last().LastDispatched.AddMinutes(1);

            rs.Save();

            return rs;
        }
    }

}
