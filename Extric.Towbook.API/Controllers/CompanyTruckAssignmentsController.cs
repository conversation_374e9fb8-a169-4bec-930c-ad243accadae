using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web.Http;
using Extric.Towbook.Integration;
using Extric.Towbook.WebShared;
using Microsoft.AspNetCore.Mvc;

namespace Extric.Towbook.API.Controllers
{
    public class CompanyTruckAssignmentsModel
    {
        public bool promptDriversTruckAssignment { get; set; }
        public bool promptDriversTruckInspection { get; set; }
    }

    [Route("companyTruckAssignments")]
    public class CompanyTruckAssignmentsController : ControllerBase
    {
        [ApiPermission(true, Extric.Towbook.User.TypeEnum.Manager)]
        [HttpGet]
        [Route("")]
        public CompanyTruckAssignmentsModel Get()
        {
            return new CompanyTruckAssignmentsModel {
                promptDriversTruckAssignment = CompanyKeyValue.GetFirstValueOrNull(WebGlobal.CurrentUser.CompanyId, Provider.Towbook.ProviderId, "PromptDriversTruckAssignment") == "1", 
                promptDriversTruckInspection = CompanyKeyValue.GetFirstValueOrNull(WebGlobal.CurrentUser.CompanyId, Provider.Towbook.ProviderId, "PromptDriversTruckInspection") == "1"
            };
        }

        [ApiPermission(true, Extric.Towbook.User.TypeEnum.Manager)]
        [HttpPost]
        [Route("")]
        public async Task<IActionResult> PostAsync([FromBody] CompanyTruckAssignmentsModel companyTruckAssignmentsModel)
        {
            var ckvPromptDriversTruckAssignment = CompanyKeyValue.GetFirstValueOrNew(WebGlobal.CurrentUser.CompanyId, Provider.Towbook.ProviderId, "PromptDriversTruckAssignment");
            var ckvPromptDriversTruckInspection = CompanyKeyValue.GetFirstValueOrNew(WebGlobal.CurrentUser.CompanyId, Provider.Towbook.ProviderId, "PromptDriversTruckInspection");

            if (await WebGlobal.CurrentUser.Company.HasFeatureAsync(Extric.Towbook.Generated.Features.DriverCheckIn))
            {
                ckvPromptDriversTruckAssignment.Value = companyTruckAssignmentsModel.promptDriversTruckAssignment ? "1" : "0";
                ckvPromptDriversTruckAssignment.Save();

                if (companyTruckAssignmentsModel.promptDriversTruckAssignment && await WebGlobal.CurrentUser.Company.HasFeatureAsync(Extric.Towbook.Generated.Features.PreTripInspections)) {
                    ckvPromptDriversTruckInspection.Value = companyTruckAssignmentsModel.promptDriversTruckInspection ? "1" : "0";
                    ckvPromptDriversTruckInspection.Save();
                }
            }
            
            return new ObjectResult(
                new CompanyTruckAssignmentsModel { 
                    promptDriversTruckAssignment = ckvPromptDriversTruckAssignment.Value == "1" ? true : false, 
                    promptDriversTruckInspection = ckvPromptDriversTruckInspection.Value == "1" ? true : false
                }
            )
            {
                StatusCode = (int)HttpStatusCode.OK
            };    
        }
    }
}
