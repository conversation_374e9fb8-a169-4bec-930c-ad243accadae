using Extric.Towbook.API.Models.EventNotifications;
using Extric.Towbook.EventNotifications;
using Extric.Towbook.WebShared;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;

namespace Extric.Towbook.API.Controllers
{
    [Route("eventnotificationqueues")]
    public class EventNotificationQueuesController : ControllerBase
    {
        /// <summary>
        /// Returns a list of available event notifications
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("")]
        public object Get()
        {
            return JsonConvert.SerializeObject(new { Message = "hello world" });
        }

        [HttpPost]
        [Route("")]
        public async Task<object> Post([FromBody] EventNotificationQueueModel model)
        {
            if (model == null)
                throw new TowbookException("You don't have access or permission to perform this action.");

            if(model.CompanyId != null &&
                !WebGlobal.CurrentUser.HasAccessToCompany(model.CompanyId.Value))
                throw new TowbookException("You don't have access or permission to perform this action.");

            if (model.Group == EventNotificationGroup.EquipmentInspection)
            {
                if(model.CompanyId == null || model.PerformerId == null || model.ReferenceId == null)
                    throw new TowbookException("Equipment Inspection Trigger Error: CompanyId, PerformerId, or ReferenceId cannot be null.");

                var qi = new PreTripInspectionQueueItem();
                qi.PreTripInspectionId = model.ReferenceId.Value;
                qi.UserId = model.PerformerId.Value;
                qi.CompanyId = model.CompanyId.Value;
                qi.Type = model.Type == EventNotificationType.EquipmentInspectionFailure ? PreTripTriggerType.FailedItem : PreTripTriggerType.Unspecified;

                await qi.TriggerEvent(model.Users, model.UserTypes, model.DeliveryMethods, model.TestMode);
                return JsonConvert.SerializeObject(new { Message = "Successfully entered your notification into the queue" });
            }

            if (model.Group == EventNotificationGroup.Dispatching)
            {
                if (model.CompanyId == null || model.ReferenceId == null)
                    throw new TowbookException("Dispatching Trigger Error: CompanyId or ReferenceId cannot be null.");

                if(model.Type == EventNotificationType.CallAcceptedByDriver && model.DriverId == null)
                    throw new TowbookException("Dispatching Trigger Error: DriverId cannot be null.");

                var dqi = new DispatchingQueueItem();
                
                dqi.DispatchEntryId = model.ReferenceId.Value;
                dqi.Type = (DispatchingTriggerType)model.Type;

                if (model.DriverId != null)
                    dqi.DriverId = model.DriverId.Value;
                
                if (model.ArrivalEta != null)
                    dqi.ArrivalEta = model.ArrivalEta;

                if (model.StartAtTime != null)
                    dqi.StartAtTime = model.StartAtTime;

                await dqi.TriggerEvent(model.Users, model.UserTypes, model.DeliveryMethods, model.TestMode);

                return JsonConvert.SerializeObject(new { Message = "Successfully entered your notification into the queue" });
            }

            if (model.Group == EventNotificationGroup.DigitalDispatch)
            {
                if (model.CompanyId == null || model.ReferenceId == null)
                    throw new TowbookException("DigitalDispatch Trigger Error: CompanyId or ReferenceId cannot be null.");

                var dqi = new DigitalDispatchQueueItem();
                dqi.CallRequestId = model.ReferenceId.Value;
                dqi.Type = (DigitalDispatchTriggerType)model.Type;
                await dqi.TriggerEvent(model.Users, model.UserTypes, model.DeliveryMethods, model.TestMode);

                return JsonConvert.SerializeObject(new { Message = "Successfully entered your notification into the queue" });
            }

            if (model.Group == EventNotificationGroup.RoadsideSurvey)
            {
                if (model.ReferenceId == null)
                    throw new TowbookException("Roadside/Survey Trigger Error: ReferenceId cannot be null. You must provide the DispatchId.");

                if (model.PerformerId == null)
                    throw new TowbookException("Roadside/Survey Trigger Error: PerformerId cannot be null. You must provide the DispatchUserId.");

                var rqi = new RoadsideSurveyQueueItem()
                {
                    DispatchId = model.ReferenceId.Value,
                    Type = (RoadsideSurveyTriggerType)model.Type,
                    DispatchUserId = model.PerformerId.Value
                };

                await rqi.TriggerEvent(model.Users, model.UserTypes, model.DeliveryMethods, model.TestMode);

                return JsonConvert.SerializeObject(new { Message = "Successfully entered your notification into the queue" });
            }

            if (model.Group == EventNotificationGroup.RoadsideDispatch)
            {
                if (model.ReferenceId == null)
                    throw new TowbookException("Roadside/Dispatch Trigger Error: ReferenceId cannot be null. You must provide the RoadsideDispatchId or the DispatchEntryContactId.");

                int? referenceId = model.ReferenceId;
                int? dispatchEntryId = null;
                int? dispatchEntryContactId = null;

                if ((RoadsideDispatchTriggerType)model.Type == RoadsideDispatchTriggerType.CreateRoadsideUser)
                    dispatchEntryContactId = model.ReferenceId;
                else if ((RoadsideDispatchTriggerType)model.Type == RoadsideDispatchTriggerType.PredictedArrivalAlert)
                    dispatchEntryId = model.ReferenceId;


                var rqi = new RoadsideDispatchQueueItem()
                {
                    DispatchEntryContactId = dispatchEntryContactId,
                    DispatchEntryId = dispatchEntryId,
                    Type = (RoadsideDispatchTriggerType)model.Type
                };

                await rqi.TriggerEvent();

                return JsonConvert.SerializeObject(new { Message = "Successfully entered your notification into the queue" });
            }

            return JsonConvert.SerializeObject(new { Message = "No event notification was triggered.  Check your POST data and try again." });
        }
    }
}
