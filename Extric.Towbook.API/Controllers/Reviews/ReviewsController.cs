using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using Extric.Roadside.API.Models;
using Extric.Roadside.Surveys;
using Extric.Towbook.API.Models;
using Extric.Towbook.Utility;
using Extric.Towbook.WebShared;
using Microsoft.AspNetCore.Mvc;

namespace Extric.Towbook.API.Controllers
{
    [Route("reviews")]
    public class ReviewsController : ControllerBase
    {
        [HttpGet]
        [Route("")]
        public async Task<IEnumerable<SurveyReviewModel>> GetAsync([FromQuery]int? page = 1, [FromQuery]int? pageSize = 50)
        {
            // If the company doesn't have the roadside feature, send examples
            if (!await WebGlobal.CurrentUser.Company.HasFeatureAsync(Generated.Features.Roadside))
                return GetExamples();

            var companies = await WebGlobal.GetCompaniesAsync();

            if (Web.HttpContext.Current.Request?.Headers != null &&
                Web.HttpContext.Current.Request.Headers["X-Company"].Count > 0)
            {
                companies = await this.GetCompaniesForRequestAsync();
            }

            var ret = Survey
                .Search(companies.Select(s => s.Id).ToArray(), null, null, null, null, null, null, page, pageSize)
                .Select(s => SurveyReviewModel.Map(s))
                .ToCollection();

            var items = new Collection<SurveyResponseWebComponentItem>();
            if (await WebGlobal.CurrentUser.Company.HasFeatureAsync(Generated.Features.Roadside))
                items = SurveyResponseWebComponentItem.GetBySurveyResponseIds(ret.Select(s => s.ResponseId).ToArray()).ToCollection();

            var connect = SurveyResponseWebComponentConnection.GetByCompanyIds(ret.Select(s => s.CompanyId).ToArray());

            foreach (var r in ret)
            {
                r.WebsiteConnectItem = items.Where(w => w.SurveyResponseId == r.ResponseId).FirstOrDefault();
                r.AvailableActions = await GetAvailableActionsAsync(r, connect);
            }

            return ret;
        }

        ///<summary>
        /// Delete does not mean delete the survey response.  Rather, it is called 
        /// to exclude a survey rating from the calculation towards the company rating.
        /// This action is limited to system administrators as there is an business ethical dilema.
        ///</summary>
        ///<param name="id">The response to perform the action against</param>
        [HttpDelete]
        [Route("{id:int}")]
        public HttpResponseMessage Delete(int id)
        {
            if (WebGlobal.ImpersonationUser == null ||
                WebGlobal.ImpersonationUser.Type != Extric.Towbook.User.TypeEnum.SystemAdministrator)
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.Forbidden)
                {
                    Content = new StringContent("System Administrators can only delete survey results. You do not have permission to perform this action.")
                });
            }

            var response = SurveyResponse.GetById(id);
            if (response == null)
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest)
                {
                    Content = new StringContent("The survey request is invalid.")
                });
            }

            response.Delete(WebGlobal.ImpersonationUser);

            return new HttpResponseMessage(HttpStatusCode.OK);
        }


        internal static async Task<Collection<string>> GetAvailableActionsAsync(SurveyReviewModel m, IEnumerable<SurveyResponseWebComponentConnection> connect = null) {
            var result = new Collection<string>();

            result.Add("view");

            if (WebGlobal.CurrentUser.Type == Towbook.User.TypeEnum.Manager && 
                await WebGlobal.CurrentUser.Company.HasFeatureAsync(Generated.Features.Roadside_SocialMediaLinks))
            {
                if (connect != null && connect.Where(w => w.CompanyId == m.CompanyId && w.EnableWebComponent).Any())
                {
                    if (m.WebsiteConnectItem != null)
                        result.Add("websiteRemove");
                    else
                        result.Add("websiteAdd");
                }

                if (m.FacebookConnectItem == null)
                    result.Add("facebookAdd");
            }

            result.Add("notes");

            if (WebGlobal.CurrentUser.Type == Towbook.User.TypeEnum.Manager)
            {
                result.Add("resend");
            }

            if (WebGlobal.ImpersonationUser != null && WebGlobal.ImpersonationUser.Type == Extric.Towbook.User.TypeEnum.SystemAdministrator)
            {
                result.Add("delete");
            }

            return result;
        }

        internal static IEnumerable<SurveyReviewModel> GetExamples()
        {

            var ret = new Collection<SurveyReviewModel>();
            int count = 1;

            var demo = Integration.Roadside.SurveyResultsWebComponentsController.GetDemo();

            foreach (var r in demo.Reviews)
            {
                ret.Add(new SurveyReviewModel()
                {
                    ResponseId = count,
                    CallNumber = 1000 + count,
                    AccountName = "Cash Customer",
                    DriverName = "Jason",
                    TruckName = "Flatbed",
                    SubmittedDate = r.SurveyDate,
                    Name = r.Name,
                    MobileNumber = $"555-555-{count}{count}{count}{count}",
                    PurchaseOrderNumber = $"ABC{count}2{count}8{count}3{count}3",
                    Email = string.Empty,
                    AverageRating = r.Rating,
                    Answers = new Collection<SurveyQuestionAnswerModel>().ToJson(),
                    Review = "EXAMPLE - " + r.Comment,
                    WebsiteConnectItem = null,
                    AvailableActions = new string[] { "view" }.ToArray(),
                });

                count++;
            }

            return ret.OrderByDescending(o => o.SubmittedDate);
        }
    }
}
