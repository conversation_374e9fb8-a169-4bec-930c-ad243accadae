using System;
using Extric.Towbook.Generated;
using Extric.Towbook.Integration;
using Extric.Towbook.Management;
using Extric.Towbook.WebShared;
using Microsoft.AspNetCore.Mvc;
using System.Collections.ObjectModel;
using System.Linq;
using System.Web.Http;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Extric.Towbook.API.Controllers
{
    [Route("Features")]
    public class FeaturesController : ControllerBase
    {
        /// <summary>
        /// Returns a list of available features
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("")]
        public async Task<Collection<ConfigFeatureModel>> GetAsync([FromQuery] int companyId = 0)
        {
            companyId = companyId != 0 ? companyId : WebGlobal.CurrentUser.CompanyId;

            var vc = new Collection<ConfigFeatureModel>();
            var cc = CompanyContract.GetByCompanyId(companyId);
            var features = Feature.GetAll();

            var ccFeatures = cc?.Features ?? Array.Empty<CompanyContractFeature>();

            // STICKERING_BETA
            if (WebGlobal.CurrentUser.Id == 3913)
            {
                ccFeatures = ccFeatures.Union(new CompanyContractFeature[] {
                    new CompanyContractFeature() { FeatureId = (int)Features.Stickering}
                }).ToArray();
            }

            if (CompanyKeyValue.GetFirstValueOrNull(companyId,
                Provider.Towbook.ProviderId,
                "Towbook_Calls_AutomaticallyAddMiles")
                == "1")
            {
                ccFeatures = ccFeatures.Union(new CompanyContractFeature[] {
                    new CompanyContractFeature() { FeatureId = (int)Features.Options_AutomaticallyAddMiles }
                }).ToArray();
            }

            if (this.GetCurrentToken().ClientVersionId != null)
            {
                if (Platform.ClientVersion.GetById(this.GetCurrentToken().ClientVersionId.Value)?.Type == Platform.ClientVersionType.Agent)
                {
                    if (CompanyKeyValue.GetFirstValueOrNull(WebGlobal.CurrentUser.CompanyId,
                        Provider.QuickBooks.ProviderId, "DataSource") == "QBO")
                        ccFeatures = ccFeatures.Where(o => o.FeatureId != (int)Features.QuickBooks).ToArray();
                }
            }

            var serviceCallsOnly = CompanyKeyValue.GetFirstValueOrNull(WebGlobal.CurrentUser.CompanyId, Provider.Towbook.ProviderId, "ServiceCallsOnly");
            if (serviceCallsOnly == "1")
            {
                ccFeatures = ccFeatures.Where(o => o.FeatureId != (int)Features.Trucks &&
                    o.FeatureId != (int)Features.TruckExpenses &&
                    o.FeatureId != (int)Features.Impounds).ToArray();
            }
            else if (serviceCallsOnly == "2")
            {
                ccFeatures = ccFeatures.Where(o => o.FeatureId != (int)Features.Impounds).ToArray();
            }
            else
            {
                ccFeatures = ccFeatures.Union(new CompanyContractFeature[] {
                    new CompanyContractFeature(Features.TruckExpenses),
                    new CompanyContractFeature(Features.Impounds),
                    new CompanyContractFeature(Features.Trucks) }).ToArray();
            }

            if (serviceCallsOnly == "1" || serviceCallsOnly == "2")
            {
                ccFeatures = ccFeatures.Union(new CompanyContractFeature[] {
                    new CompanyContractFeature() { FeatureId = (int)Features.RoadsideServiceOnly}
                }).ToArray();
            }

            if (WebGlobal.CurrentUser.AccountId > 0)
            {
                if (AccountKeyValue.GetFirstValueOrNull(WebGlobal.CurrentUser.CompanyId,
                        WebGlobal.CurrentUser.AccountId, Provider.Towbook.ProviderId, "StickeringEnabled") == "0" ||
                        !await WebGlobal.CurrentUser.Company.HasFeatureAsync(Features.Stickering))
                    ccFeatures = ccFeatures.Where(o => o.FeatureId != (int)Features.Stickering).ToArray();

                if (AccountKeyValue.GetFirstValueOrNull(WebGlobal.CurrentUser.CompanyId,
                    WebGlobal.CurrentUser.AccountId, Provider.Towbook.ProviderId, "ParkingPermitsEnabled") == "0" ||
                    !await WebGlobal.CurrentUser.Company.HasFeatureAsync(Features.ParkingPermits))
                    ccFeatures = ccFeatures.Where(o => o.FeatureId != (int)Features.ParkingPermits).ToArray();

                ccFeatures = ccFeatures.Where(o =>
                    o.FeatureId != (int)Features.Chat &&
                    o.FeatureId != (int)Features.DriverCheckIn &&
                    o.FeatureId != (int)Features.PreTripInspections &&
                    o.FeatureId != (int)Features.GPS).ToArray();
            }


            // FOR COVID19. We are enabling this for all companies effective immediately
            // during covid19 pandemic to help companies. 
            ccFeatures = ccFeatures.Union(new CompanyContractFeature[] {
                    new CompanyContractFeature() { FeatureId = (int)Features.Videos }
                }).ToArray();

            foreach (var x in features)
            {
                var featureId = x.Id;


                var cfm = new ConfigFeatureModel()
                {
                    Id = featureId,
                    Name = x.Name,
                    Enabled = (cc == null ? false : ccFeatures.Where(o => o.FeatureId == featureId && o.IsDeleted == false).Any()),
                    DependsOnfeatureId = x.DependsOnFeatureId,
                    Description = x.Description,
                    Internal = x.Internal
                };

                cfm = FinishConfigFeatures(cfm);

                vc.Add(cfm);
            }

            if (WebGlobal.CurrentUser.Type == Towbook.User.TypeEnum.Manager ||
                WebGlobal.CurrentUser.Type == Towbook.User.TypeEnum.Dispatcher)
            {
                ccFeatures = ccFeatures.Union(new CompanyContractFeature[] {
                    new CompanyContractFeature() { FeatureId = (int)Features.Dispatching_BillingNotes}
                }).ToArray();
            }

            return vc;
        }

        [NonAction]
        public IEnumerable<ConfigFeatureModel> FinishConfigFeatures(IEnumerable<ConfigFeatureModel> features)
        {
            if (features == null)
                return features;

            List<ConfigFeatureModel> missing = new List<ConfigFeatureModel>();

            if (!features.Any(a => a.Id == (int)Features.BetaPrivatePropertyMaps))
                missing.Add(ConfigFeatureModel.Map(Features.BetaPrivatePropertyMaps));

            if (!features.Any(a => a.Id == (int)Features.BetaProfilePhotoUpload))
                missing.Add(ConfigFeatureModel.Map(Features.BetaProfilePhotoUpload));

            if (!features.Any(a => a.Id == (int)Features.BetaPromptDriversforTruckAssignments))
                missing.Add(ConfigFeatureModel.Map(Features.BetaPromptDriversforTruckAssignments));

            if (!features.Any(a => a.Id == (int)Features.BetaPropertyReleaseForms))
                missing.Add(ConfigFeatureModel.Map(Features.BetaPropertyReleaseForms));
            
            if (!features.Any(a => a.Id == (int)Features.BetaReportHistory)) {
                missing.Add(ConfigFeatureModel.Map(Features.BetaReportHistory));
            }

            var mapFeature = features.FirstOrDefault(f => f.Id == (int)Features.Mapping);
            if (mapFeature?.Enabled == false)
            {
                var betaPrivatePropertyMaps = features.FirstOrDefault(f => f.Id == (int)Features.BetaPrivatePropertyMaps);
                if (betaPrivatePropertyMaps != null)
                    betaPrivatePropertyMaps.Enabled = false;
            }

            if (!features.Any(a => a.Id == (int)Features.PaymentIntegrations_Square_SquareTipping))
                missing.Add(ConfigFeatureModel.Map(Features.PaymentIntegrations_Square_SquareTipping));

            features = features.Union(missing);

            return features.Select(FinishConfigFeatures);

        }

        private ConfigFeatureModel FinishConfigFeatures(ConfigFeatureModel config)
        {
            var featureId = config.Id;

            #region Beta Features
            if (featureId == (int)Features.PaymentIntegrations_Square_SquareTipping)
                config.Enabled = WebGlobal.CurrentUser.Company.HasAccessToBaseFeature("tipping");
            if (featureId == (int)Features.BetaPrivatePropertyMaps)
                config.Enabled = WebGlobal.CurrentUser.Company.HasAccessToBaseFeature("customTags");
            if (featureId == (int)Features.BetaPromptDriversforTruckAssignments)
                config.Enabled = WebGlobal.CurrentUser.Company.HasAccessToBaseFeature("inspectionDefault");
            if (featureId == (int)Features.BetaPropertyReleaseForms)
                config.Enabled = WebGlobal.CurrentUser.Company.HasAccessToBaseFeature("mobilePropertyRelease");
            if (featureId == (int)Features.BetaProfilePhotoUpload)
                config.Enabled = WebGlobal.CurrentUser.Company.HasAccessToBaseFeature("userDriverNewFieldsAndPhotos");
            if (featureId == (int)Features.BetaReportHistory)
                config.Enabled = WebGlobal.CurrentUser.Company.HasAccessToBaseFeature("Report-History");
            #endregion

            if (new[] { "FL", "TX" }.Contains(WebGlobal.CurrentUser.Company.State))
            {
                var ckv = CompanyKeyValue.GetFirstValueOrNull(WebGlobal.CurrentUser.CompanyId, Provider.Towbook.ProviderId, "PeddleIntegration_OptOutOfFeatureInclusion");

                if (featureId == (int)Features.PeddleIntegration)
                {
                    config.Enabled = ckv != "1";
                }
            }

            return config;
        }
    }

    public class ConfigFeatureModel
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public bool Enabled { get; set; }
        public int DependsOnfeatureId { get; set; }
        public string Description { get; set; }
        public bool Internal { get; set; }

        static public ConfigFeatureModel Map(Features f)
        {
            return new ConfigFeatureModel
            {
                Id = (int)f,
                Name = f.ToString(),
                Enabled = false
            };
        }
    }
}
