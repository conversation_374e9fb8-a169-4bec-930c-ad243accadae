using Extric.Towbook.API.Models;
using Extric.Towbook.Company;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;

namespace Extric.Towbook.API.Controllers
{
    [Route("truckmaintenance")]
    public class TruckMaintenanceController : ControllerBase
    {

        [ApiPermission(Extric.Towbook.User.TypeEnum.Manager)]
        [Route("")]
        [HttpPost]
        public object Post(TruckServiceGroupItemModel model)
        {
            if (model == null)
            {
                throw new Extric.Towbook.Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.Forbidden)
                {
                    Content = new StringContent("Your request body is empty. Did you forget to include the JSON or set the content-type to application/json?")
                });
            }

            if (model.TruckServiceGroupItemId > 0)
            {
                throw new Extric.Towbook.Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.Forbidden)
                {
                    Content = new StringContent("You shouldn't set the ID in a POST request. If you're trying to update an existing object, use PUT method instead")
                });
            }

            var truckServiceGroupItem = new TruckServiceGroupItem();
            TruckServiceGroupItemModel.MapModelToDomainObject(model, truckServiceGroupItem).Save();

            return truckServiceGroupItem;
        }


        [ApiPermission(Extric.Towbook.User.TypeEnum.Manager)]
        [Route("")]
        [HttpPut]
        public async Task<object> Put(int id, TruckServiceGroupItemModel model)
        {
            if (model == null)
            {
                throw new Extric.Towbook.Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.Forbidden)
                {
                    Content = new StringContent("Your request body is empty. Did you forget to include the JSON or set the content-type to application/json?")
                });
            }

            if (model.TruckServiceGroupItemId < 1)
            {
                throw new Extric.Towbook.Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.InternalServerError)
                {
                    Content = new StringContent("You must set the Id in a PUT request. If you're trying to create a new truck, use the POST method instead.")
                });
            }

            if (model.TruckServiceGroupItemId != id)
            {
                throw new Extric.Towbook.Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.InternalServerError)
                {
                    Content = new StringContent("The ID in the body of the request doesn't match the ID passed via the incoming URL.")
                });
            }

            TruckServiceGroupItem truckServiceGroupItem;

            if (model.TruckServiceGroupItemId > 0)
            {
                truckServiceGroupItem = await TruckServiceGroupItem.GetByIdAsync(model.TruckServiceGroupItemId);
            }
            else
            {
                truckServiceGroupItem = new TruckServiceGroupItem();
            }

            TruckServiceGroupItemModel.MapModelToDomainObject(model, truckServiceGroupItem).Save();

            return truckServiceGroupItem;
        }


    }
}
