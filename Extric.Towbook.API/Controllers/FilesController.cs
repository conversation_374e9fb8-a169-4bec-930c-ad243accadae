using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;
using Extric.Towbook.Accounts;
using Extric.Towbook.API.Models;
using Extric.Towbook.Integration;
using Extric.Towbook.Storage;
using Extric.Towbook.Utility;
using Extric.Towbook.Web;
using Extric.Towbook.WebShared;
using Extric.Towbook.WebShared.Multipart;
using Microsoft.AspNetCore.Mvc;
using MimeKit;
using static Extric.Towbook.API.ApiUtility;
using HttpContextHelper = Extric.Towbook.Web.HttpContext;

namespace Extric.Towbook.API.Controllers
{
    [Route("files")]
    public class FilesController : ControllerBase
    {
        /// <summary>
        /// Returns a list of files for the specified object. You can only specify one type to search by at a time. (For example, if you specifiy DriverId and TruckId, an exception will be thrown)
        /// </summary>
        /// <param name="driverId"></param>
        /// <param name="accountId"></param>
        /// <param name="truckId"></param>
        /// <param name="callId"></param>
        /// <param name="userId"></param>
        /// <returns>Array of files with Id, Filename, Url, Size, CreateDate, and Description</returns>
        [HttpGet]
        [Route("")]
        [Route("get")]
        public async Task<IEnumerable<CompanyFileModel>> Get(
            [FromQuery] int? driverId = null, 
            [FromQuery] int? accountId = null,
            [FromQuery] int? truckId = null, 
            [FromQuery] int? callId = null,
            [FromQuery] int? userId = null)
        {
            int values = 0;

            if (driverId.HasValue)
                values++;

            if (truckId.HasValue)
                values++;

            if (accountId.HasValue)
                values++;

            if (callId.HasValue)
                values++;

            if (userId.HasValue)
                values++;

            if (values == 0)
                throw new Exception("Must specify at least one type to search by. TruckId, DriverId, AccountId, UserId, or CallId.");

            if (values > 1)
                throw new Exception("You can only specify one field to search by at a time. You specified " + values);

            if (WebGlobal.CurrentUser.Type == Towbook.User.TypeEnum.Driver &&
                // TODO: Can be made async
                CompanyKeyValue.GetFirstValueOrNull(
                                    WebGlobal.CurrentUser.CompanyId,
                                    Provider.Towbook.ProviderId,
                                    "PreventDriversFromViewingFiles") == "1")
                return Array.Empty<CompanyFileModel>();

            if (WebGlobal.CurrentUser?.Type == Towbook.User.TypeEnum.PoliceDispatcher ||
                WebGlobal.CurrentUser?.Type == Towbook.User.TypeEnum.PoliceOfficer)
            {
                // don't allow police user types to see files (requirement for CTS... I don't think this makes sense; 
                // files are a great communication tool and letting officers view them
                // seems like a good idea... we're probably going to make this NOT be a default for these user types
                // because other departments are going to want to have files visible for all user types. 
                return Array.Empty<CompanyFileModel>();
            }

            if (callId != null)
            {
                var e = await Dispatch.Entry.GetByIdAsync(callId.Value);
                if (e != null && await WebGlobal.CurrentUser.HasAccessToCompanyAsync(e.CompanyId))
                {
                    var ret = Map(await CompanyFile.GetByDispatchEntryIdAsync(callId.Value, e.CompanyId)).ToCollection();
                    foreach (var r in ret)
                        r.Url = r.Url + "&callId=" + callId;

                    return ret;
                }
            }

            if (WebGlobal.CurrentUser.Type == Towbook.User.TypeEnum.AccountUser)
                return Array.Empty<CompanyFileModel>();

            if (driverId != null)
            {
                await ThrowIfNoCompanyAccessAsync((await Driver.GetByIdAsync(driverId.Value))?.CompanyId);
                return Map(await CompanyFile.GetByDriverIdAsync(driverId.Value));
            }

            if (truckId != null)
            {
                await ThrowIfNoCompanyAccessAsync((await Truck.GetByIdAsync(truckId.Value))?.CompanyId);
                return Map(await CompanyFile.GetByTruckIdAsync(truckId.Value));
            }


            if (accountId != null)
            {
                if (WebGlobal.CurrentUser.Type == Towbook.User.TypeEnum.Driver)
                {
                    var hideNotes = CompanyKeyValue.GetFirstValueOrNull(
                        WebGlobal.CurrentUser.CompanyId,
                        Provider.Towbook.ProviderId, "HideAccountNotesFromDrivers");

                    if (hideNotes == "1")
                        return Array.Empty<CompanyFileModel>();
                }
                // TODO: Can be made async
                var account = await Account.GetByIdAsync(accountId.Value);
                await ThrowIfNoCompanyAccessAsync(account?.Companies);

                return Map(await CompanyFile.GetByAccountIdAsync(accountId.Value));
            }

            if(userId != null)
            {
                var user = await Towbook.User.GetByIdAsync(userId.Value);
                await ThrowIfNoCompanyAccessAsync(user?.CompanyId);

                return Map(await CompanyFile.GetByUserIdAsync(userId.Value));
            }

            return null;
        }

        /// <summary>
        /// Deletes a file associated with a call.
        /// </summary>
        /// <param name="id">The ID of the file to delete</param>
        /// <returns></returns>
        [ApiPermission(Extric.Towbook.User.TypeEnum.Manager)]
        [HttpDelete]
        [Route("{id}")]
        [Route("{id}/delete")]
        public async Task<HttpResponseMessage> DeleteAsync(int id)
        {
            var p = await CompanyFile.GetByIdAsync(id);

            if (p == null)
                return new HttpResponseMessage(HttpStatusCode.NoContent);

            if (p.OwnerUserId != WebGlobal.CurrentUser.Id)
            {
                bool noCompanyAccess = true;

                if (!(await WebGlobal.CurrentUser.HasAccessToCompanyAsync(p.CompanyId)))
                {
                    // user doesn't have access to the company for this file.
                    noCompanyAccess = true;
                }

                var uploader = await Towbook.User.GetByIdAsync(p.OwnerUserId);
                if (uploader != null &&
                    await WebGlobal.CurrentUser.HasAccessToCompanyAsync(uploader.CompanyId))
                {
                    // user has access to the company that uploaded the file.
                    noCompanyAccess = false;
                }

                if (noCompanyAccess)
                {
                    throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.Forbidden)
                    {
                        Content = new StringContent("You don't have access to remove this file.")
                    });
                }
            }

            await p.DeleteAsync(WebGlobal.CurrentUser);

            return new HttpResponseMessage(HttpStatusCode.NoContent);
        }

        /// <summary>
        /// Upload a file for a call
        /// </summary>
        /// <param name="callId">The call/entry that the photo is associated with.</param>
        /// <param name="description"></param>
        /// <returns>If successful, returns HTTP Status 201. 
        /// 
        /// Returns a JSON object equilivant to calling PhotosController.Get(callId, id). 
        /// </returns>
        /// 
        [HttpPost]
        [Route("")]
        [DisableFormValueModelBinding]
        public async Task<ObjectResult> Post(
            [FromQuery] int callId,
            [FromQuery] string description = "")
        {
            //if (!Request.Content.IsMimeMultipartContent())
            if (!Web.HttpContext.Current.Request.HasFormContentType)
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.UnsupportedMediaType));
            }
            var d = await Dispatch.Entry.GetByIdAsync(callId);

            if (d == null || !(await WebGlobal.CurrentUser.HasAccessToCompanyAsync(d.CompanyId)))
            {
                throw new Web.HttpResponseException(
                    new HttpResponseMessage(HttpStatusCode.NotFound)
                    {
                        Content = new StringContent("You don't have access to the company specified, or it doesn't exist.")
                    });
            }

            if (Web.HttpContext.Current.Request.Form.Files.Count == 0)
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest)
                {
                    Content = new StringContent("No files were included in your request.")
                });
            }

            // Save file
            string path = System.IO.Path.GetTempPath();

            //MultipartFormDataStreamProvider provider = new MultipartFormDataStreamProvider(path);

            //var task = await Request.Content.ReadAsMultipartAsync(provider);

            var file = Web.HttpContext.Current.Request.Form.Files[0];

            try
            {
                string fn = "";
                var dispatchEntries = new System.Collections.ObjectModel.Collection<int>();
                dispatchEntries.Add(callId);

                var cn = new CompanyFile();

                cn.CompanyId = d.CompanyId;

                cn.OwnerUserId = WebGlobal.CurrentUser.Id;
                cn.DispatchEntries = dispatchEntries;
                cn.Filename = file.FileName;
                cn.Size = (int)file.Length;
                cn.RawUrl = cn.Filename;
                cn.Description = description;
                // TODO: Can be made async
                cn.Save();

                fn = HttpContextHelper.MapPath(cn.LocalLocation);

                if (!Directory.Exists(Path.GetDirectoryName(fn)))
                {
                    Directory.CreateDirectory(Path.GetDirectoryName(fn));
                }

                await FileUploadUtils.saveAsAsync(file, fn);


                if (!DefenderApi.IsFileUnsafe(fn))
                {
                    var result = await FileUtility.SendFileAsync(fn);
                    if (result.IsHttpSuccess())
                        System.IO.File.Delete(fn);
                }
                else
                {
                    // TODO: Can be made async
                    cn.Delete(WebGlobal.CurrentUser);
                    System.IO.File.Delete(fn);

                    return StatusCode((int)HttpStatusCode.BadRequest, null);
                }


                var sc = await CallsController.GetSubcontractorCallAsync(d);
                if (sc != null)
                {
                    if (sc.SubcontractorEntry != null)
                    {
                        var cn2 = new Extric.Towbook.CompanyFile();

                        cn2.CompanyId = sc.SubcontractorEntry.CompanyId;
                        cn2.OwnerUserId = 1;
                        cn2.DispatchEntries = new System.Collections.ObjectModel.Collection<int>();
                        cn2.DispatchEntries.Add(sc.SubcontractorEntry.Id);
                        cn2.Filename = file.FileName;
                        cn2.Size = (int)file.Length;
                        cn2.RawUrl = cn.Filename;
                        // TODO: Can be made async
                        cn2.Save();
                        cn2.Description = "Sent from " + WebGlobal.CurrentUser.Company;
                        var fn2 = HttpContextHelper.MapPath(cn2.LocalLocation);

                        await FileUtility.RemoteCopyFile(fn, fn2);
                    }
                }
                else if (d.Account?.MasterAccountId == MasterAccountTypes.Towbook)
                {
                    int destCompanyId = 0;
                    if (d.Account.ReferenceNumber != null)
                        int.TryParse(d.Account.ReferenceNumber, out destCompanyId); // Sending Company

                    var thisDigital = await Dispatch.CallRequest.GetByDispatchEntryId(d.Id);
                    if (thisDigital != null)
                    {
                        var senderCallId = Convert.ToInt32(thisDigital.PurchaseOrderNumber);
                        var senderCall = await Dispatch.Entry.GetByIdAsync(senderCallId);
                        if (senderCall?.CompanyId == destCompanyId)
                        { 
                            var cn3 = new Extric.Towbook.CompanyFile();

                            cn3.CompanyId = senderCall.CompanyId;
                            cn3.OwnerUserId = 1;
                            cn3.DispatchEntries = new System.Collections.ObjectModel.Collection<int>();
                            cn3.DispatchEntries.Add(senderCall.Id);
                            cn3.Filename = file.FileName;
                            cn3.Size = (int)file.Length;
                            cn3.RawUrl = cn.Filename;
                            cn3.Save();
                            cn3.Description = "Sent from " + WebGlobal.CurrentUser.Company;
                            var fn3 = HttpContextHelper.MapPath(cn3.LocalLocation);

                            await FileUtility.RemoteCopyFile(fn, fn3);
                        }
                    }
                }

                return StatusCode((int)HttpStatusCode.Created, CompanyFileModel.Map(cn));
                //return this.Request.CreateResponse(HttpStatusCode.Created, CompanyFileModel.Map(cn), PerRequestJsonSettingsFormatter.Instance);
            }
            catch
            {
                throw;
                //throw new HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotImplemented) { Content = new StringContent("Invalid Image File. Only JPG, PNG, BMP and GIF files are supported.") });
            }
            finally
            {
                //file.Delete();
            }
        }

        private static IEnumerable<CompanyFileModel> Map(IEnumerable<CompanyFile> files)
        {
            return files
                .Where(o => !(o.Filename == "Internal_ParsedResult.json" && o.OwnerUserId < 20))
                .Select(o => CompanyFileModel.Map(o));
        }

        [HttpGet]
        [Route("{id}/view")]
        public async Task<HttpResponseMessage> View(int id)
        {
            CompanyFile cf = await CompanyFile.GetByIdAsync(id);

            await ThrowIfNoCompanyAccessAsync(cf?.CompanyId);

            if (cf == null || await FileUtility.GetFileAsync(HttpContextHelper.MapPath(cf.LocalLocation)) == null)
                return new HttpResponseMessage(HttpStatusCode.NotFound);

            if (cf != null)
            {
                if (cf.Filename.ToUpper().Contains(".XML"))
                {
                    // TODO: Can be made async
                    FaxTransaction ft = FaxTransaction.FromFile(cf.LocalLocation);

                    var file1 = ft.Documents.Where(o => o.DocType.ToUpperInvariant() == "TIFF").FirstOrDefault();

                    if (file1 == null)
                        return new HttpResponseMessage(HttpStatusCode.NoContent);

                    byte[] buffer = Convert.FromBase64String(file1.FileData);

                    HttpResponseMessage response = new HttpResponseMessage(HttpStatusCode.OK);
                    response.Content = new ByteArrayContent(buffer);
                    response.Content.Headers.ContentType = new MediaTypeHeaderValue("application/octet-stream");
                    response.Content.Headers.ContentLength = buffer.Length;

                    return response;
                }
                else if (cf.Filename.ToUpper().Contains(".EML"))
                {
                    using (var stream = new FileStream(await FileUtility.GetFileAsync(HttpContextHelper.MapPath(cf.LocalLocation)),
                        FileMode.Open, FileAccess.Read))
                    {
                        var parser = new MimeParser(stream, MimeFormat.Entity);
                        var message = await parser.ParseMessageAsync();

                        var html = message.HtmlBody;
                        StringBuilder outputHtml = new StringBuilder();

                        outputHtml.Append("<html><head></head><body style=\"background-color: #efefef\"><div style=\"margin: 0 auto; max-width: 1000px; background-color: white; padding: 10px\">");
                        outputHtml.Append("<p style=\"font-family: verdana; font-size: 14px\"><strong style=\"min-width: 100px; display:inline-block\">Subject</strong>" + message.Subject + "</p>");
                        if (String.IsNullOrWhiteSpace(html))
                            outputHtml.Append("<pre style=\"background-color: white; white-space: pre-wrap; margin: 0 auto; max-width: 1000px\">" + message.TextBody + "</pre>");
                        else
                            outputHtml.Append(html);

                        outputHtml.Append("</body></html>");

                        HttpResponseMessage response = new HttpResponseMessage(HttpStatusCode.OK);
                        response.Content = new StringContent(outputHtml.ToString());
                        response.Content.Headers.ContentType = new MediaTypeHeaderValue("text/html");

                        return response;
                    }
                }
                else
                {
                    var stream = new FileStream(await FileUtility.GetFileAsync(HttpContextHelper.MapPath(cf.LocalLocation)),
                        FileMode.Open, FileAccess.Read, FileShare.ReadWrite);
                    HttpResponseMessage response = new HttpResponseMessage(HttpStatusCode.OK);

                    response.Content = new StreamContent(stream);

                    response.Content.Headers.ContentType = new MediaTypeHeaderValue(cf.Filename.ToUpper().Contains(".PDF") ? "application/pdf" : "application/octet-stream");
                    response.Content.Headers.ContentDisposition = new ContentDispositionHeaderValue("inline");
                    response.Content.Headers.ContentDisposition.FileName = cf.Filename;

                    return response;
                }
            }

            return new HttpResponseMessage(HttpStatusCode.NoContent);
        }

        [HttpGet]
        [Route("{id}/download")]
        public async Task<HttpResponseMessage> Download(int id)
        {
            // gets the file row from the db
            var cf = await CompanyFile.GetByIdAsync(id);

            await ThrowIfNoCompanyAccessAsync(cf?.CompanyId);

            var serverPathFileName = HttpContextHelper.MapPath(cf.LocalLocation);
            var filePath = await FileUtility.GetFileAsync(serverPathFileName);

            // check if the file exists
            if (filePath == null)
            {
                if (!System.IO.File.Exists(serverPathFileName))
                    return new HttpResponseMessage(HttpStatusCode.NotFound);
            }

            if (cf != null)
            {
                // if made to this point is a valid file and return the content
                HttpResponseMessage result = new HttpResponseMessage(HttpStatusCode.OK);

                // writes the content to the response stream
                if (filePath == null)
                    result.Content = new StreamContent(new FileStream(serverPathFileName, FileMode.Open, FileAccess.Read, FileShare.ReadWrite));
                else
                    result.Content = new StreamContent(new FileStream(filePath, FileMode.Open, FileAccess.Read));

                if (filePath == null)
                    result.Content.Headers.ContentLength = new FileInfo(serverPathFileName).Length;

                result.Content.Headers.ContentType = new MediaTypeHeaderValue("application/octet-stream");
                result.Content.Headers.ContentDisposition = new ContentDispositionHeaderValue("attachment");
                result.Content.Headers.ContentDisposition.FileName = cf.Filename;
                
                return result;
            }

            return new HttpResponseMessage(HttpStatusCode.NotFound);
        }
    }
}
