using Extric.Towbook.Accounts;
using Extric.Towbook.API.Controllers;
using Extric.Towbook.API.Models.Quotes;
using Extric.Towbook.Company;
using Extric.Towbook.Dispatch;
using Extric.Towbook.Dispatch.CallModels;
using Extric.Towbook.Dispatch.QuoteModels;
using Extric.Towbook.Integrations.Email;
using Extric.Towbook.Utility;
using Extric.Towbook.WebShared;
using Microsoft.Azure.Cosmos;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Configuration;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Mail;
using System.Net.Mime;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Extric.Towbook.Auctions;
using static Extric.Towbook.API.ApiUtility;
using Extric.Towbook.API.Models.Calls;
using Extric.Towbook.Web;
using Extric.Towbook.API.Models.Quotes.Extensions;
using QuoteModelExtension = Extric.Towbook.API.Models.Quotes.QuoteModelExtension;


namespace Extric.Towbook.API.Quotes.Controllers
{
    [Route("quotes")]

    //routes.MapHttpRoute(
    //    name: "Quotes Default actions",
    //    routeTemplate: "quotes/{id}/{action}",
    //    defaults: new { controller = "Quotes", id = RouteParameter.Optional, action = RouteParameter.Optional })
    //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Quotes.Controllers" } } };
    public class QuotesController : ControllerBase
    {
        //routes.MapHttpRoute(
        //    name: "Quote_GET",
        //    routeTemplate: "quotes/{id}",
        //    defaults: new { controller = "Quotes", action = "Get" },
        //        constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "GET" }) })
        //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Quotes.Controllers" } } };
        [HttpGet]
        [Route("{id}")]
        public async Task<QuoteModel> Get(Guid id)
        {
            await ThrowIfFeatureNotAssignedAsync(Generated.Features.Dispatching_Quotes);

            if (id == Guid.Empty)
                return await QuoteModelExtension.MapAsync(new QuoteModel());

            var companies = await WebGlobal.GetCompaniesAsync();
            var webCompanies = companies.Length > 1 ? companies.Select(s => s.Id).ToArray() : new int[] { companies.First().Id }.ToArray();

            var model = await QuoteModel.GetById(id, webCompanies);

            ThrowIfNotFound(model, "Quote");
            await ThrowIfNoCompanyAccessAsync(model.CompanyId);

            if ((WebGlobal.CurrentUser.Type == Towbook.User.TypeEnum.AccountManager |
                WebGlobal.CurrentUser.Type == Towbook.User.TypeEnum.AccountUser) && (model.AccountId != WebGlobal.CurrentUser.AccountId && model.Call.BillToAccountId != WebGlobal.CurrentUser.AccountId))
            {
                throw new HttpResponseException(new HttpResponseMessage(HttpStatusCode.Forbidden)
                {
                    Content = new StringContent($"Your user account doesn't have access to this quote.")
                });
            }

            return await QuoteModelExtension.MapAsync(model);
        }

        [HttpGet]
        [Route("")]
        public async Task<IEnumerable<QuoteModel>> Get([FromQuery] string qs = null)
        {
            await ThrowIfFeatureNotAssignedAsync(Generated.Features.Dispatching_Quotes);

            var newClient = CosmosDB.Get().Client;
            var rx = newClient.GetDatabase(GetDatabaseName());
            var container = rx.GetContainer("quotes");

            var result = new Collection<QuoteModel>();

            var webCompanies = await WebGlobal.GetCompaniesAsync();

            if (webCompanies.Length > 1)
            {
                result = await ExecuteQuery<QuoteModel>(container,
                    new QueryDefinition($"SELECT * FROM r WHERE (r.companyId IN({string.Join(",", webCompanies.Select(o => o.Id))})) and r.deleted=false ORDER BY r.quoteNumber DESC"), null);
            }
            else
            {
                result = await ExecuteQuery<QuoteModel>(container,
                    new QueryDefinition("SELECT * FROM r WHERE r.deleted=false ORDER BY r.quoteNumber DESC"),
                    new PartitionKey(webCompanies.First().Id));
            }

            if (!string.IsNullOrWhiteSpace(qs))
            {
                result = await FilterBySearchTerm(result, qs);
            }

            if (WebGlobal.CurrentUser.Type == Towbook.User.TypeEnum.AccountManager ||
                WebGlobal.CurrentUser.Type == Towbook.User.TypeEnum.AccountUser)
            {
                result = result.Where(q => 
                    q.AccountId == WebGlobal.CurrentUser.AccountId || 
                    q.Call.BillToAccountId == WebGlobal.CurrentUser.AccountId)
                    .ToCollection();
            }

            var orderResult = result.OrderByDescending(o => o.CreateDate).ToCollection();

            return await QuoteModelExtension.MapAsync(orderResult);
        }

        [HttpPut]
        [Route("{id}")]
        public async Task<QuoteModel> Put(Guid id, QuoteModel model)
        {
            await ThrowIfFeatureNotAssignedAsync(Generated.Features.Dispatching_Quotes);

            if (id == Guid.Empty)
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest)
                {
                    Content = new StringContent("Id must be provided. Did you mean to do a POST request?")
                });

            ThrowIfNotFound(model.Call, "Call Data");
            ThrowIfNotFound(model.Call.Account, "Call Data");

            if (model.CompanyId < 1 || model.AccountId < 1)
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest)
                {
                    Content = new StringContent("A companyId and accountId must be provided.")
                });

            // Get original for checks and balances
            var orig = await QuoteModel.GetById(id, new int[] { model.CompanyId });
            
            orig.Call = model.Call;
            
            if(!string.IsNullOrWhiteSpace(model.Name))
                orig.Name = model.Name;

            orig.CompanyId = model.CompanyId;
            orig.AccountId = model.Call.Account.Id > 0 ? model.Call.Account.Id : 1;

            ThrowIfNotFound(orig, "Quote");
            await ThrowIfNoCompanyAccessAsync(orig.CompanyId, "Quote");

            orig = await QuoteModelExtension.MapAsync(orig);

            if (!orig.AvailableActions.Contains("MODIFY"))
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest)
                {
                    Content = new StringContent("You don't own this quote or don't have permissions to make modifications to it.")
                });
            }

            Entry quoteMappedEntry = await orig.MapToEntry();
            Entry callMappedEntry = await Models.Calls.CallModelExtensions.MapAsync(orig.Call, quoteMappedEntry);

            orig.Call = await CallModel.MapAsync(
                        callMappedEntry,
                        Array.Empty<CallModel.CallInsightHelper.CallInsightModel>(),
                        Array.Empty<InvoicePayment>(),
                        null,
                        new Towbook.Impounds.Impound(),
                        Array.Empty<VehicleTitle>(),
                        Array.Empty<EntryAuctionDetail>());

            await QuoteModel.UpdateInAzure(orig);

            return orig;
        }

        [HttpPost]
        [Route("{id}/convert")]
        public async Task<ObjectResult> Convert(Guid id, [FromQuery] bool delete = true)
        {
            CallsController callsController = (CallsController) new CallsController().Init(this);

            await ThrowIfFeatureNotAssignedAsync(Generated.Features.Dispatching_Quotes);

            var companies = await WebGlobal.GetCompaniesAsync();
            var webCompanies = companies.Length > 1 ? companies.Select(s => s.Id).ToArray() : new int[] { companies.First().Id }.ToArray();

            var quote = await QuoteModel.GetById(id, webCompanies);

            ThrowIfNotFound(quote, "Quote");
            await ThrowIfNoCompanyAccessAsync(quote.CompanyId, "Company");

            var call = quote.Call;

            if (string.IsNullOrEmpty(call.TowSource))
                call.TowSource = call.Waypoints?.FirstOrDefault(f => f.Title == "Pickup")?.Address;
                
            if (string.IsNullOrEmpty(call.TowSource))
                call.TowSource = "No address provided from Quote #" + quote.QuoteNumber;

            if (string.IsNullOrEmpty(call.TowDestination))
                call.TowDestination = call.Waypoints?.FirstOrDefault(f => f.Title == "Destination")?.Address;

            var cavm = new Collection<Dispatch.AttributeValue>();
            cavm.Add(new Dispatch.AttributeValue()
            {
                DispatchEntryAttributeId = Dispatch.AttributeValue.BUILTIN_DISPATCH_QUOTE_EVENT_JSON,
                Value = new QuoteEventJson()
                {
                    QuoteName = quote.Name,
                    QuoteNumber = quote.QuoteNumber,
                    Performer = WebGlobal.CurrentUser.FullName,
                    PerformerId = WebGlobal.CurrentUser.Id,
                    ConvertDate = DateTime.Now.ToUniversalTime()
                }.ToJson()
            });

            call.ArrivalETA = (DateTime?)null;
            call.CreateDate = DateTime.Now;

            call.Attributes = call.Attributes?.Union(CallAttributeValueModel.Map(cavm.ToArray())).ToArray();

            call.Notes = $"Call created from Quote #{quote.QuoteNumber} on {Core.OffsetDateTime(WebGlobal.CurrentUser.Company, DateTime.Now).ToShortDateString()} @ {Core.OffsetDateTime(WebGlobal.CurrentUser.Company, DateTime.Now).ToShortTowbookTimeString()}\n\n{call.Notes}";


            var callId = await callsController.InsertOrUpdateAsync(0, call, false, false, true);

            if (callId > 0)
                call = await callsController.Get(callId);

            if (delete)
                await DeleteQuote(quote);

            return StatusCode((int)HttpStatusCode.Created, call);
        }

        //routes.MapHttpRoute(
        //    name: "Quote_Duplicate_GET",
        //    routeTemplate: "quotes/{id}/duplicate",
        //    defaults: new { controller = "Quotes", action = "DuplicateQuote" },
        //        constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "GET" }) })
        //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { "Extric.Towbook.API.Quotes.Controllers" } } };
        [HttpGet]
        [Route("{id}/duplicate")]
        public async Task<QuoteModel> DuplicateQuote(Guid id)
        {
            await ThrowIfFeatureNotAssignedAsync(Generated.Features.Dispatching_Quotes);

            if (id == Guid.Empty)
                throw new HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest)
                {
                    Content = new StringContent("Id must be provided to duplicate a quote")
                });

            var orig = await QuoteModel.GetById(id, (await WebGlobal.GetCompaniesAsync()).Select(s => s.Id).ToArray());

            ThrowIfNotFound(orig, "quote");



            var quote = await QuoteModelExtension.MapAsync(new QuoteModel()
            {
                Id = Guid.Empty,
                Name = "Duplicate of Quote #" + orig.QuoteNumber.ToString(),
                Call = orig.Call,
                Owner = new OwnerModel()
                {
                    Id = WebGlobal.CurrentUser.Id,
                    Name = WebGlobal.CurrentUser.FullName
                },
                CompanyId = orig.CompanyId,
                AccountId = orig.AccountId
            });

            quote.AvailableActions = new string[] { "MODIFY", "CREATE" };

            return quote;
        }

        [HttpPost]
        [Route("{id}/duplicate")]
        public async Task<QuoteModel> Duplicate(Guid id)
        {
            await ThrowIfFeatureNotAssignedAsync(Generated.Features.Dispatching_Quotes);

            if (id == Guid.Empty)
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest)
                {
                    Content = new StringContent("Id must be provided to duplicate a quote")
                });

            var orig = await QuoteModel.GetById(id, (await WebGlobal.GetCompaniesAsync()).Select(s => s.Id).ToArray());

            ThrowIfNotFound(orig, "quote");

            orig.Call.CreateDate = Core.OffsetDateTime(WebGlobal.CurrentUser.Company, DateTime.Now, true);

            var quote = await Post(new QuoteModel()
            {
                Id = Guid.Empty,
                Name = "Duplicate of Quote #" + orig.QuoteNumber.ToString(),
                // Don't offsite time. We want to save in Eastern time, and the API servers use Eastern time.
                CreateDate = DateTime.Now,
                Call = orig.Call
            });

            return await QuoteModelExtension.MapAsync(quote);
        }

        [HttpPost]
        [Route("{id}/rename")]
        public async Task<HttpResponseMessage> Rename(Guid id, QuoteModel model)
        {
            await ThrowIfFeatureNotAssignedAsync(Generated.Features.Dispatching_Quotes);

            if (string.IsNullOrEmpty(model.Name))
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest)
                {
                    Content = new StringContent("Name must be provided.")
                });
            }

            var companies = await WebGlobal.GetCompaniesAsync();
            var webCompanies = companies.Length > 1 ? companies.Select(s => s.Id).ToArray() : new int[] { companies.First().Id }.ToArray();

            var quote = await QuoteModel.GetById(id, webCompanies);

            ThrowIfNotFound(quote, "Quote");
            await ThrowIfNoCompanyAccessAsync(quote.CompanyId, "Company");

            quote = await QuoteModelExtension.MapAsync(quote);

            if (!quote.AvailableActions.Contains("RENAME"))
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest)
                {
                    Content = new StringContent("You don't have permission to perform this action.")
                });
            }

            quote.Name = model.Name;

            await QuoteModel.UpdateInAzure(quote);

            return new HttpResponseMessage() { StatusCode = HttpStatusCode.OK };
        }

        [HttpPost]
        [Route("")]
        public async Task<QuoteModel> Post(QuoteModel model)
        {
            await ThrowIfFeatureNotAssignedAsync(Generated.Features.Dispatching_Quotes);
            ThrowIfNotFound(model.Call, "Call Data");
            
            // force accountId and companyId to match the call data
            model.CompanyId = model.Call.CompanyId;
            model.AccountId = model.Call.Account?.Id > 0 ? model.Call.Account.Id : 1;

            model = await QuoteModelExtension.MapAsync(model);

            if (!model.AvailableActions.Contains("CREATE"))
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest)
                {
                    Content = new StringContent("You don't have permission to perform this action")
                });
            }

            if (model.Id != Guid.Empty)
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest)
                {
                    Content = new StringContent("Id is provided. Did you mean to do a PUT request?")
                });

            if (model.CompanyId < 1 || model.AccountId < 1)
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest)
                {
                    Content = new StringContent("A companyId and accountId must be provided.")
                });

            await ThrowIfNoCompanyAccessAsync(model.CompanyId, "Company");

            var acc = await Account.GetByIdAsync(model.AccountId);
            if (acc.CompanyId != 1)
                await ThrowIfNoCompanyAccessAsync(acc.CompanyId, "Account");

            #region Finish Model
            var e = await Extric.Towbook.API.Models.Quotes.Extensions.QuoteModelExtension.MapToEntry(model);

            model.Call = await CallModel.MapAsync(
                        await Models.Calls.CallModelExtensions.MapAsync(model.Call, e),
                        new CallModel.CallInsightHelper.CallInsightModel[0],
                        Array.Empty<InvoicePayment>(), 
                        null,
                        new Towbook.Impounds.Impound(),
                        Array.Empty<VehicleTitle>(),
                        Array.Empty<Towbook.Auctions.EntryAuctionDetail>());
            
            // create unique id
            model.Id = Guid.NewGuid();
            // Doesn't need to be offset for the company because the API servers are already in Eastern time,
            // and we want this saved in Eastern time.
            model.CreateDate = DateTime.Now;
            model.Call.CreateDate = model.CreateDate;

            // force performer
            model.Owner = new OwnerModel()
            {
                Id = WebGlobal.CurrentUser.Id,
                Name = WebGlobal.CurrentUser.FullName
            };

            // Generate sequence number
            // NOTE: the procedure used here will check for shared company scenarios and 
            // keep the sequencing shared across mulitcompanies.
            model.QuoteNumber = await QuoteModel.GetNextQuoteNumberAsync(model.CompanyId);

            if (string.IsNullOrWhiteSpace(model.Name))
                model.Name = $"Quote #{model.QuoteNumber}";
            #endregion

            await QuoteModel.UpdateInAzure(model);

            return await QuoteModelExtension.MapAsync(model);
        }

        [HttpDelete]
        [Route("{id}")]
        [Route("{id}/delete")]
        public async Task<HttpResponseMessage> Delete(Guid id)
        {
            await ThrowIfFeatureNotAssignedAsync(Generated.Features.Dispatching_Quotes);

            var companies = await WebGlobal.GetCompaniesAsync();
            var webCompanies = companies.Length > 1 ? companies.Select(s => s.Id).ToArray() : new int[] { companies.First().Id }.ToArray();

            var quote = await QuoteModel.GetById(id, webCompanies);

            ThrowIfNotFound(quote, "Quote");
            await ThrowIfNoCompanyAccessAsync(quote.CompanyId);

            quote = await QuoteModelExtension.MapAsync(quote);

            if(quote == null || !quote.AvailableActions.Contains("DELETE"))
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest)
                {
                    Content = new StringContent("You don't own this quote or don't have permissions to delete it.")
                });

            await DeleteQuote(quote);

            return new HttpResponseMessage() { StatusCode = HttpStatusCode.OK };
        }

        [HttpPost]
        [Route("{id}/email")]
        public async Task<HttpResponseMessage> Email(Guid id, EmailInput emailInput)
        {
            await ThrowIfFeatureNotAssignedAsync(Generated.Features.Dispatching_Quotes);

            QuoteModel quote = await QuoteModel.GetById(id, (await WebGlobal.GetCompaniesAsync()).Select(o => o.Id).ToArray());

            if (quote == null || !await WebGlobal.CurrentUser.HasAccessToCompanyAsync(quote.CompanyId))
                throw new Exception("You don't have permission to access the quote or the quote doesn't exist.");

            string domain = WebGlobal.GetAppDomain();
            // TODO: Can be made async
            var company = await Company.Company.GetByIdAsync(quote.CompanyId);

            var notifications = new List<string>();
            notifications.AddRange(emailInput.emails);

            if (notifications.Count > 0)
            {
                string output = "";
                QuoteWebLink webLink = null;

                var sendFromEmail = company.Email;
                var sendFromName = company.Name;

                // TODO: Can be made async
                var billingAddress = AddressBookEntry.GetByName("Billing Address", company.Id, false).FirstOrDefault();
                if(Core.IsEmailValid(billingAddress?.Email))
                    sendFromEmail = billingAddress?.Email;

                string url =  domain + "/dispatch2/Invoice.aspx?q=" + id.ToString();
                // TODO: Can be made async
                string md5 = Core.MD5(quote.QuoteNumber.ToString() + ":27ed2fb84d816");

                url += "&auth=" + md5;

                foreach (string xemail in notifications)
                {
                    string possiblePhoneNumber = xemail.Replace(" ", "").Replace("-", "").Replace("(", "").Replace(")", "").Replace(".", "");

                    webLink = new QuoteWebLink()
                    {
                        CompanyId = WebGlobal.CurrentUser.CompanyId,
                        CreateDate = DateTime.Now,
                        OwnerUserId = WebGlobal.CurrentUser.Id,
                        UrlHash = ShortCode.NewShortCodeByDate(DateTime.Now),
                        QuoteId = quote.Id.ToString()
                    };

                    // TODO: This isn't performant, (n database/network calls on a loop)
                    await webLink.Save();

                    long phoneNumberOutput = 0;
                    if (long.TryParse(possiblePhoneNumber, out phoneNumberOutput))
                    {
                        if (possiblePhoneNumber.Length == 11 && possiblePhoneNumber[0] == '1')
                            possiblePhoneNumber = possiblePhoneNumber.Substring(1);

                        if (possiblePhoneNumber.Length == 10)
                        {

                            long phoneNumber = 0;

                            if (long.TryParse(possiblePhoneNumber, out phoneNumber))
                            {

                                var message = $"View a copy of your quote from " +
                                    company.Name.Trim() + ", at:\n" + webLink.TinyUrl;

                                // TODO: This isn't performant, (n database/network calls on a loop)
                                await DispatchNotificationMessage.SendAsync(WebGlobal.CurrentUser, possiblePhoneNumber, message);
                            }

                            output += "Sent SMS Text Message to " + WebGlobal.FormatPhone(possiblePhoneNumber) + "\n";
                            continue;
                        }
                    }

                    if (Core.IsEmailValid(xemail))
                    {
                        // TODO: Can be made async, also this isn't performant, (n database/network calls on a loop)
                        using (var mm = new MailMessage(new MailAddress(EmailAddress.GetTowbookDotNetEmailAddressForCompany(company.Id), company.Name), new MailAddress(xemail))) 
                        { 

                            mm.Subject = company.Name.Trim() + $": Quote #" + quote.QuoteNumber.ToString();

                            mm.ReplyToList.Add(new MailAddress(
                                sendFromEmail,
                                sendFromName));

                            System.IO.Stream stream = null;

                            try
                            {
                                var response = await WebGlobal.GetResponseFromUrlAsync(url, false);

                                if (response.StatusCode != HttpStatusCode.OK)
                                    throw new TowbookException("Error retreiving quote as an attachment.");
                                
                                var html = await response.Content.ReadAsStringAsync();

                                stream = await GeneratePdf(html, request: Web.HttpContext.Current.Request);

                                stream.Seek(0, SeekOrigin.Begin);

                                ContentType ct = new ContentType(MediaTypeNames.Application.Pdf);
                                Attachment attach = new Attachment(stream, ct);
                                var filename = String.Format("Q{0}.pdf", quote.QuoteNumber);

                                attach.ContentDisposition.FileName = filename;
                                mm.Attachments.Add(attach);

                                mm.IsBodyHtml = false;
                                mm.Body = emailInput.optionalMessage + "\n\n";

                                mm.Body += $"Attached is a PDF copy of Quote #{quote.QuoteNumber} created on {Core.OffsetDateTime(company, quote.CreateDate).ToShortDateString(company)} at {Core.OffsetDateTime(company, quote.CreateDate).ToShortTowbookTimeString()}.  ";

                                mm.Body += "To view it, please open the attachment.\n\n";
                                mm.Body += "If you cannot open the attachment, you can download Adobe Acrobat Reader from http://get.adobe.com/reader/\n\n";


                                

                                mm.Body += $"You can view a copy of your quote by visiting {webLink.TinyUrl}\n\n";

                                mm.Body += "If you have any questions, please feel free to contact us.\n\n";

                                mm.Body += "Thank you for your business,\n\n" +
                                    company.Name + "\n" +
                                    company.Phone + "\n" +
                                    (!String.IsNullOrWhiteSpace(company.Website) ? company.Website + "\n\n" : "\n") +
                                    "Sent using Towbook";


                                var metaData = new Dictionary<string, string>()
                                {
                                    { "quoteId", quote.Id.ToString() },
                                    { "quoteNumber", quote.QuoteNumber.ToString() }
                                };

                                for (int i = 0; i < 3; i++)
                                {
                                    try
                                    {
                                        using (var sc = new SmtpClient().Get())
                                        {
                                            // TODO: Optimization opportunity, parallel sending of this (perhaps even batch send)?
                                            await SmtpClientExtensions.Send(sc, mm, WebGlobal.CurrentUser, "Quotes", EmailType.Quote, quote.QuoteNumber, new int[]{}, quote.Id, null, null, webLink, metaData);
                                        }
                                        break;
                                    }
                                    catch
                                    {
                                        if (i == 3)
                                            throw;
                                    }
                                }
                            }
                            catch(Exception ee)
                            {
                                return new HttpResponseMessage(HttpStatusCode.InternalServerError) { Content = new StringContent($"Error retreiving quote {quote.Id}. ({ee.InnerException?.Message ?? "HTTP result:" + ee.HResult.ToString() })") };
                            }
                            finally
                            {
                                if (stream != null)
                                    stream.Dispose();
                            }
                            
                        }
                        

                        output += "Sent quote via email to " + xemail + "\n";
                    }
                }

                return new HttpResponseMessage(HttpStatusCode.OK) { Content = new StringContent(output) };
            }
            else
            {
                return new HttpResponseMessage(HttpStatusCode.InternalServerError) { Content = new StringContent("No emails or phone numbers specified.") };
            }

        }

        private async Task DeleteQuote(QuoteModel quote)
        {
            if (quote == null)
                return;

            await QuoteModel.UpdateInAzure(quote, true);
        }

        private async Task<Collection<QuoteModel>> FilterBySearchTerm(Collection<QuoteModel> list, string search)
        {
            if (string.IsNullOrEmpty(search))
                return list;

            var found = new Collection<Guid>();
            foreach (var q in list) {

                if (q.Call == null)
                    break;

                var reason = await Reason.GetByIdAsync(q.Call.Reason?.Id ?? 0);

                var quotePairs = new Dictionary<string, string>() {
                        { "QuoteNumber", q.QuoteNumber.ToString() },
                        { "ContactNames", string.Join(", ", q.Call.Contacts?.Select(s => s.Name?.ToLowerInvariant()) ?? new string[]{}) },
                        { "ContactPhones", string.Join(", ", q.Call.Contacts?.Select(s => s.Phone?.ToLowerInvariant()) ?? new string[]{}) },
                        { "Notes", q.Call.Notes?.ToLowerInvariant() ?? string.Empty },
                        { "TowSource", q.Call.TowSource?.ToLowerInvariant() ?? string.Empty },
                        { "TowDestination", q.Call.TowDestination?.ToLowerInvariant() ?? string.Empty },
                        // TODO: Can be made async
                        { "Reason", reason?.Name.ToLowerInvariant() ?? string.Empty },
                        // TODO: Can be made async
                        { "Account", (await Account.GetByIdAsync(q.AccountId))?.Company.ToLowerInvariant() ?? string.Empty },
                        // TODO: Can be made async
                        { "Company", (await Company.Company.GetByIdAsync(q.CompanyId))?.Name.ToLowerInvariant() ?? string.Empty },
                        { "VehicleYear", string.Join(", ", q.Call.Assets?.Select(s => s.Year?.ToString()) ?? new string[]{}) },
                        { "VehicleMake", string.Join(", ", q.Call.Assets?.Select(s => s.Make?.ToLowerInvariant()) ?? new string[]{}) },
                        { "VehicleModel", string.Join(", ", q.Call.Assets?.Select(s => s.Model?.ToLowerInvariant()) ?? new string[]{}) },
                        { "VIN", string.Join(", ", q.Call.Assets?.Select(s => s.Vin?.ToLowerInvariant()) ?? new string[]{}) },
                        { "LicenseNumber", string.Join(", ", q.Call.Assets?.Select(s => s.LicenseNumber?.ToLowerInvariant()) ?? new string[]{}) },
                        { "LicenseState", string.Join(", ", q.Call.Assets?.Select(s => s.LicenseState?.ToLowerInvariant()) ?? new string[]{}) },
                        { "PurchaseOrder", q.Call.PurchaseOrderNumber ?? string.Empty },
                        { "InvoiceNumber", q.Call.InvoiceNumber ?? string.Empty },
                };

                if (quotePairs.Values.Any(a => a.Contains(search.ToLowerInvariant())))
                    found.Add(q.Id);
            }

            return list.Where(w => found.Contains(w.Id)).ToCollection();
        }

        internal static string GetDatabaseName()
        {
            return Core.CosmosDatabase ?? "towbook";
        }
    }

    public class EmailInput
    {
        public string[] emails { get; set; }
        public int[] files { get; set; }
        public string optionalMessage { get; set; }
        public int[] DamageFormIds { get; set; }
        public bool HidePricing { get; set; }
        public bool HideDiscounts { get; set; } = false;
        public bool HidePhotos { get; set; } = false;
        public bool ShowSurveyLink { get; set; } = false;
        public bool IncludePaymentLink { get; set; }
    }

}
