using Extric.Towbook.API.Models;
using Extric.Towbook.WebShared;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using Extric.Towbook.Utility;
using static Extric.Towbook.API.ApiUtility;
using Microsoft.AspNetCore.Mvc;

namespace Extric.Towbook.API.Controllers
{
    [Route("TaxRates")]
    public class TaxRatesController : ControllerBase
    {
        [HttpGet]
        [Route("")]
        public async Task<IEnumerable<TaxRateModel>> GetAsync()
        {
            var taxRates = await TaxRate.GetByCompanyAsync((await this.GetCompaniesForRequestAsync()).Select(o => o.Id).ToArray());

            var list = taxRates.Select(TaxRateModel.Map).ToCollection();

            return list;
        }

        [HttpGet]
        [Route("{id}")]
        public async Task<IActionResult> Get(int id)
        {
            var taxRate = await TaxRate.GetByIdAsync(id);

            if (taxRate != null && taxRate.CompanyId > 0)
            {
                if (!WebGlobal.CurrentUser.HasAccessToCompany(taxRate.CompanyId))
                {
                    taxRate = null;
                }
            }

            if (taxRate != null)
            {
                return Ok(TaxRateModel.Map(taxRate));
            }
            else
            {
                return NotFound($"TaxRate {id} does not exist.");
            }
        }


        [ApiPermission(Extric.Towbook.User.TypeEnum.Manager)]
        [HttpPost]
        [Route("")]
        public async Task<TaxRateModel> Post(TaxRateModel model)
        {
            if (model == null)
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest)
                {
                    Content = new StringContent("Your request body is empty. Did you forget to include JSON content in your request?")
                });
            }

            if (model.Id > 0)
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest)
                {
                    Content = new StringContent("You shouldn't set the ID in a POST request. If you're trying to update an existing object, use PUT method instead")
                });
            }

            var taxRate = new TaxRate();

            TaxRateModel.Map(model, taxRate);

            taxRate.CompanyId = WebGlobal.CurrentUser.CompanyId;
            await taxRate.Save();

            return TaxRateModel.Map(taxRate);
        }

        [ApiPermission(Extric.Towbook.User.TypeEnum.Manager)]
        [HttpPut]
        [Route("{id}")]
        public async Task<TaxRateModel> Put(int id, TaxRateModel model)
        {
            if (model == null)
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest)
                {
                    Content = new StringContent("Your request body is empty. Did you forget to include the JSON or set the content-type to application/json?")
                });
            }

            if (model.Id < 1)
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest)
                {
                    Content = new StringContent("You must set the Id in a POST request. If you're trying to create a new taxRate, use the POST method instead.")
                });
            }

            if (model.Id != id)
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest)
                {
                    Content = new StringContent("The ID in the body of the request doesn't match the ID passed via the incoming URL.")
                });
            }

            var taxRate = await TaxRate.GetByIdAsync(id);

            await ThrowIfNoCompanyAccessAsync(taxRate?.CompanyId);

            TaxRateModel.Map(model, taxRate);
            await taxRate.Save();
            
            return TaxRateModel.Map(taxRate);
        }

        [ApiPermission(Extric.Towbook.User.TypeEnum.Manager)]
        [HttpDelete]
        [Route("{id}")]
        public async Task<HttpResponseMessage> Delete(int id)
        {
            var taxRate = await TaxRate.GetByIdAsync(id);

            if (taxRate == null)
                return new HttpResponseMessage(HttpStatusCode.OK);

            if (taxRate.CompanyId == 0 || !WebGlobal.CurrentUser.HasAccessToCompany(taxRate.CompanyId))
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.Forbidden)
                {
                    Content = new StringContent("You cannot update built-in taxRates or taxRates that you don't have access to.")
                });

            await taxRate.Delete(WebGlobal.CurrentUser);

            return new HttpResponseMessage(HttpStatusCode.Accepted);
        }
    }
}
