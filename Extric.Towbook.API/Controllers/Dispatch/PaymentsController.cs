using Extric.Towbook.Dispatch;
using Extric.Towbook.WebShared;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using Extric.Towbook.Integration;
using Extric.Towbook.API.Models.Calls;
using static Extric.Towbook.API.ApiUtility;
using Extric.Towbook.SquareIntegration;
using Extric.Towbook.Management.Payments;
using Extric.Towbook.API.Models;
using Extric.Towbook.API.Integration.Square;
using Extric.Towbook.API.Controllers.Accounting;
using Extric.Towbook.Company.Accounting;
using Extric.Towbook.Utility;
using Extric.Towbook.Company;
using Microsoft.AspNetCore.Mvc;
using NLog;

namespace Extric.Towbook.API.Controllers
{
    [Route("calls/{callId}/payments")]
    public class PaymentsController : ControllerBase
    {
        private static readonly Logger logger = LogManager.GetCurrentClassLogger();

        [HttpGet]
        [Route("")]
        public async Task<object> GetSolverAsync(int callId, [FromQuery] int? id, [FromQuery] string method, [FromQuery] bool includeVoided = false) =>
            id != null && id > 0 ? await GetAsync(callId, (int)id) : await GetAsync(callId, includeVoided);

        /// <summary>
        /// get all payments recorded for this call
        /// /api/calls/{callId}>/payments
        /// </summary>
        /// <param name="callId"></param>
        /// <param name="includeVoided"></param>
        /// <returns></returns>
        //[HttpGet]
        //[Route("")]
        private async Task<IEnumerable<PaymentModel>> GetAsync(int callId, [FromQuery] bool includeVoided = false)
        {
            var e = await Entry.GetByIdAsync(callId);

            if (e == null)
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound));

            if (!(await WebGlobal.CurrentUser.HasAccessToDispatchEntryAsync(e)))
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotImplemented)
                {
                    Content = new StringContent("You don't have access to the specified call/invoice.")
                });

            return await InternalGetPaymentsAsync(callId, e, includeVoided);
        }

        public static async Task<IEnumerable<PaymentModel>> InternalGetPaymentsAsync(int callId, Entry e, bool includeVoided = false)
        {
            var blockPayments = false;

            if (WebGlobal.CurrentUser.Type == Towbook.User.TypeEnum.Driver ||
                WebGlobal.CurrentUser.Type == Towbook.User.TypeEnum.Dispatcher)
            {
                var preventDriversFromViewingCharges = (await CompanyKeyValue.GetByCompanyIdAsync(e.CompanyId, Provider.Towbook.ProviderId, "PreventDriversFromViewingInvoiceItems")).FirstOrDefault();
                var preventDispatchersFromViewingCharges = (await CompanyKeyValue.GetByCompanyIdAsync(e.CompanyId, Provider.Towbook.ProviderId, "PreventDispatchersFromViewingInvoiceItems")).FirstOrDefault();

                if (!string.IsNullOrWhiteSpace(preventDriversFromViewingCharges?.Value) &&
                    WebGlobal.CurrentUser.Type == Towbook.User.TypeEnum.Driver)
                    blockPayments = true;

                if (preventDispatchersFromViewingCharges != null &&
                    preventDispatchersFromViewingCharges.Value == "1" &&
                    WebGlobal.CurrentUser.Type == Towbook.User.TypeEnum.Dispatcher)
                    blockPayments = true;
            }

            var r = await InvoicePayment.GetByDispatchEntryIdAsync(callId, includeVoided);


            if (r != null)
            {
                IEnumerable<PaymentModel> payments;
                
                if (blockPayments)
                {
                    payments = r.Where(o => o.OwnerUserId == WebGlobal.CurrentUser.Id)
                        .Select(o => PaymentModel.Map(o, WebGlobal.CurrentUser));
                }
                else
                {
                    payments = r.Select(o => PaymentModel.Map(o, WebGlobal.CurrentUser));
                }

                if (await WebGlobal.CurrentUser.Company.HasFeatureAsync(Generated.Features.PaymentIntegrations_Square))
                {
                    return payments.Select(o =>
                    {
                        if (o.Type != PaymentType.Square.Id)
                            return o;

                        var x = InvoicePaymentState.GetCurrentStatusByInvoicePaymentId(o.Id);
                        if (x != null)
                        {
                            o.StatusId = x.StatusId;
                            o.LastUpdateDate = x.CreateDate;
                        }

                        // Get transaction details if the square payment (or refund) has moved beyond created status
                        if (o.ReferenceNumber != null && o.StatusId > (int)InvoicePaymentStatus.Created)
                        {
                            var tdm = new TransactionDetailsModel();
                           
                            var sp = Extric.Towbook.SquareIntegration.SquarePayment.GetByInvoicePaymentId(o.Id, e.CompanyId);
                            if (sp != null)
                            {
                                decimal amount = 0;
                                if (sp.TotalMoney != null)
                                    amount = Convert.ToDecimal(sp.TotalMoney) / Convert.ToDecimal(100); // square amounts is in cents
                                else if (sp.Amount != null)
                                    amount = Convert.ToDecimal(sp.Amount) / Convert.ToDecimal(100); // square amounts is in cents

                                decimal tipAmount = Convert.ToDecimal(sp.Tip ?? 0) / Convert.ToDecimal(100); // square tip is in cents

                                tdm.PaymentId = sp.PaymentId;
                                tdm.Currency = sp.Currency;
                                tdm.TotalAmount = amount;
                                tdm.TipAmount = tipAmount;
                                tdm.ReceiptUrl = sp.ReceiptUrl;
                                tdm.PaymentCardBrand = sp.PaymentCardBrand;
                                tdm.PaymentCardType = sp.PaymentCardType;
                                tdm.PaymentCardLast4 = sp.PaymentCardLast4;
                                tdm.PaymentStatus = sp.PaymentStatus;
                                tdm.PaymentStatementDescription = sp.PaymentStatementDescription;
                            }

                            var sr = SquareRefund.GetByInvoicePaymentId(o.Id);
                            if (sr != null)
                            {

                                tdm.RefundId = sr.RefundId;
                                tdm.RefundStatus = sr.Status;
                                tdm.RefundTotalAmount = sr.Amount > 0 ? (decimal)(sr.Amount / 100) : 0;
                                tdm.RefundFeeAmount = sr.ProcessingFeeAmount > 0 ? (decimal)(sr.ProcessingFeeAmount / 100) : 0;
                                tdm.RefundFeeCurrency = sr.ProcessingFeeCurrency;
                                tdm.RefundCreateDate = sr.RefundCreatedAt;
                                tdm.RefundUpdateDate = sr.RefundUpdatedAt;
                            }

                            o.TransactionDetails = tdm;
                        }

                        return o;
                    });   
                }
                else
                {
                    return payments;
                }
            }
            else
            {
                return Array.Empty<PaymentModel>();
            }
        }

        /// <summary>
        /// Get specified payment. 
        /// /api/calls/{callId}>/payments/{paymentId}
        /// </summary>
        /// <param name="callId"></param>
        /// <param name="paymentId"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("{paymentId}")]
        public async Task<dynamic> GetAsync(int callId, int paymentId)
        {
            var e = await Entry.GetByIdAsync(callId);

            if (e == null)
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound));

            if (!(await WebGlobal.CurrentUser.HasAccessToDispatchEntryAsync(e)))
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotImplemented) { Content = new StringContent("You don't have access to the specified call/invoice.") });

            var o = await InvoicePayment.GetByIdAsync(paymentId);

            if (o == null || e.Invoice.Id != o.InvoiceId)
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound));
            
            var model = PaymentModel.Map(o, WebGlobal.CurrentUser);

            if (o.PaymentType == PaymentType.Square.Id)
            {
                var transaction = new TransactionDetailsModel();
                var sp = SquarePayment.GetByInvoicePaymentId(paymentId, e.CompanyId);
               
                if (sp != null)
                {
                    decimal amount = 0;
                    if (sp.TotalMoney != null)
                        amount = (decimal)(sp.TotalMoney / 100); // square amounts is in cents
                    else if (sp.Amount != null)
                        amount = (decimal)(sp.Amount / 100); // square amounts is in cents

                    decimal tipAmount = Convert.ToDecimal(sp.Tip ?? 0) / Convert.ToDecimal(100); // square tip is in cents
                    transaction.PaymentId = sp.PaymentId;
                    transaction.TotalAmount = amount;
                    transaction.TipAmount = tipAmount;
                    transaction.Currency = sp.Currency;
                    transaction.ReceiptUrl = sp.ReceiptUrl;
                    transaction.PaymentStatus = sp.Status;
                    transaction.PaymentCardBrand = sp.PaymentCardBrand;
                    transaction.PaymentCardType = sp.PaymentCardType;
                    transaction.PaymentCardLast4 = sp.PaymentCardLast4;
                    transaction.PaymentCreateDate = sp.PaymentCreatedAt;
                    transaction.PaymentUpdateDate = sp.PaymentUpdatedAt;
                    transaction.PaymentStatementDescription = sp.PaymentStatementDescription;
                }
                
                var sr = SquareRefund.GetByInvoicePaymentId(paymentId);
                if (sr != null)
                {
                    transaction.RefundId = sr.RefundId;
                    transaction.RefundStatus = sr.Status;
                    transaction.RefundReason = sr.Reason;
                    transaction.RefundTotalAmount = sr.Amount > 0 ? (decimal)(sr.Amount / 100) : 0;
                    transaction.RefundFeeAmount = sr.ProcessingFeeAmount > 0 ? (decimal)(sr.ProcessingFeeAmount / 100) : 0;
                    transaction.RefundFeeCurrency = sr.ProcessingFeeCurrency;
                    transaction.RefundCreateDate = sr.RefundCreatedAt;
                    transaction.RefundUpdateDate = sr.RefundUpdatedAt;
                }
                
                model.TransactionDetails = transaction;
            }

            return model;
        }

        public class TransactionDetails
        {
            public string IdempotencyKey { get; set; }
            public string PaymentId { get; set; }
        }

        public class PaymentBody
        {
            public int CallId { get; set; }
            public decimal Amount { get; set; }
            public decimal? TipAmount { get; set; }
            public decimal? TotalAmount { get; set; }

            public int Type { get; set; }
            public string ReferenceNumber { get; set; }
            public DateTime PaymentDate { get; set; }
            public int? ClassId { get; set; }
            public int? StatusId { get; set; }
            public TransactionDetails TransactionDetails { get; set; }
        }

        /// <summary>
        /// Handle Form Post
        /// </summary>
        /// <param name="callId"></param>
        /// <param name="msg"></param>
        /// <param name="lockCall"></param>
        /// <param name="markAudit"></param>
        /// <returns></returns>
        [Obsolete]
        [HttpPost]
        [Route("")]
        [Consumes("application/x-www-form-urlencoded")]
        public async Task<object> Post(int callId, [FromForm] PaymentBody msg, [FromForm] bool lockCall = false, [FromQuery] bool markAudit = false)
        {
            return await PostJsonBody(callId, msg, lockCall, markAudit);
        }

        /// <summary>
        /// Creates a payment and applies it against the specified call. Use for recording payments.
        /// </summary>
        /// <param name="callId"></param>
        /// <param name="msg">Amount, Type, ReferenceNumber, PaymentDate</param>
        /// <param name="lockCall">Optionally, lock the call after the payment is recorded</param>
        /// <param name="markAudit">Optionally, mare the call as audited after the payment is recorded</param>
        /// <returns></returns>
        [HttpPost]
        [Route("")]
        public async Task<IActionResult> PostJsonBody(int callId,
            [FromBody] PaymentBody msg,
            [FromQuery] bool lockCall = false,
            [FromQuery] bool markAudit = false)
        {
            var e = await Entry.GetByIdAsync(callId);

            if (e == null)
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound));

            if (!(await WebGlobal.CurrentUser.HasAccessToDispatchEntryAsync(e)))
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotImplemented) { Content = new StringContent("You don't have access to the specified call/invoice.") });

            if (msg.CallId != callId)
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.InternalServerError) { Content = new StringContent("CallId specified doesn't match request.") });

            var pt = (PaymentType)msg.Type;

            if (pt?.CompanyId > 0)
            {
                // Payment types are shared from the parent to all child companies. Check for multicompany scenario.
                // The user may not have access to the parent through the user sharing setting but the user still has permission
                // if the payment type is from the parent/primary company.

                var companyShared = await SharedCompany.GetByCompanyIdAsync(WebGlobal.CurrentUser.CompanyId);
                var primaryCompanyId = companyShared?.FirstOrDefault()?.CompanyId ?? WebGlobal.CurrentUser.CompanyId;

                if (!(pt.CompanyId == primaryCompanyId || WebGlobal.CurrentUser.HasAccessToCompany(pt.CompanyId)))
                {
                    throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.Forbidden)
                    {
                        Content = new StringContent("You don't have access to the specified payment type.")
                    });
                }
            }

            msg.Amount = Math.Round(msg.Amount, 2);

            if (msg.Amount > e.BalanceDue && !e.Impound)
            {
                var message = $"The payment submitted ({msg.Amount:C}) is greater than the amount due ({e.BalanceDue:C}) " +
                                " on the invoice. Submitting the payment would cause a negative balance. No changes saved.";

                logger.LogEvent(message, e.CompanyId, LogLevel.Error, new Dictionary<string, object>() { { "data", msg.ToJson() } });
                return BadRequest(message);
            }

            if (Core.GetRedisValue("maintenance_block_call_creation") == "1")
            {
                await Task.Delay(5000);
                var logEvent = new LogEventInfo();

                logEvent.LoggerName = logger.Name;

                logEvent.Message = "Call Payment Blocked - redis key maintenance_block_call_creation is set to 1.";
                logEvent.Level = LogLevel.Warn;
                logEvent.TimeStamp = DateTime.Now;
                logEvent.Properties["companyId"] = e.CompanyId;
                logEvent.Properties["username"] = WebGlobal.CurrentUser.Username;
                logEvent.Properties["type"] = "maintenance_Block";
                try
                {
                    logEvent.Properties["callId"] = callId;
                }
                catch
                {

                }

                logger.Log(logEvent);

                throw new Web.HttpResponseException(new HttpResponseMessage(Forbidden())
                {
                    Content = new StringContent(
                        "An error occurred while uploading the photo. Please try again in a few minutes.")
                });
            }

            #region Validate Payment Date against Closed Accounting Period
            bool overrideClosedPeriod = false;

            var tempToken = (HttpContext?.Request?.Headers?.TryGetValue("X-Closed-Call-Temp-Token", out var tokenValue) ?? false) ? tokenValue.FirstOrDefault() : null;
            var allowSkip = (HttpContext?.Request?.Headers?.TryGetValue("X-Closed-Call", out var skipValue) ?? false) && skipValue.FirstOrDefault() == "1";

            if (await e.Company.HasFeatureAsync(Generated.Features.AdvancedBilling_ClosedAccountingPeriod))
            {
                await ClosedPeriodCallExtension.ValidatePaymentDateAgainstClosedAccountingPeriod(e,
                    ClosedPeriodOption.GetByCompanyId(e.CompanyId),
                    msg.PaymentDate,
                    tempToken,
                    allowSkip,
                    Web.HttpContext.Current.IsMobileAppRequest());
                
                overrideClosedPeriod = true;
            }
            #endregion
            
            
            var prevent = false;

            #region check if we need to block drivers from recording payments
            if (WebGlobal.CurrentUser.Type == Towbook.User.TypeEnum.Driver)
            {
                var preventDriverFromPerforming = (await CompanyKeyValue.GetByCompanyIdAsync(e.CompanyId, Provider.Towbook.ProviderId, "PreventDriversFromRecordingPayments")).FirstOrDefault();

                if (preventDriverFromPerforming?.Value == "1")
                {
                    prevent = true;
                }
                else
                {
                    var k = CompanyKeyValue.GetFirstValueOrNull(e.CompanyId,
                        Provider.Towbook.ProviderId, "MapDriverPaymentsToBillToAccountIdJson");

                    if (!String.IsNullOrWhiteSpace(k))
                    {
                        var mappings = Newtonsoft.Json.JsonConvert.DeserializeObject<dynamic[]>(k);

                        var link = mappings?.Where(o => o.paymentTypeId == msg.Type).FirstOrDefault();
                        if (link != null)
                        {
                            e.Invoice.AccountId = link.accountId;
                            await e.Invoice.SaveAsync(this.GetCurrentToken(), this.GetRequestingIp(), overrideClosedPeriod);
                            prevent = true;
                        }
                    }
                }
            }

            if (WebGlobal.CurrentUser.Type == Towbook.User.TypeEnum.Dispatcher)
            {
                var preventDispatchersFromPerforming = (await CompanyKeyValue.GetByCompanyIdAsync(e.CompanyId, Provider.Towbook.ProviderId, "PreventDispatchersFromRecordingPayments")).FirstOrDefault();

                if (preventDispatchersFromPerforming?.Value == "1")
                {
                    prevent = true;
                }
            }

            if (WebGlobal.CurrentUser.Type == Towbook.User.TypeEnum.AccountManager ||
                WebGlobal.CurrentUser.Type == Towbook.User.TypeEnum.AccountUser)
                prevent = true;

            if (prevent)
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.Forbidden)
                {
                    Content = new StringContent("Your user account doesn't have permission to record payments.")
                });

            #endregion

            if (msg.PaymentDate == DateTime.MinValue)
                msg.PaymentDate = DateTime.Now;

            var ip = new InvoicePayment();

            ip.InvoiceId = e.Invoice.Id;
            ip.OwnerUserId = WebGlobal.CurrentUser.Id;
            ip.ReferenceNumber = msg.ReferenceNumber;
            ip.PaymentType = pt;
            ip.Amount = msg.Amount;
            ip.PaymentDate = msg.PaymentDate;

            var cs = e.Invoice.ClassBalances;
            var cashClassBalance = cs.Where(o => o.Id == 1).Sum(o => o.Balance);

            if (msg.ClassId.GetValueOrDefault() == 0 && ip.ClassId == 0)
            {
                // auto assign class if its not specified
                var cb = e.Invoice.ClassBalances;
                var foundClass = e.Invoice.ClassBalances.FirstOrDefault(o => o.Balance == ip.Amount);
                if (foundClass != null)
                {
                    ip.ClassId = foundClass.Id;
                }
                else if (cb.Where(o => o.Balance > 0).Count() == 1)
                {
                    ip.ClassId = cb.Where(o => o.Balance > 0).First().Id;
                }
            }

            if (ip.ClassId == 0)
            {
                if (await (new Dispatch.CallModels.CallModel() { CompanyId = WebGlobal.CurrentUser.CompanyId }).ShouldBlockChargesAsync() ||
                    cashClassBalance == msg.Amount)
                {
                    ip.ClassId = ChargeClass.Cash;
                }
                else
                {
                    if (msg.ClassId != null)
                        ip.ClassId = msg.ClassId.Value;
                }
                if (ip.ClassId == 0)
                {
                    if (ip.PaymentType?.Id == PaymentType.Cash.Id)
                        ip.ClassId = ChargeClass.Cash;
                }
            }

            #region Square payment type handling
            SquareCompanyAuthorization squareAuthorization = null;
            LogEventInfo squarePaymentLogEvent = null;
            object squareAuthorizationLog = null;
            SquareOrder squareOrder = null;
            if (ip.PaymentType == PaymentType.Square)
            {
                squarePaymentLogEvent = new LogEventInfo();

                if (!string.IsNullOrEmpty(ip.ReferenceId) && await WebGlobal.CurrentUser.Company.HasFeatureAsync(Generated.Features.PaymentIntegrations_Square))
                {
                    squareAuthorization = await SquareUtils.GetAuthorizationAsync(e.CompanyId);
                    if (squareAuthorization != null)
                    {
                        squareAuthorizationLog = new 
                        {
                            squareAuthorization.MerchantId,
                            squareAuthorization.TokenType,
                            squareAuthorization.ExpiresAt,
                            squareAuthorization.CreateDate,
                            squareAuthorization.Location?.LocationId,
                            squareAuthorization.Location?.CompanyId,
                            squareAuthorization.Renewed,
                            squareAuthorization.Valid
                        };

                        if (!string.IsNullOrEmpty(msg.TransactionDetails?.IdempotencyKey))
                        {
                            squareOrder = await SquareOrder.GetByIdempotencyKey(msg.TransactionDetails.IdempotencyKey, e.CompanyId);
                            
                            if (squareOrder is { DispatchEntryPaymentId: > 0 })
                            {
                                // Use {msg.TransactionDetails.IdempotencyKey} to retrieve the SquareOrder record and check if an InvoicePayment
                                // has already been associated with it to avoid creating multiple DispatchEntryPayment records for a single Square payment.
                                // Then return the existing InvoicePayment instead of creating a new one, which would result in an incorrect duplicate. 
                                var invoicePayment = await InvoicePayment.GetByIdAsync(squareOrder.DispatchEntryPaymentId.Value);
                                if (invoicePayment is not null)
                                {
                                    return Ok(PaymentModel.Map(invoicePayment, WebGlobal.CurrentUser));
                                }
                            }
                        }
                        
                        // [Legacy] Try to resolve the Square PaymentID from a deprecated TransactionID that usually is provided by the deprecated Square Reader SDK
                        var paymentId = await SquareUtils.ResolveSquarePaymentIdFromReferenceNumber(e.CompanyId, ip.ReferenceId);
                        if (paymentId != null && paymentId != ip.ReferenceNumber)
                        {
                            // replace the referenceNumber with the resolved paymentId. 
                            ip.ReferenceNumber = paymentId;
                        }
                    }
                }

                #region Payment:Create Log
                squarePaymentLogEvent = new LogEventInfo();
                squarePaymentLogEvent.LoggerName = logger.Name;
                squarePaymentLogEvent.Message = "Square Payment Create Log";
                squarePaymentLogEvent.Level = LogLevel.Warn;
                squarePaymentLogEvent.TimeStamp = DateTime.Now;
                squarePaymentLogEvent.Properties["companyId"] = e.CompanyId;
                squarePaymentLogEvent.Properties["username"] = WebGlobal.CurrentUser.Username;
                squarePaymentLogEvent.Properties["userId"] = WebGlobal.CurrentUser.Id;
                squarePaymentLogEvent.Properties["userType"] = WebGlobal.CurrentUser.Type.ToString();
                squarePaymentLogEvent.Properties["type"] = "Create Invoice Payment";
                squarePaymentLogEvent.Properties["event_type"] = msg.Amount <= 0 ? "Amount is invalid" : $"Amount is valid";
                squarePaymentLogEvent.Properties["callId"] = e.Id;
                squarePaymentLogEvent.Properties["invoiceId"] = e.Invoice.Id;
                squarePaymentLogEvent.Properties["data"] = msg.ToJson();
                squarePaymentLogEvent.Properties["commitId"] = Core.GetCommitId();
                #endregion
            }
            #endregion


            bool isClosedPeriodCallActivity = false;

            if ((allowSkip || !string.IsNullOrEmpty(tempToken)) &&
                await e.Company.HasFeatureAsync(Generated.Features.AdvancedBilling_ClosedAccountingPeriod) &&
                !ClosedPeriodExtensions.IsDateOutsideOfClosedAccountingPeriod(await ClosedPeriodOption.GetByCompanyIdAsync(e.CompanyId), ip.PaymentDate.Value))
            {
                isClosedPeriodCallActivity = true;
                ip.ClosedPeriodActivitySave = true;
            }

            // Lock or audit the call before saving the payment object.  Necessary for the
            // call insights to be updated in cosmos before the pusher call_update event is braodcast
            // after the payment is saved.
            try
            {
                if (lockCall)
                    await new CallsController().Lock(e.Id);

                if (markAudit)
                    await new CallsController().Audit(e.Id);
            }
            catch
            {
                // silently ignore lock/audit failures; should this be logged somewhere?
            }

            await ip.Save(WebGlobal.CurrentUser, this.GetRequestingIp(), null, overrideClosedPeriod);

            #region Closed Call log
            if (isClosedPeriodCallActivity)
            {
                var userTempToken = EntryTempToken.GetByToken(tempToken);

                var overrideLog = new ClosedPeriodCallActivityLog()
                {
                    ActionType = ActionType.CreatePayment,
                    DispatchEntryId = e.Id,
                    CompanyId = e.CompanyId,
                    PaymentId = ip.Id,
                    TokenId = userTempToken?.Id
                };

                await overrideLog.SaveAsync(WebGlobal.CurrentUser, this.GetRequestingIp());
            }
            #endregion

            #region Additional Square procedures
            if (ip.PaymentType == PaymentType.Square && 
                await WebGlobal.CurrentUser.Company.HasFeatureAsync(Generated.Features.PaymentIntegrations_Square) && 
                squareAuthorization != null)
            {
                long tipAmountInCents = (long)(msg.TipAmount.GetValueOrDefault() * 100);
                long totalAmountInCents = (long)(msg.TotalAmount.GetValueOrDefault() * 100);

                // verify payload data, or correct total amount.
                if(tipAmountInCents > 0 && msg.TotalAmount != (msg.TipAmount + msg.Amount))
                    totalAmountInCents = ((long)(ip.Amount * 100)) + tipAmountInCents;

                await SquareUtils.RegisterSquarePayment(ip, e.CompanyId, e.Company.CurrencyCode, totalAmountInCents, tipAmountInCents);

                // apply tips
                if (tipAmountInCents > 0)
                {
                    await SquareUtils.ApplySquarePaymentTips(ip, e, tipAmountInCents);

                    await Entry.UpdateInAzure(Entry.GetByIdNoCache(e.Id), false);
                }

                // Update the {SquareOrder} record with the just created {InvoicePayment} record.
                if (squareOrder != null)
                {
                    squareOrder.DispatchEntryPaymentId = ip.Id;
                    squareOrder.Status = "IP_CREATED";
                    await squareOrder.Save();
                }
            }

            if (msg.StatusId.GetValueOrDefault() > 0)
            {
                await InvoicePaymentState.RegisterStatusUpdate(ip.Id, InvoicePaymentStatus.Processing);
            }

            if (squarePaymentLogEvent != null)
            {
                squarePaymentLogEvent.Properties["json"] = new
                {
                    this.GetCurrentToken()?.ClientVersionId,
                    Source = Web.HttpContext.Current.IsAndroidDevice() ? "Android" : Web.HttpContext.Current.IsAppleDevice() ? "iOS" : "Web",
                    InvoicePayment = ip,
                    DispatchEntryPaymentTips = DispatchEntryPaymentTip.GetByPaymentId(ip.Id),
                    tipAmountInCents = (long)msg.TipAmount.GetValueOrDefault() * 100,
                    SquareAuthorization = squareAuthorizationLog,
                    hasFeature = await WebGlobal.CurrentUser.Company.HasFeatureAsync(Generated.Features.PaymentIntegrations_Square)
            }.ToJson();

                logger.Log(squarePaymentLogEvent);
            }
            #endregion

            await ProcessQueueHelper.AddEntryToQueueAsync(e);

            return Ok(PaymentModel.Map(ip, WebGlobal.CurrentUser));
        }

        public class PaymentVoidBody
        {
            /// <summary>
            /// ID of the payment to void
            /// </summary>
            public int PaymentId { get; set; }

            /// <summary>
            /// Indicate why the void is being made
            /// </summary>
            public string Notes { get; set; }
        }


        /// <summary>
        /// Delete/Void the specified payment.
        /// </summary>
        /// <param name="callId"></param>
        /// <param name="paymentId">ID of the payment to void</param>
        /// <param name="msg"></param>
        /// <returns></returns>
        [ApiPermission(Towbook.User.TypeEnum.Manager, Towbook.User.TypeEnum.Accountant)]
        [HttpDelete]
        [Route("{paymentId}")]
        public async Task<HttpResponseMessage> Delete(int callId, int paymentId, PaymentVoidBody msg)
        {
            var e = await Entry.GetByIdAsync(callId);

            ValidateBeforeVoid(e);
            if (msg == null)
                ThrowIfRequestBodyMissing();

            if (paymentId != msg?.PaymentId)
                ThrowIfNotFound(null, "payment");

            var ip = await InvoicePayment.GetByIdAsync(msg.PaymentId);

            if (ip != null)
            {
                var tempToken = HttpContext.Request.Headers["X-Closed-Call-Temp-Token"].FirstOrDefault();
                var allowSkip = HttpContext.Request.Headers["X-Closed-Call"].FirstOrDefault() == "1";

                await ClosedPeriodCallExtension.ValidateClosedAccountingPeriodBeforeVoid(e, ip, tempToken, allowSkip, Web.HttpContext.Current.IsMobileAppRequest());

                if (ip.PaymentVerificationId != null)
                {
                    var pv = PaymentVerification.GetById(ip.PaymentVerificationId.Value);
                    if(pv != null)
                        pv.Delete(WebGlobal.CurrentUser);
                }

                if (ip.InvoiceId == e.Invoice.Id)
                {
                    var returnMessage = string.Empty;

                    bool isClosedPeriodCallActivity = false;
                    if ((allowSkip || !string.IsNullOrEmpty(tempToken)) &&
                        await e.Company.HasFeatureAsync(Generated.Features.AdvancedBilling_ClosedAccountingPeriod) &&
                        !ClosedPeriodExtensions.IsDateOutsideOfClosedAccountingPeriod(await ClosedPeriodOption.GetByCompanyIdAsync(e.CompanyId), ip.PaymentDate.Value))
                    {
                        isClosedPeriodCallActivity = true;
                        ip.ClosedPeriodActivitySave = true;
                    }


                    if (ip.PaymentType == PaymentType.Square &&
                        await WebGlobal.CurrentUser.Company.HasFeatureAsync(Generated.Features.PaymentIntegrations_Square) &&
                        await SquareUtils.GetAuthorizationAsync(e.CompanyId) != null)
                    {
                        var squarePayment = SquarePayment.GetByPaymentId(ip.ReferenceId, e.CompanyId);

                        if (squarePayment != null)
                        {
                            if (squarePayment.Status != SquarePaymentStatus.Completed)
                            {
                                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest)
                                {
                                    Content = new StringContent("Only captured payments can be refunded, payment is in status " + squarePayment.Status)
                                });
                            }

                            try
                            {
                                // refund request to Square api
                                await SquareUtils.RefundPayment(e.CompanyId, e.Id, ip.Id, WebGlobal.CurrentUser.Id);
                            }
                            catch (Exception ex)
                            {
                                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest)
                                {
                                    Content = new StringContent(ex.Message)
                                });
                            }
                        }
                        else
                        {
                            await ip.Void(WebGlobal.CurrentUser, this.GetRequestingIp());

                            returnMessage = "Payment was voided successfully. Please make sure to void the payment in Square as well.";
                        }
                    }
                    else
                    {
                        await ip.Void(WebGlobal.CurrentUser, this.GetRequestingIp());

                        if (ip.PaymentType == PaymentType.Square)
                        {
                            returnMessage = "Payment was voided successfully. Please make sure to void the payment in Square as well.";
                        }
                    }

                    #region Closed Call log
                    if (isClosedPeriodCallActivity)
                    {
                        var userTempToken = EntryTempToken.GetByToken(tempToken);

                        var overrideLog = new ClosedPeriodCallActivityLog()
                        {
                            ActionType = ActionType.VoidPayment,
                            DispatchEntryId = e.Id,
                            CompanyId = e.CompanyId,
                            PaymentId = ip.Id,
                            TokenId = userTempToken?.Id
                        };

                        await overrideLog.SaveAsync(WebGlobal.CurrentUser, this.GetRequestingIp());
                    }
                    #endregion

                    return new HttpResponseMessage(HttpStatusCode.Accepted)
                    {
                        Content = new StringContent(returnMessage)
                    };
                }
                else
                {
                    throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.InternalServerError));
                }
            }

            await PushNotificationProvider.UpdateCall(e.CompanyId, callId);

            return new HttpResponseMessage(HttpStatusCode.OK);
        }

        [Obsolete("Use Delete instead.")]
        [HttpPost]
        [Route("{paymentId}/refund")] 
        public async Task<object> Refund(int callId, int paymentId) => await Delete(callId, paymentId, new PaymentVoidBody() { PaymentId = paymentId });

        private void ValidateBeforeVoid(Entry e)
        {
            if (e == null)
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound));

            if (!WebGlobal.CurrentUser.HasAccessToCompany(e.CompanyId))
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.Forbidden) { Content = new StringContent("You don't have access to the specified call/invoice.") });

            var prevent = CompanyKeyValue.GetFirstValueOrNull(
                WebGlobal.CurrentUser.CompanyId,
                Provider.Towbook.ProviderId,
                "PreventPaymentVoiding");

            var allow = UserKeyValue.GetByUser(WebGlobal.CurrentUser.CompanyId, WebGlobal.CurrentUser.Id, Provider.Towbook.ProviderId, "Permission_Payments_Void")
                .FirstOrDefault();

            if (allow?.Value == "1")
                return;
            else if (allow?.Value == "2")
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.Forbidden) { Content = new StringContent("Your account doesn't have access to void payments.") });


            if (prevent != null)
            {
                switch (prevent)
                {
                    // 1=prevent drivers
                    case "1":
                        if (WebGlobal.CurrentUser.Type == Towbook.User.TypeEnum.Driver)
                        {
                            throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.Forbidden) { Content = new StringContent("Your account doesn't have access to void payments.") });
                        }

                        break;

                    // 2=drivers and dispatchers
                    case "2":
                        if (WebGlobal.CurrentUser.Type == Towbook.User.TypeEnum.Driver ||
                            WebGlobal.CurrentUser.Type == Towbook.User.TypeEnum.Dispatcher)
                        {
                            throw new Web.HttpResponseException(new HttpResponseMessage(Forbidden()) { Content = new StringContent("Your account doesn't have access to void payments") });
                        }
                        break;

                    // 3=drivers,dispatchers,accountants
                    case "3":
                        if (WebGlobal.CurrentUser.Type == Towbook.User.TypeEnum.Driver ||
                            WebGlobal.CurrentUser.Type == Towbook.User.TypeEnum.Dispatcher ||
                            WebGlobal.CurrentUser.Type == Towbook.User.TypeEnum.Accountant)
                        {
                            throw new Web.HttpResponseException(new HttpResponseMessage(Forbidden()) { Content = new StringContent("Your account doesn't have access to void payments") });
                        }
                        break;
                }
            }
        }
    }
}
