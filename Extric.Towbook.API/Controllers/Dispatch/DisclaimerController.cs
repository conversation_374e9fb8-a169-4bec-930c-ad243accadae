using Microsoft.AspNetCore.Mvc;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Text.RegularExpressions;
using Extric.Towbook.Dispatch;
using Extric.Towbook.WebShared;

using static Extric.Towbook.API.ApiUtility;
using System.Threading.Tasks;

namespace Extric.Towbook.API.Controllers
{
    [Route("calls")]
    public class DisclaimerController : ControllerBase
    {
        [HttpGet("{callId}/disclaimer")]
        public async Task<HttpResponseMessage> GetAsync(int callId, [FromQuery] int darkmode = 0)
        {
            var e = await Entry.GetByIdAsync(callId);

            await ThrowIfNoCompanyAccessAsync(e?.CompanyId, "call");

            var disclaimer = Company.InvoiceDisclaimer.GetByCompanyId(e.CompanyId, 
                e.ReasonId, 
                true ? e.AccountId : (int?)null, 
                e.Impound)?.Disclaimer ?? string.Empty;

            var sb = new StringBuilder();

            sb.AppendLine("<style>*{font-family:<PERSON><PERSON><PERSON>, <PERSON><PERSON> } .disclaimer-root { color: " + (darkmode == 0 ? "#777" : "#fff") + "; font-weight: bold; display:inline-block; min-width:100px } </style>");
            sb.AppendLine("<div style='margin:0;padding:0' class='disclaimer-root'>");

            sb.AppendLine(Regex.Replace(disclaimer, @"\r\n?|\n", "<br/>"));
            sb.AppendLine("</div>");

            var response = new HttpResponseMessage()
            {
                Content = new StringContent(sb.ToString())
            };

            response.Content.Headers.ContentType = System.Net.Http.Headers.MediaTypeHeaderValue.Parse("text/html");

            return response;
        }

    }
}
