using System.Threading.Tasks;
using Extric.Towbook.API.Models.Calls;
using Extric.Towbook.Dispatch;
using Extric.Towbook.WebShared;
using Microsoft.AspNetCore.Mvc;

namespace Extric.Towbook.API.Controllers
{
    // calls/deleted/{callId}
    [Route("calls/deleted")]
    public class DeletedCallsController : ControllerBase
    {
        [HttpGet]
        public async Task<object> Get(
            [FromQuery] int page = 1, 
            [FromQuery] int size = 50)
        {
            return await CallModelExtensions.MapAsync(
                Entry.GetDeletedByCompanyId(WebGlobal.CurrentUser.CompanyId, page, size));
        }
    }
}
