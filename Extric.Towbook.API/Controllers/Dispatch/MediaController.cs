using System;
using System.IO;
using System.IO.Compression;
using System.Threading.Tasks;
using Extric.Towbook.Storage;
using Extric.Towbook.WebShared;
using System.Linq;
using System.Text;
using Extric.Towbook.Dispatch;
using static Extric.Towbook.API.ApiUtility;
using Microsoft.AspNetCore.Mvc;

namespace Extric.Towbook.API.Controllers
{
    [Route("calls")]
    public class MediaController : ControllerBase
    {
        [HttpGet("{callId}/media/download")]
        public async Task<FileContentResult> Download(int callId)
        {
            var entry = Dispatch.Entry.GetById(callId);
            int callNumber = entry.CallNumber;

            ThrowIfNoCompanyAccess(entry?.CompanyId, "media");

            if (WebGlobal.CurrentUser?.Type == Towbook.User.TypeEnum.PoliceDispatcher ||
                WebGlobal.CurrentUser?.Type == Towbook.User.TypeEnum.PoliceOfficer)
            {
                // don't allow police user types to see photos (requirement for CTS... I don't think this makes sense; 
                // photos are a great communication tool and letting officers snap photos or view them
                // seems like a good idea... we're probably going to make this NOT be a default for these user types
                // because other departments are going to want to have photos visible for all user types. 
                return null;
            }

            var photos = (await Dispatch.Photo.GetByDispatchEntryIdAsync(callId)).Where(p => p.FileSize > 0);
            var sticker = await Towbook.Stickering.Sticker.GetByDispatchEntryIdAsync(callId);
            var stickeringPhotos = sticker != null
                ? Towbook.Stickering.Photo.GetByStickerId(sticker.Id)
                : Enumerable.Empty<Towbook.Stickering.Photo>();
            var videos = await Extric.Towbook.Dispatch.Video.GetByDispatchEntryIdAsync(callId);

            // each row in the description is roughly 100 chars
            int mediaCount = photos.Count() + stickeringPhotos.Count() + videos.Count();
            var sb = new StringBuilder(CSVHeader, mediaCount * 110);
            int idx = 1;
            using (var ms = new MemoryStream())
            {
                using (var archive = new ZipArchive(ms, ZipArchiveMode.Create, true))
                {
                    foreach (var photo in photos)
                    {
                        string photoName = $"call_{callNumber}_image_{idx++}{GetPhotoExtension(photo)}";
                        await CsvDescriptionAsync(sb, entry, photo, photoName);
                        var zipEntry = archive.CreateEntry(photoName, CompressionLevel.Fastest);
                        using (var zipStream = zipEntry.Open())
                        {
                            string photoFile = await FileUtility.GetFileAsync(photo.Location.Replace("%1", entry.CompanyId.ToString()));
                            var bytes = System.IO.File.ReadAllBytes(photoFile);
                            await zipStream.WriteAsync(bytes, 0, bytes.Length);
                        }
                    }

                    foreach (var photo in stickeringPhotos)
                    {
                        string photoName = $"call_{callNumber}_stickering_{idx++}{GetPhotoExtension(photo)}";
                        sb.AppendFormat("{0}, 0.000000, 0.000000, {1}{2}", photoName, WebGlobal.OffsetDateTime(photo.CreateDate, entry.Company).ToShortTowbookTimeString(), Environment.NewLine);
                        var zipEntry = archive.CreateEntry(photoName, CompressionLevel.Fastest);
                        using (var zipStream = zipEntry.Open())
                        {
                            string photoFile = await FileUtility.GetFileAsync(photo.Location.Replace("%1", entry.CompanyId.ToString()));
                            var bytes = System.IO.File.ReadAllBytes(photoFile);
                            await zipStream.WriteAsync(bytes, 0, bytes.Length);
                        }
                    }

                    foreach (var video in videos)
                    {
                        string videoName = $"call_{callNumber}_video_{idx++}{GetVideoExtension(video)}";
                        CsvDescription(sb, entry, video, videoName);
                        var zipEntry = archive.CreateEntry(videoName, CompressionLevel.Fastest);
                        using (var zipStream = zipEntry.Open())
                        {
                            string videoFile = await FileUtility.GetFileAsync(video.Location.Replace("%1", entry.CompanyId.ToString()));
                            var bytes = System.IO.File.ReadAllBytes(videoFile);
                            await zipStream.WriteAsync(bytes, 0, bytes.Length);
                        }
                    }

                    // Add text file with photo info to download
                    var infoTextEntry = archive.CreateEntry($"call_{callNumber}_media_info.txt", CompressionLevel.Fastest);
                    using (var entryStream = infoTextEntry.Open())
                    using (var writer = new StreamWriter(entryStream))
                    {
                        await writer.WriteAsync(sb.ToString());
                    }
                }

                ms.Position = 0;

                return File(ms.ToArray(), "application/zip", $"call_{entry.Id}_files.zip");
            }
        }

        private static readonly string CSVHeader = $"filename, latitude, longitude, timestamp, status, callNumber, PO Number, description, ip address{Environment.NewLine}";

        private static async Task CsvDescriptionAsync(StringBuilder sb, Entry entry, Photo photo, string photoName)
        {
            string status = "";
            if (photo.DispatchEntryStatusId.HasValue)
            {
                status = (await Status.GetByIdAsync(photo.DispatchEntryStatusId.Value))?.Name;
            }
            sb.AppendFormat("{0}, {1}, {2}, {3}, {4}, {5}, {6}, {7}, {8}{9}",
                    photoName, photo.Latitude, photo.Longitude, WebGlobal.OffsetDateTime(photo.CreateDate, entry.Company).ToShortTowbookTimeString(), status, entry.CallNumber, entry.PurchaseOrderNumber, photo.Description, photo.RemoteIp, Environment.NewLine);
        }

        private static void CsvDescription(StringBuilder sb, Entry entry, Video video, string videoName) =>
            sb.AppendFormat("{0}, {1}, {2}, {3}, {4}, , {5}, {6}, {7}{8}", 
                videoName, video.Latitude, video.Longitude, WebGlobal.OffsetDateTime(video.CreateDate, entry.Company).ToShortTowbookTimeString(), entry.CallNumber, entry.PurchaseOrderNumber, video.Description, video.RemoteIp, Environment.NewLine);

        private static void CsvDescription(StringBuilder sb, Entry entry, Towbook.Stickering.Photo photo, string photoName) =>
            sb.AppendFormat("{0}, , , {1}, , {2}, {3}, {4}, {5}{6}", 
                photoName, WebGlobal.OffsetDateTime(photo.CreateDate, entry.Company).ToShortTowbookTimeString(), entry.CallNumber,entry.PurchaseOrderNumber, photo.Description, photo.RemoteIp, Environment.NewLine);


        private static string GetPhotoExtension(Towbook.Stickering.Photo photo) => GetContentTypeExtension(photo.ContentType);
        private static string GetPhotoExtension(Photo photo) => GetContentTypeExtension(photo.ContentType);
        private static string GetVideoExtension(Video video) => GetContentTypeExtension(video.ContentType);

        private static string GetContentTypeExtension(string contentType)
        {
            switch (contentType)
            {
                case "image/jpg":
                case "image/jpeg": return ".jpg";
                case "image/png": return ".png"; ;
                case "image/bmp": return ".bmp"; ;
                case "image/gif": return ".gif"; ;
                case "image/tiff": return ".tif";;
                case "video/mp4": return ".mp4";
                case "video/avi": return ".avi";
                case "video/mov": return ".mov";
                case "video/wmv": return ".wmv";
                case "video/flv": return ".flv";
                case "video/3gp": return ".3gp";
                case "video/mkv": return ".mkv";
                case "video/webm": return ".webm";
            }

            return "";
        }
    }
}
