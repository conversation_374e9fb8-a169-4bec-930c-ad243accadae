using System;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using Extric.Towbook.API.Models;
using Extric.Towbook.Dispatch;
using Extric.Towbook.WebShared;
using Extric.Towbook.WebShared.Multipart;
using Microsoft.AspNetCore.Mvc;
using Plivo.XML;
using static Extric.Towbook.API.ApiUtility;


namespace Extric.Towbook.API.Controllers
{
    [Route("calls")]
    public class DamageFormDamagePhotosController : ControllerBase
    {
        [HttpGet("{callId}/damageForms/{damageFormId}/damages/{damageId}/photos")]
        public async Task<object> GetAllAsync(int callId, int damageFormId, int damageId)
        {
            var e = await Entry.GetByIdAsync(callId);
            await ThrowIfNoCompanyAccessAsync(e?.CompanyId, "call");

            #region Validation
            var df = EntryDamage.GetById(damageFormId);

            if (df == null || df.DispatchEntryId != callId)
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest)
                {
                    Content = new StringContent($"Damage Form with id <damageFormId> = {damageFormId} does not exist.")
                });
            }

            var d = EntryDamageRegion.GetById(damageId);

            if (d == null)
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest)
                {
                    Content = new StringContent($"Damage with id <damageId> = {damageId} does not exist.")
                });
            }

            // to validate if the requested damage is assigned to the requested damage form
            if (EntryDamageRel.GetByIds(damageFormId, damageId) == null)
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest)
                {
                    Content = new StringContent($"Damage with id <damageId> = {damageId} is not assigned to Damage Form with id <damageFormId> = {damageFormId}.")
                });
            }

            #endregion

            var photos = EntryDamagePhoto.GetPhotosByDamageRegionId(damageId);

            if (photos.Count == 0)
                return Array.Empty<object>();

            return photos.Select(o => DamagePhotoModel.Map(o));
        }

        [HttpGet("{callId}/damageForms/{damageFormId}/damages/{damageId}/photos/{photoId}")]
        public async Task<HttpResponseMessage> Get(int callId, int damageFormId, int damageId, int photoId)
        {
            var e = await Entry.GetByIdAsync(callId);
            await ThrowIfNoCompanyAccessAsync(e?.CompanyId, "call");

            #region Validations
            var df = EntryDamage.GetById(damageFormId);

            if (df == null || df.DispatchEntryId != callId)
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest)
                {
                    Content = new StringContent($"Damage Form with id <damageFormId> = {damageFormId} does not exist.")
                });
            }

            var d = EntryDamageRegion.GetById(damageId);

            if (d == null || d.DispatchEntryId != callId)
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest)
                {
                    Content = new StringContent($"Damage with id <damageId> = {damageId} does not exist.")
                });
            }

            // to validate if the requested damage is assigned to the requested damage form
            if (EntryDamageRel.GetByIds(damageFormId, damageId) == null)
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest)
                {
                    Content = new StringContent($"Damage with id <damageId> = {damageId} is not assigned to Damage Form with id <damageFormId> = {damageFormId}.")
                });
            }

            var p = EntryDamagePhoto.GetById(photoId);

            if (p == null)
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest)
                {
                    Content = new StringContent($"Damage Photo with id <photoId> = {photoId} does not exist.")
                });
            }

            // to validate if the requested photo is assigned to the requested damage form
            if (EntryDamageRel.GetByIds(damageFormId, p.DispatchEntryDamageRegionId.Value) == null)
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest)
                {
                    Content = new StringContent($"Damage Photo with id <photoId> = {photoId} is not assigned to Damage Form with id <damageFormId> = {damageFormId}.")
                });
            }
            #endregion

            return await HttpImageResponse(p.Location, p.ContentType);
        }


        [HttpPost("{callId}/damageForms/{damageFormId}/damages/{damageId}/photos")]
        [DisableFormValueModelBinding]
        public async Task<ObjectResult> Post(
            int callId,
            int damageFormId,
            int damageId)
        {
            Entry e = await Entry.GetByIdAsync(callId);
            EntryDamage ed;
            EntryDamageRegion edr;

            #region Validations 
            await ThrowIfNoCompanyAccessAsync(e?.CompanyId, "call");

            //if (!Request.Content.IsMimeMultipartContent())
            if (!Web.HttpContext.Current.Request.HasFormContentType)
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.UnsupportedMediaType));
            }

            ed = EntryDamage.GetById(damageFormId);

            if (ed == null || ed.DispatchEntryId != callId)
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest)
                {
                    Content = new StringContent($"Damage Form with with id <damageFormId>={damageFormId} does not exist.")
                });
            }

            edr = EntryDamageRegion.GetById(damageId);

            if (edr == null)
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest)
                {
                    Content = new StringContent($"Damage with id <damageId> = {damageId} does not exist.")
                });
            }

            if (damageFormId != edr.DispatchEntryDamageId)
            {
                var msgErr = $"Damage region with (<damageRegionId>={damageId}) does not exist or is not assigned to this damage record (<damageId>={damageFormId})";

                if (ed.ParentDispatchEntryDamageId != null)
                    msgErr = $"Only can post images to damage regions that has property 'isOwnership = true'.";

                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest)
                {
                    Content = new StringContent(msgErr)
                });
            }



            #endregion

            #region Save to temp file
            FileInfo tempFileInfo;
            try
            {
                var postFiles = await GetFilesFromHttpPostRequest(Request);
                tempFileInfo = postFiles.First();
            }
            catch (Exception err)
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.InternalServerError)
                {
                    Content = new StringContent(err.Message)
                });
            }
            #endregion

            //// Copy temp file and rename with new file name and correct extension
            var damageRegion = EntryDamageRegion.GetById(damageId);

            try
            {
                var damagePhoto = new EntryDamagePhoto();
                damagePhoto.DispatchEntryDamageId = damageFormId;
                damagePhoto.DispatchEntryDamageRegionId = damageId;
                damagePhoto.DispatchEntryId = callId;
                damagePhoto.PhotoType = "PHOTO";
                damagePhoto.ContentType = "image/jpg";
                damagePhoto.OwnerUserId = WebGlobal.CurrentUser.Id;
                damagePhoto.Save();

                damagePhoto.AssetId = ed.AssetId;
                damagePhoto.RegionId = edr.RegionId;
                damagePhoto.TypeId = edr.TypeId;

                await CreateaPhotoFile(tempFileInfo.FullName, damagePhoto.Location);

                return StatusCode((int)HttpStatusCode.Created, DamagePhotoModel.Map(damagePhoto));
                //return Request.CreateResponse(HttpStatusCode.Created, DamagePhotoModel.Map(damagePhoto), PerRequestJsonSettingsFormatter.Instance);
            }
            finally
            {
                tempFileInfo.Delete();
            }
        }
    }
}
