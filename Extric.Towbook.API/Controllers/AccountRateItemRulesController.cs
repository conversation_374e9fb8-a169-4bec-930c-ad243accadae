using System.Collections.ObjectModel;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using Extric.Towbook.Accounts;
using Extric.Towbook.API.Models;
using Extric.Towbook.Utility;
using Extric.Towbook.WebShared;
using Microsoft.AspNetCore.Mvc;

using static Extric.Towbook.API.ApiUtility;

namespace Extric.Towbook.API.Controllers
{
    [Route("accountRateItemRules")]
    public class AccountRateItemRulesController : ControllerBase
    {
        [HttpGet]
        [Route("{id}")]
        public AccountRateItemRuleModel Get(int id)
        {
            var r = AccountRateItemRuleModel.Map(AccountRateItemRule.GetById(id));

            if (r == null)
            {
                throw new Extric.Towbook.Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound)
                {
                    Content = new StringContent("The specified rule either doesn't exist or you don't have access to it.")
                });
            }
            return r;
        }

        [HttpGet]
        [Route("")]
        public async Task<Collection<AccountRateItemRuleModel>> Get([FromQuery] int? accountId = null)
        {
            var r = (AccountRateItemRule.GetByCompanyId(
                        (await WebGlobal.GetCompaniesAsync())
                            .Select(o => o.Id)
                            .ToArray()))
                    .Select(o => new AccountRateItemRuleModel
                    {
                        Id = o.Id,
                        AccountId = o.AccountId,
                        ReasonId = o.ReasonId,
                        BodyTypeId = o.BodyTypeId,
                        DefaultQuantity = o.DefaultQuantity,
                        RateItemId = o.RateItemId,
                        ImpoundAddAfterThisManyDaysHeld = o.ImpoundAddAfterThisManyDaysHeld
                    }).ToCollection();

            if (accountId != null)
            {
                return r.Where(o => o.AccountId == accountId.Value).ToCollection();
            }

            return r;
        }

        [HttpPost]
        [Route("{id?}")]
        [ApiPermission(Towbook.User.TypeEnum.Manager, Towbook.User.TypeEnum.Accountant)]
        public async Task<AccountRateItemRuleModel> Post([FromBody] AccountRateItemRuleModel rule)
        {
            if (rule.AccountId == 0)
                throw new TowbookException("You must specify an AccountId when saving a pricing rule.");

            var arir = AccountRateItemRuleModel.Map(rule);

            if (arir.AccountId != null)
            {
                var a = await Account.GetByIdAsync(arir.AccountId.Value);
                await ThrowIfNoCompanyAccessAsync(a.Companies, "account");
            }

            arir.OwnerUserId = WebGlobal.CurrentUser.Id;
            await arir.Save();

            return AccountRateItemRuleModel.Map(arir);
        }

        [HttpDelete]
        [Route("{id}")]
        [ApiPermission(Towbook.User.TypeEnum.Manager, Towbook.User.TypeEnum.Accountant)]
        public async Task<HttpResponseMessage> Delete(int id)
        {
            var arir = AccountRateItemRule.GetById(id);

            if (arir != null)
            {
                if (arir.AccountId != null)
                {
                    var a = await Account.GetByIdAsync(arir.AccountId.Value);
                    await ThrowIfNoCompanyAccessAsync(a.Companies, "account");
                }
            }

            if (arir == null)
                return new HttpResponseMessage(HttpStatusCode.NoContent);

            await arir.Delete();

            return new HttpResponseMessage(HttpStatusCode.OK);
        }

        [HttpPut]
        [Route("{id}")]
        [ApiPermission(Towbook.User.TypeEnum.Manager, Towbook.User.TypeEnum.Accountant)]
        public async Task<AccountRateItemRuleModel> Put(int id, [FromBody] AccountRateItemRuleModel rule)
        {
            var arir = AccountRateItemRule.GetById(id);

            if (arir == null)
                throw new TowbookException("Rule doesn't exist.");

            if (arir.AccountId != null)
            {
                var a = await Account.GetByIdAsync(arir.AccountId.Value);
                await ThrowIfNoCompanyAccessAsync(a.Companies, "account");
            }

            arir = AccountRateItemRuleModel.Map(rule, arir);
            await arir.Save();

            return AccountRateItemRuleModel.Map(arir);
        }
    }
}
