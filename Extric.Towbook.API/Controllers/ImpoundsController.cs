using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Collections.Specialized;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web;
using System.Net.Mail;
using System.Net.Mime;
using System.Threading.Tasks;
using Extric.Towbook.Accounts;
using Extric.Towbook.ActivityLogging;
using Extric.Towbook.API.Models;
using Extric.Towbook.API.Models.Calls;
using Extric.Towbook.API.Models.Impounds;
using Extric.Towbook.Dispatch;
using Extric.Towbook.Dispatch.CallModels;
using Extric.Towbook.Generated;
using Extric.Towbook.Impounds;
using Extric.Towbook.Integration;
using Extric.Towbook.Integrations.Email;
using Extric.Towbook.API.Integration.Square;
using Extric.Towbook.API.Integration.Square.Exceptions;
using Extric.Towbook.Tasks;
using Extric.Towbook.Utility;
using Extric.Towbook.Web;
using Extric.Towbook.WebShared;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System.IO;
using static Extric.Towbook.API.ApiUtility;
using Async = System.Threading.Tasks;
using Extric.Towbook.Auctions;
using Extric.Towbook.Integrations.AutoDataDirect;
using Extric.Towbook.AutoDataDirect;
using System.Globalization;
using Extric.Towbook.Storage;
using Extric.Towbook.AutoDataDirect.Exceptions;
using Extric.Towbook.Company;
using Extric.Towbook.API.Auctions.Controllers;
using Extric.Towbook.Integrations.AutoData;
using Extric.Towbook.SquareIntegration;
using System.Text;
using Extric.Towbook.Company.Accounting;
using HttpContextHelper = Extric.Towbook.Web.HttpContext;

namespace Extric.Towbook.API.Controllers
{
    [Route("impounds")]
    public class ImpoundsController : ControllerBase
    {
        private static readonly string _domain = Core.GetAppSetting("Towbook:WebAppUrl") ?? "https://app.towbook.com";
        /// <summary>
        /// Returns a list of vehicles currently held in storage/impound.
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("")]
        public async Task<object> Get(
            [FromQuery] bool showImpounded = true,
            [FromQuery] bool showReleased = false,
            [FromQuery] bool showAuctions = false,
            [FromQuery] int limit = 0,
            [FromQuery] int startAt = 0,
            [FromQuery] int page = 0,
            [FromQuery] int? accountId = null)
        {
            var companies = await WebGlobal.GetCompaniesAsync();

            if (WebGlobal.CurrentUser.CompanyId == 1525)
                companies = new Company.Company[] { WebGlobal.CurrentUser.Company };
            else
                companies = companies.Where(o => o.Id != 1525).ToArray();

            if ((WebGlobal.CurrentUser.Notes ?? "").Contains("BlockImpounds"))
                return Array.Empty<object>();

            // TODO: Can be made async
            if (!WebGlobal.CurrentUser.AccountUserHasPermissionToViewImpounds())
            {
                return Array.Empty<object>();
            }

            IEnumerable<Impound> impounds;

            if (companies.Any(o => o.Id == 49039 || o.Id == 272195))
            {
                impounds = await Impound.GetByCompanyAsync(companies.Select(o => o.Id).ToArray(), showImpounded, showReleased, showAuctions, startAt, limit, accountId);
            }
            else
            {
                Impound.ImpoundListReturnType type = Impound.ImpoundListReturnType.Current;
                if (showImpounded && showReleased)
                    type = Impound.ImpoundListReturnType.All;
                else if (showImpounded)
                    type = Impound.ImpoundListReturnType.Current;
                else if (showReleased)
                    type = Impound.ImpoundListReturnType.Released;
                else if (showAuctions)
                {
                    type = Impound.ImpoundListReturnType.Auction;

                    if (WebGlobal.CurrentUser.IsAccountTypeUser())
                        return Array.Empty<object>();

                }

                var ret = await Impound.GetByCompanyAsync(companies.Select(o => o.Id).ToArray(),
                    type, page, limit, accountId);



                if (type == Impound.ImpoundListReturnType.Current)
                {
                    var outOfSync = ret.List.Where(o => o.ReleaseDate != null || o.Auction == true || o.Deleted == true);
                    foreach (var xx in outOfSync)
                    {
                        // TODO: This is not performant, (n database/network calls on a loop)
                        await Entry.UpdateInAzure(xx.DispatchEntry, xx.Deleted);
                    }

                    // hack to get rid of released ones that shouldn't be in here. 
                    ret.List = ret.List.Where(o => o.ReleaseDate == null && !o.Auction && !o.Deleted).OrderByDescending(o => o.ImpoundDate).ToList();
                }

                else if (type == Impound.ImpoundListReturnType.Auction)
                {
                    var outOfSync = ret.List.Where(o => o.Auction == false);
                    foreach (var xx in outOfSync)
                    {
                        // TODO: This is not performant, (n database/network calls on a loop)
                        await Entry.UpdateInAzure(xx.DispatchEntry);
                    }

                    // hack to get rid of released ones that shouldn't be in here. 
                    ret.List = ret.List.Where(o => o.Auction == true && o.ReleaseDate == null).OrderByDescending(o => o.ImpoundDate).ToList();
                }
                impounds = ret.List;

                if (ret.TotalCount > 0)
                    Web.HttpContext.Current.Response.Headers["X-Records-Count"] = ret.TotalCount.ToString();
            }

            var batchIds = impounds.Select(z => z.DispatchEntry.Id).ToArray();
            var batchImpoundIds = impounds.Select(z => z.Id).ToArray();

            var payments = await InvoicePayment.GetByDispatchEntryIdsAsync(batchIds, null);
            var dispatchPhotos = await Dispatch.Photo.GetByDispatchEntryIdsAsync(batchIds);
            var impoundPhotos = await Towbook.Impounds.Photo.GetByImpoundIdAsync(batchImpoundIds);
            var notes = await Towbook.Impounds.Note.GetByImpoundIdsAsync(batchImpoundIds);
            var releaseDetails = await ReleaseDetails.GetByImpoundIdsAsync(impounds.Where(o => o.ReleaseDate != null).Select(o => o.Id));
            var impoundTasks = await ImpoundTask.GetByImpoundsAsync(batchImpoundIds);
            var vehicleTitles = await VehicleTitle.GetByDispatchEntryIdsAsync(batchIds);
            var auctionDetails = await EntryAuctionDetail.GetByDispatchEntryIdsAsync(batchIds);

            // TODO: Can be made async
            impounds = FilterListByCurrentUser(impounds);

            var companyTasks = await CompanyTask.GetByIdsAsync(impoundTasks.Where(w => w.DueDate.HasValue)
                .Select(s => s.TaskId).ToArray());

            var impoundTasksForList = BuildImpoundTasks(impoundTasks, companyTasks);

            return (
                await Task.WhenAll(
                    impounds.Select(async impound =>
                        await MapAsync(
                            impound,
                            payments,
                            impoundPhotos,
                            dispatchPhotos,
                            releaseDetails,
                            notes,
                            impoundTasksForList.Where(o => o.ImpoundId == impound.Id),
                            vehicleTitles,
                            auctionDetails)))
            ).ToList();
        }

        private static IEnumerable<Impound> FilterListByCurrentUser(IEnumerable<Impound> impounds)
        {
            // TODO: Can be made async
            if (!WebGlobal.CurrentUser.HasPermissionToViewImpounds())
            {
                return Array.Empty<Impound>();
            }
            if (WebGlobal.CurrentUser.Type == Towbook.User.TypeEnum.Driver &&
                // TODO: Can be made async
                !WebGlobal.CurrentUser.HasPermissionToViewAllImpounds())
            {
                // TODO: Can be made async
                var drivers = Driver.GetByUserId(WebGlobal.CurrentUser.Id);

                if (drivers != null)
                {
                    impounds = impounds.Where(impound =>
                        (impound.DispatchEntry.Driver != null && drivers.Any(d => d.Id == impound.DispatchEntry.DriverId)) ||  // where call.Driver == current driver
                        (impound.DispatchEntry.Assets != null && impound.DispatchEntry.Assets.Any(entryAsset =>
                            entryAsset.Drivers != null && entryAsset.Drivers.Any(entryAssetDriver => entryAssetDriver.DriverId != null // where asset.Drivers contains a driver entry for current driver
                                && drivers.Any(driver => driver.Id == entryAssetDriver.DriverId))))).ToCollection();        // where asset.Driver contains current driver
                }
            }

            if (WebGlobal.CurrentUser.IsAccountTypeUser())
            {
                // TODO: Can be made async
                var userAccounts = AccountUser.GetByUserId(WebGlobal.CurrentUser.Id)
                    .Select(o => o.AccountId).Union(new[]
                {
                    WebGlobal.CurrentUser.AccountId
                });

                // filter impounds to account access
                impounds = impounds.Where(w => userAccounts.Contains(w.DispatchEntry?.AccountId ?? 0)).ToCollection();

                // TODO: Can be made async
                string blockCompletedKv = CompanyKeyValue.GetFirstValueOrNull(WebGlobal.CurrentUser.CompanyId,
                    Provider.Towbook.ProviderId,
                    "PreventAccountUsersFromViewingCompletedCalls");

                if (blockCompletedKv == "1")
                {
                    return impounds.Where(impound => impound.DispatchEntry.Status.Id != Status.Completed.Id).ToCollection();
                }
            }

            return impounds;
        }

        /// <summary>
        /// method to allow update impound data [TO BE COMPLETED]
        /// </summary>
        /// <param name="id"></param>
        /// <param name="model"></param>
        [HttpPut]
        [Route("{id}")]
        public async Task Put(int id, ImpoundModel model)
        {
            var impound = await Impound.GetByIdAsync(id);

            await ThrowIfNoCompanyAccessAsync(impound?.Company?.Id, "impound");

            impound.Auction = model.Auction;
            await impound.Save(WebGlobal.CurrentUser, true);

            if (await impound.Company.HasFeatureAsync(Features.Impounds_ImpoundAuctions))
            {
                var ad = await EntryAuctionDetail.GetByDispatchEntryIdAsync(impound.DispatchEntry.Id) ?? new EntryAuctionDetail() { DispatchEntryId = impound.DispatchEntry.Id };
                await ad.SaveAsync(this.GetCurrentToken(), this.GetRequestingIp());

                if (ad.AuctionId > 0)
                {
                    // remove from auction and update item count
                    // note: will send pusher update event
                    await new AuctionsController().Delete(ad.AuctionId.Value, new AuctionsController.AuctionBatchItemRequest()
                    {
                        StockNumbers = new[] { impound.Id }
                    });
                }
            }

            var en = await Entry.GetByIdNoCacheAsync(impound.DispatchEntry.Id);
            await Entry.UpdateInAzure(en);

            var token = this.GetCurrentToken();
            if ((token?.UserId ?? 0) > 10)
                await PushNotificationProvider.UpdateCall(impound.Company.Id, impound.DispatchEntry.Id);
        }

        public static async Task<dynamic> MapAsync(Impound o,
            IEnumerable<InvoicePayment> payments = null,
            IEnumerable<Towbook.Impounds.Photo> impoundPhotos = null,
            IEnumerable<Dispatch.Photo> dispatchPhotos = null,
            IEnumerable<ReleaseDetails> releaseDetailList = null,
            IEnumerable<Towbook.Impounds.Note> notesList = null,
            IEnumerable<ImpoundTasksForImpoundList> impoundTasks = null,
            IEnumerable<VehicleTitle> vehicleTitles = null,
            IEnumerable<EntryAuctionDetail> auctionDetails = null)
        {
            var photos = new Collection<PhotoModel>();
            var drivers = new Collection<Driver>();
            foreach (var photo in impoundPhotos?.Where(ip => ip.ImpoundId == o.Id) ?? await Towbook.Impounds.Photo.GetByImpoundIdAsync(o.Id))
            {
                photos.Add(PhotoModel.Map(photo));
            }

            if (o.DispatchEntry == null)
                return null;

            foreach (var photo in (dispatchPhotos?.Where(dp =>
                dp.DispatchEntryId == o.DispatchEntry.Id) ?? await Dispatch.Photo.GetByDispatchEntryIdAsync(o.DispatchEntry.Id)))
            {
                photos.Add(PhotoModel.Map(photo));
            }

            ReleaseDetails releaseDetails = null;

            if (o.ReleaseDate.HasValue)
                releaseDetails = releaseDetailList?.Where(rd => rd.ImpoundId == o.Id).OrderByDescending(rd => rd.CreateDate).FirstOrDefault();

            var notes = (notesList?.Where(nl => nl.ImpoundId == o.Id).ToList() ?? await Towbook.Impounds.Note.GetByImpoundIdAsync(o.Id))
                .Select(n => new
                {
                    Id = n.Id,
                    CreateDate = n.CreateDate,
                    Content = n.Content,
                    AuthorName = n.User.FullName,
                    AuthorId = n.User.Id
                });

            var blockPrices = WebGlobal.CurrentUser?.CompanyId == 32564;

            #region Consider manually entered quantities for storage line items.
            var storageItem = o.DispatchEntry.InvoiceItems.FirstOrDefault(f =>
                        f.IsStorageItem() &&
                        f.RelatedInvoiceItemId == null &&
                        f.RateItem?.Predefined?.Id != PredefinedRateItem.BUILTIN_AFTER_HOUR_RELEASE_FEE);

            decimal? storageQuantity = null;
            if (storageItem != null && storageItem.Quantity != -1 /*ignore auto calculations */)
                storageQuantity = storageItem.Quantity;
            #endregion

            var ret = new
            {
                Id = o.Id,
                CallId = o.DispatchEntry.Id,
                CallNumber = o.DispatchEntry.CallNumber,
                Call = await (await CallModel.MapAsync(o.DispatchEntry, Array.Empty<CallModel.CallInsightHelper.CallInsightModel>(), payments, impound: o, vehicleTitles: vehicleTitles, auctionDetails: auctionDetails)).FinishMapAsync(),
                CompanyId = o.DispatchEntry.CompanyId,
                StockNumber = o.Id.ToString().PadLeft(6, '0'),
                InvoiceNumber = o.DispatchEntry.InvoiceNumber,
                VehicleMake = o.DispatchEntry.VehicleMake,
                VehicleModel = o.DispatchEntry.VehicleModel,
                VehicleYear = o.DispatchEntry.Year,
                LicensePlate = o.DispatchEntry.LicenseNumber,
                LicensePlateState = o.DispatchEntry.LicenseState,
                Account = (!(await WebGlobal.GetCompaniesAsync()).Any(r => r.Id == (o.Company?.Id ?? 0)) ? (o.Company?.Name ?? "") : (o.DispatchEntry.Account != null ? o.DispatchEntry.Account.Company : null)),
                AccountId = (o.DispatchEntry.Account != null ? o.DispatchEntry.Account.Id : 0),
                BillToAccountId = o.DispatchEntry.Invoice?.AccountId,
                Vin = o.DispatchEntry.VIN,
                DaysHeld = o.DaysHeld,
                DaysHeldBillable = o.DaysHeldBillable,
                InvoiceTotal = blockPrices ? 0 : o.InvoiceTotal,
                StorageTotal = blockPrices ? 0 : o.InvoiceStorageTotal,
                StorageQuantity = storageQuantity,
                Tax = o.InvoiceTax,
                ImpoundDate = o.ImpoundDate,
                ReleaseDate = o.ReleaseDate,
                ReleaseReason = o.ReleaseReason,
                Attributes = o.DispatchEntry.Attributes.Select(a => new { Key = a.Value.DispatchEntryAttributeId, Value = a.Value.Value }),
                Notes = notes,
                Photos = photos,
                ImpoundTasks = impoundTasks,
                StorageLot = (o.Lot != null ? o.Lot.Name : "Default"),
                Status = o.CurrentStatus.Name,
                Hold = o.Hold,
                InvoiceItems = blockPrices ? Array.Empty<CallInvoiceItemModel>() : CallInvoiceItemModel.MapDomainObjectListToModel(o.InvoiceItems),
                Reason = o.Reason,
                PurchaseOrderNumber = o.DispatchEntry.PurchaseOrderNumber,
                Contacts = o.DispatchEntry.Contacts.Select(s => new { Name = s.Name, Phone = s.Phone }),
                CreateDate = o.DispatchEntry.CreateDate,
                EnrouteTime = o.DispatchEntry.EnrouteTime,
                ArrivalTime = o.DispatchEntry.ArrivalTime,
                CompletionTime = o.DispatchEntry.CompletionTime,
                Drivers = o.DispatchEntry.Drivers,
                ReleaseDetails = releaseDetails,
            };

            if (blockPrices)
            {
                ret.Call.Payments = Array.Empty<Extric.Towbook.Dispatch.CallModels.PaymentModel>();
                ret.Call.PaymentsApplied = 0;
                ret.Call.BalanceDue = 0;
                ret.Call.InvoiceTotal = 0;
                ret.Call.InvoiceSubtotal = 0;
                ret.Call.InvoiceTax = 0;
                ret.Call.InvoiceItems = Array.Empty<CallInvoiceItemModel>();
            }

            return ret;
        }

        private static async Task<IEnumerable<ImpoundTasksForImpoundList>> BuildImpoundTasksAsync(int impoundId, 
            IEnumerable<ImpoundTask> repo,
            IEnumerable<CompanyTask> companyTasksRepo)
        {
            
            var impoundTasks = repo != null ? repo?.Where(o => o.ImpoundId == impoundId).Where(w => !w.Deleted) :
                (await ImpoundTask.GetByImpoundAsync(impoundId)).Where(w => !w.Deleted);

            var companyTasks = companyTasksRepo != null ? companyTasksRepo.Where(o => impoundTasks.Any(t => t.TaskId == o.Id)) : 
                   await CompanyTask.GetByIdsAsync(impoundTasks.Where(w => w.DueDate.HasValue)
                    .Select(s => s.TaskId).ToArray());
            return BuildImpoundTasks(repo, companyTasksRepo).Where(o => o.ImpoundId == impoundId);
        }

        private static IEnumerable<ImpoundTasksForImpoundList> BuildImpoundTasks(
            IEnumerable<ImpoundTask> repo,
            IEnumerable<CompanyTask> companyTasksRepo)
        {
            var result = new List<ImpoundTasksForImpoundList>();

            foreach (var it in repo.Where(w => w.DueDate.HasValue))
            {
                var task = companyTasksRepo.FirstOrDefault(f => f.Id == it.TaskId);
                if (task == null)
                    continue;

                result.Add(new ImpoundTasksForImpoundList()
                {
                    ImpoundId = it.ImpoundId,
                    ImpoundTaskId = it.ImpoundTaskId,
                    ImpoundReminderDefinitionId = it.ImpoundReminderDefinitionId,
                    DueDate = it.DueDate,
                    Status = (int)task.Status,
                    Due = GetDueDateString(it.DueDate),
                    Title = it.Title,
                    CompletionDate = task.CompletionDate,
                    Hidden = it.Hidden.GetValueOrDefault()
                });
            }

            return result;
        }

        private static string GetDueDateString(DateTime? dueDate)
        {
            if (dueDate == null)
                return string.Empty;

            if (dueDate.Value.Date == DateTime.Today.Date)
                return "Due Today";

            if (dueDate < DateTime.Today.Date)
                return "Overdue";

            if (dueDate > DateTime.Today.Date && dueDate < DateTime.Today.AddDays(3).Date)
                return "Due soon";

            return "Due Next";
        }

        private string GetValueOrNull(string input)
        {
            if (string.IsNullOrWhiteSpace(input))
                return null;
            else
                return input;
        }

        [HttpGet("search")]
        public async Task<object> SearchAsync()
        {
            // TODO: Can be made async
            if (!WebGlobal.CurrentUser.HasPermissionToViewImpounds())
            {
                return Array.Empty<Impound>();
            }

            NameValueCollection nvc = HttpUtility.ParseQueryString(Web.HttpContext.Current.Request.QueryString.Value);

            string quickSearch = GetValueOrNull(nvc["quickSearch"]);

            string lp = GetValueOrNull(nvc["plateNumber"]) ?? GetValueOrNull(nvc["lp"]);
            string rsn = GetValueOrNull(nvc["rsn"]);
            string dh = GetValueOrNull(nvc["dh"]);
            string sl = GetValueOrNull(nvc["sl"]);
            int callNumber = GetValueOrNull(nvc["callNumber"]) != null ? Convert.ToInt32(nvc["callNumber"]) : 0;
            string a = GetValueOrNull(nvc["a"]);
            if (a == "0") a = null;
            string on = GetValueOrNull(nvc["on"]);
            string op = GetValueOrNull(nvc["op"]);
            bool impounded = GetValueOrNull(nvc["impounded"]) != null ? Convert.ToBoolean(nvc["impounded"]) : true;
            bool released = GetValueOrNull(nvc["released"]) != null ? Convert.ToBoolean(nvc["released"]) : false;
            string mk = GetValueOrNull(nvc["mk"]);
            string md = GetValueOrNull(nvc["md"]);
            string yr = GetValueOrNull(nvc["yr"]);
            string vin = GetValueOrNull(nvc["vin"]);
            DateTime? sd = GetValueOrNull(nvc["sd"]) != null ? (DateTime?)Convert.ToDateTime(nvc["sd"]) : null;
            DateTime? ed = GetValueOrNull(nvc["ed"]) != null ? (DateTime?)Convert.ToDateTime(nvc["ed"]) : null;
            bool searchDateByImpoundDate = GetValueOrNull(nvc["sdb"]) != null && Convert.ToInt32(nvc["sdb"]) == 2 ? false : true;
            string @in = GetValueOrNull(nvc["in"]);
            string attributeId = GetValueOrNull(nvc["cn"]);
            string attributeValue = GetValueOrNull(nvc["cnv"]);
            string towsource = GetValueOrNull(nvc["ts"]);

            int page = GetValueOrNull(nvc["page"]) != null ? Convert.ToInt32(nvc["page"]) : 1;
            int pageSize = GetValueOrNull(nvc["pageSize"]) != null ? Convert.ToInt32(nvc["pageSize"]) : 250;

            if (pageSize > 250)
                pageSize = 250;

            int companyId = Convert.ToInt32(GetValueOrNull(nvc["companyId"]) ?? "0");

            if (WebGlobal.CurrentUser.AccountId > 0)
            {
                a = WebGlobal.CurrentUser.AccountId.ToString();
            }

            Int32.TryParse(@in, out callNumber);

            var sq = new SearchQuery
            {
                LicensePlate = lp,
                ImpoundTypeId = WebGlobal.StringToNullableInt(rsn),
                MinDaysHeld = WebGlobal.StringToNullableInt(dh),
                ImpoundLotId = WebGlobal.StringToNullableInt(sl),
                CallNumber = (callNumber > 0 ? (int?)callNumber : null),
                InvoiceNumber = @in,
                ImpoundId = (callNumber > 0 ? (int?)callNumber : null),
                AccountId = WebGlobal.StringToNullableInt(a),
                OwnerName = on,
                OwnerPhone = op,
                DisplayImpounded = impounded,
                DisplayReleased = released,
                MakeString = mk,
                ModelString = md,
                ModelYear = WebGlobal.StringToNullableInt(yr),
                VIN = vin,
                StartDate = sd,
                EndDate = ed,
                SearchDateByImpoundDate = searchDateByImpoundDate,
                AttributeId = WebGlobal.StringToNullableInt(attributeId),
                AttributeValue = attributeValue,
                TowSource = towsource,
                BatchSize = pageSize,
                PageNum = page
            };

            var companies = await CompanyUser.GetByUserIdAsync(WebGlobal.CurrentUser.Id);

            if (string.IsNullOrEmpty(quickSearch))
            {
                var found = (await Extric.Towbook.Impounds.Impound.SearchAsync(WebGlobal.CurrentUser.Company, sq)).ToList();
                if (found != null)
                {
                    // search across multiple companies
                    if (companies.Length > 1 && WebGlobal.CurrentUser.CompanyId != 1525)
                    {
                        companies = companies.Where(o => o.CompanyId != 1525).ToArray();
                        foreach (var cu in companies.Where(o => o.CompanyId != WebGlobal.CurrentUser.CompanyId))
                        {
                            found.AddRange((await Impound.SearchAsync(await Company.Company.GetByIdAsync(cu.CompanyId), sq)).ToList());
                        }
                    }

                    var batchIds = found.Select(o => o.DispatchEntry.Id).ToArray();
                    var batchImpoundIds = found.Select(f => f.Id).ToArray();

                    var payments = await InvoicePayment.GetByDispatchEntryIdsAsync(batchIds, null);
                    var dispatchPhotos = await Dispatch.Photo.GetByDispatchEntryIdsAsync(batchIds);
                    var impoundPhotos = await Towbook.Impounds.Photo.GetByImpoundIdAsync(batchImpoundIds);
                    var notes = await Towbook.Impounds.Note.GetByImpoundIdsAsync(batchImpoundIds);
                    var releaseDetails = await ReleaseDetails.GetByImpoundIdsAsync(found.Where(o => o.ReleaseDate != null).Select(o => o.Id));
                    var impoundTasks = await ImpoundTask.GetByImpoundsAsync(batchImpoundIds);
                    var vehicleTitles = await VehicleTitle.GetByDispatchEntryIdsAsync(batchIds);
                    var auctionDetails = await EntryAuctionDetail.GetByDispatchEntryIdsAsync(batchIds);

                    List<dynamic> results = [];

                    var impoundTasksForList = BuildImpoundTasks(impoundTasks, await CompanyTask.GetByIdsAsync(impoundTasks.Where(w => w.DueDate.HasValue)
                        .Select(s => s.TaskId).ToArray()));

                    foreach (var entry in found)
                    {
                        results.Add(await MapAsync(entry, payments, impoundPhotos, dispatchPhotos, releaseDetails, notes, 
                            impoundTasksForList.Where(t => t.ImpoundId == entry.Id), vehicleTitles, auctionDetails));
                    }
                    
                    return results;
                }
                else
                    return Array.Empty<Impound>();
            }
            else
            {
                // quick search based on one string
                List<dynamic> results = new List<dynamic>();
                int[] c = (await WebGlobal.GetCompaniesAsync()).Select(s => s.Id).ToArray();

                // TODO: Can be made async
                var impounds = await Impound.FindAsync(c, quickSearch, impounded, released, page, pageSize);

                var batchIds = impounds.Select(o => o.DispatchEntry.Id).ToArray();
                var batchImpoundIds = impounds.Select(o => o.Id).ToArray();

                var payments = await InvoicePayment.GetByDispatchEntryIdsAsync(batchIds, null);
                var dispatchPhotos = await Dispatch.Photo.GetByDispatchEntryIdsAsync(batchIds);
                var impoundPhotos = await Towbook.Impounds.Photo.GetByImpoundIdAsync(batchImpoundIds);
                var notes = await Towbook.Impounds.Note.GetByImpoundIdsAsync(batchImpoundIds);
                var releaseDetails = await ReleaseDetails.GetByImpoundIdsAsync(impounds.Where(o => o.ReleaseDate != null).Select(o => o.Id));
                var impoundTasks = await ImpoundTask.GetByImpoundsAsync(batchImpoundIds);
                var vehicleTitles = await VehicleTitle.GetByDispatchEntryIdsAsync(batchIds);
                var auctionDetails = await EntryAuctionDetail.GetByDispatchEntryIdsAsync(batchIds);

                var impoundTasksForList = BuildImpoundTasks(impoundTasks, await CompanyTask.GetByIdsAsync(impoundTasks.Where(w => w.DueDate.HasValue)
                    .Select(s => s.TaskId).ToArray()));

                foreach (var i in impounds)
                {
                    if (i.DispatchEntry != null)
                    {
                        results.Add(await MapAsync(i, payments, impoundPhotos, dispatchPhotos, releaseDetails, notes,
                            impoundTasksForList.Where(t => t.ImpoundId == i.Id), vehicleTitles, auctionDetails));
                    }
                }

                if (WebGlobal.CurrentUser.AccountId > 0)
                {
                    // TODO: Can be made async
                    var aul = AccountUser.GetByUserId(WebGlobal.CurrentUser.Id);

                    results = results.Where(o => o.AccountId == WebGlobal.CurrentUser.AccountId ||
                        aul.Any(au => au.AccountId == o.AccountId)).ToList();
                }

                return results;
            }
        }

        /// <summary>
        /// Retrieve an Impound by its unique ID
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("{id}")]
        public async Task<object> GetAsync(int id)
        {
            var imp = await Impound.GetByIdAsync(id);

            await ThrowIfNoCompanyAccessAsync(imp?.Company?.Id, "impound");

            if (WebGlobal.CurrentUser.AccountId > 0)
            {
                if (imp.Account == null || imp.Account.Id != WebGlobal.CurrentUser.AccountId)
                    throw new Web.HttpResponseException(
                    new HttpResponseMessage(HttpStatusCode.NotFound)
                    {
                        Content = new StringContent("The specified impound either doesn't exist or you don't have access to it.")
                    });
            }

            Collection<ReleaseDetails> releaseDetailList = null;
            if (imp.ReleaseDate != null)
            {
                var releaseDetail = await ReleaseDetails.GetByImpoundAsync(imp);
                if (releaseDetail != null)
                {
                    releaseDetailList = new Collection<ReleaseDetails>();
                    releaseDetailList.Add(releaseDetail);
                }
            }

            var payments = await InvoicePayment.GetByDispatchEntryIdsAsync(new[] { imp.DispatchEntry.Id }, null);
            var dispatchPhotos = await Dispatch.Photo.GetByDispatchEntryIdsAsync(new[] { imp.DispatchEntry.Id });
            var impoundPhotos = await Towbook.Impounds.Photo.GetByImpoundIdAsync(new[] { imp.Id });
            var notes = await Towbook.Impounds.Note.GetByImpoundIdsAsync(new[] { imp.Id });
            var impoundTasks = await ImpoundTask.GetByImpoundsAsync(new[] { imp.Id });
            var vehicleTitles =  await VehicleTitle.GetByDispatchEntryIdsAsync(new[] { imp.DispatchEntry.Id });
            var auctionDetails = await EntryAuctionDetail.GetByDispatchEntryIdsAsync(new[] { imp.DispatchEntry.Id });

            var impoundTasksForList = BuildImpoundTasks(impoundTasks, await CompanyTask.GetByIdsAsync(impoundTasks.Where(w => w.DueDate.HasValue)
                .Select(s => s.TaskId).ToArray()));

            return await MapAsync(imp, payments, impoundPhotos, dispatchPhotos, releaseDetailList, notes, impoundTasksForList, vehicleTitles, auctionDetails);
        }

        [NewRelic.Api.Agent.Transaction]
        [HttpGet("generateReport")]
        public async Task<object> GenerateReport(
            [FromQuery] int[] accountIds,
            [FromQuery] int[] impoundLotIds,
            [FromQuery] bool showImpounded,
            [FromQuery] bool showReleased,
            [FromQuery] bool excludeAuction,
            [FromQuery] int includePoliceHold,
            [FromQuery] int? atLeastDays,
            [FromQuery] DateTime? impoundDateStart,
            [FromQuery] DateTime? impoundDateEnd,
            [FromQuery] DateTime? releasedDateStart,
            [FromQuery] DateTime? releasedDateEnd,
            [FromQuery] int[] companyIds)
        {
            if (!WebGlobal.CurrentUser.HasPermissionToViewImpounds())
            {
                return Array.Empty<Impound>();
            }

            var companies = await WebGlobal.GetCompaniesAsync();

            if (companyIds == null)
                companyIds = companies.Select(s => s.Id).ToArray();

            #region validate inputs
            if (companyIds != null && (companyIds.Length == 0 || companyIds[0] == 0))
            {
                companyIds = companies.Select(s => s.Id).ToArray();

                if (WebGlobal.CurrentUser.CompanyId == 4082 || WebGlobal.CurrentUser.CompanyId == 6437)
                    companyIds = new int[] { WebGlobal.CurrentUser.Company.Id };
            }

            // Limit to current users' company access (IDOR)
            companyIds = companyIds.Where(w => companies.Select(s => s.Id).Contains(w)).ToArray();

            if (impoundLotIds != null && (impoundLotIds.Length == 0 || impoundLotIds[0] == 0))
                impoundLotIds = Array.Empty<int>();

            if (accountIds != null && (accountIds.Length == 0 || accountIds[0] == 0))
                accountIds = Array.Empty<int>();

            DateTime start = DateTime.Now.Date.AddDays(-90);
            DateTime stopDate = DateTime.Now.Date.AddDays(1);

            DateTime releaseDateStart = DateTime.MinValue;
            DateTime releaseDateStop = DateTime.MinValue;

            if (impoundDateStart.HasValue && impoundDateStart > DateTime.MinValue && impoundDateStart < DateTime.MaxValue)
                start = Convert.ToDateTime(impoundDateStart.Value);

            if (impoundDateEnd.HasValue)
            {
                stopDate = Convert.ToDateTime(impoundDateEnd.Value);
                if (stopDate == DateTime.MinValue) stopDate = DateTime.Now.Date.AddDays(1);
            }

            if (releasedDateStart.HasValue)
            {
                releaseDateStart = Convert.ToDateTime(releasedDateStart.Value);
                if (releaseDateStart == DateTime.MinValue) releaseDateStart = DateTime.Now.Date.AddDays(1);
            }

            if (releasedDateEnd.HasValue)
            {
                releaseDateStop = Convert.ToDateTime(releasedDateEnd.Value);
                if (releaseDateStop == DateTime.MinValue) releaseDateStop = DateTime.Now.Date.AddDays(1);
            }

            int daysPast = 0;
            if (atLeastDays.HasValue)
                if (!Int32.TryParse(atLeastDays.Value.ToString(), out daysPast))
                    daysPast = 0;
            #endregion

            var list = await Impound.GetByCompanyAsync(companyIds,
                                ImpoundType.NotSpecified,
                                showImpounded,
                                showReleased,
                                accountIds,
                                start,
                                stopDate,
                                releaseDateStart,
                                releaseDateStop,
                                excludeAuction,
                                includePoliceHold,
                                impoundLotIds);



            // filter by atLeastDaysHeld value
            if (daysPast > 0)
                list = list.Where(w => w.DaysHeld > daysPast).ToList();

            list = FilterListByCurrentUser(list).ToList();

            await Impound.InitAsync(list);

            var releaseDetails = ReleaseDetails.GetByImpoundIds(list.Where(o => o.ReleaseDate != null).Select(o => o.Id));
            var batchImpoundIds = list.Select(o => o.Id).ToArray();
            var batchIds = list.Where(w => w.DispatchEntry != null).Select(z => z.DispatchEntry.Id).ToArray();

            var payments = await InvoicePayment.GetByDispatchEntryIdsAsync(batchIds, null);
            var dispatchPhotos = await Dispatch.Photo.GetByDispatchEntryIdsAsync(batchIds);
            var impoundPhotos = await Towbook.Impounds.Photo.GetByImpoundIdAsync(batchImpoundIds);
            var notes = await Towbook.Impounds.Note.GetByImpoundIdsAsync(batchImpoundIds);
            var impoundTasks = await ImpoundTask.GetByImpoundsAsync(batchImpoundIds);
            var vehicleTitles = await VehicleTitle.GetByDispatchEntryIdsAsync(batchIds);
            var auctionDetails = await EntryAuctionDetail.GetByDispatchEntryIdsAsync(batchIds);

            var impoundTasksForList = BuildImpoundTasks(impoundTasks, await CompanyTask.GetByIdsAsync(impoundTasks.Where(w => w.DueDate.HasValue)
                .Select(s => s.TaskId).ToArray()));

            return await Task.WhenAll(list.OrderBy(b => b.ImpoundDate)
                .Select(async o => await MapAsync(o, payments, impoundPhotos, dispatchPhotos, releaseDetails, notes, 
                    impoundTasksForList.Where(t => t.ImpoundId == o.Id), vehicleTitles, auctionDetails))
                .Where(w => w != null)
                .ToList());
        }

        [HttpGet]
        [Route("{id}/release")]
        public async Task<ImpoundReleaseModel> ReleaseAsync(int id)
        {
            var imp = await Impound.GetByIdAsync(id);

            ThrowIfNotFound(imp, "Impound");
            ThrowIfNotFound(imp.Company, "Impound");
            await ThrowIfNoCompanyAccessAsync(imp.Company?.Id, "Impound");

            if (WebGlobal.CurrentUser.Type == Towbook.User.TypeEnum.Driver)
            {
                // TODO: Can be made async
                var preventDrivers = CompanyKeyValue.GetFirstValueOrNull(imp.Company.Id, Provider.Towbook.ProviderId, "PreventDriversFromViewingImpounds");
                if (preventDrivers == "1")
                    throw new HttpResponseException(new HttpResponseMessage(HttpStatusCode.Forbidden)
                    {
                        Content = new StringContent("You do not have permission to release this impound.")
                    });
            }


            // TODO: Can be made async
            var ruleSet = ReleaseValidationRuleSet.GetByCompany(imp.Company.Id, imp.Invoice.AccountId ?? imp.DispatchEntry.AccountId);
            var model = new ImpoundReleaseModel()
            {
                Id = id,
                CompanyId = imp.Company.Id
            };

            model.ValidationRuleSets = ReleaseRuleSetModel.ValidateRelease(model, imp, ruleSet, WebGlobal.CurrentUser);

            return model;
        }


        /// <summary>Calculate the predicted invoice total and billable days held quantity within a provided date range.</summary>
        /// <param name="id">The impound id.</param>
        /// <param name="callId">(optional) the callId of the equivalent impound</param>
        /// <param name="startDate">(optional) the beginning date when storage calculations begin - usually the impoundment date</param>
        /// <param name="endDate">(optional) the date to end storage calculations - usually the released date or "now"</param>
        [HttpGet]
        [Route("{id}/calculate")]
        public async Task<CalculatedStorageBillableModel> CalculateDaysHeldBillable(int id = 0,
            [FromQuery] int? callId = null,
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null)
        {
            Impound imp = null;
            Entry entry = null;

            if (id > 0)
            {
                imp = await Impound.GetByIdAsync(id);
                entry = imp.DispatchEntry;
            }
            else if (callId.GetValueOrDefault() > 0)
            {
                entry = await Entry.GetByIdAsync(callId.Value);
                // TODO: Can be made async
                imp = Impound.GetByDispatchEntry(entry);
            }

            ThrowIfNotFound(imp, "impound");
            ThrowIfNotFound(entry, "call");
            await ThrowIfNoCompanyAccessAsync(entry.CompanyId);

            // get storage invoice item
            var storageItem = entry.InvoiceItems.FirstOrDefault(f =>
                                f.IsStorageItem() &&
                                f.RelatedInvoiceItemId == null &&
                                f.RateItem?.Predefined?.Id != PredefinedRateItem.BUILTIN_AFTER_HOUR_RELEASE_FEE);

            if (storageItem == null || storageItem.RateItem == null || !entry.Impound)
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound)
                {
                    Content = new StringContent($"The call {entry.Id} is not an impound or no storage item could be found.")
                });
            }

            // set default dates
            startDate = startDate ?? imp.ImpoundDate ?? entry.CompletionTime ?? entry.CreateDate;
            endDate = endDate ?? imp.ReleaseDate ?? DateTime.Now;

            // get predicted storage info by dates
            var r = await CalculateStorageRateAmountAsync(storageItem.RateItem?.RateItemId ?? -1 /*default rate*/,
                        entry.Assets?.FirstOrDefault()?.BodyTypeId ?? 0,
                        startDate.Value,
                        endDate.Value,
                        null,
                        entry.AccountId,
                        entry.CompanyId,
                        storageItem.CustomPrice);

            if (r == null)
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.InternalServerError)
                {
                    Content = new StringContent($"The impound calculations for call {entry.Id} cannot be predicted or an error occured.")
                });
            }

            // apply assetId and invoiceId to predicted storage items
            r.StorageInvoiceItems = r.StorageInvoiceItems.Select(ii => {
                ii.AssetId = storageItem.AssetId;
                ii.InvoiceId = storageItem.InvoiceId;
                return ii;
            }).ToArray();

            // replace storage invoice item with predicted storage quantity
            storageItem = await CallInvoiceItemModel.MapAsync(r.StorageInvoiceItems.FirstOrDefault(f => f.RateItemId == storageItem.RateItem.RateItemId), storageItem);

            // add or replace possible after hour storage item
            var ahii = r.StorageInvoiceItems?.FirstOrDefault(f => f.PredefinedRate?.Id == PredefinedRateItem.BUILTIN_AFTER_HOUR_RELEASE_FEE);
            if (ahii != null)
            {
                var ii = entry.InvoiceItems.FirstOrDefault(f => f.RateItem?.Predefined?.Id == PredefinedRateItem.BUILTIN_AFTER_HOUR_RELEASE_FEE);
                if (ii != null)
                    ii = await CallInvoiceItemModel.MapAsync(ahii, ii);
                else
                    entry.InvoiceItems.Add(await CallInvoiceItemModel.MapAsync(ahii, ii));
            }

            // update invoice totals
            // TODO: Can be made async
            entry.Invoice.ForceRecalculate(false);

            return new CalculatedStorageBillableModel()
            {
                DaysHeldBillable = r.DaysHeldBillable,
                StartDate = startDate.Value,
                EndDate = endDate.Value,
                Invoice = new CalculatedStorageBillableInvoiceModel()
                {
                    Subtotal = entry.InvoiceSubtotal,
                    Tax = entry.InvoiceTax,
                    Total = entry.InvoiceTotal,
                    BalanceDue = entry.BalanceDue,
                    InvoiceItems = CallInvoiceItemModel.MapDomainObjectListToModel(entry.InvoiceItems)
                }
            };
        }

        [Route("calculate")]
        [HttpGet]
        public async Task<CalculatedStorageBillableModel> CalculateDaysHeldBillableAsync(
            [FromQuery] int companyId,
            [FromQuery] DateTime? startDate,
            [FromQuery] DateTime? endDate = null,
            [FromQuery] int? accountId = null)
        {
            if (companyId < 1)
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest)
                {
                    Content = new StringContent("Your request body is empty.  Did you forget to include the company id?")
                });
            }

            if (!await WebGlobal.CurrentUser.HasAccessToCompanyAsync(companyId))
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.Forbidden)
                {
                    Content = new StringContent("The current user does not have access to the company specified: " + companyId)
                });
            }

            if (startDate != null)
                startDate = WebGlobal.OffsetDateTime(startDate.Value, true);
            else
                startDate = DateTime.Now;

            if (endDate != null)
                endDate = WebGlobal.OffsetDateTime(endDate.Value, true);
            else
                endDate = DateTime.Now;

            if (startDate.Value > DateTime.Now)
                startDate = DateTime.Now;

            return new CalculatedStorageBillableModel()
            {
                // TODO: Can be made async
                DaysHeldBillable = Impound.CalculateDaysHeldBillable(companyId, accountId, startDate, endDate),
                StartDate = startDate.Value,
                EndDate = endDate == null ? DateTime.Now : endDate.Value,
                Invoice = null
            };
        }

        /// <summary> '/api/impounds/storage'
        /// Used to predict what the storage amount will be based off of the parameters provided.  You don't need an impound object to make the calculation.
        /// </summary>
        /// <param name="rateItemId">Required. Must be a storage rate item. Tiered storage item is accepted.</param>
        /// <param name="bodyTypeId">Required. Used to retrieve the correct storage price.</param>
        /// <param name="startDate">Required. The equivalent date to the impound date.</param>
        /// <param name="endDate">Required. The equivalent date to the release date.</param>
        /// <param name="daysHeld">Optional. Forces the quantity of the storage item to stay constant.</param>
        /// <param name="accountId">Optional. Allows for an account rate item pricing structure to be considered when determining the storage rate.</param>
        /// <param name="companyId">Optional. Used when there is a multicompany setup where you need to determine the rate based off a sister company's pricing/storage settings.</param>
        /// <param name="customPrice">Optional. Forces the rate to be a constant price (ignore any rate item structure at the account or company level).</param>
        /// <returns>The object returned includes many properties.  A few to highlight are:
        ///             1) The storage total as a decimal (line total)
        ///             2) the billable days as a decimal (quantity)
        ///             3) If the storage is tiered, the return contains an array of tired children items used to calculate the total.
        /// </returns>
        /// 

        //routes.MapHttpRoute(
        //    name: "Storage",
        //    routeTemplate: "impounds/storage",
        //    defaults: new { controller = "Impounds", action = "CalculateStorageRateAmount" })
        //    .DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };
        [Route("storage")]
        [HttpGet]
        public async Task<CalculatedStorageModel> CalculateStorageRateAmountAsync(
            [FromQuery] int rateItemId,
            [FromQuery] int bodyTypeId,
            [FromQuery] DateTime startDate,
            [FromQuery] DateTime endDate,
            [FromQuery] decimal? daysHeld = null,
            [FromQuery] int? accountId = null,
            [FromQuery] int? companyId = null,
            [FromQuery] decimal? customPrice = null)
        {
            RateItem ri = await RateItem.GetByIdAsync(rateItemId);
            if (ri == null)
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest)
                {
                    Content = new StringContent("Your request body is missing a value.  Did you forget to include the storage rate id?")
                });
            }

            var cId = companyId.HasValue ? companyId.Value : ri.CompanyId;

            if (!await WebGlobal.CurrentUser.HasAccessToCompanyAsync(cId))
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.Forbidden)
                {
                    Content = new StringContent("The current user does not have access to the company specified: " + WebGlobal.CurrentUser.CompanyId)
                });
            }

            var bodyType = await Vehicle.BodyType.GetByIdAsync(bodyTypeId);
            if (bodyType == null)
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest)
                {
                    Content = new StringContent("Your request body is missing a value.  Did you forget to include the vehicle body type id?")
                });
            }

            // Convert local datetime to server timezone
            DateTime localImpoundDate = startDate;
            DateTime localReleaseDate = endDate;

            // check for valid start and end dates provided by the user.  Swap if backwards.
            if (localImpoundDate > localReleaseDate)
            {
                // reverse the dates
                var temp = localReleaseDate;
                localReleaseDate = localImpoundDate;
                localImpoundDate = temp;
            }

            return await CalculatedStorageModel.CalculateStorageAsync(ri, bodyType, localImpoundDate, localReleaseDate, daysHeld, accountId, cId, customPrice);
        }

        /// <summary>
        /// Record a new Impound Release
        /// </summary>
        /// <param name="id">the impound id</param>
        /// <param name="model">form data passed as ImpoundReleaseModel</param>
        /// <returns>ImpoundReleaeModel</returns>
        [HttpPost("{id}/release")]
        public async Task<object> Release(int id, ImpoundReleaseModel model)
        {
            if (model == null)
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest)
                {
                    Content = new StringContent("Your request body is empty. Did you forget to include the JSON or set the content-type to application/json?")
                });
            }

            if (model.Id > 0)
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest)
                {
                    Content = new StringContent("You shouldn't set the ID in a POST request. If you're trying to update an existing object, use PUT method instead")
                });
            }

            var impound = await Impound.GetByIdAsync(id);

            if (impound == null || !await WebGlobal.CurrentUser.HasAccessToCompanyAsync(impound.Company.Id))
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound) { Content = new StringContent("The specified impound either doesn't exist or you don't have access to it.") });

            if (WebGlobal.CurrentUser.Type == Towbook.User.TypeEnum.Driver)
            {
                // TODO: Can be made async
                var preventDrivers = CompanyKeyValue.GetFirstValueOrNull(impound.Company.Id, Provider.Towbook.ProviderId, "PreventDriversFromViewingImpounds");
                if (preventDrivers == "1")
                    throw new HttpResponseException(new HttpResponseMessage(HttpStatusCode.Forbidden)
                    {
                        Content = new StringContent("You do not have permission to release this impound.")
                    });
            }

            if (impound.ReleaseDate != null)
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest) { Content = new StringContent("This impound has already been released.") });

            if (impound.ImpoundDate == null)
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest) { Content = new StringContent("This Impound must have an Impound Date before you can release it.") });

            // TODO: Can be made async
            if (impound.Hold && !WebGlobal.CurrentUser.HasPermissionToRemovePoliceHolds())
                throw new HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest) { Content = new StringContent("This vehicle is currently under police hold and you do not have permission to release the hold.") });

            bool validateAgainstRuleSets = !impound.DispatchEntry.Attributes.ContainsKey(Dispatch.AttributeValue.BUILTIN_TOWOUT_CALL);

            // store original days held (before adjusting dates)
            var odh = impound.DaysHeldBillable;

            Impound senderImpound = null;


            // find the Impound from the police department that created this impound
            var cr = await CallRequest.GetByDispatchEntryId(impound.DispatchEntry.Id);
            if (cr != null && (await Account.GetByIdAsync(cr.AccountId))?.MasterAccountId == MasterAccountTypes.Towbook)
            {
                var senderAccount = await Account.GetByIdAsync(cr.AccountId);

                var senderCall = Entry.GetById(Convert.ToInt32(cr.PurchaseOrderNumber));
                if (senderCall.CompanyId.ToString() == senderAccount.ReferenceNumber)
                {
                    senderImpound = Impound.GetByDispatchEntry(senderCall, true);
                }
            }
            

                
            


            impound.ReleasePickupDate = impound.ReleaseDate = model.ReleaseDate;
            impound.ReleaseReason = model.ReleaseReason;
            impound.ReleaseNotes = model.Notes;
            impound.Auction = false;
            impound.Hold = false;

            if (senderImpound != null)
            {
                senderImpound.ReleasePickupDate = senderImpound.ReleaseDate = impound.ReleasePickupDate;
                senderImpound.ReleaseReason = impound.ReleaseReason;
                senderImpound.Auction = impound.Auction;
                senderImpound.Hold = false;
            }

            if (impound.ReleaseDate < impound.ImpoundDate)
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest) { Content = new StringContent("The release date is earlier than the impound date. ReleaseDate=" + impound.ReleaseDate + " ImpoundDate " + impound.ImpoundDate) });

            // Check for difference in storage quantity.
            // Different? Change storage days.
            // TODO: Can be made async
            var dh = impound.GetDaysHeldBillable(impound.ImpoundDate, impound.ReleaseDate);
            if (odh != dh && model.ShouldUpdateStorageQuantityOnRelease)
            {
                foreach (var ii in impound.InvoiceItems)
                {
                    if (!ii.IsStorageItem())
                        continue;

                    if (ii.Quantity != dh)
                        ii.Quantity = dh; // change the storage quantity.
                }
            }

            // Validate against rule sets for release
            // TODO: Can be made async
            if (validateAgainstRuleSets && !ValidateReleaseAgainstRuleSets(model, impound, WebGlobal.CurrentUser, out var message))
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(Forbidden())
                {
                    Content = new StringContent(message)
                });
            }

            #region After Hour Service Fee
            if (await WebGlobal.CurrentUser.Company.HasFeatureAsync(Generated.Features.Impounds_AfterHoursReleaseFee))
            {
                // TODO: Can be made async
                var fee = AfterHoursReleaseFeeModel.GetAfterHourReleaseFeeIfAny(impound.Company, impound.ReleaseDate.Value, impound.DispatchEntry.Account, impound.DispatchEntry.BodyType);
                if (fee > 0)
                {
                    if (!impound.InvoiceItems.Any(w => w.RateItem != null && w.RateItem.Predefined != null && w.RateItem.Predefined.Id == PredefinedRateItem.BUILTIN_AFTER_HOUR_RELEASE_FEE))
                    {
                        var ri = RateItem.GetPredefinedByCompany(WebGlobal.CurrentUser.Company)
                            .Where(w => w.Predefined != null && w.Predefined.Id == PredefinedRateItem.BUILTIN_AFTER_HOUR_RELEASE_FEE).FirstOrDefault();

                        impound.DispatchEntry.InvoiceItems.Add(new InvoiceItem()
                        {
                            InvoiceId = impound.Invoice.Id,
                            RateItem = ri,
                            Quantity = 1,
                            CustomPrice = fee,
                            CustomName = ri.Name
                        });

                        impound.DispatchEntry.Invoice.ForceRecalculate(true);
                    }
                }
            }
            #endregion

            var hi = new Extric.Towbook.Impounds.HistoryItem();
            hi.ImpoundId = impound.Id;
            hi.Action = Extric.Towbook.Impounds.HistoryItem.ActionEnum.Released;
            hi.Data = impound.ReleaseDate.Value.ToShortDateString();
            hi.User = WebGlobal.CurrentUser;

            var releaseDetail = new ReleaseDetails();
            releaseDetail.ImpoundId = impound.Id;
            releaseDetail.FullName = model.FullName;
            releaseDetail.Address = model.Address;
            releaseDetail.City = model.City;
            releaseDetail.State = model.State;
            releaseDetail.Zip = model.Zip;
            releaseDetail.LicenseNumber = model.LicenseNumber;
            releaseDetail.LicenseExpirationDate = model.LicenseNumberExpirationDate ?? model.Expiration;
            releaseDetail.LicenseType = model.LicenseType;
            releaseDetail.BirthDate = model.BirthDate;
            releaseDetail.Phone = model.Phone;

            await impound.Save(WebGlobal.CurrentUser, this.GetCurrentToken(), this.GetRequestingIp());
            if (senderImpound != null)
                await senderImpound.Save(WebGlobal.CurrentUser, this.GetCurrentToken(), this.GetRequestingIp());

            // TODO: Can be made async
            releaseDetail.Save(WebGlobal.CurrentUser);

            if (senderImpound != null)
            {
                var senderRelease = new ReleaseDetails();

                senderRelease.ImpoundId = senderImpound.Id;
                senderRelease.FullName = releaseDetail.FullName;
                senderRelease.Address = releaseDetail.Address;
                senderRelease.City = releaseDetail.City;
                senderRelease.State = releaseDetail.State;
                senderRelease.Zip = releaseDetail.Zip;
                senderRelease.LicenseNumber = releaseDetail.LicenseNumber;
                senderRelease.LicenseExpirationDate = releaseDetail.LicenseExpirationDate;
                senderRelease.LicenseType = releaseDetail.LicenseType;
                senderRelease.BirthDate = releaseDetail.BirthDate;
                senderRelease.Phone = releaseDetail.Phone;

                senderRelease.Save(WebGlobal.CurrentUser);
                
                var hi2 = new HistoryItem();
                hi2.ImpoundId = senderImpound.Id;
                hi2.Action = HistoryItem.ActionEnum.Released;
                hi2.Data = impound.ReleaseDate.Value.ToShortDateString();
                hi2.User = WebGlobal.CurrentUser;
                
                hi2.Save();
            }

            int? paymentId = null;
            bool sendPusherUpdate = false;
            try
            {
                var payments = model.Payments?.ToCollection() ?? new Collection<ReleasePaymentModel>();

                if (!payments.Any() && model.PaymentType != null && model.PaymentAmount > 0)
                {
                    payments.Add(new ReleasePaymentModel()
                    {
                        Amount = model.PaymentAmount,
                        Reference = model.PaymentReferenceNumber,
                        Type = PaymentType.GetById(model.PaymentType.Value)
                    });
                }

                var paymentInvoices = await RecordPaymentAtTimeOfRelease(impound, payments, WebGlobal.CurrentUser, this.GetRequestingIp());

                if (paymentInvoices.Any())
                {
                    // apply first payment Id
                    paymentId = paymentInvoices.First().Id;
                }
                else
                {
                    sendPusherUpdate = true;
                }

            }
            catch (Exception ex)
            {
                if (ex is SquareApiException sae)
                    return new HttpResponseMessage(HttpStatusCode.BadRequest) { Content = new StringContent(sae.Message) };

                return new HttpResponseMessage(HttpStatusCode.InternalServerError) { Content = new StringContent(ex.Message) };
            }

            // TODO: Can be made async
            hi.Save();

            #region History logging
            var releaseDetailsTrackable = new ReleaseEvent()
            {
                ImpoundId = impound.Id,
                DispatchEntryId = impound.DispatchEntry.Id,
                ReleaseDate = (DateTime?)null,
                ReleaseReason = string.Empty,
                ReleaseNotes = string.Empty
            };

            // add changed values for tracking
            releaseDetailsTrackable.ReleaseDate = impound.ReleaseDate;
            // TODO: Can be made async
            releaseDetailsTrackable.ReleaseReason = impound.ReleaseReason.HasValue ? (await ReleaseReason.GetByIdAsync((int)impound.ReleaseReason))?.Description ?? "" : "";
            releaseDetailsTrackable.ReleaseNotes = impound.ReleaseNotes;

            // save any changes to history/activity logging
            await releaseDetailsTrackable.Save(WebGlobal.CurrentUser, false);
            #endregion


            // Remove vehicle from auction
            var ad = await EntryAuctionDetail.GetByDispatchEntryIdAsync(impound.DispatchEntry.Id);
            if (ad?.AuctionId > 0)
            {
                var auctionItem = (await AuctionItem.GetByAuctionIdAsync(ad.AuctionId.GetValueOrDefault())).FirstOrDefault(f => f.DispatchEntryId == impound.DispatchEntry.Id);
                if (auctionItem != null && auctionItem.AuctionId == ad.AuctionId)
                {
                    // TODO: Can be made async
                    var auction = Auction.GetById(auctionItem.AuctionId);
                    if (auction != null && auction.EndDate.HasValue && auction.EndDate > DateTime.Now)
                    {
                        var c = new AuctionsController();
                        var b = new AuctionsController.AuctionBatchItemRequest();

                        b.StockNumbers = new[] { impound.Id };

                        await c.Delete(ad.AuctionId.GetValueOrDefault(), b);
                    }
                }
            }

            var en = await Extric.Towbook.Dispatch.Entry.GetByIdNoCacheAsync(impound.DispatchEntry.Id);

            await Extric.Towbook.Dispatch.Entry.UpdateInAzure(en);

            await ProcessQueueHelper.AddFromImpoundIdAsync(impound.Id);

            if (sendPusherUpdate)
            {
                // saving a payment has already sent a pusher event...but since there were no payments, we need to send
                // an update pusher event now
                await PushNotificationProvider.UpdateCall(impound.DispatchEntry.CompanyId, impound.DispatchEntry.Id);
            }

            return new
            {
                PaymentId = paymentId,
                ReleaseDetailsId = releaseDetail.Id
            };
        }

        private static async Task<IEnumerable<InvoicePayment>> RecordPaymentAtTimeOfRelease(
            Impound imp,
            IEnumerable<ReleasePaymentModel> payments,
            User currentUser,
            string ipAddress)
        {
            List<InvoicePayment> ret = new List<InvoicePayment>();

            if (imp == null || payments?.Count() == 0)
                return ret;

            // We allow multiple payments from the web app.  But, we do not allow square terminal payments to be combined
            // with other payments.  Strip out any other payment types other than square. The UI is enforcing this rule but we
            // need to be prudent.
            if (payments.Count() > 1 && payments.Any(c => c.Type == PaymentType.Square))
                payments = payments.Where(w => w.Type == PaymentType.Square);

            foreach (var p in payments)
            {
                var paymentInvoice = new InvoicePayment()
                {
                    InvoiceId = imp.DispatchEntry.Invoice.Id,
                    OwnerUserId = WebGlobal.CurrentUser.Id,
                    PaymentType = PaymentType.GetById(p.Type.Value),
                    Amount = p.Amount,
                    ReferenceNumber = p.Reference
                };
                // TODO: This isn't performant, (n database/network calls on a loop)
                await paymentInvoice.Save(currentUser, ipAddress);

                ret.Add(paymentInvoice);

                #region ImpoundReleasePayment (link payment to the action of release)
                // Prior to Oct/2023, a special delimiter of "[TB:R]" was attached to the end of
                // the reference id of the InvoicePayment object to qualify the payment as a 
                // payment recorded at the time of release.
                // Now, use ImpoundReleasePayment object to link the payment to the release action.
                var irp = new ImpoundReleasePayment()
                {
                    ImpoundId = imp.Id,
                    DispatchEntryPaymentId = paymentInvoice.Id
                };

                // TODO: This isn't performant, (n database/network calls on a loop)
                await irp.SaveAsync();
                #endregion


                #region Handle Square Payment
                if (paymentInvoice.PaymentType == PaymentType.Square && await WebGlobal.CurrentUser.Company.HasFeatureAsync(Generated.Features.PaymentIntegrations_Square))
                {
                    var authorization = await SquareUtils.GetAuthorizationAsync(imp.DispatchEntry.CompanyId);

                    if (authorization == null)
                        throw new AuthorizationMissingException($"Square authorization is missing or invalid for companyId = {imp.Company.Id}.");
                    if (string.IsNullOrEmpty(authorization?.Location?.LocationId))
                        throw new AuthorizationMissingLocationIdException($"Could not find a Square location configured for use with {imp.Company?.ShortName ?? imp.Company?.Name ?? "company " + imp.Company.Id.ToString()}. Please review your Square settings in Towbook and set up a location.");

                    // We've resolving the paymentId first because payments recorded from mobile devices uses Square Reader SDK
                    // and the sdk is returning the OrderId instead the PaymentId, so this method "ResolveSquarePaymentIdFromReferenceNumber()"
                    // wil try to find the paymentId looking for it in "payments" & "orders" api.
                    var referenceNumber = paymentInvoice.ReferenceId;
                    var squarePaymentId = await SquareUtils.ResolveSquarePaymentIdFromReferenceNumber(imp.Company.Id, referenceNumber);

                    if (squarePaymentId != null)
                    {
                        // If the resolved paymentId is different that the stored referenceNumber in InvoicePayment object then that means
                        // it is the OrderId, then we need to fix the referenceNumber saving the resolved paymentId value as the InvoicePayment referenceNumber 
                        if (squarePaymentId != referenceNumber)
                        {
                            paymentInvoice.ReferenceNumber = squarePaymentId;
                            await paymentInvoice.Save();
                        }

                        await SquareUtils.RegisterSquarePayment(paymentInvoice, imp.Company.Id, imp.DispatchEntry.Company.CurrencyCode);
                    }
                    else
                    {
                        throw new SquareApiException("Couldn't find Square payment with reference number = " + referenceNumber);
                    }
                }
                #endregion
            }

            return ret;
        }

        /// <summary>
        /// Handles the voiding and refunding of payments as a result of the unrelease of an impound.
        /// </summary>
        /// <param name="imp">The impound being unreleased</param>
        /// <param name="currentUser"></param>
        /// <param name="ipAddress"></param>
        /// <param name="towOut">If the unrelease is a result of undoing a towout, include the towout object</param>
        /// <returns></returns>
        private static async Task<IEnumerable<InvoicePayment>> VoidPaymentsAtTimeOfUnrelease(Impound imp,
            User currentUser,
            string ipAddress,
            EntryTowOut towOut = null)
        {
            var voidedPayments = new List<InvoicePayment>();

            if (imp == null)
                return voidedPayments;

            // Get all payments of the impound
            var allUnvoidedPayments = (await InvoicePayment.GetByImpoundIdAsync(imp.Id)).Where(w => !w.IsVoid);

            // Get payment ids captured at the time of release
            // TODO: Can be made async
            var releasePayments = ImpoundReleasePayment.GetByImpoundId(imp.Id);

            // Prior to Oct/2023, a special delimiter of "[TB:R]" was attached to the end of
            // the reference id of the InvoicePayment object to qualify the payment as a 
            // payment recorded at the time of release. We need to look for these for historical reasons.
            // Otherwise, include all payments captured at the time of release.
            var paymentInvoices = allUnvoidedPayments.Where(w =>
                    // historical
                    (w.ReferenceNumber != null && w.ReferenceNumber.Contains("[TB:R]")) ||
                    // is part of undoing a towout
                    (towOut != null && w.CreateDate > towOut.CreateDate) ||
                    // payments linked to the release of the impound
                    releasePayments.Select(s => s.DispatchEntryPaymentId).Contains(w.Id))
                .ToList();

            if (paymentInvoices.Any())
            {
                foreach (var payment in paymentInvoices)
                {
                    // refund square payment
                    if (payment.PaymentType == PaymentType.Square &&
                        await WebGlobal.CurrentUser.Company.HasFeatureAsync(Features.PaymentIntegrations_Square) &&
                        // TODO: This is not performant, (n database/network calls on a loop)
                        await SquareUtils.GetAuthorizationAsync(imp.Company.Id) != null)
                    {
                        // TODO: This is not performant, (n database/network calls on a loop)
                        var squarePayment = await SquarePayment.GetByPaymentIdAsync(payment.ReferenceId, imp.Company.Id);

                        if (squarePayment != null && squarePayment.Status == SquarePaymentStatus.Completed)
                        {
                            // refund request to Square api
                            // TODO: This is not performant, (n database/network calls on a loop)
                            await SquareUtils.RefundPayment(imp.Company.Id, imp.DispatchEntry.Id, payment.Id, WebGlobal.CurrentUser.Id);
                        }
                    }

                    // void the payment
                    // TODO: This is not performant, (n database/network calls on a loop)
                    await payment.Void(currentUser, ipAddress);

                    voidedPayments.Add(payment);
                }
            }

            return voidedPayments;
        }

        private static bool ValidateReleaseAgainstRuleSets(ImpoundReleaseModel model, Impound impound, User user, out string message)
        {
            message = string.Empty;

            // TODO: Can be made async
            var ruleSet = ReleaseValidationRuleSet.GetByCompany(impound.Company.Id, impound.Invoice.AccountId ?? impound.DispatchEntry.AccountId);
            var rules = ReleaseRuleSetModel.ValidateRelease(model, impound, ruleSet, user);

            foreach (var r in rules.Where(w => w.IsRequired && !w.Passed))
            {
                switch (r.Name)
                {
                    case "ReleaseMethod":
                        message = "A release type must be specified to release this vehicle (scrapped, tow out, new title, etc).";
                        break;

                    case "ReleaseName":
                        message = "A full name must be provided to release this vehicle.";
                        break;

                    case "ReleasePhone":
                        message = "A phone number must be provided to release this vehicle.";
                        break;

                    case "ReleaseAddress":
                        message = "An address must be provided to release this vehicle.";
                        break;

                    case "ReleaseDriversLicense":
                        message = "A driver's license number must be provided to release this vehicle.";
                        break;

                    case "ReleaseDriversLicenseExpiration":
                        message = "A driver's license expiration date must be provided to release this vehicle.";
                        break;

                    case "ReleaseBirthDate":
                        message = "A birth date must be provided to release this vehicle.";
                        break;

                    case "ReleasePaymentInFull":
                        if (impound.Invoice.BalanceDue > 0)
                        {
                            if (model.PaymentType == null || (model.Payments != null && model.Payments.Select(s => s.Type == null).Any()))
                                message = $"Payment type must be provided to release this vehicle.";
                            else
                                message = $"Payment amount must be greater than zero to release this vehicle.";
                        }
                        break;
                }
            }

            if (!string.IsNullOrEmpty(message))
                return false;

            return true;
        }

        /// <summary>
        /// Unrelease an impound that was released
        /// </summary>
        /// <param name="id">the impound id</param>
        /// <returns>ImpoundReleaeModel</returns>
        [HttpPost]
        [Route("{id}/unrelease")]
        public async Task<HttpResponseMessage> Unrelease(int id)
        {
            if (id < 1)
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest)
                {
                    Content = new StringContent("A proper Id wasn't given. This time you must set the Id in a POST request.")
                });
            }

            var impound = await Impound.GetByIdAsync(id);

            if (impound == null || !await WebGlobal.CurrentUser.HasAccessToCompanyAsync(impound.Company.Id))
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound) { Content = new StringContent("The specified impound either doesn't exist or you don't have access to it.") });

            if (impound.ReleaseDate == null)
                throw new HttpResponseException(new HttpResponseMessage(HttpStatusCode.BadRequest) { Content = new StringContent("This impound is not released.") });

            // TODO: Can be made async
            if (impound.IsWithinClosedAccountingPeriod())
            {
                throw new HttpResponseException(new HttpResponseMessage(Forbidden()) { Content = new StringContent($"This impound ({id}) is locked by closed period settings and cannot be unreleased.") });
            }

            if (impound.DispatchEntry.IsLocked)
            {
                throw new HttpResponseException(new HttpResponseMessage(Forbidden()) { Content = new StringContent($"This impound ({id}) is locked and cannot be unreleased.") });
            }

            try
            {
                // TODO: Can be made async
                var towOut = EntryTowOut.GetByDispatchEntryId(impound.DispatchEntry.Id);

                await Unrelease(impound, WebGlobal.CurrentUser, this.GetCurrentToken(), this.GetRequestingIp(), towOut);
            }
            catch (Exception ex)
            {
                if (ex is SquareApiException sae)
                    return new HttpResponseMessage(HttpStatusCode.BadRequest) { Content = new StringContent(sae.Message) };

                return new HttpResponseMessage(HttpStatusCode.InternalServerError) { Content = new StringContent(ex.Message) };
            }

            return new HttpResponseMessage(HttpStatusCode.OK);
        }

        internal async Task Unrelease(Impound impound,
            User performer = null,
            AuthenticationToken token = null,
            string ipAddress = null,
            EntryTowOut towout = null)
        {
            var hi = new HistoryItem();
            hi.ImpoundId = impound.Id;
            hi.Action = HistoryItem.ActionEnum.Unreleased;
            hi.Data = impound.ReleaseDate.Value.ToShortDateString();
            hi.User = WebGlobal.CurrentUser;

            impound.ReleaseDate = null;
            impound.ReleasePickupDate = null;
            impound.ReleaseReason = null;
            impound.ReleaseNotes = null;
            await impound.Save(performer, token, ipAddress);

            var releaseDetail = await ReleaseDetails.GetByImpoundAsync(impound);
            // TODO: Can be made async
            releaseDetail?.Delete();

            var voidedPayments = await VoidPaymentsAtTimeOfUnrelease(impound, performer, ipAddress, towout);

            // TODO: Can be made async
            hi.Save();

            var en = await Entry.GetByIdNoCacheAsync(impound.DispatchEntry.Id);

            await Entry.UpdateInAzure(en);

            // voiding a payment sends a pusher event...but since there are no payments voided, send now
            if (!voidedPayments.Any())
                await PushNotificationProvider.UpdateCall(impound.DispatchEntry.CompanyId, impound.DispatchEntry.Id);
        }

        [HttpPost]
        [Route("CreateTasks")]
        public async Task<ObjectResult> CreateTasksAsync(int id)
        {
            var imp = await Impound.GetByIdAsync(id);

            if (imp == null || !await WebGlobal.CurrentUser.HasAccessToCompanyAsync(imp.Company.Id))
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound)
                {
                    Content = new StringContent("The specified impound either doesn't exist or you don't have access to it. Did not create any tasks.")
                });
            }

            int createdTasksCount = await ImpoundTask.CreateTasksForImpoundAsync(id, WebGlobal.CurrentUser.CompanyId);
            if (createdTasksCount > 0)
                return StatusCode((int)HttpStatusCode.Created, new { TasksCreated = createdTasksCount });
            //return this.Request.CreateResponse(HttpStatusCode.Created, new { TasksCreated = createdTasksCount }, PerRequestJsonSettingsFormatter.Instance);
            else
                return StatusCode((int)HttpStatusCode.Created, new { TasksCreated = 0 });
            //return this.Request.CreateResponse(HttpStatusCode.OK, new { TasksCreated = 0 }, PerRequestJsonSettingsFormatter.Instance);
        }


        public class ImportRegistrationBody
        {
            public string TransactionType { get; set; }
            public bool? Attestation { get; set; }
        }

        [HttpPost]
        [Route("{id}/importRegistration")]
        public async Task<HttpResponseMessage> ImportRegistration(int id, ImportRegistrationBody body)
        {
            var imp = await Impound.GetByIdAsync(id);

            await ThrowIfNoCompanyAccessAsync(imp?.Company?.Id, "impound");

            var logData = new Dictionary<string, object>()
            {
                { "Vin", imp?.DispatchEntry?.VIN },
                { "ImpoundId", imp?.Id },
                { "TransactionType", body.TransactionType },
                { "Attestation", body.Attestation }
            };

            if (string.IsNullOrEmpty(imp?.DispatchEntry?.VIN) ||
                (imp.DispatchEntry.VIN.Length < 11 && imp.DispatchEntry.VIN.Length > 17))
            {
                return AddUtility.LogAndReturnError(WebGlobal.CurrentUser, "Towbook",
                    new Exception("Missing or Invalid VIN",
                        new Exception("This record does not contain a valid VIN. To lookup owner/lienholder information, please add a valid VIN to the call.")),
                    imp.Company.Id,
                    imp.DispatchEntryId, logData, "ImportRegistration");
            }

            var client = new AutoDataDirectClient();
            var add = await AutoDataDirect.AutoDataDirectConnection.GetByUserIdAsync(imp.Company.Id, WebGlobal.CurrentUser.Id);

            if (add == null)
            {
                return AddUtility.LogAndReturnError(WebGlobal.CurrentUser, "Towbook",
                    new Exception("Not Connected",
                        new Exception("Your user isn't connected to AutoDataDirect. To use this feature, you need to connect your Towbook account AutoDataDirect in Settings.")),
                    imp.Company.Id,
                    imp?.DispatchEntryId, logData, "ImportRegistration");
            }

            var x = new RecordRequest();

            x.ReferenceId = imp.DispatchEntry.CallNumber.ToString();
            x.ReturnType = "XML";
            x.Parameters = new Collection<RecordRequest.ParameterModel>();

            x.AddParameter("exemption", "dppa_7");


            x.Async = "false";

            var vehicleState = "";

            var vt = await VehicleTitle.GetByDispatchEntryIdAsync(imp.DispatchEntry.Id);
            if (!string.IsNullOrWhiteSpace(vt?.TitleJurisdiction))
                vehicleState = vt.TitleJurisdiction?.Trim().ToUpper();

            if (string.IsNullOrWhiteSpace(vehicleState))
                vehicleState = imp.DispatchEntry.LicenseState?.ToUpperInvariant().Trim();

            if (body.TransactionType != "NMVTIS")
            {
                if (!logData.ContainsKey("VehicleState"))
                    logData.Add("VehicleState", vehicleState);

                if (string.IsNullOrWhiteSpace(vehicleState))
                {
                    return AddUtility.LogAndReturnError(WebGlobal.CurrentUser, "AutoDataDirect",
                            new Exception("Error",
                                new Exception("This record doesn't contain a License Plate State or Titling Jurisdiction")),
                        imp.Company.Id, imp.DispatchEntryId, logData, "ImportRegistration");
                }
                else if (vehicleState.Length != 2)
                {
                    return AddUtility.LogAndReturnError(WebGlobal.CurrentUser, "AutoDataDirect",
                        new Exception("Error",
                            new Exception("This record contains an invalid License Plate State or Titling Jurisdiction: it must be two characters.")),
                        imp.Company.Id, imp.DispatchEntryId, logData, "ImportRegistration");
                }
            }


            if (vehicleState == "NY")
            {
                x.AddParameter("case-number", imp.DispatchEntry.CallNumber.ToString());
            }
            else if (vehicleState == "MO" || body.TransactionType == "MOVIN")
            {
                x.AddParameter("year", imp.DispatchEntry.Year.ToString());

                var ncic = Vehicle.VehicleUtility.GetNcicByName(imp.DispatchEntry.VehicleMake);

                if (!logData.ContainsKey("VehicleMake"))
                    logData.Add("VehicleMake", ncic);

                if (!string.IsNullOrEmpty(ncic))
                    x.AddParameter("make", ncic);
                else
                    return AddUtility.LogAndReturnError(WebGlobal.CurrentUser, "AutoDataDirect",
                        new Exception("Error",
                            new Exception("Towbook doesn't contain a NCIC Make Code for the manufacturer: " + imp.DispatchEntry.VehicleMake)),
                        imp.Company.Id, imp.DispatchEntryId, logData, "ImportRegistration");
            }
            else if (vehicleState == "NC" || body.TransactionType == "NCVIN")
            {
                // add impoundId as tow-ticket parameter. Required by Towers to perform queries.
                x.AddParameter("tow-ticket", imp.Id.ToString());
            }

            if (body.TransactionType == "NMVTIS")
                body.TransactionType = "nmvtisppi";

            if (body.TransactionType != null)
                x.TransactionType = body.TransactionType;
            else
                x.TransactionType = vehicleState + "VIN";

            if (x.TransactionType == "NMVIN")
            {
                // New Mexico lookups require we get attestation from user per lookup
                if (body.Attestation == true)
                    x.AddParameter("sb36", "true");
            }

            // test companies - force to use mock lookup requests.
            if (new int[] { 10000, 151676, 147267 }.Contains(imp.Company.Id) || AutoDataHelper.IsMockVin(imp.DispatchEntry.VIN))
            {
                x.TransactionType = "MOCK_VIN";

                if (imp.DispatchEntry.VIN == "2B3HD56J7XH514123" && body.TransactionType == "nmvtisppi")
                    x.TransactionType = "nmvtisppi";

                if (AutoDataHelper.IsMockVin(imp.DispatchEntry.VIN))
                    x.AddParameter("key", imp.DispatchEntry.VIN);
                else
                    x.AddParameter("key", "2B3HD56J7XH514123");
            }
            else
                x.AddParameter("key", imp.DispatchEntry.VIN);

            if (x.TransactionType == null)
                throw new Exception("must pass TransactionType");

            var requestXml = x.ToXml();
            var cfJson = new CompanyFile()
            {
                CompanyId = imp.Company.Id,
                DispatchEntries = new Collection<int> { imp.DispatchEntry.Id },
                Filename = "internal_lookup_request.xml",
                RawUrl = "internal_lookup_request.xml",
                OwnerUserId = WebGlobal.CurrentUser.Id,
                Description = "ADD",
                Size = requestXml.Length
            };
            // TODO: Can be made async
            cfJson.Save();

            await FileUtility.SendFileWithString(cfJson.LocalLocation, requestXml);
            string response = string.Empty;

            try
            {
                // TODO: Can be made async
                response = client.UqlRequestWithOAuthTokenRefresh(add, requestXml);

                var cfJson2 = new CompanyFile()
                {
                    CompanyId = imp.Company.Id,
                    DispatchEntries = new Collection<int> { imp.DispatchEntry.Id },
                    Filename = "internal_lookup_response.xml",
                    RawUrl = "internal_lookup_response.xml",
                    OwnerUserId = WebGlobal.CurrentUser.Id,
                    Description = "ADD",
                    Size = response.Length
                };
                // TODO: Can be made async
                cfJson2.Save();
                await FileUtility.SendFileWithString(cfJson2.LocalLocation, response);

                var responseModel = RecordResponse.FromXml(response);

                if (body.TransactionType == "nmvtisppi")
                {
                    var ppi = responseModel.Payloads.FirstOrDefault()?.NmvtisPpiRecord;

                    if (responseModel.TransactionStatus == "NOT_FOUND")
                    {
                        return AddUtility.LogAndReturnError(WebGlobal.CurrentUser,
                            "AutoDataDirect",
                            new Exception((ppi?.recordtitle ?? body.TransactionType) + " - No Result Found",
                                new Exception("AutoDataDirect returned NOT_FOUND for the requested VIN.")),
                            imp.Company.Id, imp.DispatchEntry?.Id, logData, "ImportRegistration");
                    }

                    var x2 = new RecordRequest();
                    x2.ReturnType = "PDF";
                    x2.TransactionId = responseModel.TransactionUuid;

                    // TODO: Can be made async
                    var pdfResponseModel = RecordAsyncResponse.FromXml(client.UqlRequestWithOAuthTokenRefresh(add, x2.ToXml()));
                    var pdf = Convert.FromBase64String(pdfResponseModel.Payload);

                    var cfJson3 = new CompanyFile()
                    {
                        CompanyId = imp.Company.Id,
                        DispatchEntries = new Collection<int> { imp.DispatchEntry.Id },
                        Filename = "nmvtis.pdf",
                        RawUrl = "nmvtis-" + imp.DispatchEntry.VIN.ToLower() + ".pdf",
                        OwnerUserId = WebGlobal.CurrentUser.Id,
                        Description = "NMVTIS Title History Report",
                        Size = pdf.Length
                    };
                    // TODO: Can be made async
                    cfJson3.Save();
                    await FileUtility.SendFile(cfJson3.LocalLocation, pdf, "application/pdf");


                    // Get 1 page version record request
                    var x3 = new RecordRequest();
                    x3.ReturnType = "PDF";
                    x3.TransactionId = responseModel.TransactionUuid;
                    x3.Template = "corporate";

                    // TODO: Can be made async
                    pdfResponseModel = RecordAsyncResponse.FromXml(client.UqlRequestWithOAuthTokenRefresh(add, x3.ToXml()));

                    if (!string.IsNullOrEmpty(pdfResponseModel.Payload))
                    {
                        pdf = Convert.FromBase64String(pdfResponseModel.Payload);

                        var cfJson4 = new CompanyFile()
                        {
                            CompanyId = imp.Company.Id,
                            DispatchEntries = new Collection<int> { imp.DispatchEntry.Id },
                            Filename = "nmvtis-corporate.pdf",
                            RawUrl = "nmvtis-" + imp.DispatchEntry.VIN.ToLower() + "-corporate.pdf",
                            OwnerUserId = WebGlobal.CurrentUser.Id,
                            Description = "NMVTIS Corporate Title History Report",
                            Size = pdf.Length
                        };
                        // TODO: Can be made async
                        cfJson4.Save();
                        await FileUtility.SendFile(cfJson4.LocalLocation, pdf, "application/pdf");
                    }


                    bool saveVehicleTitle = false;
                    bool saveImpound = false;

                    var titlingJurisdition = ppi?.VinPointerRecords?.FirstOrDefault()?.TitlingJurisdiction;
                    var brandCodeName = ppi?.vehiclebrands?.Brands?.FirstOrDefault()?.BrandCodeName;


                    if (vt == null &&
                        (!string.IsNullOrWhiteSpace(titlingJurisdition) ||
                        !string.IsNullOrWhiteSpace(brandCodeName)))
                    {
                        vt = new VehicleTitle()
                        {
                            DispatchEntryId = imp.DispatchEntry.Id,
                            ImpoundId = imp.Id
                        };
                    }

                    if (!string.IsNullOrWhiteSpace(titlingJurisdition) && titlingJurisdition != vt.TitleJurisdiction)
                    {
                        vt.TitleJurisdiction = titlingJurisdition;
                        saveVehicleTitle = true;
                    }

                    if (!string.IsNullOrWhiteSpace(brandCodeName) && brandCodeName != vt.TitleBrand)
                    {
                        vt.TitleBrand = brandCodeName;
                        saveVehicleTitle = true;
                    }

                    if (saveVehicleTitle)
                        await vt.Save(this.GetCurrentToken(), this.GetRequestingIp());

                    #region Stolen Records
                    if (ppi?.StolenRecords?.Count() > 0 && imp.DispatchEntry?.Assets?.Count() > 0)
                    {
                        var attr = imp.DispatchEntry.Attributes?.FirstOrDefault(f => f.Key == Dispatch.AttributeValue.BUILTIN_VEHICLE_STOLEN_RECORD_JSON).Value;

                        // Remove the attribute record on the call if no stolen records are active
                        // Could happen on a second lookup.
                        if (attr != null && !ppi.StolenRecords.Any(a => a.Status == "Active"))
                        {
                            await attr.Delete();
                            imp.DispatchEntry.Attributes.Remove(Dispatch.AttributeValue.BUILTIN_VEHICLE_STOLEN_RECORD_JSON);

                            saveImpound = true;
                        }

                        // retrieve all records stored in the db.
                        var stolenRecords = await VehicleStolenRecord.GetByImpoundIdAsync(imp.Id);

                        foreach (var sr in ppi.StolenRecords.OrderBy(o => o.IncidentDate))
                        {

                            var updateDate = DateTime.Now;

                            // Match by date, vin and state in order to allow status updates.
                            // Or, create new stolen record.
                            var vsr = stolenRecords?.FirstOrDefault(f =>
                                        f.Source == "ADD" &&
                                        f.IncidentDate == sr.IncidentDate &&
                                        f.Vin == sr.Vin &&
                                        f.State == sr.State) ??
                                    new VehicleStolenRecord()
                                    {
                                        DispatchEntryId = imp.DispatchEntry.Id,
                                        ImpoundId = imp.Id,
                                        AssetId = imp.DispatchEntry.Assets.First().Id,
                                        Source = "ADD",
                                        CreateDate = updateDate,
                                        Status = StolenStatusType.Unknown
                                    };

                            vsr.SourceId = ppi.recorduuid;
                            vsr.LastUpdateDate = updateDate;
                            vsr.IncidentDate = sr.IncidentDate;
                            vsr.State = sr.State;
                            vsr.Vin = sr.Vin;

                            // update to current status
                            if (sr.Status.ToLowerInvariant() == "active")
                                vsr.Status = StolenStatusType.Active;
                            else if (sr.Status.ToLowerInvariant() == "closed")
                                vsr.Status = StolenStatusType.Closed;

                            await vsr.Save();



                            if (vsr.Status == StolenStatusType.Active)
                            {
                                // mark imp as police hold
                                if (!imp.Hold)
                                {
                                    imp.Hold = true;
                                    saveImpound = true;
                                }

                                var stolenMessage = $"On {sr.IncidentDate.ToShortDate()}, an ACTIVE stolen vehicle record was placed on this vehicle";

                                // add to call notes (if not there already)
                                if (!imp.DispatchEntry.Notes.Contains("an ACTIVE stolen vehicle record was placed on this vehicle"))
                                {
                                    imp.DispatchEntry.Notes = $"{stolenMessage} (see NMVTIS Title History Report).\n" + imp.DispatchEntry.Notes;
                                    saveImpound = true;
                                }

                                var val = new StolenVehicleRecordModel()
                                {
                                    Id = vsr.Id,
                                    Status = vsr.Status.ToString().ToUpperInvariant(),
                                    Description = stolenMessage
                                };

                                // set attribute of active stolen record found
                                if (attr == null)
                                {
                                    attr = new Dispatch.AttributeValue()
                                    {
                                        DispatchEntryId = imp.DispatchEntry.Id,
                                        DispatchEntryAttributeId = Dispatch.AttributeValue.BUILTIN_VEHICLE_STOLEN_RECORD_JSON,
                                        Value = val.ToJson()
                                    };

                                    imp.DispatchEntry.Attributes.Add(Dispatch.AttributeValue.BUILTIN_VEHICLE_STOLEN_RECORD_JSON, attr);
                                }
                                else
                                {
                                    attr.Value = val.ToJson();
                                }

                                await attr.SaveAsync();
                            }

                        }
                    }
                    #endregion

                    if (saveImpound)
                    {
                        // AutoDataDirect user id is 24
                        // TODO: Can be made async
                        var addUser = await Towbook.User.GetByIdAsync(24);
                        await imp.Save(addUser, null, null);
                    }
                }
                else
                {
                    if (responseModel.TransactionStatus == "NOT_FOUND")
                    {
                        return AddUtility.LogAndReturnError(WebGlobal.CurrentUser,
                            "AutoDataDirect",
                            new Exception(responseModel.Payloads.FirstOrDefault()?.Vehicle?.RecordTitle + " - No Result Found",
                                new Exception("AutoDataDirect returned NOT_FOUND for the requested VIN.")),
                            imp.Company.Id, imp.DispatchEntry?.Id, logData, "ImportRegistration");
                    }

                    // retrieve pdf copy and store as a file with the impound
                    if (!string.IsNullOrEmpty(responseModel.TransactionUuid) &&
                        !string.IsNullOrWhiteSpace(body.TransactionType))
                    {
                        var x3 = new RecordRequest();
                        x3.ReturnType = "PDF";
                        x3.RetrieveParameters = new Collection<RecordRequest.RetrieveParameterModel>();
                        x3.AddRetrieveParameter("pdf-render-engine", "athena");
                        x3.TransactionId = responseModel.TransactionUuid;

                        // TODO: Can be made async
                        var pdfResponseModel = RecordAsyncResponse.FromXml(client.UqlRequestWithOAuthTokenRefresh(add, x3.ToXml()));

                        if (!string.IsNullOrEmpty(pdfResponseModel.Payload))
                        {
                            var pdf = Convert.FromBase64String(pdfResponseModel.Payload);

                            var cfJson4 = new CompanyFile()
                            {
                                CompanyId = imp.Company.Id,
                                DispatchEntries = new Collection<int> { imp.DispatchEntry.Id },
                                Filename = body.TransactionType + ".pdf",
                                RawUrl = body.TransactionType + "-" + imp.DispatchEntry.VIN.ToLower() + ".pdf",
                                OwnerUserId = WebGlobal.CurrentUser.Id,
                                Description = body.TransactionType + " Title History Report",
                                Size = response.Length
                            };
                            // TODO: Can be made async
                            cfJson4.Save();
                            await FileUtility.SendFile(cfJson4.LocalLocation, pdf, "application/pdf");
                        }
                    }

                    #region Interested Parties
                    var newContacts = AutoDataHelper.MapVehicleRegistrantsToEntryContacts(responseModel.Payloads?.Where(w => w.Vehicle != null)?.SelectMany(s => s.Vehicle?.InterestedParties), imp.DispatchEntry.Contacts);
                    foreach (var ec in newContacts)
                        imp.DispatchEntry.Contacts.Add(ec);
                    #endregion
                }

                await imp.DispatchEntry.Save(false, this.GetCurrentToken(), this.GetRequestingIp());
            }
            catch (Exception e)
            {
                return AddUtility.LogAndReturnError(WebGlobal.CurrentUser,
                    "AutoDataDirect", e, imp.Company.Id, imp.DispatchEntry?.Id, logData, "ImportRegistration");
            }

            AddUtility.LogInfo(
                imp.Company.Id,
                "ImportRegistration",
                body.TransactionType,
                x.ToJson(),
                response,
                imp.DispatchEntry.Id,
                WebGlobal.CurrentUser,
                null,
                vehicleState);

            return new HttpResponseMessage(HttpStatusCode.OK);
        }


        public class SendLetterModel
        {
            public int LetterTemplateId { get; set; }

            public DateTime MailOutDate { get; set; }

            public int[] Recipients { get; set; }

            public int MailType { get; set; }
        }

        [HttpPost]
        [Route("{id}/sendLetter")]
        public async Task<HttpResponseMessage> SendLetter(int id, SendLetterModel slm)
        {
            var imp = await Impound.GetByIdAsync(id);

            await ThrowIfNoCompanyAccessAsync(imp?.Company?.Id, "impound");

            if (slm == null)
                return AddUtility.ReturnError(
                    "Towbook",
                     "Error",
                    "Missing POST body.");

            var logData = new Dictionary<string, object>()
            {
                { "Vin", imp?.DispatchEntry?.VIN },
                { "ImpoundId", imp?.Id },
                { "MailType", slm.MailType },
                { "LetterTemplateId", slm.LetterTemplateId },
                { "Recipients", slm.Recipients.Any() ? string.Join(", ", slm.Recipients) : string.Empty },
                { "MailOutDate", slm.MailOutDate }
            };

            if (string.IsNullOrEmpty(imp?.DispatchEntry?.VIN) ||
                imp.DispatchEntry.VIN.Length < 11 || imp.DispatchEntry.VIN.Length > 17)
            {
                return AddUtility.LogAndReturnError(WebGlobal.CurrentUser, "Towbook",
                    new Exception("Missing or Invalid VIN",
                        new Exception("This record does not contain a valid VIN. To send letters, a valid VIN must be a part of the impound record and at least 11 - 17 digits.")),
                    imp.Company.Id,
                    imp.DispatchEntryId,
                    logData,
                    "SendLetter");
            }

            if (LetterTemplate.GetById(new int[] { (int)imp?.Company?.Id }, slm.LetterTemplateId) == null)
            {
                return AddUtility.LogAndReturnError(WebGlobal.CurrentUser, "Towbook",
                   new Exception("Missing or Invalid Letter",
                       new Exception("The Letter Template you specified is invalid")),
                    imp.Company.Id,
                    imp.DispatchEntryId,
                    logData,
                    "SendLetter");
            }

            // TODO take into account weekends/holidays.
            if (slm.MailOutDate.Date < DateTime.Now.Date)
            {
                return AddUtility.LogAndReturnError(WebGlobal.CurrentUser, "Towbook",
                      new Exception("Invalid Mail Out Date",
                        new Exception("The earliest you can send a letter out is tomorrow")),
                      imp.Company.Id,
                      imp.DispatchEntryId,
                      logData,
                      "SendLetter");
            }

            // 1/27/2025 Stop allowing North Carolina to mail through AutoDataDirect (for now)
            if (imp?.Company.State == "NC")
            {
                return AddUtility.LogAndReturnError(WebGlobal.CurrentUser, "Towbook",
                      new Exception("DPO service disabled",
                        new Exception("North Carolina companies cannot send letters via AutoDataDirect.")),
                      imp.Company.Id,
                      imp.DispatchEntryId,
                      logData,
                      "SendLetter");
            }

            /**
             * Only allow Tennessee companies to send with ADD until June 28, 2024
             * https://github.com/towbook/Towbook/issues/3806
             */
            if (slm.MailOutDate > new DateTime(2024, 06, 28) && imp?.Company.State == "TN")
            {
                return AddUtility.LogAndReturnError(WebGlobal.CurrentUser, "Towbook",
                      new Exception("Invalid Mail Out Date",
                        new Exception("Tennessee companies cannot send letters via AutoDataDirect after July 28, 2024.")),
                      imp.Company.Id,
                      imp.DispatchEntryId,
                      logData,
                      "SendLetter");
            }

            if (slm.Recipients == null || !slm.Recipients.Any())
            {
                return AddUtility.LogAndReturnError(WebGlobal.CurrentUser, "Towbook",
                      new Exception("Invalid Request",
                        new Exception("You need to specify at least one recipient")),
                      imp.Company.Id,
                      imp.DispatchEntryId,
                      logData,
                      "SendLetter");
            }

            List<EntryContact> destinationContacts = new List<EntryContact>();


            foreach (var recipient in slm.Recipients)
            {
                var contact = imp.DispatchEntry.Contacts.FirstOrDefault(i => i.Id == recipient);
                if (contact == null)
                {
                    return AddUtility.LogAndReturnError(WebGlobal.CurrentUser, "Towbook",
                      new Exception("Invalid Contact",
                        new Exception("Invalid Recipient ContactId")),
                      imp.Company.Id,
                      imp.DispatchEntryId,
                      logData,
                      "SendLetter");
                }

                if (string.IsNullOrWhiteSpace(contact.Name))
                {
                    return AddUtility.LogAndReturnError(WebGlobal.CurrentUser, "Towbook",
                      new Exception("Invalid Contact",
                        new Exception("Invalid Recipient Name")),
                      imp.Company.Id,
                      imp.DispatchEntryId,
                      logData,
                      "SendLetter");
                }

                if (string.IsNullOrWhiteSpace(contact.State) ||
                    string.IsNullOrWhiteSpace(contact.City) ||
                    string.IsNullOrWhiteSpace(contact.Zip) ||
                    string.IsNullOrWhiteSpace(contact.Address))
                {
                    return AddUtility.LogAndReturnError(WebGlobal.CurrentUser, "Towbook",
                      new Exception("Invalid Contact",
                        new Exception("Invalid Address")),
                      imp.Company.Id,
                      imp.DispatchEntryId,
                      logData,
                      "SendLetter");
                }

                destinationContacts.Add(contact);
            }

            var client = new AutoDataDirectClient();

            var add = await AutoDataDirect.AutoDataDirectConnection.GetByUserIdAsync(imp.Company.Id, WebGlobal.CurrentUser.Id);

            if (add == null)
                return new HttpResponseMessage(HttpStatusCode.BadRequest)
                {
                    Content = new StringContent(new
                    {
                        source = "Towbook",
                        title = "Not Connected",
                        error = "Your user isn't connected to AutoDataDirect. To use this feature, you need to connect your Towbook account AutoDataDirect in Settings."
                    }.ToJson(), Encoding.UTF8, "application/json")
                };

            if (imp.Company?.Name != null && imp.Company.Name.Length > 60)
                return AddUtility.LogAndReturnError(WebGlobal.CurrentUser, "Towbook",
                      new Exception("Invalid Company Name",
                        new Exception("Your company name is over 60 characters in length. Please update your company settings to make your name shorter and try again.")),
                      imp.Company.Id,
                      imp.DispatchEntryId,
                      logData,
                      "SendLetter");

            var companyAddress = imp.Company.Address;
            var companyCity = imp.Company.City;
            var companyState = imp.Company.State;
            var companyZip = imp.Company.Zip;

            // TODO: Can be made async
            var billingAddress = AddressBookEntry.GetByName("Billing Address", imp.Company.Id, false).FirstOrDefault();
            if (billingAddress != null)
            {
                companyAddress = billingAddress.Address;
                companyCity = billingAddress.City;
                companyState = billingAddress.State;
                companyZip = billingAddress.Zip;
            }

            if (string.IsNullOrEmpty(companyZip) ||
                string.IsNullOrEmpty(companyState) ||
                string.IsNullOrEmpty(companyCity) ||
                string.IsNullOrEmpty(companyAddress))
            {
                var msg = "Your company address is missing a street address, city, state or zip code.";
                if (billingAddress != null)
                    msg = "The billing address in the address book is missing a street address, city, state or zip code.  Please check your billing address in the settings.";

                return AddUtility.LogAndReturnError(WebGlobal.CurrentUser, "Towbook",
                      new Exception("Invalid Return Address",
                        new Exception(msg)),
                      imp.Company.Id,
                      imp.DispatchEntryId,
                      logData,
                      "SendLetter");
            }

            string companyZip4 = null;
            if (companyZip.Contains("-"))
            {
                var split = companyZip.Split('-');
                companyZip = split[0];
                companyZip4 = split[1];
            }

            var x = (new DpoPacket());

            byte[] array = null;
            Dictionary<int, string> pdfs = new Dictionary<int, string>();

            var isLegalPrintSize = false;

            if ((DpoLetterType)slm.MailType == DpoLetterType.CertifiedMailBusinessReplySelfMailerWithUspsReturnReceiptElectronic /*DPOCMSMER*/)
            {
                isLegalPrintSize = true;
            }

            foreach (var n in slm.Recipients)
            {
                // TODO: Can be made async
                using (var stream = Extric.Towbook.WebShared.WebGlobal.GetResponseFromUrlAsStream(
                     $"{_domain}/impounds/lettertemplate.aspx?pdf=1&impoundId=" +
                     id + "&id=" + slm.LetterTemplateId + "&contactId=" + n +
                     (isLegalPrintSize ? "&legal=true" : "") + "&flatten=true", out var result, addDomain:false, cookieDomain: 
                     WebGlobal.CookieDomain))
                {
                    using (var memoryStream = new MemoryStream())
                    {
                        await stream.CopyToAsync(memoryStream);
                        array = memoryStream.ToArray();

                        var fname = $"ADD_SendLetter_{slm.LetterTemplateId}_" + n + "_Copy.pdf";

                        var copyFile = new CompanyFile()
                        {
                            CompanyId = imp.Company.Id,
                            DispatchEntries = new Collection<int> { imp.DispatchEntry.Id },
                            Filename = fname,
                            RawUrl = fname,
                            Size = array.Length,
                            OwnerUserId = WebGlobal.CurrentUser.Id,
                            Description = "Copy of letter send via AutoDataDirect for contactId " + n
                        };

                        // TODO: Can be made async
                        copyFile.Save();

                        string fn = copyFile.LocalLocation;

                        if (!Directory.Exists(Path.GetDirectoryName(fn)))
                        {
                            Directory.CreateDirectory(Path.GetDirectoryName(fn));
                        }

                        using (var fileStream = System.IO.File.Create(copyFile.LocalLocation))
                        {
                            memoryStream.Seek(0, SeekOrigin.Begin);
                            await memoryStream.CopyToAsync(fileStream);
                        }

                        if (System.IO.File.Exists(copyFile.LocalLocation))
                        {
                            var uploadResult = await FileUtility.SendFileAsync(copyFile.LocalLocation);
                            if (uploadResult.IsHttpSuccess())
                                System.IO.File.Delete(copyFile.LocalLocation);
                        }

                        pdfs[n] = Convert.ToBase64String(array);
                    }
                }

            }

            var vin = imp.DispatchEntry.VIN;

            x.Auth = new DpoAuth(DpoEnvironment.Production);

            if (imp.Company.Id == 10000)
            {
                x.Auth.RunLevel = DpoEnvironment.Testing;
            }

            foreach (var contact in destinationContacts)
            {
                var zip = contact.Zip;
                string zip4 = null;
                if (zip.Contains("-"))
                {
                    var split = zip.Split('-');
                    zip4 = split[1];
                    zip = split[0];
                }
                var letter = new DpoLetter()
                {
                    LetterType = (DpoLetterType)slm.MailType,
                    ExternalLetterText = "VIN:" + "XXXXXXXXX" + vin.Substring(17 - 8),
                    MailFromCity = companyCity,
                    MailFromState = companyState,
                    MailFromZip = companyZip,
                    MailFromZip4 = companyZip4,
                    MailFromName = imp.Company.Name,
                    MailFromAddress1 = companyAddress,
                    MailFromAddress2 = "",
                    MailOn = slm.MailOutDate.ToString("yyyy-MM-dd", CultureInfo.InvariantCulture),
                    MailToAddress1 = contact.Address,
                    MailToAddress2 = "",
                    MailToCity = contact.City,
                    MailToName = contact.Name,
                    MailToState = contact.State,
                    MailToZip = zip,
                    MailToZip4 = zip4,
                    PdfBase64 = pdfs[contact.Id]
                };

                if (WebGlobal.CurrentUser.Company.State == "FL") // florida specific stuff
                {
                    letter.LetterData.Add(new LetterData(LetterDataKey.LetterType, ""));
                    letter.LetterData.Add(new LetterData(LetterDataKey.ExtendedEnvelopeDetail,
                        Core.FormatPhoneWithDashesOnly(imp.Company.Phone)));

                    // owner, lienholder, etc.
                    letter.LetterData.Add(new LetterData(LetterDataKey.RecipientType, ""));
                }
                x.DpoLetter.Add(letter);
            }

            x.IndexKey = vin; //ADD recommends using the VIN for this.
            x.GroupUuid = Guid.NewGuid().ToString("D");
            x.UserTransUuid = Guid.NewGuid().ToString("D"); // Id representing the transaction in towbook
            x.UserIndexKey = id.ToString();

            if (WebGlobal.CurrentUser.Company.State == "FL") // florida specific stuff
            {
                x.PacketData.Add(new PacketData(PacketDataKey.Vin, vin));
                x.PacketData.Add(new PacketData(PacketDataKey.AmountDue, imp.InvoiceTotal.ToString("0.00")));
                //x.PacketData.Add(new PacketData(PacketDataKey.City, "Sarasota"));
                x.PacketData.Add(new PacketData(PacketDataKey.TowDate, imp.ImpoundDate.Value.ToShortDate()));
            }

            var cfJson = new CompanyFile()
            {
                CompanyId = imp.Company.Id,
                DispatchEntries = new Collection<int> { imp.DispatchEntry.Id },
                Filename = "internal_request.xml",
                RawUrl = "internal_request.xml",
                OwnerUserId = WebGlobal.CurrentUser.Id,
                Description = "ADD"
            };

            // TODO: Can be made async
            cfJson.Save();

            await FileUtility.SendFileWithString(cfJson.LocalLocation, x.ToXml());
            string responseXml = string.Empty;
            try
            {
                // TODO: Can be made async
                responseXml = client.DpoWithOAuthTokenRefresh(add, x.ToXml());

                var responseFile = new CompanyFile()
                {
                    CompanyId = imp.Company.Id,
                    DispatchEntries = new Collection<int> { imp.DispatchEntry.Id },
                    Filename = "internal_response.xml",
                    RawUrl = "internal_response.xml",
                    OwnerUserId = WebGlobal.CurrentUser.Id,
                    Description = "ADD"
                };

                // TODO: Can be made async
                responseFile.Save();
                await FileUtility.SendFileWithString(responseFile.LocalLocation, responseXml);
            }
            catch (Exception e)
            {
                if (e is DpoLetterRequestException ae)
                    ae.LetterTemplateId = slm.LetterTemplateId;
                var props = new Dictionary<string, object>();
                props.Add("isLegalPrintSize", isLegalPrintSize);
                props.Add("letterType", x.DpoLetter?.FirstOrDefault().LetterType.ToString());
                props.Add("letterTypeId", x.DpoLetter?.FirstOrDefault().LetterType);
                props.Add("impoundId", imp.Id);
                props.Add("vin", vin);
                props.Add("payload", slm);
                props.Add("requestXml", x.ToXml());


                var responseFile = new CompanyFile()
                {
                    CompanyId = imp.Company.Id,
                    DispatchEntries = new Collection<int> { imp.DispatchEntry.Id },
                    Filename = "internal_response_dpo_error.xml",
                    RawUrl = "internal_response_dpo_error.xml",
                    OwnerUserId = WebGlobal.CurrentUser.Id,
                    Description = "ADD:" + e.Message
                };

                var errorResponse = new ErrorResponse()
                {
                    Source = e.Source.Contains("add123") || e.Source.Contains("dpo") ? "AutoDataDirect" : "Towbook",
                    Message = e.Message,
                    Cause = new ErrorResponse.CauseModel()
                    {
                        Message = e.ToJson(true)
                    }
                };

                // TODO: Can be made async
                responseFile.Save();
                await FileUtility.SendFileWithString(responseFile.LocalLocation, errorResponse.ToXml());

                return AddUtility.LogAndReturnError(WebGlobal.CurrentUser, "AutoDataDirect",
                    new Exception("Error", e),
                    imp.Company.Id,
                    imp.DispatchEntryId,
                    props,
                    "SendLetter");
            }

            AddUtility.LogInfo(
                imp.Company.Id,
                "SendLetter",
                ((DpoLetterType)slm.MailType).ToString(),
                x.ToJson(),
                responseXml,
                imp.DispatchEntry.Id,
                WebGlobal.CurrentUser,
                slm.LetterTemplateId);

            return new HttpResponseMessage(HttpStatusCode.OK);
        }

        [HttpGet]
        [Route("Invoice")]
        public async Task<string> InvoiceAsync(int id, [FromQuery] string format, [FromQuery] int width)
        {
            try
            {
                var imp = await Impound.GetByIdAsync(id);
                await ThrowIfNoCompanyAccessAsync(imp?.Company?.Id, "impound");

                string stringToReturn = "Format Value Incorrect";

                if (imp == null)
                {
                    throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound)
                    {
                        Content = new StringContent("The specified impound either doesn't exist or you don't have access to it.")
                    });
                }
                if (format.ToLower() == "zpl")
                {
                    stringToReturn = await GenerateInvoiceZPLAsync(imp, width);
                }
                return stringToReturn;
            }
            catch (Exception e)
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.InternalServerError) { Content = new StringContent(e.ToString()) });
            }
        }

        public class EmailInput
        {
            public string[] emails { get; set; }
            public int[] files { get; set; }
            public bool HidePricing { get; set; }
            public bool hideDiscounts { get; set; }
            public bool HidePhotos { get; set; }
            public bool HideLineItems { get; set; }
            public int[] DamageFormIds { get; set; }
            public string optionalMessage { get; set; }
            /// <summary>
            /// If payment vendor is connected, include a link to make online payment
            /// </summary>
            public bool? IncludePaymentLink { get; set; }
            /// <summary>
            /// Multiple 'To' receipients will be transparent to all recipients.
            /// Default is to send an individul email copy to each recipient separately
            /// and not exposing the other receipients.
            /// </summary>
            public bool? ChainRecipients { get; set; }
        }

        [HttpPost]
        [Route("{id}/Email")]
        public async Task<HttpResponseMessage> Email(int id, EmailInput emailInput)
        {
            List<string> notifications = new List<string>();

            if (emailInput.ChainRecipients.GetValueOrDefault() && emailInput.emails.Count() > 1)
                notifications.Add(string.Join(",", emailInput.emails));
            else
                notifications.AddRange(emailInput.emails);

            var imp = await Impound.GetByIdAsync(id);

            if (imp == null || !WebGlobal.CurrentUser.HasAccessToCompany(imp.Company.Id))
            {
                throw new Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.Forbidden)
                {
                    Content = new StringContent("You don't have access to this impound or it doesn't exist.")
                });
            }

            if (notifications.Count > 0)
            {
                string output = "";
                bool hidePrices = false;
                var callModel = await CallModel.MapAsync(imp.DispatchEntry);
                if (callModel != null)
                    // TODO: Can be made async
                    hidePrices = await callModel.ShouldBlockChargesAsync();
                if (hidePrices)
                    emailInput.HidePricing = true;

                foreach (string xemail in notifications)
                {
                    string possiblePhoneNumber = xemail.Replace(" ", "").Replace("-", "").Replace("(", "").Replace(")", "").Replace(".", "");

                    if (possiblePhoneNumber.Length == 10)
                    {
                        string url = $"{_domain}/PublicAccess/Impound.aspx?id=" + id;
                        // TODO: Can be made async
                        string md5 = Core.ProtectId(imp.DispatchEntry.Id, imp.DispatchEntry.OwnerUserId);

                        url += "&sc=" + md5;

                        var entryWebLink = await CallsController.GetPaymentLinkUrl(imp.DispatchEntry, emailInput.HidePricing, emailInput.hideDiscounts, !emailInput.HidePhotos, emailInput.IncludePaymentLink.GetValueOrDefault());
                        if (entryWebLink != null)
                        {
                            url = entryWebLink.TinyUrl;
                        }

                        if (long.TryParse(possiblePhoneNumber, out long phoneNumber))
                        {
                            if (emailInput.hideDiscounts)
                                // TODO: Can be made async
                                url += "&d=" + Core.ProtectId(imp.DispatchEntry.CallNumber, 1);
                            if (emailInput.HidePricing)
                                // TODO: Can be made async
                                url += "&p=" + Core.ProtectId(imp.DispatchEntry.CallNumber, 1);

                            var message = "View a copy of your recent impound invoice from " +
                                imp.Company.Name + ", at:\n" + url;

                            // don't include if its >= 320 characters.
                            if (!string.IsNullOrWhiteSpace(emailInput.optionalMessage) && emailInput.optionalMessage.Length < 320)
                                message += "\n\n" + emailInput.optionalMessage;

                            await DispatchNotificationMessage.SendAsync(WebGlobal.CurrentUser, possiblePhoneNumber, message);

                            var cf = new CompanyFile();

                            cf.DispatchEntries.Add(imp.DispatchEntryId);
                            cf.Filename = "Sent_SMS_" + DateTime.Now.ToFileTimeUtc() + "_" + possiblePhoneNumber + ".txt";
                            cf.Size = message.Length;
                            cf.CompanyId = imp.Company.Id;
                            cf.Description = "Copy of Sent SMS";
                            cf.OwnerUserId = WebGlobal.CurrentUser.Id;
                            cf.RawUrl = cf.Filename;
                            // TODO: Can be made async
                            cf.Save(true);

                            await FileUtility.SendFileWithString(cf.LocalLocation, message);
                        }

                        output += "Sent SMS Text Message to " + WebGlobal.FormatPhone(possiblePhoneNumber) + "\n";

                        continue;
                    }

                    if (WebGlobal.CurrentUser.PrimaryCompanyId == 1376)
                        await SendEmail(imp.DispatchEntry, xemail, true, true, !emailInput.HidePricing, !emailInput.HidePhotos, emailInput.files, emailInput.hideDiscounts, emailInput.DamageFormIds, emailInput.optionalMessage, emailInput.IncludePaymentLink);
                    else
                        await SendEmail(imp.DispatchEntry, xemail, false, true, !emailInput.HidePricing, !emailInput.HidePhotos, emailInput.files, emailInput.hideDiscounts, emailInput.DamageFormIds, emailInput.optionalMessage, emailInput.IncludePaymentLink, emailInput.HideLineItems);

                    output += "Sent " + (emailInput.HidePricing ? " summary details " : "invoice") + " via email to " + xemail + (emailInput.HidePricing ? " without pricing details" : "") + "\n";
                }

                // send pusher here to update email history
                await PushNotificationProvider.UpdateCall(imp.DispatchEntry.CompanyId, imp.DispatchEntryId);
                return new HttpResponseMessage(HttpStatusCode.OK) { Content = new StringContent(output) };
            }
            else
            {
                return new HttpResponseMessage(HttpStatusCode.InternalServerError) { Content = new StringContent("No emails or phone numbers specified.") };
            }
        }

        private static async Async.Task SendEmail(Entry entry,
          string xemail,
          bool sendFromUserInsteadOfCompany = false,
          bool sendAsPdf = true,
          bool showPricing = true,
          bool showPhotosLink = false,
          int[] files = null,
          bool hideDiscounts = false,
          int[] damageFormIds = null,
          string optionalMessage = "",
          bool? forceIncludePaymentLink = false,
          bool? hideLineItems = false)
        {
            CallModel call = await (await CallModel.MapAsync(entry)).FinishMapAsync();

            int statusCode = 0;
            using (MailMessage mm = new MailMessage())
            {
                // TODO: Can be made async
                mm.From = new MailAddress(EmailAddress.GetTowbookDotNetEmailAddressForCompany(call.CompanyId),
                    sendFromUserInsteadOfCompany ? (WebGlobal.CurrentUser.FullName + "(" + entry.Company.Name + ")") : entry.Company.Name);

                if (xemail.Contains(";") || xemail.Contains(","))
                    mm.To.Add(xemail.Replace(";", ","));
                else
                    mm.To.Add(new MailAddress(xemail));


                if (!string.IsNullOrWhiteSpace(call.InvoiceNumber))
                {
                    mm.Subject = entry.Company.Name.Trim() + ": Impound Invoice #" + call.InvoiceNumber.ToString();
                }
                else
                {
                    mm.Subject = entry.Company.Name.Trim() + ": Impound " + call.CallNumber.ToString();
                }
                // TODO: Can be made async
                if (CompanyKeyValue.GetFirstValueOrNull(WebGlobal.CurrentUser.CompanyId, Provider.Towbook.ProviderId,
                    "Towbook_Calls_EmailReplyToFromUserEmail") == "1")
                {
                    // Reply to
                    mm.ReplyToList.Add(new MailAddress(WebGlobal.CurrentUser.Email, WebGlobal.CurrentUser.FullName));
                }
                else
                {
                    var sendFromEmail = sendFromUserInsteadOfCompany ? WebGlobal.CurrentUser.Email : WebGlobal.CurrentUser.Company.Email;
                    var sendFromName = sendFromUserInsteadOfCompany ? WebGlobal.CurrentUser.FullName + "(" + WebGlobal.CurrentUser.Company + ")" : WebGlobal.CurrentUser.Company.Name;

                    // TODO: Can be made async
                    var billingAddress = AddressBookEntry.GetByName("Billing Address", call.CompanyId, false).FirstOrDefault();
                    if (Core.IsEmailValid(billingAddress?.Email))
                        sendFromEmail = billingAddress?.Email;

                    // allow account to specify reply to email address based on account settings
                    // TODO: Can be made async
                    var replytoEmail = AccountKeyValue.GetFirstValueOrNull(entry.CompanyId, entry.AccountId, Provider.Towbook.ProviderId, "ReplyToEmailAddress");
                    if (Core.IsEmailValid(replytoEmail))
                        sendFromEmail = replytoEmail;

                    mm.ReplyToList.Add(new MailAddress(
                        sendFromEmail,
                        sendFromName));
                }

                // TODO: Can be made async
                var imp = Impound.GetByDispatchEntry(entry);

                // TODO: Can be made async
                string invoice = WebGlobal.GetResponseFromUrl($"{_domain}/Impounds/Invoice.aspx?local=1&key={Core.MD5(entry.Id + ":27ed2fb84d816")}&" +
                    $"id={imp.Id}&showPrices={(showPricing ? "1" : "0")}&hideDiscounts={(hideDiscounts ? "1" : "0")}{(hideLineItems.GetValueOrDefault() ? "&hideLineItems=1" : "")}",
                    out statusCode, false, false, WebGlobal.CookieDomain);

                if (statusCode != 200)
                {
                    throw new Exception("Error retrieving Invoice. HttpStatusCode:" + statusCode + ", Response:" + invoice);
                }

                mm.IsBodyHtml = true;
                mm.Body = invoice;

                if (files != null)
                {
                    foreach (int fileId in files)
                    {
                        // TODO: This is not performant (n database/network calls on a loop)
                        var cf = await CompanyFile.GetByIdAsync(fileId);
                        // TODO: This is not performant (n database/network calls on a loop)
                        if (cf != null && await WebGlobal.CurrentUser.HasAccessToCompanyAsync(cf.CompanyId))
                        {
                            // TODO: This is not performant (n database/network calls on a loop)
                            await mm.Attachments.AddAsync(cf);
                        }
                        else
                        {
                            throw new Exception("Invalid FileID");
                        }
                    }
                }

                bool containsDamageFormPhotos = false;
                if (damageFormIds != null)
                {
                    foreach (int damageFormId in damageFormIds)
                    {
                        // get html contents
                        statusCode = 0;
                        var url = "/dispatch/vdr.aspx?id=" + damageFormId;
                        // TODO: Can be made async, this is not performant (n database/network calls on a loop)
                        var html = WebGlobal.GetResponseFromUrl(url, out statusCode, false, false, "towbook.com");

                        if (statusCode != 200)
                        {
                            // TODO: Can be made async, this is not performant (n database/network calls on a loop)
                            html = WebGlobal.GetResponseFromUrl(
                                _domain + "/dispatch/vdr.aspx?local=1&&id=" + damageFormId, out statusCode, false, false,
                                "towbook.com");
                        }

                        // Generate PDF
                        var dfms = await GeneratePdf(html, request: HttpContextHelper.Current.Request);
                        dfms.Position = 0;

                        var ct = new ContentType(MediaTypeNames.Application.Pdf);
                        var attach = new Attachment(dfms, ct);
                        var filename =
                            $"DamageReport{damageFormId}]-Call{imp.DispatchEntry.CallNumber}-{imp.DispatchEntry.Year} {imp.DispatchEntry.VehicleMake} {imp.DispatchEntry.VehicleModel}.pdf";

                        attach.ContentDisposition.FileName = filename;

                        mm.Attachments.Add(attach);

                        if (!containsDamageFormPhotos)
                        {
                            containsDamageFormPhotos =
                                (await EntryDamagePhoto.GetPhotosByDispatchEntryDamageIdAsync(damageFormId)).Count != 0;
                        }
                    }
                }

                // TODO: Can be made async
                if (CompanyKeyValue.GetFirstValueOrNull(WebGlobal.CurrentUser.CompanyId, Provider.Towbook.ProviderId, "Towbook_Calls_NeverShowPhotoLink") == "1")
                    showPhotosLink = false;

                EntryWebLink webLink = null;

                if (sendAsPdf)
                {
                    if (showPricing)
                    {
                        // only include PDF if pricing is included.
                        var footer = "<div style=\"color: #333333; font-family: verdana; font-size: 9px; font-weight: bold\"><br /><br />Created with Towbook Management Software | www.towbook.com</div>";
                        var ms = await ApiUtility.GeneratePdf(invoice, footer);

                        //Send the PdfDocument to the client
                        ms.Position = 0;

                        var ct = new System.Net.Mime.ContentType(System.Net.Mime.MediaTypeNames.Application.Pdf);
                        var attach = new System.Net.Mail.Attachment(ms, ct);
                        attach.ContentDisposition.FileName = "Impound-" + imp.Id + ".pdf";

                        mm.Attachments.Add(attach);
                    }
                    mm.IsBodyHtml = false;
                    if (!string.IsNullOrWhiteSpace(optionalMessage))
                    {
                        mm.Body = optionalMessage + "\n\n";
                    }
                    else
                    {
                        mm.Body = "";
                    }

                    string source = call.TowSource;
                    if (call.Account != null)
                    {
                        // TODO: Can be made async
                        var account = await Account.GetByIdAsync(call.Account.Id);

                        if (account != null && account.Type == AccountType.PrivateProperty)
                            source = call.Account.Company + " (" + call.TowSource + ")";
                    }

                    if (showPricing)
                    {
                        mm.Body += "Attached is a PDF copy of the impound ticket for the vehicle" + (call.Assets.Length > 1 ? "s" : "");
                        mm.Body += " towed from " + source + ".\n\n";

                        mm.Body += "To view it, please open the attachment.\n\n";
                        mm.Body += "If you cannot open the attachment, you can download Adobe Acrobat Reader from http://get.adobe.com/reader/\n\n";
                    }
                    else
                    {
                        var service = "towed";
                        if (imp.Company.Id == 22956)
                            service = "impounded";

                        if (call.TowTime != null)
                        {
                            mm.Body = "This message is to notify you that we " + service + " a vehicle from " + source + " on " +
                            Core.OffsetDateTime(imp.Company, call.TowTime.Value).ToShortDateString() + " " +
                            Core.OffsetDateTime(imp.Company, call.TowTime.Value).ToShortTowbookTimeString() + ". See details below:\n\n";
                        }
                        else
                            mm.Body = "This message is to notify you that we " + service + " a vehicle from " + source + " on " +
                                Core.OffsetDateTime(imp.Company, imp.ImpoundDate.Value).ToShortDateString() + " " +
                                Core.OffsetDateTime(imp.Company, imp.ImpoundDate.Value).ToShortTowbookTimeString() + ". See details below:\n\n";
                    }

                    if (!string.IsNullOrWhiteSpace(imp.Reason))
                    {
                        mm.Body += "Reason: " + imp.Reason + "\n";
                    }
                    else if (call.Reason != null)
                    {
                        mm.Body += "Reason: " + call.Reason.Name + "\n";
                    }

                    if (!string.IsNullOrWhiteSpace(entry.PurchaseOrderNumber))
                        mm.Body += "Purchase Order #: " + entry.PurchaseOrderNumber + "\n";

                    foreach (var a in call.Assets)
                    {
                        string asset = "";

                        asset += (a.Year > 0 ? a.Year.ToString() : "") + " " + a.Make + " " + a.Model + " ";

                        if (a.Color != null)
                            asset += " " + a.Color.Name;

                        if (!string.IsNullOrWhiteSpace(a.LicenseNumber))
                        {
                            asset += "\nLicense: " + a.LicenseNumber;

                            if (!string.IsNullOrWhiteSpace(a.LicenseState))
                                asset += " (" + a.LicenseState + ")";
                        }

                        if (!string.IsNullOrWhiteSpace(a.Vin))
                            asset += "\nVIN: " + a.Vin + "\n";

                        if (!string.IsNullOrWhiteSpace(asset))
                            mm.Body += "Vehicle: " + asset + "\n";
                    }

                    mm.Body += "Location: " + call.TowSource + "\n";

                    if (!string.IsNullOrWhiteSpace(call.TowDestination) && !call.Impound.GetValueOrDefault())
                    {
                        mm.Body += "Destination: " + call.TowDestination + "\n";
                    }

                    if (imp.Lot != null)
                        mm.Body += $"Destination: {imp.Lot.Name}, {imp.Lot.Address}, {imp.Lot.City} {imp.Lot.State} {imp.Lot.Zip}\n";

                    if (imp.ImpoundDate != null)
                    {
                        mm.Body += "Date/Time Impounded: " + Core.OffsetDateTime(imp.Company, imp.ImpoundDate.Value).ToShortDateString() + " @ " +
                            Core.OffsetDateTime(imp.Company, imp.ImpoundDate.Value).ToShortTowbookTimeString() + "\n";
                    }

                    if (showPricing)
                        mm.Body += "\nInvoice Total: " + call.InvoiceTotal.ToString("C") + "\n";

                    mm.Body += "\n";

                    if (showPhotosLink)
                    {
                        // TODO: This is not performant (n database/network calls on a loop)
                        var photos = await Dispatch.Photo.GetByDispatchEntryIdAsync(call.Id);

                        // TODO: This is not performant (n database/network calls on a loop)
                        var photos2 = await Towbook.Impounds.Photo.GetByImpoundIdAsync(imp.Id);

                        if (photos.Count > 0 || photos2.Count > 0 || containsDamageFormPhotos)
                        {
                            string url = $"{_domain}/PublicAccess/Photos.aspx?id={call.Id}";

                            // TODO: Can be made async, and this is not performant (n database/network calls on a loop)
                            string md5 = Core.ProtectId(call.Id, call.Owner.Id);
                            url += "&sc=" + md5;

                            mm.Body += "To view photos associated with this incident, visit: " + url + "\n\n";
                        }
                    }

                    #region Square Payment Link

                    if (await WebGlobal.CurrentUser.Company.HasFeatureAsync(Features.PaymentIntegrations_Square) && call.BalanceDue > 0)
                    {
                        // TODO: This is not performant (n database/network calls on a loop)
                        var sqConn = await SquareUtils.GetAuthorizationAsync(imp.DispatchEntry.CompanyId);
                        if (sqConn != null && !string.IsNullOrEmpty(sqConn.Location?.LocationId))
                        {
                            // TODO: Can be made async, and this is not performant (n database/network calls on a loop)
                            var setting = AccountKeyValue.GetFirstValueOrNull(imp.DispatchEntry.CompanyId, imp.DispatchEntry.AccountId, Provider.Towbook.ProviderId, "Square_AlwaysIncludePaymentLinkOnInvoices") ??
                                            CompanyKeyValue.GetFirstValueOrNull(imp.DispatchEntry.CompanyId, Provider.Towbook.ProviderId, "Square_AlwaysIncludePaymentLinkOnInvoices");

                            bool includePaymentLink = setting == "1";

                            if (forceIncludePaymentLink.HasValue)
                            {
                                if (forceIncludePaymentLink.Value)
                                    includePaymentLink = true;

                                if (!forceIncludePaymentLink.Value)
                                    includePaymentLink = false;
                            }

                            if (includePaymentLink)
                            {
                                var pl = new PaymentLink()
                                {
                                    CompanyId = imp.DispatchEntry.CompanyId,
                                    Status = PaymentLinkStatus.LinkGenerated,
                                    Vendor = PaymentVendor.SQUARE,
                                    DispatchEntryId = call.Id,
                                    CreateDate = DateTime.Now,
                                    OwnerUserId = WebGlobal.CurrentUser.Id
                                };

                                // TODO: This is not performant (n database/network calls on a loop)
                                await pl.SaveAsync();

                                webLink = new EntryWebLink()
                                {
                                    CompanyId = imp.DispatchEntry.CompanyId,
                                    CreateDate = DateTime.Now,
                                    OwnerUserId = WebGlobal.CurrentUser.Id,
                                    PaymentLinkId = pl.Id,
                                    ShowCharges = showPricing,
                                    HideDiscount = hideDiscounts,
                                    HidePhotos = !showPhotosLink,
                                    UrlHash = ShortCode.NewShortCodeByDate(DateTime.Now),
                                    DispatchEntryId = call.Id
                                };

                                // TODO: This is not performant (n database/network calls on a loop)
                                await webLink.SaveAsync();

                                mm.Body += $"Online payments can be made by visiting {webLink.TinyUrl}.\n\n";
                            }
                        }
                    }

                    #endregion

                    mm.Body += "If you have any questions, please feel free to contact us.\n\n";

                    mm.Body += entry.Company.Name + "\n" +
                        entry.Company.Phone + "\n" +
                        (!string.IsNullOrWhiteSpace(entry.Company.Website) ? entry.Company.Website + "\n\n" : "\n") +
                        "Sent using Towbook";
                }

                var metaData = new Dictionary<string, string>()
                {
                    { "callId", entry.Id.ToString() },
                    { "callNumber", entry.CallNumber.ToString() }
                };

                if (call.ImpoundDetails?.Id > 0)
                    metaData["impoundId"] = call.ImpoundDetails.Id.ToString();

                for (int i = 0; i < 3; i++)
                {
                    try
                    {
                        using (var sc = new SmtpClient().Get())
                        {
                            // TODO: This is not performant (n database/network calls on a loop), plus instantiating 3 different instances of an smtp client
                            await sc.Send(mm, WebGlobal.CurrentUser, "Impound Invoice", EmailType.Impound, imp.Id, new int[] { call.Id }, Guid.Empty, webLink, null, null, metaData);
                        }
                        break;
                    }
                    catch
                    {
                        if (i == 3)
                            throw;
                    }
                }
            }
        }

        private static async Task<string> GenerateInvoiceZPLAsync(Impound imp, int width)
        {
            var zplHelper = new ZebraHelper(width);
            string companyNameText = imp.Company.Name;
            string addressText = imp.Company.Address + ", " +
                                    imp.Company.City + " " +
                                    imp.Company.State + " " +
                                    imp.Company.Zip;
            string phoneText = "Phone: " + Extric.Towbook.Core.FormatPhone(imp.Company.Phone);

            //Define Header invoice
            zplHelper.addLineWhite();
            zplHelper.addLineTextCenter(companyNameText, 30, "bold");
            zplHelper.addLineWhite();
            zplHelper.addLineTextCenter(addressText, 20, "bold");
            zplHelper.addLineTextCenter(phoneText, 20, "bold");

            if (WebGlobal.CurrentUser.Company.Country == Extric.Towbook.Company.Company.CompanyCountry.Canada)
            {
                Extric.Towbook.Company.Countries.Canada cx = await Extric.Towbook.Company.Countries.Canada.GetByCompanyAsync(WebGlobal.CurrentUser.Company);
                if (cx != null && cx.GST.Length > 0)
                    phoneText = phoneText + " | " + cx.TaxName + "# " + cx.GST;
            }
            zplHelper.addLineTextCenter(phoneText, 20, "bold");

            //Define and display Contacts
            zplHelper.addLine();
            zplHelper.addLineText("Contacts", 20, "bold");

            foreach (var contact in imp.DispatchEntry.Contacts)
            {
                zplHelper.addKeyValueText("Type", contact.Type.ToString(), 20, "bold");
                zplHelper.addLineText(contact.Name, 20, "bold");
                zplHelper.addLineText(contact.Address, 20, "bold");
                zplHelper.addLineText(contact.City + " " + contact.State + " " + contact.Zip, 20, "bold");
                zplHelper.addLineText(contact.Phone, 20, "bold");
                zplHelper.addLineWhite();
            }

            //Display Basic Detailss
            zplHelper.addLine();
            zplHelper.addLineText("Basic Details", 20, "bold");

            var dispatchEntry = imp.DispatchEntry;

            zplHelper.addKeyValueText("Invoice #:", dispatchEntry.InvoiceNumber, 20, "bold");
            zplHelper.addKeyValueText("Reference #:", dispatchEntry.CallNumber.ToString(), 20, "bold");
            if (imp.Account != null)
                zplHelper.addKeyValueText("Account", imp.Account.Company, 20, "bold");

            if (dispatchEntry.CompletionTime != null)
                zplHelper.addKeyValueText("Date/Time Towed: ",
                    Core.OffsetDateTime(dispatchEntry.Company, dispatchEntry.CompletionTime.Value).ToShortDateString() + " " +
                    Core.OffsetDateTime(dispatchEntry.Company, dispatchEntry.CompletionTime.Value).ToShortTowbookTimeString(), 20, "bold");

            if (imp.ImpoundDate != null)
                zplHelper.addKeyValueText("Date/Time Impounded:",
                    Core.OffsetDateTime(dispatchEntry.Company, imp.ImpoundDate.Value).ToShortDateString() + " " +
                    Core.OffsetDateTime(dispatchEntry.Company, imp.ImpoundDate.Value).ToShortTowbookTimeString(), 20, "bold");

            if (imp.MotorVehicleReportDate != null)
                zplHelper.addKeyValueText("MVR Report Date:", Core.OffsetDateTime(imp.Company, imp.MotorVehicleReportDate.Value).ToShortDateString(), 20, "bold");

            if (imp.ReleaseDate != null)
                zplHelper.addKeyValueText("Date/Time Released:", Core.OffsetDateTime(imp.Company, imp.ReleaseDate.Value).ToShortDateString() + " " +
                    Core.OffsetDateTime(imp.Company, imp.ReleaseDate.Value).ToShortTowbookTimeString(), 20, "bold");

            if (imp.ReleaseDate != null)
                zplHelper.addKeyValueText("Days Held in Impound", imp.DaysHeldBillable + " days", 20, "bold");

            if (dispatchEntry.Reason != null)
                zplHelper.addKeyValueText("Reason for Impound", dispatchEntry.Reason.Name, 20, "bold");

            //Vehicle Summary
            zplHelper.addLine();
            zplHelper.addLineText("Vehicle Summary", 20, "bold");

            if (!String.IsNullOrEmpty(dispatchEntry.VIN))
                zplHelper.addKeyValueText("VIN Number:", dispatchEntry.VIN, 20, "bold");

            zplHelper.addKeyValueText("Model:", (dispatchEntry.Year > 0 ? dispatchEntry.Year.ToString() : "") + " " +
                dispatchEntry.MakeModelFormatted + (dispatchEntry.Color != null ? " (" + dispatchEntry.Color.Name + ")" : ""), 20, "bold");

            if (dispatchEntry.Odometer > 0)
                zplHelper.addKeyValueText("Odomoter:", dispatchEntry.Odometer.ToString(), 20, "bold");

            if (dispatchEntry.LicenseNumber.Length > 0)
                zplHelper.addKeyValueText("License Plate:", dispatchEntry.LicenseNumber + (dispatchEntry.LicenseState.Length > 0 ? " (" + dispatchEntry.LicenseState + ")" : ""), 20, "bold");

            zplHelper.addKeyValueText("Drivable:", (dispatchEntry.Drivable == true ? "Yes" : "No"), 20, "bold");
            zplHelper.addKeyValueText("Keys:", (imp.HasKeys == true ? "Yes" : "No"), 20, "bold");

            if (dispatchEntry.TowSource != null && dispatchEntry.TowSource.Length > 0)
                zplHelper.addKeyValueText("Towed from:", dispatchEntry.TowSource, 20, "bold");

            if (imp.Lot != null)
                zplHelper.addKeyValueText("Stored at:", imp.Lot.Address + ", " + imp.Lot.City, 20, "bold");
            else if (dispatchEntry.TowDestination != null && dispatchEntry.TowDestination.Trim().Length > 1)
                zplHelper.addKeyValueText("Stored at:", dispatchEntry.TowDestination, 20, "bold");


            if (imp.DispatchEntry.Company.State == "TX" ||
                (await Dispatch.InvoiceOptions.GetByCompanyIdAsync(imp.DispatchEntry.CompanyId)).ShowDriver)
            {
                if (imp.DispatchEntry.Drivers.Any())
                {
                    foreach (var driverId in imp.DispatchEntry.Drivers)
                    {
                        // TODO: This is not performant (n database/network calls on a loop)
                        var driver = await Driver.GetByIdAsync(driverId);

                        if (driver != null)
                        {
                            zplHelper.addKeyValueText("Driver", driver.Name, 20, "bold");

                            var driverLicenseValues = await Licenses.DriverLicenseKeyValue.GetByDriverIdAsync(driver.Id);
                            var driverKeys = await Licenses.DriverLicenseKey.GetAllAsync();
                            var driverPrintables = driverLicenseValues.Where(y => driverKeys.Where(o => o.Id == y.KeyId && o.ShowValueOnPrintables == true).Any());

                            if (driverPrintables.Any())
                            {
                                foreach (var value in driverPrintables)
                                {
                                    var key = driverKeys.Where(o => o.Id == value.KeyId).FirstOrDefault();
                                    if (!String.IsNullOrWhiteSpace(value.Value))
                                    {
                                        zplHelper.addKeyValueText(key.Name, value.Value, 20, "bold");
                                    }
                                }
                            }
                        }
                    }
                }
            }

            if (imp.DispatchEntry.Company.State == "TX" ||
                (await Dispatch.InvoiceOptions.GetByCompanyIdAsync(imp.DispatchEntry.CompanyId)).ShowTruck)
            {
                if (imp.DispatchEntry.Trucks.Any())
                {
                    foreach (var truckId in imp.DispatchEntry.Trucks)
                    {
                        // TODO: This is not performant, (n database/network calls on a loop)
                        var truck = await Truck.GetByIdAsync(truckId);

                        if (truck != null)
                        {
                            zplHelper.addKeyValueText("Truck", truck.Name, 20, "bold");

                            if (imp.Company.State == "TX" && !string.IsNullOrWhiteSpace(truck.PlateNumber))
                                zplHelper.addKeyValueText("Truck Plate #", truck.PlateNumber, 20, "bold");

                            // TODO: Can be made async, and this is not performant (n database/network calls on a loop)
                            var truckLicenseValues = Licenses.TruckLicenseKeyValue.GetByTruckId(truck.Id);
                            // TODO: Can be made async, and this is not performant (n database/network calls on a loop)
                            var truckKeys = Licenses.TruckLicenseKey.GetAll();
                            var truckPrintables = truckLicenseValues.Where(y => truckKeys.Where(o => o.Id == y.KeyId && o.ShowValueOnPrintables == true).Any());

                            if (truckPrintables.Any())
                            {
                                foreach (var value in truckPrintables)
                                {
                                    var key = truckKeys.Where(o => o.Id == value.KeyId).FirstOrDefault();
                                    if (!String.IsNullOrWhiteSpace(value.Value))
                                    {
                                        zplHelper.addKeyValueText(key.Name, value.Value, 20, "bold");
                                    }
                                }
                            }
                        }
                    }
                }
            }

            // Custom Fields

            foreach (var av in imp.DispatchEntry.Attributes.Values)
            {
                // TODO: This is not performant, (n database/network calls on a loop)
                var attr = await Dispatch.Attribute.GetByIdAsync(av.DispatchEntryAttributeId);

                if (attr != null && attr.PrintOnTowInvoice)
                {
                    if (attr.Type == AttributeType.Boolean)
                    {
                        if (av.Value == "1")
                            av.Value = "Yes";
                        else if (av.Value == "0")
                            av.Value = "No";
                    }
                    zplHelper.addKeyValueText(attr.Name, av.Value, 20, "bold");
                }
            }

            //Charges
            var categorizedList = new Dictionary<int, Collection<InvoiceItem>>();

            foreach (var item in imp.InvoiceItems)
            {
                int categoryId = 0; // default to towing.
                if (item.RateItem != null && item.RateItem.CategoryId != null)
                {
                    var r = await RateItemCategory.GetByIdAsync(item.RateItem.CategoryId.Value);
                    if (r != null && r.ParentCategoryId != null)
                        categoryId = r.ParentCategoryId.Value;
                    else
                        categoryId = 0;
                }
                else
                    categoryId = 0;

                if (!categorizedList.ContainsKey(categoryId))
                    categorizedList.Add(categoryId, new Collection<InvoiceItem>());

                categorizedList[categoryId].Add(item);
            }

            decimal subTotal = 0;
            foreach (var item in categorizedList)
            {
                int catId = item.Key;
                string categoryName = catId != 0 ? (await RateItemCategory.GetByIdAsync(catId)).Name + " " : "";
                zplHelper.addChargeElementsText(new List<string>() { categoryName + "Charges", "Quantity", "Price", "Total" }, 20, "bold");

                foreach (var invoice in categorizedList[catId])
                {
                    var invoiceObject = (await getInvoiceValuesAsync(invoice, dispatchEntry)).ToJson();
                    Dictionary<string, string> invoiceDictionary = JsonConvert.DeserializeObject<Dictionary<string, string>>(invoiceObject);

                    string invoiceName = invoiceDictionary["name"];
                    string invoiceQuantity = invoiceDictionary["quantity"];
                    string invoicePrice = invoiceDictionary["price"];
                    string invoiceLineTotal = invoiceDictionary["lineTotal"];

                    subTotal = subTotal + decimal.Parse(invoiceDictionary["subTotal"]);

                    zplHelper.addChargeElementsText(new List<string>() { invoiceName, invoiceQuantity, invoicePrice, invoiceLineTotal }, 20, "bold");
                }
                zplHelper.addLine();
            }

            zplHelper.addLineWhite();

            decimal tax = imp.InvoiceTax;
            decimal grandTotal = subTotal + tax;

            zplHelper.addChargeElementsText(new List<string>() { "", "", "Sub Total", String.Format("{0:C}", subTotal) }, 20, "bold");

            string TaxValue = (WebGlobal.CurrentUser.Company.Country == Extric.Towbook.Company.Company.CompanyCountry.Canada ? "GST" : "Taxes");

            zplHelper.addChargeElementsText(new List<string>() { "", "", TaxValue, String.Format("{0:C}", tax) }, 20, "bold");
            zplHelper.addChargeElementsText(new List<string>() { "", "", "Grand Total", String.Format("{0:C}", subTotal + tax) }, 20, "bold");

            //Define bottom message
            string messageLine1 = "";

            if (String.IsNullOrEmpty(WebGlobal.CurrentUser.Company.InvoicingTagline))
            {
                messageLine1 = String.Format("{0} appreciates your business; if you have any questions regarding this invoice, please contact us at {1}",
                    WebGlobal.CurrentUser.Company.Name, WebGlobal.CurrentUser.Company.Phone);
            }
            else
            {
                messageLine1 = WebGlobal.CurrentUser.Company.InvoicingTagline;
            }

            zplHelper.addTextAreaLine(messageLine1, 20, "bold");

            zplHelper.addLineWhite();
            zplHelper.addLineWhite();
            zplHelper.addLineText("Signature:", 20, "bold");
            zplHelper.addLineWhite();
            zplHelper.addLine();

            // TODO: Can be made async
            var licenseValues = Licenses.CompanyLicenseKeyValue.GetByCompanyId(imp.Company.Id);
            // TODO: Can be made async
            var keys = Licenses.CompanyLicenseKey.GetAll();
            var printables = licenseValues.Where(y => keys.Where(o => o.Id == y.KeyId && o.ShowValueOnPrintables == true).Any());

            if (printables.Any())
            {
                foreach (var value in printables)
                {
                    var key = keys.Where(o => o.Id == value.KeyId).FirstOrDefault();
                    if (!string.IsNullOrWhiteSpace(value.Value))
                    {
                        zplHelper.addLineText(string.Format("{0}{2} {1}", key.Name, value.Value, (key.Name.EndsWith("#") ? "" : ":")), 20, "bold");
                    }
                }
            }

            if (imp.Company.State.ToUpperInvariant() == "TX" ||
                imp.Company.State.ToUpperInvariant() == "TEXAS")
            {
                zplHelper.addLineText("Direct all complaints to:", 20, "bold");
                zplHelper.addTextAreaLine("Texas Dept. of Licensing & Regulation Towing Program, P.O. Box 12157 | Austin, TX 78711 | (800) 803-9202", 20, "bold");
                zplHelper.addLineText("Email: <EMAIL> |", 20, "bold");
                zplHelper.addLineText("http://www.tdlr.texas.gov/", 20, "bold");
            }

            return zplHelper.getZPLGenerated();
        }

        private static async Task<object> getInvoiceValuesAsync(InvoiceItem e, Dispatch.Entry _de)
        {
            string category = "";
            string name = e.Name;
            int quantity = (int)e.Quantity;
            string unitPrice = e.Price.ToString();
            string lineTotal = e.Total.ToString();
            decimal cost = 0;

            Extric.Towbook.Dispatch.InvoiceItem i = (Extric.Towbook.Dispatch.InvoiceItem)e;

            name = i.RateItem != null ? i.RateItem.Name : i.CustomName;
            quantity = (int)i.Quantity;

            if (i.RateItem?.CategoryId != null)
            {
                var rateItemCategory = await RateItemCategory.GetByIdAsync(i.RateItem.CategoryId.Value);
                category = "(" + rateItemCategory.Name + ") ";
            }

            if (i.CustomPrice != null)
            {
                unitPrice = String.Format("{0:C}", i.CustomPrice);
                cost = i.CustomPrice.Value;
            }
            else if (i.RateItem != null)
            {
                decimal myCost = 0;

                if (i.RateItem.ExtendedRateItems != null &&
                    _de.BodyType != null &&
                    i.RateItem.ExtendedRateItems.ContainsKey(_de.BodyType.Id))
                {
                    myCost = i.RateItem.ExtendedRateItems[_de.BodyType.Id].Amount;
                }
                else
                {
                    myCost = i.RateItem.Cost;
                }

                unitPrice = String.Format("{0:C}", myCost);
                cost = myCost;
            }

            lineTotal = String.Format("{0:C}", cost * i.Quantity);
            decimal subTotal = cost * i.Quantity;
            if ((i.RateItem != null && i.RateItem.Taxable) || i.Taxable)
            {
                //_taxableAmount += cost * i.Quantity;
            }
            //_grandTotal = _subTotal + _tax;

            return new
            {
                Name = name,
                Quantity = quantity,
                Price = unitPrice,
                LineTotal = lineTotal,
                SubTotal = subTotal
            };
        }

    }



    public sealed class ImpoundTasksForImpoundList
    {
        [JsonIgnore] 
        public int ImpoundId { get; set; }
        public int ImpoundTaskId { get; set; }
        public int? ImpoundReminderDefinitionId { get; set; }
        public DateTime? DueDate { get; set; }
        public int Status { get; set; }
        public string Due { get; set; }
        public string Title { get; set; }
        public DateTime? CompletionDate { get; set; }
        public bool Hidden { get; set; }
    }




    public class ReleaseEvent : TrackableObject
    {
        private string _releaseReason;

        public int ImpoundId { get; set; }
        public int DispatchEntryId { get; set; }
        public DateTime? ReleaseDate { get; set; }
        public string ReleaseReason
        {
            get { return _releaseReason; }
            set { SetField(ref _releaseReason, value, "ReleaseReason"); }
        }

        public string ReleaseNotes { get; set; }

        public async Task Save(User performer, bool isUnrelease)
        {
            if (this.IsDirty)
            {
                await new ActivityLogItem()
                {
                    ParentObjectId = ImpoundId,
                    ParentObjectTypeId = ActivityLogType.Impound,
                    ObjectId = DispatchEntryId,
                    Type = ActivityLogType.DispatchEntry,
                    ActionId = isUnrelease ? (int)ActivityLogActionType.ImpoundUnreleased : (int)ActivityLogActionType.ImpoundReleased,
                    IpAddress = "0.0.0.0",
                    UserId = performer != null ? performer.Id : WebGlobal.CurrentUser.Id,
                    Details = new ActivityLogItemDetail()
                    {
                        Data = this.ChangedFields.ToJson(),
                        ActivityLogItemDetailTypeId = ActivityLogItemDetailType.LoggedFieldChange
                    }
                }.SaveAsync();
            }
        }
    }
}
