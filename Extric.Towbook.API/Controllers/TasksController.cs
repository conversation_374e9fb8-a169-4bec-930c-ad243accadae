using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using Extric.Towbook.Utility;
using Extric.Towbook.WebShared;
using Microsoft.AspNetCore.Mvc;
using static Extric.Towbook.API.ApiUtility;

namespace Extric.Towbook.API.Controllers
{
    [Route("tasks")]
    public class TasksController : ControllerBase
    {
        [HttpGet]
        [Route("")]
        public async Task<object> Get(int impoundId)
        {
            var ret = new Collection<dynamic>();

            try
            {
                var impoundTasks = await Tasks.ImpoundTask.GetByImpoundAsync(impoundId);

                if (impoundTasks != null && impoundTasks.Count() > 0) 
                {
                    var companyId = impoundTasks.FirstOrDefault().CompanyId;
                    
                    await ThrowIfNoCompanyAccessAsync(companyId);

                    var definitions = await Towbook.Impounds.ReminderItem.GetByCompanyIdAsync(companyId);
                    var templates = (await LetterTemplate.GetByIdsAsync(new int[] { companyId },
                            definitions
                                .Where(w => w.LetterTemplateId.GetValueOrDefault() > 0)
                                .Select(s => s.LetterTemplateId.Value)
                                .ToArray()
                             )).ToCollection();

                    foreach (var impoundTask in impoundTasks)
                    {
                        var task = await CompanyTask.GetByIdAsync(impoundTask.TaskId);
                        var definition = definitions.FirstOrDefault(a =>
                                                a.LetterTemplateId != null &&
                                                a.Id == impoundTask.ImpoundReminderDefinitionId.GetValueOrDefault());
                        var template = templates.FirstOrDefault(f => f.Id == definition?.LetterTemplateId.GetValueOrDefault());

                        ret.Add(new
                        {
                            TaskId = impoundTask.TaskId,
                            ImpoundTaskId = impoundTask.ImpoundTaskId,
                            ImpoundId = impoundTask.ImpoundId,
                            ImpoundReminderDefinitionId = impoundTask.ImpoundReminderDefinitionId,
                            TypeId = impoundTask.TypeId,
                            Type = SetTaskType(impoundTask.TypeId),
                            Title = impoundTask.Title,
                            Details = impoundTask.Details,
                            DueDate = (impoundTask.DueDate.HasValue) ? impoundTask.DueDate.Value.ToLocalTime().ToString() : null,
                            Processed = impoundTask.Processed,
                            ReminderActionId = impoundTask.ReminderActionId,
                            Reminders = impoundTask.Reminders,
                            Priority = task.Priority.ToString(),
                            StatusCode = task.Status,
                            Status = task.Status.ToString(),
                            CompletionDate = task.CompletionDate.HasValue ? task.CompletionDate.Value.ToLocalTime().ToString() : null,
                            AtDay = definition?.Days,
                            LetterTemplate = template == null ? null : new
                            {
                                template?.Id,
                                template?.Title,
                            }
                        });
                    }
                }

                return ret.OrderBy(o => o.AtDay);
            } 
            catch(Exception e)
            {
                throw new Exception("error: " + e.ToString());
            }
        }

        private static string SetTaskType(int typeId)
        {
            switch (typeId)
            {
                case 1: return "Regular";
                case 2: return "Impound";
                case 3: return "Truck Maintenance";
            }
            return "unknown";
        }


        public sealed class TaskUpdateModel
        {
            public string Status { get; set; }
        }

        /// <summary>
        /// Update an Impound Task. Use to update the status and other bits of information. If the data passed is blank, it will mark the task as complete.
        /// </summary>
        /// <param name="id"></param>
        /// <param name="tum"></param>
        /// <returns></returns>
        [HttpPut("{id}")]
        public async Task Put(int id, [FromForm] TaskUpdateModel tum)
        {
            var task = await CompanyTask.GetByIdAsync(id);

            if (task == null || !WebGlobal.CurrentUser.HasAccessToCompany(task.CompanyId))
                throw new Extric.Towbook.Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound)
                {
                    Content = new StringContent("The specified task either doesn't exist or you don't have access to it.")
                });


            if (tum.Status.ToLowerInvariant() == "complete")
            {
                task.Status = TaskStatus.Completed;
                task.CompletionDate = DateTime.Now;
            }
            else if (tum.Status.ToLowerInvariant() == "in-progress")
                task.Status = TaskStatus.InProgress;
            else
                throw new Exception("unknown status: " + tum.Status);

            task.Save();
        }

        /// <summary>
        /// Deletes the specified Task. 
        /// </summary>
        /// <param name="id">TaskId of the task to delete.</param>
        /// 
        [HttpDelete("{id}")]
        public async Task Delete(int id)
        {
            var task = await CompanyTask.GetByIdAsync(id);

            if (task == null || !WebGlobal.CurrentUser.HasAccessToCompany(task.CompanyId))
                throw new Extric.Towbook.Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound)
                {
                    Content = new StringContent("The specified task either doesn't exist or you don't have access to it.")
                });

            task.Delete(WebGlobal.CurrentUser);
        }
    }
}
