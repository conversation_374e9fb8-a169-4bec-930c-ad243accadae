using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using Extric.Towbook.API.Models;
using Extric.Towbook.Dispatch;
using Extric.Towbook.EventNotifications;
using Extric.Towbook.Integration;
using Extric.Towbook.Integration.MotorClubs.Services;
using Extric.Towbook.Utility;
using Extric.Towbook.Web;
using Extric.Towbook.WebShared;
using Newtonsoft.Json;
using static Extric.Towbook.API.ApiUtility;

namespace Extric.Towbook.API.Controllers
{

    [Route("notifications")]
    public class NotificationsController : ControllerBase
    {

        //routes.MapHttpRoute(
        //    name: "DefaultApiGet",
        //    routeTemplate: "{controller}/{id}",
        //    defaults: new { id = RouteParameter.Optional, action = "Get" },
        //    constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "GET" }),
        //                       id = new RestStyleConstraint() }
        //    ).DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };
        [Route("{id}")]
        [HttpGet]
        public object Get(int id)
        {
            var nm = NotificationMessage.GetById(id);
            
            if (nm != null)
            {
                if (nm.UserId == WebGlobal.CurrentUser.Id)
                    return new HttpResponseMessage() { Content = new StringContent(nm.Json, System.Text.Encoding.UTF8, "application/json") };
            }

            return new HttpResponseMessage(HttpStatusCode.NotFound) { Content = new StringContent("Message doesn't exist.") };
        }


        //routes.MapHttpRoute(
        //    name: "DefaultApiGetSingle",
        //    routeTemplate: "{controller}/{action}",
        //    defaults: new { action = "Get" },
        //    constraints: new
        //    {
        //        httpMethod = new HttpMethodConstraint(new string[] { "GET" }),
        //    }
        //    ).DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };
        [Route("")]
        [HttpGet]
        public async Task<IEnumerable<NotificationMessageModel>> Get()
        {
            return NotificationMessageModel.Map(await NotificationMessage.GetByUserIdAsync(WebGlobal.CurrentUser.Id));
        }

        /// <summary>
        /// Notify the server that the user has accepted the message/request for action. 
        /// </summary>
        /// <param name="id"></param>
        //routes.MapHttpRoute(
        //    name: "DefaultApiActionPost",
        //    routeTemplate: "{controller}/{id}/{action}",
        //    defaults: new { },
        //    constraints: new
        //    {
        //        httpMethod = new HttpMethodConstraint(new string[] { "POST" }),
        //        id = new RestStyleConstraint()
        //    }
        //    ).DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };
        [Route("{id}/accept")]
        [HttpPost]
        public async Task<HttpResponseMessage> Accept(int id)
        {
            var nm = NotificationMessage.GetById(id);
            var ok = new HttpResponseMessage(HttpStatusCode.NoContent);
            
            if (nm != null)
            {
                if (nm.UserId == WebGlobal.CurrentUser.Id)
                {

                    var autoAdvance = CompanyKeyValue.GetFirstValueOrNull(WebGlobal.CurrentUser.CompanyId,
                                          Provider.Towbook.ProviderId, "Towbook_Calls_AcceptAutoAdvanceStatusToEnroute") == "1";

                    var preventEnrouteMultipleCalls = CompanyKeyValue.GetFirstValueOrNull(WebGlobal.CurrentUser.CompanyId,
                                          Provider.Towbook.ProviderId, "Towbook_Calls_PreventEnrouteOnMultipleCalls") == "1";

                    var autoDispatch = await WebGlobal.CurrentUser.Company.HasFeatureAsync(Generated.Features.AutoDispatch);

                    try
                    {
                        var msg = JsonConvert.DeserializeObject<dynamic>(nm.Json);

                        if (msg.callId == null)
                            return ok;

                        var e = await Entry.GetByIdAsync((int)msg.callId);

                        var currentDrivers = Driver.GetByUserId(nm.UserId).Select(o => o.Id).ToArray();
                        if (!e.Drivers.Any(o => currentDrivers.Contains(o)))
                        {
                            // driver is no longer assigned to this job
                            return ok;
                        }

                        if (autoDispatch)
                        {
                            string key = "DriverRefuseCounter:" + e.CompanyId + "_" + e.DriverId;
                            await Core.DeleteRedisKeyAsync(key);
                        }

                        nm.Status = NotificationMessageStatus.Accepted;
                        nm.StatusTime = DateTime.Now;
                        await nm.Save();

                        await UserController.ForceDriverUp(WebGlobal.CurrentUser);

                        var enableGeofence = CompanyKeyValue.GetFirstValueOrNull(WebGlobal.CurrentUser.CompanyId,
                            Provider.Towbook.ProviderId, "Towbook_Calls_GeofencedOnScene") == "1";

                        if (e.Account.MasterAccountId == Towbook.Accounts.MasterAccountTypes.Allstate ||
                            e.Account.MasterAccountId == Towbook.Accounts.MasterAccountTypes.Agero ||
                            e.Account.MasterAccountId == Towbook.Accounts.MasterAccountTypes.Swoop ||
                            e.Account.MasterAccountId == Towbook.Accounts.MasterAccountTypes.Towbook)
                            enableGeofence = true;

                        if (enableGeofence)
                        {
                            var pickup = e.Waypoints.FirstOrDefault(o => o.Title == "Pickup" || o.Position == 1);

                            if (pickup != null && pickup.Latitude != 0)
                            {
                                int? remoteCallId = null;
                                int? remoteCompanyId = null;

                                if (e.Account.MasterAccountId == Towbook.Accounts.MasterAccountTypes.Towbook)
                                {
                                    var cr2 = CallRequest.GetById(e.Id);
                                    if (cr2 != null)
                                    {
                                        if (cr2.PurchaseOrderNumber != null)
                                        {
                                            if (int.TryParse(cr2.PurchaseOrderNumber, out int sendingCallId))
                                            {
                                                var en = await Entry.GetByIdAsync(sendingCallId);
                                                if (en != null)
                                                {
                                                    remoteCallId = en.Id;
                                                    remoteCompanyId = en.CompanyId;
                                                }
                                            }
                                        }
                                    }
                                }

                                await LocationController.AddGeofence(WebGlobal.CurrentUser.Id,
                                    new LocationController.Geofence()
                                    {
                                        CallId = e.Id,
                                        Lat = pickup.Latitude,
                                        Lng = pickup.Longitude,
                                        Status = Status.AtSite.Id,
                                        RemoteCallId = remoteCallId,
                                        RemoteCompanyId = remoteCompanyId
                                    });
                            }
                        }

                        string gpsSource = "unknown";

                        if (Web.HttpContext.Current.IsAndroidDevice())
                            gpsSource = "android";
                        else if (Web.HttpContext.Current.IsAppleDevice())
                            gpsSource = "ios";

                        var cr = await CallRequest.GetByDispatchEntryId(e.Id);
                        decimal userLat = 0;
                        decimal userLng = 0;

                        if (cr != null)
                        {
                            userLat = RecentLatitude();
                            userLng = RecentLongitude();

                            if (userLat == 0 || userLng == 0)
                            {
                                var uli = await UserLocationHistoryItem.GetCurrentByUserIdAsync(WebGlobal.CurrentUser.Id, DateTime.Now.AddMinutes(-5), DateTime.Now);
                                if (uli != null)
                                {
                                    userLat = uli.Latitude;
                                    userLng = uli.Longitude;
                                }
                            }

                            await DigitalDispatchService.NotifyCallUpdateEventAsync(e.CompanyId, e.AccountId,
                                new
                                {
                                    CallRequestId = cr.CallRequestId,
                                    NewStatusId = e.Status.Id,
                                    Latitude = userLat,
                                    Longitude = userLng,
                                    Source = gpsSource,
                                    DriverId = e.DriverId,
                                    DriverName = e.Driver?.Name,
                                    CurrentWaypointId = 0,
                                    Subsource = "driverAcceptedDispatch"
                                }.ToJson(null), DigitalDispatchService.CallUpdateType.StatusUpdate,
                                ownerUserId: WebGlobal.CurrentUser.Id);
                        }

                        if (!autoDispatch && !autoAdvance)
                            return ok;

                        if (autoAdvance)
                        {
                            if (e.Status.Id < Status.EnRoute.Id)
                                e.Status = Status.EnRoute;

                            var blockSave = false;
                            if (preventEnrouteMultipleCalls)
                            {
                                if ((await Entry.GetCurrentByCompanySuperchargedAsync(
                                    (await WebGlobal.GetCompaniesAsync()).Select(o => o.Id).ToArray(),
                                    true, false, false, true,
                                    currentDrivers)).Any(o => o.Status.IsCurrent() && !o.Reason.IsGoa() &&
                                                             o.Status.Id > Status.Dispatched.Id))
                                {
                                    blockSave = true;
                                }
                            }

                            if (!blockSave)
                                await e.Save(false, await this.GetCurrentTokenAsync(), this.GetRequestingIp());

                            if (cr != null)
                            {
                                // delay by 30 seconds - if Assign and 
                                await DigitalDispatchService.NotifyCallUpdateEventAsync(e.CompanyId, e.AccountId,
                                    new
                                    {
                                        CallRequestId = cr.CallRequestId,
                                        NewStatusId = e.Status.Id,
                                        Latitude = userLat,
                                        Longitude = userLng,
                                        Source = gpsSource,
                                        DriverId = e.DriverId,
                                        DriverName = e.Driver?.Name,
                                        CurrentWaypointId = 0
                                    }.ToJson(null), 
                                    DigitalDispatchService.CallUpdateType.StatusUpdate,
                                    DateTime.Now.AddSeconds(30),
                                    ownerUserId: WebGlobal.CurrentUser.Id);
                            }

                            await CallsController.ChatUpdateMembers(null, e);
                        }
                    }
                    catch
                    {

                    }

                    return ok;
                }
            }

            return new HttpResponseMessage(HttpStatusCode.NotFound) { Content = new StringContent("Message doesn't exist.") };
        }

        /// <summary>
        /// Reject the specified message. Used for when a user can't do what the message/notification asked. 
        /// </summary>
        /// <param name="id"></param>
        /// <param name="message">optional message specifying why the user rejected the request</param>
        //routes.MapHttpRoute(
        //    name: "DefaultApiActionPost",
        //    routeTemplate: "{controller}/{id}/{action}",
        //    defaults: new { },
        //    constraints: new
        //    {
        //        httpMethod = new HttpMethodConstraint(new string[] { "POST" }),
        //        id = new RestStyleConstraint()
        //    }
        //    ).DataTokens = new RouteValueDictionary { { "Namespaces", new string[] { myNamespace } } };
        [Route("{id}/reject")]
        [HttpPost]
        public async Task<HttpResponseMessage> Reject(int id, [FromQuery] string message = null)
        {
            var nm = NotificationMessage.GetById(id);

            if (nm != null)
            {
                if (nm.UserId == WebGlobal.CurrentUser.Id)
                {
                    nm.Status = NotificationMessageStatus.Rejected;
                    nm.StatusResponse = message;
                    nm.StatusTime = DateTime.Now;
                    await nm.Save();

                    await UserController.ForceDriverUp(WebGlobal.CurrentUser);

                    try
                    {
                        // TODO: move this to a AutoDispatchService helper class as a method, NotifyDriverRefused()

                        var msg = JsonConvert.DeserializeObject<dynamic>(nm.Json);

                        if (msg.callId != null)
                        {
                            var e = await Entry.GetByIdAsync((int)msg.callId);

                            var qc = await ServiceBusHelper.CreateProducerQueueAsync("autodispatch");
                            var bm = new BrokeredMessage(new
                            {
                                callId = e.Id,
                                companyId = e.CompanyId,
                                driverId = e.DriverId,
                                type = "driver_refused"
                            }.ToJson());

                            bm.TimeToLive = TimeSpan.FromMinutes(5);

                            await qc.SendAsync(bm);
                        }
                    }
                    catch
                    {

                    }

                    return new HttpResponseMessage(HttpStatusCode.NoContent);
                }
            }

            return new HttpResponseMessage(HttpStatusCode.NotFound) { Content = new StringContent("Message doesn't exist.") };
        }
    }
}
