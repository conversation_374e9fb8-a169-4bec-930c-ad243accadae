using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Formatting;
using System.Net.Http.Headers;
using System.Threading.Tasks;

namespace Extric.Towbook.API
//namespace Extric.Towbook.API.Controllers
{
    public static class HttpRequestMessageExtensionsNet5
    {

        /*
        public static HttpResponseMessage CreateResponse<T>(this HttpRequestMessage request, T value)
        {
            if (request == null)
            {
                throw new ArgumentNullException(nameof(request));
            }

            if (value == null)
            {
                throw new ArgumentNullException(nameof(value));
            }

            return CreateResponse<T>(request, HttpStatusCode.OK, value);
        }

        public static HttpResponseMessage CreateResponse<T>(
            this HttpRequestMessage request,
            HttpStatusCode statusCode,
            T value)
        {
            return CreateResponse<T>(request, statusCode, value, new MediaTypeHeaderValue("application/json"));
        }

        public static HttpResponseMessage CreateResponse<T>(
            this HttpRequestMessage request,
            HttpStatusCode statusCode,
            T value,
            MediaTypeHeaderValue mediaType)
        {
            if (request == null)
            {
                throw new ArgumentNullException(nameof(request));
            }

            if (value == null)
            {
                throw new ArgumentNullException(nameof(value));
            }

            if (mediaType == null)
            {
                throw new ArgumentNullException(nameof(mediaType));
            }

            //AGG
            var formatter = new PerRequestJsonSettingsFormatter();

            return request.CreateResponse(statusCode, value, formatter, mediaType);
        }


        public static HttpResponseMessage CreateResponse<T>(
            this HttpRequestMessage request,
            HttpStatusCode statusCode,
            T value,
            MediaTypeFormatter formatter)
        {
            if (request == null)
            {
                throw new ArgumentNullException(nameof(request));
            }

            if (value == null)
            {
                throw new ArgumentNullException(nameof(value));
            }

            if (formatter == null)
            {
                throw new ArgumentNullException(nameof(formatter));
            }

            return request.CreateResponse(statusCode, value, formatter, (MediaTypeHeaderValue)null);
        }

        public static HttpResponseMessage CreateResponse<T>(
            this HttpRequestMessage request,
            HttpStatusCode statusCode,
            T value,
            MediaTypeFormatter formatter,
            string mediaType)
        {
            if (request == null)
            {
                throw new ArgumentNullException(nameof(request));
            }

            if (value == null)
            {
                throw new ArgumentNullException(nameof(value));
            }

            if (formatter == null)
            {
                throw new ArgumentNullException(nameof(formatter));
            }

            var mediaTypeHeader = mediaType != null ? new MediaTypeHeaderValue(mediaType) : null;
            return request.CreateResponse(statusCode, value, formatter, mediaTypeHeader);
        }


        public static HttpResponseMessage CreateResponse<T>(
            this HttpRequestMessage request,
            HttpStatusCode statusCode,
            T value,
            MediaTypeFormatter formatter,
            MediaTypeHeaderValue mediaType)
        {
            if (request == null)
            {
                throw new ArgumentNullException(nameof(request));
            }

            if (formatter == null)
            {
                throw new ArgumentNullException(nameof(formatter));
            }

            var response = new HttpResponseMessage(statusCode)
            {
                RequestMessage = request,
            };

            response.Content = new ObjectContent<T>(value, formatter, mediaType);

            return response;
        }
        */
    }
}
