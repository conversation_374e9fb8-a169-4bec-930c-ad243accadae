using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using Extric.Towbook.API.Models;
using Microsoft.AspNetCore.Mvc.ModelBinding.Validation;

namespace Extric.Towbook.API
{
    public class UsernameExistsAttribute : ValidationAttribute, IClientModelValidator
    {
        public override bool IsValid(object value)
        {
            // don't obther performing server validation.
            // client side validation should have caught it,
            // and the extric.towbook.user.save method will catch duplicates.

            // string username = (string)value;
            // if (!String.IsNullOrEmpty(username) && (User.IsUsernameTaken(username))) {
            //    return false;
            // }
            return true;
        }

        public void AddValidation(ClientModelValidationContext context)
        {
            var errorMessage = FormatErrorMessage(context.ModelMetadata.GetDisplayName());
            MergeAttribute(context.Attributes, "data-val-usernameexists", errorMessage);
        }

        public static bool MergeAttribute(
            IDictionary<string, string> attributes,
            string key,
            string value)
        {
            if (attributes.ContainsKey(key))
            {
                return false;
            }
            attributes.Add(key, value);
            return true;
        }
    }


    public class PasswordNotTheSameAttribute : ValidationAttribute, IClientModelValidator
    {
        private static string errorMessage = "";
        protected override ValidationResult IsValid(object value, ValidationContext validationContext)
        {
            var password = value?.ToString();
            string username = (String) GetPropValue(validationContext.ObjectInstance, "Username");
            if (!String.IsNullOrEmpty(username) && String.Equals(username, password, StringComparison.OrdinalIgnoreCase))
            {
                return new ValidationResult(errorMessage);
            }
            return ValidationResult.Success;
        }

        public void AddValidation(ClientModelValidationContext context)
        {
            errorMessage = FormatErrorMessage(context.ModelMetadata.GetDisplayName());
            UsernameExistsAttribute.MergeAttribute(context.Attributes, "data-val-passwordnotthesame", errorMessage);
        }
        private static object GetPropValue(object src, string propName)
        {
            return src.GetType().GetProperty(propName).GetValue(src, null);
        }

    }

    public class PasswordNotPasswordAttribute : ValidationAttribute, IClientModelValidator
    {
        public override bool IsValid(object value)
        {
            if (value != null && value.ToString().ToLowerInvariant().Equals("password"))
            {
                return false;
            }
            return true;
        }

        public void AddValidation(ClientModelValidationContext context)
        {
            var errorMessage = FormatErrorMessage(context.ModelMetadata.GetDisplayName());
            UsernameExistsAttribute.MergeAttribute(context.Attributes, "data-val-passwordnotpassword", errorMessage);
        }

    }


    public class PasswordLengthAttribute : ValidationAttribute, IClientModelValidator
    {
        public override bool IsValid(object value)
        {
            return value == null || string.IsNullOrWhiteSpace(value.ToString()) || value.ToString().Length >= 8;
        }

        public void AddValidation(ClientModelValidationContext context)
        {
            var errorMessage = this.ErrorMessage;
            UsernameExistsAttribute.MergeAttribute(context.Attributes, "data-val-passwordlength", errorMessage);
        }
    }

}
