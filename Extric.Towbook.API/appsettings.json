{
  "Swagger": {
    "ApiToken": "ZTNiNzJhNTAtOWQ1Ny00YzQzLTkxZjUtOGEyZDRlOTQzOGFi"
  },
  "appDomain": "https://azapp.towbook.dev",
  "menuProxy": "/api/integration/accounting/providers/quickbooks/MenuProxy",
  "grantUrl": "/api/integration/accounting/providers/quickbooks/OauthGrant",
  "oauth_callback_url": "/api/integration/accounting/providers/quickbooks/OauthResponse",
  "SlotIdentifier": "01",
  // Towbook - Swipe integration
  "Stripe": {
    "PublicKey": "pk_test_QAfuzEq7BxGbSBDdGH3ofUWZ",
    "SecretKey": "sk_test_83aigoEPIRdTjKAnO7VM0Yef"
  },
  // ParkingPermits API - JWT authentication
  "PermitRequestsApi": {
    "SecretJwtKey": "MFswDQYJKoZIhvcNAQEBBQADSgAwRwJAQoIPgOhuXz22QXVYzPXH9fYHOgDm7nUG"
  },
  "Square": {
    "ApplicationId": "*****************************",
    "ApplicationSecret": "sq0csp-dlL9Mj1d8ngg3AHSWABRFiH0k28qNMUFjrz6OZ6YHRg",
    "AccessToken": "EAAAEKGlvUZpzHx-sCfGnx2cycbSZzm6dPR84p1-QzBOwcsR0iS8Vf2hH3YuW_fo",
    "WebhookSignatureKey": "TvayThX8C4SJOijyxc_A5Q",
    "Sandbox": {
      "ApplicationId": "*************************************",
      "ApplicationSecret": "sandbox-sq0csb-mqKt56UG85C_U_w9CmkaQ_PsPewLniMebxy2uBZR--Q",
      "AccessToken": "EAAAEJbXqEQjLfhDlFE5aRuxsFjuOdElbb0R07HTGdzJqx2cSh3wiJO7ByH0OMv4",
      "LocationId": "XPSRDD6DQ003P"
    }
  },
  "SearchService": {
    "Name": "",
    "Admin.ApiKey": "",
    "Client.ApiKey": ""
  },
  "ConnectionStrings": {
    "Database": "",
    //"Database.Azure": "",
    "Microsoft.ServiceBus": ""
    //"Microsoft.ServiceBusNC": "",
    //"Microsoft.ServiceBusWC": "",
    //"Microsoft.ServiceBusDev": ""
  },
  "Redis": {
    "Servers": "",
    "Credentials": "",
    "SentinelServiceName": "",
    "ConnectionPoolSize": 1
  },
  "DataProtection": {
    "Certificate": "-----BEGIN CERTIFICATE-----\nMIIDazCCAlOgAwIBAgIUJef+amzmuJkc9tucAlOQJkqJETwwDQYJKoZIhvcNAQEL\nBQAwRTELMAkGA1UEBhMCQVUxEzARBgNVBAgMClNvbWUtU3RhdGUxITAfBgNVBAoM\nGEludGVybmV0IFdpZGdpdHMgUHR5IEx0ZDAeFw0yNTAxMjExNjQ0NTBaFw0yNjAx\nMjExNjQ0NTBaMEUxCzAJBgNVBAYTAkFVMRMwEQYDVQQIDApTb21lLVN0YXRlMSEw\nHwYDVQQKDBhJbnRlcm5ldCBXaWRnaXRzIFB0eSBMdGQwggEiMA0GCSqGSIb3DQEB\nAQUAA4IBDwAwggEKAoIBAQDDlFFMTyX9lfHkXtmcpNt8KXXJe6xOuMSJ6S29sMEw\ngeDO+uBv+aQqygp9DNx5a35Sb3grQ6KK6bNpH9wLC43L7xr8IZ6yqIRYxAtLQAyU\ne3Cu+xSTQre1OTaQaowGNUimZQjopu8TjhqXW6C10hJK5Q0JXFmfXWfJRQm8MlPj\noWhuVzM1CldWA99oxQSbY+/1oHwx4p9oHq0EunQmu0lvdfLubFTaVr4zxeGJcDgW\npMDmM/MIv8yZOcIRnJJHHya2EjjLWvZYrSz4tQ0xs83sh7qKToWYBoy8UwT8+kRv\ns8d5XzQhQGHdoyx09gyiPw27nhaSI3nzb4KuuKZQHER/AgMBAAGjUzBRMB0GA1Ud\nDgQWBBQNapQWv435bQN1XChFwRWhoFZtMjAfBgNVHSMEGDAWgBQNapQWv435bQN1\nXChFwRWhoFZtMjAPBgNVHRMBAf8EBTADAQH/MA0GCSqGSIb3DQEBCwUAA4IBAQA6\nlpBaM7fRUXQJeKJrw39JKZl/mHUv6ACS6gDOSRPlxihCUls8XdQ6rD8J7WTkNNwh\nhAVWm135O0x8vWEOlFt4MbnVyzusw441VxpxUw5axz7jmHoP7KF5zHC5hFiOfElL\nhOVqD+/AxuaF44yFrIzYHNCWM9nGVt9SL8g4OnqkgFHMEt71ivUtU4jxMBhkUd7B\nokLfxD82plrJJooZFCj8x7upJg5945EfmMdLi2dDKcN/oPZLd2JFP3gidSCTK3Rr\nQb4Z3LJPapxI4qv0yHh5BXp5NzGArkLTQ/COURRnAaGtZoYYW0UYF0iLLlVmcbkE\n658juZOz83wT2/XHIhhm\n-----END CERTIFICATE-----\n",
************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
  },
  "CosmosDb": {
    "Url": "",
    "AuthKey": "",
    "Database": ""
  },
  "ActivityLogDb": {
    "Url": "",
    "AuthKey": "",
    "Database": "",
    "Collection": "",
    "Mode": ""
  },
  "EventNotificationDb": {
    "Url": "",
    "AuthKey": "",
    "Database": "",
    "Collection": ""
  },
  "AllowedHosts": "*",
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.AspNetCore.Server.Kestrel": "Information",
      "Microsoft.Hosting.Lifetime": "Information"
    }
  },
  "Smtp": {
    "Host": "smtp.sendgrid.net",
    "Port": 587,
    "Username": "apikey",
    "Password": "*********************************************************************",
    "EnableSsl": true
  },
  "Loggly": {
    "CustomerToken": "************************************",
    "ApplicationName": "Extric.Towbook.API",
    "Transport": {
      "EndpointHostname": "logs-01.loggly.com",
      "EndpointPort": 6514,
      "LogTransport": "SyslogSecure"
    },
    "SimpleTagValue": "mojix-dev"
  },
  "Pdf": {
    "Host": "",
    "Port": 80,
    "DefaultBaseUrl": ""
  },
  "Quickbooks": {
    "menuProxy": "/api/integration/accounting/providers/quickbooks/MenuProxy",
    "grantUrl": "/api/integration/accounting/providers/quickbooks/OauthGrant",
    "oauth_callback_url": "/api/integration/accounting/providers/quickbooks/OauthResponse",
    "clientId": "ABJMVZl5imV9XTN0vop3GEwHToFuzhCO64fbQJl6jcbd1jiKjZ",
    "clientSecret": "Fm3QWBdMgLBVFOpyr15TadLbtqCbbVJVUyrYo0zU",
    "redirectUri": "https://azapp.towbook.dev/api/integration/accounting/providers/quickbooks/OauthResponse",
    "environment": "sandbox",
    "Client_Url_Base": "https://azapp.towbook.dev/api/"
  },
  "Authentication": {
    "ValidationKey": "C50B3C89CB21F4F1422FF158A5B42D0E8DB8CB5CDA1742572A487D9401E3400267682B202B746511891C1BAF47F8D25C07F6C39A104696DB51F17C529AD3CABE",
    "DecryptionKey": "8A9BE8FD67AF6979E7D20198CFEA50DD3D3799C77AF2B72F"
  },
  "PushNotifications": {
    //"Disable": "1",
    //"ForceAllowSend": "1",
    //"DisableIOS": "1",
    //"DisableGetRegistrations": "1"
  },
  "SmsServiceProvider": "Twilio",
  "Twilio": {
    //"ServiceSid": "",
    "ApiKey": "**********************************",
    "ApiSecret": "********************************",
    "PushCredentialSid": "CR6172891466e0b784aeb12f4b30d24272",
    "AccountSid": "**********************************",
    "AuthToken": "6fcbc12f151dc2ae13ca891c47f31055",
    "Numbers": "+***********,+***********,+***********,+***********,+***********,+***********,+***********,+***********,+***********,+***********,+***********,+***********,+***********,+***********,+***********,+***********,+***********"
  },
  "Bandwidth": {
    //"AccountId": "5005709",
    //"ApiSecret": "decdc92978f93ce2c64388a3e034739641103519a33692e4",
    //"ApiToken": "7fca852aa15bcd632434528ea60f13ae8cf33c5032f16439",
    //"ApplicationId": "95a8c352-b407-4d92-bc76-40e7a738ae0e",
    //"Numbers": "+***********,+***********,+***********,+***********,+***********,+***********,+***********,+***********,+***********,+***********,+***********,+***********,+***********,+***********,+***********,+***********,+***********,+***********,+***********,+***********"
  },
  "Azure": {
    "KeyVault": {
      //"VaultUrl": "",
      //"ClientId": "",
      //"ClientSecret": "",
      //"TenantId": ""
    }
  },
  "MotorClubs": {
    "Agero": {
      //"UrlBase": "https://agero.test/towbook",
      "ConsumerKey": "CONSUMER_KEY",
      "ConsumerSecret": "CONSUMER_SECRET",
      "ConsumerBase64": "CONSUMER_BASE64"
    },
    "Allstate": {
      "Certificate": {
        //"AzureName": "",
        //"LocalThumbprint": ""
      },
      "Staging": {
        //"UrlBase": "",
        //"ApiroUrl": "",
        //"ApiglextUrl": "",
        //"ClientId": "",
        //"ClientSecret": ""
      },
      "Production": {
        //"UrlBase": "",
        //"ApiroUrl": "",
        //"ApiglextUrl": "",
        //"ClientId": "",
        //"ClientSecret": ""
      }
    },
    "Swoop": {
      "Staging": {
        //"UrlBase": "https://staging.joinswoop.com"
      },
      "Production": {
        //"UrlBase": "https://api.joinswoop.com"
      }
    },
    "Sykes": {
      "Staging": {
        //"UrlBase": "https://sta-api.sykesassistance.com/edispatch/v1/"
      },
      "Production": {
        //"UrlBase": "https://prd-api.sykesassistance.com/edispatch/api/v1/"
      }
    },
    "Urgently": {
      "Staging": {
        //"UrlBase": "https://dev01-apis.urgent.ly/v3/b2b/"
      },
      "Production": {
        //"UrlBase": "https://towbook-apis.urgent.ly/v3/b2b/"
      }
    }
  },
  "Towbook": {
    "WebAppUrl": "http://localhost",
    "Maps": {
      "Directions": {
        //"Disable": "0"
      }
    },
    "CallRequests": {
      //"Disable": "0"
    },
    "Gps": {
      "TomTom": {
        //"Disable": "0"
      }
    }
  },
  //"Production": {
  //  "Rims": "VALUE"
  //  "Towbook": "VALUE"
  //},
  //"SlotIdentifier": "VALUE"
  "Cache": {
    "CacheToUse": "web",
    "DependencyManagerToUse": "Default",
    "DistributedCacheName": "Towbook",
    "DistributedCacheServers": "localhost:6379",
    "IsCacheDependencyManagementEnabled": "True",
    "IsCacheEnabled": "True",
    "LoggingLevel": "None"
  }
}