/*using Extric.Towbook.WebShared;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Mail;
using System.Text;
using System.Web;
using System.Web.Http.Filters;

namespace Extric.Towbook.API
{
    public class UnhandledExceptionFilter : ExceptionFilterAttribute
    {
        public override void OnException(HttpActionExecutedContext context)
        {
            using (MailMessage msg = new MailMessage())
            {

                msg.From = new MailAddress("<EMAIL>",
                    "Towbook API Error Reporter (" + Environment.MachineName + ")");
                msg.To.Add("<EMAIL>");
                msg.Priority = MailPriority.High;
                msg.Subject = "Towbook REST API Error Report";

                msg.IsBodyHtml = false;
                msg.Body = "API Error Report\n\n";

                var Server = HttpContext.Current.Server;
                var Request = HttpContext.Current.Request;

                try
                {
                    if (WebGlobal.CurrentUser != null)
                    {
                        msg.Body += "Customer Details\n";
                        msg.Body += "---------------\n";
                        msg.Body += "User: " + WebGlobal.CurrentUser.Username + " (UserId=" + WebGlobal.CurrentUser.Id + ")\n";
                        msg.Body += "Company: " + WebGlobal.CurrentUser.Company.Name + " (CompanyId=" + WebGlobal.CurrentUser.Company.Id + ")\n";
                        msg.Body += "Phone: " + Core.FormatPhone(WebGlobal.CurrentUser.Company.Phone) + " (" + WebGlobal.CurrentUser.Company.State + ")\n";
                        msg.Body += "\n\n";
                    }
                }
                catch { }

                try
                {
                    msg.Body += "Exception Details\n";
                    msg.Body += "-----------------\n";
                    msg.Body += "Message: " + context.Exception.Message + "\n\n";
                    msg.Body += "Source: " + context.Exception.Source + "\n";
                    msg.Body += "Stack Trace: " + context.Exception.StackTrace.ToString() + "\n";
                }
                catch { }

                msg.Body += "\r\n";

                try
                {

                    msg.Body += "Related Details\n";
                    msg.Body += "---------------\n";
                    msg.Body += "Path: " + Request.PhysicalPath.ToString() + "\n";
                    msg.Body += "Remote IP: " + Request.ServerVariables["REMOTE_ADDR"].ToString() + "\n";
                    msg.Body += "Requesting IP: " + WebGlobal.GetRequestingIp() + "\n";

                    if (HttpContext.Current.Items["Exceptionbook:RequestId"] != null)
                    {
                        msg.Body += "HTTP Request ID: " + HttpContext.Current.Items["Exceptionbook:RequestId"] + "\n";
                    }


                    msg.Body += "\nQueryString: {\n";

                    if (Request.QueryString.Keys.Count > 0)
                    {

                        foreach (string key in Request.QueryString.Keys)
                        {
                            msg.Body += "\"" + key + "\": \"" + Request.QueryString[key] + "\", \n";
                        }
                    }

                    msg.Body += "},\nPOST Params: {\n";
                    if (Request.Form.Keys.Count > 0)
                    {
                        foreach (string key in Request.Form.Keys)
                        {
                            msg.Body += "\"" + key + "\": \"" + Request.Form[key] + "\", \n";
                        }
                    }

                    msg.Body += "},\n\nHTTP Cookies: {\n";

                    foreach (string key in Request.Cookies.Keys)
                    {
                        msg.Body += "\"" + key + "\": \"" + Request.Cookies[key].Value + "\", \n";
                    }

                    msg.Body += "},\n\nHTTP Request Headers: {\n";

                    foreach (string key in Request.Headers.Keys)
                    {
                        msg.Body += "\"" + key + "\": \"" + Request.Headers[key] + "\", \n";
                    }

                    msg.Body += "},\n\nHTTP Server: {\n";

                    foreach (string key in Request.ServerVariables.Keys)
                    {
                        msg.Body += "\"" + key + "\": \"" + Request.ServerVariables[key] + "\", \n";
                    }


                    msg.Body += "}";
                }
                catch
                {
                }


                #region attempt to associate error with account via note

                try
                {
                    if (WebGlobal.CurrentUser != null)
                    {
                        Extric.Towbook.Company.Note n = new Extric.Towbook.Company.Note();
                        n.CompanyId = WebGlobal.CurrentUser.Company.Id;
                        n.User = WebGlobal.CurrentUser;
                        n.Content = "[ERROR REPORT]\n" +
                            msg.Body.Substring(msg.Body.IndexOf("Exception Details"));

                        if (n.Content.Length > 2000)
                            n.Content = n.Content.Substring(0, 2000);
                        n.Save();
                    }
                }
                catch (Exception e)
                {
                    msg.Body += "\r\nWARNING: FAILED TO ATTACH ERROR INFO TO COMPANY HISTORY TABLE: " + e.ToString();
                }
                #endregion

                try
                {
                    using (SmtpClient sc = new SmtpClient())
                    {
                        sc.Send(msg);
                    }
                }
                catch
                {
                    // don't throw an email error. 
                }
            }
            base.OnException(context);
        }
    }
    
}
*/