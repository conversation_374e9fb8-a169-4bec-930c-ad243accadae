using System;
using System.Globalization;
using System.IO.Compression;
using System.Net.Http;
using System.Security.Cryptography.X509Certificates;
using System.Threading;
using Extric.Towbook.API.Config;
using Extric.Towbook.API.Formatters;
using Extric.Towbook.API.Integration.Gateway.AutoRescueApi;
using Extric.Towbook.API.Integration.MotorClubs.Aaa.HttpModules;
using Extric.Towbook.API.Integration.MotorClubs.AaaAce.HttpModules;
using Extric.Towbook.API.Integration.MotorClubs.AaaNe.HttpModules;
using Extric.Towbook.API.Integration.MotorClubs.Honk.HttpModules;
using Extric.Towbook.API.Integration.MotorClubs.Nac.HttpModules;
using Extric.Towbook.API.Integration.MotorClubs.Nsd.HttpModules;
using Extric.Towbook.API.Integration.MotorClubs.OON.Agero.HttpModules;
using Extric.Towbook.API.Integration.MotorClubs.RoadsideProtect.HttpModules;
using Extric.Towbook.API.Integration.MotorClubs.Sykes.HttpModules;
using Extric.Towbook.API.Integration.MotorClubs.Urgently.HttpModules;
using Extric.Towbook.Configuration;
using Extric.Towbook.PermitRequests.API;
using Extric.Towbook.Utility;
using Extric.Roadside.API;
using Extric.Towbook.WebShared;
using Extric.Towbook.WebShared.Net5;
using Loggly.Config;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.DataProtection;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.Features;
using Microsoft.AspNetCore.Mvc.Formatters;
using Microsoft.AspNetCore.ResponseCompression;
using Microsoft.AspNetCore.Rewrite;
using Microsoft.AspNetCore.Server.Kestrel.Core;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Net.Http.Headers;
using Microsoft.OpenApi.Models;
using Newtonsoft.Json.Serialization;
using AspNetCore.LegacyAuthCookieCompat;
using Extric.Towbook.API.Integration.MotorClubs.Bcaa.HttpModules;
using Extric.Towbook.WebWrapper;

namespace Extric.Towbook.API;

public class Startup
{
    public IConfiguration Configuration { get; }

    public Startup(IConfiguration configuration)
    {
        Configuration = configuration;
        Configuration.ConfigureHelper();
    }

    // This method gets called by the runtime. Use this method to add services to the container.
    public void ConfigureServices(IServiceCollection services)
    {
        ThreadPool.SetMinThreads(625, 500);

        Web.HttpContextFactory.Instance = new HttpContextNet5();

        Configuration.GetSection("Quickbooks").Bind(new QuickbooksConfiguration());
        Configuration.GetSection("DataProtection").Bind(new DataProtectionConfiguration());

        // Clean up for docker run with .env file
        //var formattedCert = DataProtectionConfiguration.Certificate.Replace("\\n", "\n");
        //var formattedPrivateKey = DataProtectionConfiguration.PrivateKey.Replace("\\n", "\n");

        //We are calling this method to initialize the PdfClient with the proper http client,
        //Since the PdfClient is a derived static class and its constructor is not automatical called,
        //So we need to call this method before using it.
        //Case documentation: https://stackoverflow.com/questions/4652454/whats-the-best-way-to-ensure-a-base-classs-static-constructor-is-called
        PdfClient.Init();

        services.ConfigureCore();

//         services.AddDataProtection()
//                 .SetApplicationName("towbook")
//                 .PersistKeysToStackExchangeRedis(Core.GetRedisConnection())
//                 .ProtectKeysWithCertificate(
//                     X509Certificate2.CreateFromPem(
//                         formattedCert, formattedPrivateKey));

        //for IHttpClientFactory
        services.AddHttpClient();

        //single instance of HttpClient
        // Singleton instance of HttpClient for MotorClub's Rest Clients.
        services.AddSingleton(config =>
        {
            return new HttpClient(
                new SocketsHttpHandler
                {
                    PooledConnectionLifetime = TimeSpan.FromMinutes(5),
                    PooledConnectionIdleTimeout = TimeSpan.FromSeconds(30)
                }
            );
        });
        services.AddScoped<IRequestContext, NullSafeConcurrentDictionary>();

        services.AddControllers(options =>
        {
            options.AllowEmptyInputInBodyModelBinding = true;
            options.ModelBinderProviders.Insert(0, new CustomActionValueBinder());
            options.InputFormatters.Insert(0, new Towbook.Formatters.PerJsonSettingsInputFormatter());

            // HttpResponseMessageOutputFormatter needs to be first, to avoid catching basic response by other Formatters
            options.OutputFormatters.Insert(0, new WebShared.Formatters.HttpResponseMessageOutputFormatter());

            options.OutputFormatters.Add(new Towbook.Formatters.XlsxMediaTypeOutputFormatter(new MediaTypeHeaderValue("application/xlsx")));
            options.OutputFormatters.Add(new Towbook.Formatters.PerJsonSettingsOutputFormatter());
            options.OutputFormatters.Add(new Towbook.Formatters.SoapMediaTypeOutputFormatter());

            options.OutputFormatters.RemoveType<XmlDataContractSerializerOutputFormatter>();
            options.OutputFormatters.RemoveType<HttpNoContentOutputFormatter>();

            options.FormatterMappings.SetMediaTypeMappingForFormat("xlsx", new MediaTypeHeaderValue("application/xlsx"));

            options.Filters.Add(typeof(WebShared.Filters.HttpResponseExceptionActionFilter));
            //TODO is this order correct? 
            options.Filters.Add(typeof(ValidateFilterAttribute));

            options.Conventions.Add(new ComplexTypeConvention());

        }).AddJsonOptions(options =>
        {
        }).AddNewtonsoftJson(options =>
        {
            options.SerializerSettings.ContractResolver = new CamelCasePropertyNamesContractResolver();
        });

        services.AddSwaggerGen(options =>
        {
            options.SwaggerDoc("v1", new OpenApiInfo { Title = "Towbook Rest API Service", Version = "v1" });
            options.CustomSchemaIds(type => type.ToString());
        });

        services.AddHttpContextAccessor();

        // TODO: [CHECK] Set up AllowSynchronousIO to True for HttpResponseException with JsonMediaTypeFormatter (Kestrel and IIS Server)
        services.Configure<KestrelServerOptions>(options =>
        {
            options.AllowSynchronousIO = true;
        });
        services.Configure<IISServerOptions>(options =>
        {
            options.AllowSynchronousIO = true;
        });
        services.AddResponseCompression(options =>
        {
            options.EnableForHttps = true;
            options.Providers.Add<GzipCompressionProvider>();
            options.Providers.Add<BrotliCompressionProvider>();
        });
        services.Configure<GzipCompressionProviderOptions>(options =>
        {
            options.Level = CompressionLevel.SmallestSize;
        });
        services.Configure<BrotliCompressionProviderOptions>(options =>
        {
            options.Level = CompressionLevel.Fastest;
        });

        services.Configure<FormOptions>(options =>
        {
            // Maximum default number of form values allowed is 1024.  Increased to 4000.
            options.ValueCountLimit = 4000;
        });

        // This service registers the output formatter for the Permits api that
        // is used to serialize the response to the client based on the JWT token/permit user.
        services.AddPermitApiRequestServices();
    }

    // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
    public void Configure(IApplicationBuilder app, IWebHostEnvironment env, IHostApplicationLifetime appLifetime)
    {
        app.ConfigureHelper();
        //HttpConfiguration config = new HttpConfiguration();

        //config.Formatters.XmlFormatter.UseXmlSerializer = true;
        //config.Formatters.Remove(config.Formatters.JsonFormatter);

        // API Token Middleware for Swagger
        app.UseWhen(context => context.Request.Path.StartsWithSegments("/api/swagger"), appBuilder =>
        {
            appBuilder.Use(async (context, next) =>
            {
                var cookieName = Core.GetAppSetting("Authentication:CookieName", ".XTBAUTHSEC");
                var tokenValid = false;
                User userDb = null;

                if (context.Request.Headers["X-Api-Token"].Count > 0)
                {
                    var tokenArrived = context.Request.Headers["X-Api-Token"];
                    if (tokenArrived == Core.GetAppSetting("Swagger:ApiToken"))
                        tokenValid = true;
                    else
                        userDb = await User.GetByTokenAsync(tokenArrived);
                }
                else if (context.Request.Cookies[cookieName] != null)
                {
                    byte[] decryptionKeyBytes = HexUtils.HexToBinary(Core.GetAppSetting("Authentication:DecryptionKey"));
                    byte[] validationKeyBytes = HexUtils.HexToBinary(Core.GetAppSetting("Authentication:ValidationKey"));

                    var legacyFormsAuthenticationTicketEncryptor = new LegacyFormsAuthenticationTicketEncryptor(decryptionKeyBytes, validationKeyBytes, ShaVersion.Sha1, CompatibilityMode.Framework20SP2);
                    var ticket = legacyFormsAuthenticationTicketEncryptor.DecryptCookie(context.Request.Cookies[cookieName]);
                    userDb = await User.GetByTokenAsync(ticket.UserData);
                }

                if (!tokenValid & (userDb is null || userDb.Type != User.TypeEnum.SystemAdministrator))
                {
                    context.Response.StatusCode = 401;
                    context.Response.ContentType = "text/plain";
                    await context.Response.WriteAsync("Invalid Security Token. Please re-authenticate");
                    return;
                }

                await next();
            });
        });

        if (env.IsDevelopment())
        {
            app.UseDeveloperExceptionPage();
        }

        app.UseSwagger(options => options.RouteTemplate = "api/swagger/{documentName}/swagger.json");
        app.UseSwaggerUI(options =>
        {
            options.SwaggerEndpoint("v1/swagger.json", "v1");
            options.RoutePrefix = "api/swagger";
        });

       /* var ro = new RewriteOptions()
            .AddRewrite(@"^calls/(\d+)/{2}payments$",
            "calls/$1/payments", false);

        app.UseRewriter(ro);*/

        app.Use(async (context,next) =>
        {
            var url = context.Request.Path.Value;
            if (url.Contains("//"))
            {
                var newUrl = url.Replace("//", "/");
                Console.WriteLine("Replace " + url + " with " + newUrl);
                context.Request.Path = newUrl;
            }
            await next();
        });

        app.ConfigureExceptionHandlerMiddleware();

        app.UsePathBase("/api");
        app.UseRouting();

        app.UseAuthentication();
        app.UseAuthorization();

        app.UsePreSendRequestHeaders();


        app.UseWhen(context => context.Request.Path.StartsWithSegments(new PathString("/permits")), branch =>
        {
            branch.UsePermitRequestsAuthentication();
        });

        app.UseWhen(context => context.Request.Path.StartsWithSegments(new PathString("/receivers/oon/agero")), branch =>
        {
            branch.UseOonAgeroApiAuthentication();
        });

        app.UseHonkApiAuthentication();
        app.UseSykesAuthentication();

        app.UseWhen(context => context.Request.Path.StartsWithSegments(new PathString("/receivers/urgently")), branch =>
        {
            branch.UseUrgentlyAuthentication();
        });

        app.UseWhen(context => context.Request.Path.StartsWithSegments(new PathString("/receivers/aaa/aca")), branch =>
        {
            branch.UseAaaAcaAuthentication();
        });

        app.UseWhen(context => context.Request.Path.StartsWithSegments(new PathString("/receivers/aaa/ace")), branch =>
        {
            branch.UseAaaAceAuthentication();
        });

        app.UseWhen(context => context.Request.Path.StartsWithSegments(new PathString("/receivers/aaa/northeast")), branch =>
        {
            branch.UseAaaNortheastAuthentication();
        });

        app.UseWhen(context => context.Request.Path.StartsWithSegments(new PathString("/receivers/aaa/bcaa")), branch =>
        {
            branch.UseBcaaAuthentication();
        });


        app.UseWhen(context => context.Request.Path.StartsWithSegments(new PathString("/receivers/aaa")), branch =>
        {
            branch.UseAaaAuthentication();
        });

        app.UseWhen(context => context.Request.Path.StartsWithSegments(new PathString("/receivers/autorescue")), branch =>
        {
            branch.UseAutoRescueAuthentication();
        });

        app.UseTrxAuthentication();
        app.UseNsdAuthentication();
        app.UseNacAuthentication();
        app.UseStackThreeAuthentication();

        app.UseWhen(context => context.Request.Path.StartsWithSegments(new PathString("/receivers/roadsideprotect")), branch =>
        {
            branch.UseRoadsideProtectAuthentication();
        });

        app.UseWhen(context =>
               !context.Request.Path.StartsWithSegments(new PathString("/receivers/oon/agero"))
            && !context.Request.Path.StartsWithSegments(new PathString("/receivers/honk"))
            && !context.Request.Path.StartsWithSegments(new PathString("/receivers/sykes"))
            && !context.Request.Path.StartsWithSegments(new PathString("/receivers/urgently"))
            && !context.Request.Path.StartsWithSegments(new PathString("/receivers/trx"))
            && !context.Request.Path.StartsWithSegments(new PathString("/receivers/nac"))
            && !context.Request.Path.StartsWithSegments(new PathString("/receivers/stackthree"))
            && !context.Request.Path.StartsWithSegments(new PathString("/receivers/nsd"))
            && !context.Request.Path.StartsWithSegments(new PathString("/receivers/aaa"))
            && !context.Request.Path.StartsWithSegments(new PathString("/permits"))
            && !context.Request.Path.StartsWithSegments(new PathString("/roadside/surveys"))
            && !context.Request.Path.StartsWithSegments(new PathString("/roadside/dispatches"))
            && !context.Request.Path.StartsWithSegments(new PathString("/receivers/autorescue"))
            , branch =>
            {
                branch.ConfigureApiAuthentication();
            });


        //app.Use(async (context, next) =>
        //{
        //    context.Response.Headers["Server"] = "Extric-Towbook/5.0";
        //    await next.Invoke();
        //});
        app.UseResponseCompression();

        //app.ConfigureLoggingMiddleware();

        app.UseEndpoints(endpoints =>
        {
            endpoints.MapControllers();
        });

        appLifetime.ApplicationStopped.Register(async () =>
        {
            await Towbook.Integration.MotorClubs.Services.ServiceBusHelper.Cleanup();
            await EventNotifications.ENServiceBusHelper.Cleanup();
        });

        // Temporal fix for a bad time format in docker using icu-lib 73.2
        var cultureInfo = new CultureInfo("en-US");
        cultureInfo.DateTimeFormat.LongTimePattern = "h:mm:ss tt";
        CultureInfo.DefaultThreadCurrentCulture = cultureInfo;
        CultureInfo.DefaultThreadCurrentUICulture = cultureInfo;

        //setup static accessor 
        IHttpContextAccessor httpContextAccessor = app.ApplicationServices.GetRequiredService<IHttpContextAccessor>();
        IWebHostEnvironment webHostEnvironment = app.ApplicationServices.GetRequiredService<IWebHostEnvironment>();

        Web.HttpContext.Configure(httpContextAccessor, webHostEnvironment, app.ApplicationServices);

        WebApiApplication.Application_Start();

        //Web.HttpContextFactory.Instance = new HttpContextNet5();
    }
}
