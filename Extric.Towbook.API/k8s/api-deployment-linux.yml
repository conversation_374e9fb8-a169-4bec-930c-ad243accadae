apiVersion: v1
kind: Service
metadata:
  name: api-service
spec:
  selector:
    app: api-pod
  ports:
  - port: 80
    targetPort: 5000
  type: LoadBalancer
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: api
spec:
  selector:
    matchLabels:
      app: api-pod
  template:
    metadata:
      labels:
        app: api-pod
    spec:
      containers:
      - name: api-container
        image: towbookapiregistry.azurecr.io/api:latest
        resources:
          requests:
            memory: "512Mi"
            cpu: "200m"
          limits:
            memory: "2Gi"
            cpu: "800m"
        ports:
        - containerPort: 5000
        env:
        - name: ASPNETCORE_ENVIRONMENT
          value: "Development"
        - name: CORECLR_ENABLE_PROFILING
          value: "${CORECLR_ENABLE_PROFILING}"
        - name: CORECLR_PROFILER
          value: "{36032161-FFC0-4B61-B559-F6C5D41BAE5A}"
        - name: NEW_RELIC_APP_NAME
          value: "NewRelic-Api"
        - name: NEW_RELIC_LICENSE_KEY
          value: "ba6a3e6ef84b36cc9a86e7ed156ca2e1FFFFNRAL"
        - name: Pdf__Host
          value: "api-utils-service.default.svc.cluster.local"
        - name: Pdf__Port
          value: "80"
        - name: ConnectionStrings__Database
          valueFrom:
            secretKeyRef:
              name: kvtowbook-secrets
              key: ConnectionStrings-Database
        # - name: ConnectionStrings__Database.Azure
        #   valueFrom:
        #     secretKeyRef:
        #       name: kvtowbook-secrets
        #       key: ConnectionStrings-DatabaseAzure
        - name: ConnectionStrings__Microsoft.ServiceBus
          valueFrom:
            secretKeyRef:
              name: kvtowbook-secrets
              key: ConnectionStrings-MicrosoftServiceBus
        - name: Redis__Credentials
          valueFrom:
            secretKeyRef:
              name: kvtowbook-secrets
              key: Redis-Credentials
        - name: CosmosDb__Url
          valueFrom:
            secretKeyRef:
              name: kvtowbook-secrets
              key: CosmosDb-Url
        - name: CosmosDb__AuthKey
          valueFrom:
            secretKeyRef:
              name: kvtowbook-secrets
              key: CosmosDb-AuthKey
        - name: CosmosDb__Database
          valueFrom:
            secretKeyRef:
              name: kvtowbook-secrets
              key: CosmosDb-Database
        - name: EventNotificationDb__Url
          valueFrom:
            secretKeyRef:
              name: kvtowbook-secrets
              key: CosmosDb-Url
        - name: EventNotificationDb__AuthKey
          valueFrom:
            secretKeyRef:
              name: kvtowbook-secrets
              key: CosmosDb-AuthKey
        - name: EventNotificationDb__Database
          valueFrom:
            secretKeyRef:
              name: kvtowbook-secrets
              key: CosmosDb-Database
        - name: EventNotificationDb__Collection
          valueFrom:
            secretKeyRef:
              name: kvtowbook-secrets
              key: EventNotificationDb-Collection
        - name: EventNotificationDb__Mode
          valueFrom:
            secretKeyRef:
              name: kvtowbook-secrets
              key: EventNotificationDb-Mode
        - name: SearchService__Name
          valueFrom:
            secretKeyRef:
              name: kvtowbook-secrets
              key: SearchService-Name
        - name: SearchService__Admin.ApiKey
          valueFrom:
            secretKeyRef:
              name: kvtowbook-secrets
              key: SearchService-AdminApiKey
        - name: SearchService__Client.ApiKey
          valueFrom:
            secretKeyRef:
              name: kvtowbook-secrets
              key: SearchService-ClientApiKey
        - name: Smtp__Port
          valueFrom:
            secretKeyRef:
              name: kvtowbook-secrets
              key: Smtp-Port
        - name: Smtp__Host
          valueFrom:
            secretKeyRef:
              name: kvtowbook-secrets
              key: Smtp-Host
        - name: Smtp__Username
          valueFrom:
            secretKeyRef:
              name: kvtowbook-secrets
              key: Smtp-Username
        - name: Smtp__Password
          valueFrom:
            secretKeyRef:
              name: kvtowbook-secrets
              key: Smtp-Password
        - name: Smtp__EnabledSsl
          valueFrom:
            secretKeyRef:
              name: kvtowbook-secrets
              key: Smtp-EnabledSsl
        - name: Towbook__CookieDomain
          valueFrom:
            secretKeyRef:
              name: kvtowbook-secrets
              key: Towbook-CookieDomain
        - name: appDomain
          valueFrom:
            secretKeyRef:
              name: kvtowbook-secrets
              key: appDomain
        - name: DataProtection__PrivateKey
          valueFrom:
            secretKeyRef:
              name: kvtowbook-secrets
              key: DataProtection-PrivateKey
        - name: DataProtection__Certificate
          valueFrom:
            secretKeyRef:
              name: kvtowbook-secrets
              key: DataProtection-Certificate
        - name: Towbook__WebAppUrl
          value: "https://azapp.towbook.dev"
        volumeMounts:
          - name:  secrets-store
            mountPath:  "mnt/secrets-store"
            readOnly: true
      nodeSelector:
        selector: nplin
      volumes:
        - name: secrets-store
          csi:
            driver: secrets-store.csi.k8s.io
            readOnly: true
            volumeAttributes:
              secretProviderClass: "azure-kvtowbook-msi"