using System;
using Extric.Towbook.Accounts;
using Extric.Towbook.Integration;
using System.Collections.Generic;
using System.Linq;

namespace Extric.Towbook.API.Models
{
    public class MasterAccountModel
    {
        public int MasterAccountId { get; set; }
        public int? CompanyId { get; set; }
        public string Name { get; set; }
        public string Address { get; set; }
        public string City { get; set; }
        public string State { get; set; }
        public string Zip { get; set; }
        public string Phone { get; set; }
        public string Fax { get; set; }
        public string Email { get; set; }
        public string Country { get; set; }
        public decimal? Latitude { get;set; }
        public decimal? Longitude { get; set; }
        public int RelationshipId { get; set; }
        public bool IsDeleted { get; set; }
        public string[] DataSharing { get; set; } = Array.Empty<string>();
        public MasterAccountReason[] Reasons { get; set; }

        public static MasterAccountModel Map(MasterAccount d, User currentUser)
        {
            var ma = new MasterAccountModel();

            ma.MasterAccountId = d.Id;
            ma.Address = d.Address;
            ma.City = d.City;
            ma.CompanyId = d.CompanyId;
            ma.Country = d.Country;
            ma.Email = d.Email;
            ma.Fax = d.Fax;
            ma.IsDeleted = d.IsDeleted;
            ma.Latitude = d.Latitude;
            ma.Longitude = d.Longitude;
            ma.Name = d.Name;
            ma.Phone = d.Phone;
            ma.RelationshipId = d.RelationshipId;
            ma.State = d.State;
            ma.Zip = d.Zip;

            switch (ma.MasterAccountId)
            {
                case MasterAccountTypes.Agero:
                case MasterAccountTypes.OonAgero:
                case MasterAccountTypes.OonSwoop:
                    ma.DataSharing = new[] { "Status Updates", "GPS (While Call Call In Progress)", "Driver Name", "Photos"};
                    break;

                case MasterAccountTypes.Allstate:
                case MasterAccountTypes.OonAllstate:
                    ma.DataSharing = new[] { "Status Updates", "GPS (While Call In Progress)" };
                    break;

                case MasterAccountTypes.Urgently:
                    ma.DataSharing = new[] { "Status Updates", "GPS (Always)", "Driver Name and Phone Number", "Photos"};
                    break;

                case MasterAccountTypes.Geico:
                case MasterAccountTypes.Tesla:
                case MasterAccountTypes.Usac:
                case MasterAccountTypes.RoadsideProtect:
                case MasterAccountTypes.RoadAmerica:
                case MasterAccountTypes.Quest:
                case MasterAccountTypes.Nsd:
                    ma.DataSharing = new[] { "Status Updates", "GPS (While Call In Progress)" };
                    break;

                default:
                    ma.DataSharing = Array.Empty<string>();
                    break;
            }

            if (currentUser != null)
            {
                var blockPhotos = CompanyKeyValue.GetFirstValueOrNull(currentUser.CompanyId, Provider.Towbook.ProviderId, "PreventPhotoSharing") == "1";

                if (blockPhotos && ma.DataSharing != null)
                    ma.DataSharing = ma.DataSharing.Where(o => o != "Photos").ToArray();
            }

            return ma;
        }

        public static MasterAccountModel Map(MasterAccount d, IEnumerable<MasterAccountReason> reasons, User currentUser)
        {
            var r = Map(d, currentUser);
            
            r.Reasons = reasons.OrderBy(o => o.Name).ToArray();

            return r;
        }
    }
}