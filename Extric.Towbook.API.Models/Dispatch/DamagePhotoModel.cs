using System;
using Extric.Towbook.Dispatch;

namespace Extric.Towbook.API.Models
{
    public class DamagePhotoModel
    {
        public int? Id { get; set; }
        public int? CallId { get; set; }
        public int? DamageId { get; set; }
        public int? DamageRegionId { get; set; }
        public string ContentType { get; set; }
        public int? OwnerUserId { get; set; }
        public DateTime CreateDate { get; set; }

        public int AssetId { get; set; }
        public int RegionId { get; set; }
        public int TypeId { get; set; }

        public static DamagePhotoModel Map(EntryDamagePhoto o)
        {
            if (o == null)
                return null;

            var dp = new DamagePhotoModel();

            dp.Id = o.Id;
            dp.CallId = o.DispatchEntryId;
            dp.DamageId = o.DispatchEntryDamageId;
            dp.DamageRegionId = o.DispatchEntryDamageRegionId;
            dp.OwnerUserId = o.OwnerUserId;
            dp.ContentType = o.ContentType;
            dp.OwnerUserId = o.OwnerUserId;
            dp.CreateDate = o.CreateDate;

            //
            dp.AssetId = o.AssetId;
            dp.RegionId = o.RegionId;
            dp.TypeId = o.TypeId;

            return dp;
        }
    }
}