using System;

namespace Extric.Towbook.API.Models
{
    public class DriverReplayModel
    {
        public int Id { get; set; }
        public string DriverReplayGuid { get; set; }
        public int DispatchEntryId { get; set; }
        public int DriverId { get; set; }
        public int CompanyId { get; set; }
        public DateTime CreateDate { get; set; }
        public int OwnerUserId { get; set; }

        public static DriverReplayModel Map(DriverReplay driverReplay)
        {
            return new DriverReplayModel()
            {
                Id = driverReplay.Id,
                DriverReplayGuid = driverReplay.DriverReplayGuid.ToString("N").ToLower(),
                DispatchEntryId = driverReplay.DispatchEntryId,
                DriverId = driverReplay.DriverId,
                CompanyId = driverReplay.CompanyId,
                CreateDate = driverReplay.CreateDate,
                OwnerUserId = driverReplay.OwnerUserId,
            };
        }
    }
}
