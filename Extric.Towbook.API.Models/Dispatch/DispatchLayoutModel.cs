using System;
using System.Collections.Generic;
using System.Linq;
using Extric.Towbook.Dispatch;
using System.Collections.ObjectModel;

namespace Extric.Towbook.API.Models
{
	public class DispatchLayoutModel
	{
		public int Id { get; set; }
		public int? CompanyId { get; set; }
        public int? UserId { get; set; }
		public bool? OneLine { get; set; }
		public IEnumerable<DispatchLayoutColumnModel> Columns { get; set; }

        public DispatchLayoutModel()
        {
            Id = 0;
            CompanyId = null;
            UserId = null;
            OneLine = false;
        }

        public static DispatchLayoutModel Map(DispatchLayout o)
        {
            var model = new DispatchLayoutModel();

            model.Id = o.DispatchLayoutId;
            model.CompanyId = o.CompanyId;
            model.UserId = o.UserId;
            model.OneLine = o.OneLine;

            return model;
        }

        public static DispatchLayout Map(DispatchLayoutModel model, DispatchLayout original = null)
        {
            DispatchLayout o = new DispatchLayout();

            if (original != null)
                o = original;

            if (model.CompanyId != null) o.CompanyId = model.CompanyId.Value;
            if (model.UserId != null) o.UserId = model.UserId.Value;
            if (model.OneLine != null) o.OneLine = model.OneLine.Value;

            return o;
        }
	}
}