using Extric.Towbook.Company;

namespace Extric.Towbook.API.Models
{
    public class InvoiceDisclaimerModel
    {
        public int Id { get; set; }
        public int? ReasonId { get; set; }
        public int CompanyId { get; set; }
        public int? AccountId { get; set; }
        public string Disclaimer { get; set; }
        public bool? Impound { get; set; }

        public static InvoiceDisclaimerModel Map(InvoiceDisclaimer id)
        {
            return new InvoiceDisclaimerModel()
            {
                Id = id.Id,
                CompanyId = id.CompanyId,
                AccountId = id.AccountId,
                ReasonId = id.ReasonId.GetValueOrDefault(),
                Disclaimer = id.Disclaimer,
                Impound = id.Impound                
            };
        }
    }
}
