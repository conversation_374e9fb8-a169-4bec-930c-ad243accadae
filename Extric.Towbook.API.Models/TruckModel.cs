using System;
using System.Collections.Generic;
using System.Linq;
using T = Extric.Towbook;
using System.ComponentModel.DataAnnotations;
using System.Collections.ObjectModel;
using Extric.Towbook.Licenses;
using System.ComponentModel;

namespace Extric.Towbook.API.Models
{
    public class TruckModel
    {
        public int Id { get; set; }
        public int CompanyId { get; set; }

        [Required]
        public string Name { get; set; }
        public string Description { get; set; }
        public Extric.Towbook.Truck.DutyType? Duty { get; set; }
        
        public string InsuranceCompany { get; set; }
        public DateTime? InsuranceExpirationDate { get; set; }
        public string InsurancePolicyNumber { get; set; }

        public string Manufacturer { get; set; }
        public string Model { get; set; }
        public string Notes { get; set; }
        public int? Odometer { get; set; }
        public DateTime? OdometerDate { get; set; }
        public DateTime? PlateExpirationDate { get; set; }
        public string PlateNumber { get; set; }
        public Truck.TruckType? Type { get; set; }
        public string VIN { get; set; }
        public int? Year { get; set; }

        [DisplayName("Active")]
        public bool? IsActive { get; set; } = true;
        public bool? Deleted { get; set; } = false;

        public Collection<LicenseKeyValueModel> Licenses { get; set; }

        public int[] Companies { get; set; }


        public static TruckModel Map(Truck input)
        {
            TruckModel t = new TruckModel();

            t.Id = input.Id;
            t.Description = input.Description;
            t.Duty = input.Duty;
            t.InsuranceCompany = input.InsuranceCompany;
            t.InsuranceExpirationDate = input.InsuranceExpirationDate;
            t.InsurancePolicyNumber = input.InsurancePolicyNumber;
            t.Manufacturer = input.Manufacturer;
            t.Model = input.Model;
            t.Name = input.Name;
            t.Notes = input.Notes;
            t.Odometer = input.Odometer;
            t.PlateExpirationDate = input.PlateExpirationDate;
            t.PlateNumber = input.PlateNumber;
            t.Type = input.Type;
            t.VIN = input.VIN;
            t.Year = input.Year;
            t.Notes = input.Notes;
            t.Licenses = LicenseKeyValueModel.Map(TruckLicenseKeyValue.GetByTruckId(input.Id));
            t.Companies = input.Companies;
            t.IsActive = input.IsActive;
            t.Deleted = input.Deleted;


            return t;
        }

        public static Truck Map(TruckModel model, Truck input)
        {
            if (input == null)
                throw new TowbookException("Cannot map TruckModel to null Truck.");

            if(model.Description != null)
                input.Description = model.Description;

            if (model.Duty != null) 
                input.Duty = model.Duty.Value;

            if (model.InsuranceCompany != null) 
                input.InsuranceCompany = model.InsuranceCompany;

            if (model.InsuranceExpirationDate != null) 
                input.InsuranceExpirationDate = model.InsuranceExpirationDate.Value;

            if (model.InsurancePolicyNumber != null) 
                input.InsurancePolicyNumber = model.InsurancePolicyNumber;

            if (model.Manufacturer != null) 
                input.Manufacturer = model.Manufacturer;

            if (model.Model != null) 
                input.Model = model.Model;

            if (model.Name != null) 
                input.Name = model.Name;

            if (model.Notes != null) 
                input.Notes = model.Notes;

            if (model.Odometer != null) 
                input.Odometer = model.Odometer.Value;

            if (model.PlateExpirationDate != null) 
                input.PlateExpirationDate = model.PlateExpirationDate;

            if (model.PlateNumber != null) 
                input.PlateNumber = model.PlateNumber;

            if (model.Type != null) 
                input.Type = model.Type.Value;

            if (model.VIN != null) 
                input.VIN = model.VIN;

            if (model.Year != null) 
                input.Year = model.Year.Value;

            if (model.Notes != null) 
                input.Notes = model.Notes;

            if (model.IsActive != null)
                input.IsActive = model.IsActive.Value;

            return input;
        }


        public static Collection<TruckModel> Map(IEnumerable<Truck> input)
        {
            Collection<TruckModel> output = new Collection<TruckModel>();

            foreach (Truck t in input)
            {
                output.Add(Map(t));
            }

            return output;
        }

        public static Collection<LicenseKeyValueModel> GetAllMatchedKeyValues(Collection<TruckLicenseKey> filteredTruckLicenseKeys, Collection<TruckLicenseKeyValue> allTruckLicenseKeyValues)
        {
            Collection<LicenseKeyValueModel> truckLicenseKeyValues = TruckModel.MapLicenseKeyValueObjectToModel(allTruckLicenseKeyValues);

            Collection<LicenseKeyValueModel> licenseData = new Collection<LicenseKeyValueModel>();
            if (truckLicenseKeyValues.Count() != 0)
            {
                foreach (var clk in filteredTruckLicenseKeys)
                {
                    var license = truckLicenseKeyValues.Where(w => w.KeyId == clk.Id).FirstOrDefault();

                    if (license != null)
                    {
                        license.KeyId = clk.Id;
                        license.KeyName= clk.Name;
                        licenseData.Add(license);
                    }
                    else
                    {
                        var license_null = new LicenseKeyValueModel();
                        license_null.KeyId = clk.Id;
                        license_null.KeyName= clk.Name;
                        licenseData.Add(license_null);
                    }
                }
            }
            else
            {
                foreach (var clk in filteredTruckLicenseKeys)
                {
                    var license = new LicenseKeyValueModel();
                    license.KeyId = clk.Id;
                    license.KeyName = clk.Name;
                    licenseData.Add(license);
                }
            }
            return licenseData;
        }

        public static Collection<LicenseKeyValueModel> MapLicenseKeyValueObjectToModel(Collection<TruckLicenseKeyValue> input)
        {
            Collection<LicenseKeyValueModel> result = new Collection<LicenseKeyValueModel>();

            foreach (TruckLicenseKeyValue kv in input)
            {
                result.Add(new LicenseKeyValueModel()
                {
                    Id = kv.Id,
                    Value = kv.Value,
                    KeyId = kv.KeyId,
                });

            }

            return result;
        }
        
    }
}