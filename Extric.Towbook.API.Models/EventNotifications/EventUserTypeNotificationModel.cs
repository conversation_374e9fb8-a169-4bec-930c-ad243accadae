using Extric.Towbook.EventNotifications;
using Extric.Towbook.Utility;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;

namespace Extric.Towbook.API.Models
{
    public class EventUserTypeNotificationModel
    {
        public int Id { get; set; }
        public int EventNotificationId { get; set; }
        public User.TypeEnum UserType {get;set;}
        public int CompanyId { get; set; }

        public bool? RequireEmail { get; set; }
        public bool? RequireText { get; set; }
        public bool? RequireMobile { get; set; }
        public bool? RequireDesktop { get; set; }

        public static EventUserTypeNotificationModel Map(EventUserTypeNotification o)
        {
            return new EventUserTypeNotificationModel
            {
                Id = o.Id,
                EventNotificationId = o.EventNotificationId,
                UserType = (User.TypeEnum)o.UserTypeId,
                CompanyId = o.CompanyId,
                RequireEmail = o.RequireEmail,
                RequireText = o.RequireText,
                RequireMobile = o.RequirePushNotification,
                RequireDesktop = o.RequireWebNotification
            };
        }

        public static EventUserTypeNotification Map(EventUserTypeNotificationModel model, EventUserTypeNotification original = null)
        {
            var item = original ?? new EventUserTypeNotification();

            item.EventNotificationId = model.EventNotificationId;
            item.UserTypeId = (int)model.UserType;
            item.CompanyId = model.CompanyId;
            item.RequireEmail = model.RequireEmail;
            item.RequireText = model.RequireText;
            item.RequirePushNotification = model.RequireMobile;
            item.RequireWebNotification = model.RequireDesktop;

            return item;
        }
    }
}