using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using Extric.Towbook.Auctions;
using Extric.Towbook.Company;
using Extric.Towbook.Company.Accounting;
using Extric.Towbook.Dispatch;
using Extric.Towbook.Dispatch.CallModels;
using Extric.Towbook.Generated;
using Extric.Towbook.Impounds;
using Extric.Towbook.Integration;
using Extric.Towbook.Utility;
using Extric.Towbook.Web;
using Microsoft.AspNetCore.Http;
using A = Extric.Towbook.Accounts;

namespace Extric.Towbook.API.Models.Calls
{
    public static class CallModelExtensions
    {
        private static User CurrentUser => (User) Web.HttpContextFactory.Instance.CurrentUser;

        private static Company.Company[] Companies => (Company.Company[]) Web.HttpContextFactory.Instance.Companies;
        private static async Task<Company.Company[]> GetCompaniesAsync()
        {
            return (Company.Company[])await Web.HttpContextFactory.Instance.GetCompaniesAsync();
        }

        private static IHttpContext HttpContextCurrent => Web.HttpContextFactory.Instance;

        public static async Task<Collection<CallModel>> MapAsync(Collection<Entry> inputList)
        {
            if (inputList == null)
                return new Collection<CallModel>();

            var o = new Collection<CallModel>();

            // Ensure that AccountUsers can't see calls that don't belong to them
            if (CurrentUser.AccountId > 0)
            {
                var userAccounts = (await A.AccountUser.GetByUserIdAsync(CurrentUser.Id))
                    .Select(ro => ro.AccountId).Union(new[]
                    {
                CurrentUser.AccountId
                    }).ToArray();

                inputList = inputList.Where(r =>
                    (r.Account != null && userAccounts.Any(x => r.Account.Id == x)) ||
                    (r.Invoice.AccountId != null && userAccounts.Any(x => r.Invoice.AccountId == x))
                ).ToCollection();
            }

            var insights = await CallModel.CallInsightHelper.GetByDispatchEntriesAsync(inputList.Select(z => z.Id).ToArray());
            var payments = await InvoicePayment.GetByDispatchEntryIdsAsync(inputList.Select(z => z.Id).ToArray(), null);
            var vehicleTitles = await VehicleTitle.GetByDispatchEntryIdsAsync(inputList.Select(z => z.Id).ToArray());

            Collection<EntryAuctionDetail> auctionDetails = null;
            if (await CurrentUser.Company.HasFeatureAsync(Features.Impounds_ImpoundAuctions))
                auctionDetails = (await EntryAuctionDetail.GetByDispatchEntryIdsAsync(inputList.Select(z => z.Id).ToArray())).ToCollection();

            foreach (var x in inputList)
            {
                var mapped = await CallModel.MapAsync(x, insights, payments, null, null, vehicleTitles, auctionDetails);
                var ox = await mapped.FinishMapAsync();
                o.Add(ox);
            }

            return o;
        }



        public static async Task<Entry> MapAsync(CallModel call, Entry b)
        {
            if (b == null || call == null)
                throw new TowbookException("null parameter passed");

            call = InitializeOrUpdateInvoiceStatusId(call, b);

            if (call.CompanyId > 0)
                b.CompanyId = call.CompanyId;

            if (call.TowSource != null)
                b.TowSource = call.TowSource.Replace("\r\n", " ");

            if (call.TowDestination != null)
                b.TowDestination = call.TowDestination.Replace("\r\n", " ");

            if (call.Type != null)
                b.Type = call.Type.Value;

            if (call.Waypoints != null)
            {
                // translate the waypoints back into real ones.
                foreach (var w in call.Waypoints)
                {
                    var x = await CallWaypointModel.Map(w, b.Waypoints.FirstOrDefault(o => o.Id == w.Id && w.Id != 0));
                    b.Waypoints = b.Waypoints.Where(o => o.Id != x.Id || o.Id == 0).Union(new[] { x }).ToCollection();


                    if (string.IsNullOrWhiteSpace(b.TowSource)
                        && string.IsNullOrWhiteSpace(call.TowSource)
                        && x.Title == "Pickup")
                        b.TowSource = x.Address;
                    if (string.IsNullOrWhiteSpace(b.TowDestination)
                        && string.IsNullOrWhiteSpace(b.TowDestination)
                        && x.Title == "Destination")
                        b.TowDestination = x.Address;
                }
            }

            if (call.Status != null)
                b.Status = await Status.GetByIdAsync(call.Status.Id);

            if (call.Reason != null)
            {
                if (b.ReasonId != call.Reason.Id)
                {
                    b.ReasonId = call.Reason.Id;

                    if (CurrentUser != null && await CurrentUser.Company.HasFeatureAsync(Features.AutoDispatch) && b.Reason != null)
                    {
                        if (b.Reason.NameMatchable().Contains("goneonarrival") || b.Reason.NameMatchable().Contains("goa"))
                        {
                            var msg = "A call assigned to you was marked as a GOA.\n" +
                                b.Company.Name + " Call #: " + b.CallNumber + "\n" +
                                (b.Account != null && b.Account.MasterAccountId > 0 ? "Account: " + A.MasterAccountTypes.GetName(b.Account.MasterAccountId) + "\n" : "");

                            if (b.Driver != null && Core.IsPhoneValidStandard(b.Driver.MobilePhone))
                            {
                                await DispatchNotificationMessage.SendAsync(CurrentUser, b.Driver.MobilePhone, msg);
                            }
                        }
                    }
                }
            }

            var preventAccountModify = CurrentUser?.Company != null && await CompanyKeyValue.GetFirstValueOrNullAsync(CurrentUser.Company.Id,
                Provider.Towbook.ProviderId, "Towbook_Calls_PreventDriversFromModifyingAccount") == "1" && CurrentUser.Type == User.TypeEnum.Driver;

            if (b.Account?.MasterAccountId == A.MasterAccountTypes.OonAgero ||
                b.Account?.MasterAccountId == A.MasterAccountTypes.OonAllstate ||
                b.Account?.MasterAccountId == A.MasterAccountTypes.OonUrgently ||
                b.Account?.MasterAccountId == A.MasterAccountTypes.OonSwoop ||
                b.Account?.MasterAccountId == A.MasterAccountTypes.OonQuest ||
                b.Account?.MasterAccountId == A.MasterAccountTypes.Towbook)
            {
                preventAccountModify = true;

                if (await CurrentUser.Company.HasFeatureAsync(Features.DispatchToSubcontractors) &&
                    b.Account.MasterAccountId == A.MasterAccountTypes.Towbook)
                {
                    var cr = await CallRequest.GetByDispatchEntryId(b.Id);

                    if (cr == null)
                        preventAccountModify = false;
                }
            }

            if (call.Account != null && b.Account != null && preventAccountModify)
                call.Account = null;

            if (call.Account != null)
            {
                if (call.Account?.Id == 0)
                    call.Account.Id = 1;

                // don't allow a driver to change the account from motor club to no account specified
                // handles case where app changes to no account specified because the app doesnt have a config 
                // with the current account on the call
                if (CurrentUser?.Type == User.TypeEnum.Driver &&
                    b.Account?.Type == A.AccountType.MotorClub && call.Account?.Id == 1)
                    call.Account.Id = b.AccountId;

                if (b.AccountId != call.Account.Id)
                {
                    if (call.Account.Id != 1)
                    {
                        var acc = await A.Account.GetByIdAsync(call.Account.Id);
                        if (acc == null || (CurrentUser != null && !await CurrentUser.HasAccessToCompanyAsync(acc.Companies) && acc.CompanyId != 1))
                            throw new TowbookException("Invalid AccountId");
                    }

                    b.AccountId = call.Account.Id;

                    if (call.InvoiceTaxExempt == null)
                    {
                        // make sure tax exempt is updated on the invoice based on the account preference
                        var ba = await A.Account.GetByIdAsync(b.AccountId);
                        if (ba != null && b.Id > 0)
                        {
                            b.Invoice.IsTaxExempt = ba.TaxExempt;
                        }
                    }
                }
            }

            if (call.CreateDate != null && call.CreateDate > DateTime.MinValue)
                b.CreateDate = call.CreateDate.Value;

            if (call.CancellationReason != null)
                b.CancellationReason = call.CancellationReason;

            if (call.Notes != null)
                b.Notes = call.Notes;

            if (call.InvoiceNumber != null)
                b.InvoiceNumber = call.InvoiceNumber;

            if (!string.IsNullOrWhiteSpace(call.PurchaseOrderNumber))
                b.PurchaseOrderNumber = call.PurchaseOrderNumber;

            if (b.OwnerUserId < 64)
            {
                if (CurrentUser?.Type == User.TypeEnum.Driver ||
                   (CurrentUser?.Notes != null && CurrentUser.Notes.Contains("GrantSupervisorRole")))
                {
                    var lockDispatcher = await CompanyKeyValue.GetFirstValueOrNullAsync(call.CompanyId, Provider.Towbook.ProviderId, "PreventDriverFromBeingMarkedAsDispatcher") == "1";
                    if (lockDispatcher)
                    {
                        if (b.OwnerUserId == 0)
                            b.OwnerUserId = CurrentUser.Id;
                    }
                    else
                        b.OwnerUserId = CurrentUser.Id;
                }
                else
                {
                    if (CurrentUser != null)
                        b.OwnerUserId = CurrentUser.Id;
                }
            }

            if (call.Owner != null &&
                b.OwnerUserId != call.Owner.Id &&
                CurrentUser?.Type == User.TypeEnum.Manager)
            {
                var allowDispatcherChange = await CompanyKeyValue.GetFirstValueOrNullAsync(call.CompanyId, Provider.Towbook.ProviderId, "AllowManagersToChangeCallDispatcher");
                if (allowDispatcherChange == "1")
                {
                    var user = await User.GetByIdAsync(call.Owner.Id);
                    if (user == null || !await CurrentUser.HasAccessToCompanyAsync(user.CompanyId))
                        user = null;

                    if (user != null)
                        b.OwnerUserId = call.Owner.Id;
                }
            }

            await CheckPreventByUserTypeKeyValueAsync(call, "PreventDriversFromModifyingStatusTimes", "1", (o) =>
            {
                call.ArrivalETA = null;
                call.DispatchTime = null;
                call.EnrouteTime = null;
                call.ArrivalTime = null;
                call.TowTime = null;
                call.DestinationArrivalTime = null;
                call.CompletionTime = null;
            });

            // don't allow status times to be manipulated for towbook digital dispatches (ohio turnpike)
            if (b.Account?.MasterAccountId != A.MasterAccountTypes.Towbook)
            {
                if (call.DispatchTime != null)
                    b.DispatchTime = call.DispatchTime;

                if (call.EnrouteTime != null)
                    b.EnrouteTime = call.EnrouteTime;

                if (call.ArrivalTime != null)
                    b.ArrivalTime = call.ArrivalTime;

                if (call.TowTime != null)
                    b.TowTime = call.TowTime;

                if (call.DestinationArrivalTime != null)
                    b.DestinationArrivalTime = call.DestinationArrivalTime;

                if (call.CompletionTime != null)
                {
                    if (b.Status != Entry.EntryStatus.Canceled)
                    {
                        b.CompletionTime = call.CompletionTime;
                        b.Status = Dispatch.Status.Completed;
                    }
                }
            }

            // check if we need to block drivers from modifying eta
            await CheckPreventByUserTypeKeyValueAsync(call, "PreventDriversFromModifyingArrivalEta", "1", (o) =>
            {
                call.ArrivalETA = null;
            });


            // check if we need to block drivers from modifying invoice number
            await CheckPreventByUserTypeKeyValueAsync(call, "PreventDriversFromModifyingInvoiceNum", "1", (o) =>
            {
                call.InvoiceNumber = null;
            });


            await CheckPreventByUserTypeKeyValueAsync(call, "PreventDriversFromViewingNotes", "1", (o) =>
            {
                o.Notes = null;
            });

            await CheckPreventByUserTypeKeyValueAsync(call, "PreventDriversFromViewingPONumber", "1", (o) =>
            {
                o.PurchaseOrderNumber = null;
                o.Attributes = o.Attributes?.Where(ro => ro.AttributeId != AttributeValue.BUILTIN_ACCOUNT_PURCHASEORDER).ToArray();
            });

            await CheckPreventByUserTypeKeyValueAsync(call, "PreventDriversFromViewingMCMN", "1", (o) =>
            {
                o.Attributes = o.Attributes?.Where(ro => ro.AttributeId != AttributeValue.BUILTIN_MOTORCLUB_MEMBERSHIPNUMBER).ToArray();
            });

            await CheckPreventByUserTypeKeyValueAsync(call, "PreventDriversFromViewingMCDN", "1", (o) =>
            {
                o.Attributes = o.Attributes?.Where(ro => ro.AttributeId != AttributeValue.BUILTIN_MOTORCLUB_DISPATCHNUMBER).ToArray();
            });

            await CheckPreventByUserTypeKeyValueAsync(call, "PreventDispatchersFromViewingBillingNotes", "1", (o) =>
            {
                o.Attributes = o.Attributes?.Where(ro => ro.AttributeId != AttributeValue.BUILTIN_BILLING_NOTES).ToArray();
            }, User.TypeEnum.Dispatcher);

            if (call.ArrivalETA != null)
            {
                if (call.ArrivalETA == DateTime.MinValue)
                    b.ArrivalETA = null;
                else
                    b.ArrivalETA = call.ArrivalETA;
            }

            if (call.Priority != null)
                b.Priority = (Entry.EntryPriority)call.Priority.Value;

            if (call.Impound == null && call.ImpoundLotId > 0)
                call.Impound = true;

            return b;
        }

        public static async Task<CallModel> FinishMapAsync(this CallModel r)
        {
            if (r == null)
                return null;

            r = await BlockInvoicePricingWhenApplicableAsync(r);
            r = await BlockCoverageAsync(r);

            r = await BlockContactDetailsAsync(r);

            if (CurrentUser?.Type == User.TypeEnum.Driver)
                r.Attributes = r.Attributes.Where(o => o.AttributeId != Dispatch.AttributeValue.BUILTIN_EXTERNAL_CHAT_URL).ToArray();

            if (r.LastModifiedTimestamp != null)
                r.LastModifiedTimestamp = r.LastModifiedTimestamp.Value.ToLocalTime();

            r.Channels = GetAvailablePusherChannels(r.CompanyId, r.Id);

            if (!string.IsNullOrWhiteSpace(r.PurchaseOrderNumber) &&
                !r.Attributes.Any(rx => rx.AttributeId == AttributeValue.BUILTIN_ACCOUNT_PURCHASEORDER))
            {
                r.Attributes = r.Attributes
                    .Union(new[]
                    {
                new CallAttributeValueModel(-3, AttributeValue.BUILTIN_ACCOUNT_PURCHASEORDER, r.PurchaseOrderNumber)
                    })
                    .ToArray();
            }

            await CheckPreventByUserTypeKeyValueAsync(r, "HideAccountDetailsFromDrivers", "4", o =>
            {
                if (CurrentUser.Type == User.TypeEnum.Driver && r.Account != null)
                {
                    o.Attributes = o.Attributes
                        .Concat(new[]
                        {
                    new CallAttributeValueModel(-56, AttributeValue.BUILTIN_DISPATCH_ACCOUNT_NAME_READONLY, r.Account.Company)
                        })
                        .ToArray();
                }
            });

            if (r.Account != null)
                r.Account = await r.Account.ReduceDetailsAsync(r.CompanyId, (A.AccountType)r.Account.TypeId);

            await CheckPreventByUserTypeKeyValueAsync(r, "PreventDriversFromViewingNotes", "1", o =>
            {
                o.Notes = null;
            });

            await CheckPreventByUserTypeKeyValueAsync(r, "PreventDriversFromViewingPONumber", "1", o =>
            {
                o.PurchaseOrderNumber = null;
                o.Attributes = o.Attributes
                    .Where(a => a.AttributeId != AttributeValue.BUILTIN_ACCOUNT_PURCHASEORDER)
                    .ToArray();
            });

            // hide billing notes from all drivers
            if (CurrentUser?.Type == User.TypeEnum.Driver)
                r.Attributes = r.Attributes
                    .Where(a => a.AttributeId != AttributeValue.BUILTIN_BILLING_NOTES)
                    .ToArray();

            if (CurrentUser?.Type == User.TypeEnum.Dispatcher &&
                await CompanyKeyValue.GetFirstValueOrNullAsync(r.CompanyId, Provider.Towbook.ProviderId, "PreventDispatchersFromViewingBillingNotes") == "1")
            {
                r.Attributes = r.Attributes
                    .Where(a => a.AttributeId != AttributeValue.BUILTIN_BILLING_NOTES)
                    .ToArray();
            }

            if (r.CompanyId == 4185 || r.CompanyId == 4189 || r.CompanyId == 4194 || r.CompanyId == 6559)
            {
                if (CurrentUser.Type != User.TypeEnum.Manager)
                {
                    r.Attributes = r.Attributes
                        .Where(a =>
                            a.AttributeId != AttributeValue.BUILTIN_MOTORCLUB_DISPATCHNUMBER &&
                            a.AttributeId != AttributeValue.BUILTIN_MOTORCLUB_COVERAGELIMIT &&
                            a.AttributeId != AttributeValue.BUILTIN_MOTORCLUB_MEMBERSHIPNUMBER)
                        .ToArray();
                }
            }

            var statuses = new List<int>
            {
                Dispatch.Status.Waiting.Id,
                Dispatch.Status.Dispatched.Id,
                Dispatch.Status.EnRoute.Id,
                Dispatch.Status.AtSite.Id
            };

            bool hasDestinationArrivalStatus = await CompanyKeyValue.GetFirstValueOrNullAsync(
                r.CompanyId, Provider.Towbook.ProviderId, "EnableDestinationArrivalStatus") == "1";

            bool alwaysShowTowing = await CompanyKeyValue.GetFirstValueOrNullAsync(
                r.CompanyId, Provider.Towbook.ProviderId, "DisableTowingStatusForServiceCalls") != "1";

            bool serviceCallsOnly = await CompanyKeyValue.GetFirstValueOrNullAsync(
                CurrentUser.CompanyId, Provider.Towbook.ProviderId, "ServiceCallsOnly") == "1";

            if (!string.IsNullOrWhiteSpace(r.TowDestination) ||
                r.Account?.MasterAccountId == A.MasterAccountTypes.SafeClear ||
               ((r.Reason?.Name?.ToLowerInvariant().Contains("tow") ?? false) || alwaysShowTowing))
            {
                if (!serviceCallsOnly)
                    statuses.Add(Dispatch.Status.BeingTowed.Id);
                if (hasDestinationArrivalStatus)
                    statuses.Add(Dispatch.Status.DestinationArrival.Id);
            }

            statuses.Add(Dispatch.Status.Completed.Id);

            if (r.Account?.MasterAccountId == A.MasterAccountTypes.OonAgero ||
                r.Account?.MasterAccountId == A.MasterAccountTypes.OonAllstate ||
                r.Account?.MasterAccountId == A.MasterAccountTypes.OonQuest ||
                r.Account?.MasterAccountId == A.MasterAccountTypes.OonUrgently ||
                r.Account?.MasterAccountId == A.MasterAccountTypes.OonSwoop ||
                r.Account?.MasterAccountId == A.MasterAccountTypes.Allstate)
            {
                if (string.IsNullOrWhiteSpace(r.TowDestination) &&
                    !(r.Reason?.Name?.ToLowerInvariant().Contains("tow") ?? false))
                {
                    statuses.Remove(Dispatch.Status.BeingTowed.Id);
                    statuses.Remove(Dispatch.Status.DestinationArrival.Id);
                }
            }

            r.Statuses = statuses.ToArray();

            if (await CurrentUser.Company.HasFeatureAsync(Features.Dispatching_CalculatedETA))
            {
                var ceta = await Core.GetRedisValueAsync("calculatedEta:" + r.Id);
                if (ceta != null)
                {
                    var rt = Newtonsoft.Json.JsonConvert.DeserializeObject<Dispatch.CallModels.CallModel.RealTimeRecord>(ceta);
                    try
                    {
                        r.CalculatedEta = new Dispatch.CallModels.CallModel.CalculatedEtaModel
                        {
                            DistanceRemaining = rt.DriverDistanceRemaining.GetValueOrDefault(),
                            Eta = rt.DriverEta.GetValueOrDefault(),
                            LastUpdated = rt.LastUpdated
                        };
                    }
                    catch { }
                }
            }

            // predict next status and waypoint id
            var currentWaypointId = r.Assets != null ? r.Assets
                .Where(a => a.Drivers != null && a.Drivers.Where(b => b.Driver?.Id != null).Any())
                .SelectMany(s => s.Drivers)
                .FirstOrDefault()?.Driver?.CurrentWaypointId : (int?)null;

            r.Status = CallStatusUpdateModel.Map(
                r.Status.Id, currentWaypointId, r.Waypoints, hasDestinationArrivalStatus);
            
            // handle allstate specific cases
            if (r.Status?.Next != null)
            {
                var allstateJobType = r.Attributes?.FirstOrDefault(a => a.AttributeId == AttributeValue.BUILTIN_ALLSTATE_JOB_INFO_PRIMARY_TASK)?.Value;
                if (!string.IsNullOrEmpty(allstateJobType))
                {
                    if (allstateJobType.Equals("tow", StringComparison.InvariantCultureIgnoreCase) &&
                        r.Waypoints.Any(o => o.Title == "Passenger Dropoff"))
                    {
                        allstateJobType = "tow with passenger ride along";
                    }
                    r.Status.Next = CallStatusUpdateWaypointModel.MapForAllstate(
                        allstateJobType, r.Status.Next,
                        r.Waypoints.FirstOrDefault(wp => wp.Id == r.Status.Next.WaypointId));
                }
            }

            if (HttpContextCurrent.IsAppleDevice())
            {
                foreach (var waypoint in r.Waypoints)
                {
                    if (waypoint.Latitude == 0 || waypoint.Longitude == 0)
                        waypoint.Latitude = waypoint.Longitude = null;
                }
            }

            if (HttpContextCurrent.IsAndroidDevice() && r.Contacts != null)
            {
                foreach (var x in r.Contacts)
                {
                    x.Name = x.Name ?? string.Empty;
                    x.Address = x.Address ?? string.Empty;
                    x.City = x.City ?? string.Empty;
                    x.State = x.State ?? string.Empty;
                    x.Zip = x.Zip ?? string.Empty;
                    x.Phone = x.Phone ?? string.Empty;
                    x.Email = x.Email ?? string.Empty;
                }
            }

            if (r.Waypoints.All(o => o.Position > 0))
                r.Waypoints = r.Waypoints.Where(o => o.Id != 0).OrderBy(o => o.Position).ToArray();

            // Duplicated from CallModel.Map Line 295
            // as it doesn't get called on calls that come direct from CosmosDB. 
            if (r.Payments != null)
            {
                r.Payments = r.Payments.Where(o => !o.IsVoid);
                r.PaymentsApplied = r.Payments.Sum(o => o.Amount);
            }

            r = await UpdateTowOutModelAsync(r);
            r.AvailableActions = await GetAvailableActionsAsync(r);
            
            if (HttpContextCurrent.IsAndroidDevice() ||
                HttpContextCurrent.IsAppleDevice() ||
                r.Id < 81500000)
            {
                // don't allow calls before 81.5m to be pending, these calls are old, and when payments are recorded against them,
                // they're ending up with this status causing customers to complain to support and on social media.
                // we introduced this change on 1/8/2021

                // mobile apps dont support acknowledge feature yet. return original status.
                if (r.Status?.Id == Status.CancelledAcknowledgePending.Id)
                    r.Status.Id = Status.Cancelled.Id;

                if (r.Status?.Id == Status.CompletedAcknowledgePending.Id)
                    r.Status.Id = Status.Completed.Id;
            }

            return r;
        }

        public static Collection<CallModel> AddSurveyRatingsAsInsight(
            Collection<CallModel> models,
            IEnumerable<Roadside.RoadsideDispatch> dispatches)
        {
            if (dispatches == null)
                return models;

            foreach (var model in models)
            {
                var rd = dispatches.FirstOrDefault(w => w.DispatchEntryId == model.Id);
                if (rd != null && rd.MovingAverageRating.HasValue)
                {
                    var insight = new CallModel.CallInsightHelper.CallInsightModel()
                    {
                        Name = "CustomerRating",
                        Value = new { RoadsideDispatchId = rd.Id, Average = rd.MovingAverageRating }.ToJson()
                    };

                    CallModel.AddInsight(model, insight);
                }
            }

            return models;
        }




        internal static Collection<CallChannelModel> GetAvailablePusherChannels(int companyId, int callId)
        {
            var result = new Collection<CallChannelModel>();

            // Add "active user" channel that supports the pusher webhook "-call" implemntation
            var token = Extensions.GetCurrentToken();

            if (token?.ClientVersionId != null)
            {
                if (companyId != 11401)
                {
                    var channelName = PushNotificationProvider.GetCallChannelName(callId, token.UserId, token.ClientVersionId.Value);
                    result.Add(new CallChannelModel { Type = "activeUser", Name = channelName });
                }
            }

            return result;
        }

        public static CallModel BlockInvoicePricingWhenApplicable(CallModel r)
        {
            if (r == null)
                return null;

            if (CurrentUser == null)
                return r;

            if (r.CompanyId == 0)
                r.CompanyId = CurrentUser.CompanyId;

            var blockCharges = ShouldBlockCharges(r);
            var blockPriceOnly = ShouldShowChargesWithoutPricesExceptForCash(r);

            // show everything (default)
            // hide everything except cash charges.
            // show everything, with prices hidden except for cash charges

            if (blockPriceOnly || blockCharges)
            {
                var taxableTotal = r.InvoiceItems.Where(o => o.Taxable.GetValueOrDefault() == true && o.ItemTotal.HasValue).Sum(o => o.ItemTotal.Value);
                if (taxableTotal > 0)
                {
                    var rx = r.Attributes.Where(o => o.AttributeId == AttributeValue.BUILTIN_TAXRATE_OVERRIDE).FirstOrDefault();
                    if (rx != null)
                    {
                        //TODO can be async
                        var tr = TaxRate.GetById(Convert.ToInt32(rx.Value));
                        if (tr != null)
                        {
                            r.InvoiceTax = decimal.Round(taxableTotal * (tr.Rate / 100), 2, MidpointRounding.ToEven);
                        }
                    }
                }

                if (blockPriceOnly)
                {
                    foreach (var x in r.InvoiceItems.Where(o =>
                         !((o.Name ?? string.Empty).ToLowerInvariant().Contains("cash") ||
                           (o.Name ?? string.Empty).ToLowerInvariant().Contains("overage") ||
                           o.ClassId == ChargeClass.Cash ||
                           o.ClassId == ChargeClass.BillableReimbursement ||
                           o.ClassId == ChargeClass.Reimbursement)))
                    {
                        x.Price = 0;
                        x.ItemTotal = 0;
                    }

                    r.InvoiceItems = r.InvoiceItems.Where(p => !p.Name.Contains("FreeQuantity")).ToArray();
                }
                else
                {
                    r.InvoiceItems = r.InvoiceItems.Where(o =>
                        (o.Name ?? string.Empty).ToLowerInvariant().Contains("cash") ||
                        (o.Name ?? string.Empty).ToLowerInvariant().Contains("overage") ||
                        o.ClassId == ChargeClass.Cash ||
                        o.ClassId == ChargeClass.Reimbursement ||
                        o.ClassId == ChargeClass.BillableReimbursement).ToArray();
                }

                r.BalanceByClass = r.BalanceByClass.Where(o => o.Name.ToLowerInvariant().Contains("cash")).ToArray();

                var preventDriversFromModifyingCharges = CompanyKeyValue.GetByCompanyId(r.CompanyId, Provider.Towbook.ProviderId, "PreventDriversFromManipulatingInvoiceItems").FirstOrDefault();

                if (preventDriversFromModifyingCharges != null &&
                    preventDriversFromModifyingCharges.Value == "1" &&
                    CurrentUser.Type == Towbook.User.TypeEnum.Driver)
                {
                    if (r.InvoiceItems != null)
                    {
                        r.InvoiceItems = r.InvoiceItems.Where(o => (o.PredefinedRate == null ||
                            ((o.PredefinedRate != null && o.PredefinedRate.Id != PredefinedRateItem.BUILTIN_MILEAGE_LOADED) &&
                            (o.PredefinedRate != null && o.PredefinedRate.Id != PredefinedRateItem.BUILTIN_MILEAGE_LOADED)))).ToArray();
                    }
                }

                r.InvoiceSubtotal = r.InvoiceItems.Sum(o => o.ItemTotal.Value);


                // TODO: this gets rid of the tax, but if there happens to be an item that the user can see, they'll be able to see *all* tax.
                // need to move our tax calculation code to it's own place so we can calculate tax against a list of InvoiceItem or InvoiceItemModels.
                if (r.InvoiceSubtotal == 0)
                    r.InvoiceTax = 0;


                r.InvoiceTotal = r.InvoiceSubtotal + r.InvoiceTax;

                // TODO: make this use classes instead of the InvoiceItems from above
                r.BalanceDue = Math.Max(0, r.BalanceByClass.Sum(o => o.Balance));

                // don't allow the user to see payments if pricing is blocked.
                r.PaymentsApplied = 0;

                r.Payments = Array.Empty<Dispatch.CallModels.PaymentModel>();
            }

            return r;
        }

        public static async Task<CallModel> BlockInvoicePricingWhenApplicableAsync(CallModel r)
        {
            if (r == null)
                return null;

            if (CurrentUser == null)
                return r;

            if (r.CompanyId == 0)
                r.CompanyId = CurrentUser.CompanyId;

            var blockCharges = await ShouldBlockChargesAsync(r);
            var blockPriceOnly = await ShouldShowChargesWithoutPricesExceptForCashAsync(r);

            if (blockPriceOnly || blockCharges)
            {
                var taxableTotal = r.InvoiceItems
                    .Where(o => o.Taxable.GetValueOrDefault() && o.ItemTotal.HasValue)
                    .Sum(o => o.ItemTotal.Value);

                if (taxableTotal > 0)
                {
                    var rx = r.Attributes.FirstOrDefault(o => o.AttributeId == AttributeValue.BUILTIN_TAXRATE_OVERRIDE);
                    if (rx != null)
                    {
                        var tr = await TaxRate.GetByIdAsync(Convert.ToInt32(rx.Value));
                        if (tr != null)
                        {
                            r.InvoiceTax = decimal.Round(taxableTotal * (tr.Rate / 100), 2, MidpointRounding.ToEven);
                        }
                    }
                }

                if (blockPriceOnly)
                {
                    foreach (var x in r.InvoiceItems.Where(o =>
                        !((o.Name ?? string.Empty).ToLowerInvariant().Contains("cash") ||
                          (o.Name ?? string.Empty).ToLowerInvariant().Contains("overage") ||
                          o.ClassId == ChargeClass.Cash ||
                          o.ClassId == ChargeClass.BillableReimbursement ||
                          o.ClassId == ChargeClass.Reimbursement)))
                    {
                        x.Price = 0;
                        x.ItemTotal = 0;
                    }

                    r.InvoiceItems = r.InvoiceItems.Where(p => !p.Name.Contains("FreeQuantity")).ToArray();
                }
                else
                {
                    r.InvoiceItems = r.InvoiceItems.Where(o =>
                        (o.Name ?? string.Empty).ToLowerInvariant().Contains("cash") ||
                        (o.Name ?? string.Empty).ToLowerInvariant().Contains("overage") ||
                        o.ClassId == ChargeClass.Cash ||
                        o.ClassId == ChargeClass.Reimbursement ||
                        o.ClassId == ChargeClass.BillableReimbursement).ToArray();
                }

                r.BalanceByClass = r.BalanceByClass.Where(o => o.Name.ToLowerInvariant().Contains("cash")).ToArray();

                var preventDriversFromModifyingCharges = (await CompanyKeyValue.GetByCompanyIdAsync(r.CompanyId, Provider.Towbook.ProviderId, "PreventDriversFromManipulatingInvoiceItems")).FirstOrDefault();

                if (preventDriversFromModifyingCharges != null &&
                    preventDriversFromModifyingCharges.Value == "1" &&
                    CurrentUser.Type == Towbook.User.TypeEnum.Driver)
                {
                    if (r.InvoiceItems != null)
                    {
                        r.InvoiceItems = r.InvoiceItems.Where(o => o.PredefinedRate == null ||
                            (o.PredefinedRate != null && o.PredefinedRate.Id != PredefinedRateItem.BUILTIN_MILEAGE_LOADED)).ToArray();
                    }
                }

                r.InvoiceSubtotal = r.InvoiceItems.Sum(o => o.ItemTotal.Value);

                if (r.InvoiceSubtotal == 0)
                    r.InvoiceTax = 0;

                r.InvoiceTotal = r.InvoiceSubtotal + r.InvoiceTax;

                r.BalanceDue = Math.Max(0, r.BalanceByClass.Sum(o => o.Balance));

                r.PaymentsApplied = 0;
                r.Payments = Array.Empty<Dispatch.CallModels.PaymentModel>();
            }

            return r;
        }


        public static bool ShouldBlockCharges(this CallModel r)
        {
            if (r.CompanyId == 0)
                r.CompanyId = CurrentUser.CompanyId;

            if (r.Account != null &&
                CurrentUser?.Type == User.TypeEnum.Driver &&
             !new int[] { 15456, 35210, 35211, 35212, 35213, 35214, 35215, 35216, 35217, 35218, 35219, 38966, 249310, 7124 }.Contains(r.CompanyId))
            {
                // block charges from showing to drivers for ALL transport accounts.
                if (r.Account?.TypeId == (int)A.AccountType.Transport)
                    return true;
            }

            // block charges from ohio turnpike
            if (r.CompanyId == 20195)
                return true;

            // police officers and police dispatchers don't need access to pricing.
            if (CurrentUser?.Type == User.TypeEnum.PoliceOfficer ||
                CurrentUser?.Type == User.TypeEnum.PoliceDispatcher)
                return true;

            // block charges if account user, and key value is set
            if (CompanyKeyValue.GetFirstValueOrNull(CurrentUser.CompanyId, Provider.Towbook.ProviderId, "HideChargesFromAccountUsers") == "1"
                && CurrentUser != null && CurrentUser.IsAccountTypeUser())
                return true;

            bool blockCharges = false;
            if (CurrentUser.Type == Towbook.User.TypeEnum.Driver ||
                CurrentUser.Type == User.TypeEnum.Dispatcher)
            {

                var preventDriversFromViewingCharges =
                    CompanyKeyValue.GetByCompanyId(r.CompanyId, Provider.Towbook.ProviderId,
                    "PreventDriversFromViewingInvoiceItems").FirstOrDefault();

                var preventDispatchersFromViewingCharges =
                    CompanyKeyValue.GetByCompanyId(r.CompanyId, Provider.Towbook.ProviderId,
                    "PreventDispatchersFromViewingInvoiceItems").FirstOrDefault();

                var drivers = Driver.GetByUserId(CurrentUser.Id);

                if (drivers != null && drivers.Any())
                {
                    foreach (var d in drivers)
                    {
                        var hidePricing = DriverKeyValue.GetByDriver(r.CompanyId, d.Id, Provider.Towbook.ProviderId, "HidePricing").FirstOrDefault();
                        if (hidePricing != null && new string[] { "1", "3" }.Contains(hidePricing.Value))
                        {
                            blockCharges = true;
                            break;
                        }
                    }
                }

                if ((preventDriversFromViewingCharges != null &&
                    new string[] { "1", "3" }.Contains(preventDriversFromViewingCharges.Value) &&
                    CurrentUser.Type == Towbook.User.TypeEnum.Driver) ||
                     (preventDispatchersFromViewingCharges != null &&
                     new string[] { "1", "3" }.Contains(preventDispatchersFromViewingCharges.Value) &&
                     CurrentUser.Type == Towbook.User.TypeEnum.Dispatcher))
                {
                    blockCharges = true;
                }

                if (preventDriversFromViewingCharges != null &&
                    preventDriversFromViewingCharges.Value == "2" &&
                    CurrentUser.Type == Towbook.User.TypeEnum.Driver &&
                     r.Status != null && r.Status.Id == Extric.Towbook.Dispatch.Status.Completed.Id)
                {
                    blockCharges = true;
                }
            }

            return blockCharges;
        }


        public static async Task<bool> ShouldBlockChargesAsync(this CallModel r)
        {
            if (r.CompanyId == 0)
                r.CompanyId = CurrentUser.CompanyId;

            if (r.Account != null &&
                CurrentUser?.Type == User.TypeEnum.Driver &&
                !new int[] { 15456, 35210, 35211, 35212, 35213, 35214, 35215, 35216, 35217, 35218, 35219, 38966, 249310, 7124 }.Contains(r.CompanyId))
            {
                if (r.Account?.TypeId == (int)A.AccountType.Transport)
                    return true;
            }

            if (r.CompanyId == 20195)
                return true;

            if (CurrentUser?.Type == User.TypeEnum.PoliceOfficer ||
                CurrentUser?.Type == User.TypeEnum.PoliceDispatcher)
                return true;

            var hideChargesFromAccountUsers = await CompanyKeyValue.GetFirstValueOrNullAsync(
                CurrentUser.CompanyId, Provider.Towbook.ProviderId, "HideChargesFromAccountUsers");

            if (hideChargesFromAccountUsers == "1" && CurrentUser.IsAccountTypeUser())
                return true;

            bool blockCharges = false;

            if (CurrentUser.Type == Towbook.User.TypeEnum.Driver ||
                CurrentUser.Type == User.TypeEnum.Dispatcher)
            {
                var preventDriversFromViewingCharges = (await CompanyKeyValue.GetByCompanyIdAsync(
                    r.CompanyId, Provider.Towbook.ProviderId, "PreventDriversFromViewingInvoiceItems")).FirstOrDefault();

                var preventDispatchersFromViewingCharges = (await CompanyKeyValue.GetByCompanyIdAsync(
                    r.CompanyId, Provider.Towbook.ProviderId, "PreventDispatchersFromViewingInvoiceItems")).FirstOrDefault();

                var drivers = await Driver.GetByUserIdAsync(CurrentUser.Id);

                if (drivers != null && drivers.Any())
                {
                    var requestId = System.Web.HttpContext.Current?.Items["RequestId"]?.ToString() ?? Guid.NewGuid().ToString("N")[..8];
                    var threadId = System.Threading.Thread.CurrentThread.ManagedThreadId;
                    Console.WriteLine($"[ShouldBlockChargesAsync] DRIVER LOOP START - RequestId: {requestId}, ThreadId: {threadId}, CallId: {r.Id}, DriverCount: {drivers.Count()}");

                    foreach (var d in drivers)
                    {
                        Console.WriteLine($"[ShouldBlockChargesAsync] CALLING DriverKeyValue.GetByDriverAsync - RequestId: {requestId}, ThreadId: {threadId}, CallId: {r.Id}, DriverId: {d.Id}");
                        var hidePricing = (await DriverKeyValue.GetByDriverAsync(
                            r.CompanyId, d.Id, Provider.Towbook.ProviderId, "HidePricing")).FirstOrDefault();

                        if (hidePricing != null && new string[] { "1", "3" }.Contains(hidePricing.Value))
                        {
                            blockCharges = true;
                            break;
                        }
                    }
                }

                if ((preventDriversFromViewingCharges != null &&
                     new string[] { "1", "3" }.Contains(preventDriversFromViewingCharges.Value) &&
                     CurrentUser.Type == Towbook.User.TypeEnum.Driver) ||
                    (preventDispatchersFromViewingCharges != null &&
                     new string[] { "1", "3" }.Contains(preventDispatchersFromViewingCharges.Value) &&
                     CurrentUser.Type == Towbook.User.TypeEnum.Dispatcher))
                {
                    blockCharges = true;
                }

                if (preventDriversFromViewingCharges?.Value == "2" &&
                    CurrentUser.Type == Towbook.User.TypeEnum.Driver &&
                    r.Status != null && r.Status.Id == Extric.Towbook.Dispatch.Status.Completed.Id)
                {
                    blockCharges = true;
                }
            }

            return blockCharges;
        }


        private static int[] DetermineGroupsForCall(CallModel model)
        {
            var companyId = CompanyUser.GetByUserId(model.CompanyId).Select(s => s.CompanyId).ToArray();
            var accountId = model.Account != null ? model.Account.Id : (int?)null;
            var reasonId = model.Reason != null ? model.Reason.Id : (int?)null;
            var impoundId = model.ImpoundDetails != null ? model.ImpoundDetails.Id : (int?)null;
            int? accountType = model.Account != null ? (int)A.Account.GetById(model.Account.Id).Type : (int?)null;

            var eligibleGroups = new List<int>();
            var groups = Group.GetByCompany(companyId);

            foreach (var g in groups)
            {
                bool reasonMatches = false;
                bool impoundMatches = false;
                bool accountMatches = false;
                bool accountTypeMatches = false;

                if (!g.Reasons.Any() && !g.Impounds.Any() && !g.Accounts.Any() && !g.AccountTypes.Any())
                    break;

                if (!g.Reasons.Any())
                    reasonMatches = true;
                else
                {
                    if (reasonId != null)
                    {
                        foreach (var reason in g.Reasons)
                        {
                            if (reason == reasonId)
                            {
                                reasonMatches = true;
                                break;
                            }
                        }
                    }
                }

                if (!g.Impounds.Any())
                    impoundMatches = true;
                else
                {
                    // g.Impounds is a list of bits indicating whether to include calls that are impounds
                    foreach (var impound in g.Impounds)
                    {

                        if ((impound == true && impoundId > 0) || (impound == false && impoundId == null))
                        {
                            impoundMatches = true;
                            break;
                        }
                    }
                }

                if (!g.Accounts.Any())
                    accountMatches = true;
                else
                {
                    if (accountId != null)
                    {
                        foreach (var account in g.Accounts)
                        {
                            if (account == accountId.Value)
                            {
                                accountMatches = true;
                                break;
                            }
                        }
                    }
                }

                if (!g.AccountTypes.Any())
                    accountTypeMatches = true;
                else
                {
                    if (accountType != null)
                    {
                        foreach (var at in g.AccountTypes)
                        {
                            if (at == accountType)
                            {
                                accountTypeMatches = true;
                                break;
                            }
                        }
                    }
                }

                if (accountMatches && impoundMatches && reasonMatches && accountTypeMatches)
                    eligibleGroups.Add(g.DispatchEntryGroupId);
            }

            return eligibleGroups.ToArray();
        }

        public static string[] GetAvailableActions(this CallModel x)
        {
            var callStatus = Dispatch.Status.GetById(x.Status?.Id ?? 0);
            var result = new Collection<string>();

            // only offer extend eta if driver isn't on scene.
            if ((x.Account?.MasterAccountId == A.MasterAccountTypes.Allstate ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.OonAllstate ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.Geico ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.Swoop ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.Tesla ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.Sykes ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.Nac ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.RoadsideProtect) &&
                callStatus.Id < Dispatch.Status.AtSite.Id &&
                (CurrentUser.Type == User.TypeEnum.Manager ||
                CurrentUser.Type == User.TypeEnum.Dispatcher))
            {
                result.Add("DIGITAL_EXTEND_ETA");
            }

            if (x.Account?.MasterAccountId == A.MasterAccountTypes.Fleetnet)
                result.Add("COMPLETION_NOTES");

            var hideManualCancel = false;

            // allow digital_cancel always.
            if ((x.Account?.MasterAccountId == A.MasterAccountTypes.Allstate ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.OonAllstate ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.OonAgero ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.OonSwoop ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.OonQuest ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.OonRoadsideProtect ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.OonTrx ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.AaaAce ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.AaaAcg ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.AaaNationalFsl ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.AaaNational ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.AaaWashington ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.AaaNewYork ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.Bcaa ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.Honk ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.DrivenSolutions ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.StackThreeAtlas ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.Swoop ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.Nac ||
                (x.Account?.MasterAccountId == A.MasterAccountTypes.Nsd && x.CompanyId == 202034) ||
                (x.Account?.MasterAccountId == A.MasterAccountTypes.Quest && x.CompanyId == 5780) ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.RoadsideProtect ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.Geico ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.Tesla) &&
                callStatus != Dispatch.Status.Cancelled &&
                callStatus != Dispatch.Status.Completed)
            {
                result.Add("DIGITAL_CANCEL");
                result.Remove("CANCEL");

                if (x.Account?.MasterAccountId == A.MasterAccountTypes.Allstate ||
                    x.Account?.MasterAccountId == A.MasterAccountTypes.OonAllstate)
                {
                    // let user manually cancel if a digital-cancel was requested.
                    if (Core.GetRedisValue(x.Id + ":ss_dc") != "1" &&
                        Core.GetRedisValue(x.Id + ":ss_dc") != "1" &&
                        Core.GetRedisValue(x.Id + ":ss_goa") != "1")
                        hideManualCancel = true;
                }
            }

            // only offer GOA and service failure if driver is on scene.
            if ((x.Account?.MasterAccountId == A.MasterAccountTypes.Allstate ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.OonAllstate) &&
                (x.Status?.Id ?? 0) >= Dispatch.Status.EnRoute.Id &&
                callStatus != Dispatch.Status.Completed &&
                (CurrentUser.Type == User.TypeEnum.Manager ||
                CurrentUser.Type == User.TypeEnum.Dispatcher))
            {
                result.Add("DIGITAL_REQUEST_GOA");
            }

            // only offer GOA and service failure if driver is on scene.
            if (((x.Account?.MasterAccountId == A.MasterAccountTypes.Swoop) ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.OonSwoop) &&
                (x.Status?.Id ?? 0) >= Dispatch.Status.AtSite.Id && callStatus != Dispatch.Status.Completed &&
                (CurrentUser.Type == User.TypeEnum.Manager || CurrentUser.Type == User.TypeEnum.Dispatcher))
            {
                result.Add("DIGITAL_REQUEST_GOA");
            }

            var minGoaStatus = Dispatch.Status.EnRoute.Id;
            if (x.Account?.MasterAccountId == A.MasterAccountTypes.AaaAcg ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.AaaWashington ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.AaaNewYork ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.AaaAca)
                minGoaStatus = Dispatch.Status.AtSite.Id;

            if ((x.Account?.MasterAccountId == A.MasterAccountTypes.Urgently ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.OonUrgently ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.OonSwoop ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.OonQuest ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.AaaAce ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.AaaAcg ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.AaaNationalFsl ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.AaaNational ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.AaaWashington ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.AaaNewYork ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.AaaAca ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.Bcaa ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.DrivenSolutions ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.StackThreeAtlas ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.Nac ||
                (x.Account?.MasterAccountId == A.MasterAccountTypes.Nsd && x.CompanyId == 202034) ||
                (x.Account?.MasterAccountId == A.MasterAccountTypes.Quest && x.CompanyId == 5780) ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.RoadsideProtect ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.OonRoadsideProtect ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.Geico ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.OonAgero) &&
                (x.Status?.Id ?? 0) >= minGoaStatus &&
                callStatus != Dispatch.Status.Completed &&
                (CurrentUser.Type == User.TypeEnum.Manager ||
                CurrentUser.Type == User.TypeEnum.Dispatcher ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.AaaAcg))
            {
                result.Add("DIGITAL_REQUEST_GOA");
            }

            if (CurrentUser.Company.HasFeature(Features.SendFaxes))
                result.Add("FAX");

            if (x.Account?.MasterAccountId == A.MasterAccountTypes.Geico)
            {
                result.Add("DIGITAL_REQUEST_INFO");
            }

            if ((x.Account?.MasterAccountId == A.MasterAccountTypes.Geico ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.Allstate ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.Swoop ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.StackThreeAtlas ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.DrivenSolutions ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.OonAllstate) &&
                (x.Status?.Id ?? 0) >= Dispatch.Status.AtSite.Id &&
                (CurrentUser.Type == User.TypeEnum.Manager ||
                CurrentUser.Type == User.TypeEnum.Dispatcher))
            {
                if (callStatus.IsCurrent())
                    result.Add("DIGITAL_REPORT_SERVICE_FAILURE");

                if (x.Account?.MasterAccountId == A.MasterAccountTypes.Geico)
                    result.Add("DIGITAL_REQUEST_ADDITIONAL_SERVICE");
            }

            if (x.Account?.MasterAccountId == A.MasterAccountTypes.OonAllstate)
            {
                if (Core.GetRedisValue(x.Id + ":ss_goa") == "1" ||
                    Core.GetRedisValue(x.Id + ":ss_sf") == "1" ||
                    Core.GetRedisValue(x.Id + ":ss_dc") == "1")
                {
                    result.Remove("DIGITAL_REQUEST_GOA");
                    result.Remove("DIGITAL_REPORT_SERVICE_FAILURE");
                    result.Remove("DIGITAL_CANCEL");
                    result.Remove("DIGITAL_EXTEND_ETA");
                }
            }

            if (!x.IsLocked())
            {
                result.Add("MODIFY");

                if (x.Status?.Id == Dispatch.Status.Completed.Id)
                    result.Add("UNDO_COMPLETE");

                if (!(x.Status?.Id == Dispatch.Status.Cancelled.Id && x.Attributes.Any(r => r.AttributeId == AttributeValue.BUILTIN_DISPATCH_CANCEL_ACKNOWLEDGEMENT_JSON))
                    && (x.Status?.Id != Dispatch.Status.Completed.Id))
                    result.Add("COMPLETE");

                if (x.Status?.Id == Dispatch.Status.Cancelled.Id &&
                    x.Account?.MasterAccountId != A.MasterAccountTypes.AaaWashington &&
                    x.Account?.MasterAccountId != A.MasterAccountTypes.AaaNewYork)
                    result.Add("UNDO_CANCEL");

                if (x.Status?.Id != Dispatch.Status.Completed.Id &&
                    x.Status?.Id != Dispatch.Status.Cancelled.Id &&
                    !hideManualCancel)
                    result.Add("CANCEL");
            }
            else
                result.Add("VIEW");

            result.Add("EMAIL");

            if ((x.Status?.Id ?? 0) == (int)Entry.EntryStatus.Completed)
            {
                if (x.IsLocked())
                {
                    if (CurrentUser.HasAccessToUnlockCalls())
                        result.Add("UNLOCK");
                }
                else
                {
                    if (CurrentUser.HasAccessToLockCalls())
                        result.Add("LOCK");

                    // todo: test
                    // do not let unreleased impounds be locked
                    if (x.Impound.GetValueOrDefault() &&
                        x.ImpoundDetails?.ReleaseDate == null)
                        result.Remove("LOCK");
                }
            }

            if ((x.Status?.Id ?? 0) == (int)Entry.EntryStatus.Completed)
            {
                var preventWhenLocked = CompanyKeyValue.GetFirstValueOrNull(x.CompanyId,
                   Provider.Towbook.ProviderId, "PreventAuditWhenLocked") == "1";

                if (x.Attributes.Any(o => o.AttributeId == AttributeValue.BUILTIN_DISPATCH_AUDITED))
                {
                    if (CurrentUser.HasAccessToUnauditCalls())
                    {
                        if (!x.IsLocked() || !preventWhenLocked)
                            result.Add("UNAUDIT");
                    }
                }
                else
                {
                    if (CurrentUser.HasAccessToAuditCalls())
                    {
                        if (!x.IsLocked() || !preventWhenLocked)
                            result.Add("AUDIT");
                    }
                }
            }

            if (CurrentUser.Company.HasFeature(Features.QuickBooks) && x.Status?.Id == Status.Completed.Id)
                result.Add("PUSH_TO_QUICKBOOKS");


            if (CurrentUser?.Type == User.TypeEnum.Manager ||
                CurrentUser?.Type == User.TypeEnum.Dispatcher ||
                (CurrentUser?.Type == User.TypeEnum.Driver &&
                CompanyKeyValue.GetFirstValueOrNull(x.CompanyId,
                    Provider.Towbook.ProviderId, "AllowDriversToViewInternalNotes") == "1"))
            {
                result.Add("INTERNAL_NOTES");
            }


            if (CurrentUser?.Type == User.TypeEnum.Manager ||
                CurrentUser?.Type == User.TypeEnum.Dispatcher ||
                CurrentUser?.Type == User.TypeEnum.Accountant)
            {
                if (x.Status?.Id == Status.CompletedAcknowledgePending.Id)
                {
                    result.Add("ACKNOWLEDGE_COMPLETE");
                    result.Remove("COMPLETE");
                }

                if (x.Status?.Id == Status.CancelledAcknowledgePending.Id)
                {
                    result.Add("ACKNOWLEDGE_CANCEL");
                    result.Remove("COMPLETE");
                }
            }

            //1=Prevent moving to Ready to Bill/Billed if not audited, 2=Prevent if not locked, 3=Prevent if not audited and locked
            var preventEmail = CompanyKeyValue.GetFirstValueOrNull(x.CompanyId, Provider.Towbook.ProviderId, "PreventEmailIfNotAuditedLocked");
            if (preventEmail == "1")
            {
                if (!x.IsLocked() || !x.Attributes.Any(o => o.AttributeId == AttributeValue.BUILTIN_DISPATCH_AUDITED))
                    result.Remove("EMAIL");
            }

            if (CurrentUser.Type == User.TypeEnum.Manager)
            {
                var deletePermission = CompanyKeyValue.GetFirstValueOrNull(x.CompanyId, Provider.Towbook.ProviderId, "Permission_Calls_Delete") ?? "0";

                if (deletePermission == "" ||
                    deletePermission == "0" ||
                    deletePermission == "1")
                {
                    result.Add("DELETE");
                }
                else
                {
                    var ukv = UserKeyValue.GetByUser(x.CompanyId, CurrentUser.Id, Provider.Towbook.ProviderId, "Permission_Calls_Delete").FirstOrDefault();
                    if (ukv?.Value == "1")
                        result.Add("DELETE");
                }
            }

            // if this is set, then the user is able to change the driver assignment on the call.
            result.Add("ASSIGN_DRIVERS");

            // if the user is a driver or a dispatcher, check for permissions and remove them if necessary
            if (CurrentUser.Type == User.TypeEnum.Driver)
            {
                var preventModifyCompletedCalls = CompanyKeyValue.GetFirstValueOrNull(x.CompanyId, Provider.Towbook.ProviderId, "PreventDriversFromModifyingAfterCompletion");
                if (preventModifyCompletedCalls == "1" && ((x.Status?.Id ?? 0) == Dispatch.Status.Completed.Id))
                {
                    if (CurrentUser.Company.HasFeature(Features.Reimbursements) &&
                        x.CompletionTime > DateTime.Now.AddMinutes(-30))
                    {
                        // if its within last 30 minutes, let the modify so they can enter reimbursements
                        // for stuff like tolls
                        result.Remove("UNDO_COMPLETE");
                    }
                    else
                    {
                        result.Remove("MODIFY");
                        result.Remove("UNDO_COMPLETE");
                    }
                }

                if (CompanyKeyValue.GetFirstValueOrNull(x.CompanyId, Provider.Towbook.ProviderId, "PreventDriversFromModifyingCalls") == "1")
                {
                    // iOS and android don't honor this properly so we cant enforce this for now.
                }

                var preventCancelCalls = CompanyKeyValue.GetFirstValueOrNull(x.CompanyId, Provider.Towbook.ProviderId, "PreventDriversFromCancelling");
                if (preventCancelCalls == "1")
                {
                    result.Remove("CANCEL");
                    result.Remove("UNDO_CANCEL");
                }

                if (preventCancelCalls == "1" || CompanyKeyValue.GetFirstValueOrNull(x.CompanyId, Provider.Towbook.ProviderId, "PreventDriversFromMCSelfService") == "1")
                {
                    result.Remove("DIGITAL_REQUEST_ADDITIONAL_SERVICE");
                    result.Remove("DIGITAL_REPORT_SERVICE_FAILURE");
                    result.Remove("DIGITAL_REQUEST_GOA");
                    result.Remove("DIGITAL_CANCEL");
                }

                var preventEmailCalls = CompanyKeyValue.GetByCompanyId(x.CompanyId, Provider.Towbook.ProviderId, "PreventDriversFromEmailingCalls").FirstOrDefault();
                if (preventEmailCalls?.Value == "1")
                {
                    result.Remove("EMAIL");
                    result.Remove("FAX");
                }

                result.Remove("ASSIGN_DRIVERS");
                result.Remove("ACKNOWLEDGE_COMPLETE");
            }
            else if (CurrentUser.Type == User.TypeEnum.Dispatcher)
            {
                var preventModifyCompletedCalls = CompanyKeyValue.GetByCompanyId(x.CompanyId, Provider.Towbook.ProviderId, "PreventDispatchersFromModifyingAfterCompletion").FirstOrDefault();
                if (preventModifyCompletedCalls?.Value == "1" && ((x.Status?.Id ?? 0) == Dispatch.Status.Completed.Id))
                    result.Remove("MODIFY");
            }
            else if (CurrentUser.Type == User.TypeEnum.Manager)
            {
                var allowDispatcherChange = CompanyKeyValue.GetByCompanyId(x.CompanyId, Provider.Towbook.ProviderId, "AllowManagersToChangeCallDispatcher").FirstOrDefault();
                if (allowDispatcherChange?.Value == "1" && !x.IsLocked())
                    result.Add("ASSIGN_DISPATCHER");
            }
            else if (CurrentUser.Type == User.TypeEnum.ReportingOnly)
            {
                result.Remove("MODIFY");
            }
            else if (CurrentUser.IsAccountTypeUser())
            {
                if (!AccountUserIsAllowedToEdit(CurrentUser, x.Status.Id, x.CompanyId))
                {
                    result.Remove("MODIFY");
                    result.Add("VIEW");
                }

                if (!AccountUserIsAllowedToCancel(CurrentUser, x.Status.Id, x.CompanyId))
                {
                    result.Remove("CANCEL");
                }

                result.Remove("COMPLETE");
            }

            if ((x.Status.Id == 252 || x.Status.Id == 253) && (CurrentUser.Type == User.TypeEnum.Driver || CurrentUser.Type == User.TypeEnum.Accountant))
            {
                result.Remove("COMPLETE");
            }

            if ((CurrentUser.Type == User.TypeEnum.Driver ||
                CurrentUser.Type == User.TypeEnum.Dispatcher) &&
                (CurrentUser.Notes ?? "").Contains("GrantSupervisorRole"))
            {
                // TODO: TEST THIS WITH A DRIVER.
                var drivers = Towbook.Driver.GetByUserId(CurrentUser.Id);
                if (drivers != null)
                {
                    if (!drivers.Any(driver => x.Assets
                            .SelectMany(asset => asset.Drivers.Select(d => d.Driver?.Id))
                            .Any(driverId => driverId.GetValueOrDefault() == driver.Id)))
                    {
                        result.Remove("MODIFY");
                        result.Remove("ASSIGN_DRIVERS");
                        result.Remove("COMPLETE");
                        result.Remove("CANCEL");
                        result.Remove("UNDO_CANCEL");
                    }
                }
            }

            if (CurrentUser.Type == User.TypeEnum.Manager ||
                CurrentUser.Type == User.TypeEnum.Dispatcher)
            {
                result.Add("DUPLICATE");
            }

            if (x.Status.Id != Status.Cancelled.Id)
                result.Add("VIEW_INVOICE");

            if (CurrentUser.Type != User.TypeEnum.AccountUser &&
                CurrentUser.Type != User.TypeEnum.PoliceDispatcher &&
                CurrentUser.Type != User.TypeEnum.PoliceManager &&
                CurrentUser.Type != User.TypeEnum.PoliceOfficer)
            {
                result.Add("VIEW_PAYMENTS");
                result.Add("CREATE_PAYMENTS");

                if ((x.Status.Id != Status.Completed.Id &&
                    x.Status.Id != Status.Cancelled.Id) ||
                    HttpContextCurrent.IsAndroidDevice() ||
                    HttpContextCurrent.IsAppleDevice())
                    result.Add("UPDATE_STATUS");
            }

            if (CurrentUser.Type == User.TypeEnum.PoliceDispatcher ||
                CurrentUser.Type == User.TypeEnum.PoliceManager ||
                CurrentUser.Type == User.TypeEnum.PoliceOfficer)
            {
                result.Remove("COMPLETE");
                result.Remove("CANCEL");
                
                if (CurrentUser.Type != User.TypeEnum.PoliceManager)
                    result.Remove("VIEW_INVOICE");
            }


            if (CurrentUser.Company.HasFeature(Features.Impounds_TowOuts))
            {
                if (IsTowoutCall(x))
                {
                    result.Add("UNDO_TOW_FROM_STORAGE");
                }
                else
                {
                    if (callStatus.Id == 5 &&
                        x.ImpoundDetails?.PoliceHold.GetValueOrDefault() == false &&
                        x.Impound.GetValueOrDefault() &&
                        x.ImpoundLotId > 0)
                    {
                        var lot = Towbook.Impounds.Lot.GetById(x.CompanyId, x.ImpoundLotId.Value);
                        if (lot != null)
                        {
                            if(lot.AccountId.GetValueOrDefault(1) == 1)
                                result.Add("TOW_FROM_STORAGE");
                        }
                    }
                }

                if(CurrentUser.Type == User.TypeEnum.Driver &&
                    CompanyKeyValue.GetFirstValueOrNull(x.CompanyId, Provider.Towbook.ProviderId, "Towouts_PreventDriversFromReleasingFromStorage") == "1")
                {
                    result.Remove("TOW_FROM_STORAGE");
                    result.Remove("UNDO_TOW_FROM_STORAGE");
                }
            }

            var company = Companies.FirstOrDefault(f => f.Id == x.CompanyId);
            if (HttpContextCurrent.IsMobileAppRequest() && 
                company != null &&
                company.HasFeature(Features.AdvancedBilling_ClosedAccountingPeriod) &&
                x.IsWithinClosedAccountingPeriod())
            {
                result.Remove("ACKNOWLEDGE_COMPLETE");
                result.Remove("ACKNOWLEDGE_CANCEL");
                result.Remove("UNDO_COMPLETE");
                result.Remove("UNDO_CANCEL");
                result.Remove("MODIFY");
                result.Remove("COMPLETE");
                result.Remove("CANCEL");
                result.Remove("DIGITAL_EXTEND_ETA");
                result.Remove("DIGITAL_REPORT_SERVICE_FAILURE");
                result.Remove("DIGITAL_CANCEL");
                result.Remove("LOCK");
                result.Remove("UNLOCK");
                result.Remove("AUDIT");
                result.Remove("UNAUDIT");
                result.Remove("TOW_FROM_STORAGE");
                result.Remove("UNDO_TOW_FROM_STORAGE");
                result.Remove("UPDATE_STATUS");
                result.Remove("DELETE");
                result.Remove("ASSIGN_DRIVERS");
            }

            return result.ToArray();
        }

        public static async Task<string[]> GetAvailableActionsAsync(this CallModel x)
        {
            var callStatus = await Dispatch.Status.GetByIdAsync(x.Status?.Id ?? 0);
            var result = new Collection<string>();

            // only offer extend eta if driver isn't on scene.
            if ((x.Account?.MasterAccountId == A.MasterAccountTypes.Allstate ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.OonAllstate ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.Geico ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.Swoop ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.Tesla ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.Sykes ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.Nac ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.RoadsideProtect) &&
                callStatus.Id < Dispatch.Status.AtSite.Id &&
                (CurrentUser.Type == User.TypeEnum.Manager ||
                CurrentUser.Type == User.TypeEnum.Dispatcher))
            {
                result.Add("DIGITAL_EXTEND_ETA");
            }

            if (x.Account?.MasterAccountId == A.MasterAccountTypes.Fleetnet)
                result.Add("COMPLETION_NOTES");

            var hideManualCancel = false;

            // allow digital_cancel always.
            if ((x.Account?.MasterAccountId == A.MasterAccountTypes.Allstate ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.OonAllstate ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.OonAgero ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.OonSwoop ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.OonQuest ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.OonRoadsideProtect ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.OonTrx ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.AaaAce ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.AaaAcg ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.AaaNationalFsl ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.AaaNational ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.AaaWashington ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.AaaNewYork ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.Bcaa ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.Honk ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.DrivenSolutions ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.StackThreeAtlas ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.Swoop ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.Nac ||
                (x.Account?.MasterAccountId == A.MasterAccountTypes.Nsd && x.CompanyId == 202034) ||
                (x.Account?.MasterAccountId == A.MasterAccountTypes.Quest && x.CompanyId == 5780) ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.RoadsideProtect ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.Geico ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.Tesla) &&
                callStatus != Dispatch.Status.Cancelled &&
                callStatus != Dispatch.Status.Completed)
            {
                result.Add("DIGITAL_CANCEL");
                result.Remove("CANCEL");

                if (x.Account?.MasterAccountId == A.MasterAccountTypes.Allstate ||
                    x.Account?.MasterAccountId == A.MasterAccountTypes.OonAllstate)
                {
                    // let user manually cancel if a digital-cancel was requested.
                    if (await Core.GetRedisValueAsync(x.Id + ":ss_dc") != "1" &&
                        await Core.GetRedisValueAsync(x.Id + ":ss_dc") != "1" &&
                        await Core.GetRedisValueAsync(x.Id + ":ss_goa") != "1")
                        hideManualCancel = true;
                }
            }

            // only offer GOA and service failure if driver is on scene.
            if ((x.Account?.MasterAccountId == A.MasterAccountTypes.Allstate ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.OonAllstate) &&
                (x.Status?.Id ?? 0) >= Dispatch.Status.EnRoute.Id &&
                callStatus != Dispatch.Status.Completed &&
                (CurrentUser.Type == User.TypeEnum.Manager ||
                CurrentUser.Type == User.TypeEnum.Dispatcher))
            {
                result.Add("DIGITAL_REQUEST_GOA");
            }

            // only offer GOA and service failure if driver is on scene.
            if (((x.Account?.MasterAccountId == A.MasterAccountTypes.Swoop) ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.OonSwoop) &&
                (x.Status?.Id ?? 0) >= Dispatch.Status.AtSite.Id && callStatus != Dispatch.Status.Completed &&
                (CurrentUser.Type == User.TypeEnum.Manager || CurrentUser.Type == User.TypeEnum.Dispatcher))
            {
                result.Add("DIGITAL_REQUEST_GOA");
            }

            var minGoaStatus = Dispatch.Status.EnRoute.Id;
            if (x.Account?.MasterAccountId == A.MasterAccountTypes.AaaAcg ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.AaaWashington ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.AaaNewYork ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.AaaAca)
                minGoaStatus = Dispatch.Status.AtSite.Id;

            if ((x.Account?.MasterAccountId == A.MasterAccountTypes.Urgently ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.OonUrgently ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.OonSwoop ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.OonQuest ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.AaaAce ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.AaaAcg ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.AaaNationalFsl ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.AaaNational ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.AaaWashington ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.AaaNewYork ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.AaaAca ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.Bcaa ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.DrivenSolutions ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.StackThreeAtlas ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.Nac ||
                (x.Account?.MasterAccountId == A.MasterAccountTypes.Nsd && x.CompanyId == 202034) ||
                (x.Account?.MasterAccountId == A.MasterAccountTypes.Quest && x.CompanyId == 5780) ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.RoadsideProtect ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.OonRoadsideProtect ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.Geico ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.OonAgero) &&
                (x.Status?.Id ?? 0) >= minGoaStatus &&
                callStatus != Dispatch.Status.Completed &&
                (CurrentUser.Type == User.TypeEnum.Manager ||
                CurrentUser.Type == User.TypeEnum.Dispatcher ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.AaaAcg))
            {
                result.Add("DIGITAL_REQUEST_GOA");
            }

            if (await CurrentUser.Company.HasFeatureAsync(Features.SendFaxes))
                result.Add("FAX");

            if (x.Account?.MasterAccountId == A.MasterAccountTypes.Geico)
            {
                result.Add("DIGITAL_REQUEST_INFO");
            }

            if ((x.Account?.MasterAccountId == A.MasterAccountTypes.Geico ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.Allstate ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.Swoop ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.StackThreeAtlas ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.DrivenSolutions ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.OonAllstate) &&
                (x.Status?.Id ?? 0) >= Dispatch.Status.AtSite.Id &&
                (CurrentUser.Type == User.TypeEnum.Manager ||
                CurrentUser.Type == User.TypeEnum.Dispatcher))
            {
                if (callStatus.IsCurrent())
                    result.Add("DIGITAL_REPORT_SERVICE_FAILURE");

                if (x.Account?.MasterAccountId == A.MasterAccountTypes.Geico)
                    result.Add("DIGITAL_REQUEST_ADDITIONAL_SERVICE");
            }

            if (x.Account?.MasterAccountId == A.MasterAccountTypes.OonAllstate)
            {
                if (await Core.GetRedisValueAsync(x.Id + ":ss_goa") == "1" ||
                    await Core.GetRedisValueAsync(x.Id + ":ss_sf") == "1" ||
                    await Core.GetRedisValueAsync(x.Id + ":ss_dc") == "1")
                {
                    result.Remove("DIGITAL_REQUEST_GOA");
                    result.Remove("DIGITAL_REPORT_SERVICE_FAILURE");
                    result.Remove("DIGITAL_CANCEL");
                    result.Remove("DIGITAL_EXTEND_ETA");
                }
            }

            if (!x.IsLocked())
            {
                result.Add("MODIFY");

                if (x.Status?.Id == Dispatch.Status.Completed.Id)
                    result.Add("UNDO_COMPLETE");

                if (!(x.Status?.Id == Dispatch.Status.Cancelled.Id && x.Attributes.Any(r => r.AttributeId == AttributeValue.BUILTIN_DISPATCH_CANCEL_ACKNOWLEDGEMENT_JSON))
                    && (x.Status?.Id != Dispatch.Status.Completed.Id))
                    result.Add("COMPLETE");

                if (x.Status?.Id == Dispatch.Status.Cancelled.Id &&
                    x.Account?.MasterAccountId != A.MasterAccountTypes.AaaWashington &&
                    x.Account?.MasterAccountId != A.MasterAccountTypes.AaaNewYork)
                    result.Add("UNDO_CANCEL");

                if (x.Status?.Id != Dispatch.Status.Completed.Id &&
                    x.Status?.Id != Dispatch.Status.Cancelled.Id &&
                    !hideManualCancel)
                    result.Add("CANCEL");
            }
            else
                result.Add("VIEW");

            result.Add("EMAIL");

            if ((x.Status?.Id ?? 0) == (int)Entry.EntryStatus.Completed)
            {
                if (x.IsLocked())
                {
                    if (CurrentUser.HasAccessToUnlockCalls())
                        result.Add("UNLOCK");
                }
                else
                {
                    if (CurrentUser.HasAccessToLockCalls())
                        result.Add("LOCK");

                    // todo: test
                    // do not let unreleased impounds be locked
                    if (x.Impound.GetValueOrDefault() &&
                        x.ImpoundDetails?.ReleaseDate == null)
                        result.Remove("LOCK");
                }
            }

            if ((x.Status?.Id ?? 0) == (int)Entry.EntryStatus.Completed)
            {
                var preventWhenLocked = await CompanyKeyValue.GetFirstValueOrNullAsync(x.CompanyId,
                   Provider.Towbook.ProviderId, "PreventAuditWhenLocked") == "1";

                if (x.Attributes.Any(o => o.AttributeId == AttributeValue.BUILTIN_DISPATCH_AUDITED))
                {
                    if (CurrentUser.HasAccessToUnauditCalls())
                    {
                        if (!x.IsLocked() || !preventWhenLocked)
                            result.Add("UNAUDIT");
                    }
                }
                else
                {
                    if (CurrentUser.HasAccessToAuditCalls())
                    {
                        if (!x.IsLocked() || !preventWhenLocked)
                            result.Add("AUDIT");
                    }
                }
            }

            if (await CurrentUser.Company.HasFeatureAsync(Features.QuickBooks) && x.Status?.Id == Status.Completed.Id)
                result.Add("PUSH_TO_QUICKBOOKS");


            if (CurrentUser?.Type == User.TypeEnum.Manager ||
                CurrentUser?.Type == User.TypeEnum.Dispatcher ||
                (CurrentUser?.Type == User.TypeEnum.Driver &&
                await CompanyKeyValue.GetFirstValueOrNullAsync(x.CompanyId,
                    Provider.Towbook.ProviderId, "AllowDriversToViewInternalNotes") == "1"))
            {
                result.Add("INTERNAL_NOTES");
            }


            if (CurrentUser?.Type == User.TypeEnum.Manager ||
                CurrentUser?.Type == User.TypeEnum.Dispatcher ||
                CurrentUser?.Type == User.TypeEnum.Accountant)
            {
                if (x.Status?.Id == Status.CompletedAcknowledgePending.Id)
                {
                    result.Add("ACKNOWLEDGE_COMPLETE");
                    result.Remove("COMPLETE");
                }

                if (x.Status?.Id == Status.CancelledAcknowledgePending.Id)
                {
                    result.Add("ACKNOWLEDGE_CANCEL");
                    result.Remove("COMPLETE");
                }
            }

            //1=Prevent moving to Ready to Bill/Billed if not audited, 2=Prevent if not locked, 3=Prevent if not audited and locked
            var preventEmail = await CompanyKeyValue.GetFirstValueOrNullAsync(x.CompanyId, Provider.Towbook.ProviderId, "PreventEmailIfNotAuditedLocked");
            if (preventEmail == "1")
            {
                if (!x.IsLocked() || !x.Attributes.Any(o => o.AttributeId == AttributeValue.BUILTIN_DISPATCH_AUDITED))
                    result.Remove("EMAIL");
            }

            if (CurrentUser.Type == User.TypeEnum.Manager)
            {
                var deletePermission = await CompanyKeyValue.GetFirstValueOrNullAsync(x.CompanyId, Provider.Towbook.ProviderId, "Permission_Calls_Delete") ?? "0";

                if (deletePermission == "" ||
                    deletePermission == "0" ||
                    deletePermission == "1")
                {
                    result.Add("DELETE");
                }
                else
                {
                    var ukv = (await UserKeyValue.GetByUserAsync(x.CompanyId, CurrentUser.Id, Provider.Towbook.ProviderId, "Permission_Calls_Delete")).FirstOrDefault();
                    if (ukv?.Value == "1")
                        result.Add("DELETE");
                }
            }

            // if this is set, then the user is able to change the driver assignment on the call.
            result.Add("ASSIGN_DRIVERS");

            // if the user is a driver or a dispatcher, check for permissions and remove them if necessary
            if (CurrentUser.Type == User.TypeEnum.Driver)
            {
                var preventModifyCompletedCalls = await CompanyKeyValue.GetFirstValueOrNullAsync(x.CompanyId, Provider.Towbook.ProviderId, "PreventDriversFromModifyingAfterCompletion");
                if (preventModifyCompletedCalls == "1" && ((x.Status?.Id ?? 0) == Dispatch.Status.Completed.Id))
                {
                    if (await CurrentUser.Company.HasFeatureAsync(Features.Reimbursements) &&
                        x.CompletionTime > DateTime.Now.AddMinutes(-30))
                    {
                        // if its within last 30 minutes, let the modify so they can enter reimbursements
                        // for stuff like tolls
                        result.Remove("UNDO_COMPLETE");
                    }
                    else
                    {
                        result.Remove("MODIFY");
                        result.Remove("UNDO_COMPLETE");
                    }
                }

                if (await CompanyKeyValue.GetFirstValueOrNullAsync(x.CompanyId, Provider.Towbook.ProviderId, "PreventDriversFromModifyingCalls") == "1")
                {
                    // iOS and android don't honor this properly so we cant enforce this for now.
                }

                var preventCancelCalls = await CompanyKeyValue.GetFirstValueOrNullAsync(x.CompanyId, Provider.Towbook.ProviderId, "PreventDriversFromCancelling");
                if (preventCancelCalls == "1")
                {
                    result.Remove("CANCEL");
                    result.Remove("UNDO_CANCEL");
                }

                if (preventCancelCalls == "1" || await CompanyKeyValue.GetFirstValueOrNullAsync(x.CompanyId, Provider.Towbook.ProviderId, "PreventDriversFromMCSelfService") == "1")
                {
                    result.Remove("DIGITAL_REQUEST_ADDITIONAL_SERVICE");
                    result.Remove("DIGITAL_REPORT_SERVICE_FAILURE");
                    result.Remove("DIGITAL_REQUEST_GOA");
                    result.Remove("DIGITAL_CANCEL");
                }

                var preventEmailCalls = (await CompanyKeyValue.GetByCompanyIdAsync(x.CompanyId, Provider.Towbook.ProviderId, "PreventDriversFromEmailingCalls")).FirstOrDefault();
                if (preventEmailCalls?.Value == "1")
                {
                    result.Remove("EMAIL");
                    result.Remove("FAX");
                }

                result.Remove("ASSIGN_DRIVERS");
                result.Remove("ACKNOWLEDGE_COMPLETE");
            }
            else if (CurrentUser.Type == User.TypeEnum.Dispatcher)
            {
                var preventModifyCompletedCalls = (await CompanyKeyValue.GetByCompanyIdAsync(x.CompanyId, Provider.Towbook.ProviderId, "PreventDispatchersFromModifyingAfterCompletion")).FirstOrDefault();
                if (preventModifyCompletedCalls?.Value == "1" && ((x.Status?.Id ?? 0) == Dispatch.Status.Completed.Id))
                    result.Remove("MODIFY");
            }
            else if (CurrentUser.Type == User.TypeEnum.Manager)
            {
                var allowDispatcherChange = (await CompanyKeyValue.GetByCompanyIdAsync(x.CompanyId, Provider.Towbook.ProviderId, "AllowManagersToChangeCallDispatcher")).FirstOrDefault();
                if (allowDispatcherChange?.Value == "1" && !x.IsLocked())
                    result.Add("ASSIGN_DISPATCHER");
            }
            else if (CurrentUser.Type == User.TypeEnum.ReportingOnly)
            {
                result.Remove("MODIFY");
            }
            else if (CurrentUser.IsAccountTypeUser())
            {
                if (!AccountUserIsAllowedToEdit(CurrentUser, x.Status.Id, x.CompanyId))
                {
                    result.Remove("MODIFY");
                    result.Add("VIEW");
                }

                if (!AccountUserIsAllowedToCancel(CurrentUser, x.Status.Id, x.CompanyId))
                {
                    result.Remove("CANCEL");
                }

                result.Remove("COMPLETE");
            }

            if ((x.Status.Id == 252 || x.Status.Id == 253) && (CurrentUser.Type == User.TypeEnum.Driver || CurrentUser.Type == User.TypeEnum.Accountant))
            {
                result.Remove("COMPLETE");
            }

            if ((CurrentUser.Type == User.TypeEnum.Driver ||
                CurrentUser.Type == User.TypeEnum.Dispatcher) &&
                (CurrentUser.Notes ?? "").Contains("GrantSupervisorRole"))
            {
                // TODO: TEST THIS WITH A DRIVER.
                var drivers = await Towbook.Driver.GetByUserIdAsync(CurrentUser.Id);
                if (drivers != null)
                {
                    if (!drivers.Any(driver => x.Assets
                            .SelectMany(asset => asset.Drivers.Select(d => d.Driver?.Id))
                            .Any(driverId => driverId.GetValueOrDefault() == driver.Id)))
                    {
                        result.Remove("MODIFY");
                        result.Remove("ASSIGN_DRIVERS");
                        result.Remove("COMPLETE");
                        result.Remove("CANCEL");
                        result.Remove("UNDO_CANCEL");
                    }
                }
            }

            if (CurrentUser.Type == User.TypeEnum.Manager ||
                CurrentUser.Type == User.TypeEnum.Dispatcher)
            {
                result.Add("DUPLICATE");
            }

            if (x.Status.Id != Status.Cancelled.Id)
                result.Add("VIEW_INVOICE");

            if (CurrentUser.Type != User.TypeEnum.AccountUser &&
                CurrentUser.Type != User.TypeEnum.PoliceDispatcher &&
                CurrentUser.Type != User.TypeEnum.PoliceManager &&
                CurrentUser.Type != User.TypeEnum.PoliceOfficer)
            {
                result.Add("VIEW_PAYMENTS");
                result.Add("CREATE_PAYMENTS");

                if ((x.Status.Id != Status.Completed.Id &&
                    x.Status.Id != Status.Cancelled.Id) ||
                    HttpContextCurrent.IsAndroidDevice() ||
                    HttpContextCurrent.IsAppleDevice())
                    result.Add("UPDATE_STATUS");
            }

            if (CurrentUser.Type == User.TypeEnum.PoliceDispatcher ||
                CurrentUser.Type == User.TypeEnum.PoliceManager ||
                CurrentUser.Type == User.TypeEnum.PoliceOfficer)
            {
                result.Remove("COMPLETE");
                result.Remove("CANCEL");

                if (CurrentUser.Type != User.TypeEnum.PoliceManager)
                    result.Remove("VIEW_INVOICE");
            }


            if (await CurrentUser.Company.HasFeatureAsync(Features.Impounds_TowOuts))
            {
                if (IsTowoutCall(x))
                {
                    result.Add("UNDO_TOW_FROM_STORAGE");
                }
                else
                {
                    if (callStatus.Id == 5 &&
                        x.ImpoundDetails?.PoliceHold.GetValueOrDefault() == false &&
                        x.Impound.GetValueOrDefault() &&
                        x.ImpoundLotId > 0)
                    {
                        var lot = await Towbook.Impounds.Lot.GetByIdAsync(x.CompanyId, x.ImpoundLotId.Value);
                        if (lot != null)
                        {
                            if (lot.AccountId.GetValueOrDefault(1) == 1)
                                result.Add("TOW_FROM_STORAGE");
                        }
                    }
                }

                if (CurrentUser.Type == User.TypeEnum.Driver &&
                    await CompanyKeyValue.GetFirstValueOrNullAsync(x.CompanyId, Provider.Towbook.ProviderId, "Towouts_PreventDriversFromReleasingFromStorage") == "1")
                {
                    result.Remove("TOW_FROM_STORAGE");
                    result.Remove("UNDO_TOW_FROM_STORAGE");
                }
            }

            var company = (await GetCompaniesAsync()).FirstOrDefault(f => f.Id == x.CompanyId);
            if (HttpContextCurrent.IsMobileAppRequest() &&
                company != null &&
                await company.HasFeatureAsync(Features.AdvancedBilling_ClosedAccountingPeriod) &&
                x.IsWithinClosedAccountingPeriod())
            {
                result.Remove("ACKNOWLEDGE_COMPLETE");
                result.Remove("ACKNOWLEDGE_CANCEL");
                result.Remove("UNDO_COMPLETE");
                result.Remove("UNDO_CANCEL");
                result.Remove("MODIFY");
                result.Remove("COMPLETE");
                result.Remove("CANCEL");
                result.Remove("DIGITAL_EXTEND_ETA");
                result.Remove("DIGITAL_REPORT_SERVICE_FAILURE");
                result.Remove("DIGITAL_CANCEL");
                result.Remove("LOCK");
                result.Remove("UNLOCK");
                result.Remove("AUDIT");
                result.Remove("UNAUDIT");
                result.Remove("TOW_FROM_STORAGE");
                result.Remove("UNDO_TOW_FROM_STORAGE");
                result.Remove("UPDATE_STATUS");
                result.Remove("DELETE");
                result.Remove("ASSIGN_DRIVERS");
            }

            return result.ToArray();
        }

        public static Collection<string> GetAvailableActions(this Dispatch.Entry x, Impound imp = null)
        {
            var result = new Collection<string>();

            // only offer extend eta if driver isn't on scene.
            if ((x.Account?.MasterAccountId == A.MasterAccountTypes.Allstate ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.OonAllstate ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.Geico ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.Swoop ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.Tesla ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.Sykes ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.Nac ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.RoadsideProtect) &&
                (x.Status?.Id ?? 0) < Dispatch.Status.AtSite.Id &&
                (CurrentUser.Type == User.TypeEnum.Manager ||
                CurrentUser.Type == User.TypeEnum.Dispatcher))
            {
                result.Add("DIGITAL_EXTEND_ETA");
            }

            if (x.Account?.MasterAccountId == A.MasterAccountTypes.Fleetnet)
                result.Add("COMPLETION_NOTES");

            var hideManualCancel = false;

            // allow digital_cancel always.
            if ((x.Account?.MasterAccountId == A.MasterAccountTypes.Allstate ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.OonAllstate ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.OonAgero ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.OonQuest ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.Honk ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.AaaAce ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.AaaAcg ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.AaaNationalFsl ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.AaaNational ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.AaaWashington ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.AaaNewYork ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.AaaAca ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.Bcaa ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.StackThreeAtlas ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.DrivenSolutions ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.Nac ||
                (x.Account?.MasterAccountId == A.MasterAccountTypes.Nsd && x.CompanyId == 202034) ||
               (x.Account?.MasterAccountId == A.MasterAccountTypes.Quest && x.CompanyId == 5780) ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.RoadsideProtect ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.OonRoadsideProtect ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.OonTrx ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.Geico ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.Tesla) &&
                x.Status != Dispatch.Status.Cancelled &&
                x.Status != Dispatch.Status.Completed)
            {
                result.Add("DIGITAL_CANCEL");
                result.Remove("CANCEL");

                if (x.Account?.MasterAccountId == A.MasterAccountTypes.Allstate ||
                    x.Account?.MasterAccountId == A.MasterAccountTypes.OonAllstate)
                {
                    // let user manually cancel if a digital-cancel was requested.
                    if (Core.GetRedisValue(x.Id + ":ss_dc") != "1" &&
                        Core.GetRedisValue(x.Id + ":ss_dc") != "1" &&
                        Core.GetRedisValue(x.Id + ":ss_goa") != "1")
                        hideManualCancel = true;
                }
            }

            // only offer GOA and service failure if driver is on scene.
            if ((x.Account?.MasterAccountId == A.MasterAccountTypes.Allstate ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.OonAllstate) &&
                (x.Status?.Id ?? 0) >= Dispatch.Status.EnRoute.Id &&
                x.Status != Dispatch.Status.Completed &&
                (CurrentUser.Type == User.TypeEnum.Manager ||
                CurrentUser.Type == User.TypeEnum.Dispatcher))
            {
                result.Add("DIGITAL_REQUEST_GOA");
            }

            var minGoaStatus = Dispatch.Status.EnRoute.Id;

            if (x.Account?.MasterAccountId == A.MasterAccountTypes.AaaAcg ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.AaaWashington ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.AaaNewYork)
                minGoaStatus = Dispatch.Status.AtSite.Id;

            if ((x.Account?.MasterAccountId == A.MasterAccountTypes.Urgently ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.OonUrgently ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.OonSwoop ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.OonQuest ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.AaaAce ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.AaaAcg ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.AaaNationalFsl ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.AaaNational ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.AaaWashington ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.AaaNewYork ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.AaaAca ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.Bcaa||
                x.Account?.MasterAccountId == A.MasterAccountTypes.DrivenSolutions ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.StackThreeAtlas ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.Nac ||
                (x.Account?.MasterAccountId == A.MasterAccountTypes.Nsd && x.CompanyId == 202034) ||
                (x.Account?.MasterAccountId == A.MasterAccountTypes.Quest && x.CompanyId == 5780) ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.RoadsideProtect ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.OonRoadsideProtect ||
                (x.Account?.MasterAccountId == A.MasterAccountTypes.Swoop) ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.Geico ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.OonAgero) &&
                (x.Status?.Id ?? 0) >= minGoaStatus &&
                x.Status != Dispatch.Status.Completed &&
                (CurrentUser.Type == User.TypeEnum.Manager ||
                CurrentUser.Type == User.TypeEnum.Dispatcher ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.AaaAcg // ACG requires ALL user types to be able to request GOA.
                ))
            {
                result.Add("DIGITAL_REQUEST_GOA");
            }

            if (CurrentUser.Company.HasFeature(Features.SendFaxes))
                result.Add("FAX");

            if (x.Account?.MasterAccountId == A.MasterAccountTypes.Geico)
            {
                result.Add("DIGITAL_REQUEST_INFO");
            }

            if (CurrentUser.Company.HasFeature(Features.QuickBooks) && x.Status == Status.Completed)
                result.Add("PUSH_TO_QUICKBOOKS");

            if ((x.Account?.MasterAccountId == A.MasterAccountTypes.Geico ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.Swoop ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.Allstate ||
                x.Account?.MasterAccountId == A.MasterAccountTypes.OonAllstate) &&
                (x.Status?.Id ?? 0) >= Dispatch.Status.AtSite.Id &&
                (CurrentUser.Type == User.TypeEnum.Manager ||
                CurrentUser.Type == User.TypeEnum.Dispatcher))
            {
                if (x.Status.IsCurrent())
                    result.Add("DIGITAL_REPORT_SERVICE_FAILURE");

                if (x.Account?.MasterAccountId == A.MasterAccountTypes.Geico)
                    result.Add("DIGITAL_REQUEST_ADDITIONAL_SERVICE");
            }

            if (x.Account?.MasterAccountId == A.MasterAccountTypes.OonAllstate)
            {
                if (Core.GetRedisValue(x.Id + ":ss_goa") == "1" ||
                    Core.GetRedisValue(x.Id + ":ss_sf") == "1" ||
                    Core.GetRedisValue(x.Id + ":ss_dc") == "1")
                {
                    result.Remove("DIGITAL_REQUEST_GOA");
                    result.Remove("DIGITAL_REPORT_SERVICE_FAILURE");
                    result.Remove("DIGITAL_CANCEL");
                    result.Remove("DIGITAL_EXTEND_ETA");
                }
            }

            if (!x.IsLocked)
            {
                result.Add("MODIFY");

                if (x.Status?.Id == Dispatch.Status.Completed.Id)
                    result.Add("UNDO_COMPLETE");

                if (x.Status?.Id != Dispatch.Status.Cancelled.Id &&
                    x.Status?.Id != Dispatch.Status.Completed.Id)
                    result.Add("COMPLETE");

                if (x.Status?.Id == Dispatch.Status.Cancelled.Id && 
                    x.Account?.MasterAccountId != A.MasterAccountTypes.AaaWashington &&
                    x.Account?.MasterAccountId != A.MasterAccountTypes.AaaNewYork)
                    result.Add("UNDO_CANCEL");

                if (x.Status?.Id != Dispatch.Status.Completed.Id &&
                    x.Status?.Id != Dispatch.Status.Cancelled.Id &&
                    !hideManualCancel)
                    result.Add("CANCEL");
            }
            else
                result.Add("VIEW");

            result.Add("EMAIL");

            if ((x.Status?.Id ?? 0) == (int)Entry.EntryStatus.Completed)
            {
                if (x.IsLocked)
                {
                    if (CurrentUser.HasAccessToUnlockCalls())
                        result.Add("UNLOCK");
                }
                else
                {
                    if (CurrentUser.HasAccessToLockCalls())
                        result.Add("LOCK");

                    // do not let unreleased impounds be locked
                    if (x.Impound && !x.Released)
                        result.Remove("LOCK");
                }
            }

            if ((x.Status?.Id ?? 0) == (int)Entry.EntryStatus.Completed)
            {
                var preventWhenLocked = CompanyKeyValue.GetFirstValueOrNull(x.CompanyId,
                   Provider.Towbook.ProviderId, "PreventAuditWhenLocked") == "1";

                if (x.Attributes.ContainsKey(AttributeValue.BUILTIN_DISPATCH_AUDITED))
                {
                    if (CurrentUser.HasAccessToUnauditCalls())
                    {
                        if (!x.IsLocked || !preventWhenLocked)
                            result.Add("UNAUDIT");
                    }
                }
                else
                {
                    if (CurrentUser.HasAccessToAuditCalls())
                    {
                        if (!x.IsLocked || !preventWhenLocked)
                            result.Add("AUDIT");
                    }
                }
            }

            if (CurrentUser?.Type == User.TypeEnum.Manager ||
                CurrentUser?.Type == User.TypeEnum.Dispatcher ||
                (CurrentUser?.Type == User.TypeEnum.Driver &&
                CompanyKeyValue.GetFirstValueOrNull(x.CompanyId,
                    Provider.Towbook.ProviderId, "AllowDriversToViewInternalNotes") == "1"))
            {
                result.Add("INTERNAL_NOTES");
            }

            //1=Prevent moving to Ready to Bill/Billed if not audited, 2=Prevent if not locked, 3=Prevent if not audited and locked
            var preventEmail = CompanyKeyValue.GetFirstValueOrNull(x.CompanyId, Provider.Towbook.ProviderId, "PreventEmailIfNotAuditedLocked");
            if (preventEmail == "1")
            {
                if (!x.IsLocked || !x.Attributes.ContainsKey(AttributeValue.BUILTIN_DISPATCH_AUDITED))
                    result.Remove("EMAIL");
            }

            if (CurrentUser.Type == User.TypeEnum.Manager)
            {
                var deletePermission = CompanyKeyValue.GetFirstValueOrNull(x.CompanyId, Provider.Towbook.ProviderId, "Permission_Calls_Delete") ?? "0";

                if (deletePermission == "" ||
                    deletePermission == "0" ||
                    deletePermission == "1")
                {
                    result.Add("DELETE");
                }
                else
                {
                    var ukv = UserKeyValue.GetByUser(x.CompanyId, CurrentUser.Id, Provider.Towbook.ProviderId, "Permission_Calls_Delete").FirstOrDefault();
                    if (ukv?.Value == "1")
                        result.Add("DELETE");
                }
            }

            // if this is set, then the user is able to change the driver assignment on the call.
            result.Add("ASSIGN_DRIVERS");

            // if the user is a driver or a dispatcher, check for permissions and remove them if necessary
            if (CurrentUser.Type == User.TypeEnum.Driver)
            {
                var preventModifyCompletedCalls = CompanyKeyValue.GetFirstValueOrNull(x.CompanyId, Provider.Towbook.ProviderId, "PreventDriversFromModifyingAfterCompletion");
                if (preventModifyCompletedCalls == "1" && ((x.Status?.Id ?? 0) == Dispatch.Status.Completed.Id))
                {
                    if (CurrentUser.Company.HasFeature(Features.Reimbursements) &&
                        x.CompletionTime > DateTime.Now.AddMinutes(-30))
                    {
                        // if its within last 30 minutes, let the modify so they can enter reimbursements
                        // for stuff like tolls
                        result.Remove("UNDO_COMPLETE");
                    }
                    else
                    {
                        result.Remove("MODIFY");
                        result.Remove("UNDO_COMPLETE");
                    }
                }

                if (CompanyKeyValue.GetFirstValueOrNull(x.CompanyId, Provider.Towbook.ProviderId, "PreventDriversFromModifyingCalls") == "1")
                {
                    // iOS and android don't honor this properly so we cant enforce this for now.
                    //      if (!(System.Web.HttpContext.Current?.IsAppleDevice() ?? false))
                    //          result.Remove("MODIFY");
                }

                var preventCancelCalls = CompanyKeyValue.GetFirstValueOrNull(x.CompanyId, Provider.Towbook.ProviderId, "PreventDriversFromCancelling");
                if (preventCancelCalls == "1")
                {
                    result.Remove("CANCEL");
                    result.Remove("UNDO_CANCEL");
                }

                if (preventCancelCalls == "1" || CompanyKeyValue.GetFirstValueOrNull(x.CompanyId, Provider.Towbook.ProviderId, "PreventDriversFromMCSelfService") == "1")
                {
                    result.Remove("DIGITAL_REQUEST_ADDITIONAL_SERVICE");
                    result.Remove("DIGITAL_REPORT_SERVICE_FAILURE");
                    result.Remove("DIGITAL_REQUEST_GOA");
                    result.Remove("DIGITAL_CANCEL");
                }

                var preventEmailCalls = CompanyKeyValue.GetByCompanyId(x.CompanyId, Provider.Towbook.ProviderId, "PreventDriversFromEmailingCalls").FirstOrDefault();
                if (preventEmailCalls?.Value == "1")
                {
                    result.Remove("EMAIL");
                    result.Remove("FAX");
                }

                result.Remove("ASSIGN_DRIVERS");

            }
            else if (CurrentUser.Type == User.TypeEnum.Dispatcher)
            {
                var preventModifyCompletedCalls = CompanyKeyValue.GetByCompanyId(x.CompanyId, Provider.Towbook.ProviderId, "PreventDispatchersFromModifyingAfterCompletion").FirstOrDefault();
                if (preventModifyCompletedCalls?.Value == "1" && ((x.Status?.Id ?? 0) == Dispatch.Status.Completed.Id))
                    result.Remove("MODIFY");
            }
            else if (CurrentUser.Type == User.TypeEnum.Manager)
            {
                var allowDispacherChange = CompanyKeyValue.GetByCompanyId(x.CompanyId, Provider.Towbook.ProviderId, "AllowManagersToChangeCallDispatcher").FirstOrDefault();
                if (allowDispacherChange?.Value == "1" && !x.IsLocked)
                    result.Add("ASSIGN_DISPATCHER");
            }
            else if (CurrentUser.Type == User.TypeEnum.ReportingOnly)
            {
                result.Remove("MODIFY");
            }
            else if (CurrentUser.IsAccountTypeUser())
            {
                if (!AccountUserIsAllowedToEdit(CurrentUser, x.Status.Id, x.CompanyId))
                {
                    result.Remove("MODIFY");
                    result.Add("VIEW");
                }

                if (!AccountUserIsAllowedToCancel(CurrentUser, x.Status.Id, x.CompanyId))
                {
                    result.Remove("CANCEL");
                }

                result.Remove("COMPLETE");
            }

            if ((CurrentUser.Type == User.TypeEnum.Driver ||
                CurrentUser.Type == User.TypeEnum.Dispatcher) &&
                (CurrentUser.Notes ?? "").Contains("GrantSupervisorRole"))
            {
                var drivers = Towbook.Driver.GetByUserId(CurrentUser.Id);
                if (drivers != null)
                {
                    if (!drivers.Where(o => o.Id == x.DriverId).Any())
                    {
                        result.Remove("MODIFY");
                        result.Remove("ASSIGN_DRIVERS");
                        result.Remove("COMPLETE");
                        result.Remove("CANCEL");
                        result.Remove("UNDO_CANCEL");
                    }
                }
            }

            if (CurrentUser.Type == User.TypeEnum.Manager ||
                CurrentUser.Type == User.TypeEnum.Dispatcher)
            {
                result.Add("DUPLICATE");
            }

            if (HttpContextCurrent.IsMobileAppRequest() &&
                x.IsWithinClosedAccountingPeriod())
            {
                result.Remove("ACKNOWLEDGE_COMPLETE");
                result.Remove("ACKNOWLEDGE_CANCEL");
                result.Remove("UNDO_COMPLETE");
                result.Remove("UNDO_CANCEL");
                result.Remove("MODIFY");
                result.Remove("COMPLETE");
                result.Remove("CANCEL");
                result.Remove("DIGITAL_EXTEND_ETA");
                result.Remove("DIGITAL_REPORT_SERVICE_FAILURE");
                result.Remove("DIGITAL_CANCEL");
                result.Remove("LOCK");
                result.Remove("UNLOCK");
                result.Remove("AUDIT");
                result.Remove("UNAUDIT");
                result.Remove("TOW_FROM_STORAGE");
                result.Remove("UNDO_TOW_FROM_STORAGE");
                result.Remove("UPDATE_STATUS");
                result.Remove("DELETE");
                result.Remove("ASSIGN_DRIVERS");
            }

            return result;
        }


        public static bool ShouldShowChargesWithoutPricesExceptForCash(this CallModel r)
        {
            if (r.CompanyId == 0)
                r.CompanyId = CurrentUser.CompanyId;

            bool blockCharges = false;
            if (CurrentUser.Type == User.TypeEnum.Driver ||
                CurrentUser.Type == User.TypeEnum.Dispatcher)
            {
                var preventDriversFromViewingCharges = CompanyKeyValue.GetByCompanyId(r.CompanyId, Provider.Towbook.ProviderId, "PreventDriversFromViewingInvoiceItems").FirstOrDefault();
                var preventDispatchersFromViewingCharges = CompanyKeyValue.GetByCompanyId(r.CompanyId, Provider.Towbook.ProviderId, "PreventDispatchersFromViewingInvoiceItems").FirstOrDefault();

                var drivers = Extric.Towbook.Driver.GetByUserId(CurrentUser.Id);

                if (drivers != null && drivers.Any())
                {
                    foreach (var d in drivers)
                    {
                        var hidePricing = DriverKeyValue.GetByDriver(r.CompanyId, d.Id, Provider.Towbook.ProviderId, "HidePricing").FirstOrDefault();
                        if (hidePricing != null && new string[] { "3" }.Contains(hidePricing.Value))
                        {
                            blockCharges = true;
                            break;
                        }
                    }
                }

                if ((preventDriversFromViewingCharges != null &&
                    preventDriversFromViewingCharges.Value == "3" &&
                    CurrentUser.Type == Towbook.User.TypeEnum.Driver) ||
                     (preventDispatchersFromViewingCharges != null &&
                     preventDispatchersFromViewingCharges.Value == "3" &&
                     CurrentUser.Type == Towbook.User.TypeEnum.Dispatcher))
                {
                    blockCharges = true;
                }
            }

            return blockCharges;
        }

        public static async Task<bool> ShouldShowChargesWithoutPricesExceptForCashAsync(this CallModel r)
        {
            if (r.CompanyId == 0)
                r.CompanyId = CurrentUser.CompanyId;

            bool blockCharges = false;

            if (CurrentUser.Type == User.TypeEnum.Driver ||
                CurrentUser.Type == User.TypeEnum.Dispatcher)
            {
                var preventDriversFromViewingCharges = (await CompanyKeyValue.GetByCompanyIdAsync(
                    r.CompanyId, Provider.Towbook.ProviderId, "PreventDriversFromViewingInvoiceItems")).FirstOrDefault();

                var preventDispatchersFromViewingCharges = (await CompanyKeyValue.GetByCompanyIdAsync(
                    r.CompanyId, Provider.Towbook.ProviderId, "PreventDispatchersFromViewingInvoiceItems")).FirstOrDefault();

                var drivers = await Extric.Towbook.Driver.GetByUserIdAsync(CurrentUser.Id);

                if (drivers != null && drivers.Any())
                {
                    foreach (var d in drivers)
                    {
                        var hidePricing = (await DriverKeyValue.GetByDriverAsync(
                            r.CompanyId, d.Id, Provider.Towbook.ProviderId, "HidePricing")).FirstOrDefault();

                        if (hidePricing != null && new string[] { "3" }.Contains(hidePricing.Value))
                        {
                            blockCharges = true;
                            break;
                        }
                    }
                }

                if ((preventDriversFromViewingCharges != null &&
                    preventDriversFromViewingCharges.Value == "3" &&
                    CurrentUser.Type == Towbook.User.TypeEnum.Driver) ||
                     (preventDispatchersFromViewingCharges != null &&
                     preventDispatchersFromViewingCharges.Value == "3" &&
                     CurrentUser.Type == Towbook.User.TypeEnum.Dispatcher))
                {
                    blockCharges = true;
                }
            }

            return blockCharges;
        }



        public static bool IsLocked(this CallModel cm)
        {
            return (cm?.Insights != null && cm.Insights.Any(o => o.Key.ToLowerInvariant() == "lockedbyuserid")) ||
                ((CurrentUser?.Type == User.TypeEnum.PoliceDispatcher ||
                CurrentUser?.Type == User.TypeEnum.PoliceManager ||
                CurrentUser?.Type == User.TypeEnum.PoliceOfficer) && cm.Status.Id != Status.Waiting.Id);
        }

        private static async Task<CallModel> UpdateTowOutModelAsync(this CallModel r)
        {
            if (!IsTowoutCall(r))
                return r;

            // show call to first driver(s) as completed
            if (CurrentUser.Type == User.TypeEnum.Driver)
            {
                if (new int[] { Status.Waiting.Id, Status.Dispatched.Id, Status.EnRoute.Id, Status.AtSite.Id, Status.BeingTowed.Id, Status.DestinationArrival.Id }.Contains(r.Status.Id))
                {
                    var drivers = await Driver.GetByUserIdAsync(CurrentUser.Id);
                    if (drivers != null)
                    {
                        var isFirstAssetDriver = r.Assets?.FirstOrDefault()?.Drivers?.Any(a => a.Driver != null && drivers.Select(s => s.Id).Contains(a.Driver.Id));
                        var isSecondAssetDriver = r.Assets?.Skip(1)?.FirstOrDefault()?.Drivers?.Any(a => a.Driver != null && drivers.Select(s => s.Id).Contains(a.Driver.Id));

                        if (!(isFirstAssetDriver == null && isSecondAssetDriver == null))
                        {
                            if (isFirstAssetDriver.GetValueOrDefault() && !isSecondAssetDriver.GetValueOrDefault())
                                r.Status.Id = 5;
                        }
                    }
                }
            }

            // handle status of tow out by second asset.
            var currentWaypointId = r.Assets
                    .Where(a => a.Drivers != null && a.Drivers.Where(b => b.Driver?.Id != null).Any())
                    .SelectMany(s => s.Drivers)
                    .LastOrDefault()?.Driver?.CurrentWaypointId;

            r.Status = CallStatusUpdateModel.Map(r.Status.Id, 
                currentWaypointId,
                r.Waypoints.Where(w => w.Title.ToLowerInvariant().Contains("tow out")),
                (bool)((await CompanyKeyValue.GetFirstValueOrNullAsync(r.CompanyId, Provider.Towbook.ProviderId, "EnableDestinationArrivalStatus")) == "1"));

            // tow desintation
            if (!r.Waypoints.Where(a => a.Title == "Tow Out Destination").Any())
            {
                r.TowDestination = string.Empty;

                // always enforce a tow out destination to the waypoints (for showing the correct label in the client apps)
                r.Waypoints = r.Waypoints.Append(new CallWaypointModel()
                {
                    Id = 0,
                    Address = String.Empty,
                    Title = "Tow Out Destination",
                    Position = r.Waypoints.Count() + 1
                }).ToArray();
            }

            // mobile app and driver types
            if (HttpContextCurrent.IsAndroidDevice() ||
                HttpContextCurrent.IsAppleDevice() ||
                CurrentUser.Type == User.TypeEnum.Driver)
            {
                // use second asset only
                r.Assets = r.Assets.Where(w => w == r.Assets.Last()).ToArray();

                // Mobile apps will force destination to be an impound lot
                // Mimic a non-impound call for the towout portion.
                r.Impound = false;
                r.ImpoundLotId = null;
                r.ImpoundDetails = null;

                // waypoints for mobile/drivers
                r.Waypoints = r.Waypoints
                    .Where(w => w.Title.ToLowerInvariant().StartsWith("tow out"))
                    .Select(a => {
                        if (a.Title.ToLowerInvariant().StartsWith("tow out"))
                        {
                            if (a.Title == "Tow Out Pickup")
                            {
                                a.Title = "Pickup";
                                r.TowSource = a.Address;
                            }

                            if (a.Title == "Tow Out Destination")
                            {
                                a.Title = "Destination";
                                r.TowDestination = a.Address;
                            }

                            a.Position = a.Position - 2;
                        }
                        return a;
                    }).ToArray();


                // invoice items - return second asset items only
                r.InvoiceItems = r.InvoiceItems.Where(w => r.Assets.Any(a => a.Id == w.AssetId)).ToArray();
            }

            return r;
        }

        private static async Task<CallModel> BlockCoverageAsync(this CallModel call)
        {
            if (call == null)
                return call;

            if (CurrentUser?.Type == User.TypeEnum.Driver)
            {
                var blockCoverage = (await CompanyKeyValue.GetByCompanyIdAsync(call.CompanyId,
                    Provider.Towbook.ProviderId, "PreventDriversFromViewingCoverage")).FirstOrDefault();

                if (blockCoverage?.Value == "1")
                {
                    call.Attributes = call.Attributes?.Where(o => o.AttributeId != AttributeValue.BUILTIN_MOTORCLUB_COVERAGELIMIT).ToArray();
                }
            }

            return call;
        }


        public static CallModel BlockContactDetails(this CallModel call)
        {
            if (call == null)
                return call;

            if (CurrentUser?.Type == User.TypeEnum.Driver)
            {
                var block = CompanyKeyValue.GetFirstValueOrNull(call.CompanyId,
                    Provider.Towbook.ProviderId, "PreventDriversFromViewingContactDetails");

                if (block == "1")
                {
                    call.Contacts = call.Contacts.Where(o => o != null)
                        .Select(o => new CallContactModel()
                        {
                            CallId = o.CallId,
                            Id = o.Id,
                            Name = o.Name,
                            Email = string.Empty,
                            Phone = string.Empty,
                            Address = string.Empty,
                            State = string.Empty,
                            City = string.Empty,
                            Zip = string.Empty,
                        }).ToArray();
                }
            }

            return call;
        }
        public static async Task<CallModel> BlockContactDetailsAsync(this CallModel call)
        {
            if (call == null)
                return call;

            if (CurrentUser?.Type == User.TypeEnum.Driver)
            {
                var block = await CompanyKeyValue.GetFirstValueOrNullAsync(call.CompanyId,
                    Provider.Towbook.ProviderId, "PreventDriversFromViewingContactDetails");

                if (block == "1")
                {
                    call.Contacts = call.Contacts.Where(o => o != null)
                        .Select(o => new CallContactModel()
                        {
                            CallId = o.CallId,
                            Id = o.Id,
                            Name = o.Name,
                            Email = string.Empty,
                            Phone = string.Empty,
                            Address = string.Empty,
                            State = string.Empty,
                            City = string.Empty,
                            Zip = string.Empty,
                        }).ToArray();
                }
            }

            return call;
        }


        public static CallModel CheckPreventByUserTypeKeyValue(CallModel call,
            string keyName,
            string trueValue,
            Action<CallModel> whenTrue,
            User.TypeEnum userTypeToMatch = User.TypeEnum.Driver)
        {
            if (call == null || keyName == null || trueValue == null || whenTrue == null)
                return call;

            if (CurrentUser?.Type == userTypeToMatch)
            {
                var kv = CompanyKeyValue.GetByCompanyId(call.CompanyId,
                    Provider.Towbook.ProviderId, keyName).FirstOrDefault();

                if (kv?.Value == trueValue)
                {
                    whenTrue(call);
                    return call;
                }
            }

            return call;
        }

        public static async Task<CallModel> CheckPreventByUserTypeKeyValueAsync(CallModel call,
        string keyName,
        string trueValue,
        Action<CallModel> whenTrue,
        User.TypeEnum userTypeToMatch = User.TypeEnum.Driver)
        {
            if (call == null || keyName == null || trueValue == null || whenTrue == null)
                return call;

            if (CurrentUser?.Type == userTypeToMatch)
            {
                var kv = (await CompanyKeyValue.GetByCompanyIdAsync(call.CompanyId,
                    Provider.Towbook.ProviderId, keyName)).FirstOrDefault();

                if (kv?.Value == trueValue)
                {
                    whenTrue(call);
                    return call;
                }
            }

            return call;
        }


        public static bool AccountUserIsAllowedToEdit(User user, int callStatusId, int companyId)
        {
            if (user.IsAccountTypeUser())
            {
                string accUserModifyBlockKey = CompanyKeyValue.GetFirstValueOrNull(companyId,
                    Provider.Towbook.ProviderId, "PreventAccountUsersFromModifyingCalls");

                if (accUserModifyBlockKey == "1" ||
                    (accUserModifyBlockKey == "2" && callStatusId > Dispatch.Status.Waiting.Id) ||
                    (accUserModifyBlockKey == "3" && callStatusId > Dispatch.Status.Dispatched.Id))
                {
                    return false;
                }
            }
            return true;
        }

        public static bool AccountUserIsAllowedToCancel(User user, int callStatusId, int companyId)
        {
            if (user.IsAccountTypeUser())
            {
                string accUserCancelBlockKey = CompanyKeyValue.GetFirstValueOrNull(companyId,
                    Provider.Towbook.ProviderId, "PreventAccountUsersFromCancellingCalls");

                if (accUserCancelBlockKey == "1" ||
                    (accUserCancelBlockKey == "2" && callStatusId > Dispatch.Status.Waiting.Id) ||
                    (accUserCancelBlockKey == "3" && callStatusId > Dispatch.Status.Dispatched.Id))
                {
                    return false;
                }
            }
            return true;
        }

        public static CallModel InitializeOrUpdateInvoiceStatusId(this CallModel call, Entry e)
        {
            if (call.InvoiceStatusId != null)
                e.InvoiceStatusId = call.InvoiceStatusId.Value;

            // On new/duplicated calls created as completed, set invoice status Id to completed.
            // This will make sure that cosmos/Azure can be queried to retrieve
            // recent jobs with the byAccounts query (account calls) without having to 
            // need a second save event to get the invoice status synced correctly.
            if (e.Id <= 0 &&
                /* Note: dispatch editor sends completionTime value for complated calls */
                (call.Status?.Id == 5 || call.CompletionTime.HasValue) && 
                /* Unassigned account is not billable. Account id must exist. */
                call.Account?.Id > 1)
            {
                e.InvoiceStatusId = (int)A.InvoiceStatus.InvoiceStatusType.Completed;

                if (call.InvoiceStatusId == null)
                    call.InvoiceStatusId = (int)A.InvoiceStatus.InvoiceStatusType.Completed;
            }

            return call;
        }

        internal static bool IsTowoutCall(CallModel cm)
            => cm.Attributes?.FirstOrDefault(a => a.AttributeId == Dispatch.AttributeValue.BUILTIN_TOWOUT_CALL)?.Value == "1";
    }
}
