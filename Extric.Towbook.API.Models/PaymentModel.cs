using System;
using System.Collections.ObjectModel;
using System.Linq;
using Extric.Towbook.Dispatch;
using Extric.Towbook.Integration;

namespace Extric.Towbook.API.Models
{
    public class PaymentModel
    {
        public int Id { get; set; }
        public int InvoiceId { get; set; }
        public decimal Amount { get; set; }
        public int Type { get; set; }
        public string TypeName { get; set; }
        public int ClassId { get; set; }
        public string ReferenceNumber { get; set; }
        public DateTime CreateDate { get; set; }
        public DateTime? PaymentDate { get; set; }
        public int UserId { get; set; }
        public int StatusId { get; set; } 
        public DateTime? LastUpdateDate { get; set; }
        public int? PaymentVerificationId { get; set; }
        public bool IsVoid { get; set; }
        public int? VoidedByUserId { get; set; }
        public DateTime? VoidedDate { get; set; }
        
        public TransactionDetailsModel TransactionDetails { get; set; }

        public string[] AvailableActions { get; set; }

        public static PaymentModel Map(InvoicePayment o, User currentUser)
        {
            return new PaymentModel()
            {
                Id = o.Id,
                InvoiceId = o.InvoiceId,
                Amount = o.Amount,
                Type = o.PaymentType.Id,
                TypeName = o.PaymentType.Name,
                ClassId = o.ClassId,
                ReferenceNumber = o.ReferenceId,
                CreateDate = o.CreateDate,
                PaymentDate = o.PaymentDate,
                UserId = o.OwnerUserId,
                PaymentVerificationId = o.PaymentVerificationId,
                IsVoid = o.IsVoid,
                VoidedByUserId = o.VoidedByUserId,
                VoidedDate = o.VoidedDate,
                AvailableActions = GetAvailableActions(o, currentUser)
            };
        }

        public static string[] GetAvailableActions(InvoicePayment o, User u)
        {
            if (o == null)
                throw new ArgumentNullException(nameof(o));

            if (u == null)
                return Array.Empty<string>();

            var actions = new Collection<string>();
            if (!o.IsVoid)
            {
                var allow = UserKeyValue.GetByUser(u.CompanyId, u.Id, Provider.Towbook.ProviderId, "Permission_Payments_Void")
                    .FirstOrDefault()?.Value;

                var prevent = CompanyKeyValue.GetFirstValueOrNull(
                    u.CompanyId,
                    Provider.Towbook.ProviderId,
                    "PreventPaymentVoiding");

                if (allow == null)
                {
                    if (u.Type == User.TypeEnum.Driver && prevent == "1")
                        allow = "2";
                    else if ((u.Type == User.TypeEnum.Driver || u.Type == User.TypeEnum.Dispatcher) && 
                        prevent == "2")
                        allow = "2";
                    else if ((u.Type == User.TypeEnum.Driver ||
                        u.Type == User.TypeEnum.Dispatcher ||
                        u.Type == User.TypeEnum.Accountant) &&
                        prevent == "3")
                        allow = "2";
                }

                if (u.Type == User.TypeEnum.Manager ||
                    u.Type == User.TypeEnum.Accountant)
                {
                    actions.Add("VOID");
                }

                if (allow == "2")
                    actions.Remove("VOID");
                else if (allow == "1" && !actions.Contains("VOID"))
                    actions.Add("VOID");

            }

            return actions.ToArray();
        }
    }
}
