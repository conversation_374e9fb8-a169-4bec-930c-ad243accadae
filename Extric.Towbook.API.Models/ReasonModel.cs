using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Extric.Towbook.API.Models
{
    public class ReasonModel
    {
        public int Id { get; set; }
        public string Reason { get; set; }
        public string Description { get; set; }
        public bool? Impound { get; set; }

        public bool? Active { get; set; }
        public int? CompanyId { get; set; }

        public static ReasonModel Map(Dispatch.Reason reason)
        {
            if (reason == null)
                return null;

            return new ReasonModel()
            {
                Id = reason.Id,
                Reason = reason.Name,
                Impound = reason.Impound,
                Description = reason.Description,
                Active = reason.IsActive,
                CompanyId = reason.CompanyId > 0 ? (int?)reason.CompanyId : null
            };
        }

        public static Dispatch.Reason Map(ReasonModel model, Dispatch.Reason original)
        {
            if (model == null)
                return original;

            Dispatch.Reason reason = null;

            if (original != null)
                reason = original;
            else
                reason = new Dispatch.Reason();

            if (model.Reason != null)
                reason.Name = model.Reason;

            if (model.Description != null)
                reason.Description = model.Description;

            if (model.Impound != null)
                reason.Impound = model.Impound.Value;

            if (model.Active != null && model.Active != original.IsActive)
                throw new TowbookException("To mark a reason as Inactive, send an HTTP DELETE request instead.");

            return reason;
        }
    }
}
