using System;
using System.Collections.Generic;
using Extric.Towbook.Company;
using System.ComponentModel.DataAnnotations;


namespace Extric.Towbook.API.Models
{
    public class StorageRateModel {

        public int Id { get; set; }
        public string AccountName { get; set; }
        public int AccountId { get; set; }
        public List<Extric.Towbook.Accounts.Account> Accounts { get; set; }

        [DisplayFormat(DataFormatString = "{0:#,##0.00#}", ApplyFormatInEditMode = true)]
        public decimal MaximumCharge { get; set; }
        public int MaxInitial { get; set; }
        public int StorageGracePeriodHours { get; set; }
        public StorageRate.StorageChargeStartEnum ChargeStart { get; set; }

        [Display(Name = "Don't include Saturdays in days of storage calculation")]
        public bool FreeSaturdays { get; set; }

        [Display(Name = "Don't include Sundays in days of storage calculation")]
        public bool FreeSundays { get; set; }

        [Display(Name = "Automatically charge the first day of storage at Midnight (override the grace period)")]
        public bool ForceChargeAtMidnight { get; set; }

        public int CompanyId { get; set; }

        public static StorageRateModel MapDomainObjectToModel(StorageRate input) 
        {
            StorageRateModel d = new StorageRateModel();

            d.Id = input.Id;
            d.AccountId = input.AccountId;
            d.MaximumCharge = input.MaximumCharge;
            d.MaxInitial = input.StorageChargeInitialHoursLimitToOneDay;
            d.StorageGracePeriodHours = input.StorageGracePeriodHours;
            d.ChargeStart = input.StorageChargeStart;
            d.CompanyId = input.CompanyId;

            d.FreeSaturdays = input.FreeSaturdays;
            d.FreeSundays = input.FreeSundays;

            d.ForceChargeAtMidnight = !input.MidnightWaitForGracePeriod;

            return d;
        }

        public static StorageRate MapModelToDomainObject(StorageRateModel model, StorageRate input)
        {
            input.MaximumCharge = model.MaximumCharge;
            input.StorageChargeInitialHoursLimitToOneDay = model.MaxInitial;
            input.StorageGracePeriodHours = model.StorageGracePeriodHours;
            input.StorageChargeStart = model.ChargeStart;

            input.FreeSaturdays = model.FreeSaturdays;
            input.FreeSundays = model.FreeSundays;

            input.MidnightWaitForGracePeriod = !model.ForceChargeAtMidnight;

            return input;
        }

        internal static List<StorageRateModel> MapDomainObjectListToModelList(List<StorageRate> input)
        {
            List<StorageRateModel> output = new List<StorageRateModel>();

            foreach (StorageRate t in input)
            {
                output.Add(MapDomainObjectToModel(t));
            }

            return output;
        }
    }
    
}