using Extric.Towbook.Dispatch;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Extric.Towbook.API.Models
{
    public class EntryLocationRequestModel
    {
        public int Id { get; set; }
        public decimal? Latitude { get; set; }
        public decimal? Longitude { get; set; }
        public int? GpsAccuracy { get; set; }
        public DateTime CreateDate { get; set; }

        public DateTime? LocationDate { get; set; }
        public string IpAddress { get; set; }
        public int? OwnerId { get; set; }
        public string MobileNumber { get; set; }

        public static EntryLocationRequestModel Map(EntryLocationRequest d)
        {
            var r = new EntryLocationRequestModel();

            r.Id = d.Id;
            r.CreateDate = d.CreateDate;
            r.LocationDate  = d.LocationDate;
            r.GpsAccuracy = d.GpsAccuracy;
            r.IpAddress = d.IpAddress;
            r.Latitude = d.Latitude;
            r.Longitude = d.Longitude;
            r.OwnerId = d.OwnerUserId;
            r.<PERSON>umber = d.MobileNumber;

            return r;
        }

        public static EntryLocationRequest Map(EntryLocationRequestModel model, EntryLocationRequest original = null)
        {
            EntryLocationRequest r = new EntryLocationRequest();

            if (original != null)
                r = original;
            r.Id = model.Id;

            r.CreateDate = model.CreateDate;
            
            if (model.GpsAccuracy != null)
                r.GpsAccuracy = model.GpsAccuracy.Value;

            if (model.IpAddress != null)
                r.IpAddress = model.IpAddress;

            if (model.Latitude != null)
                r.Latitude = model.Latitude.Value;

            if (model.Longitude != null)
                r.Longitude = model.Longitude.Value;

            if (r.OwnerUserId != null)
                r.OwnerUserId = model.OwnerId;
            
            if (model.MobileNumber != null)
                r.MobileNumber = model.MobileNumber;
            
            return r;
        }
    }
}