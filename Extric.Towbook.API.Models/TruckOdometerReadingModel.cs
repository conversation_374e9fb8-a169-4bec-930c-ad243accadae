using System.Collections.Generic;
using Extric.Towbook;
using System;
using Extric.Towbook.Dispatch.CallModels;

namespace Extric.Towbook.API.Models
{
    public class TruckOdometerReadingModel
    {
        public int Id { get; set; }
        public int TruckId { get; set; }
        public float Odometer { get; set; }
        
        public int UserId { get; set; }
        public DateTime Date { get; set; }

        public PartialUserModel User { get; set; }

        /// <summary>
        /// Maps a TruckOdometerReading to a model class suitable for REST API and UI display.
        /// </summary>
        /// <param name="input">Domain object to map</param>
        /// <returns></returns>
        public static TruckOdometerReadingModel Map(TruckOdometerReading input)
        {
            TruckOdometerReadingModel l = new TruckOdometerReadingModel();

            l.Id = input.Id;
            l.TruckId = input.TruckId;
            l.UserId = input.UserId;

            l.Odometer = input.Odometer;
            l.Date = input.Date;

            var user = Extric.Towbook.User.GetById(l.UserId);

            if (user != null)
                l.User = new PartialUserModel() { FullName = user.FullName, Id = user.Id };

            return l;
        }

        /// <summary>
        /// Maps a TruckOdometerReadingModel to the domain TruckOdometerReading object. 
        /// </summary>
        /// <param name="model">Model class to translate/map</param>
        /// <param name="input">The domain TruckOdometerReading object to use as a base</param>
        /// <returns>TruckOdometerReading with the Model values mapped.</returns>
        public static TruckOdometerReading Map(TruckOdometerReadingModel model, TruckOdometerReading input)
        {
            if (input == null)
                input = new TruckOdometerReading();

            input.Odometer = model.Odometer;
            input.Date = model.Date;
            input.TruckId = model.TruckId;
            input.UserId = model.UserId;

            return input;
        }
    }

}