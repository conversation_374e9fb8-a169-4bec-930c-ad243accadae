using Extric.Towbook.Company;

namespace Extric.Towbook.API.Models
{
    public class ElectronicConsentDisclaimerModel
    {
        public int Id { get; set; }
        public int CompanyId { get; set; }
        public string Disclaimer { get; set; }

        public static ElectronicConsentDisclaimerModel Map(ElectronicConsentDisclaimer disclaimer, User currentUser)
        {
            return new ElectronicConsentDisclaimerModel()
            {
                Id = disclaimer.Id,
                CompanyId = disclaimer.Id == 1 ? currentUser.CompanyId : disclaimer.CompanyId,
                Disclaimer = disclaimer.Content
            };
        }
    }
}
