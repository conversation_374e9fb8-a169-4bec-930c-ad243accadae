using Extric.Towbook.Chat;
using System;
using System.Linq;

namespace Extric.Towbook.API.Models.Chats
{
    public class ChatModel
    {
        public long Id { get; set; }
        public DateTime CreateDate { get; set; }
        /// <summary>
        /// Who started the conversation
        /// </summary>
        public int OwnerUserId { get; set; }

        /// <summary>
        /// UserId's that are participating in the conversation
        /// </summary>
        public int[] Participants { get; set; }

        /// <summary>
        /// How many messages have not yet been downloaded for the user requesting this chat data?
        /// </summary>
        public int UndeliveredMessages { get; set; }

        /// <summary>
        /// How many messages have not yet been confirmed as read for the user requesting this chat data?
        /// </summary>
        public int UnreadMessages { get; set; }
        
        /// <summary>
        /// Returns a list of messages for this chat.
        /// </summary>
        public ChatMessageModel[] Messages { get; set; }

        /// <summary>
        /// Type of chat (Company, Call, Private, etc)
        /// </summary>
        public ChatTypes Type { get; set; }

        /// <summary>
        /// Status of the chat. Open, Closed. 
        /// </summary>
        public ChatStatus? Status { get; set; }

        /// <summary>
        /// If the conversation is set to ReadOnly, no new messages can be sent for it.
        /// </summary>
        public bool? ReadOnly { get; set; }

        /// <summary>
        /// Title of the chat as supplied by the server
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// The last known message for this chatId
        /// </summary>
        public ChatMessageModel LastMessage { get ; set; }


        public static ChatModel Map(Chat.Chat p) => Map(p, null);

        public static ChatModel Map(Chat.Chat p, ChatMessageModel lastMessage = null)
        {
            var rv = new ChatModel();

            rv.Id = p.Id;
            rv.OwnerUserId = p.OwnerUserId;
            rv.Status = p.Status;
            rv.CreateDate = p.CreateDate;
            rv.ReadOnly = p.ReadOnly;
            rv.Name = p.Name;
            rv.Type = (ChatTypes)p.ChatTypeId;

            rv.LastMessage = lastMessage;

            rv.UnreadMessages = p.UnreadMessages;

            return rv;
        }

        public static Chat.Chat Map(ChatModel model)
        {
            var cm = new Chat.Chat();

            if (model.Status != null)
                cm.Status = model.Status == null ? ChatStatus.Active : model.Status.Value;

            if (model.ReadOnly != null)
                cm.ReadOnly = model.ReadOnly.Value;

            cm.ChatTypeId = (int)model.Type;

            return cm;
        }
    }
}
