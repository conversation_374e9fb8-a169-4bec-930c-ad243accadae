using System;
using Extric.Towbook.Integrations.Email;

namespace Extric.Towbook.API.Models
{
    public class EmailDomainModel
    {
        public int Id { get; set; }
        public string Domain { get; set; }
        public DateTime CreateDate { get; set; }
        public bool Deleted { get; set; }
        public int OwnerUserId { get; set; }
        public static EmailDomainModel Map(EmailDomain s)
        {
            var r = new EmailDomainModel();

            r.Id = s.EmailDomainId;
            r.CreateDate = s.CreateDate;
            r.Deleted = s.Deleted;
            r.Domain = s.Domain;
            r.OwnerUserId = s.OwnerUserId;

            return r;
        }

        public static EmailDomain Map(EmailDomainModel model, EmailDomain original = null)
        {
            EmailDomain s = new EmailDomain();

            if (original != null)
                s = original;

            s.CreateDate = model.CreateDate;
            s.Deleted = model.Deleted;

            if (model.Domain != null)
                s.Domain = model.Domain;

            s.OwnerUserId = model.OwnerUserId;

            return s;
        }
    }
}
