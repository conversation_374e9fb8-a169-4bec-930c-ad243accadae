using Extric.Towbook.Utility;
using Newtonsoft.Json;

namespace Extric.Towbook.API.Models
{
    public class ParkingPermitRequestQuery
    {
        public string Plate { get; set; }
        public int? ListId { get; set; }

        [JsonConverter(typeof(StringToIntArrayConverter))]
        public int[] StatusIds { get; set; }
        public int? Page { get; set; }
        public int? PageSize { get; set; }

    }
}