using Extric.Towbook.Accounts;

namespace Extric.Towbook.API.Models
{
    public class ParkingPermitFeeModel
    {
        public int Id { get; set; }
        public int CompanyId { get; set; }
        public int? AccountId { get; set; }
        public int FeeNumber { get; set; }
        public int? ListId { get; set; }
        public decimal Amount { get; set; }


        public static ParkingPermitFeeModel Map(ParkingPermitFee fee)
        {
            if (fee == null)
            {
                return null;
            }
            
            var p = new ParkingPermitFeeModel();

            p.Id = fee.Id;
            p.CompanyId = fee.CompanyId;
            p.AccountId = fee.AccountId;
            p.FeeNumber = fee.FeeNumber;
            p.ListId = fee.PermitListId;
            p.Amount = fee.Amount;

            return p;
        }

        public static ParkingPermitFee Map(ParkingPermitFeeModel model, User currentUser, ParkingPermitFee original = null)
        {
            ParkingPermitFee ppf = new ParkingPermitFee();

            if (original != null)
                ppf = original;

            ppf.Id = model.Id;
            ppf.CompanyId = model.CompanyId;
            ppf.AccountId = model.AccountId;
            ppf.FeeNumber = model.FeeNumber;
            ppf.Amount = model.Amount > 0 ? model.Amount : 0;

            if (model.ListId != null)
                ppf.PermitListId = model.ListId.Value;
            else
                ppf.PermitListId = 1;

            ppf.OwnerUserId = currentUser.Id;

            return ppf;
        }
    }
}
