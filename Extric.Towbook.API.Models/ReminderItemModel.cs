using System.Collections.Generic;
using System.Linq;
using Extric.Towbook.Impounds;

namespace Extric.Towbook.API.Models
{
    public class ReminderItemModel
    {
        public int ReminderItemId { get; set; }
        public int CompanyId { get; set; }
        public int Days { get; set; }
        public int ImpoundTypeId { get; set; }
        public string ImpoundTypeName { get; set; }
        public string Title { get; set; }
        public bool SendReminder { get; set; }
        public bool SendEmail { get; set; }
        public int? LetterTemplateId { get; set; }
        public string LetterTemplateName { get; internal set; }


        public static ReminderItemModel MapDomainObjectToModel(ReminderItem input)
        {
            ReminderItemModel r = new ReminderItemModel();

            r.ReminderItemId = input.Id;
            r.CompanyId = input.CompanyId;
            r.Days = input.Days;
            r.ImpoundTypeId = input.ImpoundTypeId;
            r.ImpoundTypeName = ((ImpoundType)input.ImpoundTypeId).ToString();
            r.Title = input.Title   ;
            r.SendReminder = input.SendReminder;
            r.SendEmail = input.SendEmail;
            r.LetterTemplateId = input.LetterTemplateId;

            return r;
        }

        public static ReminderItem MapModelToDomainObject(ReminderItemModel model, ReminderItem input)
        {
            input.Days = model.Days;
            input.ImpoundTypeId = model.ImpoundTypeId;
            input.Title = model.Title;
            input.SendReminder = model.SendReminder;
            input.SendEmail = model.SendEmail;
            input.LetterTemplateId = model.LetterTemplateId;

            return input;
        }

        internal static List<ReminderItemModel> MapDomainObjectListToModelList(
            IEnumerable<ReminderItem> input, 
            IEnumerable<LetterTemplate> templates = null)
        {
            List<ReminderItemModel> output = new List<ReminderItemModel>();

            foreach (ReminderItem t in input)
            {
                var m = MapDomainObjectToModel(t);

                var p = templates.FirstOrDefault(f => f.Id == t.LetterTemplateId);
                if (p != null)
                {
                    m.LetterTemplateName = p.Title;
                    m.LetterTemplateId = p.Id;
                }

                output.Add(m);
            }

            return output;
        }
    }

}