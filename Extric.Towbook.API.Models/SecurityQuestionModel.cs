using System;
using System.Collections.Generic;
using System.Linq;

namespace Extric.Towbook.API.Models
{
    public class SecurityQuestionAnswer
    {
        public int Id { get; set; }
        public string Answer { get; set; }
    }

    public class SecurityQuestionModel
    {
        public int Id { get; set; }
        public string Question { get; set; }

        public SecurityQuestionAnswer[] QuestionAnswers { get; set; }
        
        public static SecurityQuestionModel Map(SecurityQuestion sq)
        {
            var rv = new SecurityQuestionModel();

            rv.Id = sq.Id;
            rv.Question = sq.Question;

            return rv;
        }
    }
}