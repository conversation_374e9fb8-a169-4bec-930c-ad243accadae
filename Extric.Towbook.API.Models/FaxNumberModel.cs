using Extric.Towbook.Integrations.Faxing;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Extric.Towbook.API.Models
{
    public class FaxNumberModel
    {
        public int Id { get; set; }
        public string Number { get; set; }
        public int CompanyId { get; set; }
        public DateTime CreateDate { get; set; }
        public int OwnerUserId { get; set; }
        public bool Deleted { get; set; }

        public static FaxNumberModel Map(FaxNumber s)
        {
            var r = new FaxNumberModel();

            r.Id = s.Id;
            r.CreateDate = s.CreateDate;
            r.Deleted = s.Deleted;
            r.OwnerUserId = s.OwnerUserId;
            r.Number = s.Number;
            r.CompanyId = s.CompanyId;

            return r;
        }

        public static FaxNumber Map(FaxNumberModel model, FaxNumber original = null)
        {
            FaxNumber s = new FaxNumber();

            if (original != null)
                s = original;

            s.CreateDate = model.CreateDate;
            s.Deleted = model.Deleted;
            s.CompanyId = model.CompanyId;
            
            if (model.Number != null)
                s.Number = model.Number;

            s.OwnerUserId = model.OwnerUserId;

            return s;
        }
    }
}