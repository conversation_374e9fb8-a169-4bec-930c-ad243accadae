using Extric.Towbook.Commissions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Extric.Towbook.API.Models
{
    public class CommissionModel
    {
        public IEnumerable<CompanyCommissionModel> Company { get; set; }
        public IEnumerable<DriverCommissionModel> Drivers { get; set; }
        public IEnumerable<RateItemCommissionModel> Rates { get; set; }
    }

    public class CompanyCommissionModel
    {
        public int Id { get; set; }
        public decimal Rate { get; set; }
    }

    public class DriverCommissionModel
    {
        public int DriverId { get; set; }
        public decimal? Rate { get; set; }

        public static async Task<IEnumerable<DriverCommissionModel>> GetCommissionDriversAsync(IEnumerable<Company.Company> companies)
        {
            return (await Driver.GetByCompaniesAsync(companies))
                .Where(o => o.CommissionRate != null)
                .Select(o => new DriverCommissionModel()
                {
                    DriverId = o.Id,
                    Rate = o.CommissionRate
                });
        }
    }

    public class RateItemCommissionModel
    {
        public int RateItemId { get; set; }
        public int? DriverId { get; set; }
        public string Rate { get; set; }
        public IEnumerable<BodyTypeCommissionModel> BodyTypes { get; set; }

        public static async Task<IEnumerable<RateItemCommissionModel>> InternalGetCommissionItems(int[] companyIds, int? driverId = -1, bool includeCompanyAndDriverDefaults = false)
        {
            var allRates = await CommissionRateItem.GetByCompanies(companyIds, driverId.GetValueOrDefault());
            var allRatesExtended = new List<CommissionRateItemBodyType>();

            if (includeCompanyAndDriverDefaults)
                allRatesExtended = (await CommissionRateItemBodyType.GetAllDefaultByCompanies(companyIds)).ToList();
            else
                allRatesExtended = await CommissionRateItemBodyType.GetByCompanies(companyIds, driverId);


            // get a unique/distinct list of all rateItems with commissions specified
            var rateItemIds = allRates.Select(o => o.RateItem?.Id ?? 0)
                .Union(allRatesExtended.Select(o => o.RateItem?.Id ?? 0))
                .Distinct()
                .ToList();

            // get a unique/distinct list of all drivers that have a commission set at either the RateItem or BodyType level.
            var driverIds = allRates.Where(o => o.Driver != null)
                .Select(o => o.Driver.Id)
                .Union(allRatesExtended.Where(z => z.Driver != null)
                .Select(o => o.Driver.Id)).Distinct().ToList();

            List<RateItemCommissionModel> retval = new List<RateItemCommissionModel>();

            #region  grab all of the commissions specified at the company level

            foreach (var id in rateItemIds)
            {
                var rateBase = allRates.Where(o => (o?.RateItem?.Id ?? 0) == id && o.Driver == null).FirstOrDefault();

                string rate = null;
                if (rateBase != null)
                {
                    rate = rateBase.GetValue();
                }

                var extended = allRatesExtended.Where(o => (o?.RateItem?.Id ?? 0) == id && o.Driver == null).Select(t =>
                       new BodyTypeCommissionModel()
                       {
                           BodyTypeId = t.BodyType.Id,
                           Value = t.GetValue(),
                           CompanyId = t.CompanyId
                       });

                // only add the item IF there is a rate specified at rate level or bodyType. if both are null, dont add. 
                if (extended.Any() || rate != null)
                {
                    retval.Add(new RateItemCommissionModel()
                    {
                        RateItemId = id,
                        DriverId = rateBase == null ? null : rateBase.Driver != null ? (int?)rateBase.Driver.Id : null,
                        Rate = rate,
                        BodyTypes = extended.OrderBy(o => o.BodyTypeId)
                    });
                }
            }
            #endregion

            #region grab all of the commissions for each individual driver that has a commission set at either rate or bodyType level
            foreach (var lDriverId in driverIds)
            {
                foreach (var id in rateItemIds)
                {
                    var rateBase = allRates.Where(o => (o?.RateItem?.Id ?? 0) == id && o.Driver != null && o.Driver.Id == lDriverId).FirstOrDefault();

                    string rate = null;
                    if (rateBase != null)
                    {
                        rate = rateBase.GetValue();
                    }

                    var rateExtendedRates = allRatesExtended
                            .Where(o => (o?.RateItem?.Id ?? 0) == id && o.Driver != null && o.Driver.Id == lDriverId);

                    // only add the item IF there is a rate specified at rate level or bodyType. if both are null, dont add. 
                    if (rateExtendedRates.Any() || rate != null)
                    {
                        retval.Add(new RateItemCommissionModel()
                        {
                            RateItemId = id,
                            DriverId = lDriverId,
                            Rate = rate,
                            BodyTypes = rateExtendedRates.Where(w => w.DriverId == lDriverId).Select(t =>
                                new BodyTypeCommissionModel()
                                {
                                    BodyTypeId = t.BodyType.Id,
                                    Value = t.GetValue(),
                                    CompanyId = t.CompanyId,
                                }).OrderBy(o => o.BodyTypeId)
                        });
                    }
                }
            }
            #endregion

            return retval;
        }
    }

    public class BodyTypeCommissionModel
    {
        public int? CompanyId { get; set; }
        public int BodyTypeId { get; set; }
        public string Value { get; set; }
    }
}
