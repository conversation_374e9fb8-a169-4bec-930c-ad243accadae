using Extric.Towbook.Stickering;
using System;

namespace Extric.Towbook.API.Stickering.Models
{
    public sealed class NoteModel
    {
        public int Id { get; set; }
        public int StickerId { get; set; }
        public int UserId { get; set; }
        public DateTime CreateDate { get; set; }
        public string Message { get; set; }
        public string UserFullName { get; set; }
        public bool ShowDelete { get; set; }

        public static NoteModel Map(Note d)
        {
            var r = new NoteModel();

            r.Id = d.Id;
            r.StickerId = d.StickerId;
            r.UserId = d.UserId;
            r.CreateDate = d.CreateDate;
            r.Message = d.Message;
            r.UserFullName = d.UserFullName;

            return r;
        }

        public static Note Map(NoteModel model, Note original = null)
        {
            var r = new Note();

            if (original != null)
                r = original;

            r.StickerId = model.StickerId;
            r.UserId = model.UserId;
            r.CreateDate = model.CreateDate;
            
            if (model.Message != null)
                r.Message = model.Message;

            if (model.UserFullName != null)
                r.UserFullName = model.UserFullName;

            return r;
        }
    }
}