using Extric.Towbook.Integration;
using Extric.Towbook.Stickering;
using Extric.Towbook.Utility;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Dynamic;
using System.Linq;
using System.Threading.Tasks;

namespace Extric.Towbook.API.Stickering.Models
{
    public class StickerModel
    {
        public int Id { get; set; }
        public int? CompanyId { get; set; }
        public int? StickerNumber { get; set; }
        public string CustomNumber { get; set; }
        public int? CallId { get; set; }
        public int? CallNumber { get; set; }
        public int? AccountId { get; set; }


        /// <summary>
        /// Whether you need to provide a signature from the user when approving a sticker
        /// </summary>
        public bool RequireApprovalSignature { get; set; }

        /// <summary>
        /// Whether the sticker needed approval from a manager
        /// </summary>
        public bool RequiresManagerApproval { get; set; }


        /// <summary>
        /// Description of where the vehicle is located. Doesn't need to be address. This is to describe the parking
        /// location of where the vehicle is. 
        /// </summary>
        public string VehicleLocation { get; set; }
        public decimal? VehicleLatitude { get; set; }
        public decimal? VehicleLongitude { get; set; }

        public string Notes { get; set; }
        public int? ColorId { get; set; }
        public int? BodyTypeId { get; set; }
        public string Make { get; set; }
        public string Model { get; set; }
        public string VIN { get; set; }
        public int? ModelYear { get; set; }
        public string LicenseNumber { get; set; }
        public string LicenseState { get; set; }
        public DateTime CreateDate { get; set; } = DateTime.Now;
        public int? StatusId { get; set; }
        public string StatusName { get; set; }
        public string StatusColor { get; set; }
        public int[] Reasons { get; set; }

        /// <summary>
        ///  when was this authorized? 
        /// </summary>
        public DateTime? AuthorizationDate { get; set; }
        public int? AuthorizationUserId { get; set; }
        public int? AuthorizationUserSignatureId { get; set; }

        /// <summary>
        /// when can we tow it? you cannot tow a sticker before it's expiration time is reached.
        /// </summary>
        public DateTime? GracePeriodExpirationDate { get; set; }
        public DateTime? TowableDate { get; set; }
        public int? TowableProgressInPercent { get; set; }

        /// <summary>
        ///  when was this rejected? 
        /// </summary>
        public DateTime? RejectedDate { get; set; }
        public int? RejectedUserId { get; set; }

        /// <summary>
        ///  when was this resovled? 
        /// </summary>
        public DateTime? ResolvedDate { get; set; }
        public int? ResolvedUserId { get; set; }

        public int? GracePeriod { get; set; }

        public IEnumerable<NoteModel> StickerNotes { get; set; }

        public DateTime? ExpirationDate { get; set; }
        public int? ExpirationProgressInPercent { get; set; }

        public string[] AvailableActions { get; set; }

        public string PropertyGateCode { get; set; }

        public object Insights { get; set; }

        public static async Task<StickerModel> Map(User user, Sticker d)
        {
            return await Map(user, d, null);
        }

        public static async Task<StickerModel> Map(User user, Sticker d, IEnumerable<StickerInsightHelper.StickerInsightModel> insights)
        {
            if (d == null)
                return null;

            // Older stickers (prior to Dec 2018) have a NULL value for AuthorizationRequired. The 
            // Stickering background service is not updating sticker statuses for NULL values.
            if (true || d.AuthorizationRequired == null)
            {
                #region Check for Expired
                // check for expired sticker
                if (d.StatusId == StickerStatus.Waiting.Id ||
                    d.StatusId == StickerStatus.Approved.Id ||
                    d.StatusId == StickerStatus.Towable.Id ||
                    d.StatusId == StickerStatus.Converted.Id)
                {
                    if (Sticker.CheckIfExpired(d))
                    {
                        if (d.Reasons == null)
                            d.Reasons = Reason.GetByStickerId(d.Id).Select(s => s.Id).ToArray();

                        d.StatusId = StickerStatus.Expired.Id;
                        d.StatusName = StickerStatus.Expired.Name;
                        await d.AddStatusEvent(d.StatusId, 1);
                        await d.Save(false);
                    }
                }
                #endregion

                #region Check for end of grace period
                // check for end of wait (grace) period
                if (d.StatusId == StickerStatus.Waiting.Id || d.StatusId == StickerStatus.Approved.Id)
                {
                    if (Sticker.CheckIfApproved(d) && Sticker.CheckIfTowable(d))
                    {
                        // update sticker status
                        if (d.Reasons == null)
                            d.Reasons = Reason.GetByStickerId(d.Id).Select(s => s.Id).ToArray();

                        d.StatusId = StickerStatus.Towable.Id;
                        d.StatusName = StickerStatus.Towable.Name;
                        await d.AddStatusEvent(d.StatusId, 1);
                        await d.Save(false);
                    }
                }
                #endregion
            }

            var r = new StickerModel();

            r.Id = d.Id;
            r.CompanyId = d.CompanyId;
            r.AccountId = d.AccountId;
            r.StickerNumber = d.StickerNumber;
            r.CustomNumber = d.CustomNumber;
            r.Notes = d.Notes;
            r.ColorId = d.ColorId;
            r.BodyTypeId = d.BodyTypeId;
            r.Make = d.VehicleMake;
            r.Model = d.VehicleModel;
            r.VIN = d.VehicleVIN;
            r.ModelYear = d.VehicleYear;
            r.LicenseNumber = d.LicenseNumber;
            r.LicenseState = d.LicenseState;
            r.CreateDate = d.CreateDate;
            r.Reasons = d.Reasons;
            r.StatusId = d.StatusId;
            r.StatusName = d.StatusName;
            r.StatusColor = d.StatusColor;

            r.TowableDate = d.ExtendedExpirationDate != null ? d.ExtendedExpirationDate : d.GracePeriodExpirationDate; // soonest it can be towed
            r.GracePeriodExpirationDate = d.ExtendedExpirationDate != null ? d.ExtendedExpirationDate : d.GracePeriodExpirationDate; 
            r.AuthorizationDate = d.AuthorizationDate;
            r.AuthorizationUserId = d.AuthorizationUserId;
            r.AuthorizationUserSignatureId = d.AuthorizationUserSignatureId;
            r.RequiresManagerApproval = d.Setting?.TowManagerApprovalRequired ?? d.AuthorizationRequired.GetValueOrDefault() > 0;
            r.RejectedDate = d.RejectedDate;
            r.RejectedUserId = d.RejectedUserId;
            r.ResolvedDate = d.ResolvedDate;
            r.ResolvedUserId = d.ResolvedUserId;

            r.VehicleLocation = d.VehicleLocation;
            r.VehicleLatitude = d.VehicleLatitude;
            r.VehicleLongitude = d.VehicleLongitude;
            r.ExpirationDate = d.ExpirationDate;

            r.CallId = d.DispatchEntryId;
            r.CallNumber = d.CallNumber;

            r.AvailableActions = GetAvailableActions(user, d);

            r = AddInsightsToModel(user, r, insights);

            r = AddStatusProgressToModel(r);

            return r;
        }

        public static async Task<Collection<StickerModel>> Map(User user, Collection<Sticker> inputList)
        {
            if (inputList == null)
                return new Collection<StickerModel>();

            var o = new Collection<StickerModel>();

            // Ensure that AccountUsers cant see calls that don't belong to them
            if (user.AccountId > 0)
            {
                inputList = inputList.Where(r =>
                    r.AccountId == user.AccountId).ToCollection();
            }

            var insights = StickerInsightHelper.GetByStickers(inputList.Select(z => z.Id).ToArray());

            return (await Task.WhenAll(inputList.Select(async ro => await Map(user, ro, insights)))).ToCollection();

        }

        public static Sticker Map(StickerModel model, Sticker original = null)
        {
            Sticker r = new Sticker();

            if (original != null)
                r = original;

            if (model.StatusId != null)
                r.StatusId = model.StatusId.Value;

            if (model.CompanyId != null)
                r.CompanyId = model.CompanyId.Value;

            if (model.AccountId != null)
                r.AccountId = model.AccountId.Value;

            if (model.CallId != null)
                r.DispatchEntryId = model.CallId.Value;

            if (model.CustomNumber != null)
                r.CustomNumber = model.CustomNumber;

            if (model.CreateDate != null)
                r.CreateDate = model.CreateDate;

            if (model.StatusId != null)
                r.StatusId = model.StatusId.Value;

            if (model.ColorId != null)
                r.ColorId = model.ColorId.Value;

            if (model.BodyTypeId != null)
                r.BodyTypeId = model.BodyTypeId.Value;

            if (model.ModelYear != null)
                r.VehicleYear = model.ModelYear.Value;

            if (model.Notes != null)
                r.Notes = model.Notes;

            if (model.Reasons != null)
                r.Reasons = model.Reasons;

            if (model.StatusName != null)
                r.StatusName = model.StatusName;

            if (model.Model != null)
                r.VehicleModel = model.Model;

            if (model.Make != null)
                r.VehicleMake = model.Make;

            if (model.VIN != null)
                r.VehicleVIN = model.VIN;

            if (model.LicenseNumber != null)
                r.LicenseNumber = model.LicenseNumber;

            if (model.LicenseState != null)
                r.LicenseState = model.LicenseState;

            if (model.VehicleLocation != null)
                r.VehicleLocation = model.VehicleLocation;

            if (model.VehicleLatitude != null)
                r.VehicleLatitude = model.VehicleLatitude;

            if (model.VehicleLongitude != null)
                r.VehicleLongitude = model.VehicleLongitude;

            // ExpirationDate is determined server side and cannot be updated.
            // GracePeriodExpirationDate is determined server side and cannot be updated.
            // AuthorizationDate is determined server side and cannot be updated.
            // AuthorizationUserId is determined server side and cannot be updated.
            // ReslovedDate is determined server side and cannot be updated.
            // ResolvedUserId is determined server side and cannot be updated.
            // RejectedDate is determined server side and cannot be updated.
            // RejectedUserId is determined server side and cannot be updated.

            return r;
        }

        public class StickerInsightHelper
        {
            public class StickerInsightModel
            {
                public int StickerId { get; set; }
                public string Name { get; set; }
                public string Value { get; set; }
                public int ValueAsInt
                {
                    get
                    {
                        int n = 0;
                        int.TryParse(Value, out n);
                        return n;
                    }
                }
            }

            public static IEnumerable<StickerInsightModel> GetByStickers(int[] ids)
            {
                var results = SqlMapper.QuerySP<StickerInsightModel>("StickerInsightsGetByArray", 
                    new { StickerIds = String.Join(",", ids) });

                return results;
            }
        }

        public static StickerModel AddInsightsToModel(User user, StickerModel m, IEnumerable<dynamic> insights = null)
        {
            AddInsightsToModels(user, new[] { m }.ToCollection(), insights);

            return m;
        }

        public static Collection<StickerModel> AddInsightsToModels(User user, Collection<StickerModel> models, IEnumerable<dynamic> insights = null)
        {
            if (models == null)
                return new Collection<StickerModel>();

            // Ensure that AccountUsers cant see calls that don't belong to them
            if (user.AccountId > 0)
            {
                models = models.Where(r =>
                    r.AccountId == user.AccountId).ToCollection();
            }

            if (insights == null)
                insights = StickerInsightHelper.GetByStickers(models.Select(m => m.Id).ToArray());

            foreach (var m in models)
            {
                var outputRow = new ExpandoObject() as IDictionary<string, Object>;

                foreach (var row in insights.Where(z => z.StickerId == m.Id))
                {
                    if (outputRow.ContainsKey(row.Name))
                    {
                        var sc = outputRow[row.Name] as Collection<object>;
                        if (sc != null)
                            sc.Add(row.Value);
                        else
                        {
                            sc = new Collection<object>();
                            sc.Add(outputRow[row.Name]);
                            sc.Add(row.Value);
                        }
                    }
                    else
                    {
                        outputRow.Add(row.Name, row.Value);
                    }
                }

                m.Insights = outputRow;
            }

            return models;
        }

        internal static StickerModel AddStatusProgressToModel(StickerModel m)
        {
            if (m.TowableDate != null)
            {
                if (m.TowableDate.Value < DateTime.Now)
                {
                    m.TowableProgressInPercent = 100;
                }
                else
                {
                    var fullDiff = (TimeSpan)(m.TowableDate.Value - m.CreateDate);
                    var nowDiff = DateTime.Now - m.CreateDate;
                    if (fullDiff.Ticks > 0)
                    {
                        m.TowableProgressInPercent = (int)(nowDiff.TotalHours / fullDiff.TotalHours * 100);

                        if (m.TowableProgressInPercent > 100)
                            m.TowableProgressInPercent = 100;
                    }
                }
            }

            if (m.TowableDate != null && m.ExpirationDate != null)
            {
                if (m.ExpirationDate.Value < DateTime.Now)
                {
                    m.ExpirationProgressInPercent = 100;
                }
                else
                {
                    if (DateTime.Now >= m.TowableDate.Value)
                    {
                        var fullDiff = (TimeSpan)(m.ExpirationDate.Value - m.TowableDate.Value);
                        var nowDiff =  DateTime.Now - m.TowableDate.Value;
                        if (fullDiff.Ticks > 0)
                        {
                            m.ExpirationProgressInPercent = (int)(nowDiff.TotalHours / fullDiff.TotalHours * 100);

                            if (m.ExpirationProgressInPercent > 100)
                                m.ExpirationProgressInPercent = 100;
                        }
                    }
                    else
                    {
                        m.ExpirationProgressInPercent = 0;
                    }
                }
            }

            return m;
        }

        internal static string[] GetAvailableActions(User user, Sticker sticker)
        {
            var result = new Collection<string>();
            var ss = StickerSetting.GetByCompanyId(sticker.CompanyId, sticker.AccountId) ?? new StickerSetting();

            bool canModify = false;
            bool canApprove = false;
            bool canUnapprove = false;
            bool canReject = false;
            bool canUnreject = false;
            bool canExtend = false;
            bool canResolve = false;
            bool canUnresolve = false;
            bool canDelete = false;
            bool canCreateCall = false;

            // Account users / Property managers
            if (user.IsAccountTypeUser())
            {
                if (sticker.StatusId == StickerStatus.Waiting.Id)
                    canModify = canApprove = canReject = canExtend = canResolve = true;

                if (sticker.StatusId == StickerStatus.Approved.Id)
                    canModify = canUnapprove = canReject = canExtend = canResolve = true;

                if (sticker.StatusId == StickerStatus.Towable.Id)
                    canExtend = canResolve = true;

                if (sticker.StatusId == StickerStatus.Rejected.Id)
                    canUnreject = true;

                // setting overrides
                if (!ss.PropertyApprovalRequired)
                {
                    canUnapprove = false;
                    canApprove = false;
                }

                if (!ss.AllowExtensions)
                    canExtend = false;
            }

            // Drivers and Dispatchers
            if (user.Type == User.TypeEnum.Driver ||
                user.Type == User.TypeEnum.Dispatcher)
            {
                if (sticker.StatusId == StickerStatus.Waiting.Id)
                    canModify = canResolve = true;

                if (sticker.StatusId == StickerStatus.Approved.Id)
                    canModify = canResolve = true;

                if (sticker.StatusId == StickerStatus.Towable.Id)
                    canModify = canResolve = canCreateCall = true;

                if (sticker.StatusId == StickerStatus.Towed.Id)
                    canModify = true;
            }

            // Tow Managers
            if (user.Type == User.TypeEnum.Manager)
            {
                if (sticker.StatusId == StickerStatus.Waiting.Id)
                    canModify = canApprove = canExtend = canResolve = canDelete = true;

                if (sticker.StatusId == StickerStatus.Approved.Id)
                    canModify = canUnapprove = canExtend = canResolve = canDelete = true;

                if (sticker.StatusId == StickerStatus.Towable.Id)
                    canModify = canResolve = canDelete = canCreateCall = true;

                if (sticker.StatusId == StickerStatus.Towed.Id)
                    canModify = canDelete = true;

                if (sticker.StatusId == StickerStatus.Rejected.Id)
                    canModify = true;

                if (sticker.StatusId == StickerStatus.Resolved.Id)
                    canModify = canUnresolve = true;

                // setting overrides
                if (!ss.TowManagerApprovalRequired)
                {
                    canUnapprove = false;
                    canApprove = false;
                }

                if (!ss.AllowExtensions)
                    canExtend = false;
            }

            // approval overrides / digital signatures
            bool mustApproveWithSignature = false;
            if (canApprove)
            {
                if (ss.RequireApprovalSignature)
                    mustApproveWithSignature = true;

                if (user.Company.HasFeature(Generated.Features.UserSignatures)) {
                    // check for saved signature for approvals
                    var us = UserSignature.GetByUserId(user.Id);
                    if (us != null)
                    {
                        var st = Dispatch.SignatureType.GetByName(user.CompanyId, "Tow Authorization");
                        if (st != null)
                        {
                            var usa = UserSignatureAgreement.GetBySignatureType(user.Id, st.SignatureTypeId);
                            if (usa != null)
                            {
                                // found consent agreement for "Tow Authorization".  No signature capture required
                                mustApproveWithSignature = false;
                            }
                        }
                    }
                }

            }


            if (canModify)
                result.Add("MODIFY");
            if (canApprove) {
                if(mustApproveWithSignature)
                    result.Add("APPROVE_WITH_SIGNATURE");
                else
                    result.Add("APPROVE");
            }
            if (canUnapprove)
                result.Add("UNAPPROVE");
            if (canReject)
                result.Add("REJECT");
            if (canUnreject)
                result.Add("UNREJECT");
            if (canExtend)
                result.Add("EXTEND");
            if (canResolve)
                result.Add("RESOLVE");
            if (canUnresolve)
                result.Add("UNRESOLVE");
            if (canDelete)
                result.Add("DELETE");
            if (canCreateCall)
                result.Add("CREATE_CALL");
            
            return result.ToArray();
        }
    }
}
