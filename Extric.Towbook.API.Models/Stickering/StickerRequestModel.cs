using Extric.Towbook.Stickering;
using System;

namespace Extric.Towbook.API.Models.Stickering
{
    public class StickerRequestModel
    {
        public int Id { get; set; }
        public int? AccountId { get; set; }
        public int? StatusId { get; set; }
        public DateTime CreateDate { get; set; }
        public DateTime? DueDate { get; set; }
        public int? OwnerUserId { get; set; }

        public static StickerRequestModel Map(StickerRequest o)
        {
            return new StickerRequestModel()
            {
                CreateDate = o.CreateDate,
                AccountId = o.AccountId,
                DueDate = o.DueDate,
                Id = o.Id,
                StatusId = o.StatusId,
                OwnerUserId = o.OwnerUserId
            };
        }

        public static StickerRequest Map(StickerRequestModel o, User currentUser, StickerRequest original = null)
        {

            if (original == null)
                original = new StickerRequest() { OwnerUserId = currentUser.Id };

            if (o.AccountId != null)
                original.AccountId = o.AccountId.Value;

            if (o.DueDate != null)
                original.DueDate = o.DueDate.Value;

            if (o.StatusId != null)
                original.StatusId = o.StatusId.Value;

            return original;

        }
    }
}