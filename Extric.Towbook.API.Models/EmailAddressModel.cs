using System;
using Extric.Towbook.Integrations.Email;

namespace Extric.Towbook.API.Models
{
    public class EmailAddressModel
    {
        public int Id { get; set; }
        public int CompanyId { get; set; }
        public string Address { get; set; }
        public DateTime CreateDate { get; set; }
        public int OwnerUserId { get; set; }
        public bool Deleted { get; set; }
        public int DomainId { get; set; }

        public static EmailAddressModel Map(EmailAddress s)
        {
            var r = new EmailAddressModel();

            r.Id = s.Id;
            r.CreateDate = s.CreateDate;
            r.Deleted = s.Deleted;
            r.DomainId = s.DomainId;
            r.OwnerUserId = s.OwnerUserId;
            r.Address = s.Address;
            r.CompanyId = s.CompanyId;

            return r;
        }

        public static EmailAddress Map(EmailAddressModel model, EmailAddress original = null)
        {
            EmailAddress s = new EmailAddress();

            if (original != null)
                s = original;

            s.CreateDate = model.CreateDate;
            s.Deleted = model.Deleted;
            s.CompanyId = model.CompanyId;
            s.DomainId = model.DomainId;

            if (model.Address != null)
                s.Address = model.Address;

            s.OwnerUserId = model.OwnerUserId;

            return s;
        }
    }
}
