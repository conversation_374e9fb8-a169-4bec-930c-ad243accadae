using Extric.Towbook.Accounts;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Extric.Towbook.API.Models
{
    public class ParkingPermitSettingModel
    {
        public int Id { get; set; }
        public int CompanyId { get; set; }
        public int? AccountId { get; set; }
        public int PermitsPerResident { get; set; } = 1;
        public int GuestPermitsAllowed { get; set; } = 0;
        public int? GuestExpirationDays { get; set; }
        public PermitExpirationType ExpirationType { get; set; } = 0;
        public DateTime? ExpirationDate { get; set; }

        // Mandatory required fields
        public bool RequireYear = true;
        public bool RequireMake = true;
        public bool RequireModel = true;
        public bool RequireLicensePlate = true;
        public bool RequireLicensePlateState = true;
        public bool RequireName = true;
        public bool RequirePhone = true;

        // Optional required fields
        public bool RequireColor { get; set; }
        public bool RequireVin { get; set; }
        public bool RequireVehicleRegistration { get; set; }
        public bool RequireVehicleRegistrationExpiration { get; set; }
        public bool RequireEmail { get; set; }
        public bool RequireAddress { get; set; }

        public int? TaxRateId { get; set; }

        public string Disclaimer { get; set; }

        public ParkingPermitPublicLinkModel PublicLink { get; set; }


        public ParkingPermitSettingModel()
        {
            Id = 0;
        }

        public static ParkingPermitSettingModel Map(ParkingPermitSetting s)
        {
            var model = new ParkingPermitSettingModel();

            if (s == null)
                return model;

            model.Id = s.Id;
            model.CompanyId = s.CompanyId;
            model.AccountId = s.AccountId;
            model.PermitsPerResident = s.PermitsPerResident;
            model.GuestPermitsAllowed = s.GuestPermitsPerResident;
            model.GuestExpirationDays = s.GuestExpirationDays;
            model.RequireColor = s.RequireColor;
            model.ExpirationType = s.ExpirationType;
            model.RequireVin = s.RequireVin;
            model.RequireEmail = s.RequireEmail;
            model.RequireVehicleRegistration = s.RequireVehicleRegistration;
            model.RequireVehicleRegistrationExpiration = s.RequireVehicleRegistrationExpiration;
            model.TaxRateId = s.TaxRateId;
            model.RequireAddress = s.RequireAddress;

            var d = ParkingPermitDisclaimer.GetByCompanyId(s.CompanyId, s.AccountId, null);
            if (d != null)
                model.Disclaimer = d.Content;

            return model;
        }

        public static ParkingPermitSetting Map(ParkingPermitSettingModel model, ParkingPermitSetting s = null)
        {
            if (s == null)
                s = new ParkingPermitSetting();

            if (model.Id > 0)
                s.Id = model.Id;

            if (model.AccountId != null)
                s.AccountId = model.AccountId;

            s.CompanyId = model.CompanyId;
            s.RequireColor = model.RequireColor;
            s.RequireVin = model.RequireVin;
            s.RequireEmail = model.RequireEmail;
            s.RequireVehicleRegistration = model.RequireVehicleRegistration;
            s.RequireVehicleRegistrationExpiration = model.RequireVehicleRegistrationExpiration;
            s.ExpirationType = model.ExpirationType;
            s.GuestExpirationDays = model.GuestExpirationDays;
            s.TaxRateId = model.TaxRateId;
            s.RequireAddress = model.RequireAddress;

            return s;
        }

    }
}