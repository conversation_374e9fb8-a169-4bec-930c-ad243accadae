namespace Extric.Towbook.API.Models
{
    public class AddressMinimalModel
    {
        public string Address { get; set; }
        public string City { get; set; }
        public string State { get; set; }
        public string Zip { get; set; }
        public decimal? Latitude { get; set; }
        public decimal? Longitude { get; set; }

        public override string ToString()
        {
            return (Address +
                (!string.IsNullOrWhiteSpace(Address) ? ", " : "") +
                City + " " +
                State + " " +
                Zip).Trim().Trim(',');
        }
    }
}