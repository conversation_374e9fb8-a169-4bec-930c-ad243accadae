using Extric.Towbook.Auctions;
using Extric.Towbook.Impounds;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Extric.Towbook.API.Models.Auctions
{
    public class AuctionModel
    {
        public int Id { get; set; }
        public int CompanyId { get; set; }
        public string Name { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public AuctionLocationModel Location { get; set; }
        public IEnumerable<AuctionItemModel> Items { get; set; }
        public int ItemCount {get;set;}
        public int PhotoCount { get; set; }
        public string Description { get; set; }

        public string RemoteId { get; set; }
        public string RemoteSystem { get; set; }

        public static AuctionModel Map(
            Company.Company company,
            Auction auction,
            Lot lot = null)
        {
            if(auction == null || company == null)
                return null;

            var model = new AuctionModel();

            model.Id = auction.Id;
            model.CompanyId = auction.CompanyId;
            model.StartDate = auction.StartDate;
            model.EndDate = auction.EndDate;
            model.Location = AuctionLocationModel.Map(auction, lot);

            model.ItemCount = auction.ItemCount;
            model.Description = auction.Description;
            model.Name = GetAuctionDefaultName(company, auction, lot);

            model.RemoteId = auction.RemoteId;
            model.RemoteSystem = auction.RemoteSystem;
            return model;
        }

        public static async Task<AuctionModel> MapAsync(
            Company.Company company,
            Auction auction,
            Lot lot = null)
        {
            if(auction == null || company == null)
                return null;

            var model = new AuctionModel();

            model.Id = auction.Id;
            model.CompanyId = auction.CompanyId;
            model.StartDate = auction.StartDate;
            model.EndDate = auction.EndDate;
            model.Location = await AuctionLocationModel.MapAsync(auction, lot);

            model.ItemCount = auction.ItemCount;
            model.Description = auction.Description;
            model.Name = GetAuctionDefaultName(company, auction, lot);

            model.RemoteId = auction.RemoteId;
            model.RemoteSystem = auction.RemoteSystem;
            return model;
        }

        private static string GetAuctionDefaultName(Company.Company c, Auction auction, Lot lot)
        {
            if (!string.IsNullOrEmpty(auction?.Name))
                return auction.Name;

            string ret = "";

            if (auction.StartDate != null)
                ret += Core.OffsetDateTime(c, auction.StartDate.Value).ToShortDateString() + " - ";

            if (lot != null)
                ret += lot.Name;
            else
                ret += "Auction #" + auction.Id.ToString();

            return ret;
        }
        
    }
}
