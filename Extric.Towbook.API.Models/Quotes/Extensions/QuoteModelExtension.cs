using Extric.Towbook.API.Models.Calls;
using Extric.Towbook.Dispatch;
using Extric.Towbook.Dispatch.CallModels;
using Extric.Towbook.Dispatch.QuoteModels;
using Extric.Towbook.Generated;
using Extric.Towbook.Integration;
using Extric.Towbook.Utility;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;

namespace Extric.Towbook.API.Models.Quotes
{
    public static class QuoteModelExtension
    {
        public static async Task<Collection<QuoteModel>> MapAsync(Collection<QuoteModel> inputList)
        {
            var o = new Collection<QuoteModel>();

            if (inputList == null)
                return o;

            foreach (var x in inputList)
            {
                o.Add(await MapAsync(x));
            }

            return o;
        }

        public static async Task<QuoteModel> MapAsync(QuoteModel x)
        {
            // add available actions based on current user context
            x = GetAvailableActions(x);

            await Calls.CallModelExtensions.CheckPreventByUserTypeKeyValueAsync(x.Call, "PreventDriversFromViewingNotes", "1", (o) =>
            {
                o.Notes = null;
            });

            await CheckPreventDriverKeyValueAsync(x, "HideAccountDetailsFromDrivers", "1", (o) => {
                if (o.Call.Account != null)
                {
                    o.Call.Account.Address = "";
                    o.Call.Account.City = "";
                    o.Call.Account.Email = "";
                    o.Call.Account.Phone = "";
                    o.Call.Account.State = "";
                    o.Call.Account.Zip = "";
                }
            });

            await CheckPreventDriverKeyValueAsync(x, "HideAccountDetailsFromDrivers", "2", (o) => {
                if (o.Call.Account != null)
                {
                    o.Call.Account.Address = "";
                    o.Call.Account.City = "";
                    o.Call.Account.Email = "";
                    o.Call.Account.Phone = "";
                    o.Call.Account.State = "";
                    o.Call.Account.Zip = "";

                    // show the Type as the Name.
                    o.Call.Account.Company = ((Towbook.Accounts.AccountType)o.Call.Account.TypeId).ToString();
                }
            });

            await CheckPreventDriverKeyValueAsync(x, "HideAccountDetailsFromDrivers", "3", (o) => {
                if (o.Call.Account != null)
                {
                    o.Call.Account.Address = "";
                    o.Call.Account.City = "";
                    o.Call.Account.Email = "";
                    o.Call.Account.Phone = "";
                    o.Call.Account.State = "";
                    o.Call.Account.Zip = "";
                    o.Call.Account.Company = "(Hidden)";
                }
            });

            await CheckPreventDriverKeyValueAsync(x, "HideAccountDetailsFromDrivers", "4", (o) =>
            {
                if (x.Call.Account != null)
                {
                    o.Call.Attributes = o.Call.Attributes.Concat(new Collection<CallAttributeValueModel>()
                    {
                        new CallAttributeValueModel()
                        {
                             Id = -56,
                             AttributeId = AttributeValue.BUILTIN_DISPATCH_ACCOUNT_NAME_READONLY,
                             Value = x.Call.Account.Company
                        }

                    }).ToArray();

                    o.Call.Account.Company = "(Not Available)";
                }
            });

            await Calls.CallModelExtensions.CheckPreventByUserTypeKeyValueAsync(x.Call, "PreventDriversFromViewingPONumber", "1", (o) =>
            {
                o.PurchaseOrderNumber = null;
                o.Attributes = o.Attributes?.Where(ro => ro.AttributeId != AttributeValue.BUILTIN_ACCOUNT_PURCHASEORDER).ToArray();
            });

            await CheckPreventDriverKeyValueAsync(x, "PreventDriversFromCreatingCalls", "1", (o) =>
            {
                o.AvailableActions = o.AvailableActions.Where(w => w != "CONVERT").ToArray();
            });

            await CheckPreventDriverKeyValueAsync(x, "PreventDriversFromViewingQuotes", "1", (o) =>
            {
                o.AvailableActions = o.AvailableActions.Where(w => w != "VIEW").ToArray();
            });

            await CheckPreventDriverKeyValueAsync(x, "PreventDriversFromEmailingCalls", "1", (o) =>
            {
                o.AvailableActions = o.AvailableActions.Where(w => w != "EMAIL").ToArray();
            });

            if (x.Call != null)
            {
                x.Call.BalanceByClass = x.Call.BalanceByClass ?? Array.Empty<ClassBalanceModel>();

                x.Call = await Calls.CallModelExtensions.BlockInvoicePricingWhenApplicableAsync(x.Call);
                x.Call = await Calls.CallModelExtensions.BlockContactDetailsAsync(x.Call);

                if (x.Call.ArrivalETA?.Date == DateTime.MinValue.Date)
                    x.Call.ArrivalETA = null;

                if (string.IsNullOrEmpty(x.Call.TowSource))
                    x.Call.TowSource = x.Call.Waypoints?.FirstOrDefault(f => f.Title == "Pickup")?.Address;
                if (string.IsNullOrEmpty(x.Call.TowDestination))
                    x.Call.TowDestination = x.Call.Waypoints?.FirstOrDefault(f => f.Title == "Destination")?.Address;
            }

            if (x.Call?.InvoiceItems != null)
            {
                var uniqueId = -1000;
                foreach (var ii in x.Call.InvoiceItems)
                {
                    var i = await CallInvoiceItemModel.MapAsync(ii, await InvoiceItem.GetByIdAsync(uniqueId++));

                    // set taxable by rateItem
                    if (i.RateItem != null)
                    {
                        if (!x.Call.InvoiceTaxExempt.GetValueOrDefault())
                        {
                            if (i.RateItem.RateItemId == RateItem.BUILTIN_FUEL_SURCHARGE)
                            {
                                var s = await Surcharges.SurchargeRate.GetBySurchargeAsync(Surcharges.Surcharge.SURCHARGE_FUEL, x.CompanyId);
                                if (s != null)
                                {
                                    if (s.Taxable)
                                        i.Taxable = true;
                                }
                            }
                            else
                            {
                                i.Taxable = i.RateItem.Taxable;
                            }
                        }
                    }
                }
            }

            return x;
        }

        internal static QuoteModel GetAvailableActions(this QuoteModel x)
        {
            var CurrentUser = (User)Web.HttpContextFactory.Instance.CurrentUser;
            var actions = new string[] { "CONVERT", "VIEW", "EMAIL" }.ToList();
            if (CurrentUser.Type == User.TypeEnum.Manager ||
                CurrentUser.Type == User.TypeEnum.SystemAdministrator ||
                x.Owner?.Id == CurrentUser.Id)
            {
                actions.Add("DELETE");
                actions.Add("MODIFY");
            }

            if (CurrentUser.Type == User.TypeEnum.Dispatcher)
            {
                actions.Add("MODIFY");
            }

            if (CurrentUser.Type == User.TypeEnum.Manager ||
                CurrentUser.Type == User.TypeEnum.SystemAdministrator ||
                CurrentUser.Type == User.TypeEnum.Accountant ||
                CurrentUser.Type == User.TypeEnum.Dispatcher)
            {
                actions.Add("CREATE");
                actions.Add("DUPLICATE");
            }

            x.AvailableActions = actions.Distinct().OrderBy(a => a).ToArray();

            return x;
        }

        internal static async Task<QuoteModel> CheckPreventDriverKeyValueAsync(QuoteModel quote, string keyName, string trueValue, Action<QuoteModel> whenTrue)
        {
            var CurrentUser = (User)Web.HttpContextFactory.Instance.CurrentUser;
            if (quote == null || quote.Call == null || keyName == null || trueValue == null || whenTrue == null)
                return quote;

            if (CurrentUser?.Type == User.TypeEnum.Driver)
            {
                var kv = (await CompanyKeyValue.GetByCompanyIdAsync(quote.CompanyId,
                    Provider.Towbook.ProviderId, keyName)).FirstOrDefault();

                if (kv?.Value == trueValue)
                {
                    whenTrue(quote);
                    return quote;
                }
            }

            return quote;
        }


        public static async Task<Entry> InternalApplyFreeQuantities(Entry e)
        {
            #region free miles

            var bucket = new List<InvoiceItem>();

            foreach (var itemToHandle in
                        e.InvoiceItems.Where(o => o.RateItem?.ParentRateItemId == 0 &&
                            (o.CustomName == null || !o.CustomName.Contains("FreeQuantity")) &&
                            o.RelatedInvoiceItemId == null))
            {
                decimal freeQuantity = itemToHandle.RateItem.FreeQuantity;

                if (e.Account != null)
                {
                    var ari = await Extric.Towbook.Accounts.RateItem.GetByRateItemAsync(e.Account, itemToHandle.RateItem);
                    if (ari != null)
                    {
                        if (ari.FreeQuantity > 0)
                            freeQuantity = ari.FreeQuantity;
                    }
                }

                var allFreeItems = e.InvoiceItems.Where(o =>
                    o.RelatedInvoiceItemId == itemToHandle.Id &&
                    (o.RateItem != null && o.RateItem.ParentRateItemId == 0)
                ).ToCollection();

                var freeItem = allFreeItems.FirstOrDefault();

                if (freeItem == null)
                {
                    if (freeQuantity > 0)
                    {
                        /* Don't add free credit for time based rate items (free minutes has been calculated into the quantity for rounding purposes) */
                        if (itemToHandle.RateItem?.TimeRound > 0)
                            continue;

                        freeItem = new InvoiceItem()
                        {
                            RelatedInvoiceItemId = itemToHandle.Id,
                            InvoiceId = 0,
                            CustomName = "FreeQuantity Credit " + itemToHandle.RateItem.RateItemId,
                            RateItem = itemToHandle.RateItem,
                            AssetId = itemToHandle.AssetId,
                            ClassId = itemToHandle.ClassId
                        };
                    }
                    else
                    {
                        continue;
                    }
                }

                freeItem.CustomPrice = -itemToHandle.Price;
                // free=5, item=1
                if (freeQuantity > itemToHandle.Quantity)
                    freeItem.Quantity = itemToHandle.Quantity;
                else
                    freeItem.Quantity = freeQuantity;

                freeItem.Taxable = itemToHandle.Taxable;


                // add or update free invoice item
                if (freeItem != null)
                {
                    var fi = e.InvoiceItems.FirstOrDefault(f => f.Id == freeItem.Id);
                    if (fi == null)
                        bucket.Add(freeItem);
                }
            }

            foreach (var b in bucket)
                e.InvoiceItems.Add(b);
            #endregion


            #region time based free qty
            if (await e.Company.HasFeatureAsync(Features.TimeBasedRates) && e.InvoiceItems != null)
            {
                // consider invoice items that have rate items that are time based and have a quantity of zero (not valid)
                foreach (var ii in e.InvoiceItems.Where(w => w.RateItem != null &&
                                        w.RateItem.IsTimeBasedItem() &&
                                        w.Quantity == 0)
                                        .ToCollection())
                {
                    RateItem ri = ii.RateItem as RateItem;

                    // only consider completion end times
                    if (ri == null || ri.TimeStopAtStatusId != Status.Completed.Id)
                        continue;

                    // consider account free quantity
                    var ari = await Towbook.Accounts.RateItem.GetByRateItemAsync(e.Invoice.AccountId ?? e.AccountId, ii.RateItem.RateItemId);
                    if (ari != null && ari.FreeQuantity > 0)
                        ri.FreeQuantity = ari.FreeQuantity;

                    // get qty based on time based settings for rate item
                    var qty = ri.GetTimeBasedQuantity(e);
                    if (qty != null)
                    {
                        // update quantity for save to entry/invoice/invoiceitems
                        ii.Quantity = qty.Value;
                    }

                    // apply quantity, if zero
                    if (ii.Quantity == 0)
                    {
                        ii.Quantity = RateItem.GetTimedBasedTransformedCost(ii.RateItem as RateItem, e);
                    }
                }
            }
            #endregion

            return e;
        }

    }

    public class QuoteEventJson
    {
        public int QuoteNumber { get; set; }
        public string QuoteName { get; set; }
        public DateTime ConvertDate { get; set; }
        public int? PerformerId { get; set; }
        public string Performer { get; set; }

    }
}
