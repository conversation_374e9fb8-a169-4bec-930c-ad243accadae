using System;
using System.Collections.Generic;
using System.Linq;
using System.Collections.ObjectModel;
using Extric.Towbook.Accounts;

namespace Extric.Towbook.API.Models
{
    public class AccountRateItemModel
    {
        /// <summary>
        /// RateItemId
        /// </summary>
        public int Id { get; set; }

        public string CompanyName { get; set; }
        public string AccountName { get; set; }
        public int AccountCompanyId { get; set; }
        public int AccountId { get; set; }
        public decimal Value { get; set; }

        public decimal? FreeQuantity { get; set; }

        public AccountRateItemExtendedModel[] ExtendedItems { get; set; }



        public static AccountRateItemModel[] TranslateDomainToModel(Collection<Extric.Towbook.Accounts.RateItem> collection)
        {
            Collection<AccountRateItemModel> list = new Collection<AccountRateItemModel>();

            foreach (var x in collection)
            {
                list.Add(AccountRateItemModel.TranslateDomainToModel(x));
            }

            return list.ToArray();
        }

        public static AccountRateItemModel TranslateDomainToModel(Extric.Towbook.Accounts.RateItem input)
        {
            if (input == null)
                return null;

            AccountRateItemModel output = new AccountRateItemModel();

            output.AccountId = input.AccountId;
            output.Id = input.Id;
            output.Value = input.Cost;
            output.FreeQuantity = input.FreeQuantity;

            output.ExtendedItems = AccountRateItemExtendedModel.TranslateDomainToModel(input.ExtendedRateItems);

            var x = Extric.Towbook.Accounts.Account.GetById(input.AccountId);

            output.AccountName = x.Company; // performance hog... 
            output.CompanyName = Company.Company.GetById(x.CompanyId).Name;
            output.AccountCompanyId = x.CompanyId;

            return output;
        }

        public static Extric.Towbook.Accounts.RateItem TranslateModelToDomain(AccountRateItemModel model, Towbook.Accounts.RateItem ri)
        {
            if (model.FreeQuantity != null)
                ri.FreeQuantity = model.FreeQuantity.GetValueOrDefault();

            ri.Cost = model.Value;

            foreach (var e in model.ExtendedItems)
            {
                if (!ri.ExtendedRateItems.ContainsKey(e.BodyType))
                    ri.ExtendedRateItems.Add(e.BodyType, new Extric.Towbook.Accounts.ExtendedRateItem() { BodyTypeId = e.BodyType, AccountId = model.AccountId, RateItemId = model.Id });

                ri.ExtendedRateItems[e.BodyType].Amount = (e.Value != null ? e.Value.Value : 0);
            }

            return ri;
        }

        public static IEnumerable<InternalGetForAccountObject> InternalGetForAccount(int accountId, User currentUser)
        {
            return InternalGetForAccount(new Account[] { Account.GetById(accountId) }, currentUser);
        }

        /// <summary>
        /// Returns a list of rates with Cost, FreeQuantity, Discount, DiscountExempt, and ExtendedRates, for 
        /// </summary>
        /// <param name="accounts"></param>
        /// <param name="currentUser"></param>
        /// <returns></returns>
        public static IEnumerable<InternalGetForAccountObject> InternalGetForAccount(IEnumerable<Account> accounts, User currentUser)
        {
            bool blockCharges = InternalGetForAccountObject.ShouldBlockCharges(currentUser);

            return Extric.Towbook.Accounts.RateItem.GetByAccount(accounts, true, false)
                .Select(o => InternalGetForAccountObject.Map(o, currentUser))
                .Where(o => !blockCharges || o.Cost > 0);
        }

    }

    public class AccountRateItemUpdateModel : AccountRateItemModel
    {
        public bool Delete { get; set; }
        public int AccountRateItemId { get; set; }
    }
}
