using System;
using System.Collections.Generic;
using System.Text;

namespace Extric.Towbook.API.PreTripInspections.Models
{
    public class PreTripInspectionItemTypeDetailModel
    {
        public string Name { get; set; }
        public int? DetailId { get; set; }
        public int? TruckTypeId { get; set; }
        public int? QuestionCount { get; set; }
    }

    

    public class PreTripInspectionReportsModel
    {
        public PreTripInspectionItemTypeDetailModel[] Inspections { get; set; } = new PreTripInspectionItemTypeDetailModel[0];
    }
}
