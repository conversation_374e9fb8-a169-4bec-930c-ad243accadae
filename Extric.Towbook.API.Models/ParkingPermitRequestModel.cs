using Extric.Towbook.Accounts;
using System;

namespace Extric.Towbook.API.Models
{
    public class ParkingPermitRequestModel
    {
        public int Id { get; set; }
        public int AccountId { get; set; }
        public int? UserId { get; set; }
        public int? UserTypeId { get; set; }
        public string CustomPermitNumber { get; set; }
        public string UnitNumber { get; set; }

        public DateTime RequestDate { get; set; }
        public DateTime? LastActivity { get; set; }

        public string CellPhone { get; set; }
        public string Email { get; set; }
        public string FullName { get; set; }

        public string Url { get; set; }
        public string Code { get; set; }

        
        public static ParkingPermitRequestModel Map(ParkingPermitRequest d)
        {
            var p = new ParkingPermitRequestModel();

            p.Id = d.Id;
            p.AccountId = d.AccountId;
            p.UserId = d.UserId;
            p.FullName = d.FullName;
            p.CellPhone = d.CellPhone;
            p.Email = d.Email;
            p.UnitNumber = d.UnitNumber;
            p.RequestDate = d.CreateDate;
            p.LastActivity = d.LastActivity;
            p.CustomPermitNumber = d.CustomPermitNumber;
            p.Url = d.Url;
            p.Code = d.Code;
            p.UserTypeId = d.UserTypeId;

            return p;
        }

        public static ParkingPermitRequest Map(ParkingPermitRequestModel model, User currentUser, ParkingPermitRequest original = null)
        {
            ParkingPermitRequest pp = new ParkingPermitRequest();

            if (original != null)
                pp = original;

            if (model.FullName != null)
                pp.FullName = model.FullName;

            if (model.CellPhone != null)
                pp.CellPhone = Core.FormatPhone(model.CellPhone, true);

            if (model.Email != null)
                pp.Email = model.Email;

            if (model.UnitNumber != null)
                pp.UnitNumber = model.UnitNumber;

            if (model.CustomPermitNumber != null)
                pp.CustomPermitNumber = model.CustomPermitNumber;

            pp.OwnerUserId = currentUser.Id;
            pp.AccountId = model.AccountId;

            if (model.UserId != null)
                pp.UserId = model.UserId;

            if (model.UserTypeId != null)
                pp.UserTypeId = model.UserTypeId;

            return pp;
        }
    }
}
