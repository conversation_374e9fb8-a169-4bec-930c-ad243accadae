using Extric.Towbook.Accounts;
using System;

namespace Extric.Towbook.API.Accounts.Models
{
    public class ParkingPermitPhotoModel
    {
        public int Id { get; set; }
        public int ParkingPermitId { get; set; }
        public string ContentType { get; set; }
        public string Description { get; set; }
        public DateTime CreateDate { get; set; }
        public string RemoteIp { get; set; }
        public int OwnerUserId { get; set; }
        public bool Deleted { get; set; }

        public static ParkingPermitPhotoModel Map(ParkingPermitPhoto d)
        {
            var p = new ParkingPermitPhotoModel();

            p.Id = d.Id;
            p.ParkingPermitId = d.ParkingPermitId;
            p.ContentType = d.ContentType;
            p.CreateDate = d.CreateDate;
            p.Description = d.Description;
            p.OwnerUserId = d.OwnerUserId;
            p.RemoteIp = d.RemoteIp;

            return p;
        }

        public static ParkingPermitPhoto Map(ParkingPermitPhotoModel model, ParkingPermitPhoto original = null)
        {
            ParkingPermitPhoto p = new ParkingPermitPhoto();

            if (original != null)
                p = original;

            p.ParkingPermitId = model.ParkingPermitId;
            p.CreateDate = model.CreateDate;
            p.OwnerUserId = model.OwnerUserId;

            if (model.Description != null)
                p.Description = model.Description;

            if (model.RemoteIp != null)
                p.RemoteIp = model.RemoteIp;

            if (model.ContentType != null)
                p.ContentType = model.ContentType;

            return p;
        }

    }
}
