using System;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Threading.Tasks;
using System.Web;
using Extric.Towbook.SSO;
using NLog;

namespace Extric.Towbook.WebShared.SSO
{
    public class OIDCCallback : SSOCallbackBase
    {
        private static readonly Logger logger = LogManager.GetCurrentClassLogger();

        private User _ssoUser;
        private bool _ssoEnable;
        private string _idTokenHint;
        private long _expiration;
        private DateTimeOffset _SecurityTokenValidTo;
        private SSOTokens _ssoTokens;
        private SSORelayState _relayState;

        protected HttpRequest _request;

        public override SSOTokens SsoTokens => _ssoTokens;

        public override bool IsMobileRequest => RelayState.IsMobileDevice;

        private SSORelayState RelayState
        {
            get
            {
                if (_relayState is null)
                {
                    var stateValue = _request.Form["state"];
                    _relayState = SSORelayState.Create(stateValue);
                }
                return _relayState;
            }
        }

        public override string SSO_TYPE => "OIDC";

        public OIDCCallback(HttpRequest request)
        {
            _request = request;
            _guid = Guid.NewGuid();
        }

        public override async Task<User> GetUserAsync()
        {
            if (_ssoUser != null)
            {
                return _ssoUser;
            }

            if (_request.Form != null && _request.Form["id_token"] != null)
            {
                var idToken = _request.Form["id_token"];
                var code = _request.Form["code"];

                var handler = new JwtSecurityTokenHandler();

                JwtSecurityToken jwtSecurityToken = handler.ReadJwtToken(idToken);
                var username = FindValue(jwtSecurityToken, "preferred_username");
                var email = FindValue(jwtSecurityToken, "email") ?? username;
                var nonce = FindValue(jwtSecurityToken, "nonce");
                var rvNonce = Core.GetRedisValue(nonce);
                if (string.IsNullOrEmpty(username) || string.IsNullOrEmpty(rvNonce) || rvNonce.ToLowerInvariant() != username.ToLowerInvariant())
                {
                    logger.Warn("The nonce value does not match the value of the request.");
                    return null;
                }
                var firstName = FindValue(jwtSecurityToken, "given_name");
                var lastName = FindValue(jwtSecurityToken, "family_name");
                var fullName = FindValue(jwtSecurityToken, "name") ??
                    (string.IsNullOrEmpty(firstName) || string.IsNullOrEmpty(lastName) ? username : firstName + " " + lastName);

                var exp = FindValue(jwtSecurityToken, "exp");
                _expiration = string.IsNullOrEmpty(exp) ? 0 : long.Parse(exp);

                var ssoConfigs = await CompanySecuritySetting.GetEnabledSsoByType(SSO_TYPE);
                foreach (var companySecuritySetting in ssoConfigs)
                {
                    try
                    {
                        var domainCode = CompanySecuritySetting.GetDomain(username);
                        if (companySecuritySetting.CompanyDomain.Equals(domainCode))
                        {
                            DateTime dateTime = new DateTime(1970, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc);
                            _SecurityTokenValidTo = dateTime.AddSeconds(_expiration).ToLocalTime();
                            if (companySecuritySetting != null && companySecuritySetting.SsoEnable)
                            {
                                _ssoEnable = companySecuritySetting.SsoEnable;
                                _ssoUser = await CheckAndGetUserAsync(username, email, fullName, companySecuritySetting.CompanyId, companySecuritySetting.DefaultUserType);
                                _idTokenHint = companySecuritySetting.SsoProvider.Name.ToUpper().Contains("OKTA") ? idToken : FindValue(jwtSecurityToken, "login_hint");
                                _ssoTokens = new SSOTokens
                                {
                                    TokenHint = _idTokenHint,
                                    Code = code,
                                    State = _request.Form["state"],
                                    Username = username
                                };
                                break;
                            }
                        }
                        else
                        {
                            logger.Warn("The Username's domain does not match with the Configuration.");
                        }
                    }
                    catch (Exception ex)
                    {
                        logger.Warn(ex, "Error on decoding OIDCResponse. " + ex.Message);
                        continue;
                    }
                }
            }
            return _ssoUser;
        }

        public override DateTimeOffset ValidTo()
        {
            return _SecurityTokenValidTo;
        }

        public override string GetRedirectUrl()
        {
            if (_ssoEnable && _request.Form != null)
            {
                return RelayState.IsMobileDevice
                    ? string.Format(RETURN_MOBILE_FORMAT, _guid)
                    : RelayState.ReturnUrl;
            }
            return null;
        }

        protected string FindValue(JwtSecurityToken jwtSecurityToken, string key)
        {
            var itemValue = jwtSecurityToken.Claims
                .Where(x => x.Type == key)
                .Select(y => y.Value);
            return itemValue.FirstOrDefault();
        }

        public override void RegisterTokenHint(string uniqueKey)
        {
            if (!RelayState.IsMobileDevice && !string.IsNullOrEmpty(_idTokenHint))
            {
                var currentTime = new DateTimeOffset(DateTime.UtcNow).ToUnixTimeSeconds();
                var duration = Math.Ceiling((_expiration - currentTime) / 60.0);
                Core.SetRedisValue("oidc_idTokenHint:" + uniqueKey, _idTokenHint, TimeSpan.FromMinutes(duration));
            }
        }
    }
}
