using System;
using Newtonsoft.Json;

namespace Extric.Towbook.SSO
{
    public class AccessToken
    {
        public AccessToken()
        {
            RefreshToken = Guid.NewGuid().ToString();
        }

        private const string FormatKey = "sso:refresh:{0}";

        [JsonProperty("token_type")]
        public string TokenType { get; set; }

        [JsonProperty("scope")]
        public string Scope { get; set; }

        [JsonProperty("access_token")]
        public string AccessTokenProvider { get; set; }

        [JsonProperty("expires_in")]
        public int ExpiresIn { get; set; }

        [JsonProperty("ext_expires_in")]
        public int ExtExpiresIn { get; set; }

        [JsonProperty("refresh_token")]
        public string RefreshTokenProvider { get; set; }

        public string RefreshToken { get; set; }
        public string Username { get; set; }
        public string TokenHint { get; set; }
        public string ApiToken { get; set; }

        public string GetKey() => string.Format(FormatKey, RefreshToken);

        public static AccessToken GetFromCache(string refreshToken)
        {
            var value = Core.GetRedisValue(string.Format(FormatKey, refreshToken));
            if (value == null) return null;

            var tokens = JsonConvert.DeserializeObject<AccessToken>(value);
            if (tokens != null)
            {
                return tokens;
            }
            return null;
        }
    }
}
