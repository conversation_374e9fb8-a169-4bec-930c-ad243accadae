using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Xml.Linq;
using Extric.Towbook.Agent.API.Models;
using Extric.Towbook.Agent.QuickBooks;
using Extric.Towbook.Agent.Sync;
using Extric.Towbook.WebShared;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Async = System.Threading.Tasks;

namespace Extric.Towbook.Agent.API.Controllers
{
    [Route("agent/sync/events")]
    public class SyncEventsResponseController : ControllerBase
    {
        /*
            name: "Agent_Sync_Events_Response",
            routeTemplate: "agent/sync/events/{id}/response",
            defaults: new { controller = "SyncEventsResponse", id = RouteParameter.Optional })
            .DataTokens = new RouteValueDictionary { ["Namespaces"] = new string[] { agentNamespace } };
         */
        private static readonly DataService service = new DataService();

        private async Task<int> getSessionIdAsync()
        {
            var agent = await Agent.Session.GetByCompanyIdAsync(WebGlobal.CurrentUser.CompanyId);
            if (agent == null)
                return 0;

            return agent.Id;
        }

        [ApiExplorerSettings(IgnoreApi = true)]
        public async Task<IEnumerable<object>> Get()
        {
            var ret = new Collection<SyncEventModel>();

            var sid = await getSessionIdAsync();

            if (sid == 0)
                return new SyncEventModel[0];

            return ret;
        }

        [Route("{id}/response")]
        [HttpGet]
        public async Task<object> Get(long id)
        {
            var ev = service.GetSyncEventById(id);

            if (ev.AgentSessionId != await getSessionIdAsync())
                return null;

            var evd = service.GetEventResponseDataByIds(new long[] { id });

            return new { Event = ev, Response = evd };
        }

        public class EventResponseDataModel
        {
            public string Key { get; set; }
            public string Data { get; set; }
        }

        [Route("{id}/response")]
        [HttpPost]
        public async Task<object> Post(long id, [FromBody] EventResponseDataModel eventResponseDataModel)
        {
            // handle recording of QuickBooks filename
            if (id == -99)
            {
                if (eventResponseDataModel.Key == "quickBooksFilename")
                {
                    var ag = await Session.GetByCompanyIdAsync(WebGlobal.CurrentUser.CompanyId);
                    if (ag != null)
                    {
                        ag.QuickBooksFilename = eventResponseDataModel.Data;
                        ag.Save();
                    }
                }

                return Ok();
            }


            var erd = new EventResponseData();
            erd.Key = eventResponseDataModel.Key;
            erd.Data = eventResponseDataModel.Data;
            erd.SyncEventId = id;

            service.SaveX(erd);

            // look up the original request.

            var ev = service.GetSyncEventById(id);

            var evr = service.GetEventRequestDataByIds(new long[] { id }).FirstOrDefault();

            switch (ev.Type)
            {
                case SyncEventType.Invoice:
                    await ParseInboundInvoiceResponseAsync(ev, erd, evr);
                    break;
                case SyncEventType.Payment:
                    await ParseInboundPaymentResponseAsync(ev, erd, evr);
                    break;
                case SyncEventType.Item:
                    await ParseInboundItemResponseAsync(ev, erd, evr);
                    break;
            }
            
            return Ok();
        }

        private async Async.Task ParseInboundPaymentResponseAsync(Event ev, EventResponseData erd, EventRequestData evr)
        {
            var inv = JsonConvert.DeserializeObject<Payment>(evr.Data);

            XDocument xdoc = XDocument.Parse(erd.Data);

            //Run query
            var lv1s = (from lv1 in xdoc.Descendants("ReceivePaymentRet")
                        select new
                        {
                            TxnId = lv1.Descendants("TxnID").FirstOrDefault()?.Value,
                            EditSequence = lv1.Descendants("EditSequence").FirstOrDefault()?.Value,
                            TxnNumber = Convert.ToInt64(lv1.Descendants("TxnNumber").FirstOrDefault()?.Value ?? "0"),
                            ArAccountId = lv1.Descendants("ARAccountRef").Descendants("ListID").FirstOrDefault()?.Value,
                            ArAccountName = lv1.Descendants("ARAccountRef").Descendants("FullName").FirstOrDefault()?.Value,
                            CustomerId = lv1.Descendants("CustomerRef").Descendants("ListID").FirstOrDefault()?.Value,
                            CustomerName = lv1.Descendants("CustomerRef").Descendants("FullName").FirstOrDefault()?.Value,
                            PaymentMethodId = lv1.Descendants("PaymentMethodRef").Descendants("ListID").FirstOrDefault()?.Value,
                            PaymentMethodName = lv1.Descendants("PaymentMethodRef").Descendants("FullName").FirstOrDefault()?.Value,
                            TotalAmount = Convert.ToDecimal(lv1.Descendants("TotalAmount")?.FirstOrDefault()?.Value ?? "0"),
                            TxnDate = Convert.ToDateTime(lv1.Descendants("TxnDate").FirstOrDefault()?.Value),
                            Memo = lv1.Descendants("Memo").FirstOrDefault()?.Value,
                            AppliedTo = lv1.Descendants("AppliedToTxnRet")?.Select(o => new
                            {
                                TxnId = o.Descendants("TxnID").FirstOrDefault()?.Value,
                                Type = o.Descendants("TxnType").FirstOrDefault()?.Value,
                                Date = Convert.ToDateTime(o.Descendants("TxnDate").FirstOrDefault()?.Value),
                                RefNumber = o.Descendants("RefNumber").FirstOrDefault()?.Value,
                                Amount = Convert.ToDecimal(o.Descendants("Amount").FirstOrDefault()?.Value),
                            })
                        }).FirstOrDefault();

            if (lv1s?.TxnId != null)
            {
                inv.ObjectId = lv1s.TxnId;
                inv.EditSequence = lv1s.EditSequence;
                inv.ARAccountRefName = lv1s.ArAccountName;
                inv.CustomerRefName = lv1s.CustomerName;
                inv.Amount = lv1s.TotalAmount;
                inv.Date = lv1s.TxnDate;
                inv.Memo = lv1s.Memo;
                inv.TransactionNumber = lv1s.TxnNumber;
                inv.Id = service.FindIdByObjectId(inv, await getSessionIdAsync());

                service.SaveX(inv);

                foreach (var x in lv1s.AppliedTo)
                {
                    PaymentLine pl = null;

                    if (x.TxnId != null)
                        pl = inv.Invoices.Where(o =>
                            o.ObjectId == x.TxnId &&
                            o.RefNumber == x.RefNumber).FirstOrDefault();

                    if (pl == null)
                        pl = inv.Invoices.Where(o =>
                            o.RefNumber == x.RefNumber).FirstOrDefault();

                    if (pl == null)
                    {
                        pl = new PaymentLine();
                        inv.Invoices.Add(pl);
                    }

                    pl.PaymentId = inv.Id;
                    pl.Amount = (decimal)x.Amount;
                    pl.RefNumber = x.RefNumber;
                    pl.ObjectId = x.TxnId;
                }

                var keyId = Integration.InvoicePaymentKey.GetByProviderId(Integration.Provider.QuickBooks.ProviderId, "AgentPaymentId").Id;
                var ipkv = Integration.InvoicePaymentKeyValue.GetByInvoicePaymentId(
                    ev.SourceId).Where(o => o.KeyId == keyId).FirstOrDefault() ??
                    new Integration.InvoicePaymentKeyValue();
                ipkv.KeyId = keyId;
                ipkv.DispatchEntryPaymentId = ev.SourceId;

                if (ipkv.Value != inv.ObjectId)
                {
                    ipkv.Value = inv.ObjectId;
                    ipkv.Save();
                }

                foreach (var x in inv.Invoices)
                {
                    service.SaveX(x);
                }
            }
        }

        private async Async.Task ParseInboundInvoiceResponseAsync(Event ev, EventResponseData erd, EventRequestData evr)
        {
            var inv = JsonConvert.DeserializeObject<QuickBooks.Invoice>(evr.Data);

            XDocument xdoc = XDocument.Parse(erd.Data);

            //Run query
            var lv1s = (from lv1 in xdoc.Descendants("InvoiceRet")
                        select new
                        {
                            TxnId = lv1.Descendants("TxnID").FirstOrDefault()?.Value,
                            EditSequence = lv1.Descendants("EditSequence").FirstOrDefault()?.Value,
                            ArAccountId = lv1.Descendants("ARAccountRef").Descendants("ListID").FirstOrDefault()?.Value,
                            ArAccountName = lv1.Descendants("ARAccountRef").Descendants("FullName").FirstOrDefault()?.Value,
                            Subtotal = Convert.ToDouble(lv1.Descendants("Subtotal")?.FirstOrDefault()?.Value ?? "0"),
                            TxnDate = Convert.ToDateTime(lv1.Descendants("TxnDate").FirstOrDefault()?.Value),
                            Memo = lv1.Descendants("Memo").FirstOrDefault()?.Value
                        }).FirstOrDefault();
            if (lv1s == null)
                return;

            inv.ObjectId = lv1s.TxnId;
            inv.EditSequence = lv1s.EditSequence;
            inv.ARAccountId = lv1s.ArAccountId;
            inv.ARAccountName = lv1s.ArAccountName;
            inv.Subtotal = lv1s.Subtotal;
            inv.TxnDate = lv1s.TxnDate;
            inv.Memo = lv1s.Memo;
            inv.Id = service.FindIdByObjectId(inv, await getSessionIdAsync());

            service.SaveX(inv);
            var keyId = Integration.DispatchEntryKey.GetByProviderId(Integration.Provider.QuickBooks.ProviderId, "AgentInvoiceId").Id;
            
            var ipkv = Integration.DispatchEntryKeyValue.GetByDispatchEntryId(
                ev.SourceId, Integration.Provider.QuickBooks.ProviderId, "AgentInvoiceId").FirstOrDefault() ??
                new Integration.DispatchEntryKeyValue();

            ipkv.KeyId = keyId;
            ipkv.DispatchEntryId = ev.SourceId;
            ipkv.Value = inv.ObjectId;
            // TODO: log if inv.ObjectId != ipkv.Value when it already exists from a query above.
            ipkv.Save();

            var en = await Dispatch.Entry.GetByIdAsync(ev.SourceId);
            if (en != null)
            {
                await Dispatch.Entry.UpdateInsight(en.Id, en.CompanyId, "pushedToQuickBooks", "Yes");
            }
        }

        private async Async.Task ParseInboundItemResponseAsync(Event ev, EventResponseData erd, EventRequestData evr)
        {
            var item = JsonConvert.DeserializeObject<QuickBooks.Item>(evr.Data);

            XDocument xdoc = XDocument.Parse(erd.Data);

            //Run query
            var lv1s = (from lv1 in xdoc.Descendants("ItemServiceRet")
                        select new
                        {
                            ListID = lv1.Descendants("ListID").FirstOrDefault()?.Value,
                            EditSequence = lv1.Descendants("EditSequence").FirstOrDefault()?.Value,
                            FullName = lv1.Descendants("FullName").FirstOrDefault()?.Value,
                            IsActive = lv1.Descendants("IsActive").FirstOrDefault()?.Value,
                            AccountName = lv1.Descendants("AccountRef").Descendants("FullName").FirstOrDefault()?.Value
                        }).FirstOrDefault();

            if (lv1s == null)
            {
                lv1s = (from lv1 in xdoc.Descendants("ExistingItemRs")
                        select new
                        {
                            ListID = lv1.Attribute("id").Value,
                            EditSequence = "0",
                            FullName = lv1.Attribute("name").Value,
                            IsActive = "true",
                            AccountName = ""
                        }).FirstOrDefault();

                
            }
            if (lv1s == null)
                return;

            item.AgentSessionId = ev.AgentSessionId;
            item.ObjectId = lv1s.ListID;
            item.Id = service.FindIdByObjectId(item, await getSessionIdAsync());
            item.EditSequence = lv1s.EditSequence;
            item.Name = lv1s.FullName;
            item.IsActive = lv1s.IsActive == "true";
            item.AccountName = lv1s.AccountName;

            service.SaveX(item);

            ev.Status = SyncEventStatus.Success;
            service.SaveX(ev);
        }

        public class ReturnData
        {
            public string TxnId { get; set; }
            public string EditSequence { get; set; }
        }

        public static ReturnData Parse(string key, string xml)
        {
            XDocument xdoc = XDocument.Parse(xml);

            //Run query
            var lv1s = (from lv1 in xdoc.Descendants(key)
                        select new
                        {
                            TxnId = lv1.Descendants("TxnID").FirstOrDefault()?.Value,
                            EditSequence = lv1.Descendants("EditSequence").FirstOrDefault()?.Value,
                        }).FirstOrDefault();

            return new ReturnData() { TxnId = lv1s.TxnId, EditSequence = lv1s.EditSequence };
        }

    }
}
