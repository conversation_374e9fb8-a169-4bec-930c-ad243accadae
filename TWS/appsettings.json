{"ConnectionStrings": {"Database": "", "Database.Azure": "", "Microsoft.ServiceBus": "", "Microsoft.ServiceBusDev": ""}, "Redis": {"Servers": "", "Credentials": "", "SentinelServiceName": "", "ConnectionPoolSize": 1}, "CosmosDb": {"Url": "", "AuthKey": "", "Database": ""}, "Google": {"Maps": {"ApiKey": "AIzaSyC8TMngtja24PQvJlfkKepOz_jg1DY_j2g"}}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "AllowedHosts": "*", "Cache": {"CacheToUse": "web", "DependencyManagerToUse": "<PERSON><PERSON><PERSON>", "DistributedCacheName": "Towbook", "DistributedCacheServers": "localhost:6379", "IsCacheDependencyManagementEnabled": "True", "IsCacheEnabled": "True", "LoggingLevel": "None"}}