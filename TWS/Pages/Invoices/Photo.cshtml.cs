using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Extric.Towbook.Dispatch;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;

namespace TWSNet5.Pages.Invoices
{
    public class PhotoModel : PageModel
    {
        public Photo p { get; set; }
        [FromQuery(Name = "id")]
        public string id { get; set; }
        public string src { get; set; }
        public void OnGet()
        {
            ViewData["Title"] = "Towbook - Impound/Upload Photo";

            if (String.IsNullOrEmpty(id))
                id = "0";

            p = Photo.GetById(Convert.ToInt32(id));
            src = $"http://localhost{p.Location.Replace("%1", BeginRequestMiddleware.CurrentCompany.Id.ToString())}";

        }
    }
}
