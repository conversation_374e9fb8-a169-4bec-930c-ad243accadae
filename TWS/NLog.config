<?xml version="1.0" encoding="utf-8" ?>
<nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" throwExceptions="true">
  <extensions>
    <add assembly="NLog.Targets.Loggly" />
  </extensions>
  <targets>
    <target name="Loggly" xsi:type="Loggly" layout="${message}" />
  </targets>
  <rules>
    <logger name="*" minLevel="Info" writeTo="Loggly" />
  </rules>
</nlog>