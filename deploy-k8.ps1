param ($svc, 
	$env = "uat", 
	$replicas = 1, 
	$configbase="x:/_config8", 
	$nrkey = "8cafb55ce90fcecb19791cd5d262c82eFFFFNRAL", 
	$extra = "", 
	$bgmode = $null, 
	$apply=0,
	$sb="", 
    $swap = 0)

$globalVersion = "25.7.10"
$commitId=$(git rev-parse --short HEAD)
$nrpath="C:/Program Files/New Relic/New Relic CLI/newrelic.exe";

$servicesBgModes = @{ 
  "http-api"= 1
  "http-ajax"= 1
}

if ($bgmode -eq $null)
{
    $default = $servicesBgModes[$svc];
    echo "bg mode isnt set... use $default";

    if ($default -eq $null)
    {
      echo "not set, set to 0"
      $bgmode = 0;
    }
    else
    {
      $bgmode = $default;
    }
}

try { invoke-expression "& '$nrpath' --version" > $nul  } catch { 
	echo "doesn't exist" 
    if ($IsWindows) {
        choco install newrelic-cli -y
        invoke-expression "& '$nrpath' profile add --profile 'prod_us_392681_change_tracking' --region 'us' --apiKey 'NRAK-84CIJ772GTYB2EPDFI9S8IO5HZF' --accountId '392681' --licenseKey '$nrkey'"
    }
}

function notifySlack {
   param( 
     $message
   )

  $token = "******************************************************";
  $channel = "#dev-deployment";
  $body = @{
    channel = $channel
    text    = $message
  };

  Invoke-RestMethod -Uri "https://slack.com/api/chat.postMessage" -Method Post -Headers @{Authorization = "Bearer $token"} -Body $body -ContentType "application/x-www-form-urlencoded"
}

function swapIngress { 
  param (
    [Parameter(Mandatory = $true)]
    [string]$mode, 
    [Parameter(Mandatory = $true)]
    [string]$healthUrl
  )

  $rest = Invoke-RestMethod -Uri $healthUrl -Method Get

  kubectl apply -f $configbase/prod/$img/ingress-$extra.yaml
  Start-Sleep -Seconds 2

  $rest2 = Invoke-RestMethod -Uri $healthUrl -Method Get

  $oldCommitId = $rest.commitIdl;
  $oldCommitId = ([string]::IsNullOrEmpty($oldCommitId)) ? $rest.CommitId : $oldCommitId;

  $newCommitId = $rest2.commitId;
  $newCommitId = ([string]::IsNullOrEmpty($newCommitId)) ? $rest.CommitId : $newCommitId;

  $envName = "";
  if ($env -eq "uat") { $envName = "UAT" }
  elseif ($env -eq "prod") { $envName = "Production"; } 
  elseif ($env -eq "dev") { $envName = "Dev"; }
  elseif ($env -eq "test") { $envName = "Test" }

  notifySlack "Updated $envName ``$img`` to $mode; moved from ``<https://www.github.com/towbook/towbook/commits/$oldCommitId|$oldCommitId>`` to ``<https://www.github.com/towbook/towbook/commits/$newCommitId|$newCommitId>`` | <https://www.github.com/towbook/towbook/compare/$oldCommitId..$newCommitId|Compare Changes>";
}


function getCurrentEnv {
  param (
    $svc,
    $env,
    $extra,
    $type="ingress"
  )

  $services = @{
    "http-ajax" = "public-ingress-towbook-ajax"
    "http-api" = "public-ingress-towbook"
    "http-pdf" = "public-pdf-towbook"
    "http-twbk" = "public-twbk-co"
  }

  $sn = $services[$svc];
  $r = (kubectl get $type $sn -n towbook-$env -o "jsonpath={.spec.rules[].http.paths[].backend.service.name}")

  if (!$r){
    return;
  }

  if ($r.EndsWith("-green")){
    return "green";
  }
  if ($r.EndsWith("-blue")){
    return "blue";
  }
}

if ($bgmode) { 
  Write-Host "*** using blue green deployment mode ***"
  $bg = getCurrentEnv $svc $env $extra

  if (!$bg)
  {
	Write-Host "Relogin to IBM and try again. Exiting.";
	Write-Host "";

	return;
  }

  Write-Host "current $svc in environment towbook-$env is serving from: $bg";

  if ($bg -eq "blue") {
    $extra = "green";
  }
  elseif ($bg -eq "green") {
    $extra = "blue";
  }

  Write-Host "Preparing to deploy to: $extra...";
  Write-Host 
  Write-Host 
}


$fullname = "";

$basePath = (Get-Item .).FullName;

$services = @{ 
  "mcdispatchsvc" = "MotorClubDispatchingService"
  "cachesyncsvc" = "CacheSyncService"
  "eventnotificationsvc" = "EventNotificationService"
  "mcbillingsvc" = "MotorClubBillingService"
  "mcgpssvc" = "MotorClubGpsService"
  "gpscollectorsvc" = "GpsCollectorService"
  "processqueuesvc" = "ProcessQueueService"
  "queueproxysvc" = "QueueProxyService"
  "callindexersvc" = "CallIndexerService"
  "http-api"= "../Extric.Towbook.API"
  "http-ajax"= "../Ajax"
  "http-public"= "../Extric.Towbook.Web.PublicMvc"
  "http-locate"= "../Extric.Towbook.Web.LocateMvc"
  "http-pdf"= "../Extric.Towbook.API.Utils"
  "http-twbk" = "../Web/twbk.co/PublicAccess"
  "twbkacglistenersvc" = "../Services/AaaAcgSalesforceListener/src/TestAppWorker"
  "aaawcnylistenersvc" = "../Services/AaaWashingtonSalesforceListener/src/TwbkAaaWashingtonListener"
  "aaanatlistenersvc" = "../Services/AaaNationalSalesforceListener/src/TwbkAaaNationalListener"
  "supportdatasyncsvc" = "../Services/SupportDataSyncService"
  "mcpaymentimportersvc" = "../Services/MotorClubPaymentImportService"
  "safeclearsyncsvc" = "../Services/SafeclearSyncService"
  "predictedarrivalsvc" = "../Services/PredictedArrivalService"
  "vpsvc" = "../Services/VendorProcessingService"
  "mcprocessingsvc" = "../Services/MotorClubProcessingService"
  "statementsvc" = "Extric.Towbook.AutomatedMessagesService"
  "squaresvc" = "../Services/SquareService"
}

$nrDeploys = @{
 "http-api" = "MzkyNjgxfEFQTXxBUFBMSUNBVElPTnw3NTY5Njg0NDE"
 "http-pdf" = "MzkyNjgxfEFQTXxBUFBMSUNBVElPTnw3NTMwODY5MzA" 
}

$fullname = "";

if ($svc -and $services.ContainsKey($svc)) {
$fullname = $services[$svc];
}

if ([string]::IsNullOrEmpty($fullname)) {
  Write-Output "Unknown service: $svc`n";
  Write-Output "deploy-k8.ps1 [svc] [env=uat] [replicas=1]";
  return;
}


$docker = docker ps 2>&1
if($docker.CategoryInfo) { 
	Write-Output "Docker isn't running. Start Docker Engine and try again.";
	return;
}

if($svc -eq "twbkacglistenersvc") { $basePath = $basePath + "/" + "Services/AaaAcgSalesforceListener"}
if($svc -eq "aaawcnylistenersvc") { $basePath = $basePath + "/" + "Services/AaaWashingtonSalesforceListener"}
if($svc -eq "aaanatlistenersvc") { $basePath = $basePath + "/" + "Services/AaaNationalSalesforceListener"}

Write-Output "Base Path -> $basePath";

$nrname = "$env-$svc"
$configPath = "$configbase/$env/$svc/appsettings.json";

if (!(Test-Path $configPath)) {


if ($env -eq "") {
  Write-Output "$configPath doesn't exist. Cannot continue.";
  return;
} elseif ($extra) {
   $configPath = "$configbase/$env/$svc/appsettings-$extra.json";

if (!(Test-Path $configPath)) {
  Write-Output "$configPath doesn't exist. Cannot continue..";
  return;
  }
}

}


Write-Output "New Relic App Name set to -> $nrname";
Write-Output "Preparing $svc -> $fullname";

if ($env -eq "uat") { Write-Output "Deploying UAT...";}
elseif ($env -eq "prod") {Write-Output "Deploying Production..."; }
elseif ($env -eq "dev") {Write-Output "Deploying Dev..."; }
elseif ($env -eq "test") {Write-Output "Deploying Test..."; }
else { 
  Write-Output "invalid environment $env";
  return
}



Write-Output "commit -> $commitId"
$img = $svc;

if ($swap -eq 1)  {
  Write-Host "FLIP $img!"; 

$healthUrl = "";

if ($img -eq "http-api")  {
  $healthUrl = "https://api.towbook.com/health";
} elseif ($img -eq "http-ajax") { 
  $healthUrl = "https://app.towbook.com/ajax/health";
}
  Write-Output "HU= $healthUrl";
  swapIngress $bg $healthUrl
return

}


if ($env -eq "") {
  Copy-Item $configbase/$env/$svc/appsettings.json -Destination ./Services/$fullname/ -force
}elseif ($extra) {
  Copy-Item $configbase/$env/$svc/appsettings-$extra.json -Destination ./Services/$fullname/appsettings.json -force
  $svc="$svc-$extra";

  if (($extra -ne "blue") -and ($extra -ne "green")){
    $img = $svc;
    Write-Output "Using image name -> $img";
  }
  Write-Output "Using service name -> $svc";
} else {
  Copy-Item $configbase/$env/$svc/appsettings.json -Destination ./Services/$fullname/appsettings.json -force
  Write-Output "Using service name -> $svc";
}

$imageUrl = "us.icr.io/towbook-$env/$img" + ":" + $commitId;
Write-Output "output path: $imageUrl";

docker buildx build -f "./Services/$fullname/Dockerfile" -t $imageUrl --build-arg "COMMIT=$commitId" --build-arg "NEW_RELIC_LICENSE_KEY=$nrkey" --build-arg "NEW_RELIC_APP_NAME=$nrname" --build-arg "CORECLR_ENABLE_PROFILING=1" $basePath --platform linux/amd64 --push --builder cloud-towbook-towbook
kubectl create deployment $svc --image=$imageUrl -n towbook-$env --replicas=$replicas --dry-run=client -o yaml > deployment.yaml 

if ($apply) { 
  kubectl apply -f ./deployment.yaml
  return
}

function mcGpsDeployment ($name) {
  kubectl create deployment $svc-$name --image=$imageUrl -n towbook-$env --replicas=$replicas --dry-run=client -o yaml > deployment2.yaml 
  $data = Get-Content deployment2.yaml
  $data = $data[0..($data.count-3)]
  $img =  $data[($data.count-2)..($data.count-2)]
  $data = $data[0..($data.count-3)]
  $data = $data + $img + @"
        name: mcgpssvc-$name
        env:
        - name: Args__MotorClub
          value: $name
        - name: Args__ExitAfter
          value: "0"
"@
  $data | Out-File ./deployment-$name.yaml
  kubectl apply -f ./deployment-$name.yaml
}

function blueGreenDeployment ($name) {
  kubectl create deployment $svc --image=$imageUrl -n towbook-$env --replicas=$replicas --dry-run=client -o yaml > deployment2.yaml 
  $data = Get-Content deployment2.yaml
  $data = $data[0..($data.count-2)]
  $img =  $data[($data.count-2)..($data.count-1)]
  $data = $data[0..($data.count-3)]
  $data = $data + $img + @"
        env:
        - name: NEW_RELIC_APP_NAME
          value: $nrname
"@
  $data | Out-File ./deployment.yaml
}


function serviceBusDeployment ($name) {

  kubectl create deployment $svc-$name --image=$imageUrl -n towbook-$env --replicas=$replicas --dry-run=client -o yaml > deployment2.yaml 

  $sbConfig = Get-Content $configbase/$env/$svc/$name.txt

 if ($sbConfig) { 


  $data = Get-Content deployment2.yaml

  $data = Get-Content deployment2.yaml
  $data = $data[0..($data.count-3)]
  $img =  $data[($data.count-2)..($data.count-2)]
  $data = $data[0..($data.count-3)]
  $data = $data + $img + @"
        name: $svc-$name
        env:
        - name: ConnectionStrings__Microsoft.ServiceBus
          value: $sbConfig

"@
  $data | Out-File ./deployment-$name.yaml
  kubectl apply -f ./deployment-$name.yaml
}
else{
  Write-Output " MISSING REQUIRED CONFIGURATION for $name";
  return;
} 

}


function updateDeployment {
  if ($img -eq 'http-pdf' -or ($img -eq "http-api") -or ($img -eq "http-ajax")) {
    $xrr = $nrDeploys[$img];
    invoke-expression "& '$nrpath' entity deployment create --guid '$xrr' --version '$globalVersion' --commit '$commitId' --deploymentType 'BLUE_GREEN' --description '' --user 'xdansmith' --profile 'prod_us_392681_change_tracking'"

    notifySlack "Staged ``$img``, version *$globalVersion*, commit ``$commitId``";
    return 
  }
}

  $envName = "";
  if ($env -eq "uat") { $envName = "UAT" }
  elseif ($env -eq "prod") { $envName = "Production"; } 
  elseif ($env -eq "dev") { $envName = "Dev"; }
  elseif ($env -eq "test") { $envName = "Test" }


if ($svc -eq "mcgpssvc") {
  mcGpsDeployment "swoop"
  mcGpsDeployment "allstate"
  mcGpsDeployment "urgently"
  mcGpsDeployment "rp"
  mcGpsDeployment "sts"
  mcGpsDeployment "honk"
  mcGpsDeployment "acg"
  mcGpsDeployment "aaanatfsl"
  mcGpsDeployment "trx"
  mcGpsDeployment "nsd"

  notifySlack "Deployed ``$img`` (Swoop, Allstate, Urgently, RP, Stack Three, Honk, ACG, AAA National FSL, TRX, NSD), version *$globalVersion*, commit ``$commitId`` to $envName";

  return
}

if ($svc -eq "mcbillingsvc") {
  serviceBusDeployment "wc"
  serviceBusDeployment "nc"

  return
}

if ($svc -eq "mcdispatchsvc") {


  serviceBusDeployment "wc"
  notifySlack "Deployed ``$img`` (WC), version *$globalVersion*, commit ``$commitId`` to $envName";
  serviceBusDeployment "nc"
  notifySlack "Deployed ``$img`` (NC), version *$globalVersion*, commit ``$commitId`` to $envName";

  return
}

if ($svc -eq "processqueuesvc") {
  serviceBusDeployment "wc"
  serviceBusDeployment "nc"

  return
}

if ($svc -eq "eventnotificationsvc") {
  serviceBusDeployment "wc"
  serviceBusDeployment "nc"

  return
}

if ($svc -eq "statementsvc") {
  serviceBusDeployment "wc"
  serviceBusDeployment "nc"

  return
}

if (($sb -eq "nc") -or ($sb -eq "wc")) {
  serviceBusDeployment $sb
  return
}

if ($bgmode -eq "1") {
  blueGreenDeployment
  kubectl apply -f ./deployment.yaml
  updateDeployment
  return
}