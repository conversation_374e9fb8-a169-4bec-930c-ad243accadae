using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Extric.Towbook.Integrations.Phone.Grasshopper
{
    public class Call
    {
        public int Id { get; set; }
        public string OriginNumber { get; set; }
        public string DestinationNumber { get; set; }
        public CallStatus Status { get; set; }
        public int Extension { get; set; }
        public string ExtensionDescription { get; set; }
        public DateTime CreateDate { get; set; }
        public TimeSpan Duration { get; set; }
    }

    public enum CallStatus
    {
        None = 0,
        Connected = 1,
        Voicemail = 2,
        Missed = 3,
        FaxReceived = 4
    }
}
