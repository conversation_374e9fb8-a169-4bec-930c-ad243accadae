diff --git a/Extric.Towbook.API/Controllers/ImpoundsController.cs b/Extric.Towbook.API/Controllers/ImpoundsController.cs
index 0a4592ff3e..ef02142860 100644
--- a/Extric.Towbook.API/Controllers/ImpoundsController.cs
+++ b/Extric.Towbook.API/Controllers/ImpoundsController.cs
@@ -1243,7 +1243,7 @@ namespace Extric.Towbook.API.Controllers
             // add changed values for tracking
             releaseDetailsTrackable.ReleaseDate = impound.ReleaseDate;
             // TODO: Can be made async
-            releaseDetailsTrackable.ReleaseReason = impound.ReleaseReason.HasValue ? ReleaseReason.GetById((int)impound.ReleaseReason)?.Description ?? "" : "";
+            releaseDetailsTrackable.ReleaseReason = impound.ReleaseReason.HasValue ? (await ReleaseReason.GetByIdAsync((int)impound.ReleaseReason))?.Description ?? "" : "";
             releaseDetailsTrackable.ReleaseNotes = impound.ReleaseNotes;
 
             // save any changes to history/activity logging
diff --git a/Extric.Towbook/Impound/ReleaseReason.cs b/Extric.Towbook/Impound/ReleaseReason.cs
index fb2ff00065..087d7a62a1 100644
--- a/Extric.Towbook/Impound/ReleaseReason.cs
+++ b/Extric.Towbook/Impound/ReleaseReason.cs
@@ -2,6 +2,7 @@
 using System.Collections.Generic;
 using System.Collections.ObjectModel;
 using System.Linq;
+using System.Threading.Tasks;
 using Extric.Towbook.Utility;
 using ProtoBuf;
 
@@ -78,6 +79,18 @@ namespace Extric.Towbook.Impounds
             return SqlMapper.Query<ReleaseReason>("SELECT * FROM ImpoundReleaseReasons WHERE ImpoundReleaseReasonId = @Id",
                     new { Id = id }).FirstOrDefault();
         }
+        
+        public static async Task<ReleaseReason> GetByIdAsync(int id) =>
+            await Cache.Instance.GetAsync(id, async (int reasonId) => await GetByIdWithoutCacheAsync(reasonId));
+
+        public static async Task<ReleaseReason> GetByIdWithoutCacheAsync(int id)
+        {
+            if (id == 0)
+                return null;
+
+            return (await SqlMapper.QueryAsync<ReleaseReason>("SELECT * FROM ImpoundReleaseReasons WHERE ImpoundReleaseReasonId = @Id",
+                    new { Id = id })).SingleOrDefault();
+        }
 
         public static void InvalidateCache(int companyId)
         {
