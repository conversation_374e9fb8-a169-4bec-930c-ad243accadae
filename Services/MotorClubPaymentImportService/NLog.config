<?xml version="1.0" encoding="utf-8" ?>
<nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" throwExceptions="true" internalLogFile="C:/towbook/motorclubpaymentimportservice.log" internalLogLevel="Debug" autoReload="true">
  <extensions>
    <add assembly="NLog.Targets.Loggly" />
  </extensions>

  <targets async="true">
    <target name="console" xsi:type="ColoredConsole" layout="${message}"/>
    <target name="loggly" xsi:type="Loggly" layout="${message}" />
  </targets>

  <rules>
    <logger name="*" minLevel="Trace" writeTo="loggly" />
    <logger name="*" minlevel="Trace" writeTo="console" enabled="true" />
  </rules>
</nlog>