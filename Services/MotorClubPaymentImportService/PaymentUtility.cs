using Extric.Towbook;
using Extric.Towbook.Integration;
using Extric.Towbook.Integration.MotorClubs;
using Extric.Towbook.Utility;
using NLog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MotorClubPaymentImportService
{
    internal class PaymentUtility
    {
        public static void HandleException(Logger logger, LogInfo logInfo, Exception e, string action, object data = null)
        {
            logInfo.FlushEvents(logger);
            LogEvent(logger, logInfo, $"{logInfo.MotorClubName} -- {action} failed because of exception \"{e.Message}\", {data?.ToJson()} -- {e}", LogLevel.Error);
        }

        public static void LogEvent(Logger logger, LogInfo logInfo, string message, LogLevel logLevel, Dictionary<object, object> properties = null)
        {
            logger.LogEvent(logInfo.Prefix(message), logInfo.CompanyId, logLevel, logInfo.GetProperties(properties));
        }

        public static DateTime? GetLastPaymentImportDate(int accountId)
        {
            var lastImported = AccountKeyValue.GetFirstValueOrNull(0, accountId, Provider.Towbook.ProviderId, "ImportPaymentsLastProcessed");

            var filteredStartDate = DateTime.MinValue;

            if (lastImported != null)
            {
                if (!DateTime.TryParse(lastImported, out filteredStartDate))
                {
                    Console.WriteLine("Invalid ImportPaymentsLastProcessed Value: " + lastImported + "... using " + filteredStartDate + " instead");
                    return null;
                }

                return filteredStartDate;
            }

            return null;
        }
        public static void SetLastPaymentImportDate(int accountId, DateTime date)
        {
            // don't allow it to be set to TODAY, some motor clubs send multiple payments on same day at different times of day.
            if (date >= DateTime.Today)
                date = DateTime.Today.AddDays(-1);

            var last = GetLastPaymentImportDate(accountId);
            if (date > last || last == null)
                SaveAccountKeyValue(accountId, date.ToShortDateString(), "ImportPaymentsLastProcessed");
        }

        public static bool SaveAccountKeyValue(int accountId, string value, string key)
        {
            var saved = false;

            if (!string.IsNullOrWhiteSpace(value))
            {
                var kv = AccountKeyValue.GetByAccount(0, accountId, Provider.Towbook.ProviderId, key).FirstOrDefault()
                    ?? new AccountKeyValue(accountId, Provider.Towbook.GetKey(KeyType.Account, key).Id, "");

                if (kv.Value != value)
                {
                    kv.Value = value;
                    kv.Save();
                    saved = true;
                }
            }

            return saved;
        }

    }

    public static class PaymentExtensions
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="logger"></param>
        /// <param name="masterAccountId">Specify the Motor Club this event is for. This should ALWAYS be set.</param>
        /// <param name="type">General category. Connection, Offer, Status Updates, Self Service</param>
        /// <param name="message"></param>
        /// <param name="contractorId">Optional</param>
        /// <param name="locationId"></param>
        /// <param name="dispatchId"></param>
        /// <param name="companyId"></param>
        /// <param name="o"></param>
        public static async Task Info(this Logger logger,
            int masterAccountId,
            string type,
            string message,
            int accountId,
            int companyId,
            object data = null) => await Log(logger, LogLevel.Info, masterAccountId, type, message, accountId, companyId, data);

        public static async Task Warn(this Logger logger,
            int masterAccountId,
            string type,
            string message,
            int accountId,
            int companyId,
            object data = null) => await Log(logger, LogLevel.Warn, masterAccountId, type, message, accountId, companyId, data);

        public static async Task Error(this Logger logger,
            int masterAccountId,
            string type,
            string message,
            int accountId,
            int companyId,
            object data = null) => await Log(logger, LogLevel.Error, masterAccountId, type, message, accountId, companyId, data);

        public static async Task Fatal(this Logger logger,
            int masterAccountId,
            string type,
            string message,
            int accountId,
            int companyId,
            object data = null) => await Log(logger, LogLevel.Fatal, masterAccountId, type, message, accountId, companyId, data);

        private static async Task Log(this Logger logger,
            LogLevel level,
            int masterAccountId,
            string type,
            string message,
            int accountId,
            int companyId,
            object data = null)
        {
            var e = new LogEventInfo();
            e.Level = level;
            e.Message = message;
            e.Properties["commitId"] = Core.GetCommitId();

            if (masterAccountId > 0)
            {
                e.Properties.Add("masterAccountId", masterAccountId);
                e.Properties.Add("masterAccountName", Extric.Towbook.Accounts.MasterAccountTypes.GetName(masterAccountId));
            }

            e.Properties["type"] = type;

            if (accountId > 0)
                e.Properties["accountId"] = accountId;

            if (companyId > 0)
            {
                e.Properties["companyId"] = companyId;

                var company = await Extric.Towbook.Company.Company.GetByIdAsync(companyId);
                if (company != null)
                    e.Properties["companyName"] = company.Name;
            }

            if (data != null)
            {
                if (data is string)
                    e.Properties["data"] = data;
                else if (data.GetType().IsArray)
                    e.Properties["array"] = data;
                else
                    e.Properties["object"] = data;
            }

            logger.Log(e);
        }

    }
}
