using Extric.Towbook.Accounts;
using Extric.Towbook.Integration;
using Extric.Towbook.Integration.MotorClubs;
using Extric.Towbook.Utility;
using Microsoft.Extensions.Hosting;
using NLog;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using static Microsoft.Azure.Amqp.Serialization.SerializableType;
using static MotorClubPaymentImportService.PaymentUtility;

namespace MotorClubPaymentImportService
{
    public partial class MotorClubPaymentImportService : IHostedService
    {
        private static Logger logger = LogManager.GetCurrentClassLogger();
        private readonly IHostApplicationLifetime _appLifetime;
        private readonly string[] _args;

        private static int runningThreadCount = 0;
        
        // in minutes
        private static int cycleInterval = 240;

        private int maxThreads = 16;
        private System.Timers.Timer executionTimer = new System.Timers.Timer();

        public MotorClubPaymentImportService(string[] args, IHostApplicationLifetime appLifetime)
        {
            _args = args;
            _appLifetime = appLifetime;
            _appLifetime.ApplicationStopped.Register(OnStopped);
            Extric.Towbook.CosmosDB.Get();
        }

        public string CompanyIds { get; set; }

        public Task StartAsync(CancellationToken cancellationToken)
        {
            logger.Info("MotorClubPaymentImportService Start Async ...");
            OnStart(_args);
            return Task.CompletedTask;
        }

        public Task StopAsync(CancellationToken cancellationToken)
        {
            logger.Info("MotorClubPaymentImportService Stop Async ...");
            OnStop();
            return Task.CompletedTask;
        }


        protected void OnStart(string[] args)
        {
            Console.WriteLine($"Service Started on {Environment.MachineName}");


            // parse the arguments to set max opened tread count and cycle interval
            for (int i = 0; i < args.Length; i++)
            {
                if (args[i] == "/threads" || args[i] == "/t")
                    maxThreads = Convert.ToInt32(args[i + 1]);
                if (args[i] == "-cycle" || args[i] == "/cycle")
                    cycleInterval = Convert.ToInt32(args[i + 1]);
                if (args[i] == "-companyIds" || args[i] == "/companyIds")
                    CompanyIds = args[i + 1];
            }

            Console.WriteLine("[main] -> Service fetch cycle defined: {0} minutes", cycleInterval);

            // starts the timer that will open the threads for the calculation fetch
            executionTimer.AutoReset = false;
            executionTimer.Interval = 5000;
            executionTimer.Elapsed += executionTimer_Elapsed;

            if (Program.IsInInteractiveMode)
                executionTimer_Elapsed(null, null);

            Console.WriteLine("MotorClubPaymentImportService is running as console application");
            executionTimer.Start();

        }

        protected void OnStop()
        {
            Console.WriteLine("Service Stopping on {0}", Environment.MachineName);
        }

        void executionTimer_Elapsed(object sender, System.Timers.ElapsedEventArgs e)
        {
            // every cycle, performs the calculation upon the database
            runningThreadCount += 1;

            try
            {

                int[] companyIds = null;
                if (CompanyIds != null)
                {

                    if (CompanyIds.Contains(","))
                        companyIds = CompanyIds.Split(',').Select(r => Convert.ToInt32(r)).ToArray();
                    else
                        companyIds = new int[] { Convert.ToInt32(CompanyIds) };
                }

                RunWeeklyPaymentImports(companyIds);
            }
            catch (Exception ex)
            {
                Console.WriteLine("Error running payment import: {0}", ex);
            }
            finally
            {
                runningThreadCount -= 1;
                executionTimer.Interval = cycleInterval * 60 * 1000;
                executionTimer.Start();
            }
        }

        public class PaymentAccount
        {
            public int AccountId;
            public int CompanyId;
            public int? MasterAccountId;
            public string Username;
            public string Password;
            public string ProviderId;
            public DateTime LastPaymentImportDate;
        }


        public class CompanyAccount
        {
            public int AccountId { get; set; }
            public int CompanyId { get; set; }
            public int MasterAccountId { get; set; }

        }

        public static void RunWeeklyPaymentImports(int[] companyIds = null)
        {
            var runList = new List<PaymentAccount>();

            
            var results = SqlMapper.Query<CompanyAccount>(
                @"SELECT AKV.AccountId, A.CompanyId, A.MasterAccountId FROM Integration.ProviderAccountKeyValues AKV INNER JOIN Accounts A on A.AccountId=AKV.AccountId WHERE 
                    AKV.ProviderAccountKeyId=(select ProviderAccountKeyId From towbook.integration.provideraccountkeys where name='ImportPayments' and IntegrationProviderId=4 AND Value='1')")
                .ToArray();

            if (companyIds != null)
                results = results.Where(r => companyIds.Contains(r.CompanyId)).ToArray();

            Parallel.ForEach(results,
                new ParallelOptions() { MaxDegreeOfParallelism = 8 },
                (dynamic r) =>
                {
                    int companyId = r.CompanyId;
                    int accountId = r.AccountId;
                    int? masterAccountId = r.MasterAccountId;

                    string username = AccountKeyValue.GetByAccount(companyId, accountId, Provider.Towbook.ProviderId, "McUsername").FirstOrDefault()?.Value;
                    string password = AccountKeyValue.GetByAccount(companyId, accountId, Provider.Towbook.ProviderId, "McPassword").FirstOrDefault()?.Value;
                    string providerId = AccountKeyValue.GetByAccount(companyId, accountId, Provider.Towbook.ProviderId, "ProviderId").FirstOrDefault()?.Value;

                    if (!string.IsNullOrWhiteSpace(username) && !string.IsNullOrWhiteSpace(password))
                    {
                        runList.Add(new PaymentAccount()
                        {
                            CompanyId = r.CompanyId,
                            AccountId = r.AccountId,
                            MasterAccountId = masterAccountId,
                            Username = username,
                            Password = password,
                            ProviderId = providerId,
                            //LastPaymentImportDate =  lastPayment
                        });
                    }
                });

            Console.WriteLine("*** Working with " + runList.Count + " accounts ***");
            var sw = Stopwatch.StartNew();

            foreach (var r in runList.GroupBy(o => o.MasterAccountId).OrderBy(o => o.Key))
            {
                Console.WriteLine("Importing: " + r.Count() + " accounts  for masterAccountId " + MasterAccountTypes.GetName(r.Key.Value));

                Parallel.ForEach(r,
                    new ParallelOptions() { MaxDegreeOfParallelism = 10 },
                    async (o) =>
                    {
                        var logInfo = new LogInfo($"PaymentImportService/{DateTime.Now.Ticks}", o.CompanyId, false, MasterAccountTypes.GetName(o.MasterAccountId ?? 0));
                        logInfo.AccountId = o.AccountId;

                        Console.WriteLine(MasterAccountTypes.GetName(o.MasterAccountId.Value) + ": Importing Payments for companyId " + o.CompanyId + ", accountId " + o.AccountId);
                        /*
                        if (o.MasterAccountId == MasterAccountTypes.Geico)
                            GeicoPaymentImporter.SyncGeicoPayments(logInfo, o.AccountId, o.Username, o.Password);
                        else
                            return;*/

                        DateTime lastPayment = GetLastPaymentImportDate(o.AccountId) ?? DateTime.Today.AddDays(-7);

                        try
                        {
                            if (o.MasterAccountId == MasterAccountTypes.Agero)
                                await AgeroPaymentImporter.SyncAgeroPayments(logInfo, o.AccountId, o.Username, o.Password);
                            else if (o.MasterAccountId == MasterAccountTypes.Geico)
                                await GeicoPaymentImporter.SyncGeicoPayments(logInfo, o.AccountId, o.Username, o.Password);
                            else if (o.MasterAccountId == MasterAccountTypes.Allstate)
                                await AllstatePaymentImporter.SyncAllstatePayments(logInfo, o.AccountId, o.Username, o.Password, o.ProviderId, lastPayment, DateTime.Today);
                            //else if (o.MasterAccountId == MasterAccountTypes.RoadAmerica)
                            //    await RoadAmericaPaymentImporter.SyncRoadAmericaPayments(logInfo, o.AccountId, o.Username, o.Password, lastPayment, DateTime.Today);
                            else if (o.MasterAccountId == MasterAccountTypes.Quest)
                                await QuestPaymentImporter.SyncQuestPayments(logInfo, o.AccountId, o.Username, o.Password, o.ProviderId, lastPayment, DateTime.Today);
                            else if (o.MasterAccountId == MasterAccountTypes.Nsd)
                                await NsdPaymentImporter.SyncNsdPayments(logInfo, o.AccountId, o.Username, o.Password, lastPayment, DateTime.Today);
                        }
                        catch (Exception e)
                        {
                            HandleException(logger, logInfo, e, "Payment import",
                                new { o.AccountId, o.Username, o.ProviderId });
                        }

                        Console.Title = "MCPayment Importer (Running: " + sw.ElapsedMilliseconds + "ms elapsed)";
                    });
            }

            Console.WriteLine("Done processing; took "+ sw.ElapsedMilliseconds + "ms");
            Console.Title = "MCPayment Importer (Finished: " + sw.ElapsedMilliseconds + "ms elapsed)";

            return;
        }

        private void OnStopped()
        {
            logger.Info("MotorClubPaymentImportService stopped successfully");
            LogManager.Shutdown();
            Console.WriteLine("MotorClubPaymentImportService Stopped finished");
        }

    }
}
