{"ConnectionStrings": {"Database": "", "Database.Azure": "", "Microsoft.ServiceBus": ""}, "Redis": {"Servers": "", "Credentials": "", "SentinelServiceName": "", "ConnectionPoolSize": 1}, "CosmosDb": {"Url": "", "AuthKey": "", "Database": ""}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "Loggly": {"ApplicationName": "squaresvc"}}