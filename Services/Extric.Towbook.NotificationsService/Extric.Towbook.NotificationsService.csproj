<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <NoWin32Manifest>true</NoWin32Manifest>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    <DockerfileContext>..\..</DockerfileContext>
    <ContainerDevelopmentMode>Regular</ContainerDevelopmentMode>
    <UserSecretsId>9d92ffcc-16da-450a-b1cb-617b1549ccc9</UserSecretsId>
  </PropertyGroup>
  <ItemGroup>
    <None Remove="appsettings.json" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="appsettings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <None Update="NLog.config" CopyToOutputDirectory="Always" />
  </ItemGroup>
  <ItemGroup>
	  <None Include="../../openssl.cnf">
	    <CopyToOutputDirectory>Always</CopyToOutputDirectory>
	  </None>
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Core.System.Configuration.Install" Version="1.1.0" />
    <PackageReference Include="Microsoft.CSharp" Version="4.7.0" />
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.21.0" />
    <PackageReference Include="NLog.Database" Version="5.3.3" />
    <PackageReference Include="System.Data.Common" Version="4.3.0" />
    <PackageReference Include="System.Data.DataSetExtensions" Version="4.5.0" />
    <PackageReference Include="System.Data.OracleClient" Version="1.0.8" />
    <PackageReference Include="System.Data.SqlClient" Version="4.8.6" />
    <PackageReference Include="System.IO" Version="4.3.0" />
    <PackageReference Include="System.IO.Compression" Version="4.3.0" />
    <!--<PackageReference Include="System.Management" Version="7.0.2" />-->
	<PackageReference Include="System.Management" Version="8.0.0" />
    <PackageReference Include="System.Net.Http" Version="4.3.4" />
    <PackageReference Include="System.Net.Primitives" Version="4.3.1" />
    <PackageReference Include="System.Net.Requests" Version="4.3.0" />
    <PackageReference Include="System.Private.DataContractSerialization" Version="4.3.0" />
    <PackageReference Include="System.Runtime.Serialization.Json" Version="4.3.0" />
    <PackageReference Include="System.Runtime.Serialization.Primitives" Version="4.3.0" />
    <PackageReference Include="System.Security.AccessControl" Version="6.0.1" />
    <PackageReference Include="System.Security.Principal.Windows" Version="5.0.0" />
	<PackageReference Include="System.ServiceProcess.ServiceController" Version="8.0.0" />
    <PackageReference Include="System.Xml.ReaderWriter" Version="4.3.1" />
    <PackageReference Include="System.Xml.XDocument" Version="4.3.0" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\Extric.Towbook\Extric.Towbook.csproj" />
  </ItemGroup>
</Project>