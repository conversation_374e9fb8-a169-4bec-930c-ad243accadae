{"ConnectionStrings": {"Database": "", "Database.Azure": "", "Microsoft.ServiceBus": ""}, "EventNotificationDb": {"Url": "", "AuthKey": "", "Database": "", "Collection": ""}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "Loggly": {"CustomerToken": "", "ApplicationName": "notificationsservice", "Transport": {"EndpointHostname": "logs-01.loggly.com", "EndpointPort": 6514, "LogTransport": "SyslogSecure"}, "SimpleTagValue": "mojix-dev"}, "ExecutionInterval": 5000, "fromEmailAddress": "<EMAIL>", "nameToDisplay": "Towbook Notification Service"}