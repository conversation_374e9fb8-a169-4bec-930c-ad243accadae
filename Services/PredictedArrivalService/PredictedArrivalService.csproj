<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    <DockerfileContext>..\..</DockerfileContext>
    <ContainerDevelopmentMode>Regular</ContainerDevelopmentMode>
    <UserSecretsId>9d92ffcc-16da-450a-b1cb-617b1549ccc9</UserSecretsId>
  </PropertyGroup>
  <ItemGroup>
    <None Remove="appsettings.json" />
    <None Remove="NLog.config" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="appsettings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NLog.config" CopyToOutputDirectory="Allways" />
  </ItemGroup>
  <ItemGroup>
    <None Include="../../openssl.cnf">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Azure.Core" Version="1.42.0" />
    <PackageReference Include="Azure.Core.Amqp" Version="1.3.1" />
    <PackageReference Include="Azure.Messaging.ServiceBus" Version="7.18.1" />
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.21.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="System.Data.OracleClient" Version="1.0.8" />
    <PackageReference Include="System.Data.SqlClient" Version="4.8.6" />
    <PackageReference Include="System.ServiceProcess.ServiceController" Version="8.0.0" />
    <PackageReference Include="System.Data.DataSetExtensions" Version="4.5.0" />
    <PackageReference Include="System.IO.Compression" Version="4.3.0" />
    <PackageReference Include="EnterpriseLibrary.TransientFaultHandling.Core" Version="3.1.0" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\Extric.Towbook.Integration.MotorClubs\Extric.Towbook.Integration.MotorClubs.csproj" />
    <ProjectReference Include="..\..\Extric.Towbook\Extric.Towbook.csproj" />
    <ProjectReference Include="..\..\Roadside\Extric.Roadside\Extric.Roadside.csproj" />
  </ItemGroup>
</Project>