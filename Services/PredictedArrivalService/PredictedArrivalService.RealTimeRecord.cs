using System;
using Newtonsoft.Json;

namespace Extric.Towbook.Services.PredictedArrivalService;
public sealed partial class PredictedArrivalService
{
    public sealed class RealTimeRecord
    {
        public int? CallId { get; set; }
        public int? CallStatus { get; set; }

        public int? DriverId { get; set; }
        public int? UserId { get; set; }

        [JsonIgnore]
        public bool SecondCall { get; set; }

        public decimal? CallLatitude { get; set; }
        public decimal? CallLongitude { get; set; }

        public decimal? UserLatitude { get; set; }
        public decimal? UserLongitude { get; set; }

        public DateTime? UserTimestamp { get; set; }

        public DateTime? CallEta { get; set; }
        public DateTime? DriverEta { get; set; }

        public DateTime LastUpdated { get; set; }
        public decimal? DriverDistanceRemaining { get; set; }
    }
}
