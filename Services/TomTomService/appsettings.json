{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "Loggly": {"CustomerToken": "", "ApplicationName": "tomtomservice", "Transport": {"EndpointHostname": "logs-01.loggly.com", "EndpointPort": 6514, "LogTransport": "SyslogSecure"}, "SimpleTagValue": "mojix-dev"}, "Core.Database.ConnectionString": "Data Source=localhost;Initial Catalog=TowbookDev;User id=sa;Password=*****", "Microsoft.ServiceBus.ConnectionString": "Endpoint=sb://dev-towbook.servicebus.windows.net/;SharedSecretIssuer=owner;SharedSecretValue=w5YhSkFQk6/OuSca9KmkUSlH48RxkjOusCq/ihStXgw=", "Core.Cache.Redis.Servers": "127.0.0.1"}