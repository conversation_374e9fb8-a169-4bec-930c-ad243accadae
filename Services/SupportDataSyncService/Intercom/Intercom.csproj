<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{A2269678-CBCC-4444-AD9D-5D82ABC52800}</ProjectGuid>
    <OutputType>Library</OutputType>
    <RootNamespace>Library</RootNamespace>
    <AssemblyName>Library</AssemblyName>
    <TargetFrameworkVersion>v4.5</TargetFrameworkVersion>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug</OutputPath>
    <DefineConstants>DEBUG;</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <ConsolePause>false</ConsolePause>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>full</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release</OutputPath>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <ConsolePause>false</ConsolePause>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="RestSharp">
      <HintPath>..\packages\RestSharp.105.2.3\lib\net45\RestSharp.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json">
      <HintPath>..\packages\Newtonsoft.Json.8.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Core\Model.cs" />
    <Compile Include="Core\Authentication.cs" />
    <Compile Include="Core\Models.cs" />
    <Compile Include="Data\User.cs" />
    <Compile Include="Data\Avatar.cs" />
    <Compile Include="Data\Companies.cs" />
    <Compile Include="Data\Company.cs" />
    <Compile Include="Data\LocationData.cs" />
    <Compile Include="Data\Pages.cs" />
    <Compile Include="Data\Segment.cs" />
    <Compile Include="Data\Segments.cs" />
    <Compile Include="Data\SocialProfile.cs" />
    <Compile Include="Data\SocialProfiles.cs" />
    <Compile Include="Data\Tag.cs" />
    <Compile Include="Data\Tags.cs" />
    <Compile Include="Data\Users.cs" />
    <Compile Include="Data\Plan.cs" />
    <Compile Include="Exceptions\IntercomException.cs" />
    <Compile Include="Core\Constants.cs" />
    <Compile Include="Data\Error.cs" />
    <Compile Include="Data\Errors.cs" />
    <Compile Include="Enums\OrderBy.cs" />
    <Compile Include="Clients\CompanyClient.cs" />
    <Compile Include="Clients\EventClient.cs" />
    <Compile Include="Data\Event.cs" />
    <Compile Include="Data\Events.cs" />
    <Compile Include="Data\Metadata.cs" />
    <Compile Include="Data\Contact.cs" />
    <Compile Include="Data\Contacts.cs" />
    <Compile Include="Data\Attachment.cs" />
    <Compile Include="Data\Assignee.cs" />
    <Compile Include="Data\Author.cs" />
    <Compile Include="Clients\ContactsClient.cs" />
    <Compile Include="Clients\AdminsClient.cs" />
    <Compile Include="Data\Admin.cs" />
    <Compile Include="Data\Admins.cs" />
    <Compile Include="Clients\SegmentsClient.cs" />
    <Compile Include="Clients\TagsClient.cs" />
    <Compile Include="Clients\CountsClient.cs" />
    <Compile Include="Data\Note.cs" />
    <Compile Include="Clients\NotesClient.cs" />
    <Compile Include="Data\Notes.cs" />
    <Compile Include="Core\Client.cs" />
    <Compile Include="Core\ClientConfiguration.cs" />
    <Compile Include="Core\ClientResponse.cs" />
    <Compile Include="Converters\AttributeConverters\ListJsonConverter.cs" />
    <Compile Include="Converters\ClassConverters\ConversationAdminCountJsonConverter.cs" />
    <Compile Include="Converters\AttributeConverters\MetadataJsonConverter.cs" />
    <Compile Include="Converters\ClassConverters\CompanyCountJsonConverter.cs" />
    <Compile Include="Converters\ClassConverters\UserCountJsonConverter.cs" />
    <Compile Include="Converters\ClassConverters\ConversationAppCountJsonConverter.cs" />
    <Compile Include="Data\AppCount.cs" />
    <Compile Include="Data\CompanySegmentCount.cs" />
    <Compile Include="Data\CompanyTagCount.cs" />
    <Compile Include="Data\CompanyUserCount.cs" />
    <Compile Include="Data\ConversationAdminCount.cs" />
    <Compile Include="Data\ConversationAppCount.cs" />
    <Compile Include="Data\Count.cs" />
    <Compile Include="Data\SegmentCount.cs" />
    <Compile Include="Data\TagCount.cs" />
    <Compile Include="Data\UserCount.cs" />
    <Compile Include="Data\UserSegmentCount.cs" />
    <Compile Include="Data\UserTagCount.cs" />
    <Compile Include="Data\AdminConversationMessage.cs" />
    <Compile Include="Data\AdminConversationReply.cs" />
    <Compile Include="Data\Conversation.cs" />
    <Compile Include="Data\ConversationMessage.cs" />
    <Compile Include="Data\ConversationPart.cs" />
    <Compile Include="Data\Conversations.cs" />
    <Compile Include="Data\Message.cs" />
    <Compile Include="Data\Reply.cs" />
    <Compile Include="Data\UserConversationMessage.cs" />
    <Compile Include="Data\UserConversationReply.cs" />
    <Compile Include="Clients\ConversationsClient.cs" />
    <Compile Include="Clients\UserConversationsClient.cs" />
    <Compile Include="Clients\AdminConversationsClient.cs" />
    <Compile Include="Clients\UsersClient.cs" />
    <Compile Include="Converters\ClassConverters\AppCountJsonConverter.cs" />
    <Compile Include="Exceptions\ApiException.cs" />
    <Compile Include="Exceptions\JsonConverterException.cs" />
    <Compile Include="Clients\VisitorsClient.cs" />
    <Compile Include="Data\Visitor.cs" />
  </ItemGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <ItemGroup>
    <None Include="packages.config" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Core\" />
    <Folder Include="Data\" />
    <Folder Include="Exceptions\" />
    <Folder Include="Clients\" />
    <Folder Include="Enums\" />
    <Folder Include="Converters\" />
    <Folder Include="Converters\ClassConverters\" />
    <Folder Include="Converters\AttributeConverters\" />
  </ItemGroup>
</Project>