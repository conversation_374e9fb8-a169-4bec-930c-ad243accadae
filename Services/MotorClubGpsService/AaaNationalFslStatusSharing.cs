using Extric.Towbook.Accounts;
using System.Data.SqlClient;
using System.Diagnostics;
using System.Threading;
using System;
using NLog;
using System.Threading.Tasks;
using Extric.Towbook.Utility;
using static MotorClubGpsService.MotorClubGpsSharing;
using System.Collections.ObjectModel;
using Extric.Towbook.Integrations.MotorClubs.Aaa;
using System.Linq;
using Extric.Towbook.Integrations.MotorClubs.Aaa.NationalFsl;

namespace MotorClubGpsService;

internal class AaaNationalFslStatusSharing
{
    private static readonly Logger logger = LogManager.GetCurrentClassLogger();

    public struct AcgBreadcrumbModel
    {
        public int DispatchEntryId { get; set; }
        public string DispatchId { get; set; }
        public int AaaContractorId { get; set; }
        public string ServiceResourceId { get; set; }
        public string VehicleId { get; set; }
        public decimal Latitude { get; set; }
        public decimal Longitude { get; set; }
        public DateTime Timestamp { get; set; }
    }

    private static string NationalFslEnvironmentIdToString(int id)
    {
        if (id == 1)
        {
            return "DEV";
        }
        else if (id == 2)
        {
            return "UAT";
        }
        else if (id == 3)
        {
            return "PROD";
        }
        else if (id == 4)
        {
            return "PROD";
        }
        return "DEV";
    }

    public static async Task AcgGpsShare(CancellationToken cancellationToken)
    {
        const string sql = "SELECT * FROM mcdispatch.[vwAaaNationalFslGpsBreadcrumbExport]";

        Collection<AcgBreadcrumbModel> records = null;

        while (!cancellationToken.IsCancellationRequested)
        {
            var lastRan = DateTime.Now;
            var sw = Stopwatch.StartNew();
            Console.WriteLine(".");
            try
            {
                records = SqlMapper.Query<AcgBreadcrumbModel>(sql)
                    .ToCollection();

                foreach (var x in records)
                {
                    var ac = AaaContractor.GetById(x.AaaContractorId);
                    
                    var acg = new AaaNationalFslRestClient(NationalFslEnvironmentIdToString(ac.EnvironmentId), "0.0.0.0", ac.CompanyId, 0, 0, x.DispatchEntryId, x.DispatchId,
                        ac);
                    
                    await acg.Breadcrumb(x.ServiceResourceId,
                        new AaaNationalFslRestClient.BreadrumbPayload
                        {
                            VehicleId = x.VehicleId,
                            Latitude = x.Latitude,
                            Longitude = x.Longitude
                        });
                }

                await UpdateGlobalStatusCounter(MasterAccountTypes.AaaNationalFsl,
                    records.Count(), sw.ElapsedMilliseconds, true);

                logger.Log(LogLevel.Info, "Gps-Sharing/AAA/NationalFsl: sent " + records.Count() + " in " + sw.ElapsedMilliseconds);
                records = null;

            }
            catch (SqlException se)
            {
                Console.WriteLine(se.ToString());
                logger.Log(LogLevel.Info, "Gps-Sharing/AAA/NationalFsl: SQL ERROR " + se.ToString() + " in " + sw.ElapsedMilliseconds);
            }
            catch (Exception e)
            {
                logger.Log(LogLevel.Info, "Gps-Sharing/AAA/NationalFsl: ERROR " + e.ToString() + " in " + sw.ElapsedMilliseconds);
            }

            Console.WriteLine("");
            while (!cancellationToken.IsCancellationRequested && DateTime.Now < lastRan.AddSeconds(60))
            {
                Console.Write(".");
                Thread.Sleep(5000);
            }
            Console.WriteLine("---");
        }
    }


    public static async Task AcgGpsShareContinuous(CancellationToken cancellationToken)
    {
        const string sql = "SELECT * FROM mcdispatch.[vwAaaNationalFslGpsBreadcrumbExportUnassigned]";

        Collection<AcgBreadcrumbModel> records = null;

        while (!cancellationToken.IsCancellationRequested)
        {
            var lastRan = DateTime.Now;
            var sw = Stopwatch.StartNew();
            
            Console.WriteLine(".");
            try
            {
                records = SqlMapper.Query<AcgBreadcrumbModel>(sql)
                    .ToCollection();

                foreach (var x in records)
                {
                    var ac = AaaContractor.GetById(x.AaaContractorId);
                    if (ac == null)
                        continue;

                    var acg = new AaaNationalFslRestClient(NationalFslEnvironmentIdToString(ac.EnvironmentId), "0.0.0.0", ac.CompanyId, 0, 0, x.DispatchEntryId, x.DispatchId,
                        ac);

                    await acg.Breadcrumb(x.ServiceResourceId,
                        new AaaNationalFslRestClient.BreadrumbPayload
                        {
                            VehicleId = x.VehicleId,
                            Latitude = x.Latitude,
                            Longitude = x.Longitude
                        });
                }

                await UpdateGlobalStatusCounter(MasterAccountTypes.AaaNationalFsl,
                    records.Count(), sw.ElapsedMilliseconds, true);

                logger.Log(LogLevel.Info, "Gps-Sharing/AAA/NationalFsl: sent " + records.Count() + " in " + sw.ElapsedMilliseconds);
                records = null;

            }
            catch (SqlException se)
            {
                Console.WriteLine(se.ToString());
                logger.Log(LogLevel.Info, "Gps-Sharing/AAA/NationalFsl: SQL ERROR " + se.ToString() + " in " + sw.ElapsedMilliseconds);
            }
            catch (Exception e)
            {
                logger.Log(LogLevel.Info, "Gps-Sharing/AAA/NationalFsl: ERROR " + e.ToString() + " in " + sw.ElapsedMilliseconds);
            }

            Console.WriteLine("Waiting 5 minutes to run again. " + DateTime.Now.ToString());
            while (!cancellationToken.IsCancellationRequested && DateTime.Now < lastRan.AddSeconds(60*5))
            {
                Console.Write(".");
                Thread.Sleep(1000);
            }
            Console.WriteLine("---");
        }
    }

}
