using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using NLog;

namespace MotorClubGpsService;

public class MetricsCalculationTask
{
    public IEnumerable<int> AccountIds { get; set; }
    public int CycleInterval { get; set; }
    public DateTime LastFetchDate { get; set; }
}

partial class MotorClubGpsService : BackgroundService
{
    private static readonly Logger logger = LogManager.GetCurrentClassLogger();

    private readonly IHostApplicationLifetime _hostApplicationLifetime;
    private readonly ServiceOptions _options;
    private readonly CancellationTokenSource _cancellationTokenSource;

    private List<Task> _tasks;

    public MotorClubGpsService(IHostApplicationLifetime hostApplicationLifetime, IOptions<ServiceOptions> options)
    {
        _hostApplicationLifetime = hostApplicationLifetime;
        _hostApplicationLifetime.ApplicationStopped.Register(OnStopped);
        _options = options.Value;

        _cancellationTokenSource = new CancellationTokenSource();
        NewRelic.Api.Agent.NewRelic.StartAgent();
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        Console.WriteLine("Service is running as console application"); // from Program.cs 'master' branch
        logger.Info("MotorClubGpsService Starting");
        logger.Log(LogLevel.Info, "MotorClubGpsService Started on {0}", Environment.MachineName);
        logger.Log(LogLevel.Info, "> for MotorClub: {0}", string.IsNullOrEmpty(_options.MotorClub) ? "DEFAULT" : _options.MotorClub);

        // Start multiple asynchronous tasks
        _tasks = MotorClubGpsSharing.ShareGps(_options, _cancellationTokenSource.Token);

        // Wait for the tasks to complete or the stopping token to be triggered
        await Task.WhenAny(Task.WhenAll(_tasks), Task.Delay(Timeout.Infinite, stoppingToken));

        logger.Info("MotorClubGpsService is finishing its execution.");

        // Stop the application
        _hostApplicationLifetime.StopApplication();
    }

    public override async Task StopAsync(CancellationToken cancellationToken)
    {
        logger.Log(LogLevel.Info, "MotorClubGpsService Stopping on {0}", Environment.MachineName);

        await OnStop();

        logger.Info("MotorClubGpsService stopped successfully");

        await base.StopAsync(cancellationToken);
    }

    protected async Task OnStop()
    {
        logger.Info("MotorClubGpsService is stopping.");

        if (MotorClubGpsSharing.ForceExit)
        {
            logger.Log(LogLevel.Info, "Exiting because ForceExit == true");

            _hostApplicationLifetime.StopApplication();
            return;
        }

        _cancellationTokenSource.Cancel();

        // Wait for all tasks to complete
        await Task.WhenAll(_tasks);
    }

    private void OnStopped()
    {
        logger.Info("MotorClubGpsService Stopped finished");
        LogManager.Shutdown();
        Console.WriteLine("MotorClubGpsService Stopped finished");
    }

    override public void Dispose()
    {
        _cancellationTokenSource.Dispose();
        base.Dispose();
    }
}
