using Newtonsoft.Json;
using System;

namespace MotorClubGpsService
{

    public class LogDataCount
    {
        [JsonProperty("count")]
        public long Count { get; set; }
        [JsonProperty("milliseconds")]
        public long Milliseconds { get; set; }
        public LogDataCount(long count, long milliseconds)
        {
            Count = count;
            Milliseconds = milliseconds;
        }
    }

    public class LogDataCountDate : LogDataCount
    {
        [JsonProperty("delta")]
        public double Delta { get; set; }
        [JsonProperty("newerThan")]
        public DateTime LastTimestamp { get; set; }

        public LogDataCountDate(
            long count,
            long milliseconds,
            DateTime last,
            DateTime first) : base(count, milliseconds)
        {
            LastTimestamp = last;
            Delta = Math.Round((last - first).TotalSeconds,2);
        }
    }

}
