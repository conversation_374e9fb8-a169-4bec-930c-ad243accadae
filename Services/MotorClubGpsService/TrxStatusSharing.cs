using Extric.Towbook.Integrations.MotorClubs.Trx;
using Extric.Towbook.Utility;
using NLog;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Diagnostics;
using System.Threading;
using Extric.Towbook;
using Extric.Towbook.Accounts;
using Extric.Towbook.Integration.MotorClubs;
using System.Threading.Tasks;
using System.Linq;

namespace MotorClubGpsService
{
    public class TrxStatusSharing
    {
        private static readonly Logger logger = LogManager.GetCurrentClassLogger();
        
        public static async Task TrxShare(CancellationToken cancellationToken)
        {
            while (!cancellationToken.IsCancellationRequested)
            {
                DateTime lastRan = DateTime.Now;

                try
                {
                    var sw = Stopwatch.StartNew();

                    var rowsToSend = SqlMapper.Query<dynamic>("select * from MCDispatch.[vwTrxGpsBreadcrumbExport] with (nolock)").ToCollection();

                    foreach (var row in rowsToSend.Batch(50))
                    {
                        foreach (var r in row)
                        {
                            string firstName = "", lastName = "";
                            if (r.DriverName.IndexOf(" ") > 0)
                            {
                                firstName = JsonExtensions.Truncate(r.DriverName.Substring(0, r.DriverName.IndexOf(" ")).Trim(),50);
                                lastName = JsonExtensions.Truncate(r.DriverName.Substring(r.DriverName.IndexOf(" ") + 1).Trim(), 50);
                            }
                            
                            var trx = new TrxRestClient(MasterAccountTypes.AlliedDispatch, "PROD", "Internal",
                                (int)r.CompanyId, 0, (int)r.CallRequestId, (int)r.DispatchEntryId, (string)r.DispatchId);

                            trx.StatusUpdate(new TrxRestClient.DispatchStatusUpdateModel()
                            {
                                Type = "breadcrumb",
                                Status = TrxRestClient.TowbookStatusIdToName((int)r.StatusId),
                                ContractorId = r.ContractorId,
                                DispatchId = r.DispatchId,
                                Driver = new TrxRestClient.DriverModel()
                                {
                                    Latitude = r.Latitude,
                                    Longitude = r.Longitude,
                                    FirstName = firstName,
                                    LastName = lastName
                                }
                            });
                        }

                        try
                        {
                            Console.WriteLine(DateTime.Now.ToString() + ": Pushed " + row.Count() + " gps events");
                        }
                        catch (Exception e)
                        {
                            Console.WriteLine(e.ToString());
                        }
                    }

                    logger.Info(MasterAccountTypes.Nsd, "GpsSharing", "Finished sending breadcrumbs.",
                        data: new LogDataCount(rowsToSend.Count, sw.ElapsedMilliseconds));

                    await MotorClubGpsSharing.UpdateGlobalStatusCounter(MasterAccountTypes.Nsd, rowsToSend.Count, sw.ElapsedMilliseconds, true);
                }
                catch (SqlException se)
                {
                    Console.WriteLine(se.ToString());
                }
                catch (Exception e)
                {
                    Console.WriteLine(e.ToString());

                }
                Console.WriteLine("");
                while (!cancellationToken.IsCancellationRequested && DateTime.Now < lastRan.AddSeconds(60))
                {
                    Console.Write(".");
                    Thread.Sleep(3000);
                }
                Console.WriteLine("---");
            }

            logger.Info("TrxShare finished");
        }

    }
}
