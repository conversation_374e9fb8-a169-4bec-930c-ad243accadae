<Project Sdk="Microsoft.NET.Sdk">
	<PropertyGroup>
		<OutputType>Exe</OutputType>
		<TargetFramework>net8.0</TargetFramework>
		<DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
		<DockerfileContext>..\..</DockerfileContext>
		<ContainerDevelopmentMode>Regular</ContainerDevelopmentMode>
		<UserSecretsId>9d92ffcc-16da-450a-b1cb-617b1549ccc9</UserSecretsId>
	</PropertyGroup>
	<ItemGroup>
		<None Remove="appsettings.json" />
	</ItemGroup>
	<ItemGroup>
		<Content Include="appsettings.json">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
	</ItemGroup>

	<ItemGroup>
	  <None Include="App.config">
	    <CopyToOutputDirectory>Always</CopyToOutputDirectory>
	  </None>
	</ItemGroup>

	<ItemGroup>
	  <None Include="../../openssl.cnf">
	    <CopyToOutputDirectory>Always</CopyToOutputDirectory>
	  </None>
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.CSharp" Version="4.7.0" />
		<PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.21.0" />
		<PackageReference Include="System.Buffers" Version="4.5.1" />
		<PackageReference Include="System.ComponentModel.Composition" Version="8.0.0" />
		<PackageReference Include="System.Memory" Version="4.5.5" />
		<PackageReference Include="System.Numerics.Vectors" Version="4.5.0" />
		<PackageReference Include="System.ServiceProcess.ServiceController" Version="8.0.0" />
		<PackageReference Include="System.Threading.Tasks.Extensions" Version="4.5.4" />
	</ItemGroup>
	<ItemGroup>
		<None Update="NLog.config" CopyToOutputDirectory="Always" />
	</ItemGroup>
	<ItemGroup>
		<ProjectReference Include="..\..\Extric.Towbook.Integration.MotorClubs\Extric.Towbook.Integration.MotorClubs.csproj" />
		<ProjectReference Include="..\..\Integrations\MotorClubs\Agero\Agero.csproj" />
		<ProjectReference Include="..\..\Integrations\MotorClubs\Fleetnet\Fleetnet.csproj" />
	</ItemGroup>
</Project>