apiVersion: apps/v1
kind: Deployment
metadata:
  name: mcbillingservice
spec:
  selector:
    matchLabels:
      app: mcbillingservice-pod
  template:
    metadata:
      labels:
        app: mcbillingservice-pod
    spec:
      containers:
      - name: mcbillingservice-container
        image: towbookapiregistry.azurecr.io/mcbillingservice:latest
        resources:
          requests:
            memory: "64Mi"
            cpu: "50m"
          limits:
            memory: "250Mi"
            cpu: "100m"
        env:
        - name: ASPNETCORE_ENVIRONMENT
          value: "Development"
        - name: ConnectionStrings__Database
          valueFrom:
            secretKeyRef:
              name: kvtowbook-secrets
              key: ConnectionStrings-Database
        - name: ConnectionStrings__Database.Azure
          valueFrom:
            secretKeyRef:
              name: kvtowbook-secrets
              key: ConnectionStrings-DatabaseAzure
        - name: ConnectionStrings__Microsoft.ServiceBus
          valueFrom:
            secretKeyRef:
              name: kvtowbook-secrets
              key: ConnectionStrings-MicrosoftServiceBus
        - name: Redis__Credentials
          valueFrom:
            secretKeyRef:
              name: kvtowbook-secrets
              key: Redis-Credentials
        volumeMounts:
          - name:  secrets-store
            mountPath:  "mnt/secrets-store"
            readOnly: true
      nodeSelector:
        selector: nplin
      volumes:
        - name: secrets-store
          csi:
            driver: secrets-store.csi.k8s.io
            readOnly: true
            volumeAttributes:
              secretProviderClass: "azure-kvtowbook-msi"