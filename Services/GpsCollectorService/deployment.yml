apiVersion: apps/v1
kind: Deployment
metadata:
  name: gpscollectorservice
spec:
  selector:
    matchLabels:
      app: gpscollectorservice-pod
  template:
    metadata:
      labels:
        app: gpscollectorservice-pod
    spec:
      containers:
      - name: gpscollectorservice-container
        image: towbookapiregistry.azurecr.io/gpscollectorservice:latest
        resources:
          requests:
            memory: "64Mi"
            cpu: "50m"
          limits:
            memory: "250Mi"
            cpu: "100m"
        env:
        - name: ASPNETCORE_ENVIRONMENT
          value: "Development"
        - name: ConnectionStrings__Database
          valueFrom:
            secretKeyRef:
              name: kvtowbook-secrets
              key: ConnectionStrings-Database
        - name: ConnectionStrings__Database.Azure
          valueFrom:
            secretKeyRef:
              name: kvtowbook-secrets
              key: ConnectionStrings-DatabaseAzure
        - name: Redis__Credentials
          valueFrom:
            secretKeyRef:
              name: kvtowbook-secrets
              key: Redis-Credentials
      nodeSelector:
        selector: nplin