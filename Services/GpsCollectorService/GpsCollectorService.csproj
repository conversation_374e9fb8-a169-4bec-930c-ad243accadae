<Project Sdk="Microsoft.NET.Sdk">
	<PropertyGroup>
		<OutputType>Exe</OutputType>
		<TargetFramework>net8.0</TargetFramework>
		<DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
		<DockerfileContext>..\..</DockerfileContext>
        <ContainerDevelopmentMode>Regular</ContainerDevelopmentMode>
		<UserSecretsId>9d92ffcc-16da-450a-b1cb-617b1549ccc9</UserSecretsId>
	</PropertyGroup>
	<ItemGroup>
	  <None Remove="appsettings.json" />
	</ItemGroup>
	<ItemGroup>
	  <Content Include="appsettings.json">
	    <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	    <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
	    <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
	  </Content>
	</ItemGroup>

	<ItemGroup>
	  <None Include="../../openssl.cnf">
	    <CopyToOutputDirectory>Always</CopyToOutputDirectory>
	  </None>
	</ItemGroup>


	<ItemGroup>
		<PackageReference Include="Humanizer" Version="2.14.1" />
		<PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.21.0" />
		<PackageReference Include="System.Buffers" Version="4.5.1" />
		<PackageReference Include="System.Memory" Version="4.5.5" />
		<PackageReference Include="System.Numerics.Vectors" Version="4.5.0" />
		<PackageReference Include="System.Reflection.Emit.ILGeneration" Version="4.7.0" />
		<PackageReference Include="System.Reflection.Emit.Lightweight" Version="4.7.0" />
		<PackageReference Include="System.Threading.Tasks.Extensions" Version="4.5.4" />
	</ItemGroup>
	<ItemGroup>
	  <ProjectReference Include="..\..\Integrations\GPS\Extric.Towbook.Integrations.DriverLocate\Extric.Towbook.Integrations.DriverLocate.csproj" />
	  <ProjectReference Include="..\..\Integrations\GPS\Extric.Towbook.Integrations.FleetComplete\Extric.Towbook.Integrations.FleetComplete.csproj" />
	  <ProjectReference Include="..\..\Integrations\GPS\Extric.Towbook.Integrations.Fleetmatics\Extric.Towbook.Integrations.Fleetmatics.csproj" />
	  <ProjectReference Include="..\..\Integrations\GPS\Extric.Towbook.Integrations.GeoTab\Extric.Towbook.Integrations.GeoTab.csproj" />
	  <ProjectReference Include="..\..\Integrations\GPS\Extric.Towbook.Integrations.Gps.Azuga\Extric.Towbook.Integrations.Gps.Azuga.csproj" />
	  <ProjectReference Include="..\..\Integrations\GPS\Extric.Towbook.Integrations.Samsara\Extric.Towbook.Integrations.Samsara.csproj" />
	  <ProjectReference Include="..\..\Integrations\GPS\Extric.Towbook.Integrations.TomTom\Extric.Towbook.Integrations.TomTom.csproj" />
	  <ProjectReference Include="..\..\Integrations\GPS\Extric.Towbook.Integrations.USFleetTracking\Extric.Towbook.Integrations.USFleetTracking.csproj" />
	</ItemGroup>
	<ItemGroup>
	  <None Update="NLog.config">
	    <CopyToOutputDirectory>Always</CopyToOutputDirectory>
	  </None>
	</ItemGroup>
</Project>