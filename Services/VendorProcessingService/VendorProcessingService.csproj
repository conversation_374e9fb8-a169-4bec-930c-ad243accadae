<Project Sdk="Microsoft.NET.Sdk">
	<PropertyGroup>
		<OutputType>Exe</OutputType>
		<TargetFramework>net8.0</TargetFramework>
		<NoWin32Manifest>true</NoWin32Manifest>
		<DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
		<DockerfileContext>..\..</DockerfileContext>
		<ContainerDevelopmentMode>Regular</ContainerDevelopmentMode>
    	<UserSecretsId>9d92ffcc-16da-450a-b1cb-617b1549ccc9</UserSecretsId>
	</PropertyGroup>
	<ItemGroup>
	  <None Remove="appsettings.json" />
	</ItemGroup>
	<ItemGroup>
	  <Content Include="appsettings.json">
	    <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	  </Content>
	</ItemGroup>
	<ItemGroup>
		<None Update="App.config" CopyToOutputDirectory="Always" />
		<None Update="NLog.config" CopyToOutputDirectory="Always" />
		<None Update="Properties\Settings.settings">
		  <Generator>SettingsSingleFileGenerator</Generator>
		  <LastGenOutput>Settings.Designer.cs</LastGenOutput>
		</None>
	</ItemGroup>
	<ItemGroup>
		<None Include="../../openssl.cnf">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
        <None Include="libpoppler-7.dll">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Include="pdftotext.exe">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
	</ItemGroup>
	<ItemGroup>
		<PackageReference Include="Azure.Core" Version="1.42.0" />
		<PackageReference Include="Azure.Core.Amqp" Version="1.3.1" />
		<PackageReference Include="Azure.Messaging.ServiceBus" Version="7.18.1" />
		<PackageReference Include="EPPlus" Version="7.3.0" />
		<PackageReference Include="Microsoft.Azure.Amqp" Version="2.6.9" />
		<PackageReference Include="Microsoft.Azure.Cosmos" Version="3.42.0" />
		<PackageReference Include="Microsoft.Bcl.AsyncInterfaces" Version="9.0.0" />
		<PackageReference Include="Microsoft.IdentityModel.JsonWebTokens" Version="8.0.2" />
		<PackageReference Include="Microsoft.IdentityModel.Logging" Version="8.0.2" />
		<PackageReference Include="Microsoft.IdentityModel.Tokens" Version="8.0.2" />
		<PackageReference Include="Microsoft.NETCore.Platforms" Version="7.0.4" />
		<PackageReference Include="Microsoft.Rest.ClientRuntime" Version="2.3.24" />
		<PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.21.0" />
		<PackageReference Include="Microsoft.Win32.Primitives" Version="4.3.0" />
		<PackageReference Include="NETStandard.Library" Version="2.0.3" />
		<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
		<PackageReference Include="RestSharp" Version="111.4.1" />
		<PackageReference Include="System.AppContext" Version="4.3.0" />
		<PackageReference Include="System.Buffers" Version="4.5.1" />
		<PackageReference Include="System.Collections" Version="4.3.0" />
		<PackageReference Include="System.Collections.Concurrent" Version="4.3.0" />
		<PackageReference Include="System.Collections.Immutable" Version="8.0.0" />
		<PackageReference Include="System.Collections.NonGeneric" Version="4.3.0" />
		<PackageReference Include="System.Collections.Specialized" Version="4.3.0" />
		<PackageReference Include="System.Console" Version="4.3.1" />
		<PackageReference Include="System.Diagnostics.Debug" Version="4.3.0" />
		<PackageReference Include="System.Diagnostics.DiagnosticSource" Version="8.0.1" />
		<PackageReference Include="System.Diagnostics.Tools" Version="4.3.0" />
		<PackageReference Include="System.Diagnostics.TraceSource" Version="4.3.0" />
		<PackageReference Include="System.Diagnostics.Tracing" Version="4.3.0" />
		<PackageReference Include="System.Dynamic.Runtime" Version="4.3.0" />
		<PackageReference Include="System.Globalization" Version="4.3.0" />
		<PackageReference Include="System.Globalization.Calendars" Version="4.3.0" />
		<PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.0.2" />
		<PackageReference Include="System.IO" Version="4.3.0" />
		<PackageReference Include="System.IO.Compression" Version="4.3.0" />
		<PackageReference Include="System.IO.Compression.ZipFile" Version="4.3.0" />
		<PackageReference Include="System.IO.FileSystem" Version="4.3.0" />
		<PackageReference Include="System.IO.FileSystem.Primitives" Version="4.3.0" />
		<PackageReference Include="System.Linq" Version="4.3.0" />
		<PackageReference Include="System.Linq.Expressions" Version="4.3.0" />
		<PackageReference Include="System.Linq.Queryable" Version="4.3.0" />
		<PackageReference Include="System.Memory" Version="4.5.5" />
		<PackageReference Include="System.Memory.Data" Version="8.0.0" />
		<PackageReference Include="System.Net.Http" Version="4.3.4" />
		<PackageReference Include="System.Net.Http.WinHttpHandler" Version="8.0.2" />
		<PackageReference Include="System.Net.NameResolution" Version="4.3.0" />
		<PackageReference Include="System.Net.NetworkInformation" Version="4.3.0" />
		<PackageReference Include="System.Net.Primitives" Version="4.3.1" />
		<PackageReference Include="System.Net.Requests" Version="4.3.0" />
		<PackageReference Include="System.Net.Security" Version="4.3.2" />
		<PackageReference Include="System.Net.Sockets" Version="4.3.0" />
		<PackageReference Include="System.Net.WebHeaderCollection" Version="4.3.0" />
		<PackageReference Include="System.Net.WebSockets" Version="4.3.0" />
		<PackageReference Include="System.Net.WebSockets.Client" Version="4.3.2" />
		<PackageReference Include="System.Numerics.Vectors" Version="4.5.0" />
		<PackageReference Include="System.ObjectModel" Version="4.3.0" />
		<PackageReference Include="System.Reflection" Version="4.3.0" />
		<PackageReference Include="System.Reflection.Extensions" Version="4.3.0" />
		<PackageReference Include="System.Reflection.Primitives" Version="4.3.0" />
		<PackageReference Include="System.Resources.ResourceManager" Version="4.3.0" />
		<PackageReference Include="System.Runtime" Version="4.3.1" />
		<PackageReference Include="System.Runtime.CompilerServices.Unsafe" Version="6.0.0" />
		<PackageReference Include="System.Runtime.Extensions" Version="4.3.1" />
		<PackageReference Include="System.Runtime.Handles" Version="4.3.0" />
		<PackageReference Include="System.Runtime.InteropServices" Version="4.3.0" />
		<PackageReference Include="System.Runtime.InteropServices.RuntimeInformation" Version="4.3.0" />
		<PackageReference Include="System.Runtime.Numerics" Version="4.3.0" />
		<PackageReference Include="System.Security.Cryptography.Algorithms" Version="4.3.1" requireReinstallation="true" />
		<PackageReference Include="System.Security.Cryptography.Encoding" Version="4.3.0" />
		<PackageReference Include="System.Security.Cryptography.Primitives" Version="4.3.0" />
		<PackageReference Include="System.Security.Cryptography.X509Certificates" Version="4.3.2" />
		<PackageReference Include="System.Security.SecureString" Version="4.3.0" />
		<PackageReference Include="System.ServiceModel.Primitives" Version="8.0.0" />
        <PackageReference Include="System.ServiceModel.Duplex" Version="6.0.0" />
        <PackageReference Include="System.ServiceModel.Http" Version="8.0.0" />
        <PackageReference Include="System.ServiceModel.NetTcp" Version="8.0.0" />
        <PackageReference Include="System.ServiceModel.Security" Version="6.0.0" />
        <PackageReference Include="System.Xml.XmlSerializer" Version="4.3.0" />
		
		<PackageReference Include="System.ServiceProcess.ServiceController" Version="8.0.0" />
		<PackageReference Include="System.Text.Encoding" Version="4.3.0" />
		<PackageReference Include="System.Text.Encoding.Extensions" Version="4.3.0" />
		<PackageReference Include="System.Text.Encodings.Web" Version="9.0.0" />
		<PackageReference Include="System.Text.Json" Version="9.0.0" />
		<PackageReference Include="System.Text.RegularExpressions" Version="4.3.1" />
		<PackageReference Include="System.Threading" Version="4.3.0" />
		<PackageReference Include="System.Threading.Tasks" Version="4.3.0" />
		<PackageReference Include="System.Threading.Tasks.Extensions" Version="4.5.4" />
		<PackageReference Include="System.Threading.Timer" Version="4.3.0" />
		<PackageReference Include="System.ValueTuple" Version="4.5.0" />
		<PackageReference Include="System.Xml.ReaderWriter" Version="4.3.1" />
		<PackageReference Include="System.Xml.XDocument" Version="4.3.0" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\..\Extric.Towbook.Integration.MotorClubs\Extric.Towbook.Integration.MotorClubs.csproj" />
		<ProjectReference Include="..\..\Extric.Towbook.Integrations.Email\Extric.Towbook.Integrations.Email.csproj" />
	</ItemGroup>

	<ItemGroup>
	  <Compile Update="Properties\Settings.Designer.cs">
	    <DesignTimeSharedInput>True</DesignTimeSharedInput>
	    <AutoGen>True</AutoGen>
	    <DependentUpon>Settings.settings</DependentUpon>
	  </Compile>
	</ItemGroup>

</Project>