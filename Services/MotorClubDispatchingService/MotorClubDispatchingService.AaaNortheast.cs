using Azure.Messaging.ServiceBus;
using Extric.Towbook.Accounts;
using Extric.Towbook.Dispatch;
using Extric.Towbook.Integration.MotorClubs;
using Extric.Towbook.Integration.MotorClubs.Dispatch;
using Extric.Towbook.Integration.MotorClubs.Queue;
using Extric.Towbook.Integration.MotorClubs.Services;
using Extric.Towbook.Integration.MotorClubs.Services.Models;
using Extric.Towbook.Integrations.MotorClubs.Aaa;
using Extric.Towbook.Integrations.MotorClubs.Aaa.Ace;
using Extric.Towbook.Utility;
using Extric.Towbook.Vehicle;
using NewRelic.Api.Agent;
using Newtonsoft.Json;
using NLog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Async = System.Threading.Tasks;
using AttributeValue = Extric.Towbook.Dispatch.AttributeValue;

namespace Extric.Towbook.Services.MotorClubDispatchingService
{

    public partial class MotorClubDispatchingService
    {
        public static bool disableNortheast = false;
        private const int NortheastUserId = 695894;

        [Transaction]
        public static async Task<bool> HandleAaaNortheastIncomingMessage(DigitalDispatchActionQueueItem qi, AaaMessage jsonObj, ProcessMessageEventArgs sourceMessage)
        {
            var contractor = AaaContractor.GetById(jsonObj.AaaContractorId);

            switch (qi.Type)
            {
                case DigitalDispatchActionQueueItemType.IncomingCallReceived:
                    async Task doReceived()
                    {
                        logger.Info(MasterAccountTypes.AaaNortheast,
                             "CallReceived",
                             "Incoming Job Offer",
                             contractor.ContractorId,
                             null,
                             jsonObj.DispatchId,
                             qi.CompanyId,
                             new
                             {
                                 request = jsonObj
                             },
                             queueItemId: qi.QueueItemId);

                        var rcr = await CreateCallRequest(jsonObj, MotorClubName.AaaNortheast);

                        qi.CallRequestId = rcr.CallRequestId;
                        DigitalDispatchService.LogAction(qi);

                        logger.Info(MasterAccountTypes.AaaNortheast,
                            "CallReceived",
                            "Incoming Job Offer Created",
                            contractor.ContractorId,
                            null,
                            jsonObj.DispatchId,
                            qi.CompanyId,
                            callRequestId: rcr.CallRequestId,
                            queueItemId: qi.QueueItemId);

                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                        await sourceMessage.CompleteAsync();
                    }
                    await doReceived();
                    break;

                case DigitalDispatchActionQueueItemType.IncomingCallRejected:
                    async Task doRejected()
                    {
                        var cc = JsonConvert.DeserializeObject<IncomingRefusedModel>(jsonObj.JsonData);
                        var ccujo = AaaDispatch.GetByDispatchId(jsonObj.DispatchId, jsonObj.AaaContractorId);
                        if (ccujo == null)
                        {
                            logger.Error(MasterAccountTypes.AaaNortheast,
                                "IncomingCallRejected",
                                "Received cancel for call that doesn't exist",
                                contractor.ContractorId, null, jsonObj.DispatchId, qi.CompanyId,
                                new
                                {
                                    message = cc
                                });

                            DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                            await sourceMessage.CompleteAsync();
                            return;
                        }

                        var ccr = CallRequest.GetById(ccujo.CallRequestId);
                        await ccr.UpdateStatus(CallRequestStatus.RejectedByMotorClub);

                        qi.CallRequestId = ccr.CallRequestId;
                        DigitalDispatchService.LogAction(qi);

                        logger.Log(LogLevel.Info, "Northeast/{0}: Offer Rejected by AAA. Type = {1}, CallRequestId = {2}", qi.QueueItemId, qi.Type, ccr.CallRequestId);
                        
                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                        await sourceMessage.CompleteAsync();
                    }
                    await doRejected();
                    break;

                case DigitalDispatchActionQueueItemType.IncomingCallCancelled:
                    async Task doCancelled()
                    {
                        var icm = JsonConvert.DeserializeObject<IncomingCancelledModel>(jsonObj.JsonData);
                        var ccujo = AaaDispatch.GetByDispatchId(jsonObj.DispatchId, jsonObj.AaaContractorId);
                        if (ccujo == null)
                        {
                            logger.Error(MasterAccountTypes.AaaNortheast,
                                "IncomingCallCancelled",
                                "Received cancel for call that doesn't exist",
                                contractor.ContractorId, null, jsonObj.DispatchId, qi.CompanyId,
                                new
                                {
                                    message = icm
                                });

                            DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                            await sourceMessage.CompleteAsync();
                            return;
                        }

                        var ccr = CallRequest.GetById(ccujo.CallRequestId);
                        await ccr.UpdateStatus(CallRequestStatus.Cancelled);

                        bool cancelled = false;

                        if (ccr.DispatchEntryId.GetValueOrDefault() > 0)
                        {
                            var en = Entry.GetByIdNoCache(ccr.DispatchEntryId.Value);
                            if (en != null)
                            {
                                if (ShouldAllowCancel(en))
                                {
                                    await en.Cancel("Cancelled by AAA: " + icm.Notes, new AuthenticationToken()
                                    {
                                        UserId = NortheastUserId,
                                        ClientVersionId = MyClientVersionId
                                    }, "127.0.0.1");

                                    cancelled = true;
                                }
                                else
                                {
                                    cancelled = false;
                                }
                            }
                        }

                        qi.CallRequestId = ccr.CallRequestId;
                        DigitalDispatchService.LogAction(qi);

                        if (cancelled)
                        {
                            logger.Log(LogLevel.Info, "Northeast/{0}: Offer Cancelled. Type = {1}, CallRequestId = {2}", qi.QueueItemId, qi.Type, ccr.CallRequestId);
                        }
                        else
                        {
                            logger.Log(LogLevel.Info, "Northeast/{0}: Offer Cancel event received but failed to be canceled. Type = {1}, CallRequestId = {2}", qi.QueueItemId, qi.Type, ccr.CallRequestId);
                        }

                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                        await sourceMessage.CompleteAsync();
                    }
                    await doCancelled();
                    break;

                case DigitalDispatchActionQueueItemType.IncomingCallAccepted:
                    async Task doAccepted()
                    {
                        var accepted = JsonConvert.DeserializeObject<IncomingAcceptedModel>(jsonObj.JsonData);
                        var aaaDispatch = AaaDispatch.GetByDispatchId(jsonObj.DispatchId, jsonObj.AaaContractorId);
                        
                        if (aaaDispatch == null)
                        {
                            logger.Error(MasterAccountTypes.AaaNortheast,
                                "IncomingCallAccepted",
                                "Received accept for call that doesn't exist",
                                contractor.ContractorId, null, jsonObj.DispatchId, qi.CompanyId,
                                new
                                {
                                    message = accepted
                                });

                            DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                            await sourceMessage.CompleteAsync();
                            return;
                        }

                        var ccr = CallRequest.GetById(aaaDispatch.CallRequestId);

                        await ccr.UpdateStatus(CallRequestStatus.Accepted);

                        await NortheastDoCallCreateOrUpdate(qi, accepted, aaaDispatch, ccr, contractor,sourceMessage);
                    }
                    await doAccepted();
                    break;

                case DigitalDispatchActionQueueItemType.IncomingCallImport:
                    async Task doImport()
                    {
                        var ccr = await  CreateCallRequest(jsonObj, MotorClubName.AaaNortheast, false,
                            MasterAccountTypes.AaaNortheast);

                        await ccr.UpdateStatus(CallRequestStatus.Accepted);

                        var aaaDispatch = AaaDispatch.GetByDispatchId(jsonObj.DispatchId, jsonObj.AaaContractorId);
                        var job = JsonConvert.DeserializeObject<DispatchModel>(aaaDispatch.DispatchJson);

                        qi.CallRequestId = ccr.CallRequestId;
                        DigitalDispatchService.LogAction(qi);

                        await NortheastDoCallCreateOrUpdate(qi, new IncomingAcceptedModel()
                        {
                            ContractorId = job.ContractorId,
                            DispatchId = job.DispatchId,
                            LocationId = job.LocationId,
                            PoNumber = job.PurchaseOrderNumber
                        }, aaaDispatch, ccr, contractor, sourceMessage);
                    }
                    await doImport();
                    break;


                case DigitalDispatchActionQueueItemType.IncomingCallGoaResponse:
                    async Task doGoa()
                    {
                        var cc = JsonConvert.DeserializeObject<IncomingGoaModel>(jsonObj.JsonData);
                        var ccujo = AaaDispatch.GetByDispatchId(jsonObj.DispatchId, jsonObj.AaaContractorId);

                        if (ccujo == null)
                        {
                            logger.Error(MasterAccountTypes.AaaNortheast,
                                "IncomingCallGoaResponse",
                                "Received GOA accept for call that doesn't exist",
                                contractor.ContractorId, null, jsonObj.DispatchId, qi.CompanyId,
                                new
                                {
                                    message = cc
                                });

                            DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                            await sourceMessage.CompleteAsync();
                            return;
                        }

                        var ccr = CallRequest.GetById(ccujo.CallRequestId);
                        await ccr.UpdateStatus(CallRequestStatus.Cancelled);

                        if (ccr.DispatchEntryId.GetValueOrDefault() > 0)
                        {
                            var en = Entry.GetByIdNoCache(ccr.DispatchEntryId.Value);
                            if (en != null)
                            {
                                var now = Core.OffsetDateTime(en.Company, DateTime.Now);

                                var toAdd = "AAA GOA Response received at " + now.ToShortDateString() + " " + now.ToShortTowbookTimeString();
                                en.Notes = toAdd + "\n" + (en.Notes ?? "");

                                en.SetAttribute(Dispatch.AttributeValue.BUILTIN_BILLING_NOTES,
                                    toAdd + "\n" + en.BillingNotes());

                                en.Status = Status.Completed;

                                await en.Save(false, new AuthenticationToken()
                                {
                                    UserId = NortheastUserId,
                                    ClientVersionId = MyClientVersionId
                                }, "127.0.0.1");
                                logger.Log(LogLevel.Info, "Northeast/{0}: Offer completed by way of GOA response. Type = {1}, CallRequestId = {2}", qi.QueueItemId, qi.Type, ccr.CallRequestId);
                            }
                        }

                        qi.CallRequestId = ccr.CallRequestId;
                        DigitalDispatchService.LogAction(qi);
                        
                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                        await sourceMessage.CompleteAsync();
                    }
                    await doGoa();
                    break;

                case DigitalDispatchActionQueueItemType.IncomingCallUpdate:
                    async Task doCallUpdate()
                    {
                        var update = JsonConvert.DeserializeObject<DispatchModel>(jsonObj.JsonData);
                        var ccujo = AaaDispatch.GetByDispatchId(jsonObj.DispatchId, jsonObj.AaaContractorId);

                        if (ccujo == null)
                        {
                            logger.Error(MasterAccountTypes.AaaNortheast,
                                "IncomingCallUpdate",
                                "Received updaet for call that doesn't exist",
                                contractor.ContractorId, null, jsonObj.DispatchId, qi.CompanyId,
                                new
                                {
                                    message = update
                                });

                            DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                            await sourceMessage.CompleteAsync();
                            return;
                        }

                        var ccr = CallRequest.GetById(ccujo.CallRequestId);

                        if (ccr.DispatchEntryId.GetValueOrDefault() > 0)
                        {
                            var en = Entry.GetByIdNoCache(ccr.DispatchEntryId.Value);
                            if (en != null)
                            {
                                if (update.Call?.TowDestination != null
                                     && !string.IsNullOrWhiteSpace(update.Call.TowDestination.Address.StreetAddress)
                                     && !string.IsNullOrWhiteSpace(update.Call.TowDestination.Address.City)
                                     && !string.IsNullOrWhiteSpace(update.Call.TowDestination.Address.ZipCode))
                                {
                                    var dest = en.Waypoints.FirstOrDefault(o => o.Title == "Destination");
                                    if (dest == null)
                                    {
                                        dest = new EntryWaypoint()
                                        {
                                            Title = "Destination",
                                            Position = 2
                                        };
                                        en.Waypoints.Add(dest);
                                    }

                                    dest.Address = update.Call.TowDestination.Address.ToString();

                                    if (!string.IsNullOrWhiteSpace(update.Call.TowDestination.Latitude))
                                        dest.Latitude = decimal.Parse(update.Call.TowDestination.Latitude);

                                    if (!string.IsNullOrWhiteSpace(update.Call.TowDestination.Longitude))
                                        dest.Longitude = decimal.Parse(update.Call.TowDestination.Longitude);
                                }

                                if (update.Call?.BreakdownLocation != null 
                                    && !string.IsNullOrWhiteSpace(update.Call.BreakdownLocation.Address.StreetAddress)
                                    && !string.IsNullOrWhiteSpace(update.Call.BreakdownLocation.Address.City)
                                    && !string.IsNullOrWhiteSpace(update.Call.BreakdownLocation.Address.ZipCode))
                                {
                                    var pickup = en.Waypoints.FirstOrDefault(o => o.Title == "Pickup");
                                    if (pickup == null)
                                    {
                                        pickup = new EntryWaypoint()
                                        {
                                            Title = "Pickup",
                                            Position = 1
                                        };
                                        en.Waypoints.Add(pickup);
                                    }

                                    pickup.Address = update.Call.BreakdownLocation.Address.ToString();

                                    if (!string.IsNullOrWhiteSpace(update.Call.BreakdownLocation.Latitude))
                                        pickup.Latitude = decimal.Parse(update.Call.BreakdownLocation.Latitude);

                                    if (!string.IsNullOrWhiteSpace(update.Call.BreakdownLocation.Longitude))
                                        pickup.Longitude = decimal.Parse(update.Call.BreakdownLocation.Longitude);
                                }

                                if (update.Call?.Vehicle != null)
                                {
                                    var asset = en.Assets.FirstOrDefault();

                                    if (asset == null)
                                    {
                                        asset = new EntryAsset() { BodyTypeId = 1 };
                                        en.Assets.Add(asset);
                                    }

                                    asset.Make = update.Call.Vehicle.MakeName;
                                    asset.Model = update.Call.Vehicle.ModelName;
                                    if (update.Call.Vehicle.ModelYear != null)
                                        asset.Year = update.Call.Vehicle.ModelYear.Value;
                                }


                                await en.Save(false, new AuthenticationToken()
                                {
                                    UserId = NortheastUserId,
                                    ClientVersionId = MyClientVersionId
                                }, "127.0.0.1");

                                logger.Log(LogLevel.Info, "Northeast/{0}: Job upated", qi.QueueItemId, qi.Type, ccr.CallRequestId);
                            }
                        }

                        qi.CallRequestId = ccr.CallRequestId;
                        DigitalDispatchService.LogAction(qi);

                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                        if (sourceMessage != null)
                            await sourceMessage.CompleteAsync();
                    }
                    await doCallUpdate();
                    break;

                default:
                    logger.Error(MasterAccountTypes.AaaNortheast,
                        "UnsupportedEvent",
                        "Event Type Not Implemented",
                        contractor.ContractorId,
                        contractor.LocationCode,
                        jsonObj.DispatchId,
                        qi.CompanyId,
                        new
                        {
                            message = jsonObj
                        },
                        ownerUserId: qi.OwnerUserId,
                        queueItemId: qi.QueueItemId);

                    DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                    await sourceMessage.CompleteAsync();
                    return true;
            }

            return true;
        }

        private static async Task<Entry> NortheastDoCallCreateOrUpdate(
            DigitalDispatchActionQueueItem qi,
            IncomingAcceptedModel accepted,
            AaaDispatch dispatch,
            CallRequest callRequest,
            AaaContractor contractor,
            ProcessMessageEventArgs sourceMessage)
        {
            var job = JsonConvert.DeserializeObject<DispatchModel>(dispatch.DispatchJson);

            Entry fe = await DistributedLock.ForAsync("Northeast",
                accepted.DispatchId,
                10000,
                lockAcquired: async delegate ()
                {
                    Entry call = null;

                    var existingAAA = AttributeValue.GetAllByValue(qi.CompanyId.GetValueOrDefault(),
                        AttributeValue.BUILTIN_MOTORCLUB_DISPATCHNUMBER, accepted.DispatchId).LastOrDefault();

                    if (existingAAA != null)
                        call = await Entry.GetByIdNoCacheAsync(existingAAA.DispatchEntryId);

                    if (call != null && call.CreateDate < DateTime.Now.AddDays(-7))
                        call = null;

                    if (call == null)
                    {
                        call = new Entry();
                        call.CompanyId = callRequest.CompanyId;
                        call.AccountId = callRequest.AccountId;
                    }
                    else
                    {
                        logger.LogEvent("Northeast/{0}: Found existing towbook call for Case {1}... Call #{2}... we're going to update this one.",
                            callRequest.CompanyId, LogLevel.Info, sourceMessage.Message.MessageId, callRequest.CallRequestId.ToString(), call.CallNumber);

                        if (call.Status == Status.Cancelled)
                        {
                            logger.LogEvent("Northeast/{0}: Call is cancelled. Proceeding to uncancel and set status to waiting...",
                                callRequest.CompanyId, LogLevel.Info, sourceMessage.Message.MessageId, accepted.PoNumber.ToString(), call.CallNumber);

                            await call.Uncancel(new AuthenticationToken() { UserId = NortheastUserId, ClientVersionId = MyClientVersionId });
                        }
                    }

                    if (job.Call.RequestedService.Priority != null &&
                        job.Call.RequestedService.Priority.StartsWith("P"))
                    {
                        call.Priority = Entry.EntryPriority.High;
                        call.Notes = "Priority Code: " + job.Call.RequestedService.Priority + "\n" +
                            call.Notes;
                    }

                    if (qi.Type == DigitalDispatchActionQueueItemType.IncomingCallImport)
                    {
                        call.Status = Status.Completed;

                        var resolution = job.Call.RequestedService.TroubleCodes.FirstOrDefault(o => o.Type == "RESOLUTION")?.Code;

                        if (resolution != null)
                        {
                            var rc = MasterAccountReason.GetByMasterAccountId(MasterAccountTypes.AaaNortheast).FirstOrDefault(o => o.Code == resolution);

                            string resText = resolution;
                            if (rc != null)
                                resText = rc.Name;

                            call.Notes += "Resolution: " + resText + "\n";
                        }

                        // handle status times

                        var sp = job.Call.CallEvents.FirstOrDefault(o => o.Status == "SP")?.EventTime.ToLocalTime() ?? DateTime.MinValue;
                        var di = job.Call.CallEvents.FirstOrDefault(o => o.Status == "DI")?.EventTime.ToLocalTime() ?? DateTime.MinValue;
                        var er = job.Call.CallEvents.FirstOrDefault(o => o.Status == "ER")?.EventTime.ToLocalTime() ?? DateTime.MinValue;
                        var ol = job.Call.CallEvents.FirstOrDefault(o => o.Status == "OL")?.EventTime.ToLocalTime() ?? DateTime.MinValue;
                        var cl = job.Call.CallEvents.FirstOrDefault(o => o.Status == "CL")?.EventTime.ToLocalTime() ?? DateTime.MinValue;

                        var rawDriver = job.Call.DriverData.ServicingStationEmployeeName;

                        if (rawDriver != null && rawDriver.Contains(" - "))
                        {
                            var name = rawDriver.Split('-')[1].Trim().ToLowerInvariant();

                            var drivers = await Driver.GetByExactCompanyIdAsync(call.CompanyId);

                            var driver = drivers.FirstOrDefault(o => o?.Name.Trim().ToLowerInvariant() == name);

                            if (driver != null)
                                call.DriverId = driver.Id;
                            else
                            {
                                addNote("Driver: " + rawDriver, true);
                            }
                        }

                        if (sp != DateTime.MinValue)
                            call.CreateDate = sp;

                        if (di != DateTime.MinValue)
                            call.DispatchTime = di;

                        if (er != DateTime.MinValue)
                            call.EnrouteTime = er;

                        if (ol != DateTime.MinValue)
                            call.ArrivalTime = ol;

                        if (cl != DateTime.MinValue)
                            call.CompletionTime = cl;

                        // handle invoice items

                        foreach (var x in job.Call.ProvidedServices)
                        {
                            // ACE sends over invoice items for stuff like FD(Fuel Delivery) with a price but no quantity, 
                            // so we need to default it to 1.
                            if (x.Quantity == 0)
                                x.Quantity = 1;

                            call.InvoiceItems.Add(
                                new InvoiceItem()
                                {
                                    CustomName = x.Service,
                                    Quantity = x.Quantity,
                                    CustomPrice = x.Charge
                                });
                        }
                    }

                    if (call.Account?.DefaultPriority == 1)
                        call.Priority = Entry.EntryPriority.High;

                    #region Vehicle

                    EntryAsset asset = null;
                    if (call.Assets != null)
                        asset = call.Assets.FirstOrDefault();

                    if (asset == null)
                        asset = new EntryAsset() { BodyTypeId = 1 };

                    if (asset.BodyTypeId == 0)
                        asset.BodyTypeId = 1;

                    var vc = VehicleUtility.GetColorIdByName(job.Call.Vehicle.Color);

                    asset.Year = job.Call.Vehicle.ModelYear.GetValueOrDefault();
                    asset.Make = VehicleUtility.GetManufacturerByName(job.Call.Vehicle.MakeName);
                    asset.Model = VehicleUtility.GetModelByName(job.Call.Vehicle.ModelName);

                    if (vc != 0)
                        asset.ColorId = vc;

                    asset.Vin = job.Call.Vehicle.VehicleDetail.Vin;

                    asset.LicenseNumber = job.Call.Vehicle.LicenseNumber;
                    asset.LicenseState = job.Call.Vehicle.LicenseState;
                    #endregion Vehicle

                    #region Locations

                    var pickup = call.Waypoints.FirstOrDefault(o => o.Title == "Pickup");
                    var dest = call.Waypoints.FirstOrDefault(o => o.Title == "Destination");

                    if (call.Notes == null)
                        call.Notes = "";

                    if (job.Call.BreakdownLocation != null)
                    {
                        if (string.IsNullOrWhiteSpace(call.TowSource))
                        {
                            var breakdown = job.Call.BreakdownLocation.Address.ToString();

                            if (!string.IsNullOrWhiteSpace(job.Call.BreakdownLocation.Landmark))
                                breakdown += " (" + job.Call.BreakdownLocation.Landmark + ")";

                            call.TowSource = breakdown;

                            if (pickup == null)
                            {
                                pickup = new EntryWaypoint()
                                {
                                    Title = "Pickup",
                                    Position = 1
                                };
                                call.Waypoints.Add(pickup);
                            }
                            pickup.Address = call.TowSource;

                            if (!string.IsNullOrWhiteSpace(job.Call.BreakdownLocation.Latitude))
                                pickup.Latitude = decimal.Parse(job.Call.BreakdownLocation.Latitude);

                            if (!string.IsNullOrWhiteSpace(job.Call.BreakdownLocation.Longitude))
                                pickup.Longitude = decimal.Parse(job.Call.BreakdownLocation.Longitude);
                        }
                    }

                    if (job.Call.TowDestination != null)
                    {
                        if (string.IsNullOrWhiteSpace(call.TowDestination))
                        {
                            var destAddress = job.Call.TowDestination.Address.ToString();

                            if (job.Call.TowDestination.Name == "DISCUSS WITH DRIVER")
                            {
                                destAddress = "(" + job.Call.TowDestination.Name + ")";
                            }
                            else
                            {
                                if (!string.IsNullOrWhiteSpace(job.Call.TowDestination.Landmark))
                                    destAddress += " (" + job.Call.TowDestination.Landmark + ")";
                            }

                            call.TowDestination = destAddress;
                            if (!string.IsNullOrWhiteSpace(call.TowDestination))
                            {
                                if (dest == null)
                                {
                                    dest = new EntryWaypoint() { Title = "Destination", Position = 2 };
                                    call.Waypoints.Add(dest);
                                }

                                dest.Address = call.TowDestination;

                                if (!string.IsNullOrWhiteSpace(job.Call.TowDestination.Latitude))
                                    dest.Latitude = decimal.Parse(job.Call.TowDestination.Latitude);

                                if (!string.IsNullOrWhiteSpace(job.Call.TowDestination.Longitude))
                                    dest.Longitude = decimal.Parse(job.Call.TowDestination.Longitude);
                            }
                        }
                    }

                    #endregion Locations

                    // don't use offerExpires time, and they dont have a transaction timestamp, so we'll just use current server time.
                    if (call.CreateDate == DateTime.MinValue)
                        call.CreateDate = DateTime.Now.AddSeconds(-DateTime.Now.Second); // dont use accept.service.OfferExpiresLocal;

                    if (dispatch.Eta != null)
                        call.ArrivalETA = call.CreateDate.AddMinutes(dispatch.Eta.Value);


                    if (job.Call.ServiceStatus.GetAppointmentTime() != null)
                    {
                        call.ArrivalETA = job.Call.ServiceStatus.GetAppointmentTime();
                        var offsetDate = Core.OffsetDateTime(call.Company, call.ArrivalETA.Value);
                        addNote("** SCHEDULED CALL: " + offsetDate.ToShortDateString() + " " + offsetDate.ToShortTowbookTimeString());
                    }

                    if (job.Call.RequestedService.NumberOfPassengers > 0)
                        addNote("Number of Passengers:" + job.Call.RequestedService.NumberOfPassengers);

                    if (job.Call.CallType == "RAP")
                        addNote("** RAP CALL **", true);

                    #region po number
                    call.PurchaseOrderNumber = accepted.PoNumber;

                    if (!string.IsNullOrWhiteSpace(job?.Call?.MemberContactInfo?.MemberNumber?.CardNumber))
                        call.SetAttribute(AttributeValue.BUILTIN_MOTORCLUB_MEMBERSHIPNUMBER, 
                            job.Call.MemberContactInfo.MemberNumber.CardNumber);

                    call.SetAttribute(AttributeValue.BUILTIN_MOTORCLUB_DISPATCHNUMBER, accepted.DispatchId);


                    #endregion

                    #region Reason

                    var reason = job.Call.RequestedService.TroubleCodes.FirstOrDefault(o => o.Type == "PROBLEM");
                    string tcode = "";
                    
                    if (reason != null)
                        tcode = reason.Code;

                    call.ReasonId = await ReasonHelper.DetermineReasonId(call.Account.MasterAccountId, qi.CompanyId.Value,
                        reason?.Description);

                    if (call.ReasonId == 1635 && reason != null)
                        call.Notes = "Service Needed: " + tcode + " " + reason.Description + "\n" + call.Notes;


                    addNote("Trouble Codes: " + string.Join(", ", job.Call.RequestedService.TroubleCodes.Select(o => o.Code + " - " + o.Description)), top:true);

                    #endregion Reason
                    addNote("Body Type: " + job.Call.Vehicle.VehicleDetail.BodyTypeFullName());


                    if (!string.IsNullOrWhiteSpace(job.Call.BreakdownLocation.Instruction))
                        addNote("Breakdown Location Instructions: " + job.Call.BreakdownLocation.Instruction);

                    if (callRequest != null)
                    {
                        if (callRequest.OwnerUserId == null)
                            callRequest.OwnerUserId = 1;

                        call.CallRequestId = callRequest.CallRequestId;

                        if (call.OwnerUserId < 100)
                            call.OwnerUserId = callRequest.OwnerUserId.GetValueOrDefault(0);
                    }
                    else if (call.OwnerUserId < 100)
                        call.OwnerUserId = 1;

                    #region Notes

                    void addNote(string line, bool top = false)
                    {
                        if (string.IsNullOrWhiteSpace(line))
                            return;

                        if (call.Notes == null || !call.Notes.Contains(line))
                        {
                            if (call.Notes == null)
                                call.Notes = line + "\n";
                            else
                            {
                                if (top)
                                    call.Notes = line + call.Notes.Trim('\n') + "\n";
                                else
                                    call.Notes += "\n" + line.Trim('\n');
                            }
                        }
                    }
                    #endregion Notes

                    call.SetAttribute(AttributeValue.BUILTIN_MOTORCLUB_COVERAGELIMIT, job.Call.MemberContactInfo.CurrentBenefitLevel);
                    call.SetAttribute(AttributeValue.BUILTIN_AAA_TROUBLE_CODE_PROBLEM, tcode);

                    if (job.Call.Payment?.Required == true)
                    {
                        call.SetAttribute(AttributeValue.BUILTIN_MOTORCLUB_COVERAGELIMIT, "COD / Payment Required from customer");
                    }

                    await ApplyRoundRobinDispatcherLogicAsync(qi, call, dispatch.DispatchId, contractor.ContractorId);

                    if (call.Assets == null || call.Assets.Count == 0)
                    {
                        call.Assets = new System.Collections.ObjectModel.Collection<EntryAsset>();
                        call.Assets.Add(asset);
                    }

                    bool newContact = false;

                    var customerName = Core.FormatName(job.Call.MemberContactInfo?.Name?.ToString());

                    foreach (var x in job.Call.MemberContactInfo?.PhoneNumbers)
                    {
                        var phone = Core.FormatPhone(x.DisplayPhoneNumber);

                        EntryContact c = call.Contacts.FirstOrDefault(o =>
                            Core.FormatPhone(o.Phone) == phone);

                        if (c == null)
                        {
                            c = new EntryContact();
                            call.Contacts.Add(c);
                            newContact = true;
                        }
                        
                        c.Name = customerName;
                        c.Phone = phone;
                    }

                    if (!string.IsNullOrWhiteSpace(job.DriverNotes))
                    {
                        call.Notes = job.DriverNotes + "\n" + call.Notes;
                    }

                    if (!string.IsNullOrWhiteSpace(job.DrivingDirection))
                    {
                        call.Notes = "Driving Directions: " + job.DrivingDirection + "\n" + call.Notes;
                    }

                    await call.Save();

                    if (newContact)
                    {
                        foreach (var c in call.Contacts)
                            await CheckForRoadsideFeatureAndAutoInvite(call, c);
                    }

                    foreach (var note in job.Call.Comments.OrderBy(o => o.CommentDateTime))
                    {
                        if (note.Text == null ||
                            note.Text.StartsWith("|") ||
                            note.Text.Contains("Page/Grid") ||
                            note.Text.StartsWith("**PACESETTER SERVICE"))
                            continue;

                        var en = new EntryNote() { DispatchEntryId = call.Id, OwnerUserId = NortheastUserId };
                        en.Content = note.Text;
                        en.CreateDate = note.CommentDateTime.ToLocalTime();
                        //en.OwnerUserId = AaaSystemUserId;
                        await en.SaveAsync();
                    }


                    await AutoDispatch.AutoDispatchServiceBusHandler.Send(call);

                    return call;
                },
                alreadyLocked: async delegate ()
                {
                    logger.LogEvent("{0}/CR{1}: Lock already exists for {2}:{3}... pausing 250ms",
                        qi.CompanyId, LogLevel.Warn,
                        sourceMessage.Message.MessageId,
                        (callRequest != null ? callRequest.CallRequestId.ToString() : "NULL"),
                        qi.AccountId.Value,
                        accepted.LocationId);


                    await Task.Delay(250);

                    return true;
                });

            if (fe == null)
            {
                logger.LogEvent("Northeast/{0}: Creation of call failed; {1}",
                    qi.CompanyId, LogLevel.Error, sourceMessage.Message.MessageId, qi.ToJson());

                if (callRequest != null)
                    await callRequest.UpdateStatus(CallRequestStatus.AcceptFailed);
            }
            else
            {
                if (callRequest != null)
                {
                    await callRequest.UpdateStatus(CallRequestStatus.Accepted, po: fe.PurchaseOrderNumber);

                    qi.CallRequestId = callRequest.CallRequestId;
                    DigitalDispatchService.LogAction(qi);

                    callRequest.DispatchEntryId = fe.Id;
                    await callRequest.Save();

                    await sourceMessage.CompleteAsync();
                    DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);

                    logger.Info(MasterAccountTypes.AaaNortheast,
                        "CallCreated",
                        "Created new towbook call for AAA ACE",
                        companyId: qi.CompanyId,
                        data: new
                        {
                            waypoints = fe.Waypoints.Select(o => new { o.Address, o.Latitude, o.Longitude })
                        },
                        dispatchId: dispatch.DispatchId.ToString(),
                        callId: fe.Id,
                        callRequestId: callRequest.CallRequestId,
                        queueItemId: qi.QueueItemId,
                        poNumber: fe.PurchaseOrderNumber,
                        callNumber: fe.CallNumber);

                }
            }
            return fe;
        }

        private static AaaNortheastRestClient GetNortheastClient(AaaContractor contractor, DigitalDispatchActionQueueItem qi, int callRequestId = 0, int? callId = 0, string dispatchId = null)
        {
            string env = AceEnvironmentIdToString(contractor.EnvironmentId);
            return new AaaNortheastRestClient(env, "127.0.0.1", qi.CompanyId.GetValueOrDefault(), qi.QueueItemId, callRequestId, callId.GetValueOrDefault(), dispatchId, contractor);
        }

        [Transaction]
        public static async Async.Task HandleAaaNortheastQueueOutgoingMessage(DigitalDispatchActionQueueItem qi, dynamic jsonObj, ProcessMessageEventArgs sourceMessage)
        {
            AaaNortheastRestClient client;

            if (!disableNortheast)
            {
                switch (qi.Type)
                {
                    case DigitalDispatchActionQueueItemType.OutgoingAcceptCall:
                        AceCallRequestContainer x = AceGetCallRequestAndDispatch(jsonObj);
                        async Task doAccept()
                        {
                            CallRequest callRequest = x.CallRequest;
                            AaaDispatch dispatch = x.Job;
                            var contractor = AaaContractor.GetById(dispatch.AaaContractorId);
                            client = GetNortheastClient(contractor, qi, callRequest.CallRequestId, callRequest.DispatchEntryId, dispatch.DispatchId);

                            if (callRequest == null)
                            {
                                await sourceMessage.DeadLetterAsync();
                                return;
                            }

                            if (dispatch == null)
                            {
                                logger.Log(LogLevel.Error, "Northeast/CR" + callRequest.CallRequestId + "/C" + callRequest.CompanyId + "/OutgoingAcceptCall: Couldn't find AceDispatch");

                                await sourceMessage.DeadLetterAsync();
                                return;
                            }

                            dispatch.Eta = x.Eta;
                            dispatch.Save();

                            try
                            {
                                logger.Info(MasterAccountTypes.AaaNortheast,
                                    "AcceptCall",
                                    "Outgoing Call Accept",
                                    contractor.ContractorId,
                                    null,
                                    dispatch.DispatchId,
                                    x.CallRequest.CompanyId,
                                    new
                                    {
                                        json = JsonExtensions.ToJson(jsonObj)
                                    },
                                    callRequestId: x.CallRequest.CallRequestId,
                                    queueItemId: qi.QueueItemId);

                                bool postDispatchIsOk()
                                {
                                    try
                                    {
                                        client.Accept(
                                            new AaaNortheastRestClient.DispatchAcceptModel()
                                            {
                                                ContractorId = contractor.ContractorId,
                                                DispatchId = dispatch.DispatchId,
                                                Eta = x.Eta
                                            });

                                        return true;
                                    }
                                    catch (Exception rfe)
                                    {
                                        logger.Error(MasterAccountTypes.AaaNortheast,
                                            "AcceptCall",
                                            "Outgoing Call Accept Failed",
                                            contractor.ContractorId, null, dispatch.DispatchId, x.CallRequest.CompanyId,
                                            new
                                            {
                                                json = rfe.ToJson()
                                            },
                                            callRequestId: x.CallRequest.CallRequestId,
                                            queueItemId: qi.QueueItemId);

                                        return false;
                                    }
                                }

                                if (!postDispatchIsOk())
                                {
                                    await callRequest.UpdateStatus(CallRequestStatus.AcceptFailed);
                                }
                                await sourceMessage.CompleteAsync();
                            }
                            catch (Exception ex)
                            {
                                await sourceMessage.DeadLetterAsync("Attempted to accept callRequestId " + callRequest.CallRequestId + ", but  error occurred",
                                    ex.ToJson(true));

                                logger.Error(MasterAccountTypes.AaaNortheast,
                                    "AcceptCall",
                                    "Sent accept failed.",
                                    contractor.ContractorId, 
                                    null, 
                                    dispatch?.DispatchId,
                                    qi?.CompanyId,
                                    new
                                    {
                                        errorMessage = ex.Message,
                                        exception = ex
                                    },
                                    queueItemId: qi.QueueItemId);

                                DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                            }
                        }
                        try
                        {
                            await doAccept();
                        }
                        catch(Exception y)
                        {
                            if (y.Message == "Network Error, retry recommended.")
                                break;

                            logger.Error(MasterAccountTypes.AaaNortheast,
                                "Accept",
                                "Failed: " + y.Message,
                                null,
                                callRequestId: x.CallRequest?.CallRequestId ?? null,
                                companyId: qi.CompanyId.GetValueOrDefault(),
                                data: new
                                {
                                    exception = y
                                },
                                queueItemId: qi.QueueItemId);

                            try
                            {
                                await sourceMessage.CompleteAsync();
                            }
                            catch { }

                            if (x.CallRequest != null)
                            {
                                await x.CallRequest.UpdateStatus(CallRequestStatus.AcceptFailed);
                            }
                            DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                        }
                        break;

                    case DigitalDispatchActionQueueItemType.OutgoingCallCanceled:
                        async Task DoCancel()
                        {
                            CallRequest cr = CallRequest.GetById(Convert.ToInt32(jsonObj.CallRequestId ?? jsonObj.Id));

                            var chj = AaaDispatch.GetByCallRequestId(cr.CallRequestId);
                            var cancelContractor = AaaContractor.GetById(chj.AaaContractorId);
                            client = GetNortheastClient(cancelContractor, qi, cr.CallRequestId, cr.DispatchEntryId, chj.DispatchId);

                            var cancelReason = MasterAccountReason.GetById((int)jsonObj.ReasonId);
                            
                            if (cancelReason.Code == "X002")
                            {
                                // create a Complete event instead so we can pass service line items.
                            }
                            
                            var cdm = new AaaNortheastRestClient.DispatchCancelModel();

                            cdm.ContractorId = cancelContractor.ContractorId;
                            cdm.DispatchId = chj.DispatchId;
                            cdm.ResolutionCode = cancelReason.Code;
                            cdm.Comments = jsonObj.Comments.ToString();

                            logger.Info(MasterAccountTypes.AaaNortheast, 
                                "OutgoingCancel", 
                                "OutboundMessage",
                                cancelContractor.ContractorId, null, chj.DispatchId, qi.CompanyId,
                                data: new { json = cdm.ToJson() },
                                callRequestId: chj.CallRequestId,
                                queueItemId: qi.QueueItemId);

                            client.Cancel(cdm);

                            logger.Info(MasterAccountTypes.AaaNortheast, "OutgoingCancel", "OutboundMessageResponse",
                                cancelContractor.ContractorId, null, chj.DispatchId.ToString(), qi.CompanyId, "",
                                callRequestId: chj.CallRequestId,
                                queueItemId: qi.QueueItemId);

                            var e = Entry.GetByIdNoCache(cr.DispatchEntryId.Value);
                            if (e != null)
                            {
                                await e.Cancel("Digitally Cancelled: " + cancelReason.Name,
                                    new AuthenticationToken() { UserId = qi.OwnerUserId.Value }, "127.0.0.1");
                            }
                            DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                            await sourceMessage.CompleteAsync();
                        }

                        await DoCancel();

                        break;

                    case DigitalDispatchActionQueueItemType.OutgoingSharePhoto:
                        void doPhotoUpload()
                        {
                            logger.Info("SharePhoto");

                            SharePhotoModel pm = JsonConvert.DeserializeObject<SharePhotoModel>(qi.JsonObject);

                            var phj = AaaDispatch.GetByCallRequestId(pm.CallRequestId);
                            var contractor = AaaContractor.GetById(phj.AaaContractorId);
                            client = GetNortheastClient(contractor, qi, pm.CallRequestId, pm.DispatchEntryId, phj.DispatchId);

                            Console.WriteLine(JsonExtensions.ToJson(pm, true));

                            var thePhoto = Dispatch.Photo.GetById(pm.PhotoId);
                            
                            client.Photo(new AaaNortheastRestClient.DispatchPhotoModel()
                            {
                                ContractorId = contractor.ContractorId, 
                                DispatchId = phj.DispatchId, 
                                Timestamp = thePhoto.CreateDate.ToUniversalTime(),
                                Url = thePhoto.HttpLocation,
                                Latitude = thePhoto.CameraLatitude.GetValueOrDefault(),
                                Longitude = thePhoto.CameraLongitude.GetValueOrDefault(),
                            });
                        }
                        doPhotoUpload();
                        break;

                    case DigitalDispatchActionQueueItemType.OutgoingRejectCall:
                        AceCallRequestContainer x2 = AceGetCallRequestAndDispatch(jsonObj);
                        async Task doReject()
                        {
                            
                            CallRequest callRequestRC = x2.CallRequest;

                            client = GetNortheastClient(AaaContractor.GetById(x2.Contractor.AaaContractorId), qi, callRequestRC.CallRequestId, callRequestRC.DispatchEntryId, x2.Job?.DispatchId);
                            var contractor = x2.Contractor;
                            AaaDispatch dispatchRC = x2.Job;

                            if (callRequestRC == null)
                            {
                                await sourceMessage.DeadLetterAsync();
                                return;
                            }

                            MasterAccountReason rejectReason = MasterAccountReason.GetById(Convert.ToInt32(jsonObj.MasterAccountReasonId));

                            if (rejectReason == null)
                            {
                                await sourceMessage.DeadLetterAsync("Attempted to reject callRequestId " + callRequestRC.CallRequestId +
                                    ", but no reason is present in database for it",
                                    callRequestRC.ToJson(true));
                                await callRequestRC.UpdateStatus(CallRequestStatus.RejectFailed);
                                return;
                            }

                            client.Refuse(new AaaNortheastRestClient.DispatchRefuseModel()
                            {
                                ContractorId = contractor.ContractorId,
                                DispatchId = dispatchRC.DispatchId,
                                LocationId = contractor.LocationCode,
                                ReasonId = (int)rejectReason.MasterAccountReasonId,
                                ReasonName = rejectReason.Code
                            });

                            //TODO: Parse response to see if everything went ok (when we get an actual response :))

                            await callRequestRC.UpdateStatus(CallRequestStatus.Rejected);

                            await callRequestRC.Save();

                            qi.CallRequestId = callRequestRC.CallRequestId;
                            DigitalDispatchService.LogAction(qi);

                            await sourceMessage.CompleteAsync();
                            DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                        }
                        try
                        {
                            await doReject();
                        }
                        catch(Exception y)
                        {
                            if (y.Message == "Network Error, retry recommended.")
                                break;

                            logger.Error(MasterAccountTypes.AaaNortheast,
                                "Reject",
                                "Failed: " + y.Message,
                                null,
                                callRequestId: x2.CallRequest?.CallRequestId ?? null,
                                companyId: qi.CompanyId.GetValueOrDefault(),
                                data: new
                                {
                                    exception = y
                                },
                                queueItemId: qi.QueueItemId);

                            try
                            {
                                await sourceMessage.CompleteAsync();
                            }
                            catch { }

                            if (x2.CallRequest != null)
                            {
                                await x2.CallRequest.UpdateStatus(CallRequestStatus.RejectFailed);
                            }
                            DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                        }
                        break;

                    case DigitalDispatchActionQueueItemType.OutgoingStatusUpdate:
                        string newStatusName = null;
                        async Task doStatusUpdate()
                        {
                            CallRequest callRequestSU = CallRequest.GetById(Convert.ToInt32(jsonObj.CallRequestId));
                            var call = Entry.GetByIdNoCache(callRequestSU.DispatchEntryId.GetValueOrDefault());

                            if (callRequestSU == null)
                            {
                                logger.Log(LogLevel.Error, "CR" + jsonObj.CallRequestId + ": Couldn't find CallRequestId.");
                                logger.Log(LogLevel.Error, "CR" + JsonExtensions.ToJson(jsonObj));

                                await sourceMessage.DeadLetterAsync();
                                return;
                            }
                            var dispatchSU = AaaDispatch.GetByCallRequestId(Convert.ToInt32(callRequestSU.CallRequestId));
                            var contractor = AaaContractor.GetById(dispatchSU.AaaContractorId);

                            if (contractor == null)
                            {
                                logger.Error(MasterAccountTypes.AaaNortheast,
                                     "StatusUpdate",
                                     "ContractorID isn't registered as an active connection.",
                                     contractor.ContractorId,
                                     null,
                                     dispatchSU.DispatchId,
                                     qi.CompanyId,
                                     new
                                     {
                                         request = jsonObj
                                     },
                                     callId: callRequestSU.DispatchEntryId,
                                     callRequestId: callRequestSU.CallRequestId,
                                     queueItemId: qi.QueueItemId);

                                await sourceMessage.CompleteAsync();
                                DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                                return;
                            }

                            client = GetNortheastClient(contractor, qi, callRequestSU.CallRequestId, callRequestSU.DispatchEntryId, dispatchSU.DispatchId);

                            if (Core.GetRedisValue(call.Id + ":ace_compl") != null)
                            {
                                logger.Info(MasterAccountTypes.AaaNortheast,
                                     "StatusUpdate",
                                     "Call was already marked Completed, not sending.",
                                     contractor.ContractorId,
                                     null,
                                     dispatchSU.DispatchId,
                                     qi.CompanyId,
                                     new
                                     {
                                         request = jsonObj
                                     },
                                     callId: callRequestSU.DispatchEntryId,
                                     callRequestId: callRequestSU.CallRequestId,
                                     queueItemId: qi.QueueItemId);

                                await sourceMessage.CompleteAsync();
                                DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);

                                return;
                            }


                            double? lat = (double?)jsonObj.Latitude;
                            double? lng = (double?)jsonObj.Longitude;
                            int newStatusId = (int)jsonObj.NewStatusId;

                            string statusUpdate;
                            if (newStatusId == Status.Dispatched.Id)
                            {
                                statusUpdate = "DISPATCHED";
                            }
                            else if (newStatusId == Status.EnRoute.Id)
                            {
                                statusUpdate = "EN_ROUTE";
                            }
                            else if (newStatusId == Status.AtSite.Id)
                            {
                                statusUpdate = "ON_SCENE";
                            }
                            else if (newStatusId == Status.BeingTowed.Id)
                            {
                                statusUpdate = "TOWING";
                            }
                            else if (newStatusId == Status.DestinationArrival.Id)
                            {
                                statusUpdate = "DESTINATION";
                            }
                            else if (newStatusId == Status.Completed.Id)
                            {
                                statusUpdate = "COMPLETE";
                            }
                            else
                            {
                                await sourceMessage.CompleteAsync();
                                return;
                            }
                            newStatusName = statusUpdate;

                            var aaadriver = new AaaNortheastRestClient.DriverModel();

                            var driver = await Driver.GetByIdAsync((int)jsonObj.DriverId);
                            if (driver != null)
                            {
                                string firstName = "",
                                    lastName = "";

                                if (driver.Name.Contains(" "))
                                {
                                    // ignore numbers
                                    var tname = new String(driver.Name.Where(r => !Char.IsDigit(r)).ToArray());

                                    var names = tname.Split(new char[] { ' ' }, 2, StringSplitOptions.RemoveEmptyEntries);
                                    if (names.Length == 2)
                                    {
                                        firstName = Core.FormatName(names[0]);
                                        lastName = Core.FormatName(names[1]);
                                    }
                                    else
                                        firstName = Core.FormatName(driver.Name);
                                }
                                else
                                {
                                    firstName = Core.FormatName(driver.Name);
                                }

                                aaadriver.Id = driver.Id.ToString();
                                aaadriver.FirstName = firstName;
                                aaadriver.LastName = lastName;
                            }

                            if (Core.GetRedisValue(call.Id + ":ace_compl") == "1")
                            {
                                logger.Log(LogLevel.Warn, "CR " + jsonObj.CallRequestId + ": " +
                                    "Already completed; not sending any future status updates. " +
                                    qi.JsonObject);

                                statusUpdate = "";
                            }

                            if (statusUpdate != "")
                            {
                                if (statusUpdate == "COMPLETE")
                                {
                                    var list = new List<AaaNortheastRestClient.DispatchCompleteModel.InvoiceItemModel>();

                                    var codes = new string[] { "DL", "DS","FB","FD","FL","FM","LS","LT","MA","MD",
                                        "ML","MT","MU","OM","PR","PY","RV","S1","S2","SM","ST","TL","TM","TR","TW" };

                                    foreach (var line in call.InvoiceItems)
                                    {
                                        string bc = null;

                                        if (line.RateItem?.Predefined?.Id == PredefinedRateItem.BUILTIN_MILEAGE_UNLOADED &&
                                            line.Price > 0 && line.Quantity > 0)
                                            bc = "OM";
                                        else if (line.RateItem?.Predefined?.Id == PredefinedRateItem.BUILTIN_MILEAGE_LOADED &&
                                            line.Price > 0 && line.Quantity > 0)
                                            bc = "TW";
                                        else
                                        {
                                            foreach (var code in codes)
                                            {
                                                if (line.Name.StartsWith(code + " -"))
                                                {
                                                    bc = code;
                                                    break;
                                                }
                                            }
                                        }

                                        if (bc != null)
                                            list.Add(new AaaNortheastRestClient.DispatchCompleteModel.InvoiceItemModel()
                                            {
                                                Code = bc,
                                                Price = line.Price,
                                                Quantity = line.Quantity
                                            });
                                    }

                                    var rc = "";

                                    if (jsonObj.CompletionReasonId != null)
                                    {
                                        int completionReason = Convert.ToInt32(jsonObj.CompletionReasonId);
                                        if (completionReason != 0)
                                            rc = MasterAccountReason.GetById(completionReason).Code;
                                    }

                                    // verify that On Site status was sent: 

                                    await SendFakeStatusIfNotAlreadySent(":ace_su_dispatched", AaaNortheastRestClient.StatusCodes.Dispatched, "COMPLETE");
                                    await SendFakeStatusIfNotAlreadySent(":ace_su_enroute", AaaNortheastRestClient.StatusCodes.Enroute, "COMPLETE");
                                    await SendFakeStatusIfNotAlreadySent(":ace_su_onsite", AaaNortheastRestClient.StatusCodes.OnScene, "COMPLETE");

                                    client.Complete(new AaaNortheastRestClient.DispatchCompleteModel()
                                    {
                                        ContractorId = contractor.ContractorId,
                                        DispatchId = dispatchSU.DispatchId,
                                        ResolutionCode = rc,
                                        Driver = aaadriver,
                                        InvoiceItems = list.ToArray(),
                                        Odometer = call.Odometer,
                                        Vin = call.VIN,
                                        DispatchedTime = call.DispatchTime,
                                        EnrouteTime = call.EnrouteTime,
                                        OnSceneTime = call.ArrivalTime,
                                        TowingTime = call.TowTime,
                                        DestinationTime = call.DestinationArrivalTime,
                                        CompletionTime = call.CompletionTime
                                    });

                                    if (qi.ScheduledDate != null)
                                    {
                                        call.SetAttribute(Dispatch.AttributeValue.BUILTIN_DISPATCH_COMPLETION_ACKNOWLEDGEMENT_JSON,
                                            new AcknowledgeJson()
                                            {
                                                Confirmed = true,
                                                Timestamp = DateTime.UtcNow,
                                                UserId = qi.OwnerUserId != null ? qi.OwnerUserId.Value : NortheastUserId
                                            }.ToJson());
                                    }

                                    await call.Save(false, new AuthenticationToken()
                                    {
                                        UserId = qi.OwnerUserId != null ? qi.OwnerUserId.Value : NortheastUserId,
                                        ClientVersionId = MyClientVersionId
                                    });

                                    Core.SetRedisValue(call.Id + ":ace_compl", "1", TimeSpan.FromHours(48));
                                    Core.DeleteRedisKey(call.Id + ":ace_su_onsite");
                                }
                                else
                                {
                                    // Send a dispatched status if it was never sent. 

                                    if (Core.GetRedisValue(call.Id + ":ace_su_dispatched") == null && 
                                        newStatusId > Status.Dispatched.Id)
                                    {
                                        await SendFakeStatusIfNotAlreadySent(":ace_su_dispatched", AaaNortheastRestClient.StatusCodes.Dispatched, statusUpdate);
                                    }

                                    if (Core.GetRedisValue(call.Id + ":ace_su_enroute") == null &&
                                        newStatusId > Status.EnRoute.Id)
                                    {
                                        await SendFakeStatusIfNotAlreadySent(":ace_su_enroute", AaaNortheastRestClient.StatusCodes.Enroute, statusUpdate);
                                    }

                                    if (Core.GetRedisValue(call.Id + ":ace_su_onsite") == null && 
                                        newStatusId > Status.AtSite.Id)
                                    {
                                        await SendFakeStatusIfNotAlreadySent(":ace_su_onsite", AaaNortheastRestClient.StatusCodes.OnScene, statusUpdate);
                                    }

                                    client.StatusUpdate(new AaaNortheastRestClient.DispatchStatusUpdateModel()
                                    {
                                        ContractorId = contractor.ContractorId,
                                        DispatchId = dispatchSU.DispatchId,
                                        Status = statusUpdate,
                                        Driver = aaadriver
                                    });

                                    if (statusUpdate == AaaNortheastRestClient.StatusCodes.OnScene)
                                        Core.SetRedisValue(call.Id + ":ace_su_onsite", "1", TimeSpan.FromHours(48));
                                    else if (statusUpdate == AaaNortheastRestClient.StatusCodes.Enroute)
                                        Core.SetRedisValue(call.Id + ":ace_su_enroute", "1", TimeSpan.FromHours(48));
                                    else if (statusUpdate == AaaNortheastRestClient.StatusCodes.Dispatched)
                                        Core.SetRedisValue(call.Id + ":ace_su_dispatched", "1", TimeSpan.FromHours(48));
                                    
                                }
                            }

                            await sourceMessage.CompleteAsync();

                            DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);


                           
                            async Task SendFakeStatusIfNotAlreadySent(string key, string status, string desiredStatus)
                            {
                                if (Core.GetRedisValue(call.Id + key) == null)
                                {
                                    client.StatusUpdate(new AaaNortheastRestClient.DispatchStatusUpdateModel()
                                    {
                                        ContractorId = contractor.ContractorId,
                                        DispatchId = dispatchSU.DispatchId,
                                        Status = status,
                                        Driver = aaadriver
                                    });

                                    Core.SetRedisValue(call.Id + key, "2", TimeSpan.FromHours(24));

                                    logger.Warn(MasterAccountTypes.AaaNortheast, "StatusUpdate",
                                        "Sent fake " + status + " status update so that " + desiredStatus + " won't fail.",
                                         contractor.ContractorId, 
                                         dispatchId: dispatchSU.DispatchId, 
                                         companyId: callRequestSU.CompanyId,
                                         callRequestId: callRequestSU.CallRequestId,
                                         queueItemId: qi.QueueItemId);

                                    await Task.Delay(5000);
                                }
                            }
                        }

                        try
                        {
                            await doStatusUpdate();
                        }
                        catch (Exception y)
                        {
                            if (y.Message == "Network Error, retry recommended.")
                                break;

                            try
                            {
                                await sourceMessage.CompleteAsync();
                            }
                            catch { }


                            int errorCallRequestId = 0;
                            int errorCallId = 0;
                            try
                            {
                                var callRequestSU = CallRequest.GetById(Convert.ToInt32(jsonObj.CallRequestId));

                                if (callRequestSU != null)
                                {
                                    var call = Entry.GetByIdNoCache(callRequestSU.DispatchEntryId.GetValueOrDefault());

                                    if (call != null)
                                    {
                                        errorCallRequestId = callRequestSU.CallRequestId;
                                        errorCallId = call.Id;

                                        var en = new EntryNote() { DispatchEntryId = call.Id, OwnerUserId = 1 };
                                        en.Content = "Failed to share " + newStatusName + " Status Update with AAA: " + y.Message;
                                        en.CreateDate = DateTime.Now;
                                        en.OwnerUserId = 1;
                                        await en.SaveAsync();
                                    }
                                }
                            }
                            catch
                            {
                                // silently ignore note failure.
                            }

                            logger.Error(MasterAccountTypes.AaaNortheast,
                                "StatusUpdate",
                                "Failed: " + y.Message,
                                null,
                                null,
                                null,
                                qi.CompanyId,
                                new
                                {
                                    exception = y
                                },
                                callId: errorCallId,
                                callRequestId: errorCallRequestId,
                                queueItemId: qi.QueueItemId);

                            DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                        }
                        break;

                    case DigitalDispatchActionQueueItemType.OutgoingRequestGoa:
                        async Task doGoa()
                        {
                            var requestGoaMsg = JsonConvert.DeserializeObject<RequestGoaMessage>(qi.JsonObject);

                            CallRequest callRequestSU = CallRequest.GetById(requestGoaMsg.CallRequestId);
                            if (callRequestSU == null)
                            {
                                logger.Log(LogLevel.Error, "CR" + jsonObj.CallRequestId + ": Couldn't find CallRequestId.");
                                logger.Log(LogLevel.Error, "CR" + JsonExtensions.ToJson(jsonObj));

                                await sourceMessage.DeadLetterAsync();
                                return;
                            }

                            var dispatchSU = AaaDispatch.GetByCallRequestId(requestGoaMsg.CallRequestId);
                            var contractor = AaaContractor.GetById(dispatchSU.AaaContractorId);

                            if (Core.GetRedisValue(callRequestSU.DispatchEntryId + ":ace_compl") != null)
                            {
                                await sourceMessage.CompleteAsync();
                                DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);

                                logger.Info(MasterAccountTypes.AaaNortheast,
                                     "StatusUpdate",
                                     "Call was already marked Completed, not sending.",
                                     contractor?.ContractorId,
                                     null,
                                     dispatchSU.DispatchId,
                                     qi.CompanyId,
                                     new
                                     {
                                         request = jsonObj
                                     },
                                     queueItemId: qi.QueueItemId);

                                return;
                            }


                            client = GetNortheastClient(contractor, qi, callRequestSU.CallRequestId, callRequestSU.DispatchEntryId, dispatchSU.DispatchId);

                            MasterAccountReason reason = MasterAccountReason.GetById(Convert.ToInt32(jsonObj.ReasonId));

                            client.Goa(new AaaNortheastRestClient.DispatchGoaModel()
                            {
                                ContractorId = contractor.ContractorId,
                                DispatchId = dispatchSU.DispatchId,
                                ResolutionCode = reason.Code,
                            });

                            await sourceMessage.CompleteAsync();

                            DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                        }

                        try
                        {
                            await doGoa();
                        }
                        catch (Exception y)
                        {
                            logger.Log(LogLevel.Warn,
                                "AAA " + jsonObj.CallRequestId + ", QueueItemId: " + qi.QueueItemId + ": GOA Failed: " + qi.JsonObject);

                            if (y.Message == "Network Error, retry recommended.")
                                break;

                            try
                            {
                                CallRequest callRequestSU = CallRequest.GetById(Convert.ToInt32(jsonObj.CallRequestId));
                                var call = Entry.GetByIdNoCache(callRequestSU.DispatchEntryId.GetValueOrDefault());
                                logger.Error(MasterAccountTypes.AaaNortheast,
                                    "GOA",
                                    "Failed: " + y.Message,
                                    null,
                                    callRequestId: callRequestSU.CallRequestId,
                                    companyId: qi.CompanyId.GetValueOrDefault(),
                                    data: new
                                    {
                                        exception = y
                                    },
                                    callId: callRequestSU.DispatchEntryId.GetValueOrDefault(),
                                    queueItemId: qi.QueueItemId,
                                    poNumber: call?.PurchaseOrderNumber);

                                var en = new EntryNote() { DispatchEntryId = call.Id, OwnerUserId = 1 };
                                en.Content = "Sending GOA Status to AAA failed with error: " + y.Message;
                                en.CreateDate = DateTime.Now;
                                en.OwnerUserId = 1;
                                await en.SaveAsync();
                            }
                            catch
                            {
                                // silently ignore note failure.
                            }
                            try
                            {
                                await sourceMessage.CompleteAsync();
                            }
                            catch
                            {

                            }
                            DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                        }
                        break;
                        

                    default:
                        await sourceMessage.DeadLetterAsync("No implementation written", qi.ToJson());
                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                        logger.LogEvent("Queue item doesn't have a Type set that we have a implementation written for... MessageId {0}, Body = {1}", qi.CompanyId, LogLevel.Error, sourceMessage.Message.MessageId, qi.ToJson());
                        return;
                }
            }
        }
    }
}
    
