using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using Azure.Messaging.ServiceBus;
using Extric.Towbook.Accounts;
using Extric.Towbook.Dispatch;
using Extric.Towbook.EventNotifications;
using Extric.Towbook.Generated;
using Extric.Towbook.Integration;
using Extric.Towbook.Integration.MotorClubs;
using Extric.Towbook.Integration.MotorClubs.Queue;
using Extric.Towbook.Integration.MotorClubs.Services;
using Extric.Towbook.Storage;
using Extric.Towbook.Utility;
using Microsoft.Azure.Cosmos.Linq;
using Microsoft.Extensions.Hosting;
using NewRelic.Api.Agent;
using Newtonsoft.Json;
using NLog;
using Async = System.Threading.Tasks;

namespace Extric.Towbook.Services.MotorClubDispatchingService
{
    public partial class MotorClubDispatchingService : IHostedService
    {
        [Transaction]
        private async Async.Task HandleTowbookQueueOutgoingMessage(DigitalDispatchActionQueueItem qi, dynamic jsonObj, ProcessMessageEventArgs sourceMessage)
        {
            CallRequest cr = CallRequest.GetById(Convert.ToInt32(jsonObj.CallRequestId ?? jsonObj.Id));
            if (cr == null && qi.Type != DigitalDispatchActionQueueItemType.OutgoingSharePhoto && 
                qi.Type !=  DigitalDispatchActionQueueItemType.OutgoingShareSignature && 
                qi.Type != DigitalDispatchActionQueueItemType.OutgoingShareFile)
            {
                logger.Log(LogLevel.Error, "CR" + jsonObj.Id + ": Couldn't find CallRequestId.");
                logger.Log(LogLevel.Error, "CR" + jsonObj.Id + ": " + JsonExtensions.ToJson(jsonObj));

                await sourceMessage.DeadLetterAsync();
                return;
            }

            if (cr != null)
            {
                if (jsonObj.OwnerUserId != null)
                {
                    cr.OwnerUserId = Convert.ToInt32(jsonObj.OwnerUserId);
                }
                else
                {
                    cr.OwnerUserId = qi.OwnerUserId;
                }
            }
            switch (qi.Type)
            {
                case DigitalDispatchActionQueueItemType.OutgoingAcceptCall:
                    string responseAccept = "";
                    try
                    {
                        logger.Log(LogLevel.Info, $"Towbook/Accepting Towbook CallRequestId={cr.CallRequestId}/OriginatingCallId={cr.PurchaseOrderNumber}");

                        async Task<Entry> doAccept()
                        {
                            int id = Convert.ToInt32(cr.PurchaseOrderNumber);

                            var sourceCall = Entry.GetByIdNoCache(id);
                            var newCall = await Entry.DuplicateFrom(sourceCall, cr.CompanyId);
                            var company = await Company.Company.GetByIdAsync(cr.CompanyId);

                            newCall.AccountId = cr.AccountId;
                            newCall.ArrivalETA = DateTime.Now.AddMinutes(Convert.ToInt32(jsonObj.Eta));
                            newCall.CreateDate = DateTime.Now;
                            newCall.DispatchTime = null;
                            newCall.OwnerUserId = cr.OwnerUserId ?? 1;
                            newCall.ReasonId = (await Reason.GetByCompany(company))
                                .Where(o => o.NameMatchable() == newCall.Reason?.NameMatchable()).FirstOrDefault()?.Id ?? newCall.Reason?.Id ?? 0;
                            newCall.Status = Status.Waiting;

                            var user = await User.GetByIdAsync(newCall.OwnerUserId);
                            if (user?.Type == User.TypeEnum.Driver)
                            {
                                var drv = Driver.GetByUserId(user.Id).FirstOrDefault();

                                if (drv != null)
                                {
                                    newCall.DriverId = drv.Id;
                                }
                            }

                            // Since the call is created on the accept here we do not need to store the driver with 
                            // call request in redis
                            if (jsonObj.DriverId != null)
                            {
                                int driverId = Convert.ToInt32(jsonObj.DriverId);
                                if (driverId > 1)
                                {
                                    newCall.DriverId = driverId;
                                    newCall.Status = Extric.Towbook.Dispatch.Status.Dispatched;
                                }
                            }

                            if (newCall.Notes != null && newCall.Notes.Contains("["))
                                newCall.Notes = string.Join("\n", 
                                        newCall.Notes.Split('\n')
                                        .Where(o => !o.StartsWith("[") && !o.EndsWith("]")));

                            var io = await InvoiceOptions.GetByCompanyIdAsync(sourceCall.CompanyId);

                            if (!string.IsNullOrWhiteSpace(io?.DefaultInvoiceNumberPrefix))
                                newCall.PurchaseOrderNumber = io.DefaultInvoiceNumberPrefix + sourceCall.CallNumber;

                            await newCall.Save();

                            if (sourceCall.Impound)
                            {
                                var imp = Impounds.Impound.GetByDispatchEntry(sourceCall);
                                if (imp != null)
                                {
                                    if (imp.Lot != null)
                                    {
                                        var lots = await Impounds.Lot.GetByCompanyAsync(company);
                                        
                                        var acc = await Account.GetByIdAsync(newCall.AccountId);
                                        var accountPreferredLot = lots.FirstOrDefault(o => o.Id == acc?.ImpoundDestinationStorageLotId);
                                        
                                        var lotToUse = accountPreferredLot
                                            ?? lots.FirstOrDefault(o => string.Equals(o.Name, imp.Lot.Name)) 
                                            ?? lots.FirstOrDefault();

                                        if (lotToUse != null)
                                        {
                                            var newImp = new Impounds.Impound()
                                            {
                                                Account = newCall.Account,
                                                Company = newCall.Company,
                                                Invoice = newCall.Invoice,
                                                DispatchEntry = newCall,
                                                Lot = lotToUse,
                                                OwnerUserId = newCall.OwnerUserId,
                                                ImpoundDate = newCall.CreateDate
                                            };
                                            await newImp.Save(User.GetById(qi.OwnerUserId.Value));

                                            if (imp.Hold)
                                            {
                                                newCall.SetAttribute(Dispatch.AttributeValue.BUILTIN_SUBCONTRACTOR_IMPOUND_HOLD_JSON, new { Hold = true }.ToJson());
                                                await newCall.Save();
                                            }
                                        }
                                    }
                                }
                            }

                            if (sourceCall.CompanyId == 96242 || 
                                sourceCall.CompanyId == 101960 ||
                                await sourceCall.Company.HasFeatureAsync(Features.DispatchToSubcontractors_SendtoAllProviders))
                            {
                                sourceCall.Notes = "[" + newCall.Company?.Name + "/" + (await User.GetByIdAsync(qi.OwnerUserId.Value))?.FullName + " accepted.]\n" +
                                    sourceCall.Notes;
                            }
                            else
                            {
                                sourceCall.InvoiceNumber = newCall.CallNumber.ToString();
                                sourceCall.ArrivalETA = newCall.ArrivalETA;
                            }

                            sourceCall.SetAttribute(Dispatch.AttributeValue.BUILTIN_SUBCONTRACTOR_RESPONSE_JSON,
                                new SubcontractorResponseModel("accepted", cr.CompanyId).ToJson());




                            await sourceCall.Save();




                            if (sourceCall.CompanyId == 96242 || 
                                sourceCall.CompanyId == 101960 ||
                                await sourceCall.Company.HasFeatureAsync(Features.DispatchToSubcontractors_SendtoAllProviders))
                            {
                                var qc = ServiceBusHelper.CreateProducerQueueAsync("autocancel");
                                #region auto cancel if not on scene. 
                                var bm5 = new BrokeredMessage(
                                    new AutoCancelModel(
                                        "auto_cancel",
                                        newCall.Id,
                                        newCall.CompanyId,
                                        "auto cancel after 15 minutes if status isn't on scene",
                                        sourceCall.CompanyId).ToJson());

                                bm5.TimeToLive = TimeSpan.FromMinutes(20);
                                bm5.ScheduledEnqueueTime = DateTime.UtcNow.AddMinutes(15);

                                await qc.Result.SendAsync(bm5);

                                #endregion
                            }


                            logger.Info(MasterAccountTypes.Towbook, 
                                "CallAccepted", 
                                "Accepted and created call in destination successfully.", 
                                companyId: newCall.CompanyId, 
                                data: new
                                {
                                    companyName = newCall.Company.Name,
                                    sourceCompanyId = sourceCall.CompanyId,
                                    sourceCompanyName = sourceCall.Company.Name,
                                    sourceCallId = id,
                                }, 
                                callId: newCall.Id, 
                                callRequestId: cr.CallRequestId,
                                callNumber: newCall.CallNumber);

                            cr.DispatchEntryId = newCall.Id;
                            await cr.Save();

                            return newCall;
                        }

                        await cr.UpdateStatus(CallRequestStatus.Accepting);

                        var fe = await doAccept();

                        // DO NOT set the PO number here to the PO of the call. It needs to retain the original ID, which 
                        // is the dispatchEntryId from the sender.
                        await cr.UpdateStatus(CallRequestStatus.Accepted); //, po: fe.PurchaseOrderNumber);

                        qi.CallRequestId = cr.CallRequestId;
                        DigitalDispatchService.LogAction(qi);

                        await sourceMessage.CompleteAsync();
                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                    }
                    catch (Exception exc)
                    {
                        logger.Log(LogLevel.Error, "Error accepting Towbook call..." + exc.Message + "\n" + exc.ToJson() + "...Response=" + (responseAccept ?? ""));
                        await sourceMessage.DeadLetterAsync("Attempted to accept callRequestId " + cr.CallRequestId + ", but  error occurred",
                            exc.ToJson(true));
                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                    }
                    break;

                case DigitalDispatchActionQueueItemType.OutgoingRejectCall:

                    async Task<bool> doRefuse()
                    {
                        logger.Log(LogLevel.Info, $"Towbook/Rejecting Towbook CallRequestId={cr.CallRequestId}/CaseId={cr.PurchaseOrderNumber}");

                        int id = Convert.ToInt32(cr.PurchaseOrderNumber);

                        var sourceCall = await Entry.GetByIdNoCacheAsync(id);

                        sourceCall.SetAttribute(Dispatch.AttributeValue.BUILTIN_SUBCONTRACTOR_RESPONSE_JSON,
                            new SubcontractorResponseModel("refused", cr.CompanyId).ToJson());

                        await sourceCall.Save();
                        logger.Log(LogLevel.Info, $"Towbook/Rejected Towbook CallRequestId={cr.CallRequestId}/CaseId={cr.PurchaseOrderNumber}");

                        var rotationMode = sourceCall.GetAttribute(Dispatch.AttributeValue.BUILTIN_SUBCONTRACTOR_ROTATION);
                        if (rotationMode == "1")
                        {
                            var currentCompany = qi.CompanyId;


                            if (sourceCall.CompanyId == 49039)
                            {
                                logger.Log(LogLevel.Info, $"Towbook/Rejected Towbook CallRequestId={cr.CallRequestId}/CaseId={cr.PurchaseOrderNumber}...Not sending to next provider.");

                                return false;
                            }

                            var nextUp = RotationStatusHandler.Pull(sourceCall.AccountId, sourceCall.BodyType?.Id ?? 1);
                            var nextUpAccountId = nextUp.Rotation.SubcontractorAccountId;

                            var ac = await Account.GetByIdAsync(nextUpAccountId);

                            if (ac != null)
                            {
                                var companyAccount = ac.ReferenceNumber?.Split('|');
                                int subCompanyId = 0;
                                int subAccountId = 0;
                                if (companyAccount != null && companyAccount.Length == 2)
                                {
                                    subCompanyId = Convert.ToInt32(companyAccount[0]);
                                    subAccountId = Convert.ToInt32(companyAccount[1]);
                                }
                                if (subCompanyId != cr.CompanyId)
                                {
                                    var existing = CallRequest.GetByForeignId(subAccountId, sourceCall.Id.ToString());
                                    if (existing == null)
                                    {

                                        var destCr = new CallRequest()
                                        {
                                            CompanyId = subCompanyId,
                                            AccountId = subAccountId,
                                            PurchaseOrderNumber = sourceCall.Id.ToString(),
                                            ExpirationDate = DateTime.Now.AddSeconds(91),
                                            RequestDate = DateTime.Now,
                                            ProviderId = sourceCall.CompanyId.ToString(),
                                            Reason = sourceCall.Reason?.Name ?? "",
                                            ServiceNeeded = sourceCall.Reason?.Name ?? "",
                                            StartingLocation = sourceCall.TowSource,
                                            TowDestination = sourceCall.TowDestination,
                                            Vehicle = (sourceCall.Assets.FirstOrDefault()?.Year ?? sourceCall.Year) + " " + sourceCall.VehicleMake + " " + sourceCall.VehicleModel
                                        };

                                        await destCr.SaveAsync();

                                        await SendExpiredRequestEvent(destCr.CallRequestId, destCr.ExpirationDate.Value);

                                        sourceCall.SetAttribute(Dispatch.AttributeValue.BUILTIN_SUBCONTRACTOR_ID, nextUpAccountId.ToString());
                                        await sourceCall.Save();

                                        logger.Log(LogLevel.Info,
                                            $"Towbook/Rotation: Sent to next company CallRequestId={destCr.CallRequestId}/CaseId={destCr.PurchaseOrderNumber}, {nextUp.ToJson()}");
                                    }
                                }
                            }
                        }
                        else if (rotationMode == "2")
                        {
                            // TODO: DUPLICATED FROM MotorClubDispatchingService.cs LINE 718. Move to method that can be used in both places.
                            // send to next nearest provider.

                            var nearestJson = Core.GetRedisValue("dts_nearest:" + sourceCall.Id);
                            if (nearestJson == null)
                                throw new Exception("no dts_nearest" + sourceCall.Id);

                            var distances = JsonConvert.DeserializeObject<List<SubcontractorDistanceModel>>(nearestJson);

                            for (var i = 0; i < distances.Count; i++)
                            {
                                if (distances[i].CompanyId == cr.CompanyId)
                                {
                                    if (!(distances.Count > i))
                                        break;

                                    var next = distances[i + 1];

                                    var existing = CallRequest.GetByForeignId(next.AccountId, sourceCall.Id.ToString());
                                    if (existing == null)
                                    {
                                        // don't send it a second time
                                        var destCr = new CallRequest()
                                        {
                                            CompanyId = next.CompanyId,
                                            AccountId = next.AccountId,
                                            PurchaseOrderNumber = sourceCall.Id.ToString(),
                                            ExpirationDate = DateTime.Now.AddSeconds(91),
                                            RequestDate = DateTime.Now,
                                            ProviderId = sourceCall.CompanyId.ToString(),
                                            Reason = sourceCall.Reason?.Name ?? "",
                                            ServiceNeeded = sourceCall.Reason?.Name ?? "",
                                            StartingLocation = sourceCall.TowSource,
                                            TowDestination = sourceCall.TowDestination,
                                            Vehicle = (sourceCall.Assets.FirstOrDefault()?.Year ?? sourceCall.Year) + " " + sourceCall.VehicleMake + " " + sourceCall.VehicleModel
                                        };

                                        await destCr.SaveAsync();

                                        await SendExpiredRequestEvent(destCr.CallRequestId, destCr.ExpirationDate.Value);

                                        sourceCall.SetAttribute(Dispatch.AttributeValue.BUILTIN_SUBCONTRACTOR_ID, next.SenderAccountId.ToString());

                                        await sourceCall.Save();

                                        logger.Log(LogLevel.Info, "Towbook/Rotation/Expired: Sent to next company CallRequestId={0}/CaseId={1}," +
                                            next.ToJson(), destCr.CallRequestId, destCr.PurchaseOrderNumber);
                                    }
                                    else
                                    {
                                        logger.Log(LogLevel.Info, "Towbook/Rotation/Rejected: Stopping rotation for this call; this provider was already tried once.  " +
                                            "CallRequestId={0}/CaseId={1}," +
                                            next.ToJson(), existing.CallRequestId, existing.PurchaseOrderNumber);
                                    }

                                    break;
                                }

                                await AutoDispatch.AutoDispatchServiceBusHandler.RecommendCancel(sourceCall,
                                    "No providers accepted the offer. Cancel or manually re-dispatch to a provider that will take the call.");
                            }
                        }
                        return true;
                    }

                    await doRefuse();

                    await cr.UpdateStatus(CallRequestStatus.Rejected);
                    await cr.Save();

                    qi.CallRequestId = cr.CallRequestId;
                    DigitalDispatchService.LogAction(qi);

                    await sourceMessage.CompleteAsync();
                    DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);

                    break;

                case DigitalDispatchActionQueueItemType.OutgoingCallCanceled:
                case DigitalDispatchActionQueueItemType.OutgoingStatusUpdate:
                case DigitalDispatchActionQueueItemType.OutgoingReassignJob:
                    async Task<bool> doStatusUpdate()
                    {
                        logger.Log(LogLevel.Info, "Towbook/UpdatingStatus/CallRequestId={0}/CaseId={1}", cr.CallRequestId, cr.PurchaseOrderNumber);

                        var company = await Company.Company.GetByIdAsync(cr.CompanyId);

                        if (cr.DispatchEntryId == null)
                        {
                            // it was never accepted, so no need to cancel it.
                            return true;
                        }

                        var en = Entry.GetByIdNoCache(cr.DispatchEntryId.Value);

                        if (qi.Type == DigitalDispatchActionQueueItemType.OutgoingReassignJob)
                        {
                            logger.Log(LogLevel.Info, "Towbook/ReassignDriver/CallRequestId={0}/CaseId={1} ",
                                cr.CallRequestId, cr.PurchaseOrderNumber, Convert.ToInt32(jsonObj.NewStatusId),
                                JsonExtensions.ToJson(jsonObj));    
                        }

                        var requestingCall = Entry.GetByIdNoCache(Convert.ToInt32(cr.PurchaseOrderNumber));

                        if (requestingCall != null)
                        {
                            var sub = requestingCall.GetAttribute(Dispatch.AttributeValue.BUILTIN_SUBCONTRACTOR_ID);
                            if (sub == "-10" || 
                                (sub == null && await requestingCall.Company.HasFeatureAsync(Generated.Features.DispatchToSubcontractors)))
                            {
                                logger.Info(MasterAccountTypes.Towbook, "StatusUpdate",
                                    "Ignored: Final subcontractor hasn't been picked yet, so we don't want to share status back.",
                                    callRequestId: cr.CallRequestId,
                                    callId: requestingCall.Id);

                                return false;
                            }

                            if (qi.Type == DigitalDispatchActionQueueItemType.OutgoingCallCanceled)
                            {
                                if (requestingCall.Status.Id != Status.Cancelled.Id)
                                {
                                    // TODO: change it to have a Cancelled flag or something like we do for auto dispatch warnings.

                                    if (sub != null)
                                    {
                                        if (int.TryParse(sub, out var subId))
                                        {
                                            var acc = await Account.GetByIdAsync(subId);
                                            if (acc.ReferenceNumber != null && acc.ReferenceNumber.Contains("|"))
                                            {
                                                var refn = acc.ReferenceNumber.Split('|')[0];

                                                if (en.CompanyId.ToString() != refn)
                                                {
                                                    logger.Info(MasterAccountTypes.Towbook, "OutgoingCallCancel",
                                                        "Ignored: This company isn't the currently assigned subcontractor.",
                                                        callRequestId: cr.CallRequestId,
                                                        callId: en.Id,
                                                        companyId: qi.CompanyId,
                                                        data: new
                                                        {
                                                            sourceCallId = requestingCall.Id,
                                                            sourceCompanyId = requestingCall.CompanyId, 
                                                        });

                                                    return false;
                                                }
                                            }
                                        }
                                    }

                                    requestingCall.Notes = en.Company.Name + " cancelled call at " + 
                                        Core.OffsetDateTime(requestingCall.Company, DateTime.Now).ToShortTowbookTimeString() +
                                        " -  Needs to be re-dispatched or cancelled.\n\n" + requestingCall.Notes;
                                    requestingCall.Status = Status.Waiting;
                                    requestingCall.SetAttribute(Dispatch.AttributeValue.BUILTIN_SUBCONTRACTOR_ID, null);
                                    await requestingCall.Save(false, new AuthenticationToken() { UserId = 1, ClientVersionId = MyClientVersionId });
                                }

                                if (en.Status.Id != Status.Cancelled.Id)
                                {
                                    await en.Cancel(en.CancellationReason,
                                        new AuthenticationToken() { UserId = 1, ClientVersionId = MyClientVersionId });
                                    
                                    // TODO: need to place subcontractor back to next-up spot. 

                                    var rs = RotationSubcontractor.GetBySubcontractorAccountId(
                                        requestingCall.AccountId,
                                        requestingCall.BodyType.Id,
                                        Convert.ToInt32(requestingCall.Attributes[Dispatch.AttributeValue.BUILTIN_SUBCONTRACTOR_ID].Value));

                                    if (rs != null)
                                    {
                                        rs.MakeFirst();
                                        logger.Info(MasterAccountTypes.Towbook, "Cancel", "Marked subcontractor MakeFirst", data: new { subcontractor = rs }, callRequestId: cr.CallRequestId,
                                            callId: requestingCall.Id);
                                    }
                                }

                                logger.Log(LogLevel.Info, "Towbook/Cancel/Towbook CallRequestId={0}/SourceCallId=={1}/ContractorCallId={2}: Cancelled with reason {3}", cr.CallRequestId, requestingCall.Id, en.Id,
                                    requestingCall.CancellationReason);
                            }
                            else
                            {
                                Status newStatus = await Status.GetByIdAsync(Convert.ToInt32(jsonObj.NewStatusId));
                                Status oldStatus = requestingCall.Status;

                                logger.Log(LogLevel.Info, "Towbook/StatusUpdate/Towbook CallRequestId={0}/SourceCallId={1}/DestCallId={2}: SourceStatusId={3}, ContractorStatusId={4} ContractorCall: " +
                                    en.Id + "..." + en.CallNumber + "..." + en.CompanyId,
                                    cr.CallRequestId, cr.PurchaseOrderNumber, cr.DispatchEntryId,
                                    oldStatus.Name, newStatus.Name);

                                // only share if status is enroute, onscene, towing, dest arrival, completed.
                                if (newStatus.Id > Status.Dispatched.Id)
                                    requestingCall.Status = newStatus;

                                if (requestingCall.Status == Status.EnRoute)
                                    requestingCall.EnrouteTime = en.EnrouteTime ?? DateTime.Now;
                                else if (requestingCall.Status == Status.AtSite)
                                    requestingCall.ArrivalTime = en.ArrivalTime ?? DateTime.Now;
                                else if (requestingCall.Status == Status.BeingTowed)
                                    requestingCall.TowTime = en.TowTime ?? DateTime.Now;
                                else if (requestingCall.Status == Status.DestinationArrival)
                                    requestingCall.DestinationArrivalTime = en.DestinationArrivalTime ?? DateTime.Now;
                                else if (requestingCall.Status == Status.Completed)
                                    requestingCall.CompletionTime = en.CompletionTime ?? DateTime.Now;

                                await requestingCall.Save(false);

                                logger.Log(LogLevel.Info, "Towbook/StatusUpdate/Towbook CallRequestId={0}/SourceCallId={1}/DestCallId={2}: SourceStatusId={3}, ContractorStatusId={4} ContractorCall: " +
                                    en.Id + "..." + en.CallNumber + "..." + en.CompanyId,
                                    cr.CallRequestId, cr.PurchaseOrderNumber, cr.DispatchEntryId,
                                    requestingCall.Status.Name, newStatus.Name);
                            }
                        }

                        logger.Log(LogLevel.Info, "Towbook/StatusUpdate/Towbook CallRequestId={0}/SourceCallId=={1}/DestCallId={2}: Finished.", cr.CallRequestId, cr.PurchaseOrderNumber, requestingCall?.Id);

                        return true;
                    }

                    await doStatusUpdate();
                    qi.CallRequestId = cr.CallRequestId;
                    DigitalDispatchService.LogAction(qi);

                    await sourceMessage.CompleteAsync();
                    DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                    break;

                case DigitalDispatchActionQueueItemType.OutgoingRequestGoa:

                    async Task<bool> doCompleteAsGoa()
                    {
                        logger.Log(LogLevel.Info, "Towbook/OutgoingRequestGoa/CallRequestId={0}/CaseId={1}", cr.CallRequestId, cr.PurchaseOrderNumber);

                        var company = await Company.Company.GetByIdAsync(cr.CompanyId);
                        var en = await Entry.GetByIdNoCacheAsync(cr.DispatchEntryId.Value);
                        var towbookTruck = en.Truck;
                        var towbookDriver = await Driver.GetByIdAsync(Convert.ToInt32(jsonObj.DriverId));

                        if (towbookDriver == null)
                        {
                            towbookDriver = en.Driver;
                        }

                        decimal lat = 0, lng = 0;
                        var location = UserLocationHistoryItem.GetCurrentByUserId(towbookDriver.UserId, DateTime.Now.AddMinutes(-30), DateTime.Now);

                        if (location != null)
                        {
                            lat = location.Latitude;
                            lng = location.Longitude;
                        }

                        int userId = qi.OwnerUserId ?? 1;
                        en.Status = Status.Completed;
                        en.Notes = "Call completed as GOA.\n" + en.Notes;
                        var goaReasonCode = (await Reason.GetByCompany(en.Company)).Where(o => o.Name.ToUpperInvariant().Contains("GOA") ||
                            o.Name.ToLowerInvariant().Contains("gone") &&
                            o.Name.ToLowerInvariant().Contains("on") &&
                            o.Name.ToLowerInvariant().Contains("arrival")).FirstOrDefault();
                        if (goaReasonCode != null)
                            en.ReasonId = goaReasonCode.Id;
                        en.CompletionTime = DateTime.Now;
                        await en.Save(false, new AuthenticationToken() { UserId = userId }, "127.0.0.1");

                        logger.Log(LogLevel.Info, "Towbook/RequestGoa/Towbook CallRequestId={0}/CaseId={1}: Finished.", cr.CallRequestId, cr.PurchaseOrderNumber);

                        return true;
                    }

                    await doCompleteAsGoa();
                    qi.CallRequestId = cr.CallRequestId;
                    DigitalDispatchService.LogAction(qi);

                    await sourceMessage.CompleteAsync();
                    DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                    break;

                case DigitalDispatchActionQueueItemType.OutgoingSharePhoto:
                case DigitalDispatchActionQueueItemType.OutgoingShareSignature:

                    // copy the photo

                    async Async.Task SharePhotoOrSignature(int callRequestId, string json)
                    {
                        try
                        {
                            SharePhotoModel pm = JsonConvert.DeserializeObject<SharePhotoModel>(json);

                            cr = CallRequest.GetById(callRequestId);
                            if (cr == null && pm.DispatchEntryId == 0 )
                            {
                                logger.Log(LogLevel.Error, "CR" + cr.CallRequestId + "/C" + cr.CompanyId + "/SharePhoto: Couldn't find CallRequestId.");

                                await sourceMessage.CompleteAsync();
                                return;
                            }

                            if (cr != null)
                                qi.CallRequestId = cr.CallRequestId;

                            int id = cr != null ? Convert.ToInt32(cr.PurchaseOrderNumber) : 0;

                            if (id == 0 && pm.DispatchEntryId > 0)
                            {
                                // this is the SENDER call;
                                // we're sharing a photo with the subcontractor. 
                                // so we need the callId of the subcontractor .

                                var call = Entry.GetByIdNoCache(pm.DispatchEntryId);

                                if (call != null)
                                {
                                    if (call.Attributes.ContainsKey(Dispatch.AttributeValue.BUILTIN_SUBCONTRACTOR_ID) &&
                                        call.Attributes[Dispatch.AttributeValue.BUILTIN_SUBCONTRACTOR_ID].Value != "" &&
                                        call.Attributes[Dispatch.AttributeValue.BUILTIN_SUBCONTRACTOR_ID].Value != "0" &&
                                        call.Attributes[Dispatch.AttributeValue.BUILTIN_SUBCONTRACTOR_ID].Value != "-9")
                                    {
                                        var avSub = Convert.ToInt32(call.Attributes[Dispatch.AttributeValue.BUILTIN_SUBCONTRACTOR_ID].Value);
                                        var senderSub = await Account.GetByIdAsync(avSub);
                                        if (senderSub!= null)
                                        {
                                            if (senderSub.ReferenceNumber != null &&
                                                senderSub.ReferenceNumber.Contains("|"))
                                                avSub = Convert.ToInt32(senderSub.ReferenceNumber.Split('|')[1]);
                                        }

                                        cr = CallRequest.GetByForeignId(avSub, call.Id.ToString());

                                        if (cr?.DispatchEntryId != null)
                                            id = cr.DispatchEntryId.Value;
                                    }
                                }
                            }
                            
                            var callToSavePhotoTo = await Entry.GetByIdNoCacheAsync(id);

                            if (qi.Type == DigitalDispatchActionQueueItemType.OutgoingShareSignature)
                            {
                                var sig = Signature.GetById((int)jsonObj.SignatureId);

                                if (sig == null)
                                {
                                    throw new Exception("no such signature. " + qi.JsonObject);
                                }

                                var signature = new Signature()
                                {
                                    DispatchEntryId = callToSavePhotoTo.Id,
                                    FileSize = sig.FileSize,
                                    RemoteIp = sig.RemoteIp,
                                    OwnerUserId = 1,
                                    ContentType = sig.ContentType,
                                    Description = sig.Description,
                                    Latitude = sig.Latitude,
                                    Longitude = sig.Longitude,
                                    SignatureTypeId = sig.SignatureTypeId
                                };
                                signature.Save();

                                await FileUtility.RemoteCopyFile(
                                    sig.Location.Replace("%1", qi.CompanyId.ToString()),
                                    signature.Location.Replace("%1", callToSavePhotoTo.CompanyId.ToString()));

                                DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                            }
                            else if (qi.Type == DigitalDispatchActionQueueItemType.OutgoingSharePhoto)
                            {
                                var srcPhoto = Dispatch.Photo.GetById((int)jsonObj.PhotoId);
                                if (srcPhoto == null)
                                {
                                    throw new Exception("no such photo. " + qi.JsonObject);
                                }

                                var existingPhoto = Dispatch.Photo.GetByDispatchEntryId(callToSavePhotoTo.Id)
                                    .FirstOrDefault(o => o.FileSize == srcPhoto.FileSize && 
                                    o.CameraLatitude == srcPhoto.CameraLatitude && 
                                    o.DispatchEntryStatusId == srcPhoto.DispatchEntryStatusId);

                                var destPhoto = new Dispatch.Photo()
                                {
                                    CameraLatitude = srcPhoto.CameraLatitude,
                                    CameraLongitude = srcPhoto.CameraLongitude,
                                    ClientVersionId = srcPhoto.ClientVersionId,
                                    DispatchEntryStatusId = srcPhoto.DispatchEntryStatusId,
                                    FileSize = srcPhoto.FileSize,
                                    RemoteIp = srcPhoto.RemoteIp,
                                    OwnerUserId = 1,
                                    ContentType = srcPhoto.ContentType,
                                    Description = srcPhoto.Description,
                                    DispatchEntryId = callToSavePhotoTo.Id
                                };

                                if (existingPhoto != null)
                                    destPhoto.UnsafeSetId(existingPhoto.Id);

                                destPhoto.Save(await User.GetByIdAsync(1));

                                await FileUtility.RemoteCopyFile(
                                    srcPhoto.Location.Replace("%1", qi.CompanyId.ToString()),
                                    destPhoto.Location.Replace("%1", callToSavePhotoTo.CompanyId.ToString()));

                                await Dispatch.Entry.UpdateInsight(callToSavePhotoTo.Id, callToSavePhotoTo.CompanyId, "PhotoCount", Dispatch.Photo.GetCountByDispatchEntryId(callToSavePhotoTo.Id));
                                await PushNotificationProvider.UpdateCall(callToSavePhotoTo.CompanyId, callToSavePhotoTo.Id, "update");

                                DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                            }
                        }
                        catch (Exception y)
                        {
                            logger.Error(MasterAccountTypes.Towbook, "SharePhoto", "Error sending photo: " + y.Message,
                                companyId: qi.CompanyId,
                                data: y,
                                callRequestId: callRequestId);
                        }

                        await sourceMessage.CompleteAsync();
                        DigitalDispatchService.LogAction(qi);
                        return;
                    }

                    await SharePhotoOrSignature(Convert.ToInt32(jsonObj.CallRequestId), qi.JsonObject);

                    break;

                default:
                    await sourceMessage.DeadLetterAsync("No implementation written", qi.ToJson());
                    DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                    logger.LogEvent("Queue item doesn't have a Type set that we have a implementation written for... MessageId {0}, Body = {1}", 
                        qi.CompanyId, LogLevel.Error, sourceMessage.Message.MessageId, qi.ToJson());
                    return;
            }
        }
    }

    public class AutoCancelModel
    {
        public string Type { get; set; }
        public int CallId { get; set; }
        public int CompanyId { get; set; }
        public string Notes { get; set; }
        public int SourceCompanyId { get; set; }

        public AutoCancelModel()
        {

        }

        public AutoCancelModel(string type, int callId, int companyId, string notes, int sourceCompanyId)
        {
            Type = type;
            CallId = callId;
            CompanyId = companyId;
            Notes = notes;
            SourceCompanyId = sourceCompanyId;
        }
    }
}
