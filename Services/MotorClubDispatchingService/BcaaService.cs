using Azure.Messaging.ServiceBus;
using Extric.Towbook.Accounts;
using Extric.Towbook.Dispatch;
using Extric.Towbook.Integration;
using Extric.Towbook.Integration.MotorClubs;
using Extric.Towbook.Integration.MotorClubs.Dispatch;
using Extric.Towbook.Integration.MotorClubs.Model;
using Extric.Towbook.Integration.MotorClubs.Queue;
using Extric.Towbook.Integration.MotorClubs.Services;
using Extric.Towbook.Integration.MotorClubs.Services.Models;
using Extric.Towbook.Integrations.MotorClubs.Aaa;
using Extric.Towbook.Integrations.MotorClubs.Aaa.Bcaa;
using Extric.Towbook.Utility;
using Extric.Towbook.Vehicle;
using NewRelic.Api.Agent;
using Newtonsoft.Json;
using NLog;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Async = System.Threading.Tasks;
using AttributeValue = Extric.Towbook.Dispatch.AttributeValue;

namespace Extric.Towbook.Services.MotorClubDispatchingService
{

    public partial class BcaaService
    {
        private static readonly Logger logger = LogManager.GetCurrentClassLogger();
        private const int BcaaUserId = 22;

        [Transaction]
        public static async Task<bool> HandleBcaaIncomingMessage(DigitalDispatchActionQueueItem qi, AaaMessage jsonObj, ProcessMessageEventArgs sourceMessage)
        {
            var contractor = AaaContractor.GetById(jsonObj.AaaContractorId);

            switch (qi.Type)
            {
                case DigitalDispatchActionQueueItemType.IncomingCallReceived:
                    async Task doReceived()
                    {
                        logger.Info(MasterAccountTypes.Bcaa,
                             "CallReceived",
                             "Incoming Job Offer",
                             contractor.ContractorId,
                             null,
                             jsonObj.DispatchId,
                             qi.CompanyId,
                             new
                             {
                                 request = jsonObj
                             },
                             queueItemId: qi.QueueItemId);

                        var rcr = await MotorClubDispatchingService.CreateCallRequest(jsonObj, MotorClubName.Bcaa);

                        qi.CallRequestId = rcr.CallRequestId;
                        DigitalDispatchService.LogAction(qi);

                        logger.Info(MasterAccountTypes.Bcaa,
                            "CallReceived",
                            "Incoming Job Offer Created",
                            contractor.ContractorId,
                            null,
                            jsonObj.DispatchId,
                            qi.CompanyId,
                            callRequestId: rcr.CallRequestId,
                            queueItemId: qi.QueueItemId);

                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                        await sourceMessage.CompleteAsync();
                    }
                    await doReceived();
                    break;

                case DigitalDispatchActionQueueItemType.IncomingCallRejected:
                    async Task doRejected()
                    {
                        var cc = JsonConvert.DeserializeObject<IncomingRefusedModel>(jsonObj.JsonData);
                        var ccujo = AaaDispatch.GetByDispatchId(jsonObj.DispatchId, jsonObj.AaaContractorId);
                        if (ccujo == null)
                        {
                            logger.Error(MasterAccountTypes.Bcaa,
                                "IncomingCallRejected",
                                "Received cancel for call that doesn't exist",
                                contractor.ContractorId, null, jsonObj.DispatchId, qi.CompanyId,
                                new
                                {
                                    message = cc
                                });

                            DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                            await sourceMessage.CompleteAsync();
                            return;
                        }

                        var ccr = CallRequest.GetById(ccujo.CallRequestId);
                        await ccr.UpdateStatus(CallRequestStatus.RejectedByMotorClub);

                        qi.CallRequestId = ccr.CallRequestId;
                        DigitalDispatchService.LogAction(qi);

                        logger.Log(LogLevel.Info, "BCAA/{0}: Offer Rejected by AAA. Type = {1}, CallRequestId = {2}", qi.QueueItemId, qi.Type, ccr.CallRequestId);
                        
                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                        await sourceMessage.CompleteAsync();
                    }
                    await doRejected();
                    break;

                case DigitalDispatchActionQueueItemType.IncomingCallCancelled:
                    async Task doCancelled()
                    {
                        var icm = JsonConvert.DeserializeObject<IncomingCancelledModel>(jsonObj.JsonData);
                        var ccujo = AaaDispatch.GetByDispatchId(jsonObj.DispatchId, jsonObj.AaaContractorId);
                        if (ccujo == null)
                        {
                            logger.Error(MasterAccountTypes.Bcaa,
                                "IncomingCallCancelled",
                                "Received cancel for call that doesn't exist",
                                contractor.ContractorId, null, jsonObj.DispatchId, qi.CompanyId,
                                new
                                {
                                    message = icm
                                });

                            DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                            await sourceMessage.CompleteAsync();
                            return;
                        }

                        var ccr = CallRequest.GetById(ccujo.CallRequestId);
                        await ccr.UpdateStatus(CallRequestStatus.Cancelled);

                        bool cancelled = false;

                        if (ccr.DispatchEntryId.GetValueOrDefault() > 0)
                        {
                            var en = Entry.GetByIdNoCache(ccr.DispatchEntryId.Value);
                            if (en != null)
                            {
                                if (MotorClubDispatchingService.ShouldAllowCancel(en))
                                {
                                    await en.Cancel("Cancelled by AAA: " + icm.Notes, new AuthenticationToken()
                                    {
                                        UserId = BcaaUserId,
                                        ClientVersionId = MotorClubDispatchingService.MyClientVersionId
                                    }, "127.0.0.1");

                                    cancelled = true;
                                }
                                else
                                {
                                    cancelled = false;
                                }
                            }
                        }

                        qi.CallRequestId = ccr.CallRequestId;
                        DigitalDispatchService.LogAction(qi);

                        if (cancelled)
                        {
                            logger.Log(LogLevel.Info, "BCAA/{0}: Offer Cancelled. Type = {1}, CallRequestId = {2}", qi.QueueItemId, qi.Type, ccr.CallRequestId);
                        }
                        else
                        {
                            logger.Log(LogLevel.Info, "BCAA/{0}: Offer Cancel event received but failed to be canceled. Type = {1}, CallRequestId = {2}", qi.QueueItemId, qi.Type, ccr.CallRequestId);
                        }

                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                        await sourceMessage.CompleteAsync();
                    }
                    await doCancelled();
                    break;

                case DigitalDispatchActionQueueItemType.IncomingCallAccepted:
                    async Task doAccepted()
                    {
                        var accepted = JsonConvert.DeserializeObject<IncomingAcceptedModel>(jsonObj.JsonData);
                        var aaaDispatch = AaaDispatch.GetByDispatchId(jsonObj.DispatchId, jsonObj.AaaContractorId);
                        
                        if (aaaDispatch == null)
                        {
                            logger.Error(MasterAccountTypes.Bcaa,
                                "IncomingCallAccepted",
                                "Received accept for call that doesn't exist",
                                contractor.ContractorId, null, jsonObj.DispatchId, qi.CompanyId,
                                new
                                {
                                    message = accepted
                                });

                            DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                            await sourceMessage.CompleteAsync();
                            return;
                        }

                        var ccr = CallRequest.GetById(aaaDispatch.CallRequestId);

                        await ccr.UpdateStatus(CallRequestStatus.Accepted);

                        await BcaaDoCallCreateOrUpdate(qi, accepted, aaaDispatch, ccr, contractor,sourceMessage);
                    }
                    await doAccepted();
                    break;

                case DigitalDispatchActionQueueItemType.IncomingCallGoaResponse:
                    async Task doGoa()
                    {
                        var cc = JsonConvert.DeserializeObject<IncomingGoaModel>(jsonObj.JsonData);
                        var ccujo = AaaDispatch.GetByDispatchId(jsonObj.DispatchId, jsonObj.AaaContractorId);

                        if (ccujo == null)
                        {
                            logger.Error(MasterAccountTypes.Bcaa,
                                "IncomingCallGoaResponse",
                                "Received GOA accept for call that doesn't exist",
                                contractor.ContractorId, null, jsonObj.DispatchId, qi.CompanyId,
                                new
                                {
                                    message = cc
                                });

                            DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                            await sourceMessage.CompleteAsync();
                            return;
                        }

                        var ccr = CallRequest.GetById(ccujo.CallRequestId);
                        await ccr.UpdateStatus(CallRequestStatus.Cancelled);

                        if (ccr.DispatchEntryId.GetValueOrDefault() > 0)
                        {
                            var en = Entry.GetByIdNoCache(ccr.DispatchEntryId.Value);
                            if (en != null)
                            {
                                var now = Core.OffsetDateTime(en.Company, DateTime.Now);

                                var toAdd = "AAA GOA Response received at " + now.ToShortDateString() + " " + now.ToShortTowbookTimeString();
                                en.Notes = toAdd + "\n" + (en.Notes ?? "");

                                en.SetAttribute(Dispatch.AttributeValue.BUILTIN_BILLING_NOTES,
                                    toAdd + "\n" + en.BillingNotes());

                                en.Status = Status.Completed;

                                await en.Save(false, new AuthenticationToken()
                                {
                                    UserId = BcaaUserId,
                                    ClientVersionId = MotorClubDispatchingService.MyClientVersionId
                                }, "127.0.0.1");
                                logger.Log(LogLevel.Info, "BCAA/{0}: Offer completed by way of GOA response. Type = {1}, CallRequestId = {2}", qi.QueueItemId, qi.Type, ccr.CallRequestId);
                            }
                        }

                        qi.CallRequestId = ccr.CallRequestId;
                        DigitalDispatchService.LogAction(qi);
                        
                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                        await sourceMessage.CompleteAsync();
                    }
                    await doGoa();
                    break;

                case DigitalDispatchActionQueueItemType.IncomingCallUpdate:

                    var updatePayload = JsonConvert.DeserializeObject<DispatchModel>(jsonObj.JsonData);

                    var ccujo = AaaDispatch.GetByDispatchId(updatePayload.DispatchId, jsonObj.AaaContractorId);
                    var ccr = CallRequest.GetById(ccujo.CallRequestId);

                    if (ccr.DispatchEntryId == null)
                    {
                        logger.Info(contractor.MasterAccountId,
                            "CallUpdate",
                            "Failed to Call - DispatchEntryId is null.",
                            contractor.ContractorId,
                            contractor.LocationCode,
                            jsonObj.DispatchId,
                            qi.CompanyId,
                            new
                            {
                                message = updatePayload.ToJson(true)
                            },
                            ownerUserId: qi.OwnerUserId,
                            queueItemId: qi.QueueItemId);
                        
                        qi.CallRequestId = ccr.CallRequestId;
                        DigitalDispatchService.LogAction(qi);

                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                        await sourceMessage.CompleteAsync();
                        break;

                    }

                    var call = await Entry.GetByIdNoCacheAsync(ccr.DispatchEntryId.Value);
                    var data = updatePayload.Job;

                    var originalJob = JsonConvert.DeserializeObject<DispatchModel>(ccujo.DispatchJson).Job;

                    if (!string.IsNullOrWhiteSpace(data.DestinationAddress?.Address))
                    {
                        var dest = call.Waypoints.FirstOrDefault(o => o.Title == "Destination");

                        var destAddress = data.DestinationAddress.ToString();

                        call.TowDestination = destAddress;
                        if (!string.IsNullOrWhiteSpace(call.TowDestination))
                        {
                            if (dest == null)
                            {
                                dest = new EntryWaypoint() { Title = "Destination", Position = 2 };
                                call.Waypoints.Add(dest);
                            }

                            dest.Address = call.TowDestination;

                            var ghr = await GeocodeHelper.Geocode(dest.Address);
                            if (ghr != null)
                            {
                                dest.Latitude = ghr.Latitude;
                                dest.Longitude = ghr.Longitude;
                            }
                            else
                            {
                                dest.Latitude = 0;
                                dest.Longitude = 0;
                            }
                        }
                    }

                    if (!string.IsNullOrWhiteSpace(data.IncidentAddress.ToString()))
                    {
                        var pickup = call.Waypoints.FirstOrDefault(o => o.Title == "Pickup");

                        var breakdown = data.IncidentAddress.ToString();

                        if (!string.IsNullOrWhiteSpace(data.IncidentAddress.Landmark))
                            breakdown += " (" + data.IncidentAddress.Landmark + ")";

                        call.TowSource = breakdown;

                        if (pickup == null)
                        {
                            pickup = new EntryWaypoint()
                            {
                                Title = "Pickup",
                                Position = 1
                            };
                            call.Waypoints.Add(pickup);
                        }
                        pickup.Address = call.TowSource;

                        if (data.IncidentAddress.Latitude != 0 && data.IncidentAddress.Longitude !=0 )
                        {
                            pickup.Latitude = Convert.ToDecimal(data.IncidentAddress.Latitude);
                            pickup.Longitude = Convert.ToDecimal(data.IncidentAddress.Longitude);
                        }
                        else
                        {
                            var ghr = await GeocodeHelper.Geocode(pickup.Address);
                            if (ghr != null)
                            {
                                pickup.Latitude = ghr.Latitude;
                                pickup.Longitude = ghr.Longitude;
                            }
                            else
                            {
                                pickup.Latitude = 0;
                                pickup.Longitude = 0;
                            }
                        }
                    }

                    if (data.Vehicle != null)
                    {
                        var asset = call.Assets.FirstOrDefault();

                        if (asset == null)
                        {
                            asset = new EntryAsset() { BodyTypeId = 1 };
                            call.Assets.Add(asset);
                        }

                        asset.Make = data.Vehicle.Make;
                        asset.Model = data.Vehicle.Model;
                        if (data.Vehicle.Year != null && int.TryParse(data.Vehicle.Year, out int vyear))
                            asset.Year = vyear;


                        var colorId = data.Vehicle.Color != null ? VehicleUtility.GetColorIdByName(data.Vehicle.Color) : 0;
                        if (colorId != 0)
                            asset.ColorId = colorId;

                        if (!string.IsNullOrWhiteSpace(data.Vehicle.LicensePlate))
                            asset.LicenseNumber = data.Vehicle.LicensePlate;
                    }

                    await call.Save();

                    logger.Info(contractor.MasterAccountId,
                        "CallUpdate",
                        "Updated Call",
                        contractor.ContractorId,
                        contractor.LocationCode,
                        jsonObj.DispatchId,
                        qi.CompanyId,
                        new
                        {
                            message = updatePayload.ToJson(true)
                        },
                        ownerUserId: qi.OwnerUserId,
                        queueItemId: qi.QueueItemId);
                    qi.CallRequestId = ccr.CallRequestId;
                    DigitalDispatchService.LogAction(qi);

                    DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                    await sourceMessage.CompleteAsync();
                    break;


                default:
                    logger.Error(MasterAccountTypes.Bcaa,
                        "UnsupportedEvent",
                        "Event Type Not Implemented",
                        contractor.ContractorId,
                        contractor.LocationCode,
                        jsonObj.DispatchId,
                        qi.CompanyId,
                        new
                        {
                            message = jsonObj
                        },
                        ownerUserId: qi.OwnerUserId,
                        queueItemId: qi.QueueItemId);

                    DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                    await sourceMessage.CompleteAsync();
                    return true;
            }

            return true;
        }

        private static async Task<Entry> BcaaDoCallCreateOrUpdate(
            DigitalDispatchActionQueueItem qi,
            IncomingAcceptedModel accepted,
            AaaDispatch dispatch,
            CallRequest callRequest,
            AaaContractor contractor,
            ProcessMessageEventArgs sourceMessage)
        {
            var job = JsonConvert.DeserializeObject<DispatchModel>(dispatch.DispatchJson);

            Entry fe = await DistributedLock.ForAsync("BCAA",
                accepted.DispatchId,
                10000,
                lockAcquired: async delegate ()
                {
                    Entry call = null;

                    var existingAAA = AttributeValue.GetAllByValue(qi.CompanyId.GetValueOrDefault(),
                        AttributeValue.BUILTIN_MOTORCLUB_DISPATCHNUMBER, accepted.DispatchId).LastOrDefault();

                    if (existingAAA != null)
                        call = await Entry.GetByIdNoCacheAsync(existingAAA.DispatchEntryId);

                    if (call != null && call.CreateDate < DateTime.Now.AddDays(-7))
                        call = null;

                    if (call == null)
                    {
                        call = new Entry();
                        call.CompanyId = callRequest.CompanyId;
                        call.AccountId = callRequest.AccountId;
                    }
                    else
                    {
                        logger.LogEvent("BCAA/{0}: Found existing towbook call for Case {1}... Call #{2}... we're going to update this one.",
                            callRequest.CompanyId, LogLevel.Info, sourceMessage.Message.MessageId, callRequest.CallRequestId.ToString(), call.CallNumber);

                        if (call.Status == Status.Cancelled)
                        {
                            logger.LogEvent("BCAA/{0}: Call is cancelled. Proceeding to uncancel and set status to waiting...",
                                callRequest.CompanyId, LogLevel.Info, sourceMessage.Message.MessageId, accepted.PoNumber.ToString(), call.CallNumber);

                            await call.Uncancel(new AuthenticationToken() { UserId = BcaaUserId, ClientVersionId = MotorClubDispatchingService.MyClientVersionId });
                        }
                    }

                    if (job.Job.Priority != null &&
                        job.Job.Priority.StartsWith("P"))
                    {
                        call.Priority = Entry.EntryPriority.High;
                        call.Notes = "Priority Code: " + job.Job.Priority + "\n" +
                            call.Notes;
                    }

                    if (call.Account?.DefaultPriority == 1)
                        call.Priority = Entry.EntryPriority.High;

                    #region Vehicle

                    EntryAsset asset = null;
                    if (call.Assets != null)
                        asset = call.Assets.FirstOrDefault();

                    if (asset == null)
                        asset = new EntryAsset() { BodyTypeId = 1 };

                    if (asset.BodyTypeId == 0)
                        asset.BodyTypeId = 1;

                    var vc = VehicleUtility.GetColorIdByName(job.Job.Vehicle.Color);

                    if (int.TryParse(job.Job.Vehicle.Year, out int year))
                        asset.Year = year;
                    asset.Make = VehicleUtility.GetManufacturerByName(job.Job.Vehicle.Make);
                    asset.Model = VehicleUtility.GetModelByName(job.Job.Vehicle.Model);

                    if (vc != 0)
                        asset.ColorId = vc;

                    asset.Vin = job.Job.Vehicle.Vin;

                    asset.LicenseNumber = job.Job.Vehicle.LicensePlate;
                    #endregion Vehicle

                    #region Locations

                    var pickup = call.Waypoints.FirstOrDefault(o => o.Title == "Pickup");
                    var dest = call.Waypoints.FirstOrDefault(o => o.Title == "Destination");

                    if (call.Notes == null)
                        call.Notes = "";

                    if (job.Job.IncidentAddress != null)
                    {
                        if (string.IsNullOrWhiteSpace(call.TowSource))
                        {
                            var breakdown = job.Job.IncidentAddress.ToString();

                            if (!string.IsNullOrWhiteSpace(job.Job.IncidentAddress.Landmark))
                                breakdown += " (" + job.Job.IncidentAddress.Landmark + ")";

                            call.TowSource = breakdown;

                            if (pickup == null)
                            {
                                pickup = new EntryWaypoint()
                                {
                                    Title = "Pickup",
                                    Position = 1
                                };
                                call.Waypoints.Add(pickup);
                            }
                            pickup.Address = call.TowSource;

                            if (job.Job.IncidentAddress.Latitude !=0)
                                pickup.Latitude = job.Job.IncidentAddress.Latitude;

                            if (job.Job.IncidentAddress.Longitude != 0)
                                pickup.Longitude = job.Job.IncidentAddress.Longitude;
                        }
                    }

                    if (job.Job.DestinationAddress != null)
                    {
                        if (string.IsNullOrWhiteSpace(call.TowDestination))
                        {
                            var destAddress = job.Job.DestinationAddress.ToString();

                            if (!string.IsNullOrWhiteSpace(job.Job.DestinationAddress.Landmark))
                                destAddress = "(" + job.Job.DestinationAddress.Landmark + ")";
                            
                            call.TowDestination = destAddress;
                            if (!string.IsNullOrWhiteSpace(call.TowDestination))
                            {
                                if (dest == null)
                                {
                                    dest = new EntryWaypoint() { Title = "Destination", Position = 2 };
                                    call.Waypoints.Add(dest);
                                }

                                dest.Address = call.TowDestination;

                                if (job.Job.DestinationAddress.Latitude.GetValueOrDefault() != 0)
                                    dest.Latitude = job.Job.DestinationAddress.Latitude.Value;

                                if (job.Job.DestinationAddress.Longitude.GetValueOrDefault() != 0)
                                    dest.Longitude = job.Job.DestinationAddress.Longitude.Value;
                            }
                        }
                    }

                    #endregion Locations

                    // don't use offerExpires time, and they dont have a transaction timestamp, so we'll just use current server time.
                    if (call.CreateDate == DateTime.MinValue)
                        call.CreateDate = DateTime.Now.AddSeconds(-DateTime.Now.Second); // dont use accept.service.OfferExpiresLocal;

                    if (dispatch.Eta != null)
                        call.ArrivalETA = call.CreateDate.AddMinutes(dispatch.Eta.Value);


                    if (job.Job.ScheduledAppointmentTime != null)
                    {
                        call.ArrivalETA = job.Job.ScheduledAppointmentTime;
                        var offsetDate = Core.OffsetDateTime(call.Company, call.ArrivalETA.Value);
                        addNote("** SCHEDULED CALL: " + offsetDate.ToShortDateString() + " " + offsetDate.ToShortTowbookTimeString());
                    }

                    if (job.Job.NumberOfPassengers > 0)
                        addNote("Number of Passengers:" + job.Job.NumberOfPassengers);

                    if (job.Job.Type == "RAP")
                        addNote("** RAP CALL **", true);

                    #region po number
                    call.PurchaseOrderNumber = accepted.PoNumber;

                    static string MaskString(string input)
                    {
                        return input; 
                        /*
                        if (input == null || input.Length <= 4)
                        {
                            return input;  // Return the input string as is if it's null or less than or equal to 4 characters
                        }

                        string maskedPart = new string('*', input.Length - 4);  // Create a string of '*' of length equal to the characters to be masked
                        string lastFour = input.Substring(input.Length - 4);  // Get the last four characters of the string

                        return maskedPart + lastFour;  // Concatenate the masked part with the last four characters
                        */
                    }

                    if (!string.IsNullOrWhiteSpace(job.Job.Customer?.MembershipNumber))
                        call.SetAttribute(AttributeValue.BUILTIN_MOTORCLUB_MEMBERSHIPNUMBER, 
                            MaskString(job.Job.Customer.MembershipNumber));

                    call.SetAttribute(AttributeValue.BUILTIN_MOTORCLUB_DISPATCHNUMBER, accepted.DispatchId);

                    #endregion

                    #region Reason

                    var reason = job.Job.TroubleCode;
                    string tcode = "";
                    
                    if (reason != null)
                        tcode = reason;

                    call.ReasonId = await ReasonHelper.DetermineReasonId(call.Account.MasterAccountId, qi.CompanyId.Value,
                        reason);

                    if (call.ReasonId == 1635 && reason != null)
                        call.Notes = "Service Needed: " + tcode + " " + reason + "\n" + call.Notes;

                    if (!string.IsNullOrWhiteSpace(job.Job.PacesetterCode))
                        addNote("Pacesetter Code: " + job.Job.PacesetterCode);

                    #endregion Reason

                    if (!string.IsNullOrWhiteSpace(job.Job.Vehicle.BodyType))
                        addNote("Body Type: " + job.Job.Vehicle.BodyType);

                    if (callRequest != null)
                    {
                        if (callRequest.OwnerUserId == null)
                            callRequest.OwnerUserId = 1;

                        call.CallRequestId = callRequest.CallRequestId;

                        if (call.OwnerUserId < 100)
                            call.OwnerUserId = callRequest.OwnerUserId.GetValueOrDefault(0);
                    }
                    else if (call.OwnerUserId < 100)
                        call.OwnerUserId = 1;

                    #region Notes

                    void addNote(string line, bool top = false)
                    {
                        if (string.IsNullOrWhiteSpace(line))
                            return;

                        if (call.Notes == null || !call.Notes.Contains(line))
                        {
                            if (call.Notes == null)
                                call.Notes = line + "\n";
                            else
                            {
                                if (top)
                                    call.Notes = line + call.Notes.Trim('\n') + "\n";
                                else
                                    call.Notes += "\n" + line.Trim('\n');
                            }
                        }
                    }
                    #endregion Notes

                    call.SetAttribute(AttributeValue.BUILTIN_MOTORCLUB_COVERAGELIMIT, job.Job.Customer.MembershipLevel);
                    call.SetAttribute(AttributeValue.BUILTIN_AAA_TROUBLE_CODE_PROBLEM, tcode);

                    //if (job.Job.Payment?.Required == true)
                    //{
                    //    call.SetAttribute(AttributeValue.BUILTIN_MOTORCLUB_COVERAGELIMIT, "COD / Payment Required from customer");
                    //}

                    await MotorClubDispatchingService.ApplyRoundRobinDispatcherLogicAsync(qi, call, dispatch.DispatchId, contractor.ContractorId);

                    if (call.Assets == null || call.Assets.Count == 0)
                    {
                        call.Assets = new System.Collections.ObjectModel.Collection<EntryAsset>();
                        call.Assets.Add(asset);
                    }

                    bool newContact = false;

                    var customerName = Core.FormatName(job.Job.Customer?.CustomerName);

                    var x  = job.Job.Customer.CustomerPhone;
                    
                    EntryContact c = call.Contacts.FirstOrDefault(o =>
                        Core.FormatPhone(o.Phone) == x);

                    if (c == null)
                    {
                        c = new EntryContact();
                        call.Contacts.Add(c);
                        newContact = true;
                    }
                        
                    c.Name = customerName;
                    c.Phone = x;
                    

                    if (job.Job.Notes != null && job.Job.Notes.Length > 0 )
                    {
                        call.Notes = String.Join('\n', job.Job.Notes) + "\n" + call.Notes;
                    }

                    if (!string.IsNullOrWhiteSpace(job.Job.DriverDirections))
                    {
                        call.Notes = "Driver Directions: " + job.Job.DriverDirections + "\n" + call.Notes;
                    }

                    await call.Save();

                    if (newContact)
                    {
                        foreach(var cx in call.Contacts)
                            await MotorClubDispatchingService.CheckForRoadsideFeatureAndAutoInvite(call, cx);
                    }

                    await AutoDispatch.AutoDispatchServiceBusHandler.Send(call);

                    return call;
                },
                alreadyLocked: async delegate ()
                {
                    logger.LogEvent("BCAA {0}/CR{1}: Lock already exists for {2}:{3}... pausing 250ms",
                        qi.CompanyId, LogLevel.Warn,
                        sourceMessage.Message.MessageId,
                        (callRequest != null ? callRequest.CallRequestId.ToString() : "NULL"),
                        qi.AccountId.Value,
                        accepted.LocationId);


                    await Task.Delay(250);

                    return true;
                });

            if (fe == null)
            {
                logger.LogEvent("BCAA/{0}: Creation of call failed; {1}",
                    qi.CompanyId, LogLevel.Error, sourceMessage.Message.MessageId, qi.ToJson());

                if (callRequest != null)
                    await callRequest.UpdateStatus(CallRequestStatus.AcceptFailed);
            }
            else
            {
                if (callRequest != null)
                {
                    await callRequest.UpdateStatus(CallRequestStatus.Accepted, po: fe.PurchaseOrderNumber);

                    qi.CallRequestId = callRequest.CallRequestId;
                    DigitalDispatchService.LogAction(qi);

                    callRequest.DispatchEntryId = fe.Id;
                    await callRequest.Save();

                    await sourceMessage.CompleteAsync();
                    DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);

                    logger.Info(MasterAccountTypes.Bcaa,
                        "CallCreated",
                        "Created new towbook call for BCAA",
                        companyId: qi.CompanyId,
                        data: new
                        {
                            waypoints = fe.Waypoints.Select(o => new { o.Address, o.Latitude, o.Longitude })
                        },
                        dispatchId: dispatch.DispatchId.ToString(),
                        callId: fe.Id,
                        callRequestId: callRequest.CallRequestId,
                        queueItemId: qi.QueueItemId,
                        poNumber: fe.PurchaseOrderNumber,
                        callNumber: fe.CallNumber);

                }
            }
            return fe;
        }

        public class AceCallRequestContainer
        {
            public CallRequest CallRequest { get; set; }
            public AaaDispatch Job { get; set; }
            public int Eta { get; set; }
            public int DriverId { get; set; }
            public AaaContractor Contractor { get; set; }
        }

        internal static AceCallRequestContainer BcaaGetCallRequestAndDispatch(dynamic jsonObj)
        {
            CallRequest callRequest = null;
            AaaDispatch dispatch = null;
            AaaContractor contractor = null;

            try
            {
                callRequest = CallRequest.GetById(Convert.ToInt32(jsonObj.Id));
                if (callRequest == null)
                {
                    logger.Log(LogLevel.Error, "CR" + jsonObj.DispatchId + ": Couldn't find CallRequestId.");
                    logger.Log(LogLevel.Error, "CR" + jsonObj.DispatchId + ": " + JsonExtensions.ToJson(jsonObj));
                }
                else
                {
                    dispatch = AaaDispatch.GetByCallRequestId(callRequest.CallRequestId);
                    contractor = AaaContractor.GetById(dispatch.AaaContractorId);
                }
            }
            catch (Exception)
            {
                return new AceCallRequestContainer
                {
                    CallRequest = callRequest,
                    Job = dispatch,
                    Eta = jsonObj.Eta
                };
            }

            return new AceCallRequestContainer
            {
                CallRequest = callRequest,
                Job = dispatch,
                Eta = jsonObj.Eta != null ? jsonObj.Eta : 0,
                Contractor = contractor
            };
        }

        private static string AceEnvironmentIdToString(int id)
        {
            if (id == 1)
            {
                return "DEV";
            }
            else if (id == 2)
            {
                return "TEST";
            }
            else if (id == 3)
            {
                return "PROD";
            }
            return "DEV";
        }
        public static BcaaRestClient GetClient(AaaContractor contractor, DigitalDispatchActionQueueItem qi, int callRequestId = 0, int? callId = 0, string dispatchId = null)
        {
            string env = AceEnvironmentIdToString(contractor.EnvironmentId);
            return new BcaaRestClient(env, "127.0.0.1", qi.CompanyId.GetValueOrDefault(), qi.QueueItemId, callRequestId, callId.GetValueOrDefault(), dispatchId, contractor);
        }

        [Transaction]
        public static async Async.Task HandleBcaaQueueOutgoingMessage(DigitalDispatchActionQueueItem qi, dynamic jsonObj, ProcessMessageEventArgs sourceMessage)
        {
            BcaaRestClient client;

            switch (qi.Type)
            {
                case DigitalDispatchActionQueueItemType.OutgoingAcceptCall:
                    AceCallRequestContainer x = BcaaGetCallRequestAndDispatch(jsonObj);
                    async Task doAccept()
                    {
                        CallRequest callRequest = x.CallRequest;
                        AaaDispatch dispatch = x.Job;
                        var contractor = AaaContractor.GetById(dispatch.AaaContractorId);
                        client = GetClient(contractor, qi, callRequest.CallRequestId, callRequest.DispatchEntryId, dispatch.DispatchId);

                        if (callRequest == null)
                        {
                            await sourceMessage.DeadLetterAsync();
                            return;
                        }

                        if (dispatch == null)
                        {
                            logger.Log(LogLevel.Error, "BCAA/CR" + callRequest.CallRequestId + "/C" + callRequest.CompanyId + "/OutgoingAcceptCall: Couldn't find AceDispatch");

                            await sourceMessage.DeadLetterAsync();
                            return;
                        }

                        dispatch.Eta = x.Eta;
                        dispatch.Save();

                        try
                        {
                            logger.Info(MasterAccountTypes.Bcaa,
                                "AcceptCall",
                                "Outgoing Call Accept",
                                contractor.ContractorId,
                                null,
                                dispatch.DispatchId,
                                x.CallRequest.CompanyId,
                                new
                                {
                                    json = JsonExtensions.ToJson(jsonObj)
                                },
                                callRequestId: x.CallRequest.CallRequestId,
                                queueItemId: qi.QueueItemId);

                            bool postDispatchIsOk()
                            {
                                try
                                {
                                    client.Accept(
                                        new BcaaRestClient.DispatchAcceptModel()
                                        {
                                            ContractorId = contractor.ContractorId,
                                            DispatchId = dispatch.DispatchId,
                                            Eta = x.Eta
                                        });

                                    return true;
                                }
                                catch (Exception rfe)
                                {
                                    logger.Error(MasterAccountTypes.Bcaa,
                                        "AcceptCall",
                                        "Outgoing Call Accept Failed",
                                        contractor.ContractorId, null, dispatch.DispatchId, x.CallRequest.CompanyId,
                                        new
                                        {
                                            json = rfe.ToJson()
                                        },
                                        callRequestId: x.CallRequest.CallRequestId,
                                        queueItemId: qi.QueueItemId);

                                    return false;
                                }
                            }

                            if (!postDispatchIsOk())
                            {
                                await callRequest.UpdateStatus(CallRequestStatus.AcceptFailed);
                            }
                            await sourceMessage.CompleteAsync();
                        }
                        catch (Exception ex)
                        {
                            await sourceMessage.DeadLetterAsync("Attempted to accept callRequestId " + callRequest.CallRequestId + ", but  error occurred",
                                ex.ToJson(true));

                            logger.Error(MasterAccountTypes.Bcaa,
                                "AcceptCall",
                                "Sent accept failed.",
                                contractor.ContractorId,
                                null,
                                dispatch?.DispatchId,
                                qi?.CompanyId,
                                new
                                {
                                    errorMessage = ex.Message,
                                    exception = ex
                                },
                                queueItemId: qi.QueueItemId);

                            DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                        }
                    }
                    try
                    {
                        await doAccept();
                    }
                    catch (Exception y)
                    {
                        if (y.Message == "Network Error, retry recommended.")
                            break;

                        logger.Error(MasterAccountTypes.Bcaa,
                            "Accept",
                            "Failed: " + y.Message,
                            null,
                            callRequestId: x.CallRequest?.CallRequestId ?? null,
                            companyId: qi.CompanyId.GetValueOrDefault(),
                            data: new
                            {
                                exception = y
                            },
                            queueItemId: qi.QueueItemId);

                        try
                        {
                            await sourceMessage.CompleteAsync();
                        }
                        catch { }

                        if (x.CallRequest != null)
                        {
                            await x.CallRequest.UpdateStatus(CallRequestStatus.AcceptFailed);
                        }
                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                    }
                    break;

                case DigitalDispatchActionQueueItemType.OutgoingCallCanceled:
                    async Task DoCancel()
                    {
                        CallRequest cr = CallRequest.GetById(Convert.ToInt32(jsonObj.CallRequestId ?? jsonObj.Id));

                        var chj = AaaDispatch.GetByCallRequestId(cr.CallRequestId);
                        var cancelContractor = AaaContractor.GetById(chj.AaaContractorId);
                        client = GetClient(cancelContractor, qi, cr.CallRequestId, cr.DispatchEntryId, chj.DispatchId);

                        var cancelReason = MasterAccountReason.GetById((int)jsonObj.ReasonId);

                        var cdm = new BcaaRestClient.DispatchCancelModel();

                        cdm.ContractorId = cancelContractor.ContractorId;
                        cdm.DispatchId = chj.DispatchId;
                        cdm.ResolutionCode = cancelReason.Code;
                        cdm.Comments = jsonObj.Comments?.ToString();

                        logger.Info(MasterAccountTypes.Bcaa,
                            "OutgoingCancel",
                            "OutboundMessage",
                            cancelContractor.ContractorId, null, chj.DispatchId, qi.CompanyId,
                            data: new { json = cdm.ToJson() },
                            callRequestId: chj.CallRequestId,
                            queueItemId: qi.QueueItemId);

                        var e = Entry.GetByIdNoCache(cr.DispatchEntryId.Value);
                        try
                        {
                            client.Cancel(cdm);
                        }
                        catch
                        {
                            if (e != null)
                            {
                                e.Notes = "Digital Cancel Failed: Please contact ACE to cancel the call.\n" +
                                    e.Notes;

                                await e.Save(false, token: new AuthenticationToken() { UserId = BcaaUserId });

                                await e.Cancel("Digital Cancel Failed, contact BCAA so they know it's cancelled.",
                                    new AuthenticationToken() { UserId = BcaaUserId }, "127.0.0.1");

                                DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                                await sourceMessage.CompleteAsync();
                                return;
                            }
                        }
                        logger.Info(MasterAccountTypes.Bcaa, "OutgoingCancel", "OutboundMessageResponse",
                            cancelContractor.ContractorId, null, chj.DispatchId.ToString(), qi.CompanyId, "",
                            callRequestId: chj.CallRequestId,
                            queueItemId: qi.QueueItemId);

                        if (e != null)
                        {
                            await e.Cancel("Digitally Cancelled: " + cancelReason.Name,
                                new AuthenticationToken() { UserId = qi.OwnerUserId.Value }, "127.0.0.1");
                        }
                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                        await sourceMessage.CompleteAsync();
                    }

                    await DoCancel();

                    break;

                case DigitalDispatchActionQueueItemType.OutgoingSharePhoto:
                    async Task doPhotoUploadAsync()
                    {
                        logger.Info("SharePhoto");

                        SharePhotoModel pm = JsonConvert.DeserializeObject<SharePhotoModel>(qi.JsonObject);

                        var phj = AaaDispatch.GetByCallRequestId(pm.CallRequestId);
                        var contractor = AaaContractor.GetById(phj.AaaContractorId);
                        client = GetClient(contractor, qi, pm.CallRequestId, pm.DispatchEntryId, phj.DispatchId);

                        Console.WriteLine(JsonExtensions.ToJson(pm, true));

                        var thePhoto = Dispatch.Photo.GetById(pm.PhotoId);

                        client.Photo(new BcaaRestClient.DispatchPhotoModel()
                        {
                            ContractorId = contractor.ContractorId,
                            DispatchId = phj.DispatchId,
                            Timestamp = thePhoto.CreateDate.ToUniversalTime(),
                            Url = thePhoto.HttpLocation,
                            Latitude = thePhoto.CameraLatitude.GetValueOrDefault(),
                            Longitude = thePhoto.CameraLongitude.GetValueOrDefault(),
                        });

                        qi.CallRequestId = pm.CallRequestId;
                        DigitalDispatchService.LogAction(qi);

                        await sourceMessage.CompleteAsync();
                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                    }
                    await doPhotoUploadAsync();
                    break;

                case DigitalDispatchActionQueueItemType.OutgoingRejectCall:
                    AceCallRequestContainer x2 = BcaaGetCallRequestAndDispatch(jsonObj);
                    async Task doReject()
                    {

                        CallRequest callRequestRC = x2.CallRequest;

                        client = GetClient(AaaContractor.GetById(x2.Contractor.AaaContractorId), qi, callRequestRC.CallRequestId, callRequestRC.DispatchEntryId, x2.Job?.DispatchId);
                        var contractor = x2.Contractor;
                        AaaDispatch dispatchRC = x2.Job;

                        if (callRequestRC == null)
                        {
                            await sourceMessage.DeadLetterAsync();
                            return;
                        }

                        MasterAccountReason rejectReason = MasterAccountReason.GetById(Convert.ToInt32(jsonObj.MasterAccountReasonId));

                        if (rejectReason == null)
                        {
                            await sourceMessage.DeadLetterAsync("Attempted to reject callRequestId " + callRequestRC.CallRequestId +
                                ", but no reason is present in database for it",
                                callRequestRC.ToJson(true));
                            await callRequestRC.UpdateStatus(CallRequestStatus.RejectFailed);
                            return;
                        }

                        client.Refuse(new BcaaRestClient.DispatchRefuseModel()
                        {
                            ContractorId = contractor.ContractorId,
                            DispatchId = dispatchRC.DispatchId,
                            LocationId = contractor.LocationCode,
                            ReasonId = (int)rejectReason.MasterAccountReasonId,
                            ReasonName = rejectReason.Code
                        });

                        //TODO: Parse response to see if everything went ok (when we get an actual response :))

                        await callRequestRC.UpdateStatus(CallRequestStatus.Rejected);

                        await callRequestRC.Save();

                        qi.CallRequestId = callRequestRC.CallRequestId;
                        DigitalDispatchService.LogAction(qi);

                        await sourceMessage.CompleteAsync();
                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                    }
                    try
                    {
                        await doReject();
                    }
                    catch (Exception y)
                    {
                        if (y.Message == "Network Error, retry recommended.")
                            break;

                        logger.Error(MasterAccountTypes.Bcaa,
                            "Reject",
                            "Failed: " + y.Message,
                            null,
                            callRequestId: x2.CallRequest?.CallRequestId ?? null,
                            companyId: qi.CompanyId.GetValueOrDefault(),
                            data: new
                            {
                                exception = y
                            },
                            queueItemId: qi.QueueItemId);

                        try
                        {
                            await sourceMessage.CompleteAsync();
                        }
                        catch { }

                        if (x2.CallRequest != null)
                        {
                            await x2.CallRequest.UpdateStatus(CallRequestStatus.RejectFailed);
                        }
                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                    }
                    break;

                case DigitalDispatchActionQueueItemType.OutgoingStatusUpdate:
                    string newStatusName = null;
                    async Task doStatusUpdate()
                    {
                        CallRequest callRequestSU = CallRequest.GetById(Convert.ToInt32(jsonObj.CallRequestId));
                        var call = Entry.GetByIdNoCache(callRequestSU.DispatchEntryId.GetValueOrDefault());

                        if (callRequestSU == null)
                        {
                            logger.Log(LogLevel.Error, "CR" + jsonObj.CallRequestId + ": Couldn't find CallRequestId.");
                            logger.Log(LogLevel.Error, "CR" + JsonExtensions.ToJson(jsonObj));

                            await sourceMessage.DeadLetterAsync();
                            return;
                        }
                        var dispatchSU = AaaDispatch.GetByCallRequestId(Convert.ToInt32(callRequestSU.CallRequestId));
                        var contractor = AaaContractor.GetById(dispatchSU.AaaContractorId);

                        if (contractor == null)
                        {
                            logger.Error(MasterAccountTypes.Bcaa,
                                 "StatusUpdate",
                                 "ContractorID isn't registered as an active connection.",
                                 contractor.ContractorId,
                                 null,
                                 dispatchSU.DispatchId,
                                 qi.CompanyId,
                                 new
                                 {
                                     request = jsonObj
                                 },
                                 callId: callRequestSU.DispatchEntryId,
                                 callRequestId: callRequestSU.CallRequestId,
                                 queueItemId: qi.QueueItemId);

                            await sourceMessage.CompleteAsync();
                            DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                            return;
                        }

                        client = GetClient(contractor, qi, callRequestSU.CallRequestId, callRequestSU.DispatchEntryId, dispatchSU.DispatchId);

                        if (Core.GetRedisValue(call.Id + ":ace_compl") != null)
                        {
                            logger.Info(MasterAccountTypes.Bcaa,
                                 "StatusUpdate",
                                 "Call was already marked Completed, not sending.",
                                 contractor.ContractorId,
                                 null,
                                 dispatchSU.DispatchId,
                                 qi.CompanyId,
                                 new
                                 {
                                     request = jsonObj
                                 },
                                 callId: callRequestSU.DispatchEntryId,
                                 callRequestId: callRequestSU.CallRequestId,
                                 queueItemId: qi.QueueItemId);

                            await sourceMessage.CompleteAsync();
                            DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);

                            return;
                        }


                        double? lat = (double?)jsonObj.Latitude;
                        double? lng = (double?)jsonObj.Longitude;
                        int newStatusId = (int)jsonObj.NewStatusId;

                        string statusUpdate;
                        if (newStatusId == Status.Dispatched.Id)
                        {
                            statusUpdate = "DISPATCHED";
                        }
                        else if (newStatusId == Status.EnRoute.Id)
                        {
                            statusUpdate = "EN_ROUTE";
                        }
                        else if (newStatusId == Status.AtSite.Id)
                        {
                            statusUpdate = "ON_SCENE";
                        }
                        else if (newStatusId == Status.BeingTowed.Id)
                        {
                            statusUpdate = "TOWING";
                        }
                        else if (newStatusId == Status.DestinationArrival.Id)
                        {
                            statusUpdate = "DESTINATION";
                        }
                        else if (newStatusId == Status.Completed.Id)
                        {
                            statusUpdate = "COMPLETE";
                        }
                        else
                        {
                            await sourceMessage.CompleteAsync();
                            return;
                        }
                        newStatusName = statusUpdate;

                        var aaadriver = new BcaaRestClient.DriverModel();

                        var driver = await Driver.GetByIdAsync((int)jsonObj.DriverId);
                        if (driver != null)
                        {
                            string firstName = "",
                                lastName = "";

                            if (driver.Name.Contains(" "))
                            {
                                // ignore numbers
                                var tname = new String(driver.Name.Where(r => !Char.IsDigit(r)).ToArray());

                                var names = tname.Split(new char[] { ' ' }, 2, StringSplitOptions.RemoveEmptyEntries);
                                if (names.Length == 2)
                                {
                                    firstName = Core.FormatName(names[0]);
                                    lastName = Core.FormatName(names[1]);
                                }
                                else
                                    firstName = Core.FormatName(driver.Name);
                            }
                            else
                            {
                                firstName = Core.FormatName(driver.Name);
                            }

                            aaadriver.Id = driver.Id.ToString();
                            aaadriver.FirstName = firstName;
                            aaadriver.LastName = lastName;
                        }

                        if (Core.GetRedisValue(call.Id + ":ace_compl") == "1")
                        {
                            logger.Log(LogLevel.Warn, "CR " + jsonObj.CallRequestId + ": " +
                                "Already completed; not sending any future status updates. " +
                                qi.JsonObject);

                            statusUpdate = "";
                        }

                        if (statusUpdate != "")
                        {
                            if (statusUpdate == "COMPLETE")
                            {
                                var list = new List<BcaaRestClient.DispatchCompleteModel.InvoiceItemModel>();

                                var codes = new string[] { "DL", "DS","FB","FD","FL","FM","LS","LT","MA","MD",
                                        "ML","MT","MU","OM","PR","PY","RV","S1","S2","SM","ST","TL","TM","TR","TW" };

                                foreach (var line in call.InvoiceItems)
                                {
                                    string bc = null;

                                    if (line.RateItem?.Predefined?.Id == PredefinedRateItem.BUILTIN_MILEAGE_UNLOADED &&
                                        line.Price > 0 && line.Quantity > 0)
                                        bc = "OM";
                                    else if (line.RateItem?.Predefined?.Id == PredefinedRateItem.BUILTIN_MILEAGE_LOADED &&
                                        line.Price > 0 && line.Quantity > 0)
                                        bc = "TW";
                                    else
                                    {
                                        foreach (var code in codes)
                                        {
                                            if (line.Name.StartsWith(code + " -"))
                                            {
                                                bc = code;
                                                break;
                                            }
                                        }
                                    }

                                    if (bc != null)
                                        list.Add(new BcaaRestClient.DispatchCompleteModel.InvoiceItemModel()
                                        {
                                            Code = bc,
                                            Price = line.Price,
                                            Quantity = line.Quantity
                                        });
                                }

                                var rc = "";

                                if (jsonObj.CompletionReasonId != null)
                                {
                                    int completionReason = Convert.ToInt32(jsonObj.CompletionReasonId);
                                    if (completionReason != 0)
                                        rc = MasterAccountReason.GetById(completionReason).Code;
                                }

                                // verify that On Site status was sent: 

                                await SendFakeStatusIfNotAlreadySent(":ace_su_dispatched", BcaaRestClient.StatusCodes.Dispatched, "COMPLETE");
                                await SendFakeStatusIfNotAlreadySent(":ace_su_enroute", BcaaRestClient.StatusCodes.Enroute, "COMPLETE");
                                await SendFakeStatusIfNotAlreadySent(":ace_su_onsite", BcaaRestClient.StatusCodes.OnScene, "COMPLETE");

                                client.Complete(new BcaaRestClient.DispatchCompleteModel()
                                {
                                    ContractorId = contractor.ContractorId,
                                    DispatchId = dispatchSU.DispatchId,
                                    ResolutionCode = rc,
                                    Driver = aaadriver,
                                    InvoiceItems = list.ToArray(),
                                    Odometer = call.Odometer,
                                    Vin = call.VIN,
                                    DispatchedTime = call.DispatchTime,
                                    EnrouteTime = call.EnrouteTime,
                                    OnSceneTime = call.ArrivalTime,
                                    TowingTime = call.TowTime,
                                    DestinationTime = call.DestinationArrivalTime,
                                    CompletionTime = call.CompletionTime
                                });

                                if (qi.ScheduledDate != null)
                                {
                                    call.SetAttribute(Dispatch.AttributeValue.BUILTIN_DISPATCH_COMPLETION_ACKNOWLEDGEMENT_JSON,
                                        new AcknowledgeJson()
                                        {
                                            Confirmed = true,
                                            Timestamp = DateTime.UtcNow,
                                            UserId = qi.OwnerUserId != null ? qi.OwnerUserId.Value : BcaaUserId
                                        }.ToJson());
                                }

                                await call.Save(false, new AuthenticationToken()
                                {
                                    UserId = qi.OwnerUserId != null ? qi.OwnerUserId.Value : BcaaUserId,
                                    ClientVersionId = MotorClubDispatchingService.MyClientVersionId
                                });

                                Core.SetRedisValue(call.Id + ":ace_compl", "1", TimeSpan.FromHours(48));
                                Core.DeleteRedisKey(call.Id + ":ace_su_onsite");
                            }
                            else
                            {
                                // Send a dispatched status if it was never sent. 

                                if (Core.GetRedisValue(call.Id + ":ace_su_dispatched") == null &&
                                    newStatusId > Status.Dispatched.Id)
                                {
                                    await SendFakeStatusIfNotAlreadySent(":ace_su_dispatched", BcaaRestClient.StatusCodes.Dispatched, statusUpdate);
                                }

                                if (Core.GetRedisValue(call.Id + ":ace_su_enroute") == null &&
                                    newStatusId > Status.EnRoute.Id)
                                {
                                    await SendFakeStatusIfNotAlreadySent(":ace_su_enroute", BcaaRestClient.StatusCodes.Enroute, statusUpdate);
                                }

                                if (Core.GetRedisValue(call.Id + ":ace_su_onsite") == null &&
                                    newStatusId > Status.AtSite.Id)
                                {
                                    await SendFakeStatusIfNotAlreadySent(":ace_su_onsite", BcaaRestClient.StatusCodes.OnScene, statusUpdate);
                                }

                                client.StatusUpdate(new BcaaRestClient.DispatchStatusUpdateModel()
                                {
                                    ContractorId = contractor.ContractorId,
                                    DispatchId = dispatchSU.DispatchId,
                                    Status = statusUpdate,
                                    Driver = aaadriver
                                });

                                if (statusUpdate == BcaaRestClient.StatusCodes.OnScene)
                                    Core.SetRedisValue(call.Id + ":ace_su_onsite", "1", TimeSpan.FromHours(48));
                                else if (statusUpdate == BcaaRestClient.StatusCodes.Enroute)
                                    Core.SetRedisValue(call.Id + ":ace_su_enroute", "1", TimeSpan.FromHours(48));
                                else if (statusUpdate == BcaaRestClient.StatusCodes.Dispatched)
                                    Core.SetRedisValue(call.Id + ":ace_su_dispatched", "1", TimeSpan.FromHours(48));

                            }
                        }

                        await sourceMessage.CompleteAsync();

                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);

                        async Task SendFakeStatusIfNotAlreadySent(string key, string status, string desiredStatus)
                        {
                            if (Core.GetRedisValue(call.Id + key) == null)
                            {
                                client.StatusUpdate(new BcaaRestClient.DispatchStatusUpdateModel()
                                {
                                    ContractorId = contractor.ContractorId,
                                    DispatchId = dispatchSU.DispatchId,
                                    Status = status,
                                    Driver = aaadriver
                                });

                                Core.SetRedisValue(call.Id + key, "2", TimeSpan.FromHours(24));

                                logger.Warn(MasterAccountTypes.Bcaa, "StatusUpdate",
                                    "Sent fake " + status + " status update so that " + desiredStatus + " won't fail.",
                                     contractor.ContractorId,
                                     dispatchId: dispatchSU.DispatchId,
                                     companyId: callRequestSU.CompanyId,
                                     callRequestId: callRequestSU.CallRequestId,
                                     queueItemId: qi.QueueItemId);

                                await Task.Delay(5000);
                            }
                        }
                    }

                    try
                    {
                        await doStatusUpdate();
                    }
                    catch (Exception y)
                    {
                        if (y.Message == "Network Error, retry recommended.")
                            break;

                        try
                        {
                            await sourceMessage.CompleteAsync();
                        }
                        catch { }


                        int errorCallRequestId = 0;
                        int errorCallId = 0;
                        try
                        {
                            var callRequestSU = CallRequest.GetById(Convert.ToInt32(jsonObj.CallRequestId));

                            if (callRequestSU != null)
                            {
                                var call = Entry.GetByIdNoCache(callRequestSU.DispatchEntryId.GetValueOrDefault());

                                if (call != null)
                                {
                                    errorCallRequestId = callRequestSU.CallRequestId;
                                    errorCallId = call.Id;

                                    var en = new EntryNote() { DispatchEntryId = call.Id, OwnerUserId = 1 };
                                    en.Content = "Failed to share " + newStatusName + " Status Update with AAA: " + y.Message;
                                    en.CreateDate = DateTime.Now;
                                    en.OwnerUserId = 1;
                                    await en.SaveAsync();
                                }
                            }
                        }
                        catch
                        {
                            // silently ignore note failure.
                        }

                        logger.Error(MasterAccountTypes.Bcaa,
                            "StatusUpdate",
                            "Failed: " + y.Message,
                            null,
                            null,
                            null,
                            qi.CompanyId,
                            new
                            {
                                exception = y
                            },
                            callId: errorCallId,
                            callRequestId: errorCallRequestId,
                            queueItemId: qi.QueueItemId);

                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                    }
                    break;

                case DigitalDispatchActionQueueItemType.OutgoingRequestGoa:
                    async Task doGoa()
                    {
                        var requestGoaMsg = JsonConvert.DeserializeObject<RequestGoaMessage>(qi.JsonObject);

                        CallRequest callRequestSU = CallRequest.GetById(requestGoaMsg.CallRequestId);
                        if (callRequestSU == null)
                        {
                            logger.Log(LogLevel.Error, "CR" + jsonObj.CallRequestId + ": Couldn't find CallRequestId.");
                            logger.Log(LogLevel.Error, "CR" + JsonExtensions.ToJson(jsonObj));

                            await sourceMessage.DeadLetterAsync();
                            return;
                        }

                        var dispatchSU = AaaDispatch.GetByCallRequestId(requestGoaMsg.CallRequestId);
                        var contractor = AaaContractor.GetById(dispatchSU.AaaContractorId);

                        if (Core.GetRedisValue(callRequestSU.DispatchEntryId + ":ace_compl") != null)
                        {
                            await sourceMessage.CompleteAsync();
                            DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);

                            logger.Info(MasterAccountTypes.Bcaa,
                                 "StatusUpdate",
                                 "Call was already marked Completed, not sending.",
                                 contractor?.ContractorId,
                                 null,
                                 dispatchSU.DispatchId,
                                 qi.CompanyId,
                                 new
                                 {
                                     request = jsonObj
                                 },
                                 queueItemId: qi.QueueItemId);

                            return;
                        }


                        client = GetClient(contractor, qi, callRequestSU.CallRequestId, callRequestSU.DispatchEntryId, dispatchSU.DispatchId);

                        MasterAccountReason reason = MasterAccountReason.GetById(Convert.ToInt32(jsonObj.ReasonId));

                        client.Goa(new BcaaRestClient.DispatchGoaModel()
                        {
                            ContractorId = contractor.ContractorId,
                            DispatchId = dispatchSU.DispatchId,
                            ResolutionCode = reason.Code,
                        });

                        await sourceMessage.CompleteAsync();

                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                    }

                    try
                    {
                        await doGoa();
                    }
                    catch (Exception y)
                    {
                        logger.Log(LogLevel.Warn,
                            "AAA " + jsonObj.CallRequestId + ", QueueItemId: " + qi.QueueItemId + ": GOA Failed: " + qi.JsonObject);

                        if (y.Message == "Network Error, retry recommended.")
                            break;

                        try
                        {
                            CallRequest callRequestSU = CallRequest.GetById(Convert.ToInt32(jsonObj.CallRequestId));
                            var call = Entry.GetByIdNoCache(callRequestSU.DispatchEntryId.GetValueOrDefault());
                            logger.Error(MasterAccountTypes.Bcaa,
                                "GOA",
                                "Failed: " + y.Message,
                                null,
                                callRequestId: callRequestSU.CallRequestId,
                                companyId: qi.CompanyId.GetValueOrDefault(),
                                data: new
                                {
                                    exception = y
                                },
                                callId: callRequestSU.DispatchEntryId.GetValueOrDefault(),
                                queueItemId: qi.QueueItemId,
                                poNumber: call?.PurchaseOrderNumber);

                            var en = new EntryNote() { DispatchEntryId = call.Id, OwnerUserId = 1 };
                            en.Content = "Sending GOA Status to BCAA failed with error: " + y.Message;
                            en.CreateDate = DateTime.Now;
                            en.OwnerUserId = 1;
                            await en.SaveAsync();
                        }
                        catch
                        {
                            // silently ignore note failure.
                        }
                        try
                        {
                            await sourceMessage.CompleteAsync();
                        }
                        catch
                        {

                        }
                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                    }
                    break;

                case DigitalDispatchActionQueueItemType.OutgoingUpdateVehicle:
                    async Task doUpdateVehicle()
                    {
                        var uv = JsonConvert.DeserializeObject<VehicleUpdatePayload>(qi.JsonObject);

                        Console.WriteLine(uv.ToJson(true));

                        var aac = AaaContractor.GetByCompanyId(qi.CompanyId.Value)
                            .FirstOrDefault(o => o.AccountId == qi.AccountId.Value);

                        var dispatch = AaaDispatch.GetByCallRequestId(uv.CallRequestId);

                        if (dispatch != null && dispatch.IsClosed)
                        {
                            if (sourceMessage != null)
                                await sourceMessage.CompleteAsync();

                            DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                            return;
                        }
                        client = GetClient(aac, qi, uv.CallRequestId, uv.CallId, dispatch.DispatchId);
                        
                        client.Update( new BcaaRestClient.DispatchUpdateVehiclePayload()
                            {
                                DispatchId = dispatch.DispatchId,
                                ContractorId = aac.ContractorId,
                                Vehicle = new BcaaRestClient.DispatchUpdateVehiclePayload.VehicleUpdate()
                                {
                                    Make = uv.Make ?? "Unknown",
                                    Model = uv.Model ?? "Unknown",
                                    Color = uv.Color ?? "Unknown",
                                    Year = uv.Year != null ? uv.Year.ToString() : "Unkwn",
                                    Odometer = uv.Odometer ?? "",
                                    DriveType = uv.DriveType ?? "", 
                                    LicensePlate = uv.LicenseNumber ?? "",
                                    Vin = uv.Vin ?? "",
                                }
                            });

                        if (sourceMessage != null)
                            await sourceMessage.CompleteAsync();

                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                    }
                    await doUpdateVehicle();
                    break;

                case DigitalDispatchActionQueueItemType.OutgoingUpdatePickup:

                    async Task doUpdatePickup()
                    {
                        var uv = JsonConvert.DeserializeObject<VehicleUpdatePickupAddressPayload>(qi.JsonObject);

                        Console.WriteLine(uv.ToJson(true));

                        var aac = AaaContractor.GetByCompanyId(qi.CompanyId.Value)
                            .FirstOrDefault(o => o.AccountId == qi.AccountId.Value);

                        var dispatch = AaaDispatch.GetByCallRequestId(uv.CallRequestId);
                        if (dispatch != null && dispatch.IsClosed)
                        {
                            if (sourceMessage != null)
                                await sourceMessage.CompleteAsync();

                            DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                            return;
                        }

                        client = GetClient(aac, qi, uv.CallRequestId, uv.CallId, dispatch.DispatchId);

                        var ghr = await GeocodeHelper.Geocode(uv.Latitude + ", " + uv.Longitude);

                        if (ghr != null)
                        {
                            var ica = new BcaaRestClient.DispatchUpdateIncidentAddressPayload
                            {
                                DispatchId = dispatch.DispatchId,
                                ContractorId = aac.ContractorId,
                                IncidentAddress = new BcaaRestClient.DispatchUpdateIncidentAddressPayload.IncAddress()
                                {
                                    Latitude = uv.Latitude.GetValueOrDefault(),
                                    Longitude = uv.Longitude.GetValueOrDefault(),
                                    State = ghr.State,
                                    City = ghr.City,
                                    Address = ghr.Address ?? uv.Latitude + ", " + uv.Longitude,
                                    Zip = ghr.Zip
                                }
                            };

                            var hash = Core.MD5(Math.Round(uv.Latitude.GetValueOrDefault(), 6) + "," +
                                Math.Round(uv.Longitude.GetValueOrDefault(), 6));

                            if (Core.GetRedisValue("bcaa:" + uv.CallId + ":pu") != hash)
                            {
                                var job = JsonConvert.DeserializeObject<DispatchModel>(
                                    dispatch.DispatchJson).Job;

                                if (job.IncidentAddress.Latitude != ica.IncidentAddress.Latitude ||
                                    job.IncidentAddress.Longitude != ica.IncidentAddress.Longitude)
                                {
                                    client.Update(ica);
                                }
                                Core.SetRedisValue("bcaa:" + uv.CallId + ":pu", hash, TimeSpan.FromDays(2));
                            }
                        }

                        if (sourceMessage != null)
                            await sourceMessage.CompleteAsync();

                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                    }
                    await doUpdatePickup();
                    break;

                case DigitalDispatchActionQueueItemType.OutgoingUpdateDestination:
                    async Task doUpdateDestination()
                    {
                        var uv = JsonConvert.DeserializeObject<VehicleUpdateDestinationAddressPayload>(qi.JsonObject);

                        Console.WriteLine(uv.ToJson(true));

                        var aac = AaaContractor.GetByCompanyId(qi.CompanyId.Value)
                            .FirstOrDefault(o => o.AccountId == qi.AccountId.Value);

                        var dispatch = AaaDispatch.GetByCallRequestId(uv.CallRequestId);

                        if (dispatch != null && dispatch.IsClosed)
                        {
                            if (sourceMessage != null)
                                await sourceMessage.CompleteAsync();

                            DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                            return;
                        }
                        client = GetClient(aac, qi, uv.CallRequestId, uv.CallId, dispatch.DispatchId);

                        var ghr = await GeocodeHelper.Geocode(uv.Latitude + ", " + uv.Longitude);

                        if (ghr != null)
                        {
                            var payload = new BcaaRestClient.DispatchUpdateDestinationAddressPayload
                            {
                                DispatchId = dispatch.DispatchId,
                                ContractorId = aac.ContractorId,
                                DestinationAddress = new BcaaRestClient.DispatchUpdateDestinationAddressPayload.DestAddress()
                                {
                                    Latitude = uv.Latitude.GetValueOrDefault(),
                                    Longitude = uv.Longitude.GetValueOrDefault(),
                                    State = ghr.State,
                                    City = ghr.City,
                                    Address = ghr.Address ?? (uv.Latitude + "," + uv.Longitude),
                                    Zip = ghr.Zip                                    
                                }
                            };

                            var hash = Core.MD5(Math.Round(uv.Latitude.GetValueOrDefault(), 6) + "," + Math.Round(uv.Longitude.GetValueOrDefault(), 6));

                            if (Core.GetRedisValue("bcaa:" + uv.CallId + ":td") != hash)
                            {
                                var job = JsonConvert.DeserializeObject<DispatchModel>(
                                    dispatch.DispatchJson).Job;

                                if (job.DestinationAddress?.Latitude != payload.DestinationAddress.Latitude ||
                                    job.DestinationAddress?.Longitude != payload.DestinationAddress.Longitude)
                                {
                                    client.Update(payload);
                                }

                                Core.SetRedisValue("bcaa:" + uv.CallId + ":td", hash, TimeSpan.FromDays(2));
                            }
                        }

                        if (sourceMessage != null)
                            await sourceMessage.CompleteAsync();

                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                    }
                    await doUpdateDestination();
                    break;

                case DigitalDispatchActionQueueItemType.OutgoingUpdateNotes:
                    async Task doUpdateNotes()
                    {
                        var uv = JsonConvert.DeserializeObject<NotesUpdatePayload>(qi.JsonObject);

                        Console.WriteLine(uv.ToJson(true));

                        var aac = AaaContractor.GetByCompanyId(qi.CompanyId.Value)
                            .FirstOrDefault(o => o.AccountId == qi.AccountId.Value);

                        var dispatch = AaaDispatch.GetByCallRequestId(uv.CallRequestId);

                        if (dispatch != null && dispatch.IsClosed)
                        {
                            if (sourceMessage != null)
                                await sourceMessage.CompleteAsync();

                            DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                            return;
                        }

                        client = GetClient(aac, qi, uv.CallRequestId, uv.CallId, dispatch.DispatchId);

                        var hash = Core.MD5(uv.Notes);

                        var cleanedNote = string.Join("\n", uv.Notes.Split('\n').Where(o =>
                            !o.StartsWith("Priority:") &&
                            !o.StartsWith("** NEEDS FLATBED **")));


                        if ((await Core.GetRedisValueAsync("bcaa:" + uv.CallId + ":notes")) != hash)
                        {/*
                            await client.UpdateCallDetails(
                                new 
                                {
                                    DispatchId = dispatch.DispatchId,
                                    Notes = cleanedNote
                                });
                            */
                            await Core.SetRedisValueAsync("bcaa:" + uv.CallId + ":notes", hash);
                        }

                        if (sourceMessage != null)
                            await sourceMessage.CompleteAsync();

                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                    }
                    await doUpdateNotes();
                    break;


                default:
                    await sourceMessage.DeadLetterAsync("BCAA No implementation written", qi.ToJson());
                    DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                    logger.LogEvent("Queue item doesn't have a Type set that we have a implementation written for... MessageId {0}, Body = {1}", qi.CompanyId, LogLevel.Error, sourceMessage.Message.MessageId, qi.ToJson());
                    return;
            }

        }
    }
}
    
