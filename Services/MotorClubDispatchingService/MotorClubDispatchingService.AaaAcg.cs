using Azure.Messaging.ServiceBus;
using Extric.Towbook;
using Extric.Towbook.Accounts;
using Extric.Towbook.AutoDataDirect;
using Extric.Towbook.Dispatch;
using Extric.Towbook.EventNotifications;
using Extric.Towbook.Integration;
using Extric.Towbook.Integration.MotorClubs;
using Extric.Towbook.Integration.MotorClubs.Dispatch;
using Extric.Towbook.Integration.MotorClubs.Model;
using Extric.Towbook.Integration.MotorClubs.Queue;
using Extric.Towbook.Integration.MotorClubs.Services;
using Extric.Towbook.Integration.MotorClubs.Services.Models;
using Extric.Towbook.Integrations.MotorClubs.Aaa;
using Extric.Towbook.Integrations.MotorClubs.Aaa.ACG;
using Extric.Towbook.Management;
using Extric.Towbook.Services.MotorClubDispatchingService;
using Extric.Towbook.Services.MotorClubDispatchingService.AutoDispatch;
using Extric.Towbook.Utility;
using Extric.Towbook.Vehicle;
using NewRelic.Api.Agent;
using Newtonsoft.Json;
using NLog;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Runtime.ConstrainedExecution;
using System.Threading;
using System.Threading.Tasks;
using AttributeValue = Extric.Towbook.Dispatch.AttributeValue;

namespace Extric.Towbook.Services.MotorClubDispatchingService
{
    public partial class MotorClubDispatchingService
    {
        public static bool disableAcg = false;
        private const int AcgUserId = 23;

        [Transaction]
        public static async Task<bool> HandleAaaAcgIncomingMessage(DigitalDispatchActionQueueItem qi, AaaMessage jsonObj, ProcessMessageEventArgs sourceMessage)
        {
            var contractor = AaaContractor.GetById(jsonObj?.AaaContractorId ?? 0);

            switch (qi.Type)
            {
                case DigitalDispatchActionQueueItemType.IncomingCallOffered:
                case DigitalDispatchActionQueueItemType.IncomingCallReceived:
                    async Task doReceived()
                    {
                        var sj = JsonConvert.DeserializeObject<SalesforceData<ScheduledJobPayload>>(jsonObj.JsonData);

                        logger.Info(MasterAccountTypes.AaaAcg,
                             "CallReceived",
                             "Incoming Job Offer",
                             contractor.ContractorId,
                             null,
                             jsonObj.DispatchId,
                             qi.CompanyId,
                             new
                             {
                                 request = sj
                             },
                             queueItemId: qi.QueueItemId);

                        if (sj.Payload.IsRemoved)
                        {
                            var crx = AaaDispatch.GetAllByMasterAccountDispatchId(sj.Payload.WorkOrderId, MasterAccountTypes.AaaAcg)
                              .Select(o => CallRequest.GetById(o.CallRequestId))
                              .FirstOrDefault(o => o?.DispatchEntryId != null);

                            var removeEntry = Entry.GetByIdNoCache(crx?.DispatchEntryId ?? 0);
                            if (removeEntry != null)
                            {
                                if (removeEntry.Status.IsCurrent())
                                {
                                    await removeEntry.Cancel("Reassigned to another facility by AAA", new AuthenticationToken()
                                    {
                                        UserId = AcgUserId,
                                        ClientVersionId = MyClientVersionId
                                    }, "0.0.0.0");
                                }

                                await AcgGetClient(contractor, qi, crx.CallRequestId, removeEntry.Id, sj.Payload.WorkOrderId)
                                    .AcknowledgePlatformEvent(PlatformEventType.CallAssignment,
                                        sj.Payload.WorkOrderId,
                                        new AcknowledgePlatformEventPayload()
                                        {
                                            EventId = sj.Event.EventUuid,
                                            SyncStatus = "Success",
                                            SyncStatusDescription = "Processed IsRemoved successfully.",
                                            ExternalId = crx.CallRequestId.ToString()
                                        });
                            }
                            else
                            {
                                crx = AaaDispatch.GetAllByMasterAccountDispatchId(sj.Payload.WorkOrderId, MasterAccountTypes.AaaAcg)
                                    .Select(o => CallRequest.GetById(o.CallRequestId))
                                    .FirstOrDefault(o => o?.DispatchEntryId == null && (o?.Status == CallRequestStatus.None ||
                                    o?.Status == CallRequestStatus.Cancelled));
                                if (crx?.Status == CallRequestStatus.None)
                                {
                                    await crx.UpdateStatus(CallRequestStatus.Cancelled);
                                    await AcgGetClient(contractor, qi, crx.CallRequestId, removeEntry.Id, sj.Payload.WorkOrderId)
                                        .AcknowledgePlatformEvent(PlatformEventType.CallAssignment,
                                            sj.Payload.WorkOrderId,
                                            new AcknowledgePlatformEventPayload()
                                            {
                                                EventId = sj.Event.EventUuid,
                                                SyncStatus = "Success",
                                                SyncStatusDescription = "Processed IsRemoved successfully, cancelled pending offer.",
                                                ExternalId = crx.CallRequestId.ToString()
                                            });
                                }
                                else
                                {
                                    await AcgGetClient(contractor, qi, crx?.CallRequestId ?? 0, 0, sj.Payload.WorkOrderId)
                                        .AcknowledgePlatformEvent(PlatformEventType.CallAssignment,
                                            sj.Payload.WorkOrderId,
                                            new AcknowledgePlatformEventPayload()
                                            {
                                                EventId = sj.Event.EventUuid,
                                                SyncStatus = "Failure",
                                                SyncStatusDescription = "Couldn't find existing call to remove.",
                                                ExternalId = "unknown"
                                            });
                                }
                            }

                            DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);

                            if (sourceMessage != null)
                                await sourceMessage.CompleteAsync();

                            return;
                        }
                        else
                        {
                            // check if it's already assigned to another company... if it is, remove it.
                            foreach (var aaad in AaaDispatch.GetAllByMasterAccountDispatchId(sj.Payload.WorkOrderId, MasterAccountTypes.AaaAcg))
                            {
                                if (aaad != null)
                                {
                                    var cr = CallRequest.GetById(aaad.CallRequestId);

                                    aaad.IsClosed = true;
                                    aaad.Save();

                                    if (cr?.DispatchEntryId != null)
                                    {
                                        var en = await Entry.GetByIdNoCacheAsync(cr.DispatchEntryId.Value);
                                        if (en != null && en.Status.IsCurrent())
                                        {
                                            await en.Cancel("Cancelled by AAA - Reassigned to another facility", new AuthenticationToken()
                                            {
                                                UserId = AcgUserId,
                                                ClientVersionId = MyClientVersionId
                                            }, "0.0.0.0");
                                        }
                                    }
                                }
                            }
                        }


                        var rcr = await CreateCallRequest(jsonObj, MotorClubName.AaaAcg);

                        qi.CallRequestId = rcr.CallRequestId;
                        DigitalDispatchService.LogAction(qi);

                        var client = AcgGetClient(contractor, qi, rcr.CallRequestId, 0, sj.Payload.WorkOrderId);

                        await client.AcknowledgePlatformEvent(PlatformEventType.CallAssignment,
                            sj.Payload.WorkOrderId,
                            new AcknowledgePlatformEventPayload()
                            {
                                EventId = sj.Event.EventUuid,
                                SyncStatus = "Success",
                                SyncStatusDescription = "Created call offer successfully",
                                ExternalId = rcr.CallRequestId.ToString()
                            });

                        logger.Info(MasterAccountTypes.AaaAcg,
                            "CallReceived",
                            "Incoming Job Offer Created",
                            contractor.ContractorId,
                            null,
                            jsonObj.DispatchId,
                            qi.CompanyId,
                            callRequestId: rcr.CallRequestId,
                            queueItemId: qi.QueueItemId);

                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);

                        if (sourceMessage != null)
                            await sourceMessage.CompleteAsync();
                    }
                    await doReceived();
                    break;

                case DigitalDispatchActionQueueItemType.IncomingCallRejected:
                    async Task doRejected()
                    {
                        var cc = JsonConvert.DeserializeObject<IncomingRefusedModel>(jsonObj.JsonData);
                        var ccujo = AaaDispatch.GetByDispatchId(jsonObj.DispatchId, jsonObj.AaaContractorId);
                        if (ccujo == null)
                        {
                            logger.Error(MasterAccountTypes.AaaAcg,
                                "IncomingCallRejected",
                                "Received cancel for call that doesn't exist",
                                contractor.ContractorId, null, jsonObj.DispatchId, qi.CompanyId,
                                new
                                {
                                    message = cc
                                });

                            DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                            await sourceMessage.CompleteAsync();
                            return;
                        }

                        var ccr = CallRequest.GetById(ccujo.CallRequestId);
                        await ccr.UpdateStatus(CallRequestStatus.RejectedByMotorClub);

                        qi.CallRequestId = ccr.CallRequestId;
                        DigitalDispatchService.LogAction(qi);

                        logger.Log(LogLevel.Info, "ACE/{0}: Offer Rejected by AAA. Type = {1}, CallRequestId = {2}", qi.QueueItemId, qi.Type, ccr.CallRequestId);

                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                        await sourceMessage.CompleteAsync();
                    }
                    await doRejected();
                    break;

                case DigitalDispatchActionQueueItemType.IncomingCallCancelled:
                    async Task doCancelled()
                    {
                        var icm = JsonConvert.DeserializeObject<IncomingCancelledModel>(jsonObj.JsonData);
                        var ccujo = AaaDispatch.GetByDispatchId(jsonObj.DispatchId, jsonObj.AaaContractorId);
                        if (ccujo == null)
                        {
                            logger.Error(MasterAccountTypes.AaaAcg,
                                "IncomingCallCancelled",
                                "Received cancel for call that doesn't exist",
                                contractor.ContractorId, null, jsonObj.DispatchId, qi.CompanyId,
                                new
                                {
                                    message = icm
                                });

                            DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                            await sourceMessage.CompleteAsync();
                            return;
                        }


                        var ccr = CallRequest.GetById(ccujo.CallRequestId);
                        await ccr.UpdateStatus(CallRequestStatus.Cancelled);

                        ccujo.IsClosed = true;
                        ccujo.Save();

                        bool cancelled = false;

                        if (ccr.DispatchEntryId.GetValueOrDefault() > 0)
                        {
                            var en = Entry.GetByIdNoCache(ccr.DispatchEntryId.Value);
                            if (en != null)
                            {
                                if (ShouldAllowCancel(en))
                                {
                                    await en.Cancel("Cancelled by AAA: " + icm.Notes, new AuthenticationToken()
                                    {
                                        UserId = AcgUserId,
                                        ClientVersionId = MyClientVersionId
                                    }, "127.0.0.1");

                                    cancelled = true;
                                }
                                else
                                {
                                    cancelled = false;
                                }
                            }
                        }

                        qi.CallRequestId = ccr.CallRequestId;
                        DigitalDispatchService.LogAction(qi);

                        if (cancelled)
                        {
                            logger.Log(LogLevel.Info, "ACE/{0}: Offer Cancelled. Type = {1}, CallRequestId = {2}", qi.QueueItemId, qi.Type, ccr.CallRequestId);
                        }
                        else
                        {
                            logger.Log(LogLevel.Info, "ACE/{0}: Offer Cancel event received but failed to be canceled. Type = {1}, CallRequestId = {2}", qi.QueueItemId, qi.Type, ccr.CallRequestId);
                        }

                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                        await sourceMessage.CompleteAsync();
                    }
                    await doCancelled();
                    break;

                case DigitalDispatchActionQueueItemType.IncomingCallAccepted:
                    // not used. unless there's a platform event im not aware of?
                    break;
                case DigitalDispatchActionQueueItemType.IncomingCallUpdate:
                    async Task doCallUpdate()
                    {
                        var sj1 = JsonConvert.DeserializeObject<SalesforceData<dynamic>>(jsonObj.JsonData);


                        AaaDispatch ad = AaaDispatch.GetByDispatchId((string)sj1.Payload.workOrderId);
                        var cr = CallRequest.GetById(ad.CallRequestId);
                        var en = Entry.GetByIdNoCache(cr.DispatchEntryId.Value);

                        var client = AcgGetClient(AaaContractor.GetById(ad.AaaContractorId),
                            qi, cr.CallRequestId, 0, ad.DispatchId);

                        var originalDispatch = JsonConvert.DeserializeObject<SalesforceData<ScheduledJobPayload>>(
                            ad.DispatchJson).Payload;

                        if (sj1.Payload.operationType == "VehicleChange")
                        {
                            var vc = JsonConvert.DeserializeObject<SalesforceData<UpdateCallDetailsVehicleChangePayload>>(jsonObj.JsonData);
                            Console.WriteLine("Processing Vehicle Change");
                            Console.WriteLine(vc.ToJson(true));

                            var fa = en.Assets.First();
                            if (vc.Payload.VehicleColor != null)
                            {
                                var vuc = VehicleUtility.GetColorIdByName(vc.Payload.VehicleColor);
                                if (vuc != 0)
                                    fa.ColorId = vuc;

                            }
                            fa.Make = vc.Payload.VehicleMake;
                            fa.Model = vc.Payload.VehicleModel;

                            if (int.TryParse(vc.Payload.VehicleYear, out int vehicleYear))
                                fa.Year = vehicleYear;

                            await en.Save(false, new AuthenticationToken()
                            {
                                UserId = AcgUserId,
                                ClientVersionId = MyClientVersionId
                            });

                            await client.AcknowledgePlatformEvent(PlatformEventType.CallDetailsUpdate,
                                originalDispatch.ServiceAppointmentId,
                                new AcknowledgePlatformEventPayload()
                                {
                                    ExternalId = en.Id.ToString(),
                                    SyncStatus = "Success",
                                    SyncStatusDescription = "VehicleChangeAccept",
                                    EventId = sj1.Event.EventUuid
                                });
                        }
                        else if (sj1.Payload.operationType == "PhoneNumberChange")
                        {
                            var vc = JsonConvert.DeserializeObject<SalesforceData<UpdateCallDetailsPhoneNumberChangePayload>>(jsonObj.JsonData);
                            Console.WriteLine("Processing Vehicle Change");
                            Console.WriteLine(vc.ToJson(true));

                            var contact = en.Contacts.FirstOrDefault();
                            if (contact != null)
                            {
                                contact.Phone = vc.Payload.MemberPhoneNumber;

                            }
                            else
                            {
                                contact = new EntryContact() { Phone = vc.Payload.MemberPhoneNumber };
                                en.Contacts.Add(contact);
                            }

                            await en.Save(false, new AuthenticationToken()
                            {
                                UserId = AcgUserId,
                                ClientVersionId = MyClientVersionId
                            });

                            await client.AcknowledgePlatformEvent(PlatformEventType.CallDetailsUpdate,
                                originalDispatch.ServiceAppointmentId,
                                new AcknowledgePlatformEventPayload()
                                {
                                    ExternalId = en.Id.ToString(),
                                    SyncStatus = "Success",
                                    SyncStatusDescription = "PhoneNumberChangeAccept",
                                    EventId = sj1.Event.EventUuid
                                });
                        }
                        else if (sj1.Payload.operationType == "BreakdownAddressChange" ||
                            sj1.Payload.operationType == "BreakdownAdressChange")
                        {
                            var vc = JsonConvert.DeserializeObject<SalesforceData<UpdateCallDetailsBreakdownAddressChange>>(jsonObj.JsonData).Payload;
                            Console.WriteLine("Processing Breakdown Change");
                            Console.WriteLine(vc.ToJson(true));

                            var wp = en.Waypoints.FirstOrDefault(r => r.Title == "Pickup");

                            wp.Address = vc.Street + ", " + vc.City + ", " + vc.State + " " + vc.PostalCode;
                            if (vc.Latitude != null && vc.Longitude != null)
                            {
                                wp.Latitude = vc.Latitude.Value;
                                wp.Longitude = vc.Longitude.Value;
                            }
                            else
                            {
                                wp.Latitude = wp.Longitude = 0;
                            }

                            await en.Save(false, new AuthenticationToken()
                            {
                                UserId = AcgUserId,
                                ClientVersionId = MyClientVersionId
                            });

                            await client.AcknowledgePlatformEvent(PlatformEventType.CallDetailsUpdate,
                                originalDispatch.ServiceAppointmentId,
                                new AcknowledgePlatformEventPayload()
                                {
                                    ExternalId = en.Id.ToString(),
                                    SyncStatus = "Success",
                                    SyncStatusDescription = "BreakdownAddressChangeAccept",
                                    EventId = sj1.Event.EventUuid
                                });
                        }
                        else if (sj1.Payload.operationType == "TowAddressChange" ||
                            sj1.Payload.operationType == "TowAdressChange")
                        {
                            var vc = JsonConvert.DeserializeObject<SalesforceData<UpdateCallDetailsTowAddressChange>>(jsonObj.JsonData).Payload;
                            Console.WriteLine("Processing Tow Change");
                            Console.WriteLine(vc.ToJson(true));

                            var wp = en.Waypoints.FirstOrDefault(r => r.Title == "Destination");
                            if (wp == null)
                            {
                                wp = new EntryWaypoint() { Title = "Destination", Position = 2 };
                                en.Waypoints.Add(wp);
                            }

                            wp.Address = vc.TowStreet + ", " + vc.TowCity + ", " + vc.TowState + " " + vc.TowPostalCode;
                            if (vc.TowLatitude != null && vc.TowLongitude != null)
                            {
                                wp.Latitude = vc.TowLatitude.Value;
                                wp.Longitude = vc.TowLongitude.Value;
                            }
                            else
                            {
                                wp.Latitude = wp.Longitude = 0;
                            }

                            await en.Save(false, new AuthenticationToken()
                            {
                                UserId = AcgUserId,
                                ClientVersionId = MyClientVersionId
                            });

                            await client.AcknowledgePlatformEvent(PlatformEventType.CallDetailsUpdate,
                                originalDispatch.ServiceAppointmentId,
                                new AcknowledgePlatformEventPayload()
                                {
                                    ExternalId = en.Id.ToString(),
                                    SyncStatus = "Success",
                                    SyncStatusDescription = "TowAddressChangeAccept",
                                    EventId = sj1.Event.EventUuid
                                });
                        }

                        else if (sj1.Payload.operationType == "NotesUpdate")
                        {
                            var vc = JsonConvert.DeserializeObject<SalesforceData<UpdateCallDetailsNotesUpdatePayload>>(jsonObj.JsonData).Payload;
                            Console.WriteLine("Processing Notes Change");
                            Console.WriteLine(vc.ToJson(true));

                            en.Notes = vc.Notes;

                            await en.Save(false, new AuthenticationToken()
                            {
                                UserId = AcgUserId,
                                ClientVersionId = MyClientVersionId
                            });

                            await client.AcknowledgePlatformEvent(PlatformEventType.CallDetailsUpdate,
                                originalDispatch.ServiceAppointmentId,
                                new AcknowledgePlatformEventPayload()
                                {
                                    ExternalId = en.Id.ToString(),
                                    SyncStatus = "Success",
                                    SyncStatusDescription = "NotesUpdateAccept",
                                    EventId = sj1.Event.EventUuid
                                });
                        }

                        // ACG updated the call details. What should we do with it?
                        Console.WriteLine(JsonExtensions.ToJson(JsonConvert.DeserializeObject<dynamic>(jsonObj?.JsonData), true));

                        // 885150422

                        if (sourceMessage != null)
                            await sourceMessage.CompleteAsync();
                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                    }
                    await doCallUpdate();
                    break;

                case DigitalDispatchActionQueueItemType.IncomingCallUpdateStatus:
                    async Task doUpdateStatus()
                    {
                        var ucd = JsonConvert.DeserializeObject<SalesforceData<UpdateCallDetails>>(jsonObj.JsonData);

                        var newStatusId = 0;

                        switch (ucd.Payload.Status)
                        {
                            case StatusCodes.Spotted:
                            case StatusCodes.Scheduled:
                                newStatusId = Status.Waiting.Id;
                                break;

                            case StatusCodes.Dispatched:
                                newStatusId = Status.Dispatched.Id;
                                break;

                            case StatusCodes.Enroute:
                                newStatusId = Status.EnRoute.Id;
                                break;

                            case StatusCodes.OnScene:
                                newStatusId = Status.AtSite.Id;
                                break;

                            case StatusCodes.TowLoaded:
                                newStatusId = Status.BeingTowed.Id;
                                break;

                            case StatusCodes.Towing:
                                newStatusId = Status.BeingTowed.Id;
                                break;

                            case StatusCodes.Destination:
                                newStatusId = Status.DestinationArrival.Id;
                                break;

                            case StatusCodes.Complete:
                                newStatusId = Status.Completed.Id;
                                break;

                            case StatusCodes.Cancelled:
                                newStatusId = Status.Cancelled.Id;
                                break;

                            default:
                                newStatusId = -1;
                                break;
                        }

                        var ad = AaaDispatch.GetByDispatchId(ucd.Payload.WorkOrderId);
                        var cr = CallRequest.GetById(ad.CallRequestId);

                        var client = AcgGetClient(AaaContractor.GetById(ad.AaaContractorId),
                            qi, cr.CallRequestId, 0, ad.DispatchId);


                        if (cr.DispatchEntryId == null)
                        {
                            await client.AcknowledgePlatformEvent(PlatformEventType.ServiceAppointment,
                                ucd.Payload.ServiceAppointmentId,
                                new AcknowledgePlatformEventPayload()
                                {
                                    ExternalId = ucd.Payload.WorkOrderId,
                                    SyncStatus = "Failure",
                                    SyncStatusDescription = $"Status couldn't be updated. Call doesn't exist in Towbook, it was never created/accepted.",
                                    EventId = ucd.Event.EventUuid
                                });

                            // If it was spotted status, remove it. 
                            if (ucd.Payload.Status == "Spotted")
                            {
                                await cr.UpdateStatus(CallRequestStatus.Cancelled);
                            }



                            Console.WriteLine(JsonExtensions.ToJson(ucd, true));

                            DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);

                            if (sourceMessage != null)
                                await sourceMessage.CompleteAsync();
                            return;
                        }

                        var en = Entry.GetByIdNoCache(cr.DispatchEntryId.Value);

                        var newServiceResourceId = ucd.Payload.ServiceResourceId;

                        if (newServiceResourceId != null)
                        {
                            var existing = DriverKeyValue.Get(qi.CompanyId.Value, Provider.Towbook.ProviderId, "AcgServiceResourceId")
                                .FirstOrDefault(o => o.Value == newServiceResourceId)?.DriverId;

                            var aaaDriver = AaaDriver.GetByAaaContractorId(contractor.AaaContractorId).FirstOrDefault(r => r.AaaId == newServiceResourceId);
                            if (aaaDriver?.DriverId != null)
                                existing = aaaDriver.DriverId;

                            if (existing != null)
                            {
                                en.DriverId = existing.Value;

                                var fa = en.Assets.FirstOrDefault();

                                if (fa != null)
                                {
                                    fa.Drivers.Clear();

                                    var dtd = DriverTruckDefault.GetByDriverId(existing.Value);

                                    fa.Drivers.Add(new EntryAssetDriver()
                                    {
                                        DriverId = existing.Value,
                                        AssetId = fa.Id,
                                        TruckId = dtd?.TruckId ?? 0
                                    });
                                }
                            }
                        }

                        if (!string.IsNullOrWhiteSpace(ucd.Payload.Super7))
                        {
                            en.SetAttribute(AttributeValue.BUILTIN_ACG_SUPER7, ucd.Payload.Super7);
                        }

                        var originalStatus = en.Status.Name;


                        if (newStatusId == Status.Cancelled.Id)
                        {
                            await en.Cancel("AAA Cancelled", new AuthenticationToken()
                            {
                                UserId = AcgUserId,
                                ClientVersionId = MyClientVersionId
                            });
                        }
                        else if (newStatusId != -1)
                        {
                            en.Status = await Status.GetByIdAsync(newStatusId);

                            await en.Save(false, new AuthenticationToken()
                            {
                                UserId = AcgUserId,
                                ClientVersionId = MyClientVersionId
                            });
                        }

                        await client.AcknowledgePlatformEvent(PlatformEventType.ServiceAppointment,
                            ucd.Payload.ServiceAppointmentId,
                            new AcknowledgePlatformEventPayload()
                            {
                                ExternalId = en.Id.ToString(),
                                SyncStatus = newStatusId > -1 ? "Success" : "Failure",
                                SyncStatusDescription = (newStatusId == -1 ? $"Status couldn't be updated. Towbook doesn't have a mapping implemented that recognizes {ucd.Payload.Status}" :
                                    "Updated Status of call from " + originalStatus + " to " + en.Status.Name),
                                EventId = ucd.Event.EventUuid
                            });

                        if (newStatusId == Status.Dispatched.Id)
                        {
                            await AutoDispatchServiceBusHandler.SendDispatchToDriver(en, en.Driver);
                        }

                        Console.WriteLine(JsonExtensions.ToJson(ucd, true));

                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);

                        if (sourceMessage != null)
                            await sourceMessage.CompleteAsync();
                    }
                    await doUpdateStatus();
                    // ACG updated the call status. What should we do with it?
                    break;

                case DigitalDispatchActionQueueItemType.IncomingDriverCreated:
                    async Task doDriverAdd()
                    {
                        Provider.Towbook.RegisterKey(Extric.Towbook.Integration.KeyType.Driver, "AcgServiceResourceId", "");
                        Provider.Towbook.RegisterKey(Extric.Towbook.Integration.KeyType.Truck, "AcgVehicleId", "");
                        
                        var ucd = JsonConvert.DeserializeObject<SalesforceData<CreateServiceResource>>(jsonObj.JsonData);

                        contractor = AaaContractor.GetByContractorId(ucd.Payload.AcgEnterpriseFacilityId,
                            MasterAccountTypes.AaaAcg, 3);

                        if (contractor == null)
                            contractor = AaaContractor.GetByServiceTerritory(ucd.Payload.ServiceTerritoryId, 3);

                        if (contractor == null)
                        {
                            Console.WriteLine("no such contractor, add to defer queue and retry in 30 seconds.");

                            var message2 = new BrokeredMessage(qi.ToJson());

                            message2.MessageId = "Retry_DigitalDispatchService/" + qi.QueueItemId;
                            message2.ApplicationProperties["id"] = qi.QueueItemId.ToString();
                            message2.ApplicationProperties["requestCreated"] = DateTime.UtcNow.ToString();
                            message2.ApplicationProperties["type"] = (int)qi.Type;
                            message2.ApplicationProperties["companyId"] = qi.CompanyId;
                            message2.ApplicationProperties["jsonObject"] = qi.JsonObject;
                            message2.ApplicationProperties["masterAccountId"] = 45;
                            message2.ApplicationProperties["environmentId"] = 3;
                            message2.ApplicationProperties["eventUuid"] = ucd.Event.EventUuid;
                            message2.ApplicationProperties["replayId"] = ucd.Event.ReplayId;

                            message2.ScheduledEnqueueTime = DateTime.UtcNow.AddSeconds(60);

                            var qc = await ServiceBusHelper.CreateProducerQueueAsync("Mcb-DigitalDispatchInQueue", true);
                            await qc.SendAsync(message2);
                            Console.WriteLine("sent " + qi.ToJson(true));
                            if (sourceMessage != null)
                                await sourceMessage.CompleteAsync();

                            return;
                        }

                        if (contractor != null && qi.CompanyId == null)
                            qi.CompanyId = contractor.CompanyId;
                        var allDrivers = Driver.GetByCompany(await Company.Company.GetByIdAsync(qi.CompanyId.Value));


                        var client = AcgGetClient(contractor, qi, 0, 0, ucd.Payload.AcgEnterpriseFacilityId);

                        var resourceId = ucd.Payload.ServiceResourceId;
                        int driverId = 0;

                        var existing = DriverKeyValue.Get(qi.CompanyId.Value, Provider.Towbook.ProviderId, "AcgServiceResourceId")
                            .Where(o => o.Value == resourceId);

                        if (existing.Any() || AaaDriver.GetByAaaContractorId(contractor.AaaContractorId).Any(o => o.AaaId == resourceId))
                        {
                            if (sourceMessage != null)
                            await client.AcknowledgePlatformEvent(PlatformEventType.ServiceResource, resourceId,
                                new AcknowledgePlatformEventPayload()
                                {
                                    EventId = ucd.Event.EventUuid,
                                    SyncStatus = "Success",
                                    ExternalId = qi.QueueItemId + "." + existing.First().DriverId.ToString(),
                                    SyncStatusDescription = "Driver already exists, no action taken."
                                });
                            // already exists, don't update it. 
                        }
                        else
                        {
                            var name = ucd.Payload.FirstName + " " + ucd.Payload.LastName;

                            var existingDriver = allDrivers.FirstOrDefault(o => o.Email?.ToLower() == ucd.Payload.Email.ToLower() ||
                                o.Name?.ToLower().Replace(" ", "") == name.ToLower().Replace(" ", ""));
                            var result = "";

                            if (existingDriver != null)
                            {
                                driverId = existingDriver.Id;
                                result = "Linked with existing driver";
                            }
                            else
                            {
                                existingDriver = new Driver();
                                existingDriver.CompanyId = qi.CompanyId.Value;
                                existingDriver.Name = name;
                                existingDriver.Email = ucd.Payload.Email;

                                existingDriver.Notes = "Created by ACG Integration - Linked with matching Driver in Salesforce FSL";

                                await existingDriver.Save();

                                driverId = existingDriver.Id;
                                result = "Created new driver";
                            }

                            var aaaDriver = new AaaDriver() { DriverId = driverId, AaaId = resourceId, AaaContractorId = contractor.AaaContractorId };
                            await aaaDriver.Save();

                            if (ucd.Payload.VehicleRecordId != null)
                            {
                                var linkedTrucks = AaaVehicle.GetByAaaContractorId(contractor.AaaContractorId);

                                var foundTruckLink = linkedTrucks.FirstOrDefault(o => o.AaaId == ucd.Payload.VehicleRecordId);
                                if (foundTruckLink != null)
                                {
                                    await new DriverTruckDefault()
                                    {
                                        TruckId = foundTruckLink.TruckId,
                                        DriverId = driverId,
                                        SourceId = DriverTruckDefaultSource.Unspecified
                                    }.Save(1);
                                }
                                else
                                {
                                    var newTruck = new Truck()
                                    {
                                        CompanyId = qi.CompanyId.Value,
                                        Name = "AAA Vehicle " + ucd.Payload.VehicleRecordId
                                    };
                                    await newTruck.Save();

                                    await new DriverTruckDefault()
                                    {
                                        TruckId = newTruck.Id,
                                        DriverId = driverId,
                                        SourceId = DriverTruckDefaultSource.Unspecified
                                    }.Save(1);

                                    await new AaaVehicle()
                                    {
                                        TruckId = newTruck.Id,
                                        AaaContractorId = contractor.AaaContractorId,
                                        AaaId = ucd.Payload.VehicleRecordId
                                    }.Save();
                                }
                            }

                            await client.AcknowledgePlatformEvent(PlatformEventType.ServiceResource, resourceId,
                                new AcknowledgePlatformEventPayload()
                                {
                                    EventId = ucd.Event.EventUuid,
                                    SyncStatus = "Success",
                                    ExternalId = qi.QueueItemId + "." + driverId.ToString(),
                                    SyncStatusDescription = result
                                });
                        }

                        // check for an existing driver

                        // if not found, create a new driver

                        // if found, update the driver

                        Console.WriteLine(JsonExtensions.ToJson(ucd, true));

                        if (sourceMessage != null)
                            await sourceMessage.CompleteAsync();

                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                    }
                    await doDriverAdd();
                    break;

                case DigitalDispatchActionQueueItemType.IncomingTruckCreated:
                    async Task doTruckAdd()
                    {
                        var tad = JsonConvert.DeserializeObject<SalesforceData<FacilityVehicle>>(jsonObj?.JsonData);
                        var ta = tad.Payload;
                        Console.WriteLine(JsonExtensions.ToJson(JsonConvert.DeserializeObject<SalesforceData<FacilityVehicle>>(jsonObj?.JsonData), true));

                        var aac = AaaContractor.GetByContractorId(
                            ta.FacilityId, MasterAccountTypes.AaaAcg, ta.EnvironmentId);

                        if (aac == null)
                        {
                            Console.WriteLine("no such contractor, add to defer queue and retry in 30 seconds.");

                            int environmentId = 3;

                            if (sourceMessage != null)
                            {
                                if (sourceMessage.Message.ApplicationProperties.TryGetValue("environmentId",
                                    out object tempEnvironmentId))
                                {
                                    if (tempEnvironmentId != null)
                                        environmentId = (int)tempEnvironmentId;
                                }
                            }

                            AcgHelpers.AddToQueue(ta.FacilityId,
                                environmentId, qi);

                            if (sourceMessage != null)
                                await sourceMessage.CompleteAsync();

                            return;
                        }

                        var trucks = await Truck.GetByCompanyAsync(await Company.Company.GetByIdAsync(aac.CompanyId), false);

                        var newTruck = trucks.FirstOrDefault(o => o.Name?.Trim().ToLowerInvariant() == ta.Name.Trim().ToLowerInvariant());

                        if (newTruck == null)
                        {
                            newTruck = new Truck();
                            newTruck.CompanyId = aac.CompanyId;
                            newTruck.Name = ta.Name;
                            newTruck.IsActive = true;

                            if (ta.VehicleType == "Flatbed")
                                newTruck.Type = Truck.TruckType.FlatBed;
                            else if (ta.VehicleType == "Light_Service")
                            {
                                newTruck.Type = Truck.TruckType.ServiceVehicle;
                                newTruck.Duty = Truck.DutyType.Light;
                            }
                            else if (ta.VehicleType == "Wheel_Lift")
                                newTruck.Type = Truck.TruckType.Wrecker;

                            newTruck.Notes = "Created from ACG Registration/Import";

                            await newTruck.Save();
                        }

                        var av = new AaaVehicle()
                        {
                            TruckId = newTruck.Id,
                            AaaId = ta.VehicleId,
                            Name = ta.Name,
                            AaaContractorId = aac.AaaContractorId
                        };

                        await av.Save();
                        /*
                        var client = AcgGetClient(aac, qi);

                        await client.AcknowledgePlatformEvent(PlatformEventType.ServiceResource, ta.VehicleId,
                            new AcknowledgePlatformEventPayload()
                            {
                                EventId = tad.Event.EventUuid,
                                SyncStatus = "Success",
                                ExternalId = qi.QueueItemId + "." + newTruck.Id.ToString(),
                                SyncStatusDescription = "success"
                            });
                        */
                        if (sourceMessage != null)
                            await sourceMessage.CompleteAsync();
                    }

                    await doTruckAdd();
                    break;

                case DigitalDispatchActionQueueItemType.IncomingContractorCreated:
                    async Task doAddContractor()
                    {
                        var ucde = JsonConvert.DeserializeObject<SalesforceData<Facility>>(jsonObj.JsonData);
                        var ucd = ucde.Payload;

                        Console.WriteLine(JsonExtensions.ToJson(ucd, true));

                        var existing = AaaContractor.GetByContractorId(ucd.FacilityId, MasterAccountTypes.AaaAcg, ucd.EnvironmentId);

                        if (existing != null)
                        {
                            DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                            return;
                        }

                        var newAccount = new Account()
                        {
                            CompanyId = qi.CompanyId.Value,
                            Company = "AAA " + ucd.FacilityId,
                            Type = AccountType.MotorClub,
                            MasterAccountId = MasterAccountTypes.AaaAcg
                        };

                        await newAccount.Save();

                        var newContractor = new AaaContractor()
                        {
                            CompanyId = qi.CompanyId.Value,
                            AccountId = newAccount.Id,
                            EnvironmentId = ucd.EnvironmentId,
                            MasterAccountId = MasterAccountTypes.AaaAcg,
                            ContractorId = ucd.FacilityId,
                            IsValidated = true,
                        };

                        newContractor.Save();

                        var akv = new AccountKeyValue(newAccount.Id, AccountKey.GetByProviderId(Provider.Towbook.ProviderId, "AcgFacilityId").Id,
                             ucd.AcgFacilitySfid);

                        akv.Save();

                        var contract = CompanyContract.GetExistingOrCreateNew(newAccount.CompanyId, 1);

                        if (contract.Features.Any(o => o.FeatureId == (int)Generated.Features.DriverSchedules && !o.IsDeleted))
                        {
                            Console.WriteLine("Company already has the feature specified assigned.");
                        }
                        else
                        {
                            var ccnew = new CompanyContractFeature();

                            ccnew.FeatureId = (int)Generated.Features.DriverSchedules;
                            ccnew.CompanyContractId = contract.Id;

                            await ccnew.Save(newAccount.CompanyId, 1);
                        }

                        /*
                        var client = AcgGetClient(newContractor, qi);

                        await client.AcknowledgePlatformEvent(PlatformEventType.ServiceResource, ucd.FacilityId,
                            new AcknowledgePlatformEventPayload()
                            {
                                EventId = ucde.Event.EventUuid,
                                SyncStatus = "Success",
                                ExternalId = qi.QueueItemId + "." + qi.CompanyId.ToString(),
                                SyncStatusDescription = "success"
                            });
                        */
                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);

                        if (sourceMessage != null)
                            await sourceMessage.CompleteAsync();

                        await AcgHelpers.CheckForQueuedUpdates(ucd.FacilityId,
                            ucd.EnvironmentId);

                    }
                    await doAddContractor();

                    break;


                case DigitalDispatchActionQueueItemType.IncomingCallGoaResponse:
                    async Task doGoa()
                    {
                        var cc = JsonConvert.DeserializeObject<IncomingGoaModel>(jsonObj.JsonData);
                        var ccujo = AaaDispatch.GetByDispatchId(jsonObj.DispatchId, jsonObj.AaaContractorId);

                        if (ccujo == null)
                        {
                            logger.Error(MasterAccountTypes.AaaAcg,
                                "IncomingCallGoaResponse",
                                "Received GOA accept for call that doesn't exist",
                                contractor.ContractorId, null, jsonObj.DispatchId, qi.CompanyId,
                                new
                                {
                                    message = cc
                                });

                            DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                            await sourceMessage.CompleteAsync();
                            return;
                        }

                        var ccr = CallRequest.GetById(ccujo.CallRequestId);
                        await ccr.UpdateStatus(CallRequestStatus.Cancelled);

                        if (ccr.DispatchEntryId.GetValueOrDefault() > 0)
                        {
                            var en = Entry.GetByIdNoCache(ccr.DispatchEntryId.Value);
                            if (en != null)
                            {
                                var now = Core.OffsetDateTime(en.Company, DateTime.Now);

                                var toAdd = "AAA GOA Response received at " + now.ToShortDateString() + " " + now.ToShortTowbookTimeString();
                                en.Notes = toAdd + "\n" + (en.Notes ?? "");

                                en.SetAttribute(Dispatch.AttributeValue.BUILTIN_BILLING_NOTES,
                                    toAdd + "\n" + en.BillingNotes());

                                en.Status = Status.Completed;

                                await en.Save(false, new AuthenticationToken()
                                {
                                    UserId = AcgUserId,
                                    ClientVersionId = MyClientVersionId
                                }, "127.0.0.1");
                                logger.Log(LogLevel.Info, "ACE/{0}: Offer completed by way of GOA response. Type = {1}, CallRequestId = {2}", qi.QueueItemId, qi.Type, ccr.CallRequestId);
                            }
                        }

                        qi.CallRequestId = ccr.CallRequestId;
                        DigitalDispatchService.LogAction(qi);

                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                        await sourceMessage.CompleteAsync();
                    }
                    await doGoa();
                    break;

                default:
                    logger.Error(MasterAccountTypes.AaaAcg,
                        "UnsupportedEvent",
                        "Event Type Not Implemented",
                        contractor.ContractorId,
                        contractor.LocationCode,
                        jsonObj.DispatchId,
                        qi.CompanyId,
                        new
                        {
                            message = jsonObj
                        },
                        ownerUserId: qi.OwnerUserId,
                        queueItemId: qi.QueueItemId);

                    DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                    if (sourceMessage != null)
                        await sourceMessage.CompleteAsync();
                    return true;
            }

            return true;
        }

        private static async Task<Entry> AcgDoCallCreateOrUpdate(
            DigitalDispatchActionQueueItem qi,
            ScheduledJobPayload accepted,
            AaaDispatch dispatch,
            CallRequest callRequest,
            AaaContractor contractor,
            ProcessMessageEventArgs sourceMessage)
        {
            var job = accepted;

            bool sendToDriver = false;

            Entry fe = await DistributedLock.ForAsync("ACG",
                accepted.WorkOrderId,
                10000,
                lockAcquired: async delegate ()
                {
                    Entry call = null;

                    var existingAAA = AttributeValue.GetAllByValue(qi.CompanyId.GetValueOrDefault(),
                        AttributeValue.BUILTIN_ACCOUNT_PURCHASEORDER, accepted.AppointmentNumber).LastOrDefault();

                    if (existingAAA != null)
                        call = Entry.GetByIdNoCache(existingAAA.DispatchEntryId);

                    if (call != null && call.CreateDate < DateTime.Now.AddDays(-7))
                        call = null;

                    if (call == null)
                    {
                        call = new Entry();
                        call.CompanyId = callRequest.CompanyId;
                        call.AccountId = callRequest.AccountId;
                    }
                    else
                    {
                        logger.LogEvent("ACG/{0}: Found existing towbook call for Case {1}... Call #{2}... we're going to update this one.",
                            callRequest.CompanyId, LogLevel.Info, sourceMessage?.Message?.MessageId, callRequest.CallRequestId.ToString(), call.CallNumber);

                        if (call.Status == Status.Cancelled)
                        {
                            logger.LogEvent("ACG/{0}: Call is cancelled. Proceeding to uncancel and set status to waiting...",
                                callRequest.CompanyId, LogLevel.Info, sourceMessage.Message.MessageId, job.AppointmentNumber.ToString(), call.CallNumber);

                            await call.Uncancel(new AuthenticationToken() { UserId = AcgUserId, ClientVersionId = MyClientVersionId });
                        }
                    }

                    if (job.Priority != null)
                    {
                        if (job.Priority == "Critical")
                            call.Priority = Entry.EntryPriority.High;

                        var line = "Priority: " + job.Priority + "\n";

                        if (!(call.Notes != null && call.Notes.Contains(line)))
                            call.Notes = line + call.Notes;
                    }

                    if (call.Account?.DefaultPriority == 1)
                        call.Priority = Entry.EntryPriority.High;

                    #region Vehicle

                    EntryAsset asset = null;
                    if (call.Assets != null)
                        asset = call.Assets.FirstOrDefault();

                    if (asset == null)
                        asset = new EntryAsset() { BodyTypeId = 1 };

                    if (asset.BodyTypeId == 0)
                        asset.BodyTypeId = 1;

                    var vc = VehicleUtility.GetColorIdByName(job.VehicleColor);

                    if (int.TryParse(job.VehicleYear, out int year))
                        asset.Year = year;

                    asset.Make = VehicleUtility.GetManufacturerByName(job.VehicleMake);
                    asset.Model = VehicleUtility.GetModelByName(job.VehicleModel);
                    if (!string.IsNullOrEmpty(job.VehicleDriveType))
                        asset.DriveType = job.VehicleDriveType;

                    if (vc != 0)
                        asset.ColorId = vc;


                    if (job.ServiceResourceId != null)
                    {
                        var driver = await GetDriverByServiceResourceId(dispatch, job.ServiceResourceId);

                        if (asset.Drivers == null)
                            asset.Drivers = new Collection<EntryAssetDriver>();

                        if (driver != null)
                        {
                            var defaultTruck = DriverTruckDefault.GetByDriverId(driver.Id);
                            asset.Drivers.Add(new EntryAssetDriver() { DriverId = driver.Id, TruckId = defaultTruck?.TruckId });
                        }

                        if (job.Status == StatusCodes.Dispatched || job.Status == "Scheduled")
                        {
                            if (call.Status != Status.Dispatched)
                            {
                                call.Status = Status.Dispatched;

                                sendToDriver = true;
                            }
                        }
                    }

                    #endregion Vehicle

                    #region Locations

                    var pickup = call.Waypoints.FirstOrDefault(o => o.Title == "Pickup");
                    var dest = call.Waypoints.FirstOrDefault(o => o.Title == "Destination");

                    if (call.Notes == null)
                        call.Notes = "";

                    if (job.Street != null)
                    {
                        if (string.IsNullOrWhiteSpace(call.TowSource))
                        {
                            var breakdown = job.Street;

                            if (!string.IsNullOrWhiteSpace(job.City) ||
                                !string.IsNullOrWhiteSpace(job.State) ||
                                !string.IsNullOrWhiteSpace(job.PostalCode))
                                breakdown += ",";

                            if (!string.IsNullOrWhiteSpace(job.City))
                                breakdown += " " + job.City;

                            if (!string.IsNullOrWhiteSpace(job.State))
                                breakdown += " " + job.State;

                            if (!string.IsNullOrWhiteSpace(job.PostalCode))
                                breakdown += " " + job.PostalCode;

                            if (!string.IsNullOrWhiteSpace(job.LocationType) &&
                                !string.Equals(job.LocationType, "Tow", StringComparison.InvariantCultureIgnoreCase) &&
                                !string.Equals(job.LocationType, "Breakdown", StringComparison.InvariantCultureIgnoreCase))
                                breakdown += " (" + job.LocationType + ")";

                            call.TowSource = breakdown;

                            if (pickup == null)
                            {
                                pickup = new EntryWaypoint()
                                {
                                    Title = "Pickup",
                                    Position = 1
                                };
                                call.Waypoints.Add(pickup);
                            }
                            pickup.Address = call.TowSource;

                            if (job.Latitude != null && job.Longitude != null)
                            {
                                pickup.Latitude = job.Latitude.Value;
                                pickup.Longitude = job.Longitude.Value;
                            }
                        }
                    }

                    if (job.TowStreet != null)
                    {
                        if (string.IsNullOrWhiteSpace(call.TowDestination))
                        {
                            var destAddress = job.TowStreet;


                            if (!string.IsNullOrWhiteSpace(job.TowCity) ||
                             !string.IsNullOrWhiteSpace(job.TowState) ||
                                !string.IsNullOrWhiteSpace(job.TowPostalCode))
                                destAddress += ",";

                            if (!string.IsNullOrWhiteSpace(job.TowCity))
                                destAddress += " " + job.TowCity;

                            if (!string.IsNullOrWhiteSpace(job.TowState))
                                destAddress += " " + job.TowState;

                            if (!string.IsNullOrWhiteSpace(job.TowPostalCode))
                                destAddress += " " + job.TowPostalCode;

                            if (!string.IsNullOrWhiteSpace(job.TowDestinationName) &&
                                !string.Equals(job.TowDestinationName, "Tow", StringComparison.InvariantCultureIgnoreCase))
                                destAddress += " (" + job.TowDestinationName + ")";

                            call.TowDestination = destAddress;
                            if (!string.IsNullOrWhiteSpace(call.TowDestination))
                            {
                                if (dest == null)
                                {
                                    dest = new EntryWaypoint() { Title = "Destination", Position = 2 };
                                    call.Waypoints.Add(dest);
                                }

                                dest.Address = call.TowDestination;

                                if (job.TowLatitude != null && job.TowLongitude != null)
                                {
                                    dest.Latitude = job.TowLatitude.Value;
                                    dest.Longitude = job.TowLongitude.Value;
                                }
                            }
                        }
                    }

                    #endregion Locations

                    // don't use offerExpires time, and they dont have a transaction timestamp, so we'll just use current server time.
                    if (call.CreateDate == DateTime.MinValue)
                        call.CreateDate = DateTime.Now.AddSeconds(-DateTime.Now.Second); // dont use accept.service.OfferExpiresLocal;

                    if (dispatch.Eta != null)
                        call.ArrivalETA = call.CreateDate.AddMinutes(dispatch.Eta.Value);


                    if (!string.IsNullOrWhiteSpace(job.Notes))
                        addNote(job.Notes + "\n", true);

                    if (job.FlatbedRequested)
                        addNote("** FLATBED REQUESTED **");

                    if (job.NeedsFlatbed)
                        addNote("** NEEDS FLATBED **");

                    if (job.NumberOfPassengers != null)
                        addNote("Number of Passengers:" + accepted.NumberOfPassengers.Value);

                    if (job.CallType == "RAP")
                        addNote("** RAP CALL **", true);

                    call.PurchaseOrderNumber = accepted.WorkOrderNumber ?? accepted.AppointmentNumber;

                    if (job.IsScheduledAppointment && job.StartDateTime != null)
                    {
                        var local = Core.OffsetDateTime(call.Company, job.StartDateTime.Value.ToLocalTime());

                        addNote("** SCHEDULED JOB FOR: " + local.ToShortDateString() + " at " + local.ToShortTowbookTimeString() + " **", true);

                        call.ArrivalETA = job.StartDateTime.Value.ToLocalTime();
                    }

                    #region Reason

                    var reason = job.WorkTypeName;

                    call.ReasonId = await ReasonHelper.DetermineReasonId(call.Account.MasterAccountId, qi.CompanyId.Value,
                        reason);

                    if (call.ReasonId == 1635 && reason != null)
                        call.Notes = "Service Needed: " + reason + "\n" + call.Notes;

                    #endregion Reason

                    if (callRequest != null)
                    {
                        if (callRequest.OwnerUserId == null)
                            callRequest.OwnerUserId = 1;

                        call.CallRequestId = callRequest.CallRequestId;

                        if (call.OwnerUserId < 100)
                            call.OwnerUserId = callRequest.OwnerUserId.GetValueOrDefault(0);
                    }
                    else if (call.OwnerUserId < 100)
                        call.OwnerUserId = 1;

                    #region Notes

                    void addNote(string line, bool top = false)
                    {
                        if (string.IsNullOrWhiteSpace(line))
                            return;

                        if (call.Notes == null || !call.Notes.Contains(line))
                        {
                            if (call.Notes == null)
                                call.Notes = line + "\n";
                            else
                            {
                                if (top)
                                    call.Notes = line + call.Notes.Trim('\n') + "\n";
                                else
                                    call.Notes += "\n" + line.Trim('\n');
                            }
                        }
                    }
                    #endregion Notes

                    call.SetAttribute(AttributeValue.BUILTIN_MOTORCLUB_COVERAGELIMIT, job.MemberMembershipLevel);
                    //call.SetAttribute(AttributeValue.BUILTIN_AAA_TROUBLE_CODE_PROBLEM, tcode);

                    await ApplyRoundRobinDispatcherLogicAsync(qi, call, dispatch.DispatchId, contractor.ContractorId);

                    if (call.Assets == null || call.Assets.Count == 0)
                    {
                        call.Assets = new System.Collections.ObjectModel.Collection<EntryAsset>();
                        call.Assets.Add(asset);
                    }

                    bool newContact = false;

                    var customerName = Core.FormatName(job.MemberFirstName + " " + job.MemberLastName);

                    var phone = Core.FormatPhone(job.MemberPhoneNumber);

                    var c = call.Contacts.FirstOrDefault(o =>
                        Core.FormatPhone(o.Phone) == phone);

                    if (c == null)
                    {
                        c = new EntryContact();
                        call.Contacts.Add(c);
                        newContact = true;
                    }

                    c.Name = customerName;
                    c.Phone = phone;

                    await call.Save();

                    if (newContact)
                        await CheckForRoadsideFeatureAndAutoInvite(call, c);

                    await AutoDispatch.AutoDispatchServiceBusHandler.Send(call);

                    return call;
                },
                alreadyLocked: async delegate ()
                {
                    logger.LogEvent("{0}/CR{1}: Lock already exists for {2}:{3}... pausing 250ms",
                        qi.CompanyId, LogLevel.Warn,
                        sourceMessage.Message.MessageId,
                        (callRequest != null ? callRequest.CallRequestId.ToString() : "NULL"),
                        qi.AccountId.Value);

                    await Task.Delay(250);

                    return true;
                });

            if (fe == null)
            {
                logger.LogEvent("ACG/{0}: Creation of call failed; {1}",
                    qi.CompanyId, LogLevel.Error, sourceMessage?.Message?.MessageId, qi.ToJson());

                if (callRequest != null)
                    await callRequest.UpdateStatus(CallRequestStatus.AcceptFailed);
            }
            else
            {
                if (callRequest != null)
                {
                    await callRequest.UpdateStatus(CallRequestStatus.Accepted, po: fe.PurchaseOrderNumber);

                    qi.CallRequestId = callRequest.CallRequestId;
                    DigitalDispatchService.LogAction(qi);

                    callRequest.DispatchEntryId = fe.Id;
                    await callRequest.Save();

                    await sourceMessage.CompleteAsync();
                    DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);

                    logger.Info(MasterAccountTypes.AaaAcg,
                        "CallCreated",
                        "Created new towbook call for AAA ACG",
                        companyId: qi.CompanyId,
                        data: new
                        {
                            waypoints = fe.Waypoints.Select(o => new { o.Address, o.Latitude, o.Longitude })
                        },
                        dispatchId: dispatch.DispatchId.ToString(),
                        callId: fe.Id,
                        callRequestId: callRequest.CallRequestId,
                        queueItemId: qi.QueueItemId,
                        poNumber: fe.PurchaseOrderNumber,
                        callNumber: fe.CallNumber);

                    if (sendToDriver)
                    {
                        await AutoDispatchServiceBusHandler.SendDispatchToDriver(fe, fe.Driver);
                    }
                }
            }
            return fe;
        }

        private static async Task<Driver> GetDriverByServiceResourceId(AaaDispatch dispatch, string serviceResourceId)
        {
            var contractor = AaaContractor.GetById(dispatch.AaaContractorId);

            var match = DriverKeyValue.Get(contractor.CompanyId, Provider.Towbook.ProviderId, "AcgServiceResourceId")
                .FirstOrDefault(o => o.Value == serviceResourceId);

            var aaaDriver = AaaDriver.GetByAaaContractorId(contractor.AaaContractorId).FirstOrDefault(o => o.AaaId == serviceResourceId);

            if (aaaDriver != null)
                return await Driver.GetByIdAsync(aaaDriver.DriverId);

            if (match == null)
            {
                logger.Log(LogLevel.Error, "CR" + dispatch.DispatchId + ": Couldn't find AcgServiceResourceId for company " + contractor.CompanyId + " for " + serviceResourceId);
                return null;
            }

            return await Driver.GetByIdAsync(match.DriverId);
        }

        public sealed class AcgCallRequestContainer
        {
            public CallRequest CallRequest { get; set; }
            public AaaDispatch Job { get; set; }
            public int Eta { get; set; }
            public int DriverId { get; set; }
            public AaaContractor Contractor { get; set; }
        }

        private static AcgCallRequestContainer AcgGetCallRequestAndDispatch(dynamic jsonObj)
        {
            CallRequest callRequest = null;
            AaaDispatch dispatch = null;
            AaaContractor contractor = null;

            try
            {
                callRequest = CallRequest.GetById(Convert.ToInt32(jsonObj.Id));
                if (callRequest == null)
                {
                    logger.Log(LogLevel.Error, "CR" + jsonObj.DispatchId + ": Couldn't find CallRequestId.");
                    logger.Log(LogLevel.Error, "CR" + jsonObj.DispatchId + ": " + JsonExtensions.ToJson(jsonObj));
                }
                else
                {
                    dispatch = AaaDispatch.GetByCallRequestId(callRequest.CallRequestId);
                    contractor = AaaContractor.GetById(dispatch.AaaContractorId);
                }
            }
            catch (Exception)
            {
                return new AcgCallRequestContainer
                {
                    CallRequest = callRequest,
                    Job = dispatch,
                    Eta = jsonObj.Eta
                };
            }

            return new AcgCallRequestContainer
            {
                CallRequest = callRequest,
                Job = dispatch,
                Eta = jsonObj.Eta != null ? jsonObj.Eta : 0,
                Contractor = contractor
            };
        }

        private static string AcgEnvironmentIdToString(int id)
        {
            if (id == 1)
            {
                return "DEV";
            }
            else if (id == 2)
            {
                return "TEST";
            }
            else if (id == 3)
            {
                return "PROD";
            }
            return "DEV";
        }
        private static AcgRestClient AcgGetClient(AaaContractor contractor, DigitalDispatchActionQueueItem qi, int callRequestId = 0, int? callId = 0, string dispatchId = null)
        {
            string env = AcgEnvironmentIdToString(contractor.EnvironmentId);
            return new AcgRestClient(env, "127.0.0.1", qi.CompanyId.GetValueOrDefault(), qi.QueueItemId, callRequestId, callId.GetValueOrDefault(), dispatchId, contractor);
        }

        public sealed class ScheduleContainer
        {
            public int DriverId { get; set; }
            public string Json { get; set; }
        }

        [Transaction]
        public static async Task HandleAaaAcgQueueOutgoingMessage(DigitalDispatchActionQueueItem qi, dynamic jsonObj, ProcessMessageEventArgs sourceMessage)
        {
            AcgRestClient client;

            switch (qi.Type)
            {
                case DigitalDispatchActionQueueItemType.OutgoingAcceptCall:
                    AceCallRequestContainer x = AceGetCallRequestAndDispatch(jsonObj);
                    async Task doAccept()
                    {
                        CallRequest callRequest = x.CallRequest;
                        AaaDispatch dispatch = x.Job;
                        var contractor = AaaContractor.GetById(dispatch.AaaContractorId);
                        client = AcgGetClient(contractor, qi, callRequest.CallRequestId, callRequest.DispatchEntryId, dispatch.DispatchId);

                        if (callRequest == null)
                        {
                            await sourceMessage.DeadLetterAsync();
                            return;
                        }

                        if (dispatch == null)
                        {
                            logger.Log(LogLevel.Error, "ACE/CR" + callRequest.CallRequestId + "/C" + callRequest.CompanyId + "/OutgoingAcceptCall: Couldn't find AceDispatch");

                            await sourceMessage.DeadLetterAsync();
                            return;
                        }

                        dispatch.Eta = x.Eta;
                        dispatch.Save();

                        try
                        {
                            logger.Info(MasterAccountTypes.AaaAcg,
                                "AcceptCall",
                                "Outgoing Call Accept",
                                contractor.ContractorId,
                                null,
                                dispatch.DispatchId,
                                x.CallRequest.CompanyId,
                                new
                                {
                                    json = JsonExtensions.ToJson(jsonObj)
                                },
                                callRequestId: x.CallRequest.CallRequestId,
                                queueItemId: qi.QueueItemId);

                            var job = JsonConvert.DeserializeObject<SalesforceData<ScheduledJobPayload>>(
                                dispatch.DispatchJson).Payload;

                            async Task<bool> postDispatchIsOk()
                            {
                                try
                                {
                                    await client.AssignCall(new CallAssignmentPayload()
                                    {
                                        ServiceAppointmentId = job.ServiceAppointmentId,
                                        WorkOrderId = job.WorkOrderId,
                                        ServiceResourceId = job.ServiceResourceId,
                                        Status = "dispatched",
                                        CallStatus = "Accept",
                                        ScheduledStart = DateTime.UtcNow,
                                        ScheduledEnd = DateTime.UtcNow.AddHours(1),
                                        EstimatedTravelTime = x.Eta.ToString() // eta in minutes, confirm this is ok?
                                    });

                                    // TODO: create call immediately.

                                    return true;
                                }
                                catch (Exception rfe)
                                {
                                    logger.Error(MasterAccountTypes.AaaAcg,
                                        "AcceptCall",
                                        "Outgoing Call Accept Failed",
                                        contractor.ContractorId, null, dispatch.DispatchId, x.CallRequest.CompanyId,
                                        new
                                        {
                                            json = rfe.ToJson()
                                        },
                                        callRequestId: x.CallRequest.CallRequestId,
                                        queueItemId: qi.QueueItemId);

                                    return false;
                                }
                            }

                            if (!await postDispatchIsOk())
                            {
                                await callRequest.UpdateStatus(CallRequestStatus.AcceptFailed);
                                if (sourceMessage != null)
                                    await sourceMessage.CompleteAsync();
                            }
                            else
                            {
                                await AcgDoCallCreateOrUpdate(qi, job, dispatch, x.CallRequest, contractor, sourceMessage);
                            }
                        }
                        catch (Exception ex)
                        {
                            await sourceMessage.DeadLetterAsync("Attempted to accept callRequestId " + callRequest.CallRequestId + ", but  error occurred",
                                ex.ToJson(true));

                            logger.Error(MasterAccountTypes.AaaAcg,
                                "AcceptCall",
                                "Sent accept failed.",
                                contractor.ContractorId,
                                null,
                                dispatch?.DispatchId,
                                qi?.CompanyId,
                                new
                                {
                                    errorMessage = ex.Message,
                                    exception = ex
                                },
                                queueItemId: qi.QueueItemId);

                            DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                        }
                    }
                    try
                    {
                        await doAccept();
                    }
                    catch (Exception y)
                    {
                        if (y.Message == "Network Error, retry recommended.")
                            break;

                        logger.Error(MasterAccountTypes.AaaAcg,
                            "Accept",
                            "Failed: " + y.Message,
                            null,
                            callRequestId: x.CallRequest?.CallRequestId ?? null,
                            companyId: qi.CompanyId.GetValueOrDefault(),
                            data: new
                            {
                                exception = y
                            },
                            queueItemId: qi.QueueItemId);

                        try
                        {
                            await sourceMessage.CompleteAsync();
                        }
                        catch { }

                        if (x.CallRequest != null)
                        {
                            await x.CallRequest.UpdateStatus(CallRequestStatus.AcceptFailed);
                        }
                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                    }
                    break;

                case DigitalDispatchActionQueueItemType.OutgoingCallCanceled:
                    async Task DoCancel()
                    {
                        CallRequest cr = CallRequest.GetById(Convert.ToInt32(jsonObj.CallRequestId ?? jsonObj.Id));

                        var chj = AaaDispatch.GetByCallRequestId(cr.CallRequestId);

                        if (chj != null && chj.IsClosed)
                        {
                            if (sourceMessage != null)
                                await sourceMessage.CompleteAsync();

                            DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                            return;
                        }

                        var cancelContractor = AaaContractor.GetById(chj.AaaContractorId);
                        client = AcgGetClient(cancelContractor, qi, cr.CallRequestId, cr.DispatchEntryId, chj.DispatchId);

                        var cancelReason = MasterAccountReason.GetById((int)jsonObj.ReasonId);

                        var job = JsonConvert.DeserializeObject<SalesforceData<ScheduledJobPayload>>(chj.DispatchJson).Payload;

                        var serviceResourceId = (await Core.GetRedisValueAsync("acg.reassigned.driver:" +
                            cr.DispatchEntryId)) ??
                            job.ServiceResourceId;

                        bool success = false;

                        if (cancelReason.Code == "BREAKDOWN_DECLINE")
                        {
                            var en = Entry.GetByIdNoCache(cr.DispatchEntryId.Value);

                            var ghr = await GeocodeHelper.Geocode(en.Waypoints.First(r => r.Title == "Pickup").Latitude + ", " +
                                en.Waypoints.First(r => r.Title == "Pickup").Longitude);

                            if (ghr.Country == "US")
                                ghr.Country = "United States";

                            var payload = new UpdateCallDetailsBreakdownAddressChangeAcceptOrDeclinePayload()
                            {
                                OperationType = "BreakdownAddressChangeDecline",
                                WorkOrderId = chj.DispatchId,
                                BreakdownLatitude = ghr.Latitude,
                                BreakdownLongitude = ghr.Longitude,
                                BreakdownState = ghr.State,
                                BreakdownCity = ghr.City,
                                BreakdownStreet = ghr.Address ?? ghr.Latitude + ", " + ghr.Longitude,
                                BreakdownPostalCode = ghr.Zip,
                                BreakdownCountry = ghr.Country,
                                BreakdownCounty = ghr.County,
                                DeclineReason = "Long Tow",
                            };

                            success = await client.UpdateCallDetails(
                                 payload);
                        }
                        else if (cancelReason.Code == "DESTINATION_DECLINE")
                        {
                            var en = Entry.GetByIdNoCache(cr.DispatchEntryId.Value);

                            if (!en.Waypoints.Any(o => o.Title == "Destination"))
                            {
                                en.Notes = "Digital Cancel was not sent to ACG: Cannot send Decline Tow Address - This job doesn't have a tow address.";

                                await en.Save(false, token: new AuthenticationToken() { UserId = AcgUserId });

                                DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);

                                if (sourceMessage != null)
                                    await sourceMessage.CompleteAsync();

                            }
                            else
                            {
                                var ghr = await GeocodeHelper.Geocode(en.Waypoints.First(r => r.Title == "Destination").Latitude + ", " +
                                    en.Waypoints.First(r => r.Title == "Destination").Longitude);

                                if (ghr.Country == "US")
                                    ghr.Country = "United States";

                                var payload = new UpdateCallDetailsTowAddressChangeAcceptOrDeclinePayload()
                                {
                                    OperationType = "TowAddressChangeDecline",
                                    WorkOrderId = chj.DispatchId,
                                    TowLatitude = ghr.Latitude,
                                    TowLongitude = ghr.Longitude,
                                    TowState = ghr.State,
                                    TowCity = ghr.City,
                                    TowStreet = ghr.Address ?? ghr.Latitude + ", " + ghr.Longitude,
                                    TowPostalCode = ghr.Zip,
                                    TowCountry = ghr.Country,
                                    TowCounty = ghr.County,
                                    TowDestinationName = ghr.Name ?? ghr.Address,
                                    DeclineReason = "Long Tow"
                                };

                                success = await client.UpdateCallDetails(
                                     payload);
                            }
                        }
                        else
                        {
                            var cdm = new CallStatusUpdatePayload()
                            {
                                WorkOrderId = job.WorkOrderId,
                                ServiceResourceId = serviceResourceId, // TODO: if user re-assigns driver, then need to use its value here instead of the original
                                ServiceAppointmentId = job.ServiceAppointmentId,
                                Status = StatusCodes.Complete,
                                ReasonForCancellation = jsonObj.Comments?.ToString(),
                            };

                            var reasonId = (int?)jsonObj.ReasonId;
                            if (reasonId != null)
                            {
                                var reason = MasterAccountReason.GetById(reasonId.Value);
                                if (reason != null)
                                {
                                    cdm.CancellationReason = reason.Code;
                                    if (reason.Code == "Other")
                                    {
                                        cdm.CancellationReason = reason.Code;
                                            
                                        if (jsonObj.OtherReason != null)
                                            cdm.CancellationReasonOther = jsonObj.OtherReason.ToString();
                                    }
                                }
                            }

                            var en = Entry.GetByIdNoCache(cr.DispatchEntryId.Value);

                            if (en.Status.Id < Dispatch.Status.EnRoute.Id)
                                cdm.ResolutionCode = "X001";
                            else
                                cdm.ResolutionCode = "X002";

                            logger.Info(MasterAccountTypes.AaaAcg,
                                "OutgoingCancel",
                                "OutboundMessage",
                                cancelContractor.ContractorId, null, chj.DispatchId, qi.CompanyId,
                                data: new { json = cdm.ToJson() },
                                callRequestId: chj.CallRequestId,
                                queueItemId: qi.QueueItemId);

                            success = await client.StatusUpdate(cdm);
                        }

                        logger.Info(MasterAccountTypes.AaaAcg, "OutgoingCancel", "OutboundMessageResponse",
                            cancelContractor.ContractorId, null, chj.DispatchId.ToString(), qi.CompanyId, "",
                            callRequestId: chj.CallRequestId,
                            queueItemId: qi.QueueItemId);

                        var e = Entry.GetByIdNoCache(cr.DispatchEntryId.Value);
                        if (e != null)
                        {
                            if (success)
                            {
                                await e.Cancel("Digitally Cancelled: " + cancelReason.Name,
                                    new AuthenticationToken() { UserId = qi.OwnerUserId.Value }, "127.0.0.1");
                            }
                            else
                            {
                                e.Notes = "Digital Cancel Failed: Please contact ACG to cancel the call.\n" +
                                    e.Notes;

                                await e.Save(false, token: new AuthenticationToken() { UserId = AcgUserId });

                                await e.Cancel("Digitally Cancel Failed, contact ACG so they know it's cancelled.",
                                    new AuthenticationToken() { UserId = AcgUserId }, "127.0.0.1");
                            }
                        }

                        chj.IsClosed = true;
                        chj.Save();

                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);

                        if (sourceMessage != null)
                            await sourceMessage.CompleteAsync();
                    }

                    await DoCancel();

                    break;

                case DigitalDispatchActionQueueItemType.OutgoingSharePhoto:
                    void doPhotoUpload()
                    {
                        logger.Info("SharePhoto");

                        SharePhotoModel pm = JsonConvert.DeserializeObject<SharePhotoModel>(qi.JsonObject);

                        var phj = AaaDispatch.GetByCallRequestId(pm.CallRequestId);
                        var contractor = AaaContractor.GetById(phj.AaaContractorId);
                        client = AcgGetClient(contractor, qi, pm.CallRequestId, pm.DispatchEntryId, phj.DispatchId);

                        Console.WriteLine(JsonExtensions.ToJson(pm, true));

                        var thePhoto = Dispatch.Photo.GetById(pm.PhotoId);


                    }
                    doPhotoUpload();
                    break;

                case DigitalDispatchActionQueueItemType.OutgoingRejectCall:
                    AceCallRequestContainer x2 = AceGetCallRequestAndDispatch(jsonObj);
                    async Task doReject()
                    {
                        CallRequest callRequestRC = x2.CallRequest;

                        client = AcgGetClient(AaaContractor.GetById(x2.Contractor.AaaContractorId), qi, callRequestRC.CallRequestId, callRequestRC.DispatchEntryId, x2.Job?.DispatchId);
                        var contractor = x2.Contractor;
                        AaaDispatch dispatchRC = x2.Job;

                        if (callRequestRC == null)
                        {
                            await sourceMessage.DeadLetterAsync();
                            return;
                        }

                        MasterAccountReason rejectReason = MasterAccountReason.GetById(Convert.ToInt32(jsonObj.MasterAccountReasonId));

                        if (rejectReason == null)
                        {
                            await sourceMessage.DeadLetterAsync("Attempted to reject callRequestId " + callRequestRC.CallRequestId +
                                ", but no reason is present in database for it",
                                callRequestRC.ToJson(true));
                            await callRequestRC.UpdateStatus(CallRequestStatus.RejectFailed);
                            return;
                        }

                        var job = JsonConvert.DeserializeObject<SalesforceData<ScheduledJobPayload>>(dispatchRC.DispatchJson).Payload;

                        await client.AssignCall(new CallAssignmentPayload()
                        {
                            Status = "Spotted",
                            CallStatus = StatusCodes.Decline,
                            ServiceAppointmentId = job.ServiceAppointmentId,
                            WorkOrderId = job.WorkOrderId,
                            ServiceResourceId = job.ServiceResourceId,
                            DeclineReason = rejectReason?.Code ?? "Not allowed"
                        });

                        //TODO: Parse response to see if everything went ok (when we get an actual response :))

                        await callRequestRC.UpdateStatus(CallRequestStatus.Rejected);

                        await callRequestRC.Save();

                        qi.CallRequestId = callRequestRC.CallRequestId;
                        DigitalDispatchService.LogAction(qi);

                        await sourceMessage.CompleteAsync();
                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                    }
                    try
                    {
                        await doReject();
                    }
                    catch (Exception y)
                    {
                        if (y.Message == "Network Error, retry recommended.")
                            break;

                        logger.Error(MasterAccountTypes.AaaAcg,
                            "Reject",
                            "Failed: " + y.Message,
                            null,
                            callRequestId: x2.CallRequest?.CallRequestId ?? null,
                            companyId: qi.CompanyId.GetValueOrDefault(),
                            data: new
                            {
                                exception = y
                            },
                            queueItemId: qi.QueueItemId);

                        try
                        {
                            await sourceMessage.CompleteAsync();
                        }
                        catch { }

                        if (x2.CallRequest != null)
                        {
                            await x2.CallRequest.UpdateStatus(CallRequestStatus.RejectFailed);
                        }
                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                    }
                    break;

                case DigitalDispatchActionQueueItemType.OutgoingReassignJob:
                case DigitalDispatchActionQueueItemType.OutgoingStatusUpdate:
                    string newStatusName = null;
                    async Task doStatusUpdate()
                    {
                        CallRequest callRequestSU = CallRequest.GetById(Convert.ToInt32(jsonObj.CallRequestId));
                        var call = Entry.GetByIdNoCache(callRequestSU.DispatchEntryId.GetValueOrDefault());

                        if (callRequestSU == null)
                        {
                            logger.Log(LogLevel.Error, "CR" + jsonObj.CallRequestId + ": Couldn't find CallRequestId.");
                            logger.Log(LogLevel.Error, "CR" + JsonExtensions.ToJson(jsonObj));

                            await sourceMessage.DeadLetterAsync();
                            return;
                        }
                        var dispatchSU = AaaDispatch.GetByCallRequestId(Convert.ToInt32(callRequestSU.CallRequestId));
                        var contractor = AaaContractor.GetById(dispatchSU.AaaContractorId);

                        var job = JsonConvert.DeserializeObject<SalesforceData<ScheduledJobPayload>>(dispatchSU.DispatchJson).Payload;


                        if (contractor == null)
                        {
                            logger.Error(MasterAccountTypes.AaaAcg,
                                 "StatusUpdate",
                                 "ContractorID isn't registered as an active connection.",
                                 contractor.ContractorId,
                                 null,
                                 dispatchSU.DispatchId,
                                 qi.CompanyId,
                                 new
                                 {
                                     request = jsonObj
                                 },
                                 callId: callRequestSU.DispatchEntryId,
                                 callRequestId: callRequestSU.CallRequestId,
                                 queueItemId: qi.QueueItemId);

                            await sourceMessage.CompleteAsync();
                            DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                            return;
                        }

                        client = AcgGetClient(contractor, qi, callRequestSU.CallRequestId, callRequestSU.DispatchEntryId, dispatchSU.DispatchId);

                        if (Core.GetRedisValue(call.Id + ":ace_compl") != null)
                        {
                            logger.Info(MasterAccountTypes.AaaAcg,
                                 "StatusUpdate",
                                 "Call was already marked Completed, not sending.",
                                 contractor.ContractorId,
                                 null,
                                 dispatchSU.DispatchId,
                                 qi.CompanyId,
                                 new
                                 {
                                     request = jsonObj
                                 },
                                 callId: callRequestSU.DispatchEntryId,
                                 callRequestId: callRequestSU.CallRequestId,
                                 queueItemId: qi.QueueItemId);

                            if (sourceMessage != null)
                                await sourceMessage.CompleteAsync();
                            DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);

                            return;
                        }

                        if (qi.Type == DigitalDispatchActionQueueItemType.OutgoingReassignJob)
                        {
                            var reassign = JsonConvert.DeserializeObject<ReassignPayload>(qi.JsonObject);
                            if (reassign.NewDriverId > 0)
                            {
                                var newServiceResourceId = DriverKeyValue.GetByDriver(callRequestSU.CompanyId, reassign.NewDriverId, Provider.Towbook.ProviderId, "AcgServiceResourceId").FirstOrDefault()?.Value;

                                var aaaDriver = AaaDriver.GetByDriverId(contractor.AaaContractorId, reassign.NewDriverId);
                                if (aaaDriver?.AaaId != null)
                                    newServiceResourceId = aaaDriver.AaaId;

                                decimal userLat = 0;
                                decimal userLng = 0;

                                var newDriver = await Driver.GetByIdAsync(reassign.NewDriverId);
                                if (newDriver.UserId > 0)
                                {
                                    var userLocation = UserLocationHistoryItem.GetCurrentByUserId(newDriver.UserId);
                                    if (userLocation != null)
                                    {
                                        userLat = userLocation.Latitude;
                                        userLng = userLocation.Longitude;
                                    }
                                }
                                if (userLat == 0)
                                {
                                    var cc = await Company.Company.GetByIdAsync(callRequestSU.CompanyId);

                                    userLat = cc.Latitude;
                                    userLng = cc.Longitude;
                                }

                                if (newServiceResourceId != null)
                                {
                                    var directions = await client.GetDirections(qi.OwnerUserId.Value, MyClientVersionId,
                                        userLat + ", " + userLng,
                                        job.Latitude + ", " + job.Longitude);

                                    var directionsDest = await client.GetDirections(qi.OwnerUserId.Value, MyClientVersionId,
                                        job.Latitude + ", " + job.Longitude,
                                        job.TowLatitude + ", " + job.TowLongitude);

                                    DateTime incStart = DateTime.UtcNow;
                                    DateTime incEnd = directions != null ? incStart.AddMinutes((double)directions.TotalDurationMinutes + 10) : incStart.AddMinutes(30);

                                    DateTime? towStart = null;
                                    DateTime? towEnd = null;

                                    if (directionsDest != null)
                                    {
                                        towStart = incEnd;
                                        towEnd = incEnd.AddMinutes((double)directionsDest.TotalDurationMinutes);
                                    }

                                    // it's better to keep the original date/times sent from ACG so that we don't screw up the schedules. 
                                    // TODO: as of this change on 5/7/25, this is being applied to ACG code but not AAA National, until we see that it works fully 
                                    // on ACG we shouldn't copy this to the NationalFSL code.
                                    if (job.StartDateTime != null)
                                        incStart = job.StartDateTime.Value.ToUniversalTime();

                                    if (job.TowStartDateTime != null)
                                        towStart = job.TowStartDateTime.Value.ToUniversalTime();

                                    if (job.TowEndDateTime != null)
                                        towEnd = job.TowEndDateTime.Value.ToUniversalTime();

                                    await client.AssignCall(new CallAssignmentPayload()
                                    {
                                        WorkOrderId = job.WorkOrderId,
                                        ServiceResourceId = newServiceResourceId,
                                        ServiceAppointmentId = job.ServiceAppointmentId,
                                        Status = StatusCodes.Dispatched,
                                        CallStatus = StatusCodes.Reassign,
                                        ScheduledStart = incStart,
                                        ScheduledEnd = incEnd,
                                        TowScheduledStart = towStart,
                                        TowScheduledEnd = towEnd,
                                        ApproximateTravelDistanceTo = directions?.TotalDistanceDecimal.ToString() ?? "9.9",
                                        EstimatedTravelTime = directions?.TotalDurationMinutes.ToString() ?? "60"
                                    });

                                    await Core.SetRedisValueAsync("acg.reassigned.driver:" + reassign.CallId, newServiceResourceId, TimeSpan.FromDays(1));
                                }
                            }

                            if (sourceMessage != null)
                                await sourceMessage.CompleteAsync();

                            DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                            return;
                        }

                        double? lat = (double?)jsonObj.Latitude;
                        double? lng = (double?)jsonObj.Longitude;
                        int newStatusId = (int)jsonObj.NewStatusId;

                        var serviceResourceId = (await Core.GetRedisValueAsync("acg.reassigned.driver:" +
                            callRequestSU.DispatchEntryId)) ??
                            job.ServiceResourceId;

                        string statusUpdate;
                        if (newStatusId == Status.Dispatched.Id)
                        {
                            statusUpdate = StatusCodes.Dispatched;
                        }
                        else if (newStatusId == Status.EnRoute.Id)
                        {
                            statusUpdate = StatusCodes.Enroute;
                        }
                        else if (newStatusId == Status.AtSite.Id)
                        {
                            statusUpdate = StatusCodes.OnScene;
                        }
                        else if (newStatusId == Status.BeingTowed.Id)
                        {
                            statusUpdate = StatusCodes.TowLoaded;
                        }
                        else if (newStatusId == Status.DestinationArrival.Id)
                        {
                            statusUpdate = StatusCodes.Destination;
                        }
                        else if (newStatusId == Status.Completed.Id)
                        {
                            statusUpdate = StatusCodes.Complete;
                        }
                        else
                        {
                            if (sourceMessage != null)
                                await sourceMessage.CompleteAsync();
                            return;
                        }
                        newStatusName = statusUpdate;
                        string super7value = null;

                        if (newStatusId == Status.EnRoute.Id)
                            super7value = call.GetAttribute(AttributeValue.BUILTIN_ACG_SUPER7) ?? "";

                        var driver = await Driver.GetByIdAsync((int)jsonObj.DriverId);

                        if (Core.GetRedisValue(call.Id + ":ace_compl") == "1" || dispatchSU.IsClosed)
                        {
                            logger.Log(LogLevel.Warn, "CR " + jsonObj.CallRequestId + ": " +
                                "Already completed; not sending any future status updates. " +
                                qi.JsonObject);

                            statusUpdate = "";
                        }

                        if (statusUpdate != "")
                        {
                            if (statusUpdate == StatusCodes.Complete)
                            {
                                var rc = "";

                                if (jsonObj.CompletionReasonId != null)
                                {
                                    int completionReason = Convert.ToInt32(jsonObj.CompletionReasonId);
                                    if (completionReason != 0)
                                        rc = MasterAccountReason.GetById(completionReason).Code;
                                }

                                // verify that On Site status was sent: 

                                // calls come over as dispatched, no need to 'fake' it.
                                //await SendFakeStatusIfNotAlreadySent(":ace_su_dispatched", StatusCodes.Dispatched, "COMPLETE");
                                await SendFakeStatusIfNotAlreadySent(":ace_su_enroute", StatusCodes.Enroute, "COMPLETE");
                                await SendFakeStatusIfNotAlreadySent(":ace_su_onsite", StatusCodes.OnScene, "COMPLETE");

                                var payload = new CallStatusUpdatePayload()
                                {
                                    WorkOrderId = job.WorkOrderId,
                                    ServiceAppointmentId = job.ServiceAppointmentId,
                                    Status = statusUpdate,
                                    ServiceResourceId = serviceResourceId,
                                    ResolutionCode = rc
                                };

                                if (newStatusId == Status.Completed.Id)
                                {
                                    if (lat.GetValueOrDefault() != 0 && lng.GetValueOrDefault() != 0)
                                    {
                                        payload.ClearLatitude = (decimal?)lat;
                                        payload.ClearLongitude= (decimal?)lng;

                                        var pickup = call.Waypoints.FirstOrDefault(o => o.Title == "Pickup");

                                        // calc distance between breakdown lat/long and onlocation lat/long
                                        var result = await AutoDispatchServiceBusHandler.GetMatrixAsync(
                                            payload.ClearLatitude.ToString() + "," + payload.ClearLongitude.ToString(),
                                            pickup.Latitude.ToString() + "," + pickup.Longitude.ToString());

                                        if (result != null)
                                            payload.ClearBreakdownDistance = Math.Round(result.Miles,1).ToString();
                                    }

                                    if (rc == "R001")
                                    {
                                        payload.NsrReason = "No Member";
                                    }
                                    else if (rc == "R006")
                                    {
                                        payload.NsrReason = "NSR Issue already resolved";
                                    }
                                    else if (rc == "R199")
                                    {
                                        payload.NsrReason = "No Member and No Vehicle";
                                    }
                                    
                                    payload.ArrivalContactResult = call.GetAttribute(AttributeValue.BUILTIN_ACG_ARRIVAL_CONTACT_RESULT);
                                }
                                
                                var success = await client.StatusUpdate(payload);

                                if (statusUpdate == StatusCodes.Complete)
                                {
                                    var list = new List<Integrations.MotorClubs.Aaa.Ace.AaaAceRestClient.DispatchCompleteModel.InvoiceItemModel>();

                                    var codes = new string[] { "ET", "HW" };

                                    // no longer allowed: "HD", "HT", "P9", "PS", "SE", "WS", "DS", "DI", "DR", "DW", "W1", "W3", "SS", "ST", "AH", "CF", "II", "FD", "FB", "FC", "RK", "MB", "OA", "RV", "SR", "SA", "SC" 

                                    foreach (var line in call.InvoiceItems)
                                    {
                                        string bc = null;

                                        foreach (var code in codes)
                                        {
                                            if (line.Name.StartsWith(code + " -"))
                                            {
                                                bc = code;
                                                break;
                                            }
                                        }

                                        if (bc != null)
                                            list.Add(new Integrations.MotorClubs.Aaa.Ace.AaaAceRestClient.DispatchCompleteModel.InvoiceItemModel()
                                            {
                                                Code = bc,
                                                Price = line.Price,
                                                Quantity = line.Quantity
                                            });
                                    }

                                    foreach (var li in list)
                                    {
                                        await client.UpdateWorkOrderLineItem(
                                            new WorkOrderLineItemPayload(job.WorkOrderId, li.Code, li.Quantity, (li.Code == "HW" ? li.Price : (decimal?)null))
                                         );
                                    }


                                }

                                if (qi.ScheduledDate != null)
                                {
                                    call.SetAttribute(Dispatch.AttributeValue.BUILTIN_DISPATCH_COMPLETION_ACKNOWLEDGEMENT_JSON,
                                        new AcknowledgeJson()
                                        {
                                            Confirmed = true,
                                            Timestamp = DateTime.UtcNow,
                                            UserId = qi.OwnerUserId != null ? qi.OwnerUserId.Value : AcgUserId
                                        }.ToJson());
                                }

                                dispatchSU.IsClosed = true;
                                dispatchSU.Save();

                                await call.Save(false, new AuthenticationToken()
                                {
                                    UserId = qi.OwnerUserId != null ? qi.OwnerUserId.Value : AcgUserId,
                                    ClientVersionId = MyClientVersionId
                                });

                                Core.SetRedisValue(call.Id + ":ace_compl", "1", TimeSpan.FromHours(48));
                                Core.DeleteRedisKey(call.Id + ":ace_su_onsite");
                            }
                            else
                            {
                                // Send a dispatched status if it was never sent. 

                                if (Core.GetRedisValue(call.Id + ":ace_su_dispatched") == null &&
                                    newStatusId > Status.Dispatched.Id)
                                {
                                    //await SendFakeStatusIfNotAlreadySent(":ace_su_dispatched", AaaAcgRestClient.StatusCodes.Dispatched, statusUpdate);
                                }

                                if (Core.GetRedisValue(call.Id + ":ace_su_enroute") == null &&
                                    newStatusId > Status.EnRoute.Id)
                                {
                                    await SendFakeStatusIfNotAlreadySent(":ace_su_enroute", StatusCodes.Enroute, statusUpdate);
                                }

                                if (Core.GetRedisValue(call.Id + ":ace_su_onsite") == null &&
                                    newStatusId > Status.AtSite.Id)
                                {
                                    await SendFakeStatusIfNotAlreadySent(":ace_su_onsite", StatusCodes.OnScene, statusUpdate);
                                }

                                if (statusUpdate == StatusCodes.TowLoaded && job.TowAppointmentId != null)
                                {
                                    await client.StatusUpdate(new CallStatusUpdatePayload()
                                    {
                                        WorkOrderId = job.WorkOrderId,
                                        ServiceResourceId = serviceResourceId,
                                        ServiceAppointmentId = job.TowAppointmentId,
                                        Status = StatusCodes.Towing
                                    });
                                }

                                if (!(statusUpdate == StatusCodes.Destination && job.TowAppointmentId == null))
                                {
                                    var payload = new CallStatusUpdatePayload()
                                    {
                                        WorkOrderId = job.WorkOrderId,
                                        ServiceAppointmentId = (statusUpdate == StatusCodes.Destination ?
                                            job.TowAppointmentId : job.ServiceAppointmentId),
                                        Status = statusUpdate,
                                        ServiceResourceId = serviceResourceId,
                                        Super7 = super7value
                                    };


                                    if (newStatusId == Status.AtSite.Id)
                                    {
                                        if (lat.GetValueOrDefault() != 0 && lng.GetValueOrDefault() != 0)
                                        {
                                            payload.OnLocationLatitude = (decimal?)lat;
                                            payload.OnLocationLongitude = (decimal?)lng;

                                            var pickup = call.Waypoints.FirstOrDefault(o => o.Title == "Pickup");

                                            // calc distance between breakdown lat/long and onlocation lat/long
                                            var result = await AutoDispatchServiceBusHandler.GetMatrixAsync(
                                                payload.OnLocationLatitude.ToString() + "," + payload.OnLocationLongitude.ToString(),
                                                pickup.Latitude.ToString() + "," + pickup.Longitude.ToString());

                                            if (result != null)
                                                payload.OnLocationBreakdownDistance = Math.Round(result.Miles, 1).ToString();
                                        }
                                    }

                                    var success = await client.StatusUpdate(payload);

                                    if (success)
                                    {
                                        if (statusUpdate == StatusCodes.OnScene)
                                            Core.SetRedisValue(call.Id + ":ace_su_onsite", "1", TimeSpan.FromHours(48));
                                        else if (statusUpdate == StatusCodes.Enroute)
                                            Core.SetRedisValue(call.Id + ":ace_su_enroute", "1", TimeSpan.FromHours(48));
                                        else if (statusUpdate == StatusCodes.Dispatched)
                                            Core.SetRedisValue(call.Id + ":ace_su_dispatched", "1", TimeSpan.FromHours(48));
                                    }
                                }
                            }
                        }
                        if (sourceMessage != null)
                            await sourceMessage.CompleteAsync();

                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);


                        async Task SendFakeStatusIfNotAlreadySent(string key, string status, string desiredStatus)
                        {
                            if (status == StatusCodes.Dispatched)
                                return;

                            if (Core.GetRedisValue(call.Id + key) == null)
                            {
                                await client.StatusUpdate(new CallStatusUpdatePayload
                                {
                                    WorkOrderId = job.WorkOrderId,
                                    ServiceResourceId = serviceResourceId,
                                    ServiceAppointmentId = job.ServiceAppointmentId,
                                    Status = status
                                });

                                Core.SetRedisValue(call.Id + key, "2", TimeSpan.FromHours(24));

                                logger.Warn(MasterAccountTypes.AaaAcg, "StatusUpdate",
                                    "Sent fake " + status + " status update so that " + desiredStatus + " won't fail.",
                                     contractor.ContractorId,
                                     dispatchId: dispatchSU.DispatchId,
                                     companyId: callRequestSU.CompanyId,
                                     callRequestId: callRequestSU.CallRequestId,
                                     queueItemId: qi.QueueItemId);

                                await Task.Delay(5000);
                            }
                        }
                    }

                    try
                    {
                        await doStatusUpdate();
                    }
                    catch (Exception y)
                    {
                        if (y.Message == "Network Error, retry recommended.")
                            break;

                        try
                        {
                            await sourceMessage.CompleteAsync();
                        }
                        catch { }


                        int errorCallRequestId = 0;
                        int errorCallId = 0;
                        try
                        {
                            var callRequestSU = CallRequest.GetById(Convert.ToInt32(jsonObj.CallRequestId));

                            if (callRequestSU != null)
                            {
                                var call = Entry.GetByIdNoCache(callRequestSU.DispatchEntryId.GetValueOrDefault());

                                if (call != null)
                                {
                                    errorCallRequestId = callRequestSU.CallRequestId;
                                    errorCallId = call.Id;

                                    var en = new EntryNote() { DispatchEntryId = call.Id, OwnerUserId = 1 };
                                    en.Content = "Failed to share " + newStatusName + " Status Update with AAA: " + y.Message;
                                    en.CreateDate = DateTime.Now;
                                    en.OwnerUserId = 1;
                                    await en.SaveAsync();
                                }
                            }
                        }
                        catch
                        {
                            // silently ignore note failure.
                        }

                        logger.Error(MasterAccountTypes.AaaAcg,
                            "StatusUpdate",
                            "Failed: " + y.Message,
                            null,
                            null,
                            null,
                            qi.CompanyId,
                            new
                            {
                                exception = y
                            },
                            callId: errorCallId,
                            callRequestId: errorCallRequestId,
                            queueItemId: qi.QueueItemId);

                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                    }
                    break;

                case DigitalDispatchActionQueueItemType.OutgoingRequestGoa:
                    var requestGoaMsg = JsonConvert.DeserializeObject<RequestGoaMessage>(qi.JsonObject);
                    async Task doGoa()
                    {

                        CallRequest callRequestSU = CallRequest.GetById(requestGoaMsg.CallRequestId);
                        if (callRequestSU == null)
                        {
                            logger.Log(LogLevel.Error, "CR" + jsonObj.CallRequestId + ": Couldn't find CallRequestId.");
                            logger.Log(LogLevel.Error, "CR" + JsonExtensions.ToJson(jsonObj));


                            if (sourceMessage != null)
                                await sourceMessage.CompleteAsync();
                            DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);

                            return;
                        }

                        var dispatchSU = AaaDispatch.GetByCallRequestId(requestGoaMsg.CallRequestId);
                        var contractor = AaaContractor.GetById(dispatchSU.AaaContractorId);
                        var job = JsonConvert.DeserializeObject<SalesforceData<ScheduledJobPayload>>(dispatchSU.DispatchJson).Payload;


                        if (Core.GetRedisValue(callRequestSU.DispatchEntryId + ":ace_compl") != null && !dispatchSU.IsClosed)
                        {
                            if (sourceMessage != null)
                                await sourceMessage.CompleteAsync();

                            DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);

                            logger.Info(MasterAccountTypes.AaaAcg,
                                "StatusUpdate",
                                "Call was already marked Completed, not sending.",
                                contractor?.ContractorId,
                                null,
                                dispatchSU.DispatchId,
                                qi.CompanyId,
                                new
                                {
                                    request = jsonObj
                                },
                                callRequestId: callRequestSU.CallRequestId,
                                callId: callRequestSU.DispatchEntryId,
                                queueItemId: qi.QueueItemId);

                            return;
                        }

                        client = AcgGetClient(contractor, qi, callRequestSU.CallRequestId, callRequestSU.DispatchEntryId, dispatchSU.DispatchId);

                        var entry = Entry.GetByIdNoCache(callRequestSU.DispatchEntryId.GetValueOrDefault());

                        var reason = MasterAccountReason.GetById(requestGoaMsg.ReasonId);

                        var goa = new CallStatusUpdatePayload()
                        {
                            Status = StatusCodes.Complete,
                            WorkOrderId = dispatchSU.DispatchId,
                            ResolutionCode = reason.Code,
                            ServiceResourceId = "" // TODO
                        };

                        if (reason.Code == "NSR Issue already resolved")
                            goa.ResolutionCode = "R006";
                        else
                            goa.ResolutionCode = "R001";

                        goa.NsrReason = reason.Code;
                        goa.ArrivalContactResult = entry.GetAttribute(AttributeValue.BUILTIN_ACG_ARRIVAL_CONTACT_RESULT);

                        decimal? userLat = null;
                        decimal? userLng = null;
                        
                        var newDriver = await Driver.GetByIdAsync(entry.DriverId);
                        if (newDriver != null && newDriver.UserId > 0)
                        {
                            var userLocation = UserLocationHistoryItem.GetCurrentByUserId(newDriver.UserId);
                            if (userLocation != null)
                            {
                                userLat = userLocation.Latitude;
                                userLng = userLocation.Longitude;
                            }
                        }
                            
                        goa.ClearLatitude = userLat;
                        goa.ClearLongitude = userLng;

                        if (goa.ClearLatitude != null && goa.ClearLongitude != null)
                        {
                            var pickup = entry.Waypoints.FirstOrDefault(o => o.Title == "Pickup");

                            // calc distance between breakdown lat/long and on location lat/long
                            var result = await AutoDispatchServiceBusHandler.GetMatrixAsync(
                                goa.ClearLatitude.ToString() + "," + goa.ClearLongitude.ToString(),
                                pickup.Latitude.ToString() + "," + pickup.Longitude.ToString());

                            if (result != null)
                                goa.ClearBreakdownDistance = Math.Round(result.Miles, 1).ToString();
                        }


                        goa.ServiceAppointmentId = (entry.Status.Id == Status.DestinationArrival.Id ?
                            job.TowAppointmentId : job.ServiceAppointmentId);

                        await client.StatusUpdate(goa);

                        entry.Status = Status.Completed;
                        await entry.Save(false, new AuthenticationToken()
                        {
                            UserId = AcgUserId,
                            ClientVersionId = MyClientVersionId
                        });
                        
                        logger.Log(LogLevel.Info, "ACG/{0}: Offer completed by because we successfully sent GOA/NSR. Type = {1}, CallRequestId = {2}", qi.QueueItemId, qi.Type, 
                            callRequestSU.CallRequestId);

                        if (sourceMessage != null)
                            await sourceMessage.CompleteAsync();

                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                    }

                    try
                    {
                        await doGoa();
                    }
                    catch (Exception y)
                    {
                        logger.Log(LogLevel.Warn,
                            "AAA " + requestGoaMsg.CallRequestId + ", QueueItemId: " + qi.QueueItemId + ": GOA Failed: " + qi.JsonObject);

                        if (y.Message == "Network Error, retry recommended.")
                            break;

                        try
                        {
                            var callRequestSU = CallRequest.GetById(requestGoaMsg.CallRequestId);
                            var call = Entry.GetByIdNoCache(callRequestSU.DispatchEntryId.GetValueOrDefault());
                            logger.Error(MasterAccountTypes.AaaAcg,
                                "GOA",
                                "Failed: " + y.Message,
                                null,
                                callRequestId: callRequestSU.CallRequestId,
                                companyId: qi.CompanyId.GetValueOrDefault(),
                                data: new
                                {
                                    exception = y
                                },
                                callId: callRequestSU.DispatchEntryId.GetValueOrDefault(),
                                queueItemId: qi.QueueItemId,
                                poNumber: call?.PurchaseOrderNumber);

                            var en = new EntryNote() { DispatchEntryId = call.Id, OwnerUserId = 1 };
                            en.Content = "Sending GOA Status to AAA failed with error: " + y.Message;
                            en.CreateDate = DateTime.Now;
                            en.OwnerUserId = 1;
                            await en.SaveAsync();
                        }
                        catch
                        {
                            // silently ignore note failure.
                        }
                        try
                        {
                            if (sourceMessage != null)
                                await sourceMessage.CompleteAsync();
                        }
                        catch
                        {

                        }
                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                    }
                    break;

                case DigitalDispatchActionQueueItemType.OutgoingShareDriverSchedule:
                    async Task doUpdateDriverSchedule()
                    {
                        var sc = JsonConvert.DeserializeObject<ScheduleContainer>(qi.JsonObject);
                        var schedule = JsonConvert.DeserializeObject<IEnumerable<AutoDispatch.AutoDispatchServiceBusHandler.ScheduleItem>>(sc.Json);

                        Console.WriteLine(schedule.ToJson(true));

                        schedule = AutoDispatch.AutoDispatchServiceBusHandler.ScheduleItem.LocalToUtc(schedule);

                        Console.WriteLine(schedule.ToJson(true));


                        var aac = AaaContractor.GetByCompanyId(qi.CompanyId.Value).FirstOrDefault(o => o.AccountId == qi.AccountId.Value);

                        client = AcgGetClient(aac, qi, 0, 0, null);
                        var ura = new UpdateResourceAvailabilityPayload();

                        var driverId = sc.DriverId;
                        var existing = DriverKeyValue.GetByDriver(qi.CompanyId.Value, driverId, Provider.Towbook.ProviderId, "AcgServiceResourceId").FirstOrDefault()?.Value;

                        var aaaDriver = AaaDriver.GetByDriverId(aac.AaaContractorId, driverId);


                        if (aaaDriver?.AaaId != null)
                            existing = aaaDriver.AaaId;

                        ura.ResourceId = existing;

                        var facilityId = AccountKeyValue.GetByAccount(aac.CompanyId, aac.AccountId, Provider.Towbook.ProviderId, "AcgFacilityId").FirstOrDefault()?.Value;

                        if (facilityId != null)
                            ura.PrimaryServiceTerritory = facilityId;
                        else
                        {
                            if (aac.ContractorId == "F400")
                                ura.PrimaryServiceTerritory = "0Hh8L0000004MwUSAU";
                            if (aac.ContractorId == "F100")
                                ura.PrimaryServiceTerritory = "0018L00000GFtzpQAD";
                            if (aac.ContractorId == "C6")
                                ura.PrimaryServiceTerritory = "0Hh3C0000006XqMSAU";
                        }

                        // where do I get this from 

                        ura.TimeSlots = new List<UpdateResourceAvailabilityPayload.TimeSlot>();

                        ura.TimeSlots.Add(new UpdateResourceAvailabilityPayload.TimeSlot() { StartTime = "", EndTime = "", Type = "delete", DayOfWeek = "Sunday" });
                        ura.TimeSlots.Add(new UpdateResourceAvailabilityPayload.TimeSlot() { StartTime = "", EndTime = "", Type = "delete", DayOfWeek = "Monday" });
                        ura.TimeSlots.Add(new UpdateResourceAvailabilityPayload.TimeSlot() { StartTime = "", EndTime = "", Type = "delete", DayOfWeek = "Tuesday" });
                        ura.TimeSlots.Add(new UpdateResourceAvailabilityPayload.TimeSlot() { StartTime = "", EndTime = "", Type = "delete", DayOfWeek = "Wednesday" });
                        ura.TimeSlots.Add(new UpdateResourceAvailabilityPayload.TimeSlot() { StartTime = "", EndTime = "", Type = "delete", DayOfWeek = "Thursday" });
                        ura.TimeSlots.Add(new UpdateResourceAvailabilityPayload.TimeSlot() { StartTime = "", EndTime = "", Type = "delete", DayOfWeek = "Friday" });
                        ura.TimeSlots.Add(new UpdateResourceAvailabilityPayload.TimeSlot() { StartTime = "", EndTime = "", Type = "delete", DayOfWeek = "Saturday" });

                        foreach (var xx in schedule)
                        {
                            ura.TimeSlots.Add(new UpdateResourceAvailabilityPayload.TimeSlot()
                            {
                                StartTime = xx.Start + "Z",
                                EndTime = xx.Stop + "Z",
                                Type = "Normal",
                                DayOfWeek = Thread.CurrentThread.CurrentCulture.TextInfo.ToTitleCase(xx.DayName.ToLower())
                            });
                        }

                        Console.WriteLine(ura.ToJson(true));

                        await client.UpdateResourceAvailability(ura);

                        if (sourceMessage != null)
                            await sourceMessage.CompleteAsync();

                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                    }
                    await doUpdateDriverSchedule();
                    break;

                case DigitalDispatchActionQueueItemType.OutgoingShareDriverAbsence:
                    async Task doUpdateDriverAbsence()
                    {
                        var sc = JsonConvert.DeserializeObject<ScheduleAbsence>(qi.JsonObject);

                        Console.WriteLine(sc.ToJson(true));

                        var aac = AaaContractor.GetByCompanyId(qi.CompanyId.Value)
                            .FirstOrDefault(o => o.AccountId == qi.AccountId.Value);

                        client = AcgGetClient(aac, qi, 0, 0, null);
                        var ura = new UpdateResourceAvailabilityAbsencePayload();

                        var driverId = sc.DriverId;
                        var existing = DriverKeyValue.GetByDriver(qi.CompanyId.Value, driverId, Provider.Towbook.ProviderId, "AcgServiceResourceId").FirstOrDefault()?.Value;

                        var aaaDriver = AaaDriver.GetByDriverId(aac.AaaContractorId, driverId);
                        if (aaaDriver?.AaaId != null)
                            existing = aaaDriver.AaaId;

                        ura.ResourceId = existing;
                        ura.AbsenceId = sc.ReferenceId;
                        ura.Start = sc.StartDate.ToUniversalTime();
                        ura.End = sc.EndDate.ToUniversalTime();
                        ura.Type = Enum.GetName(typeof(ScheduleAbsenceType), sc.Type);

                        if (sc.ReferenceId != null)
                        {
                            ura.Type = null;
                            ura.ResourceId = null;
                            await client.UpdateResourceAvailabilityAbsenceExisting(ura);
                        }
                        else
                        {
                            sc.ReferenceId = (await client.UpdateResourceAvailabilityAbsence(ura));
                            await sc.Save();
                        }

                        if (sourceMessage != null)
                            await sourceMessage.CompleteAsync();

                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                    }
                    await doUpdateDriverAbsence();
                    break;

                case DigitalDispatchActionQueueItemType.OutgoingUpdateVehicle:
                    async Task doUpdateVehicle()
                    {
                        var uv = JsonConvert.DeserializeObject<VehicleUpdatePayload>(qi.JsonObject);

                        Console.WriteLine(uv.ToJson(true));

                        var aac = AaaContractor.GetByCompanyId(qi.CompanyId.Value)
                            .FirstOrDefault(o => o.AccountId == qi.AccountId.Value);

                        var dispatch = AaaDispatch.GetByCallRequestId(uv.CallRequestId);

                        if (dispatch != null && dispatch.IsClosed)
                        {
                            if (sourceMessage != null)
                                await sourceMessage.CompleteAsync();

                            DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                            return;
                        }
                        client = AcgGetClient(aac, qi, uv.CallRequestId, uv.CallId, dispatch.DispatchId);

                        await client.UpdateCallDetails(
                            new UpdateCallDetailsVehicleChangePayload()
                            {
                                WorkOrderId = dispatch.DispatchId,
                                VehicleMake = uv.Make ?? "Unknown",
                                VehicleModel = uv.Model ?? "Unknown",
                                VehicleColor = uv.Color ?? "Unknown",
                                VehicleYear = uv.Year != null ? uv.Year.ToString() : "Unkwn"
                            });

                        if (sourceMessage != null)
                            await sourceMessage.CompleteAsync();

                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                    }
                    await doUpdateVehicle();
                    break;

                case DigitalDispatchActionQueueItemType.OutgoingUpdatePickup:

                    async Task doUpdatePickup()
                    {
                        var uv = JsonConvert.DeserializeObject<VehicleUpdatePickupAddressPayload>(qi.JsonObject);

                        Console.WriteLine(uv.ToJson(true));

                        var aac = AaaContractor.GetByCompanyId(qi.CompanyId.Value)
                            .FirstOrDefault(o => o.AccountId == qi.AccountId.Value);

                        var dispatch = AaaDispatch.GetByCallRequestId(uv.CallRequestId);
                        if (dispatch != null && dispatch.IsClosed)
                        {
                            if (sourceMessage != null)
                                await sourceMessage.CompleteAsync();

                            DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                            return;
                        }

                        client = AcgGetClient(aac, qi, uv.CallRequestId, uv.CallId, dispatch.DispatchId);

                        var ghr = await GeocodeHelper.Geocode(uv.Latitude + ", " + uv.Longitude);

                        if (ghr != null)
                        {
                            if (ghr.Country == "US")
                                ghr.Country = "United States";

                            var payload = new UpdateCallDetailsBreakdownAddressChangeAcceptOrDeclinePayload()
                            {
                                OperationType = "BreakdownAddressChangeAccept",
                                WorkOrderId = dispatch.DispatchId,
                                BreakdownLatitude = uv.Latitude,
                                BreakdownLongitude = uv.Longitude,
                                BreakdownState = ghr.State,
                                BreakdownCity = ghr.City,
                                BreakdownStreet = ghr.Address ?? uv.Latitude + ", " + uv.Longitude,
                                BreakdownPostalCode = ghr.Zip,
                                BreakdownCountry = ghr.Country,
                                BreakdownCounty = ghr.County
                            };

                            var hash = Core.MD5(Math.Round(uv.Latitude.GetValueOrDefault(), 6) + "," +
                                Math.Round(uv.Longitude.GetValueOrDefault(), 6));

                            if (Core.GetRedisValue("acg:" + uv.CallId + ":pu") != hash)
                            {
                                var job = JsonConvert.DeserializeObject<SalesforceData<ScheduledJobPayload>>(
                                    dispatch.DispatchJson).Payload;

                                if (job.Latitude != payload.BreakdownLatitude ||
                                    job.Longitude != payload.BreakdownLongitude)
                                {
                                    await client.UpdateCallDetails(
                                        payload);
                                }
                                Core.SetRedisValue("acg:" + uv.CallId + ":pu", hash, TimeSpan.FromDays(2));
                            }
                        }

                        if (sourceMessage != null)
                            await sourceMessage.CompleteAsync();

                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                    }
                    await doUpdatePickup();
                    break;

                case DigitalDispatchActionQueueItemType.OutgoingUpdateDestination:
                    async Task doUpdateDestination()
                    {
                        var uv = JsonConvert.DeserializeObject<VehicleUpdateDestinationAddressPayload>(qi.JsonObject);

                        Console.WriteLine(uv.ToJson(true));

                        var aac = AaaContractor.GetByCompanyId(qi.CompanyId.Value)
                            .FirstOrDefault(o => o.AccountId == qi.AccountId.Value);

                        var dispatch = AaaDispatch.GetByCallRequestId(uv.CallRequestId);

                        if (dispatch != null && dispatch.IsClosed)
                        {
                            if (sourceMessage != null)
                                await sourceMessage.CompleteAsync();

                            DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                            return;
                        }
                        client = AcgGetClient(aac, qi, uv.CallRequestId, uv.CallId, dispatch.DispatchId);

                        var ghr = await GeocodeHelper.Geocode(uv.Latitude + ", " + uv.Longitude);

                        if (ghr != null)
                        {
                            if (ghr.Country == "US")
                                ghr.Country = "United States";

                            var payload = new UpdateCallDetailsTowAddressChangeAcceptOrDeclinePayload()
                            {
                                OperationType = "TowAddressChangeAccept",
                                WorkOrderId = dispatch.DispatchId,
                                TowLatitude = uv.Latitude,
                                TowLongitude = uv.Longitude,
                                TowState = ghr.State,
                                TowCity = ghr.City,
                                TowStreet = ghr.Address ?? (uv.Latitude + "," + uv.Longitude),
                                TowPostalCode = ghr.Zip,
                                TowCountry = ghr.Country,
                                TowCounty = ghr.County,
                                TowDestinationName = ghr.Address ?? (uv.Latitude + "," + uv.Longitude)
                            };

                            var hash = Core.MD5(Math.Round(uv.Latitude.GetValueOrDefault(), 6) + "," + Math.Round(uv.Longitude.GetValueOrDefault(), 6));

                            if (Core.GetRedisValue("acg:" + uv.CallId + ":td") != hash)
                            {
                                var job = JsonConvert.DeserializeObject<SalesforceData<ScheduledJobPayload>>(
                                    dispatch.DispatchJson).Payload;

                                if (job.TowLatitude != payload.TowLatitude ||
                                    job.TowLongitude != payload.TowLongitude)
                                {
                                    await client.UpdateCallDetails(payload);
                                }

                                Core.SetRedisValue("acg:" + uv.CallId + ":td", hash, TimeSpan.FromDays(2));
                            }
                        }

                        if (sourceMessage != null)
                            await sourceMessage.CompleteAsync();

                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                    }
                    await doUpdateDestination();
                    break;

                case DigitalDispatchActionQueueItemType.OutgoingUpdateNotes:
                    async Task doUpdateNotes()
                    {
                        var uv = JsonConvert.DeserializeObject<NotesUpdatePayload>(qi.JsonObject);

                        Console.WriteLine(uv.ToJson(true));

                        var aac = AaaContractor.GetByCompanyId(qi.CompanyId.Value)
                            .FirstOrDefault(o => o.AccountId == qi.AccountId.Value);

                        var dispatch = AaaDispatch.GetByCallRequestId(uv.CallRequestId);

                        if (dispatch != null && dispatch.IsClosed)
                        {
                            if (sourceMessage != null)
                                await sourceMessage.CompleteAsync();

                            DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                            return;
                        }

                        client = AcgGetClient(aac, qi, uv.CallRequestId, uv.CallId, dispatch.DispatchId);

                        var hash = Core.MD5(uv.Notes);

                        var cleanedNote = string.Join("\n", uv.Notes.Split('\n').Where(o =>
                            !o.StartsWith("Priority:") &&
                            !o.StartsWith("** NEEDS FLATBED **")));


                        if ((await Core.GetRedisValueAsync("acg:" + uv.CallId + ":notes")) != hash)
                        {
                            await client.UpdateCallDetails(
                                new UpdateCallDetailsNotesUpdatePayloadOutbound()
                                {
                                    WorkOrderId = dispatch.DispatchId,
                                    Notes = cleanedNote
                                });

                            await Core.SetRedisValueAsync("acg:" + uv.CallId + ":notes", hash);
                        }

                        if (sourceMessage != null)
                            await sourceMessage.CompleteAsync();

                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                    }
                    await doUpdateNotes();
                    break;

                case DigitalDispatchActionQueueItemType.OutgoingUpdateContact:
                    async Task doUpdateContact()
                    {
                        var uv = JsonConvert.DeserializeObject<ContactUpdatePayload>(qi.JsonObject);

                        Console.WriteLine(uv.ToJson(true));

                        var aac = AaaContractor.GetByCompanyId(qi.CompanyId.Value)
                            .FirstOrDefault(o => o.AccountId == qi.AccountId.Value);

                        var dispatch = AaaDispatch.GetByCallRequestId(uv.CallRequestId);

                        if (dispatch != null && dispatch.IsClosed)
                        {
                            if (sourceMessage != null)
                                await sourceMessage.CompleteAsync();

                            DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                            return;
                        }
                        client = AcgGetClient(aac, qi, uv.CallRequestId, uv.CallId, dispatch.DispatchId);

                        if (Core.GetRedisValue("acg:" + uv.CallId + ":phone")
                            != Core.FormatPhoneWithNumbersOnly(uv.Phone))
                        {
                            var job = JsonConvert.DeserializeObject<SalesforceData<ScheduledJobPayload>>(
                                dispatch.DispatchJson).Payload;

                            if (Core.FormatPhoneWithDashesOnly(job.MemberPhoneNumber) != Core.FormatPhoneWithDashesOnly(uv.Phone))
                            {
                                await client.UpdateCallDetails(
                                    new UpdateCallDetailsPhoneNumberChangePayload()
                                    {
                                        WorkOrderId = dispatch.DispatchId,
                                        MemberPhoneNumber = Core.FormatPhoneWithNumbersOnly(uv.Phone)
                                    });
                            }

                            await Core.SetRedisValueAsync("acg:" + uv.CallId + ":phone",
                                Core.FormatPhoneWithNumbersOnly(uv.Phone), TimeSpan.FromDays(2));
                        }

                        if (sourceMessage != null)
                            await sourceMessage.CompleteAsync();

                        DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Completed);
                    }
                    await doUpdateContact();
                    break;

                default:
                    await sourceMessage.DeadLetterAsync("No implementation written", qi.ToJson());
                    DigitalDispatchActionQueueItem.UpdateStatus(qi, DigitalDispatchActionQueueItemStatus.Error);
                    logger.LogEvent("Queue item doesn't have a Type set that we have a implementation written for... MessageId {0}, Body = {1}", qi.CompanyId, LogLevel.Error, sourceMessage.Message.MessageId, qi.ToJson());
                    return;
            }
        }
    }

}

public class AcgHelpers
{
    private static readonly Logger logger = LogManager.GetCurrentClassLogger();

    public static void AddToQueue(
        string contractorId, 
        int environmentId,
        DigitalDispatchActionQueueItem q)
    {
        var key = "acg_queued:" + environmentId + ":" + contractorId;
        Core.GetRedisDatabase().SortedSetAdd(key,
            q.QueueItemId, q.QueueItemId);
    }

    public static async Task CheckForQueuedUpdates(string contractorId, int environmentId)
    {
        var key = "acg_queued:" + environmentId + ":" + contractorId;
        if (Core.GetRedisDatabase().SortedSetLength(key) == 0)
            return;

        var aac = AaaContractor.GetByContractorId(contractorId, MasterAccountTypes.AaaAcg, environmentId);
        if (aac == null)
        {
            Console.WriteLine("facility doesn't exist yet, try again.");
            return;
        }


        foreach (var item in Core.GetRedisDatabase().SortedSetRangeByRank(
            key, order: StackExchange.Redis.Order.Ascending))
        {
            Console.WriteLine(item);

            var q = DigitalDispatchActionQueueItem.GetById((int)item);

            q.CompanyId = aac.CompanyId;
            q.AccountId = aac.AccountId;

            if (q != null)
            {
                logger.Info(MasterAccountTypes.AaaAcg,
                    "CheckForQueuedUpdates",
                    "Executing deferred driver insert event.",
                     companyId: aac.CompanyId,
                     queueItemId: q.QueueItemId);

                var am = JsonConvert.DeserializeObject<AaaMessage>(q.JsonObject);
                Console.WriteLine(am.JsonData);
                var retval = await MotorClubDispatchingService.HandleAaaAcgIncomingMessage(q, am, null);
                Console.WriteLine(q.QueueItemId + " passed " + retval);
                if (retval)
                Core.GetRedisDatabase().SortedSetRemove(key, item);
            }
        }
    }


}

