apiVersion: apps/v1
kind: Deployment
metadata:
  name: mcdispatchingservice
spec:
  selector:
    matchLabels:
      app: mcdispatchingservice-pod
  template:
    metadata:
      labels:
        app: mcdispatchingservice-pod
    spec:
      containers:
      - name: mcdispatchingservice-container
        image: towbookapiregistry.azurecr.io/mcdispatchingservice:latest
        resources:
          requests:
            memory: "64Mi"
            cpu: "50m"
          limits:
            memory: "250Mi"
            cpu: "100m"
        env:
        - name: ASPNETCORE_ENVIRONMENT
          value: "Development"
        - name: CORECLR_ENABLE_PROFILING
          value: "1"
        - name: CORECLR_PROFILER
          value: "{36032161-FFC0-4B61-B559-F6C5D41BAE5A}"
        - name: NEW_RELIC_APP_NAME
          value: "MotorClubDispatchingService"
        - name: NEW_RELIC_LICENSE_KEY
          value: "ba6a3e6ef84b36cc9a86e7ed156ca2e1FFFFNRAL"
        - name: ConnectionStrings__Database
          valueFrom:
            secretKeyRef:
              name: kvtowbook-secrets
              key: ConnectionStrings-Database
        - name: ConnectionStrings__Database.Azure
          valueFrom:
            secretKeyRef:
              name: kvtowbook-secrets
              key: ConnectionStrings-DatabaseAzure
        - name: ConnectionStrings__Microsoft.ServiceBus
          valueFrom:
            secretKeyRef:
              name: kvtowbook-secrets
              key: ConnectionStrings-MicrosoftServiceBus
        - name: Redis__Credentials
          valueFrom:
            secretKeyRef:
              name: kvtowbook-secrets
              key: Redis-Credentials
        - name: CosmosDb__Url
          valueFrom:
            secretKeyRef:
              name: kvtowbook-secrets
              key: CosmosDb-Url
        - name: CosmosDb__AuthKey
          valueFrom:
            secretKeyRef:
              name: kvtowbook-secrets
              key: CosmosDb-AuthKey
        - name: CosmosDb__Database
          valueFrom:
            secretKeyRef:
              name: kvtowbook-secrets
              key: CosmosDb-Database
        volumeMounts:
          - name:  secrets-store
            mountPath:  "mnt/secrets-store"
            readOnly: true
      nodeSelector:
        selector: nplin
      volumes:
        - name: secrets-store
          csi:
            driver: secrets-store.csi.k8s.io
            readOnly: true
            volumeAttributes:
              secretProviderClass: "azure-kvtowbook-msi"