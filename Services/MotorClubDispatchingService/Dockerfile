#See https://aka.ms/containerfastmode to understand how Visual Studio uses this Dockerfile to build your images for faster debugging.

FROM mcr.microsoft.com/dotnet/aspnet:8.0-alpine3.18 AS base
ENV TZ="America/New_York"
ENV LC_ALL=en_US.UTF-8
ENV LANG=en_US.UTF-8

RUN apk add --no-cache tzdata
RUN apk add --no-cache icu-libs icu-data-full

ARG COMMIT
RUN if [ -z "$COMMIT" ]; then echo "COMMIT is empty"; exit 1; else : echo "commitId: $COMMIT" ; fi

RUN apk update && apk upgrade

# install NewRelic agent
ARG NEW_RELIC_APP_NAME=MotorClubDispatchingService
ARG CORECLR_ENABLE_PROFILING=0
ARG CORECLR_PROFILER={36032161-FFC0-4B61-B559-F6C5D41BAE5A}
ARG NEW_RELIC_LICENSE_KEY=NEW_RELIC_LICENSE_VALUE

RUN echo "NewRelic AppName: $NEW_RELIC_APP_NAME"

RUN  mkdir /tmp/newrelic-dotnet-agent \
&& cd /tmp \
&& export NEW_RELIC_DOWNLOAD_URI=https://download.newrelic.com/$(wget -qO - "https://nr-downloads-main.s3.amazonaws.com/?delimiter=/&prefix=dot_net_agent/latest_release/newrelic-dotnet-agent" |  \
     grep -E -o "dot_net_agent/latest_release/newrelic-dotnet-agent_[[:digit:]]{1,3}(\.[[:digit:]]{1,3}){2}_amd64\.tar\.gz") \
&& echo "Downloading: $NEW_RELIC_DOWNLOAD_URI into $(pwd)" \
&& wget -O - "$NEW_RELIC_DOWNLOAD_URI" | gzip -dc | tar xf - 

ENV CORECLR_ENABLE_PROFILING=$CORECLR_ENABLE_PROFILING \
CORECLR_PROFILER=$CORECLR_PROFILER \
CORECLR_NEWRELIC_HOME=/tmp/newrelic-dotnet-agent \
CORECLR_PROFILER_PATH=/tmp/newrelic-dotnet-agent/libNewRelicProfiler.so \
NEW_RELIC_LICENSE_KEY=$NEW_RELIC_LICENSE_KEY \
NEW_RELIC_APPLICATION_LOGGING_ENABLED=true \
NEW_RELIC_APPLICATION_LOGGING_METRICS_ENABLED=true \
NEW_RELIC_APPLICATION_LOGGING_FORWARDING_ENABLED=true \
NEW_RELIC_APPLICATION_LOGGING_FORWARDING_CONTEXT_DATA_ENABLED=true \
NEW_RELIC_APPLICATION_LOGGING_FORWARDING_MAX_SAMPLES_STORED=10000 \
NEW_RELIC_APPLICATION_LOGGING_LOCAL_DECORATING_ENABLED=false \
NEW_RELIC_APP_NAME=$NEW_RELIC_APP_NAME

WORKDIR /app

FROM --platform=$BUILDPLATFORM mcr.microsoft.com/dotnet/sdk:8.0-alpine3.18 AS build
ARG TARGETARCH
ARG BUILDPLATFORM
ARG COMMIT

WORKDIR /src
COPY ["Extric.Towbook/Extric.Towbook.csproj", "Extric.Towbook/"]
COPY ["Glav.CacheAdapter/Glav.CacheAdapter.csproj", "Glav.CacheAdapter/"]
COPY ["Extric.Towbook.WebWrapper/Extric.Towbook.WebWrapper.csproj", "Extric.Towbook.WebWrapper/"]
COPY ["Extric.Towbook.Storage/Extric.Towbook.Storage.csproj", "Extric.Towbook.Storage/"]
COPY ["Extric.Towbook.Generated.Features/Extric.Towbook.Generated.Features.csproj", "Extric.Towbook.Generated.Features/"]
COPY ["Extric.Towbook.DynamicFeatureBuilder/Extric.Towbook.DynamicFeatureBuilder.csproj", "Extric.Towbook.DynamicFeatureBuilder/"]
COPY ["Extric.Towbook.Integrations.Email/Extric.Towbook.Integrations.Email.csproj", "Extric.Towbook.Integrations.Email/"]
COPY ["Extric.Towbook.Integration.MotorClubs/Extric.Towbook.Integration.MotorClubs.csproj", "Extric.Towbook.Integration.MotorClubs/"]
COPY ["Integrations/MotorClubs/Allstate/Allstate.csproj", "Integrations/MotorClubs/Allstate/"]
COPY ["Integrations/MotorClubs/Gerber/Gerber.csproj", "Integrations/MotorClubs/Gerber/"]
COPY ["Integrations/MotorClubs/Honk/Honk.csproj", "Integrations/MotorClubs/Honk/"]
COPY ["Integrations/MotorClubs/Nsd/Nsd.csproj", "Integrations/MotorClubs/Nsd/"]
COPY ["Integrations/MotorClubs/Urgently/Urgently.csproj", "Integrations/MotorClubs/Urgently/"]
COPY ["Integrations/MotorClubs/Sykes/Sykes.csproj", "Integrations/MotorClubs/Sykes/"]
COPY ["Integrations/MotorClubs/Nac/Nac.csproj", "Integrations/MotorClubs/Nac/"]
COPY ["Integrations/MotorClubs/Agero/Agero.csproj", "Integrations/MotorClubs/Agero/"]
COPY ["Integrations/MotorClubs/Fleetnet/Fleetnet.csproj", "Integrations/MotorClubs/Fleetnet/"]
COPY ["Integrations/MotorClubs/Swoop/Swoop.csproj", "Integrations/MotorClubs/Swoop/"]
COPY ["Integrations/MotorClubs/Geico/Issc.csproj", "Integrations/MotorClubs/Geico/"]
COPY ["Integrations/MotorClubs/OON/OonAgeroClient/OonAgeroClient.csproj", "Integrations/MotorClubs/OON/OonAgeroClient/"]
COPY ["Integrations/MotorClubs/Quest/Quest.csproj", "Integrations/MotorClubs/Quest/"]
COPY ["Roadside/Extric.Roadside/Extric.Roadside.csproj", "Roadside/Extric.Roadside/"]
COPY ["Services/MotorClubDispatchingService/MotorClubDispatchingService.csproj", "Services/MotorClubDispatchingService/"]
RUN dotnet restore "Services/MotorClubDispatchingService/MotorClubDispatchingService.csproj" -a $TARGETARCH 
COPY . .
RUN echo $COMMIT > Extric.Towbook/_git_commit.txt
WORKDIR "/src/Services/MotorClubDispatchingService"
RUN dotnet build "MotorClubDispatchingService.csproj" -o /app/build

FROM build AS publish
ARG COMMIT
RUN dotnet publish "MotorClubDispatchingService.csproj" -o /app/publish -a $TARGETARCH /p:AssemblyVersion=1.0.0.0 /p:Version=1.0.0.0-$COMMIT

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .

#copy openssl.cnf for alpine
COPY --from=publish /app/publish/openssl.cnf /etc/ssl
RUN rm ./openssl.cnf

RUN apk add icu-libs
ENV DOTNET_SYSTEM_GLOBALIZATION_INVARIANT=false

ENTRYPOINT ["dotnet", "MotorClubDispatchingService.dll", "-cds", "-autodispatch"]
