using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using Extric.Roadside;
using Extric.Roadside.Surveys;
using Extric.Towbook.EventNotifications;
using Extric.Towbook.Utility;
using Newtonsoft.Json;
using NLog;
using Async = System.Threading.Tasks;
using System.Threading.Tasks;

namespace Extric.Towbook.EventNotificationService
{
    public class RoadsideSurveyService
    {

        private static readonly Logger logger = LogManager.GetCurrentClassLogger();

        public async static Async.Task RoadsideSurveyServiceAsync(Services.EventNotificationService.EventNotificationService svc, IDictionary<string, Consumer> consumers)
        {
            var options = new OnMessageOptions()
            {
                AutoCompleteMessages = false,
                MaxConcurrentCalls = svc.MaxThreads
            };

            var queueName = RoadsideSurveyQueueItem.GetQueueName();
            var consumer = await ENServiceBusHelper.CreateConsumerQueueAsync(queueName, options);
            consumers.Add(queueName, consumer);
            await consumer.OnMessageAsync(async (r) =>
            {
                // Start processing message
                var body = r.GetBody<string>();
                dynamic obj = body.FromJson();

                logger.LogEvent("Roadside/Survey. Incoming Message: Id = {0}, Label = {1}", null, LogLevel.Debug, r.Message.MessageId, r.Message.Subject);

                if (r.Message.EnqueuedTime < DateTime.UtcNow.AddDays(-1))
                {
                    logger.LogEvent("IGNORE: Incoming Message is older than one day. Id = {0}, Label = {1}", null, LogLevel.Debug, r.Message.MessageId, r.Message.Subject);
                    await r.CompleteAsync();
                    return;
                }

                if (obj["type"] != null)
                {
                    int typeId = obj["type"];
                    int dispatchId = obj["dispatchId"];
                    int? performer = obj["dispatchUserId"];
                    bool testMode = r.Message.ApplicationProperties.ContainsKey("TestMode") ? true : false;

                    logger.LogEvent(RoadsideSurveyQueueItem.GetQueueName(), null, LogLevel.Debug, new Dictionary<object, object> {
                        ["type"] = ((RoadsideSurveyTriggerType)typeId).ToString(),
                        ["roadsideDispatchId"] = dispatchId,
                        ["performerId"] = performer,
                        ["testMode"] = testMode
                    });
                    

                    if (typeId != 0 && dispatchId > 0)
                    {
                        try
                        {
                            var nType = (RoadsideSurveyTriggerType)typeId;
                            var notifications = new List<dynamic>();
                            var usersToNotify = new Collection<int>();
                            var userTypesToNotify = new Collection<int>();
                            
                            SurveyResponse response = null;
                            RoadsideDispatchUser user = null;
                            

                            RoadsideDispatch dispatch = await RoadsideDispatch.GetByIdAsync(dispatchId);
                            if (dispatch == null)
                                throw new Exception($"Roadside dispatch is invalid (DispatchId: {dispatchId}).");

                            Dispatch.Entry entry = Dispatch.Entry.GetById(dispatch.DispatchEntryId);
                            if (entry == null)
                                throw new Exception($"Roadside dispatch is invalid (DispatchId: {dispatchId}/dispatchEntryId: {dispatch.DispatchEntryId}).");

                            EventNotification item = EventNotification.GetById((int)nType);
                            if (item == null)
                                throw new Exception($"RoadsideSurveyService Error => Event notification item is invalid (itemId: {(int)nType}).");

                            Collection<EventUserTypeNotification> userTypesList = new Collection<EventUserTypeNotification>();
                            Collection<EventUserNotification> userList = new Collection<EventUserNotification>();
                            
                            if (testMode)
                            {
                                // Test mode => form required users and delivery types from properties
                                int[] requiredUsers = null;
                                DeliveryMethodType[] deliveryMethods = null;

                                // reset default to no required notifications
                                item = new EventNotification();
                                item.Group = EventNotificationGroup.RoadsideSurvey;
                                item.Id = (int)RoadsideSurveyTriggerType.PoorCustomerRating;

                                if (r.Message.ApplicationProperties.ContainsKey("RecipientIds"))
                                    requiredUsers = JsonConvert.DeserializeObject<int[]>(r.Message.ApplicationProperties["RecipientIds"].ToString());

                                if (r.Message.ApplicationProperties.ContainsKey("DeliveryMethods"))
                                    deliveryMethods = JsonConvert.DeserializeObject<DeliveryMethodType[]>(r.Message.ApplicationProperties["DeliveryMethods"].ToString());

                                foreach (var u in requiredUsers)
                                {
                                    var en = new EventUserNotification()
                                    {
                                        EventNotificationId = (int)nType,
                                        UserId = u,
                                        CompanyId = entry.CompanyId,
                                        RequireEmail = deliveryMethods.Contains(DeliveryMethodType.Email),
                                        RequireText = deliveryMethods.Contains(DeliveryMethodType.Text),
                                        RequirePushNotification = deliveryMethods.Contains(DeliveryMethodType.Mobile),
                                        RequireWebNotification = deliveryMethods.Contains(DeliveryMethodType.Desktop)
                                    };

                                    userList.Add(en);
                                }
                            }
                            else
                            {
                                // get settings for required users, usertypes and delivery methods
                                userTypesList = EventUserTypeNotification.GetByCompanyId(entry.CompanyId, item.Id).ToCollection();
                                userList = EventUserNotification.GetByCompanyId(entry.CompanyId, item.Id).ToCollection();
                            }

                            switch (nType)
                            {
                                case RoadsideSurveyTriggerType.PoorCustomerRating:

                                    response = await SurveyResponse.GetByDispatchUserIdAsync(performer.GetValueOrDefault());
                                    if (response == null)
                                            throw new Exception($"Roadside survey is invalid (DispatchId: {dispatchId} / roadsideUserId: {(performer != null ? performer.Value.ToString() : "unspecified user")}).");
                                    
                                    user = await RoadsideDispatchUser.GetByDispatchUserIdAsync(response.DispatchUserId);
                                    if (user == null)
                                        throw new Exception($"Roadside user is invalid (DispatchId: {dispatchId} / roadsideUserId: {response.DispatchUserId}).");
                                    break;
                            }


                            var users = User.GetByCompanyId(entry.CompanyId).Where(o => !o.Disabled).ToCollection();
                            var pusherRecipients = new Collection<int>();

                            foreach (var u in users
                                .Where(w => (w.Type == User.TypeEnum.Manager || w.Type == User.TypeEnum.Dispatcher || w.Type == User.TypeEnum.Driver) &&
                                    (!w.Disabled && !w.Deleted)))
                            {
                                try
                                {
                                    if (!string.IsNullOrWhiteSpace(u.Email))
                                    {
                                        if (EventNotificationServiceHelper.EmailRequired(item, u, userTypesList, userList))
                                        {
                                            await SendEmail(nType, dispatch, entry, u, user, response);
                                        }
                                    }

                                    if (EventNotificationServiceHelper.TextRequired(item, u, userTypesList, userList))
                                    {
                                        await SendText(nType, dispatch, entry, u, EventNotificationGroup.RoadsideSurvey, user, response);
                                    }

                                    if (EventNotificationServiceHelper.PushNotificationRequired(item, u, userTypesList, userList))
                                    {
                                        await SendPushNotification(nType, dispatch, entry, u, EventNotificationGroup.RoadsideSurvey, user, response);
                                    }

                                    if (EventNotificationServiceHelper.PusherEventRequired(item, u, userTypesList, userList))
                                    {
                                        if (!pusherRecipients.Contains(u.Id))
                                            pusherRecipients.Add(u.Id);
                                    }
                                }
                                catch (Exception e)
                                {
                                    logger.LogEvent($"Roadside/Survey Message: {r.Message.MessageId} -> Error notifying user {u.Username}. Exception: {e}", entry.CompanyId, LogLevel.Error, new Dictionary<object, object>
                                    {
                                        ["type"] = Enum.GetName(((RoadsideSurveyTriggerType)nType).GetType(), item.Id),
                                        ["companyId"] = entry.CompanyId,
                                        ["userId"] = u.Id,
                                        ["userName"] = u.Username,
                                    });
                                }
                            }

                            // send one pusher event to all recipients (if any)
                            if (pusherRecipients.Any())
                                await SendPusherEventAsync(nType, dispatch, entry, pusherRecipients, EventNotificationGroup.RoadsideSurvey, user, response);



                        }
                        catch (Exception e)
                        {
                            await r.CompleteAsync();
                            logger.Log(LogLevel.Error, $"Failed {r.Message.MessageId} because of thrown exception. body: " + e.Message + (e.InnerException != null ? " Inner: " + e.InnerException.Message : ""));
                            return;
                        }

                        await r.CompleteAsync();

                        logger.Log(LogLevel.Debug, "Finished processing " + r.Message.MessageId);
                    }
                    else
                    {
                        await r.CompleteAsync();
                        logger.LogEvent($"Failed {r.Message.MessageId} because it doesn't contain a Type and DispatchEntryId members. body:" + body.Replace("\\\"", "'"), null, LogLevel.Debug);
                    }
                }
                else
                {
                    await r.CompleteAsync();
                    logger.LogEvent("Failed " + r.Message.MessageId + " because it doesn't contain a Type property. Body:" + body.Replace("\\\"", "'"), null, LogLevel.Debug);
                }
            });
        }

        private static async Task SendEmail(RoadsideSurveyTriggerType type,
            RoadsideDispatch dispatch,
            Dispatch.Entry entry,
            User recipient,
            RoadsideDispatchUser performer = null,
            SurveyResponse response = null)
        {
            string subject = string.Empty;
            string message = string.Empty;
            string messageTail = "<br/><br/>Towbook Management Software";
            string completed = string.Empty;
            string driverName = "unassigned";
            string reason = "";

            if (entry.CompletionTime != null)
                completed = entry.CompletionTime.Value.ToShortDateString() + " at " + entry.CompletionTime.Value.ToShortTowbookTimeString();

            if (entry.Reason != null)
                reason = entry.Reason.Name;

            if (entry.Driver != null)
                driverName = entry.Driver.Name;

            switch (type)
            {
                case RoadsideSurveyTriggerType.PoorCustomerRating:
                    if (performer == null || response == null)
                        return;

                    subject = $"Call #{entry.CallNumber} received a poor customer rating";
                    message = $"Call #{entry.CallNumber} has received a customer survey response.  Unfortunately, the results are not good.<br/><br/>" +
                                $"Rating: {response.AverageRating}<br/><br/>Customer Name: {performer.Name}<br/>phone: {performer.MobileNumber}" + 
                                $"{(string.IsNullOrEmpty(performer.Email) ? "" : $"<br/>Email: {performer.Email}")}<br/><br/>" +
                                $"The full survey results are available at <a href='{performer.Url}'>{performer.Url}</a>." +
                                $"<br/><br/>Driver: {driverName}<br/>Service Reason: {reason}<br/>Completed: {completed}" +
                                $"{ messageTail}";
                    break;
            }

            await EventNotificationServiceHelper.SendEmail(recipient.Email, message, subject, entry.CompanyId);

            logger.Log(LogLevel.Info, $"Sent {Enum.GetName(type.GetType(), type)} Email to {recipient.FullName}/{recipient.Email}");
        }

        private async static Async.Task<dynamic> SendPusherEventAsync(RoadsideSurveyTriggerType type,
            RoadsideDispatch dispatch,
            Dispatch.Entry entry,
            Collection<int> recipients,
            EventNotificationGroup group = EventNotificationGroup.System,
            RoadsideDispatchUser performer = null,
            SurveyResponse response = null)
        {

            var title = "";
            var message = "";

            switch (type)
            {
                case RoadsideSurveyTriggerType.PoorCustomerRating:
                    if (performer == null || response == null)
                        return null;

                    title = $"Poor customer rating on call #{entry.CallNumber}";
                    message = $"{performer.Name}{(string.IsNullOrEmpty(performer.MobileNumber) ? "" : " " + Core.FormatPhone(performer.MobileNumber))} " + 
                                $"has given you a rating of {response.AverageRating}.";
                    break;
            }

            var alertSound = EventNotificationAlertSound.GetByItemId(entry.CompanyId, (int)type);
            var sound = alertSound?.EventNotificationSoundId > 0 ? EventNotificationSound.GetById(alertSound.EventNotificationSoundId) : null;

            await EventNotificationServiceHelper.SendPusherEventAsync(entry.CompanyId, recipients, title, message, (EventNotificationType)type, dispatch.Id, null, null, sound);

            logger.Log(LogLevel.Info, "Sent Roadside/Survey Pusher Notification to recipients " + string.Join(",", recipients));

            return new
            {
                Type = "Pusher",
                Recipients = recipients
            };
        }

        private static async Task<dynamic> SendPushNotification(RoadsideSurveyTriggerType type,
            RoadsideDispatch dispatch,
            Dispatch.Entry entry,
            User u,
            EventNotificationGroup group = EventNotificationGroup.System,
            RoadsideDispatchUser performer = null,
            SurveyResponse response = null)
        {

            var message = "";

            switch (type)
            {
                case RoadsideSurveyTriggerType.PoorCustomerRating:
                    if (performer == null || response == null)
                        return null;

                    message = $"{performer.Name}{(string.IsNullOrEmpty(performer.MobileNumber) ? "" : " " + Core.FormatPhone(performer.MobileNumber))} " +
                                $"gave a rating of {response.AverageRating}.";
                    break;
            }

            var alertSound = EventNotificationAlertSound.GetByUserId(entry.CompanyId, (int)type, u.Id);
            var sound = alertSound?.EventNotificationSoundId > 0 ? EventNotificationSound.GetById(alertSound.EventNotificationSoundId) : null;

            await EventNotificationServiceHelper.SendPushNotification(u, $"Poor rating on call #{entry.CallNumber}", message, group, dispatch.Id, null, null, sound);

            logger.Log(LogLevel.Info, "Sent Roadside/Survey Push Notification to " + u.FullName + "/" + u.MobilePhone);

            return new
            {
                Type = "Push",
                UserId = u.Id,
                Name = u.FullName,
                Phone = u.MobilePhone,
                Email = u.Email
            };
        }

        private static async Task<dynamic> SendText(RoadsideSurveyTriggerType type,
            RoadsideDispatch dispatch,
            Dispatch.Entry entry,
            User u,
            EventNotificationGroup group = EventNotificationGroup.System,
            RoadsideDispatchUser performer = null,
            SurveyResponse response = null)
        {
            if (string.IsNullOrWhiteSpace(u.MobilePhone))
                return new { Type = "None" };

            var message = "";

            switch (type)
            {
                case RoadsideSurveyTriggerType.PoorCustomerRating:
                    if (performer == null || response == null)
                        return null;

                    message = $"Poor customer rating on call #{entry.CallNumber}. " +
                                $"{performer.Name}{(string.IsNullOrEmpty(performer.MobileNumber) ? "" : " " + Core.FormatPhone(performer.MobileNumber))} " +
                                $"gave a rating of {response.AverageRating}.";
                    break;
            }

            await EventNotificationServiceHelper.SendText(u, message);

            logger.Log(LogLevel.Info, "Sent Roadside/Survey SMS text to " + u.FullName + "/" + u.MobilePhone);

            return new
            {
                Type = "SMS",
                UserId = u.Id,
                Name = u.FullName,
                Phone = u.MobilePhone,
                Email = u.Email
            };
        }
    }
}
