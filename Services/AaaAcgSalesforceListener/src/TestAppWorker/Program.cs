using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Azure;
using PlatformListeners;
using System;
using System.Threading.Tasks;
using System.Threading;
using System.IO;
using Microsoft.Extensions.Diagnostics.HealthChecks;

namespace TestAppWorker;

internal sealed class Program
{
    public static void Main(string[] args)
    {
        Console.WriteLine("Hello.");
        Console.WriteLine("");

        CreateHostBuilder(args).Build().Run();
    }

    public static IHostBuilder CreateHostBuilder(string[] args)
    {
        return Host.CreateDefaultBuilder(args)
        .ConfigureAppConfiguration((hostContext, configBuilder) =>
        {
            configBuilder.AddAzureKeyVault(hostingEnviromentName: hostContext.HostingEnvironment.EnvironmentName);

            
            if (hostContext.HostingEnvironment.IsDevelopment())
            {
                // print out the environment
                var config = configBuilder.Build();
                config.DebugConfigurations();
            }
        })
        .ConfigureServices((hostContext, services) =>
        {
            // This is really bad. We should not be using static class to set the connection string.
            Core.ConnectionString = hostContext.Configuration.GetConnectionString("IbmSql");
            Core.SendgridKey = hostContext.Configuration.GetSection("Towbook").Get<TowbookConfiguration>().SendgridKey;
            var env = hostContext.Configuration.GetSection("Towbook").Get<TowbookConfiguration>().Environment;
            if (env.ToLower() == "dev")
            {
                Core.EnvironmentId = 1;
            }
            else if (env.ToLower() == "uat")
            {
                Core.EnvironmentId = 2;
            }
            else if (env.ToLower() == "prod")
            {
                Core.EnvironmentId = 3;
            }

            if (System.OperatingSystem.IsWindows())
            {
                Console.Title += " | " + env.ToUpperInvariant();
            }
            services.AddHealthChecks();
            services.AddSingleton<IHealthCheckPublisher, FileHealthCheckPublisher>();
            services.AddSalesforceEventBus(environmentName: env);
            services.AddCustomerSalesforceEventBus();
            services.AddAzureClients(builder =>
            {
                builder.AddServiceBusClient(hostContext.Configuration.GetConnectionString("ServiceBus"));                   
            });
        })
        .ConfigureLogging((hostContext, configLogging) =>
        {
            configLogging.AddConfiguration(hostContext.Configuration.GetSection("Logging"));
            configLogging.AddConsole();
            configLogging.AddDebug();
        });
    }
}

internal sealed class FileHealthCheckPublisher : IHealthCheckPublisher
{
    private readonly string _healthCheckFilePath = "/tmp/health";

    public FileHealthCheckPublisher()
    {
        
    }

    public Task PublishAsync(HealthReport report, CancellationToken cancellationToken)
    {
        if (report.Status == HealthStatus.Healthy)
        {
            Touch(_healthCheckFilePath);
        }
        else
        {
            Delete(_healthCheckFilePath);
        }

        return Task.CompletedTask;
    }

    private void Touch(string path)
    {
        using var fileStream = File.Open(path, FileMode.OpenOrCreate, FileAccess.ReadWrite, FileShare.ReadWrite);
        var now = DateTime.UtcNow;
        File.SetLastWriteTimeUtc(path, now);
    }

    private void Delete(string path)
    {
        try
        {
            File.Delete(path);
        }
        catch
        {
            // best effort delete; might not exist in the first place
        }
    }
}

