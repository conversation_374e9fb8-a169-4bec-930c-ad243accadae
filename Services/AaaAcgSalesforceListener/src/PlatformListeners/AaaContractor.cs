using System;
using System.Collections.Generic;
using System.ComponentModel.Design;
using System.Linq;
using System.Threading.Tasks;
using Dapper;
using Dapper.Contrib.Extensions;
using Microsoft.Data.SqlClient;

namespace PlatformListeners;

[Table("MCDispatch.AaaContractors")]
public class AaaContractor
{
    [Key]
    public int AaaContractorId { get; set; }
    public int MasterAccountId { get; set; }
    public int EnvironmentId { get; set; }
    public int CompanyId { get; set; }
    public int AccountId { get; set; }
    public string ContractorId { get; set; }
    public string LocationCode { get; set; }
    public bool? IsValidated { get; set; }
    public bool IsDeleted { get; set; }
    public DateTime CreateDate { get; set; }

    public static async Task<AaaContractor> GetById(int id)
    {
        using var x = new SqlConnection(Core.ConnectionString);

        return await x.QueryFirstOrDefaultAsync<AaaContractor>(
            "SELECT * FROM MCDispatch.AaaContractors WITH (NOLOCK) " +
            "WHERE AaaContractorId=@AaaContractorId",
            new
            {
                AaaContractorId = id
            })!;
    }

    public static async Task<AaaContractor> GetByContractorId(string contractorId,  int environmentId)
    {
        using var x = new SqlConnection(Core.ConnectionString);
        const int masterAccountId = 45;
        return await x.QueryFirstOrDefaultAsync<AaaContractor>(
            "SELECT * FROM MCDispatch.AaaContractors WITH (NOLOCK) " +
            "WHERE ContractorId = @ContractorId AND MasterAccountId=@MasterAccountId AND EnvironmentId=@EnvironmentId AND IsDeleted=0",
            new
            {
                @ContractorId = contractorId,
                MasterAccountId = masterAccountId,
                EnvironmentId = environmentId
            })!;
    }

    public static async Task<IEnumerable<AaaContractor>> GetByCompanyId(int companyId)
    {
        using var x = new SqlConnection(Core.ConnectionString);

        return (await x.QueryAsync<AaaContractor>(
            "SELECT * FROM MCDispatch.AaaContractors WITH (NOLOCK) " +
            "WHERE CompanyId=@CompanyId",
            new
            {
                CompanyId = companyId
            })).ToList();
    }

    public void Save()
    {
        if (CreateDate == DateTime.MinValue)
        {
            CreateDate = DateTime.Now;
        }

        if (string.IsNullOrWhiteSpace(ContractorId))
        {
            throw new Exception("ContractorID must be specified before saving a Contractor object.");
        }

        if (!int.TryParse(ContractorId, out var contractorId))
        {
            throw new Exception("Invalid ContractorId. Must be numbers only, no spaces or other characters.");
        }

        using var x = new SqlConnection(Core.ConnectionString);

        if (AaaContractorId == 0)
        {
            AaaContractorId = Convert.ToInt32(x.Insert(this));
        }
        else
        {
            x.Update(this);
        }
    }

    public static async Task<AaaContractor?> GetByServiceTerritory(string serviceTerritoryId, int environmentId)
    {
        using var conn = new SqlConnection(Core.ConnectionString);

        return (await conn.QueryAsync<AaaContractor>(
            "select* From MCDispatch.AaaContractors with (nolock) where EnvironmentId = @EnvironmentId and AccountId in(\nselect AccountId from Integration.ProviderAccountKeyValues with (nolock) where\nvalue = @territoryId and ProviderAccountKeyId = 152)",
        new
        {
            territoryId = serviceTerritoryId,
            EnvironmentId = environmentId
        })).FirstOrDefault();
    }
}
