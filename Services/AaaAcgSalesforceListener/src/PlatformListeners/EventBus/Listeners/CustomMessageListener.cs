using System;
using System.IO;

using Bet.Salesforce.TestApp.EventBus.Messages;

using CometD.NetCore.Bayeux;
using CometD.NetCore.Bayeux.Client;

using Microsoft.Extensions.Logging;

using Newtonsoft.Json;

namespace PlatformListeners.EventBus.Listeners
{
    /// <summary>
    /// The <see cref="CustomMessageListener"/> implements <see cref="IMessageListener"/>.
    /// </summary>
    public class CustomMessageListener : IMessageListener
    {
        private readonly ILogger<CustomMessageListener> _logger;
        private readonly IServiceProvider _serviceProvider;

        /// <summary>
        /// Initializes a new instance of the <see cref="CustomMessageListener"/> class.
        /// </summary>
        /// <param name="logger">Instance of the <see cref="ILogger{CustomMessageListener}"/>.</param>
        /// <param name="serviceProvider"></param>
        public CustomMessageListener(
            ILogger<CustomMessageListener> logger,
            IServiceProvider serviceProvider)
        {
            _logger = logger;
            _serviceProvider = serviceProvider;
        }

        /// <summary>
        /// Receives Salesforce message from Platform Event.
        /// </summary>
        /// <param name="channel"></param>
        /// <param name="message"></param>
        public void OnMessage(IClientSessionChannel channel, IMessage message)
        {
            var msg = JsonConvert.DeserializeObject<CustomMessageEnvelope>(message.Json);

            _logger.LogDebug($"{nameof(CustomMessageListener)} payload: {message.Json}");

            var formattedJson =
                JsonConvert.SerializeObject(
                    JsonConvert.DeserializeObject(message.Json),
                    Newtonsoft.Json.Formatting.Indented);

            Console.WriteLine(formattedJson);

            File.WriteAllText(
                $"C:\\temp\\aaa\\{msg.Data.Event.ReplayId}.json",
                formattedJson);

            var custName = msg.Data.Payload.CustomerName;
            var replayId = msg.Data.Event.EventUuid;

            _logger.LogDebug($"Customer Name: {custName} - ReplayId: {replayId}");

            Console.WriteLine("WHAT IS THIS?!?!!?!)");
            Console.ReadLine();
            //var busEvent = _serviceProvider.GetRequiredService<IEventBus>();

            // publishes to SF custom Object.
            /*busEvent.Publish<TestEvent__c>(new BusEvent<TestEvent__c>
            {
                Name = nameof(TestEvent__c),
                Data = new TestEvent__c { Name = custName }
            }).GetAwaiter().GetResult();*/

        }
    }
}
