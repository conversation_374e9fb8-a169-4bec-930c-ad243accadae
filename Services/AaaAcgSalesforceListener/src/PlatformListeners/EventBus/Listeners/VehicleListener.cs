using System;
using System.IO;
using System.Threading.Tasks;

using Azure.Messaging.ServiceBus;

using Bet.Salesforce.TestApp.EventBus.Messages;

using CometD.NetCore.Bayeux;
using CometD.NetCore.Bayeux.Client;
using CometD.NetCore.Salesforce.Messaging;

using Microsoft.Extensions.Logging;

using Newtonsoft.Json;

namespace PlatformListeners.EventBus.Listeners
{
    /// <summary>
    /// The <see cref="VehicleListener"/> implements <see cref="IMessageListener"/>.
    /// </summary>
    public class VehicleListener : IMessageListener
    {
        private readonly ILogger<VehicleListener> _logger;
        private readonly IServiceProvider _serviceProvider;

        private readonly ServiceBusClient _serviceBusClient;
        private readonly ServiceBusSender _serviceBusSender;

        /// <summary>
        /// Initializes a new instance of the <see cref="VehicleListener"/> class.
        /// </summary>
        /// <param name="logger">Instance of the <see cref="ILogger{VehicleListener}"/>.</param>
        /// <param name="serviceProvider"></param>
        public VehicleListener(
            ILogger<VehicleListener> logger,
            ServiceBusClient serviceBusClient,
            IServiceProvider serviceProvider)
        {
            _logger = logger;
            _serviceProvider = serviceProvider;

            _serviceBusClient = serviceBusClient;
            _serviceBusSender = serviceBusClient.CreateSender("mc-dispatch-inbound");
        }

        /// <summary>
        /// Receives Salesforce message from Platform Event.
        /// </summary>
        /// <param name="channel"></param>
        /// <param name="message"></param>
        public async void OnMessage(IClientSessionChannel channel, IMessage message)
        {
            var msg = JsonConvert.DeserializeObject<MessageEnvelope<VehiclePayload>>(message.Json);

            msg.Data.Payload.EnvironmentId = Core.EnvironmentId;

            var payload = msg.Data.Payload;

            _logger.LogDebug($"{nameof(VehicleListener)} payload: {message.Json}");

            var formattedJson = msg.Data.FormatJsonIndented();

            Console.WriteLine(formattedJson);

            File.WriteAllText(
                $"C:\\temp\\aaa\\{msg.Data.Event.ReplayId}-{msg.Channel.Replace("/", "-")}.json",
                formattedJson);

            /*{
    "acgFacilityId": "0Hh1B000000XZDQSA4",
    "name": "FLATBED-A",
    "vehicleId": "1311B000000XZDkQAO",
    "facilityId": "4901",
    "facilityName": "z-4901-Test Facility",
    "vehicleType": "Flatbed",
    "createdDate": "2024-04-23T20:43:19.441Z",
    "createdById": "0051B00000DRU34QAH"
  }*/

            var acgFacilityId = msg.Data.Payload.AcgFacilityId;
            var facilityId = msg.Data.Payload.FacilityId;
            var facilityName = msg.Data.Payload.FacilityId;
            var vehicleName = msg.Data.Payload.Name;
            var vehicleId = msg.Data.Payload.VehicleId;
            var vehiceType = msg.Data.Payload.VehicleType;

            //EmailAddress.GetByEmailAddress("");
            var ac = await AaaContractor.GetByContractorId(payload.FacilityId, Core.EnvironmentId);

            payload.EnvironmentId = Core.EnvironmentId;

            var queueItem = await DigitalDispatchActionQueueItem.CreateQueuedItem(
                msg.Data.FormatJsonIndented(),
                new AaaContractor()
                {
                    EnvironmentId = Core.EnvironmentId,
                    AaaContractorId = ac?.AaaContractorId ?? 0,
                    ContractorId = facilityId,
                    CompanyId = ac?.CompanyId ?? 0,
                    AccountId = ac?.AccountId ?? 0
                },
                DigitalDispatchActionQueueItemType.IncomingTruckCreated);

            await _serviceBusSender.SendMessage(queueItem, msg.Data.Event.EventUuid);

        }
    }
}
