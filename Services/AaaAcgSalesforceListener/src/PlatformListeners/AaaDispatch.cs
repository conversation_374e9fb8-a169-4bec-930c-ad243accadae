using System.Threading.Tasks;
using Dapper;
using Dapper.Contrib.Extensions;
using Microsoft.Data.SqlClient;

namespace PlatformListeners;

[Table("MCDispatch.AaaDispatches")]
public class AaaDispatch
{
    [Key]
    public long AaaDispatchId { get; set; }
    public int AaaContractorId { get; set; }
    public string DispatchId { get; set; }
    public string DispatchJson { get; set; }
    public int CallRequestId { get; set; }
    public int? Eta { get; set; }

    public AaaDispatch() { }

    public static async Task<AaaDispatch> GetByDispatchId(string id)
    {
        using var conn = new SqlConnection(Core.ConnectionString);
        return (await conn.QueryFirstOrDefaultAsync<AaaDispatch>("SELECT TOP 1 * FROM MCDispatch.AaaDispatches WITH (NOLOCK) " +
            "WHERE DispatchId = @Id ORDER BY 1 DESC",
            new
            {
                Id = id
            }))!;
    }
    public static async Task<AaaDispatch> GetByCallRequestId(int callRequestId)
    {
        using var conn = new SqlConnection(Core.ConnectionString);
        return (await conn.QueryFirstOrDefaultAsync<AaaDispatch>("SELECT * FROM MCDispatch.AaaDispatches WITH (NOLOCK) WHERE CallRequestId = @CallRequestId",
            new { CallRequestId = callRequestId }))!;
    }

    public async Task Save()
    {
        using var conn = new SqlConnection(Core.ConnectionString);

        if (AaaDispatchId == 0)
        {
            AaaDispatchId = await conn.InsertAsync(this);
        }
        else
        {
            await conn.UpdateAsync(this);
        }
    }
}
