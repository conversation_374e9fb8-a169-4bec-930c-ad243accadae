using Extric.Towbook.Stickering;
using Extric.Towbook.Utility;
using NLog;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Net;
using System.Net.Mail;
using System.Text;
using System.Threading.Tasks;

namespace Extric.Towbook.Services.StickeringProcessingService
{
    public class StickerDailyActivityItem
    {
        public int CompanyId { get; set; }
        public int AccountId { get; set; }
        public Collection<Sticker> Stickers {get;set;}

        private static Logger logger = LogManager.GetCurrentClassLogger();

        public StickerDailyActivityItem()
        {
        }

        public StickerDailyActivityItem(Sticker s)
        {
            if(Stickers == null)
                Stickers = new Collection<Sticker>();

            if (s == null || Stickers.Select(a => a.Id).Contains(s.Id))
                return;

            this.CompanyId = s.CompanyId;
            this.AccountId = s.AccountId;
            Stickers.Add(s);
        }

        public async Task SendDailyReportEmail(MailAddress sender)
        {
            using (var m = new SmtpClient().Get())
            {
                var users = User.GetByAccountId(AccountId);
                var needApprovalStickerIds = Stickers.Where(w => w.StatusId == StickerStatus.Waiting.Id && w.AuthorizationRequired.GetValueOrDefault() > 0 && !Sticker.CheckIfApproved(w)).Select(s => s.Id).ToArray();
                var towedStickerIds = Stickers.Where(w => (w.StatusId == StickerStatus.Towed.Id || w.StatusId == StickerStatus.Converted.Id) && w.DispatchEntryId.HasValue).Select(x => x.Id).ToArray();
                var otherStickerIds = Stickers.Where(w => !needApprovalStickerIds.Contains(w.Id) && !towedStickerIds.Contains(w.Id)).Select(x => x.Id).ToArray();

                var subject = "Stickered Vehicle 24 Hour Activity Report";
                if (needApprovalStickerIds.Any())
                    subject += " - Action Required";

                var account = await Accounts.Account.GetByIdAsync(AccountId);
                var cc = await Company.Company.GetByIdAsync(this.CompanyId);
                var allReasons = Stickering.Reason.GetByStickerIds(Stickers.Select(a => a.Id).ToArray());

                foreach (var dest in users)
                {
                    if (string.IsNullOrEmpty(dest.Email))
                        continue;

                    using (var message = new MailMessage())
                    {

                        message.From = sender;
                        message.To.Add(dest.Email);
                        message.Bcc.Add("<EMAIL>");

                        message.Subject = subject;

                        message.Body = dest.FullName + ",\n\n";

                        message.Body += "In the last 24 hours, we stickered " + Stickers.Count + (Stickers.Count > 1 ? " vehicles" : " vehicle") + " at the property" +
                            (account.Address.Length > 3 ? " located at " + account.Address + "." : ".") + " Please review your summary below.\n\n";

                        // Need Approval
                        if (needApprovalStickerIds.Any()) {

                            message.Body += "https://app.towbook.com/stickering/#" + "\n\n";
                            message.Body += "The following vehicles need your approval\n";
                            message.Body += "--------------------------------------------------------\n\n";
                            foreach (var s in Stickers.Where(w => needApprovalStickerIds.Contains(w.Id)))
                            {
                                var vehicle = $"{s.VehicleYear} {s.VehicleMake} {s.VehicleModel}";
                                var plate = $"{s.LicenseNumber} {s.LicenseState}";
                                var reasons = string.Join(", ", allReasons[s.Id].Select(a => a.Name));

                                message.Body += $"Sticker # {s.StickerNumber} - NEEDS APPROVAL\nVehicle: {vehicle} {plate}\nReason(s): {reasons}\n\n";
                            }

                            message.Body += "\n";
                        }

                        // Towed/Removed
                        if (towedStickerIds.Any())
                        {
                            message.Body += "The following vehicles were removed from the property\n";
                            message.Body += "---------------------------------------------------------------------------\n\n";
                            foreach (var s in Stickers.Where(w => towedStickerIds.Contains(w.Id)))
                            {
                                var vehicle = $"{s.VehicleYear} {s.VehicleMake} {s.VehicleModel}";
                                var plate = $"{s.LicenseNumber} {s.LicenseState}";
                                var reasons = string.Join(",", allReasons[s.Id].Select(a => a.Name));
                                var entry = Dispatch.Entry.GetById(s.DispatchEntryId.GetValueOrDefault());
                                var d = entry != null ? "on " + Core.OffsetDateTime(cc, entry.CreateDate).ToString("MM/dd/yyyy h:mm tt") : "";

                                message.Body += $"Sticker # {s.StickerNumber}\nREMOVED {d}\nVehicle: {vehicle} {plate}\nReason(s): {reasons}\n\n";
                            }

                            message.Body += "\n";
                        }

                        // all others (Rejected, Resolved, Approved, Expired)
                        if (otherStickerIds.Any())
                        {
                            message.Body += "All other activity\n";
                            message.Body += "----------------------\n\n";
                            foreach (var s in Stickers.Where(w => otherStickerIds.Contains(w.Id)))
                            {
                                var vehicle = $"{s.VehicleYear} {s.VehicleMake} {s.VehicleModel}";
                                var plate = $"{s.LicenseNumber} {s.LicenseState}";
                                var reasons = string.Join(",", allReasons[s.Id].Select(a => a.Name));
                                var status = GetStatusName(s, cc);

                                message.Body += $"Sticker # {s.StickerNumber}\n{status}\nVehicle: {vehicle} {plate}\nReason(s): {reasons}\n\n";
                            }

                            message.Body += "\n\n";
                        }

                        message.Body += cc.Name + "\n";
                        message.Body += Core.FormatPhone(cc.Phone) + "\n\n";

                        message.Body += "Sent using Towbook";

                        m.Send(message);
                    }
                }

                logger.LogEvent($"DailyActivityNotification", account.CompanyId, LogLevel.Info, new Dictionary<object, object>
                {
                    ["UserIds"] = users.Where(w => !string.IsNullOrEmpty(w.Email)).Select(s => s.Id).ToArray(),
                    ["UserEmails"] = users.Where(w => !string.IsNullOrEmpty(w.Email)).Select(s => s.Email).ToArray(),
                    ["StickerIdsLast24Hours"] = Stickers.Select(s => s.Id).ToArray(),
                    ["needApprovalStickerIds"] = needApprovalStickerIds.ToArray(),
                    ["towedStickerIds"] = towedStickerIds.ToArray(),
                    ["otherStickerIds"] = otherStickerIds.ToArray(),
                });

            }
        }

        private static string GetStatusName(Sticker s, Company.Company c)
        {
            if (s.StatusId == StickerStatus.Expired.Id)
            {
                var d = s.ExpirationDate != null ? "on " + Core.OffsetDateTime(c, s.ExpirationDate.Value).ToString("MM/dd/yyyy h:mm tt") : "";
                return $"STICKER EXPIRED {d}";
            }

            if (s.StatusId == StickerStatus.Resolved.Id)
            {
                var d = s.ResolvedDate != null ? "on " + Core.OffsetDateTime(c, s.ResolvedDate.Value).ToString("MM/dd/yyyy h:mm tt") : "";
                return $"RESOLVED {d}";
            }

            if (s.StatusId == StickerStatus.Rejected.Id)
            {
                var d = s.RejectedDate != null ? "on " + Core.OffsetDateTime(c, s.RejectedDate.Value).ToString("MM/dd/yyyy h:mm tt") : "";
                return $"REJECTED {d}";
            }

            if (s.StatusId == StickerStatus.Approved.Id)
            {
                var d = s.AuthorizationDate != null ? "on " + Core.OffsetDateTime(c, s.AuthorizationDate.Value).ToString("MM/dd/yyyy h:mm tt") : "";
                return $"APPROVED {d}";
            }

            if (s.StatusId == StickerStatus.Towable.Id)
            {
                var waitEndDate = s.ExtendedExpirationDate ?? s.GracePeriodExpirationDate;
                var towableDate = waitEndDate;

                if(s.AuthorizationDate != null && waitEndDate != null)
                    towableDate = new DateTime(Math.Min(s.AuthorizationDate.Value.Ticks, waitEndDate.Value.Ticks));

                var d = towableDate != null ? "on " + Core.OffsetDateTime(c, towableDate.Value).ToString("MM/dd/yyyy h:mm tt") : "";
                return $"READY FOR REMOVAL {d}";
            }

            return string.Empty;
        }
    }
}
