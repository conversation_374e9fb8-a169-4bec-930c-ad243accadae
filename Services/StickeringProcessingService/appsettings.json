{"ConnectionStrings": {"Database": "", "Database.Azure": "", "Microsoft.ServiceBus": "", "Microsoft.ServiceBusDev": ""}, "Redis": {"Servers": "", "Credentials": "", "SentinelServiceName": "", "ConnectionPoolSize": 1}, "CosmosDb": {"Url": "", "AuthKey": "", "Database": ""}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "Loggly": {"CustomerToken": "", "ApplicationName": "StickeringProcessingService", "Transport": {"EndpointHostname": "logs-01.loggly.com", "EndpointPort": 443, "LogTransport": "Https"}, "SimpleTagValue": "mojix-dev"}}