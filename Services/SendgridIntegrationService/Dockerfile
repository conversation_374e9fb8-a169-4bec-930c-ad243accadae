#See https://aka.ms/containerfastmode to understand how Visual Studio uses this Dockerfile to build your images for faster debugging.

FROM mcr.microsoft.com/dotnet/aspnet:8.0-alpine3.18 AS base
RUN apk add --no-cache tzdata
RUN apk add --no-cache icu-libs icu-data-full

ENV TZ="America/New_York"
ENV DOTNET_SYSTEM_GLOBALIZATION_INVARIANT=false\
    # Set the locale
    LC_ALL=en_US.UTF-8 \
    LANG=en_US.UTF-8

ARG COMMIT
RUN if [ -z "$COMMIT" ]; then echo "COMMIT is empty"; exit 1; else : echo "commitId: $COMMIT" ; fi

RUN apk update && apk upgrade

WORKDIR /app

FROM mcr.microsoft.com/dotnet/sdk:8.0-alpine3.18 AS build
ARG COMMIT
WORKDIR /src
COPY ["Services/SendgridIntegrationService/SendgridIntegrationService.csproj", "Services/SendgridIntegrationService/"]
COPY ["Extric.Towbook/Extric.Towbook.csproj", "Extric.Towbook/"]
COPY ["Glav.CacheAdapter/Glav.CacheAdapter.csproj", "Glav.CacheAdapter/"]
COPY ["Extric.Towbook.WebWrapper/Extric.Towbook.WebWrapper.csproj", "Extric.Towbook.WebWrapper/"]
COPY ["Extric.Towbook.Storage/Extric.Towbook.Storage.csproj", "Extric.Towbook.Storage/"]
COPY ["Extric.Towbook.Generated.Features/Extric.Towbook.Generated.Features.csproj", "Extric.Towbook.Generated.Features/"]
COPY ["Extric.Towbook.DynamicFeatureBuilder/Extric.Towbook.DynamicFeatureBuilder.csproj", "Extric.Towbook.DynamicFeatureBuilder/"]
RUN dotnet restore "Services/SendgridIntegrationService/SendgridIntegrationService.csproj"
COPY . .
RUN echo $COMMIT > Extric.Towbook/_git_commit.txt
WORKDIR "/src/Services/SendgridIntegrationService"
RUN dotnet build "SendgridIntegrationService.csproj" -c Release -o /app/build

FROM build AS publish
ARG COMMIT
RUN dotnet publish "SendgridIntegrationService.csproj" -c Release -o /app/publish /p:AssemblyVersion=1.0.0.0 /p:Version=1.0.0.0-$COMMIT

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .

#copy openssl.cnf for alpine
COPY --from=publish /app/publish/openssl.cnf /etc/ssl
RUN rm ./openssl.cnf

RUN apk add icu-libs

ENTRYPOINT ["dotnet", "SendgridIntegrationService.dll"]