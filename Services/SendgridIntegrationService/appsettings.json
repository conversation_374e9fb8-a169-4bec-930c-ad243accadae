{"ConnectionStrings": {"Database": "", "Database.Azure": "", "Microsoft.ServiceBus": ""}, "Redis": {"Servers": "", "Credentials": "", "SentinelServiceName": "", "ConnectionPoolSize": 1}, "CosmosDb": {"Url": "", "AuthKey": "", "Database": ""}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "Loggly": {"CustomerToken": "", "ApplicationName": "sendgridintegrationsvc", "Transport": {"EndpointHostname": "logs-01.loggly.com", "EndpointPort": 6514, "LogTransport": "SyslogSecure"}, "SimpleTagValue": "mojix-dev"}}