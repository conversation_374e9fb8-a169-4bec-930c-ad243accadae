<?xml version="1.0" encoding="utf-8" ?>
<nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" throwExceptions="true" autoReload="true">

  <extensions>
    <add assembly="NLog.Targets.Loggly" /> 
  </extensions>

  <targets>
    <target name="loggly" xsi:type="Loggly" layout="${machinename} ${message}" />
    <target name="errorDatabaseLog" xsi:type="ColoredConsole" layout="${machinename} ${message}" />
    <target name="infoDatabaseLog" xsi:type="ColoredConsole" layout="${machinename} ${message}" />
    <target name="traceDatabaseLog" xsi:type="ColoredConsole" layout="${machinename} ${message}" />
  </targets>

  <rules>
    <logger name="*" minlevel="Trace" writeTo="loggly" />
    <logger name="*" minLevel="Error" writeTo="errorDatabaseLog" />
    <logger name="*" minLevel="Info" maxLevel="Warn" writeTo="infoDatabaseLog" />
    <logger name="*" minLevel="Trace" maxLevel="Debug" writeTo="traceDatabaseLog" />
  </rules>

</nlog>