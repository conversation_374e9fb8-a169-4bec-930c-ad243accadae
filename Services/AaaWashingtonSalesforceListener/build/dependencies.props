<Project>
  <PropertyGroup>
    <MSBuildAllProjects>$(MSBuildAllProjects);$(MSBuildThisFileFullPath)</MSBuildAllProjects>
  </PropertyGroup>

  <PropertyGroup>
    <ExtensionsVersion>8.0.0</ExtensionsVersion>
    <BetCommonVersion>4.0.1</BetCommonVersion>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Update="Microsoft.Extensions.Configuration.CommandLine" Version="$(ExtensionsVersion)" />
    <PackageReference Update="Microsoft.Extensions.Configuration.EnvironmentVariables" Version="$(ExtensionsVersion)" />
    <PackageReference Update="Microsoft.Extensions.Configuration.Json" Version="$(ExtensionsVersion)" />
    <PackageReference Update="Microsoft.Extensions.Hosting" Version="$(ExtensionsVersion)" />
  </ItemGroup>

  <ItemGroup Label="Bet">
    <PackageReference Update="Bet.Extensions.Options" Version="$(BetCommonVersion)" />
    <PackageReference Update="Bet.Extensions.Hosting" Version="$(BetCommonVersion)" />
    <PackageReference Update="Bet.Extensions.AzureVault" Version="$(BetCommonVersion)" />
    <PackageReference Update="Bet.Extensions.Logging" Version="$(BetCommonVersion)" />
  </ItemGroup>

  <ItemGroup Label="Shared Libraries">
    <PackageReference Update="CometD.NetCore.Salesforce" Version="3.0.5" />
    <PackageReference Update="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.9.5" />
  </ItemGroup>
</Project>
