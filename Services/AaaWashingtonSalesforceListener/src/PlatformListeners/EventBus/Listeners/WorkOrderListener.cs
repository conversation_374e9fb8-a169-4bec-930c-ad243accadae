using System;
using System.IO;
using System.Threading.Tasks;

using Azure.Messaging.ServiceBus;

using Bet.Salesforce.TestApp.EventBus.Messages;

using CometD.NetCore.Bayeux;
using CometD.NetCore.Bayeux.Client;
using CometD.NetCore.Salesforce.Messaging;

using Microsoft.Extensions.Logging;

using Newtonsoft.Json;

namespace PlatformListeners.EventBus.Listeners
{
    public sealed class WorkOrderListener : IMessageListener, IAsyncDisposable
    {
        private readonly ILogger<WorkOrderListener> _logger;
        private readonly IServiceProvider _serviceProvider;
        private readonly ServiceBusClient _serviceBusClient;
        private readonly ServiceBusSender _serviceBusSender;

        public WorkOrderListener(
            ILogger<WorkOrderListener> logger,
            ServiceBusClient serviceBusClient,
            IServiceProvider serviceProvider)
        {
            _logger = logger;
            _serviceProvider = serviceProvider;
            _serviceBusClient = serviceBusClient;
            _serviceBusSender = serviceBusClient.CreateSender("mc-dispatch-inbound");
        }

        public async ValueTask DisposeAsync()
        {
            await _serviceBusSender.DisposeAsync().ConfigureAwait(false);

            // Suppress finalization.
            GC.SuppressFinalize(this);
        }

        public async void OnMessage(IClientSessionChannel channel, IMessage message)
        {
            var msg = JsonConvert.DeserializeObject<MessageEnvelope<WorkOrder>>(message.Json);
            var replayId = msg.Data.Event.ReplayId;

            Console.WriteLine(msg.Data.Payload!.FormatJsonIndented());

            _logger.LogDebug($"{nameof(WorkOrder)} {replayId} payload: {msg.Data.Payload?.FormatJsonIndented()}");

            var formattedJson = msg.Data.FormatJsonIndented();

            File.WriteAllText(
                $"C:\\temp\\aaa-washington\\{replayId}-{msg.Channel.Replace("/", "-")}.json",
                formattedJson);
            
            // appointmentNumber__c is our PO number identifier
            // workorderId__c is needed for all status updates etc.

            Console.WriteLine(msg.Data.Payload.FacilityId + "-" + msg.Data.Payload.FacilityName);

            var aaaContractor = await AaaContractor.GetByContractorId(msg.Data.Payload.FacilityId, Core.EnvironmentId); // dev=1, uat=2, 3=prod... 45=ACG

            if (aaaContractor == null)
            {
                Console.WriteLine("UNKNOWN ContractorId:" + msg.Data.Payload.FacilityId);

                if (aaaContractor == null)
                {
                	return;
            	}
            }

            Console.WriteLine("WE HAVE A CONTRACTOR!!!" + aaaContractor.FormatJsonIndented());

            var eventType = DigitalDispatchActionQueueItemType.IncomingCallReceived;

            if (msg.Data.Payload.EventType == "callUpdate")
            {
                eventType = DigitalDispatchActionQueueItemType.IncomingCallUpdate;
            }

            var queueItem = await DigitalDispatchActionQueueItem.CreateQueuedItem(
                formattedJson,
                aaaContractor,
                eventType);

            _logger.LogDebug($"published {queueItem.QueueItemId} ({msg.Data.Payload.EventType}) for replayId {replayId}: {queueItem}");

            await _serviceBusSender.SendMessage(queueItem, msg.Data.Event.EventUuid, replayId);
        }
    }
}
