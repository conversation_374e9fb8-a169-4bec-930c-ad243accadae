using System;

using Bet.BuildingBlocks.Abstractions;
using Bet.BuildingBlocks.SalesforceEventBus;

using CometD.NetCore.Salesforce;

namespace Microsoft.Extensions.DependencyInjection
{
    /// <summary>
    /// An extension method for <see cref="EventBus"/>.
    /// </summary>
    public static class SalesforceEventBusExtensions
    {
        /// <summary>
        /// Registers <see cref="EventBus"/> and its dependencies from <see cref="StreamingClientExtensions"/>.
        /// </summary>
        /// <param name="services"></param>
        /// <param name="sectionName"></param>
        /// <returns></returns>
        public static IServiceCollection AddSalesforceEventBus(
            this IServiceCollection services,
            string sectionName = "Salesforce",
            string environmentName = "dev",
            string clientId = "",
            string clientSecret = "",
            string username = "",
            string instanceUrl = "",
            string privateKey = "")
        {
            services.AddResilientStreamingClient2(sectionName, environment: environmentName,
                clientId: clientId,
                clientSecret: clientSecret,
                username: username,
                instanceUrl: instanceUrl,
                privateKey: privateKey);
            services.AddSingleton<IEventBus, EventBus>();

            return services;
        }
    }
}
