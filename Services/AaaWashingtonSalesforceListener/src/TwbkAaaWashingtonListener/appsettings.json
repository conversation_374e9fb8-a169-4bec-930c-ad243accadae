{
  "AzureVault": {
    "BaseUrl": "https://{name}.vault.azure.net/"
  },
  "Logging": {
    "Debug": {
      "LogLevel": {
        "Default": "Debug",
        "System": "Warning",
        "Microsoft": "Warning",
        "SetMinimumLevel": "Debug"
      }
    },
    "Console": {
      "LogLevel": {
        "Default": "Debug",
        "System": "Warning",
        "Microsoft": "Warning",
        "SetMinimumLevel": "Debug"
      }
    }
  },
  "AllowedHosts": "*",
  "ConnectionStrings": {
    "ServiceBus": "Endpoint=sb://dev-towbook.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=b55hjowoy1/BIrVRgBY1LOXpN2TT/QuSh3Jp8zCMykc=",
    "IbmSql": "Data Source=agtwbk.towbook.private;Initial Catalog=Towbook;Integrated Security=False;User Id=TowbookPublic;Password=****************;Encrypt=false;MultiSubnetFailover=true;",
  },
  "Salesforce": {
    "ClientId": "3MVG9Vd.4h6M41EQVJ79ljNuff3fVELraJw5URI7KQ0_M6T.R87ujhSIvkg2LtdTI0S6Ps8GLCUgEm9a6N_Xf",
    "ClientSecret": "****************************************************************",
    "RefreshToken": "",
    "AccessToken": "",
    "RedirectUri": "http://localhost:5050/",
    "OrganizationUrl": "",
    "LoginUrl": "https://aaawa--uat.sandbox.my.salesforce.com",
    "OAuthUri": "/services/oauth2/token",
    "EventOrTopicUri": "/event",
    "CometDUri": "/cometd/57.0",
    "Retry": 2,
    "CustomEvent": "Custom_Event__e",
    "ReplayId": "-1"
  },
  "Towbook": {
    "Environment": "uat",
    "InstanceUrl": "https://aaawa--uat.sandbox.my.salesforce.com",
    "ClientId": "3MVG9Vd.4h6M41EQVJ79ljNuff3fVELraJw5URI7KQ0_M6T.R87ujhSIvkg2LtdTI0S6Ps8GLCUgEm9a6N_Xf",
    "ClientSecret": "****************************************************************",
    "MasterAccountId": 50
  }
}
