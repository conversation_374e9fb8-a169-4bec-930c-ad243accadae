<Project Sdk="Microsoft.NET.Sdk.Worker">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <IsPackable>false</IsPackable>
    <NoWarn>$(NoWarn);</NoWarn>

    <UserSecretsId>fe93e44c-8e04-45dd-86d9-a444c107a324</UserSecretsId>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    <DockerfileContext>..\..\..</DockerfileContext>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Azure" Version="1.7.5" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="8.0.0" />
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.21.0" />

    <PackageReference Include="Bet.Extensions.Options" />
    <PackageReference Include="Bet.Extensions.Hosting" />
    <PackageReference Include="Bet.Extensions.AzureVault" />
    <PackageReference Include="Bet.Extensions.Logging" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\PlatformListeners\PlatformListeners.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Update="Bet.CodeAnalyzers" Version="1.0.12" />
  </ItemGroup>
</Project>
