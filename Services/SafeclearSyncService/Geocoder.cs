using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Net;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;

namespace SafeclearSyncService;

internal class Geocoder
{
    static Dictionary<string, Extric.Towbook.Utility.LocationModel> cache = new Dictionary<string, Extric.Towbook.Utility.LocationModel>();

    public static async Task<Extric.Towbook.Utility.LocationModel> Geocode(string address)
    {
        if (cache.ContainsKey(address))
            return cache[address];

        var gc = await Extric.Towbook.Utility.GeocodeHelper.Geocode(address);

        cache.Add(address, gc);

        return gc;
    }


    public struct GoogleSignedUrl
    {

        public static string Sign(string url, string keyString)
        {
            ASCIIEncoding encoding = new ASCIIEncoding();

            // converting key to bytes will throw an exception, need to replace '-' and '_' characters first.
            string usablePrivateKey = keyString.Replace("-", "+").Replace("_", "/");
            byte[] privateKeyBytes = Convert.FromBase64String(usablePrivateKey);

            Uri uri = new Uri(url);
            byte[] encodedPathAndQueryBytes = encoding.GetBytes(uri.LocalPath + uri.Query);

            // compute the hash
            HMACSHA1 algorithm = new HMACSHA1(privateKeyBytes);
            byte[] hash = algorithm.ComputeHash(encodedPathAndQueryBytes);

            // convert the bytes to string and make url-safe by replacing '+' and '/' characters
            string signature = Convert.ToBase64String(hash).Replace("+", "-").Replace("/", "_");

            // Add the signature to the existing URI.
            return uri.Scheme + "://" + uri.Host + uri.LocalPath + uri.Query + "&signature=" + signature;
        }
    }

    private static readonly JsonSerializer serializer = new JsonSerializer();
    public static dynamic MakeRequest(string requestUrl)
    {
        for (int retries = 0; retries < 3; retries++)
        {
            try
            {
                HttpWebRequest request = WebRequest.Create(requestUrl) as HttpWebRequest;

                using (HttpWebResponse response = request.GetResponse() as HttpWebResponse)
                {
                    if (response.StatusCode != HttpStatusCode.OK)
                        throw new Exception(String.Format(
                        "Server error (HTTP {0}: {1}).",
                        response.StatusCode,
                        response.StatusDescription));

                    using (var s = response.GetResponseStream())
                    {
                        string json = string.Empty;

                        using (StreamReader sr = new StreamReader(s, Encoding.GetEncoding("utf-8")))
                        {
                            json = sr.ReadToEnd();
                        }
                       
                        return serializer.Deserialize(new JsonTextReader(new StringReader(json)));
                    }
                }
            }
            catch (WebException)
            {
                System.Threading.Thread.Sleep(100);
                // automatically retry 
                continue;
            }
        }
        return null;
    }
}
