apiVersion: apps/v1
kind: Deployment
metadata:
  name: automatedmessagesservice
spec:
  selector:
    matchLabels:
      app: automatedmessagesservice-pod
  template:
    metadata:
      labels:
        app: automatedmessagesservice-pod
    spec:
      containers:
      - name: automatedmessagesservice-container
        image: towbookapiregistry.azurecr.io/automatedmessagesservice:latest
        resources:
         limits:
           memory: "250Mi"
           cpu: "100m"
        env:
        - name: ASPNETCORE_ENVIRONMENT
          value: "Development"