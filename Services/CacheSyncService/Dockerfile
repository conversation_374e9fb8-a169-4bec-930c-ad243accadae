#See https://aka.ms/containerfastmode to understand how Visual Studio uses this Dockerfile to build your images for faster debugging.

FROM mcr.microsoft.com/dotnet/aspnet:8.0-alpine3.18 AS base
RUN apk add --no-cache tzdata
RUN apk add --no-cache icu-libs icu-data-full

ENV TZ="America/New_York"
ENV DOTNET_SYSTEM_GLOBALIZATION_INVARIANT=false\
    # Set the locale
    LC_ALL=en_US.UTF-8 \
    LANG=en_US.UTF-8

ARG COMMIT
RUN if [ -z "$COMMIT" ]; then echo "COMMIT is empty"; exit 1; else : echo "commitId: $COMMIT" ; fi

RUN apk update && apk upgrade

# install NewRelic agent
ARG NEW_RELIC_APP_NAME=CacheSyncService
ARG CORECLR_ENABLE_PROFILING=0
ARG CORECLR_PROFILER={36032161-FFC0-4B61-B559-F6C5D41BAE5A}
ARG NEW_RELIC_LICENSE_KEY=NEW_RELIC_LICENSE_VALUE

RUN echo "NewRelic AppName: $NEW_RELIC_APP_NAME"

RUN  mkdir /tmp/newrelic-dotnet-agent \
&& cd /tmp \
&& export NEW_RELIC_DOWNLOAD_URI=https://download.newrelic.com/$(wget -qO - "https://nr-downloads-main.s3.amazonaws.com/?delimiter=/&prefix=dot_net_agent/latest_release/newrelic-dotnet-agent" |  \
     grep -E -o "dot_net_agent/latest_release/newrelic-dotnet-agent_[[:digit:]]{1,3}(\.[[:digit:]]{1,3}){2}_amd64\.tar\.gz") \
&& echo "Downloading: $NEW_RELIC_DOWNLOAD_URI into $(pwd)" \
&& wget -O - "$NEW_RELIC_DOWNLOAD_URI" | gzip -dc | tar xf - 

ENV CORECLR_ENABLE_PROFILING=$CORECLR_ENABLE_PROFILING \
CORECLR_PROFILER=$CORECLR_PROFILER \
CORECLR_NEWRELIC_HOME=/tmp/newrelic-dotnet-agent \
CORECLR_PROFILER_PATH=/tmp/newrelic-dotnet-agent/libNewRelicProfiler.so \
NEW_RELIC_LICENSE_KEY=$NEW_RELIC_LICENSE_KEY \
NEW_RELIC_APPLICATION_LOGGING_ENABLED=true \
NEW_RELIC_APPLICATION_LOGGING_METRICS_ENABLED=true \
NEW_RELIC_APPLICATION_LOGGING_FORWARDING_ENABLED=true \
NEW_RELIC_APP_NAME=$NEW_RELIC_APP_NAME

WORKDIR /app

FROM --platform=$BUILDPLATFORM mcr.microsoft.com/dotnet/sdk:8.0-alpine3.18 AS build
ARG TARGETARCH
ARG BUILDPLATFORM
ARG COMMIT

WORKDIR /src
COPY ["Services/CacheSyncService/CacheSyncService.csproj", "Services/CacheSyncService/"]
COPY ["Extric.Towbook/Extric.Towbook.csproj", "Extric.Towbook/"]
COPY ["Glav.CacheAdapter/Glav.CacheAdapter.csproj", "Glav.CacheAdapter/"]
COPY ["Extric.Towbook.WebWrapper/Extric.Towbook.WebWrapper.csproj", "Extric.Towbook.WebWrapper/"]
COPY ["Extric.Towbook.Storage/Extric.Towbook.Storage.csproj", "Extric.Towbook.Storage/"]
COPY ["Extric.Towbook.Generated.Features/Extric.Towbook.Generated.Features.csproj", "Extric.Towbook.Generated.Features/"]
COPY ["Extric.Towbook.DynamicFeatureBuilder/Extric.Towbook.DynamicFeatureBuilder.csproj", "Extric.Towbook.DynamicFeatureBuilder/"]
COPY ["Extric.Towbook.API/Extric.Towbook.API.csproj", "Extric.Towbook.API/"]
COPY ["Integrations/MotorClubs/SykesReceiverApi/SykesReceiverApi.csproj", "Integrations/MotorClubs/SykesReceiverApi/"]
COPY ["Integrations/MotorClubs/Sykes/Sykes.csproj", "Integrations/MotorClubs/Sykes/"]
COPY ["Integrations/MotorClubs/Allstate/Allstate.csproj", "Integrations/MotorClubs/Allstate/"]
COPY ["Extric.Towbook.Integration.MotorClubs/Extric.Towbook.Integration.MotorClubs.csproj", "Extric.Towbook.Integration.MotorClubs/"]
COPY ["Integrations/MotorClubs/Fleetnet/Fleetnet.csproj", "Integrations/MotorClubs/Fleetnet/"]
COPY ["Integrations/MotorClubs/Agero/Agero.csproj", "Integrations/MotorClubs/Agero/"]
COPY ["Extric.Towbook.WebShared.Net5/Extric.Towbook.WebShared.Net5.csproj", "Extric.Towbook.WebShared.Net5/"]
COPY ["Extric.Towbook.Integrations.Faxing/Extric.Towbook.Integrations.Faxing.csproj", "Extric.Towbook.Integrations.Faxing/"]
COPY ["Integrations/MotorClubs/OON/OonAgeroReceiverApi/OonAgeroReceiverApi.csproj", "Integrations/MotorClubs/OON/OonAgeroReceiverApi/"]
COPY ["Integrations/MotorClubs/OON/OonAgeroClient/OonAgeroClient.csproj", "Integrations/MotorClubs/OON/OonAgeroClient/"]
COPY ["Extric.Towbook.API.Integration.Internal.Providers.Braintree/Extric.Towbook.API.Integration.Internal.Providers.Braintree.csproj", "Extric.Towbook.API.Integration.Internal.Providers.Braintree/"]
COPY ["Integrations/GPS/Extric.Towbook.Integrations.TomTom/Extric.Towbook.Integrations.TomTom.csproj", "Integrations/GPS/Extric.Towbook.Integrations.TomTom/"]
COPY ["Roadside/Extric.Roadside.API/Extric.Roadside.API.csproj", "Roadside/Extric.Roadside.API/"]
COPY ["Roadside/Extric.Roadside/Extric.Roadside.csproj", "Roadside/Extric.Roadside/"]
COPY ["Extric.Towbook.WebShared/Extric.Towbook.WebShared.csproj", "Extric.Towbook.WebShared/"]
COPY ["Integrations/GPS/Extric.Towbook.API.Integration.GPS.Providers.Networkfleet/Extric.Towbook.API.Integration.GPS.Providers.Networkfleet.csproj", "Integrations/GPS/Extric.Towbook.API.Integration.GPS.Providers.Networkfleet/"]
COPY ["Integrations/GPS/Extric.Towbook.Integrations.Networkfleet/Extric.Towbook.Integrations.Networkfleet.csproj", "Integrations/GPS/Extric.Towbook.Integrations.Networkfleet/"]
COPY ["Integrations/GPS/Extric.Towbook.API.Integration.GPS.Providers.GeoTab/Extric.Towbook.API.Integration.GPS.Providers.GeoTab.csproj", "Integrations/GPS/Extric.Towbook.API.Integration.GPS.Providers.GeoTab/"]
COPY ["Integrations/GPS/Extric.Towbook.Integrations.GeoTab/Extric.Towbook.Integrations.GeoTab.csproj", "Integrations/GPS/Extric.Towbook.Integrations.GeoTab/"]
COPY ["Integrations/vNext/Extric.Towbook.API.Integrations.Accounting.Providers.QuickBooks/Extric.Towbook.API.Integration.Accounting.Providers.QuickBooks.csproj", "Integrations/vNext/Extric.Towbook.API.Integrations.Accounting.Providers.QuickBooks/"]
COPY ["Integrations/vNext/Extric.Towbook.Integrations.Quickbooks/Extric.Towbook.Integrations.Quickbooks.csproj", "Integrations/vNext/Extric.Towbook.Integrations.Quickbooks/"]
COPY ["Integrations/Square/Square/Extric.Towbook.Square.csproj", "Integrations/Square/Square/"]
COPY ["Extric.Towbook.DirectBilling.API/Extric.Towbook.DirectBilling.API.csproj", "Extric.Towbook.DirectBilling.API/"]
COPY ["Extric.Towbook.PermitRequests.API/Extric.Towbook.PermitRequests.API.csproj", "Extric.Towbook.PermitRequests.API/"]
COPY ["Integrations/MotorClubs/FleetnetReceiverApi/FleetnetReceiverApi.csproj", "Integrations/MotorClubs/FleetnetReceiverApi/"]
COPY ["Integrations/MotorClubs/NacReceiverApi/NacReceiverApi.csproj", "Integrations/MotorClubs/NacReceiverApi/"]
COPY ["Integrations/MotorClubs/Gerber/Gerber.csproj", "Integrations/MotorClubs/Gerber/"]
COPY ["Integrations/Square/SquareApi/Extric.Towbook.API.Integration.Square.csproj", "Integrations/Square/SquareApi/"]
COPY ["Integrations/MotorClubs/Quest/Quest.csproj", "Integrations/MotorClubs/Quest/"]
COPY ["Extric.Towbook.Integrations.Email/Extric.Towbook.Integrations.Email.csproj", "Extric.Towbook.Integrations.Email/"]
COPY ["Integrations/MotorClubs/TowbookApi/TowbookReceiverApi.csproj", "Integrations/MotorClubs/TowbookApi/"]
COPY ["Integrations/GPS/Extric.Towbook.API.Integration.GPS.Providers.Fleetmatics/Extric.Towbook.API.Integration.GPS.Providers.Fleetmatics.csproj", "Integrations/GPS/Extric.Towbook.API.Integration.GPS.Providers.Fleetmatics/"]
COPY ["Integrations/MotorClubs/Swoop/Swoop.csproj", "Integrations/MotorClubs/Swoop/"]
COPY ["Services/SquareService/SquareService.csproj", "Services/SquareService/"]
COPY ["Integrations/MotorClubs/Urgently/Urgently.csproj", "Integrations/MotorClubs/Urgently/"]
COPY ["Extric.Towbook.Agent.API/Extric.Towbook.Agent.API.csproj", "Extric.Towbook.Agent.API/"]
COPY ["Integrations/Data/Extric.Towbook.API.Integration.Data.Providers.AutoData/Extric.Towbook.API.Integration.Data.Providers.AutoData.csproj", "Integrations/Data/Extric.Towbook.API.Integration.Data.Providers.AutoData/"]
COPY ["Integrations/Data/Extric.Towbook.Integrations.AutoData/Extric.Towbook.Integrations.AutoData.csproj", "Integrations/Data/Extric.Towbook.Integrations.AutoData/"]
COPY ["Integrations/MotorClubs/QuestReceiverApi/QuestReceiverApi.csproj", "Integrations/MotorClubs/QuestReceiverApi/"]
COPY ["Integrations/MotorClubs/AaaReceiverApi/AaaReceiverApi.csproj", "Integrations/MotorClubs/AaaReceiverApi/"]
COPY ["Integrations/MotorClubs/SwoopReceiverApi/SwoopReceiverApi.csproj", "Integrations/MotorClubs/SwoopReceiverApi/"]
COPY ["Integrations/GPS/Extric.Towbook.API.Integration.GPS.Providers.FleetComplete/Extric.Towbook.API.Integration.GPS.Providers.FleetComplete.csproj", "Integrations/GPS/Extric.Towbook.API.Integration.GPS.Providers.FleetComplete/"]
COPY ["Integrations/GPS/Extric.Towbook.Integrations.FleetComplete/Extric.Towbook.Integrations.FleetComplete.csproj", "Integrations/GPS/Extric.Towbook.Integrations.FleetComplete/"]
COPY ["Integrations/GPS/Extric.Towbook.API.Integration.GPS.Providers.USFleetTracking/Extric.Towbook.API.Integration.GPS.Providers.USFleetTracking.csproj", "Integrations/GPS/Extric.Towbook.API.Integration.GPS.Providers.USFleetTracking/"]
COPY ["Integrations/GPS/Extric.Towbook.Integrations.USFleetTracking/Extric.Towbook.Integrations.USFleetTracking.csproj", "Integrations/GPS/Extric.Towbook.Integrations.USFleetTracking/"]
#COPY ["Extric.Towbook.API.Model/Extric.Towbook.API.Model.csproj", "Extric.Towbook.API.Model/"]
COPY ["Integrations/MotorClubs/GerberReceiverApi/GerberReceiverApi.csproj", "Integrations/MotorClubs/GerberReceiverApi/"]
COPY ["Integrations/MotorClubs/UrgentlyReceiverApi/UrgentlyReceiverApi.csproj", "Integrations/MotorClubs/UrgentlyReceiverApi/"]
COPY ["Integrations/MotorClubs/GeicoReceiverApi/IsscReceiverApi.csproj", "Integrations/MotorClubs/GeicoReceiverApi/"]
COPY ["Integrations/MotorClubs/Geico/Issc.csproj", "Integrations/MotorClubs/Geico/"]
COPY ["Integrations/MotorClubs/AgeroReceiverApi/AgeroReceiverApi.csproj", "Integrations/MotorClubs/AgeroReceiverApi/"]
COPY ["Integrations/MotorClubs/HonkReceiverApi/HonkReceiverApi.csproj", "Integrations/MotorClubs/HonkReceiverApi/"]
COPY ["Integrations/MotorClubs/Honk/Honk.csproj", "Integrations/MotorClubs/Honk/"]
COPY ["Integrations/GPS/Extric.Towbook.API.Integration.GPS.Providers.TomTom/Extric.Towbook.API.Integration.GPS.Providers.TomTom.csproj", "Integrations/GPS/Extric.Towbook.API.Integration.GPS.Providers.TomTom/"]
COPY ["Integrations/MotorClubs/AllstateReceiverApi/AllstateReceiverApi.csproj", "Integrations/MotorClubs/AllstateReceiverApi/"]
COPY ["Integrations/MotorClubs/NsdReceiverApi/NsdReceiverApi.csproj", "Integrations/MotorClubs/NsdReceiverApi/"]
COPY ["Integrations/MotorClubs/Nsd/Nsd.csproj", "Integrations/MotorClubs/Nsd/"]
COPY ["Integrations/GPS/Extric.Towbook.API.Integration.GPS.Providers.Samsara/Extric.Towbook.API.Integration.GPS.Providers.Samsara.csproj", "Integrations/GPS/Extric.Towbook.API.Integration.GPS.Providers.Samsara/"]
COPY ["Integrations/GPS/Extric.Towbook.Integrations.Samsara/Extric.Towbook.Integrations.Samsara.csproj", "Integrations/GPS/Extric.Towbook.Integrations.Samsara/"]
RUN dotnet restore "Services/CacheSyncService/CacheSyncService.csproj" -a $TARGETARCH 
COPY . .
RUN echo $COMMIT > Extric.Towbook/_git_commit.txt
WORKDIR "/src/Services/CacheSyncService"
RUN dotnet build "CacheSyncService.csproj" -o /app/build

FROM build AS publish
ARG COMMIT
RUN dotnet publish "CacheSyncService.csproj" -a $TARGETARCH -o /app/publish /p:AssemblyVersion=1.0.0.0 /p:Version=1.0.0.0-$COMMIT

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .

#copy openssl.cnf for alpine
COPY --from=publish /app/publish/openssl.cnf /etc/ssl
RUN rm ./openssl.cnf

RUN apk add icu-libs

#ENTRYPOINT ["dotnet", "CacheSyncService.dll", "/t", "10", "/dev"]
ENTRYPOINT ["dotnet", "CacheSyncService.dll", "/t", "64"]
