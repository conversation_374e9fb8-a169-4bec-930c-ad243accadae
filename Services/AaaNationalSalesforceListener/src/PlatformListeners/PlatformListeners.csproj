<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFrameworks>net8.0</TargetFrameworks>
		<Nullable>enable</Nullable>
		<IsPackable>false</IsPackable>
    <NoWarn>$(NoWarn);CS8618;SA1133;CS8602;SA1210;IDE0025;CS1591;CA1724</NoWarn>
    <GenerateDocumentationFile>False</GenerateDocumentationFile>
    <GeneratePackageOnBuild>False</GeneratePackageOnBuild>
	</PropertyGroup>

	<ItemGroup>
		<DotNetCliToolReference Include="NetCoreForce.ModelGenerator" Version="2.5.0" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="Azure.Messaging.ServiceBus" Version="7.18.2" />
		<PackageReference Include="Dapper" Version="2.1.35" />
		<PackageReference Include="Dapper.Contrib" Version="2.0.78" />
		<PackageReference Include="Microsoft.Data.SqlClient" Version="5.2.2" />
		<PackageReference Include="Microsoft.Extensions.Hosting" Version="8.0.1" />
		<PackageReference Include="SendGrid.Extensions.DependencyInjection" Version="1.0.1" />
		<PackageReference Include="StackExchange.Redis" Version="2.8.41" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\Bet.BuildingBlocks.SalesforceEventBus\SalesforceEventBus.csproj" />
	</ItemGroup>

	<ItemGroup>
	  <PackageReference Update="Bet.CodeAnalyzers" Version="1.0.12" />
	</ItemGroup>

	<ItemGroup>
	  <PackageReference Update="Bet.EditorConfig" Version="1.0.12" />
	</ItemGroup>

	<ItemGroup>
	  <PackageReference Update="Microsoft.SourceLink.GitHub" Version="1.1.1" />
	</ItemGroup>

</Project>
