using System.Threading;
using System.Threading.Tasks;

using Bet.BuildingBlocks.Abstractions;

using CometD.NetCore.Salesforce;

using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

using PlatformListeners;
using PlatformListeners.EventBus.Listeners;

namespace Bet.Salesforce.TestApp.Services;

/// <summary>
/// Provides with LifetimeEventsHostedService.
/// </summary>
public class SalesforceEventBusHostedService : IHostedService
{
    private readonly ILogger _logger;
    private readonly IHostApplicationLifetime _appLifetime;
    private readonly SalesforceConfiguration _options;
    private readonly IEventBus _eventBus;

    public SalesforceEventBusHostedService(
        ILogger<SalesforceEventBusHostedService> logger,
        IHostApplicationLifetime appLifetime,
        IOptions<SalesforceConfiguration> options,
        IEventBus eventBus)
    {
        _logger = logger;
        _appLifetime = appLifetime;
        _options = options.Value;
        _eventBus = eventBus;
    }

    public async Task StartAsync(CancellationToken cancellationToken)
    {
        _appLifetime.ApplicationStarted.Register(OnStarted);
        _appLifetime.ApplicationStopping.Register(OnStopping);
        _appLifetime.ApplicationStopped.Register(OnStopped);

        _logger.LogInformation("StartAsync has been called.");

        await _eventBus.Subscribe(
            new PlatformEvent<VehicleListener>()
            {
                Name = "Vehicle_Event__e",
                ReplayId = await Core.GetLastReplayIdAsync("Vehicle_Event__e", _options.ReplayId)
            });

        await _eventBus.Subscribe(
            new PlatformEvent<FacilityListener>()
            {
                Name = "Facility__e",
                ReplayId = await Core.GetLastReplayIdAsync("Facility__e", _options.ReplayId)
            });

        await _eventBus.Subscribe(
            new PlatformEvent<ScheduleCallListener>()
            {
                Name = "Schedule_Call__e",
                ReplayId = await Core.GetLastReplayIdAsync("Schedule_Call__e", _options.ReplayId)
            });

        await _eventBus.Subscribe(
            new PlatformEvent<UpdateCallDetailsListener>()
            {
                Name = "Update_call_details__e",
                ReplayId = await Core.GetLastReplayIdAsync("Update_call_details__e", _options.ReplayId)
            });

        await _eventBus.Subscribe(
            new PlatformEvent<CreateServiceResourceListener>()
            {
                Name = "Create_Service_Resource__e",
                ReplayId = await Core.GetLastReplayIdAsync("Create_Service_Resource__e", _options.ReplayId)
            });

        await _eventBus.Subscribe(
           new PlatformEvent<StatusUpdateServiceAppointmentListener>()
           {
               Name = "Status_Update_ServiceAppointment__e",
               ReplayId = await Core.GetLastReplayIdAsync("Status_Update_ServiceAppointment__e", _options.ReplayId)
           });
    }

    public async Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("StopAsync has been called.");

        await _eventBus.Unsubscribe<CustomMessageListener>(
            new PlatformEvent<CustomMessageListener>()
            {
                Name = _options.CustomEvent,
                ReplayId = _options.ReplayId
            });
    }

    private void OnStarted()
    {
        _logger.LogInformation("OnStarted has been called.");

        // Perform post-startup activities here
    }

    private void OnStopping()
    {
        _logger.LogInformation("OnStopping has been called.");

        // Perform on-stopping activities here
    }

    private void OnStopped()
    {
        _logger.LogInformation("OnStopped has been called.");

        // Perform post-stopped activities here
    }
}
