using Newtonsoft.Json;

namespace Bet.Salesforce.TestApp.EventBus.Messages;

public sealed class UpdateCallDetails : ScheduleCall
{
    /// <summary>
    /// What type of update is it? Examples show possible values.
    /// </summary>
    /// <remarks>
    ///     Expected one of: NotesUpdate, PhoneNumberChange, VehicleChange, TowAdressChange, BreakdownAdressChange
    /// </remarks>
    [JsonProperty("operationType__c")] public string OperationType { get; set; }
}
