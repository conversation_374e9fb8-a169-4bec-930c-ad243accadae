using CometD.NetCore.Salesforce.Messaging;

using Newtonsoft.Json;

namespace Bet.Salesforce.TestApp.EventBus.Messages;

public class VehiclePayload : MessagePayload
{
    [JsonProperty("ACG_Facility_ID__c")] public string AcgFacilityId { get; set; }
    [JsonProperty("Name__c")] public string Name { get; set; }
    [JsonProperty("Vehicle_Id__c")] public string VehicleId { get; set; }
    [JsonProperty("Facility_Id__c")] public string FacilityId { get; set; }
    [JsonProperty("Facility_Name__c")] public string FacilityName { get; set; }
    [JsonProperty("Vehicle_Type__c")] public string VehicleType { get; set; }
    public int EnvironmentId { get; set; }
}
