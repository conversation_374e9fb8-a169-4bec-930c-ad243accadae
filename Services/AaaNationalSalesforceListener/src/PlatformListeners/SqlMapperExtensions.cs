using System.IO;
using System.Runtime.Serialization;
using System;
using System.Threading.Tasks;
using Azure.Messaging.ServiceBus;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Diagnostics.CodeAnalysis;
using Newtonsoft.Json.Converters;
using Microsoft.Extensions.Configuration;
using StackExchange.Redis;

namespace PlatformListeners;

public class Core
{
    public static string ConnectionString { get; set; } = "";
    public static int EnvironmentId { get; set; }
    public static string SendgridKey { get; set; }

    private static string[] _redisServers = new string[]
       {
            "*************",
            "*************",
            "*************"
       };

    public static string[] RedisServers
    {
        get
        {
            return _redisServers;
        }
    }


    public Core()
    {

    }
    
    private static Lazy<ConnectionMultiplexer> RedisConnections = new Lazy<ConnectionMultiplexer>(() =>
        {
            var config = new ConfigurationOptions
            {
                AbortOnConnectFail = false,
                ConnectTimeout = 5000,
                SyncTimeout = 5000,
                AllowAdmin = true
            };
            foreach(var x in RedisServers)
            {
                config.EndPoints.Add(x);
            }
            return ConnectionMultiplexer.Connect(config);
        });

    public static ConnectionMultiplexer GetRedis()
    {
        return RedisConnections.Value;
    }

    public static async Task UpdateLastReplayIdAsync(string subscription, long replayId)
    {
        if (replayId < 1)
            return;
        subscription = subscription.Replace("/event/", "").ToLowerInvariant();

        Console.WriteLine("*** UPDATE " + subscription + " to " + replayId);
        var redis = GetRedis();
        // keep it alive for 4 hours. if we go longer than that, discard it so we return the defaultReplayId in the get method.
        await redis.GetDatabase().StringSetAsync("aaanat:" + Core.EnvironmentId + ":" + subscription, replayId, TimeSpan.FromHours(4));
    }

    public static async Task<long> GetLastReplayIdAsync(string subscription, long defaultReplayId = -1)
    {

        var redis = GetRedis();

        var redisValue = await redis.GetDatabase().StringGetAsync("aaanat:" + Core.EnvironmentId + ":" + subscription.ToLowerInvariant());
        var rv = redisValue.HasValue ? (long)redisValue : defaultReplayId;

        Console.WriteLine("Returning " + subscription + " " + rv + " as last replay id");
        return rv;
    }
}
public class LongNameContractResolver : DefaultContractResolver
{
    protected override IList<JsonProperty> CreateProperties(Type type, MemberSerialization memberSerialization)
    {
        var list = base.CreateProperties(type, memberSerialization);

        foreach (var prop in list)
        {
            prop.PropertyName = StringUtils.ToCamelCase(prop.UnderlyingName!);
        }

        return list;
    }
}
public class IsoDateTimeOffsetConverter : IsoDateTimeConverter
{
    public override bool CanConvert(Type objectType)
    {
        return objectType == typeof(DateTimeOffset) || objectType == typeof(DateTimeOffset?);
    }

    public override void WriteJson(JsonWriter writer, object value, JsonSerializer serializer)
    {
        var dateTimeOffset = (DateTimeOffset)value;
        if (dateTimeOffset.Offset == TimeSpan.Zero)
        {
            // If there is no offset, serialize as a DateTime
            base.WriteJson(writer, dateTimeOffset.UtcDateTime, serializer);
        }
        else
        {
            base.WriteJson(writer, value, serializer);
        }
    }
}

public static class CoreExtensions
{
    public static string ConnectionString { get; set; }

    private static JsonSerializerSettings jsonSettings = new JsonSerializerSettings()
    {
        ContractResolver = new LongNameContractResolver()
        {
            NamingStrategy = new CamelCaseNamingStrategy()
            {
                ProcessDictionaryKeys = true,
                OverrideSpecifiedNames = true
            }
        },
        DateTimeZoneHandling = DateTimeZoneHandling.Utc,
        Converters = new List<JsonConverter> { new IsoDateTimeOffsetConverter() },
        NullValueHandling = NullValueHandling.Ignore,
    };

    public static string FormatJsonIndented(this string o)
    {
        return JsonConvert.SerializeObject(JsonConvert.DeserializeObject(o),
            Formatting.Indented,
           jsonSettings);
    }
    public static string FormatJsonIndented(this object o)
    {
        return JsonConvert.SerializeObject(o,
            Formatting.Indented,
            jsonSettings);
    }


    public static async Task SendMessage(this ServiceBusSender i,
        DigitalDispatchActionQueueItem ddaq, string eventUuid)
    {

        var serializer = new DataContractSerializer(typeof(string));

        using var stream = new MemoryStream();
        using var writer = System.Xml.XmlDictionaryWriter.CreateBinaryWriter(stream);

        // serialize our JSON string into the XML envelope using the DataContractSerializer
        serializer.WriteObject(writer, JsonConvert.SerializeObject(ddaq));
        writer.Flush();

        // construct the ServiceBusMessage using the DataContract serialized JSON
        var message2 = new ServiceBusMessage(stream.ToArray());

        message2.MessageId = "DigitalDispatchService/" + ddaq.QueueItemId;
        message2.ApplicationProperties["id"] = ddaq.QueueItemId.ToString();
        message2.ApplicationProperties["requestCreated"] = DateTime.UtcNow.ToString();
        message2.ApplicationProperties["type"] = (int)ddaq.Type;
        message2.ApplicationProperties["companyId"] = ddaq.CompanyId;
        message2.ApplicationProperties["jsonObject"] = ddaq.JsonObject;
        message2.ApplicationProperties["eventUuid"] = eventUuid;
        message2.ApplicationProperties["masterAccountId"] = 47;
        message2.ApplicationProperties["environmentId"] = Core.EnvironmentId;

        if (ddaq.QueueItemId > 0)
        {
            await i.SendMessageAsync(message2);
            Console.WriteLine("sent " + ddaq.Type + " message to service bus with QueueItemId == " + ddaq.QueueItemId);
        }
        else
        {
            Console.WriteLine($"DID NOT SEND MESSAGE TO SERVICE BUS {ddaq.Type}/QueueItemId == 0");
        }
    }

}

internal static class StringUtils
{
    public const string CarriageReturnLineFeed = "\r\n";
    public const string Empty = "";
    public const char CarriageReturn = '\r';
    public const char LineFeed = '\n';
    public const char Tab = '\t';

    public static bool IsNullOrEmpty(string? value)
    {
        return string.IsNullOrEmpty(value);
    }

    public static string FormatWith(this string format, IFormatProvider provider, object? arg0)
    {
        return format.FormatWith(provider, new object?[] { arg0 });
    }

    public static string FormatWith(this string format, IFormatProvider provider, object? arg0, object? arg1)
    {
        return format.FormatWith(provider, new object?[] { arg0, arg1 });
    }

    public static string FormatWith(this string format, IFormatProvider provider, object? arg0, object? arg1, object? arg2)
    {
        return format.FormatWith(provider, new object?[] { arg0, arg1, arg2 });
    }

    public static string FormatWith(this string format, IFormatProvider provider, object? arg0, object? arg1, object? arg2, object? arg3)
    {
        return format.FormatWith(provider, new object?[] { arg0, arg1, arg2, arg3 });
    }

    private static string FormatWith(this string format, IFormatProvider provider, params object?[] args)
    {
        // leave this a private to force code to use an explicit overload
        // avoids stack memory being reserved for the object array
        ArgumentNotNull(format, nameof(format));

        return string.Format(provider, format, args);
    }
    public static void ArgumentNotNull([NotNull] object? value, string parameterName)
    {
        if (value == null)
        {
            throw new ArgumentNullException(parameterName);
        }
    }
    /// <summary>
    /// Determines whether the string is all white space. Empty string will return <c>false</c>.
    /// </summary>
    /// <param name="s">The string to test whether it is all white space.</param>
    /// <returns>
    /// 	<c>true</c> if the string is all white space; otherwise, <c>false</c>.
    /// </returns>
    public static bool IsWhiteSpace(string s)
    {
        if (s == null)
        {
            throw new ArgumentNullException(nameof(s));
        }

        if (s.Length == 0)
        {
            return false;
        }

        for (var i = 0; i < s.Length; i++)
        {
            if (!char.IsWhiteSpace(s[i]))
            {
                return false;
            }
        }

        return true;
    }

    public static TSource? ForgivingCaseSensitiveFind<TSource>(this IEnumerable<TSource> source, Func<TSource, string> valueSelector, string testValue)
    {
        if (source == null)
        {
            throw new ArgumentNullException(nameof(source));
        }
        if (valueSelector == null)
        {
            throw new ArgumentNullException(nameof(valueSelector));
        }

        var caseInsensitiveResults = source.Where(s => string.Equals(valueSelector(s), testValue, StringComparison.OrdinalIgnoreCase));
        if (caseInsensitiveResults.Count() <= 1)
        {
            return caseInsensitiveResults.SingleOrDefault();
        }
        else
        {
            // multiple results returned. now filter using case sensitivity
            var caseSensitiveResults = source.Where(s => string.Equals(valueSelector(s), testValue, StringComparison.Ordinal));
            return caseSensitiveResults.SingleOrDefault();
        }
    }

    public static string ToCamelCase(string s)
    {
        if (IsNullOrEmpty(s) || !char.IsUpper(s[0]))
        {
            return s;
        }

        var chars = s.ToCharArray();

        for (var i = 0; i < chars.Length; i++)
        {
            if (i == 1 && !char.IsUpper(chars[i]))
            {
                break;
            }

            var hasNext = i + 1 < chars.Length;
            if (i > 0 && hasNext && !char.IsUpper(chars[i + 1]))
            {
                // if the next character is a space, which is not considered uppercase 
                // (otherwise we wouldn't be here...)
                // we want to ensure that the following:
                // 'FOO bar' is rewritten as 'foo bar', and not as 'foO bar'
                // The code was written in such a way that the first word in uppercase
                // ends when if finds an uppercase letter followed by a lowercase letter.
                // now a ' ' (space, (char)32) is considered not upper
                // but in that case we still want our current character to become lowercase
                if (char.IsSeparator(chars[i + 1]))
                {
                    chars[i] = ToLower(chars[i]);
                }

                break;
            }

            chars[i] = ToLower(chars[i]);
        }

        return new string(chars);
    }

    private static char ToLower(char c)
    {
#if HAVE_CHAR_TO_LOWER_WITH_CULTURE
        c = char.ToLower(c, CultureInfo.InvariantCulture);
#else
        c = char.ToLowerInvariant(c);
#endif
        return c;
    }

    public static string ToSnakeCase(string s) => ToSeparatedCase(s, '_');

    public static string ToKebabCase(string s) => ToSeparatedCase(s, '-');

    private enum SeparatedCaseState
    {
        Start,
        Lower,
        Upper,
        NewWord
    }

    private static string ToSeparatedCase(string s, char separator)
    {
        if (IsNullOrEmpty(s))
        {
            return s;
        }

        var sb = new StringBuilder();
        var state = SeparatedCaseState.Start;

        for (var i = 0; i < s.Length; i++)
        {
            if (s[i] == ' ')
            {
                if (state != SeparatedCaseState.Start)
                {
                    state = SeparatedCaseState.NewWord;
                }
            }
            else if (char.IsUpper(s[i]))
            {
                switch (state)
                {
                    case SeparatedCaseState.Upper:
                        var hasNext = i + 1 < s.Length;
                        if (i > 0 && hasNext)
                        {
                            var nextChar = s[i + 1];
                            if (!char.IsUpper(nextChar) && nextChar != separator)
                            {
                                sb.Append(separator);
                            }
                        }
                        break;
                    case SeparatedCaseState.Lower:
                    case SeparatedCaseState.NewWord:
                        sb.Append(separator);
                        break;
                }

                char c;
#if HAVE_CHAR_TO_LOWER_WITH_CULTURE
                c = char.ToLower(s[i], CultureInfo.InvariantCulture);
#else
                c = char.ToLowerInvariant(s[i]);
#endif
                sb.Append(c);

                state = SeparatedCaseState.Upper;
            }
            else if (s[i] == separator)
            {
                sb.Append(separator);
                state = SeparatedCaseState.Start;
            }
            else
            {
                if (state == SeparatedCaseState.NewWord)
                {
                    sb.Append(separator);
                }

                sb.Append(s[i]);
                state = SeparatedCaseState.Lower;
            }
        }

        return sb.ToString();
    }

    public static bool IsHighSurrogate(char c)
    {
#if HAVE_UNICODE_SURROGATE_DETECTION
        return char.IsHighSurrogate(c);
#else
        return c >= 55296 && c <= 56319;
#endif
    }

    public static bool IsLowSurrogate(char c)
    {
#if HAVE_UNICODE_SURROGATE_DETECTION
        return char.IsLowSurrogate(c);
#else
        return c >= 56320 && c <= 57343;
#endif
    }

    public static int IndexOf(string s, char c)
    {
#if HAVE_INDEXOF_STRING_COMPARISON
        return s.IndexOf(c, StringComparison.Ordinal);
#else
        return s.IndexOf(c);
#endif
    }

    public static string Replace(string s, string oldValue, string newValue)
    {
#if HAVE_REPLACE_STRING_COMPARISON
        return s.Replace(oldValue, newValue, StringComparison.Ordinal);
#else
        return s.Replace(oldValue, newValue);
#endif
    }

    public static bool StartsWith(this string source, char value)
    {
        return source.Length > 0 && source[0] == value;
    }

    public static bool EndsWith(this string source, char value)
    {
        return source.Length > 0 && source[source.Length - 1] == value;
    }

    public static string Trim(this string s, int start, int length)
    {
        // References: https://referencesource.microsoft.com/#mscorlib/system/string.cs,2691
        // https://referencesource.microsoft.com/#mscorlib/system/string.cs,1226
        if (s == null)
        {
            throw new ArgumentNullException();
        }
        if (start < 0)
        {
            throw new ArgumentOutOfRangeException(nameof(start));
        }
        if (length < 0)
        {
            throw new ArgumentOutOfRangeException(nameof(length));
        }
        var end = start + length - 1;
        if (end >= s.Length)
        {
            throw new ArgumentOutOfRangeException(nameof(length));
        }
        for (; start < end; start++)
        {
            if (!char.IsWhiteSpace(s[start]))
            {
                break;
            }
        }
        for (; end >= start; end--)
        {
            if (!char.IsWhiteSpace(s[end]))
            {
                break;
            }
        }
        return s.Substring(start, end - start + 1);
    }
}
