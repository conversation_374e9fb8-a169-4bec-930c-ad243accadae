using System;

using CometD.NetCore.Bayeux.Client;

namespace CometD.NetCore.Salesforce
{
    /// <summary>
    /// The <see cref="IStreamingClient"/> a wrapper class around  <see cref="Bayeux"/> client.
    /// </summary>
    public interface IStreamingClient : IDisposable
    {
        /// <summary>
        /// Event handler that sends event if the client must reconnect.
        /// </summary>
        event EventHandler<bool> Reconnect;

        /// <summary>
        /// Inject a strategy for handling invalid replay ids.
        /// </summary>
        Action<int> InvalidReplayIdStrategy { get; set; }

        /// <summary>
        /// True/False if the <see cref="IStreamingClient"/> is connected.
        /// </summary>
        bool IsConnected { get; }

        System.Collections.Concurrent.ConcurrentDictionary<SubscriptionInfo, object> Subscriptions { get; set; }

        /// <summary>
        /// Connects to the Bayeux server.
        /// </summary>
        void Handshake();

        /// <summary>
        /// Connects to the Bayeux server.
        /// </summary>
        /// <param name="timeout"></param>
        void Handshake(int timeout);

        /// <summary>
        /// Disconnect Salesforce subscription to the platform events.
        /// </summary>
        void Disconnect();

        /// <summary>
        /// Disconnect Salesforce subscription to the platform events.
        /// </summary>
        /// <param name="timeout"></param>
        void Disconnect(int timeout);

        /// <summary>
        /// Subscribe to Salesforce Platform event.
        /// </summary>
        /// <param name="topicName"></param>
        /// <param name="listener"></param>
        /// <param name="replayId"></param>
        void SubscribeTopic(string topicName, IMessageListener listener, long replayId = -1);

        /// <summary>
        /// Unsubscribe from Salesforce Platform event.
        /// </summary>
        /// <param name="topicName"></param>
        /// <param name="listener"></param>
        /// <param name="replayId"></param>
        /// <returns></returns>
        bool UnsubscribeTopic(string topicName, IMessageListener? listener = null, long replayId = -1);
    }

    public class SubscriptionInfo
    {
        public SubscriptionInfo(string name, long replayId, Type handlerType)
        {
            Name = name;
            ReplayId = replayId;
            HandlerType = handlerType;
        }

        public string Name { get; }

        public long ReplayId { get; set; }

        public object HandlerType { get; }

        public bool Equals(SubscriptionInfo other)
        {
            if (other is null)
            {
                return false;
            }

            if (ReferenceEquals(this, other))
            {
                return true;
            }

            return other.Name == Name && other.ReplayId == ReplayId && other.HandlerType == HandlerType;
        }

        public override bool Equals(object obj)
        {
            if (obj is null)
            {
                return false;
            }

            if (ReferenceEquals(this, obj))
            {
                return true;
            }

            if (obj.GetType() != typeof(SubscriptionInfo))
            {
                return false;
            }

            return Equals((SubscriptionInfo)obj);
        }

        public override int GetHashCode()
        {
            var hash = 15;
            hash = (hash * 7) + Name.GetHashCode();
            hash = (hash * 7) + ReplayId.GetHashCode();
            hash = (hash * 7) + HandlerType.GetHashCode();

            return hash;
        }
    }
}
