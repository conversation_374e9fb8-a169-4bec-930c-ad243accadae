{"ConnectionStrings": {"Database": "", "Database.Azure": "", "Microsoft.ServiceBus": ""}, "Redis": {"Servers": "", "Credentials": "", "SentinelServiceName": "", "ConnectionPoolSize": 1}, "CosmosDb": {"Url": "", "AuthKey": "", "Database": ""}, "ExecutionInterval": "5000", "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "Loggly": {"CustomerToken": "", "ApplicationName": "ProcessQueueService", "Transport": {"EndpointHostname": "logs-01.loggly.com", "EndpointPort": 6514, "LogTransport": "SyslogSecure"}, "SimpleTagValue": "mojix-dev"}}