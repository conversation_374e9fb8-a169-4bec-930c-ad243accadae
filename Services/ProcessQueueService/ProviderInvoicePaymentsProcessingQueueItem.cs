using Extric.Towbook.Utility;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Extric.Towbook.Integration.ProcessQueueService
{
    public class ProviderInvoicePaymentsProcessingQueueItem
    {
        public int Id { get; protected set; }
        public int InvoicePaymentId { get; set; }
        public DateTime ProcessStart { get; set; }
        public DateTime ProcessEnd { get; set; }
        public QueueStatus Status { get; set; }

        public int CompanyId { get; set; }

        static public IEnumerable<ProviderInvoicePaymentsProcessingQueueItem> GetPaymentsToSend(int rowCount)
        {
            return new CacheCollection<ProviderInvoicePaymentsProcessingQueueItem>(Map(SqlMapper.QuerySP<dynamic>("Integration.ProviderInvoicePaymentsProcessingQueueGetToSend", new
                {
                    @RowCount = rowCount
                }))).Items;
        }

        static private IEnumerable<ProviderInvoicePaymentsProcessingQueueItem> Map(IEnumerable<dynamic> list)
        {
            return list.Select(o => new ProviderInvoicePaymentsProcessingQueueItem()
            {
                Id = o.Id,
                InvoicePaymentId = o.InvoicePaymentId,
                ProcessEnd = o.ProcessEnd,
                ProcessStart = o.ProcessStart,
                Status = (QueueStatus)o.Status
            });
        }
    }
}
