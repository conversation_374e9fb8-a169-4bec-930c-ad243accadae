<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <RootNamespace>Extric.Towbook.Test</RootNamespace>
    <AssemblyName>Extric.Towbook.Test</AssemblyName>
    <IsPackable>false</IsPackable>
  </PropertyGroup>
  <ItemGroup>
    <Compile Remove="AssemblyConflictTest.cs" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.11.0" />
    <PackageReference Include="MSTest.TestAdapter" Version="3.5.2" />
    <PackageReference Include="MSTest.TestFramework" Version="3.5.2" />
    <PackageReference Include="coverlet.collector" Version="6.0.2">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="xunit" Version="2.9.0" />
    <PackageReference Include="xunit.assert" Version="2.9.0" />
    <PackageReference Include="xunit.extensibility.execution" Version="2.9.0" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Extric.Towbook.WebShared.Net5\Extric.Towbook.WebShared.Net5.csproj" />
  </ItemGroup>
</Project>