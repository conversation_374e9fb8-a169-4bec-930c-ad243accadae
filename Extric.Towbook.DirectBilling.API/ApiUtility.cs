using Extric.Towbook.WebShared;
using System.Net;
using System.Net.Http;

namespace Extric.Towbook.DirectBilling.API
{
    internal static class ApiUtility
    {
        /// <summary>
        /// Checks if the object is null, and if so, returns a 404.
        /// Secondly, it checks if the current user has access to the companyId specified.
        /// </summary>
        /// <param name="companyId"></param>
        /// <param name="type">What the object should be referred to as, example: call</param>
        public static void ThrowIfNoCompanyAccess(int? companyId, string type = "object")
        {
            if (type == null)
                type = "object";

            if (companyId == null)
            {
                throw new Extric.Towbook.Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound)
                {
                    Content = new StringContent($"The {type.ToLowerInvariant()} you requested cannot be found.")
                });
            }

            if (!WebGlobal.CurrentUser.HasAccessToCompany(companyId.Value))
            {
                throw new Extric.Towbook.Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.Forbidden)
                {
                    Content = new StringContent($"Your user account doesn't have access to this {type} due to a companyId restriction.")
                });
            }
        }

        public static void ThrowIfNotFound(object o, string type = "object")
        {
            if (o == null)
                throw new Extric.Towbook.Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound)
                {
                    Content = new StringContent($"The {type} you tried to access cannot be found.")
                });
        }

        public static HttpResponseMessage NoContent()
        {
            return new HttpResponseMessage(HttpStatusCode.NoContent);
        }

        public static User CurrentUser
        {
            get
            {
                return WebGlobal.CurrentUser;
            }
        }
    }
}
