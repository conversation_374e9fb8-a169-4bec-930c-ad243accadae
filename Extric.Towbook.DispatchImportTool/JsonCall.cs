using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Newtonsoft.Json;

namespace Extric.Towbook.ExternalDispatchImportTool
{
    public class Location
    {
        public string id { get; set; }
        public string name { get; set; }
        public string lat { get; set; }
        public string lng { get; set; }
        public string xstreet { get; set; }
        public string landmark { get; set; }
        public string address1 { get; set; }
        public string address2 { get; set; }
        public string city { get; set; }
        public string state { get; set; }
        public string zip { get; set; }
        public string country { get; set; }
        public bool isLot { get; set; }
    }

    public class JsonCall
    {
        public string groupId { get; set; }
        public string callId { get; set; }
        public DateTime created { get; set; }
        public string number { get; set; }
        public string status { get; set; }
        public string notes { get; set; }
        public Eta eta { get; set; }
        public Vehicle vehicle { get; set; }
        public Track track { get; set; }
        public Truck truck { get; set; }
        public Driver driver { get; set; }
        public Customer customer { get; set; }
        public float total { get; set; }
        public Item[] items { get; set; }
        public Account account { get; set; }
        public string purchaseOrder { get; set; }
        public string reason { get; set; }
        public string vendorNumber { get; set; }
        public string caseNo { get; set; }
        public Event[] events { get; set; }
        public Location location { get; set; }
        public Location dropLocation { get; set; }
        public Lot lot { get; set; }
        public Lotmanagement lotManagement { get; set; }
        public Commission commission { get; set; }
        public string deletedReason { get; set; }
        public string deletedById { get; set; }
        public Tow123Payment[] payments { get; set; }
        public DateTime earliestActivity { get; set; }
        public DateTime completed { get; set; }
        public DateTime cancelled { get; set; }
        public string classCode { get; set; }
        public float amountDueOnScene { get; set; }
        public float amountDueOnLot { get; set; }
        public float amountBilled { get; set; }
        public string jobId { get; set; }
        public bool digitalDispatched { get; set; }
        public bool digitalDispatchResponseSent { get; set; }
        public int queueNumber { get; set; }
        public string[] callTicketIds { get; set; }
        public bool paid { get; set; }
        public string data { get; set; }
        public long revision { get; set; }
        public string id { get; set; }
        public bool cache { get; set; }
        public float paidAmount { get; set; }
    }

    public class Eta
    {
        public int interval { get; set; }
        public DateTime time { get; set; }
    }

    public class Vehicle
    {
        public string id { get; set; }
        public string year { get; set; }
        public string make { get; set; }
        public string model { get; set; }
        public string color { get; set; }
        public string vin { get; set; }
        public string tag { get; set; }
        public string tagState { get; set; }
        public string title { get; set; }
        public string odometer { get; set; }
        public string inventory { get; set; }
        public string keyLocation { get; set; }
    }

    public class Track
    {
        public object[] outbound { get; set; }
        public object[] inbound { get; set; }
        public object[] postdropoff { get; set; }
    }

    public class Truck
    {
        public string name { get; set; }
        public string tag { get; set; }
        public string description { get; set; }
        public string driverId { get; set; }
        public string iconCode { get; set; }
        public Status status { get; set; }
        public Vector vector { get; set; }
        public long revision { get; set; }
        public string id { get; set; }
        public bool cache { get; set; }
    }

    public class Status
    {
        public string duty { get; set; }
    }

    public class Vector
    {
    }

    public class Driver
    {
        public string name { get; set; }
        public string description { get; set; }
        public string dl_no { get; set; }
        public string addId { get; set; }
        public string mobileNumber { get; set; }
        public string mobileCarrier { get; set; }
        public string email { get; set; }
        public string duty { get; set; }
        public long revision { get; set; }
        public string id { get; set; }
        public bool cache { get; set; }
    }

    public class Customer
    {
        public string id { get; set; }
        public string name { get; set; }
        public object[] address { get; set; }
        public object[] phone { get; set; }
        public string email { get; set; }
    }

    public class Item
    {
        public string ticketId { get; set; }
        public string groupId { get; set; }
        public string type { get; set; }
        public string item { get; set; }
        public float total { get; set; }
        public string payer { get; set; }
        public string iconCode { get; set; }
        public string role { get; set; }

        [JsonProperty("params")]
        public ItemParameter parameters { get; set; }

        public float paidAmount { get; set; }
        public int sort { get; set; }
        public long revision { get; set; }
        public string id { get; set; }
    }

    public class ItemParameter
    {
        public string min { get; set; }
        public string minType { get; set; }
        public string defaultPrice { get; set; }
        public string quantity { get; set; }
        public string classPrices { get; set; }
        public string maxType { get; set; }
        public string description { get; set; }
        public string max { get; set; }
        public string price { get; set; }
        public string paidAmount { get; set; }
        public string category { get; set; }
        public string unit { get; set; }
        public string commissionable { get; set; }
    }

    public class Account
    {
        public string name { get; set; }
        public string status { get; set; }
        public string contact { get; set; }
        public string authorizedSignee { get; set; }
        public long revision { get; set; }
        public string id { get; set; }
        public bool cache { get; set; }
    }

    public class Lot
    {
        public string name { get; set; }
        public string description { get; set; }
        public int capacity { get; set; }
        public string notes { get; set; }
        public Location1 location { get; set; }
        public long revision { get; set; }
        public string id { get; set; }
        public bool cache { get; set; }
    }

    public class Location1
    {
        public string id { get; set; }
        public string lat { get; set; }
        public string lng { get; set; }
        public string xstreet { get; set; }
        public string landmark { get; set; }
        public string address1 { get; set; }
        public string address2 { get; set; }
        public string city { get; set; }
        public string state { get; set; }
        public string zip { get; set; }
        public string country { get; set; }
        public bool isLot { get; set; }
    }

    public class Tow123Payment
    {
        public string id { get; set; }
        public float amount { get; set; }
        public string receivedBy { get; set; }
        public DateTime appliedOn { get; set; }
        public string payerName { get; set; }
        public Tow123PaymentType tender { get; set; }
        public PaymentItem[] items { get; set; }
    }

    public class Tow123PaymentType
    {
        public string number { get; set; }
        public string type { get; set; }
    }

    public class PaymentItem
    {
        public string paymentId { get; set; }
        public string itemId { get; set; }
        public float amount { get; set; }
    }

    public class Lotmanagement
    {
        public int storageDays { get; set; }
        public string storageInterval { get; set; }
        public string policeHold { get; set; }
        public string vehicleDisposition { get; set; }
        public Releasedby releasedBy { get; set; }
        public Releasedto releasedTo { get; set; }
        public Pickedupby pickedUpBy { get; set; }
    }

    public class Releasedby
    {
        public string id { get; set; }
        public object[] address { get; set; }
        public object[] phone { get; set; }
    }

    public class Releasedto
    {
        public string id { get; set; }
        public object[] address { get; set; }
        public object[] phone { get; set; }
    }

    public class Pickedupby
    {
        public string id { get; set; }
        public object[] address { get; set; }
        public object[] phone { get; set; }
    }

    public class Commission
    {
        public float commission { get; set; }
        public float commissionableAmount { get; set; }
    }

    public class Event
    {
        public DateTime time { get; set; }
        public string status { get; set; }
        public string ticketId { get; set; }
        public Trucklocation truckLocation { get; set; }
        public Interval interval { get; set; }
        public string displayName { get; set; }
    }

    public class Trucklocation
    {
    }

    public class Interval
    {
    }
}
