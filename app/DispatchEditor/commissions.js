
// commissions.js
// This class is used to calculate the commission for a driver based on the commissions rules of the company.
// {options} should include the following:
//      _companyId: companyId of the ticket.
//      _assets: REST API compatible array of assets on the ticket.
//      _driverId: the ONE driver that the commission belongs to.
function Commissions(options) {
    this.options = $.extend({}, options);

    var Me = this;
    var _companyId = this.options != null ? this.options.companyId : null;
    var _assets = this.options != null ? this.options.assets : null;
    var _driverId = this.options != null ? this.options.driver != null ? this.options.driver.id : this.options.driverId : null;

    // private methods
    function isRatePercent(rate) {
        if (rate == null) {
            console.log("Error: a rate must be specified to check if a commission rate is a percent.");
            return;
        }

        if (!rate || !isNaN(rate))
            return false;

        if (rate.indexOf('%') == -1)
            return false;

        return true;
    }

    // returns the driver commission rate or the default company commission rate based on the rateItem and bodyType
    Me.getDefaultCommissionRate = function (rateItemId, bodyTypeId, isDiscount) {
        if (_companyId == null) {
            console.log("Commissions Error: no company specified");
            return 0;
        }

        var ri = towbook.get(towbook.rateItems, rateItemId);
        if (ri == null && !isDiscount && Me.options.preventCommissionOnAdHocItems)
          return "0%";

        if (ri && ri.classId == 4)
          return 0;

        if (_driverId != null && _driverId > 0) {
            
            var rateItem = $.grep(towbook.commissions.rates, function (item) {
                return item.rateItemId == rateItemId && item.driverId == _driverId;
             });
          
            if (rateItem.length == 0) {
                rateItem = $.grep(towbook.commissions.rates, function (item) {
                    return item.rateItemId == rateItemId && item.driverId == null;
                });
            }

            if (rateItem.length == 0) {

                // Find default body type commission rate (for this driver)
                rateItem = $.grep(towbook.commissions.rates, function (item) {
                    var bodyTypes = item.bodyTypes.filter(function (f) { return f.bodyTypeId == parseInt(bodyTypeId) && f.companyId == _companyId }) || [];
                    return bodyTypes.length > 0 && item.rateItemId == 0 && item.driverId == _driverId;
                });

                // If not found, try company default body type commission rate (for all drivers)
                if (rateItem.length == 0 && !bodyTypeId && !_companyId ) {
                    rateItem = $.grep(towbook.commissions.rates, function (item) {
                        var bodyTypes = item.bodyTypes.filter(function (f) { return f.bodyTypeId == parseInt(bodyTypeId) && f.companyId == _companyId }) || [];
                        return bodyTypes.length > 0 && item.rateItemId == 0 && item.driverId == null;
                    });
                }
            }

            if (rateItem[0] != null) {
                if (bodyTypeId != null) {
                    var bodyType = towbook.get(rateItem[0].bodyTypes, bodyTypeId, "bodyTypeId");
                    if (bodyType != null)
                        return bodyType.value;
                }

                if (rateItem[0].rate != null) {
                  if (isRatePercent(rateItem[0].rate)) {
                    // ignore default row base rate of 0 (use driver default instead)
                    var r = parseFloat(rateItem[0].rate);
                    if (!isNaN(r))
                      return rateItem[0].rate;
                  }
                  else
                    return rateItem[0].rate;
                }
            }
        }


        var driver = towbook.get(towbook.commissions.drivers, _driverId, "driverId");
      if (driver != null) {
          var r = towbook.commissions.rates.filter(function (f) { return f.rateItemId == 0 && f.driverId == null && f.rate == null; })[0] || [];
          var cr = towbook.get(r.bodyTypes, bodyTypeId, "bodyTypeId");
          if (cr && cr.value.length)
            return cr.value;
          else
            return driver.rate + '%';
        }

        var company = towbook.get(towbook.commissions.company, _companyId);
        if (company != null)
            return company.rate + '%';

        return 0;
    }

    // returns the driver commission rate or the default company commission rate based on the rateItem and bodyType
    Me.getCommissionRateByRateItemId = function (rateItemId, bodyTypeId) {
        var self = this;
        var ri = towbook.get(towbook.rateItems, rateItemId);
        if (ri && ri.classId == 4)
          return 0;

        var isDiscount = rateItemId == "2";

        // look at driver specific commissions
        if (rateItemId != null && _driverId != null) {
            var rateItem = null;

            if (bodyTypeId) {
                var rateItems = towbook.commissions.rates.filter(function (item) {
                  return towbook.get(item.bodyTypes, bodyTypeId, "bodyTypeId") != null
                });

               rateItem = rateItems.filter(function(item) {
                    return towbook.get(item.bodyTypes, bodyTypeId, "bodyTypeId") != null && item.driverId == _driverId && item.rateItemId == rateItemId;
                })[0] || null;

                if (rateItem && rateItem.bodyTypes != null) {
                    var bodyType = towbook.get(rateItem.bodyTypes, bodyTypeId, "bodyTypeId");
                    if (bodyType != null)
                        return bodyType.value;
                }
            }
            else {

                var rateItems = towbook.commissions.rates.filter(function(item) {
                    if (item.driverId == _driverId || item.rateItemId == rateItemId)
                        return item;
                });

                // order by rateItems match
                rateItems = rateItems.sort(function(a, b) {
                    return (b.rateItemId == rateItemId ? 1 : 0) - (a.rateItemId == rateItemId ? 1 : 0);
                });

                // order by driver match
                rateItems = rateItems.sort(function(a, b) {
                    return (b.driverId == _driverId ? 1 : 0) - (a.driverId == _driverId ? 1 : 0);
                });

                if (rateItems.length)
                    return rateItems[0].rate;
            }
        }

        return self.getDefaultCommissionRate(rateItemId, bodyTypeId, isDiscount);
    }
    // Returns:
    //      an object with the commistion value and 'extra' details
    // Parameters:
    //      invoiceItem: ONE Invoice item that is REST API compatible. [required]
    Me.getCommissionByInvoiceItem = function (invoiceItem) {
        if (invoiceItem == null)
            return 0;

        var self = this;
        var invItem = invoiceItem;
        var calculatedCommission = 0;
        var driverId = _driverId;
      var bodyTypeId = 1;
        var asset = towbook.get(_assets, invItem.assetId);
        if (asset && asset.bodyType)
            bodyTypeId = asset.bodyType.id;

        if (invoiceItem.length > 0)
            invItem = invoiceItem[0];

        if (asset && asset.drivers) {
            var driver = $.grep(asset.drivers, function (driver) {
                return driver != null && driver.driver != null && driver.driver.id == driverId;
            })

            if (driver.length == 0)
                return 0;
        }

        var rate = self.getCommissionRateByRateItemId(invItem.rateItemId, bodyTypeId);
        var amount = 0;
        var isPercent = false;
        var freeQuantityAmount = 0;

        // Added Aug 28/14.  Consider free quantity.
        if (invItem.freeQuantity > 0) {
          // if free quantity exceeds the given quantity, only use what is needed to balance the item's commission to 0
          if (invItem.freeQuantity > invItem.quantity)
            invItem.freeQuantity = invItem.quantity;

          freeQuantityAmount = invItem.freeQuantity * invItem.price;
        }

        if (isRatePercent(rate)) {
            rate = parseFloat(rate.replace(/\%/g, ''));
            amount = invItem.itemTotal == undefined || invItem.rateItemId == 1 /* fuel surcharge */ ? 0 : parseFloat(invItem.itemTotal - freeQuantityAmount) * (parseFloat(rate) / 100);
            isPercent = true;
        }
        else {
            rate = parseFloat(("" + rate).replace(/\$/g, ''));
            amount = rate;
            isPercent = false;
        }

        return {
            commission: amount,
            extra: {
                assetId: invItem.assetId,
                rateItemId: parseInt(invItem.rateItemId),
                bodyTypeId: parseInt(bodyTypeId),
                driverId: driverId,
                itemTotal: invItem.itemTotal - freeQuantityAmount,
                rate: rate,
                isFlatRate: !isPercent
            }
        }
    }
    // Returns:
    //      either the commission value (if returnDetails is 'false') or an object with other details
    // Parameters:
    //      ticketAmount: the ticket subtotal. [required]
    //      baseCommissionAmount: a % or $ value that the commission calculation will use.  This value essentially overrides the ticketAmount. [required]
    //      invoiceItem: ONE Invoice item that is REST API compatible. [required]
    //      invoiceItemCount: the number of invoice items on the ticket and must be higher than zero.
    //      baseDriverAmount: Must be a percent value and can be greater than '100%'. The commission value will be calculated and then changed by this rate. [optional]
    //      returnDetails: bool value.  'false' returns the commission amount only. 'true' includes more details with the commission amount. Default is 'false'.
    Me.getCommission = function (ticketAmount, baseCommissionAmount, invoiceItem, invoiceItemCount, baseDriverAmount, returnDetails) {
        if (invoiceItem == null ||
            baseCommissionAmount == null ||
            ticketAmount == null)
            return 0;

        if (invoiceItemCount == null || invoiceItemCount < 1)
            invoiceItemCount == 1;

        if (returnDetails == null)
            returnDetails = false;

        var retVal = this.getCommissionByInvoiceItem(invoiceItem);
        if (retVal == 0)
            retVal = $.extend(retVal, { commission: 0 });
        if (retVal != null) {
            var baseRate = 1;
            if (isRatePercent(baseCommissionAmount))
                baseCommissionAmount = accounting.toFixed(parseFloat(ticketAmount) * parseFloat(parseFloat(baseCommissionAmount.replace(/\%/g, '')) / 100), 4);

            baseRate = ticketAmount == 0 ? 1 : parseFloat(baseCommissionAmount) / parseFloat(ticketAmount);

            var commission = accounting.toFixed(retVal.commission * parseFloat(baseRate), 4);

            if (baseDriverAmount != null) {
                if (isRatePercent(baseDriverAmount))
                    commission = accounting.toFixed(parseFloat(commission) * parseFloat(parseFloat(baseDriverAmount.replace(/\%/g, '')) / 100), 4);
                else
                    commission = accounting.toFixed(baseDriverAmount / parseFloat(invoiceItemCount), 4);
            }

            if (returnDetails) {
                $.extend(retVal, { commission: commission });
                return retVal;
            }

            return commission;
        }

        return 0;
    },
        // Returns:
        //      an object with invoice item name, total, rate, driverId, and most importantly, the commission amount.
        // Parameters:
        //      ticketAmount: the ticket subtotal. [required]
        //      baseCommissionAmount: a % or $ value that the commission calculation will use.  This value essentially overrides the ticketAmount. [required]
        //      invoiceItems: REST API compatible array of the ticket invoice items. [required]
        //      baseDriverAmount: Must be a percent value and can be greater than '100%'.  The commission value will be calculated and then changed by this rate. [optional]
        Me.getDetailedCommission = function (ticketAmount, baseCommissionAmount, invoiceItems, baseDriverAmount) {
            if (invoiceItems == null ||
                baseCommissionAmount == null ||
                ticketAmount == null)
                return "";

            if (invoiceItems.length < 1)
                return "";

            var self = this;
            var retVal = [];

            $.each(invoiceItems, function (index, item) {
                var nfreeQuantityAmount = item.freeQuantity != null && item.freeQuantity > 0 ? item.freeQuantity * item.price : 0;
                var ret = self.getCommission(ticketAmount, baseCommissionAmount, item, invoiceItems.length, baseDriverAmount, true);

                var currentAccount = towbook.get(towbook.accounts, $('#account').val());
                if (currentAccount != null) {

                    if (currentAccount.type == 5) {
                        var overrideAccount = towbook.get(towbook.accounts, 585027);
                        if (overrideAccount != null) {
                            var overrideItem = towbook.get(overrideAccount.rateItems, item.rateItemId);
                            if (overrideItem != null) {
                                item.price = overrideItem.cost;
                                item.itemTotal = item.price * item.quantity;
                            }
                        }

                    }
                }


                var bca = baseCommissionAmount != ticketAmount && item.rateItemId != 1 /* fuel surcharge */ ? baseCommissionAmount : (item.itemTotal - nfreeQuantityAmount < 0) && item.rateItemId != 2 ? 0 : item.itemTotal - nfreeQuantityAmount;
                var bcar = ticketAmount == 0 ? 1 : baseCommissionAmount / ticketAmount;

                if (ret && ret.extra) {

                    var driverBaseAmount = ret.commission != 0 ? parseFloat((nfreeQuantityAmount < 0 ? 0 : item.itemTotal - nfreeQuantityAmount) * bcar * (baseDriverAmount.replace(/\%/g, '') / 100)) : 0

                    retVal.push({
                        name: item.name != undefined ? item.name : towbook.get(towbook.rateItems, item.rateItemId) != null ? towbook.get(towbook.rateItems, item.rateItemId).name : "",
                        type: "commission",
                        price: bca,
                        rate: ret.extra.rate,
                        effectiveRate: parseFloat(ret.extra.rate * parseFloat(baseDriverAmount.replace(/\%/g, '') / 100)),
                        rateItemId: parseInt(item.rateItemId),
                        invoiceItemId: item.id,
                        driverId: _driverId,
                        commission: (item.itemTotal - nfreeQuantityAmount < 0) && item.rateItemId != 2 ? 0 : ret.commission,
                        commissionBaseAmount: bca,
                        commissionBaseRate: bcar,
                        driverBaseRate: parseFloat(baseDriverAmount.replace(/\%/g, '') / 100),
                        driverBaseAmount: driverBaseAmount,
                        lineItemTotal: parseFloat(nfreeQuantityAmount < 0 ? 0 : item.itemTotal - nfreeQuantityAmount),
                        isFlatRate: ret.extra.isFlatRate
                    });
                }
            });

            var detailCommissionSummed = retVal.reduce(function (a, b) { return parseFloat(a) + parseFloat(b.commission); }, 0);

            if (isRatePercent(baseDriverAmount) && ((ticketAmount == 0 || baseCommissionAmount == 0) && detailCommissionSummed == 0))
              return "";

            return retVal;
        }
};

// static methods of Commissions
$.extend(Commissions, {

  get: function (data) {

    if(!data)
      data = towbook.views.dispatch.callEditor.get();

    var byDrivers = Commissions.byDrivers(data);

    return {
      totalCommission: accounting.toFixed(byDrivers.reduce(function (all, one, i) { all += one.commission; return all; }, []), 2),
      byDrivers: byDrivers
    };
  },



  // Get the commission details for the callModel
    byDrivers: function(data) {
        if (towbook.isEmpty(data))
            return [];

        var driverIds = data.assets.reduce(function(all, one, i) {
            if (one.drivers && one.drivers.length) {
                one.drivers.map(function(m) {
                    if (m.driver && m.driver.id > 0 && all.indexOf(m.driver.id) == -1)
                        all.push(parseInt(m.driver.id));
                });
                return all;
            }
        }, []) || [];

        var ticketValue = parseFloat(data.invoiceTotal);
        var baseCommissionAmount = data.commissions.totalTicketValue;

        var ret = [];
        

        driverIds.map(function(driverId) {
            var commission = new Commissions({ companyId: data.companyId, assets: data.assets, driverId: driverId });
            var driver = towbook.get(towbook.drivers, driverId);
            var tc = 0;
            var iiRet = [];

            data.invoiceItems.map(function(ii) {
                var base = towbook.get(data.commissions.drivers, driverId, "driverId") || [];
                var c = commission.getCommission(ticketValue, baseCommissionAmount, ii, data.invoiceItems.length, base.value || "100%", true);
                tc += parseFloat(c.commission);
                iiRet.push({
                    id: parseInt(ii.id),
                    name: ii.name || "",
                    commission: parseFloat(c.commission),
                    detail: c.extra,
                    price: parseFloat(ii.price),
                    rateItemId: parseInt(ii.rateItemId),
                });
            });

            ret.push({
                driverId: driverId,
                name: driver.name,
                commission: tc,
                details: iiRet
            });
        });

        return ret;
    },
});
