<%@ WebHandler Language="C#" Class="impounds" %>

using System;
using System.Web;

public class impounds : IHttpHandler {
    
    public void ProcessRequest (HttpContext context) {
        context.Response.ContentType = "text/json";
        context.Response.ContentEncoding = System.Text.Encoding.UTF8;
        context.Response.AddHeader("X-JSON", "1");
        
        // inputs:
        // released=1/0
        // impounded=1/0
        // daysHeldAtleast=0-10000
        // 

        bool bShowReleased = false;
        bool bShowImpounded = true;

        if (context.Request.QueryString["released"] == "1")
            bShowReleased = true;
        
        if (context.Request.QueryString["impounded"] == "0")
            bShowImpounded= false;
        
        
        context.Response.Write("{ \"impounds\": [\n");

        string data = String.Empty;
        string template = "\n\t{{ \"id\": \"{0}\", \"v\": \"{1}\", \"a\": \"{2}\", \"dh\": \"{3}\", \"impdate\": \"{4}\", \"inv\": \"{5}\" }},";


        foreach (Extric.Towbook.Impounds.Impound imp in Extric.Towbook.Impounds.Impound.GetByCompany(Global.CurrentUser.Company, bShowImpounded, bShowReleased))
        {
            data += String.Format(template, imp.Id, imp.DispatchEntry.MakeModelFormatted, imp.DispatchEntry.Account, imp.DaysHeldBillable, imp.ImpoundDate.Value.ToShortDateString(), imp.DispatchEntry.InvoiceNumber);
        }        
        data = data.TrimEnd(',') + "\n] }\n";

        context.Response.Write(data + "\n");
        context.Response.Cache.SetExpires(DateTime.Now.AddYears(-1));
        context.Response.Cache.SetNoStore();
        context.Response.Cache.SetNoServerCaching();
        
        
    }
 
    public bool IsReusable {
        get {
            return false;
        }
    }

}