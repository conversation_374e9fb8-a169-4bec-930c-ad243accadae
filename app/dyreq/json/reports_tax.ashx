<%@ WebHandler Language="C#" Class="reports_tax" %>

using System;
using System.Web;
using System.Data.SqlClient;


public class reports_tax : IHttpHandler {
    
    public void ProcessRequest (HttpContext context) {
        context.Response.ContentType = "text/json";

        // inputs:
        // startDate=mm/dd/yyyy
        // endDate=mm/dd/yyyy


        DateTime startDate;
        DateTime endDate;


        if (!DateTime.TryParse(context.Request.QueryString["startDate"], out startDate))
        {
            throw new Exception("Invalid StartDate.");
        }

        if (!DateTime.TryParse(context.Request.QueryString["endDate"], out endDate))
        {
            throw new Exception("Invalid EndDate.");
        }
        

        context.Response.Write("{ \"taxes\": [\n");

        string data = String.Empty;
        string template = "\n\t{{ \"total\": \"{5}\", \"nontaxed\": \"{7}\", \"taxable\": \"{6}\", \"amount\": \"{0}\", \"rate\": \"{1}\", \"description\": \"{2}\", \"startDate\": \"{3}\", \"endDate\": \"{4}\" }},";


        using(SqlDataReader dr = Extric.Towbook.SqlHelper.ExecuteReader(Extric.Towbook.Core.ConnectionString,
                "TaxRatesGetCollectedByDate",
                new SqlParameter("@CompanyId", Global.CurrentUser.Company.Id),
                new SqlParameter("@StartDate", startDate),
                new SqlParameter("@EndDate", endDate)))
        {
        
            while (dr.Read())
            {
                data += String.Format(template, 
			dr.GetValue<float>("TaxCollected").ToString("C"),			// 0
			 dr.GetValue<float>("Rate").ToString("0.00") + "%", 			// 1
			dr.GetValue<string>("Description"), 					// 2
			dr.GetValue<DateTime>("StartDate").ToShortDateString(), 		// 3
			dr.GetValue<DateTime>("EndDate").ToShortDateString(),			// 4
			dr.GetValue<float>("TotalIncome").ToString("C"),			// 5
			dr.GetValue<float>("TaxableIncome").ToString("C"),			// 6
			(dr.GetValue<float>("TotalIncome") - dr.GetValue<float>("TaxableIncome")).ToString("C"));
            }
        }
        data = data.TrimEnd(',') + "\n] }\n";

        context.Response.Write(data + "\n");
        context.Response.Cache.SetExpires(DateTime.Now.AddYears(-1));
        context.Response.Cache.SetNoStore();
        context.Response.Cache.SetNoServerCaching();
        
        
        
    }
 
    public bool IsReusable {
        get {
            return false;
        }
    }

}