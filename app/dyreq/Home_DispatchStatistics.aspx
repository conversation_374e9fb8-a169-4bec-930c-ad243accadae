<%@ Page Language="C#" AutoEventWireup="true" Inherits="ajax_Home_DispatchStatistics" CodeFile="Home_DispatchStatistics.aspx.cs" %>
<style>
.statType, .statTypeLeft { vertical-align: bottom; border:none }
</style>

<table style="width: 100%" cellspacing="0" cellpadding="0">
<tr>
<td class="statTypeLeft" >Waiting</td>
<td class="statType">Dispatched</td>
<td class="statType">En Route</td>
<td class="statType">On Scene</td>
<td class="statType">Towing</td>
<td class="statType">Completed</td>
<td class="statType">Cancelled</td>
<td class="statType" style="width: auto">&nbsp;</td>
</tr>
<tr>
<td class="statValueLeft"><% = Companies.Sum(o => o.CurrentDispatchStatistics.Waiting) %></td>
<td class="statValue"><% = Companies.Sum(o => o.CurrentDispatchStatistics.Dispatched) %></td>
<td class="statValue"><% = Companies.Sum(o => o.CurrentDispatchStatistics.EnRoute) %></td>
<td class="statValue"><% = Companies.Sum(o => o.CurrentDispatchStatistics.AtSite) %></td>
<td class="statValue"><% = Companies.Sum(o => o.CurrentDispatchStatistics.BeingTowed) %></td>
<td class="statValue"><% = Companies.Sum(o => o.CurrentDispatchStatistics.Completed) %></td>
<td class="statValue"><% = Companies.Sum(o => o.CurrentDispatchStatistics.Cancelled) %></td>
<td class="statValue" style="width: auto; font-weight: normal; color: #cfcfcf">(last updated at : <% =Global.OffsetDateTime(DateTime.Now).ToLongTimeString()%>)</td>
</tr>
</table>