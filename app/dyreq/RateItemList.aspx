<%@ Page Language="C#" AutoEventWireup="true" Inherits="dyreq_RateItemList" CodeFile="RateItemList.aspx.cs" %>
<%@ Import Namespace="Extric.Towbook.Surcharges" %>
<%@ Import Namespace="System.Linq" %>

<% 
    System.Collections.Generic.List<Extric.Towbook.IRateItem> rates = null;
    Extric.Towbook.Surcharges.SurchargeAccountRate srA = null;
    Extric.Towbook.Surcharges.SurchargeRate sr = null;
    System.Collections.Generic.Dictionary<int, Extric.Towbook.Surcharges.RateItemExclusion> srExcluded = null;
    System.Collections.Generic.Dictionary<int, Extric.Towbook.Surcharges.AccountRateItemExclusion> arieExcluded = null;

    sr = SurchargeRate.GetBySurcharge(Surcharge.SURCHARGE_FUEL, Global.CurrentUser.Company.Id);
    if (sr != null)
    {
        srExcluded = RateItemExclusion.GetByCompany(Global.CurrentUser.Company.Id, Surcharge.SURCHARGE_FUEL);
    }
    
    if (Request.QueryString["id"] != null && Request.QueryString["id"] != "1")
    {
        try
        {
            int accountId = Convert.ToInt32(Request.QueryString["id"]);
            
            rates = Extric.Towbook.RateItem.GetByCompanyId(
                        Global.CurrentUser.Company.Id, 
                        Extric.Towbook.Accounts.Account.GetById(accountId))
                    .Where(o => o.ParentRateItemId == 0).ToList();

            srA = SurchargeAccountRate.GetBySurcharge(Surcharge.SURCHARGE_FUEL, accountId);
            
            if (srA != null)
            {
                arieExcluded = AccountRateItemExclusion.GetByAccount(accountId, Extric.Towbook.Surcharges.Surcharge.SURCHARGE_FUEL);
            }
            
            Response.Write("// Account: " + accountId + "\n");
        }
        catch (Extric.Towbook.TowbookException)
        {
            rates = Extric.Towbook.RateItem.GetByCompanyId(Global.CurrentUser.Company.Id).Where(o => o.ParentRateItemId == 0).ToList();
        }   
    }
    else
    {
        rates = Extric.Towbook.RateItem.GetByCompanyId(Global.CurrentUser.Company.Id).Where(o => o.ParentRateItemId == 0).ToList();
    }

    Response.Write("//\n");
    Response.Write("// Company: " + Global.CurrentUser.Company.Id.ToString() + "\n");
    Response.Write("//\n");
	
    if (rates.Count > 0)
    {
        Response.Write("rateItems = new Object();\n");
        Response.Write("rateItems[-1] = { n: \"(choose a preset service item)\", fq: 0, tax: 0, prH: 0, prL: 0, lockPrice: true, lockQuantity: true, defaultQuantity: 0, price: null };\n");
    }

	var rates2 = rates.OrderBy(o => o.Name);

    foreach (Extric.Towbook.IRateItem ri in rates2) { %>
rateItems[<% =ri.RateItemId%>] = { pr: <% =(ri.Predefined != null ? 1 : 0) %>, 
    tax: <%=(ri.Taxable == true ? "1" : "0") %>, 
    prH: <% =(ri.Predefined != null && ri.Predefined.Hidden == true ? 1 : 0) %>, 
    prL: <% =(ri.Predefined != null && ri.Predefined.Locked == true ? 1 : 0) %>, 
    n: "<% =HttpUtility.HtmlEncode(ri.Name) %>", 
    fq: <% =ri.FreeQuantity %>, 
    max: <% =(ri.MinimumQuantity != null ? ri.MaximumQuantity.ToString() : "null") %>, 
    min: <% =(ri.MinimumQuantity != null ? ri.MinimumQuantity.ToString() : "null")%>, 
    lockPrice: <% = ri.LockCost.ToString().ToLower()%>, 
    lockQuantity: <% = ri.LockQuantity.ToString().ToLower()%>, 
    defaultQuantity: <% =(ri.DefaultQuantity != null ? ri.DefaultQuantity : (ri.Predefined != null ? 0 : 1))%>, 
    price: <% = (ri.Cost != 0 ?  ri.Cost.ToString("0.00") : "null") %>, 
    basePrice: <% = (ri.Cost != 0 ?  ri.BaseCost.ToString("0.00") : "null") %>  
}

<% if (ri.ExtendedRateItems.Count > 0)
   {
       Response.Write(String.Format("rateItems[{0}].ext = new Object();", ri.RateItemId));
       foreach (Extric.Towbook.IExtendedRateItem x in ri.ExtendedRateItems.Values)
       {
           if (x.GetType() == typeof(Extric.Towbook.ExtendedRateItem))
           {
               Extric.Towbook.ExtendedRateItem xri = (Extric.Towbook.ExtendedRateItem)x;
               Response.Write(String.Format("rateItems[{0}].ext[{1}] = {{ amt: {2}, baseAmt: {3} }}\n", ri.RateItemId, xri.BodyTypeId, xri.Amount.ToString("0.00"), xri.BaseAmount.ToString("0.00")));
           }
           else if (x.GetType() == typeof(Extric.Towbook.Accounts.ExtendedRateItem))
           {
               Extric.Towbook.Accounts.ExtendedRateItem xri = (Extric.Towbook.Accounts.ExtendedRateItem)x;
               Response.Write(String.Format("rateItems[{0}].ext[{1}] = {{ amt: {2}, baseAmt: {3} }}\n", ri.RateItemId, xri.BodyTypeId, xri.Amount.ToString("0.00"), xri.BaseAmount.ToString("0.00")));
           }
       }
   }
    }
    
    Response.Write("var surcharges = new Object();\n");

    if (srA != null || sr != null)
    {
        Response.Write("surcharges[" + Surcharge.SURCHARGE_FUEL + "] = new Object();");
        Response.Write(String.Format("surcharges[{0}].excluded = new Object();\n", Surcharge.SURCHARGE_FUEL));
    }
    
    if (srA != null)
    {
        Response.Write("surcharges[" + Surcharge.SURCHARGE_FUEL + "].rate = " + srA.Rate.ToString("0.0000") + "; // account rate\n");
        Response.Write("surcharges[" + Surcharge.SURCHARGE_FUEL + "].taxable = " + Convert.ToInt32(srA.Taxable)  + "; // account rate\n");
    }
    else if (sr != null)
    {
        Response.Write("surcharges[" + Surcharge.SURCHARGE_FUEL + "].rate = " + sr.Rate.ToString("0.0000") + "; // surcharge rate\n");
        Response.Write("surcharges[" + Surcharge.SURCHARGE_FUEL + "].taxable = " + Convert.ToInt32(sr.Taxable) + "; // account rate\n");
    }
    
    
    // note: with the way this is written, if a rateitem is excluded, an accountrateitem cannot un-exlude it. so if its not charged globally, you cant
    // charge fuel surcharge on a specific item... I can add code to change this if neccesary
    
    if (arieExcluded != null)
    {
        foreach(AccountRateItemExclusion excl in arieExcluded.Values)
        {
            Response.Write(String.Format("surcharges[{0}].excluded[{1}] = 1;\n", excl.SurchargeId, excl.RateItemId));   
        }
    }
    
    if (srExcluded != null)
    {
        foreach (RateItemExclusion exclu in srExcluded.Values)
        {
            Response.Write(String.Format("surcharges[{0}].excluded[{1}] = 1;\n", exclu.SurchargeId, exclu.RateItemId));
        }
    }
%>