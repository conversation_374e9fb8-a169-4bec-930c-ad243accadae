<%@ Page Language="C#" AutoEventWireup="true" Inherits="dyreq_windows_stickering_Search" CodeFile="Search.aspx.cs" %>

<script type="text/javascript">
    new StickerSearchWidget({
        element: '#ajax-content',
        searchSuccess: function (searchRepository) {
            $("#tabcontrol1").data("StickersTabsInstance").populateFoundEntries(searchRepository);
            $('#ajax-container').hide("slow");
        }
    });

</script>

<form method="get" action="/api/stickering/stickers/Search">
    <table>
        <% if(Global.CurrentUser.Type != Extric.Towbook.User.TypeEnum.AccountUser) { %>
        <tr>
            <td class="field-left">Account</td>
            <td><select name="account" id="gsacc" title="account name" data-width="100%"></select></td>
        </tr>
        <% } %>
        <tr>
            <td class="field-left">Vehicle</td>
            <td>
                <div style="float: left; margin-right: -333px">
                    <select name="year" id="gsyear" class="validate-number" style="width: 70px" title="year"></select>
                    <select name="make" id="gsmake" title="choose make"></select>
                    <select name="model" id="gsmodel" title="choose model"></select>
                </div>
                <div style="margin-left: 333px">
                    <input type="text" name="vin" class="default" id="gsvin" data-tbk-prompt-text="vin" maxlength="17" />
                </div>
            </td>
        </tr>
        <tr>
            <td class="field-left">Plate/Reason</td>
            <td>
                <div style="float: left;">
                    <input type="text" name="plate" class="default" id="gsplate" data-tbk-prompt-text="license plate" style="text-transform: uppercase; width: 167px;"/>
                </div>
                <div style="margin-left: 170px">
                    <select name="reason" id="gsreason" data-width="170px" title="Reason"></select>
                </div>
            </td>
        </tr>

        <tr>
            <td class="field-left">Date Range</td>
            <td>
                <input type="text" class="tbkDatePicker default" name="startDate" id="gsstart" data-tbk-prompt-text="start date - mm/dd/yyyy" style="width:170px" /><input type="text" class="tbkDatePicker default" name="endDate" id="gsend" data-tbk-prompt-text="end date - mm/dd/yyyy" style="width:170px" />
            </td>
        </tr>
        <tr>
            <td class="field-left"></td>
            <td>
                <input type="button" value="Get Results!" class="standard-button" id="getResults" />
                <input type="button" value="Cancel" class="standard-button" id="cancel" onclick="toggleSearch($('#a-search'));" />
                <input type="hidden" id="gsstickering" value="1" />
            </td>
        </tr>
    </table>
    
    <!--<div style="border: solid 1px black; background-color: red; font-weight: bold; color: white; padding: 10px; font-size: 14px">Error: You must choose at least one field to search by!</div> -->
</form>