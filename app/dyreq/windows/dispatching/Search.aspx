<%@ Page Language="C#" AutoEventWireup="true" Inherits="dyreq_windows_dispatching_Search" CodeFile="Search.aspx.cs" Async="true" %>
<script type="text/javascript"><
    new Autocomplete('gsmake', { width: 120, serviceUrl: '/dyreq/json/vehiclemakes.ashx' });

    setBlFo('gscus', '(customer name, phone number, or account name)');
    setBlFo('gsvin', '(vin)');
    setBlFo('gsmake', '(make)');
    setBlFo('gsmodel', '(model)');
    setBlFo('gsyear', '(year)');
    setBlFo('gsstart', '(start date - mm/dd/yyyy)');
    setBlFo('gsend', '(end date - mm/dd/yyyy)');
<% if (Request.QueryString["impoundMode"] == "1") { %>
    setBlFo('gsimpid', '(impound/stock number)');
    setBlFo('gslicense', '(license plate)');


<% } %>
<%
	foreach(System.Collections.ObjectModel.Collection<Extric.Towbook.Dispatch.Attribute> aList in _attributes.Values)
	{ 
		foreach(Extric.Towbook.Dispatch.Attribute attr in aList) {
			Response.Write("    setBlFo('gsattribute_" + attr.Id + "', '(" + attr.Name + ")');\n");
	 	}
	 } 
%>



$('getResults').onclick = function() {
    var q = '';

    if (($('gscus').isDft() &&
        $('gsyear').isDft() &&
<% if (Request.QueryString["impoundMode"] == "1") { %>
        $('gsimpid').isDft() &&
        $('gslicense').isDft() &&
<% } %>
        $('gsmake').isDft() &&
        $('gsmodel').isDft() &&
        $('gsvin').isDft() &&
<%	foreach(System.Collections.ObjectModel.Collection<Extric.Towbook.Dispatch.Attribute> aList in _attributes.Values)
	{ 
		foreach(Extric.Towbook.Dispatch.Attribute attr in aList) {
		%> 
	$('gsattribute_<% =attr.Id %>').isDft() &&
<% } } %>
        $('gsstart').isDft() &&
        $('gsend').isDft()) <% if (Request.QueryString["impoundMode"] != "1") { %>&& $('gsdriver').value == '0' && $('gsdispatcher').value == '0' <% } %>) {
        alert('Please type a value to search for into at least one of the search boxes.');
        return;
    }

    if (!isValidDate($('gsstart').getVal()))
    {
	alert('The start date you entered is invalid. Please enter it in mm/dd/yyyy format; example: 7/25/2010.\n\nYou can also include the time, valid example: 7/25/2010 4:00pm');
	return;
     }


    if (!isValidDate($('gsend').getVal()))
    {
	alert('The end date you entered is invalid. Please enter it in mm/dd/yyyy format; example: 7/25/2010.\n\nYou can also include the time, valid example: 7/25/2010 4:00pm');
	return;
     }

    if ($('gscus').getVal().length > 0)
        q += "&customer=" + $('gscus').getVal();
    if ($('gsyear').getVal().length > 0)
        q += "&year=" + $('gsyear').getVal();
    if ($('gsmake').getVal().length > 0)
        q += "&make=" + $('gsmake').getVal();
    if ($('gsmodel').getVal().length > 0)
        q += "&model=" + $('gsmodel').getVal();
    if ($('gsvin').getVal().length > 0)
        q += "&vin=" + $('gsvin').getVal();
    if ($('gsstart').getVal().length > 0)
        q += "&startDate=" + $('gsstart').getVal();
    if ($('gsend').getVal().length > 0)
        q += "&endDate=" + $('gsend').getVal();
<% if (Request.QueryString["impoundMode"] != "1") { %>
    if ($('gsdriver').value != '0')
        q += "&driverId=" + $('gsdriver').value;
    if ($('gsdispatcher').value != '0')
        q += "&dispatcherId=" + $('gsdispatcher').value;

<% } else { %>
    if ($('gsimpid').getVal().length > 0)
        q += "&impoundId=" + $('gsimpid').value;
    if ($('gslicense').getVal().length > 0)
        q += "&licenseNumber=" + $('gsimpid').value;


<% } %>


<%	foreach(System.Collections.ObjectModel.Collection<Extric.Towbook.Dispatch.Attribute> aList in _attributes.Values)
	{ 
		foreach(Extric.Towbook.Dispatch.Attribute attr in aList) {%> 
		if ($('gsattribute_<% =attr.Id %>').getVal().length > 0)
			q += '&attribute_<% =attr.Id %>=' + $('gsattribute_<% =attr.Id %>').value;
<%
	 	}
	 } 
%>


    window.location = 'SearchResults.aspx?search' + q;

};
    
</script>
<table>
    <tr><td class="field-left">Customer</td><td><input type="text" id="gscus" /></td></tr>
    <tr><td class="field-left">Vehicle</td><td><input type="text" id="gsmake" /><input type="text" id="gsmodel"/>
      <input type="text" id="gsyear" style="width: 40px" onkeypress="return CheckKeyCode(this, event, false, false);" maxlength="4"  />
      <input type="text" id="gsvin" maxlength="17" /></td></tr>
    <tr><td class="field-left">Date Range</td><td><input type="text" id="gsstart" /><input type="text" id="gsend" /></td></tr>
<% if (Request.QueryString["impoundMode"] != "1") { %>
    <tr><td class="field-left">Driver</td><td><select id="gsdriver"><% =insertDrivers() %></select><span style="margin-left: 30px; margin-right: 10px">Dispatcher</span><select id="gsdispatcher"><% =insertDispatchers() %></select></td></tr>
<% }  %>
<tr><td class="field-left">Identifications</td>
  <td>
<% if (Request.QueryString["impoundMode"] == "1") { %>
<input type="text" id="gsimpid" maxlength="8" />
	<input type="text" id="gslicense" maxlength="6" />
<% } %>
<% 
	foreach(System.Collections.ObjectModel.Collection<Extric.Towbook.Dispatch.Attribute> aList in _attributes.Values) { 
		foreach(Extric.Towbook.Dispatch.Attribute attr in aList) {
%>
	<input type="text" id="gsattribute_<% = attr.Id %>"  style="width: 100px" />
<% } %>

</td>
</tr>

<!--
<tr><td>Options</td><td>

<input type="checkbox" id="gsImpound" checked=1 />Show current impounds |
<input type="checkbox" id="gsReleased" hecked=1 />Show Released Impounds
</td></tr> -->
<% } %>


</table>
<div style="<% if (Request.QueryString["impoundMode"] != "1") { %>margin-top: 10px; <% } %>margin-left: 5px">
<input type="button" value="Get Results!" class="button" style="color: white" id="getResults" />
</div>
