using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Collections.ObjectModel;

public partial class dyreq_windows_dispatching_Search2 : System.Web.UI.Page
{
    protected void Page_Load(object sender, EventArgs e)
    {
 
    }

    protected string insertDrivers()
    {
        List<Extric.Towbook.Driver> drivers = Extric.Towbook.Driver.GetByCompany(Global.CurrentUser.Company);

        string output = String.Empty;
        string format = "<option value=\"{0}\">{1}</option>\n";
        output += String.Format(format, "0", "(any driver)");

        foreach (Extric.Towbook.Driver d in drivers)
        {
            output += String.Format(format, d.Id, Extric.Towbook.Core.HtmlEncode(d.Name));
        }

        return (output);
    }

    protected string insertDispatchers()
    {
        Collection<Extric.Towbook.User> users = Extric.Towbook.User.GetByCompanyId(Global.CurrentUser.Company.Id);

        string output = String.Empty;
        string format = "<option value=\"{0}\">{1}</option>\n";
        output += String.Format(format, "0", "choose dispatcher");

        foreach (Extric.Towbook.User u in users)
        {
            if (u.Type == Extric.Towbook.User.TypeEnum.Manager || u.Type == Extric.Towbook.User.TypeEnum.Dispatcher)
            {
                output += String.Format(format, u.Id, Extric.Towbook.Core.HtmlEncode(u.FullName));
            }
        }

        return (output);
    }
}
