var accountCustomerData = {
    tbAccounts: [],
    providerCustomers: []
};

$(document).ready(function () {
    $.ajax({
        type: "GET",
        url: "/api/Accounts",
        data: {},
        contentType: "application/json; charset=utf-8"
    }).done(function (response) {
        towbook.compileTemplate('towbookAccountsRow', $('#t-towbookAccountsRow'));

        var target = $('#twbAccounts tbody');
        target.html("");

        var select = $("select#cbTBAccounts");

        $.each(response, function (index, item) {
            target.append(towbook.applyTemplate('towbookAccountsRow', item));
            accountCustomerData.tbAccounts.push(item);
            select.append("<option value='" + item.id + "'>" + _enc(item.name) + "</option>");
        });

        $.ajax({
            type: "GET",
            url: "/api/integration/accounting/providers/quickbooks/Customers",
            contentType: "application/json; charset=utf-8"
        }).done(function (response) {
            towbook.compileTemplate('quickbooksCustomersRow', $('#t-quickbooksCustomersRow'));

            var target = $('#qbCustomers tbody');
            target.html("");

            var select = $("select#cbQBCustomers");
            $.each(response, function (index, item) {
                target.append(towbook.applyTemplate('quickbooksCustomersRow', item));
                /* For Select*/
                select.append("<option value='" + item.id + "'>" + _enc(item.company) + "</option>");
            });

            accountCustomerData.providerCustomers = response;

            $.ajax({
                type: "GET",
                url: "/api/integration/accounts",
                data: {},
                contentType: "application/json; charset=utf-8"
            }).done(function (response) {
                towbook.compileTemplate('twbAccountsQBCustomersMatches', $('#t-twbAccountsQBCustomersMatches'));

                var target = $('#LinkResultTable tbody');
                target.html("");

                $.each(response, function (index, item) {

                    var a = towbook.get(accountCustomerData.tbAccounts, item.accountId);
                    var q = towbook.get(accountCustomerData.providerCustomers, item.value);

                    console.log(a, q);

                    var matchData = {
                        id: item.id,
                        accountId: a.id,
                        name: a.name,
                        address: a.address.address,
                        city: a.address.city,
                        state: a.address.state,
                        zip: a.address.zip,
                        phone: a.phone,
                        qbCustomerId: q.id,
                        qbName: q.company,
                        qbAddress: (q.addressList.length > 0 ? q.addressList[0].line1 : ''),
                        qbCity: (q.addressList.length > 0 ? q.addressList[0].city : ''),
                        qbState: (q.addressList.length > 0 ? q.addressList[0].stateOrProvince : ''),
                        qbZip: (q.addressList.length > 0 ? q.addressList[0].postalCode : ''),
                        qbPhone: q.phone
                    };
                    target.append(towbook.applyTemplate('twbAccountsQBCustomersMatches', matchData));
                });

			    filterList();

            });
        });
    });

    $("#LeftDiv_Accounts_Collapsable").bind("click", function () {
        CollapseDiv(this);
    });

    $("#RightDiv_Customers_Collapsable").click(function () {
        CollapseDiv(this);
    });

    $("#Link").click(function () {

        var qbCustomerId = $("select#cbQBCustomers").val() || "create";
        var name = $("select#cbQBCustomers option:selected").text();

        var accountId = $("select#cbTBAccounts").val() || "create";
        var value = $("select#cbTBAccounts option:selected").text();

        $.ajax({
            type: "POST",
            url: "/api/integration/accounting/providers/quickbooks/Customers/?accountId=" + accountId + "&qbCustomerId=" + qbCustomerId,
            contentType: "application/json; charset=utf-8"
        }).done(function (response) {
            towbook.compileTemplate('quickbooksCustomersRow', $('#t-quickbooksCustomersRow'));

            if (qbCustomerId == "create") {
                // append new customer to the customers list... TODO: need to query the QB Customer to pass to this template. 
                var target = $('#qbCustomers tbody');
                target.append(towbook.applyTemplate('quickbooksCustomersRow', response));

                // append new customer to the customers dropdown
                var select = $("select#cbQBCustomers");
                select.append("<option value='" + response.qbCustomerId + "'>" + response.qbCustomerName + "</option>");
            }

            // add the association to the list below:
            towbook.compileTemplate('twbAccountsQBCustomersMatches', $('#t-twbAccountsQBCustomersMatches'));

            var links = $('#LinkResultTable tbody');

            var data = {
                id: response.linkId,
                accountId: response.accountId,
                name: response.accountName,
                value: response.qbCustomerId,
                qbName: response.qbCustomerName
            };

            links.prepend(towbook.applyTemplate('twbAccountsQBCustomersMatches', data));

            $('#cbTBAccounts').setVal("");
            $('#cbQBAccounts').setVal("");

            filterList();
        }).error(function (xhr, status, error) {

            

            alert(xhr.responseText);

            console.log(xhr, status, error);
        });
    });

    $("#deleteCustomer").live("click", function (e) {
        e.preventDefault();
        $(this).addClass("active");
    });

    $(".customerItem").live('click', function () {

        // Get template item associated with DIV
        var templateItem = $(this).tmplItem();
        console.log(templateItem.data);

        $(templateItem.nodes).addClass("selectedRow");
        var option = $(templateItem.nodes).find(".active");

        if (option.length > 0) {
            deleteCustomer(templateItem);
        }
    });

    $("#unlink").live("click", function (e) {
        e.preventDefault();
        $(this).addClass("active");
    });

    $(".linkedItem").live('click', function () {

        // Get template item associated with DIV
        var templateItem = $(this).tmplItem();
        console.log(templateItem.data);

        $(templateItem.nodes).addClass("selectedRow");
        var option = $(templateItem.nodes).find(".active");

        if (option.length > 0) {
            removeLink(templateItem);
        }
    });


    $('#hideLinkedCustomers, #hideLinkedAccounts').on('click', function () {
        filterList();
    });

    filterList();

});

function filterList() {
    towbook.log('fillter');
    $('#LinkResultTable tr').each(function (index, item) {
        if ($('#hideLinkedAccounts').is(':checked'))
            $('#cbTBAccounts option[value="' + $(item).data('accountid') + '"]').remove();

        if ($('#hideLinkedCustomers').is(':checked'))
            $('#cbQBCustomers option[value="' + $(item).data('qbcustomerid') + '"]').remove();
    });

    if (!$('#hideLinkedAccounts').is(':checked')) {
        $("select#cbTBAccounts").html('');
        $.each(accountCustomerData.tbAccounts, function (index, item) {
            $("select#cbTBAccounts").append("<option value='" + item.id + "'>" + item.name + "</option>");
        });
    }

    if (!$('#hideLinkedCustomers').is(':checked')) {
        $("select#cbQBCustomers").html('');
        $.each(accountCustomerData.providerCustomers, function (index, item) {
            $("select#cbQBCustomers").append("<option value='" + item.id + "'>" + item.company + "</option>");
        });
    }

    $('#cbTBAccounts').combobox();
    $('#cbQBCustomers').combobox();

    $('#cbTBAccounts').setVal("");
    $('#cbQBAccounts').setVal("");
}


function CollapseDiv(div)
{
    $(div).next("div").slideToggle(500);
    if (div.innerHTML === "HIDE") {
        div.innerHTML = "SHOW";
    }
    else {
        div.innerHTML = "HIDE";
    }  
}

function removeLink(item) {

    if (confirm('Are you sure you want to delete this link?')) {
        $.ajax({
            type: "DELETE",
            url: "/api/integration/accounts?accountKeyValueId=" + item.data.accountId,
            contentType: "application/json; charset=utf-8"
        }).done(function (response) {
            if (response) {
                $(item.nodes).remove();
            }
            else {
                alert("Error deleting");
            }
        });
    }

}