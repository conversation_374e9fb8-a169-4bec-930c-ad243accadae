using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;

public partial class Settings_Company_TaxItem : System.Web.UI.Page
{
	protected int _id;
	protected Extric.Towbook.TaxRate _ri;

	protected void Page_Load(object sender, EventArgs e)
	{
		if (Request.QueryString["id"] != null)
		{
			_id = Convert.ToInt32(Request.QueryString["id"]);
			_ri = new Extric.Towbook.TaxRate(_id, Global.CurrentUser.CompanyId);
			
			if (_ri.Company.Id != Global.CurrentUser.Company.Id)
				throw new Extric.Towbook.TowbookException("Access Denied");

			lblTitle.Text = "Modify Existing Tax Rate";
			if (!IsPostBack)
			{
				
				txtDescription.Text = _ri.Description;
				txtRate.Text = _ri.Rate.ToString();

				taxMileage.Checked = _ri.TaxMileage;
				taxStorage.Checked = _ri.TaxStorage;
				taxWinching.Checked = _ri.TaxWinching;
			}
		}
		else
		{
			lblTitle.Text = "New Tax Rate";
			_ri = new Extric.Towbook.TaxRate();
			_ri.Company = Global.CurrentUser.Company;
		}
		Response.Cache.SetNoStore();
	}
	protected void Button3_Click(object sender, EventArgs e)
	{
		_ri.Delete();
        
		Response.Redirect("Default.aspx?tab=tabTaxes");
		Response.End();

		return;
	}

	protected void Button1_Click(object sender, EventArgs e)
	{
		_ri.Rate = Convert.ToDecimal(txtRate.Text);
		_ri.Description = txtDescription.Text;
		_ri.TaxMileage = taxMileage.Checked;
		_ri.TaxStorage = taxStorage.Checked;
		_ri.TaxWinching = taxWinching.Checked;
		_ri.Save();
		_ri.Company.TaxMode = Extric.Towbook.Company.Company.TaxModeEnum.Multiple;
		_ri.Company.Save();

        Response.Redirect("Default.aspx?tab=tabTaxes");
		Response.End();

		return;
	}
}
