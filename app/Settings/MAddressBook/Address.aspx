<%@ Page Title="Add a new address book entry" Language="C#" MasterPageFile="~/UI/Dialog.master" AutoEventWireup="true" CodeFile="Address.aspx.cs" Inherits="Settings_AddressBook_Address" %>

<asp:Content ID="Content1" ContentPlaceHolderID="Head" Runat="Server">
<style type="text/css">
strong {display: block }
.tbox { border: solid 1px #afafaf; padding: 2px; margin-bottom: 3px; width: 99%; margin-top: 3px }
</style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="Content" Runat="Server">

<script type="text/javascript">
    function doAdd() {
        var formSaved = $('form1').serialize(true);

        new Ajax.Request('Address.aspx',
        {
            parameters: $('form1').serialize(true),
            onSuccess: function(transport) {
                eval(transport.responseText);

                if (aspdata.error != null) {
                    alert("An error occured..." + aspdata.error);
                }
                else {
                    if (aspdata.id > 0) {
                        parent.addContact(formSaved, aspdata.id);
                    }
                    parent.closeLightbox();
                }

            },
            onFailure: function() {
                alert('Something went wrong...' + transport.responseText)
            }
        }); 
        
    }
    
</script>

<div style="margin-right: 10px; width: 500px">

<input type="hidden" value="<% =_id %>" name="Id" />

<strong>Address Book Entry Name</strong>
<asp:TextBox runat="server" ID="name" class="tbox" />

<strong style="margin-top: 10px">Address</strong>
<asp:TextBox runat="server" ID="address" class="tbox" />

<table style="width: 100%; border-collapse; collapse; margin-top: 5px" cellpadding="0">
<tr>
  <td style="width: 30%; padding-right: 5px"><strong>City</strong>
    <asp:TextBox runat="server" ID="city" class="tbox" style="width: 310px"/>
  </td>
  <td style="padding-right: 5px"><strong><% = (Global.CurrentUser.Company.LocaleStateName) %></strong>
    <asp:TextBox runat="server" ID="state" class="tbox" style="width: 75px" />
  </td>
  <td><strong><% = (Global.CurrentUser.Company.LocaleZipCode )%></strong>
    <asp:TextBox runat="server" ID="zip" class="tbox"  style="width: 78px" />
  </td>
</tr>
</table>

<strong style="margin-top: 10px">Phone Number</strong>
<asp:TextBox runat="server" ID="phone" Width="310px" class="tbox" />





</div>

</asp:Content>
<asp:Content ID="Content3" ContentPlaceHolderID="Buttons" Runat="Server">
    <input type="button" <% if (_id == 0) { Response.Write("onclick=\"doAdd();\""); } else { Response.Write("onclick=\"$('form1').submit()\""); } %> value="Save" class="sm" />
</asp:Content>

