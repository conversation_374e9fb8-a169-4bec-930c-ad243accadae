using System;
using System.Linq;
using System.Web;
using Extric.Towbook.Dispatch;
using System.IO;
using System.Net;
using Extric.Towbook.WebShared;
using System.Collections.ObjectModel;
using Extric.Towbook;
using Extric.Towbook.Utility;
using Extric.Towbook.API.Models;
using Extric.Towbook.API.Models.Calls;
using System.Threading.Tasks;
using System.Web.UI;

public partial class Dispatch_Vrd : System.Web.UI.Page
{
    public class EntryDamageRegionLocal : EntryDamageRegion
    {
        public int Number { get; set; }
        public string RegionName { get; set; }
        public string TypeName { get; set; }
    }

    public string Error = "";
    public Entry Call;
    public EntryAsset Asset;
    public EntryDamage Damage;
    public EntryDamagePhoto Signature;
    public Collection<EntryDamageRegionLocal> DamageRegions;
    public Collection<EntryDamagePhoto> DamagePhotos;
    public Collection<DamageVideoModel> DamageVideos;

    public string[] RegionNames;
    public string[] DamageTypes;
    public int[][] photos;
    public string Disclaimer;
    public string CompanyLogoPath;
    public string _domain;
    public string vdrClassName;
    public string vdrClassImageName;
    public string reportSubtitle;

    public bool isSample = false;

    public string CompanyName;
    public string CompanyAddress;
    public string CompanyPhone;
    public DateTime FormCreateDate;

    protected void Page_Load(object sender, EventArgs e)
    {
        RegisterAsyncTask(new PageAsyncTask(PageLoadAsync));
    }

    private async Task PageLoadAsync()
    {
        var id = Convert.ToInt32(Request.QueryString["id"]);
        _domain = WebGlobal.GetDomain();

        isSample = Request.QueryString["sample"] == "1" && id == 1;

        var entryDamage = EntryDamage.GetById(id);
        if (entryDamage == null)
        {
            throw new TowbookException("Resource with id \"" + id + "\" does not exist.");
        }

        Entry call = Entry.GetById(entryDamage.DispatchEntryId);
        var local = false;

        if (Global.CurrentUser == null && Request.CheckAuthentication(call.CompanyId)) 
        {
            local = true;
        }

        if (call == null || (!local && !WebGlobal.CurrentUser.HasAccessToCompany(call.CompanyId)))
        {
            if (!isSample)
                throw new TowbookException("You don't have permission to view this damage form.");
            else
            {
                // necessary changes to dummy call for sample to work
                call.Company = WebGlobal.CurrentUser.Company;
            }
        }

        vdrClassName = "";
        vdrClassImageName = "";

        if (entryDamage.ClassId == 2)
        {
            vdrClassName = "vdr-map-truck1";
            vdrClassImageName = "Extric_truck_1.png";
        }
        else if (entryDamage.ClassId == 3)
        {
            vdrClassName = "vdr-map-truck2";
            vdrClassImageName = "Extric_truck_2.png";
        }
        else if (entryDamage.ClassId == 4)
        {
            vdrClassName = "vdr-map-van";
            vdrClassImageName = "Extric_van.png";
        }
        else
        {
            vdrClassName = "vdr-map-sedan";
            vdrClassImageName = "extric_sedan.png";
        }

        CompanyName = call.Company.Name;
        CompanyAddress = call.Company.Address;
        CompanyPhone = call.Company.Phone;
        FormCreateDate = isSample ? DateTime.Now.AddHours(-24) : Core.OffsetDateTime(call.Company, entryDamage.CreateDate, false);
        Call = call;

        foreach (var a in call.Assets)
        {
            if (a.Id == entryDamage.AssetId)
            {
                this.Asset = a;
            }
        }

        if (Request.QueryString["pdf"] == "1")
        {
            // Get this page as HTML
            var html = "";
            var sw = new StringWriter();

            Server.Execute(Request.Path + "?id=" + id.ToString() + (isSample ? "&sample=1" : ""), sw, true);
            html = sw.ToString();

            var streamResult = await GeneratePdf(html, null, _domain);
            var filename = String.Format("[Vehicle Damage Report] Call #{0} - {1} {2} {3}.pdf", call.CallNumber, this.Asset.Year, this.Asset.Make, this.Asset.Model);

            // Create the response
            var response = HttpContext.Current.Response;
            response.Clear();
            response.ClearHeaders();
            response.ContentType = "application/pdf";
            //response.AddHeader("Content-Disposition", "attachment; filename=" + filename);

            // Save PDF to response
            await streamResult.CopyToAsync(response.OutputStream);
            response.End();
        }
        else
        {
            var drList = VehicleDamage.Regions;
            var dtList = VehicleDamage.Types;

            string[] regionNames = new string[drList.Length];
            string[] damageTypes = new string[dtList.Length];

            foreach (var dr in drList)
                regionNames[dr.Id] = dr.Name;

            foreach (var dt in dtList)
                damageTypes[dt.Id] = dt.Name;


            var disclaimers = new Collection<Extric.Towbook.Company.InvoiceDisclaimer>();
            if (isSample)
            {
                disclaimers.Add(new Extric.Towbook.Company.InvoiceDisclaimer()
                {
                    CompanyId = WebGlobal.CurrentUser.CompanyId,
                    Disclaimer = "I seek assistance with above claim voluntarily and assume full responsibility for any damage or injury caused by utilizing the tow operator service. I have had the opportunity to inspect my vehicle, and have found it to be in good working order, and that no damage has occurred to the vehicle including doors, door frames, paint glass, window tint, rims, lug nuts/ studs, body or under body as a result of the service. I also acknowledge that all equipment such as jacks, lug wrenches, lug nut keys, and special tools that belong to me or the vehicle have been returned to me in good working order, and will not hold the tow operator liable for any issues further not depicted on this report.",
                });
            }
            else
            {
                disclaimers = Extric.Towbook.Company.InvoiceDisclaimer.GetByCompanyId(call.CompanyId);
                if (call.Impound)
                    disclaimers = disclaimers.Concat(Extric.Towbook.Company.InvoiceDisclaimer.GetImpoundDisclaimerByCompanyId(call.CompanyId)).ToCollection();
            }

            this.Damage = entryDamage;
            this.DamagePhotos = EntryDamagePhoto.GetPhotosByDispatchEntryDamageId(entryDamage.Id);

            if (call.Company.HasFeature(Extric.Towbook.Generated.Features.Videos))
                this.DamageVideos = EntryDamageVideo.GetByDispatchEntryDamageId(entryDamage.Id).Select(s => DamageVideoModel.Map(s, call.CompanyId, call.Id)).ToCollection();
            else
                this.DamageVideos = new Collection<DamageVideoModel>();

            this.DamageRegions = entryDamage.Damages.Select((o, i) =>
                new EntryDamageRegionLocal()
                {
                    Number = i + 1,
                    Id = o.Id,
                    RegionId = o.RegionId,
                    RegionName = o.RegionId < regionNames.Length ? regionNames[o.RegionId] : "",
                    Description = o.Description,
                    CreateDate = o.CreateDate,
                    TypeId = o.TypeId,
                    TypeName = o.TypeId < damageTypes.Length ? damageTypes[o.TypeId] : "",
                    DispatchEntryDamageId = o.DispatchEntryDamageId,
                    AssetId = o.AssetId,
                    DispatchEntryId = o.DispatchEntryId
                }).ToCollection();

            this.Signature = EntryDamagePhoto.GetSignature(entryDamage.Id);
            this.Disclaimer = "";
            if (disclaimers.Any())
            {
                if (Call.Impound)
                {
                    // Get the first impound disclaimer, or the first invoice disclaimer
                    this.Disclaimer = disclaimers.OrderByDescending(d => d.Impound).First().Disclaimer;
                }
                else
                {
                    // Get the first invoice disclaimer, or nothing
                    var disclaimer = disclaimers.FirstOrDefault(d => d.Impound == false);
                    if (disclaimer != null) this.Disclaimer = disclaimer.Disclaimer;
                }
            }

            reportSubtitle = "";
            if (Asset.Vin != null)
            {
                reportSubtitle = Asset.Vin + ", ";
            }
            if (Asset.LicenseNumber != null)
            {
                reportSubtitle += Asset.LicenseNumber + " " + Asset.LicenseState;
            }
// <% if (Asset.Vin != null) { %> Asset.Vin + ", " <% } %>  <%= Asset.LicenseNumber + " " + Asset.LicenseState %>
            #region Get Company Logo

            var cl = Extric.Towbook.Company.CompanyLogo.GetByCompanyId(call.Company.Id);
            if (cl != null)
            {
                CompanyLogoPath = cl.Url;
            }
            else
            {
                string[] opportunities = new string[] { "_template.jpg", "_full.jpg", "_left.jpg", ".jpg" };

                string baseLocal = Server.MapPath(@"..\ui\images\customer.logo\" + call.Company.Id);
                string baseRemote = "https://app.towbook.com/ui/images/customer.logo/" + call.Company.Id;
                string filename = "";

                foreach (var file in opportunities)
                {
                    if (File.Exists(baseLocal + file))
                    {
                        filename = baseRemote + file;
                        break;
                    }
                }

                string companyLogoPath = "";
                if (!string.IsNullOrEmpty(filename))
                    companyLogoPath = filename;

                this.CompanyLogoPath = companyLogoPath;
            }
            #endregion
        }
    }

    private static async Task<MemoryStream> GeneratePdf(string html, HttpRequest request = null, string domain = "")
    {
        if (request == null)
        {
            request = HttpContext.Current.Request;
        }

        var footer = "<div style=\"color: #333333; font-family: verdana; font-size: 9px; font-weight: bold\"><br /><br />" +
            "Created with Towbook Management Software | www.towbook.com " +
            "<div style=\"display:inline-block; float:right; font-weight: normal;\">Printed " + DateTime.Now.ToShortDate() + "</div></div>";

        var baseUrl = request.Url.Host + (request.Url.Port == 80 ? "/" : ":" + request.Url.Port + "/");
        var outputArea = new OutputArea(0.3f, 0.3f, 7.8f, 10.3f);
        // Convert HTML to PDF
        //foreach (string key in request.Cookies.AllKeys)
        //{
        //    options.Cookies.Add(new Cookie(
        //        request.Cookies[key].Name,
        //        request.Cookies[key].Value,
        //        request.Cookies[key].Path,
        //        request.Url.Host + (request.Url.Port == 80 ? "/" : ":" + request.Url.Port + "/")));
        //}

        return await PdfClientBase.GeneratePdf(
            html,
            baseUrl,
            outputArea,
            null,
            FileType.PDF,
            footer,
            null,
            "Towbook Management Software | www.towbook.com",
            noMarginBottom: true);
    }
}
