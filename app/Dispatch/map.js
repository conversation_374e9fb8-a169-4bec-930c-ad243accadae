/** @jsx React.DOM */

var GoogleMap = React.createClass({  
    propTypes: {
        mapCenterLat: React.PropTypes.number,
        mapCenterLng: React.PropTypes.number
    },
    getDefaultProps: function () {
        return {
            initialZoom: 8,
            mapCenterLat: 42.820170,
            mapCenterLng: -82.503580,
            minZoom: 6
        };
    },
    getInitialState: function() {
        return { 
            map: null,
            directionsDisplay: null,
            directionsService: null,
            callMarkers: [],
            userMarkers: [],
            tomtomMarkers: [],
            pinImages: [],
            pinShadow: null
        }
    },
    componentDidMount: function (rootNode) {
        var self = this;
        var mapOptions = {
            center: this.mapCenterLatLng(),
            zoom: this.props.initialZoom,
            minZoom: this.props.minZoom
        }
       
        var map = new google.maps.Map(document.getElementById('map-canvas'), mapOptions);

        var rendererOptions = {
            map: map,
            markerOptions: {
                visible: false
            },
            preserveViewport: true
        }
        var directionsDisplay = new google.maps.DirectionsRenderer(rendererOptions);

        var directionsService = new google.maps.DirectionsService();
        var pinImages = [];
        var pinColor = "FE7569";
        var pinImage = null;

        // create pin for user/driver
        var markerIconUser = {
            url: '/ui/images/icons/towbook-truck-49.png',
            size: new google.maps.Size(48, 48),
            origin: new google.maps.Point(0, 0),
            anchor: new google.maps.Point(0, 48)
        };
        pinImages.push({pinImage: markerIconUser, type: 'user'});

        // create pins for calls with different statuses
        this.props.statusMap.forEach(function(sm) {
            if(sm.map) {
                var pinColor = sm.htmlColor.replace('#', '');
                pinImage = new google.maps.MarkerImage("http://chart.apis.google.com/chart?chst=d_map_pin_letter&chld=%E2%80%A2|" + pinColor,
                    new google.maps.Size(21, 34),
                    new google.maps.Point(0, 0),
                    new google.maps.Point(10, 34));

                var pinImageSelected = new google.maps.MarkerImage("http://chart.apis.google.com/chart?chst=d_map_pin_letter&chld=%E2%80%A2|" + pinColor,
                    new google.maps.Size(26, 42),
                    new google.maps.Point(0, 0),
                    new google.maps.Point(12, 42),
                    new google.maps.Size(26, 42));

                pinImages.push({statusId: sm.id, pinColor: pinColor, pinImage: pinImage, pinImageSelected: pinImageSelected, type: 'call'});
            }
        });

        var pinShadow = new google.maps.MarkerImage("http://chart.apis.google.com/chart?chst=d_map_pin_shadow",
            new google.maps.Size(40, 37),
            new google.maps.Point(0, 0),
            new google.maps.Point(12, 35));


        $.ajax({ url: "/api/integration/gps/providers/tomtom/drivers" }).done(function (d) {
            towbook.tomTomUnits = d;
            $.ajax({ url: "/api/integration/gps/providers/tomtom/vehicles" }).done(function (v) {
                $(v).each(function (i, y) {
                    var driver = $.grep(towbook.tomTomUnits, function (o) { return o.currentUnitId == y.id })[0]; console.log(driver);
                    $.extend(driver, { _truck: y }); console.log(driver);
                });

                $(towbook.tomTomUnits).each(function (i, o) {
                    var pinImage = self.state.pinImages[0].pinImage;
                    var marker = addMarker(i, o.name, o._truck.latitude / 1000000, o._truck.longitude / 1000000, null, pinImage, 'tomtom');
                    self.state.tomtomMarkers.push({id: i, marker: marker});
                });

            });
        });

        $.ajax({ url: '/api/location/currentlocations' }).done(function (v) {
            $(v).each(function (i, y) {
                var user = $.grep(towbook.users, function (o) { return o.id == y.userId })[0];
                $.extend(user, { _gps: y });
                console.log(user);
            });

            $(towbook.users).each(function (i, o) {
                if (o._gps != null) {
                    var pinImage = self.state.pinImages[0].pinImage;
                    var marker = self.addMarker(o.id, o.name, o._gps.latitude, o._gps.longitude, null, pinImage, 'user');
                    self.state.userMarkers.push({id: o.id, marker: marker});
                }
            });
        }).error(function (xhr, status, error) {
            console.log('TOWBOOK GET CURRENT LOCATIONS ERROR!', xhr.responseText);
        });
        

        if (navigator.geolocation) {
            navigator.geolocation.getCurrentPosition(function (position) {
                initialLocation = new google.maps.LatLng(position.coords.latitude, position.coords.longitude);
                map.setCenter(initialLocation);
            });
        }

        if ((navigator.userAgent.match(/iPhone/i)) || (navigator.userAgent.match(/iPod/i)) || (navigator.userAgent.match(/iPad/i)) || (navigator.userAgent.match(/Android/i))) {
            // don't use custom resize code on touch devices...
        } 

        var controlDiv = document.createElement('DIV');
        $(controlDiv).addClass('gmap-control-container')
                     .addClass('gmnoprint');

        var controlUI = document.createElement('DIV');
        $(controlUI).addClass('gmap-control');
        $(controlUI).text('Traffic');
        $(controlDiv).append(controlUI);

        var legend = '<ul>'
                   + '<li><span style="background-color: #30ac3e">&nbsp;&nbsp;</span><span style="color: #30ac3e"> &gt; 50 mph</span></li>'
                   + '<li><span style="background-color: #ffcf00">&nbsp;&nbsp;</span><span style="color: #ffcf00"> 25 - 50 mph</span></li>'
                   + '<li><span style="background-color: #ff0000">&nbsp;&nbsp;</span><span style="color: #ff0000"> &lt; 25 mph</span></li>'
                   + '<li><span style="background-color: #c0c0c0">&nbsp;&nbsp;</span><span style="color: #c0c0c0"> No data available</span></li>'
                   + '</ul>';

        var controlLegend = document.createElement('DIV');
        $(controlLegend).addClass('gmap-control-legend');
        $(controlLegend).html(legend);
        $(controlLegend).hide();
        $(controlDiv).append(controlLegend);

        // Set hover toggle event
        $(controlUI)
            .mouseenter(function () {
                $(controlLegend).show();
            })
            .mouseleave(function () {
                $(controlLegend).hide();
            });

        var trafficLayer = new google.maps.TrafficLayer();

        google.maps.event.addDomListener(controlUI, 'click', function () {
            if (typeof trafficLayer.getMap() == 'undefined' || trafficLayer.getMap() === null) {
                $(controlUI).addClass('gmap-control-active');
                trafficLayer.setMap(map);
            } else {
                trafficLayer.setMap(null);
                $(controlUI).removeClass('gmap-control-active');
            }
        });

        map.controls[google.maps.ControlPosition.TOP_RIGHT].push(controlDiv);

        this.setState({map: map, pinImages: pinImages, pinShadow: pinShadow, directionsService: directionsService, directionsDisplay: directionsDisplay});
    },
    mapCenterLatLng: function () {
        return new google.maps.LatLng(this.props.mapCenterLat, this.props.mapCenterLng);		
    },
    addMarker: function(id, name, lat, long, description, pinImage, type){
        var self = this;
        if(id < 1)
            return;

        if(lat == 0 && long == 0)
            return;
        
        if(type == null)
            type == 'call';

        var map = this.state.map;
        var marker = null;
        var infoWindow = null;

        if(description == null)
            description = "";

        if (google == null) {
            console.log("addMarker cant run - google is undefined/null");
            return;
        }

        // new marker
        //console.log("AC: adding marker ", id, name, lat, long);
            
        marker = new google.maps.Marker({
            position: new google.maps.LatLng(lat, long),
            map: map,
            title: name,
            html: "<div style='min-width:100px'><strong>" + name + "</strong><br />" + description + '</div>',
            icon: pinImage,
            zIndex: id
        });

        infoWindow = new google.maps.InfoWindow({
            content: marker.html
        });

        google.maps.event.addListener(marker, "click", function () {
            var callMarker = self.state.callMarkers.filter(function(f) { return f.marker == marker });
            infoWindow.open(map, this);
            if(callMarker.length)
                self.props.handleMarkerClick({waypointId: callMarker[0].id});
        });

        return marker;
    },
    UpdateCallMarker: function(waypoint, call) {
        var self = this;

        if(towbook.isEmpty(call))
            return;

        if(waypoint != null && waypoint.id > 0)
        {
            var marker = towbook.get(self.state.callMarkers, waypoint.id, "id");
            var pinImages = self.state.pinImages.filter(function(f) { return f.type == 'call' });
            var pinImage = towbook.get(pinImages, call.status.id, "statusId");
            
            //Check if marker for waypoint exists.
            var callMarker = this.state.callMarkers.filter(function(f) {
                return f.id == waypoint.id;
            });

            if(callMarker.length)
            {
                marker.marker.setIcon(pinImage.pinImage);
                marker.marker.setVisible(true);
            }
            else
            {
                //Create new marker
                var marker = self.addMarker(waypoint.id, "Call #" + call.callNumber + "<br>" + waypoint.title + " - " + waypoint.address, waypoint.latitude, waypoint.longitude, null, pinImage != null ? pinImage.pinImage : null);
                if(marker != null)
                    this.state.callMarkers.push({id: waypoint.id, callId: call.id, marker: marker, type: 'call', statusId: call.status.id});
            }

        }
    },
    // Use this method to animate call markers according to the 'type'.
    //      call - the call object to animate
    //      animate - true to turn on, false to turn off
    //      type - Type of animation.
    //          'front' - bring markers to the front via zIndex
    //          'enlarge' - grow the marker by 25% of its original size
    //          'bounce' - bounce the markers using google's BOUNCE animation
    //          'jump' - similar to 'bounce' but will only bounce the markers ONCE
    animateCallMarkers: function(call, animate, type) {
        var self = this;
        var pinImages = self.state.pinImages.filter(function(f) { return f.type == 'call'});
        var topZ = 0;

        // reset call marker to default
        this.state.callMarkers.forEach(function(cm) {
            var marker = towbook.get(call.waypoints, cm.id, "id");
            if(marker != null)
            {
                var pinImage = towbook.get(pinImages, call.status.id, "statusId");
                // reset icon
                if(pinImage != null)
                    cm.marker.setIcon(pinImage.pinImage);
                // reset zIndex
                cm.marker.zIndex = cm.id;
                
                if(!animate)
                    cm.marker.setAnimation(null);    
            }

            // get the highest zIndex value
            topZ = topZ > cm.id ? topZ : cm.id;
        });

        if(animate == null || animate == false)
            return; // just a 'reset' occured.

        if(type == null)
            type = 'front';

        // find markers that belong to call and animate them
        var markers = []
        this.state.callMarkers.forEach(function(cm) {
            var marker = towbook.get(call.waypoints, cm.id, "id");
            if(marker != null)
            {
                markers.push(cm.marker);
                var pinColor = towbook.get(self.props.statusMap, call.status.id, "id");
                var pinImage = towbook.get(pinImages, call.status.id, "statusId");
                
                if(type.indexOf('enlarge') != -1)
                    cm.marker.setIcon(pinImage.pinImageSelected);

                self.animateMarker(cm.marker, type, topZ);
            }
        });
        
        if(markers.length == 2)
        {
            var start = new google.maps.LatLng(markers[0].latitude, markers[0].longitude);
            var end = new google.maps.LatLng(markers[1].latitude, markers[1].longitude);
            this.calcCallRoute(call);
        }
        else if (markers.length == 1)
        {
            if(!this.state.map.getBounds().contains(markers[0].getPosition()) || !this.state.map.getBounds().contains(markers[1].getPosition()))
            {
                this.state.map.setCenter(markers[0]);
            }
        }
        
    },
    animateUserMarker: function(id, animate, type) {
        var self = this;
        var topZ = 0;

        // reset call marker to default
        this.state.userMarkers.forEach(function(user) {
            // reset zIndex
            user.marker.zIndex = id;
                
            if(!animate)
                user.marker.setAnimation(null);   

            // get the highest zIndex value
            topZ = topZ > user.id ? topZ : user.id;

            if(animate == null || animate == false)
                return;
            else
            {
                var u = towbook.get(towbook.users, user.id, "id");
                var driver = towbook.get(towbook.drivers, id, "id");
                if(u != null && driver != null)
                    if(u.name == driver.name)
                        self.animateMarker(user.marker, type, topZ);
            }
        });
    },
    // marker: the actual marker to animate
    // Valid type: 'front', 'bounce', 'jump'
    // topZ is used for 'front' and should be the highest zIndex of similar markers.
    animateMarker: function(marker, type, topZ) {
        if(marker == null)
            return;
        
        if(type.indexOf('front') != -1)
            marker.zIndex = topZ + 1;

        if(type.indexOf('bounce') != -1)
        {
            if(marker.getAnimation() == null)
                marker.setAnimation(google.maps.Animation.BOUNCE);
        }

        if(type.indexOf('jump') != -1)
        {
            if(marker.getAnimation() == null)
            {
                marker.setAnimation(google.maps.Animation.BOUNCE);
                setTimeout(function() {
                    marker.setAnimation(null);
                }, 700);
            }
        }
    },
    calcCallRoute: function(call) {
        if(call.waypoints != null) {
            var pickup = null;
            var destination = null;
            call.waypoints.forEach(function(wp) {
                if(wp.title == 'Pickup')
                    pickup = wp;
                if(wp.title == 'Destination')
                    destination = wp;
            });

            if(pickup != null && destination != null)
            {
                if(this.calcRoute(pickup.latitude, pickup.longitude, destination.latitude, destination.longitude) == false)
                    console.log("AC: Can't show call route because the starting or ending latitudes or longitudes are null");
            }
        }
    },
    // 
    calcRoute: function(startLat, startLong, endLat, endLong) {
        if(startLat == null || startLong == null || endLat == null || endLong == null)
            return false;
        
        var self = this;
        var start = new google.maps.LatLng(startLat, startLong);
        var end = new google.maps.LatLng(endLat, endLong);

        this.state.directionsService.route({origin: start, destination: end, travelMode: google.maps.TravelMode.DRIVING}, 
            function(response, status) {
                if(status == google.maps.DirectionsStatus.OK)
                    self.state.directionsDisplay.setDirections(response);
                else
                    console.log("TOWBOOK Msg: Status of Directions Query is not OK and returned as " + status);
               
                if (!self.state.map.getBounds().contains(start) || !self.state.map.getBounds().contains(end))
                {
                    var bounds = new google.maps.LatLngBounds(start, end);
                    self.state.map.setCenter(bounds.getCenter());
                    if (!self.state.map.getBounds().contains(start) || !self.state.map.getBounds().contains(end))
                        self.state.map.fitBounds(response.routes[0].bounds);
                }
                
                    
            });

        return true;
    },
    // This method will return the calls that have "map=true" in the statusMap field
    getDisplayCalls: function() {
        var self = this;
        var calls = [];
        if(!towbook.isEmpty(this.props.data)) {
            calls = this.props.data.filter(function(c) {
                var status = towbook.get(self.props.statusMap, c.status.id, "id");
                if(status != null && status.map)
                    return c;
            });
        }
        return calls;
    },
    render: function () {
        var self = this;
        var calls = this.getDisplayCalls();

        // hide all markers
        this.state.callMarkers.forEach(function(cm) {
            cm.marker.setVisible(false);
        });

        // add and/or show markers
        calls.forEach(function(c) {
            if(c.waypoints != null) {
                c.waypoints.forEach(function(wp) {
                    self.UpdateCallMarker(wp, c);
                });
            }
        });

        return (
            <div id="map-canvas" style={{top: this.props.top + 'px', backgroundColor: 'white'}}><div className='map'></div></div>
        );
    }
});