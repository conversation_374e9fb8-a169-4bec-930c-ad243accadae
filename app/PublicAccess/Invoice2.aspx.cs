using Extric.Towbook;
using Extric.Towbook.Dispatch;
using Extric.Towbook.Storage;
using Extric.Towbook.Utility;
using Extric.Towbook.WebShared;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

// 10:54 AM 12/15/2016 xdansmith ktl
namespace TWS.Invoices
{
    public partial class Invoice2 : System.Web.UI.Page
    {
        protected Extric.Towbook.Dispatch.Entry _de;
        protected Extric.Towbook.Impounds.Impound _impound;
        protected decimal _tax;
        protected decimal _taxableAmount;
        protected decimal _subTotal;

        protected decimal _grandTotal;
        protected string _invoiceNumber;
        protected bool _hideInvoiceNumber;
	    protected Extric.Towbook.Company.Company company;

        protected bool _hideCharges = false;
        protected bool _hideDiscount = false;

	    public List<string> HideFields = new List<string>();

        public string signatureUrl;
        protected bool _showDriverSignature;
        protected string _driverSignatureName = "";

        public string _domain;

        protected bool HidePhotoLink
	    {
		    get
            {
			    var rf = Extric.Towbook.Integration.CompanyKeyValue.GetByCompanyId(_de.Company.Id, 
				    Extric.Towbook.Integration.Provider.Towbook.ProviderId, 
				    "Towbook_Calls_NeverShowPhotoLink").FirstOrDefault();

                var ahp = Extric.Towbook.Integration.AccountKeyValue.GetByAccount(_de.CompanyId,_de.AccountId,
                    Extric.Towbook.Integration.Provider.Towbook.ProviderId,
                    "AlwaysHidePhotos").FirstOrDefault();

			    if (rf != null && rf.Value == "1")
				    return true;
                else if (ahp != null && ahp.Value == "1")
                    return true;
			    else
				    return false;
    		}
	    }

        protected bool IsMultiAddressTow
        {
            get
            {
                return Extric.Towbook.Dispatch.CallModels.PublicCallModelExtensions.IsMultiAddressCall(_de);
            }
        }

        public static string _(string html)
        {
            return HttpUtility.HtmlEncode(html);
        }

        public string GetUserSignatureUrl(int driverId)
        {
            var driver = Driver.GetById(driverId);
            if (driver != null && driver.UserId > 0)
            {
                _driverSignatureName = driver.Name;

                var user = Extric.Towbook.User.GetById(driver.UserId);
                if (user != null)
                {
                    var userSig = UserSignature.GetByUserId(user.Id);
                    if (userSig != null)
                    {
                        var userSigAgr = UserSignatureAgreement.GetBySignatureType(user.Id, 3 /* Invoice Authorization */);
                        if (userSigAgr != null && userSigAgr.IsVoid == false)
                        {
                            string path = FileUtility.GetPresignedUrlForDownloadFromClient(userSig.Location, userSig.ContentType);
                            if (path != null)
                            {
                                return path;
                            }
                        }
                    }

                }
            }
            return null;
        }

        protected void Page_Load(object sender, EventArgs e)
        {
            RegisterAsyncTask(new PageAsyncTask(PageLoadAsync));
        }

        protected async Task PageLoadAsync()
        {
            try
            {
                _de = new Extric.Towbook.Dispatch.Entry(Convert.ToInt32(Request.QueryString["id"]));
                company = _de.Company;
                if (_de.Impound)
                {
                    _impound = Extric.Towbook.Impounds.Impound.GetByDispatchEntry(_de);
                }
            }
            catch
            {
                Response.Write("Invalid Invoice ID. Check the address entered and try again");
                Response.End();
            }

            _domain = WebGlobal.GetDomain();

            var link = Extric.Towbook.Core.GetRedisValue("pl:" + _de.Id);
            if (link != null && _de.BalanceDue > 0)
            {
                Response.Redirect(link);
                return;
            }

            var mississippiInvoice = (company.State.ToUpperInvariant() == "MS" || company.State.ToUpperInvariant() == "MISSISSIPPI") &&
                (_de.Account.Type == Extric.Towbook.Accounts.AccountType.PoliceDepartment || _de.Account.Type == Extric.Towbook.Accounts.AccountType.PrivateProperty);

            if (!String.IsNullOrWhiteSpace(_de.Company.Name))
                lblCompanyName.Text = _(_de.Company.Name);

            lblAddress.Text = _(_de.Company.Address + ", " + 
                _de.Company.City + " " +
                _de.Company.State + " " +
                _de.Company.Zip);

            if(!String.IsNullOrWhiteSpace(_de.Company.Phone))
                lblPhone.Text = "Phone: " + _(_de.Company.Phone);

            if (!String.IsNullOrWhiteSpace(_de.Company.Fax))
                lblFax.Text = "Fax: " + _(_de.Company.Fax);

            InvoiceOptions io = InvoiceOptions.GetByCompanyId(_de.Company.Id);

            if (io.ShowCompanyEmail && Core.IsEmailValid(company.Email))
                lblEmail.Text = "Email: " + _(_de.Company.Email);

            _showDriverSignature = io.IncludeDriverSignature;

            var companyBillingAddress = (await Extric.Towbook.Company.AddressBookEntry.GetByCompanyAsync(company)).Where(o => o.Name == "Billing Address").SingleOrDefault();

            if (companyBillingAddress != null && !String.IsNullOrWhiteSpace(companyBillingAddress.ToString()))
            {
                lblAddress.Text = _(companyBillingAddress.ToString());
            }

            string md5 = "";

            if (_de.Id > 3502200 || Request.QueryString["sc"].Length == 10)
            {
                md5 = Extric.Towbook.Core.ProtectId(_de.Id, _de.OwnerUserId);
            }
            else
            {
                md5 = Extric.Towbook.Core.MD5(_de.Id.ToString());
                md5 = md5.Substring(md5.Length - 4);
            }

            if (Request.QueryString["sc"] != md5)
            {
                Response.Write("Invalid Invoice URL. Please make sure you specified the correct address and try again.");
                Response.End();
                throw new Exception("Access Denied...");
            }

            if (Request.QueryString["p"] != null)
                _hideCharges = Extric.Towbook.Core.ProtectId(_de.CallNumber, 1) == Request.QueryString["p"];

            if (Request.QueryString["d"] != null)
                _hideDiscount = Extric.Towbook.Core.ProtectId(_de.CallNumber, 1) == Request.QueryString["d"];

            _de.Invoice.IgnoreDiscount = _hideDiscount;

            if (_de.CompanyId == 26556 || _de.CompanyId == 28172 || _de.CompanyId == 28173)
                _hideCharges = true;

            rpInvoiceItems.DataSource = _de.Invoice.InvoiceItems.Where(o => o.ClassId != 4 && o.Total != 0 && !o.Name.Contains("FreeQuant"));

            rpInvoiceItems.DataBind();

            _invoiceNumber = _("#" + DateTime.Now.Year.ToString().Substring(2, 2) + "-" + _de.CallNumber.ToString().PadLeft(5, '0'));

            if (company.Id == 12802)
            {
                io.HideAccountContact = true;
                io.HideAccountAddress = true;
                io.HideNotes = true;

                HideFields.Add("Photos");
            }

            lblVehicle.Text = "";

            if (_de.Assets != null && _de.Assets.Count > 0)
            {
                var assetNumber = 0;

                foreach (var asset in _de.Assets)
                {
                    if (_de.Assets.Count > 1)
                    {
                        assetNumber++;
                        if (assetNumber == 1)
                        {
                            lblVehicle.Text += "<h3>Vehicle " + assetNumber + " Summary</h3><div class=\"box\"><table>";
                        }
                        else
                        {
                            lblVehicle.Text += "<h3 style=\"border-top-width: 1px; border-top-style: solid; border-top-color: rgb(175, 175, 175);\">Vehicle " + assetNumber + " Summary</h3><div class=\"box\"><table>";
                        }
                    }
                    else
                    {
                        lblVehicle.Text += "<h3>Vehicle Summary</h3><div class=\"box\"><table>";
                    }

                    if (asset != null)
                    {
                        if (!String.IsNullOrWhiteSpace(asset.Vin))
                        {
                            lblVehicle.Text += "<tr><td>VIN Number:</td><td>" + _(asset.Vin) + "</td></tr>";
                        }

                        var color = Extric.Towbook.Vehicle.Color.GetById(asset.ColorId);
                        if (asset.Year > 0 || !String.IsNullOrWhiteSpace(asset.Make + " " + asset.Model) || color != null)
                        {
                            lblVehicle.Text += "<tr><td>Model:</td><td>" + _(asset.Year > 0 ? asset.Year.ToString() : "") + " " +
                                _(asset.Make + " " + asset.Model) + (color != null ? " (" + _(color.Name) + ")" : "") + "</td></tr>";
                        }

                        if (asset.Odometer > 0)
                        {
                            lblVehicle.Text += "<tr><td>Odometer:</td><td>" + asset.Odometer + "</td></tr>";
                        }

                        if (asset.LicenseNumber != null && asset.LicenseNumber.Length > 0)
                        {
                            lblVehicle.Text += "<tr><td>License Plate:</td><td>" + _(asset.LicenseNumber) + _(!string.IsNullOrEmpty(asset.LicenseState) ? " (" + asset.LicenseState + ")" : "") + "</td></tr>";
                        }

                        lblVehicle.Text += "<tr><td>Drivable:</td><td>" + _(asset.Drivable.ToDescription()) + "</td></tr>";
                        lblVehicle.Text += "<tr><td>Keys:</td><td>" + _(asset.Keys == true ? "Yes " + asset.KeysLocation : "No") + "</td></tr>";
                    }

                    lblVehicle.Text += "</table></div>";
                }
            } 
            else
            {
                lblVehicle.Text += "<h3>Vehicle Summary</h3><div class=\"box\"></div>";
            }

            lblTravel.Text += "<table>";

            if (!io.HidePrintDate)
                lblTravel.Text += "<tr><td>Printed:</td><td>" + OffsetDateTime(DateTime.Now).ToShortDateString() + "</td></tr>";

            _hideInvoiceNumber = io.HideInvoiceNumber;

            if (!io.HideInvoiceNumber && _de.Attributes.ContainsKey(AttributeValue.BUILTIN_DISPATCH_CUSTOMINVOICENUMBER))
            {
                lblTravel.Text += "<tr><td>Invoice #</td><td>" + _(_de.Attributes[AttributeValue.BUILTIN_DISPATCH_CUSTOMINVOICENUMBER].Value) + "</td></tr>";
                _invoiceNumber = "#" + _(_de.Attributes[AttributeValue.BUILTIN_DISPATCH_CUSTOMINVOICENUMBER].Value);
            }

            if (!string.IsNullOrWhiteSpace(_de.PurchaseOrderNumber))
            {
                lblTravel.Text += "<tr><td>Purchase Order #</td><td>" + _(_de.PurchaseOrderNumber) + "</td></tr>";
            }

            if (!io.HideCallNumber && !string.IsNullOrWhiteSpace(_de.CallNumber.ToString()))
                lblTravel.Text += "<tr><td>Call #</td><td>" + _de.CallNumber.ToString().PadLeft(5, '0') + "</td></tr>";

            bool showAccountContact = false;

            if (_de.Account != null)
            {
                if (_de.Company.State == "TX" ||
                    _de.Company.State == "Texas" || !io.HideAccountContact)
                {
                    showAccountContact = true;
                }

                if(!String.IsNullOrWhiteSpace(_de.Account.Company))
                    lblTravel.Text += "<tr><td>Account</td><td>" + _(_de.Account.Company) + "</td></tr>";

                string accountAddress = string.Empty;
                if (!io.HideAccountAddress)
                    accountAddress = _(_de.Account.Address + " " + _de.Account.City + " " + _de.Account.State + " " + _de.Account.Zip).Trim();

                if (!string.IsNullOrWhiteSpace(accountAddress))
                    lblTravel.Text += "<tr><td></td><td>" + _(accountAddress) + "</td></tr>";

                if (showAccountContact && !string.IsNullOrWhiteSpace(_de.Account.FullName))
                    lblTravel.Text += "<tr><td>Account Contact</td><td>" + _(_de.Account.FullName) + "</td></tr>";
            }

            if (io.ShowDispatcher)
            {
                var disp = Extric.Towbook.User.GetById(_de.OwnerUserId);
                if (disp != null && !string.IsNullOrEmpty(disp.FullName))
                    lblTravel.Text += "<tr><td>Dispatcher</td><td>" + _(disp.FullName) + "</td></tr>";
            }

            bool isTX = _de.Company.State == "TX" ||
                _de.Company.State == "Texas";

            if (isTX ||
                io.ShowDriver ||
                io.ShowTruck)
            {
                if (io.ShowDriver && _de.Drivers != null)
                {
                    foreach (var driver in _de.Drivers.Select(o => Extric.Towbook.Driver.GetById(o)))
                    {
                        if (driver == null) continue;

                        if(!String.IsNullOrWhiteSpace(driver.Name))
                            lblTravel.Text += "<tr><td>Driver</td><td>" + _(driver.Name) + "</td></tr>";

                        bool alreadyAdded = false;
                        var licenseValues = Extric.Towbook.Licenses.DriverLicenseKeyValue.GetByDriverId(driver.Id);
                        var keys = Extric.Towbook.Licenses.DriverLicenseKey.GetAll();
                        var printables = licenseValues.Where(y => keys.Where(o => o.Id == y.KeyId && o.ShowValueOnPrintables == true).Any());

                        if (isTX && printables.Any())
                        {
                            foreach (var value in printables)
                            {
                                var key = keys.Where(o => o.Id == value.KeyId).FirstOrDefault();
                                if (!String.IsNullOrWhiteSpace(value.Value))
                                {
                                    if (driver.LicenseNumber != null && driver.LicenseNumber.Contains(value.Value))
                                        continue;
                                    lblTravel.Text +=
                                    (String.Format("<tr><td style=\"font-weight:bold;\">{0}{2}</td><td>{1}</td></tr>", _(key.Name), _(value.Value), (key.Name.EndsWith("#") ? "" : ":")));
                                    alreadyAdded = true;
                                }
                            }
                        }

                        if (isTX && !alreadyAdded && company.Id != 1312 && company.State.ToUpperInvariant() != "CA" &&
                            !String.IsNullOrWhiteSpace(driver.LicenseNumber) &&
                            company.Id != 2051)
                            lblTravel.Text += "<tr><td>Driver License #</td><td>" + _(driver.LicenseNumber) + "</td></tr>";
                    }
                }

                if (io.ShowTruck && _de.Trucks != null)
                {
                    foreach (var truck in _de.Trucks.Select(o => Extric.Towbook.Truck.GetById(o)))
                    {
                        if (truck == null) continue;

                        if(!String.IsNullOrWhiteSpace(truck.Name))
                            lblTravel.Text += "<tr><td>Truck</td><td>" + _(truck.Name) + "</td></tr>";
                    }
                }
            }

            if (!io.HideCreationTime)
                lblTravel.Text += "<tr><td>Date/Time Requested:</td><td>" + OffsetDateTime(_de.CreateDate).ToShortDateString() + " @ " + OffsetDateTime(_de.CreateDate).ToShortTowbookTimeString() + "</td></tr>";

            if (io.ShowDispatchedDateTime && _de.DispatchTime.HasValue)
                lblTravel.Text += "<tr><td>Date/Time Dispatched:</td><td>" + OffsetDateTime(_de.DispatchTime.Value).ToShortDateString() + " @ " + OffsetDateTime(_de.DispatchTime.Value).ToShortTowbookTimeString() + "</td></tr>";

            if (io.ShowEnrouteDateTime && _de.EnrouteTime.HasValue)
                lblTravel.Text += "<tr><td>Date/Time Enroute:</td><td>" + OffsetDateTime(_de.EnrouteTime.Value).ToShortDateString() + " @ " + OffsetDateTime(_de.EnrouteTime.Value).ToShortTowbookTimeString() + "</td></tr>";

            if ((io.ShowOnSceneDateTime || mississippiInvoice) && _de.ArrivalTime.HasValue)
                lblTravel.Text += "<tr><td>Date/Time Arrival:</td><td>" + OffsetDateTime(_de.ArrivalTime.Value).ToShortDateString() + " @ " + OffsetDateTime(_de.ArrivalTime.Value).ToShortTowbookTimeString() + "</td></tr>";

            if (io.ShowTowingDateTime && _de.TowTime.HasValue)
                lblTravel.Text += "<tr><td>Date/Time Departure:</td><td>" + OffsetDateTime(_de.TowTime.Value).ToShortDateString() + " @ " + OffsetDateTime(_de.TowTime.Value).ToShortTowbookTimeString() + "</td></tr>";

            if (_de.DestinationArrivalTime.HasValue && io.ShowDestinationArrivalDateTime)
            {
                lblTravel.Text += "<tr><td>Date/Time Destination Arrival:</td><td>" +
                    OffsetDateTime(_de.DestinationArrivalTime.Value).ToShortDateString() + " @ " +
                    OffsetDateTime(_de.DestinationArrivalTime.Value).ToShortTowbookTimeString() + "</td></tr>";
            }

            if (io.ShowCompletionDateTime && _de.CompletionTime.HasValue)
                lblTravel.Text += "<tr><td>Date/Time Completed:</td><td>" + OffsetDateTime(_de.CompletionTime.Value).ToShortDateString() + " @ " + OffsetDateTime(_de.CompletionTime.Value).ToShortTowbookTimeString() + "</td></tr>";

            if (_de.Reason != null && !io.HideReason)
                lblTravel.Text += "<tr><td>Reason:</td><td>" + _(_de.Reason.Name) + "</td></tr>";

            if (_de.Contacts.Count > 0)
            {
                foreach (Extric.Towbook.Dispatch.EntryContact c in _de.Contacts)
                {
                    lblTravel.Text += "<tr><td>Customer:</td><td>" + _(c.Name + (String.IsNullOrEmpty(c.Phone) ? "" : "(" + c.Phone + "), ") + c.Address) + "</td></tr>";
                }
            }

            if (_de.TowSource != null && _de.TowSource.Length > 0 && (!io.HidePickupLocation || mississippiInvoice))
                lblTravel.Text += "<tr><td>Tow From:</td><td>" + _(_de.TowSource) + "</td></tr>";

            if (IsMultiAddressTow && (mississippiInvoice || (!io.HidePickupLocation && !io.HideTowDestination)))
            {
                foreach (var wp in _de.Waypoints.Where(w => !new string[] { "Start", "Pickup", "Destination" }.Contains(w.Title)))
                {
                    lblTravel.Text += "<tr><td>" + _(wp.Title) + ":</td><td>" + _(wp.Address) + "";
                }
            }

            if (_de.TowDestination != null && _de.TowDestination.Length > 0 && (!io.HideTowDestination || mississippiInvoice))
                lblTravel.Text += "<tr><td>Tow To:</td><td>" + _(_de.TowDestination) + "</td></tr>";

            if (!String.IsNullOrEmpty(_de.Notes) && !io.HideNotes)
            {
                lblTravel.Text += "<tr><td>Notes</td><td>" + _(_de.Notes) + "</td></tr>";
            }

            lblTravel.Text += "</table>";

            #region add Winching/Cleanup/Storage items
            /*
        if (_de.WinchingFee.HasValue && _de.WinchingFee.Value > 0)
            addLineItem("Cleanup Fees", 1, _de.CleanupFee.Value, _de.Company.TaxWinching);

        if (_de.CleanupFee.HasValue && _de.CleanupFee.Value > 0)
            addLineItem("Winching Fee", 1, _de.WinchingFee.Value, _de.Company.TaxWinching);

        if (_de.Miles.HasValue && _de.Miles.Value > 0)
            addLineItem("Destination/Mileage fee", _de.Miles.Value, _de.MilePrice.Value, _de.Company.TaxMileage);
		*/

            // make sure we havent exceeded maximum

            #endregion

            _tax = _de.InvoiceTax;

            _grandTotal = _subTotal + _tax;

            lblSubtotal.Text = String.Format("{0:C}", _subTotal);
            lblTaxes.Text = String.Format("{0:C}", _tax);
            lblGrandTotal.Text = String.Format("{0:C}", _grandTotal);
            lblAmountDue.Text = String.Format("{0:C}", _de.BalanceDue);

            var photo = Photo.GetByDispatchEntryId(this._de.Id).Where(o => o.Description == "__INVOICE_SIGNATURE").FirstOrDefault();

            if (photo != null)
            {
                string path = Extric.Towbook.Storage.FileUtility.GetPresignedUrlForDownloadFromClient(photo.Location.Replace("%1", company.Id.ToString()), photo.ContentType);
                if (path != null)
                    signatureUrl = path;
                else
                    signatureUrl = null;
            }

            if (signatureUrl == null)
            {
                var sig = Signature.GetByDispatchEntryId(this._de.Id).OrderBy(o => o.DispatchEntrySignatureId).LastOrDefault();

                if (sig != null)
                {
                    signatureUrl = Extric.Towbook.Storage.FileUtility.GetPresignedUrlForDownloadFromClient(
                        sig.Location.Replace("%1", this._de.Company.Id.ToString()), sig.ContentType);
                }
            }
        }

        protected void rpInvoiceItems_ItemDataBound(object sender, RepeaterItemEventArgs e)
        {
            if (e.Item.ItemType == ListItemType.Item ||
                e.Item.ItemType == ListItemType.AlternatingItem)
            {
                Literal name = (Literal)e.Item.FindControl("name");
                Literal quantity = (Literal)e.Item.FindControl("quantity");
                Literal unitPrice = (Literal)e.Item.FindControl("unitprice");
                Literal lineTotal = (Literal)e.Item.FindControl("linetotal");
                Literal iiNote = (Literal)e.Item.FindControl("iiNote");

                decimal cost = 0;

                Extric.Towbook.Dispatch.InvoiceItem i = (Extric.Towbook.Dispatch.InvoiceItem)e.Item.DataItem;
                Extric.Towbook.RateItem ri = null;
                quantity.Text = i.Quantity.ToString();

                if (i.RateItem != null)
                {
                    ri = Extric.Towbook.RateItem.GetById(i.RateItem.RateItemId);
                    name.Text = _(ri.Name);

                    if (ri.TimeRound != null)
                    {
                        decimal mins = i.Quantity * 60;
                        if (ri.TimeStartAtStatusId != null && ri.TimeStopAtStatusId != null && mins != null)
                        {
                            decimal? freeQuantity = Extric.Towbook.RateItem.GetAccountFreeQuantity(ri, _de.Account.Id);
                            name.Text = _(ri.Name + (mins != null ? " (price per hour)" : ""));
                            if (freeQuantity > 0)
                            {
                              name.Text += " (" + freeQuantity + " free minutes applied)";
                            }

                            int hours = (int)Math.Floor((mins / 60));
                            int days = (int)Math.Floor(((double)hours / 24));
                            string daysString = days > 0 ? days + " d " : "";
                            int remainingHours = hours - (days * 24);
                            int remainingMins = (int)mins - (hours * 60);
                            string hourString = remainingHours > 0 ? remainingHours + " h " : "";
                            string minutesString = remainingMins > 0 ? remainingMins + " m" : "";
                            quantity.Text += " (" + daysString + hourString + minutesString + ")";
                        }
                    }
                }
                else
                {
                    name.Text = _(i.CustomName);
                }

                if (!string.IsNullOrWhiteSpace(i.Notes))
                {
                    foreach (var p in i.Notes.Split(new[] { "\r\n", "\n" }, StringSplitOptions.None))
                    {
                        iiNote.Text += _(p) + "<br/>";
                    }
                }

                var free = _de.Invoice.InvoiceItems.Where(o => o.RelatedInvoiceItemId == i.Id).FirstOrDefault();
                if (free != null && free.Name.Contains("FreeQuan") && free.Quantity != 0)
                {
                    // Start with the actual amount of free quantity units used
                    string strQuantity = free.Quantity.ToString();

                    if (i.RateItem != null && _de.Account != null)
                    {
                        // display the full amount for free miles for the account
                        var accFree = Extric.Towbook.RateItem.GetAccountFreeQuantity(i.RateItem, _de.Account.Id);
                        if (accFree != null)
                            strQuantity = accFree.Value.ToString();
                    }

                    var measurement = ((Extric.Towbook.RateItem)i.RateItem).Measurement;

                    if (measurement == Extric.Towbook.RateItem.MeasurementEnum.Minutes ||
                        (ri != null && ri.TimeRound != null && ri.TimeStartAtStatusId != null && ri.TimeStopAtStatusId != null))
                    {
                        name.Text += " (" + strQuantity + " minutes free)";
                    }
                    else if (measurement == Extric.Towbook.RateItem.MeasurementEnum.Units)
                        name.Text += " (" + strQuantity + " unit" + (strQuantity != "1" ? "s" : "") + " free)";
                    else
                        name.Text += " (" + strQuantity + " " + _de.Company.LocaleMile + "s free)";

                    if (i.Total == 0) e.Item.Visible = false;
                }

                if (i.CustomPrice != null)
                {
                    unitPrice.Text = String.Format("{0:C}", i.CustomPrice);
                    cost = i.CustomPrice.Value;
                }
                else
                {
                    if (i.RateItem != null)
                    {
                        decimal myCost = 0;

                        if (i.RateItem.ExtendedRateItems.ContainsKey(_de.BodyType.Id))
                        {
                            myCost = i.RateItem.ExtendedRateItems[_de.BodyType.Id].Amount;
                        }
                        else
                            myCost = i.RateItem.Cost;

                        unitPrice.Text = String.Format("{0:C}", myCost);
                        cost = myCost;
                    }
                }

                decimal tmpMiles = i.Quantity;

                if (i.RateItem != null)
                {
                    if (i.CustomName != null && i.CustomName.Contains("FreeQuantity") && cost < 0)
                    {
                        string unit = "";
                        if (i.RateItem.Predefined != null &&
                            (i.RateItem.Predefined.Id == PredefinedRateItem.BUILTIN_MILEAGE_LOADED ||
                            i.RateItem.Predefined.Id == PredefinedRateItem.BUILTIN_MILEAGE_UNLOADED))
                            unit = " " + _de.Company.LocaleMile + "s";

                        name.Text = "&nbsp;&nbsp;&nbsp;&nbsp;(Credit for " + i.Quantity + " free" + unit + ")";
                    }
                }

                if (free != null)
                {
                    tmpMiles = tmpMiles - Math.Abs(free.Quantity);
                }

                lineTotal.Text = String.Format("{0:C}", cost * tmpMiles);
                _subTotal += (cost * tmpMiles);


                if ((i.RateItem != null && i.RateItem.Taxable) || i.Taxable)
                {
                    _taxableAmount += cost * i.Quantity;
                }

                _grandTotal = _subTotal + _tax;
            }
        }


        protected void addLineItem(string name, decimal quantity, decimal price, bool Taxable)
        {
            addLineItem(name, quantity, price, Taxable, true);
        }

        /// <summary>
        /// Adds a line item 
        /// </summary>
        /// <param name="name"></param>
        /// <param name="quantity"></param>
        /// <param name="price"></param>
        protected void addLineItem(string name, decimal quantity, decimal price, bool Taxable, bool Output)
        {
            if (Output == true)
            {
                lTableData.Text += "<tr>";
                lTableData.Text += "  <td>" + _(name) + "</td>";
                lTableData.Text += "  <td>" + quantity.ToString() + "</td>";
                lTableData.Text += "  <td style=\"text-align: right\">" + String.Format("{0:C}", price) + "</td>";
                lTableData.Text += "  <td style=\"text-align: right\">" + String.Format("{0:C}", price * quantity) + "</td>";
                lTableData.Text += "</tr>";
            }

            if (Taxable)
                _taxableAmount += (price * quantity);

            _subTotal += (price * quantity);


        }

        public DateTime OffsetDateTime(DateTime dt)
        {
		    return Extric.Towbook.Core.OffsetDateTime(_de.Company, dt);
        }
    }

}
