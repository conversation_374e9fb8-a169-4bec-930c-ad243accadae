<%@ Page Language="C#" AutoEventWireup="true" CodeFile="SV.aspx.cs" Inherits="TWS.Invoices.SV" Async="true" %>
<%@ Import Namespace="Extric.Towbook.Storage" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">

<html xmlns="http://www.w3.org/1999/xhtml" >
<head id="Head1" runat="server">
  <title>Subcontractor View</title>
  <meta name="ROBOTS" content="NOINDEX, NOFOLLOW">
  <script type="text/javascript">
        function changeActiveTab(tc, theTab, linkX) {
          theTab = document.getElementById(theTab);

          if (theTab == null || theTab == undefined) {
              return;
          }

          tc = document.getElementById(tc);

          if (document.getElementById("activeTab") != null) {
              if (document.getElementById("activeTab").value == theTab.id) {
                  return;
              }
          }

         if (tc == null) {
              return;
          }

          if (theTab == null) {
              return;
          }
          if (linkX == null) {
              return;
          }
          linkX.parentNode.className = 'selected';

          for (i = 0; i < linkX.parentNode.parentNode.childNodes.length; i++) {
              if (linkX.parentNode.parentNode.childNodes[i] != linkX.parentNode) {
                  linkX.parentNode.parentNode.childNodes[i].className = null;
              }
          }

          for (i = 0; i < tc.getElementsByTagName('DIV').length; i++) {
              if (tc.getElementsByTagName('DIV')[i].className == "TabContainer" && tc.childNodes[i] != theTab) {
                  tc.getElementsByTagName('DIV')[i].style.display = "none";
              }
          }

          theTab.style.display = "block";

          if (document.getElementById("activeTab") != null) {
              document.getElementById("activeTab").setAttribute("value", theTab.id);
          }
      }

  
  </script>
   <style>
       body { margin: 0; padding: 0 }
   * { font-family: Calibri, Verdana; font-size: 14px } 
    #header { height: 80px; background-color: Black; color: White; padding: 10px }
    #header * { color: White }
    .company { font-size: 24px; }
    h3
    {
    	background-color: White;
        padding-top: 8px; padding-left: 10px; 
        padding-bottom: 2px; 
        margin-bottom: 0px; 
        margin-top: 0;
        border-bottom: solid 1px #cfcfcf; border-top: solid 5px #afafaf; border-left: solid 1px #afafaf; border-right: solid 1px #afafaf
    }
    
    #items { width: 100% }
    div.box
    {
    	background-color: #fff;
        border-left: solid 1px #afafaf; 
        border-right: solid 1px #afafaf; 
        border-bottom: solid 1px #afafaf; padding: 10px;
    }
   
    .company { font-size: 24px; }
    #items { width: 100% }
    #charges { padding-top: 5px; padding-left: 10px; padding-right: 10px }

table { border-collapse: separate; padding: 10; margin: 0; border-spacing: 10px}
#twocol { width: 100% }
#twocol td { vertical-align: top; margin: 0; padding: 0; border: solid 1px #afafaf }
#twocol td h3 { border-right: none; padding-left: 5px; border-top: none; border-left: solid 5px #afafaf; }
#twocol td table { border-spacing: 0 }
#twocol td td { border: none; border-spacing: 0 }	
td .box { border: none }

#items { border-spacing: 0 }

body { margin: 0; padding: 0 }

#twocol td td.left { border-bottom: dotted 1px #dfdfdf; padding-left: 12px; padding-top: 5px; padding-bottom: 3px }
#twocol td td.right { border-left: dotted 1px #dfdfdf; border-bottom: dotted 1px #dfdfdf; padding-top: 5px; padding-bottom: 3px; padding-left: 5px }

.TabList 
{
    list-style: none;
    margin: 0;
    padding: 0;
    background-color: black;
    width: 100%;
    float: left;
    padding-left: 10px
}
.TabList li 
{
	float: left;
	display: block;
	border-right: solid 1px #efefef;
}
.TabList li a 
{
	display: block;
    text-decoration: none;
    color: black;
    padding: 10px;
    background-color: white;
    font-size: 100%;
}
.TabList li a:hover
{
    background-color: #efefef;
    color: #000000;
}

.TabList .selected a, .TabList .selected a:hover 
{
	margin-top: 0;
    text-decoration: none;
    color: white;
    background-color: #0d2b2d;
    border-top: solid 1px #0d2b2d;
    border-left: solid 1px #0d2b2d
}

.TabList .selected a:hover
{

}
.TabList .selected,
.TabList .selected a,
.TabList .selected a:focus,
.TabList .selected a:active { outline: 0 }

.TabContainer 
{
    clear:          both; 
    border-top:     solid 0px #afafaf;
    border-left:    solid 1px #afafaf;
    border-right:   solid 1px #afafaf;
    padding: 10px
}

.TabContainer .Padded
{
    padding: 10px;
}
.TabContainer .Padded textarea { margin: 0; margin: 0; display: block; width: 100% }

.TabContainer .Padded input, .TabContainer .Padded select
{ 
    border: none
}

.TabContainer .Overview
{
	margin-left: -1px;
    padding: 10px; 
    font-weight: bold;
    color: #fcfeea; 
    background-color: #1d4144; 
    background-image: url(../images/forms/header.jpg); 
    background-repeat: repeat-y;
}

.TabContainer .SubHeading
{

    border-top: dotted 1px #777777;
    padding: 10px; font-weight: bold; color: #222222; background-color: #efefef; background-repeat: repeat-x;
    cursor: pointer;
    background-image: url(../images/subheader.jpg);
    color: #222222
}


.TabContainerFooter
{
    background-image:   url(../images/forms/footer.jpg);
    background-repeat:  repeat-x;
    padding:            10px; 
    font-weight:        bold; 
    color:              darkred; 
    background-color:   #f9f9f9;
    border:             solid 1px #afafaf; 

}
   </style>
</head>
<body>
    <div>
    
    <input type="hidden" id="activeTab" name="activeTab" value="" />

            <div id="header">
    <div style="float: left">
    <asp:Label runat="Server" ID="lblCompanyName" CssClass="company" /><br />
    <asp:Label runat="Server" ID="lblAddress" /> |
    <asp:Label runat="Server" ID="lblPhone" /> |
    <asp:Label runat="Server" ID="lblFax" />
        <% if (_de.Company.Country == Extric.Towbook.Company.Company.CompanyCountry.Canada)
			{
                Extric.Towbook.Company.Countries.Canada cx = Extric.Towbook.Company.Countries.Canada.GetByCompany(_de.Company);
				if (cx != null && cx.GST.Length > 0) 
					Response.Write("| GST# " + cx.GST + "<br />");
			}
%>
    </div>
    <div style="float: right; font-size: 34px; color: #999999; line-height: 105%">Dispatch Info
<br />
    <span style="color: white; font-size: 16px; font-weight: bold; letter-spacing: 2px">INVOICE <% =_invoiceNumber %></span>
    </div>
    </div>
          <div id="tabControl">
  <ul class="TabList">
    <li class="selected"><a id="atInvoice" href="javascript:;" onclick="changeActiveTab('tabControl', 'tabInvoice', this); ">Invoice</a></li>
    <li><a id="atPhotos" href="javascript:;" onclick="changeActiveTab('tabControl', 'tabPhotos', this);">Photos</a></li>
  </ul>
  
  <div id="tabInvoice"  class="TabContainer">
      <table id="twocol">
      <tr>
        <td style="width: 50%">
            <h3>Basic Details</h3>
            <div class="box"><asp:Label runat="Server" ID="lblTravel" /></div>
        </td>
        <td style="width: 50%">
            <h3>Vehicle Summary</h3>
            <div class="box">
                <asp:Label runat="Server" ID="lblVehicle" />
            </div>
        </td>
      </tr>
    </table>


    
    <div id="charges">

       
        <% if (false){ %>
    <h3>Charges Summary</h3>
<div class="box">
<table id="items">
<%if (_hideCharges == false)
    { %> 
      <tr style="font-weight: bold">
        <td style="height: 21px; width: 70%">Service Item</td>
        <td style="height: 21px">Units/Qty</td>
        <td style="height: 21px; text-align: right">Price</td>
        <td style="height: 21px; text-align: right">Line Total</td>
      </tr>  
      <asp:Label runat="server" ID="lTableData"  />     
      
  <asp:Repeater runat="server" ID="rpInvoiceItems" OnItemDataBound="rpInvoiceItems_ItemDataBound">
  <HeaderTemplate>
    
  </HeaderTemplate>
  <ItemTemplate>
  <tr>
    <td><asp:Literal runat="Server" ID="name" /></td>
    <td><asp:Literal runat="Server" ID="quantity" /></td>
    <td style="text-align: right"><asp:Literal runat="Server" ID="unitprice" /></td>
    <td style="text-align: right"><asp:Literal runat="Server" ID="linetotal" /></td>
  </tr>
  </ItemTemplate>
  <FooterTemplate>
  </FooterTemplate>
  </asp:Repeater>
      <tr><td colspan="1" rowspan="4"></td></tr>
    <tr>
      <td style="padding-top: 15px; text-align: right; font-weight: bold" colspan="2">Subtotal</td>
      <td style="padding-top: 15px; text-align: right"><asp:Label runat="server" ID="lblSubtotal" /></td>
    </tr>
    <tr>
      <td style="text-align: right; font-weight: bold"  colspan="2"><% =(_de.Company.Country == Extric.Towbook.Company.Company.CompanyCountry.Canada ? "GST" : "Taxes")  %></td>
      <td style="text-align: right"><asp:Label runat="server" ID="lblTaxes" /></td>
    </tr>
    <tr>
      <td style="text-align: right; font-weight: bold; font-size: 16px"  colspan="2">Grand Total</td>
      <td style="text-align: right; font-weight: bold; font-size: 16px"><asp:Label runat="server" ID="lblGrandTotal" /></td>
    </tr>
<% } %>

    <% 
        var payments = Extric.Towbook.Dispatch.InvoicePayment.GetByDispatchEntryId(this._de.Id).Where(o => (o.Amount != 0)).ToList();
        if (payments != null && payments.Count > 0) {

    %>
    <tr><td colspan="4" style="height: 5px"></td></tr>
    <% foreach(var x in payments) {
		if (x.ReferenceNumber != null && x.ReferenceNumber == "[TB:R]")
			x.ReferenceNumber = "";

 %>
    <tr>
      <td style="text-align: right; font-weight: bold; font-size: 16px; padding-right: 10px" colspan="4"><% =x.PaymentType.ToString() %> payment <% if (!string.IsNullOrWhiteSpace(x.ReferenceNumber)) { %>
(Ref # <% =x.ReferenceNumber %>)
<% } %> of <% =x.Amount.ToString("C") %> applied

	</td>
    </tr>
    <% } %>

     <% }%>

    </table>  

</div>
        <% }  %>




<%
	var licenseValues = Extric.Towbook.Licenses.CompanyLicenseKeyValue.GetByCompanyId(company.Id);
        var keys = Extric.Towbook.Licenses.CompanyLicenseKey.GetAll();
	var printables = licenseValues.Where(y => keys.Where(o => o.Id == y.KeyId && o.ShowValueOnPrintables == true).Any());

	if (printables.Any()) {
%>
<div style="text-align: center; margin-top: 20px; font-size: 14px">
<%	foreach(var value in printables) {
		var key = keys.Where(o => o.Id == value.KeyId).FirstOrDefault();
		if (!String.IsNullOrWhiteSpace(value.Value))	{
			Response.Write(String.Format("<span style=\"margin-right: 10px\"><strong>{0}{2}</strong> {1}</span>", key.Name, value.Value, (key.Name.EndsWith("#") ? "" : ":")));
		}
	} %>
</div>
<% } %>


    </div>
    
    </div>

<div id="tabPhotos" style="display: none" class="TabContainer">

<%     if (!String.IsNullOrEmpty(_de.Company.InvoicingTagline))
       {
           Response.Write(_de.Company.InvoicingTagline);
       }
       else
       {
           Response.Write(String.Format("{0} appreciates your business; if you have any questions regarding this invoice, please contact us at {1}",
               _de.Company.Name,
               _de.Company.Phone));
       }
       %>
       <br /><br />

    <% 



        var callPhotoList = Extric.Towbook.Dispatch.Photo.GetByDispatchEntryId(_de.Id);
	var list = Extric.Towbook.Impounds.Photo.GetByImpoundId(_impound != null ? _impound.Id : 0);


            if (list.Count > 0 || callPhotoList.Count > 0 )
            {
                Response.Write("Click on the desired photo to view a larger copy of it.<br />");
                foreach (Extric.Towbook.Impounds.Photo p in list)
                {

		    string httpFilename2 = FileUtility.GetPresignedUrlForDownloadFromClient(p.Location.Replace("%1", _de.Company.Id.ToString()), p.ContentType, 15);
        %>
            <a href="<% = httpFilename2 %>" rel="towbook-dialog" data-dialog-height="480" ><img src="<% = httpFilename2 %>"     width="128" height="128" style="margin-right: 10px; margin-top: 10px; border: solid 1px #afafaf; padding: 1px; float: left"/></a>
        <%
                }
                
                foreach (var p in callPhotoList)
                {
                    string httpFilename = p.HttpLocation;
%>
            <a href="Photo.aspx?id=<% =p.Id.ToString() %>&callId=<%=p.DispatchEntryId %>" rel="towbook-dialog"><img src="<%=httpFilename%>"
                 width="128" height="128" style="margin-right: 10px; margin-top: 10px; border: solid 1px #afafaf; padding: 1px; float: left"/></a>
<%
                }                
            }
            else
            {
                Response.Write("There are no photos to display for this impound.");
            }


%>

    
</div>

</div>

</body>
</html>
