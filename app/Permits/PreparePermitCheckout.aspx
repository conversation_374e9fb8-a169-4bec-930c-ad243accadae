<%@ Page Language="C#" %>

<%@ Import Namespace="Extric.Towbook.Utility" %>
<html>
<head>
    <title>Generating Parking Permit Checkout</title>
    <style>
        * {
            font-family: verdana;
            font-size: 14px
        }
    </style>
    <script src="//ajax.aspnetcdn.com/ajax/jQuery/jquery-1.7.min.js" type="text/javascript"></script>  
</head>
<body>
    <%
        int accountId = 0;
        if (Request.QueryString["id"] != null)
        {
            accountId = Convert.ToInt32(Request.QueryString["id"]);
        }
        else
        {
            throw new ApplicationException("id is missing!");
        }
    %>

    <script type="text/javascript">
        $(function () {
            var objToSend = {
                fullName: "",
                email: "",
                cellPhone: ""
            };

            $('<p>Loading...</p>').appendTo($('body'));

            $.ajax({
                url: '/api/accounts/<%= accountId %>/parkingPermitRequests',
                type: 'POST',
                data: JSON.stringify(objToSend),
                contentType: "application/json; charset=utf-8"
            }).done(function (o) {
                if (o.url) {
                    window.open(o.url, "_self");
                }
            });

        });

    </script>
</body>
</html>