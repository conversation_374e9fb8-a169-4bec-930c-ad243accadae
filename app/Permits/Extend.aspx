<%@ Page Language="C#" AutoEventWireup="true" Inherits="Permit_Extend" CodeFile="Extend.aspx.cs" %>

<!DOCTYPE html>
<!--[if IE 8]>         <html class="ie8"> <![endif]-->
<!--[if gt IE 8]><!--> <html" >         <!--<![endif]-->
<head runat="server">
    <title>Extend Permit</title>
    <% if (Request.QueryString["_"] == null) { %>
    <script src="//ajax.googleapis.com/ajax/libs/jquery/1.7.2/jquery.min.js" type="text/javascript"></script>
	<script src="//ajax.googleapis.com/ajax/libs/jqueryui/1.8.14/jquery-ui.min.js" type="text/javascript"></script>
    <script src="/ui/js/jquery.tmpl.min.js"></script>
    <script src="/ui/js/towbook/towbook.js" type="text/javascript"></script>
    <script src="/ui/js/towbook.js"></script>
    <script src="https://cdn.jsdelivr.net/sweetalert2/4.2.7/sweetalert2.min.js" type="text/javascript"></script>
    <script type="text/javascript" src="/ui/js/jquery.timepicker.min.js"></script>
    <script src="/ui/js/timepicker/datepair.js"></script>

    <link rel="stylesheet" href="/UI/css/theme/jquery-ui-1.8.21.custom.css" />
    <link rel="stylesheet" href="/UI/css/application-forms.css" />
    <link rel="stylesheet" href="/UI/css/towbook.css" />
    <link rel="stylesheet" href="https://pro.fontawesome.com/releases/v5.1.0/css/all.css" integrity="sha384-87DrmpqHRiY8hPLIr7ByqhPIywuSsjuQAfMXAE0sMUpY3BM7nXjf+mLIUSvhDArs" crossorigin="anonymous">
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/sweetalert2/4.2.7/sweetalert2.min.css"/>
<% } %>

    <link rel="stylesheet" href="/Permits/editor.css" />

</head>
  <body>
    <style>
        #permit-extend-container {
            padding: 20px;
        }

        .ui-timepicker-wrapper {
            z-index: 1000;
        }
    </style>
    <div class="permitEditor">
        <div class="h3box">
            <table class="main">
                <tr class="header">
                    <td colspan="2"><div><span class="description-heading">Enter the new expiration date for permit #<%= HttpUtility.HtmlEncode(_permit.CustomPermitNumber ?? _permit.Id.ToString()) %></span></div></td>
                </tr>
                <tr>
                    <td class="property"></td>
                    <td class="value">
                        <ul class="sbs">
                            <li>
                                <label class="caption">Date</label><input type="text" class="date-picker" id="schedule-date" placeholder="Choose a date"/>
                            </li>
                        </ul>
                    </td>
                </tr>
            </table>
         </div>   

        <div class="navigation-row">
            <input type="button" class="standard-button" id="save" value="Extend" />
            <input type="button" class="cancel-button" id="discard" value="Cancel" />
        </div>

    </div>
<script type="text/javascript">
    $.extend(towbook.views, {
        permits: {
            closePopupWindow: function (data) {
                if (typeof PermitsView != "undefined") {
                    PermitsView.popup.options.onClose(data);
                    PermitsView.popup.close();
                }

                if (typeof window.popup != "undefined") {
                    window.popup.options.onClose(data);
                    window.popup.close();
                }
            },
            handleError: function (xhr, status, error) {
                console.log(xhr, status, error);
                var msg = xhr != null && xhr.responseText.length > 0 ?
                    xhr.responseText : "Server returned status of " + xhr.status;

                swal({ title: "Error", text: msg, type: "error" });
            },
        }
    });


    $(function () {
        var expirationDate = "<%= Extric.Towbook.WebShared.WebGlobal.OffsetDateTime(_permit.ExpirationDate.Value, true).ToString("o") %>";

        $('#schedule-date').datetimePicker();



        $('#schedule-date-date').val(towbook.formatDate(expirationDate));
        $('#schedule-date-time').val(towbook.formatAMPM(expirationDate));


        $('#save').on('click', function () {
            var extentionDate = $('#schedule-date-date').val() + " " + $('#schedule-date-time').val();

            var self = this;
            $(this).val('Saving...');
            $(this).prop('disabled', 'disabled');

            $.ajax({
                url: '/api/accounts/<%= _permit.AccountId %>/parkingpermits/<%= _permit.Id %>/extend',
                data: JSON.stringify({ extentionDate: extentionDate }),
                type: 'POST',
                contentType: "application/json; charset=utf-8"
            }).done(function (response) {
                console.log("Successfully extended permit");
                towbook.views.permits.closePopupWindow();
                
            }).always(function () {
                $(self).val('Extend');
                $(self).prop('disabled', '');
            }).fail(function (xhr, status, error) {
                towbook.views.permits.handleError(xhr, status, error);
            });
        });

    });


</script>    
  </body>
</html>
