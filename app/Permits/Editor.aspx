<%@ Page Language="C#" AutoEventWireup="true" Inherits="Permit_Editor" CodeFile="Editor.aspx.cs" %>
<%@ Import Namespace="Extric.Towbook.Utility" %>

<!DOCTYPE html>
<!--[if IE 8]>         <html class="ie8"> <![endif]-->
<!--[if gt IE 8]><!--> <html" >         <!--<![endif]-->
<head runat="server">
    <title>Parking Permit Editor</title>
<% if (Request.QueryString["_"] == null) { %>
    <script src="//ajax.googleapis.com/ajax/libs/jquery/1.7.2/jquery.min.js" type="text/javascript"></script>
	<script src="//ajax.googleapis.com/ajax/libs/jqueryui/1.8.14/jquery-ui.min.js" type="text/javascript"></script>
    <script src="/ui/js/jquery.tmpl.min.js"></script>
    <script src="/ui/js/towbook/towbook.js" type="text/javascript"></script>
    <script src="https://cdn.jsdelivr.net/sweetalert2/4.2.7/sweetalert2.min.js" type="text/javascript"></script>
    <script src="/ui/js/jquery.timepicker.min.js"></script>
    <script src="/ui/js/timepicker/datepair.js"></script>

    <link rel="stylesheet" href="/UI/css/theme/jquery-ui-1.8.21.custom.css" />
    <link rel="stylesheet" href="/UI/css/application-forms.css" />
    <link rel="stylesheet" href="/UI/css/towbook.css" />
    <link rel="stylesheet" href="https://pro.fontawesome.com/releases/v5.1.0/css/all.css" integrity="sha384-87DrmpqHRiY8hPLIr7ByqhPIywuSsjuQAfMXAE0sMUpY3BM7nXjf+mLIUSvhDArs" crossorigin="anonymous">
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/sweetalert2/4.2.7/sweetalert2.min.css"/>
<% } %>
    <link rel="stylesheet" href="/Permits/editor.css" />

    <script type="text/x-jQuery-tmpl" id="t-address-row">
      <tr class="permit-address" data-id="${id}">
        <td class="property"></td>
        <td class ="value ignore-preset-combo-width">
            <table>
                <tr>
                    <td>
                        <ul class="sbs">
                            <li><label>Address Type</label>
                                <select class="permit-addr-type" data-width="153px" name="listId">
                                    <option value="0">Primary</option>
                                    <option value="1">Secondary</option>
                                    <option value="2">Mailing</option>
                                    <option value="3">Other</option>
                                </select></li>
                            <li><label>Street Address</label><input type="text" class="permit-addr-street" /></li>
                        </ul>
                        <span class="add-address"><i class="fa fa-plus"></i>Add Address</span>
                        <span class="remove-address"><i class="fa fa-minus"></i></span>
                    </td>
                </tr>
                <tr>
                    <td>
                        <ul class="sbs">
                            <li><label>City</label><input type="text" class="permit-addr-city" /></li>
                            <li><label>State/Prov</label><input type="text" class="permit-addr-state" /></li>
                            <li><label>Zip/Postal Code</label><input type="text" class="permit-addr-zip" /></li>
                        </ul>
                    </td>
                </tr>
            </table>
        </td>
      </tr>
    </script>

</head>
  <body style="background-image: none;">
    <div id="permitEditor" class="permitEditor">
        
        <input type="hidden" id="permit-id" value="<%=_permit != null ? _permit.Id : -1%>"/>
        <input type="hidden" id="permit-account" value="<%=_account.Id%>"/>
        
        <table>
            <tbody id="main">
                <tr class="header">
                    <td colspan="2">
                        <div>
                            <span class="description-heading">Permit Information</span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td class="property"></td>
                    <td class ="value ignore-preset-combo-width">
                        <ul class="sbs">
                            <li><label>Permit #</label><input type="text" id="permit-permitNumber" /></li>
                            <li><label>Permit Type</label><select id="permit-listId" data-width="153px"></select></li>
                            <li><label>Expiration Date</label><input type="text" class="towbook-dt-date" id="permit-expiration" /></li>
                        </ul>
                    </td>
                </tr>
                <tr>
                    <td class="property"></td>
                    <td class ="value ignore-preset-combo-width">
                        <ul class="sbs">
                            <li><input type="checkbox" id="permit-handicap" name="permit-handicap" /><label for="permit-handicap" style="width: 153px;">Handicap <i class="fa fa-wheelchair" style="padding-left: 6px;"></i></label></li>
                            <li><label>Space Number</label><input type="text" id="permit-spaceNumber" placeholder="parking space"/></li>
                            <li><label>Decal Color</label><select id="permit-decalColor" title="none" data-width="153px"></select></li>
                        </ul>
                    </td>
                </tr>
                <tr class="header">
                    <td colspan="2">
                        <div>
                            <span class="description-heading">Contact</span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td class="property"></td>
                    <td class ="value ignore-preset-combo-width">
                        <ul class="sbs">
                            <li><label>Contact Name</label><input type="text" id="permit-fullName" /></li>
                            <li><label>Phone</label><input type="text" id="permit-phone" /></li>
                            <li><label>Email</label><input type="text" id="permit-email" /></li>
                        </ul>
                    </td>
                </tr>

                 <tr>
                    <td class="property"></td>
                    <td class ="value ignore-preset-combo-width">
                        <ul class="sbs">
                            <li><label>Unit/Apartment #</label><input type="text" id="permit-unitNumber" /></li>
                            <li><label>Lease End Date</label><input type="text" id="permit-leaseEndDate" class="date-picker" /></li>
                        </ul>
                    </td>
                </tr>
                
                <tr>
                    <td class="property"></td>
                    <td class ="value ignore-preset-combo-width">
                        <ul class="sbs">
                            
                        </ul>
                    </td>
                </tr>
                <tr class="header">
                    <td colspan="2">
                        <div>
                            <span class="description-heading">Vehicle</span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td class="property"></td>
                    <td class ="value ignore-preset-combo-width">
                        <ul class="sbs">
                            <li><label>Plate</label><input type="text" id="permit-plate" /></li>
                            <li><label>Plate State</label><select id="permit-state" data-width="153px"></select></li>
                            <li><label>VIN</label><input type="text" id="permit-vin" maxlength="17" /></li>
                        </ul>
                    </td>
                </tr>
                <tr>
                    <td class="property"></td>
                    <td class ="value ignore-preset-combo-width">
                        <ul class="sbs top-10">
                            <li><label>Year</label><select id="permit-year" class="standard validate-number" data-width="153px"></select></li>
                            <li><label>Make</label><select id="permit-make" data-width="153px"></select></li>
                            <li><label>Model</label><select id="permit-model" data-width="153px"></select></li>
                            <li><label>Color</label><select id="permit-color" data-width="153px"></select></li>
                        </ul>
                    </td>
                </tr>
                <tr class="header">
                    <td colspan="2">
                        <div>
                            <span class="description-heading">Notes</span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td class="property"></td>
                    <td class ="value ignore-preset-combo-width">
                        <textarea id="permit-notes" rows="3"></textarea>
                    </td>
                </tr>
            </tbody>
        </table>

        <div id="outputMessage"><i class="fa fa-warning"></i><span class="message"></span></div>

        <div class="navigation-row">
            <input type="button" class="standard-button" id="save" value="Create Permit" title="Save your changes for this permit" />
            <input type="button" class="cancel-button" id="discard" value="Discard Changes" />
        </div>
    </div>

<script type="text/javascript">
          
    $.extend(towbook.vehicle, {
        makes: <%=_vehicleMakesJson%>, 
        colors: <%=_vehicleColorsJson%>,
    });
    $.extend(towbook.geo, { states: <%=_statesJson%>});
    $.extend(towbook.views, {
        permits: {
            id: <%=_permit != null ? _permit.Id : -1%>,
            accountId: <%=_account.Id%>,
            accountName: "<%=HttpUtility.HtmlEncode(_account.Company) %>",
            sourcePermit: <% =_permitModel %>,
            types: <%= _parkingPermitListTypesJson ?? "[]" %>,
            decalColors: <%= _permitDecalColorsJson ?? "[]" %>,
            settings: <%= _permitSettingsJson %>,
            <% if (Request.QueryString["_"] == null) { %>
            closePopupWindow: function () {
                //swal("Saved").then(function () { window.location.reload(false); });
            },
            <% } else { %>
            closePopupWindow: function (data) {
                if (typeof PermitsView != "undefined") {
                    PermitsView.popup.options.onClose(data);
                    PermitsView.popup.close();
                }

                if (typeof window.popup != "undefined") {
                    window.popup.options.onClose(data);
                    window.popup.close();
                }
            },
            <% } %>
            handleError: function (xhr, status, error) {
                console.log(xhr, status, error);
                var msg = xhr != null && xhr.responseText.length > 0 ?
                    xhr.responseText : "Server returned status of " + xhr.status;

                swal({ title: "Error", text: msg, type: "error" });
            },
        }
    });

    $.extend(towbook.views.permits, {
        permitEditor:
        {
            _permit: null,
            get: function () {
                var state = $('#permit-state').combobox('get').customText;
                var year = parseInt($('#permit-year').val());
                var decalColorId = parseInt($('#permit-decalColor').val());

                return {
                    id: parseInt($('#permit-id').val()),
                    accountId: parseInt($('#permit-account').val()),
                    vehicleYear: isNaN(year) ? null : year,
                    vehicleMake: $('#permit-make').combobox('get').customText,
                    vehicleModel: $('#permit-model').combobox('get').customText,
                    vehicleColor: $('#permit-color').combobox('get').customText,
                    plate: $('#permit-plate').val(),
                    plateState: state == 'state' ? '' : state,
                    vin: $('#permit-vin').val(),
                    fullName: $('#permit-fullName').val(),
                    cellPhone: $('#permit-phone').val(),
                    email: $('#permit-email').val(),
                    unitNumber: $('#permit-unitNumber').val(),
                    customPermitNumber: $('#permit-permitNumber').val(),
                    leaseEndDate: $('#permit-leaseEndDate').getVal(),
                    listId: $('#permit-listId').val(),
                    notes: $('#permit-notes').val(),
                    expirationDate: $('#permit-expiration').getVal(),
                    addresses: this.addresses.get(),
                    isHandicap: $('#permit-handicap').is(':checked'),
                    spaceNumber: $('#permit-spaceNumber').val(),
                    decalColorId: isNaN(decalColorId) && decalColorId > 0 ? null : decalColorId
                };
            },
            save: function() {
                var deferred = new $.Deferred();

                var o = this.get();

                var type = 'POST'
                var url = '/api/accounts/' + o.accountId + '/parkingPermits'

                if (o.id > 0) {
                    type = 'PUT';
                    url = '/api/accounts/' + o.accountId + '/parkingPermits/' + o.id
                }

                $.ajax({
                    url: url,
                    data: JSON.stringify(o),
                    type: type,
                    contentType: "application/json; charset=utf-8"
                }).done(function (data) {
                    deferred.resolve(data);
                }).error(function (xhr, status, error) {
                    towbook.views.permits.handleError(xhr, status, error);
                    deferred.reject()
                });

                return deferred.promise();
            },
            addresses: {
                id: -1000,
                get: function () {
                    var ret = [];
                    $('.permit-address').each(function (i, address) {
                        var type = parseInt($(address).find('.permit-addr-type').getVal());
                        var street = $(address).find('.permit-addr-street').getVal();
                        var city = $(address).find('.permit-addr-city').getVal();
                        var state = $(address).find('.permit-addr-state').getVal();
                        var zip = $(address).find('.permit-addr-zip').getVal();

                        if (street || (city && state)) {
                            ret.push({
                                id: $(this).data('id'),
                                type: type,
                                address: street,
                                city: city,
                                state: state,
                                zip: zip
                            });
                        }
                    });

                    return ret;
                },
                addAddress: function (data) {
                    if (!data) {
                        data = {
                            id: this.id++,
                            type: 0,
                            street: "",
                            city: "",
                            state: "",
                            zip: ""
                        };
                    }

                    var row = towbook.applyTemplate('t-address-row', data);
                    var after = $('.permit-address').last();
                    if (after.length == 0)
                        after = $('#permit-unitNumber').closest('tr');

                    $(row).insertAfter(after);

                    $(row).find('.permit-addr-type').setVal(data.type);
                    $(row).find('.permit-addr-street').setVal(data.address);
                    $(row).find('.permit-addr-city').setVal(data.city);
                    $(row).find('.permit-addr-state').setVal(data.state);
                    $(row).find('.permit-addr-zip').setVal(data.zip);

                    $('.add-address').off('click').on('click', function () {
                        towbook.views.permits.permitEditor.addresses.addAddress();
                    });

                    $('.remove-address').off('click').on('click', function () {
                        towbook.views.permits.permitEditor.addresses.removeAddress($(this).closest('tr.permit-address').data('id'));
                    });

                    $('.add-address').hide();
                    $('.add-address:eq(0)').show();

                    $('.remove-address').show();
                    $('.remove-address:eq(0)').hide();

                    $('.permit-addr-type').combobox();


                },
                removeAddress: function (id) {
                    $('.permit-address[data-id="' + id + '"]').remove();
                },
            },
            
        }
    });



    // preset the expiration date based off of settings
    function calculateExpiration(type) {

        var setting = towbook.views.permits.settings;

        if ($('#permit-expiration').getVal() != "" || towbook.isEmpty(setting))
            return;

        if (type == 1) // resident
        {
            switch (setting.expirationType) {
                case 1:
                    $('#permit-expiration-date').val(towbook.formatDate(new Date(new Date().getFullYear() + 1, 0, 1)));
                    $('#permit-expiration-time').val("12:00 AM");
                    break;

                case 2:
                    $('#permit-expiration-date').val(towbook.formatDate(new Date(new Date().getFullYear() + 1, new Date().getMonth(), new Date().getDate())));
                    $('#permit-expiration-time').val(towbook.formatAMPM(new Date()));
                    break;

                case 3:
                    if ($('#permit-leaseEndDate').val() != "") {
                        $('#permit-expiration-date').val($('#permit-leaseEndDate').val());
                        $('#permit-expiration-time').val("11:59 PM");
                    }

                    $('#permit-leaseEndDate').off('change').on('change', function () {
                        if ($('#permit-expiration').val() != "")
                            return true;

                        $('#permit-expiration-date').val($('#permit-leaseEndDate').val());
                        $('#permit-expiration-time').val(new Date());
                    });
                    break;
            }
        }
        else if (type == 5) // Visitor
        {
            if (setting.guestExpirationDays > 0) {
                $('#permit-expiration').setVal(towbook.formatDate(
                    new Date(
                        new Date().getFullYear(),
                        new Date().getMonth(),
                        new Date().getDate() + setting.guestExpirationDays)
                ));

                $('#permit-expiration-time').val("11:59 PM");
            }
        }
    }

    this.initPermitDefaults = function () {
        var self = this;

        $('#permit-listId')
            .combobox()
            .appendOptions(towbook.views.permits.types, false, true)
            .combobox('quiet', 1);

        $('#permit-handicap').checkbox();
        $('#permit-decalColor').appendOptions(towbook.views.permits.decalColors, null, null).combobox();
        $('#permit-expiration').datetimePicker({ 'minTime': '9:00am', 'responsiveWidth': 'true' });

        var o = towbook.views.permits.sourcePermit;
        if (towbook.views.permits.id > 0) {
            $('#permit-year').setVal(o.vehicleYear);
            $('#permit-make').setVal(o.vehicleMake);
            $('#permit-model').setVal(o.vehicleModel);
            $('#permit-color').setVal(o.vehicleColor);
            $('#permit-plate').setVal(o.plate);
            $('#permit-state').combobox('quiet', o.plateState || '');
            $('#permit-vin').setVal(o.vin);
            $('#permit-fullName').setVal(o.fullName);
            $('#permit-permitNumber').setVal(o.customPermitNumber);
            $('#permit-unitNumber').setVal(o.unitNumber);
            $('#permit-leaseEndDate').setVal(towbook.formatDate(o.leaseEndDate));
            $('#permit-phone').setVal(o.cellPhone);
            $('#permit-email').setVal(o.email);
            $('#permit-listId').setVal(o.listId);
            $('#permit-notes').setVal(o.notes);
            if (o.expirationDate) {
                $('#permit-expiration').setVal(o.expirationDate);
            }
            $('#permit-handicap').prop('checked', o.isHandicap).checkbox('refresh');
            $('#permit-spaceNumber').setVal(o.spaceNumber);
            $('#permit-decalColor').setVal(o.decalColorId);

            // add addresses
            if (o.addresses && o.addresses.length > 0) {
                o.addresses.map(function (m) {
                    towbook.views.permits.permitEditor.addresses.addAddress(m);
                });
            }
            else
            {
                towbook.views.permits.permitEditor.addresses.addAddress();
            }
        }
        else
            towbook.views.permits.permitEditor.addresses.addAddress();

        $('#save').val(towbook.views.permits.id > 0 ? 'Update Permit' : 'Create Permit');

        $('#permit-permitNumber').on('blur', function () {
            if ($(this).val() == "")
                $('#outputMessage').css('visibility', 'hidden');
            else
                customNumberSearch();
        });

        $('#permit-unitNumber').on('blur', function () {
            unitNumberSearch();
        });


        $('#save').on('click', function () {
            var self = this;
            $(this).val('Saving...');
            $(this).prop('disabled', 'disabled');

            $.when(towbook.views.permits.permitEditor.save())
                .done(function (data) {
                    towbook.views.permits.closePopupWindow(data);
                })
                .always(function () {
                    $(self).val('Update Permit');
                    $(self).prop('disabled', '');
                });
        });

        calculateExpiration($('#permit-listId').val());

        // save a copy to compare for changes
        towbook.views.permits.permitEditor._permit = JSON.stringify(towbook.views.permits.permitEditor.get());
    }

    this.initVehicleForm = function () {

        $("#permit-make").combobox({
            allowMissing: true,
            title: 'name',
            selected: populateModel
        }).appendOptions(towbook.vehicle.makes, true, true, "id", "name", "(none)");

        $("#permit-model").combobox({ allowMissing: true });

        var year = (new Date).getFullYear() + 1;
        $('#permit-year').combobox({
            allowMissing: true,
            numbersOnly: true,
        }).on("keydown", function () {
            CheckKeyCode(this, event, false, false);
        }).append('<option value=0>none</option>\r\n');
        for (var i = year; i > year - 100; i--) {
            $('#permit-year').append('<option value="' + i + '">' + i + '</option>\r\n');
        }

        $('#permit-color').combobox({ allowMissing: true }).appendOptions(towbook.vehicle.colors, true, true, null, null, "(none)");
        $('#permit-state').appendOptions(towbook.geo.states, true, true, 'id', 'short', 'state');

        $('#permit-plate').on('blur', function () {
            var previous = $(this).data('previousValue');

            if ($(this).val() == "") {
                $(this).css('text-transform', 'lowercase');
                $(this).removeData('previousValue');
            } else {
                $(this).val($(this).val().toUpperCase());

                if (!previous || (previous != -1 && previous != $(this).val())) {
                    plateToVin(0);
                    permitPlateSearch();
                }

                $(this).data('previousValue', $(this).val());
            }
        }).on('focus', function () {
            $(this).css('text-transform', 'uppercase');
        });

        $('#permit-state').on('blur', function () {
            if ($(this).val() == "")
                $(this).css('text-transform', 'lowercase');
            else
            {
                $(this).val($(this).val().toUpperCase());
            }
        }).on('focus', function () {
            $(this).css('text-transform', 'uppercase');
        });


        $('#permit-state').combobox({
            selected: function () {
                if ($('body').hasClass('loading-asset'))
                    return;

                var previous = $(this).data('previousValue') || $(this).data('previousId');
                if (previous == "-1" || previous != $(this).combobox('get').customText) {
                    plateToVin(0);
                    permitPlateSearch();
                }

                $(this).data('previousValue', $(this).combobox('get').customText);
            }
        });
    }

    function plateToVin(assetId) {
        if (assetId == null || assetId.length == 0)
            return;

        var plate = $('#permit-plate').getVal();
        var state = $('#permit-state').combobox('get').customText;
        var vin = $('#permit-vin').getVal();

        if (plate.length > 1 && (state != "" && state != "state") && vin == "") {
            console.log("Plate to vin fired: " + plate + ", " + state);
            $('#permit-vin').addClass('loading-watermark');

            $.ajax({
                url: ("/api/vin?plate=" + plate + "&state=" + state),
                success: function (result) {
                    if (result.year >= 1900) {
                        $('#permit-year').setVal(result.year)
                        $('#permit-make').setVal(result.make)
                        $('#permit-model').setVal(result.model)
                        $('#permit-vin').setVal(result.vin)
                    }
                },
                complete: function () {
                    $('#permit-vin').removeClass('loading-watermark');
                }
            });
        }
    }

    var plateTimerId = null;
    function permitPlateSearch() {
        var plate = $('#permit-plate').val();
        var state = $('#permit-state').combobox('get').customText;

        if (state == 'state')
            state = '';

        if (plate && state) {
            if (plateTimerId != null) {
                clearTimeout(plateTimerId);
            }

            plateTimerId = setTimeout(function () {

                console.log("parking permit search fired: ", plate, state);

                $.ajax({
                    url: ("/api/accounts/" + towbook.views.permits.accountId + "/parkingpermits/find?licensePlate=" + plate + "&licenseState=" + state + "&pageNumber=1"),
                    success: function (result) {
                        if(result && result.length)
                            outputMessage(result[0], "plate");
                    }
                });
            }, 500);
        }
    }

    var numberTimerId = null;
    function customNumberSearch() {
        var customNumber = $('#permit-permitNumber').val();
        if (customNumber) {
            if (numberTimerId != null) {
                clearTimeout(numberTimerId);
            }

            numberTimerId = setTimeout(function () {
                console.log("parking permit search fired: ", customNumber);

                $.ajax({
                    url: ("/api/accounts/" + towbook.views.permits.accountId + "/parkingpermits/find?customPermitNumber=" + customNumber + "&pageNumber=1"),
                    success: function (result) {
                        outputMessage(result[0], "customNumber");
                    }
                });
            }, 500);
        }
    }

    var unitTimerId = null;
    function unitNumberSearch() {
        var unitNumber = $('#permit-unitNumber').val();
        if (unitNumber) {

            if (unitTimerId != null) {
                clearTimeout(unitTimerId);
            }

            unitTimerId = setTimeout(function () {
                console.log("parking permit search fired: ", unitNumber);

                $.ajax({
                    url: ("/api/accounts/" + towbook.views.permits.accountId + "/parkingpermits/find?unitNumber=" + unitNumber + "&pageNumber=1"),
                    success: function (result) {
                        outputMessage(result[0], "unitNumber");
                    }
                });
            }, 500);
        }
    }

    function outputMessage(result, inputType) {
        if (result) {
            var accName = towbook.views.permits.accountName;

            $('#outputMessage').css('visibility', 'visible');
            var type = towbook.get(towbook.views.permits.types, result.listId, "id");
            var msg = '';
            switch (inputType) {
                case "customNumber":
                    msg = "WARNING: Permit #" + result.customPermitNumber + " already exists for a " +
                        (type != null ? type.name.toUpperCase() +  ' ' : '') + " permit";
                    break;

                case "unitNumber":
                    msg = "WARNING: Unit/Apartment #" + result.unitNumber + " already exists for a " +
                        (type != null ? type.name.toUpperCase() + ' ' : '') + " permit";
                    break;

                case 'plate':
                    msg = "WARNING: A " + (type != null ? type.name.toUpperCase() + ' ' : '') +
                        "permit (#" + (result.customPermitNumber || result.id) + ") exists for a " +
                        result.vehicleYear + " " + result.vehicleMake + " " + result.vehicleModel + " " +
                        "with license plate " + result.plate;
                    break;
            }

            if (msg != '')
                $('#outputMessage').find('.message').html(msg);
        }
        else {
            $('#outputMessage').css('visibility', 'hidden');
            $('#outputMessage').find('.message').html('');
        }
    }


    this.populateModel = function (obj) {
        if (typeof obj.targetDomEl == 'undefined') {
            var target = this;

        } else {
            var target = obj.targetDomEl;
        }

        var thisRow = $(target).closest('tr');

        thisRow.find('#permit-model').empty();
        thisRow.find('#permit-model').combobox('autocomplete', 0);

        var makeId = thisRow.find('#permit-make').val();

        if (makeId == 0) {
            return;
        }

        var processModels = function (data) {
            if (data != null) {
                thisRow.find('#permit-model').appendOptions(data, null, null, "id", "model", "(none)");
            }
        };

        if (towbook._cache.vehicle.models[makeId] == null) {
            $.ajax({
                url: "/api/vehicleModels",
                data: { makeId: thisRow.find('#permit-make').data('id') }
            }).done(function (data) {
                if (typeof xdata != 'undefined') {
                    thisRow.find('#permit-make').setVal(xdata.model);
                }
                towbook._cache.vehicle.models[makeId] = data;
                processModels(data);
            });
        }
        else {
            processModels(towbook._cache.vehicle.models[makeId]);
        }
    }

    $(function () {
        towbook.compileTemplate('t-address-row', $("#t-address-row"));

        initVehicleForm();
        initPermitDefaults();
    });
    

</script>


  </body>
</html>
