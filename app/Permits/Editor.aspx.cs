using Extric.Towbook;
using Extric.Towbook.Accounts;
using Extric.Towbook.API.Models;
using Extric.Towbook.Company;
using Extric.Towbook.Utility;
using Extric.Towbook.WebShared;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class Permit_Editor : System.Web.UI.Page
{
    protected ParkingPermit _permit;
    protected Account _account;
    public string _vehicleMakesJson;
    public string _vehicleColorsJson;
    public string _statesJson;
    public string _parkingPermitListTypesJson;
    public string _permitModel;
    public string _permitSettingsJson = "";
    public string _permitDecalColorsJson = "";

    protected void Page_Load(object sender, EventArgs e)
    {

        int permitId = Convert.ToInt32(Request.QueryString["id"]);

        if (permitId > 0)
        {
            _permit = Extric.Towbook.Accounts.ParkingPermit.GetById(permitId);

            if (_permit == null)
            {
                throw new TowbookException("Permit " + permitId + " is invalid or doesn't exist.");
            }

            _account = Extric.Towbook.Accounts.Account.GetById(_permit.AccountId);
        }
        else
        {
            if(Request.QueryString["accountId"] == null)
                throw new TowbookException("An accountId was not specified in the query parameters. Please provide an account.");

            int accountId = Convert.ToInt32(Request.QueryString["accountId"]);
            _account = Extric.Towbook.Accounts.Account.GetById(accountId);

            _permit = new ParkingPermit();
        }

        if (_account == null || !WebGlobal.CurrentUser.HasAccessToCompany(_account.CompanyId))
            throw new TowbookException("You do not have permission to edit this permit.");


        Collection<StateConfig> states = new Collection<StateConfig>();

        if (WebGlobal.CurrentUser.Company.Country == Company.CompanyCountry.USA)
            states = states.Union(States.StatesUSA).ToCollection();
        else if (WebGlobal.CurrentUser.Company.Country == Company.CompanyCountry.Canada)
            states = states.Union(States.StatesCanada).ToCollection();
        else if (WebGlobal.CurrentUser.Company.Country == Company.CompanyCountry.Australia)
            states = states.Union(States.StatesAustralia).ToCollection();

        if (WebGlobal.CurrentUser.Company.HasFeature(Extric.Towbook.Generated.Features.Options_ForceIncludeUsaStates))
            states = states.Union(States.StatesUSA).Distinct().ToCollection();

        if (WebGlobal.CurrentUser.Company.HasFeature(Extric.Towbook.Generated.Features.Options_ForceIncludeCanadaProvinces))
            states = states.Union(States.StatesCanada).Distinct().ToCollection();

        _statesJson = states.ToJson();

        _vehicleMakesJson = WebGlobal.GetResponseFromUrl("/api/vehiclemakes");
        _vehicleColorsJson = WebGlobal.GetResponseFromUrl("/api/colors");
        _parkingPermitListTypesJson = WebGlobal.GetResponseFromUrl("/api/parkingPermitLists");
        _permitDecalColorsJson = WebGlobal.GetResponseFromUrl("/api/parkingPermitDecalColors");

        if (_permit.Id > 0)
            _permitModel = WebGlobal.GetResponseFromUrl("/api/accounts/" + _account.Id + "/parkingPermits/" + _permit.Id);
        
        if (string.IsNullOrWhiteSpace(_permitModel))
            _permitModel = "null";

        _permitSettingsJson = WebGlobal.GetResponseFromUrl(
                    string.Format("/api/parkingPermits/settings?accountId={0}", _account.Id));

    }
}