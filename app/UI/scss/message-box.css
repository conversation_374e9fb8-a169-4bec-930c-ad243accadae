#message-box {
  position: fixed;
  display: none;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10000;
  cursor: default;
  background: #eee;
  border: solid 1px #fff;
  -webkit-box-shadow: 1px 2px 14px 0 #777;
  -moz-box-shadow: 1px 2px 14px 0 #777;
  box-shadow: 1px 2px 14px 0 #777;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none; }
  #message-box .top-bar {
    height: 37px;
    background: #2B75BE;
    color: #fff;
    font-size: 17px;
    padding: 8px 10px; }
  #message-box .close-btn {
    position: absolute;
    top: 0;
    right: 0;
    height: 35px;
    width: 35px;
    text-align: center;
    padding-top: 8px;
    cursor: pointer; }
    #message-box .close-btn:hover {
      background: #24609C; }
    #message-box .close-btn:active {
      background: #2B75BE; }
  #message-box .content {
    padding: 10px; }
  #message-box .content-msg {
    padding-bottom: 10px; }
  #message-box th {
    padding: 2px 20px 2px 5px;
    font-weight: bold !important;
    text-align: left; }
    #message-box th:last-child {
      padding-right: 5px; }
  #message-box td {
    padding: 2px 20px 2px 5px;
    text-align: left; }
    #message-box td:last-child {
      padding-right: 5px; }
  #message-box .bottom-btns {
    margin-top: 10px;
    text-align: right; }
  #message-box .file-link {
    cursor: pointer;
    color: #0063C5; }
    #message-box .file-link:hover {
      background: #fff;
      color: #f83; }
    #message-box .file-link:active {
      background: #d8d8d8;
      color: #07417B; }

