<script type="text/x-jquery-tmpl" id="t-notificationItem">
    <li data-id="${id}" data-type="${type}" data-time-began="${timeBegan}" class="unread">
        {{if type != 1}}
        <div class="x-notification-close tip" title="Delete Notification">
            <svg fill="lightgray" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16">
                <path d="M6.663 8.01l-3.362 3.362a.955.955 0 0 0-.005 1.352.953.953 0 0 0 1.352-.005L8.01 9.357l3.362 3.362a.955.955 0 0 0 1.352.005.953.953 0 0 0-.005-1.352L9.357 8.01l3.362-3.362a.955.955 0 0 0 .005-1.352.953.953 0 0 0-1.352.005L8.01 6.663 4.648 3.301a.955.955 0 0 0-1.352-.005.953.953 0 0 0 .005 1.352L6.663 8.01z" fill-rule="evenodd"></path>
            </svg>
        </div>
        {{/if}}
        <div class="x-notification-title">${_enc(title)}</div>
        {{if type != 3 }}
        <div class="x-notification-message">${_enc(message)}</div>
        {{/if}}
        {{if type == 1 }}
        <div class="x-request-accept" style="height: 23px">
            <input type="button" class="flat-blue" value="Accept" name="request-accept" style="width: 70px" />
            <input type="button" class="flat-blue" value="Reject" name="request-reject" style="width: 70px" />
        </div>
        {{/if}}
        <div class="x-notification-actions">
            <div class="x-notification-moment">${duration} ago</div>
        </div>
        <div class="x-notification-audio" style="display: none; height: 0px; width: 2px;"></div>
    </li>
</script>