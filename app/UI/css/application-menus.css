ul#appMenu {
  list-style: none;
  margin: 0;
  width: 230px;
  top: 0;
  margin-top: 0px
}

  ul#appMenu li {
    margin-top: 10px;
    padding-bottom: 10px;
    margin-right: 0px
  }

    ul#appMenu li a {
      border: solid 1px white;
      background-repeat: no-repeat;
      background-position: 10px 14px;
      padding-left: 70px;
      padding-top: 7px;
      padding-bottom: 4px;
      padding-right: 3px;
      overflow: visible;
      text-align: justify;
      display: list-item;
      font-size: 14px;
      color: #555;
      text-decoration: none;
      margin-bottom: 1px;
    }

      ul#appMenu li a strong {
        display: block
      }

      ul#appMenu li a:hover {
        background-color: #ebebeb;
      }


ul#appMenu {
  padding-top: 0
}

  ul#appMenu li {
    margin-top: 0;
    padding-bottom: 0;
    border-bottom: none
  }

  ul#appMenu a span {
    display: none
  }

  ul#appMenu li {
    background-color: #fefefe;
    text-align: left;
    border-right: solid 1px #efefef;
  }

    ul#appMenu li a {
      padding: 14px 9px 10px 33px;
      border: none;
      text-align: left;
      border-bottom: none;
      font-size: 16px;
      font-family: "Open Sans", arial;
      color: #777;
      position: relative;
    }

      ul#appMenu li a strong {
        font-weight: normal
      }

#contents-v2 {
  padding-top: 0;
  padding-left: 0
}

ul#appMenu li.selected a, ul#appMenu li.selected a:hover {
  color: white;
  background-color: #2B75BE;
  padding-left: 31px;
}

ul#appMenu li div {
  border-left: solid 1px #efefef;
  border: solid 1px #efefef;
  border-top: none;
  display: none;
  margin-left: 0px;
  background-color: #afafaf;
  margin-top: 0px;
  padding-top: 5px;
  border-bottom-left-radius: 3px;
  border-bottom-right-radius: 3px;
}

ul#appMenu .has-sub-items a {
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}

ul#appMenu li.selected a {
  font-weight: bold
}
/**/
ul#appMenu li.selected div {
  display: block;
  background-color: #fefede;
  padding-left: 0px
}

ul#appMenu li a .new-feature-tag,
ul#appMenu li.selected a .new-feature-tag {
  position: absolute;
  display: inline-block;
  padding: 1px 4px;
  margin: 0px 5px;
  color: #777;
  background-color: #ffff00;
  border: solid 1px white;
  border-radius: 5px;
  font-size: 11px;
  font-family: Calibri;
}

/* Sub menu */
ul#appMenu li.selected div a {
  background-color: #fefede;
  color: #333;
  padding-right: 5px;
  padding-top: 3px;
  padding-bottom: 3px;
  padding-left: 15px;
  height: auto;
  margin-left: 11px
}

ul#appMenu li.selected div a {
  border: none;
  padding: 7px 28px 7px 14px;
  font-weight: normal;
  display: list-item;
  color: #555555;
  font-size: 1.3em;
  border-radius: 0;
  text-align: right
}

  ul#appMenu li.selected div a:hover {
    background-color: #efefef;
    border: solid 1px #dfdfdf;
    padding: 6px 27px 6px 13px;
    margin-left: 15px;
    color: #555555;
    margin-left: 11px
  }

  ul#appMenu li.selected div a .new-feature-tag,
  ul#appMenu li.selected div a.selected .new-feature-tag {
    right: -5px;
    font-size: 9px;
    top: 3px;
  }

  ul#appMenu li.selected div a:hover .new-feature-tag {
    top: 2px;
  }

  ul#appMenu li.selected div a:hover .new-feature-tag {
    right: -6px;
  }

  ul#appMenu li.selected div a.selected:hover .new-feature-tag {
    top: 3px;
    right: -5px;
  }

  ul#appMenu li.selected div a.last {
    border-bottom: none
  }

  ul#appMenu li.selected div a.selected {
    font-weight: bold;
    margin-left: 11px;
    padding: 6px 26px 6px 0px;
    border: solid 2px transparent; /*#c4c4c4;*/
    background-color: #ffffff;
    color: #5200FF;
    margin-bottom: 1px;
    margin-right: 3px;
  }

    ul#appMenu li.selected div a.selected:hover {
      text-decoration: none;
      background-color: white;
    }

.data {
  width: 700px
}

.data {
  cursor: pointer
}

  .data .selected {
    background-color: #efefef
  }

.statementMenu div {
  display: none;
  background-color: #fefede
}

  .statementMenu div a {
    display: inline-block;
    padding: 10px 15px 10px 40px;
    background-image: url(/ui/images/icons/car.png);
    background-repeat: no-repeat;
    text-decoration: none;
    background-position: 10px 10px
  }

    .statementMenu div a:hover {
      background-color: #afafaf;
      color: white
    }

.checkbox {
  text-align: center;
}

  .checkbox * {
    border: none;
  }

#ch {
  margin-left: 230px;
  padding-left: 10px;
  padding-right: 0px;
  background-color: #fafafa;
}

  #ch h1 {
    margin-top: 0px;
    margin-left: -11px;
    margin-right: 0px;
    margin-bottom: 20px;
    font-weight: normal;
    font-size: 20px;
    box-shadow: rgba(0, 0, 0, 0.1) 0 1px 5px;
    display: block;
    border-radius: 2px;
    border: none;
    background-color: white;
    padding-left: 16px;
    padding-bottom: 12px;
    padding-top: 12px;
    padding-right: 20px;
    color: #888;
  }

    #ch h1 span {
      font-family: "segoe ui light", "Open Sans", Georgia, helvetica;
      text-transform: none;
    }

  #ch #ajaxContent {
    margin-left: 10px
  }

.submenu {
  padding-bottom: 15px
}

ul#appMenu li div#ajaxLoading {
  background-color: transparent;
  background-image: url(../images/ajax-loader.gif);
  background-position: 0px 0px;
  background-repeat: no-repeat;
  border: none;
  width: 16px;
  height: 16px;
  float: right;
}

ul#appMenu li .submenu .ajaxLoading {
  padding-right: 5px !important
}

ul#appMenu li .submenu #ajaxLoading {
  background-color: inherit;
  background-image: url(../images/ajax-loader.gif);
  background-position: 0px 0px;
  background-repeat: no-repeat;
  border: none;
  width: 16px;
  height: 16px;
  float: right;
  position: relative;
  margin-left: 5px;
  margin-top: 3px
}

ul#appMenu li .submenu {
  display: none
}

.check-box, dt input.checkbox {
  display: inline-block;
  border: none;
  padding: 0;
  margin: 0px 0 0 0;
  width: auto;
}

#appMenuHolder {
  float: left;
  width: 230px;
  background-color: white;
  border-right: solid 1px #dfdfdf
}

@media (min-width: 481px) and (max-width: 980px) {
  #appMenuHolder {
    width: 26px;
    overflow: hidden;
    z-index: 999999;
    border-right: 3px solid gray;
    padding-right: 5px
  }

  ul#appMenu li.selected div a {
    visibility: hidden
  }

  #ch {
    margin-left: 32px
  }

  #appMenuHolder:hover {
    width: 230px;
    border-right-color: #efefef
  }

    #appMenuHolder:hover ul#appMenu li.selected div a {
      visibility: visible
    }
}

@media (min-width: 980px) {
  #appMenuHolder {
    width: 230px;
    overflow: visible
  }
}

#appMenuHolder {
  transition: all .2s linear;
  -o-transition: all .2s linear;
  -moz-transition: all .2s linear;
  -webkit-transition: all .2s linear;
}
