var w2ui=w2ui||{},w2obj=w2obj||{},w2utils=function(){function t(n){var t=/^[-+]?[0-9]+$/;return t.test(n)}function i(n){return(typeof n=="number"||typeof n=="string"&&n!=="")&&!isNaN(Number(n))}function r(n){var t=w2utils.settings,i=new RegExp("^"+(t.currencyPrefix?"\\"+t.currencyPrefix+"?":"")+"[-+]?[0-9]*[.]?[0-9]+"+(t.currencySuffix?"\\"+t.currencySuffix+"?":"")+"$","i");return(typeof n=="string"&&(n=n.replace(new RegExp(t.groupSymbol,"g"),"")),typeof n=="object"||n==="")?!1:i.test(n)}function u(n){return/^[a-fA-F0-9]+$/.test(n)}function f(n){return/^[a-zA-Z0-9_-]+$/.test(n)}function e(n){return/^[a-zA-Z0-9._%-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,4}$/.test(n)}function o(n,i,r){var h,f,e,o,c,a,l,u,s;if(!n)return!1;if(h="Invalid Date",i==null&&(i=w2utils.settings.date_format),typeof n.getUTCFullYear=="function"&&typeof n.getUTCMonth=="function"&&typeof n.getUTCDate=="function")o=n.getUTCFullYear(),f=n.getUTCMonth(),e=n.getUTCDate();else if(typeof n.getFullYear=="function"&&typeof n.getMonth=="function"&&typeof n.getDate=="function")o=n.getFullYear(),f=n.getMonth(),e=n.getDate();else{if(n=String(n),RegExp("mon","ig").test(i))for(i=i.replace(/month/ig,"m").replace(/mon/ig,"m").replace(/dd/ig,"d").replace(/[, ]/ig,"/").replace(/\/\//g,"/").toLowerCase(),n=n.replace(/[, ]/ig,"/").replace(/\/\//g,"/").toLowerCase(),c=0,a=w2utils.settings.fullmonths.length;c<a;c++)l=w2utils.settings.fullmonths[c],n=n.replace(RegExp(l,"ig"),parseInt(c)+1).replace(RegExp(l.substr(0,3),"ig"),parseInt(c)+1);u=n.replace(/-/g,"/").replace(/\./g,"/").toLowerCase().split("/"),s=i.replace(/-/g,"/").replace(/\./g,"/").toLowerCase(),s==="mm/dd/yyyy"&&(f=u[0],e=u[1],o=u[2]),s==="m/d/yyyy"&&(f=u[0],e=u[1],o=u[2]),s==="dd/mm/yyyy"&&(f=u[1],e=u[0],o=u[2]),s==="d/m/yyyy"&&(f=u[1],e=u[0],o=u[2]),s==="yyyy/dd/mm"&&(f=u[2],e=u[1],o=u[0]),s==="yyyy/d/m"&&(f=u[2],e=u[1],o=u[0]),s==="yyyy/mm/dd"&&(f=u[1],e=u[2],o=u[0]),s==="yyyy/m/d"&&(f=u[1],e=u[2],o=u[0]),s==="mm/dd/yy"&&(f=u[0],e=u[1],o=u[2]),s==="m/d/yy"&&(f=u[0],e=u[1],o=parseInt(u[2])+1900),s==="dd/mm/yy"&&(f=u[1],e=u[0],o=parseInt(u[2])+1900),s==="d/m/yy"&&(f=u[1],e=u[0],o=parseInt(u[2])+1900),s==="yy/dd/mm"&&(f=u[2],e=u[1],o=parseInt(u[0])+1900),s==="yy/d/m"&&(f=u[2],e=u[1],o=parseInt(u[0])+1900),s==="yy/mm/dd"&&(f=u[1],e=u[2],o=parseInt(u[0])+1900),s==="yy/m/d"&&(f=u[1],e=u[2],o=parseInt(u[0])+1900)}return t(o)?t(f)?t(e)?(o=+o,f=+f,e=+e,h=new Date(o,f-1,e),f==null)?!1:h==="Invalid Date"?!1:h.getMonth()+1!==f||h.getDate()!==e||h.getFullYear()!==o?!1:r===!0?h:!0:!1:!1:!1}function s(n,t){var e,o,u;if(n==null)return!1;n=String(n),n=n.toUpperCase(),o=n.indexOf("PM")>=0,u=o||n.indexOf("AM")>=0,e=u?12:24,n=n.replace("AM","").replace("PM",""),n=$.trim(n);var i=n.split(":"),r=parseInt(i[0]||0),f=parseInt(i[1]||0);return(!u||i.length!==1)&&i.length!==2?!1:i[0]===""||r<0||r>e||!this.isInt(i[0])||i[0].length>2?!1:i.length===2&&(i[1]===""||f<0||f>59||!this.isInt(i[1])||i[1].length!==2)?!1:!u&&e===r&&f!==0?!1:u&&i.length===1&&r===0?!1:t===!0?(o&&(r+=12),{hours:r,minutes:f}):!0}function h(n){var u;if(n===""||n==null||(u=new Date(n),w2utils.isInt(n)&&(u=new Date(Number(n))),u==="Invalid Date"))return"";var f=new Date,t=(f.getTime()-u.getTime())/1e3,i="",r="";return t<0?(i='<span style="color: #aaa">future<\/span>',r=""):t<60?(i=Math.floor(t),r="sec",t<0&&(i=0,r="sec")):t<3600?(i=Math.floor(t/60),r="min"):t<86400?(i=Math.floor(t/3600),r="hour"):t<2592e3?(i=Math.floor(t/86400),r="day"):t<365.25*86400?(i=Math.floor(t/365.25/8640)/10,r="month"):t>=365.25*86400&&(i=Math.floor(t/365.25/8640)/10,r="year"),i+" "+r+(i>1?"s":"")}function c(n){var t;if(n===""||n==null||(t=new Date(n),w2utils.isInt(n)&&(t=new Date(Number(n))),t==="Invalid Date"))return"";var u=w2utils.settings.shortmonths,f=new Date,i=new Date;i.setTime(i.getTime()-864e5);var r=u[t.getMonth()]+" "+t.getDate()+", "+t.getFullYear(),o=u[f.getMonth()]+" "+f.getDate()+", "+f.getFullYear(),s=u[i.getMonth()]+" "+i.getDate()+", "+i.getFullYear(),h=t.getHours()-(t.getHours()>12?12:0)+":"+(t.getMinutes()<10?"0":"")+t.getMinutes()+" "+(t.getHours()>=12?"pm":"am"),c=t.getHours()-(t.getHours()>12?12:0)+":"+(t.getMinutes()<10?"0":"")+t.getMinutes()+":"+(t.getSeconds()<10?"0":"")+t.getSeconds()+" "+(t.getHours()>=12?"pm":"am"),e=r;return r===o&&(e=h),r===s&&(e=w2utils.lang("Yesterday")),'<span title="'+r+" "+c+'">'+e+"<\/span>"}function l(n){if(!w2utils.isFloat(n)||n==="")return"";if(n=parseFloat(n),n===0)return 0;var t=parseInt(Math.floor(Math.log(n)/Math.log(1024)));return(Math.floor(n/Math.pow(1024,t)*10)/10).toFixed(t===0?0:1)+" "+["Bt","KB","MB","GB","TB"][t]}function a(t,i){var r="";return i==null&&(i=w2utils.settings.groupSymbol||","),(w2utils.isFloat(t)||w2utils.isInt(t)||w2utils.isMoney(t))&&(n=String(t).split("."),r=String(n[0]).replace(/(\d)(?=(\d\d\d)+(?!\d))/g,"$1"+i),n[1]!=null&&(r+="."+n[1])),r}function v(n,t){var e=w2utils.settings.shortmonths,o=w2utils.settings.fullmonths,i;if((t||(t=this.settings.date_format),n===""||n==null)||(i=new Date(n),w2utils.isInt(n)&&(i=new Date(Number(n))),i==="Invalid Date"))return"";var r=i.getFullYear(),u=i.getMonth(),f=i.getDate();return t.toLowerCase().replace("month",w2utils.settings.fullmonths[u]).replace("mon",w2utils.settings.shortmonths[u]).replace(/yyyy/g,r).replace(/yyy/g,r).replace(/yy/g,r>2e3?100+parseInt(String(r).substr(2)):String(r).substr(2)).replace(/(^|[^a-z$])y/g,"$1"+r).replace(/mm/g,(u+1<10?"0":"")+(u+1)).replace(/dd/g,(f<10?"0":"")+f).replace(/(^|[^a-z$])m/g,"$1"+(u+1)).replace(/(^|[^a-z$])d/g,"$1"+f)}function y(n,t){var h=w2utils.settings.shortmonths,c=w2utils.settings.fullmonths,i,e;if((t||(t=this.settings.time_format==="h12"?"hh:mi pm":"h24:mi"),n===""||n==null)||(i=new Date(n),w2utils.isInt(n)&&(i=new Date(Number(n))),w2utils.isTime(n)&&(e=w2utils.isTime(n,!0),i=new Date,i.setHours(e.hours),i.setMinutes(e.minutes)),i==="Invalid Date"))return"";var o="am",r=i.getHours(),s=i.getHours(),u=i.getMinutes(),f=i.getSeconds();return u<10&&(u="0"+u),f<10&&(f="0"+f),(t.indexOf("am")!==-1||t.indexOf("pm")!==-1)&&(r>=12&&(o="pm"),r>12&&(r=r-12)),t.toLowerCase().replace("am",o).replace("pm",o).replace("hh",r).replace("h24",s).replace("mm",u).replace("mi",u).replace("ss",f).replace(/(^|[^a-z$])h/g,"$1"+r).replace(/(^|[^a-z$])m/g,"$1"+u).replace(/(^|[^a-z$])s/g,"$1"+f)}function p(n,t){var i;return i=typeof t!="string"?[this.settings.date_format,this.settings.time_format]:t.split("|"),this.formatDate(n,i[0])+" "+this.formatTime(n,i[1])}function w(n){if(n===null)return n;switch(typeof n){case"string":n=$.trim(String(n).replace(/(<([^>]+)>)/ig,""));break;case"object":for(var t in n)n[t]=this.stripTags(n[t])}return n}function b(n){if(n===null)return n;switch(typeof n){case"string":n=String(n).replace(/&/g,"&amp;").replace(/>/g,"&gt;").replace(/</g,"&lt;").replace(/"/g,"&quot;");break;case"object":for(var t in n)n[t]=this.encodeTags(n[t])}return n}function k(n){return n===""||n==null?"":String(n).replace(/([;&,\.\+\*\~'`:"\!\^#$%@\[\]\(\)=<>\|\/? {}\\])/g,"\\$1")}function d(n){function l(n){for(var n=String(n).replace(/\r\n/g,"\n"),i="",t,r=0;r<n.length;r++)t=n.charCodeAt(r),t<128?i+=String.fromCharCode(t):t>127&&t<2048?(i+=String.fromCharCode(t>>6|192),i+=String.fromCharCode(t&63|128)):(i+=String.fromCharCode(t>>12|224),i+=String.fromCharCode(t>>6&63|128),i+=String.fromCharCode(t&63|128));return i}var e="",o,t,i,h,c,s,r,u=0,f="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";for(n=l(n);u<n.length;)o=n.charCodeAt(u++),t=n.charCodeAt(u++),i=n.charCodeAt(u++),h=o>>2,c=(o&3)<<4|t>>4,s=(t&15)<<2|i>>6,r=i&63,isNaN(t)?s=r=64:isNaN(i)&&(r=64),e=e+f.charAt(h)+f.charAt(c)+f.charAt(s)+f.charAt(r);return e}function g(n){function l(n){for(var r="",t=0,i=0,u,f;t<n.length;)i=n.charCodeAt(t),i<128?(r+=String.fromCharCode(i),t++):i>191&&i<224?(u=n.charCodeAt(t+1),r+=String.fromCharCode((i&31)<<6|u&63),t+=2):(u=n.charCodeAt(t+1),f=n.charCodeAt(t+2),r+=String.fromCharCode((i&15)<<12|(u&63)<<6|f&63),t+=3);return r}var t="",o,s,h,c,f,r,e,i=0,u="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";for(n=n.replace(/[^A-Za-z0-9\+\/\=]/g,"");i<n.length;)c=u.indexOf(n.charAt(i++)),f=u.indexOf(n.charAt(i++)),r=u.indexOf(n.charAt(i++)),e=u.indexOf(n.charAt(i++)),o=c<<2|f>>4,s=(f&15)<<4|r>>2,h=(r&3)<<6|e,t=t+String.fromCharCode(o),r!==64&&(t=t+String.fromCharCode(s)),e!==64&&(t=t+String.fromCharCode(h));return l(t)}function nt(n,t,i,r){function u(n,t,i){var r=!!window.webkitURL;return r||typeof i=="undefined"||(t=i),";"+n+": "+t+"; -webkit-"+n+": "+t+"; -moz-"+n+": "+t+"; -ms-"+n+": "+t+"; -o-"+n+": "+t+";"}var e=$(n).width(),o=$(n).height(),f=.5;if(!n||!t){console.log("ERROR: Cannot do transition when one of the divs is null");return}n.parentNode.style.cssText+=u("perspective","700px")+"; overflow: hidden;",n.style.cssText+="; position: absolute; z-index: 1019; "+u("backface-visibility","hidden"),t.style.cssText+="; position: absolute; z-index: 1020; "+u("backface-visibility","hidden");switch(i){case"slide-left":n.style.cssText+="overflow: hidden; "+u("transform","translate3d(0, 0, 0)","translate(0, 0)"),t.style.cssText+="overflow: hidden; "+u("transform","translate3d("+e+"px, 0, 0)","translate("+e+"px, 0)"),$(t).show(),window.setTimeout(function(){t.style.cssText+=u("transition",f+"s")+";"+u("transform","translate3d(0, 0, 0)","translate(0, 0)"),n.style.cssText+=u("transition",f+"s")+";"+u("transform","translate3d(-"+e+"px, 0, 0)","translate(-"+e+"px, 0)")},1);break;case"slide-right":n.style.cssText+="overflow: hidden; "+u("transform","translate3d(0, 0, 0)","translate(0, 0)"),t.style.cssText+="overflow: hidden; "+u("transform","translate3d(-"+e+"px, 0, 0)","translate(-"+e+"px, 0)"),$(t).show(),window.setTimeout(function(){t.style.cssText+=u("transition",f+"s")+"; "+u("transform","translate3d(0px, 0, 0)","translate(0px, 0)"),n.style.cssText+=u("transition",f+"s")+"; "+u("transform","translate3d("+e+"px, 0, 0)","translate("+e+"px, 0)")},1);break;case"slide-down":n.style.cssText+="overflow: hidden; z-index: 1; "+u("transform","translate3d(0, 0, 0)","translate(0, 0)"),t.style.cssText+="overflow: hidden; z-index: 0; "+u("transform","translate3d(0, 0, 0)","translate(0, 0)"),$(t).show(),window.setTimeout(function(){t.style.cssText+=u("transition",f+"s")+"; "+u("transform","translate3d(0, 0, 0)","translate(0, 0)"),n.style.cssText+=u("transition",f+"s")+"; "+u("transform","translate3d(0, "+o+"px, 0)","translate(0, "+o+"px)")},1);break;case"slide-up":n.style.cssText+="overflow: hidden; "+u("transform","translate3d(0, 0, 0)","translate(0, 0)"),t.style.cssText+="overflow: hidden; "+u("transform","translate3d(0, "+o+"px, 0)","translate(0, "+o+"px)"),$(t).show(),window.setTimeout(function(){t.style.cssText+=u("transition",f+"s")+"; "+u("transform","translate3d(0, 0, 0)","translate(0, 0)"),n.style.cssText+=u("transition",f+"s")+"; "+u("transform","translate3d(0, 0, 0)","translate(0, 0)")},1);break;case"flip-left":n.style.cssText+="overflow: hidden; "+u("transform","rotateY(0deg)"),t.style.cssText+="overflow: hidden; "+u("transform","rotateY(-180deg)"),$(t).show(),window.setTimeout(function(){t.style.cssText+=u("transition",f+"s")+"; "+u("transform","rotateY(0deg)"),n.style.cssText+=u("transition",f+"s")+"; "+u("transform","rotateY(180deg)")},1);break;case"flip-right":n.style.cssText+="overflow: hidden; "+u("transform","rotateY(0deg)"),t.style.cssText+="overflow: hidden; "+u("transform","rotateY(180deg)"),$(t).show(),window.setTimeout(function(){t.style.cssText+=u("transition",f+"s")+"; "+u("transform","rotateY(0deg)"),n.style.cssText+=u("transition",f+"s")+"; "+u("transform","rotateY(-180deg)")},1);break;case"flip-down":n.style.cssText+="overflow: hidden; "+u("transform","rotateX(0deg)"),t.style.cssText+="overflow: hidden; "+u("transform","rotateX(180deg)"),$(t).show(),window.setTimeout(function(){t.style.cssText+=u("transition",f+"s")+"; "+u("transform","rotateX(0deg)"),n.style.cssText+=u("transition",f+"s")+"; "+u("transform","rotateX(-180deg)")},1);break;case"flip-up":n.style.cssText+="overflow: hidden; "+u("transform","rotateX(0deg)"),t.style.cssText+="overflow: hidden; "+u("transform","rotateX(-180deg)"),$(t).show(),window.setTimeout(function(){t.style.cssText+=u("transition",f+"s")+"; "+u("transform","rotateX(0deg)"),n.style.cssText+=u("transition",f+"s")+"; "+u("transform","rotateX(180deg)")},1);break;case"pop-in":n.style.cssText+="overflow: hidden; "+u("transform","translate3d(0, 0, 0)","translate(0, 0)"),t.style.cssText+="overflow: hidden; "+u("transform","translate3d(0, 0, 0)","translate(0, 0)")+"; "+u("transform","scale(.8)")+"; opacity: 0;",$(t).show(),window.setTimeout(function(){t.style.cssText+=u("transition",f+"s")+"; "+u("transform","scale(1)")+"; opacity: 1;",n.style.cssText+=u("transition",f+"s")+";"},1);break;case"pop-out":n.style.cssText+="overflow: hidden; "+u("transform","translate3d(0, 0, 0)","translate(0, 0)")+"; "+u("transform","scale(1)")+"; opacity: 1;",t.style.cssText+="overflow: hidden; "+u("transform","translate3d(0, 0, 0)","translate(0, 0)")+"; opacity: 0;",$(t).show(),window.setTimeout(function(){t.style.cssText+=u("transition",f+"s")+"; opacity: 1;",n.style.cssText+=u("transition",f+"s")+"; "+u("transform","scale(1.7)")+"; opacity: 0;"},1);break;default:n.style.cssText+="overflow: hidden; "+u("transform","translate3d(0, 0, 0)","translate(0, 0)"),t.style.cssText+="overflow: hidden; "+u("transform","translate3d(0, 0, 0)","translate(0, 0)")+"; opacity: 0;",$(t).show(),window.setTimeout(function(){t.style.cssText+=u("transition",f+"s")+"; opacity: 1;",n.style.cssText+=u("transition",f+"s")},1)}setTimeout(function(){i==="slide-down"&&($(n).css("z-index","1019"),$(t).css("z-index","1020")),t&&$(t).css({opacity:"1","-webkit-transition":"","-moz-transition":"","-ms-transition":"","-o-transition":"","-webkit-transform":"","-moz-transform":"","-ms-transform":"","-o-transform":"","-webkit-backface-visibility":"","-moz-backface-visibility":"","-ms-backface-visibility":"","-o-backface-visibility":""}),n&&($(n).css({opacity:"1","-webkit-transition":"","-moz-transition":"","-ms-transition":"","-o-transition":"","-webkit-transform":"","-moz-transform":"","-ms-transform":"","-o-transform":"","-webkit-backface-visibility":"","-moz-backface-visibility":"","-ms-backface-visibility":"","-o-backface-visibility":""}),n.parentNode&&$(n.parentNode).css({"-webkit-perspective":"","-moz-perspective":"","-ms-perspective":"","-o-perspective":""})),typeof r=="function"&&r()},f*1e3)}function tt(n,t,i){var r={},u,f;typeof t=="object"?r=t:(r.msg=t,r.spinner=i),r.msg||r.msg===0||(r.msg=""),w2utils.unlock(n),$(n).prepend('<div class="w2ui-lock"><\/div><div class="w2ui-lock-msg"><\/div>'),u=$(n).find(".w2ui-lock"),f=$(n).find(".w2ui-lock-msg"),r.msg||f.css({"background-color":"transparent",border:"0px"}),r.spinner===!0&&(r.msg='<div class="w2ui-spinner" '+(r.msg?"":'style="width: 35px; height: 35px"')+"><\/div>"+r.msg),r.opacity!=null&&u.css("opacity",r.opacity),typeof u.fadeIn=="function"?(u.fadeIn(200),f.html(r.msg).fadeIn(200)):(u.show(),f.html(r.msg).show(0)),$().w2tag()}function it(n){$(n).find(".w2ui-lock").remove(),$(n).find(".w2ui-lock-msg").remove()}function rt(n,t){var i=$(n),r={left:parseInt(i.css("border-left-width"))||0,right:parseInt(i.css("border-right-width"))||0,top:parseInt(i.css("border-top-width"))||0,bottom:parseInt(i.css("border-bottom-width"))||0},u={left:parseInt(i.css("margin-left"))||0,right:parseInt(i.css("margin-right"))||0,top:parseInt(i.css("margin-top"))||0,bottom:parseInt(i.css("margin-bottom"))||0},f={left:parseInt(i.css("padding-left"))||0,right:parseInt(i.css("padding-right"))||0,top:parseInt(i.css("padding-top"))||0,bottom:parseInt(i.css("padding-bottom"))||0};switch(t){case"top":return r.top+u.top+f.top;case"bottom":return r.bottom+u.bottom+f.bottom;case"left":return r.left+u.left+f.left;case"right":return r.right+u.right+f.right;case"width":return r.left+r.right+u.left+u.right+f.left+f.right+parseInt(i.width());case"height":return r.top+r.bottom+u.top+u.bottom+f.top+f.bottom+parseInt(i.height());case"+width":return r.left+r.right+u.left+u.right+f.left+f.right;case"+height":return r.top+r.bottom+u.top+u.bottom+f.top+f.bottom}return 0}function ut(n){var t=this.settings.phrases[n];return t==null?n:t}function ft(n){n||(n="en-us"),n.length===5&&(n="locale/"+n+".json"),$.ajax({url:n,type:"GET",dataType:"JSON",async:!1,cache:!1,success:function(n){var t,i;w2utils.settings=$.extend(!0,w2utils.settings,n),t=w2obj.grid.prototype;for(i in t.buttons)t.buttons[i].caption=w2utils.lang(t.buttons[i].caption),t.buttons[i].hint=w2utils.lang(t.buttons[i].hint);t.msgDelete=w2utils.lang(t.msgDelete),t.msgNotJSON=w2utils.lang(t.msgNotJSON),t.msgRefresh=w2utils.lang(t.msgRefresh)},error:function(){console.log("ERROR: Cannot load locale "+n)}})}function et(){if(n.scrollBarSize)return n.scrollBarSize;return $("body").append('<div id="_scrollbar_width" style="position: absolute; top: -300px; width: 100px; height: 100px; overflow-y: scroll;">    <div style="height: 120px">1<\/div><\/div>'),n.scrollBarSize=100-$("#_scrollbar_width > div").width(),$("#_scrollbar_width").remove(),String(navigator.userAgent).indexOf("MSIE")>=0&&(n.scrollBarSize=n.scrollBarSize/2),n.scrollBarSize}function ot(n,t){return!n||typeof n.name=="undefined"?(console.log('ERROR: The parameter "name" is required but not supplied in $().'+t+"()."),!1):typeof w2ui[n.name]!="undefined"?(console.log('ERROR: The parameter "name" is not unique. There are other objects already created with the same name (obj: '+n.name+")."),!1):w2utils.isAlphaNumeric(n.name)?!0:(console.log('ERROR: The parameter "name" has to be alpha-numeric (a-z, 0-9, dash and underscore). '),!1)}function st(n,t,i,r){$.isArray(t)||(t=[t]);for(var u=0;u<t.length;u++)if(t[u].id===n)return console.log('ERROR: The parameter "id='+n+'" is not unique within the current '+i+". (obj: "+r+")"),!1;return!0}function ht(n){var t=[],i=n.replace(/\/\(/g,"(?:/").replace(/\+/g,"__plus__").replace(/(\/)?(\.)?:(\w+)(?:(\(.*?\)))?(\?)?/g,function(n,i,r,u,f,e){return t.push({name:u,optional:!!e}),i=i||"",""+(e?"":i)+"(?:"+(e?i:"")+(r||"")+(f||r&&"([^/.]+?)"||"([^/]+?)")+")"+(e||"")}).replace(/([\/.])/g,"\\$1").replace(/__plus__/g,"(.+)").replace(/\*/g,"(.*)");return{path:new RegExp("^"+i+"$","i"),keys:t}}var n={};return{version:"1.4.0",settings:{locale:"en-us",date_format:"m/d/yyyy",date_display:"Mon d, yyyy",time_format:"h12",currencyPrefix:"$",currencySuffix:"",currencyPrecision:2,groupSymbol:",",shortmonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],fullmonths:["January","February","March","April","May","June","July","August","September","October","November","December"],shortdays:["M","T","W","T","F","S","S"],fulldays:["Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","Sunday"],dataType:"HTTP",phrases:{}},isInt:t,isFloat:i,isMoney:r,isHex:u,isAlphaNumeric:f,isEmail:e,isDate:o,isTime:s,age:h,date:c,size:l,formatNumber:a,formatDate:v,formatTime:y,formatDateTime:p,stripTags:w,encodeTags:b,escapeId:k,base64encode:d,base64decode:g,transition:nt,lock:tt,unlock:it,lang:ut,locale:ft,getSize:rt,scrollBarSize:et,checkName:ot,checkUniqueId:st,parseRoute:ht,isIOS:navigator.userAgent.toLowerCase().indexOf("iphone")!=-1||navigator.userAgent.toLowerCase().indexOf("ipod")!=-1||navigator.userAgent.toLowerCase().indexOf("ipad")!=-1?!0:!1,isIE:navigator.userAgent.toLowerCase().indexOf("msie")!=-1||navigator.userAgent.toLowerCase().indexOf("trident")!=-1?!0:!1}}(),w2popup,w2alert,w2confirm;w2utils.event={on:function(n,t){if($.isPlainObject(n)||(n={type:n}),n=$.extend({type:null,execute:"before",target:null,onComplete:null},n),!n.type){console.log("ERROR: You must specify event type when calling .on() method of "+this.name);return}if(!t){console.log("ERROR: You must specify event handler function when calling .on() method of "+this.name);return}$.isArray(this.handlers)||(this.handlers=[]),this.handlers.push({event:n,handler:t})},off:function(n,t){var u,r,f,i;if($.isPlainObject(n)||(n={type:n}),n=$.extend({},{type:null,execute:"before",target:null,onComplete:null},n),!n.type){console.log("ERROR: You must specify event type when calling .off() method of "+this.name);return}for(t||(t=null),u=[],r=0,f=this.handlers.length;r<f;r++)i=this.handlers[r],(i.event.type===n.type||n.type==="*")&&(i.event.target===n.target||n.target===null)&&(i.handler===t||t===null)||u.push(i);this.handlers=u},trigger:function(n){var n=$.extend({type:null,phase:"before",target:null},n,{isStopped:!1,isCancelled:!1,preventDefault:function(){this.isCancelled=!0},stopPropagation:function(){this.isStopped=!0}}),i,u,r,e,t,f;for(n.phase==="before"&&(n.onComplete=null),n.target==null&&(n.target=null),$.isArray(this.handlers)||(this.handlers=[]),e=this.handlers.length-1;e>=0;e--)if(t=this.handlers[e],(t.event.type===n.type||t.event.type==="*")&&(t.event.target===n.target||t.event.target===null)&&(t.event.execute===n.phase||t.event.execute==="*"||t.event.phase==="*")&&(n=$.extend({},t.event,n),i=[],r=RegExp(/\((.*?)\)/).exec(t.handler),r&&(i=r[1].split(/\s*,\s*/)),i.length===2?t.handler.call(this,n.target,n):t.handler.call(this,n),n.isStopped===!0||n.stop===!0))return n;return(f="on"+n.type.substr(0,1).toUpperCase()+n.type.substr(1),n.phase==="before"&&typeof this[f]=="function"&&(u=this[f],i=[],r=RegExp(/\((.*?)\)/).exec(u),r&&(i=r[1].split(/\s*,\s*/)),i.length===2?u.call(this,n.target,n):u.call(this,n),n.isStopped===!0||n.stop===!0))?n:n.object!=null&&n.phase==="before"&&typeof n.object[f]=="function"&&(u=n.object[f],i=[],r=RegExp(/\((.*?)\)/).exec(u),r&&(i=r[1].split(/\s*,\s*/)),i.length===2?u.call(this,n.target,n):u.call(this,n),n.isStopped===!0||n.stop===!0)?n:(n.phase==="after"&&typeof n.onComplete=="function"&&n.onComplete.call(this,n),n)}},w2utils.keyboard=function(n){function i(){$(document).on("keydown",r);$(document).on("mousedown",u)}function r(n){var i=n.target.tagName;$.inArray(i,["INPUT","SELECT","TEXTAREA"])===-1&&$(n.target).prop("contenteditable")!=="true"&&t&&w2ui[t]&&typeof w2ui[t].keydown=="function"&&w2ui[t].keydown.call(w2ui[t],n)}function u(n){var u=n.target.tagName,r=$(n.target).parents(".w2ui-reset"),i;r.length>0&&(i=r.attr("name"),w2ui[i]&&w2ui[i].keyboard&&(t=i))}function f(n){return typeof n!="undefined"&&(t=n),t}function e(){t=null}var t=null;return n.active=f,n.clear=e,i(),n}({}),function(){$.fn.w2render=function(n){$(this).length>0&&(typeof n=="string"&&w2ui[n]&&w2ui[n].render($(this)[0]),typeof n=="object"&&n.render($(this)[0]))},$.fn.w2destroy=function(n){!n&&this.length>0&&(n=this.attr("name")),typeof n=="string"&&w2ui[n]&&w2ui[n].destroy(),typeof n=="object"&&n.destroy()},$.fn.w2marker=function(n){return n===""||n==null?$(this).each(function(n,t){t.innerHTML=t.innerHTML.replace(/\<span class=\"w2ui\-marker\"\>(.*)\<\/span\>/ig,"$1")}):$(this).each(function(t,i){function e(n){return'<span class="w2ui-marker">'+n+"<\/span>"}var u,r,f;typeof n=="string"&&(n=[n]),i.innerHTML=i.innerHTML.replace(/\<span class=\"w2ui\-marker\"\>(.*)\<\/span\>/ig,"$1");for(u in n)r=n[u],typeof r!="string"&&(r=String(r)),r=r.replace(/[-[\]{}()*+?.,\\^$|#\s]/g,"\\$&").replace(/&/g,"&amp;").replace(/</g,"&gt;").replace(/>/g,"&lt;"),f=new RegExp(r+"(?!([^<]+)?>)","gi"),i.innerHTML=i.innerHTML.replace(f,e)})},$.fn.w2tag=function(n,t){if($.isPlainObject(t)||(t={}),$.isPlainObject(t.css)||(t.css={}),typeof t["class"]=="undefined"&&(t["class"]=""),$(this).length===0){$(".w2ui-tag").each(function(n,t){var i=$(t).data("options");i==null&&(i={}),$($(t).data("taged-el")).removeClass(i["class"]),clearInterval($(t).data("timer")),$(t).remove()});return}return $(this).each(function(i,r){var s=r.id,u=w2utils.escapeId(r.id),o,e;if(n===""||n==null)$("#w2ui-tag-"+u).css("opacity",0),setTimeout(function(){clearInterval($("#w2ui-tag-"+u).data("timer")),$("#w2ui-tag-"+u).remove()},300);else{clearInterval($("#w2ui-tag-"+u).data("timer")),$("#w2ui-tag-"+u).remove(),$("body").append('<div id="w2ui-tag-'+s+'" class="w2ui-tag '+($(r).parents(".w2ui-popup").length>0?"w2ui-tag-popup":"")+'" style=""><\/div>'),o=setInterval(function(){if($(r).length===0||$(r).offset().left===0&&$(r).offset().top===0){clearInterval($("#w2ui-tag-"+u).data("timer")),f();return}$("#w2ui-tag-"+u).data("position")!==$(r).offset().left+r.offsetWidth+"x"+$(r).offset().top&&$("#w2ui-tag-"+u).css({"-webkit-transition":".2s","-moz-transition":".2s","-ms-transition":".2s","-o-transition":".2s",left:$(r).offset().left+r.offsetWidth+"px",top:$(r).offset().top+"px"}).data("position",$(r).offset().left+r.offsetWidth+"x"+$(r).offset().top)},100),setTimeout(function(){$(r).offset()&&($("#w2ui-tag-"+u).css({opacity:"1",left:$(r).offset().left+r.offsetWidth+"px",top:$(r).offset().top+"px"}).html('<div style="margin-top: -2px 0px 0px -2px; white-space: nowrap;"> <div class="w2ui-tag-body">'+n+"<\/div> <\/div>").data("text",n).data("taged-el",r).data("options",t).data("position",$(r).offset().left+r.offsetWidth+"x"+$(r).offset().top).data("timer",o),$(r).off("keypress",f).on("keypress",f).off("change",f).on("change",f).css(t.css).addClass(t["class"]),typeof t.onShow=="function"&&t.onShow())},1),e="",$(r).length>0&&(e=$(r)[0].style.cssText);function f(){($tag=$("#w2ui-tag-"+u),$tag.length<=0)||(clearInterval($tag.data("timer")),$tag.remove(),$(r).off("keypress",f).removeClass(t["class"]),$(r).length>0&&($(r)[0].style.cssText=e),typeof t.onHide=="function"&&t.onHide())}}})},$.fn.w2overlay=function(n,t){function c(){var n=$("#w2ui-overlay"+i),t;n.data("element")===r[0]&&n.length!==0&&(t=$(r).offset().left+"x"+$(r).offset().top,n.data("position")!==t?o():setTimeout(c,250))}function o(){var n=$("#w2ui-overlay"+i),r;if(n.data("keepOpen")===!0){n.removeData("keepOpen");return}(typeof t.onHide=="function"&&(r=t.onHide()),r!==!1)&&(n.remove(),$(document).off("click",o),clearInterval(n.data("timer")))}function s(){var e=$("#w2ui-overlay"+i),n=e.find(" > div"),f,c;if(e.length>0){n.height("auto").width("auto");var l=!1,o=n.height(),u=n.width();t.width&&t.width<u&&(u=t.width),u<30&&(u=30),t.tmp.contentHeight&&(o=t.tmp.contentHeight,n.height(o),setTimeout(function(){n.height()>n.find("div.menu > table").height()&&n.find("div.menu").css("overflow-y","hidden")},1),setTimeout(function(){n.find("div.menu").css("overflow-y","auto")},10)),t.tmp.contentWidth&&(u=t.tmp.contentWidth,n.width(u),setTimeout(function(){n.width()>n.find("div.menu > table").width()&&n.find("div.menu").css("overflow-x","hidden")},1),setTimeout(function(){n.find("div.menu").css("overflow-y","auto")},10));switch(t.align){case"both":t.left=17,t.width===0&&(t.width=w2utils.getSize($(r),"width"));break;case"left":t.left=17;break;case"right":t.tipLeft=u-45,t.left=w2utils.getSize($(r),"width")-u+10}var a=(u-17)/2,y=t.left,v=t.width,h=t.tipLeft;v=u!==30||v?t.width?t.width:"auto":30,a<25&&(y=25-a,h=Math.floor(a)),e.css({top:r.offset().top+w2utils.getSize(r,"height")+t.top+7+"px",left:(r.offset().left>25?r.offset().left:25)+y+"px","min-width":v,"min-height":t.height?t.height:"auto"}),f=window.innerHeight+$(document).scrollTop()-n.offset().top-7,c=window.innerWidth+$(document).scrollLeft()-n.offset().left-7,f>-50&&f<210||t.openAbove===!0?(f=n.offset().top-$(document).scrollTop()-7,t.maxHeight&&f>t.maxHeight&&(f=t.maxHeight),o>f&&(l=!0,n.height(f).width(u).css({"overflow-y":"auto"}),o=f),e.css("top",$(r).offset().top-o-24+t.top+"px"),e.find(">style").html("#w2ui-overlay"+i+":before { display: none; margin-left: "+parseInt(h)+"px; }#w2ui-overlay"+i+":after { display: block; margin-left: "+parseInt(h)+"px; }")):(t.maxHeight&&f>t.maxHeight&&(f=t.maxHeight),o>f&&(l=!0,n.height(f).width(u).css({"overflow-y":"auto"})),e.find(">style").html("#w2ui-overlay"+i+":before { display: block; margin-left: "+parseInt(h)+"px; }#w2ui-overlay"+i+":after { display: none; margin-left: "+parseInt(h)+"px; }")),u=n.width(),c=window.innerWidth+$(document).scrollLeft()-n.offset().left-7,t.maxWidth&&c>t.maxWidth&&(c=t.maxWidth),u>c&&t.align!=="both"&&(t.align="right",setTimeout(function(){s()},1)),l&&!1&&n.width(u+w2utils.scrollBarSize()+2)}}var r=this,i="",u,f,h,e;if(arguments.length==1&&(t=typeof n=="object"?n:{html:n}),arguments.length==2&&(t.html=n),$.isPlainObject(t)||(t={}),t=$.extend({},{name:null,html:"",align:"none",left:0,top:0,tipLeft:30,width:0,height:0,maxWidth:null,maxHeight:null,style:"","class":"",onShow:null,onHide:null,openAbove:!1,tmp:{}},t),t.name&&(i="-"+t.name),this.length===0||t.html===""||t.html==null)return $("#w2ui-overlay"+i).length>0?(u=$("#w2ui-overlay"+i)[0].hide,typeof u=="function"&&u()):$("#w2ui-overlay"+i).remove(),$(this);$("#w2ui-overlay"+i).length>0&&(u=$("#w2ui-overlay"+i)[0].hide,$(document).off("click",u),typeof u=="function"&&u()),$("body").append('<div id="w2ui-overlay'+i+'" style="display: none"        class="w2ui-reset w2ui-overlay '+($(this).parents(".w2ui-popup, .w2ui-overlay-popup").length>0?"w2ui-overlay-popup":"")+'">    <style><\/style>    <div style="'+t.style+'" class="'+t["class"]+'"><\/div><\/div>'),f=$("#w2ui-overlay"+i),h=f.find(" > div"),h.html(t.html),e=h.css("background-color"),e!=null&&e!=="rgba(0, 0, 0, 0)"&&e!=="transparent"&&f.css("background-color",e);f.data("element",r.length>0?r[0]:null).data("options",t).data("position",$(r).offset().left+"x"+$(r).offset().top).fadeIn("fast").on("mousedown",function(n){$("#w2ui-overlay"+i).data("keepOpen",!0),["INPUT","TEXTAREA","SELECT"].indexOf(n.target.tagName)===-1&&n.preventDefault()});return f[0].hide=o,f[0].resize=s,s(),setTimeout(function(){s();$(document).off("click",o).on("click",o);typeof t.onShow=="function"&&t.onShow()},10),c(),$(this)},$.fn.w2menu=function(n,t){function f(){setTimeout(function(){var n,r;if($("#w2ui-overlay"+i+" tr.w2ui-selected").removeClass("w2ui-selected"),n=$("#w2ui-overlay"+i+" tr[index="+t.index+"]"),r=$("#w2ui-overlay"+i+" div.menu").scrollTop(),n.addClass("w2ui-selected"),t.tmp&&(t.tmp.contentHeight=$("#w2ui-overlay"+i+" table").height()+(t.search?50:10)),t.tmp&&(t.tmp.contentWidth=$("#w2ui-overlay"+i+" table").width()),$("#w2ui-overlay"+i).length>0&&$("#w2ui-overlay"+i)[0].resize(),n.length>0){var u=n[0].offsetTop-5,e=$("#w2ui-overlay"+i+" div.menu"),f=e.height();$("#w2ui-overlay"+i+" div.menu").scrollTop(r),(u<r||u+n.height()>r+f)&&$("#w2ui-overlay"+i+" div.menu").animate({scrollTop:u-(f-n.height()*2)/2},200,"linear")}},1)}function c(n){var a=this.value,v=n.keyCode,e=!1,o,s,l;switch(v){case 13:$("#w2ui-overlay"+i).remove(),$.fn.w2menuHandler(n,t.index);break;case 9:case 27:$("#w2ui-overlay"+i).remove(),$.fn.w2menuHandler(n,-1);break;case 38:for(t.index=w2utils.isInt(t.index)?parseInt(t.index):0,t.index--;t.index>0&&t.items[t.index].hidden;)t.index--;if(t.index===0&&t.items[t.index].hidden)while(t.items[t.index]&&t.items[t.index].hidden)t.index++;t.index<0&&(t.index=0),e=!0;break;case 40:for(t.index=w2utils.isInt(t.index)?parseInt(t.index):0,t.index++;t.index<t.items.length-1&&t.items[t.index].hidden;)t.index++;if(t.index===t.items.length-1&&t.items[t.index].hidden)while(t.items[t.index]&&t.items[t.index].hidden)t.index--;t.index>=t.items.length&&(t.index=t.items.length-1),e=!0}if(!e){o=0;for(s in t.items){var u=t.items[s],h="",c="";["is","begins with"].indexOf(t.match)!==-1&&(h="^"),["is","ends with"].indexOf(t.match)!==-1&&(c="$");try{l=new RegExp(h+a+c,"i"),u.hidden=l.test(u.text)||u.text==="..."?!1:!0}catch(y){}r.type==="enum"&&$.inArray(u.id,ids)!==-1&&(u.hidden=!0),u.hidden!==!0&&o++}for(t.index=0;t.index<t.items.length-1&&t.items[t.index].hidden;)t.index++;o<=0&&(t.index=-1)}$(r).w2menu("refresh",t),f()}function h(){var r,n,o,u,l,h;if(t.spinner)return'<table class="w2ui-drop-menu"><tr><td style="padding: 5px 10px 10px 10px; text-align: center">    <div class="w2ui-spinner" style="width: 18px; height: 18px; position: relative; top: 5px;"><\/div>     <div style="display: inline-block; padding: 3px; color: #999;"> Loading...<\/div><\/td><\/tr><\/table>';var c=0,s='<table cellspacing="0" cellpadding="0" class="w2ui-drop-menu">',f=null,e=null;for(r=0;r<t.items.length;r++)n=t.items[r],typeof n=="string"?n={id:n,text:n}:(n.text!=null&&n.id==null&&(n.id=n.text),n.text==null&&n.id!=null&&(n.text=n.id),n.caption!=null&&(n.text=n.caption),f=n.img,e=n.icon,f==null&&(f=null),e==null&&(e=null)),n.hidden!==!0&&(o="",u=n.text,typeof t.render=="function"&&(u=t.render(n,t)),f&&(o='<td class="menu-icon"><div class="w2ui-tb-image w2ui-icon '+f+'"><\/div><\/td>'),e&&(o='<td class="menu-icon" align="center"><span class="w2ui-icon '+e+'"><\/span><\/td>'),typeof u=="undefined"||u===""||/^-+$/.test(u)?s+='<tr><td colspan="2" style="padding: 6px; pointer-events: none"><div style="border-top: 1px solid silver;"><\/div><\/td><\/tr>':(l=c%2==0?"w2ui-item-even":"w2ui-item-odd",t.altRows!==!0&&(l=""),h=1,o==""&&h++,n.count==null&&h++,s+='<tr index="'+r+'" style="'+(n.style?n.style:"")+'"         class="'+l+" "+(t.index===r?"w2ui-selected":"")+" "+(n.disabled===!0?"w2ui-disabled":"")+"\"        onmousedown=\"$(this).parent().find('tr').removeClass('w2ui-selected'); $(this).addClass('w2ui-selected');\"        onclick=\"event.stopPropagation();                if ("+(n.disabled===!0?"true":"false")+") return;               $('#w2ui-overlay"+i+"').remove();                $.fn.w2menuHandler(event, '"+r+"');\">"+o+'   <td class="menu-text" colspan="'+h+'">'+u+'<\/td>   <td class="menu-count">'+(n.count!=null?"<span>"+n.count+"<\/span>":"")+"<\/td><\/tr>",c++)),t.items[r]=n;return c===0&&(s+='<tr><td style="padding: 13px; color: #999; text-align: center">'+t.msgNoItems+"<\/div><\/td><\/tr>"),s+"<\/table>"}var r=this,i="",e,u,o,s;if(n==="refresh")$("#w2ui-overlay"+i).length>0?(t=$.extend($.fn.w2menuOptions,t),e=$("#w2ui-overlay"+i+" div.menu").scrollTop(),$("#w2ui-overlay"+i+" div.menu").html(h()),$("#w2ui-overlay"+i+" div.menu").scrollTop(e),f()):$(this).w2menu(t);else{if(arguments.length===1?t=n:t.items=n,typeof t!="object"&&(t={}),t=$.extend({},{index:null,items:[],render:null,msgNoItems:"No items",onSelect:null,tmp:{}},t),$.fn.w2menuOptions=t,t.name&&(i="-"+t.name),typeof t.select=="function"&&typeof t.onSelect!="function"&&(t.onSelect=t.select),typeof t.onRender=="function"&&typeof t.render!="function"&&(t.render=t.onRender),$.fn.w2menuHandler=function(n,i){typeof t.onSelect=="function"&&setTimeout(function(){t.onSelect({index:i,item:t.items[i],originalEvent:n})},10),setTimeout(function(){$(document).click()},50)},u="",t.search){u+='<div style="position: absolute; top: 0px; height: 40px; left: 0px; right: 0px; border-bottom: 1px solid silver; background-color: #ECECEC; padding: 8px 5px;">    <div class="w2ui-icon icon-search" style="position: absolute; margin-top: 4px; margin-left: 6px; width: 11px; background-position: left !important;"><\/div>    <input id="menu-search" type="text" style="width: 100%; outline: none; padding-left: 20px;" onclick="event.stopPropagation();"><\/div>',t.style+=";background-color: #ECECEC",t.index=0;for(o in t.items)t.items[o].hidden=!1}return u+='<div class="menu" style="position: absolute; top: '+(t.search?40:0)+'px; bottom: 0px; width: 100%; overflow: auto;">'+h()+"<\/div>",s=$(this).w2overlay(u,t),setTimeout(function(){$("#w2ui-overlay"+i+" #menu-search").on("keyup",c).on("keydown",function(n){n.keyCode===9&&(n.stopPropagation(),n.preventDefault())});if(t.search){if(["text","password"].indexOf($(r)[0].type)!=-1||$(r)[0].tagName=="texarea")return;$("#w2ui-overlay"+i+" #menu-search").focus()}},200),f(),s}}}(),function(){var n=function(n){this.name=null,this.box=null,this.header="",this.url="",this.routeData={},this.columns=[],this.columnGroups=[],this.records=[],this.summary=[],this.searches=[],this.searchData=[],this.sortData=[],this.postData={},this.toolbar={},this.show={header:!1,toolbar:!1,footer:!1,columnHeaders:!0,lineNumbers:!1,expandColumn:!1,selectColumn:!1,emptyRecords:!0,toolbarReload:!0,toolbarColumns:!0,toolbarSearch:!0,toolbarAdd:!1,toolbarEdit:!1,toolbarDelete:!1,toolbarSave:!1,selectionBorder:!0,recordTitles:!0,skipRecords:!0},this.autoLoad=!0,this.fixedBody=!0,this.recordHeight=24,this.keyboard=!0,this.selectType="row",this.multiSearch=!0,this.multiSelect=!0,this.multiSort=!0,this.reorderColumns=!1,this.reorderRows=!1,this.markSearch=!0,this.total=0,this.limit=100,this.offset=0,this.style="",this.ranges=[],this.menu=[],this.method=null,this.recid=null,this.parser=null,this.onAdd=null,this.onEdit=null,this.onRequest=null,this.onLoad=null,this.onDelete=null,this.onDeleted=null,this.onSubmit=null,this.onSave=null,this.onSelect=null,this.onUnselect=null,this.onClick=null,this.onDblClick=null,this.onContextMenu=null,this.onMenuClick=null,this.onColumnClick=null,this.onColumnResize=null,this.onSort=null,this.onSearch=null,this.onChange=null,this.onRestore=null,this.onExpand=null,this.onCollapse=null,this.onError=null,this.onKeydown=null,this.onToolbar=null,this.onColumnOnOff=null,this.onCopy=null,this.onPaste=null,this.onSelectionExtend=null,this.onEditField=null,this.onRender=null,this.onRefresh=null,this.onReload=null,this.onResize=null,this.onDestroy=null,this.onStateSave=null,this.onStateRestore=null,this.last={field:"all",caption:w2utils.lang("All Fields"),logic:"OR",search:"",searchIds:[],selection:{indexes:[],columns:{}},multi:!1,scrollTop:0,scrollLeft:0,sortData:null,sortCount:0,xhr:null,range_start:null,range_end:null,sel_ind:null,sel_col:null,sel_type:null,edit_col:null},$.extend(!0,this,w2obj.grid,n)};$.fn.w2grid=function(t){var r,f,p,u,o,s,h;if(typeof t!="object"&&t){if(w2ui[$(this).attr("name")])return h=w2ui[$(this).attr("name")],h[t].apply(h,Array.prototype.slice.call(arguments,1)),this;console.log("ERROR: Method "+t+" does not exist on jQuery.w2grid")}else{if(!w2utils.checkName(t,"w2grid"))return;var c=t.columns,l=t.columnGroups,e=t.records,a=t.searches,v=t.searchData,y=t.sortData,w=t.postData,b=t.toolbar,i=new n(t);$.extend(i,{postData:{},records:[],columns:[],searches:[],toolbar:{},sortData:[],searchData:[],handlers:[]}),$.extend(!0,i.toolbar,b);for(r in c)i.columns[r]=$.extend(!0,{},c[r]);for(r in l)i.columnGroups[r]=$.extend(!0,{},l[r]);for(r in a)i.searches[r]=$.extend(!0,{},a[r]);for(r in v)i.searchData[r]=$.extend(!0,{},v[r]);for(r in y)i.sortData[r]=$.extend(!0,{},y[r]);i.postData=$.extend(!0,{},w);for(f in e){if(e[f].recid==null||typeof e[f].recid=="undefined"){console.log("ERROR: Cannot add records without recid. (obj: "+i.name+")");return}i.records[f]=$.extend(!0,{},e[f])}for(p in i.columns)(u=i.columns[p],typeof u.searchable!="undefined"&&i.getSearch(u.field)==null)&&(o=u.searchable,s="",u.searchable===!0&&(o="text",s='size="20"'),i.addSearch({field:u.field,caption:u.caption,type:o,attr:s}));return i.initToolbar(),$(this).length!==0&&i.render($(this)[0]),w2ui[i.name]=i,i}},n.prototype={msgDelete:"Are you sure you want to delete selected records?",msgNotJSON:"Returned data is not in valid JSON format.",msgAJAXerror:"AJAX error. See console for more details.",msgRefresh:"Refreshing...",buttons:{reload:{type:"button",id:"w2ui-reload",icon:"w2ui-icon-reload",hint:"Reload data in the list"},columns:{type:"drop",id:"w2ui-column-on-off",icon:"w2ui-icon-columns",hint:"Show/hide columns",arrow:!1,html:""},search:{type:"html",id:"w2ui-search",html:'<div class="w2ui-icon icon-search-down w2ui-search-down" title="Select Search Field" onclick="var obj = w2ui[$(this).parents(\'div.w2ui-grid\').attr(\'name\')]; obj.searchShowFields();"><\/div>'},"search-go":{type:"check",id:"w2ui-search-advanced",caption:"Search...",hint:"Open Search Fields"},add:{type:"button",id:"w2ui-add",caption:"Add New",hint:"Add new record",icon:"w2ui-icon-plus"},edit:{type:"button",id:"w2ui-edit",caption:"Edit",hint:"Edit selected record",icon:"w2ui-icon-pencil",disabled:!0},"delete":{type:"button",id:"w2ui-delete",caption:"Delete",hint:"Delete selected records",icon:"w2ui-icon-cross",disabled:!0},save:{type:"button",id:"w2ui-save",caption:"Save",hint:"Save changed records",icon:"w2ui-icon-check"}},add:function(n){var i,t,r;$.isArray(n)||(n=[n]),i=0;for(t in n){if(this.recid||typeof n[t].recid!="undefined"||(n[t].recid=n[t][this.recid]),n[t].recid==null||typeof n[t].recid=="undefined"){console.log("ERROR: Cannot add record without recid. (obj: "+this.name+")");continue}this.records.push(n[t]),i++}return r=typeof this.url!="object"?this.url:this.url.get,r||(this.total=this.records.length,this.localSort(),this.localSearch()),this.refresh(),i},find:function(n,t){var u,e,r,f,i,o;(typeof n=="undefined"||n==null)&&(n={}),u=[],e=!1;for(i in n)String(i).indexOf(".")!=-1&&(e=!0);for(r=0;r<this.records.length;r++){f=!0;for(i in n)o=this.records[r][i],e&&String(i).indexOf(".")!=-1&&(o=this.parseField(this.records[r],i)),n[i]!=o&&(f=!1);f&&t!==!0&&u.push(this.records[r].recid),f&&t===!0&&u.push(r)}return u},set:function(n,t,i){var u,r;if(typeof n=="object"&&(i=t,t=n,n=null),n==null){for(u in this.records)$.extend(!0,this.records[u],t);i!==!0&&this.refresh()}else{if(r=this.get(n,!0),r==null)return!1;$.extend(!0,this.records[r],t),i!==!0&&this.refreshRow(n)}return!0},get:function(n,t){for(var i=0;i<this.records.length;i++)if(this.records[i].recid==n)return t===!0?i:this.records[i];return null},remove:function(){for(var i=0,n,r,t=0;t<arguments.length;t++)for(n=this.records.length-1;n>=0;n--)this.records[n].recid==arguments[t]&&(this.records.splice(n,1),i++);return r=typeof this.url!="object"?this.url:this.url.get,r||(this.localSort(),this.localSearch()),this.refresh(),i},addColumn:function(n,t){var i=0,r;arguments.length==1?(t=n,n=this.columns.length):(typeof n=="string"&&(n=this.getColumn(n,!0)),n===null&&(n=this.columns.length)),$.isArray(t)||(t=[t]);for(r in t)this.columns.splice(n,0,t[r]),n++,i++;return this.refresh(),i},removeColumn:function(){for(var i=0,n,t=0;t<arguments.length;t++)for(n=this.columns.length-1;n>=0;n--)this.columns[n].field==arguments[t]&&(this.columns.splice(n,1),i++);return this.refresh(),i},getColumn:function(n,t){for(var i=0;i<this.columns.length;i++)if(this.columns[i].field==n)return t===!0?i:this.columns[i];return null},toggleColumn:function(){for(var r=0,t,i,n=0;n<arguments.length;n++)for(t=this.columns.length-1;t>=0;t--)i=this.columns[t],i.field==arguments[n]&&(i.hidden=!i.hidden,r++);return this.refresh(),r},showColumn:function(){for(var r=0,i,n,t=0;t<arguments.length;t++)for(i=this.columns.length-1;i>=0;i--)n=this.columns[i],n.gridMinWidth&&delete n.gridMinWidth,n.field==arguments[t]&&n.hidden!==!1&&(n.hidden=!1,r++);return this.refresh(),r},hideColumn:function(){for(var r=0,t,i,n=0;n<arguments.length;n++)for(t=this.columns.length-1;t>=0;t--)i=this.columns[t],i.field==arguments[n]&&i.hidden!==!0&&(i.hidden=!0,r++);return this.refresh(),r},addSearch:function(n,t){var i=0,r;arguments.length==1?(t=n,n=this.searches.length):(typeof n=="string"&&(n=this.getSearch(n,!0)),n===null&&(n=this.searches.length)),$.isArray(t)||(t=[t]);for(r in t)this.searches.splice(n,0,t[r]),n++,i++;return this.searchClose(),i},removeSearch:function(){for(var i=0,n,t=0;t<arguments.length;t++)for(n=this.searches.length-1;n>=0;n--)this.searches[n].field==arguments[t]&&(this.searches.splice(n,1),i++);return this.searchClose(),i},getSearch:function(n,t){for(var i=0;i<this.searches.length;i++)if(this.searches[i].field==n)return t===!0?i:this.searches[i];return null},toggleSearch:function(){for(var i=0,n,t=0;t<arguments.length;t++)for(n=this.searches.length-1;n>=0;n--)this.searches[n].field==arguments[t]&&(this.searches[n].hidden=!this.searches[n].hidden,i++);return this.searchClose(),i},showSearch:function(){for(var i=0,n,t=0;t<arguments.length;t++)for(n=this.searches.length-1;n>=0;n--)this.searches[n].field==arguments[t]&&this.searches[n].hidden!==!1&&(this.searches[n].hidden=!1,i++);return this.searchClose(),i},hideSearch:function(){for(var i=0,n,t=0;t<arguments.length;t++)for(n=this.searches.length-1;n>=0;n--)this.searches[n].field==arguments[t]&&this.searches[n].hidden!==!0&&(this.searches[n].hidden=!0,i++);return this.searchClose(),i},getSearchData:function(n){for(var t in this.searchData)if(this.searchData[t].field==n)return this.searchData[t];return null},localSort:function(n){var f=typeof this.url!="object"?this.url:this.url.get,r,t,u,i;if(f){console.log("ERROR: grid.localSort can only be used on local data source, grid.url should be empty.");return}if(!$.isEmptyObject(this.sortData)){r=(new Date).getTime(),t=this,t.prepareData();for(u in this.sortData){if(i=this.getColumn(this.sortData[u].field),!i)return;i.render&&["date","age"].indexOf(i.render.split(":")[0])!=-1&&(this.sortData[u].field_=i.field+"_"),i.render&&["time"].indexOf(i.render.split(":")[0])!=-1&&(this.sortData[u].field_=i.field+"_")}return this.records.sort(function(n,i){var f=0,o,e,r,u;for(o in t.sortData)if(e=t.sortData[o].field,t.sortData[o].field_&&(e=t.sortData[o].field_),r=n[e],u=i[e],String(e).indexOf(".")!=-1&&(r=t.parseField(n,e),u=t.parseField(i,e)),typeof r=="string"&&(r=$.trim(r.toLowerCase())),typeof u=="string"&&(u=$.trim(u.toLowerCase())),r>u&&(f=t.sortData[o].direction=="asc"?1:-1),r<u&&(f=t.sortData[o].direction=="asc"?-1:1),typeof r!="object"&&typeof u=="object"&&(f=-1),typeof u!="object"&&typeof r=="object"&&(f=1),r==null&&u!=null&&(f=1),r!=null&&u==null&&(f=-1),f!=0)break;return f}),r=(new Date).getTime()-r,n!==!0&&setTimeout(function(){t.status("Sorting took "+r/1e3+" sec")},10),r}},localSearch:function(n){var a=typeof this.url!="object"?this.url:this.url.get,h,c,l,o,e,v,r,u,f,i,t,s;if(a){console.log("ERROR: grid.localSearch can only be used on local data source, grid.url should be empty.");return}if(h=(new Date).getTime(),c=this,this.total=this.records.length,this.last.searchIds=[],this.prepareData(),this.searchData.length>0&&!a){this.total=0;for(l in this.records){o=this.records[l],e=0;for(v in this.searchData)if(r=this.searchData[v],u=this.getSearch(r.field),r!=null){u==null&&(u={field:r.field,type:r.type}),i=String(c.parseField(o,u.field)).toLowerCase(),typeof r.value!="undefined"&&($.isArray(r.value)?(t=r.value[0],f=r.value[1]):t=String(r.value).toLowerCase());switch(r.operator){case"is":o[u.field]==r.value&&e++,u.type=="date"&&(i=w2utils.formatDate(o[u.field+"_"],"yyyy-mm-dd"),t=w2utils.formatDate(t,"yyyy-mm-dd"),i==t&&e++),u.type=="time"&&(i=w2utils.formatTime(o[u.field+"_"],"h24:mi"),t=w2utils.formatTime(t,"h24:mi"),i==t&&e++);break;case"between":if(["int","float","money","currency","percent"].indexOf(u.type)!=-1&&parseFloat(o[u.field])>=parseFloat(t)&&parseFloat(o[u.field])<=parseFloat(f)&&e++,u.type=="date"){var i=o[u.field+"_"],t=w2utils.isDate(t,w2utils.settings.date_format,!0),f=w2utils.isDate(f,w2utils.settings.date_format,!0);f!=null&&(f=new Date(f.getTime()+864e5)),i>=t&&i<f&&e++}if(u.type=="time"){var i=o[u.field+"_"],t=w2utils.isTime(t,!0),f=w2utils.isTime(f,!0);t=(new Date).setHours(t.hours,t.minutes,t.seconds?t.seconds:0,0),f=(new Date).setHours(f.hours,f.minutes,f.seconds?f.seconds:0,0),i>=t&&i<f&&e++}break;case"in":s=r.value,r.svalue&&(s=r.svalue),s.indexOf(i)!==-1&&e++;break;case"not in":s=r.value,r.svalue&&(s=r.svalue),s.indexOf(i)==-1&&e++;break;case"begins":case"begins with":i.indexOf(t)==0&&e++;break;case"contains":i.indexOf(t)>=0&&e++;break;case"ends":case"ends with":i.indexOf(t)==i.length-t.length&&e++}}(this.last.logic=="OR"&&e!=0||this.last.logic=="AND"&&e==this.searchData.length)&&this.last.searchIds.push(parseInt(l))}this.total=this.last.searchIds.length}return h=(new Date).getTime()-h,n!==!0&&setTimeout(function(){c.status("Search took "+h/1e3+" sec")},10),h},getRangeData:function(n,t){var o=this.get(n[0].recid,!0),h=this.get(n[1].recid,!0),s=n[0].column,c=n[1].column,e=[],i,r,f,u;if(s==c)for(i=o;i<=h;i++)r=this.records[i],u=r[this.columns[s].field]||null,t!==!0?e.push(u):e.push({data:u,column:s,index:i,record:r});else if(o==h)for(r=this.records[o],f=s;f<=c;f++)u=r[this.columns[f].field]||null,t!==!0?e.push(u):e.push({data:u,column:f,index:o,record:r});else for(i=o;i<=h;i++)for(r=this.records[i],e.push([]),f=s;f<=c;f++)u=r[this.columns[f].field],t!==!0?e[e.length-1].push(u):e[e.length-1].push({data:u,column:f,index:i,record:r});return e},addRange:function(n){var e=0,t,u,o,f,s;if(this.selectType=="row")return e;$.isArray(n)||(n=[n]);for(t in n){if(typeof n[t]!="object"&&(n[t]={name:"selection"}),n[t].name=="selection"){if(this.show.selectionBorder===!1)continue;if(u=this.getSelection(),u.length==0){this.removeRange(n[t].name);continue}else var i=u[0],r=u[u.length-1],h=$("#grid_"+this.name+"_rec_"+i.recid+" td[col="+i.column+"]"),c=$("#grid_"+this.name+"_rec_"+r.recid+" td[col="+r.column+"]")}else var i=n[t].range[0],r=n[t].range[1],h=$("#grid_"+this.name+"_rec_"+i.recid+" td[col="+i.column+"]"),c=$("#grid_"+this.name+"_rec_"+r.recid+" td[col="+r.column+"]");if(i){o={name:n[t].name,range:[{recid:i.recid,column:i.column},{recid:r.recid,column:r.column}],style:n[t].style||""},f=!1;for(s in this.ranges)if(this.ranges[s].name==n[t].name){f=t;break}f!==!1?this.ranges[f]=o:this.ranges.push(o),e++}}return this.refreshRanges(),e},removeRange:function(){for(var r=0,i,n,t=0;t<arguments.length;t++)for(i=arguments[t],$("#grid_"+this.name+"_"+i).remove(),n=this.ranges.length-1;n>=0;n--)this.ranges[n].name==i&&(this.ranges.splice(n,1),r++);return r},refreshRanges:function(){function a(t){var i=n.getSelection();n.last.move={type:"expand",x:t.screenX,y:t.screenY,divX:0,divY:0,recid:i[0].recid,column:i[0].column,originalRange:[{recid:i[0].recid,column:i[0].column},{recid:i[i.length-1].recid,column:i[i.length-1].column}],newRange:[{recid:i[0].recid,column:i[0].column},{recid:i[i.length-1].recid,column:i[i.length-1].column}]};$(document).off("mousemove",e).on("mousemove",e);$(document).off("mouseup",o).on("mouseup",o)}function e(t){var r=n.last.move,f,e,u,o;if(r&&r.type=="expand"&&(r.divX=t.screenX-r.x,r.divY=t.screenY-r.y,u=t.originalEvent.target,u.tagName!="TD"&&(u=$(u).parents("td")[0]),typeof $(u).attr("col")!="undefined"&&(e=parseInt($(u).attr("col"))),u=$(u).parents("tr")[0],f=$(u).attr("recid"),r.newRange[1].recid!=f||r.newRange[1].column!=e)){if(o=$.extend({},r.newRange),r.newRange=[{recid:r.recid,column:r.column},{recid:f,column:e}],i=n.trigger($.extend(i,{originalRange:r.originalRange,newRange:r.newRange})),i.isCancelled===!0){r.newRange=o,i.newRange=o;return}n.removeRange("grid-selection-expand"),n.addRange({name:"grid-selection-expand",range:i.newRange,style:"background-color: rgba(100,100,100,0.1); border: 2px dotted rgba(100,100,100,0.5);"})}}function o(){n.removeRange("grid-selection-expand"),delete n.last.move,$(document).off("mousemove",e),$(document).off("mouseup",o),n.trigger($.extend(i,{phase:"after"}))}var n=this,l=(new Date).getTime(),f=$("#grid_"+this.name+"_records"),s,i;for(s in this.ranges){var t=this.ranges[s],h=t.range[0],c=t.range[1],r=$("#grid_"+this.name+"_rec_"+h.recid+" td[col="+h.column+"]"),u=$("#grid_"+this.name+"_rec_"+c.recid+" td[col="+c.column+"]");$("#grid_"+this.name+"_"+t.name).length==0?f.append('<div id="grid_'+this.name+"_"+t.name+'" class="w2ui-selection" style="'+t.style+'">'+(t.name=="selection"?'<div id="grid_'+this.name+'_resizer" class="w2ui-selection-resizer"><\/div>':"")+"<\/div>"):$("#grid_"+this.name+"_"+t.name).attr("style",t.style),r.length>0&&u.length>0&&$("#grid_"+this.name+"_"+t.name).css({left:r.position().left-1+f.scrollLeft()+"px",top:r.position().top-1+f.scrollTop()+"px",width:u.position().left-r.position().left+u.width()+3+"px",height:u.position().top-r.position().top+u.height()+3+"px"})}$(this.box).find("#grid_"+this.name+"_resizer").off("mousedown").on("mousedown",a);return i={phase:"before",type:"selectionExtend",target:n.name,originalRange:null,newRange:null},(new Date).getTime()-l},select:function(){var h=0,n=this.last.selection,i,r,l,t,u,e,s,c,o,f;for(this.multiSelect||this.selectNone(),i=0;i<arguments.length;i++)if(r=typeof arguments[i]=="object"?arguments[i].recid:arguments[i],l=this.get(r),l!=null){if(t=this.get(r,!0),u=$("#grid_"+this.name+"_rec_"+w2utils.escapeId(r)),this.selectType=="row"){if(n.indexes.indexOf(t)>=0)continue;if(f=this.trigger({phase:"before",type:"select",target:this.name,recid:r,index:t}),f.isCancelled===!0)continue;n.indexes.push(t),n.indexes.sort(function(n,t){return n-t}),u.addClass("w2ui-selected").data("selected","yes"),u.find(".w2ui-grid-select-check").prop("checked",!0),h++}else{if(e=arguments[i].column,!w2utils.isInt(e)){s=[];for(c in this.columns)this.columns[c].hidden||s.push({recid:r,column:parseInt(c)});return this.multiSelect||(s=s.splice(0,1)),this.select.apply(this,s)}if(o=n.columns[t]||[],$.isArray(o)&&o.indexOf(e)!=-1)continue;if(f=this.trigger({phase:"before",type:"select",target:this.name,recid:r,index:t,column:e}),f.isCancelled===!0)continue;n.indexes.indexOf(t)==-1&&(n.indexes.push(t),n.indexes.sort(function(n,t){return n-t})),o.push(e),o.sort(function(n,t){return n-t}),u.find(" > td[col="+e+"]").addClass("w2ui-selected"),h++,u.data("selected","yes"),u.find(".w2ui-grid-select-check").prop("checked",!0),n.columns[t]=o}this.trigger($.extend(f,{phase:"after"}))}return n.indexes.length==this.records.length||this.searchData.length!==0&&n.indexes.length==this.last.searchIds.length?$("#grid_"+this.name+"_check_all").prop("checked",!0):$("#grid_"+this.name+"_check_all").prop("checked",!1),this.status(),this.addRange("selection"),h},unselect:function(){for(var s=0,n=this.last.selection,r,h,u,t,e,c,l,o,f,i=0;i<arguments.length;i++)if(r=typeof arguments[i]=="object"?arguments[i].recid:arguments[i],h=this.get(r),h!=null){if(u=this.get(h.recid,!0),t=$("#grid_"+this.name+"_rec_"+w2utils.escapeId(r)),this.selectType=="row"){if(n.indexes.indexOf(u)==-1)continue;if(f=this.trigger({phase:"before",type:"unselect",target:this.name,recid:r,index:u}),f.isCancelled===!0)continue;n.indexes.splice(n.indexes.indexOf(u),1),t.removeClass("w2ui-selected").removeData("selected"),t.length!=0&&(t[0].style.cssText="height: "+this.recordHeight+"px; "+t.attr("custom_style")),t.find(".w2ui-grid-select-check").prop("checked",!1),s++}else{if(e=arguments[i].column,!w2utils.isInt(e)){c=[];for(l in this.columns)this.columns[l].hidden||c.push({recid:r,column:parseInt(l)});return this.unselect.apply(this,c)}if(o=n.columns[u],!$.isArray(o)||o.indexOf(e)==-1)continue;if(f=this.trigger({phase:"before",type:"unselect",target:this.name,recid:r,column:e}),f.isCancelled===!0)continue;o.splice(o.indexOf(e),1),$("#grid_"+this.name+"_rec_"+w2utils.escapeId(r)+" > td[col="+e+"]").removeClass("w2ui-selected"),s++,o.length==0&&(delete n.columns[u],n.indexes.splice(n.indexes.indexOf(u),1),t.removeData("selected"),t.find(".w2ui-grid-select-check").prop("checked",!1))}this.trigger($.extend(f,{phase:"after"}))}return n.indexes.length==this.records.length||this.searchData.length!==0&&n.indexes.length==this.last.searchIds.length?$("#grid_"+this.name+"_check_all").prop("checked",!0):$("#grid_"+this.name+"_check_all").prop("checked",!1),this.status(),this.addRange("selection"),s},selectAll:function(){var i,f,u,n,t;if(this.multiSelect!==!1&&(i=this.trigger({phase:"before",type:"select",target:this.name,all:!0}),i.isCancelled!==!0)){var e=typeof this.url!="object"?this.url:this.url.get,t=this.last.selection,r=[];for(f in this.columns)r.push(parseInt(f));if(t.indexes=[],e||this.searchData.length===0)for(u=this.records.length,this.searchData.length==0||this.url||(u=this.last.searchIds.length),n=0;n<u;n++)t.indexes.push(n),this.selectType!="row"&&(t.columns[n]=r.slice());else for(n=0;n<this.last.searchIds.length;n++)t.indexes.push(this.last.searchIds[n]),this.selectType!="row"&&(t.columns[this.last.searchIds[n]]=r.slice());this.refresh(),t=this.getSelection(),t.length==1?this.toolbar.enable("w2ui-edit"):this.toolbar.disable("w2ui-edit"),t.length>=1?this.toolbar.enable("w2ui-delete"):this.toolbar.disable("w2ui-delete"),this.addRange("selection"),this.trigger($.extend(i,{phase:"after"}))}},selectNone:function(){var r=this.trigger({phase:"before",type:"unselect",target:this.name,all:!0}),n,u,i,o;if(r.isCancelled!==!0){n=this.last.selection;for(u in n.indexes){var f=n.indexes[u],e=this.records[f],s=e?e.recid:null,t=$("#grid_"+this.name+"_rec_"+w2utils.escapeId(s));if(t.removeClass("w2ui-selected").removeData("selected"),t.find(".w2ui-grid-select-check").prop("checked",!1),this.selectType!="row"){i=n.columns[f];for(o in i)t.find(" > td[col="+i[o]+"]").removeClass("w2ui-selected")}}n.indexes=[],n.columns={},this.toolbar.disable("w2ui-edit","w2ui-delete"),this.removeRange("selection"),$("#grid_"+this.name+"_check_all").prop("checked",!1),this.trigger($.extend(r,{phase:"after"}))}},getSelection:function(n){var r=[],t=this.last.selection,i,u,f;if(this.selectType=="row"){for(i in t.indexes)this.records[t.indexes[i]]&&(n===!0?r.push(t.indexes[i]):r.push(this.records[t.indexes[i]].recid));return r}for(i in t.indexes)if(u=t.columns[t.indexes[i]],this.records[t.indexes[i]])for(f in u)r.push({recid:this.records[t.indexes[i]].recid,index:parseInt(t.indexes[i]),column:u[f]});return r},search:function(n,t){var st=this,it=typeof this.url!="object"?this.url:this.url.get,f=[],l=this.last.multi,a=this.last.logic,rt=this.last.field,w=this.last.search,g,nt,v,e,ft,et,h,p,y,r,c,ot,tt,i,k;if(arguments.length==0){w="";for(e in this.searches){var i=this.searches[e],o=$("#grid_"+this.name+"_operator_"+e).val(),d=$("#grid_"+this.name+"_field_"+e),ut=$("#grid_"+this.name+"_field2_"+e),u=d.val(),s=ut.val(),b=null;if(["int","float","money","currency","percent"].indexOf(i.type)!=-1&&(g=d.data("w2field"),nt=ut.data("w2field"),g&&(u=g.clean(u)),nt&&(s=nt.clean(s))),["list","enum"].indexOf(i.type)!=-1)if(u=d.data("selected")||{},$.isArray(u)){b=[];for(v in u)b.push(w2utils.isFloat(u[v].id)?parseFloat(u[v].id):String(u[v].id).toLowerCase()),delete u[v].hidden}else u=u.id||"";if(u!=""&&u!=null||typeof s!="undefined"&&s!=""){r={field:i.field,type:i.type,operator:o},o=="between"?$.extend(r,{value:[u,s]}):o=="in"&&typeof u=="string"?$.extend(r,{value:u.split(",")}):o=="not in"&&typeof u=="string"?$.extend(r,{value:u.split(",")}):$.extend(r,{value:u}),b&&$.extend(r,{svalue:b});try{i.type=="date"&&o=="between"&&(r.value[0]=u,r.value[1]=s),i.type=="date"&&o=="is"&&(r.value=u)}catch(ht){}f.push(r)}}f.length>0&&!it?(l=!0,a="AND"):(l=!0,a="AND")}if(typeof n=="string"&&(rt=n,w=t,l=!1,a="OR",typeof t!="undefined"))if(n.toLowerCase()=="all")if(this.searches.length>0)for(e in this.searches)i=this.searches[e],(i.type=="text"||i.type=="alphanumeric"&&w2utils.isAlphaNumeric(t)||i.type=="int"&&w2utils.isInt(t)||i.type=="float"&&w2utils.isFloat(t)||i.type=="percent"&&w2utils.isFloat(t)||i.type=="hex"&&w2utils.isHex(t)||i.type=="currency"&&w2utils.isMoney(t)||i.type=="money"&&w2utils.isMoney(t)||i.type=="date"&&w2utils.isDate(t))&&(r={field:i.field,type:i.type,operator:i.type=="text"?"contains":"is",value:t},f.push(r)),["int","float","money","currency","percent"].indexOf(i.type)!=-1&&String(t).indexOf("-")!=-1&&(y=String(t).split("-"),r={field:i.field,type:i.type,operator:"between",value:[y[0],y[1]]},f.push(r));else for(ft in this.columns)r={field:this.columns[ft].field,type:"text",operator:"contains",value:t},f.push(r);else if(et=$("#grid_"+this.name+"_search_all"),i=this.getSearch(n),i==null&&(i={field:n,type:"text"}),i.field==n&&(this.last.caption=i.caption),i.type=="list"&&(r=et.data("selected"),r&&!$.isEmptyObject(r)&&(t=r.id)),t!=""){if(h="contains",p=t,["date","time","list"].indexOf(i.type)!=-1&&(h="is"),i.type=="int"&&t!=""&&(h="is",String(t).indexOf("-")!=-1&&(r=t.split("-"),r.length==2&&(h="between",p=[parseInt(r[0]),parseInt(r[1])])),String(t).indexOf(",")!=-1)){r=t.split(","),h="in",p=[];for(y in r)p.push(r[y])}r={field:i.field,type:i.type,operator:h,value:p},f.push(r)}if($.isArray(n)){c="AND",typeof t=="string"&&(c=t.toUpperCase(),c!="OR"&&c!="AND"&&(c="AND")),w="",l=!0,a=c;for(ot in n)tt=n[ot],i=this.getSearch(tt.field),i==null&&(i={type:"text",operator:"contains"}),f.push($.extend(!0,{},i,tt))}(k=this.trigger({phase:"before",type:"search",target:this.name,searchData:f,searchField:n?n:"multi",searchValue:t?t:"multi"}),k.isCancelled!==!0)&&(this.searchData=k.searchData,this.last.field=rt,this.last.search=w,this.last.multi=l,this.last.logic=a,this.last.scrollTop=0,this.last.scrollLeft=0,this.last.selection.indexes=[],this.last.selection.columns={},this.searchClose(),this.set({expanded:!1},!0),it?(this.last.xhr_offset=0,this.reload()):(this.localSearch(),this.refresh()),this.trigger($.extend(k,{phase:"after"})))},searchOpen:function(){if(this.box&&this.searches.length!=0){var n=this;$("#tb_"+this.name+"_toolbar_item_w2ui-search-advanced").w2overlay(this.getSearchesHTML(),{name:"searches-"+this.name,left:-10,"class":"w2ui-grid-searches",onShow:function(){n.last.logic=="OR"&&(n.searchData=[]),n.initSearches(),$("#w2ui-overlay-searches-"+this.name+" .w2ui-grid-searches").data("grid-name",n.name);var t=$("#w2ui-overlay-searches-"+this.name+" .w2ui-grid-searches *[rel=search]");t.length>0&&t[0].focus()}})}},searchClose:function(){this.box&&this.searches.length!=0&&(this.toolbar&&this.toolbar.uncheck("w2ui-search-advanced"),$("#w2ui-overlay-searches-"+this.name+" .w2ui-grid-searches").length>0&&$().w2overlay("",{name:"searches-"+this.name}))},searchShowFields:function(){for(var r=$("#grid_"+this.name+"_search_all"),i='<div class="w2ui-select-field"><table>',t,n=-1;n<this.searches.length;n++){if(t=this.searches[n],n==-1){if(!this.multiSearch)continue;t={field:"all",caption:w2utils.lang("All Fields")}}else if(this.searches[n].hidden===!0)continue;i+="<tr "+(w2utils.isIOS?"onTouchStart":"onClick")+"=\"w2ui['"+this.name+"'].initAllField('"+t.field+'\')">    <td><input type="radio" tabIndex="-1" '+(t.field==this.last.field?"checked":"")+"><\/td>    <td>"+t.caption+"<\/td><\/tr>"}i+="<\/table><\/div>",setTimeout(function(){$(r).w2overlay(i,{left:-10})},1)},initAllField:function(n,t){var r=$("#grid_"+this.name+"_search_all"),i=this.getSearch(n),u;n=="all"?(i={field:"all",caption:w2utils.lang("All Fields")},r.w2field("clear"),r.change().focus()):(u=i.type,["enum","select"].indexOf(u)!=-1&&(u="list"),r.w2field(u,$.extend({},i.options,{suffix:"",autoFormat:!1,selected:t})),["list","enum"].indexOf(i.type)!=-1&&(this.last.search="",this.last.item="",r.val("")),setTimeout(function(){r.focus()},1)),this.last.search!=""?this.search(i.field,this.last.search):(this.last.field=i.field,this.last.caption=i.caption),r.attr("placeholder",i.caption),$().w2overlay()},searchReset:function(n){var t=this.trigger({phase:"before",type:"search",target:this.name,searchData:[]});t.isCancelled!==!0&&(this.searchData=[],this.last.search="",this.last.logic="OR",this.last.multi=!1,this.last.xhr_offset=0,this.last.scrollTop=0,this.last.scrollLeft=0,this.last.selection.indexes=[],this.last.selection.columns={},this.searchClose(),$("#grid_"+this.name+"_search_all").val(""),n||this.reload(),this.trigger($.extend(t,{phase:"after"})))},clear:function(n){this.records=[],this.summary=[],this.last.scrollTop=0,this.last.scrollLeft=0,this.last.range_start=null,this.last.range_end=null,n||this.refresh()},reset:function(n){this.offset=0,this.total=0,this.last.scrollTop=0,this.last.scrollLeft=0,this.last.selection.indexes=[],this.last.selection.columns={},this.last.range_start=null,this.last.range_end=null,this.last.xhr_offset=0,this.searchReset(n),this.last.sortData!=null&&(this.sortData=this.last.sortData),this.set({expanded:!1},!0),n||this.refresh()},skip:function(n){var t=typeof this.url!="object"?this.url:this.url.get;t?(this.offset=parseInt(n),this.offset>this.total&&(this.offset=this.total-this.limit),(this.offset<0||!w2utils.isInt(this.offset))&&(this.offset=0),this.records=[],this.last.xhr_offset=0,this.last.pull_more=!0,this.last.scrollTop=0,this.last.scrollLeft=0,$("#grid_"+this.name+"_records").prop("scrollTop",0),this.reload()):console.log("ERROR: grid.skip() can only be called when you have remote data source.")},load:function(n,t){if(typeof n=="undefined"){console.log('ERROR: You need to provide url argument when calling .load() method of "'+this.name+'" object.');return}this.request("get-records",{},n,t)},reload:function(n){var t=typeof this.url!="object"?this.url:this.url.get;t?(this.clear(!0),this.request("get-records",{},null,n)):(this.last.scrollTop=0,this.last.scrollLeft=0,this.last.range_start=null,this.last.range_end=null,this.localSearch(),this.refresh(),typeof n=="function"&&n({status:"success"}))},request:function(n,t,i,r){var u,e,o,c,i,s,h,f;if(typeof t=="undefined"&&(t={}),(typeof i=="undefined"||i==""||i==null)&&(i=this.url),i!=""&&i!=null){if(u={},w2utils.isInt(this.offset)||(this.offset=0),w2utils.isInt(this.last.xhr_offset)||(this.last.xhr_offset=0),u.cmd=n,u.selected=this.getSelection(),u.limit=this.limit,u.offset=parseInt(this.offset)+this.last.xhr_offset,u.search=this.searchData,u.searchLogic=this.last.logic,u.sort=this.sortData,this.searchData.length==0&&(delete u.search,delete u.searchLogic),this.sortData.length==0&&delete u.sort,$.extend(u,this.postData),$.extend(u,t),n=="get-records"){if(e=this.trigger({phase:"before",type:"request",target:this.name,url:i,postData:u}),e.isCancelled===!0){typeof r=="function"&&r({status:"error",message:"Request aborted."});return}}else e={url:i,postData:u};if(o=this,this.last.xhr_offset==0?this.lock(this.msgRefresh,!0):(c=$("#grid_"+this.name+"_rec_more"),this.autoLoad===!0?c.show().find("td").html('<div><div style="width: 20px; height: 20px;" class="w2ui-spinner"><\/div><\/div>'):c.find("td").html("<div>"+w2utils.lang("Load")+" "+o.limit+" "+w2utils.lang("More")+"...<\/div>")),this.last.xhr)try{this.last.xhr.abort()}catch(l){}if(i=typeof e.url!="object"?e.url:e.url.get,u.cmd=="save-records"&&typeof e.url=="object"&&(i=e.url.save),u.cmd=="delete-records"&&typeof e.url=="object"&&(i=e.url.remove),!$.isEmptyObject(o.routeData)&&(s=w2utils.parseRoute(i),s.keys.length>0))for(h=0;h<s.keys.length;h++)o.routeData[s.keys[h].name]!=null&&(i=i.replace(new RegExp(":"+s.keys[h].name,"g"),o.routeData[s.keys[h].name]));f={type:"POST",url:i,data:e.postData,dataType:"text"},w2utils.settings.dataType=="HTTP"&&(f.data=typeof f.data=="object"?String($.param(f.data,!1)).replace(/%5B/g,"[").replace(/%5D/g,"]"):f.data),w2utils.settings.dataType=="RESTFULL"&&(f.type="GET",u.cmd=="save-records"&&(f.type="PUT"),u.cmd=="delete-records"&&(f.type="DELETE"),f.data=typeof f.data=="object"?String($.param(f.data,!1)).replace(/%5B/g,"[").replace(/%5D/g,"]"):f.data),w2utils.settings.dataType=="JSON"&&(f.type="POST",f.data=JSON.stringify(f.data),f.contentType="application/json"),this.method&&(f.type=this.method),this.last.xhr_cmd=u.cmd,this.last.xhr_start=(new Date).getTime(),this.last.xhr=$.ajax(f).done(function(t,i){o.requestComplete(i,n,r)}).fail(function(t,i,u){var s={status:i,error:u,rawResponseText:t.responseText},e=o.trigger({phase:"before",type:"error",error:s,xhr:t}),f;if(e.isCancelled!==!0){if(i!="abort"){try{f=$.parseJSON(t.responseText)}catch(h){}console.log("ERROR: Server communication failed.","\n   EXPECTED:",{status:"success",total:5,records:[{recid:1,field:"value"}]},"\n         OR:",{status:"error",message:"error message"},"\n   RECEIVED:",typeof f=="object"?f:t.responseText)}o.requestComplete("error",n,r),o.trigger($.extend(e,{phase:"after"}))}}),n=="get-records"&&this.trigger($.extend(e,{phase:"after"}))}},requestComplete:function(status,cmd,callBack){var obj=this,event_name,eventData,data,responseText,records,r,url;if(this.unlock(),setTimeout(function(){obj.status(w2utils.lang("Server Response")+" "+((new Date).getTime()-obj.last.xhr_start)/1e3+" "+w2utils.lang("sec"))},10),this.last.pull_more=!1,this.last.pull_refresh=!0,event_name="load",this.last.xhr_cmd=="save-records"&&(event_name="save"),this.last.xhr_cmd=="delete-records"&&(event_name="deleted"),eventData=this.trigger({phase:"before",target:this.name,type:event_name,xhr:this.last.xhr,status:status}),eventData.isCancelled===!0){typeof callBack=="function"&&callBack({status:"error",message:"Request aborted."});return}if(responseText=this.last.xhr.responseText,status!="error"){if(typeof responseText!="undefined"&&responseText!=""){if(typeof responseText=="object")data=responseText;else if(typeof obj.parser=="function")data=obj.parser(responseText),typeof data!="object"&&console.log("ERROR: Your parser did not return proper object");else try{eval("data = "+responseText)}catch(e){}if(obj.recid)for(r in data.records)data.records[r].recid=data.records[r][obj.recid];if(typeof data=="undefined"&&(data={status:"error",message:this.msgNotJSON,responseText:responseText}),data.status=="error")obj.error(data.message);else{if(cmd=="get-records")if(this.last.xhr_offset==0)this.records=[],this.summary=[],delete data.status,$.extend(!0,this,data);else{records=data.records,delete data.records,delete data.status,$.extend(!0,this,data);for(r in records)this.records.push(records[r])}if(cmd=="delete-records"){this.reset();return}}}}else data={status:"error",message:this.msgAJAXerror,responseText:responseText},obj.error(this.msgAJAXerror);url=typeof this.url!="object"?this.url:this.url.get,url||(this.localSort(),this.localSearch()),this.total=parseInt(this.total),this.trigger($.extend(eventData,{phase:"after"})),this.last.xhr_offset==0?this.refresh():this.scroll(),typeof callBack=="function"&&callBack(data)},error:function(n){var i=this,t=this.trigger({target:this.name,type:"error",message:n,xhr:this.last.xhr});if(t.isCancelled===!0){typeof callBack=="function"&&callBack({status:"error",message:"Request aborted."});return}w2alert(n,"Error"),this.trigger($.extend(t,{phase:"after"}))},getChanges:function(){var t=[],i,n;for(i in this.records)n=this.records[i],typeof n.changes!="undefined"&&t.push($.extend(!0,{recid:n.recid},n.changes));return t},mergeChanges:function(){var changes=this.getChanges(),c,record,s;for(c in changes){record=this.get(changes[c].recid);for(s in changes[c])if(s!="recid"){try{eval("record."+s+" = changes[c][s]")}catch(e){}delete record.changes}}this.refresh()},save:function(){var t=this,r=this.getChanges(),n=this.trigger({phase:"before",target:this.name,type:"submit",changes:r}),i;n.isCancelled!==!0&&(i=typeof this.url!="object"?this.url:this.url.save,i?this.request("save-records",{changes:n.changes},null,function(i){i.status!=="error"&&t.mergeChanges(),t.trigger($.extend(n,{phase:"after"}))}):(this.mergeChanges(),this.trigger($.extend(n,{phase:"after"}))))},editField:function(n,t,i,r){var u=this,o=u.get(n,!0),h=u.records[o],e=u.columns[t],f=e?e.editable:null,a,w,c,s,v,p,y,l;if(h&&e&&f&&h.editable!==!1){if(["enum","file"].indexOf(f.type)!=-1){console.log('ERROR: input types "enum" and "file" are not supported in inline editing.');return}if((a=u.trigger({phase:"before",type:"editField",target:u.name,recid:n,column:t,value:i,index:o,originalEvent:r}),a.isCancelled!==!0)&&(i=a.value,this.selectNone(),this.select({recid:n,column:t}),this.last.edit_col=t,["checkbox","check"].indexOf(f.type)==-1)){if(w=$("#grid_"+u.name+"_rec_"+w2utils.escapeId(n)),c=w.find("[col="+t+"] > div"),typeof f.inTag=="undefined"&&(f.inTag=""),typeof f.outTag=="undefined"&&(f.outTag=""),typeof f.style=="undefined"&&(f.style=""),typeof f.items=="undefined"&&(f.items=[]),s=h.changes&&typeof h.changes[e.field]!="undefined"?w2utils.stripTags(h.changes[e.field]):w2utils.stripTags(h[e.field]),(s==null||typeof s=="undefined")&&(s=""),typeof i!="undefined"&&i!=null&&(s=i),v=typeof e.style!="undefined"?e.style+";":"",typeof e.render=="string"&&["number","int","float","money","percent"].indexOf(e.render.split(":")[0])!=-1&&(v+="text-align: right;"),f.type=="select"){p="";for(y in f.items)p+='<option value="'+f.items[y].id+'" '+(f.items[y].id==s?"selected":"")+">"+f.items[y].text+"<\/option>";c.addClass("w2ui-editable").html('<select id="grid_'+u.name+"_edit_"+n+"_"+t+'" column="'+t+'"     style="width: 100%; '+v+f.style+'" field="'+e.field+'" recid="'+n+'"     '+f.inTag+">"+p+"<\/select>"+f.outTag);c.find("select").focus().on("change",function(){delete u.last.move}).on("blur",function(n){u.editChange.call(u,this,o,t,n)})}else c.addClass("w2ui-editable").html('<input id="grid_'+u.name+"_edit_"+n+"_"+t+'"     type="text" style="outline: none; '+v+f.style+'" field="'+e.field+'" recid="'+n+'"     column="'+t+'" '+f.inTag+">"+f.outTag),i==null&&c.find("input").val(s!="object"?s:""),l=c.find("input").get(0),$(l).w2field(f.type,$.extend(f,{selected:s})),setTimeout(function(){var n=l;f.type=="list"&&(n=$($(l).data("w2field").helpers.focus).find("input"),s!="object"&&s!=""&&n.val(s).css({opacity:1}).prev().css({opacity:1}));$(n).on("blur",function(n){u.editChange.call(u,l,o,t,n)})},10),i!=null&&$(l).val(s!="object"?s:"");setTimeout(function(){c.find("input, select").on("click",function(n){n.stopPropagation()}).on("keydown",function(i){var l=!1,c,s,v,f,r,a;switch(i.keyCode){case 9:if(l=!0,c=n,s=i.shiftKey?u.prevCell(t,!0):u.nextCell(t,!0),s==null&&(f=i.shiftKey?u.prevRow(o):u.nextRow(o),f!=null&&f!=o)){c=u.records[f].recid;for(v in u.columns)if(f=u.columns[v].editable,typeof f!="undefined"&&["checkbox","check"].indexOf(f.type)==-1&&(s=parseInt(v),!i.shiftKey))break}c===!1&&(c=n),s==null&&(s=t),this.blur(),setTimeout(function(){u.selectType!="row"?(u.selectNone(),u.select({recid:c,column:s})):u.editField(c,s,null,i)},1);break;case 13:this.blur(),r=i.shiftKey?u.prevRow(o):u.nextRow(o),r!=null&&r!=o&&setTimeout(function(){u.selectType!="row"?(u.selectNone(),u.select({recid:u.records[r].recid,column:t})):u.editField(u.records[r].recid,t,null,i)},100);break;case 38:if(!i.shiftKey)break;l=!0,r=u.prevRow(o),r!=o&&(this.blur(),setTimeout(function(){u.selectType!="row"?(u.selectNone(),u.select({recid:u.records[r].recid,column:t})):u.editField(u.records[r].recid,t,null,i)},1));break;case 40:if(!i.shiftKey)break;l=!0,r=u.nextRow(o),r!=null&&r!=o&&(this.blur(),setTimeout(function(){u.selectType!="row"?(u.selectNone(),u.select({recid:u.records[r].recid,column:t})):u.editField(u.records[r].recid,t,null,i)},1));break;case 27:a=u.parseField(h,e.field),h.changes&&typeof h.changes[e.field]!="undefined"&&(a=h.changes[e.field]),this.value=typeof a!="undefined"?a:"",this.blur(),setTimeout(function(){u.select({recid:n,column:t})},1)}l&&i.preventDefault&&i.preventDefault()});var r=c.find("input").focus();i!=null?r[0].setSelectionRange(r.val().length,r.val().length):r.select()},1),u.trigger($.extend(a,{phase:"after"}))}}},editChange:function(n,t,i){var s=t<0,u,c;t=t<0?-t-1:t;var a=s?this.summary:this.records,r=a[t],l=$("#grid_"+this.name+"_rec_"+w2utils.escapeId(r.recid)),e=this.columns[i],f=n.value,o=this.parseField(r,e.field),h=$(n).data("w2field");for(h&&(f=h.clean(f),h.type=="list"&&f!=""&&(f=$(n).data("selected"))),n.type=="checkbox"&&(f=n.checked),u={phase:"before",type:"change",target:this.name,input_id:n.id,recid:r.recid,index:t,column:i,value_new:f,value_previous:r.changes&&r.changes.hasOwnProperty(e.field)?r.changes[e.field]:o,value_original:o};;){if(f=u.value_new,(typeof o=="undefined"||o===null?"":String(o))!==String(f)){if(u=this.trigger($.extend(u,{type:"change",phase:"before"})),u.isCancelled!==!0){if(f!==u.value_new)continue;r.changes=r.changes||{},r.changes[e.field]=u.value_new,this.trigger($.extend(u,{phase:"after"}))}}else if(u=this.trigger($.extend(u,{type:"restore",phase:"before"})),u.isCancelled!==!0){if(f!==u.value_new)continue;r.changes&&delete r.changes[e.field],$.isEmptyObject(r.changes)&&delete r.changes,this.trigger($.extend(u,{phase:"after"}))}break}c=this.getCellHTML(t,i,s),s||(r.changes&&typeof r.changes[e.field]!="undefined"?$(l).find("[col="+i+"]").addClass("w2ui-changed").html(c):$(l).find("[col="+i+"]").removeClass("w2ui-changed").html(c))},"delete":function(n){var e=this,u=this.trigger({phase:"before",target:this.name,type:"delete",force:n}),t,o,f,r,i;if(u.isCancelled!==!0&&(n=u.force,t=this.getSelection(),t.length!=0)){if(this.msgDelete!=""&&!n){w2confirm({title:w2utils.lang("Delete Confirmation"),msg:e.msgDelete,btn_yes:{"class":"btn-red"},callBack:function(n){n=="Yes"&&w2ui[e.name].delete(!0)}});return}if(o=typeof this.url!="object"?this.url:this.url.remove,o)this.request("delete-records");else if(this.selectNone(),typeof t[0]!="object")this.remove.apply(this,t);else{for(f in t)r=this.columns[t[f].column].field,i=this.get(t[f].recid,!0),i!=null&&r!="recid"&&(this.records[i][r]="",this.records[i].changes&&delete this.records[i].changes[r]);this.refresh()}this.trigger($.extend(u,{phase:"after"}))}},click:function(n,t){var d=(new Date).getTime(),i=null,b,h,g,f,r,a,v,y,e,o,p,u,nt,s,tt,w,l;if(this.last.cancelClick!=!0&&(!t||!t.altKey)){if(typeof n=="object"&&(i=n.column,n=n.recid),typeof t=="undefined"&&(t={}),d-parseInt(this.last.click_time)<350&&t.type=="click"){this.dblClick(n,t);return}if(this.last.click_time=d,i==null&&t.target&&(u=t.target,u.tagName!="TD"&&(u=$(u).parents("td")[0]),typeof $(u).attr("col")!="undefined"&&(i=parseInt($(u).attr("col")))),b=this.trigger({phase:"before",target:this.name,type:"click",recid:n,column:i,originalEvent:t}),b.isCancelled!==!0){h=$("#grid_"+this.name+"_rec_"+w2utils.escapeId(n)).parents("tr"),h.length>0&&String(h.attr("id")).indexOf("expanded_row")!=-1&&(g=h.parents(".w2ui-grid").attr("name"),w2ui[g].selectNone(),h.parents(".w2ui-grid").find(".w2ui-expanded-row .w2ui-grid").each(function(n,t){var i=$(t).attr("name");w2ui[i]&&w2ui[i].selectNone()})),$(this.box).find(".w2ui-expanded-row .w2ui-grid").each(function(n,t){var i=$(t).attr("name");w2ui[i]&&w2ui[i].selectNone()}),f=this,r=this.getSelection(),$("#grid_"+this.name+"_check_all").prop("checked",!1);var c=this.get(n,!0),it=this.records[c],k=[];if(f.last.sel_ind=c,f.last.sel_col=i,f.last.sel_recid=n,f.last.sel_type="click",t.shiftKey&&r.length>0&&f.multiSelect){if(r[0].recid)for(e=this.get(r[0].recid,!0),o=this.get(n,!0),i>r[0].column?(a=r[0].column,v=i):(a=i,v=r[0].column),y=a;y<=v;y++)k.push(y);else e=this.get(r[0],!0),o=this.get(n,!0);for(p=[],e>o&&(u=e,e=o,o=u),nt=typeof this.url!="object"?this.url:this.url.get,s=e;s<=o;s++)if(!(this.searchData.length>0)||nt||$.inArray(s,this.last.searchIds)!=-1)if(this.selectType=="row")p.push(this.records[s].recid);else for(tt in k)p.push({recid:this.records[s].recid,column:k[tt]});this.select.apply(this,p)}else w=this.last.selection,l=w.indexes.indexOf(c)!=-1?!0:!1,(t.ctrlKey||t.shiftKey||t.metaKey)&&this.multiSelect||this.showSelectColumn?(this.selectType!="row"&&$.inArray(i,w.columns[c])==-1&&(l=!1),l===!0?this.unselect({recid:n,column:i}):this.select({recid:n,column:i})):(this.selectType!="row"&&$.inArray(i,w.columns[c])==-1&&(l=!1),r.length>300?this.selectNone():this.unselect.apply(this,r),l===!0?this.unselect({recid:n,column:i}):this.select({recid:n,column:i}));this.status(),f.initResize(),this.trigger($.extend(b,{phase:"after"}))}}},columnClick:function(n,t){var i=this.trigger({phase:"before",type:"columnClick",target:this.name,field:n,originalEvent:t}),r;i.isCancelled!==!0&&(r=this.getColumn(n),r.sortable&&this.sort(n,null,t&&(t.ctrlKey||t.metaKey)?!0:!1),this.trigger($.extend(i,{phase:"after"})))},keydown:function(n){function lt(){var n=Math.floor((et[0].scrollTop+et.height()/2.1)/t.recordHeight);t.records[n]||(n=0),t.select({recid:t.records[n].recid,column:0})}function ut(){if(t.last.sel_type!="click")return!1;if(t.selectType!="row"){if(t.last.sel_type="key",i.length>1){for(var n in i)if(i[n].recid==t.last.sel_recid&&i[n].column==t.last.sel_col){i.splice(n,1);break}return t.unselect.apply(t,i),!0}return!1}return(t.last.sel_type="key",i.length>1)?(i.splice(i.indexOf(t.records[t.last.sel_ind].recid),1),t.unselect.apply(t,i),!0):!1}var t=this,ft,nt,l,w,g,a,v,s,c,f,e,ht,o,r;if(t.keyboard===!0&&(ft=t.trigger({phase:"before",type:"keydown",target:t.name,originalEvent:n}),ft.isCancelled!==!0)){var d=!1,et=$("#grid_"+t.name+"_records"),i=t.getSelection();i.length==0&&(d=!0);var f=i[0]||null,u=[],ot=i[i.length-1];if(typeof f=="object"&&f!=null){for(f=i[0].recid,u=[],nt=0;;){if(!i[nt]||i[nt].recid!=f)break;u.push(i[nt].column),nt++}ot=i[i.length-1].recid}var p=t.get(f,!0),b=t.get(ot,!0),st=t.get(f),tt=$("#grid_"+t.name+"_rec_"+(p!==null?w2utils.escapeId(t.records[p].recid):"none")),h=!1,k=n.keyCode,y=n.shiftKey;k==9&&(k=n.shiftKey?37:39,y=!1,h=!0);switch(k){case 8:case 46:this.show.toolbarDelete&&t["delete"](),h=!0,n.stopPropagation();break;case 27:t.selectNone(),i.length>0&&typeof i[0]=="object"&&t.select({recid:i[0].recid,column:i[0].column}),h=!0;break;case 65:if(!n.metaKey&&!n.ctrlKey)break;t.selectAll(),h=!0;break;case 70:if(!n.metaKey&&!n.ctrlKey)break;$("#grid_"+t.name+"_search_all").focus(),h=!0;break;case 13:if(this.selectType=="row"&&t.show.expandColumn===!0){if(tt.length<=0)break;t.toggle(f,n),h=!0}else{for(v in this.columns)if(this.columns[v].editable){u.push(parseInt(v));break}this.selectType=="row"&&this.last.edit_col&&(u=[this.last.edit_col]),u.length>0&&(t.editField(f,u[0],null,n),h=!0)}break;case 37:if(d)break;if(c=$("#grid_"+this.name+"_rec_"+w2utils.escapeId(t.records[p].recid)).parents("tr"),c.length>0&&String(c.attr("id")).indexOf("expanded_row")!=-1){f=c.prev().attr("recid"),e=c.parents(".w2ui-grid").attr("name"),t.selectNone(),w2utils.keyboard.active(e),w2ui[e].set(f,{expanded:!1}),w2ui[e].collapse(f),w2ui[e].click(f),h=!0;break}if(this.selectType=="row"){if(tt.length<=0||st.expanded!==!0)break;t.set(f,{expanded:!1},!0),t.collapse(f,n)}else if(l=t.prevCell(u[0]),l!=null)if(y&&t.multiSelect){if(ut())return;var r=[],it=[],rt=[];if(u.indexOf(this.last.sel_col)==0&&u.length>1)for(o in i)r.indexOf(i[o].recid)==-1&&r.push(i[o].recid),rt.push({recid:i[o].recid,column:u[u.length-1]});else for(o in i)r.indexOf(i[o].recid)==-1&&r.push(i[o].recid),it.push({recid:i[o].recid,column:l});t.unselect.apply(t,rt),t.select.apply(t,it)}else n.shiftKey=!1,t.click({recid:f,column:l},n);else if(!y)for(s=1;s<i.length;s++)t.unselect(i[s]);h=!0;break;case 39:if(d)break;if(this.selectType=="row"){if(tt.length<=0||st.expanded===!0||t.show.expandColumn!==!0)break;t.expand(f,n)}else if(a=t.nextCell(u[u.length-1]),a!==null)if(y&&k==39&&t.multiSelect){if(ut())return;var r=[],it=[],rt=[];if(u.indexOf(this.last.sel_col)==u.length-1&&u.length>1)for(o in i)r.indexOf(i[o].recid)==-1&&r.push(i[o].recid),rt.push({recid:i[o].recid,column:u[0]});else for(o in i)r.indexOf(i[o].recid)==-1&&r.push(i[o].recid),it.push({recid:i[o].recid,column:a});t.unselect.apply(t,rt),t.select.apply(t,it)}else t.click({recid:f,column:a},n);else if(!y)for(s=0;s<i.length-1;s++)t.unselect(i[s]);h=!0;break;case 38:if(d&&lt(),tt.length<=0)break;if(l=t.prevRow(p),l!=null){if(t.records[l].expanded&&(w=$("#grid_"+t.name+"_rec_"+w2utils.escapeId(t.records[l].recid)+"_expanded_row").find(".w2ui-grid"),w.length>0&&w2ui[w.attr("name")])){t.selectNone(),e=w.attr("name"),g=w2ui[e].records,w2utils.keyboard.active(e),w2ui[e].click(g[g.length-1].recid),h=!0;break}if(y&&t.multiSelect){if(ut())return;if(t.selectType=="row")t.last.sel_ind>l&&t.last.sel_ind!=b?t.unselect(t.records[b].recid):t.select(t.records[l].recid);else if(t.last.sel_ind>l&&t.last.sel_ind!=b){l=b,r=[];for(v in u)r.push({recid:t.records[l].recid,column:u[v]});t.unselect.apply(t,r)}else{r=[];for(v in u)r.push({recid:t.records[l].recid,column:u[v]});t.select.apply(t,r)}}else t.selectNone(),t.click({recid:t.records[l].recid,column:u[0]},n);t.scrollIntoView(l),n.preventDefault&&n.preventDefault()}else{if(!y)for(s=1;s<i.length;s++)t.unselect(i[s]);if(c=$("#grid_"+t.name+"_rec_"+w2utils.escapeId(t.records[p].recid)).parents("tr"),c.length>0&&String(c.attr("id")).indexOf("expanded_row")!=-1){f=c.prev().attr("recid"),e=c.parents(".w2ui-grid").attr("name"),t.selectNone(),w2utils.keyboard.active(e),w2ui[e].click(f),h=!0;break}}break;case 40:if(d&&lt(),tt.length<=0)break;if(t.records[b].expanded&&(w=$("#grid_"+this.name+"_rec_"+w2utils.escapeId(t.records[b].recid)+"_expanded_row").find(".w2ui-grid"),w.length>0&&w2ui[w.attr("name")])){t.selectNone(),e=w.attr("name"),g=w2ui[e].records,w2utils.keyboard.active(e),w2ui[e].click(g[0].recid),h=!0;break}if(a=t.nextRow(b),a!=null){if(y&&t.multiSelect){if(ut())return;if(t.selectType=="row")this.last.sel_ind<a&&this.last.sel_ind!=p?t.unselect(t.records[p].recid):t.select(t.records[a].recid);else if(this.last.sel_ind<a&&this.last.sel_ind!=p){a=p,r=[];for(v in u)r.push({recid:t.records[a].recid,column:u[v]});t.unselect.apply(t,r)}else{r=[];for(v in u)r.push({recid:t.records[a].recid,column:u[v]});t.select.apply(t,r)}}else t.selectNone(),t.click({recid:t.records[a].recid,column:u[0]},n);t.scrollIntoView(a),h=!0}else{if(!y)for(s=0;s<i.length-1;s++)t.unselect(i[s]);if(c=$("#grid_"+this.name+"_rec_"+w2utils.escapeId(t.records[b].recid)).parents("tr"),c.length>0&&String(c.attr("id")).indexOf("expanded_row")!=-1){f=c.next().attr("recid"),e=c.parents(".w2ui-grid").attr("name"),t.selectNone(),w2utils.keyboard.active(e),w2ui[e].click(f),h=!0;break}}break;case 17:case 91:if(d)break;ht=t.copy(),$("body").append('<textarea id="_tmp_copy_data"    onpaste="var obj = this; setTimeout(function () { w2ui[\''+t.name+"'].paste(obj.value); }, 1);\"    onkeydown=\"w2ui['"+t.name+'\'].keydown(event)"   style="position: absolute; top: -100px; height: 1px; width: 1px">'+ht+"<\/textarea>"),$("#_tmp_copy_data").focus().select();$(document).on("keyup",ct);function ct(){$("#_tmp_copy_data").remove(),$(document).off("keyup",ct)}break;case 88:if(d)break;(n.ctrlKey||n.metaKey)&&setTimeout(function(){t["delete"](!0)},100)}for(r=[187,189,32],o=48;o<=90;o++)r.push(o);r.indexOf(k)==-1||n.ctrlKey||n.metaKey||h||(u.length==0&&u.push(0),r=String.fromCharCode(k),k==187&&(r="="),k==189&&(r="-"),y||(r=r.toLowerCase()),t.editField(f,u[0],r,n),h=!0),h&&n.preventDefault&&n.preventDefault(),t.trigger($.extend(ft,{phase:"after"}))}},scrollIntoView:function(n){var u=this.records.length,f,t,i,r,e;if(this.searchData.length==0||this.url||(u=this.last.searchIds.length),typeof n=="undefined"){if(f=this.getSelection(),f.length==0)return;n=this.get(f[0],!0)}(t=$("#grid_"+this.name+"_records"),u!=0)&&((i=this.last.searchIds.length,t.height()>this.recordHeight*(i>0?i:u))||(i>0&&(n=this.last.searchIds.indexOf(n)),r=Math.floor(t[0].scrollTop/this.recordHeight),e=r+Math.floor(t.height()/this.recordHeight),n==r&&t.animate({scrollTop:t.scrollTop()-t.height()/1.3},250,"linear"),n==e&&t.animate({scrollTop:t.scrollTop()+t.height()/1.3},250,"linear"),(n<r||n>e)&&t.animate({scrollTop:(n-1)*this.recordHeight})))},dblClick:function(n,t){var i=null,r,u,f;(typeof n=="object"&&(i=n.column,n=n.recid),typeof t=="undefined"&&(t={}),i==null&&t.target&&(r=t.target,r.tagName!="TD"&&(r=$(r).parents("td")[0]),i=parseInt($(r).attr("col"))),u=this.trigger({phase:"before",target:this.name,type:"dblClick",recid:n,column:i,originalEvent:t}),u.isCancelled!==!0)&&(this.selectNone(),f=this.columns[i],f&&$.isPlainObject(f.editable)?this.editField(n,i,null,t):this.select({recid:n,column:i}),this.trigger($.extend(u,{phase:"after"})))},contextMenu:function(n,t){var i=this;i.last.userSelect!="text"&&(typeof t=="undefined"&&(t={offsetX:0,offsetY:0,target:$("#grid_"+i.name+"_rec_"+n)[0]}),typeof t.offsetX=="undefined"&&(t.offsetX=t.layerX-t.target.offsetLeft,t.offsetY=t.layerY-t.target.offsetTop),w2utils.isFloat(n)&&(n=parseFloat(n)),this.getSelection().indexOf(n)==-1&&i.click(n),setTimeout(function(){var r=i.trigger({phase:"before",type:"contextMenu",target:i.name,originalEvent:t,recid:n});r.isCancelled!==!0&&(i.menu.length>0&&$(i.box).find(t.target).w2menu(i.menu,{left:t.offsetX,onSelect:function(t){i.menuClick(n,parseInt(t.index),t.originalEvent)}}),i.trigger($.extend(r,{phase:"after"})))},150),t.preventDefault&&t.preventDefault())},menuClick:function(n,t,i){var r=this,u=r.trigger({phase:"before",type:"menuClick",target:r.name,originalEvent:i,recid:n,menuIndex:t,menuItem:r.menu[t]});u.isCancelled!==!0&&r.trigger($.extend(u,{phase:"after"}))},toggle:function(n){var t=this.get(n);return t.expanded===!0?this.collapse(n):this.expand(n)},expand:function(n){function o(){var r=$("#grid_"+i.name+"_rec_"+t+"_expanded"),u=$("#grid_"+i.name+"_rec_"+t+"_expanded_row .w2ui-expanded1 > div");r.height()<5||(r.css("opacity",1),u.show().css("opacity",1),$("#grid_"+i.name+"_cell_"+i.get(n,!0)+"_expand div").html("-"))}var u=this.get(n),i=this,t=w2utils.escapeId(n),f,e,r;if($("#grid_"+this.name+"_rec_"+t+"_expanded_row").length>0||u.expanded=="none")return!1;if(f=1+(this.show.selectColumn?1:0),e="",$("#grid_"+this.name+"_rec_"+t).after('<tr id="grid_'+this.name+"_rec_"+t+'_expanded_row" class="w2ui-expanded-row '+e+'">'+(this.show.lineNumbers?'<td class="w2ui-col-number"><\/td>':"")+(this.show.expandColumn?'    <td class="w2ui-grid-data w2ui-expanded1" colspan="'+f+'"><div style="display: none"><\/div><\/td>':"")+'    <td colspan="100" class="w2ui-expanded2">        <div id="grid_'+this.name+"_rec_"+t+'_expanded" style="opacity: 0"><\/div>    <\/td><\/tr>'),r=this.trigger({phase:"before",type:"expand",target:this.name,recid:n,box_id:"grid_"+this.name+"_rec_"+t+"_expanded",ready:o}),r.isCancelled===!0){$("#grid_"+this.name+"_rec_"+t+"_expanded_row").remove();return}return $("#grid_"+this.name+"_rec_"+t).attr("expanded","yes").addClass("w2ui-expanded"),$("#grid_"+this.name+"_rec_"+t+"_expanded_row").show(),$("#grid_"+this.name+"_cell_"+this.get(n,!0)+"_expand div").html('<div class="w2ui-spinner" style="width: 16px; height: 16px; margin: -2px 2px;"><\/div>'),u.expanded=!0,setTimeout(o,this.showExpandColumn?300:1),this.trigger($.extend(r,{phase:"after"})),this.resizeRecords(),!0},collapse:function(n,t){var f=this.get(n),r=this,i=w2utils.escapeId(n),u;return $("#grid_"+this.name+"_rec_"+i+"_expanded_row").length==0?!1:(u=this.trigger({phase:"before",type:"collapse",target:this.name,recid:n,box_id:"grid_"+this.name+"_rec_"+i+"_expanded"}),u.isCancelled===!0)?void 0:($("#grid_"+this.name+"_rec_"+i).removeAttr("expanded").removeClass("w2ui-expanded"),$("#grid_"+this.name+"_rec_"+i+"_expanded").css("opacity",0),$("#grid_"+this.name+"_cell_"+this.get(n,!0)+"_expand div").html("+"),t)?($("#grid_"+r.name+"_rec_"+i+"_expanded_row").remove(),delete f.expanded,!0):(setTimeout(function(){$("#grid_"+r.name+"_rec_"+i+"_expanded").height("0px"),setTimeout(function(){$("#grid_"+r.name+"_rec_"+i+"_expanded_row").remove(),delete f.expanded,r.trigger($.extend(u,{phase:"after"})),r.resizeRecords()},300)},200),!0)},sort:function(n,t,i){var u=this.trigger({phase:"before",type:"sort",target:this.name,field:n,direction:t,multiField:i}),r,f,e;if(u.isCancelled!==!0){if(typeof n!="undefined"){r=this.sortData.length;for(f in this.sortData)if(this.sortData[f].field==n){r=f;break}if(typeof t=="undefined"||t==null)if(typeof this.sortData[r]=="undefined")t="asc";else switch(String(this.sortData[r].direction)){case"asc":t="desc";break;case"desc":t="asc";break;default:t="asc"}this.multiSort===!1&&(this.sortData=[],r=0),i!=!0&&(this.sortData=[],r=0),typeof this.sortData[r]=="undefined"&&(this.sortData[r]={}),this.sortData[r].field=n,this.sortData[r].direction=t}else this.sortData=[];this.selectNone(),e=typeof this.url!="object"?this.url:this.url.get,e?(this.trigger($.extend(u,{phase:"after"})),this.last.xhr_offset=0,this.reload()):(this.localSort(),this.searchData.length>0&&this.localSearch(!0),this.trigger($.extend(u,{phase:"after"})),this.refresh())}},copy:function(){var t=this.getSelection(),n,c,u,e,r,i,o;if(t.length==0)return"";if(n="",typeof t[0]=="object"){var s=t[0].column,h=t[0].column,f=[];for(u in t)t[u].column<s&&(s=t[u].column),t[u].column>h&&(h=t[u].column),f.indexOf(t[u].index)==-1&&f.push(t[u].index);f.sort();for(c in f){for(e=f[c],r=s;r<=h;r++)(i=this.columns[r],i.hidden!==!0)&&(n+=w2utils.stripTags(this.getCellHTML(e,r))+"\t");n=n.substr(0,n.length-1)+"\n"}}else{for(r in this.columns)(i=this.columns[r],i.hidden!==!0)&&(n+='"'+w2utils.stripTags(i.caption?i.caption:i.field)+'"\t');n=n.substr(0,n.length-1)+"\n";for(u in t){e=this.get(t[u],!0);for(r in this.columns)(i=this.columns[r],i.hidden!==!0)&&(n+='"'+w2utils.stripTags(this.getCellHTML(e,r))+'"\t');n=n.substr(0,n.length-1)+"\n"}}return(n=n.substr(0,n.length-1),o=this.trigger({phase:"before",type:"copy",target:this.name,text:n}),o.isCancelled===!0)?"":(n=o.text,this.trigger($.extend(o,{phase:"after"})),n)},paste:function(n){var f=this.getSelection(),e=this.get(f[0].recid,!0),t=f[0].column,i=this.trigger({phase:"before",type:"paste",target:this.name,text:n,index:e,column:t}),o,n,h,l,a,v;if(i.isCancelled!==!0){if(n=i.text,this.selectType=="row"||f.length==0){console.log("ERROR: You can paste only if grid.selectType = 'cell' and when at least one cell selected."),this.trigger($.extend(i,{phase:"after"}));return}o=[],n=n.split("\n");for(h in n){var c=n[h].split("\t"),r=0,u=this.records[e],s=[];for(l in c)this.columns[t+r]&&(a=this.columns[t+r].field,u.changes=u.changes||{},u.changes[a]=c[l],s.push(t+r),r++);for(v in s)o.push({recid:u.recid,column:s[v]});e++}this.selectNone(),this.select.apply(this,o),this.refresh(),this.trigger($.extend(i,{phase:"after"}))}},resize:function(){var t=this,i=(new Date).getTime(),n;if(this.box&&$(this.box).attr("name")==this.name)return($(this.box).find("> div").css("width",$(this.box).width()).css("height",$(this.box).height()),n=this.trigger({phase:"before",type:"resize",target:this.name}),n.isCancelled===!0)?void 0:(t.resizeBoxes(),t.resizeRecords(),this.trigger($.extend(n,{phase:"after"})),(new Date).getTime()-i)},refreshCell:function(n,t){var u=this.get(n,!0),i=this.getColumn(t,!0),f=this.records[u],e=this.columns[i],r=$("#grid_"+this.name+"_rec_"+n+" [col="+i+"]");r.html(this.getCellHTML(u,i)),f.changes&&typeof f.changes[e.field]!="undefined"?r.addClass("w2ui-changed"):r.removeClass("w2ui-changed")},refreshRow:function(n){var t=$("#grid_"+this.name+"_rec_"+w2utils.escapeId(n)),r;if(t.length!=0){var i=this.get(n,!0),u=t.attr("line"),f=typeof this.url!="object"?this.url:this.url.get;if(this.searchData.length>0&&!f)for(r in this.last.searchIds)this.last.searchIds[r]==i&&(i=r);$(t).replaceWith(this.getRecordHTML(i,u))}},refresh:function(){var t=this,l=(new Date).getTime(),a=typeof this.url!="object"?this.url:this.url.get,f,r,e,u,n,i,o,s,h,c;if((this.total<=0&&!a&&this.searchData.length==0&&(this.total=this.records.length),this.toolbar.disable("w2ui-edit","w2ui-delete"),this.box)&&(f=this.trigger({phase:"before",target:this.name,type:"refresh"}),f.isCancelled!==!0)){if(this.show.header?$("#grid_"+this.name+"_header").html(this.header+"&nbsp;").show():$("#grid_"+this.name+"_header").hide(),this.show.toolbar){if((!this.toolbar||!this.toolbar.get("w2ui-column-on-off")||!this.toolbar.get("w2ui-column-on-off").checked)&&($("#grid_"+this.name+"_toolbar").show(),typeof this.toolbar=="object")){n=this.toolbar.items;for(i in n)n[i].id!="w2ui-search"&&n[i].type!="break"&&this.toolbar.refresh(n[i].id)}}else $("#grid_"+this.name+"_toolbar").hide();this.searchClose(),r=$("#grid_"+t.name+"_search_all"),!this.multiSearch&&this.last.field=="all"&&this.searches.length>0&&(this.last.field=this.searches[0].field,this.last.caption=this.searches[0].caption);for(e in this.searches)this.searches[e].field==this.last.field&&(this.last.caption=this.searches[e].caption);if(this.last.multi?r.attr("placeholder","["+w2utils.lang("Multiple Fields")+"]"):r.attr("placeholder",this.last.caption),r.val()!=this.last.search&&(u=this.last.search,n=r.data("w2field"),n&&(u=n.format(u)),r.val(u)),n=this.find({summary:!0},!0),n.length>0){for(i in n)this.summary.push(this.records[n[i]]);for(i=n.length-1;i>=0;i--)this.records.splice(n[i],1);this.total=this.total-n.length}o="",o+='<div id="grid_'+this.name+'_records" class="w2ui-grid-records"    onscroll="var obj = w2ui[\''+this.name+"'];         obj.last.scrollTop = this.scrollTop;         obj.last.scrollLeft = this.scrollLeft;         $('#grid_"+this.name+"_columns')[0].scrollLeft = this.scrollLeft;        $('#grid_"+this.name+"_summary')[0].scrollLeft = this.scrollLeft;        obj.scroll(event);\">"+this.getRecordsHTML()+'<\/div><div id="grid_'+this.name+'_columns" class="w2ui-grid-columns">    <table>'+this.getColumnsHTML()+"<\/table><\/div>",$("#grid_"+this.name+"_body").html(o),this.summary.length>0?$("#grid_"+this.name+"_summary").html(this.getSummaryHTML()).show():$("#grid_"+this.name+"_summary").hide(),this.show.footer?$("#grid_"+this.name+"_footer").html(this.getFooterHTML()).show():$("#grid_"+this.name+"_footer").hide(),this.searchData.length>0?$("#grid_"+this.name+"_searchClear").show():$("#grid_"+this.name+"_searchClear").hide(),s=this.last.selection,s.indexes.length==this.records.length||this.searchData.length!==0&&s.indexes.length==this.last.searchIds.length?$("#grid_"+this.name+"_check_all").prop("checked",!0):$("#grid_"+this.name+"_check_all").prop("checked",!1),this.status(),h=t.find({expanded:!0},!0);for(c in h)t.records[h[c]].expanded=!1;return setTimeout(function(){var n=$.trim($("#grid_"+t.name+"_search_all").val());n!=""&&$(t.box).find(".w2ui-grid-data > div").w2marker(n)},50),this.trigger($.extend(f,{phase:"after"})),t.resize(),t.addRange("selection"),setTimeout(function(){t.resize(),t.scroll()},1),t.reorderColumns&&!t.last.columnDrag?t.last.columnDrag=t.initColumnDrag():!t.reorderColumns&&t.last.columnDrag&&t.last.columnDrag.remove(),(new Date).getTime()-l}},render:function(n){function o(n){if(n.which==1){if(t.last.userSelect=="text"){delete t.last.userSelect,$(t.box).find(".w2ui-grid-body").css("user-select","none").css("-webkit-user-select","none").css("-moz-user-select","none").css("-ms-user-select","none");$(this.box).on("selectstart",function(){return!1})}if(!$(n.target).parents().hasClass("w2ui-head")&&!$(n.target).hasClass("w2ui-head")&&(!t.last.move||t.last.move.type!="expand")){if(n.altKey)$(t.box).off("selectstart"),$(t.box).find(".w2ui-grid-body").css("user-select","text").css("-webkit-user-select","text").css("-moz-user-select","text").css("-ms-user-select","text"),t.selectNone(),t.last.move={type:"text-select"},t.last.userSelect="text";else{if(!t.multiSelect)return;t.last.move={x:n.screenX,y:n.screenY,divX:0,divY:0,recid:$(n.target).parents("tr").attr("recid"),column:n.target.tagName=="TD"?$(n.target).attr("col"):$(n.target).parents("td").attr("col"),type:"select",ghost:!1,start:!0}}$(document).on("mousemove",u);$(document).on("mouseup",f)}}}function u(n){var i=t.last.move,p,k,d,g,w,u,s,h,c,a,v,y,b,r,l,f,e,o;if(i&&i.type=="select"&&(i.divX=n.screenX-i.x,i.divY=n.screenY-i.y,!(Math.abs(i.divX)<=1)||!(Math.abs(i.divY)<=1))){if(t.last.cancelClick=!0,t.reorderRows==!0){i.ghost||(p=$("#grid_"+t.name+"_rec_"+i.recid),r=p.parents("table").find("tr:first-child").clone(),i.offsetY=n.offsetY,i.from=i.recid,i.pos=p.position(),i.ghost=$(p).clone(!0),i.ghost.removeAttr("id"),p.find("td:first-child").replaceWith('<td colspan="1000" style="height: '+t.recordHeight+'px; background-color: #ddd"><\/td>'),w=$(t.box).find(".w2ui-grid-records"),w.append('<table id="grid_'+t.name+'_ghost" style="position: absolute; z-index: 999999; opacity: 0.8; border-bottom: 2px dashed #aaa; border-top: 2px dashed #aaa; pointer-events: none;"><\/table>'),$("#grid_"+t.name+"_ghost").append(r).append(i.ghost)),s=$(n.target).parents("tr").attr("recid"),s!=i.from&&(k=$("#grid_"+t.name+"_rec_"+i.recid),d=$("#grid_"+t.name+"_rec_"+s),n.screenY-i.lastY<0?k.after(d):d.after(k),i.lastY=n.screenY,i.to=s),g=$("#grid_"+t.name+"_ghost"),w=$(t.box).find(".w2ui-grid-records"),g.css({top:i.pos.top+i.divY+w.scrollTop(),left:i.pos.left});return}if((i.start&&i.recid&&(t.selectNone(),i.start=!1),u=[],s=n.target.tagName=="TR"?$(n.target).attr("recid"):$(n.target).parents("tr").attr("recid"),typeof s!="undefined")&&(h=t.get(i.recid,!0),h!==null)&&(c=t.get(s,!0),c!==null)&&(a=parseInt(i.column),v=parseInt(n.target.tagName=="TD"?$(n.target).attr("col"):$(n.target).parents("td").attr("col")),h>c&&(r=h,h=c,c=r),r="ind1:"+h+",ind2;"+c+",col1:"+a+",col2:"+v,i.range!=r)){for(i.range=r,y=h;y<=c;y++)if(!(t.last.searchIds.length>0)||t.last.searchIds.indexOf(y)!=-1)if(t.selectType!="row")for(a>v&&(r=a,a=v,v=r),r=[],b=a;b<=v;b++)t.columns[b].hidden||u.push({recid:t.records[y].recid,column:parseInt(b)});else u.push(t.records[y].recid);if(t.selectType!="row"){f=t.getSelection(),r=[];for(e in u){l=!1;for(o in f)u[e].recid==f[o].recid&&u[e].column==f[o].column&&(l=!0);l||r.push({recid:u[e].recid,column:u[e].column})}t.select.apply(t,r),r=[];for(o in f){l=!1;for(e in u)u[e].recid==f[o].recid&&u[e].column==f[o].column&&(l=!0);l||r.push({recid:f[o].recid,column:f[o].column})}t.unselect.apply(t,r)}else if(t.multiSelect){f=t.getSelection();for(e in u)f.indexOf(u[e])==-1&&t.select(u[e]);for(o in f)u.indexOf(f[o])==-1&&t.unselect(f[o])}}}}function f(n){var i=t.last.move,r,o,e;(setTimeout(function(){delete t.last.cancelClick},1),$(n.target).parents().hasClass(".w2ui-head")||$(n.target).hasClass(".w2ui-head"))||(i&&i.type=="select"&&t.reorderRows==!0&&(r=t.get(i.from,!0),o=t.records[r],t.records.splice(r,1),e=t.get(i.to,!0),r>e?t.records.splice(e,0,o):t.records.splice(e+1,0,o),$("#grid_"+t.name+"_ghost").remove(),t.refresh()),delete t.last.move,$(document).off("mousemove",u),$(document).off("mouseup",f))}var t=this,e=(new Date).getTime(),i,r;if((typeof n!="undefined"&&n!=null&&($(this.box).find("#grid_"+this.name+"_body").length>0&&$(this.box).removeAttr("name").removeClass("w2ui-reset w2ui-grid").html(""),this.box=n),this.box)&&(this.last.sortData==null&&(this.last.sortData=this.sortData),i=this.trigger({phase:"before",target:this.name,type:"render",box:n}),i.isCancelled!==!0)){$(this.box).attr("name",this.name).addClass("w2ui-reset w2ui-grid").html('<div>    <div id="grid_'+this.name+'_header" class="w2ui-grid-header"><\/div>    <div id="grid_'+this.name+'_toolbar" class="w2ui-grid-toolbar"><\/div>    <div id="grid_'+this.name+'_body" class="w2ui-grid-body"><\/div>    <div id="grid_'+this.name+'_summary" class="w2ui-grid-body w2ui-grid-summary"><\/div>    <div id="grid_'+this.name+'_footer" class="w2ui-grid-footer"><\/div><\/div>'),this.selectType!="row"&&$(this.box).addClass("w2ui-ss"),$(this.box).length>0&&($(this.box)[0].style.cssText+=this.style),this.initToolbar(),this.toolbar!=null&&this.toolbar.render($("#grid_"+this.name+"_toolbar")[0]),this.last.field&&this.last.field!="all"&&(r=this.searchData,this.initAllField(this.last.field,r.length==1?r[0].value:null)),$("#grid_"+this.name+"_footer").html(this.getFooterHTML()),this.last.state||(this.last.state=this.stateSave(!0)),this.stateRestore(),this.url&&this.refresh(),this.reload();$(this.box).on("mousedown",o);$(this.box).on("selectstart",function(){return!1});if(this.trigger($.extend(i,{phase:"after"})),$(".w2ui-layout").length==0){this.tmp_resize=function(){w2ui[t.name].resize()};$(window).off("resize",this.tmp_resize).on("resize",this.tmp_resize)}return(new Date).getTime()-e}},destroy:function(){var n=this.trigger({phase:"before",target:this.name,type:"destroy"});n.isCancelled!==!0&&($(window).off("resize",this.tmp_resize),typeof this.toolbar=="object"&&this.toolbar.destroy&&this.toolbar.destroy(),$(this.box).find("#grid_"+this.name+"_body").length>0&&$(this.box).removeAttr("name").removeClass("w2ui-reset w2ui-grid").html(""),delete w2ui[this.name],this.trigger($.extend(n,{phase:"after"})))},initColumnOnOff:function(){var n,i,t,u,r,f;if(this.show.toolbarColumns){n=this,i='<div class="w2ui-col-on-off"><table><tr><td style="width: 30px">    <input id="grid_'+this.name+'_column_ln_check" type="checkbox" tabIndex="-1" '+(n.show.lineNumbers?"checked":"")+"        onclick=\"w2ui['"+n.name+"'].columnOnOff(this, event, 'line-numbers');\"><\/td><td onclick=\"w2ui['"+n.name+"'].columnOnOff(this, event, 'line-numbers'); $('#w2ui-overlay')[0].hide();\">    <label for=\"grid_"+this.name+'_column_ln_check">'+w2utils.lang("Line #")+"<\/label><\/td><\/tr>";for(t in this.columns)(u=this.columns[t],r=this.columns[t].caption,u.hideable!==!1)&&(!r&&this.columns[t].hint&&(r=this.columns[t].hint),r||(r="- column "+(parseInt(t)+1)+" -"),i+='<tr><td style="width: 30px">    <input id="grid_'+this.name+"_column_"+t+'_check" type="checkbox" tabIndex="-1" '+(u.hidden?"":"checked")+"        onclick=\"w2ui['"+n.name+"'].columnOnOff(this, event, '"+u.field+'\');"><\/td><td>    <label for="grid_'+this.name+"_column_"+t+'_check">'+r+"<\/label><\/td><\/tr>");i+='<tr><td colspan="2"><div style="border-top: 1px solid #ddd;"><\/div><\/td><\/tr>',f=typeof this.url!="object"?this.url:this.url.get,f&&n.show.skipRecords&&(i+='<tr><td colspan="2" style="padding: 0px">    <div style="cursor: pointer; padding: 2px 8px; cursor: default">'+w2utils.lang("Skip")+'        <input type="text" style="width: 45px" value="'+this.offset+'"             onkeypress="if (event.keyCode == 13) {                w2ui[\''+n.name+"'].skip(this.value);                $('#w2ui-overlay')[0].hide();             }\"> "+w2utils.lang("Records")+"    <\/div><\/td><\/tr>"),i+='<tr><td colspan="2" onclick="w2ui[\''+n.name+"'].stateSave(); $('#w2ui-overlay')[0].hide();\">    <div style=\"cursor: pointer; padding: 4px 8px; cursor: default\">"+w2utils.lang("Save Grid State")+'<\/div><\/td><\/tr><tr><td colspan="2" onclick="w2ui[\''+n.name+"'].stateReset(); $('#w2ui-overlay')[0].hide();\">    <div style=\"cursor: pointer; padding: 4px 8px; cursor: default\">"+w2utils.lang("Restore Default State")+"<\/div><\/td><\/tr>",i+="<\/table><\/div>",this.toolbar.get("w2ui-column-on-off").html=i}},initColumnDrag:function(){function i(){n.pressed=!1,clearTimeout(n.timeout)}function r(i){n.timeout&&clearTimeout(n.timeout);var r=this;n.pressed=!0,n.timeout=setTimeout(function(){var e,o;if(n.pressed){var s,h,l,c,a,v=["w2ui-col-number","w2ui-col-expand","w2ui-col-select"].concat(["w2ui-head-last"]);if($(i.originalEvent.target).parents().hasClass("w2ui-head")){for(e=0,o=v.length;e<o;e++)if($(i.originalEvent.target).parents().hasClass(v[e]))return;if(n.numberPreColumnsPresent=$(t.box).find(".w2ui-head.w2ui-col-number, .w2ui-head.w2ui-col-expand, .w2ui-head.w2ui-col-select").length,n.columnHead=c=$(i.originalEvent.target).parents(".w2ui-head"),a=parseInt(c.attr("col"),10),s=t.trigger({type:"columnDragStart",phase:"before",originalEvent:i,origColumnNumber:a,target:c[0]}),s.isCancelled===!0)return!1;h=n.columns=$(t.box).find(".w2ui-head:not(.w2ui-head-last)");$(document).on("mouseup",f);$(document).on("mousemove",u);for(n.originalPos=parseInt($(i.originalEvent.target).parent(".w2ui-head").attr("col"),10),n.ghost=$(r).clone(!0),$(n.ghost).find('[col]:not([col="'+n.originalPos+'"]), .w2ui-toolbar, .w2ui-grid-header').remove(),$(n.ghost).find(".w2ui-col-number, .w2ui-col-expand, .w2ui-col-select").remove(),$(n.ghost).find(".w2ui-grid-body").css({top:0}),l=$(n.ghost).find('[col="'+n.originalPos+'"]'),$(document.body).append(n.ghost),$(n.ghost).css({width:0,height:0,margin:0,position:"fixed",zIndex:999999,opacity:0}).addClass(".w2ui-grid-ghost").animate({width:l.width(),height:$(t.box).find(".w2ui-grid-body:first").height(),left:i.pageX,top:i.pageY,opacity:.8},0),n.offsets=[],e=0,o=h.length;e<o;e++)n.offsets.push($(h[e]).offset().left);t.trigger($.extend(s,{phase:"after"}))}}},150)}function u(t){if(n.pressed){var i=t.originalEvent.pageX,r=t.originalEvent.pageY,u=n.offsets,f=$(".w2ui-head:not(.w2ui-head-last)").width();n.targetInt=Math.max(n.numberPreColumnsPresent,o(i,u,f)),e(n.targetInt),s(i,r)}}function f(i){n.pressed=!1;var s,r,h,e,o,c=$(".w2ui-grid-ghost");if(s=t.trigger({type:"columnDragEnd",phase:"before",originalEvent:i,target:n.columnHead[0]}),s.isCancelled===!0)return!1;h=t.columns[n.originalPos],e=t.columns,o=$(n.columns[Math.min(n.lastInt,n.columns.length-1)]),r=n.lastInt<n.columns.length?parseInt(o.attr("col")):e.length,r!==n.originalPos+1&&r!==n.originalPos&&o&&o.length?($(n.ghost).animate({top:$(t.box).offset().top,left:o.offset().left,width:0,height:0,opacity:.2},300,function(){$(this).remove(),c.remove()}),e.splice(r,0,$.extend({},h)),e.splice(e.indexOf(h),1)):($(n.ghost).remove(),c.remove()),$(document).off("mouseup",f),$(document).off("mousemove",u),n.marker&&n.marker.remove(),n={},t.refresh(),t.trigger($.extend(s,{phase:"after",targetColumnNumber:r-1}))}function e(t){n.marker||n.markerLeft||(n.marker=$('<div class="col-intersection-marker"><div class="top-marker"><\/div><div class="bottom-marker"><\/div><\/div>'),n.markerLeft=$('<div class="col-intersection-marker"><div class="top-marker"><\/div><div class="bottom-marker"><\/div><\/div>')),n.lastInt&&n.lastInt===t||(n.lastInt=t,n.marker.remove(),n.markerLeft.remove(),$(".w2ui-head").removeClass("w2ui-col-intersection"),t>=n.columns.length?($(n.columns[n.columns.length-1]).children("div:last").append(n.marker.addClass("right").removeClass("left")),$(n.columns[n.columns.length-1]).addClass("w2ui-col-intersection")):t<=n.numberPreColumnsPresent?($(n.columns[n.numberPreColumnsPresent]).prepend(n.marker.addClass("left").removeClass("right")).css({position:"relative"}),$(n.columns[n.numberPreColumnsPresent]).prev().addClass("w2ui-col-intersection")):($(n.columns[t]).children("div:last").prepend(n.marker.addClass("left").removeClass("right")),$(n.columns[t]).prev().children("div:last").append(n.markerLeft.addClass("right").removeClass("left")).css({position:"relative"}),$(n.columns[t-1]).addClass("w2ui-col-intersection")))}function o(n,t,i){var r,u;if(n<=t[0])return 0;if(n>=t[t.length-1]+i)return t.length;for(r=0,u=t.length;r<u;r++){var o=t[r],f=t[r+1]||t[r]+i,e=(f-t[r])/2+t[r];if(n>o&&n<=e)return r;if(n>e&&n<=f)return r+1}return intersection}function s(t,i){$(n.ghost).css({left:t-10,top:i-10})}if(this.columnGroups&&this.columnGroups.length)throw"Draggable columns are not currently supported with column groups.";var t=this,n={};n.lastInt=null,n.pressed=!1,n.timeout=null,n.columnHead=null;$(t.box).on("mousedown",r);$(t.box).on("mouseup",i);return{remove:function(){$(t.box).off("mousedown",r),$(t.box).off("mouseup",i),$(t.box).find(".w2ui-head").removeAttr("draggable"),t.last.columnDrag=!1}}},columnOnOff:function(n,t,i){var e=this.trigger({phase:"before",target:this.name,type:"columnOnOff",checkbox:n,field:i,originalEvent:t}),o,u,f,r;if(e.isCancelled!==!0){o=this;for(u in this.records)this.records[u].expanded===!0&&(this.records[u].expanded=!1);f=!0,i=="line-numbers"?(this.show.lineNumbers=!this.show.lineNumbers,this.refresh()):(r=this.getColumn(i),r.hidden?($(n).prop("checked",!0),this.showColumn(r.field)):($(n).prop("checked",!1),this.hideColumn(r.field)),f=!1),f&&setTimeout(function(){$().w2overlay("",{name:"searches-"+this.name}),o.toolbar.uncheck("column-on-off")},100),this.trigger($.extend(e,{phase:"after"}))}},initToolbar:function(){var t,i,r,n;if(typeof this.toolbar.render=="undefined"){t=this.toolbar.items,this.toolbar.items=[],this.toolbar=$().w2toolbar($.extend(!0,{},this.toolbar,{name:this.name+"_toolbar",owner:this})),this.show.toolbarReload&&this.toolbar.items.push($.extend(!0,{},this.buttons.reload)),this.show.toolbarColumns&&this.toolbar.items.push($.extend(!0,{},this.buttons.columns)),(this.show.toolbarReload||this.show.toolbarColumn)&&this.toolbar.items.push({type:"break",id:"w2ui-break0"}),this.show.toolbarSearch&&(i='<div class="w2ui-toolbar-search"><table cellpadding="0" cellspacing="0"><tr>    <td>'+this.buttons.search.html+'<\/td>    <td>        <input id="grid_'+this.name+'_search_all" class="w2ui-search-all"             placeholder="'+this.last.caption+'" value="'+this.last.search+'"            onkeydown="if (event.keyCode == 13 && w2utils.isIE) this.onchange();"            onchange="                var val = this.value;                 var fld = $(this).data(\'w2field\');                 if (fld) val = fld.clean(val);                w2ui[\''+this.name+"'].search(w2ui['"+this.name+'\'].last.field, val);             ">    <\/td>    <td>        <div title="'+w2utils.lang("Clear Search")+'" class="w2ui-search-clear" id="grid_'+this.name+'_searchClear"               onclick="var obj = w2ui[\''+this.name+"']; obj.searchReset();\"         >&nbsp;&nbsp;<\/div>    <\/td><\/tr><\/table><\/div>",this.toolbar.items.push({type:"html",id:"w2ui-search",html:i}),this.multiSearch&&this.searches.length>0&&this.toolbar.items.push($.extend(!0,{},this.buttons["search-go"]))),this.show.toolbarSearch&&(this.show.toolbarAdd||this.show.toolbarEdit||this.show.toolbarDelete||this.show.toolbarSave)&&this.toolbar.items.push({type:"break",id:"w2ui-break1"}),this.show.toolbarAdd&&this.toolbar.items.push($.extend(!0,{},this.buttons.add)),this.show.toolbarEdit&&this.toolbar.items.push($.extend(!0,{},this.buttons.edit)),this.show.toolbarDelete&&this.toolbar.items.push($.extend(!0,{},this.buttons["delete"])),this.show.toolbarSave&&((this.show.toolbarAdd||this.show.toolbarDelete||this.show.toolbarEdit)&&this.toolbar.items.push({type:"break",id:"w2ui-break2"}),this.toolbar.items.push($.extend(!0,{},this.buttons.save)));for(r in t)this.toolbar.items.push(t[r]);n=this;this.toolbar.on("click",function(t){var i=n.trigger({phase:"before",type:"toolbar",target:t.target,originalEvent:t}),r,u,f,s,e,o;if(i.isCancelled!==!0){r=t.target;switch(r){case"w2ui-reload":if(u=n.trigger({phase:"before",type:"reload",target:n.name}),u.isCancelled===!0)return!1;n.reload(),n.trigger($.extend(u,{phase:"after"}));break;case"w2ui-column-on-off":n.initColumnOnOff(),n.initResize(),n.resize();break;case"w2ui-search-advanced":if(f=this,s=this.get(r),s.checked)n.searchClose(),setTimeout(function(){f.uncheck(r)},1);else{n.searchOpen(),t.originalEvent.stopPropagation();function h(){$("#w2ui-overlay-searches-"+n.name).data("keepOpen")!==!0&&(f.uncheck(r),$(document).off("click","body",h))}$(document).on("click","body",h)}break;case"w2ui-add":i=n.trigger({phase:"before",target:n.name,type:"add",recid:null}),n.trigger($.extend(i,{phase:"after"}));break;case"w2ui-edit":e=n.getSelection(),o=null,e.length==1&&(o=e[0]),i=n.trigger({phase:"before",target:n.name,type:"edit",recid:o}),n.trigger($.extend(i,{phase:"after"}));break;case"w2ui-delete":n["delete"]();break;case"w2ui-save":n.save()}n.trigger($.extend(i,{phase:"after"}))}})}return},initResize:function(){var n=this;$(this.box).find(".w2ui-resizer").off("click").on("click",function(n){n.stopPropagation?n.stopPropagation():n.cancelBubble=!0,n.preventDefault&&n.preventDefault()}).off("mousedown").on("mousedown",function(t){var r,i,u,f;t||(t=window.event),window.addEventListener||window.document.attachEvent("onselectstart",function(){return!1}),n.resizing=!0,n.last.tmp={x:t.screenX,y:t.screenY,gx:t.screenX,gy:t.screenY,col:parseInt($(this).attr("name"))},t.stopPropagation?t.stopPropagation():t.cancelBubble=!0,t.preventDefault&&t.preventDefault();for(r in n.columns)typeof n.columns[r].sizeOriginal=="undefined"&&(n.columns[r].sizeOriginal=n.columns[r].size),n.columns[r].size=n.columns[r].sizeCalculated;i={phase:"before",type:"columnResize",target:n.name,column:n.last.tmp.col,field:n.columns[n.last.tmp.col].field},i=n.trigger($.extend(i,{resizeBy:0,originalEvent:t})),u=function(t){if(n.resizing==!0){if(t||(t=window.event),i=n.trigger($.extend(i,{resizeBy:t.screenX-n.last.tmp.gx,originalEvent:t})),i.isCancelled===!0){i.isCancelled=!1;return}n.last.tmp.x=t.screenX-n.last.tmp.x,n.last.tmp.y=t.screenY-n.last.tmp.y,n.columns[n.last.tmp.col].size=parseInt(n.columns[n.last.tmp.col].size)+n.last.tmp.x+"px",n.resizeRecords(),n.last.tmp.x=t.screenX,n.last.tmp.y=t.screenY}},f=function(t){delete n.resizing,$(document).off("mousemove","body"),$(document).off("mouseup","body"),n.resizeRecords(),n.trigger($.extend(i,{phase:"after",originalEvent:t}))};$(document).on("mousemove","body",u);$(document).on("mouseup","body",f)}).each(function(n,t){var i=$(t).parent();$(t).css({height:"25px","margin-left":i.width()-3+"px"})})},resizeBoxes:function(){var f=$(this.box).find("> div"),n=$("#grid_"+this.name+"_header"),i=$("#grid_"+this.name+"_toolbar"),r=$("#grid_"+this.name+"_summary"),t=$("#grid_"+this.name+"_footer"),u=$("#grid_"+this.name+"_body"),e=$("#grid_"+this.name+"_columns"),o=$("#grid_"+this.name+"_records");this.show.header&&n.css({top:"0px",left:"0px",right:"0px"}),this.show.toolbar&&i.css({top:0+(this.show.header?w2utils.getSize(n,"height"):0)+"px",left:"0px",right:"0px"}),this.show.footer&&t.css({bottom:"0px",left:"0px",right:"0px"}),this.summary.length>0&&r.css({bottom:0+(this.show.footer?w2utils.getSize(t,"height"):0)+"px",left:"0px",right:"0px"}),u.css({top:0+(this.show.header?w2utils.getSize(n,"height"):0)+(this.show.toolbar?w2utils.getSize(i,"height"):0)+"px",bottom:0+(this.show.footer?w2utils.getSize(t,"height"):0)+(this.summary.length>0?w2utils.getSize(r,"height"):0)+"px",left:"0px",right:"0px"})},resizeRecords:function(){var i=this,l,b,a,s,ut,p,e,h,g,u,t,n;$(this.box).find(".w2ui-empty-record").remove();var nt=$(this.box),y=$(this.box).find("> div"),tt=$("#grid_"+this.name+"_header"),it=$("#grid_"+this.name+"_toolbar"),v=$("#grid_"+this.name+"_summary"),rt=$("#grid_"+this.name+"_footer"),c=$("#grid_"+this.name+"_body"),r=$("#grid_"+this.name+"_columns"),f=$("#grid_"+this.name+"_records");if(this.fixedBody?(l=y.height()-(this.show.header?w2utils.getSize(tt,"height"):0)-(this.show.toolbar?w2utils.getSize(it,"height"):0)-(v.css("display")!="none"?w2utils.getSize(v,"height"):0)-(this.show.footer?w2utils.getSize(rt,"height"):0),c.css("height",l)):(l=w2utils.getSize(r,"height")+w2utils.getSize($("#grid_"+i.name+"_records table"),"height"),i.height=l+w2utils.getSize(y,"+height")+(i.show.header?w2utils.getSize(tt,"height"):0)+(i.show.toolbar?w2utils.getSize(it,"height"):0)+(v.css("display")!="none"?w2utils.getSize(v,"height"):0)+(i.show.footer?w2utils.getSize(rt,"height"):0),y.css("height",i.height),c.css("height",l),nt.css("height",w2utils.getSize(y,"height")+w2utils.getSize(nt,"+height"))),b=this.records.length,this.searchData.length==0||this.url||(b=this.last.searchIds.length),a=!1,s=!1,c.width()<$(f).find(">table").width()&&(a=!0),c.height()-r.height()<$(f).find(">table").height()+(a?w2utils.scrollBarSize():0)&&(s=!0),this.fixedBody||(s=!1,a=!1),a||s?(r.find("> table > tbody > tr:nth-child(1) td.w2ui-head-last").css("width",w2utils.scrollBarSize()).show(),f.css({top:(this.columnGroups.length>0&&this.show.columns?1:0)+w2utils.getSize(r,"height")+"px","-webkit-overflow-scrolling":"touch","overflow-x":a?"auto":"hidden","overflow-y":s?"auto":"hidden"})):(r.find("> table > tbody > tr:nth-child(1) td.w2ui-head-last").hide(),f.css({top:(this.columnGroups.length>0&&this.show.columns?1:0)+w2utils.getSize(r,"height")+"px",overflow:"hidden"}),f.length>0&&(this.last.scrollTop=0,this.last.scrollLeft=0)),this.show.emptyRecords&&!s&&(ut=Math.floor(f.height()/this.recordHeight)+1,this.fixedBody))for(p=b;p<=ut;p++){for(e="",e+='<tr class="'+(p%2?"w2ui-even":"w2ui-odd")+' w2ui-empty-record" style="height: '+this.recordHeight+'px">',this.show.lineNumbers&&(e+='<td class="w2ui-col-number"><\/td>'),this.show.selectColumn&&(e+='<td class="w2ui-grid-data w2ui-col-select"><\/td>'),this.show.expandColumn&&(e+='<td class="w2ui-grid-data w2ui-col-expand"><\/td>'),h=0;!0&&this.columns.length>0;){if(n=this.columns[h],n.hidden)if(h++,typeof this.columns[h]=="undefined")break;else continue;if(e+='<td class="w2ui-grid-data" '+(typeof n.attr!="undefined"?n.attr:"")+' col="'+h+'"><\/td>',h++,typeof this.columns[h]=="undefined")break}e+='<td class="w2ui-grid-data-last"><\/td>',e+="<\/tr>",$("#grid_"+this.name+"_records > table").append(e)}if(c.length>0){var w=parseInt(c.width())-(s?w2utils.scrollBarSize():0)-(this.show.lineNumbers?34:0)-(this.show.selectColumn?26:0)-(this.show.expandColumn?26:0),k=w,o=0,d=!1;for(t=0;t<this.columns.length;t++)n=this.columns[t],n.gridMinWidth>0&&(n.gridMinWidth>k&&n.hidden!==!0&&(n.hidden=!0,d=!0),n.gridMinWidth<k&&n.hidden===!0&&(n.hidden=!1,d=!0));if(d===!0){this.refresh();return}for(t=0;t<this.columns.length;t++)(n=this.columns[t],n.hidden)||(String(n.size).substr(String(n.size).length-2).toLowerCase()=="px"?(w-=parseFloat(n.size),this.columns[t].sizeCalculated=n.size,this.columns[t].sizeType="px"):(o+=parseFloat(n.size),this.columns[t].sizeType="%",delete n.sizeCorrected));if(o!=100&&o>0)for(t=0;t<this.columns.length;t++)(n=this.columns[t],n.hidden)||n.sizeType=="%"&&(n.sizeCorrected=Math.round(parseFloat(n.size)*1e4/o)/100+"%");for(t=0;t<this.columns.length;t++)(n=this.columns[t],n.hidden)||n.sizeType=="%"&&(this.columns[t].sizeCalculated=typeof this.columns[t].sizeCorrected!="undefined"?Math.floor(w*parseFloat(n.sizeCorrected)/100)-1+"px":Math.floor(w*parseFloat(n.size)/100)-1+"px")}for(g=0,t=0;t<this.columns.length;t++)(n=this.columns[t],n.hidden)||(typeof n.min=="undefined"&&(n.min=20),parseInt(n.sizeCalculated)<parseInt(n.min)&&(n.sizeCalculated=n.min+"px"),parseInt(n.sizeCalculated)>parseInt(n.max)&&(n.sizeCalculated=n.max+"px"),g+=parseInt(n.sizeCalculated));if(u=parseInt(k)-parseInt(g),u>0&&o>0)for(t=0;;){if(n=this.columns[t],typeof n=="undefined"){t=0;continue}if(n.hidden||n.sizeType=="px"){t++;continue}if(n.sizeCalculated=parseInt(n.sizeCalculated)+1+"px",u--,u==0)break;t++}else u>0&&r.find("> table > tbody > tr:nth-child(1) td.w2ui-head-last").css("width",w2utils.scrollBarSize()).show();r.find("> table > tbody > tr:nth-child(1) td").each(function(n,t){var r=$(t).attr("col");typeof r!="undefined"&&i.columns[r]&&$(t).css("width",i.columns[r].sizeCalculated),$(t).hasClass("w2ui-head-last")&&$(t).css("width",w2utils.scrollBarSize()+(u>0&&o==0?u:0)+"px")}),r.find("> table > tbody > tr").length==3&&r.find("> table > tbody > tr:nth-child(1) td").html("").css({height:"0px",border:"0px",padding:"0px",margin:"0px"}),f.find("> table > tbody > tr:nth-child(1) td").each(function(n,t){var r=$(t).attr("col");typeof r!="undefined"&&i.columns[r]&&$(t).css("width",i.columns[r].sizeCalculated),$(t).hasClass("w2ui-grid-data-last")&&$(t).css("width",(u>0&&o==0?u:0)+"px")}),v.find("> table > tbody > tr:nth-child(1) td").each(function(n,t){var r=$(t).attr("col");typeof r!="undefined"&&i.columns[r]&&$(t).css("width",i.columns[r].sizeCalculated),$(t).hasClass("w2ui-grid-data-last")&&$(t).css("width",w2utils.scrollBarSize()+(u>0&&o==0?u:0)+"px")}),this.initResize(),this.refreshRanges(),this.last.scrollTop!=""&&f.length>0&&(r.prop("scrollLeft",this.last.scrollLeft),f.prop("scrollTop",this.last.scrollTop),f.prop("scrollLeft",this.last.scrollLeft))},getSearchesHTML:function(){for(var r='<table cellspacing="0">',f=!1,n,u,i,t=0;t<this.searches.length;t++)if(n=this.searches[t],n.type=String(n.type).toLowerCase(),!n.hidden){u="",f==!1&&(u='<button class="btn close-btn" onclick="obj = w2ui[\''+this.name+"']; if (obj) { obj.searchClose(); }\">X<\/button",f=!0),typeof n.inTag=="undefined"&&(n.inTag=""),typeof n.outTag=="undefined"&&(n.outTag=""),typeof n.type=="undefined"&&(n.type="text"),["text","alphanumeric","combo"].indexOf(n.type)!=-1&&(i='<select id="grid_'+this.name+"_operator_"+t+'" onclick="event.stopPropagation();">    <option value="is">'+w2utils.lang("is")+'<\/option>    <option value="begins">'+w2utils.lang("begins")+'<\/option>    <option value="contains">'+w2utils.lang("contains")+'<\/option>    <option value="ends">'+w2utils.lang("ends")+"<\/option><\/select>"),["int","float","money","currency","percent","date","time"].indexOf(n.type)!=-1&&(i='<select id="grid_'+this.name+"_operator_"+t+'"         onchange="w2ui[\''+this.name+"'].initOperator(this, "+t+');" onclick="event.stopPropagation();">    <option value="is">'+w2utils.lang("is")+"<\/option>"+(["int"].indexOf(n.type)!=-1?'<option value="in">'+w2utils.lang("in")+"<\/option>":"")+(["int"].indexOf(n.type)!=-1?'<option value="not in">'+w2utils.lang("not in")+"<\/option>":"")+'<option value="between">'+w2utils.lang("between")+"<\/option><\/select>"),["select","list","hex"].indexOf(n.type)!=-1&&(i='<select id="grid_'+this.name+"_operator_"+t+'" onclick="event.stopPropagation();">    <option value="is">'+w2utils.lang("is")+"<\/option><\/select>"),["enum"].indexOf(n.type)!=-1&&(i='<select id="grid_'+this.name+"_operator_"+t+'" onclick="event.stopPropagation();">    <option value="in">'+w2utils.lang("in")+'<\/option>    <option value="in">'+w2utils.lang("not in")+"<\/option><\/select>"),r+='<tr>    <td class="close-btn">'+u+'<\/td>    <td class="caption">'+n.caption+'<\/td>    <td class="operator">'+i+'<\/td>    <td class="value">';switch(n.type){case"text":case"alphanumeric":case"hex":case"list":case"combo":case"enum":r+='<input rel="search" type="text" style="width: 300px;" id="grid_'+this.name+"_field_"+t+'" name="'+n.field+'" '+n.inTag+">";break;case"int":case"float":case"money":case"currency":case"percent":case"date":case"time":r+='<input rel="search" type="text" size="12" id="grid_'+this.name+"_field_"+t+'" name="'+n.field+'" '+n.inTag+'><span id="grid_'+this.name+"_range_"+t+'" style="display: none">&nbsp;-&nbsp;&nbsp;<input rel="search" type="text" style="width: 90px" id="grid_'+this.name+"_field2_"+t+'" name="'+n.field+'" '+n.inTag+"><\/span>";break;case"select":r+='<select rel="search" id="grid_'+this.name+"_field_"+t+'" name="'+n.field+'" '+n.inTag+'  onclick="event.stopPropagation();"><\/select>'}r+=n.outTag+"    <\/td><\/tr>"}return r+('<tr>    <td colspan="4" class="actions">        <div>        <button class="btn" onclick="obj = w2ui[\''+this.name+"']; if (obj) { obj.searchReset(); }\">"+w2utils.lang("Reset")+'<\/button>        <button class="btn btn-blue" onclick="obj = w2ui[\''+this.name+"']; if (obj) { obj.search(); }\">"+w2utils.lang("Search")+"<\/button>        <\/div>    <\/td><\/tr><\/table>")},initOperator:function(n,t){var i=this,u=i.searches[t],f=$("#grid_"+i.name+"_range_"+t),r=$("#grid_"+i.name+"_field_"+t),e=r.parent().find("span input");$(n).val()=="in"||$(n).val()=="not in"?r.w2field("clear"):r.w2field(u.type),$(n).val()=="between"?(f.show(),e.w2field(u.type)):f.hide()},initSearches:function(){var o=this,i,n,t,r,s,u,f,e;for(i in this.searches){n=this.searches[i],t=this.getSearchData(n.field),n.type=String(n.type).toLowerCase(),typeof n.options!="object"&&(n.options={});switch(n.type){case"text":case"alphanumeric":$("#grid_"+this.name+"_operator_"+i).val("begins"),["alphanumeric","hex"].indexOf(n.type)!=-1&&$("#grid_"+this.name+"_field_"+i).w2field(n.type,n.options);break;case"int":case"float":case"money":case"currency":case"percent":case"date":case"time":if(t&&t.type=="int"&&["in","not in"].indexOf(t.operator)!=-1)break;$("#grid_"+this.name+"_field_"+i).w2field(n.type,n.options),$("#grid_"+this.name+"_field2_"+i).w2field(n.type,n.options),setTimeout(function(){$("#grid_"+o.name+"_field_"+i).keydown(),$("#grid_"+o.name+"_field2_"+i).keydown()},1);break;case"list":case"combo":case"enum":r=n.options,n.type=="list"&&(r.selected={}),n.type=="enum"&&(r.selected=[]),t&&(r.selected=t.value),$("#grid_"+this.name+"_field_"+i).w2field(n.type,r),n.type=="combo"&&$("#grid_"+this.name+"_operator_"+i).val("begins");break;case"select":r='<option value="">--<\/option>';for(s in n.options.items)u=n.options.items[s],$.isPlainObject(n.options.items[s])?(f=u.id,e=u.text,typeof f=="undefined"&&typeof u.value!="undefined"&&(f=u.value),typeof e=="undefined"&&typeof u.caption!="undefined"&&(e=u.caption),f==null&&(f=""),r+='<option value="'+f+'">'+e+"<\/option>"):r+='<option value="'+u+'">'+u+"<\/option>";$("#grid_"+this.name+"_field_"+i).html(r)}t!=null&&(t.type=="int"&&["in","not in"].indexOf(t.operator)!=-1&&$("#grid_"+this.name+"_field_"+i).w2field("clear").val(t.value),$("#grid_"+this.name+"_operator_"+i).val(t.operator).trigger("change"),$.isArray(t.value)?["in","not in"].indexOf(t.operator)!=-1?$("#grid_"+this.name+"_field_"+i).val(t.value).trigger("change"):($("#grid_"+this.name+"_field_"+i).val(t.value[0]).trigger("change"),$("#grid_"+this.name+"_field2_"+i).val(t.value[1]).trigger("change")):typeof t.value!="udefined"&&$("#grid_"+this.name+"_field_"+i).val(t.value).trigger("change"))}$("#w2ui-overlay-searches-"+this.name+" .w2ui-grid-searches *[rel=search]").on("keypress",function(n){n.keyCode==13&&(o.search(),$().w2overlay())})},getColumnsHTML:function(){function r(){var i="<tr>",r,u,t,f,e,o,s;for(n.columnGroups[n.columnGroups.length-1].caption!=""&&n.columnGroups.push({caption:""}),n.show.lineNumbers&&(i+='<td class="w2ui-head w2ui-col-number">    <div>&nbsp;<\/div><\/td>'),n.show.selectColumn&&(i+='<td class="w2ui-head w2ui-col-select">    <div>&nbsp;<\/div><\/td>'),n.show.expandColumn&&(i+='<td class="w2ui-head w2ui-col-expand">    <div>&nbsp;<\/div><\/td>'),r=0,u=0;u<n.columnGroups.length;u++){if(t=n.columnGroups[u],f=n.columns[r],(typeof t.span=="undefined"||t.span!=parseInt(t.span))&&(t.span=1),typeof t.colspan!="undefined"&&(t.span=t.colspan),t.master===!0){e="";for(o in n.sortData)n.sortData[o].field==f.field&&(RegExp("asc","i").test(n.sortData[o].direction)&&(e="w2ui-sort-up"),RegExp("desc","i").test(n.sortData[o].direction)&&(e="w2ui-sort-down"));s="",f.resizable!==!1&&(s='<div class="w2ui-resizer" name="'+r+'"><\/div>'),i+='<td class="w2ui-head '+e+'" col="'+r+'" rowspan="2" colspan="'+(t.span+(u==n.columnGroups.length-1?1:0))+'"     onclick="w2ui[\''+n.name+"'].columnClick('"+f.field+"', event);\">"+s+'    <div class="w2ui-col-group w2ui-col-header '+(e?"w2ui-col-sorted":"")+'">        <div class="'+e+'"><\/div>'+(f.caption?f.caption:"&nbsp;")+"    <\/div><\/td>"}else i+='<td class="w2ui-head" col="'+r+'"         colspan="'+(t.span+(u==n.columnGroups.length-1?1:0))+'">    <div class="w2ui-col-group">'+(t.caption?t.caption:"&nbsp;")+"    <\/div><\/td>";r+=t.span}return i+"<\/tr>"}function t(t){var u="<tr>",l=n.reorderColumns&&(!n.columnGroups||!n.columnGroups.length)?" w2ui-reorder-cols-head ":"",f,o,i,r,s,e,h,c;for(n.show.lineNumbers&&(u+='<td class="w2ui-head w2ui-col-number" onclick="w2ui[\''+n.name+"'].columnClick('line-number', event);\">    <div>#<\/div><\/td>"),n.show.selectColumn&&(u+='<td class="w2ui-head w2ui-col-select"         onclick="if (event.stopPropagation) event.stopPropagation(); else event.cancelBubble = true;">    <div>        <input type="checkbox" id="grid_'+n.name+'_check_all" tabIndex="-1"            style="'+(n.multiSelect==!1?"display: none;":"")+'"            onclick="if (this.checked) w2ui[\''+n.name+"'].selectAll();                      else w2ui['"+n.name+"'].selectNone();                      if (event.stopPropagation) event.stopPropagation(); else event.cancelBubble = true;\">    <\/div><\/td>"),n.show.expandColumn&&(u+='<td class="w2ui-head w2ui-col-expand">    <div>&nbsp;<\/div><\/td>'),f=0,o=0,i=0;i<n.columns.length;i++)if(r=n.columns[i],s={},i==o&&(o=o+(typeof n.columnGroups[f]!="undefined"?parseInt(n.columnGroups[f].span):0),f++),typeof n.columnGroups[f-1]!="undefined"&&(s=n.columnGroups[f-1]),!r.hidden){e="";for(h in n.sortData)n.sortData[h].field==r.field&&(RegExp("asc","i").test(n.sortData[h].direction)&&(e="w2ui-sort-up"),RegExp("desc","i").test(n.sortData[h].direction)&&(e="w2ui-sort-down"));(s.master!==!0||t)&&(c="",r.resizable!==!1&&(c='<div class="w2ui-resizer" name="'+i+'"><\/div>'),u+='<td col="'+i+'" class="w2ui-head '+e+l+'"     onclick="w2ui[\''+n.name+"'].columnClick('"+r.field+"', event);\">"+c+'    <div class="w2ui-col-header '+(e?"w2ui-col-sorted":"")+'">        <div class="'+e+'"><\/div>'+(r.caption?r.caption:"&nbsp;")+"    <\/div><\/td>")}return u+='<td class="w2ui-head w2ui-head-last"><div>&nbsp;<\/div><\/td>',u+"<\/tr>"}var n=this,i="";return this.show.columnHeaders&&(i=this.columnGroups.length>0?t(!0)+r()+t(!1):t(!0)),i},getRecordsHTML:function(){var t=this.records.length,u,n,i,r;for(this.searchData.length==0||this.url||(t=this.last.searchIds.length),this.show_extra=t>300?30:300,u=$("#grid_"+this.name+"_records"),n=Math.floor(u.height()/this.recordHeight)+this.show_extra+1,(!this.fixedBody||n>t)&&(n=t),i="<table>"+this.getRecordHTML(-1,0),i+='<tr id="grid_'+this.name+'_rec_top" line="top" style="height: 0px">    <td colspan="200"><\/td><\/tr>',r=0;r<n;r++)i+=this.getRecordHTML(r,r+1);return i+='<tr id="grid_'+this.name+'_rec_bottom" line="bottom" style="height: '+(t-n)*this.recordHeight+'px">    <td colspan="200"><\/td><\/tr><tr id="grid_'+this.name+'_rec_more" style="display: none">    <td colspan="200" class="w2ui-load-more"><\/td><\/tr><\/table>',this.last.range_start=0,this.last.range_end=n,i},getSummaryHTML:function(){var t,n;if(this.summary.length!=0){for(t="<table>",n=0;n<this.summary.length;n++)t+=this.getRecordHTML(n,n+1,!0);return t+"<\/table>"}},scroll:function(){function g(){n.markSearch!==!1&&(clearTimeout(n.last.marker_timer),n.last.marker_timer=setTimeout(function(){var t=[],r,i;for(r in n.searchData)i=n.searchData[r],$.inArray(i.value,t)==-1&&t.push(i.value);t.length>0&&$(n.box).find(".w2ui-grid-data > div").w2marker(t)},50))}var nt=(new Date).getTime(),n=this,i=$("#grid_"+this.name+"_records"),u=this.records.length,c,a,p,f,e,s,h,v,w,r,o,t,b,y,k,d,l;if(this.searchData.length==0||this.url||(u=this.last.searchIds.length),u!=0&&i.length!=0&&i.height()!=0){if(this.show_extra=u>300?30:300,i.height()<u*this.recordHeight&&i.css("overflow-y")=="hidden"){this.total>0&&this.refresh();return}if(c=Math.round(i[0].scrollTop/this.recordHeight+1),a=c+(Math.round(i.height()/this.recordHeight)-1),c>u&&(c=u),a>u&&(a=u),p=typeof this.url!="object"?this.url:this.url.get,$("#grid_"+this.name+"_footer .w2ui-footer-right").html(w2utils.formatNumber(this.offset+c)+"-"+w2utils.formatNumber(this.offset+a)+" "+w2utils.lang("of")+" "+w2utils.formatNumber(this.total)+(p?" ("+w2utils.lang("buffered")+" "+w2utils.formatNumber(u)+(this.offset>0?", skip "+w2utils.formatNumber(this.offset):"")+")":"")),p||!(!this.fixedBody||this.total<=300)){if(f=Math.floor(i[0].scrollTop/this.recordHeight)-this.show_extra,e=f+Math.floor(i.height()/this.recordHeight)+this.show_extra*2+1,f<1&&(f=1),e>this.total&&(e=this.total),s=i.find("#grid_"+this.name+"_rec_top"),h=i.find("#grid_"+this.name+"_rec_bottom"),String(s.next().prop("id")).indexOf("_expanded_row")!=-1&&s.next().remove(),this.total>e&&String(h.prev().prop("id")).indexOf("_expanded_row")!=-1&&h.prev().remove(),v=parseInt(s.next().attr("line")),w=parseInt(h.prev().attr("line")),v<f||v==1||this.last.pull_refresh){if(e<=w+this.show_extra-2&&e!=this.total)return;for(this.last.pull_refresh=!1;;){if(r=i.find("#grid_"+this.name+"_rec_top").next(),r.attr("line")=="bottom")break;if(parseInt(r.attr("line"))<f)r.remove();else break}for(r=i.find("#grid_"+this.name+"_rec_bottom").prev(),o=r.attr("line"),o=="top"&&(o=f),t=parseInt(o)+1;t<=e;t++)this.records[t-1]&&(this.records[t-1].expanded===!0&&(this.records[t-1].expanded=!1),h.before(this.getRecordHTML(t-1,t)));g(),setTimeout(function(){n.refreshRanges()},0)}else{if(f>=v-this.show_extra+2&&f>1)return;for(;;){if(r=i.find("#grid_"+this.name+"_rec_bottom").prev(),r.attr("line")=="top")break;if(parseInt(r.attr("line"))>e)r.remove();else break}for(r=i.find("#grid_"+this.name+"_rec_top").next(),o=r.attr("line"),o=="bottom"&&(o=e),t=parseInt(o)-1;t>=f;t--)this.records[t-1]&&(this.records[t-1].expanded===!0&&(this.records[t-1].expanded=!1),s.after(this.getRecordHTML(t-1,t)));g(),setTimeout(function(){n.refreshRanges()},0)}if(b=(f-1)*n.recordHeight,y=(u-e)*n.recordHeight,y<0&&(y=0),s.css("height",b+"px"),h.css("height",y+"px"),n.last.range_start=f,n.last.range_end=e,k=Math.floor(i[0].scrollTop/this.recordHeight),d=k+Math.floor(i.height()/this.recordHeight),d+10>u&&this.last.pull_more!==!0&&u<this.total-this.offset)if(this.autoLoad===!0)this.last.pull_more=!0,this.last.xhr_offset+=this.limit,this.request("get-records");else{if(l=$("#grid_"+this.name+"_rec_more"),l.css("display")=="none")l.show().on("click",function(){n.last.pull_more=!0,n.last.xhr_offset+=n.limit,n.request("get-records"),$(this).find("td").html('<div><div style="width: 20px; height: 20px;" class="w2ui-spinner"><\/div><\/div>')});l.find("td").text().indexOf("Load")==-1&&l.find("td").html("<div>"+w2utils.lang("Load")+" "+n.limit+" "+w2utils.lang("More")+"...<\/div>")}u>=this.total-this.offset&&$("#grid_"+this.name+"_rec_more").hide();return}}},getRecordHTML:function(n,t,i){var u="",a=this.last.selection,r,h,v,p,o,s,f,e,y,l;if(n==-1){u+='<tr line="0">',this.show.lineNumbers&&(u+='<td class="w2ui-col-number" style="height: 0px;"><\/td>'),this.show.selectColumn&&(u+='<td class="w2ui-col-select" style="height: 0px;"><\/td>'),this.show.expandColumn&&(u+='<td class="w2ui-col-expand" style="height: 0px;"><\/td>');for(h in this.columns)this.columns[h].hidden||(u+='<td class="w2ui-grid-data" col="'+h+'" style="height: 0px;"><\/td>');return u+='<td class="w2ui-grid-data-last" style="height: 0px;"><\/td>',u+"<\/tr>"}if(v=typeof this.url!="object"?this.url:this.url.get,i!==!0)if(this.searchData.length>0&&!v){if(n>=this.last.searchIds.length)return"";n=this.last.searchIds[n],r=this.records[n]}else{if(n>=this.records.length)return"";r=this.records[n]}else{if(n>=this.summary.length)return"";r=this.summary[n]}if(!r)return"";for(p=w2utils.escapeId(r.recid),o=!1,a.indexes.indexOf(n)!=-1&&(o=!0),u+='<tr id="grid_'+this.name+"_rec_"+r.recid+'" recid="'+r.recid+'" line="'+t+'"  class="'+(t%2==0?"w2ui-even":"w2ui-odd")+(o&&this.selectType=="row"?" w2ui-selected":"")+(r.expanded===!0?" w2ui-expanded":"")+'" '+(i!==!0?w2utils.isIOS?"    onclick  = \"w2ui['"+this.name+"'].dblClick('"+r.recid+"', event);\"":"    onclick  = \"w2ui['"+this.name+"'].click('"+r.recid+"', event);\"    oncontextmenu = \"w2ui['"+this.name+"'].contextMenu('"+r.recid+"', event);\"":"")+' style="height: '+this.recordHeight+"px; "+(!o&&typeof r.style=="string"?r.style:"")+'" '+(typeof r.style=="string"?'custom_style="'+r.style+'"':"")+">",this.show.lineNumbers&&(u+='<td id="grid_'+this.name+"_cell_"+n+"_number"+(i?"_s":"")+'" class="w2ui-col-number">'+(i!==!0?"<div>"+t+"<\/div>":"")+"<\/td>"),this.show.selectColumn&&(u+='<td id="grid_'+this.name+"_cell_"+n+"_select"+(i?"_s":"")+'" class="w2ui-grid-data w2ui-col-select"         onclick="if (event.stopPropagation) event.stopPropagation(); else event.cancelBubble = true;">'+(i!==!0?'    <div>        <input class="w2ui-grid-select-check" type="checkbox" tabIndex="-1"            '+(o?'checked="checked"':"")+"            onclick=\"var obj = w2ui['"+this.name+"'];                 if (!obj.multiSelect) { obj.selectNone(); }                if (this.checked) obj.select('"+r.recid+"'); else obj.unselect('"+r.recid+"');                 if (event.stopPropagation) event.stopPropagation(); else event.cancelBubble = true;\">    <\/div>":"")+"<\/td>"),this.show.expandColumn&&(s="",s=r.expanded===!0?"-":"+",r.expanded=="none"&&(s=""),r.expanded=="spinner"&&(s='<div class="w2ui-spinner" style="width: 16px; margin: -2px 2px;"><\/div>'),u+='<td id="grid_'+this.name+"_cell_"+n+"_expand"+(i?"_s":"")+'" class="w2ui-grid-data w2ui-col-expand">'+(i!==!0?'    <div ondblclick="if (event.stopPropagation) event.stopPropagation(); else event.cancelBubble = true;"             onclick="w2ui[\''+this.name+"'].toggle('"+r.recid+"', event);                 if (event.stopPropagation) event.stopPropagation(); else event.cancelBubble = true;\">        "+s+" <\/div>":"")+"<\/td>"),f=0;;){if(e=this.columns[f],e.hidden)if(f++,typeof this.columns[f]=="undefined")break;else continue;var w=!i&&r.changes&&typeof r.changes[e.field]!="undefined",b=this.getCellHTML(n,f,i),c="";if(typeof e.render=="string"&&(y=e.render.toLowerCase().split(":"),["number","int","float","money","currency","percent"].indexOf(y[0])!=-1&&(c+="text-align: right;")),typeof r.style=="object"&&typeof r.style[f]=="string"&&(c+=r.style[f]+";"),l=!1,o&&$.inArray(f,a.columns[n])!=-1&&(l=!0),u+='<td class="w2ui-grid-data'+(l?" w2ui-selected":"")+(w?" w2ui-changed":"")+'" col="'+f+'"     style="'+c+(typeof e.style!="undefined"?e.style:"")+'" '+(typeof e.attr!="undefined"?e.attr:"")+">"+b+"<\/td>",f++,typeof this.columns[f]=="undefined")break}return u+='<td class="w2ui-grid-data-last"><\/td>',u+"<\/tr>"},getCellHTML:function(n,t,i){var f=this.columns[t],c=i!==!0?this.records[n]:this.summary[n],r=this.getCellValue(n,t,i),l=f.editable,s,a,h;if(typeof f.render!="undefined"){if(typeof f.render=="function"&&(r=$.trim(f.render.call(this,c,n,t)),(r.length<4||r.substr(0,4).toLowerCase()!="<div")&&(r="<div>"+r+"<\/div>")),typeof f.render=="object"&&(r="<div>"+(f.render[r]||"")+"<\/div>"),typeof f.render=="string"){var u=f.render.toLowerCase().split(":"),o="",e="";["number","int","float","money","currency","percent"].indexOf(u[0])!=-1&&(typeof u[1]!="undefined"&&w2utils.isInt(u[1])||(u[1]=0),u[1]>20&&(u[1]=20),u[1]<0&&(u[1]=0),["money","currency"].indexOf(u[0])!=-1&&(u[1]=w2utils.settings.currencyPrecision,o=w2utils.settings.currencyPrefix,e=w2utils.settings.currencySuffix),u[0]=="percent"&&(e="%",u[1]!=="0"&&(u[1]=1)),u[0]=="int"&&(u[1]=0),r="<div>"+(r!==""?o+w2utils.formatNumber(Number(r).toFixed(u[1]))+e:"")+"<\/div>"),u[0]=="time"&&((typeof u[1]=="undefined"||u[1]=="")&&(u[1]=w2utils.settings.time_format),r="<div>"+o+w2utils.formatTime(r,u[1]=="h12"?"hh:mi pm":"h24:min")+e+"<\/div>"),u[0]=="date"&&((typeof u[1]=="undefined"||u[1]=="")&&(u[1]=w2utils.settings.date_display),r="<div>"+o+w2utils.formatDate(r,u[1])+e+"<\/div>"),u[0]=="age"&&(r="<div>"+o+w2utils.age(r)+e+"<\/div>"),u[0]=="toggle"&&(r="<div>"+o+(r?"Yes":"")+e+"<\/div>")}}else s="",l&&["checkbox","check"].indexOf(l.type)!=-1&&(a=i?-(n+1):n,s="text-align: center",r='<input type="checkbox" '+(r?"checked":"")+" onclick=\"    var obj = w2ui['"+this.name+"'];     obj.editChange.call(obj, this, "+a+", "+t+', event); ">'),this.show.recordTitles?(h=String(r).replace(/"/g,"''"),typeof f.title!="undefined"&&(typeof f.title=="function"&&(h=f.title.call(this,c,n,t)),typeof f.title=="string"&&(h=f.title)),r='<div title="'+w2utils.stripTags(h)+'" style="'+s+'">'+r+"<\/div>"):r='<div style="'+s+'">'+r+"<\/div>";return(r==null||typeof r=="undefined")&&(r=""),r},getCellValue:function(n,t,i){var f=this.columns[t],u=i!==!0?this.records[n]:this.summary[n],r=this.parseField(u,f.field);return u.changes&&typeof u.changes[f.field]!="undefined"&&(r=u.changes[f.field]),(r==null||typeof r=="undefined")&&(r=""),r},getFooterHTML:function(){return'<div>    <div class="w2ui-footer-left"><\/div>    <div class="w2ui-footer-right"><\/div>    <div class="w2ui-footer-center"><\/div><\/div>'},status:function(n){var r,t,i;typeof n!="undefined"?$("#grid_"+this.name+"_footer").find(".w2ui-footer-left").html(n):(r="",t=this.getSelection(),t.length>0&&(r=String(t.length).replace(/(\d)(?=(\d\d\d)+(?!\d))/g,"$1,")+" "+w2utils.lang("selected"),i=t[0],typeof i=="object"&&(i=i.recid+", "+w2utils.lang("Column")+": "+i.column),t.length==1&&(r=w2utils.lang("Record ID")+": "+i+" ")),$("#grid_"+this.name+"_footer .w2ui-footer-left").html(r),t.length==1?this.toolbar.enable("w2ui-edit"):this.toolbar.disable("w2ui-edit"),t.length>=1?this.toolbar.enable("w2ui-delete"):this.toolbar.disable("w2ui-delete"))},lock:function(){var t=$(this.box).find("> div:first-child"),n=Array.prototype.slice.call(arguments,0);n.unshift(t),setTimeout(function(){w2utils.lock.apply(window,n)},10)},unlock:function(){var n=this.box;setTimeout(function(){w2utils.unlock(n)},25)},stateSave:function(n){var t,i,u,f,r;if(!localStorage)return null;t={columns:[],show:$.extend({},this.show),last:{search:this.last.search,multi:this.last.multi,logic:this.last.logic,caption:this.last.caption,field:this.last.field,scrollTop:this.last.scrollTop,scrollLeft:this.last.scrollLeft},sortData:[],searchData:[]};for(u in this.columns)i=this.columns[u],t.columns.push({field:i.field,hidden:i.hidden,size:i.size,sizeCalculated:i.sizeCalculated,sizeOriginal:i.sizeOriginal,sizeType:i.sizeType});for(u in this.sortData)t.sortData.push($.extend({},this.sortData[u]));for(u in this.searchData)t.searchData.push($.extend({},this.searchData[u]));if(n!==!0){if(f=this.trigger({phase:"before",type:"stateSave",target:this.name,state:t}),f.isCancelled===!0){typeof callBack=="function"&&callBack({status:"error",message:"Request aborted."});return}try{r=$.parseJSON(localStorage.w2ui||"{}"),r||(r={}),r.states||(r.states={}),r.states[this.name]=t,localStorage.w2ui=JSON.stringify(r)}catch(e){return delete localStorage.w2ui,null}this.trigger($.extend(f,{phase:"after"}))}return t},stateRestore:function(n){var i=this,u,e,o,t,f,r;if(!n)try{if(!localStorage)return!1;t=$.parseJSON(localStorage.w2ui||"{}"),t||(t={}),t.states||(t.states={}),n=t.states[this.name]}catch(s){return delete localStorage.w2ui,null}if(u=this.trigger({phase:"before",type:"stateRestore",target:this.name,state:n}),u.isCancelled===!0){typeof callBack=="function"&&callBack({status:"error",message:"Request aborted."});return}if($.isPlainObject(n)){$.extend(this.show,n.show),$.extend(this.last,n.last),e=this.last.scrollTop,o=this.last.scrollLeft;for(r in n.columns)t=n.columns[r],f=this.getColumn(t.field),f&&$.extend(f,t);this.sortData.splice(0,this.sortData.length);for(r in n.sortData)this.sortData.push(n.sortData[r]);this.searchData.splice(0,this.searchData.length);for(r in n.searchData)this.searchData.push(n.searchData[r]);setTimeout(function(){i.sortData.length>0&&i.localSort(),i.searchData.length>0&&i.localSearch(),i.last.scrollTop=e,i.last.scrollLeft=o,i.refresh()},1)}return this.trigger($.extend(u,{phase:"after"})),!0},stateReset:function(){if(this.stateRestore(this.last.state),localStorage)try{var n=$.parseJSON(localStorage.w2ui||"{}");n.states&&n.states[this.name]&&delete n.states[this.name],localStorage.w2ui=JSON.stringify(n)}catch(t){return delete localStorage.w2ui,null}},parseField:function(n,t){var i="",r,u;try{i=n,r=String(t).split(".");for(u in r)i=i[r[u]]}catch(f){i=""}return i},prepareData:function(){var u,i,f,n,t,r;for(u in this.records){i=this.records[u];for(f in this.columns)(n=this.columns[f],i[n.field]!=null&&typeof n.render=="string")&&(["number","int","float","money","currency","percent"].indexOf(n.render.split(":")[0])!=-1&&typeof i[n.field]!="number"&&(i[n.field]=parseFloat(i[n.field])),["date","age"].indexOf(n.render.split(":")[0])!=-1&&(i[n.field+"_"]||(r=i[n.field],w2utils.isInt(r)&&(r=parseInt(r)),i[n.field+"_"]=new Date(r))),["time"].indexOf(n.render)!=-1&&(w2utils.isTime(i[n.field])?(t=w2utils.isTime(i[n.field],!0),r=new Date,r.setHours(t.hours,t.minutes,t.seconds?t.seconds:0,0),i[n.field+"_"]||(i[n.field+"_"]=r)):(t=i[n.field],w2utils.isInt(t)&&(t=parseInt(t)),t=t!=null?new Date(t):new Date,r=new Date,r.setHours(t.getHours(),t.getMinutes(),t.getSeconds(),0),i[n.field+"_"]||(i[n.field+"_"]=r))))}},nextCell:function(n,t){var i=n+1,r;return this.columns.length==i?null:t===!0&&(r=this.columns[i].editable,this.columns[i].hidden||typeof r=="undefined"||r&&["checkbox","check"].indexOf(r.type)!=-1)?this.nextCell(i,t):i},prevCell:function(n,t){var i=n-1,r;return i<0?null:t===!0&&(r=this.columns[i].editable,this.columns[i].hidden||typeof r=="undefined"||r&&["checkbox","check"].indexOf(r.type)!=-1)?this.prevCell(i,t):i},nextRow:function(n){if(n+1<this.records.length&&this.last.searchIds.length==0||this.last.searchIds.length>0&&n<this.last.searchIds[this.last.searchIds.length-1]){if(n++,this.last.searchIds.length>0)for(;;){if($.inArray(n,this.last.searchIds)!=-1||n>this.records.length)break;n++}return n}return null},prevRow:function(n){if(n>0&&this.last.searchIds.length==0||this.last.searchIds.length>0&&n>this.last.searchIds[0]){if(n--,this.last.searchIds.length>0)for(;;){if($.inArray(n,this.last.searchIds)!=-1||n<0)break;n--}return n}return null}},$.extend(n.prototype,w2utils.event),w2obj.grid=n}(),function(){var t=function(n){this.box=null,this.name=null,this.panels=[],this.tmp={},this.padding=1,this.resizer=4,this.style="",this.onShow=null,this.onHide=null,this.onResizing=null,this.onResizerClick=null,this.onRender=null,this.onRefresh=null,this.onResize=null,this.onDestroy=null,$.extend(!0,this,w2obj.layout,n)},n=["top","left","main","preview","right","bottom"];$.fn.w2layout=function(i){function h(n,t,i){var r=n.get(t);return(r!==null&&typeof i=="undefined"&&(i=r.tabs),r===null||i===null)?!1:($.isArray(i)&&(i={tabs:i}),$().w2destroy(n.name+"_"+t+"_tabs"),r.tabs=$().w2tabs($.extend({},i,{owner:n,name:n.name+"_"+t+"_tabs"})),r.show.tabs=!0,!0)}function c(n,t,i){var r=n.get(t);return(r!==null&&typeof i=="undefined"&&(i=r.toolbar),r===null||i===null)?!1:($.isArray(i)&&(i={items:i}),$().w2destroy(n.name+"_"+t+"_toolbar"),r.toolbar=$().w2toolbar($.extend({},i,{owner:n,name:n.name+"_"+t+"_toolbar"})),r.show.toolbar=!0,!0)}var e,r,u,s,f,o;if(typeof i!="object"&&i){if(w2ui[$(this).attr("name")])return o=w2ui[$(this).attr("name")],o[i].apply(o,Array.prototype.slice.call(arguments,1)),this;console.log("ERROR: Method "+i+" does not exist on jQuery.w2layout")}else{if(!w2utils.checkName(i,"w2layout"))return;for(e=i.panels||[],r=new t(i),$.extend(r,{handlers:[],panels:[]}),u=0,s=e.length;u<s;u++)r.panels[u]=$.extend(!0,{},t.prototype.panel,e[u]),($.isPlainObject(r.panels[u].tabs)||$.isArray(r.panels[u].tabs))&&h(r,e[u].type),($.isPlainObject(r.panels[u].toolbar)||$.isArray(r.panels[u].toolbar))&&c(r,e[u].type);for(f in n)(f=n[f],r.get(f)===null)&&r.panels.push($.extend(!0,{},t.prototype.panel,{type:f,hidden:f!=="main",size:50}));return $(this).length>0&&r.render($(this)[0]),w2ui[r.name]=r,r}},t.prototype={panel:{type:null,title:"",size:100,minSize:20,maxSize:!1,hidden:!1,resizable:!1,overflow:"auto",style:"",content:"",tabs:null,toolbar:null,width:null,height:null,show:{toolbar:!1,tabs:!1},onRefresh:null,onShow:null,onHide:null},html:function(n,t,i){return this.content(n,t,i)},content:function(n,t,i){var e=this,r=this.get(n),f,u;if(n=="css")return $("#layout_"+e.name+"_panel_css").html("<style>"+t+"<\/style>"),!0;if(r===null)return!1;if(typeof t=="undefined"||t===null)return r.content;if(t instanceof jQuery)return console.log("ERROR: You can not pass jQuery object to w2layout.content() method"),!1;var o="#layout_"+this.name+"_panel_"+r.type,h=$(o+"> .w2ui-panel-content"),s=0;return h.length>0&&($(o).scrollTop(0),s=$(h).position().top),r.content===""?(r.content=t,this.refresh(n)):(r.content=t,r.hidden||i!==null&&i!==""&&typeof i!="undefined"&&(f=$(o+"> .w2ui-panel-content"),f.after('<div class="w2ui-panel-content new-panel" style="'+f[0].style.cssText+'"><\/div>'),u=$(o+"> .w2ui-panel-content.new-panel"),f.css("top",s),u.css("top",s),typeof t=="object"?(t.box=u[0],t.render()):u.html(t),w2utils.transition(f[0],u[0],i,function(){f.remove(),u.removeClass("new-panel"),u.css("overflow",r.overflow),e.resize(),window.navigator.userAgent.indexOf("MSIE")!=-1&&setTimeout(function(){e.resize()},100)})),this.refresh(n)),e.resize(),window.navigator.userAgent.indexOf("MSIE")!=-1&&setTimeout(function(){e.resize()},100),!0},load:function(n,t,i,r){var u=this;return n=="css"?($.get(t,function(t,i,f){u.content(n,f.responseText),r&&r()}),!0):this.get(n)!==null?($.get(t,function(t,f,e){u.content(n,e.responseText,i),r&&r(),u.resize(),window.navigator.userAgent.indexOf("MSIE")!=-1&&setTimeout(function(){u.resize()},100)}),!0):!1},sizeTo:function(n,t){var i=this,r=i.get(n);return r===null?!1:($(i.box).find(" > div > .w2ui-panel").css({"-webkit-transition":".2s","-moz-transition":".2s","-ms-transition":".2s","-o-transition":".2s"}),setTimeout(function(){i.set(n,{size:t})},1),setTimeout(function(){$(i.box).find(" > div > .w2ui-panel").css({"-webkit-transition":"0s","-moz-transition":"0s","-ms-transition":"0s","-o-transition":"0s"}),i.resize()},500),!0)},show:function(n,t){var i=this,u=this.trigger({phase:"before",type:"show",target:n,object:this.get(n),immediate:t}),r;if(u.isCancelled!==!0)return(r=i.get(n),r===null)?!1:(r.hidden=!1,t===!0?($("#layout_"+i.name+"_panel_"+n).css({opacity:"1"}),r.resizable&&$("#layout_"+i.name+"_resizer_"+n).show(),i.trigger($.extend(u,{phase:"after"})),i.resize()):(r.resizable&&$("#layout_"+i.name+"_resizer_"+n).show(),$("#layout_"+i.name+"_panel_"+n).css({opacity:"0"}),$(i.box).find(" > div > .w2ui-panel").css({"-webkit-transition":".2s","-moz-transition":".2s","-ms-transition":".2s","-o-transition":".2s"}),setTimeout(function(){i.resize()},1),setTimeout(function(){$("#layout_"+i.name+"_panel_"+n).css({opacity:"1"})},250),setTimeout(function(){$(i.box).find(" > div > .w2ui-panel").css({"-webkit-transition":"0s","-moz-transition":"0s","-ms-transition":"0s","-o-transition":"0s"}),i.trigger($.extend(u,{phase:"after"})),i.resize()},500)),!0)},hide:function(n,t){var i=this,r=this.trigger({phase:"before",type:"hide",target:n,object:this.get(n),immediate:t}),u;if(r.isCancelled!==!0)return(u=i.get(n),u===null)?!1:(u.hidden=!0,t===!0?($("#layout_"+i.name+"_panel_"+n).css({opacity:"0"}),$("#layout_"+i.name+"_resizer_"+n).hide(),i.trigger($.extend(r,{phase:"after"})),i.resize()):($("#layout_"+i.name+"_resizer_"+n).hide(),$(i.box).find(" > div > .w2ui-panel").css({"-webkit-transition":".2s","-moz-transition":".2s","-ms-transition":".2s","-o-transition":".2s"}),$("#layout_"+i.name+"_panel_"+n).css({opacity:"0"}),setTimeout(function(){i.resize()},1),setTimeout(function(){$(i.box).find(" > div > .w2ui-panel").css({"-webkit-transition":"0s","-moz-transition":"0s","-ms-transition":"0s","-o-transition":"0s"}),i.trigger($.extend(r,{phase:"after"})),i.resize()},500)),!0)},toggle:function(n,t){var i=this.get(n);return i===null?!1:i.hidden?this.show(n,t):this.hide(n,t)},set:function(n,t){var i=this.get(n,!0);return i===null?!1:($.extend(this.panels[i],t),typeof t.content!="undefined"&&this.refresh(n),this.resize(),!0)},get:function(n,t){for(var i in this.panels)if(this.panels[i].type==n)return t===!0?i:this.panels[i];return null},el:function(n){var t=$("#layout_"+this.name+"_panel_"+n+"> .w2ui-panel-content");return t.length!=1?null:t[0]},hideToolbar:function(n){var t=this.get(n);t&&(t.show.toolbar=!1,$("#layout_"+this.name+"_panel_"+n+"> .w2ui-panel-toolbar").hide(),this.resize())},showToolbar:function(n){var t=this.get(n);t&&(t.show.toolbar=!0,$("#layout_"+this.name+"_panel_"+n+"> .w2ui-panel-toolbar").show(),this.resize())},toggleToolbar:function(n){var t=this.get(n);t&&(t.show.toolbar?this.hideToolbar(n):this.showToolbar(n))},hideTabs:function(n){var t=this.get(n);t&&(t.show.tabs=!1,$("#layout_"+this.name+"_panel_"+n+"> .w2ui-panel-tabs").hide(),this.resize())},showTabs:function(n){var t=this.get(n);t&&(t.show.tabs=!0,$("#layout_"+this.name+"_panel_"+n+"> .w2ui-panel-tabs").show(),this.resize())},toggleTabs:function(n){var t=this.get(n);t&&(t.show.tabs?this.hideTabs(n):this.showTabs(n))},render:function(t){function s(){i.tmp.events={resize:function(){w2ui[i.name].resize()},resizeStart:h,mouseMove:l,mouseUp:c};$(window).on("resize",i.tmp.events.resize)}function h(t,r){if(i.box){r||(r=window.event),window.addEventListener||window.document.attachEvent("onselectstart",function(){return!1});$(document).off("mousemove",i.tmp.events.mouseMove).on("mousemove",i.tmp.events.mouseMove);$(document).off("mouseup",i.tmp.events.mouseUp).on("mouseup",i.tmp.events.mouseUp);i.tmp.resize={type:t,x:r.screenX,y:r.screenY,diff_x:0,diff_y:0,value:0};for(var u in n)u=n[u],i.lock(u,{opacity:0});(t=="left"||t=="right")&&(i.tmp.resize.value=parseInt($("#layout_"+i.name+"_resizer_"+t)[0].style.left)),(t=="top"||t=="preview"||t=="bottom")&&(i.tmp.resize.value=parseInt($("#layout_"+i.name+"_resizer_"+t)[0].style.top))}}function c(t){var s;if(i.box&&(t||(t=window.event),window.addEventListener||window.document.attachEvent("onselectstart",function(){return!1}),$(document).off("mousemove",i.tmp.events.mouseMove),$(document).off("mouseup",i.tmp.events.mouseUp),typeof i.tmp.resize!="undefined")){for(s in n)i.unlock(n[s]);if(i.tmp.diff_x!==0||i.tmp.resize.diff_y!==0){var e=i.get("top"),o=i.get("bottom"),r=i.get(i.tmp.resize.type),c=parseInt($(i.box).height()),l=parseInt($(i.box).width()),h=String(r.size),u,f;switch(i.tmp.resize.type){case"top":u=parseInt(r.sizeCalculated)+i.tmp.resize.diff_y,f=0;break;case"bottom":u=parseInt(r.sizeCalculated)-i.tmp.resize.diff_y,f=0;break;case"preview":u=parseInt(r.sizeCalculated)-i.tmp.resize.diff_y,f=(e&&!e.hidden?e.sizeCalculated:0)+(o&&!o.hidden?o.sizeCalculated:0);break;case"left":u=parseInt(r.sizeCalculated)+i.tmp.resize.diff_x,f=0;break;case"right":u=parseInt(r.sizeCalculated)-i.tmp.resize.diff_x,f=0}r.size=h.substr(h.length-1)=="%"?Math.floor(u*100/(r.type=="left"||r.type=="right"?l:c-f)*100)/100+"%":u,i.resize()}$("#layout_"+i.name+"_resizer_"+i.tmp.resize.type).removeClass("active"),delete i.tmp.resize}}function l(n){if(i.box&&(n||(n=window.event),typeof i.tmp.resize!="undefined")){var t=i.get(i.tmp.resize.type),r=i.tmp.resize,s=i.trigger({phase:"before",type:"resizing",target:i.name,object:t,originalEvent:n,panel:r?r.type:"all",diff_x:r?r.diff_x:0,diff_y:r?r.diff_y:0});if(s.isCancelled!==!0){var o=$("#layout_"+i.name+"_resizer_"+r.type),f=n.screenX-r.x,e=n.screenY-r.y,u=i.get("main");o.hasClass("active")||o.addClass("active");switch(r.type){case"left":t.minSize-f>t.width&&(f=t.minSize-t.width),t.maxSize&&t.width+f>t.maxSize&&(f=t.maxSize-t.width),u.minSize+f>u.width&&(f=u.width-u.minSize);break;case"right":t.minSize+f>t.width&&(f=t.width-t.minSize),t.maxSize&&t.width-f>t.maxSize&&(f=t.width-t.maxSize),u.minSize-f>u.width&&(f=u.minSize-u.width);break;case"top":t.minSize-e>t.height&&(e=t.minSize-t.height),t.maxSize&&t.height+e>t.maxSize&&(e=t.maxSize-t.height),u.minSize+e>u.height&&(e=u.height-u.minSize);break;case"preview":case"bottom":t.minSize+e>t.height&&(e=t.height-t.minSize),t.maxSize&&t.height-e>t.maxSize&&(e=t.height-t.maxSize),u.minSize-e>u.height&&(e=u.minSize-u.height)}r.diff_x=f,r.diff_y=e;switch(r.type){case"top":case"preview":case"bottom":r.diff_x=0,o.length>0&&(o[0].style.top=r.value+r.diff_y+"px");break;case"left":case"right":r.diff_y=0,o.length>0&&(o[0].style.left=r.value+r.diff_x+"px")}i.trigger($.extend(s,{phase:"after"}))}}}var i=this,e=(new Date).getTime(),u=i.trigger({phase:"before",type:"render",target:i.name,box:t}),r,o,f;if(u.isCancelled!==!0){if(typeof t!="undefined"&&t!==null&&($(i.box).find("#layout_"+i.name+"_panel_main").length>0&&$(i.box).removeAttr("name").removeClass("w2ui-layout").html(""),i.box=t),!i.box)return!1;$(i.box).attr("name",i.name).addClass("w2ui-layout").html("<div><\/div>"),$(i.box).length>0&&($(i.box)[0].style.cssText+=i.style);for(r in n)r=n[r],o=i.get(r),f='<div id="layout_'+i.name+"_panel_"+r+'" class="w2ui-panel">    <div class="w2ui-panel-title"><\/div>    <div class="w2ui-panel-tabs"><\/div>    <div class="w2ui-panel-toolbar"><\/div>    <div class="w2ui-panel-content"><\/div><\/div><div id="layout_'+i.name+"_resizer_"+r+'" class="w2ui-resizer"><\/div>',$(i.box).find(" > div").append(f);return $(i.box).find(" > div").append('<div id="layout_'+i.name+'_panel_css" style="position: absolute; top: 10000px;"><\/div'),i.refresh(),i.trigger($.extend(u,{phase:"after"})),setTimeout(function(){s(),i.resize()},0),(new Date).getTime()-e}},refresh:function(n){var i=this,o,f,t,u,e,r,s;if(typeof n=="undefined"&&(n=null),o=(new Date).getTime(),f=i.trigger({phase:"before",type:"refresh",target:typeof n!="undefined"?n:i.name,object:i.get(n)}),f.isCancelled!==!0){if(typeof n=="string"){if(t=i.get(n),t===null)return;u="#layout_"+i.name+"_panel_"+t.type,e="#layout_"+i.name+"_resizer_"+t.type,$(u).css({display:t.hidden?"none":"block"}),t.resizable?$(e).show():$(e).hide(),typeof t.content=="object"&&typeof t.content.render=="function"?(t.content.box=$(u+"> .w2ui-panel-content")[0],setTimeout(function(){$(u+"> .w2ui-panel-content").length>0&&($(u+"> .w2ui-panel-content").removeClass().addClass("w2ui-panel-content").css("overflow",t.overflow)[0].style.cssText+=";"+t.style),t.content.render()},1)):$(u+"> .w2ui-panel-content").length>0&&($(u+"> .w2ui-panel-content").removeClass().addClass("w2ui-panel-content").html(t.content).css("overflow",t.overflow)[0].style.cssText+=";"+t.style),r=$(i.box).find(u+"> .w2ui-panel-tabs"),t.show.tabs?r.find("[name="+t.tabs.name+"]").length===0&&t.tabs!==null?r.w2render(t.tabs):t.tabs.refresh():r.html("").removeClass("w2ui-tabs").hide(),r=$(i.box).find(u+"> .w2ui-panel-toolbar"),t.show.toolbar?r.find("[name="+t.toolbar.name+"]").length===0&&t.toolbar!==null?r.w2render(t.toolbar):t.toolbar.refresh():r.html("").removeClass("w2ui-toolbar").hide(),r=$(i.box).find(u+"> .w2ui-panel-title"),t.title?r.html(t.title).show():r.html("").hide()}else{if($("#layout_"+i.name+"_panel_main").length==0){i.render();return}i.resize();for(s in this.panels)i.refresh(this.panels[s].type)}return i.trigger($.extend(f,{phase:"after"})),(new Date).getTime()-o}},resize:function(){var y,p,d,u,rt,tt,v;if(!this.box)return!1;var ot=(new Date).getTime(),u=this.tmp.resize,ut=this.trigger({phase:"before",type:"resize",target:this.name,panel:u?u.type:"all",diff_x:u?u.diff_x:0,diff_y:u?u.diff_y:0});if(ut.isCancelled!==!0){this.padding<0&&(this.padding=0),y=parseInt($(this.box).width()),p=parseInt($(this.box).height()),$(this.box).find(" > div").css({width:y+"px",height:p+"px"});var o=this,ft=this.get("main"),a=this.get("preview"),c=this.get("left"),l=this.get("right"),e=this.get("top"),s=this.get("bottom"),st=a!==null&&a.hidden!==!0?!0:!1,g=c!==null&&c.hidden!==!0?!0:!1,et=l!==null&&l.hidden!==!0?!0:!1,b=e!==null&&e.hidden!==!0?!0:!1,nt=s!==null&&s.hidden!==!0?!0:!1,f,r,t,i,h;for(d in n)(d=n[d],d!=="main")&&(u=this.get(d),u)&&(rt=String(u.size||0),rt.substr(rt.length-1)=="%"?(tt=p,u.type=="preview"&&(tt=tt-(e&&!e.hidden?e.sizeCalculated:0)-(s&&!s.hidden?s.sizeCalculated:0)),u.sizeCalculated=parseInt((u.type=="left"||u.type=="right"?y:tt)*parseFloat(u.size)/100)):u.sizeCalculated=parseInt(u.size),u.sizeCalculated=Math.max(u.sizeCalculated,parseInt(u.minSize)));if(e!==null&&e.hidden!==!0){if(f=0,r=0,t=y,i=e.sizeCalculated,$("#layout_"+this.name+"_panel_top").css({display:"block",left:f+"px",top:r+"px",width:t+"px",height:i+"px"}).show(),e.width=t,e.height=i,e.resizable){r=e.sizeCalculated-(this.padding===0?this.resizer:0),i=this.resizer>this.padding?this.resizer:this.padding;$("#layout_"+this.name+"_resizer_top").show().css({display:"block",left:f+"px",top:r+"px",width:t+"px",height:i+"px",cursor:"ns-resize"}).off("mousedown").on("mousedown",function(n){var t=o.trigger({phase:"before",type:"resizerClick",target:"top",originalEvent:n});if(t.isCancelled!==!0)return w2ui[o.name].tmp.events.resizeStart("top",n),o.trigger($.extend(t,{phase:"after"})),!1})}}else $("#layout_"+this.name+"_panel_top").hide();if(c!==null&&c.hidden!==!0){if(f=0,r=0+(b?e.sizeCalculated+this.padding:0),t=c.sizeCalculated,i=p-(b?e.sizeCalculated+this.padding:0)-(nt?s.sizeCalculated+this.padding:0),h=$("#layout_"+this.name+"_panel_left"),window.navigator.userAgent.indexOf("MSIE")!=-1&&h.length>0&&h[0].clientHeight<h[0].scrollHeight&&(t+=17),h.css({display:"block",left:f+"px",top:r+"px",width:t+"px",height:i+"px"}).show(),c.width=t,c.height=i,c.resizable){f=c.sizeCalculated-(this.padding===0?this.resizer:0),t=this.resizer>this.padding?this.resizer:this.padding;$("#layout_"+this.name+"_resizer_left").show().css({display:"block",left:f+"px",top:r+"px",width:t+"px",height:i+"px",cursor:"ew-resize"}).off("mousedown").on("mousedown",function(n){var t=o.trigger({phase:"before",type:"resizerClick",target:"left",originalEvent:n});if(t.isCancelled!==!0)return w2ui[o.name].tmp.events.resizeStart("left",n),o.trigger($.extend(t,{phase:"after"})),!1})}}else $("#layout_"+this.name+"_panel_left").hide(),$("#layout_"+this.name+"_resizer_left").hide();if(l!==null&&l.hidden!==!0){if(f=y-l.sizeCalculated,r=0+(b?e.sizeCalculated+this.padding:0),t=l.sizeCalculated,i=p-(b?e.sizeCalculated+this.padding:0)-(nt?s.sizeCalculated+this.padding:0),$("#layout_"+this.name+"_panel_right").css({display:"block",left:f+"px",top:r+"px",width:t+"px",height:i+"px"}).show(),l.width=t,l.height=i,l.resizable){f=f-this.padding,t=this.resizer>this.padding?this.resizer:this.padding;$("#layout_"+this.name+"_resizer_right").show().css({display:"block",left:f+"px",top:r+"px",width:t+"px",height:i+"px",cursor:"ew-resize"}).off("mousedown").on("mousedown",function(n){var t=o.trigger({phase:"before",type:"resizerClick",target:"right",originalEvent:n});if(t.isCancelled!==!0)return w2ui[o.name].tmp.events.resizeStart("right",n),o.trigger($.extend(t,{phase:"after"})),!1})}}else $("#layout_"+this.name+"_panel_right").hide();if(s!==null&&s.hidden!==!0){if(f=0,r=p-s.sizeCalculated,t=y,i=s.sizeCalculated,$("#layout_"+this.name+"_panel_bottom").css({display:"block",left:f+"px",top:r+"px",width:t+"px",height:i+"px"}).show(),s.width=t,s.height=i,s.resizable){r=r-(this.padding===0?0:this.padding),i=this.resizer>this.padding?this.resizer:this.padding;$("#layout_"+this.name+"_resizer_bottom").show().css({display:"block",left:f+"px",top:r+"px",width:t+"px",height:i+"px",cursor:"ns-resize"}).off("mousedown").on("mousedown",function(n){var t=o.trigger({phase:"before",type:"resizerClick",target:"bottom",originalEvent:n});if(t.isCancelled!==!0)return w2ui[o.name].tmp.events.resizeStart("bottom",n),o.trigger($.extend(t,{phase:"after"})),!1})}}else $("#layout_"+this.name+"_panel_bottom").hide();if(f=0+(g?c.sizeCalculated+this.padding:0),r=0+(b?e.sizeCalculated+this.padding:0),t=y-(g?c.sizeCalculated+this.padding:0)-(et?l.sizeCalculated+this.padding:0),i=p-(b?e.sizeCalculated+this.padding:0)-(nt?s.sizeCalculated+this.padding:0)-(st?a.sizeCalculated+this.padding:0),h=$("#layout_"+this.name+"_panel_main"),window.navigator.userAgent.indexOf("MSIE")!=-1&&h.length>0&&h[0].clientHeight<h[0].scrollHeight&&(t+=17),h.css({display:"block",left:f+"px",top:r+"px",width:t+"px",height:i+"px"}),ft.width=t,ft.height=i,a!==null&&a.hidden!==!0){if(f=0+(g?c.sizeCalculated+this.padding:0),r=p-(nt?s.sizeCalculated+this.padding:0)-a.sizeCalculated,t=y-(g?c.sizeCalculated+this.padding:0)-(et?l.sizeCalculated+this.padding:0),i=a.sizeCalculated,h=$("#layout_"+this.name+"_panel_preview"),window.navigator.userAgent.indexOf("MSIE")!=-1&&h.length>0&&h[0].clientHeight<h[0].scrollHeight&&(t+=17),h.css({display:"block",left:f+"px",top:r+"px",width:t+"px",height:i+"px"}).show(),a.width=t,a.height=i,a.resizable){r=r-(this.padding===0?0:this.padding),i=this.resizer>this.padding?this.resizer:this.padding;$("#layout_"+this.name+"_resizer_preview").show().css({display:"block",left:f+"px",top:r+"px",width:t+"px",height:i+"px",cursor:"ns-resize"}).off("mousedown").on("mousedown",function(n){var t=o.trigger({phase:"before",type:"resizerClick",target:"preview",originalEvent:n});if(t.isCancelled!==!0)return w2ui[o.name].tmp.events.resizeStart("preview",n),o.trigger($.extend(t,{phase:"after"})),!1})}}else $("#layout_"+this.name+"_panel_preview").hide();for(v in n){v=n[v];var k=this.get(v),it="#layout_"+this.name+"_panel_"+v+" > .w2ui-panel-",w=0;k&&(k.title&&(w+=w2utils.getSize($(it+"title").css({top:w+"px",display:"block"}),"height")),k.show.tabs&&(k.tabs!==null&&w2ui[this.name+"_"+v+"_tabs"]&&w2ui[this.name+"_"+v+"_tabs"].resize(),w+=w2utils.getSize($(it+"tabs").css({top:w+"px",display:"block"}),"height")),k.show.toolbar&&(k.toolbar!==null&&w2ui[this.name+"_"+v+"_toolbar"]&&w2ui[this.name+"_"+v+"_toolbar"].resize(),w+=w2utils.getSize($(it+"toolbar").css({top:w+"px",display:"block"}),"height"))),$(it+"content").css({display:"block"}).css({top:w+"px"})}return clearTimeout(this._resize_timer),this._resize_timer=setTimeout(function(){var n,t;for(n in w2ui)typeof w2ui[n].resize=="function"&&(w2ui[n].panels=="undefined"&&w2ui[n].resize(),t=$(w2ui[n].box).parents(".w2ui-layout"),t.length>0&&t.attr("name")==o.name&&w2ui[n].resize())},100),this.trigger($.extend(ut,{phase:"after"})),(new Date).getTime()-ot}},destroy:function(){var n=this.trigger({phase:"before",type:"destroy",target:this.name});if(n.isCancelled!==!0)return typeof w2ui[this.name]=="undefined"?!1:($(this.box).find("#layout_"+this.name+"_panel_main").length>0&&$(this.box).removeAttr("name").removeClass("w2ui-layout").html(""),delete w2ui[this.name],this.trigger($.extend(n,{phase:"after"})),this.tmp.events&&this.tmp.events.resize&&$(window).off("resize",this.tmp.events.resize),!0)},lock:function(t){if(n.indexOf(t)==-1){console.log("ERROR: First parameter needs to be the a valid panel name.");return}var i=Array.prototype.slice.call(arguments,0);i[0]="#layout_"+this.name+"_panel_"+t,w2utils.lock.apply(window,i)},unlock:function(t){if(n.indexOf(t)==-1){console.log("ERROR: First parameter needs to be the a valid panel name.");return}var i="#layout_"+this.name+"_panel_"+t;w2utils.unlock(i)}},$.extend(t.prototype,w2utils.event),w2obj.layout=t}(),w2popup={},function(){$.fn.w2popup=function(n,t){typeof n=="undefined"&&(t={},n="open"),$.isPlainObject(n)&&(t=n,n="open"),n=n.toLowerCase(),n==="load"&&typeof t=="string"&&(t=$.extend({url:t},arguments.length>2?arguments[2]:{})),n==="open"&&t.url!=null&&(n="load"),t=t||{};var i={};return $(this).length>0&&($(this).find("div[rel=title], div[rel=body], div[rel=buttons]").length>0?($(this).find("div[rel=title]").length>0&&(i.title=$(this).find("div[rel=title]").html()),$(this).find("div[rel=body]").length>0&&(i.body=$(this).find("div[rel=body]").html(),i.style=$(this).find("div[rel=body]")[0].style.cssText),$(this).find("div[rel=buttons]").length>0&&(i.buttons=$(this).find("div[rel=buttons]").html())):(i.title="&nbsp;",i.body=$(this).html()),parseInt($(this).css("width"))!=0&&(i.width=parseInt($(this).css("width"))),parseInt($(this).css("height"))!=0&&(i.height=parseInt($(this).css("height")))),w2popup[n]($.extend({},i,t))},w2popup={defaults:{title:"",body:"",buttons:"",style:"",color:"#000",opacity:.4,speed:.3,modal:!1,maximized:!1,keyboard:!0,width:500,height:300,showClose:!0,showMax:!1,transition:null},status:"closed",handlers:[],onOpen:null,onClose:null,onMax:null,onMin:null,onToggle:null,onKeydown:null,open:function(n){function y(n){n||(n=window.event),window.addEventListener||window.document.attachEvent("onselectstart",function(){return!1}),w2popup.status="moving",t.resizing=!0,t.x=n.screenX,t.y=n.screenY,t.pos_x=$("#w2ui-popup").position().left,t.pos_y=$("#w2ui-popup").position().top,w2popup.lock({opacity:0});$(document).on("mousemove",t.mvMove);$(document).on("mouseup",t.mvStop);if(n.stopPropagation?n.stopPropagation():n.cancelBubble=!0,n.preventDefault)n.preventDefault();else return!1}function p(n){t.resizing==!0&&(n||(n=window.event),t.div_x=n.screenX-t.x,t.div_y=n.screenY-t.y,$("#w2ui-popup").css({"-webkit-transition":"none","-webkit-transform":"translate3d("+t.div_x+"px, "+t.div_y+"px, 0px)","-moz-transition":"none","-moz-transform":"translate("+t.div_x+"px, "+t.div_y+"px)","-ms-transition":"none","-ms-transform":"translate("+t.div_x+"px, "+t.div_y+"px)","-o-transition":"none","-o-transform":"translate("+t.div_x+"px, "+t.div_y+"px)"}))}function w(n){t.resizing==!0&&(n||(n=window.event),w2popup.status="open",t.div_x=n.screenX-t.x,t.div_y=n.screenY-t.y,$("#w2ui-popup").css({left:t.pos_x+t.div_x+"px",top:t.pos_y+t.div_y+"px","-webkit-transition":"none","-webkit-transform":"translate3d(0px, 0px, 0px)","-moz-transition":"none","-moz-transform":"translate(0px, 0px)","-ms-transition":"none","-ms-transform":"translate(0px, 0px)","-o-transition":"none","-o-transform":"translate(0px, 0px)"}),t.resizing=!1,$(document).off("mousemove",t.mvMove),$(document).off("mouseup",t.mvStop),w2popup.unlock())}var e=this,i,n,r,u,l,a,o,v,f,h,c,s,t;if(w2popup.status=="closing"){setTimeout(function(){e.open.call(e,n)},100);return}if(i=$("#w2ui-popup").data("options"),n=$.extend({},this.defaults,i,{title:"",body:"",buttons:""},n,{maximized:!1}),setTimeout(function(){$("#w2ui-popup").data("options",n)},100),$("#w2ui-popup").length==0&&(w2popup.handlers=[],w2popup.onMax=null,w2popup.onMin=null,w2popup.onToggle=null,w2popup.onOpen=null,w2popup.onClose=null,w2popup.onKeydown=null),n.onOpen&&(w2popup.onOpen=n.onOpen),n.onClose&&(w2popup.onClose=n.onClose),n.onMax&&(w2popup.onMax=n.onMax),n.onMin&&(w2popup.onMin=n.onMin),n.onToggle&&(w2popup.onToggle=n.onToggle),n.onKeydown&&(w2popup.onKeydown=n.onKeydown),window.innerHeight==undefined?(r=document.documentElement.offsetWidth,u=document.documentElement.offsetHeight,w2utils.engine==="IE7"&&(r+=21,u+=4)):(r=window.innerWidth,u=window.innerHeight),parseInt(r)-10<parseInt(n.width)&&(n.width=parseInt(r)-10),parseInt(u)-10<parseInt(n.height)&&(n.height=parseInt(u)-10),l=parseInt((parseInt(u)-parseInt(n.height))/2*.6),a=parseInt((parseInt(r)-parseInt(n.width))/2),$("#w2ui-popup").length==0){if(f=this.trigger({phase:"before",type:"open",target:"popup",options:n,present:!1}),f.isCancelled===!0)return;w2popup.status="opening",w2popup.lockScreen(n),o="",n.showClose&&(o+='<div class="w2ui-msg-button w2ui-msg-close" onmousedown="event.stopPropagation()" onclick="w2popup.close()">Close<\/div>'),n.showMax&&(o+='<div class="w2ui-msg-button w2ui-msg-max" onmousedown="event.stopPropagation()" onclick="w2popup.toggle()">Max<\/div>'),v='<div id="w2ui-popup" class="w2ui-popup" style="opacity: 0; left: '+a+"px; top: "+l+"px;     width: "+parseInt(n.width)+"px; height: "+parseInt(n.height)+'px;     -webkit-transform: scale(0.8); -moz-transform: scale(0.8); -ms-transform: scale(0.8); -o-transform: scale(0.8); ">   <div class="w2ui-msg-title" style="'+(n.title==""?"display: none":"")+'">'+o+n.title+'<\/div>   <div class="w2ui-box1" style="'+(n.title==""?"top: 0px !important;":"")+(n.buttons==""?"bottom: 0px !important;":"")+'">       <div class="w2ui-msg-body'+(!n.title!=""?" w2ui-msg-no-title":"")+(!n.buttons!=""?" w2ui-msg-no-buttons":"")+'" style="'+n.style+'">'+n.body+'<\/div>   <\/div>   <div class="w2ui-box2" style="'+(n.title==""?"top: 0px !important;":"")+(n.buttons==""?"bottom: 0px !important;":"")+'">       <div class="w2ui-msg-body'+(!n.title!=""?" w2ui-msg-no-title":"")+(!n.buttons!=""?" w2ui-msg-no-buttons":"")+'" style="'+n.style+'"><\/div>       <\/div>   <div class="w2ui-msg-buttons" style="'+(n.buttons==""?"display: none":"")+'">'+n.buttons+"<\/div><\/div>",$("body").append(v),setTimeout(function(){$("#w2ui-popup .w2ui-box2").hide(),$("#w2ui-popup").css({"-webkit-transition":n.speed+"s opacity, "+n.speed+"s -webkit-transform","-webkit-transform":"scale(1)","-moz-transition":n.speed+"s opacity, "+n.speed+"s -moz-transform","-moz-transform":"scale(1)","-ms-transition":n.speed+"s opacity, "+n.speed+"s -ms-transform","-ms-transform":"scale(1)","-o-transition":n.speed+"s opacity, "+n.speed+"s -o-transform","-o-transform":"scale(1)",opacity:"1"})},1),setTimeout(function(){$("#w2ui-popup").css({"-webkit-transform":"","-moz-transform":"","-ms-transform":"","-o-transform":""}),w2popup.status="open",setTimeout(function(){e.trigger($.extend(f,{phase:"after"}))},100)},n.speed*1e3)}else{if(f=this.trigger({phase:"before",type:"open",target:"popup",options:n,present:!0}),f.isCancelled===!0)return;w2popup.status="opening",(typeof i=="undefined"||i.width!=n.width||i.height!=n.height)&&w2popup.resize(n.width,n.height),typeof i!="undefined"&&(n.prevSize=n.width+":"+n.height,n.maximized=i.maximized),h=$("#w2ui-popup .w2ui-box2 > .w2ui-msg-body").html(n.body),h.length>0&&(h[0].style.cssText=n.style),n.buttons!=""?($("#w2ui-popup .w2ui-msg-buttons").show().html(n.buttons),$("#w2ui-popup .w2ui-msg-body").removeClass("w2ui-msg-no-buttons"),$("#w2ui-popup .w2ui-box1, #w2ui-popup .w2ui-box2").css("bottom","")):($("#w2ui-popup .w2ui-msg-buttons").hide().html(""),$("#w2ui-popup .w2ui-msg-body").addClass("w2ui-msg-no-buttons"),$("#w2ui-popup .w2ui-box1, #w2ui-popup .w2ui-box2").css("bottom","0px")),n.title!=""?($("#w2ui-popup .w2ui-msg-title").show().html((n.showClose?'<div class="w2ui-msg-button w2ui-msg-close" onmousedown="event.stopPropagation()" onclick="w2popup.close()">Close<\/div>':"")+(n.showMax?'<div class="w2ui-msg-button w2ui-msg-max" onmousedown="event.stopPropagation()" onclick="w2popup.toggle()">Max<\/div>':"")+n.title),$("#w2ui-popup .w2ui-msg-body").removeClass("w2ui-msg-no-title"),$("#w2ui-popup .w2ui-box1, #w2ui-popup .w2ui-box2").css("top","")):($("#w2ui-popup .w2ui-msg-title").hide().html(""),$("#w2ui-popup .w2ui-msg-body").addClass("w2ui-msg-no-title"),$("#w2ui-popup .w2ui-box1, #w2ui-popup .w2ui-box2").css("top","0px")),c=$("#w2ui-popup .w2ui-box1")[0],s=$("#w2ui-popup .w2ui-box2")[0],w2utils.transition(c,s,n.transition),s.className="w2ui-box1",c.className="w2ui-box2",$(s).addClass("w2ui-current-box"),$("#w2ui-popup").data("prev-size",null),setTimeout(function(){w2popup.status="open",e.trigger($.extend(f,{phase:"after"}))},100)}if(n._last_w2ui_name=w2utils.keyboard.active(),w2utils.keyboard.active(null),n.keyboard)$(document).on("keydown",this.keydown);t={resizing:!1,mvMove:p,mvStop:w};$("#w2ui-popup .w2ui-msg-title").on("mousedown",function(n){y(n)});return this},keydown:function(n){var i=$("#w2ui-popup").data("options"),t;if(i.keyboard&&(t=w2popup.trigger({phase:"before",type:"keydown",target:"popup",options:i,originalEvent:n}),t.isCancelled!==!0)){switch(n.keyCode){case 27:n.preventDefault(),$("#w2ui-popup .w2ui-popup-message").length>0?w2popup.message():w2popup.close()}w2popup.trigger($.extend(t,{phase:"after"}))}},close:function(n){var i=this,n=$.extend({},$("#w2ui-popup").data("options"),n),t;$("#w2ui-popup").length!=0&&(t=this.trigger({phase:"before",type:"close",target:"popup",options:n}),t.isCancelled!==!0)&&(w2popup.status="closing",$("#w2ui-popup").css({"-webkit-transition":n.speed+"s opacity, "+n.speed+"s -webkit-transform","-webkit-transform":"scale(0.9)","-moz-transition":n.speed+"s opacity, "+n.speed+"s -moz-transform","-moz-transform":"scale(0.9)","-ms-transition":n.speed+"s opacity, "+n.speed+"s -ms-transform","-ms-transform":"scale(0.9)","-o-transition":n.speed+"s opacity, "+n.speed+"s -o-transform","-o-transform":"scale(0.9)",opacity:"0"}),w2popup.unlockScreen(n),setTimeout(function(){$("#w2ui-popup").remove(),w2popup.status="closed",i.trigger($.extend(t,{phase:"after"}))},n.speed*1e3),w2utils.keyboard.active(n._last_w2ui_name),n.keyboard&&$(document).off("keydown",this.keydown))},toggle:function(){var i=this,n=$("#w2ui-popup").data("options"),t=this.trigger({phase:"before",type:"toggle",target:"popup",options:n});t.isCancelled!==!0&&(n.maximized===!0?w2popup.min():w2popup.max(),setTimeout(function(){i.trigger($.extend(t,{phase:"after"}))},n.speed*1e3+50))},max:function(){var i=this,n=$("#w2ui-popup").data("options"),t;n.maximized!==!0&&(t=this.trigger({phase:"before",type:"max",target:"popup",options:n}),t.isCancelled!==!0)&&(w2popup.status="resizing",n.prevSize=$("#w2ui-popup").css("width")+":"+$("#w2ui-popup").css("height"),w2popup.resize(1e4,1e4,function(){w2popup.status="open",n.maximized=!0,i.trigger($.extend(t,{phase:"after"}))}))},min:function(){var r=this,n=$("#w2ui-popup").data("options"),t,i;n.maximized===!0&&(t=n.prevSize.split(":"),i=this.trigger({phase:"before",type:"min",target:"popup",options:n}),i.isCancelled!==!0)&&(w2popup.status="resizing",w2popup.resize(t[0],t[1],function(){w2popup.status="open",n.maximized=!1,n.prevSize=null,r.trigger($.extend(i,{phase:"after"}))}))},get:function(){return $("#w2ui-popup").data("options")},set:function(n){w2popup.open(n)},clear:function(){$("#w2ui-popup .w2ui-msg-title").html(""),$("#w2ui-popup .w2ui-msg-body").html(""),$("#w2ui-popup .w2ui-msg-buttons").html("")},reset:function(){w2popup.open(w2popup.defaults)},load:function(n){function f(t,i){if(delete n.url,$("body").append('<div id="w2ui-tmp" style="display: none">'+t+"<\/div>"),typeof i!="undefined"&&$("#w2ui-tmp #"+i).length>0?$("#w2ui-tmp #"+i).w2popup(n):$("#w2ui-tmp > div").w2popup(n),$("#w2ui-tmp > style").length>0){var r=$("<div>").append($("#w2ui-tmp > style").clone()).html();$("#w2ui-popup #div-style").length==0&&$("#w2ui-popup").append('<div id="div-style" style="position: absolute; left: -100; width: 1px"><\/div>'),$("#w2ui-popup #div-style").html(r)}$("#w2ui-tmp").remove()}var t;if(w2popup.status="loading",String(n.url)=="undefined"){console.log("ERROR: The url parameter is empty.");return}var r=String(n.url).split("#"),i=r[0],u=r[1];String(n)=="undefined"&&(n={}),t=$("#w2ui-popup").data(i),typeof t!="undefined"&&t!=null?f(t,u):$.get(i,function(n,t,r){f(r.responseText,u),$("#w2ui-popup").data(i,r.responseText)})},message:function(n){var r,n,i;$().w2tag(),n||(n={width:200,height:100}),parseInt(n.width)<10&&(n.width=10),parseInt(n.height)<10&&(n.height=10),typeof n.hideOnClick=="undefined"&&(n.hideOnClick=!1),r=$("#w2ui-popup").data("options")||{},(typeof n.width=="undefined"||n.width>r.width-10)&&(n.width=r.width-10),(typeof n.height=="undefined"||n.height>r.height-40)&&(n.height=r.height-40);var u=$("#w2ui-popup .w2ui-msg-title"),f=parseInt($("#w2ui-popup").width()),t=$("#w2ui-popup .w2ui-popup-message").length;$.trim(n.html)==""?($("#w2ui-popup #w2ui-message"+(t-1)).css("z-Index",250),n=$("#w2ui-popup #w2ui-message"+(t-1)).data("options")||{},$("#w2ui-popup #w2ui-message"+(t-1)).remove(),typeof n.onClose=="function"&&n.onClose(),t==1?w2popup.unlock():$("#w2ui-popup #w2ui-message"+(t-2)).show()):($("#w2ui-popup .w2ui-popup-message").hide(),$("#w2ui-popup .w2ui-box1").before('<div id="w2ui-message'+t+'" class="w2ui-popup-message" style="display: none; '+(u.length==0?"top: 0px;":"top: "+w2utils.getSize(u,"height")+"px;")+(typeof n.width!="undefined"?"width: "+n.width+"px; left: "+(f-n.width)/2+"px;":"left: 10px; right: 10px;")+(typeof n.height!="undefined"?"height: "+n.height+"px;":"bottom: 6px;")+'-webkit-transition: .3s; -moz-transition: .3s; -ms-transition: .3s; -o-transition: .3s;"'+(n.hideOnClick===!0?'onclick="w2popup.message();"':"")+"><\/div>"),$("#w2ui-popup #w2ui-message"+t).data("options",n),i=$("#w2ui-popup #w2ui-message"+t).css("display"),$("#w2ui-popup #w2ui-message"+t).css({"-webkit-transform":i=="none"?"translateY(-"+n.height+"px)":"translateY(0px)","-moz-transform":i=="none"?"translateY(-"+n.height+"px)":"translateY(0px)","-ms-transform":i=="none"?"translateY(-"+n.height+"px)":"translateY(0px)","-o-transform":i=="none"?"translateY(-"+n.height+"px)":"translateY(0px)"}),i=="none"&&($("#w2ui-popup #w2ui-message"+t).show().html(n.html),setTimeout(function(){$("#w2ui-popup #w2ui-message"+t).css({"-webkit-transform":i=="none"?"translateY(0px)":"translateY(-"+n.height+"px)","-moz-transform":i=="none"?"translateY(0px)":"translateY(-"+n.height+"px)","-ms-transform":i=="none"?"translateY(0px)":"translateY(-"+n.height+"px)","-o-transform":i=="none"?"translateY(0px)":"translateY(-"+n.height+"px)"})},1),setTimeout(function(){$("#w2ui-popup #w2ui-message"+t).css({"-webkit-transition":"0s","-moz-transition":"0s","-ms-transition":"0s","-o-transition":"0s","z-Index":1500}),t==0&&w2popup.lock(),typeof n.onOpen=="function"&&n.onOpen()},300)))},lock:function(){var n=Array.prototype.slice.call(arguments,0);n.unshift($("#w2ui-popup")),w2utils.lock.apply(window,n)},unlock:function(){w2utils.unlock($("#w2ui-popup"))},lockScreen:function(n){if($("#w2ui-lock").length>0)return!1;if(typeof n=="undefined"&&(n=$("#w2ui-popup").data("options")),typeof n=="undefined"&&(n={}),n=$.extend({},w2popup.defaults,n),$("body").append('<div id="w2ui-lock"     onmousewheel="if (event.stopPropagation) event.stopPropagation(); else event.cancelBubble = true; if (event.preventDefault) event.preventDefault(); else return false;"    style="position: '+(w2utils.engine=="IE5"?"absolute":"fixed")+"; z-Index: 1199; left: 0px; top: 0px;            padding: 0px; margin: 0px; background-color: "+n.color+'; width: 100%; height: 100%; opacity: 0;"><\/div>'),setTimeout(function(){$("#w2ui-lock").css({"-webkit-transition":n.speed+"s opacity","-moz-transition":n.speed+"s opacity","-ms-transition":n.speed+"s opacity","-o-transition":n.speed+"s opacity",opacity:n.opacity})},1),n.modal==!0){$("#w2ui-lock").on("mousedown",function(){$("#w2ui-lock").css({"-webkit-transition":".1s","-moz-transition":".1s","-ms-transition":".1s","-o-transition":".1s",opacity:"0.6"})});$("#w2ui-lock").on("mouseup",function(){setTimeout(function(){$("#w2ui-lock").css({"-webkit-transition":".1s","-moz-transition":".1s","-ms-transition":".1s","-o-transition":".1s",opacity:n.opacity})},100)})}else $("#w2ui-lock").on("mouseup",function(){w2popup.close()});return!0},unlockScreen:function(n){return $("#w2ui-lock").length==0?!1:(typeof n=="undefined"&&(n=$("#w2ui-popup").data("options")),typeof n=="undefined"&&(n={}),n=$.extend({},w2popup.defaults,n),$("#w2ui-lock").css({"-webkit-transition":n.speed+"s opacity","-moz-transition":n.speed+"s opacity","-ms-transition":n.speed+"s opacity","-o-transition":n.speed+"s opacity",opacity:0}),setTimeout(function(){$("#w2ui-lock").remove()},n.speed*1e3),!0)},resize:function(n,t,i){var r=$("#w2ui-popup").data("options"),u,f;parseInt($(window).width())-10<parseInt(n)&&(n=parseInt($(window).width())-10),parseInt($(window).height())-10<parseInt(t)&&(t=parseInt($(window).height())-10),u=(parseInt($(window).height())-parseInt(t))/2*.8,f=(parseInt($(window).width())-parseInt(n))/2,$("#w2ui-popup").css({"-webkit-transition":r.speed+"s width, "+r.speed+"s height, "+r.speed+"s left, "+r.speed+"s top","-moz-transition":r.speed+"s width, "+r.speed+"s height, "+r.speed+"s left, "+r.speed+"s top","-ms-transition":r.speed+"s width, "+r.speed+"s height, "+r.speed+"s left, "+r.speed+"s top","-o-transition":r.speed+"s width, "+r.speed+"s height, "+r.speed+"s left, "+r.speed+"s top",top:u,left:f,width:n,height:t}),setTimeout(function(){r.width=n,r.height=t,typeof i=="function"&&i()},r.speed*1e3+50)}},$.extend(w2popup,w2utils.event)}(),w2alert=function(n,t,i){t==null&&(t=w2utils.lang("Notification")),$("#w2ui-popup").length>0&&w2popup.status!="closing"?w2popup.message({width:400,height:170,html:'<div style="position: absolute; top: 0px; left: 0px; right: 0px; bottom: 45px; overflow: auto">        <div class="w2ui-centered" style="font-size: 13px;">'+n+'<\/div><\/div><div style="position: absolute; bottom: 7px; left: 0px; right: 0px; text-align: center; padding: 5px">        <button onclick="w2popup.message();" class="w2ui-popup-btn btn">'+w2utils.lang("Ok")+"<\/button><\/div>",onClose:function(){typeof i=="function"&&i()}}):w2popup.open({width:450,height:220,showMax:!1,showClose:!1,title:t,body:'<div class="w2ui-centered" style="font-size: 13px;">'+n+"<\/div>",buttons:'<button onclick="w2popup.close();" class="w2ui-popup-btn btn">'+w2utils.lang("Ok")+"<\/button>",onClose:function(){typeof i=="function"&&i()}})},w2confirm=function(n,t,i){var r={},u={msg:"",title:w2utils.lang("Confirmation"),width:$("#w2ui-popup").length>0?400:450,height:$("#w2ui-popup").length>0?170:220,yes_text:"Yes",yes_class:"",yes_style:"",yes_callBack:null,no_text:"No",no_class:"",no_style:"",no_callBack:null,callBack:null};return arguments.length==1&&typeof n=="object"?$.extend(r,u,n):typeof t=="function"?$.extend(r,u,{msg:n,callBack:t}):$.extend(r,u,{msg:n,title:t,callBack:i}),$("#w2ui-popup").length>0&&w2popup.status!="closing"?(r.width>w2popup.get().width&&(r.width=w2popup.get().width),r.height>w2popup.get().height-50&&(r.height=w2popup.get().height-50),w2popup.message({width:r.width,height:r.height,html:'<div style="position: absolute; top: 0px; left: 0px; right: 0px; bottom: 40px; overflow: auto">        <div class="w2ui-centered" style="font-size: 13px;">'+r.msg+'<\/div><\/div><div style="position: absolute; bottom: 7px; left: 0px; right: 0px; text-align: center; padding: 5px">        <button id="Yes" class="w2ui-popup-btn btn '+r.yes_class+'" style="'+r.yes_style+'">'+w2utils.lang(r.yes_text)+'<\/button>        <button id="No" class="w2ui-popup-btn btn '+r.no_class+'" style="'+r.no_style+'">'+w2utils.lang(r.no_text)+"<\/button><\/div>",onOpen:function(){$("#w2ui-popup .w2ui-popup-message .btn").on("click",function(n){w2popup.message(),typeof r.callBack=="function"&&r.callBack(n.target.id),n.target.id=="Yes"&&typeof r.yes_callBack=="function"&&r.yes_callBack(),n.target.id=="No"&&typeof r.no_callBack=="function"&&r.no_callBack()})},onKeydown:function(n){switch(n.originalEvent.keyCode){case 13:typeof r.callBack=="function"&&r.callBack("Yes"),typeof r.yes_callBack=="function"&&r.yes_callBack(),w2popup.message();break;case 27:typeof r.callBack=="function"&&r.callBack("No"),typeof r.no_callBack=="function"&&r.no_callBack(),w2popup.message()}}})):(w2utils.isInt(r.height)||(r.height=r.height+50),w2popup.open({width:r.width,height:r.height,title:r.title,modal:!0,showClose:!1,body:'<div class="w2ui-centered" style="font-size: 13px;">'+r.msg+"<\/div>",buttons:'<button id="Yes" class="w2ui-popup-btn btn '+r.yes_class+'" style="'+r.yes_style+'">'+w2utils.lang(r.yes_text)+'<\/button><button id="No" class="w2ui-popup-btn btn '+r.no_class+'" style="'+r.no_style+'">'+w2utils.lang(r.no_text)+"<\/button>",onOpen:function(n){n.onComplete=function(){$("#w2ui-popup .w2ui-popup-btn").on("click",function(n){w2popup.close(),typeof r.callBack=="function"&&r.callBack(n.target.id),n.target.id=="Yes"&&typeof r.yes_callBack=="function"&&r.yes_callBack(),n.target.id=="No"&&typeof r.no_callBack=="function"&&r.no_callBack()})}},onKeydown:function(n){switch(n.originalEvent.keyCode){case 13:typeof r.callBack=="function"&&r.callBack("Yes"),typeof r.yes_callBack=="function"&&r.yes_callBack(),w2popup.close();break;case 27:typeof r.callBack=="function"&&r.callBack("No"),typeof r.no_callBack=="function"&&r.no_callBack(),w2popup.close()}}})),{yes:function(n){return r.yes_callBack=n,this},no:function(n){return r.no_callBack=n,this}}},function(){var n=function(n){this.box=null,this.name=null,this.active=null,this.tabs=[],this.routeData={},this.right="",this.style="",this.onClick=null,this.onClose=null,this.onRender=null,this.onRefresh=null,this.onResize=null,this.onDestroy=null,$.extend(this,{handlers:[]}),$.extend(!0,this,w2obj.tabs,n)};$.fn.w2tabs=function(t){var u,i,r,f;if(typeof t!="object"&&t)return w2ui[$(this).attr("name")]?(f=w2ui[$(this).attr("name")],f[t].apply(f,Array.prototype.slice.call(arguments,1)),this):(console.log("ERROR: Method "+t+" does not exist on jQuery.w2tabs"),undefined);if(w2utils.checkName(t,"w2tabs")){for(u=t.tabs||[],i=new n(t),r=0;r<u.length;r++)i.tabs[r]=$.extend({},n.prototype.tab,u[r]);return $(this).length!==0&&i.render($(this)[0]),w2ui[i.name]=i,i}},n.prototype={tab:{id:null,text:"",route:null,hidden:!1,disabled:!1,closable:!1,hint:"",onClick:null,onRefresh:null,onClose:null},add:function(n){return this.insert(null,n)},insert:function(t,i){var r,u,f;for($.isArray(i)||(i=[i]),r=0;r<i.length;r++){if(typeof i[r].id=="undefined"){console.log('ERROR: The parameter "id" is required but not supplied. (obj: '+this.name+")");return}if(!w2utils.checkUniqueId(i[r].id,this.tabs,"tabs",this.name))return;u=$.extend({},n.prototype.tab,i[r]),t===null||typeof t=="undefined"?this.tabs.push(u):(f=this.get(t,!0),this.tabs=this.tabs.slice(0,f).concat([u],this.tabs.slice(f))),this.refresh(i[r].id)}},remove:function(){for(var i=0,t,n=0;n<arguments.length;n++){if(t=this.get(arguments[n]),!t)return!1;i++,this.tabs.splice(this.get(t.id,!0),1),$(this.box).find("#tabs_"+this.name+"_tab_"+w2utils.escapeId(t.id)).remove()}return i},select:function(n){return this.active==n||this.get(n)===null?!1:(this.active=n,this.refresh(),!0)},set:function(n,t){var i=this.get(n,!0);return i===null?!1:($.extend(this.tabs[i],t),this.refresh(n),!0)},get:function(n,t){var u,r,i;if(arguments.length===0){for(u=[],r=0;r<this.tabs.length;r++)this.tabs[r].id!=null&&u.push(this.tabs[r].id);return u}for(i=0;i<this.tabs.length;i++)if(this.tabs[i].id==n)return t===!0?i:this.tabs[i];return null},show:function(){for(var u=this,r=0,i=[],n,t=0;t<arguments.length;t++)(n=this.get(arguments[t]),n&&n.hidden!==!1)&&(r++,n.hidden=!1,i.push(n.id));return setTimeout(function(){for(var n in i)u.refresh(i[n])},15),r},hide:function(){for(var u=this,r=0,i=[],n,t=0;t<arguments.length;t++)(n=this.get(arguments[t]),n&&n.hidden!==!0)&&(r++,n.hidden=!0,i.push(n.id));return setTimeout(function(){for(var n in i)u.refresh(i[n])},15),r},enable:function(){for(var u=this,r=0,i=[],n,t=0;t<arguments.length;t++)(n=this.get(arguments[t]),n&&n.disabled!==!1)&&(r++,n.disabled=!1,i.push(n.id));return setTimeout(function(){for(var n in i)u.refresh(i[n])},15),r},disable:function(){for(var u=this,r=0,i=[],n,t=0;t<arguments.length;t++)(n=this.get(arguments[t]),n&&n.disabled!==!0)&&(r++,n.disabled=!0,i.push(n.id));return setTimeout(function(){for(var n in i)u.refresh(i[n])},15),r},refresh:function(n){var s=(new Date).getTime(),o=this.trigger({phase:"before",type:"refresh",target:typeof n!="undefined"?n:this.name,object:this.get(n)}),r,t,i,f,u,e;if(o.isCancelled!==!0){if(typeof n=="undefined")for(r=0;r<this.tabs.length;r++)this.refresh(this.tabs[r].id);else{if(t=this.get(n),t===null)return!1;typeof t.caption!="undefined"&&(t.text=t.caption),i=$(this.box).find("#tabs_"+this.name+"_tab_"+w2utils.escapeId(t.id)),f=(t.closable?'<div class="w2ui-tab-close" onclick="w2ui[\''+this.name+"'].animateClose('"+t.id+"', event);\"><\/div>":"")+'    <div class="w2ui-tab'+(this.active===t.id?" active":"")+(t.closable?" closable":"")+'"         title="'+(typeof t.hint!="undefined"?t.hint:"")+'"        onclick="w2ui[\''+this.name+"'].click('"+t.id+"', event);\">"+t.text+"<\/div>",i.length===0?(u="",t.hidden&&(u+="display: none;"),t.disabled&&(u+="opacity: 0.2; -moz-opacity: 0.2; -webkit-opacity: 0.2; -o-opacity: 0.2; filter:alpha(opacity=20);"),e='<td id="tabs_'+this.name+"_tab_"+t.id+'" style="'+u+'" valign="middle">'+f+"<\/td>",this.get(n,!0)!==this.tabs.length-1&&$(this.box).find("#tabs_"+this.name+"_tab_"+w2utils.escapeId(this.tabs[parseInt(this.get(n,!0))+1].id)).length>0?$(this.box).find("#tabs_"+this.name+"_tab_"+w2utils.escapeId(this.tabs[parseInt(this.get(n,!0))+1].id)).before(e):$(this.box).find("#tabs_"+this.name+"_right").before(e)):(i.html(f),t.hidden?i.css("display","none"):i.css("display",""),t.disabled?i.css({opacity:"0.2","-moz-opacity":"0.2","-webkit-opacity":"0.2","-o-opacity":"0.2",filter:"alpha(opacity=20)"}):i.css({opacity:"1","-moz-opacity":"1","-webkit-opacity":"1","-o-opacity":"1",filter:"alpha(opacity=100)"}))}return $("#tabs_"+this.name+"_right").html(this.right),this.trigger($.extend(o,{phase:"after"})),(new Date).getTime()-s}},render:function(n){var r=(new Date).getTime(),t=this.trigger({phase:"before",type:"render",target:this.name,box:n}),i;if(t.isCancelled!==!0)return(typeof n!="undefined"&&n!==null&&($(this.box).find("> table #tabs_"+this.name+"_right").length>0&&$(this.box).removeAttr("name").removeClass("w2ui-reset w2ui-tabs").html(""),this.box=n),!this.box)?!1:(i='<table cellspacing="0" cellpadding="1" width="100%">    <tr><td width="100%" id="tabs_'+this.name+'_right" align="right">'+this.right+"<\/td><\/tr><\/table>",$(this.box).attr("name",this.name).addClass("w2ui-reset w2ui-tabs").html(i),$(this.box).length>0&&($(this.box)[0].style.cssText+=this.style),this.trigger($.extend(t,{phase:"after"})),this.refresh(),(new Date).getTime()-r)},resize:function(){var t=(new Date).getTime(),n=this.trigger({phase:"before",type:"resize",target:this.name});if(n.isCancelled!==!0)return this.trigger($.extend(n,{phase:"after"})),(new Date).getTime()-t},destroy:function(){var n=this.trigger({phase:"before",type:"destroy",target:this.name});n.isCancelled!==!0&&($(this.box).find("> table #tabs_"+this.name+"_right").length>0&&$(this.box).removeAttr("name").removeClass("w2ui-reset w2ui-tabs").html(""),delete w2ui[this.name],this.trigger($.extend(n,{phase:"after"})))},click:function(n,t){var i=this.get(n),e,f,r,u;if(i===null||i.disabled)return!1;if(e=this.trigger({phase:"before",type:"click",target:n,tab:i,object:i,originalEvent:t}),e.isCancelled!==!0){if($(this.box).find("#tabs_"+this.name+"_tab_"+w2utils.escapeId(this.active)+" .w2ui-tab").removeClass("active"),this.active=i.id,i.route){if(f=String("/"+i.route).replace(/\/{2,}/g,"/"),r=w2utils.parseRoute(f),r.keys.length>0)for(u=0;u<r.keys.length;u++)this.routeData[r.keys[u].name]!=null&&(f=f.replace(new RegExp(":"+r.keys[u].name,"g"),this.routeData[r.keys[u].name]));setTimeout(function(){window.location.hash=f},1)}this.trigger($.extend(e,{phase:"after"})),this.refresh(n)}},animateClose:function(n,t){var r=this.get(n),u,i;if(r===null||r.disabled)return!1;(u=this.trigger({phase:"before",type:"close",target:n,object:this.get(n),originalEvent:t}),u.isCancelled!==!0)&&(i=this,$(this.box).find("#tabs_"+this.name+"_tab_"+w2utils.escapeId(r.id)).css({"-webkit-transition":".2s","-moz-transition":"2s","-ms-transition":".2s","-o-transition":".2s",opacity:"0"}),setTimeout(function(){var n=$(i.box).find("#tabs_"+i.name+"_tab_"+w2utils.escapeId(r.id)).width();$(i.box).find("#tabs_"+i.name+"_tab_"+w2utils.escapeId(r.id)).html('<div style="width: '+n+'px; -webkit-transition: .2s; -moz-transition: .2s; -ms-transition: .2s; -o-transition: .2s"><\/div>'),setTimeout(function(){$(i.box).find("#tabs_"+i.name+"_tab_"+w2utils.escapeId(r.id)).find(":first-child").css({width:"0px"})},50)},200),setTimeout(function(){i.remove(n)},450),this.trigger($.extend(u,{phase:"after"})),this.refresh())},animateInsert:function(n,t){var f,e,o,i,r,u;this.get(n)!==null&&$.isPlainObject(t)&&w2utils.checkUniqueId(t.id,this.tabs,"tabs",this.name)&&(f=$(this.box).find("#tabs_"+this.name+"_tab_"+w2utils.escapeId(t.id)),f.length===0)&&(typeof t.caption!="undefined"&&(t.text=t.caption),e='<div id="_tmp_tabs" class="w2ui-reset w2ui-tabs" style="position: absolute; top: -1000px;"><table cellspacing="0" cellpadding="1" width="100%"><tr><td id="_tmp_simple_tab" style="" valign="middle">'+(t.closable?'<div class="w2ui-tab-close"><\/div>':"")+'    <div class="w2ui-tab '+(this.active===t.id?"active":"")+'">'+t.text+"<\/div><\/td><\/tr><\/table><\/div>",$("body").append(e),o='<div style="width: 1px; -webkit-transition: 0.2s; -moz-transition: 0.2s; -ms-transition: 0.2s; -o-transition: 0.2s;">&nbsp;<\/div>',i="",t.hidden&&(i+="display: none;"),t.disabled&&(i+="opacity: 0.2; -moz-opacity: 0.2; -webkit-opacity: 0.2; -o-opacity: 0.2; filter:alpha(opacity=20);"),r='<td id="tabs_'+this.name+"_tab_"+t.id+'" style="'+i+'" valign="middle">'+o+"<\/td>",this.get(n,!0)!==this.tabs.length&&$(this.box).find("#tabs_"+this.name+"_tab_"+w2utils.escapeId(this.tabs[parseInt(this.get(n,!0))].id)).length>0?$(this.box).find("#tabs_"+this.name+"_tab_"+w2utils.escapeId(this.tabs[parseInt(this.get(n,!0))].id)).before(r):$(this.box).find("#tabs_"+this.name+"_right").before(r),u=this,setTimeout(function(){var n=$("#_tmp_simple_tab").width();$("#_tmp_tabs").remove(),$("#tabs_"+u.name+"_tab_"+w2utils.escapeId(t.id)+" > div").css("width",n+"px")},1),setTimeout(function(){u.insert(n,t)},200))}},$.extend(n.prototype,w2utils.event),w2obj.tabs=n}(),function(){var n=function(n){this.box=null,this.name=null,this.routeData={},this.items=[],this.right="",this.onClick=null,this.onRender=null,this.onRefresh=null,this.onResize=null,this.onDestroy=null,$.extend(!0,this,w2obj.toolbar,n)};$.fn.w2toolbar=function(t){var u,i,r,f;if(typeof t!="object"&&t){if(w2ui[$(this).attr("name")])return f=w2ui[$(this).attr("name")],f[t].apply(f,Array.prototype.slice.call(arguments,1)),this;console.log("ERROR: Method "+t+" does not exist on jQuery.w2toolbar")}else{if(!w2utils.checkName(t,"w2toolbar"))return;for(u=t.items||[],i=new n(t),$.extend(i,{items:[],handlers:[]}),r=0;r<u.length;r++)i.items[r]=$.extend({},n.prototype.item,u[r]);return $(this).length!==0&&i.render($(this)[0]),w2ui[i.name]=i,i}},n.prototype={item:{id:null,type:"button",text:"",route:null,html:"",img:null,icon:null,count:null,hidden:!1,disabled:!1,checked:!1,arrow:!0,hint:"",group:null,items:null,overlay:{},onClick:null},add:function(n){this.insert(null,n)},insert:function(t,i){var r,u,f;for($.isArray(i)||(i=[i]),r=0;r<i.length;r++){if(typeof i[r].type=="undefined"){console.log('ERROR: The parameter "type" is required but not supplied in w2toolbar.add() method.');return}if($.inArray(String(i[r].type),["button","check","radio","drop","menu","break","html","spacer"])===-1){console.log('ERROR: The parameter "type" should be one of the following [button, check, radio, drop, menu, break, html, spacer] in w2toolbar.add() method.');return}if(typeof i[r].id=="undefined"){console.log('ERROR: The parameter "id" is required but not supplied in w2toolbar.add() method.');return}if(!w2utils.checkUniqueId(i[r].id,this.items,"toolbar items",this.name))return;u=$.extend({},n.prototype.item,i[r]),t==null?this.items.push(u):(f=this.get(t,!0),this.items=this.items.slice(0,f).concat([u],this.items.slice(f))),this.refresh(u.id)}},remove:function(){for(var r=0,t,i,n=0;n<arguments.length;n++)(t=this.get(arguments[n]),t)&&(r++,$(this.box).find("#tb_"+this.name+"_item_"+w2utils.escapeId(t.id)).remove(),i=this.get(t.id,!0),i&&this.items.splice(i,1));return r},set:function(n,t){var i=this.get(n,!0);return i===null?!1:($.extend(this.items[i],t),this.refresh(n),!0)},get:function(n,t){var u,r,i;if(arguments.length===0){for(u=[],r=0;r<this.items.length;r++)this.items[r].id!==null&&u.push(this.items[r].id);return u}for(i=0;i<this.items.length;i++)if(this.items[i].id===n)return t===!0?i:this.items[i];return null},show:function(){for(var u=this,r=0,i=[],t,n=0;n<arguments.length;n++)(t=this.get(arguments[n]),t)&&(r++,t.hidden=!1,i.push(t.id));return setTimeout(function(){for(var n in i)u.refresh(i[n])},15),r},hide:function(){for(var u=this,r=0,i=[],t,n=0;n<arguments.length;n++)(t=this.get(arguments[n]),t)&&(r++,t.hidden=!0,i.push(t.id));return setTimeout(function(){for(var n in i)u.refresh(i[n])},15),r},enable:function(){for(var u=this,r=0,i=[],t,n=0;n<arguments.length;n++)(t=this.get(arguments[n]),t)&&(r++,t.disabled=!1,i.push(t.id));return setTimeout(function(){for(var n in i)u.refresh(i[n])},15),r},disable:function(){for(var u=this,r=0,i=[],t,n=0;n<arguments.length;n++)(t=this.get(arguments[n]),t)&&(r++,t.disabled=!0,i.push(t.id));return setTimeout(function(){for(var n in i)u.refresh(i[n])},15),r},check:function(){for(var u=this,r=0,i=[],t,n=0;n<arguments.length;n++)(t=this.get(arguments[n]),t)&&(r++,t.checked=!0,i.push(t.id));return setTimeout(function(){for(var n in i)u.refresh(i[n])},15),r},uncheck:function(){for(var u=this,r=0,i=[],t,n=0;n<arguments.length;n++)(t=this.get(arguments[n]),t)&&(r++,t.checked=!1,i.push(t.id));return setTimeout(function(){for(var n in i)u.refresh(i[n])},15),r},render:function(n){var f=(new Date).getTime(),u=this.trigger({phase:"before",type:"render",target:this.name,box:n}),i,r,t;if(u.isCancelled!==!0&&(n!=null&&($(this.box).find("> table #tb_"+this.name+"_right").length>0&&$(this.box).removeAttr("name").removeClass("w2ui-reset w2ui-toolbar").html(""),this.box=n),this.box)){for(i='<table cellspacing="0" cellpadding="0" width="100%"><tr>',r=0;r<this.items.length;r++)(t=this.items[r],t.id==null&&(t.id="item_"+r),t!==null)&&(i+=t.type==="spacer"?'<td width="100%" id="tb_'+this.name+"_item_"+t.id+'" align="right"><\/td>':'<td id="tb_'+this.name+"_item_"+t.id+'" style="'+(t.hidden?"display: none":"")+'"     class="'+(t.disabled?"disabled":"")+'" valign="middle">'+this.getItemHTML(t)+"<\/td>");return i+='<td width="100%" id="tb_'+this.name+'_right" align="right">'+this.right+"<\/td>",i+="<\/tr><\/table>",$(this.box).attr("name",this.name).addClass("w2ui-reset w2ui-toolbar").html(i),$(this.box).length>0&&($(this.box)[0].style.cssText+=this.style),this.trigger($.extend(u,{phase:"after"})),(new Date).getTime()-f}},refresh:function(n){var o=(new Date).getTime(),e=this.trigger({phase:"before",type:"refresh",target:typeof n!="undefined"?n:this.name,item:this.get(n)}),u,f,t,i,r;if(e.isCancelled!==!0){if(n==null)for(u=0;u<this.items.length;u++)f=this.items[u],f.id==null&&(f.id="item_"+u),this.refresh(f.id);return(t=this.get(n),t===null)?!1:(i=$(this.box).find("#tb_"+this.name+"_item_"+w2utils.escapeId(t.id)),r=this.getItemHTML(t),i.length===0?(r=t.type==="spacer"?'<td width="100%" id="tb_'+this.name+"_item_"+t.id+'" align="right"><\/td>':'<td id="tb_'+this.name+"_item_"+t.id+'" style="'+(t.hidden?"display: none":"")+'"     class="'+(t.disabled?"disabled":"")+'" valign="middle">'+r+"<\/td>",this.get(n,!0)===this.items.length-1?$(this.box).find("#tb_"+this.name+"_right").before(r):$(this.box).find("#tb_"+this.name+"_item_"+w2utils.escapeId(this.items[parseInt(this.get(n,!0))+1].id)).before(r)):(i.html(r),t.hidden?i.css("display","none"):i.css("display",""),t.disabled?i.addClass("disabled"):i.removeClass("disabled")),this.trigger($.extend(e,{phase:"after"})),(new Date).getTime()-o)}},resize:function(){var t=(new Date).getTime(),n=this.trigger({phase:"before",type:"resize",target:this.name});if(n.isCancelled!==!0)return this.trigger($.extend(n,{phase:"after"})),(new Date).getTime()-t},destroy:function(){var n=this.trigger({phase:"before",type:"destroy",target:this.name});n.isCancelled!==!0&&($(this.box).find("> table #tb_"+this.name+"_right").length>0&&$(this.box).removeAttr("name").removeClass("w2ui-reset w2ui-toolbar").html(""),$(this.box).html(""),delete w2ui[this.name],this.trigger($.extend(n,{phase:"after"})))},getItemHTML:function(n){var t="",r,i;typeof n.caption!="undefined"&&(n.text=n.caption),typeof n.hint=="undefined"&&(n.hint=""),typeof n.text=="undefined"&&(n.text="");switch(n.type){case"menu":case"button":case"check":case"radio":case"drop":r="<td>&nbsp;<\/td>",n.img&&(r='<td><div class="w2ui-tb-image w2ui-icon '+n.img+'"><\/div><\/td>'),n.icon&&(r='<td><div class="w2ui-tb-image"><span class="'+n.icon+'"><\/span><\/div><\/td>'),t+='<table cellpadding="0" cellspacing="0" title="'+n.hint+'" class="w2ui-button '+(n.checked?"checked":"")+'"        onclick     = "var el=w2ui[\''+this.name+"']; if (el) el.click('"+n.id+'\', event);"        onmouseover = "'+(n.disabled?"":"$(this).addClass('over');")+'"       onmouseout  = "'+(n.disabled?"":"$(this).removeClass('over').removeClass('down');")+'"       onmousedown = "'+(n.disabled?"":"$(this).addClass('down');")+'"       onmouseup   = "'+(n.disabled?"":"$(this).removeClass('down');")+'"><tr><td>  <table cellpadding="1" cellspacing="0">  <tr>'+r+(n.text!==""?'<td class="w2ui-tb-caption" nowrap>'+n.text+"<\/td>":"")+(n.count!=null?'<td class="w2ui-tb-count" nowrap><span>'+n.count+"<\/span><\/td>":"")+((n.type==="drop"||n.type==="menu")&&n.arrow!==!1?'<td class="w2ui-tb-down" nowrap><div><\/div><\/td>':"")+"  <\/tr><\/table><\/td><\/tr><\/table>";break;case"break":t+='<table cellpadding="0" cellspacing="0"><tr>    <td><div class="w2ui-break">&nbsp;<\/div><\/td><\/tr><\/table>';break;case"html":t+='<table cellpadding="0" cellspacing="0"><tr>    <td nowrap>'+n.html+"<\/td><\/tr><\/table>"}return i="",typeof n.onRender=="function"&&(i=n.onRender.call(this,n.id,t)),typeof this.onRender=="function"&&(i=this.onRender(n.id,t)),i!==""&&i!=null&&(t=i),t},menuClick:function(n){var e=this,u,f,r,t,i;if(n.item&&!n.item.disabled){if(u=this.trigger({phase:"before",type:"click",target:n.item.id+":"+n.subItem.id,item:n.item,subItem:n.subItem,originalEvent:n.originalEvent}),u.isCancelled===!0)return;if(f=n.subItem,f.route){if(r=String("/"+f.route).replace(/\/{2,}/g,"/"),t=w2utils.parseRoute(r),t.keys.length>0)for(i=0;i<t.keys.length;i++)e.routeData[t.keys[i].name]!=null&&(r=r.replace(new RegExp(":"+t.keys[i].name,"g"),this.routeData[t.keys[i].name]));setTimeout(function(){window.location.hash=r},1)}this.trigger($.extend(u,{phase:"after"}))}},click:function(n,t){var c=this,i=this.get(n),h,u,s,r,f,e,o;if(i&&!i.disabled){if(h=this.trigger({phase:"before",type:"click",target:typeof n!="undefined"?n:this.name,item:i,object:i,originalEvent:t}),h.isCancelled===!0)return;if(u=$("#tb_"+this.name+"_item_"+w2utils.escapeId(i.id)+" table.w2ui-button"),u.removeClass("down"),i.type==="radio"){for(s=0;s<this.items.length;s++)(r=this.items[s],r!=null&&r.id!==i.id&&r.type==="radio")&&r.group===i.group&&r.checked&&(r.checked=!1,this.refresh(r.id));i.checked=!0,u.addClass("checked")}if((i.type==="drop"||i.type==="menu")&&(i.checked?i.checked=!1:setTimeout(function(){function r(){$(document).off("click",r),i.checked=!1,u.removeClass("checked")}var t=$("#tb_"+c.name+"_item_"+w2utils.escapeId(i.id)),n;$.isPlainObject(i.overlay)||(i.overlay={}),n=(t.width()-50)/2,n>19&&(n=19),i.type==="drop"&&t.w2overlay(i.html,$.extend({left:n,top:3},i.overlay)),i.type==="menu"&&t.w2menu(i.items,$.extend({left:n,top:3},i.overlay,{select:function(n){c.menuClick({item:i,subItem:n.item,originalEvent:n.originalEvent}),r()}}));$(document).on("click",r)},1)),(i.type==="check"||i.type==="drop"||i.type==="menu")&&(i.checked=!i.checked,i.checked?u.addClass("checked"):u.removeClass("checked")),i.route){if(f=String("/"+i.route).replace(/\/{2,}/g,"/"),e=w2utils.parseRoute(f),e.keys.length>0)for(o=0;o<e.keys.length;o++)f=f.replace(new RegExp(":"+e.keys[o].name,"g"),this.routeData[e.keys[o].name]);setTimeout(function(){window.location.hash=f},1)}this.trigger($.extend(h,{phase:"after"}))}}},$.extend(n.prototype,w2utils.event),w2obj.toolbar=n}(),function(){var n=function(n){this.name=null,this.box=null,this.sidebar=null,this.parent=null,this.nodes=[],this.menu=[],this.routeData={},this.selected=null,this.img=null,this.icon=null,this.style="",this.topHTML="",this.bottomHTML="",this.keyboard=!0,this.onClick=null,this.onDblClick=null,this.onContextMenu=null,this.onMenuClick=null,this.onExpand=null,this.onCollapse=null,this.onKeydown=null,this.onRender=null,this.onRefresh=null,this.onResize=null,this.onDestroy=null,$.extend(!0,this,w2obj.sidebar,n)};$.fn.w2sidebar=function(t){var r,i,u;if(typeof t!="object"&&t){if(w2ui[$(this).attr("name")])return u=w2ui[$(this).attr("name")],u[t].apply(u,Array.prototype.slice.call(arguments,1)),this;console.log("ERROR: Method "+t+" does not exist on jQuery.w2sidebar")}else return w2utils.checkName(t,"w2sidebar")?(r=t.nodes,i=new n(t),$.extend(i,{handlers:[],nodes:[]}),typeof r!="undefined"&&i.add(i,r),$(this).length!==0&&i.render($(this)[0]),i.sidebar=i,w2ui[i.name]=i,i):void 0},n.prototype={node:{id:null,text:"",count:null,img:null,icon:null,nodes:[],style:"",route:null,selected:!1,expanded:!1,hidden:!1,disabled:!1,group:!1,groupShowHide:!0,plus:!1,onClick:null,onDblClick:null,onContextMenu:null,onExpand:null,onCollapse:null,parent:null,sidebar:null},add:function(n,t){return arguments.length==1&&(t=arguments[0],n=this),typeof n=="string"&&(n=this.get(n)),this.insert(n,null,t)},insert:function(t,i,r){var e,o,f,u,s,h;if(arguments.length==2){if(r=arguments[1],i=arguments[0],o=this.get(i),o===null)return $.isArray(r)||(r=[r]),e=r[0].caption!=null?r[0].caption:r[0].text,console.log('ERROR: Cannot insert node "'+e+'" because cannot find node "'+i+'" to insert before.'),null;t=this.get(i).parent}typeof t=="string"&&(t=this.get(t)),$.isArray(r)||(r=[r]);for(h in r){if(u=r[h],typeof u.id==null){e=u.caption!=null?u.caption:u.text,console.log('ERROR: Cannot insert node "'+e+'" because it has no id.');continue}if(this.get(this,u.id)!==null){e=u.caption!=null?u.caption:u.text,console.log("ERROR: Cannot insert node with id="+u.id+" (text: "+e+") because another node with the same id already exists.");continue}if(f=$.extend({},n.prototype.node,u),f.sidebar=this,f.parent=t,s=f.nodes||[],f.nodes=[],i===null)t.nodes.push(f);else{if(o=this.get(t,i,!0),o===null)return e=u.caption!=null?u.caption:u.text,console.log('ERROR: Cannot insert node "'+e+'" because cannot find node "'+i+'" to insert before.'),null;t.nodes.splice(o,0,f)}s.length>0&&this.insert(f,null,s)}return this.refresh(t.id),f},remove:function(){for(var r=0,n,i,t=0;t<arguments.length;t++)(n=this.get(arguments[t]),n!==null)&&(this.selected!==null&&this.selected===n.id&&(this.selected=null),i=this.get(n.parent,arguments[t],!0),i!==null)&&(n.parent.nodes[i].selected&&n.sidebar.unselect(n.id),n.parent.nodes.splice(i,1),r++);return r>0&&arguments.length==1?this.refresh(n.parent.id):this.refresh(),r},set:function(n,t,i){var r,u,f;if(arguments.length==2&&(i=t,t=n,n=this),typeof n=="string"&&(n=this.get(n)),n.nodes==null)return null;for(r=0;r<n.nodes.length;r++){if(n.nodes[r].id===t)return u=i.nodes,$.extend(n.nodes[r],i,{nodes:[]}),u!=null&&this.add(n.nodes[r],u),this.refresh(t),!0;if(f=this.set(n.nodes[r],t,i),f)return!0}return!1},get:function(n,t,i){var o,f,u,r,e;if(arguments.length===0){for(o=[],f=this.find({}),u=0;u<f.length;u++)f[u].id!=null&&o.push(f[u].id);return o}if((arguments.length==1||arguments.length==2&&t===!0)&&(i=t,t=n,n=this),typeof n=="string"&&(n=this.get(n)),n.nodes==null)return null;for(r=0;r<n.nodes.length;r++){if(n.nodes[r].id==t)return i===!0?r:n.nodes[r];if(e=this.get(n.nodes[r],t,i),e||e===0)return e}return null},find:function(n,t,i){var r,u,f;if(arguments.length==1&&(t=n,n=this),i||(i=[]),typeof n=="string"&&(n=this.get(n)),n.nodes==null)return i;for(r=0;r<n.nodes.length;r++){u=!0;for(f in t)n.nodes[r][f]!=t[f]&&(u=!1);u&&i.push(n.nodes[r]),n.nodes[r].nodes.length>0&&(i=this.find(n.nodes[r],t,i))}return i},hide:function(){for(var i=0,t,n=0;n<arguments.length;n++)(t=this.get(arguments[n]),t!==null)&&(t.hidden=!0,i++);return arguments.length==1?this.refresh(arguments[0]):this.refresh(),i},show:function(){for(var i=0,t,n=0;n<arguments.length;n++)(t=this.get(arguments[n]),t!==null)&&(t.hidden=!1,i++);return arguments.length==1?this.refresh(arguments[0]):this.refresh(),i},disable:function(){for(var i=0,n,t=0;t<arguments.length;t++)(n=this.get(arguments[t]),n!==null)&&(n.disabled=!0,n.selected&&this.unselect(n.id),i++);return arguments.length==1?this.refresh(arguments[0]):this.refresh(),i},enable:function(){for(var i=0,t,n=0;n<arguments.length;n++)(t=this.get(arguments[n]),t!==null)&&(t.disabled=!1,i++);return arguments.length==1?this.refresh(arguments[0]):this.refresh(),i},select:function(n){var t=this.get(n);return t?this.selected==n&&t.selected?!1:(this.unselect(this.selected),$(this.box).find("#node_"+w2utils.escapeId(n)).addClass("w2ui-selected").find(".w2ui-icon").addClass("w2ui-icon-selected"),t.selected=!0,this.selected=n,!0):!1},unselect:function(n){var t=this.get(n);return t?(t.selected=!1,$(this.box).find("#node_"+w2utils.escapeId(n)).removeClass("w2ui-selected").find(".w2ui-icon").removeClass("w2ui-icon-selected"),this.selected==n&&(this.selected=null),!0):!1},toggle:function(n){var t=this.get(n);if(t===null)return!1;if(t.plus){this.set(n,{plus:!1}),this.expand(n),this.refresh(n);return}return t.nodes.length===0?!1:this.get(n).expanded?this.collapse(n):this.expand(n)},collapse:function(n){var r=this,t=this.get(n),i=this.trigger({phase:"before",type:"collapse",target:n,object:t});if(i.isCancelled!==!0)return $(this.box).find("#node_"+w2utils.escapeId(n)+"_sub").slideUp(200),$(this.box).find("#node_"+w2utils.escapeId(n)+" .w2ui-node-dots:first-child").html('<div class="w2ui-expand">+<\/div>'),t.expanded=!1,this.trigger($.extend(i,{phase:"after"})),setTimeout(function(){r.refresh(n)},200),!0},collapseAll:function(n){if(typeof n=="undefined"&&(n=this),typeof n=="string"&&(n=this.get(n)),n.nodes==null)return!1;for(var t=0;t<n.nodes.length;t++)n.nodes[t].expanded===!0&&(n.nodes[t].expanded=!1),n.nodes[t].nodes&&n.nodes[t].nodes.length>0&&this.collapseAll(n.nodes[t]);return this.refresh(n.id),!0},expand:function(n){var r=this,t=this.get(n),i=this.trigger({phase:"before",type:"expand",target:n,object:t});if(i.isCancelled!==!0)return $(this.box).find("#node_"+w2utils.escapeId(n)+"_sub").slideDown(1),$(this.box).find("#node_"+w2utils.escapeId(n)+" .w2ui-node-dots:first-child").html('<div class="w2ui-expand">-<\/div>'),t.expanded=!0,this.trigger($.extend(i,{phase:"after"})),setTimeout(function(){r.refresh(n)},1444),!0},expandAll:function(n){if(typeof n=="undefined"&&(n=this),typeof n=="string"&&(n=this.get(n)),n.nodes==null)return!1;for(var t=0;t<n.nodes.length;t++)n.nodes[t].expanded===!1&&(n.nodes[t].expanded=!0),n.nodes[t].nodes&&n.nodes[t].nodes.length>0&&this.collapseAll(n.nodes[t]);this.refresh(n.id)},expandParents:function(n){var t=this.get(n);return t===null?!1:(t.parent&&(t.parent.expanded=!0,this.expandParents(t.parent.id)),this.refresh(n),!0)},click:function(n,t){var i=this,r=this.get(n),f,u;r!==null&&(r.disabled||r.group||($(i.box).find(".w2ui-node.w2ui-selected").each(function(n,t){var u=$(t).attr("id").replace("node_",""),r=i.get(u);r!=null&&(r.selected=!1),$(t).removeClass("w2ui-selected").find(".w2ui-icon").removeClass("w2ui-icon-selected")}),f=$(i.box).find("#node_"+w2utils.escapeId(n)),u=$(i.box).find("#node_"+w2utils.escapeId(i.selected)),f.addClass("w2ui-selected").find(".w2ui-icon").addClass("w2ui-icon-selected"),setTimeout(function(){var h=i.trigger({phase:"before",type:"click",target:n,originalEvent:t,node:r,object:r}),s,e,o;if(h.isCancelled===!0){f.removeClass("w2ui-selected").find(".w2ui-icon").removeClass("w2ui-icon-selected"),u.addClass("w2ui-selected").find(".w2ui-icon").addClass("w2ui-icon-selected");return}if(u!==null&&(u.selected=!1),i.get(n).selected=!0,i.selected=n,r.route){if(s=String("/"+r.route).replace(/\/{2,}/g,"/"),e=w2utils.parseRoute(s),e.keys.length>0)for(o=0;o<e.keys.length;o++)i.routeData[e.keys[o].name]!=null&&(s=s.replace(new RegExp(":"+e.keys[o].name,"g"),i.routeData[e.keys[o].name]));setTimeout(function(){window.location.hash=s},1)}i.trigger($.extend(h,{phase:"after"}))},1)))},keydown:function(n){function f(n,i){n===null||n.hidden||n.disabled||n.group||(t.click(n.id,i),setTimeout(function(){t.scrollIntoView()},50))}function o(n,t){for(n=t(n);n!==null&&(n.hidden||n.disabled);)if(n.group)break;else n=t(n);return n}function r(n,i){var f;if(n===null)return null;var e=n.parent,o=t.get(n.id,!0),u=null;return n.expanded&&n.nodes.length>0&&i!==!0?(f=n.nodes[0],u=f.hidden||f.disabled||f.group?r(f):f):u=e&&o+1<e.nodes.length?e.nodes[o+1]:r(e,!0),u!==null&&(u.hidden||u.disabled||u.group)&&(u=r(u)),u}function e(n){if(n===null)return null;var r=n.parent,u=t.get(n.id,!0),i=u>0?s(r.nodes[u-1]):r;return i!==null&&(i.hidden||i.disabled||i.group)&&(i=e(i)),i}function s(n){if(n.expanded&&n.nodes.length>0){var t=n.nodes[n.nodes.length-1];return t.hidden||t.disabled||t.group?e(t):s(t)}return n}var t=this,i=t.get(t.selected),u;i&&t.keyboard===!0&&(u=t.trigger({phase:"before",type:"keydown",target:t.name,originalEvent:n}),u.isCancelled!==!0)&&((n.keyCode==13||n.keyCode==32)&&i.nodes.length>0&&t.toggle(t.selected),n.keyCode==37&&(i.nodes.length>0&&i.expanded?t.collapse(t.selected):(f(i.parent),i.parent.group||t.collapse(i.parent.id))),n.keyCode==39&&(i.nodes.length>0||i.plus)&&!i.expanded&&t.expand(t.selected),n.keyCode==38&&f(o(i,e)),n.keyCode==40&&f(o(i,r)),$.inArray(n.keyCode,[13,32,37,38,39,40])!=-1&&(n.preventDefault&&n.preventDefault(),n.stopPropagation&&n.stopPropagation()),t.trigger($.extend(u,{phase:"after"})))},scrollIntoView:function(n){var i;if(typeof n=="undefined"&&(n=this.selected),i=this.get(n),i!==null){var t=$(this.box).find(".w2ui-sidebar-div"),r=$(this.box).find("#node_"+w2utils.escapeId(n)),u=r.offset().top-t.offset().top;u+r.height()>t.height()&&t.animate({scrollTop:t.scrollTop()+t.height()/1.3},250,"linear"),u<=0&&t.animate({scrollTop:t.scrollTop()-t.height()/1.3},250,"linear")}},dblClick:function(n,t){var r=this.get(n),i=this.trigger({phase:"before",type:"dblClick",target:n,originalEvent:t,object:r});i.isCancelled!==!0&&(this.toggle(n),this.trigger($.extend(i,{phase:"after"})))},contextMenu:function(n,t){var i=this,r=i.get(n);n!=i.selected&&i.click(n),setTimeout(function(){var u=i.trigger({phase:"before",type:"contextMenu",target:n,originalEvent:t,object:r});u.isCancelled!==!0&&(r.group||r.disabled||(i.menu.length>0&&$(i.box).find("#node_"+w2utils.escapeId(n)).w2menu(i.menu,{left:(t?t.offsetX||t.pageX:50)-25,onSelect:function(t){i.menuClick(n,parseInt(t.index),t.originalEvent)}}),i.trigger($.extend(u,{phase:"after"}))))},150)},menuClick:function(n,t,i){var r=this,u=r.trigger({phase:"before",type:"menuClick",target:n,originalEvent:i,menuIndex:t,menuItem:r.menu[t]});u.isCancelled!==!0&&r.trigger($.extend(u,{phase:"after"}))},render:function(n){var i=(new Date).getTime(),t=this.trigger({phase:"before",type:"render",target:this.name,box:n});if(t.isCancelled!==!0)return(typeof n!="undefined"&&n!==null&&($(this.box).find("> div > div.w2ui-sidebar-div").length>0&&$(this.box).removeAttr("name").removeClass("w2ui-reset w2ui-sidebar").html(""),this.box=n),!this.box)?void 0:($(this.box).attr("name",this.name).addClass("w2ui-reset w2ui-sidebar").html('<div><div class="w2ui-sidebar-top"><\/div><div class="w2ui-sidebar-div"><\/div><div class="w2ui-sidebar-bottom"><\/div><\/div>'),$(this.box).find("> div").css({width:$(this.box).width()+"px",height:$(this.box).height()+"px"}),$(this.box).length>0&&($(this.box)[0].style.cssText+=this.style),this.topHTML!==""&&($(this.box).find(".w2ui-sidebar-top").html(this.topHTML),$(this.box).find(".w2ui-sidebar-div").css("top",$(this.box).find(".w2ui-sidebar-top").height()+"px")),this.bottomHTML!==""&&($(this.box).find(".w2ui-sidebar-bottom").html(this.bottomHTML),$(this.box).find(".w2ui-sidebar-div").css("bottom",$(this.box).find(".w2ui-sidebar-bottom").height()+"px")),this.trigger($.extend(t,{phase:"after"})),this.refresh(),(new Date).getTime()-i)},refresh:function(n){function h(n){var e="",f=n.img,r,t,u;for(f===null&&(f=this.img),r=n.icon,r===null&&(r=this.icon),t=n.parent,u=0;t&&t.parent!==null;)t.group&&u--,t=t.parent,u++;return typeof n.caption!="undefined"&&(n.text=n.caption),n.group?e='<div class="w2ui-node-group"  id="node_'+n.id+'"        onclick="w2ui[\''+i.name+"'].toggle('"+n.id+"')\"        onmouseout=\"$(this).find('span:nth-child(1)').css('color', 'transparent')\"         onmouseover=\"$(this).find('span:nth-child(1)').css('color', 'inherit')\">"+(n.groupShowHide?"<span>"+(!n.hidden&&n.expanded?w2utils.lang("Hide"):w2utils.lang("Show"))+"<\/span>":"<span><\/span>")+"    <span>"+n.text+'<\/span><\/div><div class="w2ui-node-sub" id="node_'+n.id+'_sub" style="'+n.style+";"+(!n.hidden&&n.expanded?"":"display: none;")+'"><\/div>':(n.selected&&!n.disabled&&(i.selected=n.id),t="",f&&(t='<div class="w2ui-node-image w2ui-icon '+f+(n.selected&&!n.disabled?" w2ui-icon-selected":"")+'"><\/div>'),r&&(t='<div class="w2ui-node-image"><span class="'+r+'"><\/span><\/div>'),e='<div class="w2ui-node '+(n.selected?"w2ui-selected":"")+" "+(n.disabled?"w2ui-disabled":"")+'" id="node_'+n.id+'" style="'+(n.hidden?"display: none;":"")+'"    ondblclick="w2ui[\''+i.name+"'].dblClick('"+n.id+"', event);\"    oncontextmenu=\"w2ui['"+i.name+"'].contextMenu('"+n.id+"', event);         if (event.preventDefault) event.preventDefault();\"    onClick=\"w2ui['"+i.name+"'].click('"+n.id+'\', event); "><table cellpadding="0" cellspacing="0" style="margin-left:'+u*18+"px; padding-right:"+u*18+'px"><tr><td class="w2ui-node-dots" nowrap onclick="w2ui[\''+i.name+"'].toggle('"+n.id+'\');         if (event.stopPropagation) event.stopPropagation(); else event.cancelBubble = true;">    <div class="w2ui-expand">'+(n.nodes.length>0?n.expanded?"-":"+":n.plus?"+":"")+'<\/div><\/td><td class="w2ui-node-data" nowrap>'+t+(n.count||n.count===0?'<div class="w2ui-node-count">'+n.count+"<\/div>":"")+'<div class="w2ui-node-caption">'+n.text+'<\/div><\/td><\/tr><\/table><\/div><div class="w2ui-node-sub" id="node_'+n.id+'_sub" style="'+n.style+";"+(!n.hidden&&n.expanded?"":"display: none;")+'"><\/div>'),e}var c=(new Date).getTime(),s=this.trigger({phase:"before",type:"refresh",target:typeof n!="undefined"?n:this.name}),i,t,u,r,f,o,e;if(s.isCancelled!==!0){if(this.topHTML!==""&&($(this.box).find(".w2ui-sidebar-top").html(this.topHTML),$(this.box).find(".w2ui-sidebar-div").css("top",$(this.box).find(".w2ui-sidebar-top").height()+"px")),this.bottomHTML!==""&&($(this.box).find(".w2ui-sidebar-bottom").html(this.bottomHTML),$(this.box).find(".w2ui-sidebar-div").css("bottom",$(this.box).find(".w2ui-sidebar-bottom").height()+"px")),$(this.box).find("> div").css({width:$(this.box).width()+"px",height:$(this.box).height()+"px"}),i=this,typeof n=="undefined")t=this,r=".w2ui-sidebar-div";else{if(t=this.get(n),t===null)return;r="#node_"+w2utils.escapeId(t.id)+"_sub"}for(t!==this&&(o="#node_"+w2utils.escapeId(t.id),f=h(t),$(this.box).find(o).before('<div id="sidebar_'+this.name+'_tmp"><\/div>'),$(this.box).find(o).remove(),$(this.box).find(r).remove(),$("#sidebar_"+this.name+"_tmp").before(f),$("#sidebar_"+this.name+"_tmp").remove()),$(this.box).find(r).html(""),e=0;e<t.nodes.length;e++)u=t.nodes[e],f=h(u),$(this.box).find(r).append(f),u.nodes.length!==0&&this.refresh(u.id);return this.trigger($.extend(s,{phase:"after"})),(new Date).getTime()-c}},resize:function(){var t=(new Date).getTime(),n=this.trigger({phase:"before",type:"resize",target:this.name});if(n.isCancelled!==!0)return $(this.box).css("overflow","hidden"),$(this.box).find("> div").css({width:$(this.box).width()+"px",height:$(this.box).height()+"px"}),this.trigger($.extend(n,{phase:"after"})),(new Date).getTime()-t},destroy:function(){var n=this.trigger({phase:"before",type:"destroy",target:this.name});n.isCancelled!==!0&&($(this.box).find("> div > div.w2ui-sidebar-div").length>0&&$(this.box).removeAttr("name").removeClass("w2ui-reset w2ui-sidebar").html(""),delete w2ui[this.name],this.trigger($.extend(n,{phase:"after"})))},lock:function(){var t=$(this.box).find("> div:first-child"),n=Array.prototype.slice.call(arguments,0);n.unshift(t),w2utils.lock.apply(window,n)},unlock:function(){w2utils.unlock(this.box)}},$.extend(n.prototype,w2utils.event),w2obj.sidebar=n}(),function(n){var t=function(t){this.el=null,this.helpers={},this.type=t.type||"text",this.options=n.extend(!0,{},t),this.onSearch=t.onSearch||null,this.onRequest=t.onRequest||null,this.onLoad=t.onLoad||null,this.onError=t.onError||null,this.onClick=t.onClick||null,this.onAdd=t.onAdd||null,this.onNew=t.onNew||null,this.onRemove=t.onRemove||null,this.onMouseOver=t.onMouseOver||null,this.onMouseOut=t.onMouseOut||null,this.onIconClick=t.onIconClick||null,this.tmp={},delete this.options.type,delete this.options.onSearch,delete this.options.onRequest,delete this.options.onLoad,delete this.options.onError,delete this.options.onClick,delete this.options.onMouseOver,delete this.options.onMouseOut,delete this.options.onIconClick,n.extend(!0,this,w2obj.field)};n.fn.w2field=function(i,r){if(this.length==0){var u=t.prototype;if(u[i])return u[i].apply(u,Array.prototype.slice.call(arguments,1))}else return typeof i=="string"&&typeof r=="object"&&(i=n.extend(!0,{},r,{type:i})),typeof i=="string"&&typeof r=="undefined"&&(i={type:i}),i.type=String(i.type).toLowerCase(),this.each(function(r,u){var f=n(u).data("w2field");return typeof f=="undefined"?(f=new t(i),n.extend(f,{handlers:[]}),u&&(f.el=n(u)[0]),f.init(),n(u).data("w2field",f),f):(f.clear(),i.type=="clear")?void 0:(f=new t(i),n.extend(f,{handlers:[]}),u&&(f.el=n(u)[0]),f.init(),n(u).data("w2field",f),f)})},t.prototype={custom:{},pallete:[["000000","444444","666666","999999","CCCCCC","EEEEEE","F3F3F3","FFFFFF"],["FF011B","FF9838","FFFD59","01FD55","00FFFE","0424F3","9B24F4","FF21F5"],["F4CCCC","FCE5CD","FFF2CC","D9EAD3","D0E0E3","CFE2F3","D9D1E9","EAD1DC"],["EA9899","F9CB9C","FEE599","B6D7A8","A2C4C9","9FC5E8","B4A7D6","D5A6BD"],["E06666","F6B26B","FED966","93C47D","76A5AF","6FA8DC","8E7CC3","C27BA0"],["CC0814","E69138","F1C232","6AA84F","45818E","3D85C6","674EA7","A54D79"],["99050C","B45F17","BF901F","37761D","124F5C","0A5394","351C75","741B47"],["660205","783F0B","7F6011","274E12","0C343D","063762","20124D","4C1030"]],addType:function(n,t){return n=String(n).toLowerCase(),this.custom[n]=t,!0},removeType:function(n){return(n=String(n).toLowerCase(),!this.custom[n])?!1:(delete this.custom[n],!0)},init:function(){var i=this,t=this.options,r,f,u;if(typeof this.custom[this.type]=="function"){this.custom[this.type].call(this,t);return}if(["INPUT","TEXTAREA"].indexOf(this.el.tagName)==-1){console.log("ERROR: w2field could only be applied to INPUT or TEXTAREA.",this.el);return}switch(this.type){case"text":case"int":case"float":case"money":case"currency":case"percent":case"alphanumeric":case"hex":r={min:null,max:null,step:1,placeholder:"",autoFormat:!0,currencyPrefix:w2utils.settings.currencyPrefix,currencySuffix:w2utils.settings.currencySuffix,currencyPrecision:w2utils.settings.currencyPrecision,groupSymbol:w2utils.settings.groupSymbol,arrows:!1,keyboard:!0,precision:null,silent:!0,prefix:"",suffix:""},this.options=n.extend(!0,{},r,t),t=this.options,t.numberRE=new RegExp("["+t.groupSymbol+"]","g"),t.moneyRE=new RegExp("["+t.currencyPrefix+t.currencySuffix+t.groupSymbol+"]","g"),t.percentRE=new RegExp("["+t.groupSymbol+"%]","g"),["text","alphanumeric","hex"].indexOf(this.type)!=-1&&(t.arrows=!1,t.keyboard=!1),this.addPrefix(),this.addSuffix(),n(this.el).attr("placeholder",t.placeholder);break;case"color":r={prefix:"#",suffix:'<div style="width: '+(parseInt(n(this.el).css("font-size"))||12)+'px">&nbsp;<\/div>',placeholder:"",arrows:!1,keyboard:!1},n.extend(t,r),this.addPrefix(),this.addSuffix(),n(this.el).attr("maxlength",6),n(this.el).val()!=""&&setTimeout(function(){n(i.el).change()},1),n(this.el).attr("placeholder",t.placeholder);break;case"date":r={format:w2utils.settings.date_format,placeholder:"",keyboard:!0,silent:!0,start:"",end:"",blocked:{},colored:{}},this.options=n.extend(!0,{},r,t),t=this.options,n(this.el).attr("placeholder",t.placeholder?t.placeholder:t.format);break;case"time":r={format:w2utils.settings.time_format,placeholder:"",keyboard:!0,silent:!0,start:"",end:""},this.options=n.extend(!0,{},r,t),t=this.options,n(this.el).attr("placeholder",t.placeholder?t.placeholder:t.format=="h12"?"hh:mi pm":"hh:mi");break;case"list":case"combo":if(r={items:[],selected:{},placeholder:"",url:null,postData:{},minLength:1,cacheMax:250,maxDropHeight:350,match:"begins",silent:!0,icon:null,iconStyle:"",onSearch:null,onRequest:null,onLoad:null,onError:null,onIconClick:null,renderDrop:null,prefix:"",suffix:"",openOnFocus:!1,markSearch:!1},t.items=this.normMenu(t.items),this.type=="list"&&(r.openOnFocus=!0,r.suffix='<div class="arrow-down" style="margin-top: '+(parseInt(n(this.el).height())-6)/2+'px;"><\/div>',n(this.el).addClass("w2ui-select"),!n.isPlainObject(t.selected)))for(f in t.items)if(u=t.items[f],u&&u.id==t.selected){t.selected=n.extend(!0,{},u);break}t=n.extend({},r,t,{align:"both",altRows:!0}),this.options=t,n.isPlainObject(t.selected)||(t.selected={}),n(this.el).data("selected",t.selected),t.url&&this.request(0),this.type=="list"&&this.addFocus(),this.addPrefix(),this.addSuffix(),setTimeout(function(){i.refresh()},10),n(this.el).attr("placeholder",t.placeholder).attr("autocomplete","off"),typeof t.selected.text!="undefined"&&n(this.el).val(t.selected.text);break;case"enum":r={items:[],selected:[],placeholder:"",max:0,url:null,postData:{},minLength:1,cacheMax:250,maxWidth:250,maxHeight:350,maxDropHeight:350,match:"contains",silent:!0,openOnFocus:!1,markSearch:!0,renderDrop:null,renderItem:null,style:"",onSearch:null,onRequest:null,onLoad:null,onError:null,onClick:null,onAdd:null,onNew:null,onRemove:null,onMouseOver:null,onMouseOut:null},t=n.extend({},r,t,{align:"both",suffix:"",altRows:!0}),t.items=this.normMenu(t.items),t.selected=this.normMenu(t.selected),this.options=t,n.isArray(t.selected)||(t.selected=[]),n(this.el).data("selected",t.selected),t.url&&this.request(0),this.addSuffix(),this.addMulti();break;case"file":r={selected:[],placeholder:w2utils.lang("Attach files by dragging and dropping or Click to Select"),max:0,maxSize:0,maxFileSize:0,maxWidth:250,maxHeight:350,maxDropHeight:350,silent:!0,renderItem:null,style:"",onClick:null,onAdd:null,onRemove:null,onMouseOver:null,onMouseOut:null},t=n.extend({},r,t,{align:"both",altRows:!0}),this.options=t,n.isArray(t.selected)||(t.selected=[]),n(this.el).data("selected",t.selected),this.addMulti()}this.tmp={onChange:function(n){i.change.call(i,n)},onClick:function(n){i.click.call(i,n)},onFocus:function(n){i.focus.call(i,n)},onBlur:function(n){i.blur.call(i,n)},onKeydown:function(n){i.keyDown.call(i,n)},onKeyup:function(n){i.keyUp.call(i,n)},onKeypress:function(n){i.keyPress.call(i,n)}},n(this.el).addClass("w2field").data("w2field",this).on("change",this.tmp.onChange).on("click",this.tmp.onClick).on("focus",this.tmp.onFocus).on("blur",this.tmp.onBlur).on("keydown",this.tmp.onKeydown).on("keyup",this.tmp.onKeyup).on("keypress",this.tmp.onKeypress).css({"box-sizing":"border-box","-webkit-box-sizing":"border-box","-moz-box-sizing":"border-box","-ms-box-sizing":"border-box","-o-box-sizing":"border-box"}),this.change(n.Event("change"))},clear:function(){var u=this,i=this.options,t,r;if(["money","currency"].indexOf(this.type)!=-1&&n(this.el).val(n(this.el).val().replace(i.moneyRE,"")),this.type=="percent"&&n(this.el).val(n(this.el).val().replace(/%/g,"")),this.type=="color"&&n(this.el).removeAttr("maxlength"),this.type=="list"&&n(this.el).removeClass("w2ui-select"),["date","time"].indexOf(this.type)!=-1&&n(this.el).attr("placeholder")==i.format&&n(this.el).attr("placeholder",""),this.type="clear",t=n(this.el).data("tmp"),this.tmp){typeof t!="undefined"&&(t&&t["old-padding-left"]&&n(this.el).css("padding-left",t["old-padding-left"]),t&&t["old-padding-right"]&&n(this.el).css("padding-right",t["old-padding-right"])),n(this.el).val(this.clean(n(this.el).val())).removeClass("w2field").removeData().off("change",this.tmp.onChange).off("click",this.tmp.onClick).off("focus",this.tmp.onFocus).off("blur",this.tmp.onBlur).off("keydown",this.tmp.onKeydown).off("keyup",this.tmp.onKeyup).off("keypress",this.tmp.onKeypress);for(r in this.helpers)n(this.helpers[r]).remove();this.helpers={}}},refresh:function(){var t=this,i=this.options,f=n(this.el).data("selected"),v=(new Date).getTime(),s,o,e,h,r,c,a,u,l;if(["list"].indexOf(this.type)!=-1&&(n(t.el).parent().css("white-space","nowrap"),t.helpers.prefix&&t.helpers.prefix.hide(),setTimeout(function(){if(t.helpers.focus){!n.isEmptyObject(f)&&i.icon?(i.prefix='<span class="w2ui-icon '+i.icon+'"style="cursor: pointer; font-size: 14px; display: inline-block; margin-top: -1px; color: #7F98AD;'+i.iconStyle+'"><\/span>',t.addPrefix()):(i.prefix="",t.addPrefix());var r=t.helpers.focus.find("input");n(r).val()==""?(n(r).css("opacity",0).prev().css("opacity",0),n(t.el).val(f&&f.text!=null?f.text:""),n(t.el).attr("placeholder",n(t.el).attr("_placeholder"))):(n(r).css("opacity",1).prev().css("opacity",1),n(t.el).val(""),n(t.el).attr("_placeholder",n(t.el).attr("placeholder")).removeAttr("placeholder"),setTimeout(function(){t.helpers.prefix&&t.helpers.prefix.hide();var u="position: absolute; opacity: 0; margin: 4px 0px 0px 2px; background-position: left !important;";i.icon?(n(r).css("margin-left","17px"),n(t.helpers.focus).find(".icon-search").attr("style",u+"width: 11px !important; opacity: 1")):(n(r).css("margin-left","0px"),n(t.helpers.focus).find(".icon-search").attr("style",u+"width: 0px !important; opacity: 0"))},1))}},1)),["enum","file"].indexOf(this.type)!=-1){s="";for(o in f)e=f[o],h="",h=typeof i.renderItem=="function"?i.renderItem(e,o,'<div class="w2ui-list-remove" title="'+w2utils.lang("Remove")+'" index="'+o+'">&nbsp;&nbsp;<\/div>'):'<div class="w2ui-list-remove" title="'+w2utils.lang("Remove")+'" index="'+o+'">&nbsp;&nbsp;<\/div>'+(t.type=="enum"?e.text:e.name+'<span class="file-size"> - '+w2utils.size(e.size)+"<\/span>"),s+='<li index="'+o+'" style="max-width: '+parseInt(i.maxWidth)+"px; "+(e.style?e.style:"")+'">'+h+"<\/li>";r=t.helpers.multi,c=r.find("ul"),r.attr("style",r.attr("style")+";"+i.style),n(t.el).attr("readonly")?r.addClass("w2ui-readonly"):r.removeClass("w2ui-readonly"),r.find(".w2ui-enum-placeholder").remove(),c.find("li").not("li.nomouse").remove(),s!=""?c.prepend(s):typeof i.placeholder!="undefined"&&(a="padding-top: "+n(this.el).css("padding-top")+";padding-left: "+n(this.el).css("padding-left")+"; box-sizing: "+n(this.el).css("box-sizing")+"; line-height: "+n(this.el).css("line-height")+"; font-size: "+n(this.el).css("font-size")+"; font-family: "+n(this.el).css("font-family")+"; ",r.prepend('<div class="w2ui-enum-placeholder" style="'+a+'">'+i.placeholder+"<\/div>"));r.find("li").data("mouse","out").on("click",function(i){var r=f[n(i.target).attr("index")],u,s,e,o;if(!n(i.target).hasClass("nomouse")&&(i.stopPropagation(),u=t.trigger({phase:"before",type:"click",target:t.el,originalEvent:i.originalEvent,item:r}),u.isCancelled!==!0)){if(n(i.target).hasClass("w2ui-list-remove")){if(n(t.el).attr("readonly"))return;if(u=t.trigger({phase:"before",type:"remove",target:t.el,originalEvent:i.originalEvent,item:r}),u.isCancelled===!0)return;n().w2overlay(),f.splice(n(i.target).attr("index"),1),n(t.el).trigger("change"),n(i.target).parent().fadeOut("fast"),setTimeout(function(){t.refresh(),t.trigger(n.extend(u,{phase:"after"}))},300)}t.type!="file"||n(i.target).hasClass("w2ui-list-remove")||(s="",/image/i.test(r.type)&&(s='<div style="padding: 3px;">    <img src="'+(r.content?"data:"+r.type+";base64,"+r.content:"")+'" style="max-width: 300px;"         onload="var w = $(this).width(); var h = $(this).height();             if (w < 300 & h < 300) return;             if (w >= h && w > 300) $(this).width(300);            if (w < h && h > 300) $(this).height(300);"        onerror="this.style.display = \'none\'"    ><\/div>'),e='style="padding: 3px; text-align: right; color: #777;"',o='style="padding: 3px"',s+='<div style="padding: 8px;">    <table cellpadding="2">    <tr><td '+e+">Name:<\/td><td "+o+">"+r.name+"<\/td><\/tr>    <tr><td "+e+">Size:<\/td><td "+o+">"+w2utils.size(r.size)+"<\/td><\/tr>    <tr><td "+e+">Type:<\/td><td "+o+'>        <span style="width: 200px; display: block-inline; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">'+r.type+"<\/span>    <\/td><\/tr>    <tr><td "+e+">Modified:<\/td><td "+o+">"+w2utils.date(r.modified)+"<\/td><\/tr>    <\/table><\/div>",n(i.target).w2overlay(s)),t.trigger(n.extend(u,{phase:"after"}))}}).on("mouseover",function(i){var r=i.target,e,u;if(r.tagName!="LI"&&(r=r.parentNode),!n(r).hasClass("nomouse")){if(n(r).data("mouse")=="out"){if(e=f[n(r).attr("index")],u=t.trigger({phase:"before",type:"mouseOver",target:t.el,originalEvent:i.originalEvent,item:e}),u.isCancelled===!0)return;t.trigger(n.extend(u,{phase:"after"}))}n(r).data("mouse","over")}}).on("mouseout",function(i){var r=i.target;(r.tagName!="LI"&&(r=r.parentNode),n(r).hasClass("nomouse"))||(n(r).data("mouse","leaving"),setTimeout(function(){if(n(r).data("mouse")=="leaving"){n(r).data("mouse","out");var e=f[n(r).attr("index")],u=t.trigger({phase:"before",type:"f",target:t.el,originalEvent:i.originalEvent,item:e});if(u.isCancelled===!0)return;t.trigger(n.extend(u,{phase:"after"}))}},0))});n(this.el).height("auto"),u=n(r).find("> div").height()+w2utils.getSize(r,"+height")*2,u<26&&(u=26),u>i.maxHeight&&(u=i.maxHeight),r.length>0&&(r[0].scrollTop=1e3),l=w2utils.getSize(n(this.el),"height")-2,l>u&&(u=l),n(r).css({height:u+"px",overflow:u==i.maxHeight?"auto":"hidden"}),u<i.maxHeight&&n(r).prop("scrollTop",0),n(this.el).css({height:u+2+"px"})}return(new Date).getTime()-v},reset:function(){var t=this,n=this.type;this.clear(),this.type=n,this.init()},clean:function(t){var i=this.options;return t=String(t).trim(),["int","float","money","currency","percent"].indexOf(this.type)!=-1&&(i.autoFormat&&["money","currency"].indexOf(this.type)!=-1&&(t=String(t).replace(i.moneyRE,"")),i.autoFormat&&this.type=="percent"&&(t=String(t).replace(i.percentRE,"")),i.autoFormat&&["int","float"].indexOf(this.type)!=-1&&(t=String(t).replace(i.numberRE,"")),parseFloat(t)==t&&(i.min!==null&&t<i.min&&(t=i.min,n(this.el).val(i.min)),i.max!==null&&t>i.max&&(t=i.max,n(this.el).val(i.max))),t=t!==""&&w2utils.isFloat(t)?Number(t):""),t},format:function(n){var t=this.options;if(t.autoFormat&&n!="")switch(this.type){case"money":case"currency":n=w2utils.formatNumber(Number(n).toFixed(t.currencyPrecision),t.groupSymbol),n!=""&&(n=t.currencyPrefix+n+t.currencySuffix);break;case"percent":n=w2utils.formatNumber(t.precision?Number(n).toFixed(t.precision):n,t.groupSymbol),n!=""&&(n+="%");break;case"float":n=w2utils.formatNumber(t.precision?Number(n).toFixed(t.precision):n,t.groupSymbol);break;case"int":n=w2utils.formatNumber(n,t.groupSymbol)}return n},change:function(t){var f=this,e=f.options,i,r,u;if(["int","float","money","currency","percent"].indexOf(this.type)!=-1&&(i=n(this.el).val(),r=this.format(this.clean(n(this.el).val())),i!=""&&i!=r))return n(this.el).val(r).change(),t.stopPropagation(),t.preventDefault(),!1;this.type=="color"&&(u="#"+n(this.el).val(),n(this.el).val().length!=6&&n(this.el).val().length!=3&&(u=""),n(this.el).next().find("div").css("background-color",u),n(f.el).is(":focus")&&this.updateOverlay())},click:function(t){t.stopPropagation(),["list","combo","enum"].indexOf(this.type)!=-1&&(n(this.el).is(":focus")||this.focus(t)),["date","time","color"].indexOf(this.type)!=-1&&this.updateOverlay()},focus:function(){var t=this,i=this.options;if(["color","date","time"].indexOf(t.type)!==-1){if(n(t.el).attr("readonly"))return;n("#w2ui-overlay").length>0&&n("#w2ui-overlay")[0].hide(),setTimeout(function(){t.updateOverlay()},150)}if(["list","combo","enum"].indexOf(t.type)!=-1){if(n(t.el).attr("readonly"))return;n("#w2ui-overlay").length>0&&n("#w2ui-overlay")[0].hide(),setTimeout(function(){if(t.type=="list"&&n(t.el).is(":focus")){n(t.helpers.focus).find("input").focus();return}t.search(),setTimeout(function(){t.updateOverlay()},1)},1)}t.type=="file"&&n(t.helpers.multi).css({outline:"auto 5px #7DB4F3","outline-offset":"-2px"})},blur:function(){var t=this,i=t.options,r=n(t.el).val().trim();["color","date","time","list","combo","enum"].indexOf(t.type)!=-1&&n("#w2ui-overlay").length>0&&n("#w2ui-overlay")[0].hide(),["int","float","money","currency","percent"].indexOf(t.type)!=-1&&(r===""||t.checkType(r)||(n(t.el).val("").change(),i.silent===!1&&(n(t.el).w2tag("Not a valid number"),setTimeout(function(){n(t.el).w2tag("")},3e3)))),["date","time"].indexOf(t.type)!=-1&&(w2utils.isInt(t.el.value)&&n(t.el).val(w2utils.formatDate(new Date(parseInt(t.el.value)),i.format)).change(),r===""||t.inRange(t.el.value)?(t.type!="date"||r===""||w2utils.isDate(t.el.value,i.format)||(n(t.el).val("").removeData("selected").change(),i.silent===!1&&(n(t.el).w2tag("Not a valid date"),setTimeout(function(){n(t.el).w2tag("")},3e3))),t.type!="time"||r===""||w2utils.isTime(t.el.value)||(n(t.el).val("").removeData("selected").change(),i.silent===!1&&(n(t.el).w2tag("Not a valid time"),setTimeout(function(){n(t.el).w2tag("")},3e3)))):(n(t.el).val("").removeData("selected").change(),i.silent===!1&&(n(t.el).w2tag("Not in range"),setTimeout(function(){n(t.el).w2tag("")},3e3)))),t.type=="enum"&&n(t.helpers.multi).find("input").val("").width(20),t.type=="file"&&n(t.helpers.multi).css({outline:"none"})},keyPress:function(n){var t=this,r=t.options,i;if(["int","float","money","currency","percent","hex","color","alphanumeric"].indexOf(t.type)!=-1){if(n.metaKey||n.ctrlKey||n.altKey||n.charCode!=n.keyCode&&n.keyCode>0)return;if(i=String.fromCharCode(n.charCode),!t.checkType(i,!0)&&n.keyCode!=13)return n.preventDefault(),n.stopPropagation?n.stopPropagation():n.cancelBubble=!0,!1}["date","time"].indexOf(t.type)!=-1&&setTimeout(function(){t.updateOverlay()},1)},keyDown:function(t,i){var r=this,u=r.options,l=t.keyCode||i&&i.keyCode,h,v,f,o,a,w,p,e,s,y,d;if(["int","float","money","currency","percent"].indexOf(r.type)!=-1){if(!u.keyboard||n(r.el).attr("readonly"))return;var f=!1,a=parseFloat(n(r.el).val().replace(u.moneyRE,""))||0,o=u.step;(t.ctrlKey||t.metaKey)&&(o=10);switch(l){case 38:if(t.shiftKey)break;n(r.el).val(a+o<=u.max||u.max===null?Number((a+o).toFixed(12)):u.max).change(),f=!0;break;case 40:if(t.shiftKey)break;n(r.el).val(a-o>=u.min||u.min===null?Number((a-o).toFixed(12)):u.min).change(),f=!0}f&&(t.preventDefault(),setTimeout(function(){r.el.setSelectionRange(r.el.value.length,r.el.value.length)},0))}if(r.type=="date"){if(!u.keyboard||n(r.el).attr("readonly"))return;var f=!1,k=864e5,o=1;(t.ctrlKey||t.metaKey)&&(o=10),h=w2utils.isDate(n(r.el).val(),u.format,!0),h||(h=new Date,k=0);switch(l){case 38:if(t.shiftKey)break;v=w2utils.formatDate(h.getTime()+k,u.format),o==10&&(v=w2utils.formatDate(new Date(h.getFullYear(),h.getMonth()+1,h.getDate()),u.format)),n(r.el).val(v).change(),f=!0;break;case 40:if(t.shiftKey)break;v=w2utils.formatDate(h.getTime()-k,u.format),o==10&&(v=w2utils.formatDate(new Date(h.getFullYear(),h.getMonth()-1,h.getDate()),u.format)),n(r.el).val(v).change(),f=!0}f&&(t.preventDefault(),setTimeout(function(){r.el.setSelectionRange(r.el.value.length,r.el.value.length),r.updateOverlay()},0))}if(r.type=="time"){if(!u.keyboard||n(r.el).attr("readonly"))return;f=!1,o=1,(t.ctrlKey||t.metaKey)&&(o=60),w2utils.isInt(r.el.value)&&n(r.el).val(w2utils.formatTime(new Date(parseInt(r.el.value)),u.format)).change(),a=n(r.el).val(),w=r.toMin(a)||r.toMin((new Date).getHours()+":"+((new Date).getMinutes()-1));switch(l){case 38:if(t.shiftKey)break;w+=o,f=!0;break;case 40:if(t.shiftKey)break;w-=o,f=!0}f&&(n(r.el).val(r.fromMin(w)).change(),t.preventDefault(),setTimeout(function(){r.el.setSelectionRange(r.el.value.length,r.el.value.length)},0))}if(r.type=="color"){if(n(r.el).attr("readonly"))return;if(t.keyCode==86&&(t.ctrlKey||t.metaKey)&&(n(r.el).prop("maxlength",7),setTimeout(function(){var t=n(r).val();t.substr(0,1)=="#"&&(t=t.substr(1)),w2utils.isHex(t)||(t=""),n(r).val(t).prop("maxlength",6).change()},20)),(t.ctrlKey||t.metaKey)&&!t.shiftKey){if(typeof r.tmp.cind1=="undefined")r.tmp.cind1=-1,r.tmp.cind2=-1;else{switch(l){case 38:r.tmp.cind1--;break;case 40:r.tmp.cind1++;break;case 39:r.tmp.cind2++;break;case 37:r.tmp.cind2--}r.tmp.cind1<0&&(r.tmp.cind1=0),r.tmp.cind1>this.pallete.length-1&&(r.tmp.cind1=this.pallete.length-1),r.tmp.cind2<0&&(r.tmp.cind2=0),r.tmp.cind2>this.pallete[0].length-1&&(r.tmp.cind2=this.pallete[0].length-1)}[37,38,39,40].indexOf(l)!=-1&&(n(r.el).val(this.pallete[r.tmp.cind1][r.tmp.cind2]).change(),t.preventDefault())}}if(["list","combo","enum"].indexOf(r.type)!=-1){if(n(r.el).attr("readonly"))return;var f=!1,c=n(r.el).data("selected"),b=n(r.helpers.focus).find("input");r.type=="list"&&[37,38,39,40].indexOf(l)==-1&&r.refresh();switch(l){case 27:r.type=="list"&&(n(b).val()!=""&&n(b).val(""),t.stopPropagation());break;case 13:if(n("#w2ui-overlay").length==0)break;if(e=u.items[u.index],p=n(r.helpers.multi).find("input"),r.type=="enum")if(e!=null){if(s=r.trigger({phase:"before",type:"add",target:r.el,originalEvent:t.originalEvent,item:e}),s.isCancelled===!0)return;e=s.item,c.length>=u.max&&u.max>0&&c.pop(),delete e.hidden,delete r.tmp.force_open,c.push(e),n(r.el).change(),p.val("").width(20),r.refresh(),r.trigger(n.extend(s,{phase:"after"}))}else{if(e={id:p.val(),text:p.val()},s=r.trigger({phase:"before",type:"new",target:r.el,originalEvent:t.originalEvent,item:e}),s.isCancelled===!0)return;e=s.item,typeof r.onNew=="function"&&(c.length>=u.max&&u.max>0&&c.pop(),delete r.tmp.force_open,c.push(e),n(r.el).change(),p.val("").width(20),r.refresh()),r.trigger(n.extend(s,{phase:"after"}))}else e&&n(r.el).data("selected",e).val(e.text).change(),n(r.el).val()==""&&n(r.el).data("selected")&&n(r.el).removeData("selected").val("").change(),r.type=="list"&&(b.val(""),r.refresh()),r.tmp.force_hide=!0;break;case 8:case 46:if(r.type=="enum"&&l==8&&n(r.helpers.multi).find("input").val()==""&&c.length>0){if(e=c[c.length-1],s=r.trigger({phase:"before",type:"remove",target:r.el,originalEvent:t.originalEvent,item:e}),s.isCancelled===!0)return;c.pop(),n(r.el).trigger("change"),r.refresh(),r.trigger(n.extend(s,{phase:"after"}))}r.type=="list"&&n(b).val()==""&&(n(r.el).data("selected",{}).change(),r.refresh());break;case 38:for(u.index=w2utils.isInt(u.index)?parseInt(u.index):0,u.index--;u.index>0&&u.items[u.index].hidden;)u.index--;if(u.index==0&&u.items[u.index].hidden)while(u.items[u.index]&&u.items[u.index].hidden)u.index++;f=!0;break;case 40:for(u.index=w2utils.isInt(u.index)?parseInt(u.index):-1,u.index++;u.index<u.items.length-1&&u.items[u.index].hidden;)u.index++;if(u.index==u.items.length-1&&u.items[u.index].hidden)while(u.items[u.index]&&u.items[u.index].hidden)u.index--;y=r.el,["enum"].indexOf(r.type)!=-1&&(y=r.helpers.multi.find("input")),n(y).val()==""&&n("#w2ui-overlay").length==0?r.tmp.force_open=!0:f=!0}if(f){u.index<0&&(u.index=0),u.index>=u.items.length&&(u.index=u.items.length-1),r.updateOverlay(),t.preventDefault(),setTimeout(function(){var n;r.type=="enum"?(n=r.helpers.multi.find("input").get(0),n.setSelectionRange(n.value.length,n.value.length)):r.type=="list"?(n=r.helpers.focus.find("input").get(0),n.setSelectionRange(n.value.length,n.value.length)):r.el.setSelectionRange(r.el.value.length,r.el.value.length)},0);return}r.type=="enum"&&(y=r.helpers.multi.find("input"),d=y.val(),y.width((d.length+2)*8+"px")),[16,17,18,20,37,39,91].indexOf(l)==-1&&setTimeout(function(){r.tmp.force_hide||r.request(),r.search()},1)}},keyUp:function(t){this.type=="color"&&t.keyCode==86&&(t.ctrlKey||t.metaKey)&&n(this).prop("maxlength",6)},clearCache:function(){var n=this.options;n.items=[],this.tmp.xhr_loading=!1,this.tmp.xhr_search="",this.tmp.xhr_total=-1,this.search()},request:function(t){var i=this,r=this.options,u=n(i.el).val()||"",f;if(r.url){if(i.type=="enum"&&(f=n(i.helpers.multi).find("input"),u=f.length==0?"":f.val()),i.type=="list"&&(f=n(i.helpers.focus).find("input"),u=f.length==0?"":f.val()),r.minLength!=0&&u.length<r.minLength){r.items=[],this.updateOverlay();return}typeof t=="undefined"&&(t=350),typeof i.tmp.xhr_search=="undefined"&&(i.tmp.xhr_search=""),typeof i.tmp.xhr_total=="undefined"&&(i.tmp.xhr_total=-1),r.url&&n(i.el).prop("readonly")!=!0&&(r.items.length===0&&i.tmp.xhr_total!==0||i.tmp.xhr_total==r.cacheMax&&u.length>i.tmp.xhr_search.length||u.length>=i.tmp.xhr_search.length&&u.substr(0,i.tmp.xhr_search.length)!=i.tmp.xhr_search||u.length<i.tmp.xhr_search.length)&&(i.tmp.xhr_loading=!0,i.search(),clearTimeout(i.tmp.timeout),i.tmp.timeout=setTimeout(function(){var o=r.url,f={search:u,max:r.cacheMax},e,t;(n.extend(f,r.postData),e=i.trigger({phase:"before",type:"request",target:i.el,url:o,postData:f}),e.isCancelled!==!0)&&(o=e.url,f=e.postData,i.tmp.xhr&&i.tmp.xhr.abort(),t={type:"GET",url:o,data:f,dataType:"JSON"},w2utils.settings.dataType=="JSON"&&(t.type="POST",t.data=JSON.stringify(t.data),t.contentType="application/json"),i.tmp.xhr=n.ajax(t).done(function(t,e,o){var s=i.trigger({phase:"before",type:"load",target:i.el,search:f.search,data:t,xhr:o});if(s.isCancelled!==!0){if(t=s.data,typeof t=="string"&&(t=JSON.parse(t)),t.status!="success"){console.log("ERROR: server did not return proper structure. It should return",{status:"success",items:[{id:1,text:"item"}]});return}t.items.length>r.cacheMax&&t.items.splice(r.cacheMax,1e5),i.tmp.xhr_loading=!1,i.tmp.xhr_search=u,i.tmp.xhr_total=t.items.length,r.items=t.items,i.tmp.emptySet=u==""&&t.items.length==0?!0:!1,i.search(),i.trigger(n.extend(s,{phase:"after"}))}}).fail(function(t,r,f){var s={status:r,error:f,rawResponseText:t.responseText},o=i.trigger({phase:"before",type:"error",target:i.el,search:u,error:s,xhr:t}),e;if(o.isCancelled!==!0){if(r!="abort"){try{e=n.parseJSON(t.responseText)}catch(h){}console.log("ERROR: Server communication failed.","\n   EXPECTED:",{status:"success",items:[{id:1,text:"item"}]},"\n         OR:",{status:"error",message:"error message"},"\n   RECEIVED:",typeof e=="object"?e:t.responseText)}i.clearCache(),i.trigger(n.extend(o,{phase:"after"}))}}),i.trigger(n.extend(e,{phase:"after"})))},t))}},search:function(){var i=this,t=this.options,e=n(i.el).val(),o=i.el,s=[],r=n(i.el).data("selected"),u,h,c,l,y;if(i.type=="enum"){o=n(i.helpers.multi).find("input"),e=o.val();for(u in r)r[u]&&s.push(r[u].id)}if(i.type=="list"){o=n(i.helpers.focus).find("input"),e=o.val();for(u in r)r[u]&&s.push(r[u].id)}if(h=i.trigger({phase:"before",type:"search",target:o,search:e}),h.isCancelled!==!0){if(i.tmp.xhr_loading!==!0){c=0;for(l in t.items){var f=t.items[l],a="",v="";["is","begins"].indexOf(t.match)!=-1&&(a="^"),["is","ends"].indexOf(t.match)!=-1&&(v="$");try{y=new RegExp(a+e+v,"i"),f.hidden=y.test(f.text)||f.text=="..."?!1:!0}catch(p){}i.type=="enum"&&n.inArray(f.id,s)!=-1&&(f.hidden=!0),f.hidden!==!0&&c++}if(i.type!="combo")for(t.index=0;t.items[t.index]&&t.items[t.index].hidden;)t.index++;else t.index=-1;c<=0&&(t.index=-1),t.spinner=!1,i.updateOverlay(),setTimeout(function(){var i=n("#w2ui-overlay").html()||"";t.markSearch&&i.indexOf("$.fn.w2menuHandler")!=-1&&n("#w2ui-overlay").w2marker(e)},1)}else t.items.splice(0,t.cacheMax),t.spinner=!0,i.updateOverlay();i.trigger(n.extend(h,{phase:"after"}))}},updateOverlay:function(){var t=this,i=this.options,h,c,f,u,e,r,o;if(this.type=="color"){if(n(t.el).attr("readonly"))return;n("#w2ui-overlay").length==0?n(t.el).w2overlay(t.getColorHTML()):n("#w2ui-overlay").html(t.getColorHTML());n("#w2ui-overlay .color").on("mousedown",function(i){var u=n(i.originalEvent.target).attr("name"),r=n(i.originalEvent.target).attr("index").split(":");t.tmp.cind1=r[0],t.tmp.cind2=r[1],n(t.el).val(u).change(),n(this).html("&#149;")}).on("mouseup",function(){setTimeout(function(){n("#w2ui-overlay").length>0&&n("#w2ui-overlay").removeData("keepOpen")[0].hide()},10)})}if(this.type=="date"){if(n(t.el).attr("readonly"))return;n("#w2ui-overlay").length==0&&n(t.el).w2overlay('<div class="w2ui-reset w2ui-calendar" onclick="event.stopPropagation();"><\/div>',{css:{"background-color":"#f5f5f5"}}),f=w2utils.isDate(n(t.el).val(),t.options.format,!0),f&&(h=f.getMonth()+1,c=f.getFullYear()),function s(i,r){n("#w2ui-overlay > div > div").html(t.getMonthHTML(i,r));n("#w2ui-overlay .w2ui-calendar-title").on("mousedown",function(){if(n(this).next().hasClass("w2ui-calendar-jump"))n(this).next().remove();else{var i,r;n(this).after('<div class="w2ui-calendar-jump" style=""><\/div>'),n(this).next().hide().html(t.getYearHTML()).fadeIn(200),setTimeout(function(){n("#w2ui-overlay .w2ui-calendar-jump").find(".w2ui-jump-month, .w2ui-jump-year").on("click",function(){n(this).hasClass("w2ui-jump-month")&&(n(this).parent().find(".w2ui-jump-month").removeClass("selected"),n(this).addClass("selected"),r=n(this).attr("name")),n(this).hasClass("w2ui-jump-year")&&(n(this).parent().find(".w2ui-jump-year").removeClass("selected"),n(this).addClass("selected"),i=n(this).attr("name")),i!=null&&r!=null&&(n("#w2ui-overlay .w2ui-calendar-jump").fadeOut(100),setTimeout(function(){s(parseInt(r)+1,i)},100))});n("#w2ui-overlay .w2ui-calendar-jump >:last-child").prop("scrollTop",2e3)},1)}});n("#w2ui-overlay .w2ui-date").on("mousedown",function(){var i=n(this).attr("date");n(t.el).val(i).change(),n(this).css({"background-color":"#B6D5FB","border-color":"#aaa"})}).on("mouseup",function(){setTimeout(function(){n("#w2ui-overlay").length>0&&n("#w2ui-overlay").removeData("keepOpen")[0].hide()},10)});n("#w2ui-overlay .previous").on("mousedown",function(){var n=t.options.current.split("/");n[0]=parseInt(n[0])-1,s(n[0],n[1])});n("#w2ui-overlay .next").on("mousedown",function(){var n=t.options.current.split("/");n[0]=parseInt(n[0])+1,s(n[0],n[1])})}(h,c)}if(this.type=="time"){if(n(t.el).attr("readonly"))return;n("#w2ui-overlay").length==0&&n(t.el).w2overlay('<div class="w2ui-reset w2ui-calendar-time" onclick="event.stopPropagation();"><\/div>',{css:{"background-color":"#fff"}}),u=this.options.format=="h24"?!0:!1,n("#w2ui-overlay > div").html(t.getHourHTML());n("#w2ui-overlay .w2ui-time").on("mousedown",function(){n(this).css({"background-color":"#B6D5FB","border-color":"#aaa"});var i=n(this).attr("hour");n(t.el).val((i>12&&!u?i-12:i)+":00"+(u?"":i<12?" am":" pm")).change()}).on("mouseup",function(){var i=n(this).attr("hour");n("#w2ui-overlay").length>0&&n("#w2ui-overlay")[0].hide(),n(t.el).w2overlay('<div class="w2ui-reset w2ui-calendar-time"><\/div>',{css:{"background-color":"#fff"}}),n("#w2ui-overlay > div").html(t.getMinHTML(i));n("#w2ui-overlay .w2ui-time").on("mousedown",function(){n(this).css({"background-color":"#B6D5FB","border-color":"#aaa"});var r=n(this).attr("min");n(t.el).val((i>12&&!u?i-12:i)+":"+(r<10?0:"")+r+(u?"":i<12?" am":" pm")).change()}).on("mouseup",function(){setTimeout(function(){n("#w2ui-overlay").length>0&&n("#w2ui-overlay").removeData("keepOpen")[0].hide()},10)})})}if(["list","combo","enum"].indexOf(this.type)!=-1&&(e=this.el,r=this.el,this.type=="enum"&&(e=n(this.helpers.multi),r=n(e).find("input")),this.type=="list"&&(r=n(this.helpers.focus).find("input")),n(r).is(":focus"))){if(i.openOnFocus===!1&&n(r).val()==""&&t.tmp.force_open!==!0){n().w2overlay();return}if(t.tmp.force_hide){n().w2overlay(),setTimeout(function(){delete t.tmp.force_hide},1);return}n(r).val()!=""&&delete t.tmp.force_open,n("#w2ui-overlay").length==0&&(i.index=0),o=w2utils.lang("No matches"),i.url!=null&&n(r).val().length<i.minLength&&t.tmp.emptySet!==!0&&(o=i.minLength+" "+w2utils.lang("letters or more...")),i.url!=null&&n(r).val()==""&&t.tmp.emptySet!==!0&&(o=w2utils.lang("Type to search....")),n(e).w2menu("refresh",n.extend(!0,{},i,{search:!1,render:i.renderDrop,maxHeight:i.maxDropHeight,msgNoItems:o,onSelect:function(r){var u,f;if(t.type=="enum"){if(u=n(t.el).data("selected"),r.item){if(f=t.trigger({phase:"before",type:"add",target:t.el,originalEvent:r.originalEvent,item:r.item}),f.isCancelled===!0)return;u.length>=i.max&&i.max>0&&u.pop(),delete r.item.hidden,u.push(r.item),n(t.el).data("selected",u).change(),n(t.helpers.multi).find("input").val("").width(20),t.refresh(),n("#w2ui-overlay").length>0&&n("#w2ui-overlay")[0].hide(),t.trigger(n.extend(f,{phase:"after"}))}}else n(t.el).data("selected",r.item).val(r.item.text).change(),t.helpers.focus&&(t.helpers.focus.find("input").val(""),t.refresh())}}))}},inRange:function(t){var i=!1,f;if(this.type=="date"&&(f=w2utils.isDate(t,this.options.format,!0),f)){if(this.options.start||this.options.end){var c=typeof this.options.start=="string"?this.options.start:n(this.options.start).val(),l=typeof this.options.end=="string"?this.options.end:n(this.options.end).val(),e=w2utils.isDate(c,this.options.format,!0),o=w2utils.isDate(l,this.options.format,!0),r=new Date(f);e||(e=r),o||(o=r),r>=e&&r<=o&&(i=!0)}else i=!0;this.options.blocked&&n.inArray(t,this.options.blocked)!=-1&&(i=!1)}if(this.type=="time")if(this.options.start||this.options.end){var u=this.toMin(t),s=this.toMin(this.options.start),h=this.toMin(this.options.end);s||(s=u),h||(h=u),u>=s&&u<=h&&(i=!0)}else i=!0;return i},checkType:function(n,t){var i=this;switch(i.type){case"int":return t&&["-"].indexOf(n)!=-1?!0:w2utils.isInt(n.replace(i.options.numberRE,""));case"percent":n=n.replace(/%/g,"");case"float":return t&&["-","."].indexOf(n)!=-1?!0:w2utils.isFloat(n.replace(i.options.numberRE,""));case"money":case"currency":return t&&["-",".",i.options.groupSymbol,i.options.currencyPrefix,i.options.currencySuffix].indexOf(n)!=-1?!0:w2utils.isFloat(n.replace(i.options.moneyRE,""));case"hex":case"color":return w2utils.isHex(n);case"alphanumeric":return w2utils.isAlphaNumeric(n)}return!0},addPrefix:function(){var t=this;setTimeout(function(){if(t.type!=="clear"){var i,r=n(t.el).data("tmp")||{};if(r["old-padding-left"]&&n(t.el).css("padding-left",r["old-padding-left"]),r["old-padding-left"]=n(t.el).css("padding-left"),n(t.el).data("tmp",r),t.helpers.prefix&&n(t.helpers.prefix).remove(),t.options.prefix!==""){n(t.el).before('<div class="w2ui-field-helper">'+t.options.prefix+"<\/div>"),i=n(t.el).prev();i.css({color:n(t.el).css("color"),"font-family":n(t.el).css("font-family"),"font-size":n(t.el).css("font-size"),"padding-top":n(t.el).css("padding-top"),"padding-bottom":n(t.el).css("padding-bottom"),"padding-left":n(t.el).css("padding-left"),"padding-right":0,"margin-top":parseInt(n(t.el).css("margin-top"),10)+2+"px","margin-bottom":parseInt(n(t.el).css("margin-bottom"),10)+1+"px","margin-left":n(t.el).css("margin-left"),"margin-right":0}).on("click",function(){if(t.options.icon&&typeof t.onIconClick=="function"){var i=t.trigger({phase:"before",type:"iconClick",target:t.el,el:n(this).find("span.w2ui-icon")[0]});if(i.isCancelled===!0)return;t.trigger(n.extend(i,{phase:"after"}))}else t.type=="list"?n(t.helpers.focus).find("input").focus():n(t.el).focus()});n(t.el).css("padding-left",i.width()+parseInt(n(t.el).css("padding-left"),10)+"px"),t.helpers.prefix=i}}},1)},addSuffix:function(){var t=this,i,r;setTimeout(function(){var u,f;if(t.type!=="clear"){if(u=n(t.el).data("tmp")||{},u["old-padding-right"]&&n(t.el).css("padding-right",u["old-padding-right"]),u["old-padding-right"]=n(t.el).css("padding-right"),n(t.el).data("tmp",u),r=parseInt(n(t.el).css("padding-right"),10),t.options.arrows){t.helpers.arrows&&n(t.helpers.arrows).remove(),n(t.el).after('<div class="w2ui-field-helper" style="border: 1px solid transparent">&nbsp;    <div class="w2ui-field-up" type="up">        <div class="arrow-up" type="up"><\/div>    <\/div>    <div class="w2ui-field-down" type="down">        <div class="arrow-down" type="down"><\/div>    <\/div><\/div>'),f=w2utils.getSize(t.el,"height"),i=n(t.el).next();i.css({color:n(t.el).css("color"),"font-family":n(t.el).css("font-family"),"font-size":n(t.el).css("font-size"),height:n(t.el).height()+parseInt(n(t.el).css("padding-top"),10)+parseInt(n(t.el).css("padding-bottom"),10)+"px",padding:0,"margin-top":parseInt(n(t.el).css("margin-top"),10)+1+"px","margin-bottom":0,"border-left":"1px solid silver"}).css("margin-left","-"+(i.width()+parseInt(n(t.el).css("margin-right"),10)+12)+"px").on("mousedown",function(i){function u(){clearTimeout(n("body").data("_field_update_timer")),n("body").off("mouseup",u)}function r(u){n(t.el).focus(),t.keyDown(n.Event("keydown"),{keyCode:n(i.target).attr("type")=="up"?38:40}),u!==!1&&n("body").data("_field_update_timer",setTimeout(r,60))}n("body").on("mouseup",u);n("body").data("_field_update_timer",setTimeout(r,700)),r(!1)});r+=i.width()+12,n(t.el).css("padding-right",r+"px"),t.helpers.arrows=i}if(t.options.suffix!==""){t.helpers.suffix&&n(t.helpers.suffix).remove(),n(t.el).after('<div class="w2ui-field-helper">'+t.options.suffix+"<\/div>"),i=n(t.el).next();i.css({color:n(t.el).css("color"),"font-family":n(t.el).css("font-family"),"font-size":n(t.el).css("font-size"),"padding-top":n(t.el).css("padding-top"),"padding-bottom":n(t.el).css("padding-bottom"),"padding-left":"3px","padding-right":n(t.el).css("padding-right"),"margin-top":parseInt(n(t.el).css("margin-top"),10)+2+"px","margin-bottom":parseInt(n(t.el).css("margin-bottom"),10)+1+"px"}).on("click",function(){t.type=="list"?n(t.helpers.focus).find("input").focus():n(t.el).focus()});i.css("margin-left","-"+(w2utils.getSize(i,"width")+parseInt(n(t.el).css("margin-right"),10)+2)+"px"),r+=i.width()+3,n(t.el).css("padding-right",r+"px"),t.helpers.suffix=i}}},1)},addFocus:function(){var t=this,f=this.options,r=0,u,i;n(t.helpers.focus).remove(),u='<div class="w2ui-field-helper">    <div class="w2ui-icon icon-search"><\/div>    <input type="text" autocomplete="off"><div>',n(t.el).attr("tabindex",-1).before(u),i=n(t.el).prev(),t.helpers.focus=i,i.css({width:n(t.el).width(),"margin-top":n(t.el).css("margin-top"),"margin-left":parseInt(n(t.el).css("margin-left"))+parseInt(n(t.el).css("padding-left"))+"px","margin-bottom":n(t.el).css("margin-bottom"),"margin-right":n(t.el).css("margin-right")}).find("input").css({cursor:"default",width:"100%",outline:"none",opacity:1,margin:0,border:"1px solid transparent",padding:n(t.el).css("padding-top"),"padding-left":0,"margin-left":r>0?r+6:0,"background-color":"transparent"});i.find("input").on("click",function(i){n("#w2ui-overlay").length==0&&t.focus(i),i.stopPropagation()}).on("focus",function(i){n(t.el).css({outline:"auto 5px #7DB4F3","outline-offset":"-2px"}),n(this).val(""),n(t.el).triggerHandler("focus"),i.stopPropagation?i.stopPropagation():i.cancelBubble=!0}).on("blur",function(i){n(t.el).css("outline","none"),n(this).val(""),t.refresh(),n(t.el).triggerHandler("blur"),i.stopPropagation?i.stopPropagation():i.cancelBubble=!0}).on("keyup",function(n){t.keyUp(n)}).on("keydown",function(n){t.keyDown(n)}).on("keypress",function(n){t.keyPress(n)});i.on("click",function(){n(this).find("input").focus()});t.refresh()},addMulti:function(){var t=this,f=this.options,r,u,i;if(n(t.helpers.multi).remove(),r="",u="margin-top     : 0px; margin-bottom  : 0px; margin-left    : "+n(t.el).css("margin-left")+"; margin-right   : "+n(t.el).css("margin-right")+"; width          : "+(w2utils.getSize(t.el,"width")-parseInt(n(t.el).css("margin-left"),10)-parseInt(n(t.el).css("margin-right"),10))+"px;",t.type=="enum"&&(r='<div class="w2ui-field-helper w2ui-list" style="'+u+'; box-sizing: border-box">    <div style="padding: 0px; margin: 0px; margin-right: 20px; display: inline-block">    <ul>        <li style="padding-left: 0px; padding-right: 0px" class="nomouse">            <input type="text" style="width: 20px" autocomplete="off" '+(n(t.el).attr("readonly")?"readonly":"")+">        <\/li>","    <\/ul>    <\/div><\/div>"),t.type=="file"&&(r='<div class="w2ui-field-helper w2ui-list" style="'+u+'; box-sizing: border-box">    <div style="padding: 0px; margin: 0px; margin-right: 20px; display: inline-block">    <ul><li style="padding-left: 0px; padding-right: 0px" class="nomouse"><\/li><\/ul>    <input class="file-input" type="file" name="attachment" multiple style="display: none" tabindex="-1">',"    <\/div><\/div>"),n(t.el).before(r).css({"background-color":"transparent","border-color":"transparent"}),i=n(t.el).prev(),t.helpers.multi=i,t.type=="enum"){n(t.el).attr("tabindex",-1);i.find("input").on("click",function(i){n("#w2ui-overlay").length==0&&t.focus(i),n(t.el).triggerHandler("click")}).on("focus",function(r){n(i).css({outline:"auto 5px #7DB4F3","outline-offset":"-2px"}),n(t.el).triggerHandler("focus"),r.stopPropagation?r.stopPropagation():r.cancelBubble=!0}).on("blur",function(r){n(i).css("outline","none"),n(t.el).triggerHandler("blur"),r.stopPropagation?r.stopPropagation():r.cancelBubble=!0}).on("keyup",function(n){t.keyUp(n)}).on("keydown",function(n){t.keyDown(n)}).on("keypress",function(n){i.find(".w2ui-enum-placeholder").remove(),t.keyPress(n)});i.on("click",function(){n(this).find("input").focus()})}if(t.type=="file"){n(t.el).css("outline","none");i.on("click",function(r){(n(t.el).focus(),n(t.el).attr("readonly"))||(t.blur(r),i.find("input").click())}).on("dragenter",function(){n(t.el).attr("readonly")||n(i).addClass("w2ui-file-dragover")}).on("dragleave",function(r){if(!n(t.el).attr("readonly")){var u=n(r.target).parents(".w2ui-field-helper");u.length==0&&n(i).removeClass("w2ui-file-dragover")}}).on("drop",function(r){var f,u,e;if(!n(t.el).attr("readonly")){for(n(i).removeClass("w2ui-file-dragover"),f=r.originalEvent.dataTransfer.files,u=0,e=f.length;u<e;u++)t.addFile.call(t,f[u]);r.preventDefault(),r.stopPropagation()}}).on("dragover",function(n){n.preventDefault(),n.stopPropagation()});i.find("input").on("click",function(n){n.stopPropagation()}).on("change",function(){if(typeof this.files!="undefined")for(var n=0,i=this.files.length;n<i;n++)t.addFile.call(t,this.files[n])})}t.refresh()},addFile:function(t){var r=this,i=this.options,e=n(r.el).data("selected"),f={name:t.name,type:t.type,modified:t.lastModifiedDate,size:t.size,content:null},o=0,s=0,u,l,h,c;for(l in e)o+=e[l].size,s++;if(h=r.trigger({phase:"before",type:"add",target:r.el,file:f,total:s,totalSize:o}),h.isCancelled!==!0){if(i.maxFileSize!==0&&f.size>i.maxFileSize){u="Maximum file size is "+w2utils.size(i.maxFileSize),i.silent===!1&&n(r.el).w2tag(u),console.log("ERROR: "+u);return}if(i.maxSize!==0&&o+f.size>i.maxSize){u="Maximum total size is "+w2utils.size(i.maxSize),i.silent===!1&&n(r.el).w2tag(u),console.log("ERROR: "+u);return}if(i.max!==0&&s>=i.max){u="Maximum number of files is "+i.max,i.silent===!1&&n(r.el).w2tag(u),console.log("ERROR: "+u);return}e.push(f),typeof FileReader!="undefined"?(c=new FileReader,c.onload=function(){return function(t){var i=t.target.result,u=i.indexOf(",");f.content=i.substr(u+1),r.refresh(),n(r.el).trigger("change"),r.trigger(n.extend(h,{phase:"after"}))}}(),c.readAsDataURL(t)):(r.refresh(),n(r.el).trigger("change"))}},normMenu:function(t){var r,i;if(n.isArray(t)){for(i=0;i<t.length;i++)typeof t[i]=="string"?t[i]={id:t[i],text:t[i]}:(typeof t[i].text!="undefined"&&typeof t[i].id=="undefined"&&(t[i].id=t[i].text),typeof t[i].text=="undefined"&&typeof t[i].id!="undefined"&&(t[i].text=t[i].id),typeof t[i].caption!="undefined"&&(t[i].text=t[i].caption));return t}if(typeof t=="object"){r=[];for(i in t)r.push({id:i,text:t[i]});return r}},getColorHTML:function(){for(var r='<div class="w2ui-color"><table cellspacing="5">',i,t=0;t<8;t++){for(r+="<tr>",i=0;i<8;i++)r+='<td>    <div class="color" style="background-color: #'+this.pallete[t][i]+';" name="'+this.pallete[t][i]+'" index="'+t+":"+i+'">        '+(n(this.el).val()==this.pallete[t][i]?"&#149;":"&nbsp;")+"    <\/div><\/td>";r+="<\/tr>",t<2&&(r+='<tr><td style="height: 8px" colspan="8"><\/td><\/tr>')}return r+"<\/table><\/div>"},getMonthHTML:function(n,t){var r=new Date,k=w2utils.settings.fullmonths,nt=w2utils.settings.fulldays,a=["31","28","31","30","31","30","31","31","30","31","30","31"],d=r.getFullYear()+"/"+(Number(r.getMonth())+1)+"/"+r.getDate(),o,p,u,f,i,c,l,e;t=w2utils.isInt(t)?parseInt(t):r.getFullYear(),n=w2utils.isInt(n)?parseInt(n):r.getMonth()+1,n>12&&(n-=12,t++),(n<1||n===0)&&(n+=12,t--),a[1]=t/4==Math.floor(t/4)?"29":"28",this.options.current=n+"/"+t,r=new Date(t,n-1,1);var h=r.getDay(),v=w2utils.settings.shortdays,y="";for(o=0,p=v.length;o<p;o++)y+="<td>"+v[o]+"<\/td>";for(u='<div class="w2ui-calendar-title title">    <div class="w2ui-calendar-previous previous"> <div><\/div> <\/div>    <div class="w2ui-calendar-next next"> <div><\/div> <\/div> '+k[n-1]+", "+t+'<\/div><table class="w2ui-calendar-days" cellspacing="0">    <tr class="w2ui-day-title">'+y+"<\/tr>    <tr>",f=1,i=1;i<43;i++){if(h===0&&i==1){for(c=0;c<6;c++)u+='<td class="w2ui-day-empty">&nbsp;<\/td>';i+=6}else if(i<h||f>a[n-1]){u+='<td class="w2ui-day-empty">&nbsp;<\/td>',i%7==0&&(u+="<\/tr><tr>");continue}l=t+"/"+n+"/"+f,e="",i%7==6&&(e=" w2ui-saturday"),i%7==0&&(e=" w2ui-sunday"),l==d&&(e+=" w2ui-today");var g=f,w="",b="",s=w2utils.formatDate(l,this.options.format);this.options.colored&&this.options.colored[s]!==undefined&&(tmp=this.options.colored[s].split(":"),b="background-color: "+tmp[0]+";",w="color: "+tmp[1]+";"),u+='<td class="'+(this.inRange(s)?"w2ui-date ":"w2ui-blocked")+e+'" style="'+w+b+'" date="'+s+'">'+g+"<\/td>",(i%7==0||h===0&&i==1)&&(u+="<\/tr><tr>"),f++}return u+"<\/tr><\/table>"},getYearHTML:function(){var i=w2utils.settings.shortmonths,r="",u="",t,n;for(t in i)r+='<div class="w2ui-jump-month" name="'+t+'">'+i[t]+"<\/div>";for(n=1950;n<=2020;n++)u+='<div class="w2ui-jump-year" name="'+n+'">'+n+"<\/div>";return"<div>"+r+"<\/div><div>"+u+"<\/div>"},getHourHTML:function(){for(var t=[],r=this.options.format=="h24"?!0:!1,i,u,f,n=0;n<24;n++)i=(n>=12&&!r?n-12:n)+":00"+(r?"":n<12?" am":" pm"),n!=12||r||(i="12:00 pm"),t[Math.floor(n/8)]||(t[Math.floor(n/8)]=""),u=this.fromMin(this.toMin(i)),f=this.fromMin(this.toMin(i)+59),t[Math.floor(n/8)]+='<div class="'+(this.inRange(u)||this.inRange(f)?"w2ui-time ":"w2ui-blocked")+'" hour="'+n+'">'+i+"<\/div>";return'<div class="w2ui-calendar-time"><table><tr>    <td>'+t[0]+"<\/td>    <td>"+t[1]+"<\/td>    <td>"+t[2]+"<\/td><\/tr><\/table><\/div>"},getMinHTML:function(n){var u,i,t,f,r;for(typeof n=="undefined"&&(n=0),u=this.options.format=="h24"?!0:!1,i=[],t=0;t<60;t+=5)f=(n>12&&!u?n-12:n)+":"+(t<10?0:"")+t+" "+(u?"":n<12?"am":"pm"),r=t<20?0:t<40?1:2,i[r]||(i[r]=""),i[r]+='<div class="'+(this.inRange(f)?"w2ui-time ":"w2ui-blocked")+'" min="'+t+'">'+f+"<\/div>";return'<div class="w2ui-calendar-time"><table><tr>    <td>'+i[0]+"<\/td>    <td>"+i[1]+"<\/td>    <td>"+i[2]+"<\/td><\/tr><\/table><\/div>"},toMin:function(n){if(typeof n!="string")return null;var t=n.split(":");if(t.length==2)t[0]=parseInt(t[0]),t[1]=parseInt(t[1]),n.indexOf("pm")!=-1&&t[0]!=12&&(t[0]+=12);else return null;return t[0]*60+t[1]},fromMin:function(n){var t,i;return n>=1440&&(n=n%1440),n<0&&(n=1440+n),t=Math.floor(n/60),i=(n%60<10?"0":"")+n%60,this.options.format.indexOf("h24")!=-1?t+":"+i:(t<=12?t:t-12)+":"+i+" "+(t>=12?"pm":"am")}},n.extend(t.prototype,w2utils.event),w2obj.field=t}(jQuery),function(){var n=function(n){this.name=null,this.header="",this.box=null,this.url="",this.routeData={},this.formURL="",this.formHTML="",this.page=0,this.recid=0,this.fields=[],this.actions={},this.record={},this.original={},this.postData={},this.toolbar={},this.tabs={},this.style="",this.focus=0,this.msgNotJSON=w2utils.lang("Return data is not in JSON format."),this.msgAJAXerror=w2utils.lang("AJAX error. See console for more details."),this.msgRefresh=w2utils.lang("Refreshing..."),this.msgSaving=w2utils.lang("Saving..."),this.onRequest=null,this.onLoad=null,this.onValidate=null,this.onSubmit=null,this.onSave=null,this.onChange=null,this.onRender=null,this.onRefresh=null,this.onResize=null,this.onDestroy=null,this.onAction=null,this.onToolbar=null,this.onError=null,this.isGenerated=!1,this.last={xhr:null},$.extend(!0,this,w2obj.form,n)};$.fn.w2form=function(t){var l,e,u,r,f;if(typeof t!="object"&&t){if(w2ui[$(this).attr("name")])return f=w2ui[$(this).attr("name")],f[t].apply(f,Array.prototype.slice.call(arguments,1)),this;console.log("ERROR: Method "+t+" does not exist on jQuery.w2form")}else{if(f=this,!w2utils.checkName(t,"w2form"))return;var o=t.record,s=t.original,c=t.fields,a=t.toolbar,h=t.tabs,i=new n(t);if($.extend(i,{record:{},original:{},fields:[],tabs:{},toolbar:{},handlers:[]}),$.isArray(h)){$.extend(!0,i.tabs,{tabs:[]});for(l in h)e=h[l],typeof e=="object"?i.tabs.tabs.push(e):i.tabs.tabs.push({id:e,caption:e})}else $.extend(!0,i.tabs,h);$.extend(!0,i.toolbar,a);for(r in c)u=$.extend(!0,{},c[r]),typeof u.name=="undefined"&&typeof u.field!="undefined"&&(u.name=u.field),typeof u.field=="undefined"&&typeof u.name!="undefined"&&(u.field=u.name),i.fields[r]=u;for(r in o)i.record[r]=$.isPlainObject(o[r])?$.extend(!0,{},o[r]):o[r];for(r in s)i.original[r]=$.isPlainObject(s[r])?$.extend(!0,{},s[r]):s[r];return f.length>0&&(i.box=f[0]),i.formURL!=""?$.get(i.formURL,function(n){i.formHTML=n,i.isGenerated=!0,($(i.box).length!=0||n.length!=0)&&($(i.box).html(n),i.render(i.box))}):i.formHTML!=""||(i.formHTML=$(this).length!=0&&$.trim($(this).html())!=""?$(this).html():i.generateHTML()),w2ui[i.name]=i,i.formURL==""&&(String(i.formHTML).indexOf("w2ui-page")==-1&&(i.formHTML='<div class="w2ui-page page-0">'+i.formHTML+"<\/div>"),$(i.box).html(i.formHTML),i.isGenerated=!0,i.render(i.box)),i}},n.prototype={get:function(n,t){var r,u,i;if(arguments.length===0){r=[];for(u in this.fields)this.fields[u].name!=null&&r.push(this.fields[u].name);return r}for(i in this.fields)if(this.fields[i].name==n)return t===!0?i:this.fields[i];return null},set:function(n,t){for(var i in this.fields)if(this.fields[i].name==n)return $.extend(this.fields[i],t),this.refresh(),!0;return!1},reload:function(n){var t=typeof this.url!="object"?this.url:this.url.get;t&&this.recid!=0?this.request(n):typeof n=="function"&&n()},clear:function(){this.recid=0,this.record={},$().w2tag(),this.refresh()},error:function(n){var i=this,t=this.trigger({target:this.name,type:"error",message:n,xhr:this.last.xhr});if(t.isCancelled===!0){typeof callBack=="function"&&callBack();return}setTimeout(function(){w2alert(n,"Error")},1),this.trigger($.extend(t,{phase:"after"}))},validate:function(n){var i,e,t,u,f,o,r;typeof n=="undefined"&&(n=!0),$().w2tag(),i=[];for(e in this.fields){t=this.fields[e],this.record[t.name]==null&&(this.record[t.name]="");switch(t.type){case"int":this.record[t.name]&&!w2utils.isInt(this.record[t.name])&&i.push({field:t,error:w2utils.lang("Not an integer")});break;case"float":this.record[t.name]&&!w2utils.isFloat(this.record[t.name])&&i.push({field:t,error:w2utils.lang("Not a float")});break;case"money":this.record[t.name]&&!w2utils.isMoney(this.record[t.name])&&i.push({field:t,error:w2utils.lang("Not in money format")});break;case"color":case"hex":this.record[t.name]&&!w2utils.isHex(this.record[t.name])&&i.push({field:t,error:w2utils.lang("Not a hex number")});break;case"email":this.record[t.name]&&!w2utils.isEmail(this.record[t.name])&&i.push({field:t,error:w2utils.lang("Not a valid email")});break;case"checkbox":this.record[t.name]=this.record[t.name]==!0?1:0;break;case"date":t.options.format||(t.options.format=w2utils.settings.date_format),this.record[t.name]&&!w2utils.isDate(this.record[t.name],t.options.format)&&i.push({field:t,error:w2utils.lang("Not a valid date")+": "+t.options.format})}u=this.record[t.name],t.required&&(u===""||$.isArray(u)&&u.length==0||$.isPlainObject(u)&&$.isEmptyObject(u))&&i.push({field:t,error:w2utils.lang("Required field")}),t.equalto&&this.record[t.name]!=this.record[t.equalto]&&i.push({field:t,error:w2utils.lang("Field should be equal to ")+t.equalto})}if(f=this.trigger({phase:"before",target:this.name,type:"validate",errors:i}),f.isCancelled!==!0){if(n)for(o in f.errors)r=f.errors[o],r.field.type=="radio"?$($(r.field.el).parents("div")[0]).w2tag(r.error,{"class":"w2ui-error"}):["enum","file"].indexOf(r.field.type)!=-1?function(n){setTimeout(function(){var t=$(n.field.el).data("w2field").helpers.multi;$(n.field.el).w2tag(n.error),$(t).addClass("w2ui-error")},1)}(r):$(r.field.el).w2tag(r.error,{"class":"w2ui-error"}),this.goto(i[0].field.page);return this.trigger($.extend(f,{phase:"after"})),i}},getChanges:function(){var n=function(t,i,r){for(var u in t)typeof t[u]=="object"?(r[u]=n(t[u],i[u]||{},{}),(!r[u]||$.isEmptyObject(r[u]))&&delete r[u]):t[u]!=i[u]&&(r[u]=t[u]);return r};return n(this.record,this.original,{})},request:function(n,t){var i=this,f,u,e,o,s,r;if(typeof n=="function"&&(t=n,n=null),(typeof n=="undefined"||n==null)&&(n={}),this.url&&(typeof this.url!="object"||this.url.get)){if((this.recid==null||typeof this.recid=="undefined")&&(this.recid=0),f={},f.cmd="get-record",f.recid=this.recid,$.extend(f,this.postData),$.extend(f,n),u=this.trigger({phase:"before",type:"request",target:this.name,url:this.url,postData:f}),u.isCancelled===!0){typeof t=="function"&&t({status:"error",message:"Request aborted."});return}if(this.record={},this.original={},this.lock(this.msgRefresh),e=u.url,typeof u.url=="object"&&u.url.get&&(e=u.url.get),this.last.xhr)try{this.last.xhr.abort()}catch(h){}if(!$.isEmptyObject(i.routeData)&&(o=w2utils.parseRoute(e),o.keys.length>0))for(s=0;s<o.keys.length;s++)i.routeData[o.keys[s].name]!=null&&(e=e.replace(new RegExp(":"+o.keys[s].name,"g"),i.routeData[o.keys[s].name]));r={type:"POST",url:e,data:u.postData,dataType:"text"},w2utils.settings.dataType=="HTTP"&&(r.data=String($.param(r.data,!1)).replace(/%5B/g,"[").replace(/%5D/g,"]")),w2utils.settings.dataType=="RESTFULL"&&(r.type="GET",r.data=String($.param(r.data,!1)).replace(/%5B/g,"[").replace(/%5D/g,"]")),w2utils.settings.dataType=="JSON"&&(r.type="POST",r.data=JSON.stringify(r.data),r.contentType="application/json"),this.last.xhr=$.ajax(r).done(function(data,status,xhr){var eventData,data,responseText;if(i.unlock(),eventData=i.trigger({phase:"before",target:i.name,type:"load",xhr:xhr}),eventData.isCancelled===!0){typeof t=="function"&&t({status:"error",message:"Request aborted."});return}if(responseText=i.last.xhr.responseText,status!="error"){if(typeof responseText!="undefined"&&responseText!=""){if(typeof responseText=="object")data=responseText;else try{eval("data = "+responseText)}catch(e){}typeof data=="undefined"&&(data={status:"error",message:i.msgNotJSON,responseText:responseText}),data.status=="error"?i.error(data.message):(i.record=$.extend({},data.record),i.original=$.extend({},data.record))}}else i.error("AJAX Error "+xhr.status+": "+xhr.statusText),data={status:"error",message:i.msgAJAXerror,responseText:responseText};i.trigger($.extend(eventData,{phase:"after"})),i.refresh(),typeof t=="function"&&t(data)}).fail(function(n,t,r){var e={status:t,error:r,rawResponseText:n.responseText},f=i.trigger({phase:"before",type:"error",error:e,xhr:n}),u;if(f.isCancelled!==!0){if(t!="abort"){try{u=$.parseJSON(n.responseText)}catch(o){}console.log("ERROR: Server communication failed.","\n   EXPECTED:",{status:"success",items:[{id:1,text:"item"}]},"\n         OR:",{status:"error",message:"error message"},"\n   RECEIVED:",typeof u=="object"?u:n.responseText)}i.trigger($.extend(f,{phase:"after"}))}}),this.trigger($.extend(u,{phase:"after"}))}},submit:function(n,t){return this.save(n,t)},save:function(n,t){var i=this,r;if($(this.box).find(":focus").change(),typeof n=="function"&&(t=n,n=null),r=i.validate(!0),r.length===0){if((typeof n=="undefined"||n==null)&&(n={}),!i.url||typeof i.url=="object"&&!i.url.save){console.log("ERROR: Form cannot be saved because no url is defined.");return}i.lock(i.msgSaving+' <span id="'+i.name+'_progress"><\/span>'),setTimeout(function(){var f={},u,e,o,s,r;if(f.cmd="save-record",f.recid=i.recid,$.extend(f,i.postData),$.extend(f,n),f.record=$.extend(!0,{},i.record),u=i.trigger({phase:"before",type:"submit",target:i.name,url:i.url,postData:f}),u.isCancelled!==!0){if(e=u.url,typeof u.url=="object"&&u.url.save&&(e=u.url.save),i.last.xhr)try{i.last.xhr.abort()}catch(h){}if(!$.isEmptyObject(i.routeData)&&(o=w2utils.parseRoute(e),o.keys.length>0))for(s=0;s<o.keys.length;s++)i.routeData[o.keys[s].name]!=null&&(e=e.replace(new RegExp(":"+o.keys[s].name,"g"),i.routeData[o.keys[s].name]));r={type:"POST",url:e,data:u.postData,dataType:"text",xhr:function(){var n=new window.XMLHttpRequest;return n.upload.addEventListener("progress",function(n){if(n.lengthComputable){var t=Math.round(n.loaded/n.total*100);$("#"+i.name+"_progress").text(""+t+"%")}},!1),n}},w2utils.settings.dataType=="HTTP"&&(r.data=String($.param(r.data,!1)).replace(/%5B/g,"[").replace(/%5D/g,"]")),w2utils.settings.dataType=="RESTFULL"&&(i.recid!=0&&(r.type="PUT"),r.data=String($.param(r.data,!1)).replace(/%5B/g,"[").replace(/%5D/g,"]")),w2utils.settings.dataType=="JSON"&&(r.type="POST",r.data=JSON.stringify(r.data),r.contentType="application/json"),i.last.xhr=$.ajax(r).done(function(data,status,xhr){var eventData,data,responseText;if(i.unlock(),eventData=i.trigger({phase:"before",target:i.name,type:"save",xhr:xhr,status:status}),eventData.isCancelled!==!0){if(responseText=xhr.responseText,status!="error"){if(typeof responseText!="undefined"&&responseText!=""){if(typeof responseText=="object")data=responseText;else try{eval("data = "+responseText)}catch(e){}typeof data=="undefined"&&(data={status:"error",message:i.msgNotJSON,responseText:responseText}),data.status=="error"?i.error(data.message):i.original=$.extend({},i.record)}}else i.error("AJAX Error "+xhr.status+": "+xhr.statusText),data={status:"error",message:i.msgAJAXerror,responseText:responseText};i.trigger($.extend(eventData,{phase:"after"})),i.refresh(),data.status=="success"&&typeof t=="function"&&t(data)}}).fail(function(n,t,r){var u={status:t,error:r,rawResponseText:n.responseText},f=i.trigger({phase:"before",type:"error",error:u,xhr:n});f.isCancelled!==!0&&(console.log("ERROR: server communication failed. The server should return",{status:"success"},"OR",{status:"error",message:"error message"},", instead the AJAX request produced this: ",u),i.trigger($.extend(f,{phase:"after"})))}),i.trigger($.extend(u,{phase:"after"}))}},50)}},lock:function(){var t=$(this.box).find("> div:first-child"),n=Array.prototype.slice.call(arguments,0);n.unshift(t),w2utils.lock.apply(window,n)},unlock:function(){var n=this;setTimeout(function(){w2utils.unlock(n.box)},25)},goto:function(n){typeof n!="undefined"&&(this.page=n),$(this.box).data("auto-size")===!0&&$(this.box).height(0),this.refresh()},generateHTML:function(){var t=[],r="",o,l,u,n,i,f,s,e,c,h;for(l in this.fields)u="",n=this.fields[l],typeof n.html=="undefined"&&(n.html={}),n.html=$.extend(!0,{caption:"",span:6,attr:"",text:"",page:0},n.html),typeof o=="undefined"&&(o=n.html.page),n.html.caption==""&&(n.html.caption=n.name),i='<input name="'+n.name+'" type="text" '+n.html.attr+"/>",(n.type==="pass"||n.type==="password")&&(i='<input name="'+n.name+'" type = "password" '+n.html.attr+"/>"),n.type=="checkbox"&&(i='<input name="'+n.name+'" type="checkbox" '+n.html.attr+"/>"),n.type=="textarea"&&(i='<textarea name="'+n.name+'" '+n.html.attr+"><\/textarea>"),n.type=="toggle"&&(i='<input name="'+n.name+'" type="checkbox" '+n.html.attr+' class="w2ui-toggle"/><div><div><\/div><\/div>'),n.html.group&&(r!=""&&(u+="\n   <\/div>"),u+='\n   <div class="w2ui-group-title">'+n.html.group+'<\/div>\n   <div class="w2ui-group">',r=n.html.group),n.html.page!=o&&r!=""&&(t[t.length-1]+="\n   <\/div>",r=""),u+='\n      <div class="w2ui-field '+(typeof n.html.span!="undefined"?"w2ui-span"+n.html.span:"")+'">\n         <label>'+w2utils.lang(n.html.caption)+"<\/label>\n         <div>"+i+w2utils.lang(n.html.text)+"<\/div>\n      <\/div>",typeof t[n.html.page]=="undefined"&&(t[n.html.page]=""),t[n.html.page]+=u,o=n.html.page;if(r!=""&&(t[t.length-1]+="\n   <\/div>"),this.tabs.tabs)for(f=0;f<this.tabs.tabs.length;f++)typeof t[f]=="undefined"&&(t[f]="");for(s in t)t[s]='<div class="w2ui-page page-'+s+'">'+t[s]+"\n<\/div>";if(e="",!$.isEmptyObject(this.actions)){c="",e+='\n<div class="w2ui-buttons">';for(h in this.actions)c=["save","update","create"].indexOf(h.toLowerCase())!=-1?"btn-green":"",e+='\n    <button name="'+h+'" class="btn '+c+'">'+w2utils.lang(h)+"<\/button>";e+="\n<\/div>"}return t.join("")+e},action:function(n,t){var i=this.trigger({phase:"before",target:n,type:"action",originalEvent:t});i.isCancelled!==!0&&(typeof this.actions[n]=="function"&&this.actions[n].call(this,t),this.trigger($.extend(i,{phase:"after"})))},resize:function(){function o(){s.width($(n.box).width()).height($(n.box).height()),i.css("top",n.header!=""?w2utils.getSize(t,"height"):0),u.css("top",(n.header!=""?w2utils.getSize(t,"height"):0)+(typeof n.toolbar=="object"&&$.isArray(n.toolbar.items)&&n.toolbar.items.length>0?w2utils.getSize(i,"height"):0)),f.css("top",(n.header!=""?w2utils.getSize(t,"height"):0)+(typeof n.toolbar=="object"&&$.isArray(n.toolbar.items)&&n.toolbar.items.length>0?w2utils.getSize(i,"height")+5:0)+(typeof n.tabs=="object"&&$.isArray(n.tabs.tabs)&&n.tabs.tabs.length>0?w2utils.getSize(u,"height")+5:0)),f.css("bottom",r.length>0?w2utils.getSize(r,"height"):0)}var n=this,e=this.trigger({phase:"before",target:this.name,type:"resize"});if(e.isCancelled!==!0){var s=$(this.box).find("> div"),t=$(this.box).find("> div .w2ui-form-header"),i=$(this.box).find("> div .w2ui-form-toolbar"),u=$(this.box).find("> div .w2ui-form-tabs"),f=$(this.box).find("> div .w2ui-page"),h=$(this.box).find("> div .w2ui-page.page-"+this.page),c=$(this.box).find("> div .w2ui-page.page-"+this.page+" > div"),r=$(this.box).find("> div .w2ui-buttons");o(),(parseInt($(this.box).height())==0||$(this.box).data("auto-size")===!0)&&($(this.box).height((t.length>0?w2utils.getSize(t,"height"):0)+(typeof this.tabs=="object"&&$.isArray(this.tabs.tabs)&&this.tabs.tabs.length>0?w2utils.getSize(u,"height"):0)+(typeof this.toolbar=="object"&&$.isArray(this.toolbar.items)&&this.toolbar.items.length>0?w2utils.getSize(i,"height"):0)+(f.length>0?w2utils.getSize(c,"height")+w2utils.getSize(h,"+height")+12:0)+(r.length>0?w2utils.getSize(r,"height"):0)),$(this.box).data("auto-size",!0)),o(),n.trigger($.extend(e,{phase:"after"}))}},refresh:function(){var c=(new Date).getTime(),i=this,s,o,n,t,e,f,h,u,r;if(this.box&&this.isGenerated&&typeof $(this.box).html()!="undefined"&&($(this.box).find("input, textarea, select").each(function(n,t){var e=typeof $(t).attr("name")!="undefined"?$(t).attr("name"):$(t).attr("id"),f=i.get(e),u,r;if(f&&(u=$(t).parents(".w2ui-page"),u.length>0))for(r=0;r<100;r++)if(u.hasClass("page-"+r)){f.page=r;break}}),s=this.trigger({phase:"before",target:this.name,type:"refresh",page:this.page}),s.isCancelled!==!0)){$(this.box).find(".w2ui-page").hide(),$(this.box).find(".w2ui-page.page-"+this.page).show(),$(this.box).find(".w2ui-form-header").html(this.header),typeof this.tabs=="object"&&$.isArray(this.tabs.tabs)&&this.tabs.tabs.length>0?($("#form_"+this.name+"_tabs").show(),this.tabs.active=this.tabs.tabs[this.page].id,this.tabs.refresh()):$("#form_"+this.name+"_tabs").hide(),typeof this.toolbar=="object"&&$.isArray(this.toolbar.items)&&this.toolbar.items.length>0?($("#form_"+this.name+"_toolbar").show(),this.toolbar.refresh()):$("#form_"+this.name+"_toolbar").hide();for(o in this.fields){n=this.fields[o],typeof n.name=="undefined"&&typeof n.field!="undefined"&&(n.name=n.field),typeof n.field=="undefined"&&typeof n.name!="undefined"&&(n.field=n.name),n.$el=$(this.box).find('[name="'+String(n.name).replace(/\\/g,"\\\\")+'"]'),n.el=n.$el[0],typeof n.el=="undefined"&&console.log('ERROR: Cannot associate field "'+n.name+'" with html control. Make sure html control exists with the same name.'),n.el&&(n.el.id=n.name),u=$(n).data("w2field"),u&&u.clear();$(n.$el).off("change").on("change",function(){var n=this.value,o=i.record[this.name]?i.record[this.name]:"",t=i.get(this.name),u,f,e,s,r,h;if(["list","enum","file"].indexOf(t.type)!=-1&&$(this).data("selected")){if(u=$(this).data("selected"),f=i.record[this.name],$.isArray(u)){n=[];for(e in u)n[e]=$.extend(!0,{},u[e])}if($.isPlainObject(u)&&(n=$.extend(!0,{},u)),$.isArray(f)){o=[];for(e in f)o[e]=$.extend(!0,{},f[e])}$.isPlainObject(f)&&(o=$.extend(!0,{},f))}if(t.type=="toggle"&&(n=$(this).prop("checked")?1:0),["int","float","percent","money","currency"].indexOf(t.type)!=-1&&(n=$(this).data("w2field").clean(n)),n!==o){if(s=i.trigger({phase:"before",target:this.name,type:"change",value_new:n,value_previous:o}),s.isCancelled===!0){$(this).val(i.record[this.name]);return}r=this.value,this.type=="select"&&(r=this.value),this.type=="checkbox"&&(r=this.checked?!0:!1),this.type=="radio"&&t.$el.each(function(n,t){t.checked&&(r=t.value)}),["int","float","percent","money","currency","list","combo","enum","file","toggle"].indexOf(t.type)!=-1&&(r=n),["enum","file"].indexOf(t.type)!=-1&&r.length>0&&(h=$(t.el).data("w2field").helpers.multi,$(h).removeClass("w2ui-error")),i.record[this.name]=r,i.trigger($.extend(s,{phase:"after"}))}});n.required?$(n.el).parent().parent().addClass("w2ui-required"):$(n.el).parent().parent().removeClass("w2ui-required")}$(this.box).find("button, input[type=button]").each(function(n,t){$(t).off("click").on("click",function(n){var t=this.value;this.id&&(t=this.id),this.name&&(t=this.name),i.action(t,n)})});for(o in this.fields)if(n=this.fields[o],t=typeof this.record[n.name]!="undefined"?this.record[n.name]:"",n.el){n.type=String(n.type).toLowerCase(),n.options||(n.options={});switch(n.type){case"text":case"textarea":case"email":case"pass":case"password":n.el.value=t;break;case"int":case"float":case"money":case"currency":case"percent":case"hex":case"alphanumeric":case"color":case"date":case"time":n.el.value=t,$(n.el).w2field($.extend({},n.options,{type:n.type}));break;case"toggle":w2utils.isFloat(t)&&(t=parseFloat(t)),$(n.el).prop("checked",t?!0:!1),this.record[n.name]=t?1:0;break;case"list":case"combo":if(n.type!="list"||$.isPlainObject(t))n.el.value=n.type!="combo"||$.isPlainObject(t)?$.isPlainObject(t)&&typeof t.text!="undefined"?t.text:"":t;else for(r in n.options.items)if(e=n.options.items[r],$.isPlainObject(e)&&e.id==t){t=$.extend(!0,{},e),i.record[n.name]=t;break}else if(r==t){t={id:r,text:e},i.record[n.name]=t;break}$.isPlainObject(t)||(t={}),$(n.el).w2field($.extend({},n.options,{type:n.type,selected:t}));break;case"enum":case"file":$.isArray(t)||(t=[]),$(n.el).w2field($.extend({},n.options,{type:n.type,selected:t}));break;case"select":if(f=n.options.items,typeof f!="undefined"&&f.length>0){f=w2obj.field.prototype.normMenu(f),$(n.el).html("");for(h in f)$(n.el).append('<option value="'+f[h].id+'">'+f[h].text+"<\/option")}$(n.el).val(t);break;case"radio":$(n.$el).prop("checked",!1).each(function(n,i){$(i).val()==t&&$(i).prop("checked",!0)});break;case"checkbox":$(n.el).prop("checked",t?!0:!1);break;default:$(n.el).w2field($.extend({},n.options,{type:n.type}))}}for(u=$(this.box).find(".w2ui-page"),r=0;r<u.length;r++)$(u[r]).find("> *").length>1&&$(u[r]).wrapInner("<div><\/div>");return this.trigger($.extend(s,{phase:"after"})),this.resize(),(new Date).getTime()-c}},render:function(n){function e(){var n=$(t.box).find("input, select, textarea");n.length>t.focus&&n[t.focus].focus()}var f=(new Date).getTime(),t=this,i,r,u;if((typeof n=="object"&&($(this.box).find("#form_"+this.name+"_tabs").length>0&&$(this.box).removeAttr("name").removeClass("w2ui-reset w2ui-form").html(""),this.box=n),this.isGenerated)&&this.box&&(i=this.trigger({phase:"before",target:this.name,type:"render",box:typeof n!="undefined"?n:this.box}),i.isCancelled!==!0)){if($.isEmptyObject(this.original)&&!$.isEmptyObject(this.record)&&(this.original=$.extend(!0,{},this.record)),r="<div>"+(this.header!=""?'<div class="w2ui-form-header">'+this.header+"<\/div>":"")+'    <div id="form_'+this.name+'_toolbar" class="w2ui-form-toolbar"><\/div>    <div id="form_'+this.name+'_tabs" class="w2ui-form-tabs"><\/div>'+this.formHTML+"<\/div>",$(this.box).attr("name",this.name).addClass("w2ui-reset w2ui-form").html(r),$(this.box).length>0&&($(this.box)[0].style.cssText+=this.style),typeof this.toolbar.render!="function"){this.toolbar=$().w2toolbar($.extend({},this.toolbar,{name:this.name+"_toolbar",owner:this}));this.toolbar.on("click",function(n){var i=t.trigger({phase:"before",type:"toolbar",target:n.target,originalEvent:n});i.isCancelled!==!0&&t.trigger($.extend(i,{phase:"after"}))})}if(typeof this.toolbar=="object"&&typeof this.toolbar.render=="function"&&this.toolbar.render($("#form_"+this.name+"_toolbar")[0]),typeof this.tabs.render!="function"){this.tabs=$().w2tabs($.extend({},this.tabs,{name:this.name+"_tabs",owner:this}));this.tabs.on("click",function(n){t.goto(this.get(n.target,!0))})}if(typeof this.tabs=="object"&&typeof this.tabs.render=="function"&&this.tabs.render($("#form_"+this.name+"_tabs")[0]),this.trigger($.extend(i,{phase:"after"})),this.resize(),u=typeof this.url!="object"?this.url:this.url.get,u&&this.recid!=0?this.request():this.refresh(),$(".w2ui-layout").length==0){this.tmp_resize=function(){w2ui[t.name].resize()};$(window).off("resize","body").on("resize","body",this.tmp_resize)}return setTimeout(function(){t.resize(),t.refresh()},150),this.focus>=0&&setTimeout(e,500),(new Date).getTime()-f}},destroy:function(){var n=this.trigger({phase:"before",target:this.name,type:"destroy"});n.isCancelled!==!0&&(typeof this.toolbar=="object"&&this.toolbar.destroy&&this.toolbar.destroy(),typeof this.tabs=="object"&&this.tabs.destroy&&this.tabs.destroy(),$(this.box).find("#form_"+this.name+"_tabs").length>0&&$(this.box).removeAttr("name").removeClass("w2ui-reset w2ui-form").html(""),delete w2ui[this.name],this.trigger($.extend(n,{phase:"after"})),$(window).off("resize","body"))}},$.extend(n.prototype,w2utils.event),w2obj.form=n}();
//@ sourceMappingURL=w2ui-1.4.min.js.map