/*
 * call requests client code for towbook
 * 
 */

Pusher.log = function (message) {
  return;
  if (window.console && window.console.log)
    window.console.log('[Towbook-Pusher]', message);
};
WEB_SOCKET_DEBUG = true;

var activateCallRequestTracking = function (companyId) {
  var options = {
    playSound: true,
    lastMinimizedState: false
  };

  $.playSound('/UI/audio/init', "init sound to prevent deferrment of audio");

  // Initialize localstorage for 'CallRequestOptions'
  // If localstorage exists, retrieve options
  if (!TowbookStor.contains('CallRequestOptions'))
    TowbookStor.set('CallRequestOptions', $.extend({}, options));
  else
    options = $.extend(options, {}); // TowbookStor.get('CallRequestOptions', {}));

  $('#callRequestsBar').show();

  towbook.compileTemplate('t-callRequest', '#t-callRequest');

  var pusher = window.pusher = window.pusher || new Pusher('00d0fb70749a0a4fd6f9', { authEndpoint: '/api/pusher', authDelay: 200, authorizer: PusherBatchAuthorizer });

  var hideCall = function (id) {
    $('#callRequestContainer li[data-id=' + id + ']').slideUp(400, function () {
      this.remove();
    });
  };

  $('.block-cancel').on('click', function () {
    $.unblockUI({ fadeOut: 0 });
  });

  $("#cr-reject").on('click', function () {
    rejectCall($('#cr-reject').closest('#callRequestViewer').data('id'), $('#rc-reason').val());
  }).addClass('button');

    $('.callRequestCancel').addClass('button')
    $("#cr-accept").on('click', function () {
        acceptCall($('#cr-accept').closest('#callRequestViewer').data('id'),
            $('#cr-eta').val(),
            "",
            $('#cr-driver').val(),
            $('#cr-tire-yesno').val() == "true" ? true : false,
            $('#cr-tire-notes').val()
        );
    }).addClass('button');

  $("#cr-accept-manual").on('click', function () {
    console.log("accept");
    manuallyAcceptCall($('#cr-accept-manual').closest('div').data('id'), $('#cr-eta').val(), $('#cr-purchaseorder').val());
  }).addClass('button');


  var rejectCall = function (id, reason) {
    $.ajax({
      url: '/api/callRequests/' + id + '/reject',
      type: 'POST',
      contentType: "application/json; charset=utf-8",
      data: JSON.stringify({ id: id, masterAccountReasonId: reason })
    }).done(function (data) {
      hideCall(id);

      $('#rejectCall').hide();
      $.unblockUI();
    });
  };

  var acceptCall = function (id, eta, comments, driverId, tireAvailable, notes) {
    var forceConfirm = false;

    if (eta < 20 || eta > 60)
      forceConfirm = true;


    if ($('#cr-tire-row').is(":visible") && $('#cr-tire-yesno').val() == '') {
      alert('Please specify whether you have the requested tire available.');
      return;
    }

    if ((!$('body').hasClass("confirm-eta") && !forceConfirm) || confirm('Are you sure you want to accept this call with an ETA of:\n\n **' + eta + "** MINUTES")) {

      var dataToSend = {
        id: id,
        comments: comments,
        ETA: eta,
        driverId: driverId || 0,
        notes: notes,
        tireAvailable: tireAvailable
      };

      if ($('#cr-reason').is(':visible'))
        dataToSend.masterAccountReasonId = $('#cr-reason').val();

      console.log("sending", dataToSend);

      $.ajax({
        url: "/api/callRequests/" + id + '/accept',
        type: 'POST',
        contentType: "application/json; charset=utf-8",
        data: JSON.stringify(dataToSend)
      }).done(function (data) {
        hideCall(id);
        $('#acceptCall').hide();
        $.unblockUI();
      });
    }
  };

  var manuallyAcceptCall = function (id, eta, po) {
    console.log("accept");
    if (po == null || po.length == 0) {
      alert('Please enter the PO # for this call before clicking Manually Accept.');
    }
    $.ajax({
      url: "/api/callRequests/" + id + '/acceptMissedRequest',
      type: 'POST',
      contentType: "application/json; charset=utf-8",
      data: JSON.stringify({ id: id, purchaseOrderNumber: po, eta: eta })
    }).done(function (data) {
      hideCall(id);
      $.unblockUI();
    });
  };

  var requestPhoneCall = function (id, oncomplete) {
    $.ajax({
      url: "/api/callRequests/" + id + '/requestPhoneCall',
      type: 'POST',
      contentType: "application/json; charset=utf-8",
      data: JSON.stringify({ id: id, reason: 'Phone Call Request' })
    }).done(function (data) {
      hideCall(id);
      if (oncomplete)
        oncomplete();
    });
  };

  $('#callRequestViewer .x-accept').off('click').on('click', function () {
    var id = $(this).closest('#callRequestViewer').data('id');

    $('#cr-reason').html('');

    var cr = towbook.get(towbook.callRequests, id, 'callRequestId');
    var masterAccountId = cr.masterAccountId;
    var masterAccount = towbook.get(towbook.masterAccounts, masterAccountId, 'masterAccountId');
    var acceptReasons = $.grep(masterAccount.reasons, function (o) { return o.type == 1 }).map(
      function (o) { return { id: o.masterAccountReasonId, name: o.name } });
    $('#cr-reason').appendOptions(acceptReasons, false);

    $('#cr-eta option').remove();

    if (cr.supportedEtas) {
      $(cr.supportedEtas).each(function (i, o) {
        $('#cr-eta').append("<option>" + o + "</option>");
      });
    }

    $('#cr-eta').setVal(cr.supportedEtas[0]);

    if (acceptReasons.length == 0)
      $('#cr-reason,#cr-reason-label').hide();
    else
      $('#cr-reason,#cr-reason-label').show();

    if (masterAccountId == 21 || masterAccountId == 34 || masterAccountId == 32) {
      $('#cr-driver,#cr-driver-label').show();
      $('#cr-driver').html('');

      if (!cr.drivers || cr.drivers.length == 0)
        cr.drivers = $.map(
          $.grep(towbookDrivers, function (e) {
            return !e.companies || e.companies.indexOf(cr.companyId) != -1
          }),
          function (i) {
            return i.id
          });

      $.each(cr.drivers, function (index, item) {
        var tbd = towbook.get(towbookDrivers, item);
        if (tbd != null) {
          var name = tbd.name;
          $('#cr-driver').append('<option value="' + item + '">' + name + '</option>');
        }
      });
    }
    else
      $('#cr-driver,#cr-driver-label').hide();

    if (masterAccountId == 27 && cr.serviceNeeded == "Tire") {
      $('#cr-tire-row').show();
      $('#cr-tire-yesno').val('');
      $('#cr-tire-notes').val('');
      $('#cr-tire-notes,#cr-tire-notes-label').hide();
    } else {
      $('#cr-tire-row').hide();
    }
    if (cr.purchaseOrderNumber)
      $('#acceptCall > h2').html('Accept ' + masterAccount.name + ' Call (Dispatch # ' + cr.purchaseOrderNumber + ')');
    else
      $('#acceptCall > h2').html('Accept ' + masterAccount.name);

    $('#acceptCall').data("id", id);
    $("#cr-accept, #cr-reject").prop("disabled", false);

    if (cr.defaultEta)
      $('#cr-eta').val(cr.defaultEta);

    if ($('.subContainer > div').is(':visible'))
      $('.subContainer > div').slideUp();

    if ($('.x-call-request-scheduled').length == 1) {
      $('label[for="cr-eta"]').hide();
      $('#cr-eta').hide();
      $('#cr-eta').val('60');
    } else {
      $('label[for="cr-eta"]').show();
      $('#cr-eta').show();
    }

    $('#acceptCall').slideDown();

    return false;
  });

  $('#callRequestViewer .x-reject').on('click', function () {
    var id = $(this).closest('#callRequestViewer').data('id');

    $('#rejectCall').data("id", id);
    $('#rc-reason').html('');

    var cr = towbook.get(towbook.callRequests, id, 'callRequestId');
    var masterAccountId = cr.masterAccountId;
    var masterAccount = towbook.get(towbook.masterAccounts, masterAccountId, 'masterAccountId');

    $('#rc-reason').appendOptions(
      $.map($.grep(masterAccount.reasons, function (o) { return o.type == 2 }),
        function (o) {
          return { id: o.masterAccountReasonId, name: o.name };
        }), false);

    $('#rejectCall > h2').html('Refuse ' + masterAccount.name + ' Call (PO# ' + cr.purchaseOrderNumber + ')');
    $("#cr-accept, #cr-reject").prop("disabled", false);
    if (masterAccountId == 21) {
      $('#rc-reason, #rc-reason-label').hide();
    } else {
      $('#rc-reason, #rc-reason-label').show();
    }

    if ($('.subContainer > div').is(':visible'))
      $('.subContainer > div').slideUp();

    $('#rejectCall').slideDown();

    return false;
  });

  $('#callRequestViewer .x-requestCall').on('click', function () {
    var id = $(this).closest('#callRequestViewer').data('id');
    requestPhoneCall(id, function () { $.unblockUI(); });
    return false;
  });

  $('#callRequestViewer .x-viewMap').on('click', function () {
    $.unblockUI();
    var id = $(this).closest('#callRequestViewer').data('id');
    var win = window.open('/map2/callRequest.aspx?id=' + id, '_blank');
    win.focus();
  });

  $('#cr-eta').on('change', function () {
    var self = $(this);
    var id = $(this).closest('#callRequestViewer').data('id');
    var cr = towbook.get(towbook.callRequests, id, 'callRequestId');
    var maxEta = cr != null && cr.maxEta > 0 ? cr.maxEta : 45;

    if ($('#cr-reason option').length > 0) {
      // TODO: change 45 to MaxETA value from motor club
      if (parseInt(self.val()) >= maxEta) {
        $('#cr-reason-label, #cr-reason').show();
      } else {
        $('#cr-reason-label, #cr-reason').hide();
      }
    }
  });



  $('#cr-tire-yesno').on('change', function () {
    if ($('#cr-tire-yesno').val() != 'false') {
      $('#cr-tire-notes-label,#cr-tire-notes').hide();
    } else {
      $('#cr-tire-notes-label,#cr-tire-notes').show();
    }
  });

  var assignEvents = function (e) {
    e = $(e);

    var e2 = $('#callRequestViewer');

    e.off('click').on('click', function () {

      var id = $(e).data('id');
      var cr = towbook.get(towbook.callRequests, id, 'callRequestId');
      var masterAccountId = cr.masterAccountId;
      var masterAccount = towbook.get(towbook.masterAccounts, masterAccountId, 'masterAccountId');

      $('#callRequestViewer .masterAccountName').html(masterAccount.name);

      if ((cr.availableActions && cr.availableActions.indexOf('REQUEST_CALL') == -1) && cr.masterAccountId != 20)
        $('#callRequestViewer .x-requestCall').hide();
      else
        $('#callRequestViewer .x-requestCall').show();

      $('#callRequestViewer .timer > span:first').data('id', id).text('');

      $('#callRequestViewer').data('id', id);

      $('#callRequestViewer .subContainer > div').hide();

      var summary = $('#callRequestViewer .summary > ul');
      var details = $('#callRequestViewer .details');
      summary.html('');
      details.html('');

      $.ajax({
        url: "/api/callRequests/" + id + "?rawDetails=1",
        contentType: "application/json; charset=utf-8",
      }).done(function (data) {
        details.html(data);
      })

      console.log(cr);

      var addDetail = function (obj, key, value) {
        if (value == null || value.length == 0)
          return;

        var extra = "";
        if (key == "Location") {
          var warnStyle = "margin-left: 5px;font-weight: bold;background-color: yellow;color: black;padding: 2px 4px;border: solid 1px yellow;border-radius: 5px;"
          var regularStyle = "margin-left: 5px;font-weight: bold;background-color: #555;color: white;padding: 2px 4px;border-radius: 5px;"

          if (cr.distance && cr.distance > 0) {
            if (cr.distance >= 25)
              extra = "<span style=\"" + warnStyle + "\">" + cr.distance + " miles</span>";
            else
              extra = "<span style=\"" + regularStyle + "\">" + cr.distance + " miles</span>";
          }
        }

        $(obj).append('<li><strong>' + key + '</strong>' + value + extra + '</li>');
      }

      addDetail(summary, 'Vehicle', cr.vehicle)
      addDetail(summary, 'Service Needed', cr.serviceNeeded)
      addDetail(summary, 'Location', cr.startingLocation)


      addDetail(summary, 'Destination', cr.towDestination)

      if (towbookCompanies && towbookCompanies.length > 1) {
        var comp = towbook.get(towbookCompanies, cr.companyId);
        if (comp)
          addDetail(summary, 'Company', comp.name)
      }

      $.blockUI({
        fadeIn: 0,
        message: $('#callRequestViewer'), css: {
          'border': 0,
          'padding': 0,
          'margin': '0 auto',
          'width': '50%',
          'top': '100px',
          'left': '25%',
          'textAlign': 'center',
          'background': 'white',
          'color': '#333333',
          '-moz-opacity': '0.9',
          '-khtml-opacity': '0.9',
          'opacity': '0.9',
          'filter': 'alpha(opacity=90)',
          '-webkit-box-shadow': '5px 5px 30px 0 black',
          '-moz-box-shadow': '5px 5px 30px 0 black',
          'box-shadow': '5px 5px 30px 0 black',
        }
      });
    });

    var cr = towbook.get(towbook.callRequests, e.data('id'), 'callRequestId');

    var x = (new Date(cr.expirationDateUtc) - new Date())

    var expObj = $(e).find(".expiration");

    if (x > 0) {
      doTimer(x, 1,
        function (steps, count) {
          if (!$(e).is(":visible"))
            return;

          $(e).find(".time").text(parseInt((x / 1000) - count) + " seconds ");
          $("#callRequestViewer .timer > span").filterByData('id', cr.callRequestId).text(parseInt((x / 1000) - count));

          var count = parseInt((x / 1000) - count);
          if (count == 30) {
            if (options.playSound)
              $.playSound('/UI/audio/callRequest', "less than 30 seconds remaining for #" + cr.callRequestId);
          }

          if (count <= 30) {
            if (!expObj.hasClass('expiration-less-than-30'))
              expObj.addClass('expiration-less-than-30');
          }
        },
        function () {
          console.log("Timer expired for #" + cr.callRequestId);
        });
    }

    return e;
  };

  (function populateCallRequests() {
    var listObj = $('callRequestContainer > ul');

    listObj.html('');

    $.ajax({
      url: "/api/callRequests/",
      contentType: "application/json; charset=utf-8",
    }).done(function (data) {
      if (data != null) {
        $.each(data, function (index, data) {

          data.account = { id: data.accountId, company: data.accountName };

          if (towbookCompanies && towbookCompanies.length > 1) {
            var comp = towbook.get(towbookCompanies, data.companyId);
            if (comp)
              data.company = comp.name;
          }

          var out = towbook.applyTemplate('t-callRequest', data);

          towbook.callRequests.push(data);
          $('#callRequestContainer > ul').append(assignEvents(out));
        });

        if (data.length > 0) {
          if (options.playSound)
            $.playSound('/UI/audio/callRequest', "populateCallRequests-page load");

          if (towbook.get(towbookCompanies, 18278) != null) {
            var timer1 = null;
            var timer2 = null;

            var audioPlay1 = function () { $.playSound('/UI/audio/callRequest', "play second time #" + data.callRequestId); clearTimeout(timer1); };
            var audioPlay2 = function () { $.playSound('/UI/audio/callRequest', "play third time  #" + data.callRequestId); clearTimeout(timer2); };

            timer1 = setTimeout(audioPlay1, 3000);
            timer2 = setTimeout(audioPlay2, 6000);
          }

          // check last state of expand/collapsed

          if (options.lastMinimizedState) {
            $('#callRequestContainer').hide();
            $('#callRequestsBar').removeClass('expanded')
          }

          else {
            $('#callRequestContainer').show();
            $('#callRequestsBar').addClass('expanded');
          }


          // pulse the bar to indicate there are items

          $('#callRequestsBar').addClass('pulse');
        }
      }
    });
  })();

  (function populateMasterAccounts() {
    $.ajax({
      url: "/api/masterAccounts/?typeId=5",
      cache: true,
      contentType: "application/json; charset=utf-8",
    }).done(function (data) {
      towbook = $.extend({}, towbook, { masterAccounts: data });
    });
  })();

  towbook = $.extend({}, towbook, { callRequests: [] });

  var pusherFunctions = {
    callRequest: function (data) {
      $.ajax({
        url: "/api/callRequests/" + data.id,
        contentType: "application/json; charset=utf-8",
      }).done(function (data) {
        if (!data)
          return;
        data.account = { id: data.accountId, company: data.accountName };

        if (towbookCompanies && towbookCompanies.length > 1) {
          var comp = towbook.get(towbookCompanies, data.companyId);
          if (comp)
            data.company = comp.name;
        }

        towbook.callRequests.push(data);
        $('#callRequestContainer > ul').append(assignEvents(towbook.applyTemplate('t-callRequest', data)));

        if (options.playSound)
          $.playSound('/UI/audio/callRequest', 'play sound for #' + data.callRequestId);

        // Make sure container is visible
        $('#callRequestContainer').show();

        // Add necesssary classes to parent object
        $('#callRequestsBar').addClass('pulse').addClass('expanded');
      });
    },
    callRequestStatusChanged: function (data) {
      // sample: {"callRequestId":659,"status":1}
      console.log("call request status update received:", data);
      // find item
      var item = $('#callRequestContainer li[data-id=' + data.callRequestId + ']');
      if (item.length > 0) {
        item = item[0];

        // update status as data property for filtering reasons
        $(item).attr('data-status', data.status).data('status', data.status);

        $(item).slideUp(function () {
          // check if this is the last item to be removed. Only pulse the bar if there
          // still items with status of 0
          if ($('#callRequestContainer').find('li.callRequest').filterByData('status', 0).length == 0)
            $('#callRequestsBar').removeClass('pulse').removeClass('expanded');
        });
      }

      if ($('#callRequestViewer').data("id") == data.callRequestId) {
        // remove the call request viewer
        $.unblockUI();
      }

      var msg = "";

      if (data.status == '40' || data.status == '4' || data.status == '80') {
        $.ajax({
          url: "/api/callRequests/" + data.callRequestId,
          contentType: "application/json; charset=utf-8"
        }).done(function (callData) {
          if (callData == null)
            return;

          var timeout = 3000;

          if (data.status == '40') {
            msg = "Your ETA was rejected by " + callData.accountName + ". Call not accepted.";
          } else if (data.status == '80') {
            msg = "The job you tried to accept from " + callData.accountName + " was already accepted by another company. Call not accepted.";
          } else if (data.status == '4') {
            msg = `Call Cancelled digitally by ${callData.accountName} for PO# <a style='color:white'
                            href='/DispatchEditor/Editor.aspx?id=${callData.dispatchEntryId}' id="callEditorLink_${callData.dispatchEntryId}">${callData.purchaseOrderNumber}</a>.
                            <br /><br /><a href='#' style='color: white' onclick='$.unblockUI();'>Dismiss</a>`;
            timeout = 100000;
            return;
          }

          $.growlUI('Digital Dispatch', msg, timeout);
        });
      }
    },
    loginStatus: function (data) {

    },
    backgroundStatusUpdate: function (data) {
      console.log("houston, we have liftoff!", data);
    }
  }

  window.pusherFunctions = pusherFunctions;

  if (pusher.channel('private-TWBK_Client_' + companyId) == null)
    pusher.subscribe('private-TWBK_Client_' + companyId)

  pusher.channel('private-TWBK_Client_' + companyId)
    .unbind('call_request', pusherFunctions.callRequest)
    .bind('call_request', pusherFunctions.callRequest)
    .unbind('callRequest_statusChanged', pusherFunctions.callRequestStatusChanged)
    .bind('callRequest_statusChanged', pusherFunctions.callRequestStatusChanged)
    .unbind('account_ddLoginStatus', pusherFunctions.loginStatus)
    .bind('account_ddLoginStatus', pusherFunctions.loginStatus)
    .unbind('backgroundStatusUpdate', pusherFunctions.backgroundStatusUpdate)
    .bind('backgroundStatusUpdate', pusherFunctions.backgroundStatusUpdate);

  console.log("loaded call request bar");

  toggleRequestContainer = function (show) {
    if (show) {
      // expand
      $('#callRequestContainer').slideDown();
      $('#callRequestsBar').addClass('expanded');
      options.lastMinimizedState = false;
    }
    else {
      // minimize
      $('#callRequestsBar').removeClass('expanded');
      $('#callRequestContainer').slideUp();
      options.lastMinimizedState = true;
    }

    // save to localstorage the lastMinimizedState
    TowbookStor.set('CallRequestOptions', options);
  }

  $('#toggleRequestBar').on('click', function () {
    var retObj = $('#callRequestsBar').toggleClass("full-screen");
    if ($(retObj).hasClass('full-screen'))
      toggleRequestContainer(true);
    return false;
  });

  $('#toggleRequestView').on('click', function () {
    if ($('#callRequestsBar').hasClass("full-screen"))
      $('#callRequestsBar').toggleClass("full-screen");

    if ($('#callRequestsBar').hasClass('expanded'))
      toggleRequestContainer(false);
    else
      toggleRequestContainer(true);

    return false;
  });
};

function doTimer(length, resolution, oninstance, oncomplete) {
  var steps = parseInt((length / 100) * (resolution / 10)),
    speed = length / steps,
    count = 0,
    start = new Date().getTime();

  function instance() {
    if (count++ == steps) {
      oncomplete(steps, count);
    }
    else {
      oninstance(steps, count);

      var diff = (new Date().getTime() - start) - (count * speed);
      window.setTimeout(instance, (speed - diff));
    }
  }

  window.setTimeout(instance, speed);
}

$.fn.filterByData = function (prop, val) {
  return this.filter(
    function () { return $(this).data(prop) == val; }
  );
}