// chat.js
// This singleton object is used to handle pusher events across Towbook.
// Should include the following in options...
//      companyIds: binds the pusher events to the company(ies)
//      userId: the ONE user that is using this class.
//      onEvent: the callback method to invoke when an event is caught
// 

// Pusher setup
var pusher = window.pusher = window.pusher || new Pusher('00d0fb70749a0a4fd6f9', { authEndpoint: '/api/pusher', authDelay: 200, authorizer: PusherBatchAuthorizer });

// Debug messages suggested by pusher
pusher.connection.bind('state_change', function (states) {
  console.log("Pusher's current state is " + states.current);
});


var Chat = (function () {
  // the singleton object of Chat
  var instance;

  // used as an unique identifier for bound pusher events
  var idGenerator = 1;
  
  // local repository of registered callbacks for each chatId
  var callbacks = [];

  // local repository of chat items registered with events
  var chats = [];

  // local repository of companyIds used for current chat
  var currentCompanyIds = [];

  // local current user Id
  var currentUserId = 0;

  var chatEnabled = false;
  
  // this constructor should only be called once!
  function createInstance(companyIds, userId, enabled) {
    var object = new Object("The Instance of Chat");

    // initalize local properties
    currentCompanyIds = companyIds;
    currentUserId = userId;
    chatEnabled = enabled;

    // bind global ui events for ChatBar
    ChatBar.initialize(userId, chatEnabled);

    // hook pusher event to local callback repository
    function bindEventToCallbacks(companyId, userId) {
      var obj = this;

      // make sure the pusher channel exists
      if (pusher.channel('private-TWBK_Client_' + companyId + '-' + userId + '-chat') == null)
        pusher.subscribe('private-TWBK_Client_' + companyId + '-' + userId + '-chat');

      if (pusher.channel('private-TWBK_Client_' + companyId) == null)
        pusher.subscribe('private-TWBK_Client_' + companyId);

      var chatPusherFunctions = (function () {

        // Filter all the registered callback methods by type and
        // execute the registered callback handler
        var pusherHandler = function (type, data) {
          callbacks.map(function (c) {
            if (type == 'new_message' && c.chatId == data.chatId && type == c.type) {
              c.onEvent(data);
              console.log('[chat -> pusher event -> new_message] pusher event received for chatId # / eventId #', data.chatId, c.id);
            }
            else if (type == 'message_read' && c.chatId == data.chatId && type == c.type) {
              c.onEvent(data);
              console.log('[chat -> pusher event -> message_read] pusher event received for chatId # / eventId #', data.chatId, c.id);
            }
            else if (type == 'new_chat' && type == c.type) {
              c.onEvent(data);
              console.log('[chat -> pusher event -> new_chat] pusher event received for chatId # / eventId #', data.id, c.id);
            }
            else if (type == 'delete_chat' && type == c.type) {
              c.onEvent(data);
              console.log('[chat -> pusher event -> delete_chat] pusher event received for chatId # / eventId #', data.id, c.id);
            }
            else if (type == 'user_status' && data.id == userId && type == c.type) {
              c.onEvent(data);
              console.log('[chat -> pusher event -> user_status] pusher event received with userId # / statusId  # / eventId #', data.id, data.statusId, c.id);
            }
            else if (type == 'update_chat' && type == c.type) {
              c.onEvent(data);
              console.log('[chat -> pusher event -> update_chat] pusher event received for chatId # / eventId #', data.id, c.id);
            }
            else if (type == 'chat_kick' && c.chatId == data.id && type == c.type) {
              c.onEvent(data);
              console.log('[chat -> pusher event -> chat_kick] pusher event received for chatId # / eventId #', data.id, c.id);
            }
            else if (type == 'chat_members' && c.chatId == data.id && type == c.type) {
              c.onEvent(data);
              console.log('[chat -> pusher event -> chat_members] pusher event received for chatId # / eventId #', data.id, c.id);
            }
          });
        }

        return {
          newChatMessage: function (data) {
            return pusherHandler('new_message', data);
          },
          chatMessageRead: function (data) {
            return pusherHandler('message_read', data);
          },
          newChat: function (data) {
            return pusherHandler('new_chat', data);
          },
          deleteChat: function (data) {
            return pusherHandler('delete_chat', data);
          },
          updateUserStatus: function (data) {
            return pusherHandler('user_status', data);
          },
          updateCallChat: function (data) {
            return pusherHandler('update_chat', data);
          },
          updateChatMembers: function (data) {
            return pusherHandler('chat_members', data);
          },
          kickChatMember: function(data) {
            return pusherHandler('chat_kick', data);
          }
        }
      })();


      // bind the 'new-message' event to the correct callback function
      pusher.channel('private-TWBK_Client_' + companyId + '-' + userId + '-chat')
        .unbind('new_message', chatPusherFunctions.newChatMessage)
        .bind('new_message', chatPusherFunctions.newChatMessage);

      // bind the 'message-read' event to the correct callback function
      pusher.channel('private-TWBK_Client_' + companyId + '-' + userId + '-chat')
        .unbind('message_read', chatPusherFunctions.chatMessageRead)
        .bind('message_read', chatPusherFunctions.chatMessageRead);

      // bind the 'message-read' event to the correct callback function
      pusher.channel('private-TWBK_Client_' + companyId + '-' + userId + '-chat')
        .unbind('chat_members', chatPusherFunctions.updateChatMembers)
        .bind('chat_members', chatPusherFunctions.updateChatMembers);

      // bind the 'message-read' event to the correct callback function
      pusher.channel('private-TWBK_Client_' + companyId + '-' + userId + '-chat')
        .unbind('chat_kick', chatPusherFunctions.kickChatMember)
        .bind('chat_kick', chatPusherFunctions.kickChatMember);
      
      // bind the 'new_chat' event to the correct callback function
      pusher.channel('private-TWBK_Client_' + companyId)
        .unbind('new_chat', chatPusherFunctions.newChat)
        .bind('new_chat', chatPusherFunctions.newChat);

      // bind the 'delete_chat' event to the correct callback function
      pusher.channel('private-TWBK_Client_' + companyId)
        .unbind('delete_chat', chatPusherFunctions.deleteChat)
        .bind('delete_chat', chatPusherFunctions.deleteChat);

      // bind the 'user_status' event to the correct callback function
      pusher.channel('private-TWBK_Client_' + companyId)
        .unbind('user_status', chatPusherFunctions.updateUserStatus)
        .bind('user_status', chatPusherFunctions.updateUserStatus);

      // bind the 'chat-update' event to the correct callback function
      pusher.channel('private-TWBK_Client_' + companyId)
        .unbind('update_chat', chatPusherFunctions.updateCallChat)
        .bind('update_chat', chatPusherFunctions.updateCallChat);

    }

    var cIds = [];
    // turn single companyId into an array of companyIds
    if (towbook.isEmpty(companyIds) && !isNaN(parseInt(companyIds)))
      cIds.push(parseInt(companyIds));
    else
      cIds = companyIds;

    // bind callback event to pusher event for each companyId and userId
    for (var i = 0; i < cIds.length; i++) {
      var companyId = cIds[i];
      if (!isNaN(parseInt(companyId))) {

        // bind event to pusher event
        bindEventToCallbacks(companyId, userId);
      }
    }

    // return the one instance of Chat
    return Object;
  }


  return {
    getInstance: function (companyIds, userId, chat, type, onEvent) {
      // Check that parameters are valid
      if (towbook.isEmpty(companyIds)
        || userId == 0
        || typeof onEvent !== "function") {
        console.log("ERROR: invalid parameters specified for Chat");
        return instance;
      }

      // createInstance should only be run once (Singleton)
      if (!instance) {
        instance = createInstance(companyIds, userId, ChatBar.isEnabled());
      }

      var chatId = null;
      if (chat != null && typeof chat === 'object') {
        if (chat.hasOwnProperty('id')) {
          chatId = chat.id;
          if (towbook.get(chats, chat.id, "id") == null) {
            chats.push(chat);
            ChatBar.addChatListItem(chat);
          }
        }
      }
      else if (typeof chat === 'string')
        chatId = int.parseInt(chat);
      else
        chatId = chat;

      if (typeof chatId !== 'number') {
        // register chat independent events here.  For instance, pusher events by company
        if (companyIds != undefined && companyIds != null) {
          if (callbacks.filter(function (c) { return c.onEvent === onEvent && type === type }).length == 0) {
            callbacks.push({ id: idGenerator++, chatId: null, type: type, onEvent: onEvent, companyIds: companyIds, userId: userId });
          }
        }
      }
      else
      {
        // register callback event to local repo with the chatId and type
        if (chatId != undefined && chatId != null) {
          if (callbacks.filter(function (c) { return c.chatId == chatId && c.onEvent === onEvent && type === type }).length == 0) {
            callbacks.push({ id: idGenerator++, chatId: chatId, type: type, onEvent: onEvent, companyIds: companyIds, userId: userId });
          }
        }
      }

      // return the one instance
      return instance;
    },
    // useful debug method
    getRegisteredEvents: function() {
      return callbacks;
    },
    getRegisteredCompanyIds: function () {
      return currentCompanyIds;
    },
    getRegisteredUserId: function () {
      return currentUserId;
    },
    getChatEnabled: function () {
      return chatEnabled;
    },
    // this method returns a distinct users array
    getAllUsers: function() {
      var users = [];

      towbookUsers.map(function (value) {
        value.name = _enc(value.name);
        if (towbook.get(users, value.id, "id") == null)
          users.push(value);
      });

      return users;
    }

  };
})();


var ChatBar = (function () {
  /****************************
    START ChatBar local variables
  *****************************/
  var chatItems = [];       // master list of chat objects
  var dragging = false;     // used for draggable bar
  var chatEnabled = true;   // set to false, if feature is disabled
  var initialized = false;  // make sure we don't initialize twice
  var autoScroll = true;    // screen should auto scroll on new message
  var currentUserId = null; // current user
  var page = 1;             // api paging no
  var pageSize = 25;        // api page size
  var loadMore = true;      // allow for more queries to the server
  var isLoadingMore = false;// stop multiple loads on the same scroll event
  var syncReadTimer = null; // timer variable used for 'read by' pusher event

  // default options for everyone
  var options = {
    width: 365,         // default width of ChatBar

    visible: false,     // visible state of ChatBar:
                        // (true) chat list is visible
                        // (false) = chat bar is not visible
                        // (int) chatId that is visible

    activeType: 2,      // the type of chat to display (unknown=0, company=1, call=2, private=3)
    activeStatus: 0,    // the status of the chat to display (active=0, closed=1)

    unreadChatIds: [],  // array of chatIds that indicate last known unread status
  };
  /****************************
    END ChatBar local variables
  *****************************/



  /****************************
    START ChatBar private methods
  *****************************/

  // this method translates a userId to readable name
  function getUserById(userId) {
    var usr = towbook.get(towbookUsers, userId, "id");
    return usr == null ? "" : usr.name;
  }

  // sorts the masterlist by lastActivity/create dates (descending)
  function sortByLastActivity() {
    return chatItems.sort(function (a, b) {
      if(!towbook.isEmpty(a.lastMessage) && !towbook.isEmpty(b.lastMessage))
        return new Date(b.lastMessage.date) - new Date(a.lastMessage.date);
      else if (!towbook.isEmpty(a.lastMessage) || !towbook.isEmpty(b.lastMessage)) {
        if (!towbook.isEmpty(a.lastMessage) && towbook.isEmpty(b.lastMessage))
          return new Date(b.createDate) - new Date(a.lastMessage.date);
        else if (towbook.isEmpty(a.lastMessage) && !towbook.isEmpty(b.lastMessage))
          return new Date(b.lastMessage.date) - new Date(a.createDate);
      }
      else
        return new Date(b.createDate) - new Date(a.createDate);
    });
  }

  function getFilteredChatItems(type, status) {
    return chatItems
        .filter(function (f) { return f.type == type })
        .filter(function (f) { return f.status == status });
  }

  // show custom error message to the user
  function showErrorMessage(msg) {
    var obj = $('#chatBarMessageList');

    $('#chatBarLoadingMoreTop').hide();
    $('#chatBarLoadingMoreBottom').hide();
    $('#chatBarLoading').hide();

    $(obj).html("");
    $(obj).append('<li class="no-messages">' + msg + '</li>');
  }

  // this method will render and attach a chat message "block" to the chat view.
  function addMessage(data, insertAfter) {
    // insertAfter as true will append.  Otherwise, prepend.
    if (insertAfter !== true && insertAfter !== false)
      insertAfter = true; // default to append

    var senderUserId = data.senderUserId;
    var message = data.message;
    var date = data.date;
    if(data.hasOwnProperty("createDateUtc"))
      date = new Date(Date.parse(data.createDateUtc));

    var sender = towbook.get(towbookUsers, senderUserId, "id");
    var classname = 'direction-l';
    if (sender != null && sender.id == currentUserId)
      classname = 'direction-r';
    var relativeTime =  (data.id > 0 ? moment(date).calendar(): "Sending...");

    // Join messages together if: 
    // 1) it is from the same sender as the last item posted
    //    AND
    // 2) it is within one minute of the last
    var lastMessageObj = insertAfter ? $('#chatBarMessageList').find('li.messageItem:last') : $('#chatBarMessageList').find('li.messageItem:first');
    var lastMessageDate = lastMessageObj.length ? new Date($(lastMessageObj).data('date')) : new Date().setHours(-24);
    var lastMessageUserId = lastMessageObj.length ? $(lastMessageObj).data('user-id') : 0;
    var startAgain = false;
    if ($(lastMessageObj).find('.messageItemContainer.' + classname + '').length == 0)
      startAgain = true;
    if(!startAgain &&
        (((new Date(date).getTime() - lastMessageDate.getTime()) > (1000 * 60)) /*1 minute*/ 
        || senderUserId != lastMessageUserId))
      startAgain = true;

    if (!startAgain && $(lastMessageObj).find('.timestamp').html() == "Sending...")
      startAgain = true;

    if(data.id === undefined && data.senderUserId == currentUserId)
        startAgain = true;

    if (!sender)
        sender = { name: "(Deleted User)", id: senderUserId, disabled: true };

    var messageItem = '<li id="msg_' + data.id + '" class="' + (startAgain || !insertAfter ? 'messageItem default last-message' : 'messageItem')
        + '" data-id="' + data.id + '" data-nonce="' + data.nonce + '" data-date="' + date + '" data-user-id="' + sender.id + '">'
        + '<div class="messageItemContainer ' + classname + '"><div class="flag-wrapper"><div class="flag"><div class="headerContainer">' 
        + (sender != null ? '<div class="author" title="' + _enc(sender.name) + '">' + _enc(sender.name) + '</div>' : "")
        + '<div class="timestamp" title="' + moment(date).format('MMMM Do YYYY, h:mm:ss a') + '">' + relativeTime + '</div></div>'
        + '<ul class="messageNodeList" id=' + data.id + '><li class="messageNode">' + _enc(message) + '</li></ul>'
        + '</div></div></div><div class="clearfix"></div>'
        + '<div class="read-container ' + classname + '"></div></li>';

    // append the message item
    if (data.id !== undefined && $('#msg_' + data.id + '').length > 0)
      $('#msg_' + data.id + '').replaceWith(messageItem);
    else if (data.nonce > 0 && $('#chatBarMessageList').find('.messageItem[data-nonce="' + data.nonce + '"]').length > 0)
      $('#chatBarMessageList').find('.messageItem[data-nonce="' + data.nonce + '"]').replaceWith(messageItem);
    else
      insertAfter ? $('#chatBarMessageList').append(messageItem) : $('#chatBarMessageList').prepend(messageItem);

    if ($(lastMessageObj).length) {
      // style rounded corners and title when appending message
      if (insertAfter) {
        if (!startAgain)
          $(lastMessageObj).removeClass('last-message');

        if ($('#msg_' + data.id + '').length > 0)
          $('#msg_' + data.id + '').addClass('last-message');
        else if ($('#chatBarMessageList').find('.messageItem[data-nonce="' + data.nonce + '"]').length > 0)
          $('#chatBarMessageList').find('.messageItem[data-nonce="' + data.nonce + '"]').addClass('last-message');
      }
      else
      {
        // style rounded corners and title when prepending message
        if (!startAgain) {
          $(lastMessageObj).removeClass('default');

          if ($('#msg_' + data.id + '').length > 0)
            $('#msg_' + data.id + '').removeClass('last-message');
          else if ($('#chatBarMessageList').find('.messageItem[data-nonce="' + data.nonce + '"]').length > 0)
            $('#chatBarMessageList').find('.messageItem[data-nonce="' + data.nonce + '"]').removeClass('last-message');
        }
      }
    }
    
    $('.no-messages').hide();
    
    // make sure we keep most recent chat message in view
    if (autoScroll) {
      var objDiv = document.getElementById("chatBar").getElementsByClassName("body")[0];
      objDiv.scrollTop = objDiv.scrollHeight;
    }

    return $('#msg_' + data.id + '');
  }

  function getChatById(chatId, callback) {
    if (!chatEnabled)
      return;

    if (!chatId)
      return;

    $.ajax({
      url: '/api/chats/' + chatId,
      beforeSend: function () {
        
      },
    }).done(function (data) {

      if (callback != undefined && typeof callback == 'function') {
        callback(data);
      }
    });
  }

  // Mark Chat Message as read
  function markMessageAsRead(chatId, messageId) {
    if (!chatEnabled)
      return;

    // send PUT message to server to mark as read
    $.ajax({
      url: '/api/chats/' + chatId + '/messages/' + messageId + '/read',
      type: 'PUT',
      contentType: "application/json; charset=utf-8"
    });
  }

  // this method will POST a message to the server and render the
  // message to UI as "sending..."
  function sendMessage(chatId) {
    if (!chatEnabled)
      return;

    var message = $('#message-input').getVal();
    if(message.length == 0)
      return false;

    var nonce = new Number(currentUserId.toString() + (Date.now() - new Date().getTimezoneOffset() * 60 * 1000).toString());

    var data = {
      nonce: nonce,
      senderUserId: currentUserId,
      date: Date.now,
      priority: 0,
      message: message,
      latitude: 0,
      longitude: 0
    };

    // add message with a "sending" message until the pusher verifies from the server
    var obj = addMessage(data, true);

    $.ajax({
      url: '/api/chats/' + chatId + '/messages/',
      type: 'POST',
      data: JSON.stringify(data),
      contentType: "application/json; charset=utf-8",
      beforeSend: function(){
        $('#message-input').val("");
      }
    });
  }

  // Queries the server for messages belonging to a certain chat id
  function getChatItemsFromServer(chatId, page) {
    if (!loadMore || isLoadingMore || !chatEnabled)
      return;

    // stop multiple requests on the same scroll event
    isLoadingMore = true;

    // initialize page parameter (must be number)
    if (page == undefined || page == null || isNaN(parseInt(page)))
      page = 1;

    var Me = this;
    var chatBar = $('#chatBar');
    var obj = chatBar.find('#chatBarMessageList');
    var messageCount = chatBar.find('li.messageItem').length;
    var activeType = $('.filter-list').find('.active').data('type-id');
    var activeStatus = $('.filter-list').find('.active').data('status-id');

    // Message view...
    var chatId = chatId || options.visible; // the id of the chat
    var chatItem = towbook.get(chatItems, chatId, "id");
    if (chatItem == null) {
      // chatItem doesn't exsits (deleted?)
      // reset to the list view
      options.visible = true;
      options.activeType = activeType;
      options.activeStatus = activeStatus;
      setLocalStorage();
      ChatBar.refreshList();
      return;
    }

    // change title
    chatBar.removeClass('list').find('.header').find('span').first().html(_enc(chatItem.name)).prop('title', _enc(chatItem.name));

    // add create date as sub-title
    var subTitle = 'Created ' + moment.duration(new moment(chatItem.createDate) - new moment()).humanize(true);
    chatBar.removeClass('list').find('.header').find('span').last().html(subTitle);

    // offset pageSize by the number of items we already have
    pageSize = page == 1 ? 30 : messageCount;
    if (page > 2)
      page = 2;

    // request chat messages from server for selected chat object
    $.ajax({
      url: '/api/chats/' + chatId + '/messages/?page=' + page + '&pageSize=' + pageSize,
      beforeSend: function () { $('#chatBarLoadingMoreTop').show() },
    }).done(function (data) {

      // sort descending by message id
      if (page == 1)  data.sort(function (a, b) { return a.id - b.id });

      // send "read" to server if this chat is unread
      if (options.unreadChatIds.indexOf(chatId) != -1 && data.length > 0) {
        // Mark this chat as read by sending the most recent message id belonging to the chat
        var messageId = Math.max.apply(Math, data.map(function (d) { return d.id }));
        markMessageAsRead(chatId, messageId);
      }

      // Probably will never happen - show default message if data returned is empty
      if (towbook.isEmpty(data)) {
        if (page == 1)
          $(obj).append('<li class="no-messages">No messages to show at this time.</li>');

        // stop any more server requests
        loadMore = false;

        getChatById(chatId, function (data) {
          $('#participant-list').find('.participant-item[data-id]').remove();
          ChatBar.addParticipants(data.id, data.participants, function () {
            ChatBar.refreshChatPartipantStatuses(chatId);
          });
        });

        $('#chatBarLoadingMoreTop').hide();
        ChatBar.refresh();
        return;
      }

      // remember scroll position
      var saveHeight = $(obj).height();

      // Add all messages to the view
      $.each(data, function (n, o) {
        addMessage(o, page == 1);
      });

      // hide spinner
      $('#chatBarLoadingMoreTop').hide();

      // reset scroll position to where we left off
      if (!autoScroll && saveHeight > 0) {
        $(chatBar).find('.body').scrollTop($(obj).height() - saveHeight);
      }

      // allow next scroll event to query the server for more data
      isLoadingMore = false;

      // get updated chat info from server
      getChatById(chatId, function (data) {
        $('#participant-list').find('.participant-item[data-id]').remove();
        ChatBar.addParticipants(data.id, data.participants, function () {
          ChatBar.refreshChatPartipantStatuses(data.id);
        });
      });

    }).fail(function (xhr, status, message) {
      if (xhr && xhr.responseText.length > 0)
        showErrorMessage(status.toUpperCase() + ": " + xhr.responseText + "<br/><br/>Please try again");
      else
        showErrorMessage("There was a problem trying to get the chat messages.  Please refresh and try again");
    });
  }

  // get options from local storage
  function getLocalStorage() {
    return $.extend(options, TowbookStor.get('ChatBarOptions_' + currentUserId, {}));
  }
  // persist options in local storage
  function setLocalStorage() {
    // Initialize localstorage for 'ChatBarOptions'
    if (!TowbookStor.contains('ChatBarOptions_' + currentUserId))
      TowbookStor.set('ChatBarOptions_' + currentUserId, $.extend({}, options));

    // save to localstorage the lastMinimizedState
    TowbookStor.set('ChatBarOptions_' + currentUserId, options);
  }
  // set the noticiation number in the toolbar
  function setToolbarChatIconNumber(num) {
    var bubble = $('.x-icon.chat').find('.x-icon-bubble');

    if (towbook.isEmpty(bubble))
      return;

    if (num == 0) {
      bubble.hide();
    } else {
      bubble.html(num).show();
    }
  }

  function isListView() {
    if (options.visible === true || options.visible === false)
      return true;
    else
      return false;
  }

  function showEmptyMessage() {
    $('#chatBar').addClass('empty');

    if (chatEnabled === false) {
      $('#chatBarMessageList').html("").append('<li class="no-messages">It looks like this feature is not available. Contact Towbook to see how you can get this feature added to your account.</li>');
      return;
    }

    $('#chatBarMessageList').append('<li class="empty"><i class="fa fa-comment"></i>No messages to display</li>');
  }
  function initializePusherEvents(chatIds) {

    var companyIds = towbookCompanies.map(function (c) { return c.id });

    if (chatIds != null && chatIds.length > 0) {
      for (var i = 0; i < chatIds.length; i++) {
        var chat = chatIds[i];

        // register the pusher callback event for each chat object returned 
        // from the server (if the callback hasn't been registered).
        Chat.getInstance(companyIds, currentUserId, chat, 'new_message', ChatBar.updateListItem);
        Chat.getInstance(companyIds, currentUserId, chat, 'message_read', ChatBar.handleReadPusherEvent);
        Chat.getInstance(companyIds, currentUserId, chat, 'chat_members', ChatBar.handleChatMemberChangePusherEvent);
        Chat.getInstance(companyIds, currentUserId, chat, 'chat_kick', ChatBar.handleChatMemberKickPusherEvent);
      }
    }
  }

  function setEnabled(enabled) {
    chatEnabled = (enabled === true);
  }

  function isEnabled() {
    return chatEnabled;
  }
  /****************************
    END ChatBar private methods
  *****************************/





  /****************************
    ChatBar public methods 
  *****************************/

  return {
    isEnabled: function () {
      return isEnabled();
    },
    // This method will start the ChatBar operation.  It needs only to be
    // initialized once.  Pusher events and UI handler events will be bound
    // and the initial server request will occur.  The view will render based
    // on the last known state in the web browser local storage.
    initialize: function (userId, enabled) {

      // make sure to only initialize once.  Bind any UI events here.
      if (!initialized) {
        // save currentUserId
        currentUserId = userId;

        setEnabled(enabled);

        if (!enabled) {
          initialized = true;
          return;
        }

        // Chat icon button click handler
        $('#header').find('li.x-icon.chat').first().on('click', function () {
          $('#chatBar').toggle();
          ChatBar.refreshList();
          options.visible = options.visible === false ? true : false;
          setLocalStorage();
          ChatBar.resetMessageDetails();
          ChatBar.refresh();
        });

        // ChatBar close 'x' click handler
        $('#chatBar').find('.header').find('.fa-times, .fa-close').on('click', function () {
          $('body').removeClass('chat-visible');
          $('#chatBar').hide();
          options.visible = false;
          setLocalStorage();
          ChatBar.refreshToolbarIcon();
          ChatBar.resetMessageDetails();
        });

        // ChatBar 'back to list' click handler
        $('#chatBar').find('.header').find('.fa-bars').on('click', function () {
          $('#chatBar').addClass('list');
          $('#participant-list').html("");
          isLoadingMore = false;
          loadMore = true;
          page = 1;
          options.visible = true;
          setLocalStorage();
          ChatBar.refreshList();
          ChatBar.resetMessageDetails();
        });

        // filter click handler
        $('#filter-list').find('li').on('click', function () {
          // ignore click on active
          if ($(this).hasClass('active'))
            return;

          $(this).closest('ul').find('.active').removeClass('active');
          options.activeStatus = $(this).data('status-id');
          options.activeType = $(this).data('type-id');

          // Go right to the company chat messages if there is only one company chat type
          if (options.activeType == 1) {
            var items = getFilteredChatItems(1, 0);
            if (items.length == 1 && options.visible !== false) {
              ChatBar.selectCallChat(items[0].id);
              options.visible = items[0].id;
              setLocalStorage();
              $(this).addClass('active');
              ChatBar.refresh();
              return;
            }
          }

          setLocalStorage();
          $(this).addClass('active');
          ChatBar.refreshList();
        });

        // ChatBar 'create new chat' click handler
        $('#create-message-input-container').find('input[type="button"]').on('click', function () {
          if ($('#chatBar').hasClass('edit-message-details')) {
            // Save and create new chat
            ChatBar.saveMessageDetails(null);

            // reset fields
            ChatBar.resetMessageDetails();

            // hide 'create chat' div
            $('#chatBar').removeClass('edit-message-details');
          }
          else
          {
            // show 'create chat' div
            $('#chatBar').addClass('edit-message-details');
          }
        });

        // Settings 'cog' click handler
        $('#chatBar').find('.header').find('.title-container').find('.fa-cog').on('click', function (event) {
          $('#chatBar').removeClass('delete-chat-details');
          if ($('#chatBar').hasClass('edit-message-details')) {
            $('#chatBar').removeClass('edit-message-details');
            ChatBar.resetMessageDetails();
          }
          else
          {
            var chatItem = towbook.get(chatItems, options.visible) || [];

            $('#editMessageDetails').find('.create-chat-title').val(_enc(chatItem.name));
            $('#editMessageDetails').find('.create-chat-participants').setVal(chatItem.participants);
            $('#chatBar').addClass('edit-message-details');
          }
        });

        // delete 'trash can' click handler
        $('#chatBar').find('.header').find('.title-container').find('.fa-trash').on('click', function (event) {
          $('#chatBar').removeClass('edit-message-details');
          if ($('#chatBar').hasClass('delete-chat-details')) {
            $('#chatBar').removeClass('delete-chat-details');
            ChatBar.resetMessageDetails();
          }
          else
          {
            $('#chatBar').addClass('delete-chat-details');
          }
        });

        // Send button click handler
        $('#message-input-send').on('click', function (event) {
          sendMessage(options.visible);
        });

        // save button click handler for edit message details
        $('#editMessageDetails').find('input[type="button"]').on('click', function (event) {
          var chatItem = towbook.get(chatItems, options.visible) || [];

          // Save and create new chat
          ChatBar.saveMessageDetails(chatItem.id);
        });

        // Delete button click handler
        $('#deleteChatDetails').find('input[type="button"]').on('click', function (event) {
          var chatItem = towbook.get(chatItems, options.visible);
          if (chatItem != null) {
            $.ajax({
              url: '/api/chats/' + chatItem.id,
              type: 'DELETE',
            }).done(function (data) {
              ChatBar.removeChatListitem(chatItem.id);
              ChatBar.refreshList();
            }).always(function () {
              $('#chatBar').removeClass('delete-chat-details');
            });
          }
        });

        // Add known participants to the multiple select option for crate new chat
        $('#editMessageDetails')
          .find('select.create-chat-participants')
          .appendOptions(Chat.getAllUsers().filter(function (f) { if (f.id != currentUserId && !f.disabled) return f;}), true, true, "id", "name", "(Select/Unselect all)", null)
          .multiselect({
            firstOptionSelectsAll: true,
            addFooterBar: false,
            selected: function () {
              var obj = $('#editMessageDetails');
              var selected = $(this).val();
              var output = "";

              // convert selected to readable users
              if (selected && selected.length > 0 && selected.length < 6) {
                output = "";
                $.each(selected, function (i, userId) {
                  var user = towbook.get(towbookUsers, userId, "id");
                  if (user != null)
                    output += user.name + ", ";
                });
                // remove last comma and whitespace
                output = "Between ME and " + _enc(output.replace(/,\s*$/, "")).toUpperCase();

              }
              else if(selected && selected.length >= 6)
              {
                output = "Between Me and " + selected.length + " others";
              }

              $(obj).find('p').remove();

              // output to UI
              $(obj).find('input[type="button"]').before($('<p style="padding: 0 10px;">' + output + '</p>'));
            }
          }).multiselect('set', 1);

        // click handler for closing the message details
        $('#editMessageDetails').find('.fa-times, .fa-close').on('click', function () {
          ChatBar.resetMessageDetails();
        });

        // click handler for closing the message details
        $('#deleteChatDetails').find('.fa-times').on('click', function () {
          $('#chatBar').removeClass('delete-chat-details');
        });

        // enter key as send
        $('#message-input').on('keydown', function (event) {
          var key = event.which || e.keyCode;
          if (key == 13 && !event.shiftKey) {
            sendMessage(options.visible);
            $(this).val("");
            event.preventDefault();
          }
        });

        // Init dragable bar for resizing chat
        $('#dragbarChat').mousedown(function (e) {
          e.preventDefault();

          dragging = true;
          var main = $('body');
          var ghostbar = $('<div>', {
            id: 'ghostbarChat',
            css: {
              bottom: 0,
              top: 0,
              left: $('#chatBar').offset().left
            }
          }).appendTo('body');


          $(document).mousemove(function (e) {
            ghostbar.css("left", e.pageX + 2);
          });
        });

        // after dragging ghostbar, resize width and remove ghostbar
        $(document).mouseup(function (e) {
          if (dragging) {

            var width = $('#chatBar').width() + ($('#chatBar').offset().left - e.pageX);

            $('#chatBar').css('width', width + "px");
            options.width = width;
            setLocalStorage();

            $('#ghostbarChat').remove();
            $(document).unbind('mousemove');
            dragging = false;
          }
        });

        $(window).on('resize', function () {
          ChatBar.refresh();
        });

      }

      // auto scroll event will put view at most recent (bottom) message if the user
      // hasn't changed the scroll position manually
      var scrollTimer = null;
      $('#chatBar').find('.body').on('scroll', function (event) {
        // List view
        function processChatListScroll() {
          if (scrollTimer) window.clearTimeout(scrollTimer);
          scrollTimer = window.setTimeout(function () {
            ChatBar.getChatListFromServer(currentUserId, page++);
          }, 100);
        }
        // Message view
        function processChatItemsScroll() {
          if (scrollTimer) window.clearTimeout(scrollTimer);

          scrollTimer = window.setTimeout(function () {
            getChatItemsFromServer(options.visible, page++);
          }, 100);
        }

        if (isListView()) {
          // List view scroll event
          autoScroll = false;
          var buffer = Math.ceil($(this).height() - ($(this).height() * .8)); // consider 80% close enough to the bottom to begin the next paging request
          if ($(this).scrollTop() + $(this).innerHeight() >= ($(this)[0].scrollHeight - buffer)) {
            processChatListScroll();
          }
        }
        else
        {
          // Message view scroll event

          // set 'auto' scoll feature.  If the scrollbar is reasonably close to the bottom of
          // the view, consider the view at the bottom and always keep the scroll position at the bottom.
          autoScroll = false;
          var buffer = 10; // 10 is buffer for zooming (decimals of innerHeight)
          if ($(this).scrollTop() + $(this).innerHeight() >= ($(this)[0].scrollHeight - buffer)) {
            autoScroll = true;
          }

          // handle paging (messages view - not list view)
          buffer = Math.ceil($(this).height() - ($(this).height() * .8)); // consider 80% close enough to the top to begin the next paging request
          if (!isListView() && !autoScroll && $(this).scrollTop() < buffer)
            processChatItemsScroll();
        }
      });


      // get last known state
      options = getLocalStorage();

      if (!options.hasOwnProperty("activeStatus")) {
        options.activeStatus = 0;
        setLocalStorage();
      }

      if (!options.hasOwnProperty("activeType")) {
        options.activeType = 2;
        setLocalStorage();
      }

      // initialize view based on options
      $('#chatBar').css('width', options.width + 'px');

      // initalize the active filter view based on options
      var filterObj = $('#filter-list');
      filterObj.find(".active").removeClass('active');
      if (options.activeType == 1) {
        filterObj.find('li[data-type-id=1]').addClass('active');
      }
      else if (options.activeType == 3) {
        filterObj.find('li[data-type-id=3]').addClass('active');
      }
      else
      {
        filterObj.find('li[data-type-id=2][data-status-id=' + options.activeStatus + ']').addClass('active');
      }

      // get first chat list items from server
      ChatBar.getChatListFromServer(userId, 1, function () {
        if (options.visible !== false) {
          $('#chatBar').show();

          if (isListView()) {
            ChatBar.refreshList();
          }
          else
            ChatBar.selectCallChat(options.visible);

          ChatBar.refresh();
        }
      });

      // set as initialized...done.
      initialized = true;
    },

    resetMessageDetails: function() {
      // reset fields
      $('#editMessageDetails').find('.create-chat-title').val("");
      $('#editMessageDetails').find('.create-chat-participants').multiselect('set', "[]");
      $('#editMessageDetails').find('p').remove();

      $('#chatBar').removeClass('edit-message-details');
      if (chatEnabled == true)
        $('#chatBar').find('.footer').css('display', 'show');
      else
        $('#chatBar').find('.footer').css('display', 'hide');
    },

    saveMessageDetails: function(chatId) {
      var title = $('#editMessageDetails').find('.create-chat-title').getVal();
      var participantIds = $('#editMessageDetails').find('.create-chat-participants').val();

      if (title == "") {
        var usr = [];

        // add current user name
        var cu = towbook.get(towbookUsers, Chat.getRegisteredUserId());
        if (cu != null)
          usr.push(cu.name);

        // add other participant names
        participantIds.map(function (p) {
          var u = towbook.get(towbookUsers, p);
          if (u != null)
            usr.push(u.name);
        });

        title = _enc(usr.join(", "));
      }

      $('#editMessageDetails').find('p').remove();

      if (participantIds == null || participantIds.length == 0) {
        $('#editMessageDetails').find('input[type="button"]').before($('<p style="color: red; line-height: 12px; padding: 0 10px;">Please select one person to create a new chat.</p>'));
        return false;
      }

      // do not send "0" as a participant due to the "select all" item
      if (participantIds[0] == 0)
        participantIds.shift();

      // Go! update chat details
      ChatBar.saveChatDetails(chatId, title, participantIds);
    },

    // Add a chat item to the master repository
    addChatListItem: function (item) {
      var isNew = false;

      // only add item if it doesn't already exist.
      if (towbook.get(chatItems, item.id, "id") == null) {
        chatItems.push(item);
        isNew = true;
      }

      // update local storage
      var idx = options.unreadChatIds.indexOf(item.id);
      if(item.unreadMessages > 0 && idx == -1)
      {
        // this message is unread
        options.unreadChatIds.push(item.id);
        setLocalStorage();
      }
      else if (item.unreadMessages == 0 && idx != -1)
      {
        // message is read...remove from local storage
        options.unreadChatIds.splice(idx, 1);
        setLocalStorage();
      }

      // sort 
      sortByLastActivity();

      return isNew;
    },

    // Remove a chat item from the master repository
    removeChatListitem: function(itemId) {
      var item = towbook.get(chatItems, itemId, "id");
      if (item == null)
        return;

      var opt = getLocalStorage();
      if (opt.visible == itemId) {
        opt.visible = true; // set back to list view
        setLocalStorage();
      }

      var idx = options.unreadChatIds.indexOf(itemId);
      if (item.unreadMessages > 0 && idx != -1) {
        // clear all unread message for this chat in local storage
        options.unreadChatIds.splice(idx, 1);
        setLocalStorage();
      }

      chatItems = chatItems.filter(function (f) { return f.id != itemId });
    },

    // forms the html and returns the chat obj ready to be attached to the dom
    formListItem: function (item) {
      if (item == null)
        return;

      // intialize last message property
      if (towbook.isEmpty(item.lastMessage))
        item.lastMessage = [];

      // setup participantIds property
      var participantIds = [];
      participantIds.push(item.ownerUserId);
      if (participantIds.indexOf(item.lastMessage.senderUserId) == -1)
        participantIds.push(item.lastMessage.senderUserId);

      // translate participants to comma seperated, readable names
      var participants = participantIds.map(function (p) { return (getUserById(p)) });

      // prepare unread message count information
      var unreadMessagesCount = item.unreadMessages > 0 ? item.unreadMessages > 9 ? "9+" : item.unreadMessages : "";
      var unreadMessageTitle = item.unreadMessages > 0 ? item.unreadMessages + " unread messages" : "";

      // final data object to use for template
      var data = {
        id: item.id,
        relativeTime: !towbook.isEmpty(item.lastMessage) ? moment.duration(new moment() - new moment(item.lastMessage.date)).humanize() : moment.duration(new moment() - new moment(item.createDate)).humanize(),
        lastActivityDate: item.lastMessage.date,
        lastMessage: item.lastMessage.message,
        name: item.name,
        type: item.type,
        status: item.status,
        createDate: item.createDate,
        lastOwner: getUserById(item.lastMessage.senderUserId),
        participants: _enc(participants.join(", ")),
        unreadMessages: unreadMessagesCount,
        unreadMessageTitle: unreadMessageTitle
      }

      return towbook.applyTemplate('t-chatBarMessageListItem', data);

    },
    // takes the pusher event data and syncs that information
    // to the master list
    updateListItem: function (data) {

      // find item in repository and change it
      var chatItem = towbook.get(chatItems, data.chatId, "id");
      if (!chatItem) {
        getChatById(data.chatId, function (gcdata) {
          ChatBar.addChatListItem(gcdata);
          updateListItem(data);
        });
        return;
      }

      // capture the old
      var newItem = chatItems.filter(function (el) { return el.id === chatItem.id; })[0];

      // update participant list
      if (data.hasOwnProperty('participants'))
        newItem.participants = data.participants;

      // remove the old
      chatItems = chatItems.filter(function (el) { return el.id !== chatItem.id; });

      // re-purpose the old
      newItem = $.extend(newItem, {
        lastMessage: {
          date: new Date(),
          id: data.id,
          nonce: data.nonce,
          message: data.message,
          priority: data.priority,
          senderUserId: data.senderUserId
        }
      });

      var notifyUser = newItem.type == 1 || newItem.type == 2
        || (newItem.participants != null
          && newItem.participants.indexOf(currentUserId) != -1
          && data.senderUserId != currentUserId);

      // add 1 to the unread messge count
      if (notifyUser)
        newItem.unreadMessages++;

      // Add the re-purposed item
      chatItems.push(newItem);

      sortByLastActivity();

      // refresh and sort the list
      if (options.visible === true) {
        ChatBar.refreshList(false);
      }
      else if (options.visible !== false) {
        if (options.visible == data.chatId) {
          addMessage(data, true);
          markMessageAsRead(data.chatId, data.id);
        }
      }

      // mark item as 'unread' in UI (if the sender is not the current user)
      if (notifyUser) {
        ChatBar.flashChatListObject($('#msg_' + data.chatId + '').addClass('unread'));

        // add as unread in localstorage
        if (options.unreadChatIds.indexOf(data.chatId) == -1) {
          options.unreadChatIds.push(data.chatId);
          setLocalStorage();
        }

        // sync chat icon number
        ChatBar.refreshToolbarIcon();
      }

      // dispatching only
      if (typeof refreshChatBubbles === 'function') {
        data.dispatchEntries.map(function (m) {
          refreshChatBubbles(m, data.chatId);
        });
      }
    },

    // remove chat object from the list
    removeChatListObject: function (obj, animate) {
      if (!obj || typeof obj !== 'object' || !isListView())
        return;

      if (animate) {
        ChatBar.flashChatListObject(obj);
        window.setTimeout(function () { $(obj).slideUp(); }, 400);
      }
      else
        $(obj).slideUp();
    },

    // insert chat object into the list at a specific position
    insertChatListObject: function (obj, pos) {
      if (!obj || typeof obj !== 'object' || !isListView())
        return;

      if (!pos || isNaN(pos))
        pos = 0;

      // insert the item to the list
      if (pos == 0)
        $('#chatBarMessageList').prepend(obj);
      else
        $('#chatBarMessageList').find('li').eq(pos - 1).after(obj);

      // highlight
      ChatBar.flashChatListObject(obj);
    },

    handleChatUpdatePusherEvent: function (data) {
      if (!chatEnabled ||
        towbook.isEmpty(chatItems))
        return;

      console.log("Chat: update event found for chat", data.id);
      var item = towbook.get(chatItems, data.id);
      if (item != null) {
        item.status = data.status;
        item.name = data.name;
        item.type = data.type;
        item.readOnly = data.readOnly;
      }

      if (isListView()) {
        ChatBar.refreshList();

        // highlight new message to the user
        ChatBar.flashChatListObject($('#msg_' + data.id + ''), true);
      }
    },

    handleNewChatPusherEvent: function (data) {
      var cUser = towbook.get(towbookUsers, Chat.getRegisteredUserId(), "id");
      if (cUser == null)
        return;

      var validUser = data.members.indexOf(cUser.id) != -1;
      if (!validUser && data.callIds.length > 0) {
        // allow dispatchers and managers to get call chat details
        if (cUser.type == 1 || cUser.type == 2) {
          validUser = true;
        }
      }

      if (validUser) {
        // get updated chat info from server
        getChatById(data.id, function (data) {
          $('#participant-list').find('.participant-item[data-id]').remove();
          if (towbook.get(chatItems, data.id, "chatId") == null) {

            // hack: add chat id as property 'chatId' (fixes difference in data from pusher)
            data.chatId = data.id;

            // add new info to the local repository
            ChatBar.addChatListItem(data);

            // add pusher events for new chat
            var newId = [];
            newId.push(data.id);
            initializePusherEvents(newId);

            // refresh (sort) list view
            if (isListView()) {
              ChatBar.refreshList();

              // highlight new message to the user
              ChatBar.flashChatListObject($('#msg_' + data.id + ''), true);
            }
          }
        });
      }
      else {
        // register pusher listener action for handling member addition.
        // AC: 2/7/19 Users may be added later after chat is created.  Users need to listen for their explicit invite.
        Chat.getInstance(Chat.getRegisteredCompanyIds(), Chat.getRegisteredUserId(), data.id, 'chat_members', ChatBar.handleChatMemberChangePusherEvent);
      }
    },

    handleDeleteChatPusherEvent: function (data) {
      ChatBar.removeChatListitem(data.id);
      ChatBar.refreshList();
    },

    handleChatMemberChangePusherEvent: function (data) {
      var chat = towbook.get(chatItems, data.id, "id");
      if (chat == null && data.newMembers.indexOf(currentUserId) != -1) {
        // new member
        ChatBar.handleNewChatPusherEvent({ id: data.id, members: data.newMembers });
      }
      else
      {
        // participation switch
        ChatBar.addParticipants(data.id, data.newMembers, function () {
          ChatBar.refreshChatPartipantStatuses(data.id);
        });
      }
    },

    handleChatMemberKickPusherEvent: function (data) {
      var chat = towbook.get(chatItems, data.id, "id");
      if (chat != null) {
        var cUser = towbook.get(towbookUsers, Chat.getRegisteredUserId(), "id");
        if (cUser != null && (cUser.type == 1 || cUser.type == 2)) {
          var idx = chat.participants.indexOf(cUser.id);
          if (idx != -1) {
            chat.participants.splice(idx, 1);
            return;
          }
        }
      }

      ChatBar.handleDeleteChatPusherEvent(data);
    },

    handleReadPusherEvent: function (data) {

      if (data.readingUserId == currentUserId)
      {
        // remove chatId from local repistory only if the sender of the read message
        // is the current user.

        var idx = options.unreadChatIds.indexOf(data.chatId);
        if (idx != -1) {
          options.unreadChatIds.splice(idx, 1);
          setLocalStorage();

          // reset the unreadMessage count
          ChatBar.getChatListItems().map(function (c) {
            if (c.id == data.chatId)
              c.unreadMessages = 0;
          });

          if (isListView())
            $('#msg_' + data.chatId + '').removeClass('unread');

          // sync toolbar notification number
          ChatBar.refreshToolbarIcon();
        }

        if (typeof (refreshChatBubbles) === 'function')
          refreshChatBubbles(); // dispatching only
      }
      else
      {
        // show user has read the message

        // get all the message ids marked as read
        var messageIds = Object.keys(data.messagesRead).map(function(m) { return parseInt(m) });

        // find the most recent id
        var messageId = Math.max.apply(Math, messageIds);

        // get the user name to display
        var user = getUserById(data.readingUserId);

        // remove "last seen" information by this user
        $('#chatBarMessageList').find('.read-container').find('i[data-user-id="' + data.readingUserId + '"').remove();

        // add 'seen' information to the screen
        $('#chatBarMessageList').find('.messageItem[data-id="' + messageId + '"]').find('.read-container').append('<i data-user-id="' + data.readingUserId + '" class="fa fa-user"><span>Seen by ' + user + '</span></i>').hide();

      }

      // wait for last read pusher event before we refresh the UI
      if (syncReadTimer) window.clearTimeout(syncReadTimer);
      syncReadTimer = window.setTimeout(function () { ChatBar.syncReadStatuses() }, 400);

    },

    // this method syncs the screen
    syncReadStatuses: function() {
      // handle multiple seen blocks
      $.each($('#chatBarMessageList').find('.messageItem').find('.read-container'), function (i, row) {
        if ($(row).find('i.fa-user').length > 1) {
          var seenMessage = "Seen by ";
          $.each($(row).find('i.fa-user'), function (i, user) { seenMessage += getUserById($(user).data('userId')) + ", "; });

          // remove last comma and whitespace
          seenMessage = seenMessage.replace(/,\s*$/, "");

          $(row).addClass('multiple');

          $('<i class="fa fa-users"><span>' + seenMessage + '</span></i>').appendTo($(row));
        }
        else {
          $(row).removeClass('multiple');

          if (!$(row).closest('.messageItem').hasClass('default') && $(row).find('i.fa-user').length == 1) {
            // previous row to last-message
            $(row).closest('.messageItem').prev().addClass('last-message');

            // current row to default
            $(row).closest('.messageItem').addClass('default');

            // next row to default
            $(row).closest('.messageItem').next().addClass('default');
          }
        }

        $(row).show();
      });

      if (autoScroll && !isListView()) {
        var bodyObj = $('#chatBar').find('.body');
        $(bodyObj).scrollTop($('#chatBarMessageList').height());
      }
    },



    // sorts and displays the list
    // (also changes from chat view to list view)
    refreshList: function (scrollToTop) {
      if (!$('#chatBar').is(':visible'))
        return;
      
      var time = (new Date()).getTime();

      if (scrollToTop !== false)
        scrollToTop = true;

      loadMore = true;

      $('body').removeClass('chat-visible');
      $('#chatBarMessageList').find('li').remove();
      $('#chatBar').addClass('list').find('.header').find('span').first().html("Chats");
      $('#chatBar').addClass('list').find('.header').find('span').first().attr('title', '');
      $('#chatBar').addClass('list').find('.header').find('span').last().html("");

      // filter chatItems by type and status
      var filteredItems = getFilteredChatItems(options.activeType, options.activeStatus);

      if (filteredItems.length == 0) {
        showEmptyMessage();
      }
      else
      {
        $('#chatBar').removeClass('empty');

        for (var i = 0; i < sortByLastActivity(filteredItems).length; i++) {
          var item = filteredItems[i];
          var out = this.formListItem(item);
          $('#chatBarMessageList').append(out);
        }

        // assign click event
        $('#chatBarMessageList').find('li').off('click').on('click', function (e) {
          var chatId = $(this).data("id");

          ChatBar.selectCallChat(chatId);

          // mark this as read now (causing pusher event to remove chat icon bubble)
          if (!towbook.isEmpty(item) && !towbook.isEmpty(item.lastMessage))
            ChatBar.markCallChatRead(item.id, item.lastMessage.id);

          options.visible = chatId;
          setLocalStorage();
        });

        // scroll to top
        if (scrollToTop) {
          var objDiv = document.getElementById("chatBar").getElementsByClassName("body")[0];
          objDiv.scrollTop = 0;
        }

        // mark any unread chats as unread
        for (var i = 0; i < options.unreadChatIds.length; i++) {
          $('#msg_' + options.unreadChatIds[i] + '').addClass('unread');
        }
      }

      ChatBar.refresh();

      if (typeof (refreshChatBubbles) === 'function')
        refreshChatBubbles(); // dispatching only

      return (new Date()).getTime() - time;
    },
    // make sure the notifiction number matches what is known as unread
    refreshToolbarIcon: function () {
      // sync chat icon number
      setToolbarChatIconNumber(options.unreadChatIds.length);

      var typeIds = [];
      var statusIds = [];

      // prepare arrays on unread types and statuses
      for (var i = 0; i < options.unreadChatIds.length; i++) {
        var chatId = options.unreadChatIds[i];
        var chatItem = towbook.get(chatItems, chatId, "id");
        if(chatItem != null)
        {
          if (typeIds.indexOf(chatItem.type) == -1)
            typeIds.push(chatItem.type);

          if (statusIds.indexOf(chatItem.status) == -1)
            statusIds.push(chatItem.status);
        }
      }

      // sync chat filter list with 'unread' class
      $('#filter-list').find('li').removeClass('unread');
      for (var a = 0; a < typeIds.length; a++) {
        var typeId = typeIds[a];

        for (var b = 0; b < statusIds.length; b++) {
          var statusId = statusIds[b];

          var filterObj = $('#filter-list').find('li[data-type-id=' + typeId + '][data-status-id=' + statusId + ']');
          if (filterObj.length > 0)
            filterObj.addClass('unread');
        }
      }

    },
    // get the list of unread chats
    getUnreadChatIds: function () {
      return options.unreadChatIds.slice();
    },
    // get one or the whole list of chats from the master list
    getChatListItems: function(chatId) {
      if (chatId && !isNaN(parseInt(chatId)))
        return chatItems.filter(function (c) { if (c.id == chatId) return c; })[0] || [];
      else
        return chatItems;
    },
    // Queries the server for messages belonging to a certain chat id
    getChatListFromServer: function (userId, pageNo, callback)
    {
      if (!loadMore || isLoadingMore || !chatEnabled)
        return;

      // stop multiple requests on the same scroll event
      isLoadingMore = true;

      // initialize page parameter (must be number)
      page = 1;
      if (pageNo !== undefined && pageNo !== null && !isNaN(parseInt(pageNo)))
        page = pageNo;

      // offset pageSize by the number of items we already have
      if (page > 2) {
        page = 2;
        pageSize = ChatBar.getChatListItems().length;
      }

      $.ajax({
        url: "/api/chats/?page=" + page + "&pageSize=" + pageSize,
        contentType: "application/json; charset=utf-8",
        beforeSend: function () { if(page > 1) $('#chatBarLoadingMoreBottom').show() },
      }).done(function (data) {

        var doRefresh = true;

        if (towbook.isEmpty(data)) {
          if (chatEnabled === false)
            showEmptyMessage();
          else
            loadMore = false;

          $('#chatBarLoadingMoreBottom').hide();
          return;
        }
        else
        {
          
          // check if any ids in the return are not known
          var chatIds = chatItems.map(function(f) { return f.id });
          doRefresh = data.filter(function (d) {
            return chatIds.indexOf(d.id) == -1;
          }).length > 0;
            
          if(doRefresh){
            // add to local repository
            for (var i = 0; i < data.length; i++) {
              ChatBar.addChatListItem(data[i]);
            }
          }
        }

        if(doRefresh) {
          // remember scroll position
          var saveScrollTop = $('#chatBar').find('.body').scrollTop();

          initializePusherEvents(data.map(function (d) { return d.id }));

          if (isListView())
            ChatBar.refreshList();

          if (saveScrollTop > 0) {
            $('#chatBar').find('.body').scrollTop(saveScrollTop);
          }
        }

        // end with callback method (if provided)
        if (callback != undefined && typeof callback == 'function') {
          callback();

          if (typeof (refreshChatBubbles) === 'function')
            refreshChatBubbles(); // dispatching only
        }

        $('#chatBarLoadingMoreBottom').hide();

        isLoadingMore = false;
      });
    },
    // this method handles the change from the list 
    // view to the chat view
    selectCallChat: function (chatId) {
      // reset features
      autoScroll = true;
      loadMore = true;
      isLoadingMore = false;
      page = 1;
      options.visible = chatId;
      setLocalStorage();

      // move intercom "?"
      $('body').addClass('chat-visible');

      // clear create new message div (propably not visible)
      $('#chatBar').removeClass('edit-message-details');

      // clear rows
      $('#chatBarMessageList').find('li').remove();

      // refresh
      ChatBar.refresh();

      // query server for messages for this chat
      getChatItemsFromServer(chatId, page++);
    },

    addParticipants: function(chatId, participants, callback) {
      var chatItem = towbook.get(chatItems, chatId);
      if (chatItem == null)
        return;

      // unique the participants array...we don't want duplicates
      chatItem.participants = participants.filter(function (item, pos, self) {
        return self.indexOf(item) == pos;
      });

      // put current user last
      chatItem.participants.sort(function (a, b) { return (a == currentUserId ? -1 : 1); })

      var participationObj = $('#participant-list');
      var statusHtml = '<div class="status"><i class="fa"></i></div>';

      // don't display just one particpant
      if (chatItem.participants.length == 1) {
        ChatBar.refresh();
        return;
      }
      /*else if (chatItem.participants.length == 2) {
        // add participants as text;
        var otherName = getUserById(chatItem.participants.reduce(function (currentUserId, userId) { if (userId != currentUserId) return userId }, 0));
        otherName = otherName.length > 0 ? otherName : "1 other";
        var ids = chatItem.participants[0] + "," + chatItem.participants[1];
  
        $(participationObj).prepend('<li class="participant-item text-version status-unknown" data-id="' + ids + '" title="' + chatItem.participants.length + ' participants"><span>Me and ' + otherName + '</span>' + statusHtml + '</li>');
      }*/
      else if (chatItem.participants.length > 6) {
        // add participants as text;
        var names = "";
        var ids = "";
        $.each(chatItem.participants, function (i, p) {
          names += getUserById(p) + ", ";
          ids += p + ",";
        });

        // remove last comma and whitespace
        names = _enc(names.replace(/,\s*$/, ""));
        ids = ids.replace(/,\s*$/, "");

        $(participationObj).html("");

        $(participationObj).append('<li class="participant-item text-version status-unknown" data-id="' + ids + '" title="' + names + '"><span>Me and ' + (chatItem.participants.length - 1) + ' others</span>' + statusHtml + '</li>');

      }
      else {
        $(participationObj).html("");

        // add participants as circles
        $.each(chatItem.participants, function (i, p) {
          var nameSplit = getUserById(p).split(" ");
          var initials = nameSplit[0].charAt(0);
          if (nameSplit.length > 1)
            initials += nameSplit[1].charAt(0);
          if (initials.length != 0) {
            $(participationObj).append('<li class="participant-item status-active ' + (p == currentUserId ? "me" : "") + '" data-id="' + p + '" title="' + getUserById(p) + '">'
                    + '<span>' + initials + '</span>' + statusHtml + '</li>');
          }
        });
      }

      // end with callback method (if provided)
      if (callback != undefined && typeof callback == 'function') {
        callback();
      }
    },

    // Handler for the "user_status" pusher event.
    // this method 1) checks if the pusher event fits with the current chat in view 
    // and 2) check if the userId that belongs to the status change belongs to the current chat in view
    handleUserStatusUpdate: function(data) {
      // must be in a chat room
      if (isListView())
        return;

      if(!isNaN(option.visible))
      {
        var chatItem = towbook.get(chatItems, option.visible);
        if (chatItem == null)
          return;

        if (chatItem.participants == null || chatItem.participants.indexOf(data.userId) == -1)
          return;

        ChatBar.refreshUserStatus(data.userId, data.statusId);
      }
    },

    // Refreshes the UI for user status
    refreshUserStatus: function(userId, statusId) {

      var participantObj = $('#participant-list').find('li[data-id="' + userId + '"]');
      var statusObj = $(participantObj).find('i.fa');
      $(participantObj).removeClass('status-unknown status-online status-offline');

      var status = statusId;
      if (userId == currentUserId)
        status = 1;

      switch (status) {
        case 1: // online
          $(statusObj).removeClass('fa-circle-thin');
          $(statusObj).addClass('fa-circle');
          $(participantObj).addClass('status-online');
          break;

        case 2: // offline
          $(statusObj).removeClass('fa-circle-thin');
          $(statusObj).addClass('fa-circle');
          $(participantObj).addClass('status-offline');
          break;

        default: // unknown
          $(statusObj).addClass('fa-circle-thin');
          $(participantObj).addClass('status-unknown');
          break;
      }
    },

    // this method will sync the online status of chat participants by querying the server
    // for participant online information
    refreshChatPartipantStatuses: function(chatId) {
      // must be in a chat room
      if (isListView() || !chatEnabled)
        return;

      // request participant last known status from server using Rest API
      $.ajax({
        url: '/api/chats/' + chatId + '/members'
      }).done(function (data) {

        $.each(data.members, function (i, member) {
          ChatBar.refreshUserStatus(member.userId, member.statusId);
        });

        ChatBar.refresh();

      });
    },

    // this method will save a new chat object or update an existing one
    saveChatDetails: function (chatId, title, participantIds, onFinish) {
      if (!chatEnabled)
        return;

      var type = (chatId == undefined || chatId == null ? 'POST' : 'PUT');
      var url = '/api/chats/';
      var data = new Object();
      data.name = title;
      data.ownerUserId = currentUserId;
      data.participants = participantIds.map(function (m) { return parseInt(m) });

      // always add current user as participant
      data.participants.push(currentUserId);

      if (type == "PUT") {
        data.id = chatId;
        url = '/api/chats/' + chatId;
      }

      // Send POST to server to create new chat.  Note: don't do anything with the
      // the success...let the pusher event create the new chat object.
      $.ajax({
        url: url,
        type: type,
        contentType: "application/json; charset=utf-8",
        beforeSend: function () { $('#chatBarLoading').show(); },
        data: JSON.stringify(data),
      }).done(function (data) {
        // stop loading message
        $('#chatBarLoading').hide();

        var isNew = false;
        var chatItem = towbook.get(chatItems, data.id);
        if (chatItem == null)
          isNew = ChatBar.addChatListItem(data);
        else {
          chatItem.name = data.name;
          chatItem.participants = data.participants;
        }

        // register pusher event to the new chat instance
        if (isNew) {
          Chat.getInstance(Chat.getRegisteredCompanyIds(), Chat.getRegisteredUserId(), data.chatId, 'new_message', ChatBar.updateListItem);
          Chat.getInstance(Chat.getRegisteredCompanyIds(), Chat.getRegisteredUserId(), data.chatId, 'message_read', ChatBar.handleReadPusherEvent);
          Chat.getInstance(Chat.getRegisteredCompanyIds(), Chat.getRegisteredUserId(), data.chatId, 'chat_kick', ChatBar.handleChatMemberKickPusherEvent);
          Chat.getInstance(Chat.getRegisteredCompanyIds(), Chat.getRegisteredUserId(), data.chatId, 'chat_members', ChatBar.handleChatMemberChangePusherEvent);
        }

        ChatBar.addParticipants(data.id, data.participants, function () {
          ChatBar.refreshChatPartipantStatuses(data.id);
        });

        
        $('#chatBar').removeClass('edit-message-details');
        ChatBar.selectCallChat(data.id);

        if (onFinish && typeof onFinish == "function")
          onFinish();
      });
    },

    refresh: function() {
      var chatObj = $('#chatBar');
      var headerHeight = chatObj.find('.header').outerHeight();

      if (isListView()) {
        $('#chatBar').find('.fa-cog.fa-trash').hide();
        $('#chatBar').removeClass('delete-chat-details');
      }
      else
      {
        // remember scroll position
        var saveScrollTop = $('#chatBar').find('.body').scrollTop();

        if (saveScrollTop > 0)
          $('#chatBar').find('.body').scrollTop(saveScrollTop);

        var usr = towbook.get(towbookUsers, Chat.getRegisteredUserId(), "id")
        if (options.activeType == 1 || (options.activeType == 2 && usr != null && usr.type == 1))
          $('#chatBar').find('.fa-cog.fa-trash').hide();
        else
          $('#chatBar').find('.fa-cog.fa-trash').show();
      }

      // make body top start at the bottom of header plus participants
      chatObj.find('.body').css('top', headerHeight + 5 + 'px');

      // sync chat icon number
      ChatBar.refreshToolbarIcon();
    },
    // re-triggerable animation for highlighting an object
    flashChatListObject: function (obj, bounce) {
      if (obj && typeof obj === 'object') {
        // calling width() allows for a re-fire of animation class by reflowing the object
        $(obj).removeClass('flash-me-and-bounce flash-only').width();
        // add animation class
        if(bounce === true)
          $(obj).addClass('flash-me-and-bounce');
        else
          $(obj).addClass('flash-only');
      }
    },

    markCallChatRead: function(chatId, messageId) {
      markMessageAsRead(chatId, messageId);
    },

    // open or create a new call chat
    openCallChat: function (dispatchEntryId, onFinish) {
      if (!chatEnabled)
        return;

      $('#chatBar').show();

      // performing a 'get' with callId specified will always return a call chat
      $.ajax({
        url: '/api/chats/?callId=' + dispatchEntryId,
      }).done(function (data) {

        if (data.length > 0) {
          var item = data[0];

          // maybe a new chat...add to repository
          var isNew = ChatBar.addChatListItem(item);

          if (isNew) {
            Chat.getInstance(Chat.getRegisteredCompanyIds(), Chat.getRegisteredUserId(), item.id, 'new_message', ChatBar.updateListItem);
            Chat.getInstance(Chat.getRegisteredCompanyIds(), Chat.getRegisteredUserId(), item.id, 'message_read', ChatBar.handleReadPusherEvent);
            Chat.getInstance(Chat.getRegisteredCompanyIds(), Chat.getRegisteredUserId(), item.id, 'chat_members', ChatBar.handleChatMemberChangePusherEvent);
            Chat.getInstance(Chat.getRegisteredCompanyIds(), Chat.getRegisteredUserId(), item.id, 'chat_kick', ChatBar.handleChatMemberKickPusherEvent);
            sortByLastActivity();
          }

          // open the specific chat by id
          ChatBar.selectCallChat(item.id);

          // mark this as read now (causing pusher event to remove chat icon bubble)
          if(!towbook.isEmpty(item) && !towbook.isEmpty(item.lastMessage))
            ChatBar.markCallChatRead(item.id, item.lastMessage.id);

          if (onFinish && typeof onFinish == "function")
            // callback method provided
            onFinish(item);
        }
      });

    },

  }
    
  /****************************
    END of ChatBar public methods 
  *****************************/
})();




// This function will launch the chat feature for the proved userId
// make sure to only call this function if the Chat feature is enabled
// for the company
var activateChatEvents = function(userId, enabled) {
  // sanity check before we begin
  if (towbook.isEmpty(towbookCompanies) || isNaN(userId))
    return;

  // template used for a chat item in the list
  towbook.compileTemplate('t-chatBarMessageListItem', '#t-chatBarMessageListItem');

  // initilize ChatBar
  ChatBar.initialize(userId, enabled);

  // register the pusher callback event for all companies
  var companyIds = towbookCompanies.map(function (c) { return c.id });
  Chat.getInstance(companyIds, userId, null, 'new_chat', ChatBar.handleNewChatPusherEvent);
  Chat.getInstance(companyIds, userId, null, 'delete_chat', ChatBar.handleDeleteChatPusherEvent);
  Chat.getInstance(companyIds, userId, null, 'user_status', ChatBar.handleUserStatusUpdate);
  Chat.getInstance(companyIds, userId, null, 'update_chat', ChatBar.handleChatUpdatePusherEvent);  
};

var activateChatEvent = function (companyId, userId, chatId, type, onEvent) {
  if (companyId > 0
    && userId > 0
    && type != ""
    && typeof onEvent === 'function') {
    var companyIds = new Array();
    companyIds.push(companyId);
    Chat.getInstance(companyIds, userId, chatId, type, onEvent);
  }
  else
    console.log("ERROR: failed to activate chat event", companyId, userId, chatId, type, onEvent);
}

