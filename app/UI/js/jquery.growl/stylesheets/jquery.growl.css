/* j<PERSON><PERSON><PERSON>
 * Copyright 2013 <PERSON>
 * 1.1.0
 */
#growls {
  z-index: 50000;
  position: fixed; }
  #growls.default {
    top: 10px;
    right: 10px; }
  #growls.tl {
    top: 10px;
    left: 10px; }
  #growls.tr {
    top: 10px;
    right: 10px; }
  #growls.bl {
    bottom: 10px;
    left: 10px; }
  #growls.br {
    bottom: 10px;
    right: 10px; }

.growl {
  opacity: 0.8;
  position: relative;
  border-radius: 4px;
  -webkit-transition: all 0.4s ease-in-out;
  -moz-transition: all 0.4s ease-in-out;
  transition: all 0.4s ease-in-out; }
  .growl.growl-incoming {
    opacity: 0; }
  .growl.growl-outgoing {
    opacity: 0; }
  .growl.growl-small {
    width: 200px;
    padding: 5px;
    margin: 5px; }
  .growl.growl-medium {
    width: 250px;
    padding: 10px;
    margin: 10px; }
  .growl.growl-large {
    width: 300px;
    padding: 15px;
    margin: 15px; }
  .growl.growl-default {
    color: black;
    background: #7f8c8d; }
  .growl.growl-error {
    color: white;
    background: #c0392b; }
  .growl.growl-notice {
    color: white;
    background: #2ecc71; }
  .growl.growl-warning {
    color: white;
    background: #f39c12; }
  .growl .growl-close {
    cursor: pointer;
    float: right;
    font-size: 14px;
    line-height: 18px;
    font-weight: normal;
    font-family: helvetica, verdana, sans-serif; }
  .growl .growl-title {
    font-size: 18px;
    line-height: 24px; }
  .growl .growl-message {
    font-size: 14px;
    line-height: 18px; }

/* towbook specific sytles*/
#growls.towbook {
    top: 40px;
    right: 10px; }

.growl.growl-towbook {
    color: white;
    background: #2B75BE; }

.growl.towbook .growl-title {
    font-size: 18px;
    line-height: 24px; }

.growl-towbook .growl-message {
    font-size: 14px;
    line-height: 18px; }

.growl-towbook .growl-moment {
    font-size: 12px;
    line-height: 13px; 
    opacity: 0.6;
}
