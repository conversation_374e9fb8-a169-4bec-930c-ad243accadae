import React, {Component} from "react";

import Logger from '../utils/Logger';
import api from "../utils/apiClient";
import CliEvents from '../types/TwiClientEvents';
import {sortChannels, mapChannels, mapMessage} from '../utils/chatUtils'
import {contains, getSum} from "../utils/collectionUtils";
import {getCompanies} from '../utils/commonUtils';
import {ChannelType, TabType} from "../types";
import {ChannelModel} from "../models";
import ChatInboxPanel from './ChatInboxPanel';

import './chatinbox.css';
import {getCalls, setCalls} from "../utils/callUtils";
import {RedBadge} from "../components/common";

class ChatInboxContainer extends Component {
  rootNode = null;
  defaultTabId = TabType.ACTIVE;

  clientListener = {
    channelUpdated: ({channel, updateReasons}) => {
      if (contains(updateReasons, CliEvents.REASON_LAST_MESSAGE) || contains(updateReasons, CliEvents.REASON_LAST_CONSUMED_MESSAGE_INDEX)) {
        log.i('Updating unread message counters...');

        this.mapChannelList().then(() => {});
      } else if (contains(updateReasons, CliEvents.REASON_ATTRIBUTES)) {
        this.updateChannelList().then(() => {});
      }
    },
    channelJoined: (channel) => {
      log.i(`>>> Joined to channel[sid='${channel.sid}', name='${channel.friendlyName}'] ...`);
      this.updateChannelList().then(() => {});
    },
    channelRemoved: (channel) => {
      log.i(`>>> Channel removed channel[sid='${channel.sid}', name='${channel.friendlyName}'] ...`);
      this.updateChannelList().then(() => {});
    }
  };

  constructor(props) {
    super(props);

    this.twilioClient = window.appTwilioClient;

    this.state = {
      chatEnvLoading: false,
      chatEnvLoadSuccess: null,
      chatEnvLoadError: null,
      isLoading: false,
      panelOpen: false,
      subscribedChannels: [],
      dispatchEntryChannelDescriptors: [],
      lastMessages: {},
      channels: [],
      selectedCompanyId: -1,
      selectedTabId: 'active',
      //displayingChannels:  local copy of this.state.channels (but this will can be filtered by company and by channel type according with the selected tab)
      displayingChannels: [],
      isTabContentLoading: false
    }
  }

  componentWillMount() {
    document.addEventListener('mousedown', this.handleClick, false);
  }

  componentWillUnmount() {
    document.removeEventListener('mousedown', this.handleClick, false);
  }

  async componentDidMount() {
    const client =  this.twilioClient;

    this.setState({chatEnvLoading: true});

    try {
      await client.init();

      if (getCalls().length === 0) {
        log.i('No calls were found from cache, proceeding to fetch them from API');
        const calls = await api.get('/calls');
        log.i(`-> (${calls.length}) were fetched from API`);
        setCalls(calls);
      }

      client.addEventListener(this.clientListener);

      this.setState({
        chatEnvLoading: false,
        chatEnvLoadSuccess: true
      });

      await this.mapChannelList();
    } catch (err) { 
      console.log('ERROR: ', err);

      this.setState({
        chatEnvLoading: false,
        chatEnvLoadSuccess: false,
        chatEnvLoadError: err.message
      });
    }
  }

  handleClick = (e) => {
    if (this.rootNode.contains(e.target)) {
      return; // click inside
    }

    if (this.state.panelOpen) { // click outside
      this.togglePanel();
    }
  };

  updateUnreadCounters = async () => {
    const channels = this.state.channels;

    for (let i=0; i<channels.length; i++) {
      if (window.chatboxWrapper.isChatboxOpenBySid(channels[i].sid)) {
        channels[i].unreadMessageCount = 0;
      } else {
        const counters = await window.appTwilioClient.getChannelMessageCounters(channels[i].sid);
        channels[i].unreadMessageCount = counters.unconsumedCount;
      }
    }

    this.setState({channels: [...channels]})
  };

  updateChannelList = async () => {
    await window.appTwilioClient.updateSubscribedChannels();
    await this.mapChannelList();
  };

  mapChannelList = async () => {
    const client = window.appTwilioClient;
    const channels = mapChannels(client.getSubscribedChannels(), client.getDispatchEntryChannelDescriptors());

    this.setState({isLoading: true});

    for (let i=0; i<channels.length; i++) {
      if (!channels[i].isSubscribed)
        continue;

      const twiChannel = client.getSubscribedChannel(channels[i].sid);
      const twiMessage = await twiChannel.getMessages(1);

      if (twiMessage.items.length > 0) {
        channels[i].lastMessage = mapMessage(twiMessage.items[0]);

        if (window.chatboxWrapper.isChatboxOpenBySid(channels[i].sid)) {
          channels[i].unreadMessageCount = 0;
        } else {
          const counters = await client.getChannelMessageCounters(channels[i].sid);
          channels[i].unreadMessageCount = counters.unconsumedCount;
        }
      }
    }

    log.d('channel list re-mapping done!');
    const sortedChannels = [...sortChannels(channels)];

    this.setState({
      isLoading: false,
      channels: sortedChannels,
      selectedTabId: this.defaultTabId,
      displayingChannels: this.filterChannelsByTabId(this.defaultTabId, sortedChannels)
    });
  };

  togglePanel = () => this.setState({panelOpen: !this.state.panelOpen});

  handleClose = () => this.setState({panelOpen: false});

  handleTabChanged = (tabId) => {
    const displayingChannels = this.filterChannelsByTabId(tabId, this.state.channels);

    this.setState({isTabContentLoading: true})

    if (tabId === TabType.CLOSED && displayingChannels.length === 0) {
      api.get('/messaging/callChannels/closed').then(result => {
        const closedChannels = result.map(x => {
          const cm = new ChannelModel();
          cm.sid = x.channelSid;
          cm.companyId = x.companyId;
          cm.name = x.friendlyName;
          cm.type = 'call';
          cm.active = false;
          cm.callId = x.callId;
          cm.archived = true;
          cm.isSubscribed = x.members ? x.members.find(y => y === this.props.currentUser.id) !== undefined : false;

          return cm;
        });

        this.setState({
          isTabContentLoading: false,
          selectedTabId: tabId,
          displayingChannels: [...this.state.displayingChannels, ...closedChannels]
        })
      });
    }

    this.setState({
      selectedTabId: tabId,
      displayingChannels
    })
  };

  filterChannelsByTabId = (tabId, channels) => {
    const {selectedCompanyId} = this.state;
    let filteredChannels = [];

    switch(tabId) {
      case TabType.ACTIVE:
          filteredChannels = channels.filter(c => c.type === ChannelType.CALL && c.active);
        break;
      case TabType.CLOSED:
          filteredChannels = channels.filter(c => c.type === ChannelType.CALL && !c.active);
        break;
      case TabType.DIRECT:
          filteredChannels = channels.filter(c => c.type === ChannelType.DIRECT);
        break;
    }

    if (selectedCompanyId > 0)
      filteredChannels = filteredChannels.filter(c => c.companyId === selectedCompanyId);

    return filteredChannels;
  };
  

  handleCompanySelection = (companyId) =>
    this.setState({selectedCompanyId: companyId}, () => this.handleTabChanged(this.state.selectedTabId));

  handleChatSelection = (channel) => {
    if (channel.type === ChannelType.CALL) {
      window.chatboxWrapper.openCallChat(channel.callId);
    } else if (channel.type === ChannelType.DIRECT) {
      window.chatboxWrapper.openDirectChat(channel.members);
    }
  };

  render() {
    const {
      channels,
      displayingChannels,
      panelOpen,
      chatEnvLoading,
      isLoading,
      isTabContentLoading
    } = this.state;
    const companies = getCompanies();
    const unreadMessageTotalCount = getSum(channels.map(c => c.unreadMessageCount));

    const unreadCounters = {
      active: getSum(this.filterChannelsByTabId(TabType.ACTIVE, this.state.channels).map(c => c.unreadMessageCount)),
      closed: getSum(this.filterChannelsByTabId(TabType.CLOSED, this.state.channels).map(c => c.unreadMessageCount)),
      direct: getSum(this.filterChannelsByTabId(TabType.DIRECT, this.state.channels).map(c => c.unreadMessageCount)),
    };

    return (
      <div ref={r => this.rootNode = r}>
        <a id="chatv2-inbox-button" href="#" title="Messages" onClick={this.togglePanel}>
          {unreadMessageTotalCount > 0 &&
          <RedBadge value={unreadMessageTotalCount} style={{position: 'absolute', right: '3px', top: '15px'}}/>}
          <span id="chatv2-btn" className="fa fa-comment messaging" style={{"padding": "10px"}} />
        </a>
        
        {panelOpen &&
        <ChatInboxPanel
          {...this.props}
          isLoading={chatEnvLoading || isLoading}
          isTabContentLoading={isTabContentLoading}
          channels={displayingChannels}
          unreadCounters={unreadCounters}
          selectedCompanyId={this.state.selectedCompanyId}
          companies={companies.length > 1 ? companies : null}
          onCompanySelected={this.handleCompanySelection}
          selectedTabId={this.state.selectedTabId}
          onTabChanged={this.handleTabChanged}
          onChatSelected={this.handleChatSelection}
          onClose={this.handleClose} />}
      </div>
    )
  }
}

const log = new Logger('ChatInboxContainer');

export default ChatInboxContainer;