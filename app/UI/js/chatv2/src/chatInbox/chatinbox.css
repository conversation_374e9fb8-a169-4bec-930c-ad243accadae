
.deeplink .infinite_spinner {
    margin-top: 1rem;
    transition: opacity 1s;
    background-color: #fff;
    pointer-events: none;
}
.infinite_spinner{position:relative;margin:0 auto}
.infinite_spinner_large{width:80px;height:80px}
.infinite_spinner_medium{width:52px;height:52px}
.infinite_spinner_small{width:24px;height:24px}
.infinite_spinner_spinner{position:absolute;top:0;left:0;width:100%;height:100%;-webkit-transform:rotate(-45deg);-moz-transform:rotate(-45deg);-ms-transform:rotate(-45deg);transform:rotate(-45deg);-webkit-animation:1.2s cubic-bezier(.25,.29,.54,.86) 0s infinite normal none spin;-moz-animation:1.2s cubic-bezier(.25,.29,.54,.86) 0s infinite normal none spin;-o-animation:1.2s cubic-bezier(.25,.29,.54,.86) 0s infinite normal none spin;animation:1.2s cubic-bezier(.25,.29,.54,.86) 0s infinite normal none spin}
.infinite_spinner_tail{-webkit-animation:1.2s cubic-bezier(.41,.24,.64,.69) 0s infinite normal none spin;-moz-animation:1.2s cubic-bezier(.41,.24,.64,.69) 0s infinite normal none spin;-o-animation:1.2s cubic-bezier(.41,.24,.64,.69) 0s infinite normal none spin;animation:1.2s cubic-bezier(.41,.24,.64,.69) 0s infinite normal none spin;}
@-webkit-keyframes spin{0%{-webkit-transform:rotate(0);transform:rotate(0)}
    100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}
}
@-moz-keyframes spin{0%{-moz-transform:rotate(0);transform:rotate(0)}
    100%{-moz-transform:rotate(360deg);transform:rotate(360deg)}
}
@-ms-keyframes spin{0%{-ms-transform:rotate(0);transform:rotate(0)}
    100%{-ms-transform:rotate(360deg);transform:rotate(360deg)}
}
@keyframes spin{0%{transform:rotate(0)}
    100%{transform:rotate(360deg)}
}
.infinite_spinner_bg{stroke-width:8;fill:none;opacity:.2;stroke:grey}
.infinite_spinner_bg_white{stroke:#fff}
.infinite_spinner_path{stroke-width:8;fill:none}
.infinite_spinner_white{stroke:#fff}
.infinite_spinner_blue{stroke:#50acf4}
.infinite_spinner_path{stroke-dasharray:55,200;stroke-dashoffset:90;stroke-linecap:round}
.infinite_spinner_fast{animation-duration:.6s;}
.infinite_spinner__container{display:flex;justify-content:center;align-items:center}
.infinite_spinner__container--50vh{height:50vh}

/* *** */

.c2-unread-counter {
    background-color: #fa3e3e;
    border-radius: 2px;
    color: #fff;
    padding: 1px 3px;

    font-size: 10px;
    -webkit-font-smoothing: subpixel-antialiased;
    line-height: 1.3;
    min-height: 13px;
}

.chat-inbox-panel * {
    font-family: Helvetica, Arial, sans-serif !important;
}

.messaging-container {
    width: 40px;
    position: fixed;
    background-image: url(/ui/images/tab_bg.png);
    background-position: top center;
    background-repeat: no-repeat;
    font-size: 12px !important;
    z-index: 2000;
}
.messaging-content {
    display: block !important;
    margin-top: 7px;
    width: 400px;
    float: right;
    border: 1px solid #DDDDDD;
    color: red;
    background-color: #fff;
    color: #000;
    background: #fff;
    border: 1px solid rgba(100, 100, 100, .4);
    border-radius: 0 0 2px 2px;
    box-shadow: 0 3px 8px rgba(0, 0, 0, .25);
    color: #1d2129;
    overflow: visible;
    z-index: 2000 !important;
}

.messaging-header {
    width: 100%;
    position: relative;
    display: inline-block;
}

.messaging-header-op {
    font-size: 12px !important;
    border: 0px;
    margin: 5px !important;
    font-family: 'Segoe UI', "Open Sans", Calibri, ff-meta-web-pro !important;
}

.messaging-header a {
    font-size: 12px !important;
}

.messaging-messages-list {
    max-height: 60vh;
    overflow-y: auto;
    overflow-x: hidden;
    width: 100%;
    border: 0px solid #dddfe2;
    padding: 0px;
}

.messageContent:hover {
    background-image: linear-gradient(rgba(29, 33, 41, .04), rgba(29, 33, 41, .04)) !important;
    color: #000 !important;
    cursor: pointer;
}

.messageContent {
    display: flex !important;
    background-color: #fff !important;
    padding: 6px !important;
    border-top: 1px solid #dddfe2;
}

.messageContent-icon {
    margin: 0px !important;
}

.messageContent-text {
    flex: 1;
}
.messageContent-text .author {
    color: #1d2129;
    white-space: nowrap;
    font-size: 11px;
    font-weight: normal;
}
.messageContent-text .c-date {
    color: #90949c;
    font-weight: normal;
    font-size: 11px;
}
.messageContent-text .last-message {
    color: #1d2129;
    font-weight: normal;
    zoom: 1;
    font-size: 12px;
    width: 90%;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-bottom: 5px;
}

.messaging-op {
    background: #fff !important;
    color: #90949c !important;
    display: inline !important;
    padding: 5px 5px 5px 5px !important;
    white-space: nowrap;
    font-size: 12px;
    font-family: calibri, ff-meta-web-pro, arial !important;
}

.messaging-op:hover {
    font-weight: 600 !important;
    color: #2B75BE !important;
}

.messaging-op-sel {
    font-weight: 600 !important;
    color: #2B75BE !important;
    /*border-bottom: 2px solid #000;*/
}

.messaging-op-bt {
    color: #2B75BE !important;
}

.messaging-icon-black {
    color: #90949c !important;
}
.messaging-icon-black:hover {
    color: #000 !important;
    background-image: none !important;
    background-color: #fff !important;
}
.message-avatar {
    width: 28px;
    height: 28px;
    background-color: #0D8ABC;
    text-align: center;
    border-radius: 50%;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
}

.message-avatar-content {
    height: 100%;
    align-items: center;
    justify-content: center;
    display: flex;
    color: #fff;
    font-size: 12px;
}
.cl-msg{
    width: 90%;
    font-weight: bold;
    color: #a1a1a1;
    text-align: center;
    padding: 15px;
}
.m-left {
    float: left;
}
.m-right {
    float: right
}

.m-left a {
    float: left;
    margin-left: 10px;
}

.m-right a {
    float: right;
    margin-right: 10px;
}

.lfloat {
    float: left;
}

.rfloat {
    float: right;
}

/**/

.chatlist-container {
    flex-grow: 1;
    min-height: 0;
    height: 100%;
    overflow: auto;
    font-size: 14px;
}

.m-user {
    font-family: Helvetica, Arial, sans-serif !important;
    position: relative;
    padding: 10px 30px;
    white-space: nowrap;
    user-select: none;
}

.m-user:hover {
    background: #f8f8f8;
    cursor: pointer;
}

.m-user-online {
    user-select: none;
}

.m-user-offline {
    user-select: none;
}

.m-user-online:before {
    content: '';
    position: absolute;
    background: #2ecc71;
    height: 10px;
    width: 10px;
    left: 10px;
    top: 15px;
    border-radius: 6px;
}

.m-user-offline:before {
    content: '';
    position: absolute;
    background: #b6b6b6;
    height: 10px;
    width: 10px;
    left: 10px;
    top: 15px;
    border-radius: 6px;
}

.m-row-badge {
    top: 0;
    right: 0;
    background: #fa3e3e;
    font-size: 10px;
    min-width: 14px;
    height: 18px;
    min-height: 18px;
    line-height: 18px;
    border-radius: 40%;
    color: #fff;
    font-weight: 600;
    text-align: center;
    padding: 0px 6px 0px 6px
}