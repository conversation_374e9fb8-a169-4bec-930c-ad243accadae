import React, {Component} from 'react';

import classNames from 'classnames';
import {
  TitleB<PERSON>,
  AgentBar,
  Title,
  Row,
  Column,
  ChatList,
  ChatListItem
} from '@livechat/ui-kit'
import PropTypes from "prop-types";
import {getOnlineContactsCount} from "../utils/chatUtils";


class ChatContactListMaximized extends Component {
  static propTypes = {
    contacts: PropTypes.array.isRequired,
    onItemSelect: PropTypes.func.isRequired
  };

  handleMinimize = () => {
    this.props.minimize()
  };

  render() {
    const onlineContactsCount = getOnlineContactsCount(this.props.contacts);

    return (
      <div style={{display: 'flex', flexDirection: 'column', height: '100%'}}>
        <div onClick={this.handleMinimize} style={{cursor: 'pointer'}}>
          <TitleBar title={onlineContactsCount > 0 ? `Chat (${onlineContactsCount})` : 'Chat'} />
        </div>

        {/*<AgentBar>*/}
        {/*  <Row flexFill>*/}
        {/*    <Column flexFill>*/}
        {/*      <Title>New Chat</Title>*/}
        {/*    </Column>*/}
        {/*    <Column flexFit>*/}
        {/*    </Column>*/}
        {/*  </Row>*/}
        {/*</AgentBar>*/}

        <div className="chatlist-container">
          <ChatList style={{maxWidth: 300}}>
            {this.props.contacts.map((contact, i) => (
              <ChatListItem
                key={i}
                className={classNames('m-user', {
                  'm-user-online': contact.isOnline,
                  'm-user-offline': !contact.isOnline
                })}
                style={{padding: '10px 30px', border: '0px'}}
                onClick={() => this.props.onItemSelect(contact)}>
                <Column>
                  <Row style={{margin: 'auto'}}>
                    <Title ellipsis>{contact.name}</Title>
                  </Row>
                </Column>
              </ChatListItem>
            ))}
          </ChatList>
        </div>

        {/* <div className="chatbox-bottombar">
          {'footer text here!'}
        </div> */}
      </div>
    )
  }
}

export default ChatContactListMaximized;