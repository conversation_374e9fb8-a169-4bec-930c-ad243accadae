<%@ Master Language="C#" AutoEventWireup="true" CodeFile="Towbook.master.cs" Inherits="UI_Towbook" %><% if (RegularMode) { %><!DOCTYPE html>
<html data-master="V3">
<head id="Head1" runat="server">
    <title>Towbook Management Software</title>
    <meta http-equiv="Content-type" content="text/html;charset=UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" >
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">

    <% if (!UseJquery) { %>
    <script type='text/javascript' src='https://ajax.googleapis.com/ajax/libs/prototype/1.6.1/prototype.js'></script>
    <script type='text/javascript' src='https://ajax.googleapis.com/ajax/libs/scriptaculous/1.8.2/scriptaculous.js'></script>

    <script type='text/javascript' src='/UI/js/lightview.js'></script>
    <script type='text/javascript' src='/UI/js/autocomplete.js'></script>
    <script src="/UI/js/menu.js" type="text/javascript"></script>
    <script type='text/javascript' src='/UI/js/lightbox.js'></script>

    <% }  %>

    <script type='text/javascript' src='/UI/js/sorttable.js'></script>
    <% if (UseJquery) { %>
    <script src="//ajax.aspnetcdn.com/ajax/jQuery/jquery-1.7.min.js" type="text/javascript"></script>
    <script src="//ajax.googleapis.com/ajax/libs/jqueryui/1.8.14/jquery-ui.min.js" type="text/javascript"></script>
    <script src="//ajax.aspnetcdn.com/ajax/jquery.validate/1.9/jquery.validate.js" type="text/javascript"></script>
    <script src="//ajax.aspnetcdn.com/ajax/mvc/3.0/jquery.validate.unobtrusive.js" type="text/javascript"></script>
    <script src="/ui/js/jquery.tmpl.min.js"></script>
    <script type="text/javascript" src="/ui/js/jquery.timepicker.min.js?v=<%=CacheBreakerKey%>"></script>
    <script type="text/javascript" src="/ui/js/timepicker/datepair.js"></script>
    <script src="/ui/js/accounting.js"></script>
    <script src="/ui/js/jquery.playSound.js"></script>

    <script src="//js.pusher.com/5.0/pusher.min.js"></script>
    <script src="/UI/JS/pusher/pusher-auth.js"></script>
    <% if (ShowRedesignMode) { %>
    <script type="text/javascript" src="/UI/JS/Towbook/callRequestsv2.js?v=<%=CacheBreakerKey%>"></script>
    <% } else { %>
    <script type="text/javascript" src="/UI/JS/Towbook/callRequests.js?v=<%=CacheBreakerKey%>"></script>
    <% } %>
    <script type="text/javascript" src="/UI/JS/SagazQuery/SagazQuery-1.2.js"></script>
    <script type="text/javascript" src="/UI/JS/util-generic.js"></script>
    <script type="text/javascript" src="/UI/JS/Towbook/Towbook.js?v=<%=CacheBreakerKey%>"></script>
    <script type="text/javascript" src="/UI/JS/moment.min.js"></script>

    
    <script type="text/javascript" src="/UI/JS/Towbook/chat.js?v=<%=CacheBreakerKey%>"></script>
    <script type="text/javascript" src="/UI/JS/Towbook/notifications.js?v=<%=CacheBreakerKey%>"></script>

    <script type="text/javascript" src="/UI/JS/jquery.blockUI.js"></script>    
    <% if (!UseReact) { %>
    <script type="text/javascript" src="/UI/JS/history/jquery.history.js"></script>
    <% } %>
    <script type="text/javascript" src="/UI/js/swal2/sweetalert2.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.6/js/select2.min.js"></script>

    <script src="/UI/js/jquery.ui.selectmenu.js" type="text/javascript"></script>
    <script src="/UI/js/jquery.tipTip.js" type="text/javascript"></script>
    <script src="/UI/js/jquery.tipsy.js" type="text/javascript"></script>

    <script src="/UI/js/jquery.tablesorter.min.js" type="text/javascript"></script>
    <script src="/UI/js/jquery.fancybox-1.3.4/fancybox/jquery.fancybox-1.3.4.js?v=<%=CacheBreakerKey%>" type="text/javascript"></script>
    <script src="/UI/js/jquery.watermark-3.1.3/jquery.watermark.min.js" type="text/javascript"></script>

    <link rel="stylesheet" type="text/css" href="/UI/js/jquery.fancybox-1.3.4/fancybox/jquery.fancybox-1.3.4.css" media="screen" />

    <% } %>
    <script src="/UI/js/Towbook.js" type="text/javascript"></script>

    <link rel="stylesheet" href="/ui/css/jquery-ui.css">
	<link rel="stylesheet" href="/ui/css/theme/jquery-ui-1.8.21.custom.css">
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/font-awesome/4.4.0/css/font-awesome.min.css">
    <link rel="stylesheet" href="/UI/fonts/fa/css/all.css">

    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap" rel="stylesheet">
    <link href='https://fonts.googleapis.com/css?family=Open+Sans' rel='stylesheet' type='text/css'>
    <link rel="stylesheet" href="/UI/js/swal2/sweetalert2.css" type="text/css" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.6/css/select2.min.css" rel="stylesheet" />

<% if(HttpContext.Current.Items["towbook2"] != null) { %>
    <link rel="stylesheet" href="/UI/css/towbook2.css?v=<%=CacheBreakerKey%>" type="text/css" />
<% } else { %>
    <% if (ShowRedesignMode) { %>
    <link rel="stylesheet" href="/UI/css/towbookv2.css?v=<%=CacheBreakerKey%>" type="text/css" />
    <% } else { %>
    <link rel="stylesheet" href="/UI/css/towbook.css?v=<%=CacheBreakerKey%>" type="text/css" />
    <% } %>
<% } %>
    <link rel="stylesheet" href="/UI/css/application-menus.css?v=<%=CacheBreakerKey%>" type="text/css" />
    <link rel="stylesheet" href="/UI/css/application-forms.css?v=<%=CacheBreakerKey%>" type="text/css" />
    <link rel="stylesheet" href="/UI/css/print.css" media="print" type="text/css" />
    <link rel="stylesheet" href="/UI/css/ibox.css" type="text/css" />
    <link rel="stylesheet" href="/UI/css/tiptip.css" type="text/css" />
    <link rel="stylesheet" href="/UI/css/towbook-home.css" type="text/css" />
    <link rel="stylesheet" href="/reports/reports.css" type="text/css" />
    <link rel="stylesheet" type="text/css" href="/UI/css/lightview.css" />
    <link rel="stylesheet" type="text/css" href="/UI/css/menu.css" />


<% Response.WriteFile("/UI/templates/notifications.tpl.html"); %>

<% if(Global.CurrentUser.PrimaryCompanyId == 4817) { %>

<script>
  $(function() {
	$('body').addClass("confirm-eta");
	});
</script>
<% } %>

<style>
    .pageContainer {
        margin: 0 auto;
        max-width: 2000px;
        background: var(--slate-2);
        height: 100%;
        display: flex;
        flex-direction: column;
    }

    #header {
        flex: 0;
    }

    .contentContainer {
        flex: 1;
        overflow: auto;
    }
</style>

</head>
<body style="background-repeat: repeat-x; background-position: bottom; background-image: none; background-color: #efefef " data-current-company-id="<% =Global.CurrentUser.CompanyId %>">

<% }  %>

<% if (UseJquery) { %>
    <script>
        $(function () {
            var found = $('.towbook-top-navigation .submenu').closest('li').each(function (e) {
                var firstLink = $(this).find('a').first();
                $(firstLink).on("click", function (e) {
                    var submenu = $(this).next();
                    if (!submenu.is(":visible")) {
                        $('.submenu').stop(false, true).hide();
                        submenu.show();
                    }
                    else
                        submenu.hide();

                    return false;
                });
            });
        });
    </script>
<% } %>

    <% if (RegularMode)
       { %>
    <div class="pageContainer">
      <div id="header" <% if (Request.Url.Host.ToLowerInvariant().EndsWith("towbook.dev")) { %>style="background: #38992b"<% } %>>
        <ul id="root" class="towbook-top-navigation">
          <li class="towbook-logo" title="Towbook Account #<%=Global.CurrentUser.CompanyId %>" >&nbsp;
          <% =NavigationItemsCustom%>
            <li class="x-icon"><a href="/support/" title="Help &amp; Support"><span class="nav-icon nav-icon-help"></span></a></li>
            <li class="x-icon"><a href="/security/logout.aspx?id=<%=Extric.Towbook.Core.MD5(Extric.Towbook.WebShared.WebGlobal.CurrentUser.Id.ToString() + "+logout") %>" title="Log Out"><span class="nav-icon nav-icon-logout"></span></a>
                  <div class="submenu" style="max-height:500px">
                    <ul>
                        <li><strong><% = HttpUtility.HtmlEncode(Global.CurrentUser.FullName) %></strong></li>
                        <li><a style="padding: 5px" href="/security/logout.aspx?id=<%=Extric.Towbook.Core.MD5(Extric.Towbook.WebShared.WebGlobal.CurrentUser.Id.ToString() + "+logout") %>">Log Out</a></li>
                        <% if (this.IsMultiCompanyUser) { %>
                        <li class="head">Switch to another company...</li>
                        <% } %>
                    </ul>
                    <% if (this.IsMultiCompanyUser) { %>
                    <ul style="max-height: 400px; overflow-y: scroll; overflow-x: hidden">
                        <% foreach(var c in this.Companies) {
                            if (Global.CurrentUser.CompanyId == c.Id) { %>
                        <li class="current" style="padding: 5px; color: gray"><%=HttpUtility.HtmlEncode(c.ShortName) %> (current)</li>
                        <% } else { %>
                        <li><a style="padding: 5px" href="/change.aspx?c=<%=c.Id %>"><% =HttpUtility.HtmlEncode(c.ShortName) %></a></li>
                        <% } %>
                        <% } %>
                    </ul>
                    <% } %>
                  </div>
            </li>
			<% if (Global.CurrentUser.Company.HasFeature(Extric.Towbook.Generated.Features.Notifications_StandardEventNotifications)) { %>
			<li class="x-icon notifications">
				<a href="/notifications" title="Notifications &amp; Alerts">
					<div class="x-icon-bubble" style="display: none"></div>
					<span class="nav-icon nav-icon-alerts"></span></a>
				<div class="submenu">
					<div class="x-empty-notifications">You have no new alerts to review.</div>
					<div class="x-notification-container">
						<ul class="x-notifications"></ul>
					</div>
					<div style="text-align: center; padding-top: 5px; font-size: 12px; display: none"><a href="#" style="">View all</a></div>
				</div>
			</li>
            <% } %>

          <% if (Global.CurrentUser.Company.HasFeature(Extric.Towbook.Generated.Features.Chat) &&
      !Global.CurrentUser.Company.HasFeature(Extric.Towbook.Generated.Features.ChatV2)
      && new int[] {
                         (int)Extric.Towbook.User.TypeEnum.Manager,
                         (int)Extric.Towbook.User.TypeEnum.Dispatcher,
                         (int)Extric.Towbook.User.TypeEnum.Driver,
                         (int)Extric.Towbook.User.TypeEnum.Accountant
           }.Contains((int)Global.CurrentUser.Type))
              { %>
                <li class="x-icon chat">
					<a href="#" title="Chat">
                        <div class="x-icon-bubble" style="display: none"></div>
						<span class="nav-icon nav-icon-chats"></span></a>
            </li>
<% } %>
            <% if (Global.CurrentUser.Company.HasFeature(Extric.Towbook.Generated.Features.ChatV2)) { %>
            <li class="x-icon" id="chatv2-inbox-container"></li>
            <% } %>
            <li class="header-company-detail"><strong><%

    if (this.IsMultiCompanyUser)
    {
        var cf = this.Companies.Where(o => o.Id == Global.CurrentUser.CompanyId).FirstOrDefault();
        if (cf != null)
            Response.Write(HttpUtility.HtmlEncode(cf.ShortName));
        else
            Response.Write(HttpUtility.HtmlEncode(Global.CurrentUser.Company.Name));
    }
    else
    {
        Response.Write(HttpUtility.HtmlEncode(Global.CurrentUser.Company.Name));
    }%></strong><br />
                <% = HttpUtility.HtmlEncode(Global.CurrentUser.FullName) %> (<% =Global.CurrentUser.Type.ToString().Replace("SystemAdministrator", "Administrator")%>)
            </li>
        </ul>
        <div style="clear: both"></div>
      </div>
  <script type="text/javascript">
      function toggleSearch(link) {
          if ($('#ajax-container').is(":visible")) {
		$('#ajax-container').slideUp();
		return;
	   }
          $('#ajax-content').html('');
          $('#ajax-container').css("z-index", 1).slideDown();

          $('#ajax-content').load($(link).attr('href'), function () {
              $('#ajax-loader').hide();
              console.log("search panel loaded");
          });
      }
  </script>

  <div id="toolbar">
    <ul>
        <asp:contentplaceholder id="Toolbar" runat="server"></asp:contentplaceholder>
    </ul>
    <div style="clear: both"></div>
  </div>

  <style type="text/css">
 .autocomplete-w1 { background:url(shadow.png) no-repeat bottom right; position:absolute; top:7px; left:6px; /* IE6 fix: */ _background:none; _top:1px; }
    .autocomplete { width:300px; border:1px solid #999; background:#FFF; cursor:default; text-align:left; max-height:350px; overflow:auto; margin:-6px 6px 6px -6px; /* IE specific: */ _height:350px;  _margin:0px 6px 6px 0; overflow-x:hidden; z-index: 10000}
    .autocomplete .selected { background:#F0F0F0; }
    .autocomplete div { padding:2px 5px; white-space:nowrap; }
    .autocomplete strong { font-weight:normal; color:#3399FF; }
    #gscus { width: 500px }
    #gsmake { width: 120px }
    #gsmodel { width: 140px }
    #gsdriver { width: 172px }
    #gsdispatcher { width: 172px }

    #ajax-loader { height: 100px; background-position: 50% 50%; background-repeat: no-repeat; background-image: url(/ui/images/ajax-loading1.gif) }
  </style>
  <div id="ajax-container" style="<% if (!V2UIMode) { %>background-image: url(/ui/images/topbg.png); background-position: 0px -41px; height: 170px; color: White; <% } %>position: absolute; display: none; width: 100%; padding-bottom: 9px;">
    <div style="padding: 10px">
        <div id="ajax-loader"></div>
        <div id="ajax-content"></div>
    </div>
    </div>
  <% if ((Request.QueryString["wizard"] == "1") && ShowWizardText == true)
     { %>
  <div style="border-bottom: solid 1px #afafaf; background-image: url(/ui/images/forms/col-description.jpg); padding: 10px; font-size: 14px; font-weight: bold">
  <strong style="color: #afafaf">Setup Wizard</strong> <% =WizardText%></div>

  <% } %>

    <div class="contentContainer">
        <asp:contentplaceholder id="content" runat="server">
        </asp:contentplaceholder>
    </div>
  </div>
<!-- BEGIN FOOTER -->
  <div id="footer" <% if (HideFooter) { %>style="display:none"<% } %>>
    Copyright 2007-<% =DateTime.Now.Year %> Towbook / Extric LLC |<br />
    <span style="font-weight: normal; color: #999">- Server Time: <% =Extric.Towbook.WebShared.WebGlobal.OffsetDateTime(DateTime.Now).ToShortTimeString() %> | <% =decimal.Round((decimal)(DateTime.Now.Subtract((DateTime)HttpContext.Current.Items["Request_Start_Time"]).TotalMilliseconds), 0) %>ms</span>
  </div>

<% if (Request.QueryString["tab"] != null) { %>
<script type="text/javascript">
    changeActiveTab('tabcontrol1', '<% =Request.QueryString["tab"] %>', document.getElementById('<% =Request.QueryString["tab"].Replace("tab", "at") %>'));
</script>
<% } %>

<% } %>


        <style>
          #callRequestsBar {
                display: none;
                position: fixed;
                bottom: 0;
                left: 0;
                right: 0;
                background-color: #ffffff;
                /*min-height: 75px;*/ border-top: solid 1px #afafaf;
                padding: 8px;
                overflow-y: scroll;
                max-height: 33%;
                z-index: 999;
            }

            #callRequestContainer > div:not(:first-child) { border-left: solid 5px black; padding-left: 10px }
            #callRequestContainer > div > span { color: green; font-size: 21px; font-family: 'segoe ui', 'Open Sans'}
            #callRequestContainer > span.status {
                float: right;
            }
            #callRequestContainer > span.status > img {
                border: solid 1px green; border-radius: 33px; padding: 3px
            }
            #callRequestContainer li:hover {
                background-color: #f7f7f7;
            }
            #callRequestContainer li > strong { font-weight: normal; font-size: 18px; font-family: 'segoe ui', 'Open Sans', Calibri; color: #2B75BE; }
            #callRequestContainer li > ul > li > span {
                color: #555; padding-right: 10px; display: inline-block; min-width: 70px;
            }

            #callRequestContainer > ul li:first-child { margin-top: 10px;}
            #callRequestContainer > ul > li {
                margin-bottom: 10px;
                background-color: white;
                border-left: solid 4px #2B75BE;
                padding-left: 10px;
                padding-bottom: 0px;
                padding-top: 0px;
                cursor: pointer
            }

            #callRequestContainer li > a {
                color: green;
                font-size: 19px;
                text-decoration: none;
                margin-right: 10px;
                margin-bottom: 10px
            }

            #callRequestContainer li > a:hover {
                text-decoration: underline;
            }

            #callRequestContainer li .expiration-less-than-30 {
                background-color: red !important;
            }
            #callRequestContainer li .expiration {
                background-color: #777;
                padding: 3px 6px;
                color: white;
                font-size: 14px;
                font-weight: bold;
                position: absolute;
                margin-top: 2px;
                margin-left: 10px;
                text-transform: uppercase;
            }

            #callRequestContainer li .expiration > span {
                font-weight: bold;
            }

            #callRequestContainer li .expiration > span:last-child {
                font-weight: normal;
            }

			#toggleRequestBar { background-color: rgb(239, 239, 239);display:block;min-height:  5px;border: solid 1px #afafaf;border-radius: 1px; }
			#toggleRequestBar:hover { background-color: orange }

            #callRequestsBar #toggleRequestBar {
                -webkit-animation: none;
                -moz-animation: none;
                -o-animation: none;
                -ms-animation: none;
                animation: none;
            }
            #callRequestsBar.pulse #toggleRequestBar {
                -webkit-animation: pulse 1.2s infinite;
                -moz-animation: pulse 1.2s infinite;
                -o-animation: pulse 1.2s infinite;
                -ms-animation: pulse 1.2s infinite;
                animation: pulse 1.2s infinite;
            }

		    @keyframes pulse {
		        0% {
		            background-color: orange;
		        }
		        100% {
		            background-color: #efefef;
		        }
		    }

            #expandCollapse { width: 16px; min-height: 5px; float: right; padding-left: 6px; display: none; }
            #toggleRequestView:hover {cursor: pointer;}
            #toggleRequestView > i {line-height: 13px; font-size: 16px; position: absolute; top: 4px;}

            #callRequestsBar #expandCollapse {display: none; }
            #callRequestsBar.expanded #expandCollapse,
            #callRequestsBar.pulse #expandCollapse {
                display: inline-block;
            }

            #callRequestsBar.pulse #toggleRequestView .fa-expand { display: inline-block; }
            #callRequestsBar.pulse #toggleRequestView .fa-times { display: none; }

            #callRequestsBar.pulse.expanded #toggleRequestView .fa-expand,
            #callRequestsBar.full-screen #toggleRequestView .fa-expand {
                display: none;
            }

            #callRequestsBar.expanded #toggleRequestView .fa-times,
            #callRequestsBar.full-screen #toggleRequestView .fa-times {
                display: inline-block;
            }

        </style>
		<div id="callRequestsBar">
            <div><div id="expandCollapse"><a id="toggleRequestView"><i class="fa fa-times"></i><i class="fa fa-expand"></i></a></div><div style="overflow:hidden;"><a id="toggleRequestBar" href="#"></a></div></div>
			<div id="callRequestContainer"><ul></ul></div>
			<span class="status" style="display: none">
				<img src="/ui/images/icons/checkmark.png" />
			</span>
		</div>


        <div id="chatBar" class="list">
            <div id="dragbarChat"></div>
            <div class="header">
                <div class="header-row primary">
                    <div>
                        <i class="fa fa-bars"></i>
                        <div class="title-container">
                            <div><span>Call Chat</span><%if(Global.CurrentUser.Type == Extric.Towbook.User.TypeEnum.Manager) {%>
                                <% if (Extric.Towbook.Integration.CompanyKeyValue.GetFirstValueOrNull(Global.CurrentUser.CompanyId, Extric.Towbook.Integration.Provider.Towbook.ProviderId, "Permission_Chats_Delete") != "2") { %><i class="fa fa-trash"></i><%} %><i class="fa fa-cog"></i><% } %></div><span></span>
                        </div>
                        <i class="fa fa-close"></i>
                    </div>
                </div>
                <div class="header-row secondary"><!-- search to go here --></div>
                <div class="header-row tiertiary">
                    <div>
                        <ul id="filter-list">
                            <li data-type-id="2" data-status-id="0">Active<span class="notification"><i class="fa fa-circle"></i></span></li>
                            <li data-type-id="2" data-status-id="1">Closed<span class="notification"><i class="fa fa-circle"></i></span></li>
                            <li data-type-id="1" data-status-id="0" style="display: none;">Company<span class="notification"><i class="fa fa-circle"></i></span></li>
                            <li data-type-id="3" data-status-id="0" style="display: none;">Private<span class="notification"><i class="fa fa-circle"></i></span></li>
                        </ul>
                        <ul id="participant-list"></ul>
                    </div>
                </div>
            </div>
            <div class="body">
                <ul id="chatBarMessageList"></ul>
            </div>
            <div class="footer">
                <div id="message-footer">
                    <div id="message-input-container"><textarea id="message-input"></textarea><a id="message-input-send">SEND</a></div>
                    <div id="create-message-input-container"><input type="button" class="flat-blue" value="Create New Chat" /><input type="button" class="cancel-button" value="Cancel" /></div>
                </div>
            </div>
            <div id="editMessageDetails" class="classy-box">
                <div><h1>Select Name and Participants</h1><i class="fa fa-times"></i></div>
                <ul>
                    <li><label for="createTitle">Chat Name</label><input class="create-chat-title" name="createTitle" type="text" placeholder="Name"/></li>
                    <li><label for="createParticipants">Participants</label><select multiple class="create-chat-participants" name="createParticipants" data-width="100%"></select></li>
                </ul>
                <input type="button" class="flat-blue" value="Save" />
            </div>
            <div id="deleteChatDetails" class="classy-box">
                <div><h1>Delete chat</h1><i class="fa fa-times"></i></div>
                <p>Are you sure you want to delete this chat?</p>
                <input type="button" class="flat-blue" value="delete" />
            </div>
            <div id="chatBarLoadingMoreTop"><span class="tb-worm"></span>Loading...</div>
            <div id="chatBarLoadingMoreBottom"><span class="tb-worm"></span>Loading ...</div>
            <div id="chatBarLoading"><div><span class="tb-worm"></span>Loading ...</div></div>
        </div>

        <script type='text/x-jQuery-tmpl' id='t-chatBarMessageListItem'>
            <li id="msg_${id}" data-id="${id}" data-create-date= "${createDate}" data-last-activity-date="${lastActivityDate}" data-status="${status}" class="messageItem">
                <div>
                    <div class="unread-container {{if type == 1}}channel{{else}}call{{/if}}">
                        <i class="fa {{if type == 1}}fa-group{{else}}fa-comments{{/if}}"></i><span class="x-unread-bubble" title="${unreadMessageTitle}">${unreadMessages}</span>
                    </div>
                    <div class="lastActivityContainer">
                        <div>
                            <div class="title">${_enc(name)}</div>
                            <div class="timestamp">${relativeTime} ago</div>
                        </div>
                        <div class="lastMessageContainer">
                            {{if lastOwner}}
                            <span class="lastOwner">${_enc(lastOwner)}:</span>
                            {{/if}}
                            {{if lastMessage}}
                            <span class="lastMessage">${_enc(lastMessage)}</span>
                            {{/if}}
                        </div>
                    </div>
                </div>
            </li>
        </script>

        <script>
            var towbookCompanies = JSON.parse('<% = HttpUtility.JavaScriptStringEncode(GetCompanies()) %>');
            var towbookAccounts = JSON.parse('<% = HttpUtility.JavaScriptStringEncode(GetAccounts()) %>');
            var towbookUsers = JSON.parse('<% = HttpUtility.JavaScriptStringEncode(GetUsers()) %>');
            var towbookDrivers = JSON.parse('<%  = HttpUtility.JavaScriptStringEncode(GetDrivers()) %>');
            var towbookCurrentUser = JSON.parse('<% = HttpUtility.JavaScriptStringEncode(GetCurrentuser()) %>');
        </script>

        <script type='text/x-jQuery-tmpl' id='t-callRequest'>
    <li data-id="${callRequestId}" data-time="${expirationDate}" class="callRequest">
        <strong>{{if serviceNeeded}}${serviceNeeded}{{/if}} Request from ${account.company}</strong>
        <span class="expiration">
            <span class="time">0 sec</span>
            <span>remaining</span>
        </span>
        <ul>
            {{if offerTypeText   }} <li style="background-color: #efefef; font-weight: bold; padding: 7px 0px 4px 4px; display: inline-block;"><span style="color:  green">${offerTypeText}</span>${towbook.formatMoney(offerAmount)}</li>{{/if}}
            {{if startingLocation   }} <li><span>Location</span>${startingLocation}</li>{{/if}}
            {{if destinationLocation}} <li><span>Destination</span>${towDestination}</li>{{/if}}
            {{if vehicle            }} <li><span>Vehicle</span>${vehicle}</li>{{/if}}
            {{if company            }} <li><span>Company</span>${company}</li>{{/if}}
            {{if expirationDate }} <li><span>Expires</span><i class="expirationTime" title="${towbook.formatAMPM(expirationDate)}">${towbook.formatAMPM(expirationDate)}</i></li>{{/if}}
        </ul>
    </li>
</script>

<div id="callRequestViewerContainer" style="display:none">
<div id="callRequestViewer" class="towbook-popup">
    <h2><span class="masterAccountName"></span> Dispatch Offer
        <span class="timer">
            <span>35</span>
            <span style="">Seconds remaining to respond</span>
        </span>
    </h2>

    <div class="summary">
        <ul></ul>
    </div>

    <div class="details">
        <ul></ul>
    </div>

    <nav>
        <a href="#" class="x-accept">Accept</a>
        <a href="#" class="x-reject">Refuse</a>
        <a href="#" class="x-requestCall">Request Phone Call</a>
        <% if (Global.CurrentUser.Company.HasFeature(Extric.Towbook.Generated.Features.Mapping)) { %>
            <a href="#" class="x-viewMap">
                View on Map</a>
        <% } else { %>
            <a href="#" onclick="viewOnMapFeature()" style="color: #aaa">
                View on Map</a>
        <% } %>
        <a href="#" class="x-close block-cancel">Close</a>

        <script>
            function viewOnMapFeature() {
                var pup = new Popup({
                    title: 'You\'re missing a great feature',
                    width: 600,
                    height: 400,
                    modal: false
                });

                var content = '<div id="sample" class="h3box" style="height: 100%"><div class="left">'
                    + '<img src="/ui/images/map-sample-1.png" class="mac_pc"/>'
                    + '<div class="tb-logo"></div>'
                    + '</div><div class="right">'
                    + '<h1>View your entire operation, <br>from 100,000 feet</h1>'
                    + '<p>Towbook is integrated with Google Maps, and can show you the current locations of your drivers, active calls, and even incoming call requests.</p>'
                    + '<p>Dispatch visually, by finding the closest driver to a call.  &nbsp;See where your drivers are currently, and where they have been.  &nbsp;Decide whether or not a call is in your coverage area, and if you can accept it.</p> '
                    + '<p>Let us know if you have questions or if you want this feature added to your Towbook plan.</p>'
                    + '</div></div>'

                pup.show();
                pup.setSubtitle('View your active calls and drivers on a map');
                pup.setContent(content);
            }
        </script>
    </nav>

    <div class="subContainer">
     <div id="acceptCall"  style="display:none">
        <h2 id="rule-title">Accept Call</h2>
             <ul>
                <li>
                    <label for="cr-eta">ETA</label>
                    <select id="cr-eta" style="width: 100px">
                        <option>5</option>
                        <option>10</option>
                        <option>15</option>
                        <option>20</option>
                        <option>25</option>
<% if (Global.CurrentUser.CompanyId == 20005) { %>
                        <option>29</option>
<% } %>
                        <option>30</option>
                        <option>35</option>
                        <option>40</option>
<% if (Global.CurrentUser.CompanyId == 20005) { %>
                        <option>44</option>
<% } %>
                        <option>45</option>
                        <option>50</option>
                        <option>55</option>
<% if (Global.CurrentUser.CompanyId == 20005) { %>
                        <option>59</option>
<% } %>
                        <option>60</option>
<% if (!(new int[] { 26556,28172, 28173,28437 }.Contains(Global.CurrentUser.CompanyId))) { %>
                        <option>65</option>
                        <option>70</option>
                        <option>75</option>
<% if (Global.CurrentUser.CompanyId == 20005) { %>
                        <option>89</option>
<% } %>
                        <option>90</option>
<% if (Global.CurrentUser.CompanyId == 4983 || Global.CurrentUser.CompanyId == 5684 || Global.CurrentUser.CompanyId == 6991  || Global.CurrentUser.CompanyId == 3269 || Global.CurrentUser.CompanyId == 1795) { %>
                        <option>99</option>
<% } %>
                        <option>100</option>
                        <option>110</option>
<% if (Global.CurrentUser.CompanyId == 20005) { %>
                        <option>119</option>
<% } %>
                        <option>120</option>
                        <option>150</option>
                        <option>180</option>
                        <option>210</option>
                        <option>240</option>
                        <option>300</option>
                        <option>360</option>
                        <option>420</option>
                        <option>480</option>
                        <option>540</option>
                        <option>600</option>
                        <% if (Global.CurrentUser.CompanyId == 4983 || Global.CurrentUser.CompanyId == 5684 || Global.CurrentUser.CompanyId == 6991 || Global.CurrentUser.CompanyId == 3269 || Global.CurrentUser.CompanyId == 1795) { %>
                        <option>1000</option>
                        <option>1200</option>
                        <% } %>
<% } %>
                    </select>
                </li>
                <li id="cr-reason-row">
                    <label id="cr-reason-label" for="cr-reason" style="width: 250px">Reason</label>
                    <select id="cr-reason" style="width: 100px"></select>
                </li>

                <li id="cr-driver-row">
                    <label id="cr-driver-label" for="cr-driver" style="width: 250px">Driver</label>
                    <select id="cr-driver" style="width: 100px"></select>
		        </li>
                <li id="cr-tire-row">
                    <label id="cr-tire-yesno-label" for="cr-reason" style="width: 350px">Do you have the size in the brand requested?</label>
                    <select id="cr-tire-yesno" style="width: 100px">
                        <option value=""></option>
                        <option value="true">Yes</option>
                        <option value="false">No</option>
                    </select>
                    <label id="cr-tire-notes-label" for="cr-reason" style="width: 250px">What brand can you provide instead?</label>
                    <input id="cr-tire-notes" type="text" maxlength="500" style="width: 300px" />
                </li>
            </ul>

        <div class="callRequest_actionGroup">
            <input id="cr-accept" type="button" class="callRequestAccept" value="Accept" />
            <input type="button" class="callRequestCancel block-cancel" value="Cancel" />
        </div>
    </div>

    <div id="rejectCall" style="display:none">
        <h2>Refuse Call</h2>
            <ul>
                <li>
                    <label for="rc-reason" id="rc-reason-label">Reason for Rejecting</label>
                    <select id="rc-reason">
                    <option>OutOfArea</option>
                    <option>EquipmentNotAvailable</option>
                    <option>RestrictedRoadway</option>
                    <option>RefusedPaymentIssue</option>
                    <option>UnsafeLocation</option>
                </select>
                </li>
            </ul>

        <div class="callRequest_actionGroup">
            <input id="cr-reject" type="button" class="callRequestReject" value="Confirm" />
            <input type="button" class="callRequestCancel block-cancel" value="Cancel" />
        </div>
    </div>
  </div>
</div>
</div>

<% if (Global.CurrentUser.Company.HasFeature(Extric.Towbook.Generated.Features.ChatV2)) { %>
<script src="https://www.gstatic.com/firebasejs/5.8.0/firebase-app.js"></script>
<script src="https://www.gstatic.com/firebasejs/5.8.0/firebase-auth.js"></script>
<script src="https://www.gstatic.com/firebasejs/5.8.0/firebase-messaging.js"></script>
<% } %>



<% if (RegularMode) { %>

    <script>
        activateChatEvents(<%=Global.CurrentUser.Id%>, <% = Global.CurrentUser.Company.HasFeature(Extric.Towbook.Generated.Features.Chat) == true ? "true" : "false" %>);
	activateNotificationEvents(<%=Global.CurrentUser.Id%>, <% = Global.CurrentUser.Company.HasFeature(Extric.Towbook.Generated.Features.Notifications_StandardEventNotifications) == true ? "true" : "false" %>);
    </script>

 <%   if (Global.CurrentUser.Type != Extric.Towbook.User.TypeEnum.AccountUser && Global.CurrentUser.Type != Extric.Towbook.User.TypeEnum.Driver && !(Global.CurrentUser.Notes ?? "").Contains("DisableLiveDispatch"))  { %>
<script>
activateCallRequestTracking(<%=(Global.CurrentUser.PrimaryCompanyId == 5406 || Global.CurrentUser.PrimaryCompanyId == 4817) ? Global.CurrentUser.CompanyId : Global.CurrentUser.PrimaryCompanyId %>);
</script>
<% } %>

<% if (Extric.Towbook.WebShared.WebGlobal.CurrentUser.Type != Extric.Towbook.User.TypeEnum.SystemAdministrator) { %>
 <%   if (Global.CurrentUser.Type != Extric.Towbook.User.TypeEnum.AccountUser && !(Global.CurrentUser.Notes ?? "").Contains("DisableIntercom")) { %>
<script id="IntercomSettingsScriptTag">
  window.intercomSettings = {
    name: '<%= HttpUtility.JavaScriptStringEncode(Extric.Towbook.Utility.JsonExtensions.ToJson(Global.CurrentUser.FullName)) %>',
    email: <% = Extric.Towbook.Utility.JsonExtensions.ToJson(Global.CurrentUser.Email) %>,
    created_at: <%=(Int32)(Global.CurrentUser.CreateDate.Subtract(new DateTime(1970, 1, 1))).TotalSeconds%>,
    app_id: "kw06m3f5",
    user_hash: "<%=Global.HMACSHA256HashString(Global.CurrentUser.Email) %>",
	company: { id: <%=Global.CurrentUser.Company.Id %>,
		name: <% =Extric.Towbook.Utility.JsonExtensions.ToJson(Global.CurrentUser.Company.Name) %>,
		created_at:  <%=(Int32)(Global.CurrentUser.Company.CreateDate.Subtract(new DateTime(1970, 1, 1))).TotalSeconds%>,
        Phone: "<%= Extric.Towbook.Core.FormatPhone(Global.CurrentUser.Company.Phone) %>",
        Address: '<%= HttpUtility.JavaScriptStringEncode(Global.CurrentUser.Company.Address.Replace("'", "\\'")) %>',
        City: '<%= HttpUtility.JavaScriptStringEncode(Global.CurrentUser.Company.City.Replace("'", "\\'")) %>',
        State: '<%= HttpUtility.JavaScriptStringEncode(Global.CurrentUser.Company.State.Replace("'", "\\'")) %>',
        Zip: '<%= HttpUtility.JavaScriptStringEncode(Global.CurrentUser.Company.Zip.Replace("'", "\\'")) %>',
		Towbook_Email: "<%
	var x = Extric.Towbook.Utility.SqlMapper.QuerySP<dynamic>("internalGetPrimaryEmailByCompanyId", new { CompanyId = Global.CurrentUser.PrimaryCompanyId }).FirstOrDefault();
		if (x != null) {
			Response.Write(x.EmailAddress);
		}

%>",Towbook_Fax: "<%

	var zx = Extric.Towbook.Utility.SqlMapper.QuerySP<dynamic>("internalGetPrimaryFaxByCompanyId", new { CompanyId = Global.CurrentUser.PrimaryCompanyId }).FirstOrDefault();
		if (zx != null) {
			Response.Write(Extric.Towbook.Core.FormatPhone(zx.Fax));
		}

%>"
	},
    Towbook_Username: '<%= HttpUtility.JavaScriptStringEncode(Global.CurrentUser.Username) %>',
    Towbook_User_Type: "<% =Global.CurrentUser.Type.ToString() %>",
    Towbook_Company_State: '<%= HttpUtility.JavaScriptStringEncode(Global.CurrentUser.Company.State.ToUpper()) %>',
     price: { currency: "usd", amount: 0.01 }
};
</script>
<script>(function(){var w=window;var ic=w.Intercom;if(typeof ic==="function"){ic('reattach_activator');ic('update',intercomSettings);}else{var d=document;var i=function(){i.c(arguments)};i.q=[];i.c=function(args){i.q.push(args)};w.Intercom=i;function l(){var s=d.createElement('script');s.type='text/javascript';s.async=true;s.src='https://static.intercomcdn.com/intercom.v1.js';var x=d.getElementsByTagName('script')[0];x.parentNode.insertBefore(s,x);}if(w.attachEvent){w.attachEvent('onload',l);}else{w.addEventListener('load',l,false);}}})()</script>
<% } %>
<% } %>


<script>
  (function(i,s,o,g,r,a,m){i['GoogleAnalyticsObject']=r;i[r]=i[r]||function(){
  (i[r].q=i[r].q||[]).push(arguments)},i[r].l=1*new Date();a=s.createElement(o),
  m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;m.parentNode.insertBefore(a,m)
  })(window,document,'script','//www.google-analytics.com/analytics.js','ga');

  ga('create', 'UA-546662-13', 'towbook.com');
  ga('set', 'userId', '<%=Global.HMACSHA256HashString("towbook_user:" + Global.CurrentUser.Id) %>');
  ga('send', 'pageview');

</script>
 <script type="text/javascript">
    var _gauges = _gauges || [];
    (function() {
      var t   = document.createElement('script');
      t.type  = 'text/javascript';
      t.async = true;
      t.id    = 'gauges-tracker';
      t.setAttribute('data-site-id', '547f64b1eddd5b20b8021240');
      t.src = '//secure.gaug.es/track.js';
      var s = document.getElementsByTagName('script')[0];
      s.parentNode.insertBefore(t, s);
    })();
  </script>
<% if (HideIntercom) { %>
<style>.intercom-container,#intercom-container { display: none !important }</style>
<% } else { %>
<% if (CurrentSection == Navigation.NavigationItemEnum.Map) { %>
<style>#intercom-container #intercom-launcher{ right: 6px !important; bottom: 30px!important } </style>
<% } else { %>
<style>#intercom-container #intercom-launcher{ right: 30px !important; bottom: 30px!important } </style>
<% } %>
<% } %>

<% if (RegularMode) { %>
  <!-- sesssion-expired-message: Start -->
  <div id="sesssion-expired-message" class="popup-message" tabIndex="-1" style="display:none">
    <div>
      <div>
        <div>
          <div>
            <h2>Session Expired</h2>
            <p>Your session has expired due to no recent activity, click OK to return to the login page.</p>
            <ul class="buttons">
              <li onClick="RedirectToLogin()" class="button">OK</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- sesssion-expired-message: End -->
<% } %>

    <div class="unsupported-browser" style="display:none;">
        <div class="ieNotice">
            <div class="ieNotice_modal">
            <div class="ieNotice_content">
                <div class="ieNotice_title">Your web browser is out of date.</div>
                <div class="ieNotice_subtitle">Some new features being developed will no longer support this browser. Please download one of the free up-to-date browsers below.</div>
                <div class="ieNotice_browsers">

                <a href="https://www.microsoft.com/en-us/edge" target="_blank" class="ieNotice_browser">
                    <img src="/UI/images/browsers/edge_512x512.png" class="ieNotice_browserLogo"/>
                    <span class="ieNotice_browserName">
                    Edge
                    </span>
                </a>

                <a href="https://www.google.com/chrome/" target="_blank" class="ieNotice_browser">
                    <img src="/UI/images/browsers/chrome_512x512.png" class="ieNotice_browserLogo"/>
                    <span class="ieNotice_browserName">
                    Chrome
                    </span>
                </a>

                <a href="https://www.mozilla.org/en-US/firefox/new/" target="_blank" class="ieNotice_browser">
                    <img src="/UI/images/browsers/firefox_512x512.png" class="ieNotice_browserLogo"/>
                    <span class="ieNotice_browserName">
                    Firefox
                    </span>
                </a>

                </a>
                <button id="suppressIENotice" class="ieNotice_hide">Hide this message for 14 days</button>
            </div>
            </div>
        </div>
    </div>
    <link rel="stylesheet" href="/UI/css/ie-notice.css">
    <script type="text/javascript" src="/UI/js/ie-notice.js"></script>

    <div id="chatv2-chatbox-container"></div>

    <% if (Global.CurrentUser.Company.HasFeature(Extric.Towbook.Generated.Features.ChatV2)) { %>
    <script src="/UI/JS/chatv2/dist/main.js"></script>
    <% } %>

    <% if (UseReact) { %>
    <link rel="stylesheet" href="/dist/style.css?v=<%=CacheBreakerKey%>"/>
    <% } %>

    </body>
</html>
<% }  %>
