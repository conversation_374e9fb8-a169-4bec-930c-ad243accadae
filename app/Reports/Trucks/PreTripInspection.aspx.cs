using Extric.Towbook;
using Extric.Towbook.API.PreTripInspections.Models;
using Extric.Towbook.PreTripInspections;
using Extric.Towbook.Utility;
using Extric.Towbook.WebShared;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using System.Web;
using System.Web.UI;

public partial class Reports_Trucks_PreTripInspection : System.Web.UI.Page
{
    public string _preTripInspectionJson = "{}";
    public string _categoriesJson = "{}";
    public string _domain = "";
    public string _companyLogoPath = "";
    public int _id;
    public int _ownerUserId = 0;
    public PreTripInspectionModel _ei;
    public string[] _categories;
    public List<PreTripInspectionPhoto> _photos;
    public PreTripInspectionPhoto _odometerPhoto;
    public PreTripInspectionPhoto _signaturePhoto;
    public string _inspectionName;

    public Extric.Towbook.Company.Company _company = null;

    protected void Page_Load(object sender, EventArgs e)
    {
        RegisterAsyncTask(new PageAsyncTask(PageLoadAsync));
    }
    private async Task PageLoadAsync()
    {
        int.TryParse(Request.QueryString["id"], out _id);

        var ei = PreTripInspection.GetById(_id);

        if (ei == null && _id != 1)
            throw new TowbookException("You don't have permission to view this form.");

        if (ei != null)
            _ownerUserId = ei.OwnerUserId;

        // Allow email attachments to render without authenticating the user.
        var local = false;
        if (Global.CurrentUser == null)
        {
            bool noAuth = true;

            if (Request.QueryString["key"] != null)
            {
                // pre trip inspection.  NEVER reveal this salt in client side code. 
                if (Request.QueryString["key"] == Extric.Towbook.Core.ProtectId(_id, _ownerUserId))
                {
                    noAuth = false;
                    local = true;
                }
            }

            if (noAuth)
            {
                var authorized = Extric.Towbook.WebShared.WebGlobal.GetAuthorizedServers();
                var checkAgainst = Request.ServerVariables["HTTP_X_FORWARDED_FOR"] ?? Request.ServerVariables["REMOTE_ADDR"];


                if (!authorized.Contains(checkAgainst) &&
                    !checkAgainst.StartsWith("10.120.127") &&
                    !checkAgainst.Contains("169.55.3."))
                {
                    throw new Exception("You don't have access to this Form. Did you forget to login??" + Request.ServerVariables["REMOTE_ADDR"] + "|" + Request.ServerVariables["HTTP_X_FORWARDED_FOR"]);
                }
                else
                {
                    local = true;
                }
            }
        }
        else
        {
            if (Global.CurrentUser.Type == Extric.Towbook.User.TypeEnum.Driver)
                Response.Redirect("/");
        }

        var pdf = Request.QueryString["pdf"];

        _domain = WebGlobal.GetDomain();

        if (Request.QueryString["local"] == "1")
        {
            _domain = System.Configuration.ConfigurationManager.AppSettings["WebAppUrl"] != null
                ? System.Configuration.ConfigurationManager.AppSettings["WebAppUrl"]
                : "https://app.towbook.com";
        }

        if (_id > 0)
        {
            _inspectionName = "Equipment Inspection Report";

            if (pdf != null)
            {
                await PdfReport();
            }
            else
            {
                if (ei == null || (!local && !WebGlobal.CurrentUser.HasAccessToCompany(ei.CompanyId)))
                {
                    if (_id != 1)
                        throw new TowbookException("You don't have permission to view this form.");
                }

                if (_id == 1) // sample
                {
                    // only allow sample to be viewed from authenticated user
                    if (WebGlobal.CurrentUser == null)
                        throw new TowbookException("You don't have permission to view this form.");

                    var truck = Truck.GetByCompany(WebGlobal.CurrentUser.Company).FirstOrDefault() ?? new Truck();

                    ei.OwnerUserId = WebGlobal.CurrentUser.Id;
                    ei.InspectionDate = DateTime.Now.AddHours(-24);
                    ei.CompanyId = WebGlobal.CurrentUser.CompanyId;
                    ei.TruckId = truck.Id;
                    ei.UncheckedCount = 1;

                    _companyLogoPath = GetCompanyLogo(WebGlobal.CurrentUser.CompanyId);
                    _company = Global.CurrentUser.Company;
                }
                else
                {
                    _companyLogoPath = GetCompanyLogo(ei.CompanyId);
                    _company = Extric.Towbook.Company.Company.GetById(ei.CompanyId);
                }

                if (ei.PreTripInspectionItemTypeDetailId > 0)
                {
                    var detail = PreTripInspectionItemTypeDetail.GetById(ei.PreTripInspectionItemTypeDetailId.Value);
                    if (detail != null)
                    {
                        _inspectionName = detail.Name;
                    }

                }

                _ei = PreTripInspectionModel.Map(ei);

                _categories = _ei.Items.Select(item => item.Category).Distinct().ToArray();

                if(ei.Photos == null)
                    ei.Photos = new Collection<PreTripInspectionPhoto>();

                _photos = ei.Photos.Where(p => p.PhotoType == 1).ToList();
                _odometerPhoto = ei.Photos.Where(p => p.PhotoType == 2).LastOrDefault();
                _signaturePhoto = ei.Photos.Where(p => p.PhotoType == 3).LastOrDefault();
            }
        }
    }

    private async Task PdfReport()
    {
        _domain = Core.GetAppSetting("Towbook:WebAppUrl") ?? "https://app.towbook.com";

        // Get this page as HTML
        var html = "";
        var sw = new StringWriter();
        string md5 = Extric.Towbook.Core.ProtectId(_id, _ownerUserId);

        Server.Execute(Request.Path + "?local=1&id=" + _id.ToString() + "&key=" + md5, sw, true);
        html = sw.ToString();

        var footer = "<div style=\"color: #333333; font-family: verdana; font-size: 9px; font-weight: bold\"><br /><br />" +
            "Created with Towbook Management Software | www.towbook.com " +
            "<div style=\"display:inline-block; float:right; font-weight: normal;\">Printed " + DateTime.Now.ToShortDateString() + "</div></div>";

        // Convert HTML to PDF
        var outputArea = new OutputArea(0.3f, 0.3f, 7.8f, 10.3f);

        //foreach (string key in HttpContext.Current.Request.Cookies.AllKeys)
        //{
        //    options.Cookies.Add(new Cookie(
        //        HttpContext.Current.Request.Cookies[key].Name,
        //        HttpContext.Current.Request.Cookies[key].Value,
        //        HttpContext.Current.Request.Cookies[key].Path,
        //        options.BaseUrl));
        //}

        var title = "Equipment Inspection Report #" + _id;
        var creator = "Towbook Management Software | www.towbook.com";


        var stream = await PdfClientBase.GeneratePdf(html, null, outputArea, null, FileType.PDF, footer, title, creator);

        // Create the response
        var response = HttpContext.Current.Response;
        response.Clear();
        response.ClearHeaders();
        response.ContentType = "application/pdf";

        // Save PDF to response
        await stream.CopyToAsync(response.OutputStream);
        response.End();
    }

    private string GetCompanyLogo(int companyId)
    {
        var cl = Extric.Towbook.Company.CompanyLogo.GetByCompanyId(companyId);

        if (cl != null)
            return cl.Url;

        string[] opportunities = new string[] { "_template.jpg", "_full.jpg", "_left.jpg", ".jpg" };

        string baseLocal = Server.MapPath(@"..\..\ui\images\customer.logo\" + companyId);

        string baseRemote = (System.Configuration.ConfigurationManager.AppSettings["WebAppUrl"] != null
                    ? System.Configuration.ConfigurationManager.AppSettings["WebAppUrl"]
                    : "https://app.towbook.com") + "/ui/images/customer.logo/" + companyId;

        string filename = "";

        foreach (var file in opportunities)
        {
            if (File.Exists(baseLocal + file))
            {
                filename = baseRemote + file;
                break;
            }
        }

        string companyLogoPath = "";
        if (!string.IsNullOrEmpty(filename))
            companyLogoPath = filename;

        return companyLogoPath;
    }

    public DateTime OffsetDateTime(DateTime dt)
    {
        if (_company.TimezoneUseDST)
        {
            if (dt != DateTime.MinValue)
                return dt.AddHours(_company.TimezoneOffset);
            else
                return dt;
        }
        else
        {
            int extraOffset = 0;
             // take into account daylight savings time not being enabled...
            if (dt.IsDaylightSavingTime())
            {
                extraOffset = -1;
            }
            if (dt != DateTime.MinValue)
                return dt.AddHours(_company.TimezoneOffset).AddHours(extraOffset);
            else
                return dt.AddHours(extraOffset);
        }
    }
}
