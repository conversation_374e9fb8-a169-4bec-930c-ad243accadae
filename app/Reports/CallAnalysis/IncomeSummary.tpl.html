<script type="text/x-jQuery-tmpl" id="t-incomeSummaryRow">
  <tr style="{{if isVoid == 1}}color: red{{/if}}" data-id="${dispatchEntryId}">
    <td>${callNumber}</td>
    <td>${invoiceNumber}</td>
    <td>${_enc(accountName)}</td>
    <td>${reason}</td>
    <td>${purchaseOrderNumber}</td>
    <td>${dispatchNumber}</td>
    <td class="x-tip" sorttable_customkey="${parseFloat(tipsTotal)}">${towbook.formatMoney(tipsTotal)}</td>
    {{if isVoid == 1}}
      <td sorttable_customkey="${parseFloat(paymentAmount)}" style="text-decoration: line-through">${towbook.formatMoney(paymentAmount)}</td>
    {{else}}
      <td sorttable_customkey="${parseFloat(paymentAmount)}" class="x-verified-by-user{{if paymentVerificationId}} isVerified{{/if}}">{{if paymentAmount != 0}}${towbook.formatMoney(paymentAmount)}{{/if}}<i class="fa fa-check" style="padding-left: 5px;" title="Verified by ${paymentVerifiedBy} {{if paymentVerificationDate}}on ${towbook.formatDate(paymentVerificationDate)}{{/if}}"></i></td>
    {{/if}}
    <td>${_enc(paymentStringLong)}</td>
    <td>${_enc(paymentReference)}</td>
    <td>${recordedBy}</td>
    <td>${towbook.formatDate(paymentDate)}</td>
    <td>${towbook.formatDate(createDate)}</td>
  </tr>
</script>