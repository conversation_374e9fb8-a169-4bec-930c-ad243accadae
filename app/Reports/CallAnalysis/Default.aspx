<%@ Page Title="Towbook Reports" Language="C#" MasterPageFile="~/UI/TowbookV2.master" AutoEventWireup="true" CodeFile="Default.aspx.cs" Inherits="Reports_CallAnalysis_Default" %>

<%@ MasterType TypeName="UI_Towbook" %>
<asp:Content ID="Content1" ContentPlaceHolderID="Toolbar" runat="Server">
    <% if (reportType.Value == "AccountPerformance") {  %>
        <script type="text/javascript">
            if (window.localStorage.getItem("useNewReports") === "true") {
                window.location.href = window.location.origin + "/Reports/#/dispatching/account-performance";
            }
        </script>
    <% } %>

    <li><a id="exportReport" onclick="ExportReport()" href="javascript:;"><span style="background-image: url(/UI/Images/ToolbarIcons/Accounts/Invoices.png)"></span>Export to Excel</a></li>
    <% if (reportType.Value == "DispatchingAnalysis" && (Global.CurrentUser.CompanyId == 2 || Global.CurrentUser.CompanyId == 4649 || Global.CurrentUser.CompanyId == 4379))
       {  %>        
           <li><a id="exportPoliceReport" onclick="ExportReport('police')" href="javascript:;"><span style="background-image: url(/UI/Images/ToolbarIcons/Accounts/Invoices.png)"></span>Export Police Report</a></li>
    <% } %>
    <% if (reportType.Value == "DispatchingAnalysis")
       {  %>        
           <li><a id="exportSimpleReport" onclick="ExportReport('simple')" hrel="javascript:;" title="This export type does not include individual rate items." style="cursor: pointer;">
                <span style="background-image: url(/UI/Images/ToolbarIcons/Accounts/Invoices.png)"></span>Simple Export to Excel
            </a></li>
           <li id="exportAllServiceItems" visible="false" runat="server">
               <a id="exportAllServiceItemsReport" onclick="ExportReport('allserviceitems')" hrel="javascript:;" title="This export type includes all service items that exist under services & pricing." style="cursor: pointer;">
                <span style="background-image: url(/UI/Images/ToolbarIcons/Accounts/Invoices.png)"></span>Complete Service Item Export
           </a></li>
    <% } %>
    <% if (reportType.Value == "DispatchingAnalysis" && (Global.CurrentUser.CompanyId == 2 || Global.CurrentUser.CompanyId == 4649 || Global.CurrentUser.CompanyId == 4379)) {  %>
           <li><a id="exportAuctionReport" onclick="ExportReport('auction')" href="javascript:;"><span style="background-image: url(/UI/Images/ToolbarIcons/Accounts/Invoices.png)"></span>Export Auction Report</a></li>
    <% } %>
    <% if (reportType.Value == "DispatchingAnalysis" && Global.CurrentUser.CompanyId == 6991) {  %>
            <li><a id="exportCustomReport" onclick="ExportReport('custom')" href="javascript:;"><span style="background-image: url(/UI/Images/ToolbarIcons/Accounts/Invoices.png)"></span>Export Custom Report</a></li>
    <%  } %>
    <% if (reportType.Value == "DispatchingAnalysis" && Global.CurrentUser.CompanyId == 49039) {  %>
            <li><a id="exportFCSOReport" onclick="ExportReport('custom')" href="javascript:;"><span style="background-image: url(/UI/Images/ToolbarIcons/Accounts/Invoices.png)"></span>Export FCSO Report</a></li>
    <%  } %>
    <% if (reportType.Value == "AccountReceivable") {  %>
            <li><a id="exportDetailedReport" onclick="ExportReport('detailed')" href="javascript:;"><span style="background-image: url(/UI/Images/ToolbarIcons/Accounts/Invoices.png)"></span>Export Detailed</a></li>
    <%  } %>
    <% if (reportType.Value == "SalesTax") {  %>
            <li><a id="exportDetailedSalesTaxReport" onclick="ExportReport('detailed')" href="javascript:;"><span style="background-image: url(/UI/Images/ToolbarIcons/Accounts/Invoices.png)"></span>Export Detailed</a></li>
    <%  } %>

    <% if (reportType.Value == "DispatchingAnalysis") {  %>
        <% if(Extric.Towbook.WebShared.WebGlobal.Companies.Select(s => s.Id).Contains(26130)) { %>        
            <li><a id="exportDailyAnalysis" onclick="ExportReport('dailyAnalysis')" href="javascript:;"><span style="background-image: url(/UI/Images/ToolbarIcons/Accounts/Invoices.png)"></span>Export Daily Analysis</a></li>
        <% } %>
            
        <% if(Extric.Towbook.WebShared.WebGlobal.Companies.Select(s => s.Id).Contains(35210)) { %>
            <li><a id="exportMarketing" onclick="ExportReport('marketingExport')" href="javascript:;"><span style="background-image: url(/UI/Images/ToolbarIcons/Accounts/Invoices.png)"></span>Export Marketing</a></li>
        <% } %>

    <% } %>
    <% if (reportType.Value == "CallWorkflow")
        {  %>        
           <li><a id="exportSalesSnapshot" onclick="ExportReport('snapshot')" href="javascript:;"><span style="background-image: url(/UI/Images/ToolbarIcons/Accounts/Invoices.png)"></span>Export Sales Snapshot</a></li>
           <li><a id="exportDailySummary" onclick="ExportReport('dailysummary')" href="javascript:;"><span style="background-image: url(/UI/Images/ToolbarIcons/Accounts/Invoices.png)"></span>Export Daily Summary</a></li>

        <% if(Extric.Towbook.WebShared.WebGlobal.CurrentUser.Company.HasFeature(Extric.Towbook.Generated.Features.PeachTree)) { %>
            <li><a id="exportAccountingSoftware" onclick="ExportReport('accountingSoftwareExport')" href="javascript:;"><span style="background-image: url(/UI/Images/ToolbarIcons/Accounts/Invoices.png)"></span>Accounting Software Export</a></li>
        <% } %>
    <% } %>

    <% if (reportType.Value == "IncomeSummary")
        {
            if (Extric.Towbook.WebShared.WebGlobal.CurrentUser.Company.HasFeature(Extric.Towbook.Generated.Features.PeachTree))
            { 
    %>
            <li><a id="exportPayments" onclick="ExportReport('exportPayments')" href="javascript:;"><span style="background-image: url(/UI/Images/ToolbarIcons/Accounts/Invoices.png)"></span>Export Payments</a></li>
        
    <%      }
        } %>
    <% if (reportType.Value == "TruckExpenses") {  %>        
      <li><a href="#"  onclick="addNewTruckExpense(this, event); event.stopPropagation(); return false;">Record Expense</a></li>
    <% } %>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="content" runat="Server">
<script type="module" defer src="/dist/expense-photos.js?_=**********"></script>
<link rel="stylesheet" href="/dist/style.css" />
    <script>
        towbook.stickerReasons = JSON.parse('<%= HttpUtility.JavaScriptStringEncode(_stickerReasonsJson) %>');
        $.extend(towbook, JSON.parse('<%= HttpUtility.JavaScriptStringEncode(GetConfig()) %>'));
        towbook.trucks = JSON.parse('<%= HttpUtility.JavaScriptStringEncode(_trucksJson)%>');
        towbook.users = JSON.parse('<% = HttpUtility.JavaScriptStringEncode(usersJson) %>');
        impoundInclusion = <% =impoundInclusion%>;
        serviceCallsOnly = <%= serviceCallsOnly ? "true" : "false" %>;
        hasDestinationArrivalStatus = <%= hasDestinationArrivalStatus ? "true" : "false" %>;
        hasAccountingProvider = <%= hasAccountingProvider ? "true" : "false" %>;
        showAccountManager = <%= showAccountManager ? "true" : "false" %>;
        currentUser = JSON.parse('<% = HttpUtility.JavaScriptStringEncode(_currentUserJson) %>');

        <% if (new int[] { 4185, 4189, 4194 }.Contains(Global.CurrentUser.CompanyId)) { %>
        towbook.drivers = $.grep(towbook.drivers, function (o) { return !o.endDate });
        towbook.users = $.grep(towbook.users, function (o) { return !o.disabled && o.type != 3 });
        <%}%>

        <%if (reportType.Value == "StickerReport") { %>
        towbook.accounts = towbook.accounts.filter(function (a) { return a.propertyManagementDetails && a.propertyManagementDetails.stickeringEnabled; });
        <% } else if (reportType.Value == "Safeclear") { %>
        towbook.accounts = towbook.accounts.filter(function (a) { return a.name == 'City of Houston' || a.masterAccountId == 18; });
        <% } %>

        towbook.masterAccounts = JSON.parse('<% = HttpUtility.JavaScriptStringEncode(_masterAccounts) %>');
        towbook.accounts.map(function (account) {
            account.masterAccountName = "";
            if (account.masterAccountId && account.masterAccountId > 0) {
                var ma = towbook.get(towbook.masterAccounts, account.masterAccountId, "masterAccountId");
                if (ma != null)
                    account.masterAccountName = ma.name;
            }
        });

		$.extend(towbook, { paymentTypes: JSON.parse('<% = HttpUtility.JavaScriptStringEncode(_paymentTypesJson) %>') });

        var rateItems = [];
        if (towbook.companies.length > 1) {
            $.each(towbook.rateItems, function (i, o) {
                if ($.grep(rateItems, function (r) { return r.id == o.id }).length == 0) {
                    var cName = "";

                    // filter companies array to match companies of the rateItem object
                    var c = $.grep(towbook.companies, function (y) {
                        if ($.inArray(y.id, o.companies) !== -1)
                            return y.id;
                        else
                            return false;
                    });

                    cName = c.length > 0 ? c[0].name : "Built In";
                    o.nameWithCompany = cName + ": " + o.name;

                    rateItems.push(o);
                }
            });

            towbook.rateItems = rateItems;


	
<% if (Global.CurrentUser.Type == Extric.Towbook.User.TypeEnum.Dispatcher) { %>

    rateItems = [];
    $.each(towbook.rateItems, function(i, o) { 
        if ($.grep(rateItems, function(r) { return r.id == o.id }).length == 0) { 
            if (o != null && o.name != null && o.name.toUpperCase().indexOf("OVERAGE") != -1) { 
                console.log(o);
                rateItems.push(o);
            }
        }
    });

    towbook.rateItems = rateItems;
    towbook.rateItems.push({ id: "0", name: ".No Service Items" });


<% } %>

    console.log("overage: ", towbook.rateItems);
}

var showTips = <%= hasAccessToTips.ToString().ToLowerInvariant() %>;

$(function() { 

	$('body').on('click', '.content-editable', function(e) { 
		var self = this;
		if ($(self).data('attributeId') != null && $(self).closest('tr').data('id') != null) {
			$(self).prop('contentEditable', 'plaintext-only');
		}
	});

    $('body').on('blur', '.content-editable', function(e) { 
        var self = this;

        if ($(self).data('attributeId') != null && $(self).closest('tr').data('id') != null) {
            var attributeId = $(self).data('attributeId');
            var newValue = $(self).text();
            var callId = $(self).closest('tr').data('id');

            console.log("update attribute " + attributeId + " to " + newValue);
	        $(self).prop('contentEditable', 'false');
            $.ajax(
            {
                contentType: "application/json; charset=utf-8",
                type: 'PUT',
                url: '/api/calls/' + callId + '?reportEditor=1', 
                headers: { 'X-Twbk-From-Report-Editor': '1' },
                data: JSON.stringify({ "attributes":[
                    {"attributeId": attributeId,
                     "value": newValue,"id":attributeId}]})
            });
        }
    });


});

        function onPtoCheckboxClick(e) {
            var cl = $(e).closest('.row');
            var callId = $(cl).data('id');

            $.ajax({
                contentType: "application/json; charset=utf-8",
                type: 'PUT',
                url: '/api/calls/' + callId + '?dispatchAnalysisReportEditor=1',
                headers: { 'X-Twbk-Dispatch-Analysis-Report-Editor': '1' },
                data: JSON.stringify({
                    "attributes": [
                        {
                            "attributeId": 11246, "id": 11246,
                            "value": JSON.stringify({
                                src: { v: $(cl).find('.x-src-validated').is(":checked") ? 1 : 0  },
                                dest: { v: $(cl).find('.x-dest-validated').is(":checked") ? 1 : 0  },
                                onscene: { v: $(cl).find('.x-onscene-validated').is(":checked") ? 1 : 0  },
                                completed: { v: $(cl).find('.x-completed-validated').is(":checked") ? 1 : 0 }
                            })
                        }]
                })
            });
        }

        function onCancelledCheckboxClick(e) {
            var cl = $(e).closest('tr');
            var callId = $(cl).data('id');
            $.ajax({
                contentType: "application/json; charset=utf-8",
                type: 'PUT',
                url: '/api/calls/' + callId + '?cancelledCallReportEditor=1',
                headers: { 'X-Twbk-Cancelled-Call-Report-Editor': '1' },
                data: JSON.stringify({
                    "attributes": [
                        {
                            "attributeId": 12806, "id": 12806,
                            "value": JSON.stringify({
                                review: { v: $(cl).find('.x-cancelled-review').is(":checked") ? 1 : 0 },
                                closed: { v: $(cl).find('.x-cancelled-closed').is(":checked") ? 1 : 0 },
                                active: { v: $(cl).find('.x-cancelled-active').is(":checked") ? 1 : 0 },
                            })
                        }]
                })
            });
        }

        function onExpenseVerifiedCheckboxClick(e) {
            var truckId = $(e).closest('tbody').data('truckId');
            var expenseId = $(e).data('id');
            
            $.ajax({
                contentType: "application/json; charset=utf-8",
                type: 'POST',
                url: '/api/trucks/' + truckId + '/expenses/' + expenseId + '/verify',
                headers: { 'X-Twbk-Expense-Report-Editor': '1' },
                data: JSON.stringify({ 'verified': $(e).is(":checked") })
            });
        }

        function finishExpenseRequest(data) {
            console.log("finish expense: ", data);
            closeLightbox();
        }
    </script>
    <% int srcVersion = 118; %>
    <div style="display: none">
    <link rel="stylesheet" href="/ui/scss/towbook-overrides.css?v=<%=srcVersion.ToString() %>" media="screen" type="text/css" />
    <script type="text/javascript" src="https://maps.googleapis.com/maps/api/js?client=gme-extricllc&libraries=places"></script>
    <script src="/UI/js/Towbook/callEditorWindow.js?v=<%=srcVersion.ToString() %>"></script>
    <script src="../../UI/js/highcharts.js"></script>
    <link href="CallAnalysis.css?r=<%=srcVersion.ToString() %>" rel="stylesheet" />
    <script src="/ui/js/jquery.tmpl.min.js"></script>
    <script src="/ui/js/accounting.js"></script>
    <script src="CallAnalysis.js?v=<%=srcVersion.ToString() %>"></script>
    <script src="/UI/js/jspdf.min.js"></script>
    <script src="/UI/js/jspdf.plugin.autotable.js"></script>

    <% if (Master.Companies.Count() > 1) { %><style>#filterCompany { display: inline-block !important }</style>  <% } %>


        <% Response.WriteFile("CallAnalysis.tpl.html"); %>
        
        <% Response.WriteFile("AccountPayments.tpl.html"); %>
        <% Response.WriteFile("IncomeByUser.tpl.html"); %>

<% if (new int[] { 4185, 4189, 4194, 22083 }.Contains(Global.CurrentUser.CompanyId)) { %>
        <style>
            .hide-invoice-number .x-col-invoicenumber { display: none }
        </style>
        <% Response.WriteFile("MotorClub4185.tpl.html"); %>
        <% Response.WriteFile("IncomeSummary4185.tpl.html"); %>
        <% Response.WriteFile("DispatchAnalysis4185.tpl.html"); %>
        <% Response.WriteFile("CancelledCall4185.tpl.html"); %>
        <% Response.WriteFile("PaymentVerification.tpl.html"); %>
<% } else { %>
        <% Response.WriteFile("MotorClub.tpl.html"); %>
        <% Response.WriteFile("IncomeSummary.tpl.html"); %>
        <% Response.WriteFile("PaymentVerification.tpl.html"); %>
        <% Response.WriteFile("DispatchAnalysis.tpl.html"); %>
        <% Response.WriteFile("CancelledCall.tpl.html"); %>
<% } %>
        <% Response.WriteFile("MotorClubGeico.tpl.html"); %>
        <% Response.WriteFile("RevenueIncome.tpl.html"); %>
        <% Response.WriteFile("Utilization.tpl.html"); %>
        <% Response.WriteFile("UndeliveredEmails.tpl.html"); %>
        <% Response.WriteFile("AccountPerformance.tpl.html"); %>
        <% Response.WriteFile("Performance.tpl.html"); %>
        <% Response.WriteFile("TruckExpenses.tpl.html"); %>
        <% Response.WriteFile("RevenuePerformance.tpl.html"); %>
        <% Response.WriteFile("AccountReceivable.tpl.html"); %>
        <% Response.WriteFile("SalesTax.tpl.html"); %>
        
        <% Response.WriteFile("DeletedCalls.tpl.html"); %>
        <% Response.WriteFile("CommissionSummary.tpl.html"); %>
        <% Response.WriteFile("Safeclear.tpl.html"); %>
        <% Response.WriteFile("SubcontractorBasic.tpl.html"); %>
        <% Response.WriteFile("PreTripInspections.tpl.html"); %>
        <% Response.WriteFile("RoadsideSurveys.tpl.html"); %>
        <% Response.WriteFile("DriverSurveys.tpl.html"); %>
        <% Response.WriteFile("StickerReport.tpl.html"); %>
        <% Response.WriteFile("UserCheckInReport.tpl.html"); %>

        <% Response.WriteFile("CallWorkflow.tpl.html"); %>
        <% Response.WriteFile("DriverActivity.tpl.html"); %>
        <% Response.WriteFile("CompanyIncome.tpl.html"); %>

        <% Response.WriteFile("RotationActivity.tpl.html"); %>

        <% Response.WriteFile("AuctionPreview.tpl.html"); %>
        <% Response.WriteFile("DamageForms.tpl.html"); %>
        <% Response.WriteFile("AccidentReports.tpl.html"); %>
        </div>
    <div id="fullWidth">
        <div id="tabLoader" style="display:none"><p style="position:absolute; top:30%; left:45%; font-size:2em; color:white;">Loading Report</p></div>
        <input runat="server" id="reportType" type="hidden" value="" class="reportType" />

<div id="filterOptions" class="reportInnerDiv">
            <ul>
              <li id="filterDateRange">
                <label>Date Range</label>
                <div style="clear: both; top: -5px"> <input type="text" id="dpStartDate" class="datePicker" /> to <input type="text" id="dpEndDate" class="datePicker" /></div>
              </li>
            <li id="filterEndDate">
                <label>End Date:</label>
                <div style="clear: both; top: -5px"> <input type="text" id="dpEndDateOnly" class="datePicker" /></div>
            </li>
            <li id="filterDateTimeRange">
                <label>Date Range</label>
                <div style="clear: both; top: -5px; width: 150px; display: inline-block;">
                    <input type="text" id="dpStartDateTime" class="towbook-dt-date ui-selectmenu" />
                </div> to <!--
                --><div style="clear: both; top: -5px; width: 150px; display: inline-block;">
                    <input type="text" id="dpEndDateTime" class="towbook-dt-date ui-selectmenu" />
                </div>
              </li>
                
            <li id="filterDateRangeField">
                <label for="cbDateRangeField">Base Report On</label>
                <select name="cbDateRangeField" id="cbDateRangeField">
                    <option value="0">Completion Date</option>
                    <option value="1">Lock Date</option>
                    <option value="3">Modified Date</option>
                </select>
            </li>
            <li id="filterPaymentBy">
                <label for="cbPaymentBy">Base Report On</label>
                <select name="cbPaymentBy" id="cbPaymentBy">
                        <option value="">Payment Date</option>
                        <option value="1">Recorded Date</option>
                        <option value="2">Verified Date</option>
                </select>
            </li>

            <li id="filterCompany">
                <label>Company</label>
                <select name="cbCompany" id="cbCompany">
                    <option value="">All</option>
                </select>
            </li>

            <li id="filterReportBy">
                <label for="cbReportBy">Report by</label>
                <select name="cbReportBy" id="cbReportBy">
                </select>
            </li>



            <li id="filterReason">
                <label for="cbReason">Reason</label>
                <select name="cbReason" id="cbReason">
                    <option value="">All</option>
                </select>
            </li>
            
            <li id="filterMasterAccount">
                <label for="cbMasterAccount">Master Account</label>
                <select name="cbMasterAccount" id="cbMasterAccount">
                    <option value="">All</option>
                </select>
            </li>
            <li id="filterAccount">
                <label for="cbAccount">Account</label>
                <select name="cbAccount" id="cbAccount">
                    <option value="">All</option>
                </select>
            </li>
            <li id="filterSubAccount">
                <label for="cbSubAccount">Rotation Accounts</label>
                <select name="cbSubAccount" id="cbSubAccount">
                </select>
            </li>

            <%if (Global.CurrentUser.Company.HasFeature(Extric.Towbook.Generated.Features.Accounts_AccountManagers)) { %>
            <li id="filterAccountManager">
                <label>Manager</label>
                <select name="cbAccountManager" id="cbAccountManager">
                    <option value="">All</option>
                </select>
            </li>
            <% } %>

            <li id="filterAccountType">
                <label for="cbAccountType">Account Type</label>
                <select name="cbAccountType" id="cbAccountType">
                    <option value="">All</option>
                </select>
            </li>

            <li id="filterRecordedBy">
                <label for="cbRecordedBy">Recorded By</label>
                <select name="cbRecordedBy" id="cbRecordedBy">
                    <option value="">All</option>
                </select>
            </li>

            <li id="filterVerifiedBy">
                <label for="cbVerifiedBy">Verified By</label>
                <select name="cbVerifiedBy" id="cbVerifiedBy">
                    <option value="">All</option>
                </select>
            </li>

            <li id="filterVerifiedStatus">
                <label for="cbVerifiedStatus">Verified Status</label>
                <select name="cbVerifiedStatus" id="cbVerifiedStatus">
                        <option value="">All</option>
                        <option value="1">Yes</option>
                        <option value="0">No</option>
                </select>
            </li>

            <li id="filterDriver">
                <label for="cbDriver">Driver</label>
                <select name="cbDriver" id="cbDriver">
                    <option value="">All</option>
                </select>
            </li>
            <li id="filterTruck">
                <label for="cbTruck">Truck</label>
                <select name="cbTruck" id="cbTruck">
                    <option value="">All</option>
                </select>
            </li>
            
            <li id="filterWeightClass">
                <label for="cbWeightClass">Weight Class</label>
                <select name="cbWeightClass" id="cbWeightClass">
                    <option value="">All</option>
                </select>
            </li>

            <li id="filterDispatcher">
                <label for="cbDispatcher">Dispatcher</label>
                <select name="cbDispatcher" id="cbDispatcher">
                    <option value="">All</option>
                </select>
            </li>

            <li id="filterPerformer">
                <label for="cbPerformer">Performed by</label>
                <select name="cbPerformer" id="cbPerformer">
                    <option value="">All</option>
                </select>
            </li>

            <li id="filterUserType">
                <label for="cbUserType">User Type</label>
                <select name="cbUserType" id="cbUserType"></select>
            </li>

            <li id="filterPaymentStatus">
                <label for="cbPaymentStatus">Payment Status</label>
                <select name="cbPaymentStatus" id="cbPaymentStatus">
                    <option value="">All</option>
                    <option value="p">Paid</option>
                    <option value="u">Unpaid</option>
                    <option value="partially">Partially</option>
                </select>
            </li>
            <li id="filterPaymentType">
                <label for="cbPaymentType">Payment Type</label>
                <select name="cbPaymentType" id="cbPaymentType">
                    <option value="">All</option>
                </select>
            </li>

            <li id="filterTotalInvoiced">
            <label for="cbTotalInvoiced">Display By</label>
            <select name="cbTotalInvoiced" id="cbTotalInvoiced">
                <option value="0">Total Invoiced</option>
                <option value="1">Total Calls</option>
            </select>

            </li>    
            <li id="filterRateItem">
                <label for="cbRateItem">Service Item</label>
                <select name="cbRateItem" id="cbRateItem">
                    <option value="">n/a</option>
                </select>
            </li>
            <li id="filterAccountingMethod">
                <label for="cbAccountingMethod">Accounting Method</label>
                <select name="cbAccountingMethod" id="cbAccountingMethod">
                    <option value="0">Cash</option>
                    <option value="1">Accrual</option>
                </select>
            </li>
            <li id="filterImpounds">
                <label for="cbImpounds">Report Includes</label>
                <select name="cbImpounds" id="cbImpounds"></select>
            </li>
            <li id="filterPaymentOptions">
                <label for="cbPaymentOptions">Report Includes</label>
                <select name="cbPaymentOptions" id="cbPaymentOptions"></select>
            </li>
            <li id="filterInspectionResult">
                <label for="cbInspectionResult">Outcome</label>
                <select name="cbInspectionResult" id="cbInspectionResult" data-width="100" title="All"></select>
            </li>

<% if (new int[] { 4185, 4189, 4194, 22083 }.Contains(Global.CurrentUser.CompanyId)&& Global.CurrentUser.Type == Extric.Towbook.User.TypeEnum.Manager) { %>
            <li id="filterClasses">
                <label for="cbClasses">Class</label>
                <select name="cbClasses" id="cbClass">
                    <option value="">n/a</option>
                </select>
            </li>
            <li id="filterInvoiceStatus">
                <label for="cbInvoiceStatuses">Invoice Status</label>
                <select name="cbInvoiceStatuses" id="cbInvoiceStatus">
                    <option value="">n/a</option>
                </select>
            </li>
<% } %>

            <li id="filterTaxExempt">
                <label for="cbTaxExempt">Tax Exempt</label>
                <select name="cbTaxExempt" id="cbTaxExempt">
                    <option value="">All</option>
                    <option value="1">Tax Exempt Only</option>
                </select>
            </li>
            <li id="filterStickerStatus">
                <label for="cbStickerStatus">Status</label>
                <select name="cbStickerStatus" id="cbStickerStatus">
                    <option value="">All</option>
                </select>
            </li>
            <li id="filterStickerReason">
                <label for="cbStickerReason">Reason</label>
                <select name="cbStickerReason" id="cbStickerReason">
                    <option value="">All</option>
                </select>
            </li>

            <li id="filterDateColumn">
                <label for="cbDateColumn">Base Report on</label>
                <select name="cbDateColumn" id="cbDateColumn">
                </select>
            </li>

            <%if (Global.CurrentUser.Company.HasFeature(Extric.Towbook.Generated.Features.ClassTracking)) { %>
                <li id="filterPayouts">
                    <label for="cbPayouts">Payouts</label>
                    <select name="cbPayouts" id="cbPayouts">
                    </select>
                </li>
            <% } %>
                <li id="filterPushedToQuickBooks">
                    <label for="cbPushedToQuickBookss">Pushed To QuickBooks</label>
                    <select name="cbPushedToQuickBooks" id="cbPushedToQuickBooks">
                        <option value="">All</option>
                        <option value="1">Yes</option>
                        <option value="0">No</option>
                    </select>
                </li>
                
                <li id="filterIsAudited">
                    <label for="cbIsAudited">Audit Status</label>
                    <select name="cbIsAudited" id="cbIsAudited">
                        <option value="">All</option>
                        <option value="1">Yes</option>
                        <option value="0">No</option>
                    </select>
                </li>

                <li id="filterIsLocked">
                    <label for="cbIsLocked">Locked Status</label>
                    <select name="cbIsLocked" id="cbIsLocked">
                        <option value="">All</option>
                        <option value="1">Yes</option>
                        <option value="0">No</option>
                    </select>
                </li>


                <li id="filterIsBilled">
                    <label for="cbIsBilled">Billed Status</label>
                    <select name="cbIsBilled" id="cbIsBilled">
                        <option value="">All</option>
                        <option value="1">Yes</option>
                        <option value="0">No</option>
                    </select>
                </li>
            <li class="alwaysShow">
                <input type="button" id="btnRefresh" onclick="(function () { RefreshReport($('select#cbGroupOption').selectmenu('value'), null, new Date().getSeconds()); })();" class="standard-button" value="Update" style="margin-top: 20px; padding: 3px 3px 4px 3px !important; min-width: 95px !important" />
            </li>
            </ul>
        </div>

        <div id="x-print-pdf-container" style="display: none;"><a href ="#" onclick="ExportPdf(); return false;"><i class="fa fa-file-pdf-o"></i>Print PDF</a></div>

        <div id="groupedGraphicData" class="reportInnerDiv">
            <div id="businessMetrics" class="alignLeft reportBusinessMetricsDiv">
                    <h3>Report Summary</h3>
                    <table id="summary" style="width: 100%;">
                        <thead id="SummaryTableHeader" style="display:none">
                            <tr>
                                <td></td>
                                <td></td>
                                <td>This Period</td>
                                <td>Year-to-Date</td>
                            </tr>
                        </thead>
                        <tbody>
                        </tbody>
                    </table>
            </div>
            <div id="graphicRepresentation" class="alignRight reportGraphDiv">
                <div>
                    <div style="float: left;">
<div style="display:none">                        View Detail
                        <input id="txtViewDetail" type="text" disabled="disabled" value="" /></div>

                        <div id="groupOptionButtons_CallVolumeRevenue" style="float: left;">
                            <button type="button" id="btnGraphByCallVolume" class="CategoryForGraph" onclick="(function(sender) { $('.CategoryForGraph').removeClass('currentOption'); $('#btnGraphByCallVolume').addClass('currentOption'); RefreshReport($('select#cbGroupOption').selectmenu('value')); })()" value="CallVolume">Call Volume</button>
                            <button type="button" id="btnGraphByRevenue" class="CategoryForGraph" onclick="(function(sender) { $('.CategoryForGraph').removeClass('currentOption'); $('#btnGraphByRevenue').addClass('currentOption'); RefreshReport($('select#cbGroupOption').selectmenu('value')); })()" value="Revenue">Revenue</button>
                        </div>
                        <div id="groupOptionButtons_Income" style="float: left; display:none;">
                            <button type="button" id="btnGraphByPaymentTypeIncome" class="CategoryForGraph" onclick="(function(sender) { $('.CategoryForGraph').removeClass('currentOption'); $('#btnGraphByPaymentTypeIncome').addClass('currentOption'); RefreshReport($('select#cbGroupOption').selectmenu('value')); })()" value="PaymentType">Income by Payment Type</button>
                            <button type="button" id="btnGraphByRevenueIncome" class="CategoryForGraph" onclick="(function(sender) { $('.CategoryForGraph').removeClass('currentOption'); $('#btnGraphByRevenueIncome').addClass('currentOption'); RefreshReport($('select#cbGroupOption').selectmenu('value')); })()" value="Revenue">Income by Account</button>
                        </div>
                        <div id="groupOptionButtons_Company" style="float: left; display:none;">
                            <button type="button" id="btnGraphByCompanyIncome" class="CategoryForGraph" onclick="(function(sender) { $('.CategoryForGraph').removeClass('currentOption'); $('#btnGraphByRevenueIncome').addClass('currentOption'); RefreshReport($('select#cbGroupOption').selectmenu('value')); })()" value="Company">Income by Company</button>
                            <button type="button" id="btnGraphByPaymentTypeCompany" class="CategoryForGraph" onclick="(function(sender) { $('.CategoryForGraph').removeClass('currentOption'); $('#btnGraphByPaymentTypeIncome').addClass('currentOption'); RefreshReport($('select#cbGroupOption').selectmenu('value')); })()" value="PaymentType">Income by Payment Type</button>
                        </div>
                        <div id="groupOptionButtons_Account" style="float: left; display:none;">
                            <button type="button" id="btnGraphByPaymentTypeAccount" class="CategoryForGraph" onclick="(function(sender) { $('.CategoryForGraph').removeClass('currentOption'); $('#btnGraphByPaymentTypeAccount').addClass('currentOption'); RefreshReport($('select#cbGroupOption').selectmenu('value')); })()" value="PaymentType">Income by Payment Type</button>
                            <button type="button" id="btnGraphByAccount" class="CategoryForGraph" onclick="(function(sender) { $('.CategoryForGraph').removeClass('currentOption'); $('#btnGraphByAccount').addClass('currentOption'); RefreshReport($('select#cbGroupOption').selectmenu('value')); })()" value="Account">Income by Account</button>
                        </div>
                        <div id="groupOptionButtons_Utilization" style="float: left; display:none;">
                            <button type="button" id="btnGraphByTotalInvoiced" class="CategoryForGraph" onclick="(function(sender) { $('.CategoryForGraph').removeClass('currentOption'); $('#btnGraphByTotalInvoiced').addClass('currentOption'); RefreshReport($('select#cbGroupOption').selectmenu('value')); })()" value="TotalInvoiced">Total Invoiced</button>
                            <button type="button" id="btnGraphByTotalCalls" class="CategoryForGraph" onclick="(function(sender) { $('.CategoryForGraph').removeClass('currentOption'); $('#btnGraphByTotalCalls').addClass('currentOption'); RefreshReport($('select#cbGroupOption').selectmenu('value')); })()" value="TotalCalls">Total Calls</button>
                        </div>
                        <div id="groupOptionButtons_AccountPerformance" style="float: left; display:none;">
                            <button type="button" id="btnGraphByTopAccount" class="CategoryForGraph" onclick="(function(sender) { $('.CategoryForGraph').removeClass('currentOption'); $('#btnGraphByTopAccount').addClass('currentOption'); RefreshReport($('select#cbGroupOption').selectmenu('value')); })()" value="TopAccounts">Top 20 Accounts</button>
                            <button type="button" id="btnGraphByAccountType" class="CategoryForGraph" onclick="(function(sender) { $('.CategoryForGraph').removeClass('currentOption'); $('#btnGraphByAccountType').addClass('currentOption'); RefreshReport($('select#cbGroupOption').selectmenu('value')); })()" value="AccountType">Account Type</button>
                        </div>
                        <div id="groupOptionButtons_Performance" style="float: left; display:none;">
                            <button type="button" id="btnGraphByTopGroup" class="CategoryForGraph" onclick="(function(sender) { $('.CategoryForGraph').removeClass('currentOption'); $('#btnGraphByTopAccount').addClass('currentOption'); RefreshReport($('select#cbGroupOption').selectmenu('value')); })()" value="TopGroup">Top 20</button>
                            <button type="button" id="btnGraphByTotalGroupInvoiced" class="CategoryForGraph" onclick="(function(sender) { $('.CategoryForGraph').removeClass('currentOption'); $('#btnGraphByTotalInvoiced').addClass('currentOption'); RefreshReport($('select#cbGroupOption').selectmenu('value')); })()" value="TotalInvoiced">Total Invoiced</button>
                            <button type="button" id="btnGraphByTotalGroupCalls" class="CategoryForGraph" onclick="(function(sender) { $('.CategoryForGraph').removeClass('currentOption'); $('#btnGraphByTotalCalls').addClass('currentOption'); RefreshReport($('select#cbGroupOption').selectmenu('value')); })()" value="TotalCalls">Total Calls</button>
                        </div>
                        <div id="groupOptionButtons_TruckExpenses" style="float: left; display:none;">
                            <button type="button" id="btnGraphByExpenses" class="CategoryForGraph" onclick="(function(sender) { $('.CategoryForGraph').removeClass('currentOption'); $('#btnGraphByExpenses').addClass('currentOption'); RefreshReport($('select#cbGroupOption').selectmenu('value')); })()" value="Expenses">Expenses</button>
                            <button type="button" id="btnGraphByGrossProfit" class="CategoryForGraph" onclick="(function(sender) { $('.CategoryForGraph').removeClass('currentOption'); $('#btnGraphByGrossProfit').addClass('currentOption'); RefreshReport($('select#cbGroupOption').selectmenu('value')); })()" value="GrossProfit">Gross Profit</button>
                        </div>
                        <input id="hfCategory" type="hidden" value="" />
                    </div>                    
                    <div id="TimeFilterOptions" style="float: right;">
                        <div id="richFilterOptions" class="alignLeft">
                            <label for="cbGroupOption">Group</label>
                            <select name="cbGroupOption" id="cbGroupOption" class="cbGroupOption">
                                <option value="0">None</option>
                                <option value="1">Reason</option>
                                <option value="2">Account</option>
                                <option value="3">Driver</option>
                                <option value="4">Truck</option>
                                <option value="5">Dispatcher</option>
                            </select>
                        </div>
                        
                        <input id="hfRichOption" type="hidden" value="day" />

                        <button type="button" class="timeGroupButton" value="Hour">Hour</button>
                        <button type="button" class="timeGroupButton currentOption" value="Day">Day</button>
                        <button type="button" class="timeGroupButton" value="Week">Week</button>
                        <button type="button" class="timeGroupButton" value="Month">Month</button>
                        <button type="button" class="timeGroupButton" value="Year">Year</button>
                    </div>
                </div>
                <div id="graphContainer" style="min-width: 640px; margin: 0 auto"></div>
            </div>

        </div>
	<div style="clear: both"></div>
        
        <%if (!Global.CurrentUser.Company.HasFeature(Extric.Towbook.Generated.Features.PreTripInspections) && reportType.Value == "PreTripInspections" ) { %>
        <div style="padding: 20px;">
            <div id="sample" class="h3box" style="padding-bottom: 30px; min-height: 175px;">
                <div class="left">
                    <img src="data:image/svg+xml;charset=utf-8;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4NCjwhLS0gR2VuZXJhdG9yOiBBZG9iZSBJbGx1c3RyYXRvciAxNS4wLjIsIFNWRyBFeHBvcnQgUGx1Zy1JbiAuIFNWRyBWZXJzaW9uOiA2LjAwIEJ1aWxkIDApICAtLT4NCjwhRE9DVFlQRSBzdmcgUFVCTElDICItLy9XM0MvL0RURCBTVkcgMS4xLy9FTiIgImh0dHA6Ly93d3cudzMub3JnL0dyYXBoaWNzL1NWRy8xLjEvRFREL3N2ZzExLmR0ZCI+DQo8c3ZnIHZlcnNpb249IjEuMSIgaWQ9IkxheWVyXzEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiIHg9IjBweCIgeT0iMHB4Ig0KCSB3aWR0aD0iODAwcHgiIGhlaWdodD0iNjY3LjAxMnB4IiB2aWV3Qm94PSIwIDAgODAwIDY2Ny4wMTIiIGVuYWJsZS1iYWNrZ3JvdW5kPSJuZXcgMCAwIDgwMCA2NjcuMDEyIiB4bWw6c3BhY2U9InByZXNlcnZlIj4NCjxnPg0KCTxwYXRoIGZpbGw9IiNGNUY1RjUiIGQ9Ik03NjUuMjIyLDBIMzQuNzkxQzE1LjU3NywwLDAsMTUuNTc3LDAsMzQuNzkxdjQ5OC41NDZjMCwxOS4yMjcsMTUuNTc3LDM0Ljc5MSwzNC43OTEsMzQuNzkxaDczMC40MzINCgkJYzE5LjIxMywwLDM0Ljc3Ny0xNS41NjQsMzQuNzc3LTM0Ljc5MVYzNC43OTFDODAwLDE1LjU3Nyw3ODQuNDM1LDAsNzY1LjIyMiwweiBNNzU5LjIyNSw0ODMuODY3SDM3Ljc0NlYzNy42aDcyMS40NzlWNDgzLjg2N3oiLz4NCgk8cGF0aCBmaWxsPSIjRjVGNUY1IiBkPSJNNTE5LjU2OCw2NDkuNjE3aC0yMy40MjVsLTMwLjItNjAuOTI2SDMzMi42MTdsLTI4Ljc4NCw2MC45MjZoLTIzLjQwMWMtNC43NzMsMC04LjY5MSwzLjkwNi04LjY5MSw4LjY5MQ0KCQljMCw0Ljc5NywzLjkxOCw4LjcwMyw4LjY5MSw4LjcwM2gyMzkuMTM2YzQuNzg1LDAsOC42OTEtMy45MDYsOC42OTEtOC43MDNDNTI4LjI1OSw2NTMuNTIzLDUyNC4zNTMsNjQ5LjYxNyw1MTkuNTY4LDY0OS42MTd6Ii8+DQo8L2c+DQo8L3N2Zz4NCg==" class="mac_pc"/>
                    <div class="tb-logo"></div>
                </div>
                <div class="right">
                    <h1>Check out Equipment (pre-trip) Inspections!</h1>
                    <p>Equipment Inspection is a fast, simple way for drivers to complete pre-trip inspections before or after a shift. You can customize the questions to meet your needs, plus drivers can add photos and notes at each inspection point.</p>
                    <p>Let us know if you have questions or if you want this feature added to your Towbook plan.</p>
                </div>
            </div>
        </div>

        <% } %>

        <div id="callAnalysisTable" class="reportInnerDiv">
            <table cellspacing="0" cellpadding="0" width="100%" id="callAnalysis" class="sortable list">
                <thead></thead>
                <tbody></tbody>
            </table>
            <div id="multiNav" style="margin-bottom:20px">
            </div>
        </div>
    </div>

    <div id="html-2-pdfwrapper" style="position: absolute; left: -4000px; top: 50px; bottom: 0; overflow: auto; width: 600px;"></div>
</asp:Content>
