<script type="text/x-jQuery-tmpl" id="t-incomeSummaryRow">
    <tr data-id="${dispatchEntryId}">
        <td>${callNumber}</td>
        <td class='x-overages'>${driver}</td>
        <td>${_enc(accountName)}</td>
        <td>${_enc(reason)}</td>
        <td>${_enc(purchaseOrderNumber)}</td>
        <td>${dispatchNumber}</td>
        <td class='x-overages'>${_enc(membershipNumber)}</td>
        <td class='x-overages'>${coverageAmount}</td>
        <td sorttable_customkey="${parseFloat(paymentAmount)}">${towbook.formatMoney(paymentAmount)}</td>
        <td>${_enc(paymentStringLong)}</td>\
        <td>${_enc(paymentReference)}</td>
        <td>${recordedBy.replace("Automatic Payment Importer", "Imported")}</td>
        <td>${towbook.formatDate(paymentDate)}</td>
        <td>${towbook.formatDate(createDate)}</td>
    </tr>
</script>