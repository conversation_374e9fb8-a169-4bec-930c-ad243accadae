<script type="text/x-jQuery-tmpl" id="t-salesTaxRow">
    <tr data-id="${taxRateId}-${companyId}" class="x-clickableRow nowrap">
        <td>${company}</td>
        <td>${description}</td>
        <td>${parseFloat(accounting.toFixed(taxRate, 3))}%</td>

        {{if accountingMethod==0}}
        <td>${towbook.formatMoney(totalIncome)}</td>
        <td>${towbook.formatMoney(nonTaxCollected)}</td>
        <td>${towbook.formatMoney(taxableIncome)}</td>
        <td>${towbook.formatMoney(taxCollected)}</td>
        {{else}}
        <td>${towbook.formatMoney(revenueInvoiced)}</td>
        <td>${towbook.formatMoney(nonTaxInvoiced)}</td>
        <td>${towbook.formatMoney(taxableIncomeInvoiced)}</td>
        <td>${towbook.formatMoney(taxInvoiced)}</td>
        {{/if}}
    </tr>
    <tr data-id="${taxRateId}-${companyId}" class="showMoreInfo nowrap" style="display: none">
        <td class="zero-spacing" colspan="100" style="overflow: hidden;">
            <table style="margin-top: 0; margin-left: 20px; width: 100%">
                <thead>
                    <tr>
                        {{if accountingMethod==0}}
                        <td>Call #</td>
                        <td>Payment Date</td>
                        <td>Payment Type</td>
                        <td>Reference</td>
                        <td>Income Collected</td>
                        <td>Non Taxable Income</td>
                        <td>Taxable Collected</td>
                        <td>Tax Collected</td>
                        {{else}}
                        <td>Call #</td>
                        <td>Completion Date</td>
                        <td>Released Date</td>
                        <td>Revenue Invoiced</td>
                        <td>Non-Taxable Invoiced</td>
                        <td>Taxable Invoiced</td>
                        <td>Tax Invoiced</td>
                        {{/if}}
                    </tr>
                </thead>
                <tbody>
                    {{if accountingMethod==0}}
                        {{each paymentDetails}}
                        <tr>
                            <td>${callNumber}</td>
                            <td>{{if paymentDate}}${towbook.formatDate(paymentDate)} ${towbook.formatAMPM(paymentDate)}{{/if}}</td>
                            <td>{{if type}}${type}{{/if}}</td>
                            <td>{{if reference}}${reference}{{/if}}</td>
                            <td>{{if income}}${towbook.formatMoney(income)}{{/if}}</td>
                            <td>{{if nonTaxCollected}}${towbook.formatMoney(nonTaxCollected)}{{/if}}</td>
                            <td>{{if invoiceTaxableAmount}}${towbook.formatMoney(invoiceTaxableAmount)}{{/if}}</td>
                            <td>{{if taxCollected}}${towbook.formatMoney(taxCollected)}{{/if}}</td>
                        </tr>
                        {{/each}}
                    {{else}}
                        {{each invoiceDetails}}
                        <tr>
                            <td>${callNumber}</td>
                            <td>{{if invoiceDate}}${towbook.formatDate(invoiceDate)} ${towbook.formatAMPM(invoiceDate)}{{/if}}</td>
                            <td>{{if releasedDate}} ${towbook.formatDate(releasedDate)} ${towbook.formatAMPM(releasedDate)}{{/if}}</td>
                            <td>{{if revenueInvoiced}}${towbook.formatMoney(revenueInvoiced)}{{/if}}</td>
                            <td>{{if nonTaxInvoiced}}${towbook.formatMoney(nonTaxInvoiced)}{{/if}}</td>
                            <td>{{if taxableInvoiced}}${towbook.formatMoney(taxableInvoiced)}{{/if}}</td>
                            <td>{{if taxCollected}}${towbook.formatMoney(taxCollected)}{{/if}}</td>
                        </tr>
                        {{/each}}
                    {{/if}}
                </tbody>
            </table>
        </td>
    </tr>
</script>
