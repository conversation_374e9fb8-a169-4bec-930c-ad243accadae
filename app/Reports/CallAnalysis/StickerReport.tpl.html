<script type="text/x-jQuery-tmpl" id="t-stickerRow">
    <tr>
        <td class="no-wrap">
            <a href="/Stickering/Editor.aspx?id=${id}" target="_blank" title="Modify Sticker" onclick="editSticker('${id}'); return false;" title="Modify Sticker">${stickerNumber}</a>
        </td>
        <td>${moment(createDate).format('MM/DD/YY')}</td>
        <td>${account}</td>
        <td>${stickerSessionId}</td>
        <td>
            {{each reasons}}
            <div class="reason">${name}</div>
            {{/each}}
        </td>
        <td class="no-wrap">${statusName}</td>
        <td>${createdByUser}</td>
        <td>
            {{if towableDate}}
            <div style="display: inline-block">${moment(towableDate).format('MM/DD/YY')}</div>
            <div style="display: inline-block">${moment(towableDate).format('h:mm a')}</div>
            {{/if}}
        </td>
        <td class="no-wrap" sorttable_customkey="${callNumber}">
            <a href="/DispatchEditor/Editor.aspx?id=${dispatchEntryId}" target="_blank" title="Modify Call in new window">${callNumber}</a>
        </td>
        <td>
            {{if convertedCallDate}}
            <div style="display: inline-block">${moment(convertedCallDate).format('MM/DD/YY')}</div>
            <div style="display: inline-block">${moment(convertedCallDate).format('h:mm a')}</div>
            {{/if}}
        </td>
    </tr>
</script>