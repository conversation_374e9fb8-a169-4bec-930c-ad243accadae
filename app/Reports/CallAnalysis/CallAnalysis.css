/*fieldset {
    border: 0;
}*/
label {
    margin-right: 2px;
    display:block;
}

#addPayment label {
  display: inline-block;
}

.fixSize {
    width: 125px;
}

.currentOption {
    border: 2px solid mediumVioletRed;
}

.hiddenBlock {
    display: none;
}

#tabLoader {
    position: absolute; 
    z-index: 1000;
    width: 100%;
    height: 100%;
    /* Fallback for web browsers that doesn't support RGBa */
    background: rgb(0, 0, 0) transparent;
    /* RGBa with 0.5 opacity */
    background: rgba(0, 0, 0, 0.3);
    /* For IE 5.5 - 7*/
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr=#99000000, endColorstr=#99000000);
    /* For IE 8*/
    -ms-filter: "progid:DXImageTransform.Microsoft.gradient(startColorstr=#99000000, endColorstr=#99000000)";
    background-position: 50% 50%;
    background-repeat: no-repeat;
    background-image: url(/ui/images/ajax-loading2.gif);
}

.reportBusinessMetricsDiv {
    width: 340px;
    margin-bottom: 20px;
    float: left;
}

#pageHolder-title h2 span#subTitle {
    font-size: 0.8em;
}

.summaryRow td:first-child {
    padding-left: 10px;
}

.summaryRow td:last-child {
    padding-right: 10px;
    text-align: right;
}

.summaryRow:first-child td {
    padding-top: 10px;
}

.summaryRow:last-child td {
    padding-bottom: 10px;
}


#businessMetrics h3 {
    background-color: none;
    background-image: none;
    margin: 0;
}

#businessMetrics #summary {
    border: solid 1px #efefef;
    margin-bottom: 5px;
}

#graphicRepresentation {
    margin-left: 360px;
    float: none;
}



.alignLeft {
    float: left;
}

.alignRight, .cbReason, .cbAccount, .cbDriver, .cbTruck, .cbDispatcher, .cbGroupOption {
    float: left;
}


.ui-selectmenu {
    /*height: 1.5em;*/
    vertical-align: middle;
}

.ui-combobox {
    position: relative;
    display: inline-block;
}

.ui-combobox-toggle {
    position: absolute!important;
    top: 0;
    bottom: 0;
    margin-right: 1px!important;
    padding: 0;
    /* adjust styles for IE 6/7 */
    *height: 1.7em;
    *top: 0.1em;
}

.ui-combobox-input:not(.ui-selectmenu-status) {
  margin: 0;
  padding: 0.3em;
}


#filterOptions {
    background-color: #efefef;
    color: #777;
    padding: 7px;
    margin-bottom: 10px;
    border-bottom: solid 1px #dfdfdf;
    border-left: solid 1px #dfdfdf;
    border-right: solid 1px #dfdfdf;
}

    #filterOptions #btnRefresh {
      height: 27px;
    }

    #filterOptions ul {
        list-style: none;
    }

    #filterOptions > ul > li {
        display: none;
        vertical-align: top;
    }

    #filterOptions ul li label {
    }

    #filterOptions ul li input:not(.ui-selectmenu-status) {
      margin-top: 3px;
      border-radius: 2px;
      border: 1px solid #cfcfcf;
      text-align: center;
      padding: 3px;
    }

    #filterOptions ul li span.ui-multiselect-span {
      margin-top: 3px;
      border-radius: 0;
      border: solid 1px #afafaf;
      border-left: solid 3px #afafaf;
      margin-right: 10px;
      margin-bottom: 7px;
      height: 1.9em;
      line-height: 14px;
    }

    #filterOptions ul li input.ui-selectmenu-status {
      border-radius: 0;
      border-width: 0px !important;
      width: 100% !important;
      height: 100%;
    }

    #filterOptions ul li input.ui-selectmenu-status:hover {
      text-decoration: underline;
    }

    #filterOptions ul li.alwaysShow {
        display: inline-block;
    }


/*  Report Styles
    .report_incomeByDriver #filterReason,
*/


/***
 *** REPORT: Income 
 ***/
.report_incomeSummary #filterDateTimeRange,
.report_incomeSummary #filterAccount,
.report_incomeSummary #filterTruck,
.report_incomeSummary #filterDriver,
.report_incomeSummary #filterDispatcher,
.report_incomeSummary #filterReason,
.report_incomeSummary #filterPaymentType,
.report_incomeSummary #filterPaymentOptions,
.report_incomeSummary #filterClasses {
    display: inline-block;
}

/***
 *** REPORT: Payment Verification
 ***/
.report_paymentVerification #filterDateTimeRange,
.report_paymentVerification #filterAccount,
.report_paymentVerification #filterAccountManager,
.report_paymentVerification #filterRecordedBy,
.report_paymentVerification #filterVerifiedBy,
.report_paymentVerification #filterVerifiedStatus,
.report_paymentVerification #filterPaymentType,
.report_paymentVerification #filterPaymentBy,
.report_paymentVerification #filterPaymentOptions,
.report_paymentVerification #filterClasses,
.report_paymentVerification #filterPushedToQuickBooks {
    display: inline-block;
}
.report_paymentVerification #graphicRepresentation {
  display: none
}

/***
 *** REPORT: Company Income 
 ***/
.report_companyIncome #filterDateTimeRange,
.report_companyIncome #filterTruck,
.report_companyIncome #filterDriver,
.report_companyIncome #filterDispatcher,
.report_companyIncome #filterPaymentOptions,
.report_companyIncome #filterPaymentType,
.report_companyIncome #filterClasses {
    display: inline-block;
}

.report_companyIncome #filterCompany {
  display: none !important;
}

/***
 *** REPORT: Revenue Performance 
 ***/
.report_revenuePerformance #filterDateTimeRange,
.report_revenuePerformance #filterAccount {
  display: inline-block;
}

/***
 *** REPORT: Accounts Receivable 
 ***/
.report_accountReceivable #filterAccountType,
.report_accountReceivable #filterAccount,
.report_accountReceivable #filterAccountManager,
.report_accountReceivable #filterEndDate,
.report_accountReceivable #filterImpounds {
    display: inline-block;
}

/***
 *** REPORT: Sales Tax 
 ***/
.report_salesTax #filterDateRange,
.report_salesTax #filterCompany,
.report_salesTax #filterAccountingMethod {
    display: inline-block;
}

/***
 *** REPORT: Call Analysis 
 ***/
.report_callAnalysis #filterDateRange,
.report_callAnalysis #filterAccount,
.report_callAnalysis #filterAccountType,
.report_callAnalysis #filterTruck,
.report_callAnalysis #filterDriver,
.report_callAnalysis #filterDispatcher,
.report_callAnalysis #filterReason,
.report_callAnalysis #filterRateItem,
.report_callAnalysis #filterAccount, 
.report_callAnalysis #groupOptionButtons_CallVolumeRevenue, 
.report_callAnalysis #filterPaymentStatus,
.report_callAnalysis #filterClasses {
    display: inline-block;
}


/***
 *** REPORT: Call Workflow
 ***/
.report_callWorkflow #filterDateRange,
.report_callWorkflow #filterDateRangeField,
.report_callWorkflow #filterAccount,
.report_callWorkflow #filterAccountType,
.report_callWorkflow #filterTruck,
.report_callWorkflow #filterDriver,
.report_callWorkflow #filterDispatcher,
.report_callWorkflow #filterReason,
.report_callWorkflow #filterImpounds,
.report_callWorkflow #groupOptionButtons_CallVolumeRevenue,
.report_callWorkflow #filterPaymentStatus,
.report_callWorkflow #filterClasses,
.report_callWorkflow #filterIsAudited,
.report_callWorkflow #filterIsLocked,
.report_callWorkflow #filterIsBilled,
.report_callWorkflow #filterPushedToQuickBooks {
    display: inline-block;
}

.report_callWorkflow #graphicRepresentation,
.report_subcontractorBasic #graphicRepresentation
{
    display: none
}


    #hsummary li {
        margin-right: 5px;
        display: inline-block;
        min-width: 180px;
        padding: 10px;
        border: solid 1px #cfcfcf;
        background-color: white;
    }

    #hsummary li span {
        font-weight: bold;
        float:right; 
        padding-left: 20px;

    }

    .x-display-menu,
    .x-dropdown-temp, .x-display-menu:hover,
    .x-dropdown-temp:hover {
        background-color: #f7f7f7 !important
    }


td.x-row-selector {
    -moz-user-select: none;
    -webkit-user-select: none;
    user-select: none;
}

#callAnalysisTable .x-numbers { text-align: right }

#callAnalysisTable tbody > tr > td:first-child a {
  color: #2B75BE;
  padding: 5px;
}

#callAnalysisTable tbody > tr > td:first-child a:hover {
    color: #f83;
    text-decoration: none;
}
#callAnalysisTable tbody > tr > td:first-child a > i {
    margin-right: 5px
}

#callAnalysisTable .showMoreInfo table tbody tr td{
  height: 39px;
}

/***
 *** REPORT: Dispatch Analysis 
 ***/
    .report_dispatchingAnalysis #filterDateRange,
    .report_dispatchingAnalysis #filterMasterAccount,
    .report_dispatchingAnalysis #filterAccount,
    .report_dispatchingAnalysis #filterAccountType,
    .report_dispatchingAnalysis #filterReason,
    .report_dispatchingAnalysis #filterTruck,
    .report_dispatchingAnalysis #filterDriver,
    .report_dispatchingAnalysis #filterDispatcher,
    .report_dispatchingAnalysis #filterAccount,
    .report_dispatchingAnalysis #filterPaymentStatus,
    .report_dispatchingAnalysis #filterTaxExempt,
    .report_dispatchingAnalysis #filterClasses {
        display: inline-block;
    }

/***
 *** REPORT: Service Motor Club 
 ***/
/*.report_serviceMotorClub#filterTruck,*/
.report_serviceMotorClub #filterDispatcher,
.report_serviceMotorClub #filterDateRange,
.report_serviceMotorClub #filterReason,
.report_serviceMotorClub #filterAccount,
.report_serviceMotorClub #filterDriver,
.report_serviceMotorClub #filterTruck,
.report_serviceMotorClub #filterAccount,
.report_serviceMotorClub #filterPaymentStatus,
.report_serviceMotorClub #filterInvoiceStatus,
.report_serviceMotorClub #filterClasses {
    display: inline-block;
}

/***
 *** REPORT: Service Motor Club GEICO Billing
 ***/
.report_serviceMotorClubGeico #filterDateRange,
.report_serviceMotorClubGeico #filterReason,
.report_serviceMotorClubGeico #filterAccount,
.report_serviceMotorClubGeico #filterDriver,
.report_serviceMotorClubGeico #filterTruck,
.report_serviceMotorClubGeico #filterAccount,
.report_serviceMotorClubGeico #filterPaymentStatus,
.report_serviceMotorClubGeico #filterInvoiceStatus,
.report_serviceMotorClubGeico #filterClasses {
    display: inline-block;
}

.colRateItem { display: none }


.show_colRateItem .colRateItem { 
    display: table-cell
}

/***
 *** REPORT: Revenue Income 
 ***/
.report_revenueIncome #filterDateRange,
.report_revenueIncome #filterAccount,
.report_revenueIncome #filterTruck,
.report_revenueIncome #filterDriver,
.report_revenueIncome #filterDispatcher,
.report_revenueIncome #filterAccount,
.report_revenueIncome #filterPaymentStatus,
.report_revenueIncome #filterImpounds,
.report_revenueIncome #groupOptionButtons_Income {
    display: inline-block;
}


/***
 *** REPORT: Account Payments 
 ***/
.report_accountPayments #filterDateRange,
.report_accountPayments #filterPaymentType,
.report_accountPayments #filterAccount .report_accountPayments #groupOptionButtons_Account {
    display: inline-block;
}

/***
 *** REPORT: User Income 
 ***/
.report_incomeByUser #filterDateTimeRange,
.report_incomeByUser #filterDispatcher,
.report_incomeByUser #filterUserType,
.report_incomeByUser #filterImpounds,
.report_incomeByUser #filterDateColumn {
    display: inline-block;
}
.report_incomeByUser tr.nowrap td {
    white-space: nowrap
}

/***
 *** REPORT: Utilization
 ***/
.report_utilization #filterDateTimeRange,
.report_utilization #filterReportBy {
    display: inline-block;
}

/***
 *** REPORT: Undelivered emails
 ***/
.report_undeliveredEmails #filterDateDateRange
{
    display: inline-block;
}

/***
 *** REPORT: Account Performance 
 ***/
.report_accountPerformance #filterDateTimeRange,
.report_accountPerformance #filterTotalInvoiced,
.report_accountPerformance #filterImpounds,
.report_accountPerformance #filterTruck,
.report_accountPerformance #filterDriver,
.report_accountPerformance #filterPayouts,
.report_accountPerformance #filterPaymentStatus {
    display: inline-block;
}


/***
 *** REPORT: Performance 
 ***/
.report_performance #filterDateTimeRange,
.report_performance #filterReportBy,
.report_performance #filterTotalInvoiced,
.report_performance #filterImpounds,
.report_performance #filterAccount,
.report_performance #filterAccountManager,
.report_performance #filterReason,
.report_performance #filterTruck,
.report_performance #filterDriver,
.report_performance #filterDispatcher,
.report_performance #filterWeightClass,
.report_performance #filterPaymentStatus {
    display: inline-block;
}

/***
 *** REPORT: Driver Activity 
 ***/
.report_driverActivity #filterDateTimeRange,
.report_driverActivity #filterCompany,
.report_driverActivity #filterDriver {
  display: inline-block;
}

/***
 *** REPORT: Truck Expenses
 ***/
.report_truckExpenses #filterDateRange,
.report_truckExpenses #filterTruck {
    display: inline-block;
}

/***
 *** REPORT: Cancelled Calls
 ***/
.report_cancelledCall #filterDateRange,
.report_cancelledCall #filterAccount,
.report_cancelledCall #filterMasterAccount,
.report_cancelledCall #filterAccountType,
.report_cancelledCall #filterTruck,
.report_cancelledCall #filterDriver,
.report_cancelledCall #filterPerformer,
.report_cancelledCall #filterReason {
    display: inline-block;
}

/***
 *** REPORT: Deleted Calls
 ***/
/*.report_deletedCalls #filterDateRange,
.report_deletedCalls #filterAccount,
.report_deletedCalls #filterAccountType,
.report_deletedCalls #filterTruck,
.report_deletedCalls #filterDriver,
.report_deletedCalls #filterAccount {
  display: inline-block;
}*/


/***
 *** REPORT: Safeclear
 ***/
.report_safeclear #filterDateRange,
.report_safeclear #filterDriver,
.report_safeclear #filterAccount,
.report_safeclear #filterCompany{
    display: inline-block;
}



/***
 *** REPORT: Subcontractor Basic
 ***/
.report_subcontractorBasic #filterDateRange,
.report_subcontractorBasic #filterAccount,
.report_subcontractorBasic #filterCompany {
    display: inline-block;
}


/***
 *** REPORT: AuctionPreview
 ***/
.report_auctionPreview #filterDateRange,
.report_auctionPreview #filterCompany {
    display: inline-block;
}
.report_auctionPreview #graphicRepresentation {
  display: none
}

/***
 *** REPORT: DamageForms
 ***/
.report_damageForms #filterDateRange,
.report_damageForms #filterCompany,
.report_damageForms #filterAccount,
.report_damageForms #filterPerformer {
    display: inline-block;
}

.report_damageForms #graphicRepresentation {
    display: none
}

/***
 *** REPORT: AccidentReports
 ***/
.report_accidentReports #filterDateRange,
.report_accidentReports #filterCompany,
.report_accidentReports #filterAccount,
.report_accidentReports #filterPerformer {
    display: inline-block;
}

.report_accidentReports #graphicRepresentation {
    display: none
}

/***
 *** REPORT: PreTripInspections
 ***/
.report_preTripInspections #filterDateRange,
.report_preTripInspections #filterDispatcher,
.report_preTripInspections #filterTruck,
.report_preTripInspections #filterCompany,
.report_preTripInspections #filterInspectionResult {
    display: inline-block;
}
.report_preTripInspections #groupedGraphicData{
    display: none;
}
.report_preTripInspections #callAnalysisTable tr {
    cursor: pointer;
}
.report_preTripInspections #callAnalysisTable td {
    padding: 0 !important;
}
.report_preTripInspections .link {
    display: inline-block !important;
    padding: 10px;
    width: 100%;
    font-size: 14px !important;
}
.report_preTripInspections .has-errors {
    color: #e00 !important;
}


/***
 *** REPORT: RoadsideSurveys
 ***/
.report_roadsideSurveys #filterDateRange,
.report_roadsideSurveys #filterAccount,
.report_roadsideSurveys #filterDriver,
.report_roadsideSurveys #filterTruck {
  display: inline-block;
}

.report_roadsideSurveys .rating-stars .answer {
  display: none;
}

.report_roadsideSurveys .rating-stars .feedback {
  margin-top: 3px;
}

.report_roadsideSurveys .rating-stars .fa-star,
.report_driverSurveys .rating-stars .fa-star {
  color: #fa0;
}

.report_roadsideSurveys .rating-stars .fa-star-o,
.report_driverSurveys .rating-stars .fa-star-o {
  color: #fa0;
  opacity: .6;
}

.report_roadsideSurveys .no-wrap {
  white-space: nowrap;
}

.report_roadsideSurveys td {
  cursor: default;
}

.report_roadsideSurveys .feedback {
  margin-top: 3px;
  font-style: italic;
}


/***
 *** REPORT: DriverSurveys
 ***/
.report_driverSurveys #filterDateRange,
.report_driverSurveys #filterAccount,
.report_driverSurveys #filterDriver
 {
  display: inline-block;
}

.report_driverSurveys #businessMetrics {
  display: none;
}

.report_driverSurveys #groupedGraphicData {
  display: block;
  position: relative;
  overflow: auto;
  max-width: 2000px;
  margin-bottom: 10px;
}

.report_driverSurveys #hsummary {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
}

.report_driverSurveys #hsummary li {
  min-width: 250px;
}

.report_driverSurveys #hsummary li:last-of-type {
  margin-right: 20px;
}

.report_driverSurveys #hsummary .title {
  color: #2B75BE;
  font-size: 1.2rem;
  font-family: Calibri, Arial;
  padding-bottom: 5px;
}

.report_driverSurveys .rating-stars .num-reviews {
  display: inline-block;
  font-size: .8rem;
  line-height: 1rem;
  color: rgba(0,0,0,.6);
}

.report_driverSurveys .rating-stars table tbody,
.report_driverSurveys .rating-stars table tr,
.report_driverSurveys .rating-stars table tr td:last-child
{
  border: 0;
}

.report_driverSurveys .rating-stars table tr td {
  padding: 0px;
}

.report_driverSurveys .rating-stars .rating-num {
  padding-right: 10px;
}

.report_driverSurveys .rating-wrapper {
  display: inline-block;
  line-height: 1rem;
}

.report_driverSurveys .rating-wrapper .star-ratings-css {
  unicode-bidi: bidi-override;
  color: #c5c5c5;
  font-size: 1.3rem;
  margin: 0 auto;
  position: relative;
  padding: 0;
  text-shadow: 0px 1px 0 #a2a2a2;
}

.report_driverSurveys .rating-wrapper .star-ratings-css .star-ratings-css-top {
  color: #fa0;
  padding: 0;
  position: absolute;
  z-index: 1;
  display: block;
  overflow: hidden;
}

.report_driverSurveys .rating-wrapper .star-ratings-css .star-ratings-css-top {
  padding: 0;
  display: block;
  z-index: 0;
}

.report_driverSurveys .rating-wrapper .star-ratings-css {
  top: 2px;
  left: 0px;
}

.report_driverSurveys .rating-wrapper .star-ratings-css.rating-1 .star-ratings-css-top {
  color: #ff0000;
}

.report_driverSurveys .rating-wrapper .star-ratings-css.rating-2 .star-ratings-css-top {
  color: #ff0000;
}

.report_driverSurveys .rating-wrapper .star-ratings-css.rating-3 .star-ratings-css-top {
  color: #ff8800;
}

.report_driverSurveys .rating-wrapper .star-ratings-css.rating-4 .star-ratings-css-top {
  color: #ffbb00;
}

.report_driverSurveys .rating-wrapper .star-ratings-css.rating-5 .star-ratings-css-top {
  color: #ffbb00;
}




/***
 *** REPORT: StickerReport
 ***/
.report_stickerReport #filterDateRange,
.report_stickerReport #filterAccount,
.report_stickerReport #filterStickerReason,
.report_stickerReport #filterStickerStatus {
  display: inline-block;
}
.report_stickerReport td a {
  font-size: 13px !important;
}



/***
 *** REPORT: UserCheckInReport
 ***/
.report_userCheckInReport #filterDateRange,
.report_userCheckInReport #filterDispatcher {
  display: inline-block;
}

.report_userCheckInReport td a {
  font-size: 13px !important;
}

/***
 *** REPORT: RotationActivity
 ***/
.report_rotationActivity #filterDateTimeRange,
.report_rotationActivity #filterSubAccount,
.report_rotationActivity #filterWeightClass {
    display: inline-block;
}

.report_rotationActivity #businessMetrics {
    display: none;
}


.report_rotationActivity #groupedGraphicData {
    display: block;
    position: relative;
    overflow: auto;
    max-width: 2000px;
    margin-bottom: 10px;
}

.report_rotationActivity #hsummary {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
}

.report_rotationActivity #hsummary li {
    min-width: 250px;
}

.report_rotationActivity #hsummary li:last-of-type {
    margin-right: 20px;
}

.report_rotationActivity #hsummary .title {
    color: #2B75BE;
    font-size: 1.2rem;
    font-family: Calibri, Arial;
    padding-bottom: 5px;
}

#groupedGraphicData {
    padding: 10px;
}
#groupedGraphicData button {
    border: none;
    background-color: white;
    padding: 5px;
    margin: 0;
    border-bottom: solid 2px #afafaf;
    border-left: solid 1px #afafaf;
    border-right: solid 1px #afafaf;
    cursor: pointer;
    border-top: solid 1px #afafaf;
}

    #groupedGraphicData button.currentOption {
        background-color: #333;
        color: white;
        border-bottom: solid 2px #333;
        cursor: default;
    }

#richFilterOptions {
    width: 180px;
}

    #richFilterOptions label {
        padding: 7px;
        float: left;
    }

    #richFilterOptions .ui-selectmenu {
        height: 25px;
        margin-top: 0px;
    }

.HeaderCell {
    text-align: left;
}

#filterDateRange,
#filterEndDate,
#filterDateTimeRange {
  margin-right: 10px;
}

#filterDateTimeRange .towbook-dt {
  line-height: 33px;
  border: none;
  background-color: inherit;

}

#filterDateTimeRange .towbook-dt input {
  padding: 4px;
  border-radius: 0px;
  background-color: #ffffff
}

#filterDateTimeRange .towbook-dt-date {
  border: solid 1px #afafaf;
  border-left: solid 3px #afafaf;
  border-right: 0px;
}

#filterDateTimeRange .towbook-dt-time {
  border: solid 1px #afafaf;
}

tbody.row:nth-child(odd) * { background-color: #fafafa }

tbody.row:hover * { background-color: #efefef !important }

#contents {
    padding: 0;
}

#callAnalysisTable {
    margin-left: 10px;
    margin-right: 10px;
}

#callAnalysisTable tbody tr td a {
    text-decoration: none;
    display: inline;
    color: #2B75BE
}
#callAnalysisTable tbody tr td a.moreInfo { font-size: 12px; cursor: pointer; }

#callAnalysis .x-clickableRow {
    cursor:pointer;
}

#callAnalysisTable thead tr th div.sort {
    display:inline-block;
    margin-left: 5px;
    height: 7px;
}

#callAnalysisTable .arrow-down {
    width: 0; 
	height: 0; 
	border-left: 5px solid transparent;
	border-right: 5px solid transparent;
	border-top: 7px solid black;
}

#callAnalysisTable .arrow-up {
    width: 0; 
	height: 0; 
	border-left: 5px solid transparent;
	border-right: 5px solid transparent;
	border-bottom: 7px solid black;
}

#callAnalysisTable tbody > tr > td.truckExpenseActions a,
#callAnalysisTable tbody > tr > td.truckExpenseActions div
{
    /* Using font from react to make buttons look consistent */
    font-family: Intervar, sans serif;
    align-self: center;
}


#salesReportMoreInfo { padding: 20px; }
#salesReportMoreInfo td { padding: 10px; }


/* Lock calls / unlock calls action button*/

.x-towbook-lock,
.x-towbook-audit {
    cursor: pointer;
    color: lightgray;
}

.x-towbook-lock.isLocked .fas,
.x-towbook-audit.isAudited .fas {
    color: #999999;
}

td:hover > .x-towbook-lock:hover .fas,
td:hover > .x-towbook-audit:hover .fas {
    color: blue;
}

td:hover > .x-towbook-lock.isLocked:hover .fas,
td:hover > .x-towbook-audit.isAudited:hover .fas {
    color: blue;
}

td > .x-towbook-lock .fas:not(:first-of-type),
td > .x-towbook-audit .fas:not(:first-of-type) {
    margin-left: 7px;
}

.x-towbook-locked,
.x-towbook-audited {
    visibility: visible !important
}

.x-towbook-lock.disabled {
    visibility: hidden;
}

.x-towbook-closed {
    visibility: hidden;
    color: #999999;
}

.x-towbook-closed.isClosed {
    visibility: visible;
}

.x-email-history {
    display: none;
}
.x-email-history.hasHistory {
    display: inline-block;
}

.x-verified-by-user .fa-check {
    display: none;
}

.x-verified-by-user.isVerified .fa-check {
    display: inline-block !important;
    padding-right: 3px;
    color: green;
}

.report_callWorkflow .x-verified-by-user.isVerified,
.report_callWorkflow .x-verified-by-user.isVerified .fa-check {
    color: #00cc00;
}

#prepare-email-invoices { padding: 20px;}
#prepare-email-invoices label { display: inline-block; min-width: 120px; line-height: 36px; }
#prepare-email-invoices .info-container { padding: 20px 0px; }
#prepare-email-invoices .message-container { min-height: 200px; position: relative; overflow: hidden; }
#prepare-email-invoices .message-container .fa-spinner { top: 10px; position: relative; margin-right: 10px; }
#prepare-email-invoices input[type="text"] { width: 300px; }
#prepare-email-invoices i.fa { padding-right: 5px; }

#x-print-pdf-container {
    margin-bottom: 10px;
    padding-right: 20px;
    float: right;
}

#x-print-pdf-container a {
    color: #0072c6;
    line-height: 20px;
    text-decoration: none;
}

#x-print-pdf-container a:hover {
    text-decoration: underline;
    color: #2B75BE;
}

#x-print-pdf-container .fa {
    font-size: 20px;
    padding-right: 5px;
    
}