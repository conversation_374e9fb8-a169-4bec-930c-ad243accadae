using System;
using System.Linq;
public partial class Reports_Overview : System.Web.UI.Page
{
    protected bool ShowPayroll { get; set; }
    protected bool ShowTrucks { get; set; }

    protected void Page_Load(object sender, EventArgs e)
    {
        this.Master.CurrentSection = Navigation.NavigationItemEnum.Reports;
        this.Master.InnerTitle = "Reports";
        this.Master.UseJquery = true;
        this.Master.UseReact = true;

        if (new int[] { 4189, 4185, 4194 }.Contains(Global.CurrentUser.CompanyId))
            if (Global.CurrentUser.FullName.ToLowerInvariant().Contains("divjot"))
                ShowPayroll = true;

        if (Global.CurrentUser.Notes != null && Global.CurrentUser.Notes.Contains("ShowTruckReports"))
            ShowTrucks = true;

        if (Global.CurrentUser.Notes != null && Global.CurrentUser.Notes.Contains("EnableReportPayrollCommissionOnly"))
            ShowPayroll = true;

        if (Global.CurrentUser.Id == 32759)
            Response.Redirect("/");
    }
}
