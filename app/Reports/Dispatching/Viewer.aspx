<%@ Page Language="C#" MasterPageFile="~/UI/Towbook.master" AutoEventWireup="true" CodeFile="Viewer.aspx.cs" Inherits="Reports_Dispatching_Viewer" Title="Towbook - Report Viewer" %>

<%@ Register TagPrefix="eo" NameSpace="EO.Web" Assembly="EO.Web" %>
<%@ MasterType TypeName="UI_Towbook" %>
<asp:Content ID="Content1" ContentPlaceHolderID="content" Runat="Server">
    <script type="text/javascript" src="../../UI/JS/Dispatch.js"></script>
    <style type="text/css">
td span { font-weight: bold; padding: 3px }
   </style>
    <div style="margin-bottom: 10px">Specify the date range that you would like to display a list of calls from.</div>
<div style="float: left">
    Start Date/Time: <eo:DatePicker id="dpStart" runat="server" style="padding: 1px" DayHeaderFormat="FirstLetter" TitleLeftArrowImageUrl="DefaultSubMenuIconRTL" MinValidDate="1/1/2009" MaxValidDate="1/1/2038"
		DayCellHeight="16" OtherMonthDayVisible="True" DayCellWidth="19" TitleRightArrowImageUrl="DefaultSubMenuIcon"
		VisibleDate="2009-04-17" ControlSkinID="None" PickerFormat="MM/dd/yyyy hh:mm tt">
		<DayHoverStyle CssText="border-right: #fbe694 1px solid; border-top: #fbe694 1px solid; border-left: #fbe694 1px solid; border-bottom: #fbe694 1px solid"></DayHoverStyle>
		<TitleStyle CssText="background-color:#9ebef5;font-family:Tahoma;font-size:12px;padding-bottom:2px;padding-left:6px;padding-right:6px;padding-top:2px;"></TitleStyle>
		<DayHeaderStyle CssText="border-bottom: #aca899 1px solid"></DayHeaderStyle>
		<DayStyle CssText="border-right: solid 1px #ffffff; border-top: #ffffff 1px solid; border-left: #ffffff 1px solid; border-bottom: #ffffff 1px solid"></DayStyle>
		<SelectedDayStyle CssText="background-color: #fbe694; border-right: #ffffff 1px solid; border-top: #ffffff 1px solid; border-left: #ffffff 1px solid; border-bottom: #ffffff 1px solid"></SelectedDayStyle>
		<TitleArrowStyle CssText="cursor:hand"></TitleArrowStyle>
		<TodayStyle CssText="border-right: #bb5503 1px solid; border-top: #bb5503 1px solid; border-left: #bb5503 1px solid; border-bottom: #bb5503 1px solid"></TodayStyle>
		<PickerStyle CssText="font-family:Courier New; padding-left:5px; padding-right: 5px;"></PickerStyle>
		<OtherMonthDayStyle CssText="color: gray; border-right: #ffffff 1px solid; border-top: #ffffff 1px solid; border-left: #ffffff 1px solid; border-bottom: #ffffff 1px solid"></OtherMonthDayStyle>
		<CalendarStyle CssText="background-color: #ffffff; border-right: #7f9db9 1px solid; padding-right: 4px; border-top: #7f9db9 1px solid; padding-left: 4px; font-size: 9px; padding-bottom: 4px; border-left: #7f9db9 1px solid; padding-top: 4px; border-bottom: #7f9db9 1px solid; font-family: tahoma"></CalendarStyle>
		<DisabledDayStyle CssText="color: gray; border-right: #ffffff 1px solid; border-top: #ffffff 1px solid; border-left: #ffffff 1px solid; border-bottom: #ffffff 1px solid"></DisabledDayStyle>
		<MonthStyle CssText="font-size: 12px; margin-left: 14px; cursor: hand; margin-right: 14px; font-family: tahoma"></MonthStyle>
	</eo:DatePicker>
	</div>
	<div style="margin-left: 20px; float: left">
	End Date/Time: <eo:DatePicker id="dpEnd" runat="server" style="padding: 1px" DayHeaderFormat="FirstLetter" TitleLeftArrowImageUrl="DefaultSubMenuIconRTL" MinValidDate="1/1/2009" MaxValidDate="1/1/2038"
		DayCellHeight="16" OtherMonthDayVisible="True" DayCellWidth="19" TitleRightArrowImageUrl="DefaultSubMenuIcon"
		VisibleDate="2009-04-17" ControlSkinID="None" PickerFormat="MM/dd/yyyy hh:mm tt">
		<DayHoverStyle CssText="border-right: #fbe694 1px solid; border-top: #fbe694 1px solid; border-left: #fbe694 1px solid; border-bottom: #fbe694 1px solid"></DayHoverStyle>
		<TitleStyle CssText="background-color:#9ebef5;font-family:Tahoma;font-size:12px;padding-bottom:2px;padding-left:6px;padding-right:6px;padding-top:2px;"></TitleStyle>
		<DayHeaderStyle CssText="border-bottom: #aca899 1px solid"></DayHeaderStyle>
		<DayStyle CssText="border-right: solid 1px #ffffff; border-top: #ffffff 1px solid; border-left: #ffffff 1px solid; border-bottom: #ffffff 1px solid"></DayStyle>
		<SelectedDayStyle CssText="background-color: #fbe694; border-right: #ffffff 1px solid; border-top: #ffffff 1px solid; border-left: #ffffff 1px solid; border-bottom: #ffffff 1px solid"></SelectedDayStyle>
		<TitleArrowStyle CssText="cursor:hand"></TitleArrowStyle>
		<TodayStyle CssText="border-right: #bb5503 1px solid; border-top: #bb5503 1px solid; border-left: #bb5503 1px solid; border-bottom: #bb5503 1px solid"></TodayStyle>
		<PickerStyle CssText="font-family:Courier New; padding-left:5px; padding-right: 5px;"></PickerStyle>
		<OtherMonthDayStyle CssText="color: gray; border-right: #ffffff 1px solid; border-top: #ffffff 1px solid; border-left: #ffffff 1px solid; border-bottom: #ffffff 1px solid"></OtherMonthDayStyle>
		<CalendarStyle CssText="background-color: #ffffff; border-right: #7f9db9 1px solid; padding-right: 4px; border-top: #7f9db9 1px solid; padding-left: 4px; font-size: 9px; padding-bottom: 4px; border-left: #7f9db9 1px solid; padding-top: 4px; border-bottom: #7f9db9 1px solid; font-family: tahoma"></CalendarStyle>
		<DisabledDayStyle CssText="color: gray; border-right: #ffffff 1px solid; border-top: #ffffff 1px solid; border-left: #ffffff 1px solid; border-bottom: #ffffff 1px solid"></DisabledDayStyle>
		<MonthStyle CssText="font-size: 12px; margin-left: 14px; cursor: hand; margin-right: 14px; font-family: tahoma"></MonthStyle>
	</eo:DatePicker>
	</div>
    <div style="float: left; padding-left: 10px">Driver: <br /><asp:DropDownList runat="server" ID="drivers" /></div>
    <div style="float: left; padding-left: 10px">Truck: <br /><asp:DropDownList runat="server" ID="trucks" /></div>
	<div style="margin-left: 20px; padding-top: 10px; float: left"><asp:Button ID="Button1" runat="server" class="sm" Text="Update" /></div>


    <div style="clear: both; border-bottom: solid 2px #afafaf; padding-top: 10px; margin-bottom: 10px"></div>


<asp:Label runat="server" ID="content"></asp:Label>


<asp:Panel runat="server" ID="repDailyOverview" Visible="false">
    <asp:Repeater ID="rpDispatchEntries" runat="server">
        <HeaderTemplate>
            <table cellspacing="0" cellpadding="0" width="100%" style="margin-top: 20px" id="list"
            onmouseover="trackTableHighlight(event, 'MouseHoverOn');" 
            onmouseout="trackTableHighlight(event, 'MouseHoverOff');">
                <tr>
                    <td class="HeaderCell" style="width: 1%">Call#</td>
                    <td class="HeaderCell" style="width: 5%">Received/Requested</td>
                    <td class="HeaderCell" style="width: 3%">Dispatched</td>
                    <td class="HeaderCell" style="width: 3%">Arrived</td>
                    <td class="HeaderCell" style="width: 3%">
<% if (Global.CurrentUser.Company.State == "NV") { Response.Write("Departed"); } else { Response.Write("Towing"); } %></td>
                    <td class="HeaderCell" style="width: 3%">Completed</td>
                    <td class="HeaderCell" style="width: 5%">Truck/Driver</td>
                    <td class="HeaderCell" style="width: 5%">Reason/Status</td>
                    <td class="HeaderCell" style="width: 5%">Dispatcher/Account</td>
                    <td class="HeaderCell" style="width: 1%">
<% if (Global.CurrentUser.Company.Id == 468) { %>
Tow Total
<% } else { %>
Grand Total<% } %></td>
                </tr>
        </HeaderTemplate>
        <ItemTemplate>
        <tr id="dh<%# DataBinder.Eval(Container.DataItem, "Id") %>_1">
          <td class="CellLeft" rowspan="3">#<%# DataBinder.Eval(Container.DataItem, "CallNumber") %></td>
          <td class="Cell"><%# Global.OffsetDateTime(Convert.ToDateTime(DataBinder.Eval(Container.DataItem, "CreateDate"))).ToString("MM/d h:mmtt") %></td>
          <td class="Cell"><%# FormatDate(Global.OffsetDateTime(Convert.ToDateTime(DataBinder.Eval(Container.DataItem, "DispatchTime")))) %></td>
          <td class="Cell"><%# FormatDate(Global.OffsetDateTime(Convert.ToDateTime(DataBinder.Eval(Container.DataItem, "ArrivalTime")))) %></td>
          <td class="Cell"><%# FormatDate(Global.OffsetDateTime(Convert.ToDateTime(DataBinder.Eval(Container.DataItem, "TowTime")))) %></td>
          <td class="Cell"><%# (Global.CurrentUser.Company.State.ToUpper() == "NV" ? FormatCompletion(Container.DataItem) :
	FormatDate(Global.OffsetDateTime(Convert.ToDateTime(DataBinder.Eval(Container.DataItem, "CompletionTime")))))
 %>



</td>
          <td class="Cell"><%#: DataBinder.Eval(Container.DataItem, "Driver") %> (<%#: DataBinder.Eval(Container.DataItem, "Truck") %>)</td>
          <td class="Cell"><%#: DataBinder.Eval(Container.DataItem, "Reason") %></td>
          <td class="Cell" colspan="2" style="color: gray"><%# DataBinder.Eval(Container.DataItem, "OwnerUser.FullName") %></td>
        </tr>
        <tr id="dh<%# DataBinder.Eval(Container.DataItem, "Id") %>_2">
          <td class="Cell" colspan="2"><%#: FormatLocation(DataBinder.Eval(Container.DataItem, "TowSource"), "(not entered)") %><br /></td>
          <td class="Cell" colspan="3"><%#: FormatDestination(Container.DataItem) %></td>
          <td class="Cell" colspan="1"><%# Convert.ToInt32(DataBinder.Eval(Container.DataItem, "Year"))  > 0 ? DataBinder.Eval(Container.DataItem, "Year")  : "" %>  <%# DataBinder.Eval(Container.DataItem, "MakeModelFormatted")%> <%# DataBinder.Eval(Container.DataItem, "Color")%> <%# ( DataBinder.Eval(Container.DataItem, "VIN").ToString().Length > 0 ? "VIN:" +  DataBinder.Eval(Container.DataItem, "VIN") : "")%> <%#: (DataBinder.Eval(Container.DataItem, "LicenseNumber").ToString().Length >0 ? "<br />License Plate:  " + DataBinder.Eval(Container.DataItem, "LicenseNumber") + " " + DataBinder.Eval(Container.DataItem, "LicenseState") : "")%></td>
          <td class="Cell" style="color: gray"><%# DataBinder.Eval(Container.DataItem, "Status") %></td>
          <td class="Cell" colspan="1"><%#: DataBinder.Eval(Container.DataItem, "Account")%></td>
          <td class="Cell"><%# WriteTotal(Container.DataItem) %></td>
        </tr>
	<tr>
		<td colspan="9" class="Cell">
			<%# WriteFullDetails(Container.DataItem) %>
</td>
		
	</tr>
	
        </ItemTemplate>
        <FooterTemplate></table></FooterTemplate>
    </asp:Repeater>
    
</asp:Panel>

<asp:Panel runat="server" ID="repIndividualList" Visible="false">
    Please choose which dispatcher you'd like to view a report for.<br /><br />
    
    <asp:Repeater runat="server" ID="rpDispatchers">
        <ItemTemplate>
          - <a href="Viewer.aspx?report=individualdetails&id=<%# DataBinder.Eval(Container.DataItem, "Id") %>"><%# DataBinder.Eval(Container.DataItem, "FullName") %></a><br />
        </ItemTemplate>
    </asp:Repeater>
</asp:Panel>

<asp:Panel runat="server" ID="repIndividualView" Visible="false">
    <asp:Repeater ID="rpIndividualDispatchEntries" runat="server">
        <HeaderTemplate>
            <table cellspacing="0" cellpadding="0" width="100%" style="margin-top: 20px" id="list"
            onmouseover="trackTableHighlight(event, 'MouseHoverOn');" 
            onmouseout="trackTableHighlight(event, 'MouseHoverOff');">
                <tr>
                    <td class="HeaderCell" style="width: 1%">Call#</td>
                    <td class="HeaderCell" style="width: 5%">Received</td>
                    <td class="HeaderCell" style="width: 3%">Dispatched</td>
                    <td class="HeaderCell" style="width: 3%">Arrived</td>
                    <td class="HeaderCell" style="width: 3%">Towing</td>
                    <td class="HeaderCell" style="width: 5%">Truck/Driver</td>
                    <td class="HeaderCell" style="width: 5%">Reason</td>
                    <td class="HeaderCell" style="width: 5%">Dispatcher/Status</td>
                </tr>
        </HeaderTemplate>
        <ItemTemplate>
        <tr id="dh<%# DataBinder.Eval(Container.DataItem, "Id") %>_1">
          <td class="CellLeft" rowspan="2">#<%# DataBinder.Eval(Container.DataItem, "Id") %></td>
          <td class="Cell"><%# Global.OffsetDateTime(Convert.ToDateTime(DataBinder.Eval(Container.DataItem, "CreateDate"))).ToString("MM/d h:mtt")%></td>
          <td class="Cell"><%# FormatDate(Global.OffsetDateTime(Convert.ToDateTime(DataBinder.Eval(Container.DataItem, "DispatchTime"))))%></td>
          <td class="Cell"><%# FormatDate(Global.OffsetDateTime(Convert.ToDateTime(DataBinder.Eval(Container.DataItem, "ArrivalTime"))))%></td>
          <td class="Cell"><%# FormatDate(Global.OffsetDateTime(Convert.ToDateTime(DataBinder.Eval(Container.DataItem, "TowTime")))) %></td>
          <td class="Cell"><%# DataBinder.Eval(Container.DataItem, "Driver") %> (<%# DataBinder.Eval(Container.DataItem, "Truck") %>)</td>
          <td class="Cell"><%# DataBinder.Eval(Container.DataItem, "Reason") %></td>
          <td class="CellAction" style="color: gray"><%# DataBinder.Eval(Container.DataItem, "OwnerUser.FullName") %></td>
        </tr>
        <tr id="dh<%# DataBinder.Eval(Container.DataItem, "Id") %>_2">
          <td class="Cell" colspan="3"><%# FormatLocation(DataBinder.Eval(Container.DataItem, "TowSource"), "(not entered)") %><br /></td>
          <td class="Cell" colspan="2"><%# FormatLocation(DataBinder.Eval(Container.DataItem, "TowDestination"), "(not entered)") %></td>
          <td class="Cell" colspan="1"><%# ((Extric.Towbook.Dispatch.Entry)Container.DataItem).Year > 0 ? ((Extric.Towbook.Dispatch.Entry)Container.DataItem).Year.ToString() + " " : "" %>
          <%# DataBinder.Eval(Container.DataItem, "MakeModelFormatted") %> (<%# DataBinder.Eval(Container.DataItem, "Color.Name") %>)</td>
          <td class="CellAction" style="color: gray"><%# DataBinder.Eval(Container.DataItem, "Status") %></td>
        </tr>

        </ItemTemplate>
        <FooterTemplate></table></FooterTemplate>
    </asp:Repeater>
    
    
    
</asp:Panel>
<asp:Panel runat="server" ID="repMotorClubs" Visible="true">
    <asp:Repeater ID="rpMotorClubView" runat="server">
        <HeaderTemplate>
            <table cellspacing="0" cellpadding="0" width="100%" style="margin-top: 20px" id="list"
            onmouseover="trackTableHighlight(event, 'MouseHoverOn');" 
            onmouseout="trackTableHighlight(event, 'MouseHoverOff');">
                <tr>
                    <asp:Literal runat="server" ID="rpMotorClubHeader" />    
                </tr>
                
        </HeaderTemplate>
        <ItemTemplate>
        <tr id="dh<%# DataBinder.Eval(Container.DataItem, "Id") %>">
          <asp:Literal runat="server" ID="rpMcItem" />    
        </tr>
        </ItemTemplate>
        <FooterTemplate></table></FooterTemplate>
    </asp:Repeater>
    
    
    
</asp:Panel>



</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="Toolbar" Runat="Server">
     <li><a href="javascript:" onclick="window.print();">- Print this report</a></li> 
     <li><a href="Viewer.aspx?report=<%=_type.ToLowerInvariant()%>&startDate=<% =dpStart.SelectedDate.ToShortDateString() %>&endDate=<% =dpEnd.SelectedDate.ToShortDateString() %>&csv=1">Export to CSV/Excel</a></li> 
</asp:Content>

