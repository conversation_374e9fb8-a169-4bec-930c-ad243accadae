using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Data;
using System.Data.SqlClient;
using Extric.Towbook.Accounts;
using Extric.Towbook;


public partial class Reports_Dispatching_OverallIncome : System.Web.UI.Page
{
	protected DateTime _startDate;
	protected DateTime _endDate;

	protected void Page_Load(object sender, EventArgs e)
	{

		this.Master.CurrentSection = Navigation.NavigationItemEnum.Reports;
		this.Master.InnerTitle = "Reports - Dispatching - Income Report";

		#region Process Query String

		if (Request.QueryString["startDate"] != null)
		{
			_startDate = Global.OffsetDateTime(Convert.ToDateTime(Request.QueryString["startDate"]), true);
		}
		else
		{
			_startDate = Global.OffsetDateTime(DateTime.Now.Date, true);
		}

		if (Request.QueryString["startDate"] != null)
		{
			_endDate = Global.OffsetDateTime(Convert.ToDateTime(Request.QueryString["endDate"]), true);
		}
		else
		{
			_endDate = Global.OffsetDateTime(DateTime.Now.Date.AddDays(1), true);
		}
		if (IsPostBack)
		{
			if (dpStart.SelectedDate > DateTime.MinValue)
				_startDate = Global.OffsetDateTime(dpStart.SelectedDate, true);

			if (dpEnd.SelectedDate > DateTime.MinValue)
				_endDate = Global.OffsetDateTime(dpEnd.SelectedDate, true);
		}

		#endregion

		dpStart.SelectedDate = Global.OffsetDateTime(_startDate);
		dpEnd.SelectedDate = Global.OffsetDateTime(_endDate);

        if (Request.QueryString["generateCsv"] == "1")
        {
            Response.ClearContent();
            Response.ContentType = "text/plain";
            Response.AppendHeader("Content-Disposition", string.Format("attachment;filename=income{0}-{1}.csv", _startDate.ToShortDateString().Replace("/", ""),
                _endDate.ToShortDateString().Replace("/", ""))); 
      
            Response.Write(Extric.Towbook.Dispatch.PaymentReport.GenerateCsv(Extric.Towbook.Dispatch.InvoicePayment.GetReport(Global.CurrentUser.Company, _startDate, _endDate)));
            Response.End();
            return;
        }

Dictionary<string, decimal> values = new Dictionary<string, decimal>();

using (var reader = SqlHelper.ExecuteReader(Core.ConnectionString,
                	"ReportsGetIncomingMoneyTotals", Global.CurrentUser.Company.Id, _startDate,
			 _endDate))
{
while (reader.Read())
{
	values.Add(GetPaymentString(reader.GetValue<int>("Type")), reader.GetValue<decimal>("Total"));
}
reader.NextResult();

		rpCallDetails.DataSource = reader;
		rpCallDetails.DataBind();



}


		rpReport.DataSource = values;
		rpReport.DataBind();



	}
protected string GetPaymentString(int v)
{

string type = "";
switch(v) {
case 0: type = "Cash"; break;
case 1: type = "Check"; break;
case 2: type = "Visa"; break;
case 3: type = "MasterCard"; break;
case 4: type = "Discover"; break;
case 5: type = "American Express";break;
case 6: type = "Other";break;
case 7: type = "Debit Card";break;
case 8: type = "Account Payment";break;
case 9: type = "Bill to Account";break;
}

return type;



}
	protected void rpReport_ItemDataBound(object sender, RepeaterItemEventArgs e)
	{

		
	}
}
