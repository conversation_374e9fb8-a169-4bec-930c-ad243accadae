<%@ WebHandler Language="C#" Class="RateItemReport" %>

using System;
using System.Web;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Drawing;
using Extric.Towbook;

public class RateItemReport : IHttpHandler {
    
    public void ProcessRequest (HttpContext context) {
        context.Response.ContentType = "text/plain";
        context.Response.Cache.SetNoStore();

        DateTime start = Global.CurrentUser.Company.CreateDate;
        DateTime end = DateTime.Now;

        if (context.Request.QueryString["sd"] != null)
            start = Convert.ToDateTime(context.Request.QueryString["sd"]);

        if (context.Request.QueryString["ed"] != null)
            end = Convert.ToDateTime(context.Request.QueryString["ed"]);

        context.Response.Write(GenerateChart(Global.CurrentUser.CompanyId, start, end));    
    }

    public string GenerateChart(int companyId, DateTime startDate, DateTime endDate)
    {
        int maxValue = 0;
        
        SortedDictionary<DateTime, Dictionary<string, object>> masterData = new SortedDictionary<DateTime, Dictionary<string, object>>();
        List<string> types = new List<string>();
        Dictionary<string, List<object>> data = new Dictionary<string, List<object>>();

        if (startDate.Day == 1)
        {
            for (DateTime x = startDate; x < endDate; x = x.AddDays(7))
            {
                masterData.Add(x, new Dictionary<string, object>());
            }
        }

        using (SqlDataReader dr = SqlHelper.ExecuteReader(Extric.Towbook.Core.ConnectionString,
            "InvoiceItemsReportItems", companyId, startDate, endDate, true))
        {
            while (dr.Read())
            {
                data.Add(dr["Name"].ToString(), new List<object>());
                types.Add(dr["Name"].ToString());
            }
        }

        using (SqlDataReader dr = SqlHelper.ExecuteReader(Extric.Towbook.Core.ConnectionString,
            "InvoiceItemsReport", companyId, startDate, endDate, true,  DBNull.Value, 2))
        {

            while (dr.Read())
            {
                DateTime date = Convert.ToDateTime(dr["FullDate"]);

                if (!masterData.ContainsKey(date))
                    masterData.Add(date, new Dictionary<string, object>());

                string name = dr["Name"].ToString();
                float value = 0;
                
                if (dr["Total"] != DBNull.Value)
                    value = Convert.ToSingle(dr["Total"]);
                
                if (!masterData[date].ContainsKey(name))
                    masterData[date].Add(name, value);

                if (maxValue < value)
                    maxValue = Convert.ToInt32(value);
            }
        }

        if (maxValue == 0)
            maxValue = 1;

        if (maxValue > 1)
        {
            maxValue = maxValue + ((int)(maxValue * 0.2));
        }

        string endResult = string.Empty;
        string caption = "Showing rate item activity between: " + startDate.ToShortDateString() + " to " + endDate.ToShortDateString();

        endResult = String.Format("<graph caption='{2}' numberPrefix='{0}' decimalPrecision='0' showvalues='0'  rotateNames='1' yAxisMinValue='0' yAxisMaxValue='{1}'>\n",
            "$", maxValue, caption);
        if (maxValue > 1)
        {
            endResult += "<categories>\n";

            foreach (DateTime x in masterData.Keys)
                endResult += "<category name='" + x.ToString("MMMM d yy") + "' />\n";
            endResult += "</categories>\n";
            Random rnd = new Random();

            foreach (string type in types)
            {
                string xml = "";
                string color = ColorTranslator.ToHtml(Color.FromArgb(rnd.Next(0, 255), rnd.Next(0, 255), rnd.Next(0, 255)));

                xml += String.Format("<dataset seriesName='{0}' color='{1}'>\n", type, color);

                foreach (DateTime day in masterData.Keys)
                {
                    if (masterData[day].ContainsKey(type))
                        xml += "<set value='" + masterData[day][type] + "' />";
                    else
                        xml += "<set value='0' />";
                }

                xml += "</dataset>";

                endResult += xml;
            }
        }
        endResult += "</graph>";
        endResult = endResult.Replace("\n", "");

        return "";// InfoSoftGlobal.FusionCharts.RenderChart("/FusionCharts/FCF_MSLine.swf", "", endResult, "FactorySum", "700", "450", false, false);
    }
 
    public bool IsReusable {
        get {
            return false;
        }
    }

}