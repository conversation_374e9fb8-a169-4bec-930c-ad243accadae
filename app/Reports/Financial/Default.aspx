<%@ Page Language="C#" MasterPageFile="~/UI/TowbookV2.master" AutoEventWireup="true" Inherits="Reports_Financial_Default" Title="Reporting - Financial" CodeFile="Default.aspx.cs" %>
<%@ MasterType TypeName="UI_Towbook" %>
<asp:Content ID="Content1" ContentPlaceHolderID="content" Runat="Server">
<ul id="reportList">
    
  <% if (new int[] { 10000, 119800, 119801, 119802, 119803, 119804, 124804, 126235, 132786 }.Contains(Extric.Towbook.WebShared.WebGlobal.CurrentUser.PrimaryCompanyId)) { %>
  <li><a href="../CallAnalysis/?reportType=SubcontractorBasic">Subcontractor Accounting Report</a>
    <span>View invoices for the date range that you specify for calls sent to subcontractors.</span>
  </li>
  <% } %>

  <li><a href="../CallAnalysis/?reportType=IncomeSummary">Income Summary Report</a>
    <span>List payments for the date range that you specify with advanced filtering options.</span>
  </li>
  
  <% if (Extric.Towbook.WebShared.WebGlobal.Companies.Length > 1) { %>
  <li><a href="../CallAnalysis/?reportType=CompanyIncome">Income by Company Report</a>
    <span>View payments for the date range that you specify with across all your divisions.</span>
  </li>
  <% } %>

    

  <li><a href="../CallAnalysis/?reportType=PaymentVerification">Payment Verification</a>
    <span>Manage payments.</span>
  </li>


  <li><a href="../CallAnalysis/?reportType=AccountReceivable">A/R Aging</a>
    <span>List accounts with open balances, broken down by 30, 60 and 90 day groups.</span>
  </li>

  <li><a href="../CallAnalysis/?reportType=RevenueIncome">Revenue & Income Analysis</a>
    <span>Provides an easy-to-use report that displays invoices and payments for a selected date range.</span>
  </li>

  <li><a href="../CallAnalysis/?reportType=IncomeByUser">Income by Employee</a>
    <span>Track how much money your employees has collected with the ability to filter by date and other criteria. Useful for counting cash at the end of a driver or dispatchers shift.</span>
  </li>
  <li><a href="../CallAnalysis/?reportType=AccountPayments">Payments received from Accounts</a>
    <span>Provides a report to review payments received from accounts and filter/graph them.</span>
  </li>  
  <li><a href="../CallAnalysis/?reportType=SalesTax" old="../Accounting/?salestax=1">Sales Tax</a>
    <span>Displays a breakdown of Sales Tax information for total revenue, tax collected, and more.</span>
  </li>
  <li>
    <% if (!Global.CurrentUser.Company.HasFeature(Extric.Towbook.Generated.Features.AdvancedBilling)) { %>
      <a href="../CallAnalysis/?reportType=UndeliveredEmails" style="cursor: text; color: #444" onclick="return false;">Undeliverable Emails</a>
      <span style="display: inline-block; font-style: italic; color: #888"><i class="fas fa-exclamation" style="padding: 0px 5px 0px 10px; "></i>Report is not available with your current plan.</span>
    <% } else {  %>
      <a href="../CallAnalysis/?reportType=UndeliveredEmails">Undeliverable Emails</a>
    <% } %>
      <div>List any receipt or statement billing emails that have been returned as undeliverable.</div>
  </li>
  <% if (Global.CurrentUser.Company.HasFeature(Extric.Towbook.Generated.Features.AdvancedBilling_ClosedAccountingPeriod)) { %>
    <li>
      <a href="../ClosedPeriod" old="../Accounting/?closedaccountingperiod=1">Closed Accounting Period</a>
        <span>See all modifications made to calls closed by your closed accounting period settings.</span>
    </li>
  <% } %>
</ul>

  
</asp:Content>

<asp:Content ID="Content2" ContentPlaceHolderID="Toolbar" Runat="Server">
<ul>
  <li><a href="../">Return to Reports List</a></li>
</ul>
</asp:Content>

