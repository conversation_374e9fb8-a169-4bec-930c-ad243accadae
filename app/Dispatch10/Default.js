function CallsTabs(options) {
  this.options = $.extend({
    tabControlSelector: null, //Example: "#tabcontrol1"
    autoRefreshInterval: 1000,
    autoRefresh: false,
    deletedColor: "#CE2929",    // may become a css class
    updatedColor: "#cfcfcf",    // may become a css class
    insertedColor: "#efefef",     // may become a css class   
    animationDuration: 500,
    // tabGroupStatuses: Hashtable. key = tabLink id, value = corresponding status codes (0,1,2,255, etc) for that group
    tabGroupStatuses: null,
    accountingProviderEnabled: false
  }, options);

  var Me = this;  // an alias for "this", useful for closures

  // Local public fields    
  $.extend(this, {
    autoRefreshTimeoutId: null,
    xhr: null,
    CallsRepository: {
      version: "0",
      calls: []
    },
    ScheduledRepository: {
      version: "0",
      calls: []
    },
    Groups: [],
    $tabControl: null // Will get value on init()

  });

  /*</Member fields definition>*/

  (function init() {
    Me.$tabControl = $(Me.options.tabControlSelector);
    Me.$tabControl.data("CallsTabsInstance", Me);

    console.log('init called.  Value of $tabControl:', Me.$tabControl);
  })();

  /*<Private Methods>*/

  // Gets the number of calls per tab. Return example: {"atCurrent": 14, "atWaiting": 8, ...}
  function getCountsPerGroup(countsPerStatus) {
    //countsPerStatus = {"0": 120, "1": 45}        //Sample parameter
    var countsPerGroup = {};
    for (var tabLinkId in Me.options.tabGroupStatuses) {
      countsPerGroup[tabLinkId] = 0;
      for (var i = 0; i < Me.options.tabGroupStatuses[tabLinkId].length; i++) {
        var aStatus = Me.options.tabGroupStatuses[tabLinkId][i];
        if (typeof (countsPerStatus[aStatus]) == 'undefined') continue;
        countsPerGroup[tabLinkId] += countsPerStatus[aStatus];
      }
    }

    return countsPerGroup;
  };

  function visuallyShowCallsUpdates(updatedCalls) {
    $.each(updatedCalls, function (i, call) {
      var $newCallTr = $(CallsTabs.getDispatchEntryHtml(call));
      var $callTr = $(".entriesTable", Me.$tabControl).find("tr[data-id='" + call.id + "']");

      $callTr.replaceWith($newCallTr);
      $callTr = $newCallTr;

      $callTr.tbkBehav();
      $callTr.find("td.Cell").colorFlash({
        color: Me.options.updatedColor,
        duration: Me.options.animationDuration
      });
    });
  }

  function visuallyShowCallsInserts(insertedCalls) {
    $.each(insertedCalls, function (i, call) {
      var h = CallsTabs.getDispatchEntryHtml(call);
      $('.entriesTable', Me.$tabControl).append(h);
      var $callTr = $(".entriesTable", Me.$tabControl).find("tr[data-id='" + call.id + "']");

      $callTr.tbkBehav();
      $callTr.find("td.Cell").colorFlash({
        color: Me.options.insertedColor,
        duration: Me.options.animationDuration
      });
    });
  }

  function visuallyShowCallsDeletes(deletedCalls) {
    $.each(deletedCalls, function (i, call) {
      var $callTr = $(".entriesTable", Me.$tabControl).find("tr[data-id='" + call.id + "']");
      $calltr.slideUp(300, function () { this.remove(); });
    });
  }

  //EXPD: Depends on Me.$tabControl, CallsTabs.getDispatchEntryHtml()
  function visuallyShowCallsChanges(deletedCalls, updatedCalls, insertedCalls) {
    visuallyShowCallsUpdates(updatedCalls);
    visuallyShowCallsInserts(insertedCalls);
    visuallyShowCallsDeletes(deletedCalls);
  }

  //Changes the active tab, to the selected $tabLink    
  //EXPD: Depends on Me.xhr, Me.$tabControl, Me.programOneRefresh()
  function loadTab($tabLink, reloadAllContent, groupIds) {
    if (!reloadAllContent && $('.entriesTable', Me.$tabControl).size() == 0)
      reloadAllContent = true;

    if (Me.xhr)
      Me.xhr.abort();

    if (!reloadAllContent) {
      populateTabEntries(Me.CallsRepository.calls, $tabLink.attr("id"), groupIds);
      CallsTabs.checkForHiddenCalls();
      return;
    }

    // return value not used yet
    return Me.xhr = $.ajax({
      url: '/api/calls/',
      type: "get",
      data: { "oldCallsVersion": Me.CallsRepository.version },
      dataType: "json",
      timeout: 20000,
      extra: {
        activeTabId: $tabLink.attr("id"),
        groupIds: groupIds
      },
      error: function () {
        $('#tabLoader', Me.$tabControl).hide();
        if (reloadAllContent) {
          //Only show error if it was a manual request. If it was automatic, fall silently
          $('#tabContent', Me.$tabControl).hide();
          $('#tabNoResults', Me.$tabControl).hide();
          $('#tabError', Me.$tabControl).fadeIn();
        }
      },

      //EXPD: Depends on Me.CallsRepository, Me.$tabControl, Me.options.tabGroupStatuses, getCountsPerGroup(), CallsTabs.getDeletedCalls(), CallsTabs.getUpdatedCalls(), CallsTabs.getInsertedCalls()  ...
      success: function (dataRemoteCalls) {
        Me.CallsRepository.calls = dataRemoteCalls;

        // TODO: have the server generate groups and assign groupIds automatically
        $.ajax({
          url: "/api/groups",
          type: "GET",
          contentType: "application/json; charset=utf-8"
        }).done(function (data) {
          var groups = $.map(data, function (item) {
            return { name: item.name, groupId: item.dispatchEntryGroupId };
          })

          // Hide option for grouping calls if there are no groups specified
          if (groups == 0) {
            $('#tabcontrol1 .moreOptions').hide();
          } else {
            //Add one more default group for showing all other calls (that have no groups)
            groups.push({ name: "All Others", groupId: -1000 });
          }

          $.extend(Me.Groups, groups);

          if (groups.length) {
            $('ul.TabList li.selected').find('.moreOptions').switchClass('hideOptions', 'showOptions', 400);
          }
        });

        populateTabEntries(dataRemoteCalls, this.extra.activeTabId, this.extra.groupIds);
      }
    });
  }

  function populateTabEntries(dataRemoteCalls, activeTabId, groupIds) {

    if ($('body').data('companyId') != null) {
      var companyId = $('body').data('companyId');
      if (companyId != "" && companyId != "all") {
        dataRemoteCalls =
          $.grep(dataRemoteCalls, function (item) {
            return item.companyId == companyId
          });
      }
    }

    var index = 0;
    var newCallsInCurrentTab = $$(dataRemoteCalls || []).where(function (call) {
      var status = CallsTabs.getStatus(call);
      if (status != null) {
        return ($.inArray(status.toString(), Me.options.tabGroupStatuses[activeTabId]) != -1)    //Select only the calls of current tab
      }
    }).$$;

    $('#tabLoader', Me.$tabControl).hide();
    $('#tabError', Me.$tabControl).hide();

    createAndShowEntriesTable(newCallsInCurrentTab, groupIds);
    Me.sortCalls(dataRemoteCalls);

    Me.updateTabs();
  }

  function createAndShowEntriesTable(newCallsInCurrentTab, groupIds) {
    var tableHtml = "<ul class=\"entriesTable\" cellspacing=\"0\" cellpadding=\"0\" style=\"margin-top: 0px; border-top: none\" id=\"dcslist\"></ul>";
    var $table = $(tableHtml);

    Me.sortCalls(newCallsInCurrentTab);

    $('#tabContent', Me.$tabControl).empty().append($table);

    if (groupIds == null || groupIds.length == 0) {
      // List calls by sort (no groups)
      console.log(newCallsInCurrentTab);
      $.each(newCallsInCurrentTab, function (i, c) {
        var $callTr = CallsTabs.getDispatchEntryHtml(c);
        $('.entriesTable', Me.$tabControl).append($callTr);
        CallsTabs.showLateCalls(c);
        $callTr.tbkBehav();
      });
    } else {
      towbook.compileTemplate('tpl-group-title', $('#tpl-group-title'));

      $.each(callsTabs.Groups, function (i, groupItem) {
        // Add Group titles
        var callCount = 0;
        var groupCalls = null;
        if (groupItem.groupId < 0) {
          // -1, non group call
          callCount = $.grep(newCallsInCurrentTab, function (item) {
            return item.groups.length == 0;
          }).length;

          groupCalls = $.grep(newCallsInCurrentTab, function (item) {
            return item.groups.length == 0;
          });
        } else {
          // Group call...check for groupId in array
          callCount = $.map(newCallsInCurrentTab, function (item) {
            if (item.groups != null && item.groups.indexOf(groupItem.groupId - 0) != -1)
              return item.callNumber;
          }).length;

          // Add calls to this group
          groupCalls = $.grep(newCallsInCurrentTab, function (item) {
            if (item.groups != null)
              return item.groups.indexOf(groupItem.groupId - 0) != -1;
          });
        }

        $.extend(groupItem, { total: callCount });
        var gi = towbook.applyTemplate('tpl-group-title', groupItem);
        $('.entriesTable', Me.$tabControl).append(gi);

        // Check and add empty message
        var groupObj = $('.group[data-id=' + groupItem.groupId + ']');
        if (callCount == 0)
          groupObj.find('.emptyGroupMessage').show();
        else
          groupObj.find('.emptyGroupMessage').hide();

        if (groupCalls != null) {
          $.each(groupCalls, function (i, c) {
            // Add calls to group
            var $callTr = CallsTabs.getDispatchEntryHtml(c, null, groupItem.groupId);
            $('.entriesTable', Me.$tabControl).find('ul.groupCallList[data-id=' + groupItem.groupId + ']').append($callTr);
            CallsTabs.showLateCalls(c);
            $callTr.tbkBehav();

            // show/hide groups that aren't checked
            if (groupIds.indexOf("" + groupItem.groupId + "") == -1)
              $('.group[data-id=' + groupItem.groupId + ']').hide();
            else
              $('.group[data-id=' + groupItem.groupId + ']').show();
          });
        }

        // Set collapse/expand state based on class in the checkboxes
        if ($('.groupOption[data-id=' + groupItem.groupId + ']', $('ul.TabList li.selected:visible')).hasClass('collapsed'))
          $('.group[data-id=' + groupItem.groupId + ']').find('ul.groupCallList').hide();
      });

      // events for Group Titles
      var toggleCallsInGroupView = function (groupId) {
        var titleObj = $('.entriesTable', Me.$tabControl).find('li.groupTitle[data-id=' + groupId + ']');
        var obj = $('.entriesTable', Me.$tabControl).find('ul.groupCallList[data-id=' + groupId + ']');

        if (obj.find('li').length != 0) {
          if ($(obj).is(':visible')) {
            obj.stop().slideUp('slow', function () {
            });
            $('.groupOption[data-id=' + groupId + ']', $('ul.TabList li.selected:visible')).addClass('collapsed');

          } else {
            obj.stop().slideDown('slow');
            $('.groupOption[data-id=' + groupId + ']', $('ul.TabList li.selected:visible')).removeClass('collapsed');
          }
        }
      };

      $('.groupTitle span').off('click').on('click', function () {
        var id = $(this).closest('li').attr('data-id');
        toggleCallsInGroupView(id);
      });
    }

    // Add 'active' state to any entryRow's that are currently active in the editor window
    var activeId = $('body').attr('active-entry-id');
    if (activeId > 0)
      $('.entryRow[data-id=' + activeId + ']').addClass('active');

    $('#tabFetchingMore', Me.$tabControl).hide();

    if (newCallsInCurrentTab.length == 0) {
      if (Me.activeTabIsCompleted() || Me.activeTabIsCancelled()) {
        $('body').addClass('enable-search');
        $('#tabContainer').trigger('scroll', true);
      } else {
        $('#tabNoResults').html("There are no calls to display");
        $('#tabContent', Me.$tabControl).hide();
        $('#tabNoResults', Me.$tabControl).fadeIn('slow');
      }
    } else {
      $('#tabNoResults', Me.$tabControl).hide();
      $('#tabContent', Me.$tabControl).fadeIn('slow');
    }

    return $table;
  }

  /*</Private Methods>*/

  /*<Public methods>*/
  this.updateTabs = function () {
    (function setTabsCount(data) {
      var dataByCompany = data;
      if ($('body').data('companyId') != null) {
        var companyId = $('body').data('companyId');
        if (companyId != "" && companyId != "all") {
          dataByCompany =
            $.grep(dataByCompany, function (item) {
              return item.companyId == companyId
            });
        }
      }

      var countsPerStatus = {};

      for (var s in DispatchScreen.statusData) {
        DispatchScreen.statusData[s].q = 0;
        countsPerStatus[s] = 0;
      }
      for (var i = 0; i < dataByCompany.length; i++) {
        var s = CallsTabs.getStatus(dataByCompany[i]);
        DispatchScreen.statusData[s].q++;
        countsPerStatus[s]++;
      }
      //console.log("counts", countsPerStatus);

      var countsPerGroup = getCountsPerGroup(countsPerStatus);
      for (var groupId in countsPerGroup) {
        if (groupId == 'atCompleted' || groupId == 'atCancelled') continue;

        $("#" + groupId).children('span').remove();
        $('#' + groupId).append("<span>(" + countsPerGroup[groupId] + ")</span>");
      }



    })(Me.CallsRepository.calls);
  };

  this.activeTabType = function () {
    var $activeTab = $('ul.TabList li.selected:visible', Me.$tabControl);
    if ($activeTab.size() == 0) return null;
    if ($activeTab.is(":has(.searchTab)")) return "SEARCHTAB";
    return "STATUSTAB";
  }

  this.activeTabIsCompleted = function () {
    var $activeTab = $('ul.TabList li.selected:visible', Me.$tabControl);
    if ($activeTab.size() == 0) return false;
    if ($activeTab.is(":has(#atCompleted)")) return true;
  }

  this.activeTabIsScheduled = function () {
    var $activeTab = $('ul.TabList li.selected:visible', Me.$tabControl);
    if ($activeTab.size() == 0) return false;
    if ($activeTab.is(":has(#atScheduled)")) return true;
  }

  this.activeTabIsCancelled = function () {
    var $activeTab = $('ul.TabList li.selected:visible', Me.$tabControl);
    if ($activeTab.size() == 0) return false;
    if ($activeTab.is(":has(#atCancelled)")) return true;
  }

  this.activeTabIsSortable = function () {
    var $activeTab = $('ul.TabList li.selected:visible', Me.$tabControl);
    if ($activeTab.size() == 0) return false;

    if ($activeTab.is(":has(#atCurrent)")) return true;
    if ($activeTab.is(":has(#atWaiting)")) return true;

    return false;
  }

  this.updateActiveTab = function ($tabLink) {
    if ($tabLink == null)
      $tabLink = $('ul.TabList li.selected:visible', Me.$tabControl).find('.tabLink');

    if ($tabLink == null)
      return true;

    var groupIds = null;

    if (callsTabs.Groups.length > 0 && $('input[name=' + $tabLink.attr('id') + '_sortBy]:checked', $('ul.TabList li.selected:visible')).val() == "byGroups") {
      groupIds = $.map($('input[name=groupOptions]:checked', $('ul.TabList li.selected:visible')), function (item) {
        return item.value;
      })
      if (groupIds.length == 0)
        return false;

      console.log("Sorting by Groups", groupIds);
    }
    else
      console.log("showing calls without Groups");


    loadTab($tabLink, false, groupIds);

    return true;
  }

  this.changeMoreOptionsMenu = function ($tabLinkOld, $tabLinkNew) {
    if ($tabLinkOld != null) {
      var oldMoreOptionsObj = $tabLinkOld.closest('li').find('.moreOptions');
      oldMoreOptionsObj.css('background-color', 'transparent')
        .addClass("inMotion")
        .switchClass('showOptions', 'hideOptions', 400, function () {
          oldMoreOptionsObj.removeClass("inMotion");
        });
    }

    var newMoreOptionsObj = $tabLinkNew.closest('li').find('.moreOptions');
    newMoreOptionsObj.css('background-color', '#2B75BE')
      .addClass("inMotion")
      .switchClass('hideOptions', 'showOptions', 400, function () {
        $(this).removeAttr('style');
        $(this).removeClass("inMotion");
      });
  }

  this.debugHelper = function (message) {
    console.log("[DEBUG] " + message);
  }

  this.changeActiveTab = function ($tabLink, forceChange) {
    if (!forceChange) {
      if ($tabLink.closest('li').hasClass('selected'))
        return;
    }

    var $searchTab = $('ul.TabList li:has(.searchTab)', Me.$tabControl);
    $searchTab.hide();
    $('#topMessageBox').hide();

    if (callsTabs.Groups.length > 0) {
      var $tabLinkShowOptions = $('#tabcontrol1 .moreOptions.showOptions').closest('li').find('.tabLink');
      Me.changeMoreOptionsMenu($tabLinkShowOptions, $tabLink);
    }

    $('ul.TabList li.selected', Me.$tabControl).removeClass("selected");
    $tabLink.closest('li').addClass('selected');

    $("#tabError", Me.$tabControl).hide();
    $('#tabContent', Me.$tabControl).hide();
    $('#tabNoResults', Me.$tabControl).hide();
    $('#tabLoader', Me.$tabControl).show();

    Me.updateActiveTab($tabLink);
  }

  this.initActiveTab = function ($tabLink, calls) {
    var $searchTab = $('ul.TabList li:has(.searchTab)', Me.$tabControl);
    $searchTab.hide();

    $('ul.TabList li.selected', Me.$tabControl).removeClass("selected");
    $tabLink.closest('li').addClass('selected');

    $("#tabError", Me.$tabControl).hide();
    $('#tabContent', Me.$tabControl).hide();
    $('#tabNoResults', Me.$tabControl).hide();
    $('#tabLoader', Me.$tabControl).hide();

    var callsToShow = $.grep(calls, function (e) {
      return $.inArray(e.status.id, Me.options.tabGroupStatuses[$tabLink.attr("id")]);
    });

    console.log("check that status ==" + Me.options.tabGroupStatuses[$tabLink.attr("id")])
    console.log("calls: " + calls.length);
    console.log("callsToShow: " + callsToShow.length);

    //loadTab($tabLink, true, null);
    Me.CallsRepository.calls = calls;
    populateTabEntries(Me.CallsRepository.calls, $tabLink.attr("id"), null);

  }

  this.populateFoundEntries = function (searchRepository) {
    $('#ajax-container').hide("slow");
    var $searchTab = $('ul.TabList li:has(.searchTab)', Me.$tabControl);

    $('ul.TabList li.selected', Me.$tabControl).removeClass("selected").find('.moreOptions.showOptions').switchClass('showOptions', 'hideOptions', 1);
    $('#topMessageBox').hide();
    $searchTab.addClass('selected').show();

    $("#tabError", Me.$tabControl).hide();
    $('#tabContent', Me.$tabControl).hide();
    $('#tabNoResults', Me.$tabControl).hide();
    $('#tabLoader', Me.$tabControl).hide();

    Me.updateTabs();
    createAndShowEntriesTable(searchRepository);

  }

  this.sortCalls = function (data) {
    if (callsTabs.activeTabType() == 'SEARCHTAB')
      return;

    // Get rid of any duplicate call (can happen if pusher comes through twice on a scheduled call)
    $.unique(data);

    if (callsTabs.activeTabIsSortable()) {

      data.sort(function (a, b) {

        if (a.priority === b.priority) {
          if (a.arrivalETA == null && b.arrivalETA == null) {
            return b.callNumber - a.callNumber;
          }

          if (a.arrivalETA == null && b.arrivalETA != null)
            return 1;

          if (b.arrivalETA == null && a.arrivalETA != null)
            return -1;

          return new Date(a.arrivalETA) - new Date(b.arrivalETA);
        }


        if (a.priority != 1 && b.priority == 1 || a.priority != 1 && b.priority != 2)
          return 1;
        if (a.priority == 1 && b.priority != 1 || b.priority != 1 && a.priority != 2)
          return -1;

        return 0;
      });


      firstBy = (function () { function e(f) { f.thenBy = t; return f } function t(y, x) { x = this; return e(function (a, b) { return x(a, b) || y(a, b) }) } return e })();

      if (data != null && data.length > 0 && (data[0].companyId == 4082 || data[0].companyId == 1779)) {
        console.log("log 4082");

        data.sort(firstBy(function (a, b) { return a.status.id - b.status.id; }).thenBy(function (a, b) { return b.callNumber - a.callNumber; }));
      }
      /*

  data.sort(firstBy(function(a,b) {
      aa = (a.account != null && a.account.id != null && a.account.id == 14661);
      bb = (b.account != null && b.account.id != null && b.account.id == 14661);

      return aa - bb;
  }).thenBy(function(a,b) {  return (a.priority == 1) - (b.priority == 1) } )
    .thenBy(function(a,b) {
      if (a.arrivalETA == null && b.arrivalETA != null)
                 return 1;

              if (b.arrivalETA == null && a.arrivalETA != null)
                  return -1;

       return (new Date(a.arrivalETA)) - (new Date(b.arrivalETA)) 
     })
    .thenBy(function(a,b) {  return b.callNumber - a.callNumber  } )
    );
*/

    }
    else {
      data.sort(function (a, b) {
        return b.callNumber - a.callNumber;
      });
    }
  }

  this.removeCall = function (id) {
    console.log('CallsTabs.removeCall', id);
    _allCurrentDispatchEntries = _allCurrentDispatchEntries.filter(c => c.id !== id);
    Me.CallsRepository.calls = Me.CallsRepository.calls.filter(c => c.id !== id);
    CallsTabs.updateTabCounts();
  };

  this.handleQueryParams = function () {
    const params = new URLSearchParams(window.location.search);
    const goToCallParam = params.get('goToCall');
    if (goToCallParam) {
      CallsTabs.goToCall(goToCallParam)
    }
  }
}

var timeoutID;

//Static members of CallsTabs class
$.extend(CallsTabs, {
  updateTabCounts: function () {
    //console.log(this, CallsTab);
    callsTabs.updateTabs();
  },
  //returns null or existing version. Using the magical $$ (SagazQuery1.0.js)
  callExistsIn: function (id, calls) {
    var existentCall = $$(calls).where(function (c) { return c.id == id }).firstVal();
    return (existentCall === undefined ? null : existentCall.version);
  },
  getDeletedCalls: function (oldCalls, newCalls) {
    return $$(oldCalls).where(function (c) { return !CallsTabs.callExistsIn(c.id, newCalls) }).$$;
  },
  getInsertedCalls: function (oldCalls, newCalls) {
    return $$(newCalls).where(function (c) { return !CallsTabs.callExistsIn(c.id, oldCalls) }).$$;
  },
  //EXPD: Depends on CallsTabs.callExistsIn()
  getUpdatedCalls: function (oldCalls, newCalls) {
    return $$(newCalls).where(function (c) {
      var oldVersion = CallsTabs.callExistsIn(c.id, oldCalls);
      // !!oldVersion = boolean that indicates if callExistsIn
      return (!!oldVersion && oldVersion != c.version);
    }).$$;
  },
  getStatus: function (data, bufferInHours) {
    // Return the status of the call item.  However, we need to check if the call is a scheduled call
    // and return a status of "254" if that is the case.
    if (data == null || data.status == null)
      return null;

    if (bufferInHours == null)
      bufferInHours = 6;

    Date.prototype.addHours = function (h) {
      this.setTime(this.getTime() + (h * 60 * 60 * 1000));
      return this;
    }


    var status = data.status.id;
    var now = new Date();
    var scheduledtime = new Date(towbook.parseDateString(data.arrivalETA + 'Z'));

    if (status == 0 && towbook.formatDate(data.arrivalETA) != "" && scheduledtime.getTime() > now.addHours(bufferInHours).getTime())
      status = callsTabs.options.tabGroupStatuses["atScheduled"];

    return status;
  },


  // <return>tr for a new entry row, WITH EVENTS ATTACHED with the information of "callData"</return>
  getDispatchEntryHtml: function (callData, isSearch, groupId) {
    if (typeof ($.template['dispatch_entry']) == 'undefined')   //If template doesn't exist yet, compile it
      $("#tpl-dispatch_entry").template('dispatch_entry');

    var oS = CallsTabs.getStatus(callData);
    $.template('status', DispatchScreen.statusData[oS]['t']);   //Status text. Using it like a printf

    var isUserAccount = (towbook.currentUser.type == 100);
    var isManagerAccount = (towbook.currentUser.type == 1);

    if (towbook.companies.length > 1) {
      var cb = towbook.get(towbook.companies, callData.companyId);
      if (cb != null)
        callData.company = cb.name;
      else
        console.log("invalid: ", callData.companyId);
    }


        callData.arrivalTime = callData.arrivalTime || null;
        callData.towTime = callData.towTime || null;
        callData.completionTime = callData.completionTime || null;


    if (callData.billToAccountId != null && callData.account != null && callData.billToAccountId != callData.account.id) {
      callData.billToAccount = towbook.get(towbook.accounts, callData.billToAccountId);
    }

    callData.status.text = $.tmpl('status', callData)[0].data;
    callData.statusColorStyle = "border-right: solid 7px " + DispatchScreen.statusData[oS]['c'] + '';
    callData.statusColor = DispatchScreen.statusData[oS]['c'];

    if (callData.insights.pushedToQuickBooks != null) {
      callData.pushedToQuickBooks = true;
    }
    if (callData.insights.photoCount != null) {
      callData.photoCount = true;
    }
    var retval = '<div><ul id="nav">';

    if (callData.s != 255 && $.inArray("MODIFY", callData.availableActions) > -1)
      retval += '<li><a id="callEditorLink_${id}" href="/DispatchEditor/Editor.aspx?id=${id}">Modify</a></li>';

    if ($.inArray("VIEW", callData.availableActions) > -1)
      retval += '<li><a href="../DispatchEditor/Editor.aspx?id=${id}&readOnly=1">View</a></li>';

    if (callData.status.id != 5 && callData.status.id != 255 && !isUserAccount) {
      retval += '<li><a href="../ajax/dispatch/${id}/statusUpdate">Update Status</a></li>';
      retval += '<li><a href="../ajax/dispatch/${id}/complete">Complete Call</a></li>';
    }


            retval += '<li><a href="../ajax/dispatch/${id}/internalNotes">Notes</a></li>';


    if (callData.status.id != 255) {
        retval += '<li><a href="../ajax/dispatch/${id}/receipt">Receipt</a></li>';

        if (callsTabs.options.faxingEnabled) {
            retval += '<li><a href="../ajax/dispatch/${id}/fax">Fax</a></li>';
        }
      }

    if (!isUserAccount) {
      retval += '<li><a href="../ajax/dispatch/${id}/payments">Payments</a></li>';
    }


    if (callData.attributes != null) {
      var aaa = towbook.get(callData.attributes, 1, 'attributeId')
      if (aaa != null)
        callData.mcDispatchNumber = aaa.value;

      var bbb = towbook.get(callData.attributes, 4, 'attributeId')
      if (bbb != null)
        callData.poNumber = bbb.value;

      /*
                  var cr = towbook.get(callData.attributes, 23, 'attributeId')
                  if (cr != null)
                      callData.cancellationReason = cr.value;
      */

      if (callData.cancellationReason == null && callData.status != null && callData.status.id == 255) {
        var cr = towbook.get(callData.attributes, 23, 'attributeId')
        if (cr != null)
          callData.cancellationReason = cr.value;
      }
    }

    function getMapLink(callData) {
      var mapLink = null;

      if (towbook.companies && towbook.companies.length > 0) {
          var company = JSON.parse(JSON.stringify(towbook.get(towbook.companies, callData.companyId))); // deep clone of company
          if (company) {

              var towSource = callData.towSource;
              var towDest = callData.towDestination;

              // See if we need to start the route from the account, rather than the company
              if (!towbook.isEmpty(towbook.mapFromAddressBook) && callData.account) {
                  var mapFrom = towbook.get(towbook.mapFromAddressBook, callData.account.id, "accountId");
                  if (mapFrom) {
                      company.address = mapFrom.address.length ? mapFrom.address : company.address;
                      company.city = mapFrom.city.length ? mapFrom.city : company.city;
                      company.state = mapFrom.state.length ? mapFrom.state : company.state;
                      company.zip = mapFrom.zip.length ? mapFrom.zip : company.zip;
                  }
              }

            if (callData.account && callData.companyId != 2205) {
              var mapType = "google";

              if (callData.account.company) {
                if ((callData.account.company.toLowerCase().indexOf('allstate') != -1 ||
                  (callData.account.company.toLowerCase().indexOf('usac') != -1)) &&
                  callData.companyId == 1729) {
                  mapType = "bing";
                } else if (callData.account.company.toLowerCase().indexOf('nsd') != -1 ||
                  (callData.companyId == 971 && callData.account.company.toLowerCase().indexOf('agero') != -1)) {
                  mapType = "bing";
                }

                mapLink = towbook.getMap(mapType,
                  company.address + ", " + company.city + ", " + company.state + "  " + company.zip + " (" + company.name + ")",
                  towSource, towDest);
              }
            } else {
              mapLink = "http://maps.google.com?towbook=1&" +
                "saddr=" + company.address + ", " + company.city + ", " + company.state + "  " + company.zip + " (" + company.name.replace("#", "").replace("&", "").replace("&", "") + ")" +
                "&daddr=" + towSource + (towDest ? "+to:" + towDest : "");
            }
          }
      }

      return mapLink;
  }

    var mapLink = getMapLink(callData);
    if (mapLink)
        retval += '<li><a href="' + mapLink + '" target=\"_new\">Map</a></li>';

    retval += '<li><a href="../ajax/dispatch/${id}/photos">Photos</a></li>';


    if (!isUserAccount && callData.assets && callData.assets.length > 0 && callData.assets[0].vin && callData.assets[0].vin.length == 17 ) {
      retval += '<li><a href="/ds4/?search=' + encodeURI(callData.assets[0].vin) + '" target="_new" title="Search for calls with this VIN">VIN Search</a></li>'
    }


    if (callData.status.id == 255) {
      retval += '<li><a href="../api/calls/${id}/uncancel" class="x-dispatch-cancel" title="Place the call back under Waiting to be dispatched status">Undo Cancel</a></li>'
    }

    if (callData.status.id == 5) {
      retval += '<li><a href="../api/calls/${id}/uncomplete" class="x-dispatch-uncomplete" title="Place the call back under Waiting">Undo Complete</a></li>'
    }

    if (!isUserAccount) {
      if ($.inArray("DIGITAL_EXTEND_ETA", callData.availableActions) > -1)
        retval += '<li><a href="../ajax/dispatch/${id}/digitalExtendEta">Extend ETA</a></li>';

      if ($.inArray("DIGITAL_REQUEST_ADDITIONAL_SERVICE", callData.availableActions) > -1)
        retval += '<li><a href="../ajax/dispatch/${id}/digitalRequestAdditionalService">Request Additional Service</a></li>';

      if ($.inArray("DIGITAL_REPORT_SERVICE_FAILURE", callData.availableActions) > -1)
        retval += '<li><a href="../ajax/dispatch/${id}/digitalServiceFailure">Report Service Failure</a></li>';

      if ($.inArray("DIGITAL_REQUEST_GOA", callData.availableActions) > -1)
        retval += '<li><a href="../ajax/dispatch/${id}/digitalRequestGoa">Request GOA</a></li>';

      if ($.inArray("DIGITAL_CANCEL", callData.availableActions) > -1)
        retval += '<li><a href="../ajax/dispatch/${id}/digitalCancel">Digital Cancel</a></li>';

      retval += '<li><a href="#" class="submenu-link">...</a>';

      // TODO: implement Event History. Hide for now.
      retval += '<ul class="submenu">';

      retval += '<li><a href="../ajax/dispatch/${id}/email">Email</a></li>';

      if (towbook.currentUser.type != 3)
        retval += '<li><a href="../ajax/dispatch/${id}/history">History</a></li>';

      if ($.inArray("CANCEL", callData.availableActions) > -1)
        retval += '<li><a href="../ajax/dispatch/${id}/cancel">Cancel Call</a></li>';

        if (window["dispatching_showOptions_delete"] !== undefined && $.inArray("DELETE", callData.availableActions) > -1)
        retval += '<li><a href="../ajax/dispatch/${id}/delete">Delete Call</a></li>';

      retval += '<li><a href="../DispatchEditor/Editor.aspx?id=${id}&duplicate=true">Duplicate Call</a></li>';

      retval += '<li><a href="../ajax/dispatch/${id}/files">Files</a></li>';


      if (callsTabs.options.accountingProviderEnabled)
        retval += '<li><a href="../ajax/dispatch/${id}/pushToAccountingApp">Push to Quickbooks</a></li>';


      if (callData.impoundDetails != null && callData.impoundDetails.id > 0) {
        retval += '<li><a href="../impounds/impound.aspx?id=' + callData.impoundDetails.id + '" target="_top">View Impound</a></li>';
      }
      console.log(callData);
      if (callData.account != null)
        retval += '<li><a href="../accounts/account.aspx?id=${account.id}" target="_top">View Account</a></li>';


      if (isManagerAccount && towbook.invoiceStatuses != null && towbook.invoiceStatuses.length > 0 && callData.status.id == 5) {
        retval += '<li><a href="../ajax/dispatch/${id}/invoiceStatus" class="x-dispatch-invoiceStatus" title="Adjust invoice status">Update Invoice Status</a></li>'
      }


      retval += "</ul></li>";


    }
    else {
      if ($.inArray("CANCEL", callData.availableActions) > -1)
        retval += '<li><a href="../ajax/dispatch/${id}/cancel">Cancel Call</a></li>';

      if (callData.status.id == 255) {
        retval += '<li><a href="../api/calls/${id}/uncancel" class="x-dispatch-cancel" title="Place the call back under Waiting to be dispatched status">Undo Cancel</a></li>'
      }
    }


    retval += "</ul></div>";

    $.template('actions', retval);

    callData.tasks = $.tmpl('actions', callData).html();

    var callStyle2 = "";
    if (callData.version > 10000)
      callStyle2 = "color:green";

    if (callData.priority == 2)
      callStyle2 = "color:#999";
    if ((callStyle2).length == 0) {
      if ((callData.towDestination || "").length < 3 && !callData.impound) {
        callStyle2 = "color: #01b901";
      }
    }

    $.extend(callData, { groupId: groupId, xpf: callStyle2 });




    var $entryRow = $.tmpl('dispatch_entry', callData);


    if (callData.version > 10000)
      $entryRow.css('backgroundColor', 'rgb(200,240,200)');


    if (callData.priority == 2)
      $entryRow.css('opacity', '0.7');


    if (callData.priority == 1) {
      $entryRow.find('.title:first').addClass('emergency');


    } else {


      if (callData.account != null) {

        var acc = towbook.get(towbook.accounts, callData.account.id);
        if (acc != null && acc.type == 1)
          $entryRow.find('.title:first').addClass('emergency');

      }
    }


    (function bindEntryRow_Click() {
      $entryRow.click(function (event) {
        var container = $('#tabContainer');

        if (event.target.tagName == "A" ||
          $(".DispatchAction", this).has(event.target).size() > 0)
          return;


        var daT = $('.DispatchAction:visible');

        if (daT.length > 0)
          daT = daT.position().top
        else
          daT = 0;

        var top = $(this).position().top +
          container.scrollTop() -
          container.offset().top -
          ($(this).position().top > daT ? $('.DispatchAction').height() : 0);

        var newTop = top + ($(this).height() - $('#tabContainer').height() + $('.DispatchAction').height());

        if (container.scrollTop() < newTop) {
          container.animate({ scrollTop: newTop });
        } else if (container.scrollTop() > top) {
          container.animate({ scrollTop: top });
        }

        // if current one is BELOW previous... then use dispatchActionHeight
        //                $(".DispatchAction").slideUp(300).parent().animate({ backgroundColor: "#ffffff" }, 300);

        if (!$(this).find('div.details').hasClass('selected')) {
          $('li.entryRow').find('div.details').each(function (index, item) {
            $(item).removeClass('selected').addClass('unselected');
          });
        }

        var o = $(".DispatchAction", this);

        if (o.is(":visible")) {
          o.slideUp(300);
          if (o.parent().hasClass('selected'))
            o.parent().animate(200);
          else
            o.parent().switchClass("unselected", "selected", 200);
        } else {
          o.slideDown(300);
          if (o.parent().hasClass('selected'))
            o.parent().animate(200);
          else
            o.parent().switchClass("unselected", "selected", 200);
          if (o[0] != null)
            $('.DispatchAction[id!=' + o[0].id + ']').slideUp().parent().animate(200);
        }

      });
    })();

    (function bindDispatchActions_Click() {
      var found = $entryRow.find('a').each(function (e) {
        $(this).on("click", function (e) {

          var dataId = $(this).closest('.entryRow').data('id');

          $('#header li.x-icon div.submenu').hide();

          if ($(this).attr('href') != '#' && $(this).attr('target') == null) {
            var allow = true;
            if (typeof checkForAjaxChanges == 'function') {
              var r = checkForAjaxChanges();
              if (r.changed)
                if (!confirm(r['message'] || 'Are you sure you want to leave?'))
                  allow = false;
            }
            if (allow) {
              try {
                delete window.checkForAjaxChanges;
                delete window.onbeforeunload;
              } catch (e) { }

              if ($(window).width() < 481)
                return true;


              if ($(this).hasClass('x-dispatch-cancel')) {
                var saveUrl = "/api/calls/" + callData.id + "/uncancel";
                var saveType = "PUT";

                setLoadingMessage('#editor', 'Uncancelling...');

                $.ajax({
                  url: saveUrl,
                  data: null,
                  type: saveType,
                  contentType: "application/json; charset=utf-8"
                }).done(function (data) {
                  //window.towbook.views.dispatch.callEditor.clearDetailPanel();
                  towbook.log('TOWBOOK PUT UPDATE done!', data);
                  unsetLoadingMessage();
                })

                return false;
              }

              if ($(this).hasClass('x-dispatch-uncomplete')) {
                var saveUrl = "/api/calls/" + callData.id + "/uncomplete";
                var saveType = "PUT";

                setLoadingMessage('#editor', 'Uncancelling...');

                $.ajax({
                  url: saveUrl,
                  data: null,
                  type: saveType,
                  contentType: "application/json; charset=utf-8"
                }).done(function (data) {
                  unsetLoadingMessage();
                })

                return false;
              }



              loadAjax($(this).attr('href'));

              // Remove 'active' class from all entryRows
              $('.entryRow.active').removeClass('active');

              // Add 'active' to the entryRow that has a dispatch action
              $('.entryRow[data-id=' + dataId + ']').addClass('active');
              $('body').attr('active-entry-id', dataId);

            }
            return false;
          }
        })
      });
      return;
    })();

    (function bindDispatchActions_SubMenu() {
      var found = $entryRow.find('.submenu-link').each(function (e) {

        $(this).on("mouseenter", function (e) {
          $('ul.submenu').stop(false, true).hide();
          window.clearTimeout(timeoutID);
          var submenu = $(this).next();

          var offset = 22;

          if ($('body').hasClass('flat-design'))
            offset = -30;

          submenu.css({
            position: 'absolute',
            top: ($(this).offset().top - submenu.height()) + 'px',
            left: ($(this).offset().left - (submenu.width() + offset)) + 'px',
            zIndex: 1000
          });

          submenu.stop().show(); //slideDown(300);

          submenu.mouseleave(function () {
            $(this).hide(300);
          });

          submenu.mouseenter(function () {
            window.clearTimeout(timeoutID);
          });
        });

        $(this).on("mouseleave", function (e) {
          timeoutID = window.setTimeout(function () { $('ul.submenu').stop(false, true).hide(); }, 250);  // just hide
        });

        return;
      });
    })();


    return $entryRow;
  },
  showLateCalls: function (call) {
    var boolStopCounting = false;

    // Return if no ETA has been specified
    if (!call.arrivalETA)
      return false;

    // Check if late
    var expected = towbook.parseDateString(call.arrivalTime + 'Z') || towbook.parseDateString(call.towTime + 'Z') || towbook.parseDateString(call.completionTime + 'Z') || new Date();
    var arrival = towbook.parseDateString(call.arrivalETA + 'Z');
    if (expected > arrival) {
      target = $("*[data-id=" + call.id + "]");
      $(target).addClass('late');

      // Ingore any checking once we show this call as late AND there is an arrivalTime, otherwise we continue to calculate a new late time.
      if ($("*[data-id=" + call.id + "]").hasClass('late') && call.arrivalTime) {
        boolStopCounting = true;
      }

      if (boolStopCounting)
        $(target).addClass('arrived');

      // Calculate minutes late and show
      var difference = (expected - arrival) / 1000; // difference is in seconds
      var minutes = Math.floor((difference / 60) % 60);
      var hours = Math.floor((difference / (60 * 60)) % 24);
      var days = Math.floor(difference / (60 * 60 * 24));
      var lateMessage = (boolStopCounting == true ? "Arrived " : "");

      if (minutes == 0 && hours == 0 && days == 0) // zero minutes is not late
        return false;

      if (days) {
        lateMessage += days + (days == 1 ? " day " : " days ");
        if (hours > 0)
          lateMessage += hours + (hours == 1 ? " hour " : " hours ");
      }
      else if (days == 0 && hours) {
        lateMessage += hours + (hours == 1 ? " hour " : " hours ");
        if (minutes > 0)
          lateMessage += minutes + (minutes == 1 ? " minute " : " minutes ");
      }
      else if (days == 0 && hours == 0)
        lateMessage += minutes + (minutes == 1 ? " minute " : " minutes ");

      lateMessage += "late";

      $(target).find('.priorityBox').html(lateMessage);
      return true;
    }

    return false;
  },
  resizeEditorWindow: function () {
    var o = $('#tabContainer, #editorWrapper');
    var clh = ($('#colorLegend').outerHeight() || 0) + 10 + ($('#callRequestsBar').is(":visible") ? $('#callRequestsBar').height() || 0 : 0);

    $(o).height(($(window).height()) - o.offset().top - clh);
    $('#editor').height(($(window).height()) - o.offset().top - clh);
    $('#title').width($('#editor_ajaxView').width());
    $('.navigation-row').width($('#editor_ajaxView').width());

    var tippingPoint = $(o).height() - $('.navigation-row').height() - 21;

    if ($('#editor_ajaxView').height() < tippingPoint)
      $('.navigation-row').css('top', "").css('position', 'relative');
    else
      $('.navigation-row').css('top', ($(window).height()) - clh - $('.navigation-row').height() - 21).css('position', 'fixed');

  },
  checkForHiddenCalls: function () {

    var callList = $.map($('li.entryRow'), function (item) { return $(item).data('callNumber') });
    var groupList = $.map($('li.entryRow'), function (item) {
      var id = $(item).closest('ul.groupCallList').data('id');
      var groupOptionObj = $('.groupOption[data-id=' + id + ']', $('ul.TabList li.selected:visible'));
      if (!groupOptionObj.hasClass('hidden') || groupOptionObj.hasClass('collapsed'))
        return $(item).data('callNumber');
    });
    var callsNotShownCount = callList.length - groupList.length; //$(callList).not(groupList).get().length;
    if (groupList.length < callList.length) {
      towbook.compileTemplate('tpl-top-group-message', $('#tpl-top-group-message'));

      $('#topMessageBox').html(towbook.applyTemplate('tpl-top-group-message', {
        hidden: callsNotShownCount,
        visible: callList.length - callsNotShownCount
      })).slideDown();

      $('#tmb-showAll', '#topMessageBox').off('click').on('click', function (e) {
        $.each($('#tabcontrol1 li .groupOption input'), function (n, option) {
          $(option).prop('checked', true);
        });

        $('#topMessageBox').slideUp();
        $('.group:hidden').slideDown();
      });

    } else {
      $('#topMessageBox').slideUp();
    }
  },
  goToCall: function (id) {
    $("#callEditorLink_" + id).click();
  }
});

// Global variables and such stuff            
var DispatchScreen = {
  statusData: {
    "0": { "t": "Waiting since ${towbook.formatAMPM(createDate)}", "t2": "Waiting for action", "c": "#ff9900", "q": 0 },
    "1": { "t": "Dispatched {{if assets[0].driver}} to  ${assets[0].driver.name} {{/if}}as of ${towbook.formatAMPM(dispatchTime)}", "t2": "Dispatched", "c": "#39579a", "q": 0 },
    "2": { "t": "{{if assets[0].driver}}${assets[0].driver.name} {{/if}}enroute to scene ${towbook.formatAMPM(enrouteTime)}", "t2": "En Route", "c": "lightgreen", "q": 0 },
    "3": { "t": "On scene at ${towbook.formatAMPM(arrivalTime)}", "t2": "On Scene", "c": "#008000", "q": 0 },
    "4": { "t": "Towing at ${towbook.formatAMPM(towTime)}", "t2": "Towing is in progress", "c": "#3294F5", "q": 0 },
    "5": { "t": "Completed (${towbook.formatDate(completionTime)} @ ${towbook.formatAMPM(completionTime)})", "t2": "Completed", "c": "none", "q": 0 },
    "7": { "t": "Arrived at destination at (${towbook.formatDate(destinationArrivalTime)} @ ${towbook.formatAMPM(destinationArrivalTime)})", "t2": "DestinationArrival", "c": "#d700c9", "q": 0 },
    "252": { "t": "Completed (${towbook.formatDate(completionTime)} @ ${towbook.formatAMPM(completionTime)})", "to": "Completed (${towbook.formatDate(completionTime)} @ ${towbook.formatAMPM(completionTime)})", "t2": "Completed", "c": "#eaeaea", "q": 0, "id": 252 },
    "253": { "t": "Cancelled by Motor Club", "to": "Cancelled by Motor Club", "t2": "Cancelled", "c": "#777777", "q": 0, "id": 253 },
    "254": { "t": "Scheduled for action", "t2": "Waiting for action", "c": "#ff9900", "q": 0 },
    "255": { "t": "Cancelled", "t2": "Cancelled", "c": "gray", "q": 0 }
  }
};

var callsTabs;

$(document).ready(function () {
  callsTabs = new CallsTabs({
    tabControlSelector: "#tabcontrol1",
    tabGroupStatuses: {
      "atCurrent": eval($('#atCurrent').data('statusFilter')), //['0', '1', '2', '3', '4'],
      "atWaiting": ['0'],
      "atCompleted": ['5'],
      "atScheduled": ['254'],
      "atCancelled": ['255']
    }
  });

  $('#topMessageBox').on('resize', function () { CallsTabs.resizeEditorWindow(); });

  $('#toolbar .submenu-link').each(function (e) {
    $(this).on("click", function (e) {

      $('.submenu').stop(false, true).hide();
      window.clearTimeout(timeoutID);
      var submenu = $(this).next();
      var subSubMenu = $(submenu).find('a');

      if (subSubMenu != null) {
        subSubMenu.on('click', function (e) {
          $(submenu).hide(300);
        });
      }

      var offset = 0;

      submenu.css({
        position: 'absolute',
        top: ($(this).offset().top + $(this).parent().height()) + 'px',
        left: ($(this).offset().left) + 'px',
        zIndex: 1000
      });

      submenu.stop().show();

      submenu.mouseleave(function () {
        $(this).hide(300);
      });

      submenu.mouseenter(function () {
        window.clearTimeout(timeoutID);
      });
    });

    $(this).on("mouseleave", function (e) {
      timeoutID = window.setTimeout(function () { $('ul.submenu').stop(false, true).hide(); }, 250);  // just hide
    });

    return;
  });

  $('#tabcontrol1 .moreOptions').each(function (e) {
    $(this).on('click', function (e) {

      // adding server generated groups to the callTabs moreOptions menu
      var menuObj = $(this).closest('li').find('.submenu');
      var tabLink = menuObj.closest('li').find('.tabLink');
      var submenu = $(this).next();
      var showGroups = false;

      if (menuObj.find('li').length == 0) {
        var id = $(tabLink).attr('id');
        menuObj.append('<li><span>Sort options</span></li>');
        menuObj.append('<li><label><input type="radio" class="sortByCallsOnly" name="' + id + '_sortBy" value="noGroups"/><span>Do not group calls</span></label></li>');
        menuObj.append('<li><label><input type="radio" class="sortByGroups" name="' + id + '_sortBy" value="byGroups" /><span>Group calls</span></label></li>');
        menuObj.append('<div class="sortByGroupChoices" style="display: none;"></div>');

        towbook.compileTemplate('tpl-group-option', $('#tpl-group-option'));

        $.each(callsTabs.Groups, function (index, item) {
          menuObj.find('div.sortByGroupChoices').append(towbook.applyTemplate('tpl-group-option', { id: item.groupId, name: item.name }));
        });

        menuObj.find('.sortByGroups').on('click', function (e) {
          if (showGroups == false) {
            $('.entriesTable').slideUp(400, function () {
              callsTabs.updateActiveTab();
            });
            menuObj.find('.sortByGroupChoices').slideDown();
            showGroups = true;
          }
          else
            e.preventDefault();
        });

        menuObj.find('.sortByCallsOnly').on('click', function (e) {
          if (showGroups == true) {
            $('#topMessageBox').slideUp();
            menuObj.find('.sortByGroupChoices').slideUp();
            $('.entriesTable').slideUp(400, function () {
              callsTabs.updateActiveTab();
            });
            showGroups = false;
          }
          else
            e.preventDefault();
        });

        menuObj.find('input[name=groupOptions]').on('click', function (e) {
          var id = $(this).closest('li').data('id');
          var checked = $(this).is(':checked');
          var groupObj = $('.group[data-id=' + id + ']');

          // make sure that there is always a checkbox that stays checked (will always be "this" one).
          if ($('input[name=groupOptions]:checked', $('ul.TabList li.selected:visible')).length == 0) {
            e.prevenDefault;
            return false;
          }

          console.log("AC: groupOption clicked", towbook.get(callsTabs.Groups, id, 'groupId'));

          if (checked) {
            if (groupObj.find('ul.groupCallList').is(":hidden"))
              groupObj.find('ul.groupCallList').show();
            $(this).closest('li').removeClass('hidden');

            $('.group[data-id=' + id + ']', '.entriesTable').slideDown('slow', function () {

            });
            CallsTabs.checkForHiddenCalls();
          } else {
            $(this).closest('li').removeClass('collapsed').addClass('hidden');
            $('.group[data-id=' + id + ']', '.entriesTable').slideUp('slow', function () {

            });
            CallsTabs.checkForHiddenCalls();
          }
        });

        if (menuObj.closest('li').hasClass('selected'))
          menuObj.find('.sortByCallsOnly').prop('checked', true);
      }

      var offset = 0;
      submenu.css({
        position: 'absolute',
        top: ($(this).offset().top + $(this).parent().height()) + 'px',
        left: ($(this).closest('li').offset().left) + 'px',
        zIndex: 1000
      });

      if ($(submenu).is(':visible')) {
        $(submenu).hide();
        return;
      }

      $('.submenu').on('click', function (e) {
        e.stopPropagation(); //!important
      });

      submenu.stop().show();

      e.stopPropagation(); //!important

    });

    return;
  });

  (function loadMoreAuto() {

    var enabled = true;

    $('#tabContainer').scroll(function (e, forceScroll) {
      if (!callsTabs.activeTabIsCompleted() && !callsTabs.activeTabIsCancelled() && !callsTabs.activeTabType == 'SEARCHTAB')
        return;

      if ($('body').hasClass('enable-search')) {
        enabled = true;
        $('body').removeClass('enable-search');
      }

      if (!enabled)
        return;

      if (forceScroll == null)
        forceScroll = false;

      var offset = 200;

      if (forceScroll || $('#tabContent').height() - ($('#tabContainer').scrollTop() + $('#tabContainer').height()) < offset) {
        if ($('.entriesTable .call').length > 5000) {
          enabled = true;
          return;
        }
        enabled = false;

        var url;
        var dispatchEntryId = -1; //$('.entriesTable .call:last').data('id') || 1000000;
        var data = null;

        if (callsTabs.activeTabType() == 'SEARCHTAB') {
          dispatchEntryId = Math.min.apply(Math, $.map($('li.entryRow', '#tabContent'), function (item) { return $(item).data('id'); }));
          url = '/api/calls/search?startAtId=' + dispatchEntryId;
          if ($('body').hasClass('quick-search'))
            data = { "quick": $.trim($('#x-quick-search').val()) };
          else if ($('body').hasClass('specific-search'))
            data = $('#getResults').attr('search-query');
          else
            return;
        }
        else {
          var companyId = $('body').data('companyId');
          if (companyId == "" || companyId == "all" || companyId == undefined)
            companyId = null;

          if (callsTabs.activeTabIsCompleted()) {
            dispatchEntryId = Math.min.apply(Math, $.map($('li.entryRow', '#tabContent'), function (call) { return $(call).data('id') }));
            if (dispatchEntryId == Infinity)
              dispatchEntryId = 2147483647; // 21474783647 = max value of int in SQL
            url = '/api/calls/completed?startAtId=' + dispatchEntryId + (companyId != null ? "&companyId=" + companyId : "");
          }
          else if (callsTabs.activeTabIsCancelled()) {
            dispatchEntryId = Math.min.apply(Math, $.map($('li.entryRow', '#tabContent'), function (call) { return $(call).data('id') }));;
            if (dispatchEntryId == Infinity)
              dispatchEntryId = 2147483647; // 21474783647 = max value of int in SQL
            url = '/api/calls/cancelled?startAtId=' + dispatchEntryId + (companyId != null ? "&companyId=" + companyId : "");
          }
          else
            return;
        }

        $('#tabFetchingMore').show();

        var currentPageNumber = Math.max.apply(Math, $.map($('li.entryRow', '#tabContent'), function (item) { return $(item).data('pageNumber'); })) + 1;
        if (currentPageNumber < 0) currentPageNumber = 1;
        url += "&page=" + currentPageNumber;

        $.ajax({
          url: url,
          data: data,
          cache: false
        }).done(function (data, status, xhr) {
          console.log("loaded data!");
          $('#tabFetchingMore').hide();
          if (data == null || data.length == 0) {
            if ($('body').hasClass('quick-search'))
              $('body').removeClass('quick-search');
            if ($('body').hasClass('specific-search'))
              $('body').removeClass('specific-search');
            enabled = false;
            return;
          }

          $.each(data, function (i, c) {
                        data.pageNumber = currentPageNumber;
            var $callTr = CallsTabs.getDispatchEntryHtml(c);
                        if ($callTr) {
                            $callTr.data("pageNumber", data.pageNumber);
            $('.entriesTable').append($callTr);
            CallsTabs.showLateCalls(c);
            $callTr.tbkBehav();
                        }
          });

          if (data.length > 0 && forceScroll == true) {
            $('#tabNoResults', callsTabs.$tabControl).hide();
            $('#tabContent', callsTabs.$tabControl).fadeIn('slow');
          }

          enabled = true;
        });
      }
    });
  })();



  if ($('#tabcontrol1').hasClass('x-has-accounting'))
    callsTabs.options.accountingProviderEnabled = true;


  callsTabs.options.faxingEnabled = $('#tabcontrol1').hasClass('x-has-faxing');


  callsTabs.initActiveTab($('#atCurrent'), _allCurrentDispatchEntries);

  $('#tabcontrol1 .tabLink').click(function (e) {
    if ($('.moreOptions.inMotion', '#tabcontrol1').length == 0) {
      $('body').addClass('enable-search');
      callsTabs.changeActiveTab($(this));
    }
  });

  $('.moreOptions').tipsy({ fade: true, gravity: 'n' });

  $('#tabcontrol1 .x-change-company a').click(function () {
    console.log('CHANGE');

    $('#tabcontrol1 .x-change-company.showing').removeClass('showing');
    $(this).closest('li').addClass('showing');

    $('body').data('companyId', $(this).data('companyId'));

    if (callsTabs.activeTabType() != 'SEARCHTAB')
      callsTabs.changeActiveTab($('.TabList .selected a'), true);
  });

  if ((navigator.userAgent.match(/iPhone/i)) || (navigator.userAgent.match(/iPod/i)) || (navigator.userAgent.match(/iPad/i)) || (navigator.userAgent.match(/Android/i))) {
    // don't use custom resize code on touch devices...
  } else {
    var throttleTimeout;
    var o = $('#tabContainer, #editorWrapper');
    //        var clh = ($('#colorLegend').outerHeight() || 0) +10;
    var clh = ($('#colorLegend').outerHeight() || 0) + 10 + ($('#callRequestsBar').is(":visible") ? $('#callRequestsBar').height() || 0 : 0);

    $(o).height((($(window).height()) - o.offset().top - clh));
    $('#editor').height((($(window).height()) - o.offset().top - clh));

    $(window).bind('resize', function () {
      CallsTabs.resizeEditorWindow();
    });
  }

  return;
});

var loadAjaxCurrentUrl;

function loadAjax(href) {
  if (typeof href == 'undefined') {
    href = loadAjaxCurrentUrl;
  }

  loadAjaxCurrentUrl = href;
  setLoadingMessage('#editor', 'Loading...');

  $('html').removeClass('full');

  $.ajax({
    url: href,
    context: $('#editor_ajaxView'),
    cache: false
  }).done(function (data, status, xhr) {
    var ajLocation = xhr.getResponseHeader('X-Twbk-Location');

    if (ajLocation.match(/^\/Security\/Login.aspx/)) {
      window.location = ajLocation;
      return;
    }

    $(this).html(data);
    $(this).css({ "display": "block" });
    $('#editor_noCallSelectedView').hide();
    CallsTabs.resizeEditorWindow();

    var myactive = $('.active');
    if (myactive != null && myactive.length > 0)
      myactive[0].scrollIntoView(false);
    $('.ui-state-active', '#callType').prev().focus();

    unsetLoadingMessage();
  });
}