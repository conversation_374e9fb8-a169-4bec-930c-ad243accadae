// Variables
$base-font: 13px 'Open Sans', sans-serif;

// Responsive breakpoints
$break-1: 750px;
$break-2: 900px;
$break-3: 1400px;
$break-4: 1900px;

// Responsive height breakpoints
$h-break-1: 710px;
$h-break-2: 810px;
$h-break-3: 910px;
$h-break-4: 1010px;


// Card View (Default)
html {
  overflow: hidden;
}
#toolbar {
  border-bottom: solid 1px #d2d2d2;

  ul {
    list-style-type: none;
    margin: 0;
    padding: 0;
    overflow: visible;

    li {
      display: inline;
      padding: 0px;
      overflow: visible;
      margin-right: 1px;
    }

    li * {
      color: #2B75BE !important;
    }

    .submenu {
      display: none;
      color: #333333;
      list-style: none;
      z-index: 99999999;
      border: solid 1px #aaa;

      li {
        float: none;
        margin: 0;
      }

      a {
        background: #f6f6f6;
        width: 300px;

        &:hover {
          background: #eaeaea;
        }
      }
    }
  }

  div.submenu {
    background: #f6f6f6;
    max-height: 500px;
    overflow-y: scroll;

    
  }

  #company-filter-field {
    display: block;
    width: 100%;
    height: 40px;
    padding: 10px;
  }
}

#pageHolder-title { 
  display: none 
}
#contents {
  padding: 0 !important;
  background: white;  
}
#tabContainer {
  border: 0;
  overflow: scroll;
}
.tabContainer-shrink {
  display: inline-block;
  width: 50%;

  .entriesHeader, 
  .entriesTable {
    width: 204%;
  }
}
#editorWrapper { 
  display: none;
  width: 50%;
  position: absolute;
  top: 0px;
  right: 0px;
  overflow-y: scroll;
  overflow-x: hidden;

  .ajax-view {
    padding-left: 10px;
    padding-right: 7px;
    padding-bottom: 10px;

    h2 {
      margin: 0 0 4px 0;
      border: solid 1px #d2d2d2;
      background: #e0e0e0;
      color: #585858;
      border-radius: 0px;
      font-weight: normal;
      font: 15px "Segoe UI", "Open Sans", sans-serif !important;
    }
  }
  #wrapper {
    h2 {
      margin: 0 0 4px 0;
      border: solid 1px #d2d2d2;
      background: #e0e0e0;
      color: #585858;
      border-radius: 0px;
      font: 15px "Segoe UI", "Open Sans", sans-serif !important;
    }

    #callEditor.callEditor {
      padding-top: 70px;
    }
  }

  #assets .description-heading a.flat-ui-button span,
  #assets .driver-truck td
  {
    line-height: 28px !important;
  }
  a.flat-ui-button span {
    line-height: 32px !important;
  }

  #expand-editor,
  #collapse-editor {
    padding: 4px 6px;
    font-size: 17px;
    color: #777;

    &:hover {
      background: #ccc;
    }

    &:active {
      background: #bbb;
    }
  }
}

#editorWrapper.expanded {
  display: none;
  position: fixed;
  top: 10%;
  left: 0;
  right: 0;
  bottom: 10%;
  width: 100%;
  height: auto !important;
  background: #fff;
  border: solid 1px #666;
  border-left: none;
  border-right: none;
  -webkit-box-shadow: 1px 2px 20px 0 #444;
  -moz-box-shadow: 1px 2px 20px 0 #444;
  box-shadow: 1px 2px 20px 0 #444;
  z-index: 900;
  overflow: hidden;

  #editor {
    height: 100%;
    margin: auto 10%;
    overflow-x: hidden;
    overflow-y: scroll;
  }

  #editor_ajaxView {
    display: none;
    background: #fff !important;
    border-bottom: solid 1px #afafaf;
    margin-bottom: 57px;
  }

  #editor_noCallSelectedView {
    display: none;
    text-align: center;
    padding-top: 55px;
  }

  .ajax-view h2 {
    width: 100% !important;
    margin: 0 0 4px 0;
    background: #fff;
    color: #585858;
    border: none;
    border-radius: 0px;
    font-weight: normal;
    font: 20px "Segoe UI", "Open Sans", sans-serif !important;
  }

  #wrapper {
    display: block;
    position: relative;

    h2 {
      width: 100% !important;
      margin: 0 0 4px 0;
      background: #fff;
      color: #585858;
      border: none;
      border-radius: 0px;
      font: 20px "Segoe UI", "Open Sans", sans-serif !important;
    }

    .callHeaderWrapper {
      position: relative;
      width: 100% !important;
      max-width: unset !important;
    }
  }

  .navigation-row, #viewFiles, #viewPhotos {
    position: fixed;
    bottom: 10% !important;
    padding: 9px 9px 9px 9px !important;
    background: #fff;
    margin-bottom: 1px;
    width: auto !important;
  }

  #viewPhotos.isModern {
    position: relative;
    margin-bottom: 30px !important;

    .navigation-row {
      position: fixed !important;
      width: auto !important;
      margin-bottom: 1px !important;
    }
  }
}

#overlay2 {
	display: none;
	position: fixed;
	top: 59px;
	left: 0;
	right: 0;
	bottom: 0;
	opacity: .3;
	background: black;
	z-index: 3;
}

#callHeader .callHeaderWrapper {
  max-width: unset !important;
}

.editorWrapper-grow {
  display: inline-block !important;
}

.ui-timepicker-wrapper {
  z-index: 991;
}

@media only screen and (min-width: 1201px) and (max-width: 1500px) {

  .callEditor *, .ui-widget {
    font-size: 12px !important;
  }

  .callEditor table tbody tr td.property,
  #divETA .caption,
  #prioritySet .caption,
  #towFromDistance,
  #towToDistance,
  .nextStepDistance
  {
    display: none !important;
  }

  #towFromContainer,
  #towTo-address,
  #towTo-impound
  {
    margin-right: 0px !important;                
  }

  .callEditor table tbody tr td.value {
    padding-left: 7px !important;
    padding-right: 14px !important;
    min-width: 534px !important;
  }

  .callEditor .title {
    display: block !important;
    padding-bottom: 0px !important;
    line-height: 15px;
  }

  a.flat-ui-button span {
    line-height: 32px !important;
  }

  .driver-truck td {
    line-height: inherit !important;
  }

  .callEditor .invoiceItems .row-headings td.total-heading {
    width: 80px !important;
  }

  label.caption {
    line-height: 32px !important;
  }

  #rowReason .split-row-left-right {
    width: 236px !important;
  }
}   

.list-header {
  float: left; 
  clear: both;
  margin-bottom: 8px;
  padding: 5px 10px;
  width: 170px;
  font: bold 11px "Segoe UI", "Open Sans", sans-serif !important;
  text-transform: uppercase;
}
.tab-list {
  float: left;
  clear: both;
  margin-bottom: 8px;
  width: 170px;
  border-bottom: solid 1px #d2d2d2;

  li {
    display: inline-block;
    border-top: solid 1px #fff;
    border-bottom: solid 1px #fff;
    width: 100%;

    &:hover {
      background: #f6f6f6;
      background: #dadada;
      border-top: solid 1px #f6f6f6;
      border-bottom: solid 1px #f6f6f6;
      color: #777 !important;
    }

    a {
      display: inline-block;
      padding: 7px 10px;
      color: #777;
      font: 15px "Segoe UI", "Open Sans", sans-serif !important;
      text-transform: none !important;
      text-decoration: none;

      span {
        color: #b7b7b7;
      }
    }
  }

  .selected,
  .showing {
    background: #f6f6f6;
    color: #2B75BE;
    font: 15px "Segoe UI", "Open Sans", sans-serif !important;
    text-transform: none !important;
    border-top: solid 1px #d2d2d2;
    border-bottom: solid 1px #d2d2d2;

    &:hover {
      background: #dadada !important;
      border-top: solid 1px #d2d2d2;
      border-bottom: solid 1px #d2d2d2;
      color: #585858 !important;
    }

    span {
      color: #a2a2a2;
    }
  }

  li:last-child,
  #atCancelled,
  .searchTab {
    border-bottom: 0 !important;
  }
}

.tab-list.company-filter {
  max-height: 200px;
  overflow-x: hidden;
  overflow-y: auto;

  li {
    white-space: nowrap;
    max-width: 150vw;

    &.all {
      a {
        max-width: 150vw !important;
      }
    }

    a {
      width: 88%;
      text-overflow: ellipsis;
      overflow: hidden;
    }

    &.showing {
      background: #f6f6f6;

    .fa {
        visibility: visible;
        color: #a2a2a2;
        font-size: 14px;
        vertical-align: top;
        line-height: 36px;
      }
    }

    .fa {
      visibility: hidden;
      float: right;
      padding-right: 5px;
      width: 12%;
    }
  }

  @media (min-height: $h-break-1) {
    max-height: 250px;
  }

  @media (min-height: $h-break-2) {
    max-height: 350px;
  }

  @media (min-height: $h-break-3) {
    max-height: 450px;
  }

  @media (min-height: $h-break-4) {
    max-height: 550px;
  }
}


  .drivers-list {
    clear: both;
    float: left;
    width: 170px;
    padding: 0px 0px 6px 0;
    border-bottom: solid 1px #d2d2d2;

    .driver {
      display: inline-block;
      width: 170px;
      padding: 2px 10px;
      color: #777;
      font: 13px "Segoe UI", "Open Sans", sans-serif !important;

      &:hover {
        background: #f6f6f6;
        color: #777 !important;
      }

      span {
        color: #b7b7b7;
      }
    }
  }

  .tabControl {
    position: relative;
  }

  #tabWrapper {
    position: relative;
    margin: 7px 0 0px 170px;
  }

  #tabContent {
    display: block !important;
  }

  .navigation-row,
  #viewFiles,
  #viewPhotos {
    padding: 9px 9px 15px 9px;
    background: rgb(249, 249, 249);
    background: rgba(249, 249, 249, 0.4);
  }

  #viewPhotos.isModern {
    position: relative;

    .navigation-row {
      position: fixed !important;
      width: auto !important;
      margin-bottom: 50px !important;
    }
  }

  .submenu2 {
    display: none;
    position: absolute !important;
    top: 26px;
    left: 0;
    background: #fff;
    border: solid 1px #2b75be;
    padding: 0 4px;
    z-index: 899; // !! keep below 900 (blockUI overlay)
    li {
      display: block;

      a {
        display: block !important;
        padding: 4px 9px 7px 4px;
        color: #2B75BE;
        text-decoration: none;
        white-space: nowrap;

        &:hover {
          color: #bbb;
        }

        i {
          margin-right: 7px;
          width: 14px;
          text-align: center;
        }
      }
    }
  }


  #change-view-menu {
    display: none;
    position: absolute !important;
    top: -8px;
    right: 22px;
    background: #fff;
    border: solid 1px #2b75be;
    z-index: 1000;

    .fa {
      padding-right: 6px;
      visibility: hidden;
    }

    .fa.card-view {
      visibility: visible;
    }

    li {
      display: block !important;

      a {
        display: block !important;
        font-size: 15px;
        padding: 6px 8px;
        color: #2B75BE;
        text-decoration: none;
        white-space: nowrap;

        &:hover {
          background: #efefef;
        }
      }
    }
  }

  .sp-container button {
    margin-top: 3px;
  }

  .sp-cancel {
    display: none;
  }

  .eta {
    color: #666 !important;
    font: $base-font;
    font-size: 11px !important;
    font-weight: bold !important;
    padding: 2px 4px;
    /*-webkit-box-shadow: 1px 2px 10px 0 #9E9E9E;
  -moz-box-shadow: 1px 2px 10px 0 #9E9E9E;
  box-shadow: 1px 2px 10px 0 #9E9E9E;*/
  }

  .late .eta {
    background-color: yellow;
  }

  .arrived .eta {
    background-color: #f1f1f1;
  }

  #settings-form {
    position: absolute;
    display: none;
    margin-left: auto;
    width: 440px;
    top: -8px;
    right: 5px;
    height: 550px;
    z-index: 10000;
    background: #eee;
    border: solid 1px #fff;
    -webkit-box-shadow: 1px 2px 14px 0 #777;
    -moz-box-shadow: 1px 2px 14px 0 #777;
    box-shadow: 1px 2px 14px 0 #777;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;

    .container {
      padding: 40px 10px 10px 10px;
      height: 100%;
    }

    .top-bar {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 37px;
      background: #2B75BE;
      color: #fff;
      font-size: 17px;
      padding: 8px 10px;

      .close-btn {
        position: absolute;
        top: 0;
        right: 0;
        height: 35px;
        width: 35px;
        text-align: center;
        padding-top: 8px;
        cursor: pointer;

        &:hover {
          background: #24609C;
        }

        &:active {
          background: #2B75BE;
        }
      }
    }

    .col-reset {
      display: block;
      margin-top: 10px !important;
      width: 100%;
    }

    .settings-form-btns {
      position: absolute;
      bottom: 10px;
      right: 10px;

      #save-settings-btn {
        width: 120px !important;
        margin-right: 8px !important;
        min-width: 0 !important;
      }

      #cancel-settings-btn {
        width: 90px !important;
        min-width: 0 !important;
      }
    }

    .ui-state-highlight {
      background: #DADEE2;
    }


    header {
      font-size: 16px;
      font-weight: bold;
      padding: 10px 0;
      color: #184673;
    }

    ul {
      width: 165px;
      background: #eee;
      border: solid 1px #184673;
      padding: 2px 4px;
      min-height: 15px;

      li {
        display: block !important;
        border: solid 1px #184673;
        margin: 2px 0;
        padding: 2px 4px;
        background: #68819A;
        cursor: default;
        color: #fff;
        min-height: 23px !important;
        height: auto !important;
        width: 155px !important;

        &:hover {
          background: #426282;
        }

        .li-header {
          position: relative;
          padding-right: 14px;
        }

        .li-chevron {
          position: absolute;
          top: 0;
          right: 0;
          margin-top: 2px;
          font-size: 12px;
        }

        .li-input-wrapper {
          display: none;
          margin: 6px 0 4px 0;
          background: #eee;
          border: solid 1px #184673;
          padding: 5px;
          color: #000;

          label {
            display: inline-block;
            margin-right: 4px;
            width: 36px;
          }

          .sp-replacer {
            margin: 7px 6px 6px 0 !important;
          }

          input[type='checkbox'] {
            margin-left: 0;
          }
        }
      }
    }

    .used-columns-section {
      overflow-y: scroll;
      height: 100%;
      width: 192px;
    }

    .available-columns-section {
      float: right;
      overflow-y: scroll;
      padding-right: 10px;
      height: 447px;
    }
  }

  .entriesHeader *,
  .entriesTable * {
    position: relative;
    box-sizing: border-box;
    font: $base-font;
  }

  .entriesTable .fa {
    font: 14px "Font Awesome 5 Pro"
  }

.entriesHeader,
.entriesTable {
    position: relative;
    width: 100%;
    box-sizing: border-box;
    border-collapse: collapse;
    background: #fff;
    font: $base-font;
    color: #313131;

    .entryRow {
        border-bottom: solid 1px #eee;
        cursor: pointer;
        height: auto;

        &:hover {
            background: #f8f8f8;

            .right-bar {

                ul {
                    display: block;
                }
            }
        }

        .left-bar {
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            min-width: 68px;
            padding: 2px;

            ul {
                height: 100%;
                padding: 4px 5px;
            }

            .call-number {
                color: #909090;

                @-moz-keyframes icon-bounce {
                    0%, 35%, 65%, 100% {
                        -moz-transform: translateY(0);
                        transform: translateY(0);
                    }

                    40% {
                        -moz-transform: translateY(-2px);
                        transform: translateY(-2px);
                    }

                    45% {
                        -moz-transform: translateY(1px);
                        transform: translateY(1px);
                    }

                    50% {
                        -moz-transform: translateY(-1px);
                        transform: translateY(-1px);
                    }
                }

                @-webkit-keyframes icon-bounce {
                    0%, 35%, 65%, 100% {
                        -webkit-transform: translateY(0);
                        transform: translateY(0);
                    }

                    40% {
                        -webkit-transform: translateY(-2px);
                        transform: translateY(-2px);
                    }

                    45% {
                        -webkit-transform: translateY(1px);
                        transform: translateY(1px);
                    }

                    50% {
                        -webkit-transform: translateY(-1px);
                        transform: translateY(-1px);
                    }
                }

                @keyframes icon-bounce {
                    0%, 35%, 65%, 100% {
                        -moz-transform: translateY(0);
                        -ms-transform: translateY(0);
                        -webkit-transform: translateY(0);
                        transform: translateY(0);
                    }

                    40% {
                        -moz-transform: translateY(-2px);
                        -ms-transform: translateY(-2px);
                        -webkit-transform: translateY(-2px);
                        transform: translateY(-2px);
                    }

                    45% {
                        -moz-transform: translateY(1px);
                        -ms-transform: translateY(1px);
                        -webkit-transform: translateY(1px);
                        transform: translateY(1px);
                    }

                    50% {
                        -moz-transform: translateY(-1px);
                        -ms-transform: translateY(-1px);
                        -webkit-transform: translateY(-1px);
                        transform: translateY(-1px);
                    }
                }


                .fa {
                    font-family: "Font Awesome 5 Pro";
                    font-size: 1.0em;
                    color: #909090;
                }

                .fab, .fas, .far, .fal {
                    font-family: "Font Awesome 5 Pro";
                    font-size: 1.0em;
                    color: #909090;
                    line-height: 1;
                    font-weight: 900;
                }

                .chat-icon {
                    display: none;
                    position: relative;

                    .fa-comment {
                        color: #aaaaaa;
                        font-weight: 500;

                        .count {
                            display: none;
                            position: absolute;
                            top: -1px;
                            left: 4px;
                            height: 13px;
                            width: 100%;
                            font-size: 7px;
                            text-align: center;
                            color: red;
                            font-weight: bold;
                        }
                    }

                    .has-messages {
                        font-weight: 500;
                        color: #005EB1;
                    }

                    .unread-bounce {
                        -webkit-animation: icon-bounce 3s infinite;
                        animation: icon-bounce 3s infinite;
                        font-weight: 900;
                        /*color: #63caf3;*/
                        .count {
                            display: none;

                            .fa-circle {
                                color: red;
                            }
                        }
                    }
                }

                .x-towbook-lock {

                    .fa-lock {
                        display: none;
                        width: 15px !important;
                        color: #dadada;
                    }

                    .fa-lock-open {
                        display: inline-block;
                        width: 15px !important;
                        color: #dadada;
                    }
                }

                .x-towbook-lock.disabled {

                    .fa-lock {
                        display: none;
                    }

                    .fa-lock-open {
                        display: none;
                        color: #aaaaaa;
                    }
                }

                .x-towbook-lock.disabled.show-new {
                    .fa-lock {
                        display: inline-block;
                    }
                }

                .x-towbook-lock.locked {
                    .fa-lock {
                        display: inline-block;
                        color: #aaaaaa;
                    }

                    .fa-lock-open {
                        display: none;
                    }
                }

                .x-towbook-lock:hover .fa-lock {
                    display: inline-block;
                    color: #2B75BE;
                }

                .x-towbook-lock:hover .fa-lock-open {
                    display: none;
                    color: #2B75BE;
                }

                .x-towbook-lock.locked:hover .fa-lock {
                    display: none;
                    color: #2B75BE;
                }

                .x-towbook-lock.locked:hover .fa-lock-open {
                    display: inline-block;
                    color: #2B75BE;
                }

                .x-towbook-lock.disabled.locked:hover .fa-lock {
                    display: inline-block;
                    color: #646464;
                }

                .x-towbook-lock.disabled.locked:hover .fa-lock-open {
                    display: none;
                }


                .x-towbook-audit {

                    .fa-user-check {
                        display: none;
                        color: #dadada;
                    }

                    .fa-user-slash {
                        display: inline-block;
                        color: #dadada;
                    }
                }

                .x-towbook-audit.disabled {

                    .fa-user-check {
                        display: none;
                    }

                    .fa-user-slash {
                        display: none;
                        color: #aaaaaa;
                    }
                }

                .x-towbook-audit.disabled.show-new {
                    .fa-user-check {
                        display: inline-block;
                    }
                }

                .x-towbook-audit.audited {
                    .fa-user-check {
                        display: inline-block;
                        color: #aaaaaa;
                    }

                    .fa-user-slash {
                        display: none;
                    }
                }

                .x-towbook-audit:hover .fa-user-check {
                    display: inline-block;
                    color: #2B75BE;
                }

                .x-towbook-audit:hover .fa-user-slash {
                    display: none;
                    color: #2B75BE;
                }

                .x-towbook-audit.audited:hover .fa-user-check {
                    display: none;
                    color: #2B75BE;
                }

                .x-towbook-audit.audited:hover .fa-user-slash {
                    display: inline-block;
                    color: #2B75BE;
                }

                .x-towbook-audit.disabled.audited:hover .fa-user-check {
                    display: inline-block;
                    color: #646464;
                }

                .x-towbook-audit.disabled.audited:hover .fa-user-slash {
                    display: none;
                }


                .driver-accept-reject-icon {
                    display: none;
                }

                .driver-accept-reject-icon.no-response {
                    display: inline-block;

                    .fa-share-square {
                        display: none;
                    }

                    .fa-thumbs-up,
                    .fa-thumbs-down {
                        display: none;
                    }
                }

                .driver-accept-reject-icon.accepted {
                    display: inline-block;

                    .fa-thumbs-up {
                        display: inline-block;
                        color: #39579a;
                    }

                    .fa-share-square,
                    .fa-thumbs-down {
                        display: none;
                    }
                }

                .driver-accept-reject-icon.rejected {
                    display: inline-block;

                    .fa-thumbs-down {
                        display: inline-block;
                        color: red;
                        -webkit-animation: icon-bounce 3s infinite;
                        animation: icon-bounce 3s infinite;
                    }

                    .fa-share-square,
                    .fa-thumbs-up {
                        display: none;
                    }
                }
            }

            .invoice-number {
                color: #888;
                font-size: 10px !important;
                padding-top: 1px;
            }
        }

        .content {
            margin-left: 108px;
            padding: 0px 4px;

            .content-top {

                .header {
                    padding: 4px;
                    font: $base-font;
                    font-size: 12px;

                    @media (min-width: $break-1) {
                        display: inline-block;
                        vertical-align: top;
                        width: 23%;
                    }

                    .big-text {
                        font-size: 16px;
                        color: #005EB1;
                    }

                    .text {
                        font-size: 12px;
                        color: #8b8b8b;
                    }

                    .scheduled-eta-container {
                        padding: 2px 3px;
                    }
                }

                .details1 {
                    padding: 4px;
                    -webkit-columns: 4 250px;
                    -moz-columns: 4 250px;
                    columns: 4 250px;

                    @media (min-width: $break-1) {
                        display: inline-block;
                        vertical-align: top;
                        width: 76%;
                    }

                    li {
                        padding-top: 1px;
                        font: $base-font;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;

                        @media (min-width: $break-1) {
                            padding: 1px 4px;
                        }

                        .title {
                            position: static;
                            display: inline;
                            color: #9E9E9E;
                            font-size: 13px;
                            margin-right: 8px;
                            font: $base-font;
                        }

                        .text {
                            position: static;
                            display: inline;
                            font: $base-font;
                            word-wrap: break-word;
                        }
                    }

                    .li-wrap {
                        white-space: normal;
                    }
                }
            }

            .content-bottom {
                display: none;
                top: 0;
                left: 0;

                .details2 {
                    padding: 4px;
                    white-space: nowrap;

                    .title {
                        display: inline;
                        color: #9E9E9E;
                        font-size: 13px;
                        margin-right: 8px;
                        font: $base-font;

                        @media (min-width: $break-1) {
                            display: inline-block;
                            width: 110px;
                            white-space: nowrap;
                            overflow: hidden;
                            text-overflow: ellipsis;
                        }
                    }

                    .text {
                        display: inline;
                        white-space: normal;
                        font: $base-font;
                        max-height: 200px;
                        overflow-y: auto;


                        @media (min-width: $break-1) {
                            display: inline-block;
                            padding-right: 35px;
                            width: 80%;
                            vertical-align: top;
                        }
                    }
                }

                .link-list {
                    margin-right: 24px;

                    > li {
                        display: inline-block;
                    }

                    li {
                        color: #2B75BE; // #707070;
                        &:hover {
                            color: #bbb; //color: #003D73;
                        }

                        i {
                            font-size: 13px;
                            margin-right: 6px;
                            font-family: 'Font Awesome 5 Pro';
                        }

                        a, div {
                            display: inline-block;
                            position: relative;
                            padding: 4px 9px 7px 4px;
                            text-decoration: none;
                            color: inherit;
                            font-size: 12px;
                        }

                        .fa-chevron-right {
                            display: inline-block;
                            visibility: hidden;
                            line-height: 18px;
                            position: absolute;
                            font-size: 14px;
                            float: right;
                            right: -12px;
                        }

                        .new-feature-tag {
                            font-weight: bold;
                            margin-left: 3px;
                            margin-top: 0px;
                            font-family: Calibri, Arial;
                            font-size: 11px;
                        }
                    }

                    li.empty {
                        display: none;
                    }

                    li.sample {
                        color: #bbb;
                        padding-right: 8px;

                        &:hover {
                            color: #bbb; //color: #003D73;
                            .fa-chevron-right {
                                visibility: visible;
                                color: #2B75BE;
                            }
                        }

                        .fa-chevron-right {
                            cursor: pointer;
                        }

                        .new-feature-tag {
                            color: #777;
                        }
                    }

                    li.disabled {
                        color: #bbb;
                        padding-right: 8px;

                        &:hover {
                            .fa-chevron-right {
                                //visibility: visible;
                                color: #2B75BE;
                            }
                        }

                        a {
                            &:hover {
                                cursor: default;
                            }
                        }
                        //.fa-chevron-right {
                        //  cursor: pointer;
                        //}
                    }
                }
            }

            .star-ratings-css {
                unicode-bidi: bidi-override;
                color: #c5c5c5;
                position: relative;
                text-shadow: 0px 1px 0 #a2a2a2;
                width: calc(1em * 5);
                display: inline-block;
                padding: 0;
                overflow: hidden;
                top: 2px;

                span {
                    font-size: 14px;
                    line-height: 14px;
                    letter-spacing: 0.1em;
                }

                .star-ratings-css-top {
                    color: #ffaa00;
                    padding: 0;
                    position: absolute;
                    z-index: 1;
                    display: flex;
                    overflow: hidden;
                    top: 0;
                    left: 0;
                }

                .star-ratings-css-bottom {
                    padding: 0;
                    display: flex;
                    z-index: 0;
                }
            }
        }

        .right-bar {
            position: absolute;
            top: 0;
            left: 0;
            bottom: 0;
            width: 32px;
            overflow: visible;

            ul {
                display: none;
                padding: 4px 8px;
                white-space: nowrap;
                float: right;
                background: rgb(248,248,248);

                li {
                    margin-right: 2px;
                    text-align: right;
                    display: inline-block;

                    a {
                        color: #2D7FC7;
                        padding: 0px;

                        &:hover {
                            color: #aaa;
                        }

                        &:active {
                            color: #777;
                        }
                    }
                }

                .quick-actions i {
                    font-family: "Font Awesome 5 Pro";
                    font-size: 17px;
                }
            }
        }

        .footer {
            padding: 4px;
        }
    }

    .far, .fab, .fal {
        font-family: "Font Awesome 5 Pro" !important;
    }



    .scheduled-eta-container {
        color: #666;
        font-weight: bold;
        background-color: rgb(255, 153, 0);
        background-color: rgba(255, 153, 0, .2);
        border-radius: 3px;

        .far:before, .fab:before, .fal:before {
            padding-right: 3px;
        }

        .scheduled-eta-duration {
            margin-left: 3px;
        }
    }
}

  #entriesHeaderWrapper {
    display: none;
    height: 27px;
    overflow: hidden;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;

    .entriesHeader {
      display: none;

      .entryRow {
        background: #fff;
        border: 0;
        color: #585858;
        font-size: 14px;

        &:hover {
          background: #fff;

          .right-bar {
            display: none;
          }
        }

        .left-bar {
          padding-top: 1px;

          .header-text {
            margin: 4px 0 4px 5px;
          }
        }

        .content {

          .content-top {

            .header {
              padding-top: 5px;

              &:hover {
                .header-text .header-btn-wrapper .filter-btn {
                  display: inline-block;
                }
              }
            }

            .header.col-tiny {
              min-width: 50px;
              max-width: 90px;
              width: 60px;
            }

            .header.col-narrow {
              min-width: 70px;
              max-width: 120px;
              width: 90px;
            }

            .header.col-medium {
              min-width: 90px;
              max-width: 180px;
              width: 120px;
            }

            .header.col-wide {
              min-width: 100px;
              width: 170px;
            }

            .header.col-x-wide {
              min-width: 110px;
              width: 220px;
            }

            .details1 {

              li {
                overflow: visible;
              }
            }
          }
        }

        .header-text {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          font: bold 11px "Segoe UI", "Open Sans", sans-serif !important;
          text-transform: uppercase;
          height: 17px;

          &:hover {
            color: #2B75BE;

            .header-btn-wrapper .filter-btn {
              display: inline-block;
            }
          }

          .header-btn-wrapper {
            position: absolute !important;
            top: 0;
            right: 0;
            width: 50px;
            height: 24px;
            text-align: right;

            .header-btn {
              margin-left: -5px;
              width: 17px;
              text-align: center;

              i {
                padding: 2px;
                font: normal normal normal 14px/1 "Font Awesome 5 Pro" !important;
                background: white;
                color: #2B75BE;

                &:hover {
                  color: #1D5E9E;
                }
              }
            }

            .sort-btn {
              display: none;
            }

            .filter-btn {
              display: none;
            }
          }
        }

        .header-filter-wrapper {
          .header-filter {
            display: none;
            margin-top: 4px;

            .w-filter {
              font-size: 12px;
              padding: 2px 4px;
              width: 100%;
              border: solid 1px #D3D3D3;
              height: 25px;
            }

            select {
              width: 100%;
              font-size: 12px;

              option {
                font-size: 12px;
              }
            }

            .ui-combobox {
              width: 100% !important;

              input {
                width: 100% !important;
                padding: 3px 4px !important;
                font: 12px 'Open Sans', sans-serif !important;
                border-radius: 0px !important;
              }

              a {
                height: 20px !important;
                width: 20px !important;
                background: #fff !important;
                margin-top: -4px !important;
                top: 6px !important;
              }
            }
          }
        }
      }
    }
  }

  .ui-menu-item {
    a {
      padding: 3px 4px !important;
      -webkit-touch-callout: none;
      -webkit-user-select: none;
      -khtml-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
      user-select: none;
    }
  }

  .selected {
  }

  .unselected {
  }

  .active {
  }

  .text-wrap {
    white-space: normal !important;
    overflow: visible !important;
    word-wrap: break-word !important;
  }
