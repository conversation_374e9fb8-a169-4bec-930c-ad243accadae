.one-line-grid-view #tabContent {
  display: inline-block !important; }

.one-line-grid-view .eta {
  padding-left: 3px !important; }

.one-line-grid-view .entriesHeader .entryRow:hover .right-bar,
.one-line-grid-view .entriesTable .entryRow:hover .right-bar {
  background: #F8F8F8; }

.one-line-grid-view .entriesHeader .entryRow .left-bar,
.one-line-grid-view .entriesTable .entryRow .left-bar {
  padding: 1px 2px; }
  .one-line-grid-view .entriesHeader .entryRow .left-bar ul,
  .one-line-grid-view .entriesTable .entryRow .left-bar ul {
    padding: 3px 5px; }
  .one-line-grid-view .entriesHeader .entryRow .left-bar .invoice-number,
  .one-line-grid-view .entriesTable .entryRow .left-bar .invoice-number {
    display: none; }

.one-line-grid-view .entriesHeader .entryRow .content .content-top,
.one-line-grid-view .entriesTable .entryRow .content .content-top {
  white-space: nowrap; }
  .one-line-grid-view .entriesHeader .entryRow .content .content-top .header,
  .one-line-grid-view .entriesTable .entryRow .content .content-top .header {
    display: inline-block;
    vertical-align: top;
    width: 200px;
    white-space: normal; }
    .one-line-grid-view .entriesHeader .entryRow .content .content-top .header .big-text,
    .one-line-grid-view .entriesTable .entryRow .content .content-top .header .big-text {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      font-size: 15px; }
    .one-line-grid-view .entriesHeader .entryRow .content .content-top .header .big-text-wrap,
    .one-line-grid-view .entriesTable .entryRow .content .content-top .header .big-text-wrap {
      white-space: normal !important;
      overflow: visible !important;
      word-wrap: break-word !important; }
    .one-line-grid-view .entriesHeader .entryRow .content .content-top .header .text,
    .one-line-grid-view .entriesTable .entryRow .content .content-top .header .text {
      display: none; }
  .one-line-grid-view .entriesHeader .entryRow .content .content-top .details1,
  .one-line-grid-view .entriesTable .entryRow .content .content-top .details1 {
    display: inline-block;
    vertical-align: top; }
    .one-line-grid-view .entriesHeader .entryRow .content .content-top .details1 li,
    .one-line-grid-view .entriesTable .entryRow .content .content-top .details1 li {
      display: inline-block;
      height: 20px;
      vertical-align: top;
      overflow: visible; }
      .one-line-grid-view .entriesHeader .entryRow .content .content-top .details1 li .title,
      .one-line-grid-view .entriesTable .entryRow .content .content-top .details1 li .title {
        display: none; }
      .one-line-grid-view .entriesHeader .entryRow .content .content-top .details1 li .text,
      .one-line-grid-view .entriesTable .entryRow .content .content-top .details1 li .text {
        position: absolute;
        top: 0px;
        left: 4px;
        right: -2px;
        bottom: 0;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        padding: 1px 0px;
        word-wrap: normal; }
      .one-line-grid-view .entriesHeader .entryRow .content .content-top .details1 li .text-wrap,
      .one-line-grid-view .entriesTable .entryRow .content .content-top .details1 li .text-wrap {
        position: relative !important;
        white-space: normal !important;
        padding: 0 !important;
        word-wrap: break-word !important;
        left: 0;
        right: 0; }
    .one-line-grid-view .entriesHeader .entryRow .content .content-top .details1 .li-wrap,
    .one-line-grid-view .entriesTable .entryRow .content .content-top .details1 .li-wrap {
      height: auto;
      padding: 1px 1px 1px 4px !important; }
    .one-line-grid-view .entriesHeader .entryRow .content .content-top .details1 .col-tiny,
    .one-line-grid-view .entriesTable .entryRow .content .content-top .details1 .col-tiny {
      width: 60px; }
    .one-line-grid-view .entriesHeader .entryRow .content .content-top .details1 .col-narrow,
    .one-line-grid-view .entriesTable .entryRow .content .content-top .details1 .col-narrow {
      width: 90px; }
    .one-line-grid-view .entriesHeader .entryRow .content .content-top .details1 .col-medium,
    .one-line-grid-view .entriesTable .entryRow .content .content-top .details1 .col-medium {
      width: 120px; }
    .one-line-grid-view .entriesHeader .entryRow .content .content-top .details1 .col-wide,
    .one-line-grid-view .entriesTable .entryRow .content .content-top .details1 .col-wide {
      width: 170px; }
    .one-line-grid-view .entriesHeader .entryRow .content .content-top .details1 .col-x-wide,
    .one-line-grid-view .entriesTable .entryRow .content .content-top .details1 .col-x-wide {
      width: 220px; }

.one-line-grid-view .entriesHeader .entryRow .content .content-bottom .details2 .title,
.one-line-grid-view .entriesTable .entryRow .content .content-bottom .details2 .title {
  display: inline-block;
  width: 110px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis; }

.one-line-grid-view .entriesHeader .entryRow .content .content-bottom .details2 .text,
.one-line-grid-view .entriesTable .entryRow .content .content-bottom .details2 .text {
  display: inline-block;
  padding-right: 35px;
  width: 80%;
  vertical-align: top; }

.one-line-grid-view .entriesHeader .entryRow .right-bar,
.one-line-grid-view .entriesTable .entryRow .right-bar {
  overflow: visible; }
  .one-line-grid-view .entriesHeader .entryRow .right-bar ul,
  .one-line-grid-view .entriesTable .entryRow .right-bar ul {
    float: right;
    white-space: nowrap; }
    .one-line-grid-view .entriesHeader .entryRow .right-bar ul li,
    .one-line-grid-view .entriesTable .entryRow .right-bar ul li {
      text-align: center;
      display: inline-block;
      margin-right: 1px; }

.one-line-grid-view #entriesHeaderWrapper {
  display: block; }
  .one-line-grid-view #entriesHeaderWrapper .entriesHeader .entryRow .content .details1 li {
    height: auto; }

.one-line-grid-view #change-view-menu .fa.card-view {
  visibility: hidden; }

.one-line-grid-view #change-view-menu .fa.grid-view {
  visibility: hidden; }

.one-line-grid-view #change-view-menu .fa.one-line-grid-view {
  visibility: visible; }

.one-line-grid-view #change-view-menu .fa.classic-view {
  visibility: hidden; }

