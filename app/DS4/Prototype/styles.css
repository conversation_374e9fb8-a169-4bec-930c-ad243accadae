* {
  position: relative;
  box-sizing: border-box; }

body {
  font: 12px Open Sans;
  color: #313131; }

.item-list {
  display: table;
  width: 100%;
  border-collapse: collapse; }
  @media (min-width: 1000px) {
    .item-list {
      top: 33px; } }
  .item-list .item {
    display: table-row;
    background: #fff;
    border-bottom: solid 1px #f4f4f4;
    cursor: pointer; }
    .item-list .item:hover {
      background: #f9f9f9; }
      .item-list .item:hover .right-bar ul {
        display: block; }
    .item-list .item .left-bar,
    .item-list .item .right-bar,
    .item-list .item .header,
    .item-list .item .content {
      display: table-cell;
      vertical-align: top;
      padding: 4px; }
    .item-list .item .left-bar {
      width: 64px;
      min-width: 64px; }
      .item-list .item .left-bar ul {
        position: absolute;
        top: 2px;
        bottom: 2px;
        left: 0;
        padding: 5px; }
      .item-list .item .left-bar .status-waiting {
        border-left: solid 6px #fa3; }
      .item-list .item .left-bar .status-completed {
        border-left: solid 6px #2BD800; }
      .item-list .item .left-bar .status-dispatched {
        border-left: solid 6px #0079E0; }
      .item-list .item .left-bar .status-enroute {
        border-left: solid 6px #EA46D6; }
      .item-list .item .left-bar .status-on-scene {
        border-left: solid 6px #fa3; }
      .item-list .item .left-bar .call-number {
        color: #909090; }
    @media (min-width: 1000px) {
      .item-list .item .content {
        display: table; } }
    @media (min-width: 1000px) {
      .item-list .item .content .content-top {
        display: table-row; } }
    .item-list .item .content .content-top .header {
      width: 200px;
      min-width: 200px; }
      @media (min-width: 1000px) {
        .item-list .item .content .content-top .header {
          display: table-cell; } }
      .item-list .item .content .content-top .header .text {
        font-size: 17px;
        color: #005EB1; }
    .item-list .item .content .content-top .secondary {
      display: none; }
      @media (min-width: 1000px) {
        .item-list .item .content .content-top .secondary {
          display: table-cell !important; } }
    .item-list .item .content .content-top ul {
      display: table-cell;
      vertical-align: top;
      padding: 4px; }
      @media (min-width: 1000px) {
        .item-list .item .content .content-top ul {
          white-space: nowrap; } }
      .item-list .item .content .content-top ul li {
        display: inline-block;
        padding: 1px 5px;
        width: 179px;
        white-space: nowrap;
        overflow: hidden; }
        @media (min-width: 1000px) {
          .item-list .item .content .content-top ul li {
            display: table-cell;
            vertical-align: top;
            white-space: normal; } }
        .item-list .item .content .content-top ul li .title, .item-list .item .content .content-top ul li .title-loud {
          display: inline;
          color: #9E9E9E;
          font-size: 13px;
          margin-right: 8px; }
          @media (min-width: 1000px) {
            .item-list .item .content .content-top ul li .title, .item-list .item .content .content-top ul li .title-loud {
              display: none; } }
        .item-list .item .content .content-top ul li .text {
          display: inline; }
    .item-list .item .content .content-bottom {
      display: none;
      height: 30px; }
      .item-list .item .content .content-bottom ul {
        margin-top: 8px; }
        @media (min-width: 1000px) {
          .item-list .item .content .content-bottom ul {
            position: absolute;
            left: 0;
            right: 0;
            bottom: 0;
            /* height: 23px; */
            padding: 4px 0; } }
        .item-list .item .content .content-bottom ul li {
          display: inline-block;
          padding: 1px 5px;
          width: 120px;
          white-space: nowrap;
          overflow: hidden;
          color: #2D7FC7; }
          .item-list .item .content .content-bottom ul li:hover {
            color: #aaa; }
          .item-list .item .content .content-bottom ul li:active {
            color: #777; }
          .item-list .item .content .content-bottom ul li i {
            display: inline;
            font-size: 13px;
            margin-right: 8px; }
          .item-list .item .content .content-bottom ul li .text {
            display: inline; }
    .item-list .item .right-bar {
      width: 28px;
      min-width: 28px; }
      .item-list .item .right-bar ul {
        display: none;
        position: absolute;
        top: 0;
        right: 4px; }
        .item-list .item .right-bar ul li {
          color: #2D7FC7;
          text-align: center;
          margin-right: 4px; }
          .item-list .item .right-bar ul li:hover {
            color: #aaa; }
          .item-list .item .right-bar ul li:active {
            color: #777; }
        .item-list .item .right-bar ul .edit {
          font-size: 18px; }
        .item-list .item .right-bar ul .change-status {
          font-size: 17px; }
    .item-list .item .footer {
      display: table-row;
      padding: 4px; }

.list-header {
  display: none;
  color: #005EB1; }
  @media (min-width: 1000px) {
    .list-header {
      position: fixed;
      display: block;
      top: 0;
      z-index: 10; } }
  .list-header .item {
    background: #f9f9f9;
    border-bottom: solid 1px #DCDCDC; }

