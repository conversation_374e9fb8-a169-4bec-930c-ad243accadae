using Extric.Towbook;
using Extric.Towbook.Integration;
using Extric.Towbook.Platform;
using Extric.Towbook.WebShared;
using PusherServer;
using System;
using System.Collections.Generic;
using System.Web;

public partial class Security_Pusher : System.Web.UI.Page
{
    protected void Page_Load(object sender, EventArgs e)
    {
        // TODO: replace calls to this to /api/pusher so that we don't have duplicate code

        var provider = new Pusher(
            PushNotificationProvider.ApplicationId,
            PushNotificationProvider.ApplicationKey,
            PushNotificationProvider.ApplicationSecret);
        
        Response.ContentType = "text/json";

        if (Request["channel_name"] == null)
            return;

        var channels = new List<string>();

        foreach (var company in WebGlobal.GetCompanies())
        {
            string system = PushNotificationProvider.GetChannelName(company.Id);
            string chat = PushNotificationProvider.GetChannelName(company.Id) + "-" + WebGlobal.CurrentUser.Id + "-chat";
            string gps = "private-gps-" + company.Id;
            string eventNotification = PushNotificationProvider.GetEventNotificationChannelName(company.Id, WebGlobal.CurrentUser.Id);

            channels.Add(system);
            channels.Add(chat);
            channels.Add(gps);
            channels.Add(eventNotification);

            var ags = Extric.Towbook.Agent.Session.GetByCompanyId(company.Id);

            if (ags != null)
            {
                // used for sending sync events to the win32 agent. 
                // web app/mobile apps should not subscribe to this event.
                channels.Add("private-agent-" + ags.Id + "-push");

                // used for reporting status updates to events
                // this should be subscribed to for listening for status updates. 
                channels.Add("private-agent-" + ags.Id + "-status");
            }
        }

        // add call pusher channel
        if (Request["channel_name"].ToString().EndsWith("-call"))
        {
            string callId = "";
            int result = 0;

            callId = Request["channel_name"].ToString().Replace("private-TWBK_Client_", "").Split('-')[0];
            int.TryParse(callId, out result);
            if (result > 0)
            {
                var call = Extric.Towbook.Dispatch.Entry.GetById(result);
                if (call != null)
                {
                    if (WebGlobal.CurrentUser.HasAccessToCompany(call.CompanyId))
                    {
                        var userId = WebGlobal.CurrentUser.Id;
                        var token = GetCurrentToken();

                        if(token != null && token.ClientVersionId != null)
                            channels.Add(PushNotificationProvider.GetCallChannelName(result, userId, token.ClientVersionId.Value));
                    }
                }
            }
        }
        
        if (Request["channel_name"] != null &&
            channels.Contains(Request["channel_name"].ToString()))
        {
            Response.Write(provider.Authenticate(Request["channel_name"], Request["socket_id"]).ToJson());
            Response.End();
        }

        return;
    }

    private AuthenticationToken GetCurrentToken()
    {
        if (HttpContext.Current == null)
            return null;

        if (HttpContext.Current.Items["ServiceGlobal.CurrentUserToken"] != null)
        {
            return AuthenticationToken.GetByToken(HttpContext.Current.Items["ServiceGlobal.CurrentUserToken"].ToString());
        }
        else
        {
            if (WebGlobal.CurrentUser != null)
            {
                return new AuthenticationToken()
                {
                    ClientVersionId = ClientVersion.GetByGitHash("web-app", ClientVersionType.WebApp).Id,
                    UserId = WebGlobal.CurrentUser.Id,
                };
            }
        }

        return null;
    }
}