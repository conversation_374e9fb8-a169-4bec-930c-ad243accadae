$towbook-blue: #2b75be;
$towbook-blue-lt: #1f7fdf;

$font-size: 13px;
$line-height: 1.4;
$lines-to-show: 3;

html, body {
  max-width: unset !important; // Allow the map to be as wide as the user wants it to be
  height: 100%;
  box-sizing: border-box;
}

#footer {
  display: none
}

#toolbar {
  display: none
}

#contents {
  margin: 0;
  padding: 0
}

#contents > div {
  position: absolute;
  top: 58px;
  bottom: 20px;
  left: 0;
  right: 0;
}

main {
  display: flex;
  height: 100%;
  background: #fff;

  * {
    font: 13px 'Open Sans', sans-serif;
    position: relative;
  }

  nav {
    height: 100%;
    width: 440px;
  }

  #canvas {
    flex: 1;
    background: #999;
  }
}

.panel {
  height: 100%;
  cursor: default;

  > div {
    display: flex;
    flex-direction: column;
    height: 100%;
  }
}

nav header {
  flex-shrink: 0;
  padding: 5px 0 5px 15px;
  border-bottom: solid 1px #eee;
  background: #fff;

  > div {
    display: flex;
    align-items: center;
    font-size: 15px;
  }

  .options-wrap {
    display: flex;
    height: 48px;
    width: 30px;
    padding: 0 15px 0 12px;
    align-items: center;
    color: #777;
    // To remove:
    padding: 0;
    width: 15px;
    color: #fff;

    &:hover {
      color: $towbook-blue-lt;
      // To remove:
      color: #fff;
    }
  }
}

nav footer {
  padding: 12px;
  border-top: solid 1px #eee;
  background: #fff;

  input {
    width: 100% !important;
  }
}

nav header,
nav footer,
.list-panel {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.select-company,
.select-list {
  padding: 5px 7px;
  border: solid 1px #ccc;
  border-radius: 3px;
}

.select-company {
  flex: 1;
  margin: 8px 15px 5px 0;
}

.hide-call,
.hide-driver,
.hide-company,
.hide-replay,
.hide-dispatching,
.hide-status,
.hide-uncompleted {
  display: none !important;
}

.list-panel {

    .filter-wrap {
        flex: 1;
        margin-left: 12px;

        #calls-filter,
        #drivers-filter {
            width: 100%;
            padding: 6px 25px 6px 12px;
            border: solid 1px #ddd;
            border-radius: 3px;
            background: #f6f6f6;

            &::-webkit-input-placeholder {
                color: #aaa;
            }

            &::-moz-placeholder {
                color: #aaa;
            }

            &:-ms-input-placeholder {
                color: #aaa;
            }

            &:-moz-placeholder {
                color: #aaa;
            }

            &:active,
            &:focus,
            &.filtering {
                background: #fff;
            }
        }

        #calls-filter-stop,
        #drivers-filter-stop {
            display: none;
            position: absolute;
            right: 8px;
            top: 4px;
            font-size: 16px;
            color: #aaa;
            cursor: pointer;

            &:hover {
                color: #ccc;
            }
        }
    }

    #calls-list,
    #drivers-list {
        flex: 1;
        overflow-y: auto;

        li {
            display: table-row;
            cursor: pointer;

            &:hover {
                background: #f6f6f6;
            }

            &.completion-pending {
                background-color: #d3ffd3;

                &:hover {
                    background-color: #f8f8f8;
                }
            }

            &.cancelation-pending {
                background-color: #ad00002e;

                &:hover {
                    background-color: #f8f8f8;
                }
            }

            &.th {
                &:hover {
                    background: #fff;
                }

                > div {
                    padding-top: 8px;
                    padding-bottom: 8px;

                    i {
                        display: none;
                        margin-left: 6px;
                        top: -1px;
                    }

                    &.sort-asc {
                        i {
                            display: inline-block;
                        }
                    }

                    &.sort-desc {
                        i {
                            display: inline-block;
                            transform: rotate(180deg);
                            top: 1px;
                        }
                    }
                }

                div {
                    font-size: 10px;
                    font-weight: bold;
                    color: #666;

                    &:hover {
                        color: $towbook-blue-lt;
                    }
                }
            }

            > div {
                display: table-cell;
                padding: 10px 0 10px 14px;
                border-bottom: solid 1px #eee;
            }

            .call-number-wrap {
                margin-right: 8px;
                width: 10%;
                white-space: nowrap;
            }

            .account-wrap {
                text-overflow: ellipsis;
                overflow: hidden;
                white-space: nowrap;
                max-width: 0;
            }

            .call-eta-wrap {
                width: 10%;
                white-space: nowrap;

                * {
                    font-size: 11px;
                }

                .late {
                    background: yellow;

                    &.arrived {
                        background: #f1f1f1;
                    }
                }
            }

            .dot-wrap {
                width: 37px;
                padding: 10px 14px;
                text-align: center;

                &.multi-dot {
                    width: 100%;
                    text-align: right;
                    white-space: nowrap;

                    .dot {

                        &:hover {
                            width: 16px;
                            height: 16px;
                            margin: 0 -3px -3px -3px;
                            z-index: 10;
                        }
                    }
                }

                .accept-reject {
                    font-size: 14px !important;

                    .fa {
                        display: none;
                        font-weight: unset;
                    }

                    .fa-thumbs-up {
                        color: #39579a;
                    }

                    .fa-thumbs-down {
                        color: red;
                        -webkit-animation: icon-bounce 3s infinite;
                        animation: icon-bounce 3s infinite;
                    }

                    &.accepted {
                        .fa-thumbs-up {
                            display: inline-block;
                        }
                    }

                    &.rejected {
                        .fa-thumbs-down {
                            display: inline-block;
                        }
                    }
                }
            }

            .line2 {
                margin-top: 4px;
                font-size: 11px !important;
                color: #aaa !important;

                &.truncate {
                    max-width: 120px;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }
            }

            .addr {
                text-overflow: ellipsis;
                overflow: hidden;
                white-space: nowrap;
            }

            .has-tolls {
                margin-right: 1px;
                font-size: 13px !important;
                color: #ff9900 !important;
            }

            .driver {
                text-overflow: ellipsis;
                overflow: hidden;
                white-space: nowrap;
                padding-left: 4px;

                &.truncate {
                    max-width: 120px;
                }
            }

            .truck {
                text-overflow: ellipsis;
                white-space: nowrap;
                overflow: hidden;
                padding-left: 4px;

                &.truncate {
                    max-width: 120px;
                }


                .truck-name {
                    margin-right: 4px;
                }

                .truck-type {
                    color: #aaa;
                }
            }
        }
    }

    #calls-none,
    #drivers-none {
        display: none;
        flex: 1;
        padding: 12px 15px;
        align-self: center;
        font-size: 15px;
    }
}

.info-panel {
    display: none;

    header {

        .title-wrap {
            flex: 1;
            display: flex;
            height: 48px;
            width: calc(100% - 30px);
            align-items: center;

            * {
                margin-right: 3px;
                font-size: 15px;
            }

            .call-eta-container {
                display: flex;
                flex-direction: column;

                .call-calculated-eta {
                    display: inline-flex;
                    padding: 1px 3px;
                    font-size: 13px;
                }

                .call-eta {
                    display: inline-flex;
                    padding: 1px 3px;
                    font-size: 13px;

                    &.late {
                        background: yellow;

                        &.arrived {
                            background: #f1f1f1;
                        }
                    }
                }
            }

            .btn-back {
                color: #aaa;
                width: 30px;
                margin-right: 0;
                cursor: pointer;

                &:hover {
                    color: $towbook-blue-lt;
                }

                i {
                    font-size: 32px !important;
                }
            }

            .call-number:before {
                content: '#';
                margin-right: 2px;
            }

            .account,
            .driver-name {
                flex: 1;
                margin-right: 12px;
                text-overflow: ellipsis;
                overflow: hidden;
                white-space: nowrap;
            }

            .driver-name {
                margin-left: 59px;
            }

            .dot {
                margin: 0 15px 0 3px;
            }
        }

        .options-wrap {
            display: none;
        }
    }

    .info-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow-y: auto;

        .info-bar-content {
            flex-direction: column;
            row-gap: 5px;
            align-items: baseline;
            justify-content: center;
            flex-basis: 100%;


            .closed-call-wrapper {
                display: flex;
                flex-direction: row;
                column-gap: 10px;
                align-items: center;
                padding: 10px;
                width: 100%;
                background-color: #fff8f7;

                .closed-icon {
                    display: flex;
                    flex-direction: column;
                    flex-grow: 0;
                    row-gap: 3px;
                    padding: 10px;
                    border-style: solid;
                    border-radius: 20px;
                    color: white;
                    align-items: center;
                    font-size: 15px !important;
                    color: white;
                    background-color: #e54d2e;
                }

                .closed-message {
                    display: flex;
                    flex-direction: column;
                    flex-grow: 1;
                    row-gap: 5px;
                    color: #444444;

                    .title {
                        color: #e54d2e;
                    }
                }
            }
        }

        label {
            color: #aaa;
        }

        .map-link {
            padding: 5px 0;
            color: #0072c6;
            text-decoration: none;

            &:hover {
                color: #bbb;
            }
        }

        .no-map-link {
            padding: 5px 0;
            color: #a00;
            text-decoration: none;

            &:hover {
                color: #aaa;
            }
        }

        .call-status {
            padding: 12px 15px;
            border-bottom: solid 1px #eee;

            .status-row {
                display: flex;
                align-items: center;
                padding: 5px 0;

                label {
                    font: 13px "Open Sans", Arial, sans-serif;
                    display: inline-block;
                    width: 90px;
                }

                select {
                    display: inline-block;
                    width: 235px;
                    height: 36px;
                    padding: 0 5px;
                    border-radius: 3px;
                }
            }
        }

        .call-status-update {
            display: none;

            .status-select {
                display: none;
            }

            .input-wrap {
                margin-bottom: 10px;

                label {
                    display: inline-block;
                    padding-bottom: 2px;
                }

                select,
                textarea {
                    width: 330px;
                    font-size: 12px;
                }
            }

            #z-multipleDrivers {
                .driver-name {
                    margin: 5px 10px;
                }
            }

            .navigation-row {
                padding: 0 !important;
            }
        }

        .call-replay-btn,
        .call-acknowlege-completion,
        .call-acknowlege-cancellation {
            margin-top: 5px;
            padding: 4px 10px;
            border-radius: 3px;
            background: #f2f2f2;
            cursor: pointer;

            &:hover {
                background: #e2e2e2;
            }
        }

        .call-replay {
            /*display: none;*/
        }

        .not-on-map {
            margin-top: 10px;
            margin-bottom: 5px;
            padding: 5px 8px;
            background: #fff2f2;
            border: solid 1px #a00;
            border-radius: 3px;
            color: #a00;
            text-align: center;
        }

        .section {
            padding: 12px 15px;
            border-bottom: solid 1px #eee;

            .title {
                margin-top: 8px;
                padding-bottom: 10px;
                font: 15px "Open Sans", Arial, sans-serif;
            }

            .row {
                display: flex;

                label {
                    font: 13px "Open Sans", Arial, sans-serif;
                    vertical-align: top;
                    display: inline-block;
                    width: 90px;
                    padding: 5px 0;
                }

                .val {
                    flex: 1;
                    padding: 5px 0;

                    .until,
                    .impound-tag {
                        color: #aaa;
                        font-size: 12px;
                    }

                    .has-tolls {
                        color: #ff9900;
                    }

                    .truck-type {
                        margin-left: 6px;
                        color: #aaa;
                    }
                }
            }

            &.notes {
                .fa-caret-up {
                    display: none;
                }

                .fa-caret-down {
                    display: inline-block;
                }

                .ellipse-multiline {
                    text-overflow: ellipsis;
                    overflow: hidden;
                    width: 100%;
                    display: block;
                    display: -webkit-box;
                    height: $font-size*$line-height*$lines-to-show;
                    font-size: $font-size;
                    line-height: $line-height;
                    -webkit-line-clamp: $lines-to-show;
                    -webkit-box-orient: vertical;
                    margin-bottom: 6px;
                }

                .more {
                    display: block;

                    span {
                        font-size: 11px;
                    }

                    &:hover {
                        color: #aaa;
                        cursor: pointer;
                    }

                    .fa {
                        padding-right: 5px;
                    }
                }
            }

            &.full {
                .fa-caret-up {
                    display: inline-block;
                }

                .fa-caret-down {
                    display: none;
                }

                .ellipse-multiline {
                    text-overflow: unset;
                    overflow: unset;
                    height: unset;
                    -webkit-line-clamp: unset;
                    -webkit-box-orient: unset;
                }
            }

            .right {
                float: right;
            }

            &.pending-completion-wrapper {
                background-color: #d3ffd3;
            }

            &.pending-cancellation-wrapper {
                background-color: #ad00002e;

                label {
                    color: #444;
                }
            }
        }

        .driver-replay {
            > .title {
                margin-bottom: 8px;
            }

            .replay-msg {
                display: none;
                margin-top: 10px;
                padding: 5px 8px;
                background: #ebf2f7;
                text-align: center;
                font-size: 12px;
                color: #00639e;
                border-radius: 3px;
                border: solid 1px #0077c0;
            }

            .row {
                align-items: center;
                -webkit-user-select: none;
                -khtml-user-select: none;
                -moz-user-select: none;
                -o-user-select: none;
                user-select: none;

                > div {
                    flex: 1;

                    .date-picker,
                    .time-picker {
                        width: 100%;
                        margin-bottom: 2px;
                        padding: 2px 5px;

                        &:disabled {
                            background: #f6f6f6;
                            color: #999;
                            user-select: none;
                        }
                    }
                }

                .to {
                    max-width: 35px;
                    text-align: center;
                }

                .replay-btn {
                    width: 100px !important;
                    min-width: 100px !important;
                    margin-left: 10px;
                    height: 28px;
                    padding-top: 3px !important;
                }

                .rangeslider {
                    display: inline-block;
                    margin: 18px 0 20px 0;
                    flex: 1;
                }

                .curr-info {
                    display: inline-block;
                    margin-left: 10px;
                    max-width: 48px;
                    top: -2px;
                    white-space: nowrap;
                    text-align: center;

                    .date {
                        color: #aaa;
                        font-size: 11px;
                    }

                    .time {
                        font-size: 12px;
                    }
                }

                .media-button {
                    display: inline-block;
                    margin-right: 12px;
                    padding: 4px 8px;
                    width: 50px;
                    background: linear-gradient(#fff, #e6e6e6);
                    border: solid 1px #aaa;
                    border-radius: 40px;
                    text-align: center;
                    color: #666;
                    cursor: pointer;

                    &.play {
                        padding: 8px 12px !important;
                    }

                    &:hover {
                        background: linear-gradient(#f6f6f6, #e2e2e2);
                        color: #555;
                    }

                    &:active {
                        background: linear-gradient(#eee, #ddd);
                    }
                }

                .replay-controls {
                    opacity: 0.3;
                    pointer-events: none;

                    &.enabled {
                        opacity: 1;
                        pointer-events: auto;
                    }

                    .options {
                        margin-right: 20px;
                        margin-top: 3px;

                        &.speed {
                            padding-top: 8px;
                        }

                        label {
                            flex: 1;
                            color: #aaa;
                            text-align: right !important;
                            padding: 0 8px 0 0;
                        }

                        input[type=checkbox] {
                            margin: 6px 2px 0px 2px;
                            width: 14px;
                            height: 14px;
                        }

                        #replay-speed {
                            margin: 4px 0 4px 0;
                            padding: 2px 4px;
                            border-radius: 14px;
                            width: 48px;
                        }

                        .no-flex {
                            flex: 0 1 auto;
                        }
                    }
                }
            }
        }
    }
}

#company-picker {
  display: none;

  li {
    border: solid 1px #aaa;
    background: #eee;
    padding: 4px 8px;
    margin-bottom: 10px;
    text-align: center;
    cursor: pointer;

    &:hover {
      background: #ddd;
    }

    &:active {
      background: #ccc;
    }

    &.close-me {
      margin-bottom: 0;
      background: #fff2f2;
      border: solid 1px #c00;
      color: #c00;
      text-align: center;

      &:hover {
        background: #ffe2e2;
      }

      &:active {
        background: #ffd2d2;
      }
    }
  }
}

.dot {
  display: inline-block;
  width: 10px;
  height: 10px;
  border-radius: 50%;
}

#drivers-panel {
  display: none;
}

#driver-info-panel {
  display: none;
}

#map-btn-container {
  position: absolute;
  top: 10px;
  left: 615px;
  z-index: 100;

  #show-call-numbers-btn,
  #show-truck-locations-btn,
  #hide-calls-btn {
    /* These styles match what Google uses for their Map/Satellite buttons */
    display: inline-block;
    margin-right: 7px;
    direction: ltr;
    overflow: hidden;
    text-align: center;
    position: relative;
    color: rgb(86, 86, 86);
    font-family: Roboto, Arial, sans-serif;
    user-select: none;
    font-size: 11px;
    background-color: rgb(255, 255, 255);
    padding: 11px;
    border-radius: 2px;
    background-clip: padding-box;
    box-shadow: rgba(0, 0, 0, 0.3) 0px 1px 4px -1px;
    min-width: 40px;
    cursor: pointer;

    &:hover {
      background: #e6e6e6;
    }
  }
}

#colorLegend {
  position: absolute;
  bottom: 0px;
  left: 425px;
  padding-right: 4px;
  border-radius: 0px 4px 0px 0px;
  background: #fff;
  z-index: 100;

  > div {
    float: left;
    padding: 5px;

    span {
      float: left;
      margin: 3px 8px 0 2px;
      width: 12px;
      height: 12px;
      border-radius: 7px;
    }
  }
}

#loading-bar {
  position: absolute;
  top: 0;
  left: 425px;
  right: 0;
  z-index: 100;
}

#loading-progress {
  background: rgba(0, 119, 255, 0.23);
  padding: 0 0 1px 4px;
  white-space: nowrap;
  font-size: 10px;
}

.calls-spinner,
.status-spinner,
.replay-spinner {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  display: none;

  .ring-wrapper {
    margin: auto auto;

    .ring,
    .ring:after {
      border-radius: 50%;
      width: 65px;
      height: 65px;
    }

    .ring {
      display: inline-block;
      font-size: 10px;
      position: relative;
      text-indent: -9999em;
      border-top: 10px solid rgba(#2b75be, 0.2);
      border-right: 10px solid rgba(#2b75be, 0.2);
      border-bottom: 10px solid rgba(#2b75be, 0.2);
      border-left: 10px solid rgba(#2b75be, 1.0);
      -webkit-transform: translateZ(0);
      -ms-transform: translateZ(0);
      transform: translateZ(0);
      -webkit-animation: load8 1.1s infinite linear;
      animation: load8 1s infinite linear;
    }

    @-webkit-keyframes load8 {
      0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
      }

      100% {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg);
      }
    }

    @keyframes load8 {
      0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
      }

      100% {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg);
      }
    }
  }
}

.calls-spinner {
  top: auto;
  bottom: 71px;
  text-align: center;
  display: none;
}

.replay-spinner {
  position: absolute;
  top: -6px;
  bottom: 0;
  left: 0;
  right: 12px;
  display: none;
}

.ignition-on {
    color: lightgreen;
}

.ignition-off {
    color: red;
}

.checked-in {
  color: #4ec900;
  margin-right: 1px !important;
}

.checkin-slider {
  height: 11px !important;
  width: 42px !important;

  .rangeslider__fill {
    background: #00c20e !important;
  }
}

input:not([type=button]),
select,
textarea {
  padding: 4px;
  border: 1px solid #bbbbbb;
  border-radius: 3px;
  color: #000000;
  background-color: #ffffff;
}

.standard-button {
  border-radius: 3px;
}

.waiting, 
.scheduled {
  background-color: #ff9900;
}

.dispatched {
  background-color: #2850ab;
}

.enroute {
  background-color: #90ee90;
}

.onscene {
  background-color: #008000;
}

.towing {
  background-color: #3294F5;
}

.destinationarrival {
  background-color: #d700c9;
}

.completed {
background-color: #ccc;
}

#replay-link-container {
    .copyContainer {
        padding: 1px 12px 4px 5px;
        height: 26px;
        background-color: #2B75BE;
        color: white;
        font-size: 16px;
        transition: background-color 0.5s ease;
    }

    i.fas {
        padding-right: 6px;
    }

    .copyContainer:hover {
        cursor: pointer;
        background-color: #23629E;
    }

    .driverReplay-input {
        width: 320px;
    }

    .copyContainer label {
        color: white;
        font-size: 16px;
        font-family: Calibri, Arial;
        background-color: none;
        cursor: pointer;
    }

}