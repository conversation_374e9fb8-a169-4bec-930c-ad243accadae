using System;
using System.Linq;
using Extric.Towbook.Utility;
using Extric.Towbook.WebShared;
using Extric.Towbook.Generated;
using System.Collections.Generic;

public partial class Map_Call_Request : System.Web.UI.Page
{
    // default to st clair, mi
    protected string homeBaseLatLong = new { latitude = 42.82, longitude = -82.49 }.ToJson();

    public string _config; 
    public string _locations;
    public string _calls; 
    public string _callRequest = "null";
    public bool _showDrivers = false;

    protected void Page_Load(object sender, EventArgs e)
    {
        this.Master.CurrentSection = Navigation.NavigationItemEnum.Map;
        this.Master.UseJquery = true;
        this.Master.HideTitle = true;
        this.Master.HideFooter = true;

        if (WebGlobal.CurrentUser.Type == Extric.Towbook.User.TypeEnum.Driver)
            Response.Redirect("/");

        var headers = new Dictionary<string, string>()
        {
            { "X-Company", "all" }
        };

        if (!string.IsNullOrWhiteSpace(WebGlobal.CurrentUser.Company.Zip))
        {
            var a = SqlMapper.QuerySP<dynamic>("Geo.GetCityByZip", new { @Zip = WebGlobal.CurrentUser.Company.Zip }).FirstOrDefault();

            if (a != null)
            {
                homeBaseLatLong = new { latitude = a.Latitude, longitude = a.Longitude }.ToJson();
            }
        }

        _calls = WebGlobal.GetResponseFromUrl("/api/calls", headers);
        _config = WebGlobal.GetResponseFromUrl("/api/config", headers);

        _locations = WebGlobal.GetResponseFromUrl("/api/location/currentlocations", headers);
        _showDrivers = WebGlobal.CurrentUser.Company.HasAccessToBaseFeature("digitialDispatchImprovements");
        int status;
        var callRequestId = Convert.ToInt32(Request.QueryString["id"]);
        if (callRequestId != null)
        {
            var resp = WebGlobal.GetResponseFromUrl("/api/callRequests/" + callRequestId, out status, false, true, null, headers);
            if (status == 200)
                _callRequest = resp;
        }
    }
}