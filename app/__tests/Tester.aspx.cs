using System.Web.Script.Serialization; 
using Extric.Towbook.WebShared;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using Integration = Extric.Towbook.Integration;
using Newtonsoft.Json;
using Extric.Towbook;
using System.Collections.ObjectModel;

public partial class __tests_Tester : System.Web.UI.Page
{

    protected void BeginSection(string section, bool executeEndFirst = false)
    {
        if (executeEndFirst) 
            EndSection();

        Response.Write("<h2>" + section + "</h2>\r\n");
        Response.Write("<div>");
    }
    protected void EndSection()
    {
        Response.Write("</div>\r\n\r\n");
    }


    protected void Dump(object o)
    {
        Response.Write(JsonConvert.SerializeObject(o, Formatting.Indented).Replace(" ", "&nbsp;").Replace("\r\n", "<br />\n"));
    }
    protected void Page_Load(object sender, EventArgs e)
    {
        Response.Write("<style> div { font-family: courier; font-size: 12px; color: navy; padding-left: 20px; margin-bottom: 40px; border-left: solid 4px #afafaf } h2 { font-size: 16px; font-family: verdana }</style>");


        CompanyFile cf = new CompanyFile()
        {
            OwnerUserId = WebGlobal.CurrentUser.Id,
            CompanyId = WebGlobal.CurrentUser.CompanyId,
            Size = 123,
            DispatchEntries = new Collection<int>() { 1 },
            Accounts = new Collection<int>() { 1 },
            Description = "Test File",
            RawUrl = "Test",
            Filename = "filename.jpg"
        };

        cf.Save();

        Response.Write("x=" + cf.Id);
        

        cf.Accounts.Add(8);
        cf.Accounts.Add(35);
        cf.Accounts.Add(36);
        cf.DispatchEntries.Add(565);
        cf.DispatchEntries.Add(566);
        cf.Save();


        Dump(CompanyFile.GetByAccountId(8));
        Dump(CompanyFile.GetByDispatchEntryId(565));
        //Dump(CompanyFile.GetById(cf.Id));


        cf.Delete(WebGlobal.CurrentUser);



        #region test integration code

        if (false)
        {


            BeginSection("Integration.provider.GetAll()");
            var providers = Integration.Provider.GetAll();
            Dump(providers);

            if (false)
            {

                #region Verify GetById works - will only test if Providers has at least one entry
                if (providers.Count > 0)
                {
                    BeginSection("Integration.provider.GetById(" + providers[0].ProviderId + ")", true);
                    var x = Integration.Provider.GetById(providers[0].ProviderId);

                    if (x.ProviderId != providers[0].ProviderId)
                        throw new Exception("GetById is broken...");
                    Dump(x);
                }
                #endregion

                BeginSection("Integration.Provider.GetByCompanyId(" + WebGlobal.CurrentUser.CompanyId + ")", true);
                Dump(Integration.Provider.GetByCompanyId(WebGlobal.CurrentUser.CompanyId));

                BeginSection("Integration.CompanyKeyValue.GetByCompanyId(" + WebGlobal.CurrentUser.CompanyId + ")", true);
                Dump(Integration.CompanyKeyValue.GetByCompany(WebGlobal.CurrentUser.CompanyId));

                BeginSection("Integration.CompanyKeyValue.GetByCompanyId(" + WebGlobal.CurrentUser.CompanyId + ", 1)", true);
                Dump(Integration.CompanyKeyValue.GetByCompany(WebGlobal.CurrentUser.CompanyId, 1));

                var company_keyValue = new Integration.CompanyKeyValue() { CompanyId = WebGlobal.CurrentUser.CompanyId, KeyId = 1, Value = "Test:" + Guid.NewGuid().ToString("N") };

                company_keyValue.Save();
                company_keyValue.Value += "updated";
                company_keyValue.Save();

            }

            if (false)
            {
                BeginSection("CompanyKey.GetAll()", true);
                Dump(Integration.CompanyKey.GetAll());

                BeginSection("DispatchEntryKey.GetAll()", true);
                Dump(Integration.DispatchEntryKey.GetAll());

                BeginSection("AccountKey.GetAll()", true);
                Dump(Integration.AccountKey.GetAll());

                BeginSection("TruckKey.GetAll()", true);
                Dump(Integration.TruckKey.GetAll());

                BeginSection("DriverKey.GetAll()", true);
                Dump(Integration.DriverKey.GetAll());

                BeginSection("RateItemKey.GetAll()", true);
                Dump(Integration.RateItemKey.GetAll());
            }


            BeginSection("AccountKeyValue.GetByCompany(" + WebGlobal.CurrentUser.CompanyId + ")", true);
            Dump(Integration.AccountKeyValue.GetByCompany(WebGlobal.CurrentUser.CompanyId));

            BeginSection("DispatchEntryKeyValue.GetByCompany(" + WebGlobal.CurrentUser.CompanyId + ")", true);
            Dump(Integration.DispatchEntryKeyValue.GetByCompany(WebGlobal.CurrentUser.CompanyId));

            BeginSection("RateItemKeyValue.GetByCompany(" + WebGlobal.CurrentUser.CompanyId + ")", true);
            Dump(Integration.RateItemKeyValue.GetByCompany(WebGlobal.CurrentUser.CompanyId));

            BeginSection("RateItemKeyValue.GetByRateItem(companyId=" + WebGlobal.CurrentUser.CompanyId + ", rateItemId=4)", true);
            Dump(Integration.RateItemKeyValue.GetByRateItem(WebGlobal.CurrentUser.CompanyId, 4));

            BeginSection("TruckKeyValue.GetByCompany(" + WebGlobal.CurrentUser.CompanyId + ")", true);
            Dump(Integration.TruckKeyValue.GetByCompany(WebGlobal.CurrentUser.CompanyId));

            BeginSection("DriverKeyValue.GetByCompany(" + WebGlobal.CurrentUser.CompanyId + ")", true);
            Dump(Integration.DriverKeyValue.GetByCompany(WebGlobal.CurrentUser.CompanyId));

            if (true)
            {
                // test adds

                Integration.AccountKeyValue a = new Integration.AccountKeyValue() { AccountId = 8, KeyId = 9999, Value = "Teddst" };
                a.Save();

                Integration.DispatchEntryKeyValue dekv = new Integration.DispatchEntryKeyValue() { DispatchEntryId = 530, KeyId = 9999, Value = "Test DE Value" };
                dekv.Save();

                Integration.RateItemKeyValue rekv = new Integration.RateItemKeyValue() { RateItemId = 4, KeyId = 9999, Value = "Test RateItem from __tests Value" };
                rekv.Save();

                Integration.TruckKeyValue tkv = new Integration.TruckKeyValue() { TruckId = 239, KeyId = 9999, Value = "Test Truck Value" };
                tkv.Save();

                Integration.DriverKeyValue dkv = new Integration.DriverKeyValue() { DriverId = 5, KeyId = 9999, Value = "Test Driver Value" };
                dkv.Save();
            }

            EndSection();
        }

        #endregion
    }
}