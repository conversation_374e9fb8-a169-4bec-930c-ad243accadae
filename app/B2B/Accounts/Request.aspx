<%@ Page Title="" Language="C#" MasterPageFile="~/UI/TowbookV2.master" AutoEventWireup="true" CodeFile="Request.aspx.cs" Inherits="B2B_Accounts_Request" EnableEventValidation="false" Async="true" %>

<%@ MasterType TypeName="UI_Towbook" %>
<%@ Register TagPrefix="eo" NameSpace="EO.Web" Assembly="EO.Web" %>



<asp:Content ID="Content5" ContentPlaceHolderID="content" Runat="Server">
    <form runat="server">
<style type="text/css">
  #drivable * { display: inline; border: none; width: auto; }
  #drivable input, #drivable select { width: auto; margin: 0; }
  #account { display: inline }
  #account * { display: inline; width: auto }
  #source *, #priorities * { display: inline; width: auto; border: none; margin: 0; }
  #source label { font-weight: normal; padding-right: 5px; padding-left: 3px }
  
  .editor { width: 100% }
  .FieldX1 { padding-top: 10px; border-top: solid 1px #afafaf; padding-bottom: 10px }
  .FieldX1 input { width: 495px; margin-left: 0 }
  .FieldX1 span { display: block; width: 170px; float: left}
  .FieldX1 span input { width: auto; margin-left: 0 }
  .SubHeading span { font-size: 9px; font-weight: normal; color: #333333; margin-left: 11px }
  .Field, .Title { line-height: 200% } 
  .Field *, .Field input  { padding: 0; margin: 0 } 
  .Title *, .Title input { padding: 0; margin: 0 }
  .Field, .Title { font-weight: bold }
  .Field input, .Title input{ font-weight: normal }
  .editor .Field { padding: 5px 10px 10px 10px } 
  .editor .Title { padding: 5px 10px 10px 10px; width: 150px } 
  .editor .Field input, .editor .Field select,
  .editor .Title input, .editor .Title select { margin: 0; padding: 0; width: 100% }
  
  .Cell { padding-left: 10px; padding-right: 10px }
  .Cell select { width: 100% } 
  select { font-weight: normal } 
  .mininav a { text-decoration: none; color: #444444 }
  .mininav a:hover
  {
    text-decoration: underline
  }
  .mininav a.sel { text-decoration: none; color: Black; font-weight: bold }
</style>

<script type="text/javascript">

    function decodeVin(vin)
    {

        new Ajax.Request('/api/vin/' + vin, {
            method: 'get',
            onSuccess: function (transport) {
                var ta = transport.responseText.evalJSON();
                $('content_dlYear').value = ta.year;
                $('content_dlMake').value = ta.makeId;
                mininavModelToggle(false);
                $('content_txtModel').value = ta.model;
            }
        });
    }

    function decodePlate(plate,state) {
        new Ajax.Request('/api/vin/?plate=' + plate + '&state=' + state, {
            method: 'get',
            onSuccess: function (transport) {
                var ta = transport.responseText.evalJSON();
                $('content_dlYear').value = ta.year;
                $('content_txtVIN').value = ta.vin;
                $('content_dlMake').value = ta.makeId;
                mininavModelToggle(false);
                $('content_txtModel').value = ta.model;
            }
        });


    }




    function checkTicket(src, args) {
        var t = $('<%=txtInvoiceNumber.ClientID %>').value;
    var iT ='<% = HttpUtility.HtmlEncode(txtInvoiceNumber.Text) %>';
    var bg, c;

    if (t == iT)
    {
        args.IsValid = true;
        return true;
    }
    src = $('ticketErrorMsg');

    new Ajax.Request('/dyreq/ticketexists.aspx', {
        parameters: { 'ticket': t },
        method: 'get', asynchronous: false,
        onSuccess: function(transport) {
            if (transport.responseText != "0") {
                $(src.id).innerHTML = "Number already in use.";
                bg = '#ff0000'; c = 'white'; args.IsValid = false;
            }
            else {
                $(src.id).innerHTML = "(optional)";
                bg = 'white'; c = 'black'; args.IsValid = true;
            }
            $(src).style.backgroundColor = bg;
            $(src).style.color = c;
            return args.IsValid;
        }
    });        
}

function validateVINEx(src, arg) {
    <%
    if (Global.CurrentUser.Company.VinRequiredLength < 17)
    {
    %>
        if (arg.Value.length > 0 && arg.Value.length < <%=Global.CurrentUser.Company.VinRequiredLength %> )
        {
            arg.IsValid = false;
        return false;
    }
    else{
        arg.IsValid = true;
    return true;
    }
    <%
    }
    else 
    {
    %>
    

    if (document.getElementById('<% =dlYear.ClientID %>').value > 1980)
        return validateVIN(src, arg);
    else
    {
        arg.IsValid = true;
        return true;
    }
    <% } %>
    }



    function onMakeChange(box) {
        if (box == null) return;
        var request = '/dyreq/VehicleModels.aspx?value=' + box.value;
        new Ajax.Request(request, { 
            method: 'get',
            onSuccess: function(transport) { 
                eval(transport.responseText); 
                $('<% =dlModel.ClientID %>').options.length = 0;
            for(var obj in models)
                addOption($('<% =dlModel.ClientID %>'), models[obj].n, models[obj].id);   
        },
        onFailure: function(){ alert('Error retrieving data: ' + request) }
    } );
}

function onTowTypeChange(obj, updatePrices)
{
    if (updatePrices == null) updatePrices = true;
    var t = obj.value;
    // go through the added lists of service items and add/remove ones that apply to the selected service type 
    var vx = $('divServices').getElementsByTagName('SELECT');
    
    for(x = 0; x < vx.length; x++)
    {
        // save the value: vx[x].value;
        var saved = vx[x].value;
        var result = -1;
        // clear:dd
        vx[x].options.length = 0;
        
        // add:
        for(y in rateItems)
        {
            if ((rateItems[y].ext != null && rateItems[y].ext[t] != null) || ((rateItems[y].price != null && rateItems[y].price != 0) || y == -1)  )
            {
                addOption(vx[x], rateItems[y].n, y);
                if (y == saved) result = saved;
            }
        }
        vx[x].value = result; // set the old selected value. (make sure it exists?)
    }
    
    updateAllFields(updatePrices);
}

function updateFields(obj, updatePrice, updateQuantity) {
    var rate = rateItems[obj.value];
    var rA = obj.parentNode.parentNode.getElementsByTagName('TD');

    if (rate != null)
    {
        var r1 = rA[1].getElementsByTagName("INPUT")[0];
        
        r1.style.color = (rate.lockQuantity ? "#999999" : "#000000");
        r1.style.border = (rate.lockQuantity ? "none" : "solid 1px #afafaf");
        r1.readOnly = false;
        
        
        var r2 = rA[2].getElementsByTagName("INPUT")[0];
        r2.readOnly = false;
        r2.style.color = (rate.lockPrice ? "#999999" : "#000000");
        r2.style.border = (rate.lockPrice ? "none" : "solid 1px #afafaf");
        
        if (updateQuantity == null || updateQuantity == true)
            r1.value = rate.defaultQuantity;
            
        if (updatePrice)
        {
            if (rate.ext != null && rate.ext[$('<% =dlTowType.ClientID %>').value] != null)
                x = parseFloat(rate.ext[$('<% =dlTowType.ClientID %>').value].amt).toFixed(2);
            else 
                x = (rate.price != null ? parseFloat(rate.price).toFixed(2) : '0.00')
            
            r2.value = '$' + x;
        }
        r1.readOnly = rate.lockQuantity;
        r2.readOnly = rate.lockPrice;   
    }
    
    rA[3].getElementsByTagName("INPUT")[0].value = '';
    return;
}

function validateQuantity(obj)
{
    if (obj.parentNode.parentNode.getElementsByTagName('TD')[0].getElementsByTagName('SELECT').length > 0)
    {
        rate = rateItems[obj.parentNode.parentNode.getElementsByTagName('TD')[0].getElementsByTagName('SELECT')[0].value];
        if (rate.min == null && rate.max == null)
            return;
        else if (rate.min == null && rate.max != null) {
            if (obj.value > rate.max){
                obj.value = rate.max;
                alert('Quantity must be ' + rate.max + ' or less.');
                obj.focus();
            }
        }
        else if (rate.min != null && rate.max == null) {
            if (obj.value > rate.min){
                obj.value = rate.min;
                alert('Quantity must be ' + rate.min + ' or higher.');
                obj.focus();
            }
        }
        else if (rate.min != null && rate.max != null) {
            if (obj.value < rate.min) {
                obj.value = rate.min;
                alert('Quantity must be ' + rate.min + ' or higher (and less than ' + rate.max + ')');
                obj.focus();
            }
            else if (obj.value > rate.max)
            {
                obj.value = rate.max;
                alert('Quantity must be ' + rate.max + ' or less (and more than ' + rate.min + ')');
                obj.focus();
            }
        }
    }
}

function calculateLineTotal(obj, useBase, retTaxableAmt)
{
    if (useBase == null) useBase = false;
    if (retTaxableAmt == null) retTaxableAmt = false;
    
    rowArray = obj.parentNode.parentNode.getElementsByTagName('TD');
    
    var qty = rowArray[1].getElementsByTagName("INPUT")[0].value
    
    var rate = null;
    if (rowArray[0].getElementsByTagName('SELECT').length > 0)
    {
        rate = rateItems[rowArray[0].getElementsByTagName('SELECT')[0].value];
    }
    else if (rowArray[0].getElementsByTagName('INPUT').length > 1)
    {
        if (rateItems[rowArray[0].getElementsByTagName('INPUT')[1].value] != null)
            rate = rateItems[rowArray[0].getElementsByTagName('INPUT')[1].value];
    }
    
    if (rate != null)
    {
        if (rate.fq > 0)
        {
            qty = qty - rate.fq;
            if (qty < 0) qty = 0;
        }
    }
    
    var price = rowArray[2].getElementsByTagName("INPUT")[0].value.replace('$', '').replace(",", "");
    
    if (retTaxableAmt)
    {
        if (rate != null && rate.tax == 1)
            return (price * qty).toFixed(2);
        else
            return 0;
    }
    
    if (useBase == true && rate != null)
    {
        if (rate.ext != null && rate.ext[$('<% =dlTowType.ClientID %>').value] != null)
        {
            price = parseFloat(rate.ext[$('<% =dlTowType.ClientID %>').value].baseAmt).toFixed(2);
        }
        else
        {
            price = (rate.basePrice != null ? rate.basePrice : price);
            if (price == null) price = 0;
        }
        
        return (qty * price).toFixed(2);
    }
    else{
        rowArray[3].getElementsByTagName("INPUT")[0].value = "$" + (qty * price).toFixed(2);    
        return (qty * price).toFixed(2);
    }
}
 
function addTextbox(cell, name, type) {
    var el2 = document.createElement('input');
    el2.type = (type == null ? 'text' : type);
    el2.name = name.replace(/\_/g, '$');
    el2.id = name.replace(/\$/g, "_");
    el2.style.margin = "0";
    cell.appendChild(el2);
    return el2;
}

function addDropdown(name) {
    var sl = document.createElement("select");
    sl.name = name.replace(/\_/g, '$');
    sl.id = name.replace(/\$/g, "_");
        
    var t = $('<% =dlTowType.ClientID %>').value;
        for(x in rateItems)
        {
            if (rateItems[x].pr == 1) continue;
            if ((rateItems[x].ext != null && rateItems[x].ext[t] != null) || ((rateItems[x].price != null && rateItems[x].price != 0) || x == -1)  )
                addOption(sl, rateItems[x].n, x);
        }
        
        sl.onchange = function() { updateFields(this, true); calculateLineTotal(this); updateAllFields(this); performDropDownLogic(this); };
        
        return sl;
    }
    
    function performDropDownLogic(obj)
    {
        var mt = $('items');
        var l = mt.rows.length;
        
        var lastSelect, objRow;
        
        for (i = 1; i < l; i++) {
            var row = mt.rows[i].getElementsByTagName("select");
            if (row.length > 0)
                lastSelect = i;
        }
        
        for (i = 1; i < l; i++) {
            var row = mt.rows[i].getElementsByTagName("select");
            if (row[0] == obj) {
                if (lastSelect == i && obj.value != -1)
                    addService('items', 'select');
                
                objRow = i;
            }
        }
         
        if (objRow < lastSelect && obj.value == -1)
            $(mt.rows[objRow]).toggle('slide', { duration: 3.5 });       
    }

    function addContact(highlight) {
        var myDiv = document.getElementById('myContacts');
        var divContainer = document.createElement("div");
        var prefix = "<% =rpContacts.ClientID %>_ctl";
        
        divContainer.style.display = 'none';
        divContainer.style.borderTop = 'dotted 1px #afafaf';
        
        var tbl = document.createElement('table') // document.getElementById('myContacts');
        tbl.className = "editor";
        tbl.cellSpacing = "0";

        // if there's no header row in the table, then iteration = lastRow + 1
        var iteration = myDiv.getElementsByTagName('div').length;
        var prev = prefix + ((iteration - 1).toString()).leftPad(2, '0'); 
        prefix += ((iteration).toString()).leftPad(2, '0'); 

        divContainer.id = "divHolder" + iteration;

        // check to see if last contact field is filled in. If it's not, don't insert another!
        if (iteration > 0)
        {
            if ($(prev + 'FullName').value.length + $(prev + 'Phone').value.length < 1) {
                $(prev + 'FullName').focus();
                return;
            }
        }
        
        var row = tbl.insertRow(0);
        var cell2 = row.insertCell(0);
        cell2.className = "Field";
        cell2.style.borderTop = 'none';
        cell2.style.padding = '0';

        var tblCon = document.createElement('table');
        tblCon.style.width = "100%";
        tblCon.style.padding = "0";
        tblCon.cellSpacing = "0";
        
        var tblConnRow1 = tblCon.insertRow(0);
        var tblConnRow1Col0 = tblConnRow1.insertCell(0);
        tblConnRow1Col0.style.width = "95px";
        tblConnRow1Col0.style.paddingLeft = "10px";
        tblConnRow1Col0.appendChild(document.createTextNode("Type"));

        var tblConnRow1Col1 = tblConnRow1.insertCell(1);
        tblConnRow1Col1.style.width = "150px";
        tblConnRow1Col1.style.paddingLeft = "5px";
        tblConnRow1Col1.appendChild(document.createTextNode("<% if (_account.CompanyId == 4082) { %>Customer <% } %>Name"));

        var tblConnRow1Col2 = tblConnRow1.insertCell(2);
        tblConnRow1Col2.style.width = "100px";
        tblConnRow1Col2.style.paddingLeft = "5px";
        tblConnRow1Col2.appendChild(document.createTextNode("Phone"));

        var tblConnRow1Col3 = tblConnRow1.insertCell(3);
        tblConnRow1Col3.style.paddingLeft = "5px";
        tblConnRow1Col3.style.width = "250px";
        tblConnRow1Col3.appendChild(document.createTextNode("Address/City/State/Zip"));
        
        var tblConnRow1Col4 = tblConnRow1.insertCell(4);
        tblConnRow1Col4.style.paddingLeft = "5px";
        tblConnRow1Col4.appendChild(document.createTextNode("Email Address"));
        
        var tblConnRow2 = tblCon.insertRow(1);

        var tblConnRow2Col0 = tblConnRow2.insertCell(0);
        var tblConnRow2Col1 = tblConnRow2.insertCell(1);
        var tblConnRow2Col2 = tblConnRow2.insertCell(2);
        var tblConnRow2Col3 = tblConnRow2.insertCell(3);
        var tblConnRow2Col4 = tblConnRow2.insertCell(4);
        tblConnRow2Col0.style.paddingLeft = "15px";
        tblConnRow2Col1.style.paddingRight = "5px";
        tblConnRow2Col2.style.paddingRight = "5px";
        tblConnRow2Col3.style.paddingRight = "5px";
        tblConnRow2Col4.style.paddingRight = "10px"
        tblConnRow2Col1.style.paddingLeft = "5px";
        tblConnRow2Col2.style.paddingLeft = "5px";
        tblConnRow2Col3.style.paddingLeft = "5px";
        tblConnRow2Col4.style.paddingLeft = "5px";
        tblConnRow2Col1.style.paddingBottom = "5px";
        tblConnRow2Col2.style.paddingBottom = "5px";
        tblConnRow2Col3.style.paddingBottom = "5px";
        tblConnRow2Col4.style.paddingBottom = "5px";

        var inArp = function(obj, width, marginRight, marginTop, defaultValue)
        { 
            if (defaultValue != null)
                obj.value = defaultValue;

            obj.style.display = 'inline'; 

            if (width != null)
                obj.style.width = width; 
            
            if (marginRight != null)
                obj.style.marginRight = marginRight;
            if (marginTop != null)
                obj.style.marginTop = marginTop;
        };

        var sl = document.createElement("select");
        slb = prefix + "Type";
        sl.name = slb.replace(/\_/g, '$');
        sl.id = slb.replace(/\$/g, "_");

        addOption(sl, 'Other', 0);
        addOption(sl, 'Owner', 4);
        addOption(sl, 'Lienholder', 5);
        addOption(sl, 'Insurance', 3);
        
        tblConnRow2Col0.appendChild(sl);

        clFullName = addTextbox(tblConnRow2Col1, prefix + "FullName");
        addTextbox(tblConnRow2Col2, prefix + "Phone");
        inArp(addTextbox(tblConnRow2Col3, prefix + "Address"), "250px");
        inArp(addTextbox(tblConnRow2Col3, prefix + "City"), "136px", "3px", "3px");
        inArp(addTextbox(tblConnRow2Col3, prefix + "State"), "50px", "3px", "3px", "<% = Global.CurrentUser.Company.State %>");
        inArp(addTextbox(tblConnRow2Col3, prefix + "Zip"), "50px", null, "3px");
        addTextbox(tblConnRow2Col4, prefix + "Email");

        hidden = addTextbox(tblConnRow2Col3, prefix + "DataId", "hidden");
        hidden.value = '-1';

        cell2.appendChild(tblCon);
        
        divContainer.appendChild(tbl);
        $('myContacts').appendChild(divContainer);
        $(divContainer).slideDown({ duration: 0.5, afterFinish: function() { 
        
            if (highlight != null)
            {
                new Effect.Highlight(divContainer.id,{ startcolor: '#afafaf', endcolor: '#efefef', duration: 0.5, 
                    afterFinish: function() { new Effect.Highlight(divContainer.id,{ startcolor: '#efefef', endcolor: '#ffffff', duration: 1.2 }); }  });   
            }
            else
                setTimeout("document.getElementById('" + prefix + "FullName').focus()", 100) } });
    }
    
    function clearEmptyBoxes()
    {
        var myDiv = document.getElementById('myContacts');
        var iteration = myDiv.childNodes.length; 
        
        var row;
        
        if (iteration > 0)
            for(i = 0; i < myDiv.childNodes.length; i++)
            {
                row = myDiv.childNodes[i];
                if (row.innerHTML != null)
                {                
                    var els = row.getElementsByTagName('INPUT');
                    if (els[0].value.length + els[1].value.length + els[2].value.length == 0)
                    {
                        myDiv.removeChild(myDiv.childNodes[i]);        
                    }
                }
            }
    }
    
    function addBox(cell, name, type) {
        var el2 = document.createElement('input');
        el2.type = (type == null ? 'text' : type);
        el2.name = name;
        el2.id = "in" + name;
        el2.style.margin = "0"
        cell.appendChild(el2);
        
        return el2;
    }

    function removeElement(divNum) {
        var d = document.getElementById('myContacts');
        var olddiv = document.getElementById(divNum);
        d.removeChild(olddiv);
    }
    function ToggleFields(header, container) {
        Effect.toggle(container, 'slide', { duration: 0.5 });
        if ($(container).style.display == 'none') {
            $(header).morph('color:#000', { duration: 0.3 });
        } else {
            $(header).morph('color:#555', { duration: 0.3 });
        }
    }
    
    function dlTowToType_OnChange(obj)
    {
        switch(eval(obj.value))
        {
            case 0:
                $('<% =txtEndAddress.ClientID %>').style.display = 'inline';
                $('<% =dlImpoundLots.ClientID %>').style.display = 'none';
                
                break;
            case 1:
                $('<% =txtEndAddress.ClientID %>').style.display = 'none';
                $('<% =dlImpoundLots.ClientID %>').style.display = 'inline';
                break;
        }
    
    }
    function rbAccount_OnChange(obj)
    {

    }
    
    function mininavDo(container, activeLink) {
        var list = $(container).getElementsByTagName("A")
        for (i = 0; i < list.length; i++) {
            list[i].className = '';
            if (list[i].innerText != null)
                list[i].innerText = list[i].innerText.replace(">> ", "");
        }

        activeLink.className = 'sel';
        activeLink.innerText = ">> " + activeLink.innerText;
    }

    function mininavMakeToggle(showList) {
        $('<% =dlMake.ClientID  %>').style.display = (showList == true ? 'inline' : 'none');
          $('<% =txtMake.ClientID  %>').style.display = (showList == true ? 'none' : 'inline');
          $('<% =nMakeSaveMode.ClientID  %>').value = (showList == true ? 'list' : 'saved');
          
          if (showList == false) // dropdown 
          {
              mininavModelToggle(false);
              mininavDo('mininav_model', $('model_mnType'));
          }
      }
      
      function mininavModelToggle(showList) {
          $('<% =dlModel.ClientID  %>').style.display = (showList == true ? 'inline' : 'none');
          $('<% =txtModel.ClientID  %>').style.display = (showList == true ? 'none' : 'inline');
          $('<% =nModelSaveMode.ClientID  %>').value = (showList == true ? 'list' : 'saved');

          if (showList == true)
          {
              mininavMakeToggle(true);
              mininavDo('mininav_make', $('make_mnList'));
          }
      }
      
      var prioritySet = false;
</script>

<input type="hidden" id="activeTab" name="activeTab" value="" />

<div id="tabcontrol1">
    <div id="tabGeneral" class="TabContainer">
        <a href="AccountFinder.aspx" id="accountLaunch" rel="lightbox"></a>
        <input type="hidden" name="customerAccountId" id="iCustomerAccountId" value="<% if (_entry != null && _entry.Account != null) { Response.Write(_entry.Account.Id); } %>" />
        <input type="hidden" name="partialPostback" id="iPartialPostback" value="0" />
  <table class="editor" cellspacing="0" style="<% if (HideFields.Contains("TowType")) { %>; display: none <% } %>">
    <tr>
      <td class="Field" id="tdAccount" style="width: auto; border-left: dotted 1px #afafaf; padding: 0">

       <div id="divMotorClub" style="display: <% =(_entry.RequestedBy == Extric.Towbook.Accounts.AccountType.MotorClub ? "block" : "none") %>; border-top: dotted 1px #afafaf">
        <table style="border-collapse: collapse">
          <tr>
            <td style="width: 200px; padding-left: 10px; ">Motor Club Dispatch Number</td>
            <td style="width: 200px; padding-left: 10px; border-left: dotted 1px #afafaf">Motor Club Membership Number</td>
          </tr>
          <tr>
            <td style="padding-right: 10px; padding-left: 10px; padding-bottom: 10px"><asp:TextBox style="Width: 100%" runat="server" ID="motorClubDispatchNumber" /></td>
            <td style="padding-right: 10px; padding-left: 10px; padding-bottom: 10px; border-left: dotted 1px #afafaf"><asp:TextBox  style="Width: 100%" runat="server" ID="motorClubMembershipNumber" /></td>
          </tr>
        </table>
        </div>
        
        <div id="divAccountPO" style="display: <% =(_entry.RequestedBy != Extric.Towbook.Accounts.AccountType.MotorClub && _entry.RequestedBy != Extric.Towbook.Accounts.AccountType.PoliceDepartment && _entry.RequestedBy != Extric.Towbook.Accounts.AccountType.Individual ? "block" : "none") %>; border-top: dotted 1px #afafaf">
        <table style="border-collapse: collapse">
          <tr>
            <td style="width: 200px; padding-left: 10px; ">Purchase Order Number <span style="font-weight: normal">(optional)</span></td>
          </tr>
          <tr>
            <td style="padding-right: 10px; padding-left: 10px; padding-bottom: 10px"><asp:TextBox style="Width: 100%" runat="server" ID="accountPurchaseOrderNumber" /></td>
          </tr>
        </table>
        </div>
        
        <div style="clear: both;border-top: solid 1px #afafaf;"></div>

        <div id="customAttributes"></div>

        <div style="clear: both"></div>

	<div id="chooseBillingAccount" style="<%if (_accounts != null && _accounts.Count > 0) { %>display:block<% } else { %>display:none<% } %>; padding: 10px">
		<label>Who should be billed for this call?</label>
	<select id="dlAccount" runat="server">
        	<option value="-1">(select who is paying for this call)</option>
      	</select>
	</div>


        <div id="divContactDetails" style="padding-bottom: 10px">
        <div id="myContacts">
        <asp:Repeater runat="server" ID="rpContacts">
            <ItemTemplate>
            <div id="divholder<%# (Container.ItemIndex) %>">
            <table class="editor" cellspacing="0">
              <tr>
                <td class="Field" style="padding: 0">
                    <table class="editor" cellspacing="0">
                      <tr>
                        <td style="width: 95px; padding-left: 10px">Type</td>
                        <td style="width: 150px; padding-left: 5px;"><% if (Global.CurrentUser.CompanyId == 4082) { %> Customer <% } %>Name</td>
                        <td style="width: 100px; padding-left: 5px">Phone</td>
                        <td style="width: 250px; padding-left: 5px">Address</td>
                        <td style="padding-left: 5px">Email Address</td>
                      </tr>
                      <tr>
                        <td style="padding-right: 5px; padding-left: 10px; padding-bottom: 10px">
                        <select name="<%# (rpContacts.UniqueID + "$ctl" + Container.ItemIndex.ToString("00")) %>Type">
                          <option value="0">Other</option>
                          <option value="4" <%# (((Extric.Towbook.Dispatch.EntryContact)Container.DataItem).Type == Extric.Towbook.Dispatch.ContactType.Owner) ? "selected" : "" %>>Owner</option>
                          <option value="5"  <%# (((Extric.Towbook.Dispatch.EntryContact)Container.DataItem).Type == Extric.Towbook.Dispatch.ContactType.Lienholder) ? "selected" : "" %>>Lienholder</option>
                        </select>
                        </td>
                        <td style="padding-right: 5px; padding-left: 5px; padding-bottom: 10px"><input type="text" id="<%# (rpContacts.ClientID + "_ctl" + Container.ItemIndex.ToString("00")) %>FullName" name="<%# (rpContacts.UniqueID + "$ctl" + Container.ItemIndex.ToString("00")) %>FullName" value="<%# DataBinder.Eval(Container.DataItem, "Name") %>" /></td>
                        <td style="padding-right: 5px; padding-left: 5px; padding-bottom: 10px"><input type="text" id="<%# (rpContacts.ClientID + "_ctl" + Container.ItemIndex.ToString("00")) %>Phone" name="<%# (rpContacts.UniqueID + "$ctl" + Container.ItemIndex.ToString("00")) %>Phone" value="<%# DataBinder.Eval(Container.DataItem, "Phone") %>" /></td>
                        <td style="padding-right: 5px; padding-left: 5px; padding-bottom: 10px">
                          <input type="text" style="width: 250px"  id="<%# (rpContacts.ClientID + "_ctl" + Container.ItemIndex.ToString("00")) %>Address" name="<%# (rpContacts.UniqueID + "$ctl" + Container.ItemIndex.ToString("00")) %>Address" value="<%# DataBinder.Eval(Container.DataItem, "Address") %>" />
                          <input type="text" style="width: 136px; display: inline"  id="Text2" name="<%# (rpContacts.UniqueID + "$ctl" + Container.ItemIndex.ToString("00")) %>City" value="<%# DataBinder.Eval(Container.DataItem, "City") %>" />
                          <input type="text" style="width: 50px; display: inline"  id="Text3" name="<%# (rpContacts.UniqueID + "$ctl" + Container.ItemIndex.ToString("00")) %>State" value="<%# DataBinder.Eval(Container.DataItem, "State") %>" />
                          <input type="text" style="width: 50px; display: inline"  id="Text4" name="<%# (rpContacts.UniqueID + "$ctl" + Container.ItemIndex.ToString("00")) %>Zip" value="<%# DataBinder.Eval(Container.DataItem, "Zip") %>" />
                        </td>
                        <td style="padding-right: 10px; padding-left: 5px; padding-bottom: 10px"><input type="text" id="<%# (rpContacts.ClientID + "_ctl" + Container.ItemIndex.ToString("00")) %>Email" name="<%# (rpContacts.UniqueID + "$ctl" + Container.ItemIndex.ToString("00")) %>Email" value="<%# DataBinder.Eval(Container.DataItem, "Email") %>" /></td>
                      </tr>
                    </table>
                    <input type="hidden" name="<%# (rpContacts.UniqueID + "$ctl" + Container.ItemIndex.ToString("00")) %>DataId" value="<%# DataBinder.Eval(Container.DataItem, "Id") %>" />
                  </td>
                </tr>
              </table>
            </div>
            </ItemTemplate>
        </asp:Repeater>
        </div>        
        <div style="border-top: dotted 1px #afafaf; padding-left: 10px" id="divMoreContactsLink">
            <a style="margin: 5px; display: block; padding-top: 10px" href="javascript:;" onclick="addContact();">Need to specify more contacts? Click here</a>
        </div>
      </div>
      </td>
    </tr>
  </table>

  <table class="editor" cellspacing="0" <% if (_account.Type != Extric.Towbook.Accounts.AccountType.PoliceDepartment) { %>style="display: none" <% } %>>    
    <tr>
        <td class="Title"><label for="<% =dlTowReason.ClientID %>">Primary Reason</label><asp:DropDownList runat="server" ID="dlTowReason" /></td>
<!--        
        <% if (!HideFields.Contains("Priority")) { %>
        <td class="Field" id="priorities" style="border-left: dotted 1px #afafaf"><strong>Priority</strong><br /><asp:RadioButton runat="server" ID="priorityNormal" GroupName="Priority" Text="Normal" ForeColor="darkslateblue" onclick="prioritySet=true;" />
          <asp:RadioButton runat="server" ID="priorityHigh" GroupName="Priority" Text="High" ForeColor="red" onclick="prioritySet=true;" />
          <asp:RadioButton runat="server" ID="priorityLow" GroupName="Priority" Text="Low" ForeColor="gray" onclick="prioritySet=true;" /></td>
        <% } %>
        <td class="Field" id="Td1" <% if (HideFields.Contains("Priority")) { %>colspan="2"<% } %><% if (Global.CurrentUser.Company.Id != 311) { %> style="border-left: dotted 1px #afafaf"><strong>Estimated Arrival <span style="font-weight: normal">(optional)</span></strong>
        <asp:DropDownList runat="server" ID="dlETA"></asp:DropDownList> <% } else { %>><% } %></td>
        <td class="Field" style="border-left: dotted 1px #afafaf">Invoice# <span style="font-weight: normal" id="ticketErrorMsg">(optional)</span>
        <asp:Textbox ID="txtInvoiceNumber" runat="server" onkeyup="return checkTicket(this, event);" />
      
        </td> -->

    </tr>

    <tr>
    
        <td class="Title" colspan="4" style="line-height: 200%; padding-top: 2px<% if (HideFields.Contains("TowType")) { %>; display: none <% } %>" id="tdTowType">What type of tow?<asp:DropDownList runat="server" ID="dlTowType" onchange="onTowTypeChange(this)" /></td>
    </tr>
  </table>
  
<div class="SubHeading" id="shLocation" onclick="ToggleFields('shLocation', 'divLocation');">Location &amp; Travel Details</div>
  <div id="divLocation">
<table class="editor" cellspacing="0">
    <tr>
        <td class="Title" style="line-height: 200%; padding-top: 2px; width: 310px; padding-right: 20px">
          <label for="<% =txtStartAddress.ClientID %>">Tow From</label>
          <asp:TextBox runat="server" ID="txtStartAddress" /><asp:RequiredFieldValidator ID="RequiredFieldValidator3" runat="server" ControlToValidate="txtStartAddress" Display="Dynamic" Enabled="true"
            ErrorMessage="Towing From location is required" ForeColor="red"></asp:RequiredFieldValidator>
        </td> 
        <td class="FieldMulti" style="border-left: dotted 1px #afafaf">
<label for="<% =txtEndAddress.ClientID %>">Tow To (Address or Impound lot)</label>
          <table style="width: 100%; border-collapse: collapse" cellpadding="0">
            <tr>
              <td>
                <asp:DropDownList runat="server" ID="dlTowToType" onchange="dlTowToType_OnChange(this)">
                  <asp:ListItem Value="0">Address</asp:ListItem>
                  <asp:ListItem Value="1">Impound</asp:ListItem>
                </asp:DropDownList>
              </td>
              <td style="width: 100%; padding-right: 10px; padding-left: 5px"><asp:TextBox runat="server" ID="txtEndAddress" style="width: 100%; margin-top: 2px" />
              <asp:DropDownList ID="dlImpoundLots" runat="server" style="width: 100%" /></td>
            </tr>
          </table>
        </td>
    </tr>
</table>
</div>

<div class="SubHeading" id="shVehicleDetails" onclick="ToggleFields('shVehicleDetails', 'divVehicle');">Vehicle Details <span>(click here to specify vehicle details)</span></div>
  <div id="divVehicle">
    <table class="editor" cellspacing="0">        
      <tr>
        <td class="Title">Year<br /><asp:DropDownList runat="server" ID="dlYear" /></td>
        <td class="Title" style="border-left: dotted 1px #afafaf; width: auto" colspan="2"><span style="float: left">Make</span>
          <input type="hidden" ID="nMakeSaveMode" value="type" runat="server" />
          
          <span style="float: right; font-weight: normal" class="mininav" id="mininav_make">
          <a href="javascript:;" id="make_mnList" onclick="mininavDo('mininav_make', this); mininavMakeToggle(true); " class="sel">&gt;&gt; built-in</a> |
          <a href="javascript:;" id="make_mnType" onclick="mininavDo('mininav_make', this); mininavMakeToggle(false);">custom</a></span><br />
          
        <asp:DropDownList runat="server" ID="dlMake" onchange="onMakeChange(this)" />
        <asp:Textbox runat="server" ID="txtMake" style="display: none" /></td>
        <td class="Field" style="border-left: dotted 1px #afafaf; font-weight: bold"><span style="float: left">Model</span>
        <input type="hidden" ID="nModelSaveMode" value="type" runat="server" />
        <span style="float: right; font-weight: normal" class="mininav" id="mininav_model">
          <a href="javascript:;" id="model_mnList" onclick="mininavDo('mininav_model', this); mininavModelToggle(true);" class="sel">&gt;&gt; built-in</a> |
          <a href="javascript:;" id="model_mnType" onclick="mininavDo('mininav_model', this); mininavModelToggle(false);">custom</a></span><br />
          <asp:Textbox runat="server" ID="txtModel" style="display: none" />
        <asp:DropDownList runat="server" ID="dlModel" /></td>
      </tr>
      <tr>
        <td class="Title" id="tdPlateNumber"><label for="<% =txtPlateNumber.ClientID %>">License Plate #</label><asp:TextBox runat="server" ID="txtPlateNumber" onblur="decodePlate(this.value,  document.getElementById('content_txtPlateState').value )"  /></td>
        <td class="Title" id="tdPlateState" style="border-left: dotted 1px #afafaf"><label for="<% =txtPlateState.ClientID %>">License Plate <% = (Global.CurrentUser.Company.LocaleStateName) %></label><asp:TextBox runat="server" ID="txtPlateState"  onblur="decodePlate(document.getElementById('content_txtPlateNumber').value, document.getElementById('content_txtPlateState').value)" /></td>
        <td class="Field" id="drivable" style="width: 150px; border-left: dotted 1px #afafaf">Is the vehicle drivable?<br />
        <div style="font-weight: normal"><asp:RadioButton runat="server" ID="rbDrivableYes" GroupName="Drivable" Text="Yes" /> | 
        <asp:RadioButton runat="server" ID="rbDrivableNo" GroupName="Drivable" Text="No" /></div></td>
        <td class="Field" style="border-left: dotted 1px #afafaf"><label for="<% =txtOdometer.ClientID %>">Odometer</label><asp:TextBox runat="server" ID="txtOdometer" onkeypress="return CheckKeyCode(this, event, false, true);" style="min-width: 100px"  /></td>
      </tr>
      <tr>            
        <td class="Title" style="line-height: 200%; padding-top: 2px">Color<br /><asp:DropDownList runat="server" ID="dlColor" /></td>
        <td class="Title" style="border-left: dotted 1px #afafaf; width: auto" colspan="3"><label for="<% =txtVIN.ClientID %>">17-Digit VIN Number</label> <asp:CustomValidator ID="CustomValidator1" runat="server" 
                ClientValidationFunction="validateVINEx" ControlToValidate="txtVIN"
                Display="Dynamic" ErrorMessage="VIN Number is invalid." Font-Bold="True" ValidateEmptyText="true">INVALID VIN</asp:CustomValidator>
                <span id="vinRequired"></span><br />
            <asp:TextBox runat="server" onblur="decodeVin(this.value)" ID="txtVIN" MaxLength="17" /></td>
      </tr>
    </table>
  </div>

    <div class="SubHeading" id="shNotes" onclick="ToggleFields('shNotes', 'divNotes');">Notes</div>
    <div id="divNotes">
      <table class="editor" cellspacing="0">
        <tr>
          <td class="Title">Notes</td>
          <td class="Field"><asp:TextBox runat="server" ID="txtNotes"  TextMode="MultiLine" Height="50px" style="border: solid 1px #afafaf" /></td>
          <td class="Description2">Optional</td>
        </tr>
      </table>
    </div>


    <asp:Repeater runat="server" ID="rpAttributes" OnItemDataBound="rpAttributes_ItemDataBound">
      <HeaderTemplate>
        <div class="SubHeading" id="shAttributes" onclick="ToggleFields('shAttributes', 'divAttributes');">Additional Fields</div>
        <div id="divAttributes">
          <table class="editor" cellspacing="0">
      </HeaderTemplate>
      <ItemTemplate>
            <tr>
              <td class="Title"><%# DataBinder.Eval(Container.DataItem, "Name") %><input type="hidden" runat="server" id="DataId" /></td>
              <td class="Field"><asp:TextBox runat="server" ID="Value"  TextMode="SingleLine" style="border: solid 1px #afafaf" /></td>
              <td class="Description2">Optional</td>
            </tr>
      </ItemTemplate>
      <FooterTemplate>
          </table>
        </div>
    </FooterTemplate>
    </asp:Repeater>
  </div>
    <div class="TabContainerFooter">
        <div style="float: left"><asp:ValidationSummary ID="ValidationSummary1" runat="server" ShowMessageBox="false" ShowSummary="false"  />
        <asp:Button ID="btnSave" runat="server" Text="Save" CssClass="sm" OnClick="btnSave_Click" UseSubmitBehavior="False" />
        <input type="button" id="Button2" value="Cancel" class="sm" onclick="window.history.go(-1)" />    
        </div>
    </div>   
</div>
       

    <script type="text/javascript">
        $('divContactDetails').style.display =
        $('divLocation').style.display =
        $('divVehicle').style.display = 'block';
        $('divNotes').style.display = 'none';
        if ($('divAttributes') != null) $('divAttributes').style.display = 'none';

        
        var ticketAttributeValues = {  };


        var ticketAttributes ='';
        new Ajax.Request('/dyreq/json/ticketattributes.ashx', {
            method: 'get',
            parameters: { cmd: 'type' },
            onSuccess: function (transport) {
                ticketAttributes = transport.responseText.evalJSON().attributes;
                var t2 = new Template('<div style="border-right: dotted 1px #afafaf; padding: 10px; float: left"><strong>#{n}</strong><input type="textbox" value="#{value}"style="width: 190px" name="customAttribute_#{id}" /></div>');
                $('customAttributes').update('');
            
                var type = <% 

	if (Global.CurrentUser.AccountId > 0)
		Response.Write(Convert.ToInt32(Extric.Towbook.Accounts.Account.GetById(Global.CurrentUser.AccountId).Type).ToString());
	else
		Response.Write("null");

 %>;

            for(var x = 0; x < ticketAttributes.length; x++)
            {
                if (ticketAttributes[x].at == type)
                {
                    ticketAttributes[x].value = '';
                    var out = t2.evaluate(ticketAttributes[x]);
                    $('customAttributes').insert(out);
                }
            }
        }
    });
<% if (_account.Type != Extric.Towbook.Accounts.AccountType.PoliceDepartment && _entry.Id < 1) { %>
        addContact(false);
        <% } %>

    </script>    
</form>
</asp:Content>

<asp:Content ID="Toolbar1" ContentPlaceHolderID="Toolbar" runat="server">
  <li><a href="javascript:;" onclick="<% =btnSave.ClientID %>.click()"><span style="background-image: url(../UI/Images/ToolbarIcons/Save.png)"></span>Save Changes</a></li>
  <li><a href="Default.aspx" onclick="return confirm('Are you sure you want to go back without saving changes?')"><span style="background-image: url(../UI/Images/ToolbarIcons/Cancel.png)"></span>Cancel/Discard changes</a></li> 
</asp:Content>

