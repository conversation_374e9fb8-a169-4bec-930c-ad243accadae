<%@ Page Language="C#" Async="true" %>
<%@ Import Namespace="NVelocity.Runtime" %>
<%@ Import Namespace="NVelocity.App" %>
<%@ Import Namespace="NVelocity" %>
<%@ Import Namespace="Commons.Collections" %>
<%@ Import Namespace="System.IO" %>
<%@ Import Namespace="Extric.Towbook.WebShared" %>
<%@ Import Namespace="Extric.Towbook.API.Models.Calls" %>
<%@ Import Namespace="Extric.Towbook.Utility" %>
<%@ Import Namespace="System.Threading.Tasks" %>

<script runat="server">

    private string BuildQuery()
    {
        StringBuilder sb = new StringBuilder();
        sb.Append("?");
        foreach (string key in Request.QueryString.Keys)
        {
            if (key.ToLowerInvariant() == "pdf")
                continue;

            sb.Append(key.ToLowerInvariant());
            sb.Append("=");
            sb.Append(Request.QueryString[key]);
            sb.Append("&");
        }

        return sb.ToString().Trim('&');
    }

    public void Page_Load(object sender, EventArgs e)
    {
        RegisterAsyncTask(new PageAsyncTask(PageLoadAsync));
    }

    private async Task PageLoadAsync()
    {
        if (Request.QueryString["pdf"] == "1")
        {
            System.IO.StringWriter sw = new System.IO.StringWriter();

            Server.Execute((Request.Path+ BuildQuery()).Trim('?'), sw);

            //Create a PdfDocument object and convert
            //the Url into the PdfDocument object
            var outputArea = new OutputArea(0.3f, 0.3f, 7.8f, 10.3f);
            var stream = await PdfClientBase.GeneratePdf(sw.ToString(), null, outputArea, null, FileType.PDF,
                "<div style=\"color: #333333; font-family: verdana; font-size: 8px; font-weight: bold\"><br /><br />Created with Towbook Management Software | www.towbook.com</div>");

            //Setup HttpResponse headers
            HttpResponse response = HttpContext.Current.Response;
            response.Clear();
            response.ClearHeaders();
            response.ContentType = "application/pdf";

            //Send the PdfDocument to the client
            await stream.CopyToAsync(response.OutputStream);
        }
    }

    public string ParseTemplate(string template, Extric.Towbook.Accounts.Account account)
    {
        var ve = new VelocityEngine();
        var ep = new ExtendedProperties();


        ve.Init(ep);

        var vc = new VelocityContext();

        vc.Put("Account", account);
        vc.Put("Company", Global.CurrentUser.Company);

        // compile a list of all categories and total them up

        StringWriter writer = new StringWriter();

        ve.Evaluate(vc, writer, "Account Template", template);

        return writer.GetStringBuilder().ToString().Replace("<tk:Dynamic>", "").Replace("</TK:Dynamic>", "");
    }
</script>
<style> p, span { font-size: 14px !important; font-family: calibri; } 
body { max-width: 800px; margin: 0 auto; }</style>

<%
try
{
Response.Write(
	ParseTemplate(Extric.Towbook.LetterTemplate.GetById(Convert.ToInt32(Request.QueryString["id"])).Contents, 
		Extric.Towbook.Accounts.Account.GetById(Global.CurrentUser.AccountId)).
		Replace("$CurrentDate", DateTime.Now.ToShortDate()));
}
catch
{
Response.Write("error");
}
%>
