using Extric.Towbook;
using Extric.Towbook.Company;
using Extric.Towbook.Utility;
using Extric.Towbook.WebShared;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using Extric.Towbook.Dispatch;
using System.Text;
using Commons.Collections;
using System.IO;
using NVelocity;
using NVelocity.App;
using System.Data;
using Extric.Towbook.API.Models.Calls;
using System.Threading.Tasks;

public partial class Dispatch_LetterTemplate : System.Web.UI.Page
{
    protected int _callId;
    protected Extric.Towbook.Dispatch.Entry _de;

    protected void Page_Load(object sender, EventArgs e)
    {
        RegisterAsyncTask(new PageAsyncTask(PageLoadAsync));
    }

    private async Task PageLoadAsync()
    {
        if (Request.QueryString["callId"] == null ||
        Request.QueryString["callId"].Length == 0 ||
        Request.QueryString["callId"] == "0")
        {
            throw new Extric.Towbook.TowbookException("id is missing");
        }

        _callId = Convert.ToInt32(Request.QueryString["callId"]);
        _de = Extric.Towbook.Dispatch.Entry.GetById(_callId);

        if (WebGlobal.CurrentUser == null)
        {
            throw new Exception("You don't have access to this page.");
        }
        else if (!WebGlobal.CurrentUser.HasAccessToCompany(_de.CompanyId))
        {
            throw new Exception("Access Denied.  You don't have permission or the callId specified is invalid.");
        }

        if (Request.QueryString["templateId"] != null)
        {
            int letterTemplateId = Convert.ToInt32(Request.QueryString["templateId"]);
            LetterTemplate lt = (await LetterTemplate.GetByCompanyAsync(WebGlobal.CurrentUser.Company)).FirstOrDefault(f => f.Id == letterTemplateId);
            if (lt == null || (!lt.IsStateLetter && !WebGlobal.CurrentUser.HasAccessToCompany(lt.CompanyId)))
                throw new TowbookException("The letter template doesn't exist or you don't have permission to access it.");

            if (System.IO.File.Exists(Server.MapPath("../storage/forms/" + lt.Id + ".pdf")))
            {
                // PDF form

                string assignments = "";

                string file = Server.MapPath("../storage/forms/" + letterTemplateId + ".pdf");

                var bytes = File.ReadAllBytes(file);
                var fields = await PdfClientBase.GetFields(bytes);
                var keyValuesFromDb = new Dictionary<string, string>();

                using (var dr = await SqlHelper.ExecuteReaderAsync(Extric.Towbook.Core.ConnectionString, "dbo.LetterTemplateMergeFieldsGetByLetterTemplateId",
                            letterTemplateId))
                {

                    while (await dr.ReadAsync())
                    {
                        string key = dr["Field"].ToString();
                        string value = dr["Value"].ToString();

                        if (!keyValuesFromDb.Select(s => s.Key).Contains(key))
                            keyValuesFromDb.Add(key, value);
                        else
                            keyValuesFromDb[key] = value;
                    }
                }

                // parse letter fields, add values to pdf document and return an html output
                assignments = await TranslateDBtoDocFieldsAsync(keyValuesFromDb, fields, _de);

                //Setup HttpResponse headers
                HttpResponse response = HttpContext.Current.Response;
                if (Request.QueryString["debug"] != null)
                {

                    response.Clear();
                    response.ClearHeaders();
                    response.ContentType = "text/html";
                    Response.Write(fields.Count);
                    response.Write("<table style=\"font-family: calibri; font-size: 12px\">" + assignments + "</table>");
                    response.End();
                }
                else
                {
                    //Setup HttpResponse headers
                    response.Clear();
                    response.ClearHeaders();
                    response.ContentType = "application/pdf";
                }

                //Send the PdfDocument to the client
                var stream = await PdfClientBase.FillOutPdf(bytes, fields, null, null, null);
                await stream.CopyToAsync(response.OutputStream);
            }
            else
            {
                // Letter Template
                bool no10EnvelopeFormatted = false;

                var coreAttributes = await Extric.Towbook.Dispatch.Attribute.GetByDispatchEntryAsync(_de.Company, _de, false);

                Dictionary<string, string> attributes = new Dictionary<string, string>();

                foreach (var av in coreAttributes)
                {
                    string value = "";

                    if (_de.Attributes.ContainsKey(av.Id))
                    {
                        value = _de.Attributes[av.Id].Value;
                    }
                    else
                    {
                        value = "<span style='color: red; font-weight: bold; font-size:25px;'>[" + av.Name.ToUpper() + " NOT ENTERED]</span>";
                        _de.Attributes.Add(av.Id, new Extric.Towbook.Dispatch.AttributeValue() { Value = value });
                    }

                    if (!attributes.ContainsKey(av.Name))
                        attributes.Add(av.Name, value);
                }

                if (!attributes.ContainsKey("towing_company_name"))
                {
                    attributes.Add("towing_company_name", "$Company.Name, $Company.Address, $Company.City $Company.State $Company.Zip, $Company.Phone");
                }

                if (Request.QueryString["pdf"] != "0")
                {
                    var sw = new System.IO.StringWriter();

                    string imageHtml = "";
                    var cl = Extric.Towbook.Company.CompanyLogo.GetByCompanyId(_de.Company.Id);

                    if (cl != null)
                    {
                        imageHtml = "<img src=\"" + cl.Url + "\" />";
                    }
                    else
                    {
                        string[] opportunities = new string[] { "_template.jpg", "_full.jpg", "_left.jpg", ".jpg" };
                        string baseLocal = Server.MapPath(@"..\ui\images\customer.logo\" + _de.Company.Id);
                        string baseRemote = "https://app.towbook.com/ui/images/customer.logo/" + _de.Company.Id;
                        string filename = "";

                        foreach (var file in opportunities)
                        {

                            if (System.IO.File.Exists(baseLocal + file))
                            {
                                filename = baseRemote + file;
                                break;
                            }
                        }

                        if (!string.IsNullOrEmpty(filename))
                            imageHtml = "<img src=\"" + filename + "\" />";
                    }
                    var template = lt;

                    /////////////////

                    var outputArea = new OutputArea(0.3f, 0.3f, 7.8f, 10.3f);
                    string footerHtml = null;

                    bool hideFooter = new int[] { 17609 }.Contains(_de.CompanyId);
                    if (!hideFooter)
                        footerHtml = "<div style=\"color: #333333; font-family: verdana; font-size: 9px; font-weight: bold\"><br /><br />Created with Towbook Management Software | www.towbook.com <div style=\"display:inline-block; float:right; font-weight: normal;\">Printed " + DateTime.Now.ToShortDate() + "</div></div>";

                    var htmls = new List<string>();

                    if (no10EnvelopeFormatted)
                    {
                        // make sure there is at least one contact to avoid an error
                        if (_de.Contacts.Count == 0)
                            _de.Contacts.Add(new Extric.Towbook.Dispatch.EntryContact());

                        bool ccContacts = new int[] { 1347, 1348, 2271 }.Contains(letterTemplateId);

                        foreach (var contact in _de.Contacts)
                        {
                            var letter = await GenerateLetterHtmlAsync(template,
                                _de.Contacts.Where(w => w.Id == contact.Id).ToCollection(),
                                attributes,
                                coreAttributes,
                                imageHtml,
                                true,
                                (ccContacts ? _de.Contacts.Where(w => w.Id != contact.Id).ToCollection() : null));

                            htmls.Add(letter);
                        }
                    }
                    else
                    {
                        var letter = await GenerateLetterHtmlAsync(template, _de.Contacts, attributes, coreAttributes, imageHtml, false, null);
                        htmls.Add(letter);
                    }

                    var stream = no10EnvelopeFormatted ? await PdfClientBase.GeneratePdf(htmls, null, outputArea, null, footerHtml, true)
                        : await PdfClientBase.GeneratePdf(htmls, null);

                    //Setup HttpResponse headers
                    HttpResponse response = HttpContext.Current.Response;
                    response.Clear();
                    response.ClearHeaders();

                    response.ContentType = "application/pdf";

                    //var contentDispositionHeader = new ContentDisposition() { FileName = "invoice-" + _de.CallNumber + ".pdf" };
                    //response.AddHeader("Content-Disposition", contentDispositionHeader.ToString());


                    //Send the PdfDocument to the client
                    await stream.CopyToAsync(response.OutputStream);

                    response.End();
                }

                Response.Write("<html><head><style> body { max-width: 1000px; margin: 0 auto; font-size: 14px } * { font-family: calibri, verdana }</style></head><body>");

                Response.Write(await ParseTemplateAsync(lt,
                    _de,
                    new Dictionary<string, string>()));

                Response.Write("</body></html>");
                Response.End();
                return;
            }
        }
    }

    public async Task<string> GenerateLetterHtmlAsync(LetterTemplate template,
        Collection<Extric.Towbook.Dispatch.EntryContact> Contacts,
        Dictionary<string, string> attributes,
        Collection<Extric.Towbook.Dispatch.Attribute> coreAttributes,
        string imageHtml,
        bool no10Envelope = false,
        Collection<Extric.Towbook.Dispatch.EntryContact> ccContacts = null)
    {
        System.IO.StringWriter sw = new System.IO.StringWriter();

        bool showStockNumberAsInvoiceNumber = new int[] { 3087, 5348 }.Contains(_de.CompanyId);
        var No10EnvelopeFormatted = no10Envelope;
        bool hideTitle = new int[] { 1418, 1419, 1420, 1698, 1699, 1700, 2348 }.Contains(template.Id);
        bool useLienholder = new int[] { 1419, 1666, 1850 }.Contains(template.Id);
        bool showTotalInSummaryBlock = new int[] { 3087 }.Contains(_de.CompanyId);
        bool showDailyStorageInSummaryBlock = (_de.Company.State.ToLowerInvariant() == "WA" && _de.Company.Country == Company.CompanyCountry.USA) 
                                                    || new int[] { 835 }.Contains(_de.CompanyId) ? false : true;

        string releaseBirthDate = "N/A";
        string releasePhone = "N/A";

        string tdlrNumber = null;
        string vsfNumber = null;

        var licenseValues = Extric.Towbook.Licenses.CompanyLicenseKeyValue.GetByCompanyId(_de.CompanyId);
        var keys = Extric.Towbook.Licenses.CompanyLicenseKey.GetByCompany(_de.Company);
        var printables = licenseValues.Where(y => keys.Where(o => o.Id == y.KeyId && o.ShowValueOnPrintables == true).Any());
        Dictionary<string, string> licenses = new Dictionary<string, string>();

        foreach (var k in keys)
        {
            if (!attributes.ContainsKey(k.Name) && licenseValues.Select(s => s.KeyId).Contains(k.Id))
            {
                var lv = licenseValues.Where(w => w.KeyId == k.Id).FirstOrDefault();
                if (lv != null)
                {
                    licenses.Add(k.Name, lv.Value);
                }
            }
        }

        if (printables != null)
        {
            var tdlr = printables.Where(o => o.KeyId == 2).FirstOrDefault();
            var vsf = printables.Where(o => o.KeyId == 3).FirstOrDefault();

            if (tdlr != null)
                tdlrNumber = tdlr.Value;
            if (vsf != null)
                vsfNumber = vsf.Value;
        }

        string companyLicenses = "";
        foreach (var p in printables)
        {
            var key = keys.FirstOrDefault(s => s.Id == p.KeyId);
            companyLicenses += key.Name + " " + p.Value + "<br/>";
        }

        string companyHeader = "<div id=\"company_header\"><p style=\"text-align: center; font-family: segoe ui, 'Open Sans' !important; font-size: medium;\"><span style=\"font-size: 24px\">$Company.Name<br /></span><span style=\"font-size: small;\">$Company.Address, </span><span style=\"font-size: small;\">$Company.City $Company.State $Company.Zip<br /></span><span style=\"font-size: small;\">Office: $Company.Phone&nbsp&nbsp;</span>" +
            (_de.Company.Fax != null ? "<span style=\"font-size: small;\">Fax: $Company.Fax</span>" : "") +
            "<span style=\"font-size: small\"><br>" + companyLicenses + "</span>" +
            "<div style=\"text-align: center; font-size: 24px\">" + template.Title + "</div></div> ";

        string companyHeaderNoTitle = "<div id=\"company_header\"><p style=\"text-align: center; font-family: segoe ui, 'Open Sans' !important; font-size: medium;\"><span style=\"font-size: 24px\">$Company.Name<br /></span><span style=\"font-size: small;\">$Company.Address, </span><span style=\"font-size: small;\">$Company.City $Company.State $Company.Zip<br /></span><span style=\"font-size: small;\">Office: $Company.Phone&nbsp&nbsp;</span>" +
            (_de.Company.Fax != null ? "<span style=\"font-size: small;\">Fax: $Company.Fax</span>" : "") +
            "<span style=\"font-size: small;\"><br/>" + companyLicenses + "</span></p></div>";

        string contacts = "<div>#foreach($i in $Impound.DispatchEntry.Contacts)<div style=\"display: inline-block; width: 200px; padding-bottom: 10px;\"><strong>$i.Type.ToString().Replace(\"Unspecified\",\"Other\")</strong><br />$i.Name&nbsp;<br />$i.Address<br />$i.City $i.State $i.Zip</div>#end</div>";
        string summaryBlock = "<p><span><b>" + (showStockNumberAsInvoiceNumber ? "Invoice #:" : "Stock #:") + " </b>" + (showStockNumberAsInvoiceNumber ? _de.InvoiceNumber : _de.CallNumber.ToString()) + "</span><strong><span>&nbsp; &nbsp; Year/Make/Model&nbsp;</span></strong><span>$Ticket.Year $Ticket.MakeModelFormatted &nbsp; &nbsp;</span><strong><span>License Plate #&nbsp;</span></strong><span>$Ticket.LicenseNumber &nbsp; &nbsp;</span><strong><span size=\"2\">VIN #&nbsp;</span></strong><span>$Ticket.VIN<br /></span><strong>Date Towed</strong><span>&nbsp;$Impound.ImpoundDate.ToShortDateString() &nbsp;<strong>Towed From </strong>&nbsp;$Ticket.TowSource&nbsp;&nbsp;</span>" + (showDailyStorageInSummaryBlock ? "<strong>Daily Storage</strong><span>&nbsp;$Impound.StorageDailyRate.ToString(\"C\")&nbsp;Per Day&nbsp;</span>" : "") + (showTotalInSummaryBlock ? "<strong>Total Due</strong><span>&nbsp;$Impound.InvoiceTotal.ToString(\"C\") as of " + Core.OffsetDateTime(_de.Company, DateTime.Now).ToShortDateString() + "</span>" : "") + "</p>";
        const string impoundLot = "$Impound.Lot.Address, $Impound.Lot.City $Impound.Lot.State";
        const string invoiceItems = "<div>#foreach($i in $Ticket.InvoiceItems) <div style=\"display: block;\"><div style=\"display: inline-block; width: 400px;\"><strong>$i.Name</strong></div><div style=\"display: inline-block; width: 100px;\">$i.Quantity @ $i.Price.ToString(\"C\")</div><div style=\"display: inline-block; width: 100px;\">$i.Total.ToString(\"C\")</div></div> #end </div>";

        string inlineBodyStyle = "<style> " +
                "body { max-width: 1000px; margin: 0 auto; font-size: 14px } " +
                "* { font-family: calibri, verdana !important } " +
                "#company_header { font-family: segoe ui light, 'Open Sans' !important; font-size: 1.2em } " +
                "#no10_envelope_addresses { display: none; }" +
                "</style>";
        string no10Addresses = "&nbsp;";

        if (No10EnvelopeFormatted)
        {
            inlineBodyStyle = "<style> " +
                "body { text-align: left; white-space: normal; margin: 0; padding: 0; height: 11in; width; 8.5in; margin-left: 0.4in; margin-top: 0.4in; margin-bottom: 0.4in; font-size: 13px } " +
                "table { font-size: 13 px }" +
                "* { font-family: calibri, verdana !important }" +
                "#company_header { font-family: segoe ui light, 'Open Sans' !important; font-size: 1.2em }" +
                "#company_header p { font-align: left !important } " +
                "#no10_envelope_addresses { position: relative; height: 2.450in; width: 100%; }" +
                "#body_content { position: relative; left: 0; width: 100%; white-space: normal; margin: -10px 0 0 0; padding: 0 }" +
                "#return_address { position:absolute; width: 3.25in; text-align: left; margin-left: 0.25in; z-index:100; } " +
                "#recipient_address { position: absolute; top: 1.542in; width: 3.25in; height: 0.875in; margin-left: 0.25in; } " +
                "#cc_address { position: absolute; right: 0px; top: 0px; bottom: 0px; width: 3.0in; } " +
                "#cc_address_col { height: 2.25in; columns-count: 2; -webkit-column-count: 2; column-gap: .10in; -webkit-column-gap: .10in; overflow: hidden; } " +
                "#tx_second_notice_title { display: none; } " +
                "#recipient_address .larger { font-size: 1.2em } " + "</style>";

            if (!string.IsNullOrWhiteSpace(companyHeaderNoTitle))
                hideTitle = true;

            string title = hideTitle ? "" : "<div style=\"font-size: 20px; text-align: center;\">" + template.Title + "</div>";
            var recipient = useLienholder ? Contacts.Where(w => w.Type == Extric.Towbook.Dispatch.ContactType.Lienholder).FirstOrDefault() : Contacts.FirstOrDefault();
            string cc = "";

            if (ccContacts != null)
            {
                var showTypes = ccContacts.Count() < 5;
                cc += "<div id=\"cc_address_col\">";
                foreach (var con in ccContacts)
                {
                    cc += "<div style =\"display: inline-block; width: 100%; padding-bottom: 10px;\">" +
                        (showTypes ? "<strong>" + con.Type.ToString().Replace("Unspecified", "Other") + "</strong><br />" : "") +
                        con.Name + "<br />" + con.Address + "<br />" + con.City + " " + con.State + " " + con.Zip + "</div>";
                }
                cc += "</div>";
            }

            string returnAddress = _de.Company.Name + "<br/>" +
                                    _de.Company.Address + "<br/>" +
                                    _de.Company.City + " " + _de.Company.State + " " + _de.Company.Zip + "<br/>" +
                                    _de.Company.Phone +
                                    (!string.IsNullOrWhiteSpace(_de.Company.Fax) ? "<br/>" + _de.Company.Fax : "") +
                                    (!string.IsNullOrWhiteSpace(_de.Company.Email) ? "<br/>" + _de.Company.Email : "");

            if (_de.Company.Id == 2064)
            {
                returnAddress = "Blue Hill Wrecker & Towing<br/>14815 MacArthur Drive North, Little Rock, AR 72118<br/>265 Park Street, Clinton, AR 72031<br/>Office: 501‐851‐1575 / 501‐745‐6129<br/>Fax: ************";
            }

            Company c = _de.Company;
            var billingAddress = (await AddressBookEntry.GetByCompanyAsync(c)).Where(o => o.Name == "Billing Address").SingleOrDefault();
            if (billingAddress != null)
            {
                returnAddress = _de.Company.Name + "<br/>" + billingAddress.Address + "<br/>" + billingAddress.City + " " + billingAddress.State + " " + billingAddress.Zip + "<br/>" + billingAddress.Phone +
                                    (!string.IsNullOrWhiteSpace(_de.Company.Fax) ? "<br/>" + _de.Company.Fax : "") +
                                    (!string.IsNullOrWhiteSpace(_de.Company.Email) ? "<br/>" + _de.Company.Email : "");
            }

            if (_de.Company.Id == 3087 && (template.Id == 1658 || template.Id == 1666))
                title += "<div style=\"font-size: 20px\">Notice of Impound</div>";

            if (template.Id == 1348)
                title = "<div style =\"font-size: 20px; text-align: center; padding-top: 0px; margin-top: -20px; \">Second Notice (Texas)<span style=\"display: block; margin: 0; padding: 0; font-size: 13px;\">Final Notice Consent To Sell Impounded Vehicle</span></div>";

            no10Addresses = "<div id=\"return_address\">" + returnAddress + "</div>" +
                "<div id=\"cc_address\">" + cc + "</div>" +
                "<div id=\"recipient_address\"><span class=\"larger\">" + (recipient != null ? recipient.Name + "&nbsp;<br />" + recipient.Address + "<br />" + recipient.City + " " + recipient.State + " " + recipient.Zip : "[no contact specified as " + (useLienholder ? "'lienholder'" : "'owner'") + "]") + "</span></div>";

            companyHeader = string.Empty;
            companyHeaderNoTitle = string.Empty;
            contacts = title;
        }

        sw.Write("<html><head>" + inlineBodyStyle + "</head><body><div id=\"no10_envelope_addresses\">" + no10Addresses + "</div><div id=\"body_content\">");

        string driverName = "";
        string driverTDLR = "";
        var driverTDLRLicenseKey = (await Extric.Towbook.Licenses.DriverLicenseKey.GetLicenceKeyDataAsync(_de.Company))
        .Where(w => w.Name == "Driver TDLR#")
        .FirstOrDefault();
        if (_de != null && _de.Drivers != null)
        {
            var divider = _de.Drivers.Count > 1 ? " / " : "";
            foreach (var d in _de.Drivers)
            {
                var driver = await Driver.GetByIdAsync(d);
                if (driver != null)
                {
                    driverName += driver.Name + divider;

                    if (driverTDLRLicenseKey != null)
                    {
                        var value = Extric.Towbook.Licenses.DriverLicenseKeyValue.GetByDriverId(d).Where(w => w.KeyId == driverTDLRLicenseKey.Id).FirstOrDefault();
                        if (value != null)
                            driverTDLR += value.Value + divider;
                    }
                }
            }
        }

        string truckName = "";
        string truckTDLR = "";
        string truckTag = "";
        var truckTDLRLicenseKey = Extric.Towbook.Licenses.TruckLicenseKey.GetLicenceKeyData(_de.Company).Where(w => w.Name == "Truck TDLR#").FirstOrDefault();
        if (_de != null && _de.Trucks != null)
        {
            var divider = _de.Drivers.Count > 1 ? " / " : "";
            foreach (var t in _de.Trucks)
            {
                var truck = await Truck.GetByIdAsync(t);

                if (truckTDLRLicenseKey != null)
                {
                    var value = Extric.Towbook.Licenses.TruckLicenseKeyValue.GetByTruckId(t).Where(w => w.KeyId == truckTDLRLicenseKey.Id).FirstOrDefault();
                    if (value != null)
                        truckTDLR = value.Value;
                }

                if (truck != null)
                {
                    truckName = truck.Name + (string.IsNullOrWhiteSpace(truckTDLR) ? "" : " [" + truckTDLR + "]") + divider;
                    truckTag = truck.PlateNumber + divider;
                }
            }
        }

        var court = AddressBookEntry.GetByCompany(_de.Company).Where(o => o.Name.ToLowerInvariant().Contains("district court")).SingleOrDefault();
        var courtName = "";
        var courtAddress = "";
        var courtCity = "";
        var courtState = "";
        var courtZip = "";
        var courtFullAddress = "";
        var courtPhone = "";
        if (court != null)
        {
            courtName = court.Name;
            courtAddress = court.Address;
            courtCity = court.City;
            courtState = court.State;
            courtZip = court.Zip;
            courtFullAddress = courtAddress + " " + courtCity + ", " + courtState + " " + courtZip;
            courtPhone = court.Phone;
        }

        var deficiencyLienAmount = _de.Invoice.BalanceDue;
        if (_de.Company.State.ToUpperInvariant() == "WA" && _de.Company.Country == Company.CompanyCountry.USA && deficiencyLienAmount > 500)
        {
            var tr = await TaxRate.GetByCompanyAsync(_de.Company);
            if (tr.Count > 0)
            {
                var rate = tr[0].Rate;
                deficiencyLienAmount = 500 + (500 * (rate / 100));
            }
        }

        template.Contents = template.Contents.Replace("$CompanyHeaderNoTitle", companyHeaderNoTitle).Replace("$CompanyHeader", companyHeader).Replace("$Contacts", contacts).Replace("$SummaryBlock", summaryBlock).Replace("$ImpoundLot", impoundLot).Replace("$Charges", invoiceItems).Replace("$TDLR", tdlrNumber)
            .Replace("$TdlrTowingCompany", attributes["towing_company_name"])
            .Replace("$DriverName", driverName)
            .Replace("$DriverTDLR#", driverTDLR)
            .Replace("$TruckName", truckName)
            .Replace("$TruckPlate", truckTag)
            .Replace("$CallNumber", _de.CallNumber.ToString())
            .Replace("$AccountType", ((int)_de.Account.Type).ToString())
            .Replace("$CourtName", courtName)
            .Replace("$CourtAddress", courtAddress)
            .Replace("$CourtFullAddress", courtFullAddress)
            .Replace("$CourtCity", courtCity)
            .Replace("$CourtState", courtState)
            .Replace("$CourtZip", courtZip)
            .Replace("$CourtPhone", courtPhone)
            .Replace("$DeficiencyLienAmount", deficiencyLienAmount.ToString("C"));

        string companyTwo = _de.Company.Name;

        if (_de.Company.Id == 3308)
            companyTwo = "Phoenix Towing";

        var loadedInvoiceItem = _de.InvoiceItems.Where(w =>
            w.RateItem != null &&
            w.RateItem.Predefined != null &&
            w.RateItem.Predefined.Id == Extric.Towbook.PredefinedRateItem.BUILTIN_MILEAGE_LOADED).FirstOrDefault();

        var firstContactName = _de.Contacts.FirstOrDefault() != null ? _de.Contacts.First().Name : "";

        var paymentTotal = _de.InvoiceTotal - _de.BalanceDue;
        try
        {
            sw.Write((await ParseTemplateAsync(template, _de, licenses))
             .Replace("$CurrentDate", DateTime.Now.ToShortDateString())
             .Replace("$ReleaseBirthDate", releaseBirthDate)
             .Replace("$ReleasePhone", releasePhone)
             .Replace("$TowingCompanyName", companyTwo)
             .Replace("$Fax", "")
             .Replace("$Logo", imageHtml)
             .Replace("$LoadedMiles", (loadedInvoiceItem != null ? loadedInvoiceItem.Quantity.ToString() : String.Empty))
             .Replace("$PaymentTotal", paymentTotal.ToString("C"))
             .Replace("$ContactName", firstContactName));

        }
        catch (Exception er)
        {
            Response.Write("<Pre>");
            Response.Write(coreAttributes.ToJson());
            Response.Write(er.ToJson());
            Response.Write(template.ToJson());

            Response.End();
        }

        sw.Write("</div></body></html>");


        return sw.ToString();
    }

    private async Task<string> ParseTemplateAsync(LetterTemplate lt, Entry de, Dictionary<string, string> licenses)
    {
        StringBuilder sb = new StringBuilder();

        sb.Append(lt.Contents);

        string output = sb.ToString();

        var coreAttributes = await Extric.Towbook.Dispatch.Attribute.GetByDispatchEntryAsync(de.Company, de, true);

        Dictionary<string, string> attributes = new Dictionary<string, string>();

        foreach (var av in coreAttributes)
        {
            string value = "";

            if (de.Attributes.ContainsKey(av.Id))
            {
                value = de.Attributes[av.Id].Value;
            }
            else
            {
                value = "<span style='color: red; font-weight: bold; font-size:25px;'>[" + av.Name.ToUpper() + " NOT ENTERED]</span>";
                de.Attributes.Add(av.Id, new Extric.Towbook.Dispatch.AttributeValue() { Value = value });
            }

            if (!attributes.ContainsKey(av.Name))
                attributes.Add(av.Name, value);
        }

        // check is attribute even exists so we don't cause an exception. Return empty string if not found.
        if (lt != null && lt.Contents.IndexOf("$Attributes.get_item") != -1)
        {
            IEnumerable<int> indices = Enumerable.Range(0, lt.Contents.Length - "$Attributes.get_item".Length).Where(i => "$Attributes.get_item".Equals(lt.Contents.Substring(i, "$Attributes.get_item".Length)));

            foreach (var start in indices)
            {
                var field = lt.Contents.Substring(start);
                var end = field.IndexOf(')');
                if (end != -1)
                {
                    string text = lt.Contents.Substring(start, end);
                    string key = text.Split('(')[1];
                    string id = "";
                    string value = "";
                    if (key.IndexOf('"') != -1)
                    {
                        key = text.Split(new char[] { '\"', '\"' })[1]; // http://stackoverflow.com/a/7215364
                    }

                    int keyId = 0;
                    Extric.Towbook.Dispatch.Attribute attr = null;
                    if (int.TryParse(key, out keyId))
                    {
                        attr = await Extric.Towbook.Dispatch.Attribute.GetByIdAsync(keyId);
                    }
                    else
                    {
                        attr = (await Extric.Towbook.Dispatch.Attribute.GetByCompanyAsync(de.Company, false)).Where(w => w.Name == key).FirstOrDefault();
                    }


                    if (attr != null)
                    {
                        key = attr.Name;
                        id = attr.Id.ToString();
                        if (de.Attributes.ContainsKey(attr.Id))
                            value = de.Attributes[attr.Id].Value;
                    }
                    else
                    {
                        // check company licenses
                        if (licenses != null)
                        {
                            var findOne = licenses.Where(w => w.Key == key).ToCollection();
                            if (findOne.Count() > 0)
                            {
                                id = "temp_" + findOne[0].Key;
                                value = findOne[0].Value;
                            }
                        }
                    }

                    if (!attributes.ContainsKey(key))
                    {
                        if (!string.IsNullOrWhiteSpace(id))
                            attributes.Add(id, value);

                        attributes.Add(key, value);
                    }
                }
            }
        }

        Extric.Towbook.Company.Company company = de.Company;
        var billingAddress = AddressBookEntry.GetByCompany(de.Company).Where(o => o.Name == "Billing Address").SingleOrDefault();
        if (billingAddress != null)
        {
            company.Address = billingAddress.Address;
            company.State = billingAddress.State;
            company.Zip = billingAddress.Zip;
            company.Phone = billingAddress.Phone;
        }

        var ve = new VelocityEngine();
        var ep = new ExtendedProperties();

        ve.Init(ep);

        var vc = new VelocityContext();

        vc.Put("Ticket", de);
        vc.Put("Attributes", attributes);
        vc.Put("Company", company);
        vc.Put("Now", DateTime.Now);

        foreach (EntryContact ec in de.Contacts)
        {
            if (ec.Type == Extric.Towbook.Dispatch.ContactType.Owner)
                vc.Put("Owner", ec);
            else if (ec.Type == Extric.Towbook.Dispatch.ContactType.Lienholder)
                vc.Put("Lienholder", ec);
        }

        // compile a list of all categories and total them up

        StringWriter writer = new StringWriter();
        try
        {
            ve.Evaluate(vc, writer, lt.Title, output);
        }
        catch (Exception e)
        {
            throw new Exception("code: " + output, e);
        }

        output = writer.GetStringBuilder().ToString();

        return output;
    }

    public async Task<string> TranslateDBtoDocFieldsAsync(Dictionary<string, string> fromDB, Dictionary<string, string> fields, Extric.Towbook.Dispatch.Entry entry)
    {
        var firstContact = entry != null ? entry.Contacts.FirstOrDefault() : null;
        var coreAttributes = await Extric.Towbook.Dispatch.Attribute.GetByDispatchEntryAsync(entry.Company, entry, false);
        var missingAtttributesIds = entry.Attributes.Where(a => !coreAttributes.Select(b => b.Id).Contains(a.Key)).ToList();
        foreach (var i in missingAtttributesIds)
        {
            var a = await Extric.Towbook.Dispatch.Attribute.GetByIdAsync(i.Key);
            if (a != null)
                coreAttributes.Add(a);

            // check is there is a missing attribute and add it to the Dictionary with the value set
            if (!fromDB.ContainsKey(a.Name))
            {
                var av = entry.Attributes.Where(b => b.Key == i.Key).Select(c => c.Value).FirstOrDefault();
                if (av != null)
                    fromDB.Add(a.Name, av.Value);
            }
        }


        var assignments = "";

        if (fields.ContainsKey("DateTimePM") && fields.ContainsKey("DateTimeAM"))
        {
            if (DateTime.Now.ToString("tt") == "AM")
                fields["DateTimeAM"] = "1";
            else
                fields["DateTimePM"] = "1";
        }

        foreach (var k in fromDB.Keys)
        {
            var key = k;
            var value = fromDB[key];

            if (fields.ContainsKey(key.ToUpperInvariant()))
                key = k.ToUpperInvariant();
            else if (fields.ContainsKey(key.ToLowerInvariant()))
                key = k.ToLowerInvariant();


            if (fields.ContainsKey(key))
            {
                if (key == "Date" || key == "date" || key == "DATE" || key == "CurrentDate")
                {
                    fields[key] = DateTime.Now.ToShortDateString();
                }
                else if (key == "DateDay")
                {
                    fields["DateDay"] = DateTime.Now.ToString("dd");
                }
                else if (key == "DateMonth")
                {
                    fields["DateMonth"] = DateTime.Now.ToString("MM");
                }
                else if (key == "DateYear")
                {
                    fields["DateYear"] = DateTime.Now.ToString("yyyy");
                }
                else if (key == "NotaryDay")
                {
                    fields["NotaryDay"] = DateTime.Now.ToString("dd");
                }
                else if (key == "NotaryMonth")
                {
                    fields["NotaryMonth"] = DateTime.Now.ToString("MMMM");
                }
                else if (key == "NotaryYear")
                {
                    fields["NotaryYear"] = DateTime.Now.ToString("yyyy");
                }
                else if (key == "ContactName")
                {

                    if (firstContact != null)
                        fields[key] = firstContact.Name;
                    else
                        fields[key] = "";
                }
                else if (key == "ContactAddress")
                {
                    if (firstContact != null)
                        fields[key] = firstContact.Address;
                    else
                        fields[key] = "";
                }
                else if (key == "ContactCityStateZip")
                {
                    if (firstContact != null)
                        fields[key] = firstContact.City + ", " + firstContact.State + " " + firstContact.Zip;
                    else
                        fields[key] = "";
                }
                else if (key == "ContactPhone")
                {
                    if (firstContact != null)
                        fields[key] = firstContact.Phone;
                    else
                        fields[key] = "nope";
                }
                else if (key == "YearMakeModel")
                {
                    string r = "";
                    if (entry.Year > 0)
                        r += entry.Year + " ";
                    r += entry.MakeModelFormatted;
                    fields[key] = r;
                }
                else if (key == "OdometerHundreds" || key == "OdometerThousands")
                {
                    var s = entry.Odometer.ToString();
                    var thousands = "";
                    var hundreds = "";

                    if (s.Length > 3)
                    {
                        thousands = s.Length > 6 ? s.Substring(s.Length - 6, 3) : s.Substring(0, s.Length - 3);
                        hundreds = s.Substring(thousands.Count(), 3);
                    }
                    else
                    {
                        thousands = "";
                        hundreds = s;
                    }

                    if (key == "OdometerThousands")
                        fields[key] = thousands;
                    else
                        fields[key] = hundreds;
                }
                else if (key == "LoadedMiles")
                {
                    var loadedInvoiceItem = entry.InvoiceItems.Where(w =>
                        w.RateItem != null &&
                        w.RateItem.Predefined != null &&
                        w.RateItem.Predefined.Id == Extric.Towbook.PredefinedRateItem.BUILTIN_MILEAGE_LOADED).FirstOrDefault();

                    if (loadedInvoiceItem != null)
                        fields[key] = loadedInvoiceItem.Quantity.ToString();
                }
                else if (key == "FirstDriverName" ||
                    key == "FirstDriverLicenseNumber" ||
                    key == "FirstDriverBirthDate" ||
                    key == "FirstTruckName" ||
                    key == "FirstTruckPlate")
                {
                    if (entry != null && entry.Assets != null)
                    {
                        if ((key == "FirstDriverName" || key == "FirstDriverLicenseNumber")
                            && entry.Assets != null)
                        {
                            var ea = entry.Assets.FirstOrDefault();
                            if (ea != null && ea.Drivers != null)
                            {
                                var ead = ea.Drivers.FirstOrDefault();
                                if (ead != null && ead.DriverId != null)
                                {
                                    var driver = await Driver.GetByIdAsync(ead.DriverId.Value);
                                    if (driver != null)
                                    {
                                        if (key == "FirstDriverName")
                                            fields[key] = driver.Name;

                                        if (key == "FirstDriverLicenseNumber")
                                            fields[key] = driver.LicenseNumber;
                                    }
                                }
                            }
                        }

                        if ((key == "FirstTruckName" || key == "FirstTruckPlate") &&
                            entry.Assets != null)
                        {
                            var ea = entry.Assets.FirstOrDefault();
                            if (ea != null && ea.Drivers != null)
                            {
                                var ead = ea.Drivers.FirstOrDefault();
                                if (ead != null && ead.TruckId != null)
                                {
                                    var truck = await Truck.GetByIdAsync(ead.TruckId.Value);
                                    if (truck != null)
                                    {
                                        if (key == "FirstTruckName")
                                            fields[key] = truck.Name;

                                        if (key == "FirstTruckPlate")
                                            fields[key] = truck.PlateNumber;
                                    }
                                }
                            }
                        }
                    }
                }
                else
                {
                    var valuex = await ParseAsync(value, entry);
                    fields[key] = valuex;
                    if (!valuex.Contains('$') && valuex == "")
                        fields[key] = value;


                    assignments += "<tr><td style=\"background-color: lightgreen\">" + key + "</td><td>" + valuex + "</td></tr>\r\n";
                }

            }

            if (fields.ContainsKey(key) && fields[key] == value)
                fields[key] = "";


            if (fields.ContainsKey(key + "_duplicate"))
            {
                if (fields.ContainsKey(key))
                    fields[key + "_duplicate"] = fields[key];
            }

            if (fields.ContainsKey(key + "_copy"))
            {
                if (fields.ContainsKey(key))
                    fields[key + "_copy"] = fields[key];
            }

            if (fields.ContainsKey("AccountFullAddress"))
            {
                var account = entry.Account;
                if (account != null)
                    fields["AccountFullAddress"] = account.Address + ", " + account.City + ", " + account.State + " " + account.Zip;
            }
            if (fields.ContainsKey("AccountFullAddress1"))
            {
                var account = entry.Account;
                if (account != null)
                    fields["AccountFullAddress1"] = account.Address + ", " + account.City + ", " + account.State + " " + account.Zip;
            }
            if (fields.ContainsKey("AccountFullAddress2"))
            {
                var account = entry.Account;
                if (account != null)
                    fields["AccountFullAddress2"] = account.Address + ", " + account.City + ", " + account.State + " " + account.Zip;
            }
            if (fields.ContainsKey("AccountCityStateZip"))
            {
                var account = entry.Account;
                if (account != null)
                    fields["AccountCityStateZip"] = account.City + ", " + account.State + " " + account.Zip;
            }
        }

        foreach (var f in new List<string>(fields.Where(o => !fromDB.Keys.Contains(o.Key)).Select(k => k.Key)))
        {
            if (String.IsNullOrWhiteSpace(fields[f]))
                fields[f] = " ";

            assignments += "<tr><td style=\"background-color:red;color:white\">" + f + "</td><td>missing</td></tr>\r\n";
        }

        // report of sale owners
        var rosOwners = entry.Contacts.Where(w => w.Type == ContactType.ReportOfSaleOwner).FirstOrDefault();
        if (rosOwners != null)
        {
            if (fields.ContainsKey("ReportOfSaleName"))
                fields["ReportOfSaleName"] = rosOwners.Name;

            if (fields.ContainsKey("ReportOfSaleAddress"))
                fields["ReportOfSaleAddress"] = rosOwners.Address;

            if (fields.ContainsKey("ReportOfSaleCity"))
                fields["ReportOfSaleCity"] = rosOwners.City;

            if (fields.ContainsKey("ReportOfSaleState"))
                fields["ReportOfSaleState"] = rosOwners.State;

            if (fields.ContainsKey("ReportOfSaleZip"))
                fields["ReportOfSaleZip"] = rosOwners.Zip;

            if (fields.ContainsKey("ReportOfSaleAddressFull"))
                fields["ReportOfSaleAddressFull"] = rosOwners.Address + " " + rosOwners.City + ", " + rosOwners.State + " " + rosOwners.Zip;
        }

        var owners = entry.Contacts.Where(w => w.Type == ContactType.Owner).Take(2);
        if (owners != null && owners.Count() > 0)
        {
            if (fields.ContainsKey("Owner1_Name"))
                fields["Owner1_Name"] = owners.ElementAt(0).Name;
            if (fields.ContainsKey("Owner1_Address"))
                fields["Owner1_Address"] = owners.ElementAt(0).Address;
            if (fields.ContainsKey("Owner1_City"))
                fields["Owner1_City"] = owners.ElementAt(0).City;
            if (fields.ContainsKey("Owner1_State"))
                fields["Owner1_State"] = owners.ElementAt(0).State;
            if (fields.ContainsKey("Owner1_Zip"))
                fields["Owner1_Zip"] = owners.ElementAt(0).Zip;
            if (fields.ContainsKey("Owner1_FullAddress"))
                fields["Owner1_FullAddress"] = owners.ElementAt(0).Address + " " + owners.ElementAt(0).City + ", " + owners.ElementAt(0).State + " " + owners.ElementAt(0).Zip;

            if (owners.Count() > 1)
            {
                if (fields.ContainsKey("Owner2_Name"))
                    fields["Owner2_Name"] = owners.ElementAt(1).Name;
                if (fields.ContainsKey("Owner2_Address"))
                    fields["Owner2_Address"] = owners.ElementAt(1).Address;
                if (fields.ContainsKey("Owner2_City"))
                    fields["Owner2_City"] = owners.ElementAt(1).City;
                if (fields.ContainsKey("Owner2_State"))
                    fields["Owner2_State"] = owners.ElementAt(1).State;
                if (fields.ContainsKey("Owner2_Zip"))
                    fields["Owner2_Zip"] = owners.ElementAt(1).Zip;
                if (fields.ContainsKey("Owner2_FullAddress"))
                    fields["Owner2_FullAddress"] = owners.ElementAt(1).Address + " " + owners.ElementAt(1).City + ", " + owners.ElementAt(1).State + " " + owners.ElementAt(1).Zip;
            }
        }


        var contacts = entry.Contacts.Where(w => w.Type == ContactType.Owner ||
                            w.Type == ContactType.Individual ||
                            w.Type == ContactType.Insurance ||
                            w.Type == ContactType.Unspecified).OrderBy(o => o.Type).ToArray();

        // lienholders and Owners
        var lienholders = entry.Contacts.Where(w => w.Type == ContactType.Lienholder).ToArray();

        for (var i = 1; i <= 10; i++)
        {
            if (contacts.Length >= i)
            {
                if (fields.ContainsKey("ImpoundContact" + i))
                    fields["ImpoundContact" + i] = contacts[i - 1].Name;

                if (fields.ContainsKey("ImpoundContactFullAddress" + i))
                    fields["ImpoundContactFullAddress" + i] = contacts[i - 1].Address + " " + contacts[i - 1].City + ", " + contacts[i - 1].State + " " + contacts[i - 1].Zip;

                if (fields.ContainsKey("ImpoundContactCityStateZip" + i))
                    fields["ImpoundContactCityStateZip" + i] = contacts[i - 1].City + ", " + contacts[i - 1].State + " " + contacts[i - 1].Zip;

                if (fields.ContainsKey("ImpoundContactAddress" + i))
                    fields["ImpoundContactAddress" + i] = contacts[i - 1].Address;
                if (fields.ContainsKey("ImpoundContactCity" + i))
                    fields["ImpoundContactCity" + i] = contacts[i - 1].City;
                if (fields.ContainsKey("ImpoundContactState" + i))
                    fields["ImpoundContactState" + i] = contacts[i - 1].State;
                if (fields.ContainsKey("ImpoundContactZip" + i))
                    fields["ImpoundContactZip" + i] = contacts[i - 1].Zip;

                if (fields.ContainsKey("ImpoundContactPhone" + i))
                    fields["ImpoundContactPhone" + i] = contacts[i - 1].Phone;
            }

            if (lienholders.Length >= i)
            {
                if (fields.ContainsKey("ImpoundLienholderName" + i))
                    fields["ImpoundLienholderName" + i] = lienholders[i - 1].Name;

                if (fields.ContainsKey("ImpoundLienholderAddress" + i))
                    fields["ImpoundLienholderAddress" + i] = lienholders[i - 1].Address;

                if (fields.ContainsKey("ImpoundLienholderCityStateZip" + i))
                    fields["ImpoundLienholderCityStateZip" + i] = lienholders[i - 1].City + ", " + lienholders[i - 1].State + " " + lienholders[i - 1].Zip;

                if (fields.ContainsKey("ImpoundLienholderFullAddress" + i))
                    fields["ImpoundLienholderFullAddress" + i] = lienholders[i - 1].Address + " " + lienholders[i - 1].City + ", " + lienholders[i - 1].State + " " + lienholders[i - 1].Zip;

                if (fields.ContainsKey("ImpoundLienholderCity" + i))
                    fields["ImpoundLienholderCity" + i] = lienholders[i - 1].City;

                if (fields.ContainsKey("ImpoundLienholderState" + i))
                    fields["ImpoundLienholderState" + i] = lienholders[i - 1].State;

                if (fields.ContainsKey("ImpoundLienholderZip" + i))
                    fields["ImpoundLienholderZip" + i] = lienholders[i - 1].Zip;

                if (fields.ContainsKey("ImpoundLienholderPhone" + i))
                    fields["ImpoundLienholderPhone" + i] = lienholders[i - 1].Phone;
            }
        }

        // key is "ListCharges"
        if (fields.ContainsKey("ListCharges"))
        {
            var output = "";
            var name = "";
            foreach (var ii in entry.InvoiceItems)
            {
                name = ii.Name;
                if (ii.RateItem != null)
                {
                    if (ii.RateItem.CategoryId == 5 || (ii.RateItem.Predefined != null && ii.RateItem.Predefined.Id == 3))
                        name = "Storage";
                }

                if (ii.Total > 0)
                    output += name + " " + ii.Total.ToString("C") + "  ";
            }

            if (entry != null && entry.Invoice != null && entry.Invoice.Tax > 0)
                output += " Tax " + entry.Invoice.Tax.ToString("C");

            fields["ListCharges"] = output;
        }

        // Dispatch Attributes Fields
        foreach (var f in fields.Where(o => coreAttributes.Select(s => s.Name).Contains(o.Key) || coreAttributes.Select(t => t.Name + "_duplicate").Contains(o.Key)))
        {
            var attribute = coreAttributes.Where(w => w.Name == f.Key).FirstOrDefault();
            if (attribute != null)
            {
                if (entry.Attributes.ContainsKey(attribute.Id)
                    && fields.ContainsKey(attribute.Name)
                    && String.IsNullOrWhiteSpace(fields[attribute.Name]))
                    fields[attribute.Name] = entry.Attributes[attribute.Id].Value;
            }

            if (f.Key.Contains("_duplicate"))
            {
                attribute = coreAttributes.Where(w => w.Name == f.Key.Replace("_duplicate", "")).FirstOrDefault();
                if (attribute != null)
                {
                    if (entry.Attributes.ContainsKey(attribute.Id)
                        && fields.ContainsKey(attribute.Name + "_duplicate")
                        && String.IsNullOrWhiteSpace(fields[attribute.Name + "_duplicate"]))
                        fields[attribute.Name + "_duplicate"] = entry.Attributes[attribute.Id].Value;
                }
            }
        }


        // Company Licenses
        var licenseValues = Extric.Towbook.Licenses.CompanyLicenseKeyValue.GetByCompanyId(entry.CompanyId);
        var keys = Extric.Towbook.Licenses.CompanyLicenseKey.GetAll();
        foreach (var k in keys)
        {
            if (fields.ContainsKey(k.Name) || fields.ContainsKey(k.Name + "_duplicate") || fields.ContainsKey(k.Name + "_copy"))
            {
                var lv = licenseValues.Where(w => w.KeyId == k.Id).FirstOrDefault();
                if (lv != null)
                {
                    if (fields.ContainsKey(k.Name + "_duplicate"))
                        fields[k.Name + "_duplicate"] = lv.Value;

                    if (fields.ContainsKey(k.Name + "_copy"))
                        fields[k.Name + "_copy"] = lv.Value;

                    if (fields.ContainsKey(k.Name))
                        fields[k.Name] = lv.Value;
                }
            }
        }

        return assignments;
    }


    public async Task<string> ParseAsync(string code, Extric.Towbook.Dispatch.Entry x)
    {
        //if vin is less than 17 pad right to make it 17 as to not break substring out of index exceptions
        if (x != null)
        {
            if (x.VIN.Length == 0)
                x.VIN = "-";
            if (x.VIN.Length < 17)
                x.VIN = x.VIN.PadRight(17);
        }

        var coreAttributes = await Extric.Towbook.Dispatch.Attribute.GetByDispatchEntryAsync(x.Company, x, false);

        Dictionary<string, string> attributes = new Dictionary<string, string>();

        foreach (var av in coreAttributes)
        {
            string value = "";

            if (x.Attributes.ContainsKey(av.Id))
            {
                value = x.Attributes[av.Id].Value;
            }
            else
            {
                if (Request.QueryString["ni"] != null)
                    value = "<span style='color: red; font-weight: bold; font-size:25px;'>[" + av.Name.ToUpper() + " NOT ENTERED]</span>";
                else
                    value = " ";
                x.Attributes.Add(av.Id, new Extric.Towbook.Dispatch.AttributeValue() { Value = value });
            }

            if (!attributes.ContainsKey(av.Name))
                attributes.Add(av.Name, value);
        }

        // check is attribute even exists so we don't cause an exception. Return empty string if not found.
        if (code.IndexOf("$Attributes.get_item") != -1)
        {
            string key = code.Split(new char[] { '\"', '\"' })[1]; // http://stackoverflow.com/a/7215364
            if (!attributes.ContainsKey(key))
                return "";
        }

        var ve = new NVelocity.App.VelocityEngine();
        var ep = new Commons.Collections.ExtendedProperties();

        ve.Init(ep);
        var vc = new NVelocity.VelocityContext();

        Company c = x.Company;
        var alternateAddress = AddressBookEntry.GetByCompany(x.Company);
        if (alternateAddress != null)
        {
            var addr = alternateAddress.Where(o => o.Name == "Billing Address").FirstOrDefault();
            if (addr != null)
            {
                c.Address = addr.Address;
                c.City = addr.City;
                c.State = addr.State;
                c.Zip = addr.Zip;
                c.Phone = addr.Phone;
            }

            addr = alternateAddress.Where(o => o.Name == "Form Address").SingleOrDefault();
            if (addr != null)
            {
                c.Address = addr.Address;
                c.City = addr.City;
                c.State = addr.State;
                c.Zip = addr.Zip;
                c.Phone = addr.Phone;
            }

            addr = alternateAddress.Where(o => o.Name.ToLowerInvariant().Contains("district court")).SingleOrDefault();
            if (addr != null)
            {
                Company court = new Company();
                court.Name = addr.Name;
                court.Address = addr.Address;
                court.City = addr.City;
                court.State = addr.State;
                court.Zip = addr.Zip;
                court.Phone = addr.Phone;

                vc.Put("Court", court);
            }
        }
        vc.Put("Company", c);

        vc.Put("Account", x.Account);
        vc.Put("Call", x);
        vc.Put("Attributes", attributes);

        Extric.Towbook.Impounds.Impound imp = null;
        if (x.Impound)
            imp = Extric.Towbook.Impounds.Impound.GetByDispatchEntry(x);

        if (imp != null)
        {
            vc.Put("Impound", imp);

            vc.Put("UncategorizedTotal", imp.GetCategoryTotal(0).ToString("C"));
            vc.Put("TowingAndUncategorizedTotal", (imp.GetCategoryTotal(1) + imp.GetCategoryTotal(0)).ToString("C"));
            vc.Put("TowingTotal", imp.GetCategoryTotal(1).ToString("C"));
            vc.Put("DailyStorageTotal", imp.GetCategoryTotal(5).ToString("C"));
            vc.Put("StorageTotal", (imp.GetCategoryTotal(2) + imp.GetCategoryTotal(3) + imp.GetCategoryTotal(4) + imp.GetCategoryTotal(5)).ToString("C"));

            vc.Put("TowingImpoundTotal", (imp.GetCategoryTotal(1) + imp.GetCategoryTotal(2) + imp.GetCategoryTotal(3) + imp.GetCategoryTotal(4)).ToString("0.00"));
        }

        foreach (EntryContact ec in x.Contacts)
        {
            if (ec.Type == Extric.Towbook.Dispatch.ContactType.Owner)
                vc.Put("Owner", ec);
            else if (ec.Type == Extric.Towbook.Dispatch.ContactType.Lienholder)
                vc.Put("Lienholder", ec);
            else if (ec.Type == Extric.Towbook.Dispatch.ContactType.Insurance)
                vc.Put("Insurance", ec);
        }

        StringWriter writer = new StringWriter();

        try
        {
            ve.Evaluate(vc, writer, "merge", code);
        }
        catch (Exception e)
        {
            throw new Exception("code: " + code, e);
        }

        string output = writer.GetStringBuilder().ToString();
        if (output == code)
            return "";
        else
            return output;
    }
}
