using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using Extric.Towbook;
using Extric.Towbook.Dispatch;
using Extric.Towbook.AccidentReports;
//using Extric.Towbook.Impounds;
using System.IO;
using Extric.Towbook.WebShared;
using Extric.Towbook.Company;
using System.Collections.ObjectModel;
using Extric.Towbook.Utility;
using Extric.Towbook.Dispatch.CallModels;
using System.Web.UI;
using System.Threading.Tasks;

public partial class Accident_Report : System.Web.UI.Page
{
    public int _id = 0;
    public int _callId;
    public string _domain = "";
    public string _reportJson = "{}";
    public string _photosJson = "[]";
    public string _invoiceHtml = "";
    public string _companyLogoPath = "";
    public User _currentUser = null;
    public Entry _entry;
    public EntryAsset _asset;
    public Company _company = null;
    public int[] _contacts;
    public string _billingNotes;
    public string _internalNotes;
    public AccidentReport _ar;

    protected void Page_Load(object sender, EventArgs e)
    {
        RegisterAsyncTask(new PageAsyncTask(PageLoadAsync));
    }

    protected async Task PageLoadAsync()
    {
        int.TryParse(Request.QueryString["id"], out _id);

        _domain = WebGlobal.GetDomain();
        _company = Global.CurrentUser.Company;
        _currentUser = WebGlobal.CurrentUser;
        _companyLogoPath = GetCompanyLogo(_domain, _company.Id);

        if (_id == 0)
        {
            int.TryParse(Request.QueryString["callId"], out _callId);

            // Check for missing dispatchEntryId
            if (_callId == 0)
                throw new TowbookException("Invalid dispatchEntryId");

            _reportJson = WebGlobal.GetResponseFromUrl("/api/accidentReports/new?dispatchEntryId=" + _callId);
            _contacts = Array.Empty<int>();
        } 
        else
        {
            _ar = AccidentReport.GetById(_id);
            // Check for invalid report
            if (_ar == null)
                throw new TowbookException("Invalid report id or you don't have permission to view this report.");

            _callId = _ar.DispatchEntryId;
            _contacts = _ar.Contacts.Select(o => o.ContactId).ToArray();
            _reportJson = WebGlobal.GetResponseFromUrl("/api/accidentReports/" + _id);
        }

        _entry = Entry.GetById(_callId);
        
        // Check if user has access to this call
        if (_entry == null || !_currentUser.HasAccessToCompany(_entry.CompanyId))
            throw new TowbookException("You don't have permission to view this report.");

        if (_entry.Assets.Count == 0)
        {
            _asset = new EntryAsset();
            _asset.Make = String.Empty;
            _asset.Model = String.Empty;
        }
        else
            _asset = _entry.Assets[0];

        foreach (KeyValuePair<int, AttributeValue> x in _entry.Attributes)
        {
            if (x.Value.DispatchEntryAttributeId == 47)
            {
                _billingNotes = x.Value.Value;
            }
        }

        var users = Extric.Towbook.User.GetByCompanyId(_entry.CompanyId);
        _internalNotes = JsonExtensions.ToJson((await EntryNote.GetByDispatchEntryIdAsync(_callId)).Select(a => NoteModel.MapAsync(a)));
    }

    private string GetCompanyLogo(string domain, int companyId)
    {
        var baseLocal = Server.MapPath(@"..\ui\images\customer.logo\" + companyId);
        var baseRemote = domain + "/ui/images/customer.logo/" + companyId;

        foreach (var file in new[]{ "_template.jpg", "_full.jpg", "_left.jpg", ".jpg" })
        {
            if (File.Exists(baseLocal + file))
            {
                return baseRemote + file;
            }
        }

        return "";
    }
} 
