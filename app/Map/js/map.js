////////////////////////////////
// GMaps creator/handler class
////////////////////////////////

var TowbookMap = function (options) {

    this.options = {
        defaultLocation: {latitude: 42.82, longitude: -82.49},
        zoom: 11,
        minZoom: -1
    }

    this.map = null;
    this.geocoder = new google.maps.Geocoder();
    this.placeService = null;
    this.markers = {};
    this.infowindow = null; //new google.maps.InfoWindow({content: ''});
    this.markerCluster = null;
    this.coords = [];

    this.options = $.extend(this.options, options);

    this.init = function () {
        this.map = new google.maps.Map(document.getElementById(options.targetEl), {
            center: new google.maps.LatLng(this.options.defaultLocation.latitude, this.options.defaultLocation.longitude),
            zoom: this.options.zoom,
            minZoom: this.options.minZoom
        });

        this.map.setOptions({
            styles: [{
                featureType: 'poi.business',
                stylers: [{ visibility: 'on' }]
            }, {
                featureType: 'all',
                stylers: [{ visibility: 'on' }]
            }]
        });

        var self = this;
        
        this.infowindow = new google.maps.InfoWindow();
        this.infowindow.tags = {};
        this.infowindow.statusColor = '';

        google.maps.event.addListener(this.infowindow, 'closeclick', function () {
            // do something when the infowindow is closing
        });

        google.maps.event.addListener(this.infowindow, 'domready', function () {
            if (self.infowindow.statusColor) {
                var x = $($('.map-marker-info').parent().parent().parent().parent().parent()[0].children);
                x.css('border-left', '3px solid ' + self.infowindow.statusColor);
            }
        });

        google.maps.event.addListener(this.infowindow, 'content_changed', function () {
            self.infowindow.statusColor = '';
        });
    }

    this.getPositionFromAddress = function (address, done) {
        this.geocoder.geocode({ 'address': address }, function (results, status) {
            if (status != google.maps.GeocoderStatus.OK) {
                return done(null, new Error(status))
            }

            done({ latitude: results[0].geometry.location.lat(), longitude: results[0].geometry.location.lng() });
        }.bind(this));
    }

    this.addMarker = function (id, title, position) {
        var marker = new google.maps.Marker({
            position: new google.maps.LatLng(position.latitude, position.longitude),
            map: this.map,
            title: title
        });

        this.markers[id] = marker;
    }

    this.addStaticMarker = function(key, title, iconLabel, location, info) {
        var id = key + '-' + Math.random().toString().substring(2, 11);
        var marker = TowbookMap.createMarker({
            position: new google.maps.LatLng(location.latitude, location.longitude),
            map: this.map,
            markerIconHtml: iconLabel,
            markerLabel: title,
            html: info,
            icon: {
                path: TowbookMap.MAP_PIN,
                fillColor: '#000'
            }
        });

        marker.addListener('click', function () {
            this.infowindow.setContent(info);
            this.infowindow.open(this.map, marker);
        }.bind(this));

        var data = {
            key: key,
            title: title,
            label: iconLabel
        }

        marker.id = id;
        marker.setValues({ id: id, type: key, data: data });
        this.markers[id] = marker;
    }

    this.addIconMarker = function (key, title, iconName, location, info, data) {
        var id = key; // + '-' + Math.random().toString().substring(2, 11);
        var marker = TowbookMap.createMarker({
            position: new google.maps.LatLng(location.latitude, location.longitude),
            map: this.map,
            markerIconCls: iconName,
            markerLabel: title,
            html: info,
            icon: {
                path: TowbookMap.MAP_PIN,
                fillColor: '#000'
            }
        });

        marker.addListener('click', function () {
            if (typeof info == 'function') {
                this.infowindow.setContent(info(marker));
            } else if (typeof info == 'string') {
                this.infowindow.setContent(info);
            } else {
                alert('invalid "iw content" param')
                return;
            }

            this.infowindow.open(this.map, marker);
        }.bind(this));

        data = jQuery.extend({ key: key, title: title }, data || {})

        marker.id = id;
        marker.setValues({ id: id, type: key, data: data });
        this.markers[id] = marker;
    }

    this.addMarkerFromCall = function (call) {
        var id = 'c-' + call.id;
        var waypoint = call.getCurrentWaypoint();
        var position = new google.maps.LatLng(waypoint.latitude, waypoint.longitude);
        var markerIconCls = 'map-icon-truck';
        var markerLabel = call.callNumber.toString();

        // change icon for service calls
        if (call.towDestination == '') {
            markerIconCls = 'map-icon map-icon-car-repair';
        }

        if (call.priority == CALL_PRIORITY_EMERGENCY) {
            markerIconCls = 'map-icon-emergency';
        }

        //// TODO for emergency, you can tell based on the Account of the call having a type of Police
        //// Note, GET /api/calls/?oldCallsVersion=0 -> the response: calls[0].account.typeId is not being returned from the API
        ////if (call.account.typeId == 1) { // Police
        ////    mapIconLabel = '<span class="map-icon map-icon-police"></span>';
        ////}

        //Check Markers array for duplicate position and offset a little
        var i = this.coords.indexOf(waypoint.latitude + ',' + waypoint.longitude);
        if (i !== -1) {
            var a = 360.0 / (this.coords.length * Math.random());

            var newLat = position.lat() + -.00009 * Math.cos((+a * i) / 180 * Math.PI);  //x
            var newLng = position.lng() + -.00009 * Math.sin((+a * i) / 180 * Math.PI);  //Y
            position = new google.maps.LatLng(newLat, newLng);
        }

        var marker = TowbookMap.createMarker({
            position: position,
            map: this.map,
            markerIconCls: markerIconCls,
            markerLabelCls: call.priority == CALL_PRIORITY_EMERGENCY ? 'emergency-label' : '',
            markerLabel: markerLabel,
            icon: {
                path: TowbookMap.MAP_PIN,
                fillColor: this.getColorByStatus(call.status.id),
            }
        });

        mapDebug('ADD CALL #' + call.callNumber + ' with address: ' + waypoint.address, call);

        marker.id = id;
        marker.setValues({ id: id, type: 'call', data: call });
        marker.addListener('click', function () {
            this.openInfowindowCall(marker)
        }.bind(this));

        this.markers[id] = marker;
        this.coords.push(waypoint.latitude + ',' + waypoint.longitude);

        return marker;
    }

    this.openInfowindowCall = function (marker) {
        var call = new Call(marker.get('data'));
        var waypoint = call.getCurrentWaypoint();

        var iwIcon = '<span class="map-marker-info-icon"><img src="/Map/images/truck-mini.png"/></span>';
        if (call.towDestination == '') {
            iwIcon = '<span class="map-marker-info-icon"><div class="map-icon map-icon-car-repair"></div></span>'
        } else if (call.priority == CALL_PRIORITY_EMERGENCY) {
            iwIcon = '<span class="map-marker-info-icon"><img src="/Map/images/alert_icon-mini.png"/></span>'
        }

        var infoWindowContent = '<div class="map-marker-info">' +
            '<header>' + iwIcon + ' <h3> Call #' + call.callNumber + ' (' + this.getCallStatusText(call.status.id) + ')</h3></header>' +
            ' <section>' +            
            '  <table class="map-marker-info-table">' + 
            '   <tr><td class="key">' + waypoint.title + ' Address:</td><td>' + waypoint.address + '</td></tr>' +
            (call.reason != null ?
            '   <tr><td class="key">Reason:</td><td>' + call.reason.name + '</td></tr>' : '') +
            (call.account != null ?
            '   <tr><td class="key">Account:</td><td>' + call.account.company + '</td></tr>' : '' ) +
            '   <tr><td class="key">Vehicle:</td><td>' + (call.assets[0].make || '') + ' ' + (call.assets[0].model || '') + '</td></tr>' +
            (call.status.id != 0 ?
            '   <tr><td class="key">Assigned Driver:</td><td>' + this.getAssignedDriverLink(call) + '</td></tr>' : '' ) +
            '  </table>' + 
            '  <div class="map-marker-info-options">' +
            (call.status.id == 0 ?
            '   <a href="#" onclick="assignDriver(\'' + call.id + '\')" class="x-assign-driver winfo-option" data-call-id="' + call.id + '"><i class="fa fa-user-plus"></i> Assign Driver</a> | ' : '') +
            '   <a href="#" onclick="editCall(\'' + call.id + '\')" class="winfo-option"> <i class="fa fa-pencil"></i> Modify</a>' +
            '  </div>' +
            ' </section>' +
            '</div>'

        this.infowindow.setContent(infoWindowContent)
        
        this.infowindow.tags['currentCallId'] = call.id;
        this.infowindow.statusColor = this.getColorByStatus(call.status.id);

        this.infowindow.open(this.map, marker);
    }

    this.addMarkerFromDriver = function (driver) {
        mapDebug('ADD DRIVER:', driver);

        var position = new google.maps.LatLng(driver.location.latitude, driver.location.longitude);
        var id = 'd-' + driver.id;
        var marker = TowbookMap.createMarker({
            position: position,
            map: this.map,
            markerIconHtml: '<div style="font-size:15px;line-height:38px;">' + driver.shortName + '</div>',
            markerLabel: (driver.highestStatus ? driver.highestStatus.callNumber : '') + (driver.statuses && driver.statuses.length > 1 ? '...' : ''),
            driverId: driver.id,
            icon: {
                path: this.circlePath(0, -18, 18),
                fillColor: this.getColorByStatus(driver.highestStatus ? driver.highestStatus.statusId : '')
            }
        });

        marker.addListener('click', function () {
            this.openInfowindowDriver(marker);
        }.bind(this));

        marker.id = id;
        marker.setValues({ id: id, type: 'driver', data: driver })

        this.markers[id] = marker;
        this.coords.push(driver.location.latitude + ',' + driver.location.longitude);

        return marker;
    }

    this.openInfowindowDriver = function (marker) {
        var driver = marker.get('data');

        var gpsSourceLabel = driver.location.gpsSource ? this.getGpsSourceLabel(driver.location.gpsSource) : 'Mobile App'
        var gpsSourceIcon = gpsSourceLabel == 'Mobile App' ? 'mobile' : 'car';

        var drvStateHtml = '';
        if (driver.statuses && driver.statuses.length > 0) {
            for (var i = 0; i < driver.statuses.length; i++) {
                drvStateHtml +=
                    ('<tr><td class="key">' + twbkMap.getDriverStatusText(driver.statuses[i].statusId) + ' Call ' +
                    '</td><td>' + this.getCallLink(driver.statuses[i].callNumber) + ' &nbsp;Updated at ' + getTimeLabel(driver.statuses[i].statusTime) + '</td></tr>');
            }
        }

        var iwIcon = '<span class="map-marker-info-icon"><img src="/Map/images/driver-mini.png"/></span>';
        var infoWindowContent = '<div class="map-marker-info">' +
            '<header>' + iwIcon + '<h3>' + driver.name + '</h3></header>' +
            ' <section>' +
            '  <table class="map-marker-info-table">' +
            '   <tr><td class="key">Location Updated:</td><td>' + getTimeLabel(driver.location.timestamp) + '</td></tr>' +
            drvStateHtml +
            '  </table>' +
            '  <div class="map-marker-info-options">' +
            '  <span><i class="fa fa-' + gpsSourceIcon + '"></i> ' + gpsSourceLabel + '</span>' +
            '  </div>' +
            ' </section>' +
            '</div>'

        this.infowindow.setContent(infoWindowContent)
        this.infowindow.open(this.map, marker);
    }

    this.dropMarker = function (id) {
        if (typeof this.markers[id] == 'object') {
            this.markers[id].setMap(null);
        }
    }

    this.getMarker = function (id) {
        return typeof this.markers[id] != 'undefined' ? this.markers[id] : null;
    }

    this.getMarkerBy = function (type, key, val) {
        for (var k in this.markers) {
            if (this.markers[k].get('type') == type && this.markers[k].get('data')[key] == val) {
                return this.markers[k];
            }
        }
        
        return null;
    }

    this.getMarkerById = function (id) {
        return this.markers[id] || null;
    }

    this.getLocationFromAddress = function (address, done) {
        this.geocoder.geocode({ 'address': address }, function (results, status) {
            if (status === google.maps.GeocoderStatus.OK) {
                done(results[0].geometry.location)
            } else {
                done(null, status)
            }
        })
    }

    //
    // Utility Functions
    //

    this.getMarkersByType = function (type) {
        var data = [];

        for (var k in this.markers) {
            if (this.markers[k].get('type') == type) {
                data.push(this.markers[k]);
            }
        }

        return data;
    }

    this.getCallMarkers = function () {
        var calls = [];

        for (var k in this.markers) {
            if (this.markers[k].get('type') == 'call') {
                calls.push(this.markers[k]);
            }
        }

        return calls;
    }

    this.getDriverMarkers = function () {
        var drivers = [];

        for (var k in this.markers) {
            if (this.markers[k].get('type') == 'driver') {
                drivers.push(this.markers[k]);
            }
        }

        return drivers;
    }

    this.getAssignedDriverLink = function (call) {
        var marker = this.getMarkerBy('driver', 'id', (call.assets.length > 0 && call.assets[0].driver) ? call.assets[0].driver.id : 0);
        
        if (marker) {
            var driver = marker.get('data');
            return '<a href="#" class="winfo-option" onclick="showDriver(this, ' + driver.id + ')">' + driver.name + ' <i class="fa fa-location-arrow"></i></a>';
        } else {
            var driverName = (call.assets.length > 0 && call.assets[0].driver) ? call.assets[0].driver.name : 'Unknown';
            return driverName + '&nbsp;<span class="winfo-warn-message"><br/>(Driver isn\'t reporting location)</span>';
        }
    }

    this.getCallLink = function (callNumber) {
        var marker = this.getMarkerBy('call', 'callNumber', callNumber);

        if (marker) {
            return '<a href="#" class="winfo-option" onclick="showCall(' + callNumber + ')">#' + callNumber + ' <i class="fa fa-location-arrow"></i></a>';
        } else {
            return '#' + callNumber + '<span class="winfo-warn-message"><br/>(Warning: Call not loaded, maybe its address isn\'t geo-localizable)</span>';
        }
    }

    this.circlePath = function (cx, cy, r) {
        return 'M ' + cx + ' ' + cy + ' m -' + r + ', 0 a ' + r + ',' + r + ' 0 1,0 ' + (r * 2) + ',0 a ' + r + ',' + r + ' 0 1,0 -' + (r * 2) + ',0';
    }

    this.getColorByStatus = function (statusId) {
        switch (statusId) {
            case 0: return '#ff9900';
            case 1: return '#2850ab';
            case 2: return 'lightgreen';
            case 3: return 'green';
            case 4: return '#3294F5';
            default: return '#000';
        }

        //TODO seems table DispatchStatuses(color column) are not updated
        //for (var i = 0; i < towbook.statuses.length; i++) {
        //    if (towbook.statuses[i].id == statusId)
        //        return towbook.statuses[i].hexColor;
        //}

        //return '#000';
    }

    this.getCallStatusText = function (status) {
        switch (status) {
            case 0: return 'Waiting';
            case 1: return 'Dispatched';
            case 2: return 'En Route';
            case 3: return 'On Scene';
            case 4: return 'Towing';
            case 5: return 'Completed';
            default: return '';
        }
    }

    this.getDriverStatusText = function (status) {
        switch (status) {
            case 0: return "Currently assigned to";
            case 1: return 'Dispatched to';
            case 2: return "En route to";
            case 3: return "On scene for";
            case 4: return "Currently Towing";
            case 5: return "Finished Towing";
            default: return '';
        }
    }
 
    this.getShortName = function (name, maxChars) {
        var sn = name.split(' ').map(function (s) {
            return s.charAt(0).match(/^[a-zA-Z]*$/) ? s.charAt(0) : '';
        }).join('');

        return maxChars ? sn.substring(0, maxChars) : sn;
    }

    this.isCoordNear = function (coordsSrc, coordsTarget) {
        var precision = 5;

        if (coordsSrc.latitude.toString().substring(0, precision + 3) === coordsTarget.latitude.toString().substring(0, precision + 3) &&
            coordsSrc.longitude.toString().substring(0, precision + 3) === coordsTarget.longitude.toString().substring(0, precision + 3)) {
            return true;
        }

        return false;
    }

    this.getGpsSourceLabel = function (str) {
        switch (str.toLowerCase()) {
            case 'mobile': return 'Mobile App';
            case 'geotab': return 'GeoTab';
            case 'tomtom': return 'TomTom';
            case 'driverlocate': return 'Driver Locate';
            case 'usft': return 'US Fleet Tracking';
            default: str;
        }
    }
}

TowbookMap.createMarker = function (options) {
    var labelContent = '';
    if (options.markerIconCls) {
        labelContent = '<div class="map-icon-base ' + options.markerIconCls + '"></div>' +
            (options.markerLabel ?
                '<div class="map-label"><span class="map-label-text ' + options.markerLabelCls + '">' + options.markerLabel + '</span></div>' : '');
    } else {
        labelContent = '<div class="map-icon-base">' + options.markerIconHtml + '</div>' +
            (options.markerLabel ?
            '<div class="map-label"><span class="map-label-text">' + options.markerLabel + '</span></div>' : '');
    }

    var markerDefaults = {
        labelContent: labelContent,
        labelAnchor: new google.maps.Point(20, 40),
        labelClass: "map-icon-label",
        labelStyle: { opacity: 1.0 },
    }
    var iconDefaults = {
        path: TowbookMap.MAP_PIN,
        fillColor: '#000',
        fillOpacity: 1,
        strokeColor: '#fcfcfc',
        strokeWeight: 2,
        scale: 0.8
    }

    options = typeof jQuery != 'undefined' ? jQuery.extend(markerDefaults, options) : Object.assign(markerDefaults, options);
    options.icon = typeof jQuery != 'undefined' ? jQuery.extend(iconDefaults, options.icon) : Object.assign(iconDefaults, options.icon);

    return new MarkerWithLabel(options);
}

TowbookMap.createIcon = function (options) {
    var defaults = {
        path: twbkMap.circlePath(0, -8, 8),
        fillColor: 'orange',
        fillOpacity: 1,
        strokeColor: '#fcfcfc',
        strokeWeight: 2,
        scale: 1
    }
    return typeof jQuery != 'undefined' ? jQuery.extend(defaults, options) : Object.assign(defaults, options);
}

// Constants
TowbookMap.MAP_PIN = 'M0-48c-9.8 0-17.7 7.8-17.7 17.4 0 15.5 17.7 30.6 17.7 30.6s17.7-15.4 17.7-30.6c0-9.6-7.9-17.4-17.7-17.4z';
TowbookMap.SQUARE_PIN = 'M22-48h-44v43h16l6 5 6-5h16z';
TowbookMap.SHIELD = 'M18.8-31.8c.3-3.4 1.3-6.6 3.2-9.5l-7-6.7c-2.2 1.8-4.8 2.8-7.6 3-2.6.2-5.1-.2-7.5-1.4-2.4 1.1-4.9 1.6-7.5 1.4-2.7-.2-5.1-1.1-7.3-2.7l-7.1 6.7c1.7 2.9 2.7 6 2.9 9.2.1 1.5-.3 3.5-1.3 6.1-.5 1.5-.9 2.7-1.2 3.8-.2 1-.4 1.9-.5 2.5 0 2.8.8 5.3 2.5 7.5 1.3 1.6 3.5 3.4 6.5 5.4 3.3 1.6 5.8 2.6 7.6 3.1.5.2 1 .4 1.5.7l1.5.6c1.2.7 2 1.4 2.4 2.1.5-.8 1.3-1.5 2.4-2.1.7-.3 1.3-.5 1.9-.8.5-.2.9-.4 1.1-.5.4-.1.9-.3 1.5-.6.6-.2 1.3-.5 2.2-.8 1.7-.6 3-1.1 3.8-1.6 2.9-2 5.1-3.8 6.4-5.3 1.7-2.2 2.6-4.8 2.5-7.6-.1-1.3-.7-3.3-1.7-6.1-.9-2.8-1.3-4.9-1.2-6.4z';
TowbookMap.ROUTE = 'M24-28.3c-.2-13.3-7.9-18.5-8.3-18.7l-1.2-.8-1.2.8c-2 1.4-4.1 2-6.1 2-3.4 0-5.8-1.9-5.9-1.9l-1.3-1.1-1.3 1.1c-.1.1-2.5 1.9-5.9 1.9-2.1 0-4.1-.7-6.1-2l-1.2-.8-1.2.8c-.8.6-8 5.9-8.2 18.7-.2 1.1 2.9 22.2 23.9 28.3 22.9-6.7 24.1-26.9 24-28.3z';
TowbookMap.SQUARE = 'M-24-48h48v48h-48z';
TowbookMap.SQUARE_ROUNDED = 'M24-8c0 4.4-3.6 8-8 8h-32c-4.4 0-8-3.6-8-8v-32c0-4.4 3.6-8 8-8h32c4.4 0 8 3.6 8 8v32z';

CALL_PRIORITY_EMERGENCY = 1;
CALL_PRIORITY_NORMAL = 0;
CALL_PRIORITY_LOW = 2;

// 
var getLocationFromAddress = function (address, done) {
    var geocoder = new google.maps.Geocoder();

    geocoder.geocode({ 'address': address }, function (results, status) {
        if (status === google.maps.GeocoderStatus.OK) {
            done(results[0].geometry.location)
        } else {
            done(null, status)
        }
    })
}