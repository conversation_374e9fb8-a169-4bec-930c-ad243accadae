$.extend(towbook.views, {
    dispatch: { 
        getUrlVars: function(){
            var vars = [], hash;
            var hashes = window.location.href.slice(window.location.href.indexOf('?') + 1).split('&');
            for(var i = 0; i < hashes.length; i++)
            {
                hash = hashes[i].split('=');
                vars.push(hash[0]);
                vars[hash[0]] = hash[1];
            }
            return vars;
        },
        getUrlVar: function(name){
            return towbook.views.dispatch.getUrlVars()[name];
        }
    }
});

towbook.views.dispatch.clearDetailView = function() { $.unblockUI(); };

if (towbook.debug) {        
    Pusher.log = function (message) {
        if (window.console && window.console.log) 
            window.console.log('real-time:', message);
    };
    WEB_SOCKET_DEBUG = true;
}

Pusher.channel_auth_endpoint = '/Security/Pusher.aspx';
var pusher = new Pusher('00d0fb70749a0a4fd6f9');

$(companyId).each(function (i, companyId) {
    pusher.subscribe('private-TWBK_Client_' + companyId)
        .bind('call_update', function (data) {
            log('Pusher::call_update -> with data:', data);

            // refresh the current list
            if (data.extra == "delete") {
                var targetEl = $("*[data-id=" + data.callId + "]");
                targetEl.slideUp(function () { $(this).remove(); });
                callsTabs.CallsRepository.calls = $.grep(callsTabs.CallsRepository.calls, function (item, index) { return item.id != data.callId; });
                callsTabs.updateTabs();
                return;
            } else {
                updateListItem(data.callId, true);
            }
            refreshDrivers();
        })
        .bind('location_report', function (data) {
            $(towbook.users).each(function (i, o) {
                if (o.id == data.userId) {
                    if (o._gps == null) o.gps = {};
                    o._gps.latitude = data.latitude;
                    o._gps.longitude = data.longitude;
                    o._marker = addMarker(o.name, o._gps.latitude, o._gps.longitude, o._marker);
                }
            });
            console.log('Pusher::location_report -> with data:', data);
            refreshDrivers();
        });

    function updateListItem(callId, removeOrigin) {
        var currentFilter;

        if (callsTabs.activeTabType() == "SEARCHTAB")
            currentFilter = null;
        else
            currentFilter = ($('#tabcontrol1 .selected a').data('statusFilter') + ",").split(",");

        $.ajax({
            url: '/api/calls/' + callId,
            type: 'GET',
        }).done(function (data) {
            var target = $("*[data-id=" + callId + "]");
            var active = target.hasClass('active');
            var late = target.hasClass('late');
            var isNewCall = false;
            var animateCall = false;

            var removeOrigin = target.length > 0;

            if (removeOrigin || target.length > 0) {
                log('Updating existing call...');
                target = $("*[data-id=" + callId + "]"); // remove old element
            } else {
                log('Creating new call...');
                callsTabs.CallsRepository.calls.push(data);

                isNewCall = true;
            }

            callsTabs.CallsRepository.calls =
                $(callsTabs.CallsRepository.calls).map(function (index, item) {
                    if (item.id == callId)
                        return data;
                    else
                        return item;
                });

            var call = CallsTabs.getDispatchEntryHtml(data);

            if (currentFilter != null && currentFilter.indexOf(CallsTabs.getStatus(data).toString()) == -1) {
                $(target).slideUp(function () {
                    $(this).remove();
                });

                if (data.status.id == 5) {
                    $.playSound('/ui/audio/complete');
                }


                $.unique(callsTabs.CallsRepository.calls);
                callsTabs.updateTabs();
                return;
            }

            var oldPosition = target.index();

            $.unique(callsTabs.CallsRepository.calls);
            callsTabs.sortCalls(callsTabs.CallsRepository.calls);
            callsTabs.updateTabs();

            var activeTabLink = $('ul.TabList li.selected:visible', callsTabs.$tabControl).find('a.tabLink');
            if ($('input[name=' + (activeTabLink != null ? activeTabLink.attr('id') : '') + '_sortBy]:checked').val() == 'byGroups') {
                // Groups view. Repopulate the list.
                callsTabs.updateActiveTab($(activeTabLink));
                var id = $(call).data('id');
                $.each($('.entryRow[data-id=' + id + ']'), function (index, item) {
                    if ($(this).is(':visible')) {
                        var color = $(item).css('background-color');
                        $(item).animate({ backgroundColor: '#bed8f0' }, 250, function () {
                            $(this).animate({ backgroundColor: color }, 750, function () {
                                $(this).css("background-color", "");
                            })
                        });
                    }
                });

            } else {

                var activeTabCallRepository = [];
                var companies = towbook.companies;

                if ($('body').data('companyId') != 'all' && typeof ($('body').data('companyId')) != 'undefined')
                    companies = $('body').data('companyId');

                $.each(callsTabs.CallsRepository.calls, function (index, call) {
                    if (currentFilter != null && currentFilter.indexOf(CallsTabs.getStatus(call).toString()) != -1) {
                        if (typeof (companies) == 'number') {
                            if (companies == call.companyId)
                                activeTabCallRepository.push(call);
                        }
                        else
                            activeTabCallRepository.push(call);
                    }
                });
                $.unique(activeTabCallRepository);
                callsTabs.sortCalls(activeTabCallRepository);

                var newPosition = -1;


                for (i = 0; i < activeTabCallRepository.length; i++) {
                    if (activeTabCallRepository[i].id == data.id) {
                        newPosition = i;
                        break;
                    }
                }

                if (callsTabs.activeTabIsSortable() || oldPosition != -1 && newPosition != -1 && oldPosition != newPosition) {
                    if (target.length > 0) {
                        if (oldPosition != newPosition) {
                            console.log("AC: !!! Move call !!!", oldPosition, newPosition);

                            target.detach();

                            var newTarget = $('.entriesTable li.entryRow:eq(' + newPosition + ')', callsTabs.$tabControl);
                            if (newTarget.length == 0)
                                $('.entriesTable').append(call);
                            else
                                $(newTarget).before(call);
                            target = $("*[data-id=" + callId + "]");

                            var color = $(call).css('background-color');
                            $(call).animate({ backgroundColor: '#bed8f0' }, 250, function () {
                                $(this).animate({ backgroundColor: color }, 750, function () {
                                    $(this).css("background-color", "");
                                })
                            });
                            animateCall = true;
                        }
                        else {
                            $(target).replaceWith(call);
                            animateCall = true;
                        }
                    } else {
                        console.log("AC: !!! Add call !!!", oldPosition, newPosition);
                        var newTarget = $('.entriesTable li.entryRow:eq(' + newPosition + ')', callsTabs.$tabControl);
                        if (newTarget.length == 0)
                            $('.entriesTable').append(call);
                        else
                            $(newTarget).before(call);
                        target = $("*[data-id=" + callId + "]");
                        animateCall = true;
                    }

                } else {
                    if (isNewCall && (target == null || target.length == 0) && callsTabs.activeTabType() != "SEARCHTAB") {
                        $('.entriesTable', callsTabs.$tabControl).prepend(call);
                        target = $("*[data-id=" + callId + "]");

                    } else {
                        $(target).replaceWith(call);
                    }

                    animateCall = true;
                }


                call.tbkBehav();

                if (active)
                    $(call).addClass('active').find('.DispatchAction').show();

                if (late) {
                    $(call).addClass('late');
                }

                CallsTabs.showLateCalls(data);

                if (animateCall) {
                    if ($(call).is(':visible')) {
                        var color = $(call).css('background-color');
                        $(call).animate({ backgroundColor: '#bed8f0' }, 250, function () {
                            $(this).animate({ backgroundColor: color }, 750, function () {
                                $(this).css("background-color", "");
                            })
                        });
                    }
                }
            }
        }).error(function (xhr, status, error) {
            log("can't retrieve data for call id: " + callId);
        });
    }
});

function log(data) {
    if (typeof towbook.log != 'undefined') {
        towbook.log(data);
    }
}

var Tooltip
Tooltip = function(tip) {
    this.tip = tip;
    this.buildDOM();
};

$.extend(Tooltip.prototype, google.maps.OverlayView.prototype, {

    // build the DOM
    buildDOM: function () {
        // Window DIV
        this.wdiv = $("<div></div>").addClass('Window').append(this.tip);
        // Start Closed
        this.close();
    },

    // API - onAdd
    onAdd: function () {
        $(this.getPanes().floatPane).append(this.wdiv);
    },

    // API - onRemove
    onRemove: function () {
        this.wdiv.detach();
    },

    // API - draw
    draw: function () {
        var pos, left, top;
        // projection is accessible?
        if (!this.getProjection()) return;
        // position is accessible?
        if (!this.get('position')) return;
        // convert projection
        pos = this.getProjection().fromLatLngToDivPixel(this.get('position'));
        // top offset
        top = pos.y - this.getAnchorHeight() / 2 - this.wdiv.outerHeight() / 2;
        // left offset
        if (this.getMap().getCenter().lng() > this.get('position').lng()) {
            left = pos.x + this.wdiv.outerWidth() * 0.3;
        } else {
            left = pos.x - this.wdiv.outerWidth() * 1.3;
        }
        // window position
        this.wdiv.css('top', top);
        this.wdiv.css('left', left);
    },

    // open Tooltip
    open: function (map, anchor) {
        // bind to map
        if (map) this.setMap(map);
        // bind to anchor
        if (anchor) {
            this.set('anchor', anchor);
            this.bindTo('anchorPoint', anchor);
            this.bindTo('position', anchor);
        }
        // need to force redraw otherwise it will decide to draw after we show the Tooltip                    
        this.draw();
        // show tooltip
        this.wdiv.show();
        // set property
        this.isOpen = true;
    },

    // close Tooltip
    close: function () {
        // hide tooltip
        this.wdiv.hide();
        // set property
        this.isOpen = false;
    },

    // correctly get the anchorPoint height
    getAnchorHeight: function () {
        // See: https://developers.google.com/maps/documentation/javascript/reference#InfoWindow
        //   "The anchorPoint is the offset from the anchor's position to the tip of the InfoWindow."
        return -1 * this.get('anchorPoint').y;
    }
});