////////////////////
// Event Dispatcher
////////////////////

window.eventDispatcherDebug = debug('Twbk-Map:EventDispatcher');
window.twbk = {};

(function (App) {
    "use strict";

    var eventSubscriptions = {};

    App.eventDispatcher = {

        subscribe: function (eventName, callback) {
            // Retrieve a list of current subscribers for eventName (if any)
            var subscribers = eventSubscriptions[eventName];

            if (typeof subscribers === 'undefined') {
                // If no subscribers for this event were found,
                // initialize a new empty array
                subscribers = eventSubscriptions[eventName] = [];
            }

            // Add the given callback function to the end of the array with
            // eventSubscriptions for this event.
            subscribers.push(callback);
        },

        unsubscribe: function (eventName, existingCallback) {
            var
              subscribers = eventSubscribers[eventName],
              callbackIndex;

            // If we don't know this event, don't even worry about it.
            if (typeof subscribers === 'undefined') { return; }

            callbackIndex = subscribers.indexOf(existingCallback);

            // Not found among subscribers, don't even worry about it.
            if (callbackIndex === -1) { return; }

            //remove from subscribers
            subscribers.splice(callbackIndex, 1);
        },

        unsubscribeAll: function (eventName) {
            delete eventSubscribers[eventName];
        },

        dispatch: function (eventName, payload, context) {

            var
              // Retrieve a list of subscribers for the event being triggered
              subscribers = eventSubscriptions[eventName], i, iMax;

            if (typeof subscribers === 'undefined') {
                // No list found for this event, return early to abort execution
                eventDispatcherDebug('[EventDispatcher] trigger: no subscribers for event "' + eventName + '"');
                return;
            }

            // Ensure data is an array or is wrapped in an array,
            // for Function.prototype.apply use
            //data = (data instanceof Array) ? data : [data];

            // Set a default value for `this` in the callback
            context = context || App;

            var iMax = subscribers.length;

            for (i = 0; i < iMax; i += 1) {
                subscribers[i].apply(context, [payload]);
            }

            eventDispatcherDebug('trigger: event "' + eventName + '" dispatched to ' + subscribers.length + ' subscribers, with payload: ', payload);
        }
    };
}(window.twbk));

///////////////////
// Right Panel
///////////////////

var RightPanel = function (twbkMap) {
    this.list = null;
    this.twbkMap = null;
    this.el = null;

    this.constructor = function (twbkMap) {
        this.twbkMap = twbkMap;
        this.el = $('#drivers-datatable');
        this.list = this.el.DataTable({
            responsive: true,
            columnDefs: [
                { width: '50px', responsivePriority: 1, targets: 0 },
                { width: '100%', responsivePriority: 2, targets: -1 }
            ],
            scrollY: ($('#driver-status').height() - 100) + 'px',
            scrollCollapse: true,
            paging: false,
            ordering: false,
            info: false,
            language: {
                search: 'Search Driver',
                emptyTable: 'No active Drivers found.'
            }
        });

        // setting events
        var self = this;

        this.el.find('tbody').on('click', 'tr', function () {

            //// Toggle 'selected' class when a row is clicked
            //if ($(this).hasClass('selected')) {
            //    $(this).removeClass('selected');
            //} else {
            //    self.list.$('tr.selected').removeClass('selected');
            //    $(this).addClass('selected');
            //}

            var marker = self.twbkMap.getMarker($(this).data('marker-id'));
            self.twbkMap.map.panTo(marker.getPosition());

            google.maps.event.trigger(marker, 'click');
        });
    }

    this.add = function (data) {
        logger('Map:driver:right-panel')('add', data)
        var drvLocTime = '';
        var extraInfo = '';

        var drvStateHtml = '';
        if (data.statuses && data.statuses.length > 0) {
            for (var i = 0; i < data.statuses.length; i++) {
                drvStateHtml +=
                    ('<div class="drv-state">' + twbkMap.getDriverStatusText(data.statuses[i].statusId) +
                     ' Call <b>#' + data.statuses[i].callNumber + '</b></div>');
            }
        }

        if (data.timestamp) {
            drvLocTime = '<div>Location Updated @ ' + getTimeLabel(data.timestamp) + '</div>';
        }

        if (data.gpsSource) {
            var gpsSourceLabel = twbkMap.getGpsSourceLabel(data.gpsSource);
            var gpsSourceIcon = gpsSourceLabel == 'Mobile App' ? 'mobile' : 'car';

            extraInfo = '<div><i class="fa fa-' + gpsSourceIcon + '"></i> ' + gpsSourceLabel + '</div>';
        }

        var i = this.list.row.add([
            '<div class="driver-avatar pulse" style="background-color:' + this.twbkMap.getColorByStatus(data.highestStatusId) + '">' + data.avatar + '</div>',
            '<div class="cell-main" data-user-id="' + data.driver.user.id + '">' +
            '  <div class="cell-title">' +
            '    ' + data.title +
            '  </div>' +
            '  <div class="cell-body">' + drvStateHtml + 
            '    <span class="drv-loc-time">' + drvLocTime + '</span>' +
            '    <span class="drv-loc-extra">' + extraInfo + '</span>' +
            '  </div>' +
            '</div>'
        ]).draw(false).index();
        this.list.rows(i).nodes().to$().attr("data-marker-id", data.markerId);
    }

    this.update = function (id, data) {
        logger('Map:driver')('rightPanel:update', id, data)
        var el = $('#drivers-datatable').find('tr[data-marker-id="' + id + '"]');
        var extraInfo = '';
        var drvLocTime = '';

        el.find('.driver-avatar').removeClass('pulse')
        setTimeout(function () {
            el.find('.driver-avatar').addClass('pulse');
        }, 50)

        if (data.title) {
            el.find('.cell-title').html(data.title)
        }

        var drvStateHtml = '';
        if (data.statuses && data.statuses.length > 0) {
            for (var i = 0; i < data.statuses.length; i++) {
                drvStateHtml +=
                    ('<div class="drv-state">' + twbkMap.getDriverStatusText(data.statuses[i].statusId) +
                        ' Call <b>#' + data.statuses[i].callNumber + '</b></div>');
            }
        }

        if (data.timestamp) {
            drvLocTime = '<div>Location Updated @ ' + getTimeLabel(data.timestamp) + '</div>';
        }

        if (data.gpsSource) {
            var gpsSourceLabel = twbkMap.getGpsSourceLabel(data.gpsSource);
            var gpsSourceIcon = gpsSourceLabel == 'Mobile App' ? 'mobile' : 'car';

            extraInfo = '<div><i class="fa fa-' + gpsSourceIcon + '"></i> ' + gpsSourceLabel + '</div>';
        }    

        el.find('.cell-body').html(drvStateHtml +
            '<span class="drv-loc-time">' + drvLocTime + '</span>' +
            '<span class="drv-loc-extra">' + extraInfo + '</span>');

        el.find('.driver-avatar').css('background-color', twbkMap.getColorByStatus(data.highestStatusId))
    }

    this.constructor(twbkMap)
}

////////////////////////////
// Towbook Objects Wrappers
////////////////////////////

var Call = function (call) {
    $.extend(this, call)

    this.getCurrentWaypoint = function () {
        var waypoint = null;

        if ($.inArray(call.status.id, [3, 4]) !== -1) { // if Call.Status is [3(OnScene) or 4(Towing)] => Use TowDestination info
            waypoint = call.waypoints[1];
        } else { // else => Use TowSource info
            waypoint = call.waypoints[0];
        }

        return waypoint;
    }
}

var Driver = function (driver, user, highestStatus, statuses, location) {
    $.extend(this, driver);

    this.user = null;
    this.location = null;
    this.highestStatus = null;
    this.statuses = null;

    this.setLocation = function (location) {
        this.location = location;
    }

    if (typeof user != 'undefined')
        this.user = user;

    if (typeof location != 'undefined')
        this.setLocation(location);

    if (typeof highestStatus != 'undefined')
        this.highestStatus = highestStatus;

    if (typeof statuses != 'undefined')
        this.statuses = statuses;
}

/////////////////////
// Utility functions
/////////////////////

var getActiveDriversCount = function (users, drivers) {
    var c = 0;

    for (var i = 0; i < users.length; i++) {
        var d = towbook.get(drivers, users[i].id, "userId");
        if (d) c++
    }

    return c;
}

var assignDriverPopup = new Popup({
    title: 'Update Call Status',
    width: 700,
    height: 410,
    modal: true    
})

var assignDriver = function (callId) {
    assignDriverPopup.show();
    assignDriverPopup.setContent('<img id="loader1" src="/UI/images/ajax-loading2.gif" width="36" height="36" alt="loading" style="position:absolute;left:49%;top:49%"/>')

    $.ajax({
        url: "/ajax/dispatch/" + callId + "/statusUpdate",
        context: $('#editor_ajaxView'),
        cache: false
    }).done(function (data, status, xhr) {
        assignDriverPopup.setContent(data);

        towbook.views.dispatch.clearDetailView = function (message) {
            if (message) {
                swal('Update Call Status', message, 'success');
            }

            assignDriverPopup.close();
        };

        var originalTitle = $('.ajax-view h2');
        originalTitle.css('display', 'none');
        assignDriverPopup.setTitle(originalTitle.text());
    }).fail(function (err) {
        sweetAlert('Error trying to update the Call status', err, 'error')
    });
}

var callEd = new CallEditorWindow();

var editCall = function (id) {
    callEd.show(id)
}

var showDriver = function (self, driverId) {
    var marker = twbkMap.getMarkerBy('driver', 'id', driverId);

    if (!marker) return;

    twbkMap.map.panTo(marker.getPosition());
    google.maps.event.trigger(marker, 'click');
}

var showCall = function (callNumber) {
    var marker = twbkMap.getMarkerBy('call', 'callNumber', callNumber);
    if (!marker) return;

    twbkMap.map.panTo(marker.getPosition());
    google.maps.event.trigger(marker, 'click');
}

var getTimeLabel = function (time) {
    if (time.indexOf('T') == -1) return time;
    if (time.indexOf('-') == -1) return time;

    var now = new Date();
    var d = time.split('T')[0].split('-');
    var t = towbook.formatAMPM(new Date(time));

    // If time is on today's date, just return the time (like 9:09 AM)
    if (now.getFullYear() == d[0] && (now.getMonth()+1) == parseInt(d[1]) && now.getDate() == parseInt(d[2])) {
        return t;

    // Else return the short date and then the time (like Jun 10, 9:09 AM)
    } else {
        var c = new Date(time)
        var cp = c.toLocaleDateString('en-US', { weekday: 'long', year: 'numeric', month: 'short', day: 'numeric' })

        return cp.split(',')[1] + ', ' + t;
    }

    return towbook.formatAMPM(time)
}

function getHighestStatus(statuses, driverId) {
    
    var highestStatus;
    if (statuses && statuses.length > 0) {

        // Sort the driver statuses by statusId (descending), then by statusTime (ascending)
        statuses = statuses.sort(function (a, b) {
            if (a.statusId < b.statusId)
                return 1;
            if (a.statusId > b.statusId)
                return -1;
            if (a.statusId == b.statusId) {
                var aTime = moment(a.statusTime);
                var bTime = moment(b.statusTime);
                if (aTime < bTime)
                    return -1;
                if (aTime > bTime)
                    return 1;
                return 0;
            }
        });

        // Return the top one
        highestStatus = statuses[0];
    }
    return highestStatus;
}

function logger(name) {
    if (!debug || !towbook.debug) {
        return function () { }
    }

    if (!window.loggers)
        window.loggers = {};

    window.Debug = debug;
    window.Debug.enable('*');

    if (typeof window.loggers[name] == 'undefined')
        window.loggers[name] = debug(name)

    return window.loggers[name];
}
