.ajax-view > h2 { 
	padding: 12px; 
	font-family: "segoe ui", "Open Sans", arial;
	font-weight: normal;
	color: rgb(126, 81, 0);	
	margin-top: 0;
}

.navigation-row  {
	padding: 12px; 
}
.gps-driver { 
	border-radius: 0 !important;
	border-color: white;
}
.tb-marker {
  box-sizing:border-box;
  background: white;
  box-shadow: 0px 0px 5px white;
  border:3px solid red;
  height: 30px;
  width: 30px;
  overflow: hidden;
  padding-top: 6px;
  text-align: center;
  border-radius: 30px;
  x-webkit-animation: pulse 1s ease 1s 2;
  x-moz-animation: pulse 1s ease 1s 2;
  animation: pulse 1s ease 1s 2;
  font-size: 10px;
  font-family: "segoe ui", "Open Sans", times new roman;
}
.waiting { 
	background-image: url(pin-waiting.png);
background-repeat: no-repeat;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  padding-top: 5px;
  border: 2px solid white;
  background-color: #ff9900;
  color: white
}


.dispatched { 
  padding-top: 5px;
  border: 2px solid white;
  background-color: #2850ab !important;
  color: white !important;
}

.enroute { 
  padding-top: 5px;
  border: 2px solid white;
  background-color: lightgreen !important;
  color: white !important;
}

.onscene { 
  padding-top: 6px;
  border: 2px solid white;
    background-color: green !important;
  color: white !important
}
.towing { 
  background-color: #3294F5 !important;
  color: white !important
}

/* ANIMATIONS */
@-webkit-keyframes pulse {
 40% {
    -webkit-transform: scale(1.5);
    -moz-transform: scale(1.5);
    transform: scale(2);
  }

  100% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    transform: scale(1);
  }
}
@-moz-keyframes pulse {
 40% {
    -webkit-transform: scale(1.5);
    -moz-transform: scale(1.5);
    transform: scale(1.5);
  }

  100% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    transform: scale(1);
  }
}
@keyframes pulse {
  40% {
    -webkit-transform: scale(1.5);
    -moz-transform: scale(1.5);
    transform: scale(1.5);
  }

  100% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    transform: scale(1);
  }
}