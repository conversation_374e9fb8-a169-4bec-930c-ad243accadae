<%@ Page Language="C#" AutoEventWireup="true" CodeFile="Email.aspx.cs" Inherits="Impound_Email" %>
<style type="text/css">
    body {
        background-color: white;
    }

    textarea {
        padding: 10px;
        margin: 0;
        border: 1px solid #cfcfcf;
        border-radius: 3px;
    }

    span.x-count-text {
        color: grey;
    }
    
    span.error{
        color: red;
    }

    #loader {
        position: absolute;
        top: 50%;
        margin-top: -70px; /* half of #content height*/
        left: 0;
        width: 100%;
        display: none;
    }
    #loaderInner {
        margin-left: auto;
        margin-right: auto;
        width: 400px;
        height: 100px;
        text-align: center;
        padding-top: 90px;
        background-repeat: no-repeat;
        background-image:url(/ui/images/ajax-loading2.gif);
        background-position: 50% 50px
    } 
#SendMessageForm label { display: inline }

</style>

<div id="SendMessageForm" class="towbook-dialog" data-id="<% = _impound.Id %>">


<h1>Send a copy of this impound via Email</h1>

<style type="text/css">
label { display: block } 
span.tip { display: block; font-size: small; font-style:italic; margin-top: 10px; margin-bottom: 10px; clear: both }
.addressBook { list-style: none}
ul.list { margin-bottom: 20px }
#email { width:100% } 
#divRecipients { display: none; clear: both }
#divAddressBook { display: none; clear: both }
a.add, a.delete { cursor: pointer }

#divAddressBook ul li, #divRecipients ul li { margin-top: 5px; margin-bottom: 5px }

#divAddressBook ul li { border-left: solid 4px #cfcfcf; color: #777777; padding-left: 3px }
#divRecipients ul li { border-left: solid 4px #7fbf4d; color: #55842F; padding-left: 3px }

#listHolder, #emailListHolder {
    display: none;
}

#fileList {
    padding: 0;
    margin: 0;
}
</style>
        <div style="margin-top: 15px; margin-bottom: 10px;" class="RemoveBorders">
          <asp:Label runat="server" ID="lblNoInvoice" />
  	  <span>Please enter a valid email address or 10 digit cell phone number:</span>
	  <input type="text" name="email" id="email" value="<%: _email %>" style="padding: 5px" placeholder="Enter an email address or cell phone number to send the receipt to" />

        <br/>
        <strong>Add a message</strong>
        <textarea id="x-optional-message" placeholder="Enter an optional message to send to the recepients" rows="6"></textarea>
        <br />

		<input type="radio" id="sendFullInvoice" name="showPricing" value="full" checked onchange="selectOpt(this)"><label for="sendFullInvoice">Send full invoice with photos</label>
		<input type="radio" id="sendNoPricesWithPhotos" name="showPricing" value="summary" onchange="selectOpt(this)"><label for="sendNoPricesWithPhotos">Send summary with photos.</label>
        <br/><br />

        <div id="invoiceOptions">
            <% if (SquareIncludePaymentLink != null) { %>
            <label><input type="checkbox" id="includePaymentLinkOpt" <% = SquareIncludePaymentLink.Value ? "checked='checked'" : "" %> />Include a link to make online payment</ label ><br/>
            <% } %>
            <label><input type="checkbox" id="hideChargesOpt" <%= (HideCharges && (!(new int[] { 4654, 3643, 9866, 6200, 1235, 1873, 4552, 7394, 1917, 4082, 3853, 9083, 543, 8450, 3645, 6686, 5783, 1499, 8781, 2064 }.Contains(CompanyId))) ? "checked='checked'" : "") %> />Hide charges when printing or viewing the invoice</label><br />
            <label><input type="checkbox" id="hideDiscountsOpt" <%= HideDiscounts ? "checked='checked'" : "" %>/>Hide discounts when printing or viewing the invoice</label><br/>
            <label><input type="checkbox" id="hidePhotosOpt" <%= HidePhotos ? "checked='checked'" : "" %>/>Hide link to photos when printing or viewing the invoice</label><br/>
            <% if (AllowHidingOfLineItems) { %>
            <label><input type="checkbox" id="hideInvoiceItemsOpt" />Hide individual line items from the invoice</label><br/>
            <% } %>
        </div>
        </div>

        <div id="listHolder">
            <table id="fileList" class="list">
                <thead>
                    <tr>
                        <td colspan="2">File</td>
                        <td>Date</td>
                        <td>User</td>
                        <td>Description</td>
                        <td style="width: 40px">Invoice</td>
                    </tr>
                </thead>
                <tbody></tbody>
            </table>
        </div>

        <div id="emailListHolder">
            <table id="emailHistoryList" class="list">
                <thead>
                    <tr>
                        <td>Sent To</td>
                        <td>Sent Date</td>
                        <td>Sent By</td>
                        <td>Delivery Status</td>
                    </tr>
                </thead>
                <tbody></tbody>
            </table>
        </div>
        </div>
    <div class="towbook-dialog-footer">
        <ul>
		<li><input type="button" class="standard-button" id="send-email" value="Send" title="Send Message"  /></li>
	    <li><input type="button" class="standard-button" id="cancelBtn" value="Cancel" rel="dialog-cancel" title="Cancel" /></li>
	</ul>
    </div>
  </div>



<div id="loader">
    <div id="loaderInner" >Sending, please wait...</div>
</div>

<script type='text/x-jQuery-tmpl' id='tpl-file'>
    <tr>
        <td style="max-width:17px"><input type="checkbox" id="file_check_${id}" onclick="fileCheckClick(this)" data-id="${id}" name="file_check_${id}" /></td>
        <td><a href="${url}" target="_blank">${_enc(filename)}</a></td>
        <td>${towbook.formatDate(createDate)} ${towbook.formatAMPM(createDate)}</td>
        <td>${_enc(ownerUserName)}</td>
        <td>${_enc(description)}</td>
        <td><a href="${url}&invoice=true" target="_blank" title="Download Invoice" class="grid place-items-center" style="display: grid; place-content: center">
            <i class="fa fa-download"></i>
        </a></td>
    </tr>
</script>


<script type='text/x-jQuery-tmpl' id='tpl-emailHistory'>
    <tr>
        <td>${_enc(emailAddress)}</td>
        <td>${towbook.formatDate(createDate)} ${towbook.formatAMPM(createDate)}</td>
        <td>${_enc(ownerUserName)}</td>
        <td>${statusMessage}</td>
    </tr>
</script>

<script type="text/javascript">
    const users = JSON.parse('<%= HttpUtility.JavaScriptStringEncode(_users) %>');

    $(function () {
        $('#send-email').click(function () {
        
        const email = $('#email').val();
	    if (email == "" || email.length == 0) {
                alert("Please enter an email address to send the message to.");
                return;
            }

            $('#SendMessageForm').hide();
            $('#loader').show();

            $.ajax({
                url: "/api/impounds/" + $('#SendMessageForm').data('id') + "/email",
                context: $('.ajax-view'),
                data: JSON.stringify({
                    id: $('#SendMessageForm').data('id'),
                    emails: [$('#email').val()],
                    includePaymentLink:  $('#includePaymentLinkOpt').is(":checked") ? 'true' : 'false',
                    hidePricing: $('#hideChargesOpt').is(":checked") || $('#sendNoPricesWithPhotos').is(":checked") ? 'true' : 'false',
                    hideDiscounts: $('#hideDiscountsOpt').is(":checked") && !$('#sendNoPricesWithPhotos').is(":checked") ? 'true' : 'false',
                    hidePhotos: $('#hidePhotosOpt').is(":checked") && !$('#sendNoPricesWithPhotos').is(":checked") ? 'true' : 'false',
                    hideLineItems: $('#hideInvoiceItemsOpt').is(":checked") ? 'true' : 'false',
                    optionalMessage: $('#x-optional-message').val()
                }),
                type: "POST",
                contentType: "application/json; charset=utf-8",
            }).done(function (data) {
                document.closeLightbox();

                if (popup && popup.popup) {

                    swal({
                        title: 'Email successfully sent',
                        type: 'success',
                    }).then(function () {
                        popup.popup.close();
                    });
                }
            }).error(function (xhr, status, error) {
                $(loader).hide();
                $(SendMessageForm).show();
                alert("Error" + status + ": " + error);
            });
        });

        if (typeof $.fancybox === "undefined") {
            document.closeLightbox = function () {
                window.location.href = "/dispatch/";
            }
        } else {
            if (parent != null) {
                document.closeLightbox = function () {
                    parent.closeLightbox();
                }
            }
        }

        $("input[rel=dialog-cancel]").unbind('click').click(function () {
            document.closeLightbox();
            return false;
        });

        loadFiles(); 
        buildEmailHistory();
    });

    function selectOpt(o) {
        $('#invoiceOptions').css('visibility', ($(o).val() == 'full') ? 'visible' : 'hidden')
        }

    function loadFiles() {
        const impoundCallId = '<%= _impound.DispatchEntry.Id %>';
        $('#fileList tbody').html('');

        if (typeof ($.template['emailfile']) == 'undefined')   //If template doesn't exist yet, compile it
            $("#tpl-file").template('emailfile');

        $.ajax({
            url: `/api/files/get?callId=${impoundCallId}`,
            type: 'GET',
        }).done(function (data) {
            if (data.length > 0) {
                $('#fileList thead').show();
                for (var i = 0; i < data.length; i++) {
                    $('#fileList').find('tbody').append($.tmpl('emailfile', data[i]));
                }
            } else {
                $('#fileList thead').hide();
            }

            $('#listHolder').show();
        });
    }

    function buildEmailHistory() {
        $('#historyList tbody').html('');

        if (typeof ($.template['emailHistory']) == 'undefined')   //If template doesn't exist yet, compile it
            $("#tpl-emailHistory").template('emailHistory');

        $.when(loadEmailHistory())
        .done(function (calls) {
            if (calls.length > 0) {
                $('#historyList thead').show();

                const htmlList = $('#emailHistoryList').find('tbody');

                for (let i = 0; i < calls.length; i++) {
                    let user = users.find(u => u.id == calls[i].userId);
                    calls[i].ownerUserName = user ? user.name : "User Unknown";
                    $(htmlList).append($.tmpl('emailHistory', calls[i]));
                }
            }

            $('#emailListHolder').show();

            if (!calls.length)
                $('#emailHistoryList thead').hide();

        }).fail(function (xhr, status, error) {
            console.log(xhr, status, error);
        });
    }

    function loadEmailHistory() {
        var deferred = new $.Deferred();

        $.ajax({
            url: '/api/calls/<%= _impound.DispatchEntry.Id %>/emailHistory',
            type: 'GET',
        }).done(function (data) {
            return deferred.resolve(data);
        }).fail(function (xhr, status, error) {
            return deferred.reject(xhr, status, error);
        });

        return deferred.promise();
    }
</script>

