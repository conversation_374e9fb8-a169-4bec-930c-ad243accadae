<%@ Page Language="C#" AutoEventWireup="true" CodeFile="ImpoundStatusUpdate.aspx.cs" Inherits="Impounds_ImpoundStatusUpdate" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">

<html xmlns="http://www.w3.org/1999/xhtml" >
<head id="Head1" runat="server">
    <title>Impounds - Update Status</title>
    <link rel="stylesheet" href="/ui/css/towbook.css" />
    <style>
    body { font-family: arial; font-size: 12px; color: black; background-image: none; background-color: white}
    .RemoveBorders * { border: none }
    </style>
</head>
<body>
    <form id="form1" runat="server">
    <div style="background-color: white; padding: 10px">
    <div style="font-weight: bold; font-size: 16px; border-bottom: dotted 1px #333333; font-family: Arial; padding-bottom: 10px; margin-bottom: 10px">
    Impound: Update Status
    </div>
<div style="line-height: 160%; margin-left: 10px">

    <div style="margin-top: 15px; margin-bottom: 10px;" class="RemoveBorders">
    <asp:Label runat="server" ID="lblError" style="color: Red; font-weight: bold" />
    
<%         foreach (var x in Extric.Towbook.Dispatch.Status.GetByCompany(465,
            Extric.Towbook.Dispatch.StatusCategory.Impounds)) { %>

<div><input id="status<% =x.Id %>" type="radio" name="status" value="<% = x.Id %>" <% if (_impound.CurrentStatus.Id == x.Id) Response.Write ("checked"); %> />
<label for="status<% =x.Id %>"><% =x.Name %></label></div>

<% }  %>

       
    </div>

</div>
</div>

  <div style="position: absolute; bottom: 0; left: 0; right: 0; text-align: right; padding-right: 5px; padding-bottom: 10px; padding-top: 10px; background-color: #333333; border: solid 1px white">
  <asp:Button ID="Button1" runat="server" Text="Update" CssClass="sm" />    
  <input type="button" id="Button2" value="Cancel" class="sm" onclick="parent.closeLightbox()" />    
  </div>
    
    </form>
</body>
</html>
