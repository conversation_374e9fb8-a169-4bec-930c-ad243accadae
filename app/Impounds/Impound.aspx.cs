using System;
using System.Collections;
using System.Configuration;
using System.Data;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.HtmlControls;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using Extric.Towbook;
using Extric.Towbook.WebShared;
using Extric.Towbook.Dispatch;
using System.Text;
using System.Linq;
using Extric.Towbook.Utility;
using System.Collections.Generic;
using Extric.Towbook.Company;
using System.Collections.ObjectModel;
using Extric.Towbook.Integration;
using Extric.Towbook.Dispatch.CallModels;

// 7:37 PM 12/14/2016 
public partial class Impounds_Impound : System.Web.UI.Page
{
    protected int _id;
    protected Extric.Towbook.Impounds.Impound _impound;
    protected Entry _de;
    protected Extric.Towbook.Impounds.ReleaseDetails _releaseDetails;

    protected bool _showStatus = false;
    protected bool hidePricing = false;
    protected bool _isWithinClosedPeriod = false;
	public bool hideChargesFromAccountUsers = CompanyKeyValue.GetFirstValueOrNull(WebGlobal.CurrentUser.CompanyId, Provider.Towbook.ProviderId, "HideChargesFromAccountUsers") == "1";
    protected string policeHoldMsg = "This vehicle is currently under police hold and cannot be released at this time.\\n\\nTo release this vehicle, please have the police hold status removed.";

    public string _LetterTemplateJson = "[]";
    public string _tasksJson = "[]";
    public string _contactsJson = "[]";

    protected string _auctionsJson = "[]";
    protected string _callModelJson = "[]";
    protected string _callPhotosJson = "[]";

    public static string _(string html)
    {
        return HttpUtility.HtmlEncode(html);
    }

    protected string GetInvoiceNumber()
    {

        Extric.Towbook.Impounds.Impound imp = _impound;

        if (imp.DispatchEntry == null)
            throw new Exception("invalid dispatch entry link");


        if (imp.DispatchEntry.Attributes.ContainsKey(Extric.Towbook.Dispatch.AttributeValue.BUILTIN_DISPATCH_CUSTOMINVOICENUMBER))
            return imp.DispatchEntry.Attributes[Extric.Towbook.Dispatch.AttributeValue.BUILTIN_DISPATCH_CUSTOMINVOICENUMBER].Value;
        return "";
    }

    protected bool AllowTowOutOptions()
    {
        bool allow = Global.CurrentUser.Company.HasFeature(Extric.Towbook.Generated.Features.Impounds_TowOuts);

        if (_impound.Hold)
            allow = false;

        if (_impound.Lot != null && _impound.Lot.AccountId > 0) {
            var acc = Extric.Towbook.Accounts.Account.GetById(_impound.Lot.AccountId.Value);
            if(acc != null && acc.Type == Extric.Towbook.Accounts.AccountType.StorageFacility)
                allow = false;
        }

        return allow;
    }

    private string BuildQuery()
    {
        StringBuilder sb = new StringBuilder();
        sb.Append("?");
        foreach (string key in Request.QueryString.Keys)
        {
            if (key.ToLowerInvariant() == "pdf")
                continue;

            sb.Append(key.ToLowerInvariant());
            sb.Append("=");
            sb.Append(Request.QueryString[key]);
            sb.Append("&");
        }
        return sb.ToString();

    }

    public string FormatAttributeValue(AttributeType type, string value)
    {
        switch (type)
        {
            case AttributeType.Boolean:
                return (value == "0" ? "No" : value == "1" ? "Yes" : "");
            default:
                return value;
        }
    }


    protected void Page_Load(object sender, EventArgs e)
    {
        this.Master.CurrentSection = Navigation.NavigationItemEnum.Impounds;
        this.Master.InnerTitle = "Impound Details ";
        this.Master.UseJquery = true;
        this.Master.UseReact = true;

        if (Global.CurrentUser.CompanyId == 2 || Global.CurrentUser.CompanyId == 465)
            _showStatus = true;

        if (Request.QueryString["id"] == null ||
            Request.QueryString["id"].Length == 0 ||
            Request.QueryString["id"] == "0")
        {
            Response.Redirect("/Impounds/");
            return;
        }
          
        _id = Convert.ToInt32(Request.QueryString["id"]);


        _impound = Extric.Towbook.Impounds.Impound.GetById(_id);

        if (_impound == null || !WebGlobal.CurrentUser.HasAccessToCompany(_impound.Company.Id))
        {
            if (WebGlobal.CurrentUser.CompanyId == 32564)
            {
                hidePricing = true;
            }
            else
            {
                var allow = false;

                if (_impound != null && 
                    _impound.Account != null &&
                    _impound.Account.ReferenceNumber != null)
                {

                    int thisCompany = 0;
                    if (int.TryParse(_impound.Account.ReferenceNumber, out thisCompany))
                    {
                        if (thisCompany == WebGlobal.CurrentUser.CompanyId)
                        {
                            allow = true;
                        }
                    }
                }

                if (!allow)
                    Response.Redirect("/Impounds/");
            }
        }


        if (_impound.Hold == true && _impound.DispatchEntry.GetAttribute(73) != null)
        {
            this.policeHoldMsg = "This vehicle is currently under police hold and cannot be released until " + _impound.Account.Company + " removes the Police Hold.";
        }

        if (Global.CurrentUser.Type == Extric.Towbook.User.TypeEnum.AccountUser && (
            Global.CurrentUser.CompanyId == 1042 ||
            Global.CurrentUser.CompanyId == 534 ||
            Global.CurrentUser.CompanyId == 536 ||
            Global.CurrentUser.CompanyId == 2416 ||
            Global.CurrentUser.CompanyId == 4649
        ))
            hidePricing = true;

        if (hideChargesFromAccountUsers && Global.CurrentUser.Type == Extric.Towbook.User.TypeEnum.AccountUser)
            hidePricing = true;

        var shouldBlock = Extric.Towbook.API.Models.Calls.CallModelExtensions.ShouldBlockCharges(
            new Extric.Towbook.Dispatch.CallModels.CallModel()
            {
                Id = _impound.DispatchEntry.Id,
                CompanyId = _impound.Company.Id
            });

        if (shouldBlock)
            hidePricing = true;

        if (_impound.ReleaseDate != null)
        {
            try
            {
                _releaseDetails = Extric.Towbook.Impounds.ReleaseDetails.GetByImpound(_impound);
            }
            catch (Extric.Towbook.TowbookException)
            {
                throw;
            }
        }

        _de = _impound.DispatchEntry;
        _contactsJson = Extric.Towbook.Dispatch.CallModels.CallModel.Map(_de).Contacts.ToJson();
        _isWithinClosedPeriod = Extric.Towbook.Company.Accounting.ClosedPeriodExtensions.IsWithinClosedAccountingPeriod(_impound);

        rpHistory.DataSource = _impound.History;
        rpHistory.DataBind();

        rpNotes.DataSource = _impound.Notes.OrderByDescending(o => o.Id);
        rpNotes.DataBind();

        rpInvoiceItems.DataSource = _impound.InvoiceItems.Where(o => o.Total != 0);
        rpInvoiceItems.DataBind();

        if (rpHistory.Items.Count == 0)
        {
            rpHistory.Visible = false;
        }

        if (rpNotes.Items.Count == 0)
        {
            rpNotes.Visible = false;
        }

        this.Master.InnerTitle += " for " + (String.IsNullOrEmpty(GetInvoiceNumber()) ? "#" + _impound.Id.ToString() : GetInvoiceNumber()) + " (" +
        (_de.Year > 0 ? _de.Year.ToString() : "") + " " +
            (_de.MakeModelFormatted) + (_de.Color != null ? " (" + _de.Color.Name + ")" : "") + ")";

        if (_impound.ReleaseDate != null)
        {
            this.Master.InnerTitle += " (RELEASED)";
        }


        if (Global.CurrentUser.Type == Extric.Towbook.User.TypeEnum.AccountUser)
        {
            if (Global.CurrentUser.CompanyId == 1042)
            {
                rpNotes.Visible = false;
                rpHistory.Visible = false;
            }
        }

        if (Global.CurrentUser.Company.HasFeature(Extric.Towbook.Generated.Features.Impounds_ImpoundLetters))
        {
            var tlist = LetterTemplate.GetByCompany(Global.CurrentUser.Company).ToList();

            if (Global.CurrentUser.Company.HasFeature(Extric.Towbook.Generated.Features.Stickering))
            {
                // if impound was created from a sticker, add default tow authorization letter
                var ltsticker = Extric.Towbook.Stickering.Sticker.GetByDispatchEntryId(_impound.DispatchEntry.Id);
                if (ltsticker != null)
                {
                    tlist.Add(LetterTemplate.GetById(2372));
                }
            }

            if (tlist.Count > 0)
            {
                _LetterTemplateJson = tlist.Select(s =>
                {
                    var isPdf = System.IO.File.Exists(Server.MapPath("../storage/forms/" + s.Id + ".pdf"));

                    return new
                    {
                        Id = s.Id,
                        Title = s.Title,
                        Url = "LetterTemplate.aspx?id=" + s.Id + "&impoundId=" + _id,
                        isPdf = isPdf,
                        No10EnvelopeFormatted = s.No10EnvelopeFormatted
                    };
                }).ToJson();
            }
        }

        int statusCode = 0;
        _tasksJson = WebGlobal.GetResponseFromUrl("/api/impounds/" + _id + "/tasks", out statusCode);

        _callPhotosJson = GetCallPhotos(_impound);
        _callModelJson = WebGlobal.GetResponseFromUrl("/api/calls/" + _impound.DispatchEntry.Id.ToString(), out statusCode);

        if (_impound.Company.HasFeature(Extric.Towbook.Generated.Features.Impounds_ImpoundAuctions))
        {
            _auctionsJson = WebGlobal.GetResponseFromUrl("/api/auctions/", out statusCode);
        }
    }


    decimal _grandTotal = 0;
    decimal _subTotal = 0;
    decimal _tax = 0;
    decimal _taxableAmount = 0;


    protected void rpInvoiceItems_ItemDataBound(object sender, RepeaterItemEventArgs e)
    {
        if (e.Item.ItemType == ListItemType.Item ||
            e.Item.ItemType == ListItemType.AlternatingItem)
        {
            Literal name = (Literal)e.Item.FindControl("name");
            Literal quantity = (Literal)e.Item.FindControl("quantity");
            Literal unitPrice = (Literal)e.Item.FindControl("unitprice");
            Literal lineTotal = (Literal)e.Item.FindControl("linetotal");

            decimal cost = 0;

            var i = (InvoiceItem)e.Item.DataItem;

            if (i.RateItem != null && string.IsNullOrWhiteSpace(i.CustomName))
            {
                name.Text = _(i.RateItem.Name);
            }
            else
            {
                name.Text = _(i.CustomName);
            }

            quantity.Text = i.Quantity.ToString();

            if (i.CustomPrice != null)
            {
                unitPrice.Text = string.Format("{0:C}", i.CustomPrice);
                cost = i.CustomPrice.Value;
            }
            else
            {
                if (i.RateItem != null)
                {
                    decimal myCost = 0;

                    if (_de.BodyType != null && i.RateItem.ExtendedRateItems.ContainsKey(_de.BodyType.Id))
                    {
                        myCost = i.RateItem.ExtendedRateItems[_de.BodyType.Id].Amount;
                    }
                    else
                        myCost = i.RateItem.Cost;

                    unitPrice.Text = string.Format("{0:C}", myCost);
                    cost = myCost;
                }
            }

            decimal tmpMiles = i.Quantity;

            if (i.RateItem != null)
            {
                if (i.RateItem.FreeQuantity > 0 && cost < 0)
                {
                    string unit = "";
                    if (i.RateItem.Predefined != null &&
                        (i.RateItem.Predefined.Id == Extric.Towbook.PredefinedRateItem.BUILTIN_MILEAGE_LOADED ||
                        i.RateItem.Predefined.Id == Extric.Towbook.PredefinedRateItem.BUILTIN_MILEAGE_UNLOADED))
                        unit = " " + _de.Company.LocaleMile + "s";

                    name.Text += " -- Credit for " + i.Quantity + unit;

                }
            }

            lineTotal.Text = String.Format("{0:C}", cost * tmpMiles);
            _subTotal += (cost * tmpMiles);

            if ((i.RateItem != null && i.RateItem.Taxable) || i.Taxable)
            {
                _taxableAmount += cost * i.Quantity;
            }

            _grandTotal = _subTotal + _tax;
        }
    }

    public static string GetCallPhotos(Extric.Towbook.Impounds.Impound impound)
    {
        List<Extric.Towbook.API.Models.Auctions.AuctionPhotoModel> images = new List<Extric.Towbook.API.Models.Auctions.AuctionPhotoModel>();
        var impoundPhotoList = Extric.Towbook.Impounds.Photo.GetByImpoundId((impound != null ? impound.Id : 0));
        var callPhotoList = Photo.GetByDispatchEntryId(impound.DispatchEntry.Id);

        foreach (Photo p in callPhotoList)
        {
            var url = Extric.Towbook.Storage.FileUtility.GetPresignedUrlForDownloadFromClient(p.Location.Replace("%1", impound.Company.Id.ToString()), p.ContentType, 30);
            images.Add(new Extric.Towbook.API.Models.Auctions.AuctionPhotoModel()
            {
                Id = p.Id,
                Description = p.Description,
                Url = url
            });
        }

        return images.ToJson();
    }
}