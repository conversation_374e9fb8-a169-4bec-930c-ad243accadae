<%@ Page Language="C#" AutoEventWireup="true" CodeFile="ImpoundPrint.aspx.cs" Inherits="Impounds_ImpoundPrint" %>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">

<html xmlns="http://www.w3.org/1999/xhtml" >
<head id="Head1" runat="server">
    <title>Computer Generated Invoice</title>
    <link rel="stylesheet" href="/ui/css/towbook.css" />
    <style type="text/css">
    body { font-family: arial; font-size: 12px; color: black; background-image: none; background-color: white}
    .RemoveBorders * { border: none }
    #ready {
        font-size: 14px;
    }
    a.flat-button {
        border: none;
        background-image: none;
        padding-left: 10px;
        padding-right: 10px;
        padding-top: 8px;
        padding-bottom: 8px;
        border-radius: 0;
        background-color: transparent;
        color: #0072c6;
        text-decoration: none;
    }
    a.flat-button:hover {
        text-decoration: underline;
    }
    </style>    
    <script src="//ajax.aspnetcdn.com/ajax/jQuery/jquery-1.7.min.js" type="text/javascript"></script>
    <script type="text/javascript" src="../UI/js/Towbook.js"></script>
</head>
<body >
    <form id="form1" runat="server">
    <div style="background-color: white; padding: 10px">
      <% if(Request.QueryString["_"] != "ajax") { %>
      <div style="font-weight: bold; font-size: 16px; border-bottom: dotted 1px #333333; font-family: Arial; padding-bottom: 10px; margin-bottom: 10px">
        Impounds: Print Invoice
      </div>
      <% } %>
      <div style="line-height: 160%; margin-left: 10px">
        <div style="margin-top: 15px; margin-bottom: 10px;" class="RemoveBorders">
          <iframe id="ifWorkspace" name="ifWorkspace" src="<% if(_imp.Lot != null && _imp.Lot.AccountId != 1) { %>/dispatch2/invoice.aspx?id=<%=_imp.DispatchEntry.Id %><% } else { %>
Invoice.aspx?id=<% =Convert.ToInt32(Request.QueryString["id"]) %><% } %>" onload="document.getElementById('loading').style.display='none'; document.getElementById('ready').style.display='inline'" style="width: 1px; height: 1px"></iframe>
    
          <span id="loading">Please wait while the invoice is generated...</span>
          <div id="ready" style="display: none">
            Your invoice is ready to print! Click Print Invoice below to print it.<br /><br />
       
<% if (Global.CurrentUser.Type != Extric.Towbook.User.TypeEnum.AccountUser) { %>
            <label><input type="checkbox" id="hideChargesOpt" <%= (HideCharges && (!(new int[] { 4654, 3643, 9866, 6200, 1235, 1873, 4552, 7394, 1917, 4082, 3853, 9083, 543, 8450, 3645, 6686, 5783, 1499, 8781, 2064 }.Contains(CompanyId))) ? "checked='checked'" : "") %> />Hide charges when printing or viewing the invoice</label><br />
            <label><input type="checkbox" id="hideDiscountsOpt" <%= HideDiscounts ? "checked='checked'" : "" %>/>Hide discounts when printing or viewing the invoice</label><br />

    <% 
    // check for admin option to supress invoice items on impound invoices
    if ((Extric.Towbook.Integration.CompanyKeyValue.GetFirstValueOrNull(_imp.Company.Id,
        Extric.Towbook.Integration.Provider.Towbook.ProviderId, "AllowSuppressingOfLineItemsOnImpoundInvoices") ?? "0") == "1")
    { %>
        <label><input type="checkbox" id="hideLineItemsOpt" />Hide individual line items from the invoice</label><br />
    <% } %>

        <br/>
<% } %> 
              
                   
            <% if(_imp.Lot != null && _imp.Lot.AccountId != 1) { %>
            - <a target="_blank" onclick="viewReceipt(this, <%=_imp.DispatchEntry.Id %>, 'dispatch2', event)" href="#" class="flat-button">Click here to view the invoice in your browser instead of printing it</a> (opens in new window)<br />
            <% } else { %>
            - <a target="_blank" onclick="viewReceipt(this, <%=Convert.ToInt32(Request.QueryString["id"])%>, 'impounds', event, )" href="#" class="flat-button">Click here to view the invoice in your browser instead of printing it</a> (opens in new window)<br />
            <% } %>
<% if (Global.CurrentUser.Type != Extric.Towbook.User.TypeEnum.AccountUser && Request.QueryString["_"] != "ajax") { %>
            - <a target="_top" href="Editor.aspx?id=<% =Convert.ToInt32(Request.QueryString["id"]) %>&pdf=1" class="flat-button">Click here if you need to make corrections to the details of this impound</a> (opens in current window)
<% } %>
          </div>
        </div>
      </div>
      <div style="position: absolute; bottom: 0; left: 0; right: 0; text-align: right; padding-right: 5px; padding-bottom: 10px; padding-top: 10px; background-color: #333333; border: solid 1px white">
        <input type="button" id="x-print-page" value="Print Invoice" onclick="PrintThisPage()" class="sm" />    
        <input type="button" id="x-close-page" value="Close" class="sm" onclick="closeThisPage()" />    
      </div>
    </div>
  </form>
  <script type="text/javascript">
      var isImpound = <%=(_imp.Lot != null && _imp.Lot.AccountId != 1) ? "false" : "true"%>;

      function viewReceipt(obj, id, controller, event) {
          hideLineItems = $('#hideLineItemsOpt').is(':checked')
          var url = getReceiptUrl(id, controller, false, hideLineItems);
          $(obj).attr('href', url);
          event.preventDefault();
          redirect(url);
      }

      function getReceiptUrl(id, controller, noPdf, hideLineItems) {
          var showPrices = $('#hideChargesOpt').is(':checked') ? '0' : '1';
          var hideDiscounts = $('#hideDiscountsOpt').is(':checked') ? '1' : '0';
          var hideLineItems = hideLineItems || $('#hideLineItemsOpt').is(':checked') ? '1' : '0';
          var url = '/' + controller + '/Invoice.aspx?id=' + id + '&showPrices=' + showPrices + '&hideDiscounts=' + hideDiscounts + (noPdf === true ? '&pdf=1' : '') + (hideLineItems === "1" ? "&hidelineitems=1" : "");

          return url;
      }

      function redirect(url) {
          var a = document.createElement('a');
          a.href = url;
          a.target = '_blank';
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
      }

      $('#hideChargesOpt, #hideLineItemsOpt').change(function (e) {
          var showPrices = $('#hideChargesOpt').is(':checked') ? '0' : '1';
          var hideDiscounts = $('#hideDiscountsOpt').is(':checked') ? '1' : '0';
          var hideLineItems = $('#hideLineItemsOpt').is(':checked') ? '1' : '0';
          var id = <%=(_imp.Lot != null && _imp.Lot.AccountId != 1) ? _imp.DispatchEntry.Id : Convert.ToInt32(Request.QueryString["id"])%>;
          var url = getReceiptUrl(id, isImpound ? 'impounds' : 'dispatch2', hideLineItems) ;

          $('#ifWorkspace').prop('src', url)
      })

      function closeThisPage() {
          <%if (Request.QueryString["reloadOnClose"] == "true") { %>

          console.log("AC: reloading now!")
          $('#x-print-page').hide();
          $('#x-close-page').prop('disabled', true);
          $('#x-close-page').prop('value', 'Loading...');

          parent.location.reload(true);

          <% } else { %>

          parent.closeLightbox();

          <% } %>
      }

      function PrintThisPage() {
          window.frames['ifWorkspace'].focus();
          window.frames['ifWorkspace'].print();
          return;

      }
  </script>
</body>
</html>
