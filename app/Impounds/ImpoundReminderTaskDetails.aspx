<%@ Page Language="C#" MasterPageFile="~/UI/TowbookV2.master" AutoEventWireup="true"
    CodeFile="ImpoundReminderTaskDetails.aspx.cs" Inherits="Impounds_ImpoundReminderTaskDetails" Title="Towbook - Impound Reminders" %>

<%@ MasterType TypeName="UI_Towbook" %>
<asp:Content ID="Content1" ContentPlaceHolderID="Toolbar" runat="Server">
    <script src="/ui/js/jquery.tmpl.min.js"></script>
    <% Response.WriteFile("ImpoundTasksDetails.tpl.html"); %>
    <script type="text/javascript">

        var OptionData = new function () {
            this.ReminderId = 0;
            this.Duetype = "";
            this.OnlyCurentUser = false;

            this.GetValuesFromQueryString = function () {
                var values = location.search.substr(1).split("&")

                for (var i = 0; i < values.length; i++) {
                    


                    if (values[i] === "") return;
                    
                    var queryStringValues = values[i].split("=");

                    console.log(queryStringValues[0] + "=" + queryStringValues[1]);


                    switch (queryStringValues[0]) {
                        case "reminderId":
                            this.ReminderId = parseInt(queryStringValues[1]);
                            break;
                        case "due":
                            this.Duetype = queryStringValues[1];
                            break;
                        case "onlyForCurrentUser":
                            this.OnlyCurentUser = (queryStringValues[1] === "true") ? true : false;
                            break;
                    }


                }

                console.log(this);
            };
        }

        $(document).ready(function () {
            Refresh();
        });

        function Refresh() {
            OptionData.GetValuesFromQueryString();

            $.ajax({
                url: "/api/ImpoundReminders",
                dataType: 'json',
                data: { reminderId: OptionData.ReminderId, due: OptionData.Duetype, onlyForCurrentUser: OptionData.OnlyCurentUser },
                contentType: "application/json; charset=utf-8",
                statusCode: {
                    200: function (response) {

                        $('#dueType').html(response.due + ' tasks');
                        $('#reminderTitle').html('Reminder title: ' + response.reminderTitle);

                        towbook.compileTemplate('impoundTasksDetailsRow', $('#t-impoundTasksDetailsRow'));

                        var target = $('#ImpoundTaskReminders');
                        target.html("");

                        $.each(response.data, function (index, item) {
                            target.append(towbook.applyTemplate('impoundTasksDetailsRow', item));
                        });
                    }
                }
            });
        };

        $(".impoundTaskItem").live('click', function (e) {
            var impoundId = $(this).find('.impoundIdValue').html();
            location.href = "/Impounds/Impound.aspx?id=" + impoundId;
        });
    </script>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="content" runat="Server">
    <div style="margin-top: 5px; margin-bottom: 5px;">
        <h3 id="dueType" style="font-size:1.2em;"></h3>
        <h4 id="reminderTitle"></h4>
        <div id="ImpoundTaskReminders"></div>
    </div>
</asp:Content>
