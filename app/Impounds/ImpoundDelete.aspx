<%@ Page Language="C#" AutoEventWireup="true" Inherits="Impounds_ImpoundDelete" CodeFile="ImpoundDelete.aspx.cs"  Async="true" %>
<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml" >
<head id="Head1" runat="server">
    <title>Impounds - Delete</title>
    <link rel="stylesheet" href="/ui/css/towbook.css" />
    <style>
    body { font-family: arial; font-size: 12px; color: black; background-image: none; background-color: white}
    .RemoveBorders * { border: none }
    </style>

    <script src="//ajax.aspnetcdn.com/ajax/jQuery/jquery-1.7.min.js" type="text/javascript"></script>    
    <script src="//ajax.googleapis.com/ajax/libs/jqueryui/1.8.14/jquery-ui.min.js" type="text/javascript"></script>
</head>
<body>
    <form id="form1" runat="server" target="_top">
    <div style="background-color: white; padding: 10px">
    <div style="font-weight: bold; font-size: 16px; border-bottom: dotted 1px #333333; font-family: Arial; padding-bottom: 10px; margin-bottom: 10px">
    Impound: Delete from Towbook 
    
    </div>
<div style="line-height: 160%; margin-left: 10px">

    <div style="margin-top: 15px; margin-bottom: 10px;" class="RemoveBorders">
    <asp:Label runat="server" ID="lblError" style="color: Red; font-weight: bold" />
   <% if (_allowDelete) { %>   
        <asp:Checkbox ID="cbDelete" runat="server" Font-Size="14px" Text="Yes, permanently delete this impounded vehicle from Towbook" /><br /><br /><br /><br />
        
        <strong>Warning: This action cannot be undone. By clicking Delete below, you will remove all information related to this impound from the system. This is not the same as releasing a vehicle.</strong>
    <% } else { %>
        <strong>This impound is inside of your company's closed accounting period and cannot be deleted based on those settings.</strong>

    <% }%>
    </div>

</div>
</div>

  <div style="position: absolute; bottom: 0; left: 0; right: 0; text-align: right; padding-right: 5px; padding-bottom: 10px; padding-top: 10px; background-color: #333333; border: solid 1px white">
  <% if (_allowDelete) { %> <asp:Button ID="Button1" runat="server" Text="Delete" CssClass="sm" OnClick="Button1_Click" />   <% }%>
  <input type="button" id="Button2" value="Cancel" class="sm" onclick="parent.closeLightbox()" />    
  </div>    
</form>

<script>
    $(function () {
        $('#Button1').on('click', function () {
            console.log("AC: validating");

            if (!$('#cbDelete').is(':checked')) {
                $('#lblError').html("Error: To delete the vehicle, you must check the Yes box below<br /><br />").show();
                console.log("AC: validation was false");
                return false;
            }
        });
    });
</script>
</body>
</html>