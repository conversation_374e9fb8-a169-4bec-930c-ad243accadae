using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class Impounds_GenerateReport : System.Web.UI.Page
{
	protected void Page_Load(object sender, EventArgs e)
	{
        
		if (IsPostBack)
		{
            Response.Redirect(String.Format("Report.aspx?accountId={0}&showImpounded={1}&showReleased={2}&daysPast={3}&impoundLot={4}&start={5}&stop={6}&releaseDateStart={7}&releaseDateStop={8}",
                dlAccount.SelectedValue.ToString(), Convert.ToInt32(cbShowImpounded.Checked), Convert.ToInt32(cbShowReleased.Checked),
                txtImpoundedAtLeast.Text.Trim(), dlImpoundLot.SelectedValue,
                 Request.Form["_eo_ctl00_Content_dpStart_h"],
                Request.Form["_eo_ctl00_Content_dpEnd_h"],
                Request.Form["_eo_ctl00_Content_dpReleaseStart_h"],
                Request.Form["_eo_ctl00_Content_dpReleaseEnd_h"]));
			return;
		}
		else
		{
            dpStart.PopupExpandDirection = EO.Web.ExpandDirection.Top;
            dpEnd.PopupExpandDirection = EO.Web.ExpandDirection.Top;
			dlAccount.DataSource = Extric.Towbook.Accounts.Account.GetByCompany(Global.CurrentUser.Company); //,				Extric.Towbook.Accounts.AccountType.PoliceDepartment);
			dlAccount.DataValueField = "Id";
			dlAccount.DataTextField = "Company";
			dlAccount.DataBind();
            dlAccount.Items.Insert(0, new ListItem("(All Accounts)", "0"));

            dlImpoundLot.DataSource = Extric.Towbook.Impounds.Lot.GetByCompany(Global.CurrentUser.Company, true);
            dlImpoundLot.DataValueField = "Id";
            dlImpoundLot.DataTextField = "Name";
            dlImpoundLot.DataBind();
            dlImpoundLot.Items.Insert(0, new ListItem("(All Impound/Storage Lots)", "0"));
		}

	}
}
