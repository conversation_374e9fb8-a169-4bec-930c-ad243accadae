using System;
using Extric.Towbook.Impounds;

public partial class Impounds_Note : System.Web.UI.Page
{
	protected int _impoundId;
	
	protected void Page_Load(object sender, EventArgs e)
	{
		if (Request.QueryString["id"] != null)
		{
			_impoundId = Convert.ToInt32(Request.QueryString["id"]);
		}

        var impound = Impound.GetById(_impoundId);

        if (impound == null || !Global.CurrentUser.HasAccessToCompany(impound.Company.Id))
        {
            Response.Write("Invalid ID");
            Response.End();
        }

    }
	protected void btnSave_Click(object sender, EventArgs e)
	{
		Note n = new Note();
		n.ImpoundId = _impoundId;
		n.User = Global.CurrentUser;
		n.Content = txtDescription.Text;

		n.Save();

		Impound.ClearCacheById(_impoundId);

		Response.Redirect("Impound.aspx?id=" + _impoundId.ToString());
		return;
	}
}
