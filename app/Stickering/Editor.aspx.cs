using Extric.Towbook.Company;
using Extric.Towbook.Utility;
using Extric.Towbook.WebShared;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class Stickering_Editor : System.Web.UI.Page
{
    public string _accountsJson;
    public int _stickerCompanyId;
    public string _vehicleMakesJson;
    public string _vehicleColorsJson;
    public string _statesJson;
    public string _parkingPermitListTypesJson;

    public Extric.Towbook.Company.Company[] Companies
    {
        get
        {
            var x = Extric.Towbook.Company.CompanyUser.GetByUserId(Global.CurrentUser.Id);

            if (x == null || x.Length == 0)
            {
                return new Extric.Towbook.Company.Company[] { Global.CurrentUser.Company };
            }

            return x.Select(o => Extric.Towbook.Company.Company.GetById(o.CompanyId)).ToArray();
        }
    }

    protected void Page_Load(object sender, EventArgs e)
    {
        if (Request.QueryString["__companyId"] != null)
            _stickerCompanyId = Convert.ToInt32(Request.QueryString["__companyId"]);
        else
            _stickerCompanyId = WebGlobal.CurrentUser.CompanyId;

        if (WebGlobal.CurrentUser.Type == Extric.Towbook.User.TypeEnum.AccountUser)
        {
            if (WebGlobal.CurrentUser.AccountId > 0)
            {
                _accountsJson = WebGlobal.GetResponseFromUrl("/api/stickering/accounts/" + WebGlobal.CurrentUser.AccountId);
            }
        }
        else
        {
            _accountsJson = WebGlobal.GetResponseFromUrl("/api/stickering/accounts");
        }

        _parkingPermitListTypesJson = WebGlobal.GetResponseFromUrl("/api/parkingPermitLists");

        if (Request.QueryString["_"] == null)
        {
            Collection <Extric.Towbook.API.Models.StateConfig> states = new Collection<Extric.Towbook.API.Models.StateConfig>();

            if (WebGlobal.CurrentUser.Company.Country == Company.CompanyCountry.USA)
                states = states.Union(Extric.Towbook.API.Models.States.StatesUSA).ToCollection();
            else if (WebGlobal.CurrentUser.Company.Country == Company.CompanyCountry.Canada)
                states = states.Union(Extric.Towbook.API.Models.States.StatesCanada).ToCollection();
            else if (WebGlobal.CurrentUser.Company.Country == Company.CompanyCountry.Australia)
                states = states.Union(Extric.Towbook.API.Models.States.StatesAustralia).ToCollection();

            if (WebGlobal.CurrentUser.Company.HasFeature(Extric.Towbook.Generated.Features.Options_ForceIncludeUsaStates))
                states = states.Union(Extric.Towbook.API.Models.States.StatesUSA).Distinct().ToCollection();

            if (WebGlobal.CurrentUser.Company.HasFeature(Extric.Towbook.Generated.Features.Options_ForceIncludeCanadaProvinces))
                states = states.Union(Extric.Towbook.API.Models.States.StatesCanada).Distinct().ToCollection();

            _statesJson = states.ToJson();

            _vehicleMakesJson = WebGlobal.GetResponseFromUrl("/api/vehiclemakes");
            _vehicleColorsJson = WebGlobal.GetResponseFromUrl("/api/colors");
        }
    }
}