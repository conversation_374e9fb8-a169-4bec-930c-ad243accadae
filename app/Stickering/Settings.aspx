<%@ Page Language="C#" AutoEventWireup="true" Inherits="Stickering_Settings" CodeFile="Settings.aspx.cs" %>

<!DOCTYPE html>
<!--[if IE 8]>         <html class="ie8"> <![endif]-->
<!--[if gt IE 8]><!--> <html" >         <!--<![endif]-->
<head runat="server">
    <title>Sticker Settings</title>

    <script type="text/javascript" src="Settings.js"></script>

    <style>
        .stickerSettings .invoiceItems .description-heading {  border-left: solid 0px red }

        .stickerSettings .invoiceItems { margin-top: 20px }
        .stickerSettings .invoiceItems .header td {
	        padding: 0
        }

        .stickerSettings .invoiceItems .row-headings td {
            border-top: solid 1px #efefef; 
            border-bottom: solid 1px #efefef; 
            border-right: none;
            background-color: white; 
            padding: 10px;
        }

        .stickerSettings .invoiceItems .row-headings td:last-of-type {
	        border-right: none
        }

        .stickerSettings .invoiceItems .row-headings td.price-heading,
        .stickerSettings .invoiceItems .row-headings td.quantity-heading {
	        width: 75px
        }
        .stickerSettings .invoiceItems .row-headings td.total-heading {
	        width: 100px
        }

        .stickerSettings .invoiceItems tbody td, .stickerSettings .invoiceItems tfoot td {
	        padding: 2px
        }

        .x-item-price,
        .x-item-quantity {
	        text-align: center;
        }


        .stickerSettings .invoiceItems tbody td .x-item-total {
            display: inline-block;
            vertical-align: middle;
            height: 100%;
            padding: 7px;
            padding-right: 1px;
            width: 77px;
            text-align:right
        }

        .stickerSettings .invoiceItems tbody td .x-predefined-name,
        .stickerSettings .invoiceItems tfoot td .x-fuel-surcharge-name {
            display: inline-block;
            vertical-align: middle;
            height: 100%;
            padding: 7px
        }

        .stickerSettings .invoiceItems tbody td .x-item-tax {
            display: inline-block;
            vertical-align: middle;
            width: 10px;
            padding-right: 10px;
            padding-top: 7px; 
            padding-bottom: 7px
        }
        
        .stickerSettings .invoiceItems tfoot .right {
            padding: 10px;
            text-align:right
        }

        .stickerSettings .invoiceItems tfoot .number {
            padding-right: 24px
        }
        
        .stickerSettings .invoiceItems tfoot .discount {
            padding: 2px 2px 0 2px;
        }

        .stickerSettings .invoiceItems tfoot .left {
            text-align: left;
            padding: 10px
        }

    </style>
</head>
<body>
    <div id="stickerSettings" class="stickerSettings">
        <fieldset>
            <dl>
                <dt><label for="defaultExpTime">Default Exp. Time (in hours):</label></dt>
                <dd><input type="text" id="defaultExpTime" class="validate-number required" title="blank for none..." style="width: 300px" /></dd>
            </dl>
            <dl>
                <dt><label for="">Exp. Time per reason:</label></dt>
                <dd>
                    <table id="stickerReasons" class="invoiceItems">
                        <tbody>
                            <tr class="header" data-reason-id="0">
                                <td colspan="2">
                                </td>
                                <td class="quantity-heading">
                            </tr>
                        </tbody>
                    </table>
                </dd>
            </dl>
        </fieldset>
        <ul class="formNavigation">
            <li><input type="submit" value="Save Changes" class="button" id="save" /></li>
            <li><a href="#" class="button">Cancel</a></li>
        </ul>
    </div>

    <script type="text/x-jQuery-tmpl" id="t-stickerReason">
        <tr class="invoice-item" data-reason-id="${stickerReasonId}">
            <td><select id="stickerReason-${stickerReasonId}" data-reason-id="${stickerReasonId}" class="x-item-name" title="click here and choose an item from the list..." data-width="100%"></select></td>
            <td><input type="text" id="expTime-${stickerReasonId}" data-reason-id="${stickerReasonId}" class="x-item-quantity validate-number" maxlength="6" style="width:60px;" /></td>
        </tr>
    </script>
</body>
</html>
