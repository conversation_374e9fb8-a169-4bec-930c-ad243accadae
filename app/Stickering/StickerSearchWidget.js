function StickerSearchWidget(options) {
    afterLoad();

    /*<Member fields definition>*/
    var Me = this;  //An alias for "this", useful for closures

    this.options = $.extend({
        element: null,  //Mandatory, jquery selector or dom element
        searchSuccess: function (searchRepository) { }
    }, options);

    //Local public fields    
    $.extend(this, {
        xhr: null,
        $tabControl: null //Will get value on init()
    });
    /*</Member fields definition>*/

    (function init() {
        
        Me.$widget = $(Me.options.element);
        Me.$widget.data("StickerSearchWidgetInstance", Me);
        Me.$widget.tbkBehav();

        Me.$widget.find("#gsacc")
          .combobox()
          .appendOptions(towbook.accounts, true, true, "accountId", "company", "Select an account");

        Me.$widget.find("#gsreason")
            .combobox()
            .appendOptions(towbook.views.stickering.reasons, true, true, "id", "name", "Select a reason"); 

        console.debug(Me.$widget);
        Me.$widget.find("#gsmake").combobox({
            allowMissing: true,
            title: 'name',
            selected: function () {
                if (typeof this.targetDomEl == 'undefined') {
                    var target = this;

                } else {
                    var target = Me.$widget.targetDomEl;
                }

                var thisRow = $(target).closest('tr');

                thisRow.find('#gsmodel').empty();
                thisRow.find('#gsmodel').combobox('autocomplete', 0);

                var makeId = thisRow.find('#gsmake').val();

                if (makeId == 0) {
                    return;
                }

                var processModels = function (data) {
                    if (data != null) {
                        thisRow.find('#gsmodel').appendOptions(data, null, null, "id", "model", "(none)");
                    }
                };

                if (towbook._cache.vehicle.models[makeId] == null) {
                    $.ajax({
                        url: "/api/vehicleModels",
                        data: { makeId: thisRow.find('#gsmake').data('id') }
                    }).done(function (data) {
                        if (typeof xdata != 'undefined') {
                            thisRow.find('#gsmake').setVal(xdata.model);
                        }
                        towbook._cache.vehicle.models[makeId] = data;
                        processModels(data);
                    });
                }
                else {
                    processModels(towbook._cache.vehicle.models[makeId]);
                }
            }
        }).appendOptions(towbook.vehicle.makes, true, true, null, null, "(none)");

        Me.$widget.find("#gsmodel").combobox({ allowMissing: true });

        var year = (new Date).getFullYear() + 1;
        Me.$widget.find('#gsyear').combobox({
            allowMissing: true,
            numbersOnly: true,
        }).on("keydown", function () {
            log("AC: keypress function wow!");
            CheckKeyCode(this, event, false, false);
        }).append('<option value=0>none</option>\r\n');
        for (var i = year; i > year - 100; i--) {
            Me.$widget.find('#gsyear').append('<option value="' + i + '">' + i + '</option>\r\n');
        }

        initDefaultTitles();

        (function () {
            $('#getResults', Me.$widget).click(function () {
                $('body').removeClass('quick-search').addClass('enable-search').addClass('specific-search');
                Me.$widget.find(":text").applyTrim();

                var value = null;
                var q = new Object();

                value = $('#gsplate').getVal();
               
                q["plate"] = value;

                value = $('#gsreason').getVal();
                if (value && value != "0")
                    q["reasonId"] = parseInt(value);

                value = $('#gsacc').getVal();
                if (value && value != "0")
                  q["accountId"] = parseInt(value);

                value = $('#gsyear').combobox('get').customText;
                if (value != "")
                    q["year"] = value;
                else
                    q["year"] = 0;

                value = $('#gsmake').combobox('get').customText;
                if (value != "")
                    q["make"] = value;

                value = $('#gsmodel').combobox('get').customText;
                if (value != "")
                    q["model"] = value;

                value = $('#gsvin').val();
                if (value != "")
                    q["vin"] = value;

                value = $('#gsstart').val();
                if (value != "")
                    q["startDate"] = value;

                value = $('#gsend').val();
                if (value != "")
                    q["endDate"] = value;

                console.log("AC: q is", q);

                if (!q["plate"] &&
                    !q["reasonId"] &&
                    !q["accountId"] &&
                    !q["year"] &&
                    !q["make"] &&
                    !q["model"] &&
                    !q["vin"] &&
                    !q["startDate"] &&
                    !q["endDate"]) {
                    alert('Please type a value to search for into at least one of the search boxes.');
                    initDefaultTitles();
                    return;
                }

                $(this).attr('search-query', $.param(q, true));

                Me.searchEntries($.param(q, true));
            });

        })();
    })();

    /*<Private Methods>*/
    this.searchEntries = function (q) {
        if (Me.xhr) Me.xhr.abort();
        console.log("starting search..");
        console.log(q);
        return Me.xhr = $.ajax({
            url: Me.$widget.find("form").attr("action") + "?startAtId=0&w=1",
            type: "get",
            data: q,
            dataType: "json",
            cache: false,
            complete: function () {
                Me.$widget.unblock();
                console.log("Search ended.");
            },
            timeout: 20000,
            beforeSend: function () {
                Me.$widget.block({ message: "Searching..." });
            },
            error: function () {
                alert("There was an error while performing your search.\nPlease try again later");
            },
            success: function (searchRepository, status, xhr) {
                Me.options.searchSuccess(searchRepository);
                $('#a-search').closest('li').removeClass('selected');
            }
        });
    }
}