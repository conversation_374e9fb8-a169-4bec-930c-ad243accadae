<%@ Page Language="C#" AutoEventWireup="true" Inherits="Stickering_Extend" CodeFile="Extend.aspx.cs" %>


<% if (Request.QueryString["_"] == null) { %>
<!DOCTYPE html>
<head runat="server">
    <title>Extend Time</title>

    <script src="//ajax.googleapis.com/ajax/libs/jquery/1.7.2/jquery.min.js" type="text/javascript"></script>
	<script src="//ajax.googleapis.com/ajax/libs/jqueryui/1.8.14/jquery-ui.min.js" type="text/javascript"></script>
	<script src="/ui/js/towbook/towbook.js"></script>
    <script type="text/javascript" src="//use.typekit.com/edd6gei.js"></script>
    <script type="text/javascript">try { Typekit.load(); } catch (e) { }</script>
    <script type="text/javascript" src="https://cdn.jsdelivr.net/sweetalert2/4.2.7/sweetalert2.min.js"></script>
    
    <link rel="stylesheet" href="/UI/css/jquery-ui.css" />
	<link rel="stylesheet" href="/UI/css/theme/jquery-ui-1.8.21.custom.css" />
    <link rel="stylesheet" href="https://pro.fontawesome.com/releases/v5.1.0/css/all.css" integrity="sha384-87DrmpqHRiY8hPLIr7ByqhPIywuSsjuQAfMXAE0sMUpY3BM7nXjf+mLIUSvhDArs" crossorigin="anonymous">
    <link rel="stylesheet" href="/UI/css/towbook.css" />
    <link rel="stylesheet" href="/UI/css/application-forms.css" />
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/sweetalert2/4.2.7/sweetalert2.min.css"/>

    
    <script>
        $(function () {
            
    </script>
</head>
<body>
<% } %>    
    <div class="wrapper">
        <h3 id="pTitle">Extend Time</h3>
    
        <div class="Overview">Give extra time to a warning sticker. This will extend the time before the vehicle can be towed. Please note: <i>once an extension is given, it can't be undone</i>.</div>
        <table class="list">
            <tbody>
                <tr>
                    <td>
                        <label>Extend the warning time by</label>
                        <select id="hours" data-width="140"></select>
                    </td>
                </tr>
                <tr id="customHoursWrapper" style="display: none;">
                    <td>
                        <label>Enter the amount of hours</label><input id="customHours" type="text" class="validate-number" />
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
    
    <div class="navigation-row">
        <input type="button" class="standard-button" id="extendSticker" value="Extend"/>
        <input type="button" class="standard-button" id="discard" value="Close" onclick="towbook.views.stickering.closePopupWindow();" />
    </div>

    <style>
        .CellLeft { border-left: dotted 1px #afafaf }

        .details .clx { border: none } 
        .details .crx { border: none }

        .Overview {
            margin-left: 0;
            margin-right: 0;
            border: none;
        }

        .list label {
            position: relative;
            float: left;
            width: 280px;
            margin-right: 20px;
            top: 10px;
        }

        .list input[type="text"] {
            line-height: 32px;
            text-align: center;
        }

        .wrapper {
            padding: 10px;
        }
        
    </style>

    <script type="text/javascript">
        var options = [
            { id: 6, name: "6 hours" },
            { id: 12, name: "12 hours" },
            { id: 24, name: "24 hours" },
            { id: 48, name: "48 hours" },
            { id: 72, name: "72 hours" },
            { id: 0, name: "other" }];


        $(function () {
            $('#hours')
                .appendOptions(options, false, true, "id", "name", "none", null, true)
                .combobox({
                    allowmissing: false,
                    selected: function () {
                        if ($(this).val() == "0")
                            $('#customHoursWrapper').show();
                        else
                            $('#customHoursWrapper').hide();
                    }
                })


            $('#extendSticker').on('click', function () {
                var hours = parseInt($('#hours').val());
                if (hours == 0)
                    hours = parseInt($('#customHours').val());

                if (isNaN(hours) || hours < 1) {
                    swal({ title: "There was a problem", text: "Please check the number of hours entered. You must enter at least 1 hour.", type: "info" });
                    return;
                }


                $.ajax({
                    url: '/api/stickering/stickers/<% =Id %>/extend',
                    type: 'POST',
                    contentType: "application/json; charset=utf-8",
                    data: JSON.stringify({
                        id: '<% =Id %>',
                        hours: hours,
                    })
                }).done(function (data) {

                    towbook.views.stickering.closePopupWindow();
                }).error(function (xhr, status, error) {
                    alert("error: " + status + ", " + error);
                });
            });
        });
    </script>

</body>
