<%@ Page Language="C#" AutoEventWireup="true" Inherits="Stickering_Viewer" CodeFile="Viewer.aspx.cs" Async="true"   %>

<!DOCTYPE html>
<!--[if IE 8]>         <html class="ie8"> <![endif]-->
<!--[if gt IE 8]><!--> <html" >         <!--<![endif]-->
<head runat="server">
    <title>Sticker Viewer</title>
</head>
<body>
    <style>
        .CellLeft { border-left: dotted 1px #afafaf }

        .details .clx { border: none } 
        .details .crx { border: none } 

    </style>

    <h3 style="margin-top: 0">Details</h3>
    <div class="h3box" style="padding: 0; margin-bottom: 20px;">
        <table style="width: 100%; border-collapse: collapse" class="details">
            <tr>
                <td class="clx">Current Status</td>
                <td class="crx"><% = _sticker.StatusName %></td>
            </tr>
            <tr>
                <td class="clx">Sticker Number</td>
                <td class="crx"><% = _sticker.StickerNumber %></td>
            </tr>
            <% if (_sticker.CallNumber > 0) { %>
                <tr>
                    <td class="clx">Call Number</td>
                    <td class="crx"><% = _sticker.CallNumber %></td>
                </tr>
            <% } %>
            <tr style ="display: none;">
                <td class="clx">Sticker Custom Number</td>
                <td class="crx"><% = Extric.Towbook.Core.HtmlEncode(_sticker.CustomNumber) %></td>
            </tr>
            <tr>
                <td class="clx">Date Stickered</td>
                <td class="crx"><% = Global.OffsetDateTime(_sticker.CreateDate).ToLongDateString()%> <%=Global.OffsetDateTime(_sticker.CreateDate).ToShortTowbookTimeString() %>
            </tr>
            <% if (!string.IsNullOrEmpty(_sticker.Notes)) { %>
                <tr>
                    <td class="clx">Notes</td>
                    <td class="crx"><% = Extric.Towbook.Core.HtmlEncode(_sticker.Notes) %></td>
                </tr>
            <% } %>
            <% if (_sticker.ModelYear > 0 ||
                    !string.IsNullOrEmpty(_sticker.Make)) { %>
                <tr>
                    <td class="clx">Vehicle Description</td>
                    <td class="crx"><% = _sticker.ModelYear > 0 ? _sticker.ModelYear.ToString() : "" %> <% = !string.IsNullOrEmpty(_sticker.Make) ? Extric.Towbook.Core.HtmlEncode(_sticker.Make) : "" %> <% = !string.IsNullOrEmpty(_sticker.Model) ? Extric.Towbook.Core.HtmlEncode(_sticker.Model) : "" %></td>
                </tr>
            <% } %>
            <% if (!string.IsNullOrEmpty(_sticker.LicenseNumber)) { %>
                <tr>
                    <td class="clx">License Number</td>
                    <td class="crx"><% = Extric.Towbook.Core.HtmlEncode(_sticker.LicenseNumber) %></td>
                </tr>
            <% } %>
            <% if (!string.IsNullOrEmpty(_sticker.LicenseState)) { %>
                <tr>
                    <td class="clx">License State</td>
                    <td class="crx"><% = Extric.Towbook.Core.HtmlEncode(_sticker.LicenseState) %></td>
                </tr>
            <% } %>
            <% if (!string.IsNullOrEmpty(_sticker.VIN)) { %>
                <tr>
                    <td class="clx">VIN</td>
                    <td class="crx"><% = Extric.Towbook.Core.HtmlEncode(_sticker.VIN) %></td>
                </tr>
            <% } %>
            <% if (!string.IsNullOrEmpty(_sticker.PropertyGateCode)) { %>
                <tr>
                    <td class="clx">Gate Code</td>
                    <td class="crx"><% = Extric.Towbook.Core.HtmlEncode(_sticker.PropertyGateCode) %></td>
                </tr>
            <% } %>
        </table>
    </div>

    <h3 style="margin-top: 0">Reasons</h3>
    <div class="h3box" style="padding: 0; margin-bottom: 20px;">
        <table style="width:100%;">
        <tr style="font-weight: bold">
            <td style="width: 40%; padding-left: 10px">Name</td>
            <td style="width: 60%;">Description</td>
        </tr>
        <% foreach (var item in _stickerReasons) { %>  
            <tr>
                <td style="padding-left: 10px"><% = Extric.Towbook.Core.HtmlEncode(item.Name) %></td>
                <td style="padding-right: 10px"><% = Extric.Towbook.Core.HtmlEncode(item.Description) %></td>
            </tr>
        <% } %>  
        </table>
    </div>
    <h3 style="margin-top: 0">Status History</h3>
    <div class="h3box" style="padding: 0; margin-bottom: 20px;">
        <table style="width:100%;">
            <tr style="font-weight: bold">
                <td style="width: 40%; padding-left: 10px">Description</td>
                <td>User</td>
                <td style="width: 200px;">Date</td>
            </tr>
            <% foreach (var item in _stickerStatuses) { %>  
                <tr>
                    <td style="padding-left: 10px"><% =  Extric.Towbook.Core.HtmlEncode(item.Name) %></td>
                    <td style="padding-right: 10px"><% = Extric.Towbook.Core.HtmlEncode(item.UserName) %></td>
                    <td style="padding-right: 10px; width: 200px;"><% = Extric.Towbook.WebShared.WebGlobalExtensions.ToShortDate(Extric.Towbook.WebShared.WebGlobal.OffsetDateTime(Convert.ToDateTime(item.CreateDate)), Global.CurrentUser.Company) %></td>
                </tr>
            <% } %>  
        </table>
    </div>


    <% if (_stickerNotes.Count() > 0) { %>
        <h3 style="margin-top: 0">Quick Notes</h3>
        <div class="h3box" style="padding-bottom: 10px; margin-bottom: 70px">
            <% foreach (var item in _stickerNotes) { %>
                <div style="padding-left: 10px; padding-top: 10px"><strong> <% = Extric.Towbook.Core.HtmlEncode(item.UserFullName) %></strong> wrote at <% = Global.OffsetDateTime(item.CreateDate).ToShortTowbookTimeString() %> on <% = Global.OffsetDateTime(item.CreateDate).ToShortDateString() %>:<br />
                <div style="margin-left: 20px">
                    <% = Extric.Towbook.Core.HtmlEncode(item.Message) %>
                </div>
                </div>
            <% } %>
        </div>
    <% } %>

    <div class="navigation-row">
        <input type="button" class="standard-button" id="discard" value="Close" onclick="towbook.views.stickering.closePopupWindow();" />
    </div>
</body>
</html>
