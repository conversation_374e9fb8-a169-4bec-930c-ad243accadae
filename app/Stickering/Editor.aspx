<%@ Page Language="C#" AutoEventWireup="true" Inherits="Stickering_Editor" CodeFile="Editor.aspx.cs" %>
<%@ Import Namespace="Extric.Towbook.Utility" %>

<% if (Request.QueryString["_"] == null) { %>
<!DOCTYPE html>
<!--[if IE 8]><html class="ie8"> <![endif]-->
<!--[if gt IE 8]><!-->
<html><!--<![endif]-->
<head>
    <title>Sticker Editor</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1" />

	<script src="//ajax.googleapis.com/ajax/libs/jquery/1.7.2/jquery.min.js" type="text/javascript"></script>
	<script src="//ajax.googleapis.com/ajax/libs/jqueryui/1.8.14/jquery-ui.min.js" type="text/javascript"></script>
	<script src="/ui/js/jquery.tmpl.min.js"></script>
    <script src="/UI/js/jquery.tipTip.js" type="text/javascript"></script>
    <script src="/UI/js/jquery.tipsy.js" type="text/javascript"></script>
    <script src="//js.pusher.com/3.1/pusher.min.js"></script>
	<script src="/ui/js/towbook/towbook.js"></script>
    <script src="/ui/js/jquery.tmpl.min.js"></script>
    <script src="/ui/js/accounting.js"></script>

    <script type="text/javascript" src="https://maps.googleapis.com/maps/api/js?client=gme-extricllc&libraries=places"></script>
    <script src="/ui/js/jquery.timepicker.min.js"></script>
    <script src="/ui/js/timepicker/datepair.js"></script>
    <script type="text/javascript" src="//use.typekit.com/edd6gei.js"></script>
    <script type="text/javascript">try { Typekit.load(); } catch (e) { }</script>
    <script type="text/javascript" src="https://cdn.jsdelivr.net/sweetalert2/4.2.7/sweetalert2.min.js"></script>
    <script src="/UI/js/pusher/pusher-auth.js"></script>
    
    <link rel="stylesheet" href="/UI/css/jquery-ui.css" />
	<link rel="stylesheet" href="/UI/css/theme/jquery-ui-1.8.21.custom.css" />
    <link rel="stylesheet" href="https://pro.fontawesome.com/releases/v5.1.0/css/all.css" integrity="sha384-87DrmpqHRiY8hPLIr7ByqhPIywuSsjuQAfMXAE0sMUpY3BM7nXjf+mLIUSvhDArs" crossorigin="anonymous">
    <link rel="stylesheet" href="/UI/css/tiptip.css" />
    <link rel="stylesheet" href="/UI/js/jquery.fancybox-1.3.4/fancybox/jquery.fancybox-1.3.4.css" media="screen" type="text/css" />
    <link rel="stylesheet" href="/UI/css/towbook.css" />
    <link rel="stylesheet" href="/UI/css/application-forms.css" />
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/sweetalert2/4.2.7/sweetalert2.min.css"/>
</head>
<body>

<% } %>

    <link rel="stylesheet" href="/Stickering/Editor.css?v=1" />

    <div id="wrapper">
    </div>

    <div id="stickerEditor" class="stickerEditor" data-company-id="<% =_stickerCompanyId %>">
        <table>
            <tbody id="main">
                <tr class="header" data-reason-id="0">
                    <td colspan="2">
                        <div>
                            <span class="description-heading">Sticker Information</span>
                        </div>
                    </td>
                </tr>
                <tr id="rowAccount">
                   <td class="property"><label for="account">Account</label></td>
                   <td class="value">
                       <%if (Global.CurrentUser.Type == Extric.Towbook.User.TypeEnum.AccountUser) {%>
                        <input id="account" class="x-account" title="click or type here choose an account"  style="display: none;"/>
                       <% } else { %>
                       <select id="account" class="x-account" title="click or type here choose an account" data-width="100%" style="display: none;"/>
                       <% } %>
                   </td>
                </tr>
                <tr style="display: none;">
                    <td class="property"><label for="customNumber">Custom #*</label></td>
                    <td class="value">
                        <input type="text" id="customNumber" class="validate-number required" title="enter the sticker custom number [Required]" placeholder="enter custom #" style="width: 300px" />
                    </td>
                </tr>
            </tbody>
        </table>

        <table>
            <tbody class="asset">
            <!--<tr class="header">
                <td colspan="2">
                <div>
                    <span class="description-heading">Vehicle Information</span>
                </div>
                </td>
            </tr>-->
            <tr class="asset-item">
                <td class="property">License/VIN</td>
                <td class="value ignore-preset-combo-width">
                    <input type="text" id="licenseNumber" class="x-license-plate" title="license plate" placeholder="license plate" style="width: 100px" />
                    <select class="x-license-state standard" id="licenseState" title="<% = (Global.CurrentUser.Company.LocaleStateName).ToLowerInvariant() %>" data-width="90px" maxlength="10"></select>
                    <input type="text" id="vin" class="x-vin validate-vin" maxlength="17" title="VIN" placeholder="VIN" style="width: 297px" />
                </td>
            </tr>
            <tr class="asset-item">
                <td class="property">Vehicle</td>
                <td class="value" id="assetDetails">
                    <select id="year" class="x-year standard validate-number" title="year" data-width="65px"></select>
                    <select id="make" class="x-make" title="vehicle make" data-width="115px"></select>
                    <select id="model" class="x-model standard" title="vehicle model" data-width="121px"></select>
                    <select id="color" class="x-color" title="color" data-width="80px"></select>
                </td>
            </tr>
            <tr id="vehicleMessageContainer" style="display: none;">
                <td class="property"></td>
                <td class="value">
                    <div id="vehicleMessage">
                        <i class="fa fa-warning"></i><span class="message"></span>
                    </div>
                </td>
            </tr>
            </tbody>
        </table>
        
        <table>
            <tbody>
                <tr>
                    <td class="property">Notes</td>
                    <td class="value">
                        <textarea id="notes"></textarea>
                    </td>
                </tr>
            </tbody>
        </table>

        <table id="stickerReasons">
            <tbody>
                <tr class="header" data-reason-id="0">
                    <td colspan="2">
                        <div>
                            <span class="description-heading">Reasons</span>
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>
        
        <table id="notesHeader" style="display: none;">
            <tbody>
                <tr class="header">
                    <td colspan="2">
                        <div>
                            <span class="description-heading">Quick Notes</span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td class="property">Quick Notes</td>
                    <td class="value">
                        <div style="display: block; overflow: hidden; position: relative;">
                            <input type="text" id="stickerNote_new" title="type a quick note and press enter..." />
                            <div id="stickerNote_help" style="display: none;"><i class="fa fa-exclamation fa-1x fa-fw"></i><span>unsaved</span></div>
                        </div>
                    </td>
                    <td style="width: 32px; vertical-align: middle;">
                        <a id="stickerNoteButton_new" class="flat-ui-button icon add-internal-note" title="Save Note"><span></span></a>
                    </td>
                </tr>
            </tbody>
        </table>
        
        <div id="stickerNotes" class="h3box" style="margin: 2px; display:none;"></div>

    </div>

    <div class="navigation-row">
        <input type="button" class="standard-button" id="save" value="Create Sticker" title="Save your changes for this sticker" />
        <input type="button" class="cancel-button" id="discard" value="<% if (Request.QueryString["_"] == null) { %>Discard Changes<% } else { %>Cancel<% } %>" onclick="towbook.views.stickering.closePopupWindow();" title="Abandon any changes you've made to this sticker" />
    </div>

    <script type="text/x-jQuery-tmpl" id="t-stickerReason">
        <tr class="sticker-reason" data-reason-id="${id}">
            <td class="property">Reason</td>
            <td class="value">
                <select id="stickerReason-${id}" data-reason-id="${id}" class="x-item-name" title="click here and choose an item from the list, or start typing the name of the reason" data-width="100%"></select>
            </td>
        </tr>
    </script>

    <script type="text/x-jQuery-tmpl" id="t-stickerNote">
        <div id="stickerNote_${id}" style="padding-left: 10px; padding-top: 10px" data-note-id="${id}">
            <span style="float:right;"><input type="button" class="removeButton remove-asset" id="removeNote_${id}" data-id="${id}" style="${showDelete ? '' : 'display:none;'}" /></span>
            <strong>${userFullName}</strong> wrote at ${towbook.formatAMPM(createDate)} on ${towbook.formatDate(createDate)}:<br />
            <div style="margin-left: 20px">
                ${message}
            </div>
        </div>
    </script>

    <script type="text/javascript">
        var pusher = window.pusher = window.pusher || new Pusher('00d0fb70749a0a4fd6f9', { authEndpoint: '/api/pusher', authDelay: 200, authorizer: PusherBatchAuthorizer });

        // If the same pusher event came in multiple times within 500ms, only process the first one
        pusherData = [];
        alreadyReceived = function (data) {
            var str = JSON.stringify(data);
            if (pusherData.indexOf(str) > -1) {
                return true;
            } else {
                pusherData.push(str);
                window.setTimeout(function () {
                    var index = pusherData.indexOf(str);
                    pusherData.splice(index, 1);
                }, 500);
                return false;
            }
        }

        pusher.stickerNote_insert = function (data) {
            console.log('Pusher::stickerNote_insert -> with data:', data);
            if (alreadyReceived(data)) return;

            // refresh the current list
            if (data.extra == "delete") {
                var targetEl = $('#stickerNote_' + data.noteId);

                if ($('.remove-asset').length == 1)
                    targetEl = $('#stickerNotes');

                targetEl.data('note-id', data.noteId);

                targetEl.slideUp(function () {
                    $('#stickerNote_' + $(this).data('note-id')).remove();

                    if ($('.remove-asset').length == 0) {
                        $('#stickerNotes').hide();
                    }
                });
                console.log('Removed Sticker Note ' + data.noteId + ' from list of sticker notes.');
            } else {
                updateStickerNote(data.noteId);

                console.log('Finished!! Pusher update event sticker note execution success!');
            }
        }

        <% foreach (var c in Companies) { %>
            var tbChannel = 'private-TWBK_Client_<% =c.Id %>';
            if (pusher.channel(tbChannel) == null) pusher.subscribe(tbChannel)

            pusher.channel(tbChannel)
                .bind('stickerNote_insert_' + $('body').attr('active-sticker-id'), pusher.stickerNote_insert)
        <% } %>


    <%if (Global.CurrentUser.Type == Extric.Towbook.User.TypeEnum.AccountUser) {%>
        $('#stickerEditor').addClass('account-user');
        $('#account').val(<%= Global.CurrentUser.AccountId%>);
    <% } %>

    <% if (Request.QueryString["id"] != null) {  %>
        $('body').attr('active-sticker-id', <%=Request.QueryString["id"]%>)
    <% } %>

        $.extend(towbook, { accounts: JSON.parse('<%= HttpUtility.JavaScriptStringEncode(_accountsJson) %>') });
        if (!towbook.views.stickering) {
            $.extend(towbook.views, {
                stickering: {
                    id: 0,
                    reasons: [],
                    notes: [],
                }
            });
        }
        $.extend(towbook, { permits: { types: JSON.parse('<%= HttpUtility.JavaScriptStringEncode(_parkingPermitListTypesJson ?? "[]") %>') }});

    <% if (Request.QueryString["_"] == null) { %>
        $.extend(towbook.vehicle, {
        makes: <%=_vehicleMakesJson%>, 
        colors: <%=_vehicleColorsJson%>,
        });
        $.extend(towbook.geo, {states: <%=_statesJson%>});
        towbook.views.stickering.closePopupWindow = function () {
            swal("Saved").then(function () { window.location.reload(false); });
        }
    <% } %>

    </script>

    <script src="/Stickering/Editor.js" type="text/javascript"></script>

<% if (Request.QueryString["_"] == null) { %>
</body>
</html>
<% } %>
