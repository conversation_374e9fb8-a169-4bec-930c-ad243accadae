using System;
using System.Collections;
using System.Configuration;
using System.Data;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.HtmlControls;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using Extric.Towbook;
using Extric.Towbook.Company;
using System.Text;

public partial class Management_Clients_Default : System.Web.UI.Page
{
    protected String returnValue;

    protected void Page_Load(object sender, EventArgs e)
    {
        Master.InnerTitle = "# Clients";
        Master.CurrentSection = Navigation.NavigationItemEnum.ManagementClients;
        Master.UseJquery = true;

        if (Global.CurrentUser.Type != Extric.Towbook.User.TypeEnum.SystemAdministrator)
            throw new Extric.Towbook.TowbookException("Access Denied");

        string separator = ",";
    }
}