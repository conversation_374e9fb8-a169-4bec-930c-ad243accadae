<script type="text/x-jQuery-tmpl" id="t-companyRow">
    <tr id="${id}">
        <td class="CellLeft"><a href="Client.aspx?id=${id}"><strong>${company}</strong>
            <span class="prop">${phone}</span></a>
        </td>
        <td class="Id">${id}</td>
        <td>${firstUser}</td>
        <td class="m-state"><div style="max-width:53px; word-wrap:break-word;" title="${state}">${state}</div></td>
        <td class="m-fee">${accounting.formatMoney(monthlyFee)}</td>
        <td class="m-users">${userCount}</td>
        <td class="m-trucks">${truckCount}</td>
        <td class="m-drivers">${driverCount}</td>
        <td class="m-callCount30">${callCount30}</td>
        <td class="m-lastLogin">{{if lastLogin}}${towbook.formatDate(lastLogin, "mm/dd/yy")} ${towbook.formatAMPM(lastLogin)}{{/if}}</td>
    </tr>
</script>
