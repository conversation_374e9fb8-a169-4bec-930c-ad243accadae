using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Collections.ObjectModel;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Extric.Towbook.Accounts;
using System.Linq;
using Extric.Towbook.WebShared;
using System.Text.RegularExpressions;
using Extric.Towbook.Utility;

public partial class Accounts_Overview : System.Web.UI.Page
{
    protected int type;
    public int defaultAccountType = 0;
    public int defaultAccountTag = 0;
    public string defaultAccountZone = string.Empty;
    public bool showInactives = false;
    public bool showDeleteds = false;
    public bool showCallOuts = true;
    public string TagsJson { get; set; }
    public string LotsJson { get; set; }

    protected void Page_Load(object sender, EventArgs e)
    {
        this.Master.CurrentSection = Navigation.NavigationItemEnum.Accounts;
        this.Master.InnerTitle = "Accounts & Customers";
        this.Master.UseJquery = true;

        if (Global.CurrentUser.Type == Extric.Towbook.User.TypeEnum.Driver)
        {
            var hideAccounts = Extric.Towbook.Integration.CompanyKeyValue.GetByCompanyId(WebGlobal.CurrentUser.Company.Id, Extric.Towbook.Integration.Provider.Towbook.ProviderId, "HideAccountDetailsFromDrivers").FirstOrDefault();
            if (Global.CurrentUser.Type == Extric.Towbook.User.TypeEnum.Driver)
            {
                if (WebGlobal.CurrentUser.CompanyId == 4387 || (hideAccounts != null && (hideAccounts.Value != "0")))
                {
                    Response.Redirect("/");
                }

                var any = Extric.Towbook.Company.SharedCompany.GetByCompanyId(WebGlobal.CurrentUser.CompanyId).FirstOrDefault();
                if (any != null && any.CompanyId == 26130)
                    Response.Redirect("/");
            }
        }


        // get the accountTagModels
        //TagsJson = new Extric.Towbook.API.Controllers.AccountsController().Tags().ToJson();
        TagsJson = WebGlobal.GetResponseFromUrl("/api/accounts/tags");

        // hide Call Out accounts initially
        if (new int[] { 525 }.Contains(WebGlobal.CurrentUser.CompanyId))
            showCallOuts = false;

        // Look in user and driver notes for a zone identifier
        // The identifier must be 6 characters long and be in the
        // form of "Zone x" where the 'x' is a number.  Note: the
        // whitespace is necessary!
        Regex rgx = new Regex(@"(Zone)\s\d");
        string driverNotes = "";
        var dr = (Extric.Towbook.Driver.GetByUserId(WebGlobal.CurrentUser.Id) ?? new Collection<Extric.Towbook.Driver>()).FirstOrDefault();

        if (dr != null)
        {
            driverNotes = (!String.IsNullOrWhiteSpace(dr.Notes) ? dr.Notes : (WebGlobal.CurrentUser.Notes ?? ""));
        }
        else
        {
            driverNotes = (WebGlobal.CurrentUser.Notes ?? "");
        }

        if (driverNotes.Length > 1)
            driverNotes = driverNotes[0].ToString().ToUpper() + driverNotes.Substring(1);

        if (rgx.IsMatch(driverNotes))
            defaultAccountZone = rgx.Match(driverNotes).Value;


        LotsJson = WebGlobal.GetResponseFromUrl(
                        string.Format("/api/impoundlots"));
    }
}