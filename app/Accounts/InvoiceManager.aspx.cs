using System;
using System.Data;
using System.Linq;
using App.Accounts;
using Extric.Towbook.API.Model.Models.Calls;
using Extric.Towbook.Company;
using Extric.Towbook.Utility;
using Extric.Towbook.WebShared;

public partial class Accounts_InvoiceManager : System.Web.UI.Page
{
    public string _invoiceStatusesJson = "";
    public string _workflowAccountsJson = "";
    protected int _companyId;
    public string _callsJson = "";
    public string _userJson = "";
    public string _usersJson = "";
    public string _mapFromAddressBook = "";
    public string _companiesJson = "";
    public string _currentCompanyJson = "";

    protected void Page_Load(object sender, EventArgs e)
    {
        this.Master.CurrentSection = Navigation.NavigationItemEnum.Accounts;
        this.Master.InnerTitle = "Invoice Manager";
        this.Master.UseJquery = true;

        _companyId = Global.CurrentUser.Company.Id;
        //_invoiceStatusesJson = JsonExtensions.ToJson(new InvoiceStatusesController().Get());
        _invoiceStatusesJson = ApiAccess.GetInvoiceStatuses();
        //_workflowAccountsJson = JsonExtensions.ToJson(new InvoiceStatusWorkflowsController().GetAccounts());
        _workflowAccountsJson = ApiAccess.GetAccountsInvoiceStatusWorkflows();
        //_userJson = JsonExtensions.ToJson((new UserController().Get()));
        _userJson = ApiAccess.GetUsers();
        //_usersJson = JsonExtensions.ToJson((new UsersController().Get()));
        _usersJson = ApiAccess.GetUsers();
            _mapFromAddressBook = JsonExtensions.ToJson(AddressBookEntry.GetByName("MapFrom", WebGlobal.CurrentUser.Company.Id));
        _companiesJson = JsonExtensions.ToJson((
            WebGlobal.CurrentUser.Type == Extric.Towbook.User.TypeEnum.SystemAdministrator ? null :
            WebGlobal.GetCompanies().Select(o => new
            {
                Id = o.Id,
                Name = o.Name,
                ShortName = o.ShortName,
                Address = o.Address,
                City = o.City,
                State = o.State,
                Zip = o.Zip,
                Country = o.Country.ToString()
            })
            .OrderBy(o => o.Name)));

        // Get Recent Jobs for all accounts
        //_callsJson = JsonExtensions.ToJson(
        //    new CallsController().ByAccounts(
        //    new CallsController.ByAccountsRequest()
        //    {
        //        AccountIds = new int[] {},
        //        InvoiceStatusId = 1,
        //        PageNumber = 1,
        //    }));
        _callsJson = ApiAccess.GetCallsByAccounts(
            new ByAccountsRequest()
            {
                AccountIds = new int[] { },
                InvoiceStatusId = 1,
                PageNumber = 1,
            });

        //var _masterAccount = MasterAccount.GetById(_account.MasterAccountId);

        //List<Extric.Towbook.Company.Company> companies = new List<Extric.Towbook.Company.Company>();

        //if (Global.CurrentUser.CompanyId == 1525)
        //{
        //    companies = new Extric.Towbook.Company.Company[] { Global.CurrentUser.Company }.ToList();
        //}
        //else
        //{
        //    companies = Extric.Towbook.WebShared.WebGlobal.GetCompanies().Where(o => o.Id != 1525).ToList();
        //}
    }
}