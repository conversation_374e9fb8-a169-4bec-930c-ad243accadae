<%@ Page Language="C#" AutoEventWireup="true" CodeFile="PaymentDetails.aspx.cs" Inherits="Accounts_PaymentDetails" %>
<%@ Import Namespace ="Extric.Towbook.WebShared" %>
<%@ Import Namespace ="Extric.Towbook.Utility" %>
<%@ Import Namespace ="Extric.Towbook.API.Models.Calls" %>
<% if (Request.QueryString["_"] == null)
    { %>
<!DOCTYPE html>
<html>
  <head runat="server">
    <title>Payment Details</title>
    <script src="//ajax.googleapis.com/ajax/libs/jquery/1.7.2/jquery.min.js" type="text/javascript"></script>
	<script src="//ajax.googleapis.com/ajax/libs/jqueryui/1.8.14/jquery-ui.min.js" type="text/javascript"></script>
	<script src="/ui/js/towbook/towbook.js"></script>
    <script type="text/javascript" src="//use.typekit.com/edd6gei.js"></script>
    <script type="text/javascript">try { Typekit.load(); } catch (e) { }</script>
    <script type="text/javascript" src="https://cdn.jsdelivr.net/sweetalert2/4.2.7/sweetalert2.min.js"></script>
    
    <link rel="stylesheet" href="/UI/css/jquery-ui.css" />
	<link rel="stylesheet" href="/UI/css/theme/jquery-ui-1.8.21.custom.css" />
    <link rel="stylesheet" href="https://pro.fontawesome.com/releases/v5.1.0/css/all.css" integrity="sha384-87DrmpqHRiY8hPLIr7ByqhPIywuSsjuQAfMXAE0sMUpY3BM7nXjf+mLIUSvhDArs" crossorigin="anonymous">
    <link rel="stylesheet" href="/UI/css/towbook.css" />
    <link rel="stylesheet" href="/UI/scss/towbook-overrides.css" />
    <link rel="stylesheet" href="/UI/css/application-forms.css" />
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/sweetalert2/4.2.7/sweetalert2.min.css"/>

    <style>
      .list td { padding: 3px; vertical-align: bottom }
    </style>
  </head>
<body>
<% } %>    

<script>
    
    $(function () {
        $('#x-void-payment').on('click', function () {
            var paymentId = '<% =this._payment.Id %>';
            if (confirm("Are you sure you want to permanently void this payment?")) {
      $.ajax({
                url: '/api/accounts/<%=this._payment.AccountId %>/payments/' + paymentId,
                data: JSON.stringify({ paymentId: paymentId }),
                contentType: 'application/json; charset=utf-8',
                type: 'POST'
              }).done(function (data) {
                  parent.location.reload();
              }).error(function (xhr, status, error) {
                  alert(xhr.responseText);
              });
    }
        });

        printPaymentDetails = function(event) {
            event.preventDefault();
            event.stopPropagation();

            function doPrint() {
                if (document.queryCommandSupported('print')) {
                    $('iframe#ifPrintCall').get(0).contentWindow.document.execCommand('print', false, null);
                } else {
                    window.frames['ifPrintCall'].focus();
                    window.frames['ifPrintCall'].contentWindow.print();
                }
            }

            $('iframe#ifPrintCall').remove();

            $('<iframe id="ifPrintCall" style="width: 1px; height: 1px; padding: 0; margin-left: -50px"></iframe>').appendTo($('body'));

            var url = "paymentDetails.aspx?id=" + <% =this._payment.Id %> + "&_=" + Math.random();

            $.get(url).done(function (data) {
                $(data).appendTo($('iframe#ifPrintCall').contents().find('body'));

                doPrint();
            });
        };

        $('#fancybox-outer').css('width', '1100');
    });
</script>

<style>
    .details strong { display: inline-block; width: 100px }
    body { background-image: none; margin: 1px; font-size: 12px;}
    .towbook-dialog { margin-bottom: 70px; }
    .towbook-dialog-footer { position: fixed; background-color: white; background-color: rgba(255,255,255,.8); }

    .x-invoice-void { color: #afafaf; text-decoration: line-through; }
    #account { display: none; }

    .printLink {
        font-size: 14px;
        background-image: none;
        padding-left: 20px;
        padding-right: 20px;
        padding-top: 8px;
        padding-bottom: 8px;
        border-radius: 0;
        background-color: transparent;
        color: #0072c6;
        text-decoration: none;
    }

    table.list tbody tr td { border: none;}

    @media print
    {
        input, .printLink { display: none }
	    body { margin: 10px }
        * { font-family: Calibri, Arial; font-size: 12px;}
        h1 {font-size: 22px; }
        h2 {font-size: 18px; }
        ul.details { list-style: none; }
        a { text-decoration: none; color: black;}
        ul.details li { font-size: 12px !important; }
        table { height: auto !important; }
        table tbody tr { border: solid 1px #afafaf;}
        table tbody tr td, table thead tr th { font-size: 12px !important; text-align: left; border: none;}
        #account { display: block; }
    }

    <% if (this._payment.IsVoid) { %>

    #x-void-payment { display: none }
    <% }  %>
    #closed-status {
      display: flex;
    }
    #closed-status div { 
      display: flex; 
      font-family: InterVar, sans-serif;
    }
    .closed-wrapper {
      flex-direction: row;
      column-gap: 10px;
      align-items: center;
      padding: 10px 20px;
      width: 100%;
      background-color: #fff8f7;
    }
    .closed-wrapper .title {
      color: #b57216;
    }
    .closed-wrapper .closed-icon {
      flex-direction: column;
      flex-grow: 0;
      row-gap: 3px;
      padding: 10px;
      border-style: solid;
      border-radius: 20px;
      color: white;
      align-items: center;
      font-size: 15px !important;
      background-color: #e54d2e;
    }
    .closed-wrapper .fa {
      font-family: "Font Awesome 5 Pro" !important;
      display: flex;
    }
    .closed-wrapper .closed-message {
      flex-direction: column;
      flex: 0 0 70%;
      row-gap: 5px;
    }

</style>
<div class="towbook-dialog">
    <h1><%= _title %></h1>
    <h2 id="account"><%= _account.Company%></h2>
    <% if (_inClosedPeriod) { %>
      <div id="closed-status">
        <div class="closed-wrapper">
          <div class="closed-icon">
            <i class="fa fa-minus-circle"></i>
          </div>
          <div class="closed-message">
            <div class="title">This payment is in a closed period.</div>
            
          </div>
          <div class="closed-action">
          </div>
        </div>
      </div>
    <% } %>

    <ul class="details">
      <li>
        <strong>Amount</strong>
        <span><% =HttpUtility.HtmlEncode(_payment.Amount.ToString("C")) %> (<% =HttpUtility.HtmlEncode(_payment.Type.ToString()) %>) <%= HttpUtility.HtmlEncode(String.IsNullOrEmpty(_payment.ReferenceNumber) ? "" : "(Reference: " + _payment.ReferenceNumber + ")") %></span>
      </li>
      <li>
        <strong>Payment Date</strong>
        <span><% =HttpUtility.HtmlEncode(WebGlobal.OffsetDateTime(_payment.PaymentDate).ToShortDate()) %> </span>
      </li>
      <li>
        <strong>Entered</strong>
        <span><% =HttpUtility.HtmlEncode(FormatDate(_payment.CreateDate)) %> by <% =HttpUtility.HtmlEncode(Extric.Towbook.User.GetById(_payment.OwnerUserId).FullName) %></span>
      </li>

    <% if (!String.IsNullOrWhiteSpace(_payment.Notes))
        { %>
      <li>
        <strong>Notes</strong>
        <span><% =HttpUtility.HtmlEncode(_payment.Notes.ToString()) %></span>
      </li>
    <% } %>

    </ul>

    <div>
    <% 
        var dispatchEntryPayments = Extric.Towbook.Dispatch.InvoicePayment.GetByAccountPaymentId(_payment.Id);
        var invoices = Extric.Towbook.Invoice.GetByIds(dispatchEntryPayments.Select(o => o.InvoiceId).ToArray());
        var calls = Extric.Towbook.Dispatch.Entry.GetByIds(invoices.Select(o => o.DispatchEntryId).ToArray(), invoices);

        if (dispatchEntryPayments != null && dispatchEntryPayments.Count > 0)
        { %>
    <table class="list"  style="overflow: scroll; height: 200px; overflow-x: hidden">
      <thead>
        <tr>
          <th>Call #</th>
          <th>Vehicle</th>
          <th>PO #</th>
          <th>VIN</th>
          <th>Service Date</th>
          <th>Completed Date</th>
          <th>Invoice #</th>
          <th>Invoice Total</th>
          <th>Applied Amount</th>
          <th>Current Balance</th>
        </tr>
      </thead>
    <% foreach (var pmt in dispatchEntryPayments)
        {
            var call = calls.FirstOrDefault(r => r.Invoice.Id == pmt.InvoiceId);
            var invoice = invoices.FirstOrDefault(xi => xi.Id == pmt.InvoiceId);
            if (call == null)
            {
    %>
    <tr <% =(pmt.IsVoid ? "class=\"x-invoice-void\"" : "") %>>
        <td colspan="7"><%=HttpUtility.HtmlEncode(invoice.InvoiceItems.FirstOrDefault() != null ? invoice.InvoiceItems.First().CustomName : "") %></td>
        <td><% = pmt.Amount.ToMoney(company) %></td>
      </tr>
    <%
        }
        else
        {
    %>
      <tr <% =(pmt.IsVoid ? "class=\"x-invoice-void\"" : "") %>>
        <td><a href="/Dispatch10/Editor.aspx?id=<% = call.Id %>" target="_blank"><% = call.CallNumber %></a></td>
        <td><% = HttpUtility.HtmlEncode((call.Year > 0 ? call.Year.ToString() : "") + " " + call.MakeModelFormatted) %></td>
        <td><% = HttpUtility.HtmlEncode(call.PurchaseOrderNumber) %></td>
        <td style="font-family: courier"><% = HttpUtility.HtmlEncode(call.VIN) %></td>
        <td><% = HttpUtility.HtmlEncode(FormatDate(call.CreateDate)) %></td>
        <td><% = HttpUtility.HtmlEncode(FormatDate(call.CompletionTime)) %></td>
        <td> <% = HttpUtility.HtmlEncode(call.InvoiceNumber) %></td>
        <td><a href="/Dispatch2/Invoice.aspx?id=<% = call.Id %>" target="_blank"><% =HttpUtility.HtmlEncode(call.InvoiceTotal.ToMoney(company))%></a></td>
        <td><% = HttpUtility.HtmlEncode(pmt.Amount.ToMoney(company)) %></td>
        <td><% = HttpUtility.HtmlEncode(call.BalanceDue.ToMoney(company)) %></td>
      </tr>
    <% }
        } %>
    </table>
    <% } %>

    </div>
    <div class="towbook-dialog-footer">
        <a href="#" class="printLink" onclick="printPaymentDetails(event); return false;">Print</a>
        <% if (!_inClosedPeriod) { %>
          <input type="button" id="x-void-payment" value="Void Payment" class="standard-button cancel-button" />
        <%  } %>
        <input type="button" class="standard-button" id="close-button" value="Close" onclick="parent.closeLightbox();" />
    </div>
</div>

<% if (Request.QueryString["_"] == null)
    { %>
</body>
</html>
<% }  %>
