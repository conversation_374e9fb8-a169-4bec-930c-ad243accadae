using System;
using System.Web;
using System.Net.Mail;
using Extric.Towbook.Utility;
using Extric.Towbook.WebShared;
using Extric.Towbook.Integrations.Email;
using System.Collections.Generic;
using System.Linq;
using Extric.Towbook.Generated;
using Extric.Towbook;
using Extric.Towbook.Accounts;
using Extric.Towbook.Square;
using Extric.Towbook.Configuration;
using System.Threading.Tasks;
using System.Web.UI;
using Extric.Towbook.Company;
using Extric.Towbook.Integration;
using System.Net;
using Extric.Towbook.Dispatch;
using Extric.Towbook.EventNotifications;
using Extric.Towbook.API.OpenText;
using System.Web.Mail;

public partial class Accounts_SendEmail : Page
{
    protected bool _sent = false;
    protected int _statementId = 0;
    protected int _templateId = 0;
    protected int _accountId = 0;
    protected string _attachmentName;
    protected string recipientsJson = "[]";

    protected string subject;
    protected string content;
    protected string from;
    protected int fromType;
    protected string to;
    protected string cc;
    protected Account account;
    protected string replyToEmailAddress = "";
    protected StatementEmailOption seo;


    public async Task SendStatementEmail()
    {
        if (account == null || _accountId <= 1)
            return;

        if (to == "0")
            to = account.Email;

        if (from == "0")
            fromType = 0;
        else
            fromType = 1;
        if (subject.Length == 0) subject = "Account Statement - " + account.Company;
        if (content.Length == 0) throw new Exception("cannot send message with empty content body");

        await ENServiceBusHelper.SendMessageObjectAsync("email-statements", 
            "statement-" + _statementId + "-" + DateTime.Now.Ticks,
            new Extric.Towbook.API.Models.Email.StatementJob
            {
                AccountId = _accountId,
                SendingUserId = WebGlobal.CurrentUser.Id,
                Subject = subject,
                MessageContent = content,
                To = to,
                From = fromType,
                Cc = cc,
                StatementId = _statementId,
                TemplateId = _templateId
            }.ToJson(), "Statement Push");

        Response.Redirect("Account.aspx?id=" + _accountId.ToString() + "&emailSent=1");
        return;
    }


    protected void Page_Load(object sender, EventArgs e)
    {
        _accountId = Convert.ToInt32(Request.QueryString["id"]);

        if (_accountId == 1)
            Response.End();
        
        if (!(WebGlobal.CurrentUser.Type == Extric.Towbook.User.TypeEnum.Manager || WebGlobal.CurrentUser.Type == Extric.Towbook.User.TypeEnum.Accountant))
        {
            Response.Redirect("/");
            return;
        }

        account = Account.GetById(_accountId);

        if (Request.QueryString["statementId"] != null)
        {
            _statementId = Convert.ToInt32(Request.QueryString["statementId"]);
            _attachmentName = "Statement_" + _statementId + ".pdf";
        }

        if (Request.QueryString["templateId"] != null)
        {
            _templateId = Convert.ToInt32(Request.QueryString["templateId"]);
            _attachmentName = "File_" + _accountId + "_" + _templateId + ".pdf";
        }

        if (account != null)
        {
            if (!WebGlobal.CurrentUser.HasAccessToCompany(account.CompanyId))
                throw new TowbookException("You do not have access to this account.");

            var emails = (account.Email ?? "").Replace(";", ",").Replace(" ", "");

            // add possible account billing contact role
            var addressBookType = "Billing Address";
            if (account.Type == AccountType.MotorClub)
                addressBookType = "MapFrom";

            var billingAddress = AddressBookEntry.GetByAccountId(account.Id).Where(o => o.Name == addressBookType).FirstOrDefault();
            if (billingAddress != null && Core.IsEmailValid(billingAddress.Email))
                emails = billingAddress.Email + "," + emails;

            int idx = 1;
            List<object> options = new List<object>();
            foreach (var email in emails.Split(','))
            {
                if (!string.IsNullOrWhiteSpace(email))
                    options.Add(new { id = idx++, name = email });
            }

            recipientsJson = options.ToJson();

            #region Reply-To address            
            var replytoEmail = AccountKeyValue.GetFirstValueOrNull(account.CompanyId, account.Id, Provider.Towbook.ProviderId, "ReplyToEmailAddress");
            var company = Company.GetById(account.CompanyId);
            
            billingAddress = AddressBookEntry.GetByName("Billing Address", account.CompanyId, false).FirstOrDefault();
            if (billingAddress != null && Core.IsEmailValid(billingAddress.Email))
                replyToEmailAddress = billingAddress.Email;

            if (Core.IsEmailValid(replytoEmail)) {
                replyToEmailAddress = replytoEmail;
            }
            #endregion

            if (_statementId > 0 && 
                WebGlobal.CurrentUser.Company.HasFeature(Features.AdvancedBilling))
            {
                seo = StatementEmailOption.GetByCompanyId(account.CompanyId, account.Id);
            }
        }

        if (Request.Form["send"] != null)
        {
            subject = Request.Form["subject"];
            content = Request.Form["message"];
            from = Request.Form["from"];
            to = Request.Form["to"];
            cc = Request.Form["cc"];
            
            RegisterAsyncTask(new PageAsyncTask(SendStatementEmail));
            
        }

    }
}

    
    
