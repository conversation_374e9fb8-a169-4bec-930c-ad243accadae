using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Data;
using Extric.Towbook;
using System.Collections.ObjectModel;
using Extric.Towbook.Dispatch;
using Extric.Towbook.Accounts;
using Extric.Towbook.WebShared;
using System.Threading.Tasks;
using System.Web.UI;
using Extric.Towbook.Impounds;
using Extric.Towbook.Utility;
using NLog;

public partial class Accounts_AccountStatementCreate : System.Web.UI.Page
{
    protected Account _account;
    protected Statement _statement;
    protected Collection<Invoice> _invoices;
    protected IEnumerable<Statement.StatementDispatchEntry> _statements;
    protected Collection<Impound> _impounds;
    protected IEnumerable<EntryLock> _entryLocks;
    protected IEnumerable<User> _entryUsers;
    protected bool ShowPO { get; set; }
    protected bool ShowUnloadedMiles { get; set; }
    protected bool ShowLoadedMiles { get; set; }
    protected bool ShowVIN { get; set; }
    protected bool ShowCoverage { get; set; }
    protected bool ShowOdometer { get; set; }
    protected bool ShowReason { get; set; }
    protected bool ShowDriverTruck { get; set; }

    protected bool ShowMCDispatch { get; set; }
    protected bool ShowMCMembership { get; set; }
    protected bool ShowMCCoverage { get; set; }
    protected bool ShowAddress { get; set; }
    protected bool ShowLockAndAudit { get; set; }
    protected bool ShowCompletionDate { get; set; }

    private static readonly Logger logger = LogManager.GetCurrentClassLogger();

    protected string GetStatement(object o, bool showYear = true)
    {
        var e = o as Entry;
        if (e != null)
        {
            var numbers = _statements.Where(s => s.DispatchEntryId == e.Id).Select(i =>
                (showYear ? i.CreateDate.Year.ToString().Substring(2, 2) + "-XX" + i.StatementId.ToString() : i.StatementId.ToString()));

            string rval = "";

            if (numbers.Count() > 1)
            {
                var title = "This invoice exists on the following statements: " + string.Join(", ", numbers);
                rval += "<span><i class=\"fa fa-file-invoice\" title=\"" + title + "\" onclick=\"alert('This invoice exists on the following statements:\\n" +
                    string.Join(",\\n", numbers) + "')\"></i></span>";
            }

            if (_impounds.Any(ro => ro.DispatchEntry.Id == e.Id))
            {
                rval += "<span><i class=\"fa fa-exclamation\" title=\"This is an active impound.\"></i></span>";
            }

            return rval;
        }

        return "";
    }

    protected void Page_Load(object sender, EventArgs e)
    {
        this.Master.CurrentSection = Navigation.NavigationItemEnum.Accounts;
        this.Master.UseJquery = true;

        RegisterAsyncTask(new PageAsyncTask(PageLoadAsync));
    }

    protected async Task PageLoadAsync() 
    {
        ShowPO = true;
        ShowAddress = true;
        ShowLockAndAudit = Global.CurrentUser.Company.HasFeature(Extric.Towbook.Generated.Features.Dispatching_AuditCalls) || 
            Global.CurrentUser.Company.HasFeature(Extric.Towbook.Generated.Features.Dispatching_LockCalls);

        _account = await Account.GetByIdAsync(Convert.ToInt32(Request.QueryString["accountId"]));

        if (_account == null || !(Global.CurrentUser.HasAccessToCompany(_account.CompanyId)))
        {
            Response.Write("Invalid ID");
            Response.End();
        }

        if (!(WebGlobal.CurrentUser.Type == Extric.Towbook.User.TypeEnum.Manager || WebGlobal.CurrentUser.Type == Extric.Towbook.User.TypeEnum.Accountant))
        {
            Response.Redirect("/");
            return;
        }


        if (Request.QueryString["accountId"] != null && Request.QueryString["statementId"] != null)
        {
            this.Master.InnerTitle = "Statement Created for " + _account.Company;

            var st = await Statement.GetByIdAsync(Convert.ToInt32(Request.QueryString["statementId"]));

            if (st != null && !Global.CurrentUser.HasAccessToCompany(st.Company.Id))
            {
                Response.Write("Invalid ID");
                Response.End();
            }

            EnterDetails.Visible = false;
            Confirmation.Visible = true;

            _statement = st;
            return;
        }


        if (Global.CurrentUser.PrimaryCompanyId == 4185)
        {
            ShowReason = true;
            ShowMCDispatch = true;
            ShowMCMembership = true;
            ShowMCCoverage = true;
            ShowAddress = false;
        }

        this.Master.InnerTitle = "Create Statement for " + _account.Company;

        if (!IsPostBack)
        {
            _invoices = await Invoice.GetByAccountIdForNullDispatchEntriesAsync(this._account.Id,
                WebGlobal.GetCompanies().DefaultIfEmpty().Select(o => o.Id).ToArray());
        }

        if (Request.QueryString["id"] != null)
        {
            _statement = await Statement.GetByIdAsync(Convert.ToInt32(Request.QueryString["id"]));
            this.Master.InnerTitle = "Modify Statement for " + _account.Company + ", Statement #" +
            _statement.CreateDate.Year.ToString().Substring(2, 2) + "-" + _statement.Id.ToString().PadLeft(7, '0');
            btnSave.Text = "Modify";
        }
        else
        {
            this.Master.InnerTitle = "Create Statement for " + _account.Company;
        }

        if (Request.QueryString["action"] == "delete")
        {
            await _statement.Delete();
            Response.Redirect("Account.aspx?id=" + _statement.AccountId);
        }

        ShowDriverTruck = true;
        if (IsPostBack)
        {
            var st = new Statement();

            if (_statement != null)
                st = _statement;

            st.AccountId = _account.Id;
            st.Company = WebGlobal.CurrentUser.Company;

            if (statementDate.Value == "")
            {
                Response.Write("<script>alert('Statement Date has no value')</script>");
                return;
            }
            if (dueDate.Value == "")
            {
                Response.Write("<script>alert('Due Date has no value')</script>");
                return;
            }

            st.DueDate = Convert.ToDateTime(dueDate.Value);
            st.StatementDate = Convert.ToDateTime(statementDate.Value);

            st.DispatchEntries.Clear();
            st.Invoices.Clear();

            var callIds = new List<int>();

            foreach (RepeaterItem ri in rpDispatchCalls.Items)
            {
                var ihId = (HtmlInputHidden)ri.FindControl("DataId");
                var enable = (CheckBox)ri.FindControl("Include");

                if (enable.Checked && enable.Enabled)
                    callIds.Add(Convert.ToInt32(ihId.Value));
            }

            foreach (var call in await Entry.GetByIdsAsync(callIds.ToArray()))
            {
             //   if (WebGlobal.CurrentUser.HasAccessToCompany(call.CompanyId) || 
              //      call.Account.MasterAccountId == MasterAccountTypes.SafeClear)
                    st.DispatchEntries.Add(call);
             //   else
            //        throw new Exception("Attempted to access CallId that you don't have access to: " + call.Id);
            }

            if (Request.Form["invoice"] != null)
            {
                string invoices = Request.Form["invoice"] + ",";
                string[] invoiceIds = invoices.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries);

                foreach (string invoiceId in invoiceIds)
                {
                    st.Invoices.Add(Invoice.GetById(Convert.ToInt32(invoiceId)));
                }
            }

            if (st.DispatchEntries.Count > 0 || st.Invoices.Count > 0)
            {
                st.SourceId = StatementSource.CreateStatement;
                st.OwnerUserId = WebGlobal.CurrentUser.Id;
                await st.Save();
            }

            LogStatementCreation();
            if (st.Id > 0)
            {
                Response.Redirect("/Accounts/AccountStatementCreate.aspx?accountId=" + st.AccountId + "&statementId=" + st.Id);
            }
        }
        else
        {
            EnterDetails.Visible = true;
            Confirmation.Visible = false;

            statementDate.Value = DateTime.Now.ToShortDateString();
            dueDate.Value = Statement.GetNextStatementDueDate(DueDateDefaultType.Net30, DateTime.Now).ToShortDateString();

            var accountCompany = WebGlobal.GetCompanies().FirstOrDefault(f => f.Id == _account.CompanyId);
            if (accountCompany != null)
            {
                var so = StatementOption.GetByCompanyId(accountCompany.Id, _account.Id);
                if (so != null)
                {
                    ShowCompletionDate = so.ShowCompletionDate;
                    if (accountCompany.HasFeature(Extric.Towbook.Generated.Features.AdvancedBilling) &&
                        (so.DefaultDueDateType != DueDateDefaultType.Unspecified 
                            || so.DefaultDueDateType != DueDateDefaultType.Net30))
                    {
                        dueDate.Value = Statement.GetNextStatementDueDate(so.DefaultDueDateType, DateTime.Now).ToShortDateString();
                    }
                }
            }


            var list = new List<Entry>();
            foreach (var row in await Statement.GetUnbilledDispatchEntries(Global.CurrentUser.Company, _account))
            {
                if (row.BalanceDue > 0 ||
                    row.InvoiceTotal == 0 ||
                    Global.CurrentUser.Company.State.ToUpper() == "TX" ||
                    new int[] { 2086, 3975, 1367, 536, 1367, 2009, 3509 }.Contains(Global.CurrentUser.CompanyId))
                {
                    list.Add(row);
                }
            }

            var calls = new List<Entry>();

            foreach (var comp in WebGlobal.GetCompanies().DefaultIfEmpty())
            {
                calls = calls.Union((await Statement.GetUnbilledDispatchEntriesAsync(comp, _account))
                    .Where(o => (o.InvoiceTotal == 0 || o.BalanceDue > 0) ||
                        Global.CurrentUser.Company.State.ToUpper() == "TX" ||
                        new int[] { 1367, 2086, 3975 }.Contains(Global.CurrentUser.CompanyId))).ToList();
            }

            if (_account.MasterAccountId != 18)
            {
                var statements = await Statement.GetByCompanyAsync(Global.CurrentUser.Company,
                    _account, null, null, false, null).Where(o => o.NowBalance > 0);

                foreach (var s in statements)
                {
                    foreach (var d in s.DispatchEntries.Where(o => o.BalanceDue > 0 && o.Status == Status.Completed))
                        calls.Add(d);
                }
            }

            var finalList = new List<Entry>();

            foreach (var call in (
                Global.CurrentUser.CompanyId == 3975 ||
                Global.CurrentUser.CompanyId == 1873 ||
                Global.CurrentUser.CompanyId == 536 ?
                list : calls.Distinct()))
            {
                if (call.Impound && new int[] { 1367, 2051 }.Contains(Global.CurrentUser.CompanyId))
                {
                    var imp = Extric.Towbook.Impounds.Impound.GetByDispatchEntry(call);
                    if (imp != null)
                    {
                        if (imp.ReleaseDate != null || (imp.Lot != null && imp.Lot.AccountId > 1))
                            finalList.Add(call);
                    }
                }
                else
                {
                    finalList.Add(call);
                }
            }

            finalList = finalList.OrderBy(o => o.CreateDate).ToList();

            _impounds = (await Impound.GetByDispatchEntryIdsAsync(finalList.Where(o => o.Impound == true).Select(o => o.Id).ToCollection())).Where(o => o.ReleaseDate == null).ToCollection();
            _statements = Statement.GetStatementIdsByDispatchEntryIds(finalList.Select(o => o.Id).ToArray());


            _entryLocks = Array.Empty<EntryLock>();
            _entryUsers = Array.Empty<User>();
            if (ShowLockAndAudit)
            {
                _entryLocks = EntryLock.GetByDispatchEntryIds(finalList.Select(s => s.Id).ToArray()).ToArray();
                if (_entryLocks.Any())
                {
                    foreach (var userId in _entryLocks.Select(s => s.LockedByUserId))
                    {
                        if (!_entryUsers.Select(s => s.Id).Contains(userId))
                            _entryUsers = _entryUsers.Append(Extric.Towbook.User.GetById(userId));
                    }
                }
            }

            var maxMinCallDates = Entry.GetMaxMinDates(finalList, ShowCompletionDate);
            invoiceStartDate.Value = maxMinCallDates.MinDate.ToShortDateString();
            invoiceEndDate.Value = maxMinCallDates.MaxDate.ToShortDateString();

            rpDispatchCalls.DataSource = finalList;
            rpDispatchCalls.ItemDataBound += new RepeaterItemEventHandler(rpDispatchCalls_ItemDataBound);
            rpDispatchCalls.DataBind();
        }
    }

    void rpDispatchCalls_ItemDataBound(object sender, RepeaterItemEventArgs e)
    {
        if (e.Item.ItemType == ListItemType.Item || e.Item.ItemType == ListItemType.AlternatingItem)
        {
            HtmlInputHidden id = (HtmlInputHidden)e.Item.FindControl("DataId");
            id.Value = ((Entry)e.Item.DataItem).Id.ToString();
        }
    }
    
    public void LogStatementCreation()
    {
        var logEvent = new LogEventInfo();
        logEvent.LoggerName = logger.Name;
        logEvent.Message = "Account Statement Create Log Event";
        logEvent.Level = LogLevel.Info;
        logEvent.TimeStamp = DateTime.Now;
        logEvent.Properties["data"] = new {
            invoices = _invoices != null ? _invoices.Select(i => i.Id).ToArray().ToJson() : null,
            impounds = _impounds != null ? _impounds.Select(i => i.Id).ToArray().ToJson() : null,
            statements = _statements != null ? _statements.Select( s => s.StatementId).ToArray().ToJson() : null,
            accountName = _account.Company
        }.ToString();
        logEvent.Properties["commitId"] = Core.GetCommitId();
        logEvent.Properties["accountId"] = _account.Id;
        logEvent.Properties["companyId"] = WebGlobal.CurrentUser.CompanyId;
        logEvent.Properties["userId"] = WebGlobal.CurrentUser.Id;

        logger.Log(logEvent);
    }

    protected string GetLockAndAuditIconsHtml(object o)
    {
        var html = "";

        if (!ShowLockAndAudit)
            return html;

        var e = o as Entry;
        if (e != null)
        {
            var isTowout = false;
            if(e.Attributes != null && e.Attributes.Any(f => f.Key == Extric.Towbook.Dispatch.AttributeValue.BUILTIN_TOWOUT_CALL))
                isTowout = e.Attributes.FirstOrDefault(f => f.Key == Extric.Towbook.Dispatch.AttributeValue.BUILTIN_TOWOUT_CALL).Value.Value == "1";

            // Check for Locked
            if (e.Company.HasFeature(Extric.Towbook.Generated.Features.Dispatching_LockCalls) && (!e.Impound || e.Released || isTowout))
            {
                var entryLock = _entryLocks.FirstOrDefault(a => a.DispatchEntryId == e.Id);
                if (entryLock != null) {
                    var user = _entryUsers.FirstOrDefault(f => f.Id == entryLock.LockedByUserId);
                    
                    html += "<span><i class=\"fas fa-lock\" title=\"Call #" + e.CallNumber + 
                        " was locked on " + CoreDateTimeExtensions.ToShortDateString(Core.OffsetDateTime(e.Company, entryLock.LockDate), e.Company) + 
                        (user != null && user.Id > 1 ? " by " + Core.HtmlEncode(user.FullName) : "") + ".\"></i></span>";
                }
            }

            // Check for Audit
            if (e.Company.HasFeature(Extric.Towbook.Generated.Features.Dispatching_AuditCalls))
            {
                if (e.Attributes.ContainsKey(Extric.Towbook.Dispatch.AttributeValue.BUILTIN_DISPATCH_AUDITED))
                    html += "<span><i class=\"fas fa-user-check\" title=\"" + AttributeValueExtensions.GetAuditedInfo(e) + "\"></i></span>";
            }
        }

        return html;
    }
}


public static class EntryExtensions
{
    public static decimal LoadedMiles(this Entry s)
    {
        return s.InvoiceItems.Where(o => ((o.Name != null && o.Name.Contains("loaded miles")) ||
            (o.RateItem != null && o.RateItem.Predefined != null && o.RateItem.Predefined.Id == PredefinedRateItem.BUILTIN_MILEAGE_LOADED)) &&
            o.Quantity > 0 && !(o.CustomName ?? "").Contains("FreeQuantity")).Sum(o => o.Quantity);
    }

    public static decimal UnloadedMiles(this Entry s)
    {
        return s.InvoiceItems.Where(o => ((o.Name != null && o.Name.Contains("unloaded miles")) ||
            (o.RateItem != null && o.RateItem.Predefined != null && o.RateItem.Predefined.Id == PredefinedRateItem.BUILTIN_MILEAGE_UNLOADED)) &&
            o.Quantity > 0 && !(o.CustomName ?? "").Contains("FreeQuantity")).Sum(o => o.Quantity);
    }


    public static string CoverageAmount(this Entry s)
    {
        if (s.Attributes.ContainsKey(Extric.Towbook.Dispatch.AttributeValue.BUILTIN_MOTORCLUB_COVERAGELIMIT))
        {
            return s.Attributes[Extric.Towbook.Dispatch.AttributeValue.BUILTIN_MOTORCLUB_COVERAGELIMIT].Value;
        }

        return "-";
    }

    public static string Odometer(this Entry s)
    {
        if (s.Assets.Any()) { return s.Assets.First().Odometer.ToString(); }

        return "-";
    }

}
