<%@ Page Language="C#" AutoEventWireup="true" CodeFile="FileUpload.aspx.cs" Inherits="Accounts_FileUpload" Async="true"%>
<!DOCTYPE html>
<html>
<head>
    <title>Towbook - Account/Upload File</title>
    <link rel="stylesheet" href="/ui/css/towbook.css" />
    <link rel="stylesheet" href="/ui/css/application-forms.css" />

    <style type="text/css">@import url(/ui/js/plupload/jquery.plupload.queue/css/jquery.plupload.queue.css);</style>
    <style type="text/css">
    body { font-family: arial; font-size: 12px; color: black; background-image: none; background-color: white; margin: 0; padding: 0}
    form { margin: 0; padding: 0 }
    .pcontent * { border: none }
    .plupload_container { padding: 0 }
    .plupload_scroll .plupload_filelist { height: 141px }
    
    #advancedUploader {
        width: 100%;
        max-width: 400px; /* 800 */
        text-align: center;
        margin: 0 auto;
    }

    .box {
        font-size: 1.25rem; /* 20 */
        background-color: #c8dadf;
        position: relative;
        padding: 50px 20px;
    }

    .box.has-advanced-upload {
        background-color: transparent;
        outline: 2px dashed black;
        outline-offset: -10px;

        -webkit-transition: outline-offset .15s ease-in-out, background-color .15s linear;
        transition: outline-offset .15s ease-in-out, background-color .15s linear;
    }

    .box.has-advanced-upload .box__dragndrop {
        display: inline;
    }

    .box.is-dragover {
        outline-offset: -20px;
        outline-color: #92b0b3;
        background-color: #d9e5e6;
    }

    .box.is-uploading .box__input {
        visibility: hidden;
    }

    .box.is-uploading .box__uploading {
        display: block;
    }
    .box__dragndrop {
        display: none;
    }

    .box.has-advanced-upload .box__dragndrop {
        display: inline;
    }

    .box.has-advanced-upload .box__icon {
        width: 100%;
        height: 80px;
        fill: #92b0b3;
        display: block;
        margin-bottom: 40px;
    }

    .box.is-uploading .box__input,
    .box.is-success .box__input,
    .box.is-error .box__input {
        visibility: hidden;
    }

    .box__uploading,
    .box__success,
    .box__error {
        display: none;
    }

    .box.is-uploading .box__uploading,
    .box.is-success .box__success,
    .box.is-error .box__error {
        display: block;
        position: absolute;
        top: 70%;
        right: 0;
        left: 0;
        -webkit-transform: translateY( -50% );
        transform: translateY( -50% );
    }

    .box__uploading {
        font-style: italic;
    }

    @@-webkit-keyframes appear-from-inside
	{
		from	{ -webkit-transform: translateY( -50% ) scale( 0 ); }
		75%		{ -webkit-transform: translateY( -50% ) scale( 1.1 ); }
		to		{ -webkit-transform: translateY( -50% ) scale( 1 ); }
	}

	@@keyframes appear-from-inside
	{
		from	{ transform: translateY( -50% ) scale( 0 ); }
		75%		{ transform: translateY( -50% ) scale( 1.1 ); }
		to		{ transform: translateY( -50% ) scale( 1 ); }
	}

    .box__success {
        -webkit-animation: appear-from-inside .25s ease-in-out;
        animation: appear-from-inside .25s ease-in-out;
    }

    .box__restart {
        font-weight: 700;
    }

    .box__restart:focus,
    .box__restart:hover {
        color: #39bfd3;
    }

    .js .box__file {
        width: 0.1px;
        height: 0.1px;
        opacity: 0;
        overflow: hidden;
        position: absolute;
        z-index: -1;
    }

    .js .box__file + label {
        max-width: 80%;
        text-overflow: ellipsis;
        white-space: nowrap;
        cursor: pointer;
        display: inline-block;
        overflow: hidden;
    }

    .js .box__file + label:hover strong,
    .box__file:focus + label strong,
    .box__file.has-focus + label strong {
        color: #39bfd3;
    }

    .js .box__file:focus + label,
    .js .box__file.has-focus + label {
        outline: 1px dotted #000;
        outline: -webkit-focus-ring-color auto 5px;
    }

    .js .box__file + label * {
        /* pointer-events: none; */ /* in case of FastClick lib use */
    }

    .no-js .box__file + label {
        display: none;
    }

    .no-js .box__button {
        display: block;
    }

    .box__button {
        font-weight: 700;
        color: #e5edf1;
        background-color: #39bfd3;
        display: none;
        padding: 8px 16px;
        margin: 40px auto 0;
    }

    .box__button:hover,
    .box__button:focus {
        background-color: #0f3c4b;
    }

    </style>
    
<!-- Load Queue widget CSS and jQuery -->



<!-- Load plupload and all it's runtimes and finally the jQuery queue widget -->
<script src="//ajax.googleapis.com/ajax/libs/jquery/1.7.2/jquery.min.js" type="text/javascript"></script>
<script src="//ajax.googleapis.com/ajax/libs/jqueryui/1.8.14/jquery-ui.min.js" type="text/javascript"></script>
<script type="text/javascript" src="/ui/js/plupload/plupload.full.js"></script>
<script type="text/javascript" src="/ui/js/plupload/jquery.plupload.queue/jquery.plupload.queue.js"></script>


</head>
<body class="towbook-dialog">
<h1>Upload Files</h1>
    <div style="background-color: white">

    <script type="text/javascript">

        var media = [];

        // Convert divs to queue widgets when the DOM is ready
        $(function () {


            // check for browser support of HTML5 file upload
            var isAdvancedUpload = function () {
                var div = document.createElement('div');
                return (('draggable' in div) || ('ondragstart' in div && 'ondrop' in div)) && 'FormData' in window && 'FileReader' in window;
            }();


            if (isAdvancedUpload) {
                $('#advancedUploader').show();
                $('#uploader').hide();
                $('html').addClass('js');

                var $form = $('#dragUpload');
                var inputObj = $form.find('input[type="file"]:eq(0)');
                var $label = $form.find('label');
                var $errorMsg = $form.find('.box__error span');
                var $restart = $form.find('.box__restart');
                var droppedFiles = false;
                var showFiles = function (files) {
                    $label.text(files.length > 1 ? (inputObj.attr('data-multiple-caption') || '').replace('{count}', files.length) : files[0].name);
                    };

                // automatically submit the form on file select
                $(inputObj).on('change', function (e) {
                    showFiles(e.target.files);
                    droppedFiles = e.target.files; // the files that were selected
                    showFiles(droppedFiles);
                    $form.trigger('submit');
                });


                // drag&drop files if the feature is available
                $form.addClass('has-advanced-upload') // letting the CSS part to know drag&drop is supported by the browser

                $form
                    .on('drag dragstart dragend dragover dragenter dragleave drop', function (e) {
                        // preventing the unwanted behaviours
                        e.preventDefault();
                        e.stopPropagation();
                    })
                    .on('dragover dragenter', function () //
                    {
                        $form.addClass('is-dragover');
                    })
                    .on('dragleave dragend drop', function () {
                        $form.removeClass('is-dragover');
                    })
                    .on('drop', function (e) {
                        droppedFiles = e.originalEvent.dataTransfer.files; // the files that were dropped
                        showFiles(droppedFiles);


                        $form.trigger('submit'); // automatically submit the form on file drop


                    });


                // if the form was submitted

                $form.on('submit', function (e) {
                    // preventing the duplicate submissions if the current one is in progress
                    if ($form.hasClass('is-uploading')) return false;

                    $form.addClass('is-uploading').removeClass('is-error');

                    if (isAdvancedUpload) // ajax file upload for modern browsers
                    {
                        e.preventDefault();

                        // promise method to upload !one file!
                        var doUpload = function (formData) {
                            var deferred = new $.Deferred();

                            // ajax request
                            $.ajax(
                                {
                                    url: $form.attr('action'),
                                    type: $form.attr('method'),
                                    data: formData,
                                    cache: false,
                                    contentType: false,
                                    processData: false,
                                    complete: function () {
                                        $form.removeClass('is-uploading');
                                        return deferred.resolve();
                                    },
                                    success: function (data) {
                                        $form.addClass('is-success');
                                        //$form.addClass(data.success == true ? 'is-success' : 'is-error');
                                        //if (!data.success) $errorMsg.text(data.error);
                                    },
                                    error: function (xhr, status, error) {
                                        $form.addClass('is-error');
                                        $errorMsg.text("Upload failed.");
                                        console.log("Error uploading", xhr, status, error);
                                        return deferred.reject(xhr, status, error);
                                    }
                                });

                            return deferred.promise(); 
                        }

                        // gathering the form data
                        //var ajaxData = new FormData($form.get(0));
                        if (droppedFiles) {
                            $.each(droppedFiles, function (i, file) {
                                var ajaxData = new FormData();
                                ajaxData.append("file", file);

                                $.when(doUpload(ajaxData))
                                    .done(function () {

                                    })
                                    .always(function () {
                                        $label.html('<strong>Choose a file</strong><span class="box__dragndrop"> or drag it here</span>.');
                                    });

                            });
                        }
                    }
                    else // fallback Ajax solution upload for older browsers
                    {
                        var iframeName = 'uploadiframe' + new Date().getTime(),
                            $iframe = $('<iframe name="' + iframeName + '" style="display: none;"></iframe>');

                        $('body').append($iframe);
                        $form.attr('target', iframeName);

                        $iframe.one('load', function () {
                            var data = $.parseJSON($iframe.contents().find('body').text());
                            $form.removeClass('is-uploading').addClass(data.success == true ? 'is-success' : 'is-error').removeAttr('target');
                            if (!data.success) $errorMsg.text(data.error);
                            $iframe.remove();
                        });
                    }
                });


                // restart the form if has a state of error/success
                $restart.on('click', function (e) {
                    e.preventDefault();
                    $form.removeClass('is-error is-success');
                    inputObj.trigger('click');
                });

                // Firefox focus bug fix for file input
                inputObj
                    .on('focus', function () { inputObj.addClass('has-focus'); })
                    .on('blur', function () { inputObj.removeClass('has-focus'); });

            }
            else
            {
                $('#uploader').show();
                $('#advancedUploader').hide();
                $('html').removeClass('js');
            }









            $("#uploader").pluploadQueue({
                // General settings
                runtimes: 'gears,flash,silverlight,browserplus,html5',
                url: 'FileUpload.aspx?id=<% =Convert.ToInt32(Request.QueryString["id"]) %>&file=<% =Convert.ToInt32(Request.QueryString["files"] ?? "0") %>',
                max_file_size: '10mb',
                unique_names: true,

                // Resize images on clientside if we can
                resize: { width: 960, height: 720, quality: 90 },
                urlstream_upload: true,
                multiple_queues: true,

                // Specify what files to browse for
                filters: [
			    { title: "All files", extensions: "*" }
		],

                // Flash settings
                flash_swf_url: '/ui/js/plupload/plupload.flash.swf',

                // Silverlight settings
                silverlight_xap_url: '/ui/js/plupload/js/plupload.silverlight.xap'
            });

            // Client side form validation
            $('#form1').submit(function (e) {
                var uploader = $('#uploader').pluploadQueue();

                // Validate number of uploaded files
                if (uploader.total.uploaded == 0) {
                    // Files in queue upload them first
                    if (uploader.files.length > 0) {
                        // When all files are uploaded submit form
                        uploader.bind('UploadProgress', function () {
                            if (uploader.total.uploaded == uploader.files.length)
                                $('form').submit();
                        });

                        uploader.start();
                    } else
                        alert('You must at least upload one file.');

                    e.preventDefault();
                }
            });
        });
</script>
    <form id="form1" runat="server" target="_top">
	<div id="uploader">
		<p>You browser doesn't have Flash, Silverlight, Gears, BrowserPlus or HTML5 support.</p>
	</div>
    <div style="margin-top: 15px; margin-bottom: 10px; display: none" class="pcontent">
        
            Browse for Filegraph:<br />
            <asp:FileUpload runat="Server" ID="fuUpload" style="border: dotted 1px #afafaf; width: 550px " />
            <asp:RequiredFieldValidator ID="RequiredFieldValidator1" runat="server" 
                ErrorMessage="Please choose a Filegraph file to upload." 
                ControlToValidate="fuUpload" Display="None"></asp:RequiredFieldValidator>
            <br /><br />
        
            Description:<br />
            <asp:TextBox runat="server" ID="txtDescription" MaxLength="150" style="border: dotted 1px #afafaf; width: 550px "/><br /><br /><br />
        
            Filegraphs must be in JPG, PNG, GIF, or BMP format. TIFF files are not supported<br /><br />
            Note: Please click the Upload button only once.
            <asp:ValidationSummary ID="ValidationSummary1" runat="server" 
                ShowMessageBox="True" ShowSummary="False" />
    </div>
    </form>

    <div id="advancedUploader">
        <form method="post" action="/api/accounts/<% =Convert.ToInt32(Request.QueryString["id"]) %>/file" enctype="multipart/form-data" novalidate class="box" id="dragUpload">
            <svg class="box__icon" xmlns="http://www.w3.org/2000/svg" width="50" height="43" viewBox="0 0 50 43"><path d="M48.4 26.5c-.9 0-1.7.7-1.7 1.7v11.6h-43.3v-11.6c0-.9-.7-1.7-1.7-1.7s-1.7.7-1.7 1.7v13.2c0 .9.7 1.7 1.7 1.7h46.7c.9 0 1.7-.7 1.7-1.7v-13.2c0-1-.7-1.7-1.7-1.7zm-24.5 6.1c.******* ******* 0 .9-.2 1.2-.5l10-11.6c.7-.7.7-1.7 0-2.4s-1.7-.7-2.4 0l-7.1 8.3v-25.3c0-.9-.7-1.7-1.7-1.7s-1.7.7-1.7 1.7v25.3l-7.1-8.3c-.7-.7-1.7-.7-2.4 0s-.7 1.7 0 2.4l10 11.6z" /></svg>
            <div class="box__input">
                <input type="file" name="file" id="file" class="box__file" data-multiple-caption="{count} files selected" multiple />
                <label for="file"><strong>Choose a file</strong><span class="box__dragndrop"> or drag it here</span>.</label>
                <button type="submit" class="box__button">Upload</button>
            </div>


            <div class="box__uploading">Uploading&hellip;</div>
            <div class="box__success">Done! <a href="" class="box__restart" role="button">Upload more?</a></div>
            <div class="box__error">Error! <span></span>. <a href="" class="box__restart" role="button">Try again!</a></div>
        </form>
    </div>




  <div class="towbook-dialog-footer">
  <input type="button" id="btnCancel" value="Close" class="button" onclick="parent.location.reload()" />    
  </div>
    
</body>
</html>
