var StatementsView = {
  isConnectedToAccountingProvider: false,
  enableMotorClubBilling: false,

  // This is the data that is loaded from the API.
  // It gets run through this.formatData() and becomes this.grid.records
  data: [],

  // The w2ui grid that gets created
  grid: {},

  createGrid: function () {
    $('#statementsGrid').w2grid({
      name: 'statementsGrid',
      autoload: false,
      leftMouseClickOnly: true,       // allow right click
      localSearchOnEnterKey: false,   // filter the grid results without having to hit 'enter'
      selectType: 'none',
      fixedBody: false,
      fixedBodyLimit: 5,
      fixedBodyHeight: 500,
      multiSelect: false,
      multiSearch: true,
      itemName: 'statement',
      itemNamePlural: 'statements',
      loadingMore: false,
      stopLoadMore: false,
      search: false,
      pageNumber: 1,
      show: {
        selectionBorder: false,
        toolbar: true,
        toolbarReload: false,
        toolbarPrint: true,
        toolbarExport: true,
        toolbarSearch: false,
        noRecordsMessage: true,
      },

      onColumnOnOff: function (event) {
        event.onComplete = function () {
          StatementsView.grid.stateSave();
        }
      },

      onColumnResize: function (event) {
        event.onComplete = function () {
          StatementsView.grid.stateSave();
        }
      },

      onClick: function (event) {
        this.toggle(event.recid);
      },

      onExpand: function (event) {
        var self = this;
        var record = $.grep(this.records, function (r) { return event.recid == r.recid })[0];

        // This is a just-in-case check to root out duplicates or the record moved
        if (record == null) {
          event.isCancelled = true;
          return;
        }

        event.dropNavHtml = self.generateLinks(record);

        event.onComplete = function () {
          Towbook.applyUIBehaviour();
          self.registerEditorClickHandlers();
        }
      },

      onLoadMore: function (event) {
        //if (!this.search && !this.loadingMore) {
        //  this.loadingMore = true;
        //  this.pageNumber++;
        //  var tab = this.getCurrentTab();
        //  log("Loading more results (tab=" + tab.name + "/pageNumber=" + this.pageNumber + ")");
        //  this.refreshGrid(true, tab.id, this.pageNumber);
        //}
      },

      registerEditorClickHandlers: function () {
        $('.editor-link').click(function (event) {
          event.stopPropagation();

          // If the user didn't hold the ctrl key 
          // (meaning they wish to open the editor in a separate tab)
          if (!event.originalEvent.ctrlKey) {

            // Open the editor on-screen
            event.preventDefault();
            loadEditor($(this).attr('href'));
          }
        });
      },

        generateLinks: function (statement) {
            var links = "";
            if (statement.sourceId != 4) {
                links += '<a href="Statement.aspx?id=' + statement.recid + '&pdf=1" target="_blank"><i class="fa fa-file-alt"></i>View</a>';
                if (StatementsView.grid.getCurrentTab().id == 1) {
                    links += '<a href="AccountStatement.aspx?id=' + statement.recid + '&accountId=' + statement.accountId + '"><i class="fa fa-pencil"></i>Modify</a>';
                }
                links += '<a rel="towbook-dialog" data-dialog-height="700" href="SendEmail.aspx?id=' + statement.accountId + '&type=email&statementId=' + statement.recid + '"><i class="fa fa-envelope"></i>Send via Email</a>';
                if (statement.emailHistory.length > 0) {
                    links += '<a href="#" onclick="StatementsView.showEmailHistory(this, ' + statement.recid + ');return false;"><i class="fa fa-envelope"></i>View Emails</a>';
                }
                links += '<a href="AccountStatement.aspx?id=' + statement.recid + '&accountId=' + statement.accountId + '&action=delete" onclick="w2utils.lock(w2ui.statementsGrid.box, \'Please wait\', true); return confirm(\'Are you sure you want to delete this statement?\')"><i class="fa fa-times"></i>Delete</a>';
                if ((towbook.currentUser.type == 1 || towbook.currentUser.type == 4) && StatementsView.isConnectedToAccountingProvider) {
                    links += '<a href="PushStatement.aspx?id=' + statement.recid + '"><i class="fa fa-sign-out"></i>Push to QuickBooks</a>';
                }
            } else {
                links += '<a rel="towbook-dialog" data-dialog-height="600" href="SubmissionStatus.aspx?id=' + statement.recid + '"><i class="fa fa-binoculars"></i>View Status Details</a>';
            }
            return links;
        },

      getTab: function (id) {
        return this.tabs.filter(function (t) { return t.id == id })[0];
      },

      getCurrentTab: function () {
        return this.tabs[$(StatementsView.grid.box).find('.w2ui-grid-tabs > a.selected').index()];
      },

      loadColumns: function (tabId) {
        this.columns = $.grep(this.columnDefs, function (o) {
          return o.tab == tabId || o.tab == 'All';
        });
      },

      tabs: [{
        id: 1,
        name: (StatementsView.enableMotorClubBilling ? 'Invoice Submissions' : 'Unpaid'),
        realName: (StatementsView.enableMotorClubBilling ? 'Invoice Submissions' : 'Unpaid'),
        selected: true,
        onClick: function (e) {
          event.preventDefault();
          StatementsView.grid.loadColumns(this.id);
          StatementsView.grid.search = false;
          StatementsView.grid.loadingMore = false;
          StatementsView.grid.stopLoadMore = false;
          StatementsView.grid.refreshGrid(false, 1);
        }
      }, {
        id: 2,
        name: 'Paid',
        realName: 'Paid',
        onClick: function (e) {
          event.preventDefault();
          StatementsView.grid.loadColumns(this.id);
          StatementsView.grid.search = false;
          StatementsView.grid.loadingMore = false;
          StatementsView.grid.stopLoadMore = false;
          StatementsView.grid.refreshGrid(false, 2);
        }
      }],

      columnDefs: [
        { tab: 'All', field: 'statementNum', caption: (StatementsView.enableMotorClubBilling ? 'Batch #' : 'Statement #'), size: '120px', sortable: true, searchable: 'string' },
        {
          tab: 'All', field: 'statementDate', caption: (StatementsView.enableMotorClubBilling ? 'Submission Date' : 'Statement Date'), size: '130px', sortable: true, searchable: 'date',
          render: function (o) { return '<div>' + towbook.formatDate(o.statementDate) + "</div>" }
        },
        {
          tab: 'All', field: 'dueDate', caption: 'Due Date', size: '120px', sortable: true, searchable: 'date', hidden: (StatementsView.enableMotorClubBilling ? true : false),
          render: function (o) { return '<div>' + towbook.formatDate(o.dueDate) + "</div>" }
        },
        {
          tab: 'All', field: 'balance', caption: 'Original Amt.', size: '115px', sortable: true, searchable: 'float',
          render: function (o) { return '<div>' + towbook.formatMoney(o.balance) + "</div>" }
        },
        {
          tab: 1, field: 'nowBalance', caption: 'Current Amt. Due', size: '140px', sortable: true, searchable: 'float',
          render: function (o) { return '<div>' + towbook.formatMoney(o.nowBalance) + "</div>" }
        },
        { tab: 1, field: 'status', caption: 'Status', size: '140px', sortable: true, searchable: 'string', hidden: (StatementsView.enableMotorClubBilling ? false : true) },
        {
          tab: 'All', field: 'emailHistory', caption: 'Emails', min: '75', size: '75px', sortable: false, render: function (record) {
            return (record.emailHistory.length > 0 ?
              '<div class="emails-icon" onclick="StatementsView.showEmailHistory(this, ' + record.id + ')"><i class="fa fa-envelope"></i></div>' : '');
          }
        },
      ],

      formatData: function (data) {
        return $.map(data, function (o) {
          return {
            id: o.id,
            recid: o.id,
            accountId: o.accountId,
            statementNum: moment(o.createDate).format('YY') + '-' + o.id.padLeft(7),
            statementDate: o.statementDate,
            dueDate: o.dueDate,
            ownerUserId: o.ownerUserId,
            ownerUser: o.ownerUser,
            paidInFull: o.paidInFull,
            createDate: o.createDate,
            deleted: o.deleted,
            dispatchEntries: o.dispatchEntries,
            invoices: o.invoices,
            total: o.total,
            subtotal: o.subtotal,
            taxes: o.taxes,
            balance: o.balance,
            nowBalance: o.nowBalance,
            status: o.status,
            emailHistory: o.emailHistory || [],
            sourceId: o.sourceId
          };
        });
      },

      refreshGrid: function (fromAPI, tab) {
        var self = StatementsView.grid;
        if (self.stopLoadMore)
          return;

        if (!fromAPI) {
          if (tab == 1) {
            self.records = self.formatData(StatementsView.data.filter(function(o) { return o.nowBalance > 0 })); // Unpaid
            self.localSort();
            self.refresh();
            self.registerEditorClickHandlers();
          } else {
            self.records = self.formatData(StatementsView.data.filter(function (o) { return o.nowBalance == 0 })); // Paid
            self.localSort();
            self.refresh();
            self.registerEditorClickHandlers();
          }
          self.refreshTabCounts();
        } else {

          //// Get the URL for the API
          //var url =
          //  "/api/accounts/" + accountId + "/statements?companyId=" + companyId;

          //log("Calling API: " + url);

          //// Get the data from the API
          //$.ajax({
          //  url: url,
          //  beforeSend: function () { StatementsView.grid.lock(this.search ? 'Searching...' : 'Loading...', true); },
          //  complete: function () { StatementsView.grid.unlock(); }
          //}).done(function (data) {
          //  var self = StatementsView.grid;

          //  if (self.loadingMore && data.length == 0) {
          //    self.stopLoadMore = true;
          //    self.loadingMore = false;
          //  } else {

          //    // Load/append the data
          //    if (self.loadingMore) {
          //      StatementsView.data = StatementsView.data.concat(data);
          //      self.records = self.records.concat(self.formatData(data));
          //    }
          //    else {
          //      StatementsView.data = data;
          //      self.records = self.formatData(data);
          //    }

          //    // Refresh the grid
          //    log("statementsGrid now has " + StatementsView.data.length + " records to display");
          //    self.localSort();
          //    self.refresh();
          //    self.search = false;
          //    self.loadingMore = false;
          //  }
          //});
        }
      },

      refreshTabCounts: function () {
        var unpaidCount = StatementsView.data.filter(function (o) { return o.nowBalance > 0 }).length;
        var paidCount = StatementsView.data.filter(function (o) { return o.nowBalance == 0 }).length;
        this.tabs[0].name = this.tabs[0].realName + (unpaidCount == 0 ? "" : " (" + unpaidCount + ")");
        this.tabs[1].name = this.tabs[1].realName + (paidCount == 0 ? "" : " (" + paidCount + ")");
        $($('#statementsGrid .w2ui-grid-tabs a')[0]).text(this.tabs[0].name);
        $($('#statementsGrid .w2ui-grid-tabs a')[1]).text(this.tabs[1].name);
      }
    });

    this.grid = w2ui.statementsGrid;

    // Populate first tab with columns
    this.grid.loadColumns(1);

    // Populate the grid with initial data
    this.grid.refreshGrid(false, 1);
  },

  showEmailHistory: function (me, statementId) {
    // Show email history for this statement
    var hist = this.data.filter(function (d) { return d.id == statementId })[0];
    if (hist) {
      fancyMessage('Email History', ['Sent by', 'Email', 'Sent', 'Delivery Status'], hist.emailHistory.map(function (h) {
        var user = towbook.get(towbookUsers, h.userId);
        return '<tr><td>' + (user != null ? user.name : "") + '</td><td>' + h.emailAddress + '</td><td>' + towbook.formatDate(h.createDate) + ' ' + towbook.formatAMPM(h.createDate) + '</td><td>' + h.statusMessage + '</td></tr>';
      })
      );
    }
    event.stopPropagation();
  }
}