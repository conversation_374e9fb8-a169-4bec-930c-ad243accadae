using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using Extric.Towbook.Accounts;
using Extric.Towbook.API.Models;
using Extric.Towbook.Integration;
using Extric.Towbook.Utility;
using Extric.Towbook.WebShared;
using System.Threading.Tasks;
using Microsoft.Azure.Cosmos.Linq;

public partial class Accounts_PrepareStatements : System.Web.UI.Page
{
    protected string accountsJson = "";
    protected DateTime initialDueDate;
    protected IEnumerable<Extric.Towbook.Dispatch.Reason> reasons;
    protected void Page_Load(object sender, EventArgs e)
    {
        this.Master.CurrentSection = Navigation.NavigationItemEnum.Accounts;
        this.Master.InnerTitle = "Prepare Statements for Multiple Accounts";
        this.Master.UseJquery = true;
        this.Master.UseReact = true;

        RegisterAsyncTask(new PageAsyncTask(PageLoadAsync));
    }

    protected async Task PageLoadAsync()
    {
        reasons = await Extric.Towbook.Dispatch.Reason.GetByCompany(Global.CurrentUser.Company, false);

        var models = await AccountModel.MapAsync(await Account.GetByCompanyAsync(WebGlobal.CurrentUser.Company, true, null), true);

        models = FillAccountModels(models).ToArray();

        if (await WebGlobal.CurrentUser.Company.HasFeatureAsync(Extric.Towbook.Generated.Features.AdvancedBilling))
        {
            var accountStatementOptions = StatementOption.GetByAccountIds(models.Select(s => s.Id).ToArray());

            // initialize statement initial due date by account setting option
            foreach (var m in models)
            {
                var so = accountStatementOptions.FirstOrDefault(f => f.AccountId == m.Id);
                if (so != null &&
                    m.StatementPreferences != null)
                {
                    m.StatementPreferences.InitialDueDate = DueDateDefaultModel.Map(so.DefaultDueDateType);
                }
            }

            var cso = StatementOption.GetByCompanyI0(WebGlobal.CurrentUser.CompanyId);
            if (cso != null)
                initialDueDate = Statement.GetNextStatementDueDate(cso.DefaultDueDateType);
        }

        this.accountsJson = models.ToJson();
    }

    private IEnumerable<AccountModel> FillAccountModels(IEnumerable<AccountModel> accounts)
    {
        var accountKeyNames = new Collection<string>(){ 
                        "PreferredBillingMethod",
                        "PreferredStatementDeliveryMethod"
        };

        var customFields = new Collection<AccountKeyValue>();
            
        if(WebGlobal.CurrentUser.Company.HasFeature(Extric.Towbook.Generated.Features.AdvancedBilling))
            customFields = AccountKeyValue.GetByCompany(WebGlobal.CurrentUser.CompanyId, Provider.Towbook.ProviderId, accountKeyNames.ToArray()).ToCollection();

        return accounts.Select(a => AccountModel.FillKeyValues(a, customFields.Where(w => w.AccountId == a.Id), WebGlobal.CurrentUser));
    }
}
