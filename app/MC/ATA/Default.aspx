<%@ Page Language="C#" AutoEventWireup="true" CodeFile="Default.aspx.cs" Inherits="ATA_Status"  MasterPageFile="~/UI/TowbookV2.master" Title="Recent Actual Time of Arrival vs ETA's" %>

<%@ MasterType TypeName="UI_Towbook" %>
<%@ Import Namespace="Extric.Towbook.Utility" %>
<asp:Content ID="Content1" ContentPlaceHolderID="content" runat="Server">


This report lists your average actual time of arrival in comparison to the average ETA given.

<form method="get">
<select name="masterAccountId"> 
<option value="">(all)</option>
<% foreach(var x in Extric.Towbook.Accounts.MasterAccount.GetByAccountTypeId(5).Where(o => o.Id == 1 || o.Id == 2 || o.Id == 3 || o.Id == 4 || o.Id == 5 || o.Id == 29).OrderBy(o => o.Name)) { %> 
<option value="<% = x.Id %>"  <% if (this.MasterAccountId == x.Id) {%>selected<% } %>><% = HttpUtility.HtmlEncode(x.Name) %></option>
<%}%>
</select>
<select name="timeframe">
<option value="-1" <% if (TimeframeHours == -1) {%>selected<% } %>>Last 1 hr</option>
<option value="-2" <% if (TimeframeHours == -2) {%>selected<% } %>>Last 2 hrs</option>
<option value="-4" <% if (TimeframeHours == -4) {%>selected<% } %>>Last 4 hrs</option>
<option value="-8" <% if (TimeframeHours == -8) {%>selected<% } %>>Last 8 hrs</option>
<option value="-24" <% if (TimeframeHours == -8) {%>selected<% } %>>Last 24 hrs</option>
<option value="-168" <% if (TimeframeHours == -168) {%>selected<% } %>>Last 7 Days</option>
</select>
<input type="submit" value="Update" />
</form>

<table class="list sortable">
<thead>
<th class="sorttable_alpha">Company</th>
<th>ATA</th>
<th>ETA</th>
<th style="max-width: 75px">Difference</th>
<th style="max-width: 75px">Min ATA</th>
<th style="max-width: 75px">Max ATA</th>
<th>Number of Calls</th>
</thead>
<%

    var spName = "internalGetAtasByCompanyIdByAccount";
    if ( Global.CurrentUser.PrimaryCompanyId == 26130)
        foreach(var row in
        SqlMapper.QuerySP<dynamic>(spName, new
        {
            companyId = Global.CurrentUser.PrimaryCompanyId,
            timeframeHours = (Request.QueryString["timeframe"] != null ? Convert.ToInt32(Request.QueryString["timeframe"]) : -2),
            masterAccountId = (Request.QueryString["masterAccountId"] != null && Request.QueryString["masterAccountId"] != "" ? Convert.ToInt32(Request.QueryString["masterAccountId"]) : (int?) null)
        })) {

%>
<tr>
<td><%=HttpUtility.HtmlEncode(row.CompanyName ?? row.AccountName) %></td>
<td><%=row.AverageAta %> </td>
<td><%=row.AverageEta %> </td>
<td><%=row.AverageAtaDifference %> </td>
<td><%=row.MinAta %> </td>
<td><%=row.MaxAta %> </td>
<td><%=row.Calls %> </td>
</tr>
<%
}
%>
</table>

</asp:Content>