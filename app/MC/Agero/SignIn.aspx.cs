using Extric.Towbook;
using Extric.Towbook.Utility;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using Agero.Types;
using Agero;

public partial class MC_Agero_SignIn : System.Web.UI.Page
{
    protected void Page_Load(object sender, EventArgs e)
    {
        this.Master.CurrentSection = Navigation.NavigationItemEnum.Nothing;
        this.Master.InnerTitle = "Confirmation";
        this.Master.UseJquery = true;

        var r = new Agero.AgeroRestClient();
        var a = Extric.Towbook.Accounts.Account.GetById(Convert.ToInt32(Request.QueryString["id"]));

        var ac = AgeroSession.GetByAccountId(a.Id);

        try
        {
            r.OAuthSignIn(Global.CurrentUser.PrimaryCompanyId.ToString(), ac.AccessToken);
        }
        catch (AgeroException ae)
        {
            if (ae.Message == "Access Token not approved")
            {
                ac.Delete();
                Response.Write("<PERSON><PERSON> returned an error. Please re-connect your Agero account and try again.");
                Response.Write("<a href=\"/Accounts/Account.aspx?id=" + a.Id + "\">Click here to return to Agero Account page</a>");
                Response.End();
            }
            else
            {
                throw;
            }
        }
        

        ac.SignedIn = true;
        ac.Save();

        Response.Redirect("/Accounts/Account.aspx?id=" + a.Id);
    }
}