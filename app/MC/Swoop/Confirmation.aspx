<%@ Page Language="C#" AutoEventWireup="true" CodeFile="Confirmation.aspx.cs" Inherits="MC_Swoop_Confirmation"  MasterPageFile="~/UI/TowbookV2.master" Title="Connected to Agero (Swoop)" Async="True" %>

<%@ MasterType TypeName="UI_Towbook" %>

<asp:Content ID="Content1" ContentPlaceHolderID="content" runat="Server">
    <style>#pageHolder-title { display:none}</style>
    <h3>Congratulations!</h3>

    <p>You successfully connected Towbook to Agero (Swoop).</p>

<div style="min-height: 300px">

<a href="/Accounts/Account.aspx?id=<% =Extric.Towbook.Core.GetRedisValue("swoop:register:" + Global.CurrentUser.CompanyId) %>">Return to Account list</a>
</div>

</asp:Content>