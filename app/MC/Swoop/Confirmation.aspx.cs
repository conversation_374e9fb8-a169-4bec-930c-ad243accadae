using System;
using System.Linq;
using System.Web.UI;
using System.Web.UI.WebControls;
using Extric.Towbook.Integrations.MotorClubs.Swoop;
using Extric.Towbook.Integrations.MotorClubs.Swoop.Model;
using System.Threading.Tasks;
using Extric.Towbook.Accounts;

public partial class MC_Swoop_Confirmation : System.Web.UI.Page
{
    protected Account account;
    protected void Page_Load(object sender, EventArgs e)
    {
        System.Net.ServicePointManager.SecurityProtocol = System.Net.SecurityProtocolType.Tls12;

        this.Master.CurrentSection = Navigation.NavigationItemEnum.Nothing;
        this.Master.InnerTitle = "Confirmation";
        this.Master.UseJquery = true;

        var accountId = Extric.Towbook.Core.GetRedisValue("swoop:register:" + Global.CurrentUser.CompanyId);

        if (accountId == null)
            throw new Exception("no registration to finalize");

        account = Account.GetById(Convert.ToInt32(accountId));

        RegisterAsyncTask(new PageAsyncTask(PageLoadAsync));
    }

    protected async Task PageLoadAsync()
    {
        if (account != null)
        {
            int accountId = account.Id;
            var s = SwoopSession.GetByAccountId(accountId);
            if (s == null)
            {

                if (Request.QueryString["code"] == null)
                    throw new Exception("Missing code parameter. Did you follow an invalid link?");

                var src = new SwoopRestClient();

                var token = src.OAuthAccessToken(Request.QueryString["code"]);

                s = new SwoopSession()
                {
                    CompanyId = Global.CurrentUser.CompanyId,
                    AccountId = accountId,
                    AccessToken = token.AccessToken,
                    RefreshToken = token.RefreshToken,
                    ProviderId = Global.CurrentUser.CompanyId.ToString(),
                    OwnerUserId = Global.CurrentUser.Id
                };
                s.Save();

                s.WebhookSecret = Guid.NewGuid().ToString("N").ToLower();

                foreach (var x in await src.GetSubscriptions(s.AccessToken))
                {
                    await src.DeleteSubscription(s.AccessToken, x.Id);
                }

                // add webhook subscription
                await src.AddSubscription(s.AccessToken, "https://agsmc.api.towbook.com/notify/", s.WebhookSecret);
                s.Save();

                if (Global.CurrentUser.PrimaryCompanyId != 36649 &&
                    Global.CurrentUser.PrimaryCompanyId != 2015 &&
                    Global.CurrentUser.PrimaryCompanyId != 53053 &&
                    Global.CurrentUser.PrimaryCompanyId != 26130)
                {
                    // add a site if it doesn't already exist. 
                    var user = (await src.GetUsersAsync(s.AccessToken)).Where(o => o.Roles.Contains("admin")).FirstOrDefault().Id;
                    var existingSite = (await src.GetSites(s.AccessToken)).Where(o => o.Name == Global.CurrentUser.Company.Name).FirstOrDefault();
                    if (existingSite == null)
                    {
                        var primarySite = await src.CreateSite(s.AccessToken, new PartnerSite()
                        {
                            Name = Global.CurrentUser.Company.Name,
                            Address = Global.CurrentUser.Company.Address + ", " +
                                Global.CurrentUser.Company.City + " " +
                                Global.CurrentUser.Company.State + " " +
                                Global.CurrentUser.Company.Zip
                        }, user);

                        var ts = new SwoopSite()
                        {
                            CompanyId = s.CompanyId,
                            AccountId = s.AccountId,
                            SiteId = primarySite.Id,
                            IpAddress = Extric.Towbook.WebShared.WebGlobal.GetRequestingIp(),
                            OwnerUserId = Global.CurrentUser.Id,
                            SwoopSessionId = s.SwoopSessionId
                        };

                        ts.Save();
                    }
                    else
                    {
                        var tsr = new SwoopSite()
                        {
                            CompanyId = s.CompanyId,
                            AccountId = s.AccountId,
                            SiteId = existingSite.Id,
                            IpAddress = Extric.Towbook.WebShared.WebGlobal.GetRequestingIp(),
                            OwnerUserId = Global.CurrentUser.Id,
                            SwoopSessionId = s.SwoopSessionId
                        };

                        tsr.Save();
                    }

                    var companies = Extric.Towbook.Company.SharedCompany.GetByCompanyId(Global.CurrentUser.PrimaryCompanyId);
                    var sites = await src.GetSites(s.AccessToken);

                    if (companies.Any())
                    {
                        foreach (var company in companies)
                        {
                            var comp = Extric.Towbook.Company.Company.GetById(company.SharedCompanyId);

                            var ma = Extric.Towbook.Accounts.Account.GetByCompany(comp, Extric.Towbook.Accounts.AccountType.MotorClub)
                                .Where(o => o.MasterAccountId == 29).FirstOrDefault();

                            if (ma != null)
                            {
                                var site = sites.Where(o => o.Name == comp.Name ||
                                    o.Address.ToLower().Trim().Replace(",", " ").Replace(" street", " st").Replace(" west", " w").Replace(" east", " e").Replace("  ", " ").Trim().StartsWith(
                                    comp.Address.ToLower().Replace(",", "").Replace(" street", " st").Replace(" west", " w").Replace(" east", " e") + " " +
                                    comp.City.ToLower().Trim() + " " +
                                    comp.State.ToLower().Trim())).FirstOrDefault();

                                if (site == null)
                                    site = sites.Where(o =>
                                        o.Address.Contains(comp.City) &&
                                        o.Address.Contains(comp.State) &&
                                        o.Address.Contains(comp.Zip)).FirstOrDefault();

                                if (site == null)
                                    site = sites.Where(o =>
                                        o.Address.Contains(comp.City) &&
                                        o.Address.Contains(comp.State)).FirstOrDefault();

                                if (site == null)
                                {
                                    if (true)
                                    {
                                        var newSite = await src.CreateSite(s.AccessToken,
                                            new PartnerSite()
                                            {
                                                Name = comp.Name,
                                                Address = comp.Address + ", " + comp.City + " " + comp.State + " " + comp.Zip
                                            },
                                            user);

                                        var towbookSite = new SwoopSite()
                                        {
                                            CompanyId = comp.Id,
                                            AccountId = ma.Id,
                                            SiteId = newSite.Id,
                                            IpAddress = Extric.Towbook.WebShared.WebGlobal.GetRequestingIp(),
                                            OwnerUserId = Global.CurrentUser.Id,
                                            SwoopSessionId = s.SwoopSessionId
                                        };

                                        towbookSite.Save();
                                    }

                                }
                                else
                                {
                                    var existingSite2 = SwoopSite.GetBySiteId(s.SwoopSessionId, site.Id);
                                    if (existingSite2 == null)
                                    {
                                        var towbookSite = new SwoopSite()
                                        {
                                            CompanyId = comp.Id,
                                            AccountId = ma.Id,
                                            SiteId = site.Id,
                                            OwnerUserId = Global.CurrentUser.Id,
                                            IpAddress = Extric.Towbook.WebShared.WebGlobal.GetRequestingIp(),
                                            SwoopSessionId = s.SwoopSessionId
                                        };

                                        towbookSite.Save();
                                    }
                                }
                            }
                        }
                    }
                }
                else
                {
                    this.Master.InnerTitle = "Already Connected";
                }

                var Companies = Extric.Towbook.Company.CompanyUser.GetByUserId(Global.CurrentUser.Id).Select(o => Extric.Towbook.Company.Company.GetById(o.CompanyId)).ToList().OrderBy(o => o.ShortName).ToArray();
                var key = "global:accounts:" + string.Join(",", Companies.Select(o => o.Id).OrderBy(o => o));
                await Extric.Towbook.Core.DeleteRedisKeyAsync(key);
            }
        }
    }
}