using System;
using System.Web;
using Extric.Towbook;

public static class AppExtensions
{
    public static bool CheckAuthentication(this HttpRequest request, int currentCompanyId = 0)
    {
        var token = request.Headers["X-Api-Token"];
        if (token != null)
        {
            var authToken = AuthenticationToken.GetByToken(token);
            if (authToken == null || authToken.ExpirationDate < DateTime.Now)
                throw new Exception("Invalid access");

            if (currentCompanyId > 0)
            {
                var usr = User.GetById(authToken.UserId);
                if (usr == null || !usr.HasAccessToCompany(currentCompanyId))
                    throw new Exception("Invalid access.");
            }

            return true;
        }

        return false;
    }
}




