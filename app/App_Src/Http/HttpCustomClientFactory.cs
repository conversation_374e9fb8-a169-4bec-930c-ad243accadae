using System;
using System.Net;
using System.Net.Http;

public static class HttpCustomClientFactory
{
    /// <summary>
    /// A static constructor always runs before any other access to any other 
    /// static member of the class. Meaning this will run to configure the 
    /// static ServicePointManager before the creation of any HttpClient takes 
    /// place. This specifically takes care of limiting a pool of connections as 
    /// well as configuring the closing up of idle connections.
    /// </summary>
    static HttpCustomClientFactory()
    {
        // Set global settings
        ServicePointManager.DefaultConnectionLimit = 10;
        ServicePointManager.MaxServicePointIdleTime = (int)
            TimeSpan.FromSeconds(30).TotalMilliseconds;
    }

    /// <summary>
    /// Creates and configures a new instance of <see cref="HttpClient"/> with custom settings.
    /// </summary>
    /// <returns>A configured <see cref="HttpClient"/> instance.</returns>
    /// <remarks>
    /// This method creates a new <see cref="HttpClient"/> instance using an analogous approach to what's 
    /// a <see cref="SocketsHttpHandler"/> in .NET Core with
    /// custom connection pooling settings. The connection lifetime and idle timeout 
    /// are configured to optimize connection reuse and performance and avoid port 
    /// exhaustion.
    /// </remarks>
    /// <example>
    /// <code>
    /// var httpClient = CreateHttpClient();
    /// var response = await httpClient.GetAsync("https://api.example.com/data");
    /// </code>
    /// </example>
    public static (HttpClient client, HttpClientHandler handler) CreateHttpClient()
    {
        var handler = new CustomHttpClientHandler(TimeSpan.FromMinutes(5));

        var httpClient = new HttpClient(handler)
        {
            Timeout = TimeSpan.FromSeconds(100),
        };

        return (httpClient, handler);
    }

    /// <summary>
    /// Sets the connection lease timeout for the specified <see cref="HttpClient"/> instance.
    /// </summary>
    /// <param name="httpClient">The <see cref="HttpClient"/> instance for which to set the connection lease timeout.</param>
    /// <param name="leaseTimeout">The <see cref="TimeSpan"/> representing the lease timeout duration.</param>
    /// <remarks>
    /// This method configures the <see cref="ServicePoint.ConnectionLeaseTimeout"/> property for the <see cref="ServicePoint"/>
    /// associated with the <see cref="HttpClient"/> instance's base address. Setting a connection lease timeout can help
    /// manage the lifecycle of connections, ensuring that they are periodically refreshed to avoid potential issues with
    /// stale or long-lived connections.
    /// It is worth noting this isn't currently under use since it's a really rudimentary 
    /// approach in the sense that you would have to configure manually the refresh 
    /// behavior depending on the URL, however it will be kept here for reference.
    /// </remarks>
    /// <example>
    /// <code>
    /// var httpClient = new HttpClient();
    /// SetConnectionLeaseTimeout(httpClient, TimeSpan.FromMinutes(5));
    /// </code>
    /// </example>
    private static void SetConnectionLeaseTimeout(
        HttpClient httpClient,
        TimeSpan leaseTimeout
    )
    {
        // Set ConnectionLeaseTimeout for each ServicePoint
        httpClient.DefaultRequestHeaders.ConnectionClose = false;
        httpClient.DefaultRequestHeaders.Connection.Add("keep-alive");

        httpClient
            .SendAsync(
                new HttpRequestMessage(HttpMethod.Head, "http://example.com")
            )
            .ContinueWith(task =>
            {
                if (task.Result != null)
                {
                    var servicePoint = ServicePointManager.FindServicePoint(
                        task.Result.RequestMessage.RequestUri
                    );
                    servicePoint.ConnectionLeaseTimeout = (int)
                        leaseTimeout.TotalMilliseconds;
                }
            });
    }
}
