using System;
using System.Collections.Generic;
using System.Net.Http;

public class DefaultHttpClientFactory : IHttpClientFactory
{
    private readonly Dictionary<string, string> _urls = new Dictionary<string, string>();

    public void AddUri(string name, string url)
    {
        _urls[name] = url;
    }

    public HttpClient CreateClient(string name) => new HttpClient
    {
        BaseAddress = new Uri(_urls[name])
    };
}