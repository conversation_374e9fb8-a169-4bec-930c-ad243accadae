#!/usr/bin/env python3
"""
PDF Generator for Technical Implementation Guide
Creates a professional PDF from the markdown content
"""

import os
import sys
from reportlab.lib.pagesizes import letter, A4
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, PageBreak, Table, TableStyle
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib import colors
from reportlab.lib.enums import TA_LEFT, TA_CENTER, TA_JUSTIFY
import re

def create_pdf():
    # Read the markdown file
    try:
        with open('Technical_Implementation_Guide_Complete.md', 'r', encoding='utf-8') as f:
            content = f.read()
    except FileNotFoundError:
        print("Error: Technical_Implementation_Guide_Complete.md not found")
        return False
    
    # Create PDF document
    doc = SimpleDocTemplate(
        "Technical_Implementation_Guide_Complete.pdf",
        pagesize=A4,
        rightMargin=72,
        leftMargin=72,
        topMargin=72,
        bottomMargin=18
    )
    
    # Get styles
    styles = getSampleStyleSheet()
    
    # Custom styles
    title_style = ParagraphStyle(
        'CustomTitle',
        parent=styles['Heading1'],
        fontSize=24,
        spaceAfter=30,
        alignment=TA_CENTER,
        textColor=colors.darkblue
    )
    
    heading1_style = ParagraphStyle(
        'CustomHeading1',
        parent=styles['Heading1'],
        fontSize=18,
        spaceAfter=12,
        spaceBefore=20,
        textColor=colors.darkblue
    )
    
    heading2_style = ParagraphStyle(
        'CustomHeading2',
        parent=styles['Heading2'],
        fontSize=14,
        spaceAfter=10,
        spaceBefore=15,
        textColor=colors.darkgreen
    )
    
    heading3_style = ParagraphStyle(
        'CustomHeading3',
        parent=styles['Heading3'],
        fontSize=12,
        spaceAfter=8,
        spaceBefore=12,
        textColor=colors.darkred
    )
    
    code_style = ParagraphStyle(
        'Code',
        parent=styles['Code'],
        fontSize=9,
        fontName='Courier',
        backgroundColor=colors.lightgrey,
        borderColor=colors.grey,
        borderWidth=1,
        leftIndent=10,
        rightIndent=10,
        spaceAfter=10
    )
    
    normal_style = ParagraphStyle(
        'CustomNormal',
        parent=styles['Normal'],
        fontSize=11,
        spaceAfter=6,
        alignment=TA_JUSTIFY
    )
    
    # Story to hold all content
    story = []
    
    # Split content into lines
    lines = content.split('\n')
    
    # Process each line
    i = 0
    while i < len(lines):
        line = lines[i].strip()
        
        if not line:
            i += 1
            continue
            
        # Title (first # line)
        if line.startswith('# ') and i < 5:
            story.append(Paragraph(line[2:], title_style))
            story.append(Spacer(1, 20))
            
        # Heading 1
        elif line.startswith('# '):
            story.append(Paragraph(line[2:], heading1_style))
            
        # Heading 2
        elif line.startswith('## '):
            story.append(Paragraph(line[3:], heading2_style))
            
        # Heading 3
        elif line.startswith('### '):
            story.append(Paragraph(line[4:], heading3_style))
            
        # Code blocks
        elif line.startswith('```'):
            # Find end of code block
            code_lines = []
            i += 1
            while i < len(lines) and not lines[i].strip().startswith('```'):
                code_lines.append(lines[i])
                i += 1
            
            if code_lines:
                code_text = '\n'.join(code_lines)
                # Escape special characters for reportlab
                code_text = code_text.replace('&', '&amp;').replace('<', '&lt;').replace('>', '&gt;')
                story.append(Paragraph(f'<pre>{code_text}</pre>', code_style))
                story.append(Spacer(1, 10))
                
        # Tables (simple markdown tables)
        elif '|' in line and line.count('|') >= 2:
            table_lines = [line]
            i += 1
            # Collect all table lines
            while i < len(lines) and '|' in lines[i]:
                table_lines.append(lines[i].strip())
                i += 1
            i -= 1  # Back up one since we'll increment at end of loop
            
            # Parse table
            table_data = []
            for table_line in table_lines:
                if '---' in table_line:  # Skip separator line
                    continue
                cells = [cell.strip() for cell in table_line.split('|')[1:-1]]  # Remove empty first/last
                if cells:
                    table_data.append(cells)
            
            if table_data:
                table = Table(table_data)
                table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, 0), 10),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black)
                ]))
                story.append(table)
                story.append(Spacer(1, 12))
                
        # Bullet points
        elif line.startswith('- ') or line.startswith('* '):
            story.append(Paragraph(f'• {line[2:]}', normal_style))
            
        # Numbered lists
        elif re.match(r'^\d+\.', line):
            story.append(Paragraph(line, normal_style))
            
        # Bold text (simple **text** format)
        elif '**' in line:
            # Simple bold conversion
            line = re.sub(r'\*\*(.*?)\*\*', r'<b>\1</b>', line)
            story.append(Paragraph(line, normal_style))
            
        # Regular paragraphs
        elif line:
            # Handle inline code `code`
            line = re.sub(r'`([^`]+)`', r'<font name="Courier">\1</font>', line)
            story.append(Paragraph(line, normal_style))
            
        i += 1
    
    # Build PDF
    try:
        doc.build(story)
        print("✅ PDF created successfully: Technical_Implementation_Guide_Complete.pdf")
        return True
    except Exception as e:
        print(f"❌ Error creating PDF: {e}")
        return False

if __name__ == "__main__":
    success = create_pdf()
    sys.exit(0 if success else 1)
