using System;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Net.Mail;
using Extric.Towbook.Utility;
using Async = System.Threading.Tasks;

namespace Extric.Towbook.Integrations.Faxing
{
    /// <summary>
    /// Used to forward a fax to an email address. 
    /// </summary>
    public static class FaxDestinationExtensions
    {
        public static async Async.Task Forward(FaxTransaction ft, FaxForwardType fft = FaxForwardType.PDF)
        {
            var fn = FaxNumber.GetByFaxNumber(ft.DNIS);

            if (fn == null)
                throw new TowbookException("Fax Number: " + ft.DNIS + " doesn't exist in the FaxNumber registry.");

            // get the transaction id.
            int transactionId = FaxTransaction.Save(ft, fn.CompanyId);
            var company = await Company.Company.GetByIdAsync(fn.CompanyId);

            Collection<FaxDestination> destinations = FaxDestination.GetByFaxNumber(fn.Id);

            if (destinations.Count == 0)
                destinations.Add(new FaxDestination() { Email = company.Email });


            // compose a message.

            using (var sc = new SmtpClient().Get())
            {
                var file = ft.Documents.Where(o => o.DocType == "TIFF").FirstOrDefault();
                using (var fs = new MemoryStream())
                {
                    Attachment a = null;
                    using (var msf = new MemoryStream(Convert.FromBase64String(file.FileData)))
                    {

                        if (fft == FaxForwardType.TIFF)
                        {
                            a = new Attachment(msf, System.Net.Mime.MediaTypeNames.Application.Octet);
                            a.ContentDisposition.FileName = file.Filename;
                        }
                        else
                        {
                            //using (var img = new Bitmap(msf))
                            //    FileConversionHelper.TifToPdf(img).Save(fs);
                            //fs.Seek(0, SeekOrigin.Begin);
                            var pdfFs = await PdfClientBase.GeneratePdfFromTif(file.FileData, file.Filename);
                            pdfFs.Seek(0, SeekOrigin.Begin);
                            a = new Attachment(pdfFs, System.Net.Mime.MediaTypeNames.Application.Octet);
                            a.ContentDisposition.FileName = file.Filename.Replace(".tif", ".pdf");
                        }

                        foreach (var dest in destinations)
                        {
                            using (var msg = new MailMessage())
                            {
                                msg.From = new MailAddress("<EMAIL>", "Towbook Fax");
                                msg.Subject = "Incoming Fax from " + Core.FormatPhone(ft.ANI);
                                msg.To.Add(new MailAddress(dest.Email));
                                msg.Body = "Attached is a copy of the fax sent from " +
                                    Core.FormatPhone(ft.ANI) + " to " + (company != null ? company.Name : "[Unknown]") + " (" + Core.FormatPhone(ft.DNIS) + ")";

                                if (transactionId > 0)
                                    msg.Body += "\n\nID# " + company.Id.ToString().PadLeft(5, '0') + transactionId.ToString().PadLeft(7, '0');

                                msg.Attachments.Add(a);
                                // send the message

                                Console.WriteLine("send to " + dest.Email);
                                await sc.Send(msg, await User.GetByIdAsync(1), "FaxDestination_Forward");
                            }
                        }
                    }
                }

            }
        }
    }

}
