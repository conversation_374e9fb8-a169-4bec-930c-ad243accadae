# See https://aka.ms/customizecontainer to learn how to customize your debug container and how Visual Studio uses this Dockerfile to build your images for faster debugging.

FROM mcr.microsoft.com/dotnet/aspnet:8.0-alpine3.18 AS base
ENV TZ="America/New_York"
ENV DOTNET_SYSTEM_GLOBALIZATION_INVARIANT=false\
    # Set the locale
    LC_ALL=en_US.UTF-8 \
    LANG=en_US.UTF-8

RUN apk add --no-cache tzdata
RUN apk add --no-cache icu-libs icu-data-full

ARG COMMIT
RUN if [ -z "$COMMIT" ]; then echo "COMMIT is empty"; exit 1; else : echo "commitId: $COMMIT" ; fi

RUN apk update && apk upgrade && apk add fontconfig
RUN apk add libgdiplus-dev --update-cache --repository http://dl-3.alpinelinux.org/alpine/edge/testing/ --allow-untrusted

# install NewRelic agent
ARG NEW_RELIC_APP_NAME=LocateMvc
ARG CORECLR_ENABLE_PROFILING=1
ARG CORECLR_PROFILER={36032161-FFC0-4B61-B559-F6C5D41BAE5A}
ARG NEW_RELIC_LICENSE_KEY=NEW_RELIC_LICENSE_VALUE

RUN echo "NewRelic AppName: $NEW_RELIC_APP_NAME"

RUN  mkdir /tmp/newrelic-dotnet-agent \
&& cd /tmp \
&& export NEW_RELIC_DOWNLOAD_URI=https://download.newrelic.com/$(wget -qO - "https://nr-downloads-main.s3.amazonaws.com/?delimiter=/&prefix=dot_net_agent/latest_release/newrelic-dotnet-agent" |  \
     grep -E -o "dot_net_agent/latest_release/newrelic-dotnet-agent_[[:digit:]]{1,3}(\.[[:digit:]]{1,3}){2}_amd64\.tar\.gz") \
&& echo "Downloading: $NEW_RELIC_DOWNLOAD_URI into $(pwd)" \
&& wget -O - "$NEW_RELIC_DOWNLOAD_URI" | gzip -dc | tar xf - 

ENV CORECLR_ENABLE_PROFILING=$CORECLR_ENABLE_PROFILING \
CORECLR_PROFILER=$CORECLR_PROFILER \
CORECLR_NEWRELIC_HOME=/tmp/newrelic-dotnet-agent \
CORECLR_PROFILER_PATH=/tmp/newrelic-dotnet-agent/libNewRelicProfiler.so \
NEW_RELIC_LICENSE_KEY=$NEW_RELIC_LICENSE_KEY \
NEW_RELIC_APPLICATION_LOGGING_ENABLED=true \
NEW_RELIC_APPLICATION_LOGGING_METRICS_ENABLED=true \
NEW_RELIC_APPLICATION_LOGGING_FORWARDING_ENABLED=true \
NEW_RELIC_APP_NAME=$NEW_RELIC_APP_NAME

WORKDIR /app

EXPOSE 5020
EXPOSE 5021


# This stage is used to build the service project
FROM --platform=$BUILDPLATFORM mcr.microsoft.com/dotnet/sdk:8.0-alpine3.18 AS build
ARG TARGETARCH
ARG BUILDPLATFORM
ARG COMMIT

WORKDIR /src
COPY ["Directory.Build.props", "."]
COPY ["Web/twbk.co/PublicAccess/Extric.Towbook.Web.PublicAccess.csproj", "Web/twbk.co/PublicAccess/"]
COPY ["Extric.Towbook.WebShared.Net5/Extric.Towbook.WebShared.Net5.csproj", "Extric.Towbook.WebShared.Net5/"]
COPY ["Extric.Towbook/Extric.Towbook.csproj", "Extric.Towbook/"]
COPY ["Extric.Towbook.Generated.Features/Extric.Towbook.Generated.Features.csproj", "Extric.Towbook.Generated.Features/"]
COPY ["Extric.Towbook.Storage/Extric.Towbook.Storage.csproj", "Extric.Towbook.Storage/"]
COPY ["Glav.CacheAdapter/Glav.CacheAdapter.csproj", "Glav.CacheAdapter/"]
COPY ["Extric.Towbook.WebWrapper/Extric.Towbook.WebWrapper.csproj", "Extric.Towbook.WebWrapper/"]
COPY ["Extric.Towbook.DynamicFeatureBuilder/Extric.Towbook.DynamicFeatureBuilder.csproj", "Extric.Towbook.DynamicFeatureBuilder/"]
COPY ["Integrations/Square/Square/Extric.Towbook.Square.csproj", "Integrations/Square/Square/"]

RUN dotnet restore "Web/twbk.co/PublicAccess/Extric.Towbook.Web.PublicAccess.csproj" -a $TARGETARCH
COPY . .
RUN echo $COMMIT > Extric.Towbook/_git_commit.txt
WORKDIR "/src/Web/twbk.co/PublicAccess"
RUN dotnet build "./Extric.Towbook.Web.PublicAccess.csproj" -o /app/build

FROM build AS publish
ARG COMMIT
RUN dotnet publish "Extric.Towbook.Web.PublicAccess.csproj" -o /app/publish  -a $TARGETARCH /p:AssemblyVersion=1.0.0.0 /p:Version=1.0.0.0-$COMMIT


FROM base AS final
WORKDIR /app
RUN chown app:app .
COPY --from=publish --chown=app:app /app/publish .

#copy openssl.cnf for alpine
COPY --from=publish /app/publish/openssl.cnf /etc/ssl
RUN rm ./openssl.cnf

RUN apk add icu-libs

USER app

ENV ASPNETCORE_ENVIRONMENT=Development
ENV ASPNETCORE_URLS=http://+:5020;https://+:5021;

ENTRYPOINT ["dotnet", "PublicAccess.dll"]