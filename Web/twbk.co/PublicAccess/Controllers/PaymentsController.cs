using Extric.Towbook;
using Extric.Towbook.Accounts;
using Extric.Towbook.Company;
using Extric.Towbook.Dispatch;
using PublicAccess.Models;
using System;
using Extric.Towbook.API.Integration.Square;
using Extric.Towbook.API.Integration.Square.Exceptions;
using static PublicAccess.ApiUtility;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using NLog;
using Extric.Towbook.API.Integration.Square.Model;

using Extric.Towbook.API.Integration.Square.Model;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Http;

namespace PublicAccess.Controllers
{
    public class PaymentsController : Controller
    {
        private static readonly Logger logger = LogManager.GetCurrentClassLogger();
        private static readonly JsonSerializerSettings JsonSerializerSettings = new JsonSerializerSettings
        {
            ContractResolver = new DefaultContractResolver
            {
                NamingStrategy = new CamelCaseNamingStrategy()
            }
        };

        // GET: twbk.co/p/123
        public async Task<IActionResult> Index(string id)
        {
            return await Get(id);
        }

        // GET: twbk.co/cu/123
        /// <summary>
        /// A call payment link is a short url provided on each 
        /// invoice to make a payment.
        /// </summary>
        /// <param name="id">A hashIds string of the CallId and UserId</param>
        /// <returns>Redirects to GET:twbk.co/p/{id} route</returns>
        public async Task<IActionResult> CallPaymentLink(string id)
        {
            var h = DecodeUrlHash(id);
            if (h == null || h.Length != 2)
                return new StatusCodeResult(StatusCodes.Status400BadRequest);

            var callId = h[0];
            var userId = h[1];

            var entry = Entry.GetById(callId);
            var user = await Extric.Towbook.User.GetByIdAsync(userId);
            
            if(!user.HasAccessToCompany(entry.CompanyId))
                return new StatusCodeResult(StatusCodes.Status400BadRequest);

            var sqConn = await SquareUtils.GetAuthorizationAsync(entry.CompanyId);
            if (sqConn == null)
                return new StatusCodeResult(StatusCodes.Status404NotFound);

            var webLink = EntryWebLink.FindOrCreatePaymentLink(entry, user, PaymentVendor.SQUARE, false, false, true);
            ThrowIfNotFound(webLink, "payment link");

            return RedirectToAction("../" + webLink.UrlHash);
        }

        // GET: twbk.co/cu/123
        /// <summary>
        /// A call payment link is a short url provided on each 
        /// invoice to make a payment.
        /// </summary>
        /// <param name="id">A hashIds string of the CallId and UserId</param>
        /// <returns>Redirects to GET:twbk.co/p/{id} route</returns>
        [HttpGet]
        public async Task<IActionResult> StatementPaymentLink(string id)
        {
            var h = DecodeUrlHash(id);
            if (h == null || h.Length != 2)
                return new StatusCodeResult(StatusCodes.Status400BadRequest);

            var statementId = h[0];
            var userId = h[1];

            var statement = await Statement.GetByIdAsync(statementId);
            ThrowIfNotFound(statement, "Statement");

            var user = await Extric.Towbook.User.GetByIdAsync(userId);

            var account = await Account.GetByIdAsync(statement.AccountId);
            
            if (!await user.HasAccessToCompanyAsync(account.CompanyId))
                return new StatusCodeResult(StatusCodes.Status400BadRequest);

            var sqConn = await SquareUtils.GetAuthorizationAsync(account.CompanyId);
            if (sqConn == null)
                return new StatusCodeResult(StatusCodes.Status404NotFound);

            var webLink = StatementWebLink.FindOrCreatePaymentLink(statement, user, PaymentVendor.SQUARE);
            ThrowIfNotFound(webLink, "payment link");

            return RedirectToAction("../" + webLink.UrlHash);
        }

        [HttpGet]
        public async Task<IActionResult> Get(string id)
        {
            var pl = PaymentLink.GetByPaymentUrlHash(id);
            ThrowIfNotFound(pl, "Payment Id");

            var c = await Company.GetByIdAsync(pl.CompanyId);
            ThrowIfNotFound(c, "Company");

            InvoiceModel invoiceModel = null;
            StatementModel statementModel = null;
            IEnumerable<PaymentItem> payments = null;
            Account acc = null;
            Entry entry = null;
            Statement statement = null;

            if (pl.DispatchEntryId.HasValue)
            {
                entry = Entry.GetByIdNoCache(pl.DispatchEntryId.Value);
                ThrowIfNotFound(entry, "Call");

                invoiceModel = await InvoiceModel.MapAsync(entry, EntryWebLink.GetByPaymentLinkId(pl.Id));
                payments = PaymentModel.Map(await InvoicePayment.GetByDispatchEntryIdAsync(entry.Id)).Payments;

                acc = entry.Account;
            }
            else if (pl.StatementId.HasValue)
            {
                statement = await Statement.GetByIdAsync(pl.StatementId.Value);
                ThrowIfNotFound(statement, "Statement");

                statementModel = StatementModel.Map(statement, StatementWebLink.GetByPaymentLinkId(pl.Id));

                acc = await Account.GetByIdAsync(statement.AccountId);
            }

            var auth = await SquareUtils.GetAuthorizationAsync(c.Id);

            #region Tipping
            var setting = SquareUtils.FillKeyOptionValues(null, c, acc);

            var isTippingEnabled = setting.IsTippingEnabled && setting.TippingOptions?.ExcludeOnPaymentLinks != true;

            if (entry != null)
            {
                if (entry.Drivers == null || !entry.Drivers.Any())
                    isTippingEnabled = false;

                if (entry.Impound == true || entry.IsTowoutCall())
                    isTippingEnabled = false;

                if (entry.Account?.Type == AccountType.PoliceDepartment || entry.Account?.Type == AccountType.PrivateProperty)
                    isTippingEnabled = false;
            }

            if (statement != null)
                isTippingEnabled = false;
            #endregion

            var model = new
            {
                Company = CompanyModel.Map(c, auth?.Location?.LocationId),
                Invoice = invoiceModel,
                Payments = payments,
                Statement = statementModel,
                LinkId = pl.Id,
                Type = pl.DispatchEntryId.HasValue ? "Invoice" : pl.StatementId.HasValue ? "Statement" : "",
                ApplicationId = SquareUtils.GetConfig(c.Id, "ApplicationId"),
                IsTippingEnabled = isTippingEnabled
            };

            ViewBag.modelJson = model.ToJson();
            ViewBag.Company = model.Company;
            ViewBag.Invoice = model.Invoice;
            ViewBag.Statement = model.Statement;
            ViewBag.Payments = model.Payments;
            

            return View();
        }

        
        [HttpPost]
        public async Task<string> Post(string id, PaymentRequest paymentRequest)
        {
            var pl = PaymentLink.GetByPaymentUrlHash(id);
            ThrowIfNotFound(pl, "Payment Id");

            paymentRequest.PaymentLinkId = pl.Id;
            
            //Response.TrySkipIisCustomErrors = true;

            try
            {
                var result = await SquareUtils.ProcessPaymentLink(paymentRequest);
                return JsonConvert.SerializeObject(result.Payment, JsonSerializerSettings);
            }
            catch(Exception e)
            {
                Response.StatusCode = 400;
                
                if (e is SquareApiException sae)
                {
                    logger.Log(LogLevel.Error, sae, sae.JsonMessage);
                    return JsonConvert.SerializeObject(new {sae.Errors}, JsonSerializerSettings);
                }

                if (e is CustomerApiException cae)
                {
                    logger.Log(LogLevel.Error, cae, cae.JsonMessage);
                    return JsonConvert.SerializeObject(new { cae.Errors }, JsonSerializerSettings);
                }

                logger.Log(LogLevel.Error, e, e.Message);
                return JsonConvert.SerializeObject(new {
                    Errors = new[] { new {ErrorCode = "BAD_REQUEST", e.Message }}
                }, JsonSerializerSettings);
            }
        }
    }
}
