using Extric.Towbook;
using Extric.Towbook.Dispatch;
using Extric.Towbook.Dispatch.QuoteModels;
using System;
using System.Threading.Tasks;
using static PublicAccess.ApiUtility;
using Microsoft.AspNetCore.Mvc;

namespace PublicAccess.Controllers
{
    public class QuotesController : Controller
    {
        // GET: twbk.co/i/abc123
        public async Task<ActionResult> Index(string id)
        {
            return await Image(id);
            /*
            var ql = QuoteWebLink.GetByUrlHash(id);
            ThrowIfNotFound(ql?.QuoteId, "Quote");

            var c = Company.GetById(ql.CompanyId);
            ThrowIfNotFound(c, "Company");

            var q = QuoteModel.GetById(Guid.Parse(ql.QuoteId));
            if (q == null || q.Id == Guid.Empty)
                throw new Exception("The quote is expired, deleted, or not found.");

            if (q.CompanyId != ql.CompanyId)
                throw new Exception("Quote link is invalid");

            var model = new
            {
                Company = CompanyModel.Map(c, null),
                Type = "Quote"
            };

            ViewBag.modelJson = model.ToJson();
            ViewBag.Company = model.Company;
            ViewBag.QuoteNumber = q.QuoteNumber;
            ViewBag.QuoteId = q.Id;
            ViewBag.Hash = id;

            return View();*/

        }

        //TODO AGG VERIFY FromQuery/FromUri param
        public async Task<ActionResult> Image(string id, [FromQuery] int pdf = 1)
        {
            var ql = await QuoteWebLink.GetByUrlHash(id);
            ThrowIfNotFound(ql?.QuoteId, "Quote Web Link");

            var q = await QuoteModel.GetById(Guid.Parse(ql.QuoteId));

            ThrowIfNotFound(q, "Quote");

            int statusCode = 0;
            var stream = GetResponseFromUrlAsStream(GetWebAppUrl() + "/dispatch2/Invoice.aspx?" + string.Format("q={0}&auth={1}&pdf=1&jpg={2}",
                    q.Id,
                    Core.MD5(q.QuoteNumber + ":27ed2fb84d816"),
                    pdf > 0 ? 0 : 1), out statusCode, false);

            if (statusCode != 200 || stream == null || stream.Length == 0)
            {
                throw new Exception("Error retrieving Quote. HttpStatusCode:" + statusCode + ", Response:" + stream);
            }

            return base.File(stream[0], pdf > 0 ? "application/pdf" : "image/jpeg");
        }

    }
}