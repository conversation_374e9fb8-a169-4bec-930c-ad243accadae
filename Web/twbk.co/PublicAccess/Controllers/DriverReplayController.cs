using System;
using System.Collections.Generic;
using System.Linq;
using Extric.Towbook;
using Extric.Towbook.Dispatch.CallModels;
using Microsoft.AspNetCore.Mvc;
using NLog;

namespace PublicAccess.Controllers
{
    public class DriverReplayController : Controller
    {
        private static readonly Logger logger = LogManager.GetCurrentClassLogger();

        public ActionResult Index(string id)
        {
            Exception exception = null;
            var logJson = new Dictionary<string, object>();
            try
            {
                var guid = Guid.ParseExact(id, "N");
                logJson["id"] = guid;
                var driverReplay = DriverReplay.GetByDriverReplayGuid(guid);

                if (driverReplay == null)
                    throw new TowbookException("Invalid Driver Replay Link");

                this.ViewBag.InvalidRequest = false;

                var call = Extric.Towbook.Dispatch.Entry.GetById(driverReplay.DispatchEntryId);
                var callModel = CallModel.Map(call);


                this.ViewBag.CallJson = CleanCall(callModel).ToJson();
                logJson["call"] = this.ViewBag.CallJson;

                this.ViewBag.CompanyJson = call.Company.ToJson();
                this.ViewBag.CompanyName = call.Company.Name;
                logJson["company"] = this.ViewBag.CompanyJson;

                var driver = Driver.GetById(driverReplay.DriverId);

                this.ViewBag.DriverJson = CleanDriver(driver).ToJson();
                logJson["driver"] = this.ViewBag.DriverJson;

                // dispatch to complete
                if (call.CompletionTime == null || call.DispatchTime == null)
                {
                    logJson["error"] = "Call is not completed or lacking a dispatch time.";
                    return View("Error");
                }
                var start = call.DispatchTime.Value;
                var end = call.CompletionTime.Value;
                var locationHistory = UserLocationHistoryItem.GetByUserId(driver.UserId, start, end).ToList();
                logJson["locationHistory"] = locationHistory;
                if (locationHistory == null || locationHistory.Count == 0)
                {
                    logJson["error"] = "Location History is not there";
                    return View("Error");
                }
                this.ViewBag.LocationHistory = locationHistory.ToJson();
                this.ViewBag.VersionNumber = 5;

            }
            catch (Exception e)
            {
                exception = e;
                throw;
            }
            finally
            {
                var logEvent = new LogEventInfo { Level = LogLevel.Info, Message = "Driver Replay Controller" };
                if (exception != null)
                {
                    logEvent.Properties.Add("exception", exception);
                }
                logEvent.Properties.Add("json", logJson.ToJson());
                logger.Log(logEvent);
            }
            return View();
        }

        private CallModel CleanCall(CallModel call)
        {
            call.InvoiceItems = Array.Empty<CallInvoiceItemModel>();
            call.BalanceDue = 0;
            for (int i = 0; i < call.Assets.Length; i++)
            {
                call.Assets[i].Vin = "";
                call.Assets[i].LicenseNumber = "";
                call.Assets[i].LicenseState = "";
                call.Assets[i].LicenseYear = "";
            }

            call.InvoiceTax = 0;
            call.Contacts = null;
            return call;
        }

        private Driver CleanDriver(Driver driver)
        {
            driver.HomePhone = "";
            driver.MobilePhone = "";
            driver.WorkPhone = "";
            driver.Email = "";
            driver.Address = "";
            driver.City = "";
            driver.State = "";
            driver.Zip = "";
            driver.LicenseNumber = "";
            driver.Notes = "";
            return driver;
        }
    }
}