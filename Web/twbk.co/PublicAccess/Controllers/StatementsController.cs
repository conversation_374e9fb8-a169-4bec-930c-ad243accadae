using Extric.Towbook;
using Extric.Towbook.Accounts;
using Extric.Towbook.API.Integration.Square;
using Extric.Towbook.Company;
using PublicAccess.Models;
using SkiaSharp;
using System;
using System.IO;
using System.Linq;
using static PublicAccess.ApiUtility;
using Extric.Towbook.Utility;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;


namespace PublicAccess.Controllers
{
    public class StatementsController : Controller
    {
        // GET: twbk.co/s/abc123
        public async Task<IActionResult> Index(string id)
        {
            var pl = PaymentLink.GetByPaymentUrlHash(id);
            ThrowIfNotFound(pl, "Payment Id");
            ThrowIfNotFound(pl.DispatchEntryId, "Payment Id");

            var c = await Company.GetByIdAsync(pl.CompanyId);
            ThrowIfNotFound(c, "Company");

            var s = await Statement.GetByIdAsync(pl.StatementId.Value);
            ThrowIfNotFound(s, "Statement");

            if (s.Account == null || s.Account.CompanyId != pl.CompanyId)
                throw new Exception("Payment link is invalid");

            var statementModel = StatementModel.Map(s, StatementWebLink.GetByPaymentLinkId(pl.Id));

            var auth = await SquareUtils.GetAuthorizationAsync(c.Id);

            var model = new
            {
                Company = CompanyModel.Map(c, auth?.Location?.LocationId),
                Statement = statementModel,
                LinkId = pl.Id,
                Type = "Statement",
                isTippingEnabled = false
            };

            ViewBag.modelJson = model.ToJson();
            ViewBag.Company = model.Company;
            ViewBag.Statement = model.Statement;

            return View();
        }

        [HttpGet]
        public async Task <IActionResult> Pdf(string id, [FromQuery] bool inline = false)
        {
            var pl = PaymentLink.GetByPaymentUrlHash(id);
            ThrowIfNotFound(pl, "Payment Id");
            ThrowIfNotFound(pl.StatementId, "Payment Id");
            ThrowIfNotFound(pl.OwnerUserId, "Payment Id");

            var c = await Company.GetByIdAsync(pl.CompanyId);
            ThrowIfNotFound(c, "Company");

            var statement = await Statement.GetByIdAsync(pl.StatementId.Value);
            ThrowIfNotFound(statement, "Call");

            var ms = GetStatementAsStream(statement, false);
            ms.Position = 0;

            var cd = new System.Net.Mime.ContentDisposition
            {
                // for example foo.bak
                FileName = "Statement_" + statement.Id + ".pdf",

                // always prompt the user for downloading, set to true if you want 
                // the browser to try to show the file inline
                Inline = inline,
            };
            Response.Headers.Add("Content-Disposition", cd.ToString());
            return File(ms, "application/pdf");
        }

        [HttpGet]
        public async Task <IActionResult> Html(string id)
        {
            var pl = PaymentLink.GetByPaymentUrlHash(id);
            ThrowIfNotFound(pl, "Payment Id");
            ThrowIfNotFound(pl.StatementId, "Payment Id");
            ThrowIfNotFound(pl.OwnerUserId, "Payment Id");

            var c = await Company.GetByIdAsync(pl.CompanyId);
            ThrowIfNotFound(c, "Company");

            var statement = await Statement.GetByIdAsync(pl.StatementId.Value);
            ThrowIfNotFound(statement, "Call");

            return Content("<html><body>" + GetStatementAsHtml(statement) + "</body></html>", "text/html");
        }

        [HttpGet]
        public async Task <IActionResult> Image(string id)
        {
            var pl = PaymentLink.GetByPaymentUrlHash(id);
            ThrowIfNotFound(pl, "Payment Id");
            ThrowIfNotFound(pl.StatementId, "Payment Id");
            ThrowIfNotFound(pl.OwnerUserId, "Payment Id");

            var c = await Company.GetByIdAsync(pl.CompanyId);
            ThrowIfNotFound(c, "Company");

            var statement = await Statement.GetByIdAsync(pl.StatementId.Value);
            ThrowIfNotFound(statement, "Statement");

            var stream = GetStatementAsStream(statement, true);
            var y = new MemoryStream();

            using (SKBitmap b = SKBitmap.Decode(stream))
            {
                b.Save(y, SKEncodedImageFormat.Png, 10);
            }

            y.Position = 0;

            var cd = new System.Net.Mime.ContentDisposition
            {
                // for example foo.bak
                FileName = "Statement_" + statement.Id + ".png",

                // always prompt the user for downloading, set to true if you want 
                // the browser to try to show the file inline
                Inline = true
            };
            Response.Headers.Add("Content-Disposition", cd.ToString());
            return File(y, "image/png");
        }

        private Stream GetStatementAsStream(Statement statement, bool asImage = false)
        {
            int statusCode = 0;

            var stream = GetResponseFromUrlAsStream(GetWebAppUrl() + "/accounts/statement.aspx" + string.Format("?id={0}&{1}=1&key={2}",
                    statement.Id,
                    (asImage ? "jpg" : "pdf"),
                    Core.MD5(statement.Id + ":27ed2fb84d816")), out statusCode, false);

            if (statusCode != 200 || stream == null || stream.Count() == 0 || stream[0].Length == 0)
            {
                throw new Exception("Error retrieving Statement. HttpStatusCode:" + statusCode + ", Response:" + stream);
            }

            return stream[0];
        }

        private string GetStatementAsHtml(Statement statement)
        {
            int statusCode = 0;

            var html = GetResponseFromUrl(GetWebAppUrl() + "/accounts/statement.aspx" + string.Format("?id={0}&pdf=0&key={1}",
                    statement.Id,
                    Core.MD5(statement.Id + ":27ed2fb84d816")), out statusCode, false);

            if (statusCode != 200 )
            {
                throw new Exception("Error retrieving Statement. HttpStatusCode:" + statusCode + ", Response:" + html);
            }

            return html;
        }
    }
}
