apiVersion: apps/v1
kind: Deployment
metadata:
  name: twbkpublicaccess
spec:
  selector:
    matchLabels:
      app: twbk-pod
  template:
    metadata:
      labels:
        app: twbk-pod
    spec:
      containers:
      - name: twbk-container
        image: towbookapiregistry.azurecr.io/twbk:latest
        resources:
          requests:
            memory: "512Mi"
            cpu: "200m"
          limits:
            memory: "2Gi"
            cpu: "800m"
        ports:
        - containerPort: 5020
        env:
        - name: ASPNETCORE_ENVIRONMENT
          value: "Development"
        - name: CORECLR_ENABLE_PROFILING
          value: "${CORECLR_ENABLE_PROFILING}"
        - name: CORECLR_PROFILER
          value: "{36032161-FFC0-4B61-B559-F6C5D41BAE5A}"
        - name: NEW_RELIC_APP_NAME
          value: "NewRelic-Twbk"
        - name: NEW_RELIC_LICENSE_KEY
          value: "ba6a3e6ef84b36cc9a86e7ed156ca2e1FFFFNRAL"
        - name: ConnectionStrings__Database
          valueFrom:
            secretKeyRef:
              name: kvtowbook-secrets
              key: ConnectionStrings-Database
        # - name: ConnectionStrings__Database.Azure
        #   valueFrom:
        #     secretKeyRef:
        #       name: kvtowbook-secrets
        #       key: ConnectionStrings-DatabaseAzure
        - name: ConnectionStrings__Microsoft.ServiceBus
          valueFrom:
            secretKeyRef:
              name: kvtowbook-secrets
              key: ConnectionStrings-MicrosoftServiceBus
        - name: Redis__Credentials
          valueFrom:
            secretKeyRef:
              name: kvtowbook-secrets
              key: Redis-Credentials
        - name: CosmosDb__Url
          valueFrom:
            secretKeyRef:
              name: kvtowbook-secrets
              key: CosmosDb-Url
        - name: CosmosDb__AuthKey
          valueFrom:
            secretKeyRef:
              name: kvtowbook-secrets
              key: CosmosDb-AuthKey
        - name: CosmosDb__Database
          valueFrom:
            secretKeyRef:
              name: kvtowbook-secrets
              key: CosmosDb-Database
        volumeMounts:
          - name:  secrets-store
            mountPath:  "mnt/secrets-store"
            readOnly: true
      nodeSelector:
        selector: nplin
      volumes:
        - name: secrets-store
          csi:
            driver: secrets-store.csi.k8s.io
            readOnly: true
            volumeAttributes:
              secretProviderClass: "azure-kvtowbook-msi"
