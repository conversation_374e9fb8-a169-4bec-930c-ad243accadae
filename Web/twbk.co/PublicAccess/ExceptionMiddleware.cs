using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.Extensions;
using NLog;
using Extric.Towbook.WebShared;

namespace PublicAccess
{
    public class ExceptionMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger _logger;


        public ExceptionMiddleware(RequestDelegate next, ILogger logger)
        {
            _logger = logger;
            _next = next;
        }
        public async Task InvokeAsync(HttpContext httpContext)
        {
            try
            {
                await _next(httpContext);
            }
            catch (Exception ex)
            {
                _logger.Error($"Something went wrong: {ex}");
                HandleException(httpContext, ex);
            }
        }

        private void HandleException(HttpContext context, Exception exception)
        {
            if (exception == null)
                return;

            var eventInfo = new LogEventInfo();
            var errorId = Guid.NewGuid();
            var exceptionMessage = exception != null ? exception.InnerException.Message : exception.Message;

            if (context.Request.Method != null)
                eventInfo.Properties.Add("method", context.Request.Method);

            if (!string.IsNullOrEmpty(UriHelper.GetEncodedUrl(context.Request)))
                eventInfo.Properties.Add("url", UriHelper.GetEncodedUrl(context.Request));

            if (exception.InnerException != null)
                eventInfo.Properties.Add("exception", exception.InnerException.Message);

            eventInfo.Properties["requestingIp"] = WebGlobal.GetRequestingIp();
            eventInfo.Properties["errorId"] = errorId;

            _logger.Error(exceptionMessage,
                exception,
                eventInfo);
        }
    }

    public static class ExceptionMiddlewareExtensions
    {
        public static IApplicationBuilder ConfigureExceptionMiddleware(
            this IApplicationBuilder builder, ILogger logger)
        {
            return builder.UseMiddleware<ExceptionMiddleware>(logger);
        }
    }
}
