String.prototype.leftPad = function(l, c) { return new Array(l - this.length + 1).join(c || '0') + this; }

var getElementsByClass = function(className, type) {
    var relElems = new Array();
    if (type == null) type = 'input';
    aElements = document.getElementsByTagName(type);

    var y = 0;

    for (i = 0; i < aElements.length; i++) {

        if (aElements[i].className.indexOf(className) > -1) {
            relElems[y] = aElements[i];
            y++;
        }
    }

    return relElems;
}
function initNumbersOnly() {

    elems = getElementsByClass('NumbersOnly', 'INPUT');
    for (i = 0; i < elems.length; i++) {
        elems[i].onkeypress = function(e) { return CheckKeyCode(this, e); };
        if('function' == typeof validateQuantity) {
            elems[i].onchange = function() { validateQuantity(this); updateAllFields(false) };
        }
    }
}
function initNumbersDecimalsOnly() {
    elems = getElementsByClass('NumbersDecOnly');
    for (i = 0; i < elems.length; i++) {
        elems[i].onkeypress = function(e) { return CheckKeyCode(this, e, true, true); };

        if ('function' == typeof validateQuantity) {
            elems[i].onchange = function() { validateQuantity(this); updateAllFields(false); };
        }
    }
}
function showHide(object) {
    if (object.style.display == "block")
        object.style.display = "none";
    else
        object.style.display = "block";
}

function changeActiveTab(tc, theTab, linkX) {
    if (theTab == null || theTab == undefined) return;
    if (window.location.hash != theTab && window.location.hash != undefined) {
        window.location.hash = theTab.replace('tab', '');
    }

        tc = document.getElementById(tc);
        theTab = document.getElementById(theTab);

        if (document.getElementById("activeTab") != null && document.getElementById("activeTab").value == theTab.id)
            return;
        

        if (tc == null) {
            return;
        }

        if (theTab == null) {
            return;
        }
        if (linkX == null) {
            return;
        }
        linkX.parentNode.className='selected';

        for (i = 0; i < linkX.parentNode.parentNode.childNodes.length; i++) {
            if (linkX.parentNode.parentNode.childNodes[i] != linkX.parentNode) {
                linkX.parentNode.parentNode.childNodes[i].className = null;
            }
        }
        
        for (i = 0; i < tc.getElementsByTagName('DIV').length; i++) {
            if (tc.getElementsByTagName('DIV')[i].className == "TabContainer" && tc.childNodes[i] != theTab) {
                tc.getElementsByTagName('DIV')[i].style.display = "none";
            }
        }

        theTab.style.display = "block";
        
        if (document.getElementById("activeTab") != null) {
            document.getElementById("activeTab").setAttribute("value", theTab.id);
        }
}

function CheckUsernameField(myfield, e) {
    if (window.event) key = window.event.keyCode;
    else if (e) key = e.which;

    return /^[a-zA-Z0-9@\._]$/.test(String.fromCharCode(key));
}

function CheckKeyCode(myfield, e, dec, allowPeriods, allowExtra) {
    var key;
    var keychar;
    if (window.event) key = window.event.keyCode;
    else if (e) key = e.which;
    else return true;

    keychar = String.fromCharCode(key);

    if ((key == null) || (key == 0) || (key == 8) ||
    (key == 9) || (key == 13) || (key == 27) || (key == 46 && allowPeriods))  // control keys
        return true;
    else if ((("0123456789-").indexOf(keychar) > -1)) // numbers
        return true;
    else if (allowExtra != null && ((allowExtra).indexOf(keychar) > -1))
        return true;

    // decimal point jump
    else if (dec && (keychar == ".")) {
        //   myfield.form.elements[dec].focus();
        // return false;
    }
    else
        return false;
}

function addOption(sO, oT, oV) {
    var oO = new Option(oT, oV);
    var oR = sO.options.length;
    sO.options[oR] = oO;
   // sO.add(oO, null);
}


function CheckIsIE() {
    if (navigator.appName.toUpperCase() == 'MICROSOFT INTERNET EXPLORER') { return true; }
    else { return false; }
}

function PrintThisPage() {
  if (document.queryCommandSupported('print')) {
    $('iframe').get(0).contentWindow.document.execCommand('print', false, null);
  } else {
    window.frames['ifWorkspace'].focus();
    window.frames['ifWorkspace'].print();
  }
}

function validateVIN(src, arg) {
    VIN = arg.Value;

    if (VIN.length > 0) {


        NewVin = "";
        VIN = VIN.toUpperCase();

        for (x = 0; x < VIN.length; x++) {
            c = VIN.charAt(x);

            switch (c) {
                case 'A': NewVin += "1"; break; // 1
                case 'B': NewVin += "2"; break; // 2
                case 'C': NewVin += "3"; break; // 3
                case 'D': NewVin += "4"; break; // 3
                case 'E': NewVin += "5"; break; // 5
                case 'F': NewVin += "6"; break; // 6
                case 'G': NewVin += "7"; break; // 7
                case 'H': NewVin += "8"; break; // 8

                case 'J': NewVin += "1"; break; // 1
                case 'K': NewVin += "2"; break; // 2
                case 'L': NewVin += "3"; break; // 3
                case 'M': NewVin += "4"; break; // 4
                case 'N': NewVin += "5"; break; // 5
                case 'O': NewVin += "6"; break; // 6
                case 'P': NewVin += "7"; break; // 7
                case 'R': NewVin += "9"; break; // 8 :9

                case 'S': NewVin += "2"; break; // 1 :2
                case 'T': NewVin += "3"; break; // 2 :3
                case 'U': NewVin += "4"; break; // 3 :4
                case 'V': NewVin += "5"; break; // 4 :5
                case 'W': NewVin += "6"; break; // 5 :6
                case 'X': NewVin += "7"; break; // 6 :7
                case 'Y': NewVin += "8"; break; // 7 :8
                case 'Z': NewVin += "9"; break; // 8 :9

                default: NewVin += c; break; // leave numbers as they are.
            }
        }

        i = 1;
        cm = 0;

        for (x = 0; x < NewVin.length; x++) {
            newvinx = NewVin.charAt(x);

            switch (i) {
                case 1: cm += newvinx * 8; break;
                case 2: cm += newvinx * 7; break;
                case 3: cm += newvinx * 6; break;
                case 4: cm += newvinx * 5; break;
                case 5: cm += newvinx * 4; break;
                case 6: cm += newvinx * 3; break;
                case 7: cm += newvinx * 2; break;
                case 8: cm += newvinx * 10; break;
                case 9: cm += newvinx * 0; break; // check digit.
                case 10: cm += newvinx * 9; break;
                case 11: cm += newvinx * 8; break;
                case 12: cm += newvinx * 7; break;
                case 13: cm += newvinx * 6; break;
                case 14: cm += newvinx * 5; break;
                case 15: cm += newvinx * 4; break;
                case 16: cm += newvinx * 3; break;
                case 17: cm += newvinx * 2; break;
            }
            i++;
        }

        cm = cm % 11;

        if (cm == 10) cm = "X";
    }
    else {
        cm = "Z";
    }

    if (VIN.charAt(8) == cm) {
        arg.IsValid = true;
        document.getElementById(src.controltovalidate).style.backgroundColor = 'white';
        document.getElementById(src.controltovalidate).style.color = 'black';
        document.getElementById(src.id).style.display = 'inline';
        document.getElementById(src.id).innerHTML = '(valid).';
    }
    else {

        if (VIN.length == 0) {
            document.getElementById(src.id).style.display = 'none';        
            document.getElementById(src.id).innerHTML = "Optional.";
            arg.IsValid = true;
            document.getElementById(src.controltovalidate).style.backgroundColor = 'white';
            document.getElementById(src.controltovalidate).style.color = 'black';
        }
        else {
            document.getElementById(src.id).innerHTML = "Invalid VIN";
            arg.IsValid = false;
            document.getElementById(src.controltovalidate).style.backgroundColor = '#ff0000';
            document.getElementById(src.controltovalidate).style.color = 'white';
        }
    }
}

function addLoadEvent(func) {
    if (func == null) return;

    var oldonload = window.onload;
    if (typeof window.onload != 'function') {
        window.onload = func;
    } else {
        window.onload = function() {
            if (oldonload) {
                oldonload();
            }
            func();
        }

    }
}

String.prototype.endsWith = function (suffix) {
    return this.indexOf(suffix, this.length - suffix.length) !== -1;
};

function setActiveTab(hash) {
    if (hash != null && hash.length > 1) {
        var found = false;

        $('#appMenu a').each(function (index, o) {
            if (o.href.endsWith(hash)) {
                // do we need to expand the parent?
                tload(o);
                found = true;
                return false;
            }
        });

        if (!found) {
            tload(null, true, hash);
        }
    }
}

var curHash = null;

function initAppMenuHashing() {
return;

    curHash = document.location.hash.split('#')[1];
    
    if ($('#appMenu') == null)
        return;
    }


addLoadEvent(function() { 
	initNumbersOnly(); 
	initNumbersDecimalsOnly(); 

    if (typeof towbook != 'undefined') {
        if (towbook._hashAndRefreshWithTowbookJS) {
            initAppMenuHashing();
        }
    }

});






/**
* o------------------------------------------------------------------------------o
* | This package is licensed under the Phpguru license. A quick summary is       |
* | that for commercial use, there is a small one-time licensing fee to pay. For |
* | registered charities and educational institutes there is a reduced license   |
* | fee available. You can read more  at:                                        |
* |                                                                              |
* |                  http://www.phpguru.org/static/license.html                  |
* o------------------------------------------------------------------------------o
*
*  Copyright 2008,2009 Richard Heyes
*/

/**
* Global vars
*/
__AutoComplete = new Array();

// Basic UA detection
isIE = document.all ? true : false;
isGecko = navigator.userAgent.toLowerCase().indexOf('gecko') != -1;
isOpera = navigator.userAgent.toLowerCase().indexOf('opera') != -1;


/**
* Attachs the autocomplete object to a form element. Sets
* onkeypress event on the form element.
* 
* @param string formElement Name of form element to attach to
* @param array  data        Array of strings of which to use as the autocomplete data
*/
function AutoComplete_Create(id, data) {
    __AutoComplete[id] = { 'data': data,
        'isVisible': false,
        'element': document.getElementById(id),
        'dropdown': null,
        'highlighted': null
    };

    __AutoComplete[id]['element'].setAttribute('autocomplete', 'off');
    __AutoComplete[id]['element'].onkeydown = function(e) { return AutoComplete_KeyDown(this.getAttribute('id'), e); }
    __AutoComplete[id]['element'].onkeyup = function(e) { return AutoComplete_KeyUp(this.getAttribute('id'), e); }
    __AutoComplete[id]['element'].onkeypress = function(e) { if (!e) e = window.event; if (e.keyCode == 13 || isOpera) return false; }
    __AutoComplete[id]['element'].ondblclick = function() { AutoComplete_ShowDropdown(this.getAttribute('id')); }
    __AutoComplete[id]['element'].onclick = function(e) { if (!e) e = window.event; e.cancelBubble = true; e.returnValue = false; }

    // Hides the dropdowns when document clicked
    var docClick = function() {
        for (id in __AutoComplete) {
            AutoComplete_HideDropdown(id);
        }
    }

    if (document.addEventListener) {
        document.addEventListener('click', docClick, false);
    } else if (document.attachEvent) {
        document.attachEvent('onclick', docClick, false);
    }


    // Max number of items shown at once
    if (arguments[2] != null) {
        __AutoComplete[id]['maxitems'] = arguments[2];
        __AutoComplete[id]['firstItemShowing'] = 0;
        __AutoComplete[id]['lastItemShowing'] = arguments[2] - 1;
    }

    AutoComplete_CreateDropdown(id);

    // Prevent select dropdowns showing thru
    if (isIE) {
        __AutoComplete[id]['iframe'] = document.createElement('iframe');
        __AutoComplete[id]['iframe'].id = id + '_iframe';
        __AutoComplete[id]['iframe'].style.position = 'absolute';
        __AutoComplete[id]['iframe'].style.top = '0';
        __AutoComplete[id]['iframe'].style.left = '0';
        __AutoComplete[id]['iframe'].style.width = '0px';
        __AutoComplete[id]['iframe'].style.height = '0px';
        __AutoComplete[id]['iframe'].style.zIndex = '98';
        __AutoComplete[id]['iframe'].style.visibility = 'hidden';

        __AutoComplete[id]['element'].parentNode.insertBefore(__AutoComplete[id]['iframe'], __AutoComplete[id]['element']);
    }
}


/**
* Creates the dropdown layer
* 
* @param string id The form elements id. Used to identify the correct dropdown.
*/
function AutoComplete_CreateDropdown(id) {
    var left = AutoComplete_GetLeft(__AutoComplete[id]['element']);
    var top = AutoComplete_GetTop(__AutoComplete[id]['element']) + __AutoComplete[id]['element'].offsetHeight;
    var width = __AutoComplete[id]['element'].offsetWidth;

    __AutoComplete[id]['dropdown'] = document.createElement('div');
    __AutoComplete[id]['dropdown'].className = 'autocomplete'; // Don't use setAttribute()

    __AutoComplete[id]['element'].parentNode.insertBefore(__AutoComplete[id]['dropdown'], __AutoComplete[id]['element']);

    // Position it
    __AutoComplete[id]['dropdown'].style.left = left + 'px';
    __AutoComplete[id]['dropdown'].style.top = top + 'px';
    __AutoComplete[id]['dropdown'].style.width = width + 'px';
    __AutoComplete[id]['dropdown'].style.zIndex = '99';
    __AutoComplete[id]['dropdown'].style.visibility = 'hidden';
}


/**
* Gets left coord of given element
* 
* @param object element The element to get the left coord for
*/
function AutoComplete_GetLeft(element) {
    var curNode = element;
    var left = 0;

    do {
        left += curNode.offsetLeft;
        curNode = curNode.offsetParent;

    } while (curNode.tagName.toLowerCase() != 'body');

    return left;
}


/**
* Gets top coord of given element
* 
* @param object element The element to get the top coord for
*/
function AutoComplete_GetTop(element) {
    var curNode = element;
    var top = 0;

    do {
        top += curNode.offsetTop;
        curNode = curNode.offsetParent;

    } while (curNode.tagName.toLowerCase() != 'body');

    return top;
}


/**
* Shows the dropdown layer
* 
* @param string id The form elements id. Used to identify the correct dropdown.
*/
function AutoComplete_ShowDropdown(id) {
    AutoComplete_HideAll();

    var value = __AutoComplete[id]['element'].value;
    var toDisplay = new Array();
    var newDiv = null;
    var text = null;
    var numItems = __AutoComplete[id]['dropdown'].childNodes.length;

    // Remove all child nodes from dropdown
    while (__AutoComplete[id]['dropdown'].childNodes.length > 0) {
        __AutoComplete[id]['dropdown'].removeChild(__AutoComplete[id]['dropdown'].childNodes[0]);
    }

    // Go thru data searching for matches
    for (i = 0; i < __AutoComplete[id]['data'].length; ++i) {
        if (__AutoComplete[id]['data'][i].substr(0, value.length) == value) {
            toDisplay[toDisplay.length] = __AutoComplete[id]['data'][i];
        }
    }

    // No matches?
    if (toDisplay.length == 0) {
        AutoComplete_HideDropdown(id);
        return;
    }



    // Add data to the dropdown layer
    for (i = 0; i < toDisplay.length; ++i) {
        newDiv = document.createElement('div');
        newDiv.className = 'autocomplete_item'; // Don't use setAttribute()
        newDiv.setAttribute('id', 'autocomplete_item_' + i);
        newDiv.setAttribute('index', i);
        newDiv.style.zIndex = '99';

        // Scrollbars are on display ?
        if (toDisplay.length > __AutoComplete[id]['maxitems'] && navigator.userAgent.indexOf('MSIE') == -1) {
            newDiv.style.width = __AutoComplete[id]['element'].offsetWidth - 22 + 'px';
        }

        newDiv.onmouseover = function() { AutoComplete_HighlightItem(__AutoComplete[id]['element'].getAttribute('id'), this.getAttribute('index')); };
        newDiv.onclick = function() { AutoComplete_SetValue(__AutoComplete[id]['element'].getAttribute('id')); AutoComplete_HideDropdown(__AutoComplete[id]['element'].getAttribute('id')); }

        text = document.createTextNode(toDisplay[i]);
        newDiv.appendChild(text);

        __AutoComplete[id]['dropdown'].appendChild(newDiv);
    }


    // Too many items?
    if (toDisplay.length > __AutoComplete[id]['maxitems']) {
        __AutoComplete[id]['dropdown'].style.height = (__AutoComplete[id]['maxitems'] * 15) + 2 + 'px';

    } else {
        __AutoComplete[id]['dropdown'].style.height = '';
    }


    /**
    * Set left/top in case of document movement/scroll/window resize etc
    */
    __AutoComplete[id]['dropdown'].style.left = AutoComplete_GetLeft(__AutoComplete[id]['element']);
    __AutoComplete[id]['dropdown'].style.top = AutoComplete_GetTop(__AutoComplete[id]['element']) + __AutoComplete[id]['element'].offsetHeight;


    // Show the iframe for IE
    if (isIE) {
        __AutoComplete[id]['iframe'].style.top = __AutoComplete[id]['dropdown'].style.top;
        __AutoComplete[id]['iframe'].style.left = __AutoComplete[id]['dropdown'].style.left;
        __AutoComplete[id]['iframe'].style.width = __AutoComplete[id]['dropdown'].offsetWidth;
        __AutoComplete[id]['iframe'].style.height = __AutoComplete[id]['dropdown'].offsetHeight;

        __AutoComplete[id]['iframe'].style.visibility = 'visible';
    }


    // Show dropdown
    if (!__AutoComplete[id]['isVisible']) {
        __AutoComplete[id]['dropdown'].style.visibility = 'visible';
        __AutoComplete[id]['isVisible'] = true;
    }


    // If now showing less items than before, reset the highlighted value
    if (__AutoComplete[id]['dropdown'].childNodes.length != numItems) {
        __AutoComplete[id]['highlighted'] = null;
    }
}


/**
* Hides the dropdown layer
* 
* @param string id The form elements id. Used to identify the correct dropdown.
*/
function AutoComplete_HideDropdown(id) {
    if (__AutoComplete[id]['iframe']) {
        __AutoComplete[id]['iframe'].style.visibility = 'hidden';
    }


    __AutoComplete[id]['dropdown'].style.visibility = 'hidden';
    __AutoComplete[id]['highlighted'] = null;
    __AutoComplete[id]['isVisible'] = false;
}


/**
* Hides all dropdowns
*/
function AutoComplete_HideAll() {
    for (id in __AutoComplete) {
        AutoComplete_HideDropdown(id);
    }
}


/**
* Highlights a specific item
* 
* @param string id    The form elements id. Used to identify the correct dropdown.
* @param int    index The index of the element in the dropdown to highlight
*/
function AutoComplete_HighlightItem(id, index) {
    if (__AutoComplete[id]['dropdown'].childNodes[index]) {
        for (var i = 0; i < __AutoComplete[id]['dropdown'].childNodes.length; ++i) {
            if (__AutoComplete[id]['dropdown'].childNodes[i].className == 'autocomplete_item_highlighted') {
                __AutoComplete[id]['dropdown'].childNodes[i].className = 'autocomplete_item';
            }
        }

        __AutoComplete[id]['dropdown'].childNodes[index].className = 'autocomplete_item_highlighted';
        __AutoComplete[id]['highlighted'] = index;
    }
}


/**
* Highlights the menu item with the given index
* 
* @param string id    The form elements id. Used to identify the correct dropdown.
* @param int    index The index of the element in the dropdown to highlight
*/
function AutoComplete_Highlight(id, index) {
    // Out of bounds checking
    if (index == 1 && __AutoComplete[id]['highlighted'] == __AutoComplete[id]['dropdown'].childNodes.length - 1) {
        __AutoComplete[id]['dropdown'].childNodes[__AutoComplete[id]['highlighted']].className = 'autocomplete_item';
        __AutoComplete[id]['highlighted'] = null;

    } else if (index == -1 && __AutoComplete[id]['highlighted'] == 0) {
        __AutoComplete[id]['dropdown'].childNodes[0].className = 'autocomplete_item';
        __AutoComplete[id]['highlighted'] = __AutoComplete[id]['dropdown'].childNodes.length;
    }

    // Nothing highlighted at the moment
    if (__AutoComplete[id]['highlighted'] == null) {
        __AutoComplete[id]['dropdown'].childNodes[0].className = 'autocomplete_item_highlighted';
        __AutoComplete[id]['highlighted'] = 0;

    } else {
        if (__AutoComplete[id]['dropdown'].childNodes[__AutoComplete[id]['highlighted']]) {
            __AutoComplete[id]['dropdown'].childNodes[__AutoComplete[id]['highlighted']].className = 'autocomplete_item';
        }

        var newIndex = __AutoComplete[id]['highlighted'] + index;

        if (__AutoComplete[id]['dropdown'].childNodes[newIndex]) {
            __AutoComplete[id]['dropdown'].childNodes[newIndex].className = 'autocomplete_item_highlighted';

            __AutoComplete[id]['highlighted'] = newIndex;
        }
    }
}


/**
* Sets the input to a given value
* 
* @param string id    The form elements id. Used to identify the correct dropdown.
*/
function AutoComplete_SetValue(id) {
    __AutoComplete[id]['element'].value = __AutoComplete[id]['dropdown'].childNodes[__AutoComplete[id]['highlighted']].innerHTML;
}


/**
* Checks if the dropdown needs scrolling
* 
* @param string id    The form elements id. Used to identify the correct dropdown.
*/
function AutoComplete_ScrollCheck(id) {
    // Scroll down, or wrapping around from scroll up
    if (__AutoComplete[id]['highlighted'] > __AutoComplete[id]['lastItemShowing']) {
        __AutoComplete[id]['firstItemShowing'] = __AutoComplete[id]['highlighted'] - (__AutoComplete[id]['maxitems'] - 1);
        __AutoComplete[id]['lastItemShowing'] = __AutoComplete[id]['highlighted'];
    }

    // Scroll up, or wrapping around from scroll down
    if (__AutoComplete[id]['highlighted'] < __AutoComplete[id]['firstItemShowing']) {
        __AutoComplete[id]['firstItemShowing'] = __AutoComplete[id]['highlighted'];
        __AutoComplete[id]['lastItemShowing'] = __AutoComplete[id]['highlighted'] + (__AutoComplete[id]['maxitems'] - 1);
    }

    __AutoComplete[id]['dropdown'].scrollTop = __AutoComplete[id]['firstItemShowing'] * 15;
}


/**
* Function which handles the keypress event
* 
* @param string id    The form elements id. Used to identify the correct dropdown.
*/
function AutoComplete_KeyDown(id) {
    // Mozilla
    if (arguments[1] != null) {
        event = arguments[1];
    }

    var keyCode = event.keyCode;

    switch (keyCode) {

        // Return/Enter 
        case 13:
            if (__AutoComplete[id]['highlighted'] != null) {
                AutoComplete_SetValue(id);
                AutoComplete_HideDropdown(id);
            }

            event.returnValue = false;
            event.cancelBubble = true;
            break;

        // Escape 
        case 27:
            AutoComplete_HideDropdown(id);
            event.returnValue = false;
            event.cancelBubble = true;
            break;

        // Up arrow 
        case 38:
            if (!__AutoComplete[id]['isVisible']) {
                AutoComplete_ShowDropdown(id);
            }

            AutoComplete_Highlight(id, -1);
            AutoComplete_ScrollCheck(id, -1);
            return false;
            break;

        // Tab 
        case 9:
            if (__AutoComplete[id]['isVisible']) {
                AutoComplete_HideDropdown(id);
            }
            return;

            // Down arrow
        case 40:
            if (!__AutoComplete[id]['isVisible']) {
                AutoComplete_ShowDropdown(id);
            }

            AutoComplete_Highlight(id, 1);
            AutoComplete_ScrollCheck(id, 1);
            return false;
            break;
    }
}


/**
* Function which handles the keyup event
* 
* @param string id    The form elements id. Used to identify the correct dropdown.
*/
function AutoComplete_KeyUp(id) {
    // Mozilla
    if (arguments[1] != null) {
        event = arguments[1];
    }

    var keyCode = event.keyCode;

    switch (keyCode) {
        case 13:
            event.returnValue = false;
            event.cancelBubble = true;
            break;

        case 27:
            AutoComplete_HideDropdown(id);
            event.returnValue = false;
            event.cancelBubble = true;
            break;

        case 38:
        case 40:
            return false;
            break;

        default:
            AutoComplete_ShowDropdown(id);
            break;
    }
}

/**
* Returns whether the dropdown is visible
* 
* @param string id    The form elements id. Used to identify the correct dropdown.
*/
function AutoComplete_isVisible(id) {
    return __AutoComplete[id]['dropdown'].style.visibility == 'visible';
}

function focusText(obj, text) {
    if (obj.value == text) {
        obj.value = '';
//        obj.style.color = 'ffffff';
    }
}

function blurText(obj, text) {
    if (obj.value == '') {
        obj.value = text;
//        obj.style.color = '';
    }
}
function setBlFo(o, t) {
    if ($(o) == null)
        return;
    $(o).value = t;
    $(o).dval = t;
    $(o).onblur = function() { blurText($(o), t) };
    $(o).onfocus = function() { focusText($(o), t) };

    $(o).onkeypress = function(event) {
        var kc;
        if (window.event) kc = window.event.keyCode;
        else if (e) kc= e.which;
        else return true;
        return (kc != 13);
    };
}


function disablebackspace() {
    if (window.focus) window.focus();
    document.onkeydown = catchbackspace;
    document.onkeyup = catchbackspace;
}

function catchbackspace() {
    var kc;
    if (window.event) kc = window.event.keyCode;
    else if (e) kc = e.which;
    else return true;
    if (kc == 8 || kc == 13) {
        return false;
    }
}


function changeActiveTabEx(tabControl, theTab, linkX, theType) {
    tabControl = document.getElementById(tabControl);

    if (tabControl == null) {
        alert("TabControl specified is null");
        return;
    }

    if (linkX == null) {
        return;
    }

    linkX.parentNode.className = 'selected';

    for (i = 0; i < linkX.parentNode.parentNode.childNodes.length; i++) {
        if (linkX.parentNode.parentNode.childNodes[i] != linkX.parentNode)
            linkX.parentNode.parentNode.childNodes[i].className = null;
    }

    $('tabLoader').show();
    $('tabContent').update('');

    new Ajax.Updater('tabContent', theTab, { method: 'get', evalScripts: 'true', parameters: { 'type': theType },
        onComplete: function () { $('tabLoader').hide(); }, onFailure: function () { alert(response.responseText); }
    });
}


if (typeof console != 'undefined') {
    if (typeof console.debug == 'undefined') {
        console.debug = function (name, value) {
            console.warn("DEBUG: " + name + "==" + value);
        }
    }

    if (typeof console.log == 'undefined') {
        console.log = function (name, value) {
            console.warn("LOG: " + name + "==" + value);
        }
    }
}
else {
    console = "";
    console.log = function () { };
    console.debug = function () { };
    console.warn = function () { };
}


function isValidDate(subject){
if (subject == null || subject.length == 0) return true;

	if (isNaN(Date.parse(subject)))
		return false;
	else
		return true;
}

window.getUrlParameter = (function() {
    var startBracketRe = /[\[]/, endBracketRe = /[\]]/, regexes = {};
    return function (name) {
        name = name.replace(startBracketRe, '\\[').replace(endBracketRe, '\\]');
        var regex = regexes[name] || (regexes[name] = new RegExp('[\\?&]' + name + '=([^&#]*)'));
        var results = regex.exec(location.search);
        return results === null ? null : decodeURIComponent(results[1].replace(/\+/g, ' '));
    };
})();