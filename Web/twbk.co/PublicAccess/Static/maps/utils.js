//////////////////////////
// ViewModel functions
//////////////////////////

var CallViewModel = function (call) {
    this.model = call;
    this.id = call.id;
    this.companyId = call.companyId;
    this.status = call.status;
    this.statuses = call.statuses;
    this.statusId = isScheduled(call) ? 254 : call.status.id;
    this.statusName = getCallStatusText(isScheduled(call) ? 254 : call.status.id);
    this.statusAbb = getCallStatusText(isScheduled(call) ? 254 : call.status.id).replace(' ', '').toLowerCase();
    this.callNumber = call.callNumber;
    this.invoiceNumber = call.invoiceNumber;
    this.priority = call.priority;
    this.reason = (call.reason ? call.reason.name : "");
    this.account = call.account;
    this.accountName = (call.account && call.account.company) || '&nbsp;';
    this.attributes = call.attributes;
    this.assets = call.assets;
    this.drivers = getDrivers(call);
    this.driverName = getDriverName(call).name;
    this.driverInitials = getDriverName(call).initials;
    this.vehicle = getVehicle(call);
    this.vehicles = getVehicles(call);
    this.waypoints = call.waypoints;
    this.stepWaypoints = getStepWaypoints(call),
    this.towSource = call.towSource;
    this.destination = getFinalDestinationAddress(call);
    this.destinationLabel = getFinalDestinationLabel(call);
    this.impound = call.impound;
    this.impoundLot = call.impound ? towbook.get(towbook.impoundLots, call.impoundLotId) : null;
    this.isService = ((call.towDestination || "").length < 3 && !call.impound) ? true : false;
    this.poNumber = call.purchaseOrderNumber;
    this.acceptReject = getAcceptRejectStatus(call);
    this.createDate = call.createDate;
    this.dispatchTime = call.dispatchTime;
    this.enrouteTime = call.enrouteTime;
    this.arrivalETA = call.arrivalETA;
    this.arrivalTime = call.arrivalTime;
    this.towTime = call.towTime;
    this.destinationArrivalTime = call.destinationArrivalTime;
    this.completionTime = call.completionTime;
    this.callStyle2 = call.priority == 2 ? 'color:#999' : this.isService ? 'color: #01b901' : '';
    this.hasTolls = false;
    this.calculatedEta = call.calculatedEta || {};
    this.isClosed = call.insights && call.insights.isClosed == true || false;
    this.insights = call.insights;
    
    this.isTow = function () {
      return this.destination && this.destination.length > 3 && this.waypoints.length > 1;
    };
    this.isTowOut = call.attributes.filter(f => { return f.attributeId == 53; }).length == 1;
    this.notes = call.notes;
    this.isScheduled = function () { return isScheduled(call); };
}

var DriverViewModel = function (user, driver, location, statuses) {
    this.id = (driver && 'd' + driver.id) || 'u' + user.id;
    this.name = (driver && driver.name) || user.name;
    this.companyId = (driver && driver.companyId) || user.companyId;
    this.companies = driver && driver.companies;
    this.user = user;
    this.userId = user && user.id;
    this.driver = driver;
    this.driverId = driver && driver.id;
    this.initials = getShortName(this.name, 3);
    this.location = location;
    this.statuses = sortStatuses(statuses) || null;
    this.checkedIn = (user && user.checkedIn) || false;
    this.updated = function () {
        this.highestStatus = this.driver ? getHighestStatus(this.statuses) : null;
        this.statusId = this.highestStatus && this.highestStatus.statusId;
        this.statusName = getCallStatusText(this.statusId);
        this.statusAbb = getCallStatusText(this.statusId).replace(' ', '').toLowerCase();
        this.timestamp = this.location && this.location.timestamp;
        this.gpsSourceLabel = this.location && getGpsSourceLabel(this.location.gpsSource || '');
        this.gpsSourceIcon = this.location && this.location.gpsSource == 'mobile' ? 'mobile' : 'car';
        this.truck = getDriverTruck(this);
        this.truckName = this.truck && this.truck.name;
        this.truckType = getTruckTypeText(this.truck && this.truck.type);
        this.phone = this.driver && this.driver.phone ? towbook.formatPhoneNumber(this.driver.phone) : ''
    }
    this.updated();
}

var CompanyViewModel = function (company) {
    this.id = company.id;
    this.name = company.name;
    this.city = company.city;
    this.state = company.state;
    this.zip = company.zip;
    this.companyId = company.id;
    this.lots = company.lots;
    this.latitude = company.latitude;
    this.longitude = company.longitude;
    this.props = [{
        key: "Address",
        val: company.address,
    }, {
        key: "Phone",
        val: company.phone,
    }];
    this.childProps = [];
}

var LotViewModel = function (lot, company) {
    this.id = lot.id;
    this.name = lot.name;
    this.companyId = company.id;
    this.latitude = lot.latitude;
    this.longitude = lot.longitude;
    this.props = [{
        key: "Address",
        val: lot.address,
    }, {
        key: "Contact",
        val: lot.contact,
    }, {
        key: "Phone",
        val: lot.phone,
    }, {
        key: "Capacity",
        val: lot.capacity,
    }];
    this.childProps = [];
}

function getDrivers(call) {
    var drivers =
        // Get all the driver/truck combos for all the assets on the call
        $.map(call.assets, function (asset) {
            return $.map(asset.drivers, function (o) {
                return {
                    driver: o.driver || {},
                    truck: o.truck || {},
                };
            });

            // Filter out any duplicates by comparing driver id's
        }).filter(function (a, i, self) {
            return self.indexOf(self.filter(function (b) {
                return a.driver.id == b.driver.id;
            })[0]) == i;
        });

    return drivers || [];
}

function getDriverName(call) {
    // Get the driver assigned to this call
    var initials = '';
    var name = '';
    if (call.assets[0].driver) {
        var d = towbook.get(towbook.drivers, call.assets[0].driver.id);
        if (d) {
            initials = getShortName(d.name, 3);
            name = d.name;
        }
    }
    return { initials: initials, name: name };
}

function getTruckName(call) {
    // Get the truck assigned to this call, else the truck for the driver assigned to this call
    var initials = '';
    var name = '';
    if (call.assets[0].truck) {
        var t = towbook.get(towbook.trucks, call.assets[0].truck.id);
        if (t) {
            initials = getDigitsOrShortName(t.name);
            name = t.name;
        }
    } else {
        if (call.assets[0].driver) {
            var d = towbook.get(vm.drivers, call.assets[0].driver.id);
            if (d && d.truckName) {
                initials = getDigitsOrShortName(d.truckName);
                name = d.truckName;
            }
        }
    }
    return { initials: initials, name: name };
}

function getVehicle(call) {
    if (call.assets.length > 1)
        return 'Multiple vehicles';
    else {
        return getVehicleStr(call.assets[0]);
    }
}

function getVehicles(call) {
    var assets = call.assets.slice();
    if (call.attributes.filter(f => { return f.attributeId == 53; }).length == 1)
      assets = call.assets.slice(0, 1);

    return $.map(assets, function (asset) {
        return getVehicleStr(asset);
    }) || [];
}

function getVehicleStr(asset) {
    var str = (asset.year > 0 ? "'" + asset.year.toString().substr(-2) : "") + " " +
        (asset.make ? asset.make + " " : "") +
        (asset.model ? asset.model + " " : "") +
        (asset.color ? asset.color.name : "") + " " +
        (asset.licenseNumber ? asset.licenseNumber : "");

    str = str.replace(/null/g, '').trim();

    if (!str || str.length < 3)
        str = "(No vehicle specified)";

    return str;
}

function getTimeLabel(time) {
    if (time.indexOf('T') == -1) return time;
    if (time.indexOf('-') == -1) return time;

    var now = new Date();
    var d = time.split('T')[0].split('-');
    var t = towbook.formatAMPM(new Date(time));

    // If time is on today's date, just return the time (like 9:09 AM)
    if (now.getFullYear() == d[0] && (now.getMonth() + 1) == parseInt(d[1]) && now.getDate() == parseInt(d[2])) {
        return t;

        // Else return the short date and then the time (like Jun 10, 9:09 AM)
    } else {
        var c = new Date(time)
        var cp = c.toLocaleDateString('en-US', { weekday: 'long', year: 'numeric', month: 'short', day: 'numeric' })

        return cp.split(',')[1] + ', ' + t;
    }

    return towbook.formatAMPM(time)
}


function getTimeFormat(time, format) {
    if (!time)
        return '';
    else
        return moment(time).format(format);
}

function getTimeDiffStr(time1, time2) {
    if (!time1 || !time2)
        return '';

    // We're going to do something subtle here -- drop the seconds from both time1 and time2
    // so that the time diff between them appears to be in quantities of whole minutes
    var t1 = moment(time1).startOf('minute');
    var t2 = moment(time2).startOf('minute');

    return secondsToString(Math.abs(t2 - t1) / 1000);
}

function getEta(call) {
    var eta = {
        arrived: false,
        late: false,
        title: '',
        text: '&nbsp;',
        arrivalTime: isNaN(new Date(call.arrivalTime).getTime()) ? "" : towbook.parseDateString(call.arrivalTime + 'Z') || towbook.parseDateString(call.towTime + 'Z') || towbook.parseDateString(call.completionTime + 'Z'),
        arrivalETA: towbook.parseDateString(call.arrivalETA + 'Z'),
        bubbleText: ''
    };

    if (eta.arrivalETA && !isNaN(new Date(eta.arrivalETA).getTime())) {

        // Get the difference between arrival time (or right now) and eta, in seconds
        var diff = ((eta.arrivalTime || new Date()) - eta.arrivalETA) / 1000;

        eta.late = isNaN(diff) ? false : diff > 0;

        // Get the whether the eta is in the past
        var past = (eta.arrivalETA - new Date()) < 0;


        // Construct title and text
        eta.title = 'ETA' + (past ? ' was' : ' is') + ' at ' + towbook.formatAMPM(eta.arrivalETA);
        eta.text = secondsToString(Math.abs(diff));

        if (!isNaN(new Date(eta.arrivalTime).getTime())) {
            eta.arrived = true;
            eta.title += ', driver arrived at ' + towbook.formatAMPM(eta.arrivalTime);

            if (eta.late)
                eta.text = 'Arrived ' + eta.text + ' late';
            else
                eta.text = 'Arrived early';

        } else if (eta.late) {
            eta.text += ' late';
        } else {
          if (!past && call.status.id <= 1)
            eta.bubbleText = eta.text;
        }
    }

    return eta;
}

function secondsToString(seconds) {
    var str = '';
    var minutes = Math.floor((seconds / 60) % 60);
    var hours = Math.floor((seconds / (60 * 60)) % 24);
    var days = Math.floor(seconds / (60 * 60 * 24));

    if (days) {
        str += days + (days == 1 ? " day " : " days ");
        if (hours)
            str += hours + (hours == 1 ? " hr " : " hrs ");
    }
    else if (hours) {
        str += hours + (hours == 1 ? " hr " : " hrs ");
        if (minutes)
            str += minutes + " min ";
    }
    else if (minutes) {
        str += minutes + " min ";
    }
    else {
        str += " < 1 min ";
    }

    return str.trim();
}

function getEtaCountDown(etaUTC) { // ensure you're sending this UTC time (should have a 'Z' on the end)
    var eta = { num: '', units: '' };

    if (etaUTC) {
        var now = new Date();
        var now_utc = new Date(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate(), now.getUTCHours(), now.getUTCMinutes(), now.getUTCSeconds());
        var etaDate = towbook.parseDateString(etaUTC);
        var diff = (etaDate.getTime() - now_utc.getTime()) / 3600000;

        // If its not a scheduled call (less than 6 hrs in the future)
        if (diff <= 6) {

            if (Math.abs(diff * 60 * 60) <= 59) {
                // Get the eta in seconds
                eta.num = (diff * 60 * 60).toFixed(0);
                eta.units = 'SEC';
            } else if (Math.abs(diff * 60) <= 59) {
                // Get the eta in minutes
                eta.num = (diff * 60).toFixed(0);
                eta.units = 'MIN';
            } else {
                // Get the eta in hours
                eta.num = (diff).toFixed(1);
                eta.units = 'HRS';
            }

            if (eta.num == '-0') eta.num = '0';
        }
    }

    return eta;
}

function getAcceptRejectStatus(call) {
    var rs = { status: 0, title: '', time: null };

    if (!towbook.isEmpty(call.assets) && !towbook.isEmpty(call.assets[0].drivers)) {
        var driver = call.assets[0].drivers[0].driver;
        if (driver) {
            rs.status = driver.responseStatusId;
            rs.time = driver.responseTime;
            if (rs.status == 1)
                rs.title = driver.name + ' accepted the dispatch';
            if (rs.status == 2)
                rs.title = driver.name + ' rejected the dispatch';
        }
    }

    return rs;
}

function getIntermediateWaypoints(call) {
  if (call.waypoints && call.waypoints.length > 0) {
    return call.waypoints
      // take all but first and last
      .slice(1, -1)
      // must have latitude
      .filter(f => {
        return !isNaN(parseFloat(f.latitude));
      })
      .map(wp => {
        return { waypointId: wp.id, latLng: new google.maps.LatLng(wp.latitude, wp.longitude) };
      });
  }
  return [];
}

function getStepWaypoints(call) {
  var steps = [];
  if (call.waypoints && call.waypoints.length > 0) {
      call.waypoints
      // take all but first and last
      .slice(1, -1)
      // must have latitude
      .filter(f => {
        return !isNaN(parseFloat(f.latitude));
      })
      .map(wp => {
        var waypoint = JSON.parse(JSON.stringify(wp));

        waypoint.latLng = new google.maps.LatLng(wp.latitude, wp.longitude);

        steps.push(waypoint);
      });
  }
  return steps;
}

function getFinalDestiantionWaypoint(call) {
  if (call.waypoints && call.waypoints.length) {
    var destWp = call.waypoints.find(f => (f.title || "").toLowerCase() == "destination");

    // consider towouts
    if (call.attributes.filter(f => { return f.attributeId == 53; }).length == 1)
      destWp = call.waypoints.find(f => (f.title || "").toLowerCase() == "tow out destination");

    if (destWp)
      return destWp;
  }

  return null;
}

function getFinalDestinationAddress(call) {
  if (call.waypoints && call.waypoints.length) {

    var destWp = getFinalDestiantionWaypoint(call);

    if (destWp)
      return destWp.address;
  }

  return call.towDestination;
}

function getFinalDestinationLabel(call) {
  if (call.waypoints && call.waypoints.length) {

    var destWp = getFinalDestiantionWaypoint(call);

    if (destWp)
      return destWp.title;
  }

  return "Destination";
}

function getCurrentWaypoint(call) {
    var waypoint = null;

    // If this call has waypoints (you never know...)
    if (call.waypoints && call.waypoints.length > 0) {

        // If call status is on-scene or greater and has multiple waypoints, return next step or destination
        if ((call.status.id >= 3 || call.isTowOut) && call.waypoints.length > 1) {

          // first, assume second waypoint is the destination
          if ((call.waypoints[1].latitude && call.waypoints[1].longitude) || call.waypoints[1].address)
            waypoint = call.waypoints[1];

          // second, if next status waypoint id is present use the next waypoint (covers multi-step tows)
          if (call.status.next && call.waypoints.length > 2) {
            var nextWp = call.waypoints.find(f => f.id === (call.status.next.waypointId || 0));
            if (nextWp && ((nextWp.latitude && nextWp.longitude) || nextWp.address))
              waypoint = nextWp;
          }

          // third, if the status is completed, find and use the destination waypoint
          var destWp = getFinalDestiantionWaypoint(call);

          if (call.status.id == 5 && destWp)
            waypoint = destWp;
        } else { // else return the tow source
            waypoint = call.waypoints[0];
        }
    }

    return waypoint;
}

function getLocationFromAddress(address, done) {
    geocoder.geocode({ 'address': address }, function (results, status) {
        if (status === google.maps.GeocoderStatus.OK) {
            done(results[0].geometry.location)
        } else {
            done(null, status)
        }
    })
}

function updateDriverStatuses() {
    towbook.driverStatuses = [];
    towbook.calls.forEach(function (call) {
        // Get the driver statuses for new calls, or calls completed today
        if (call.status.id < 5 || call.status.id == 7 || (call.status.id == 5 && moment(call.completionTime) >= moment().startOf('day'))) {
            call.assets.forEach(function (asset) {
                asset.drivers.forEach(function (driver) {
                    var statusTime;
                    switch (call.status.id) {
                        case 0: statusTime = call.createDate; break;
                        case 1: statusTime = call.dispatchTime; break;
                        case 2: statusTime = call.enrouteTime; break;
                        case 3: statusTime = call.arrivalTime; break;
                        case 4: statusTime = call.towTime; break;
                        case 7: statusTime = call.destinationArrivalTime; break;
                        case 5: statusTime = call.completionTime; break;
                        default: statusTime = null; break;
                    }

                    // If we don't already have this driver's status for this call
                    if (towbook.driverStatuses.filter(function (s) {
                        return s.callId == call.id &&
                            s.statusId == call.status.id &&
                            s.driverId == (driver.driver && driver.driver.id);
                    }).length == 0) {
                        // Add it to the list
                        towbook.driverStatuses.push({
                            companyId: call.companyId,
                            callId: call.id,
                            callNumber: call.callNumber,
                            statusId: call.status.id,
                            statusTime: statusTime,
                            driverId: (driver.driver && driver.driver.id),
                            truckId: (driver.truck && driver.truck.id)
                        });
                    }
                });
            });
        }
    });
}

function addCallToCurrentList(call) {
  if (!call || !call.status)
    return false;

  //current calls
  if (currList == 0) {
    return [0, 1, 2, 3, 4, 7, 252, 253].indexOf(call.status.id) != -1
      && !call.isScheduled();
  }

  // waiting calls
  if (currList == 1) {
    return [0].indexOf(call.status.id) != -1
      && !call.isScheduled();
  }

  // Completed calls
  if (currList == 2) {
    return [5].indexOf(call.status.id) != -1 && 
      !call.isScheduled();
  }

  // driver list
  if (currList == 3) {
    return [0, 1, 2, 3, 4, 7].indexOf(call.status.id) != -1
      && !call.isScheduled();
  }

  // scheduled calls
  if (currList == 4)
    return call.isScheduled();

  // active calls
  if (currList == 5) {
    return [1, 2, 3, 4, 7].indexOf(call.status.id) != -1
      && !call.isScheduled();
  }
  
  return false;
}

function sortStatuses(statuses) {
    if (statuses && statuses.length > 1) {

        // Sort by statusId (descending), then by statusTime (ascending)
        statuses.sort(function (a, b) {
            if (a.statusId == 7 && b.statusId == 5) return 1;
            if (a.statusId == 5 && b.statusId == 7) return -1;
            if (a.statusId < b.statusId) return 1;
            if (a.statusId > b.statusId) return -1;
            if (a.statusId == b.statusId) {
                var aTime = moment(a.statusTime);
                var bTime = moment(b.statusTime);
                if (aTime < bTime) return -1;
                if (aTime > bTime) return 1;
                return 0;
            }
        });
    }
    return statuses;
}

function getHighestStatus(statuses) {
    var filtered = statuses.filter(function (status) {
        return status.statusId < 5 || status.statusId == 7;
    });
    if (filtered.length > 0) {
        return sortStatuses(filtered)[0];
    }
    return null;
}

function getDriverTruck(driver) {
    var truck = null;

    // If this driver's location included a truckId
    if (driver.location && driver.location.truckId) {
        truck = towbook.get(towbook.trucks, driver.location.truckId);
    }

    if (!truck) {
        // Assuming statuses have already been filtered by driverId and sorted by statusId,
        // get the first status with a truck assigned to it (and not yet completed)
        if (driver.statuses && driver.statuses.length > 0) {
            for (var i = 0; i < driver.statuses.length; i++) {
                if (driver.statuses[i].truckId && (driver.statuses[i].statusId < 5 || driver.statuses[i].statusId == 7)) { 
                    truck = towbook.get(towbook.trucks, driver.statuses[i].truckId);
                    break;
                }
            }
        }
    }

    if (!truck) {
        // Otherwise get the truck assigned to this driver
        var dfd = towbook.get(towbook.driverTruckDefaults, driver.driverId, 'driverId');
        if (dfd) {
            truck = towbook.get(towbook.trucks, dfd.truckId);
        }
    }

    return truck;
}

function getShortName(name, maxChars) {
    var nameArray = name.split(' ');
    var firstWord = nameArray[0];
    if (firstWord.replace(/\D+/g, ''))
        return firstWord;

    var sn = name.split(' ').map(function (s) {
        return s.charAt(0).match(/^[a-zA-Z]*$/) ? s.charAt(0) : '';
    }).join('');

    return maxChars ? sn.substring(0, maxChars) : sn;
}

function getDigitsOrShortName(name) {
    var nameArray = name.split(' ');
    var firstWord = nameArray[0];
    if (firstWord.replace(/\D+/g, ''))
        return firstWord;

    var digits = name.replace(/\D+/g, '');
    if (digits)
        return '#' + digits;
    else
        return getShortName(name, 3);
}

function isCoordNear(a, b) {
    if (Math.abs(a.latitude - b.latitude) < 0.0001 &&
        Math.abs(a.longitude - b.longitude) < 0.0001) {
        return true;
    }

    return false;
}


//////////////////////
// UI functions
//////////////////////

function getCallStatusText(status) {
    const statusText = towbook.callStatusNames[status];
    return statusText ? statusText : '';
}

function getDriverStatusText(status) {
    switch (status) {
        case 0: return "Assigned to";
        case 1: return 'Dispatched to';
        case 2: return "Enroute to";
        case 3: return "On scene for";
        case 4: return "Towing";
        case 7: return "Destination Arrival for"
        case 5: return "Finished";
        default: return '';
    }
}

function getTruckTypeText(type) {
    switch (type) {
        case 1: return "Wrecker";
        case 2: return "Flatbed";
        case 3: return "Other";
        default: return "";
    }
}

function getGpsSourceLabel(str) {
    switch (str.toLowerCase()) {
        case 'mobile': return 'Mobile App';
        case 'geotab': return 'GeoTab';
        case 'tomtom': return 'TomTom';
        case 'driverlocate': return 'Driver Locate';
        case 'usft': return 'US Fleet Tracking';
        default: return str;
    }
}

function getAcceptRejectText(status) {
    switch (status) {
        case 1: return 'Accepted';
        case 2: return 'Rejected';
        default: return '';
    }
}

function compileTemplate(name, el) {
    // If template 'name' doesn't exist, compile it
    if (typeof ($.template[name]) == 'undefined')
        el.template(name);
}

function applyTemplate(name, obj) {
    // Fill template 'name' with data from obj
    return $.tmpl(name, obj);
}

function populateTemplate(el, obj) {
    // Compile template using el's id as the name
    // then fill template with data from obj
    var name = el.attr('id').substring(1);
    compileTemplate(name, el);
    return applyTemplate(name, obj);
}

function getDriverLink(driverId, name) {
    var driver = towbook.get(vm.drivers, driverId, 'driverId');
    if (driver) {
        var marker = tbmap.findDriver(driver.id);
        if (marker) {
            return '<a href="#" class="map-link" ' +
                'onmouseover="doMouseOut = true; highlightDriver(\'' + driver.id + '\')" ' +
                'onmouseout="if (doMouseOut) unHighlightDriver(\'' + driver.id + '\')" ' +
                'onclick= "doMouseOut = false; showDriver(\'' + driver.id + '\')">' + ($('<a/>').text(name).html())+ '</a>';
        } else {
            return '<a href="#" class="no-map-link" ' +
                'onmouseover="doMouseOut = true; highlightDriver(\'' + driver.id + '\')" ' +
                'onmouseout="if (doMouseOut) unHighlightDriver(\'' + driver.id + '\')" ' +
                'onclick= "doMouseOut = false; showDriver(\'' + driver.id + '\')">' + ($('<a/>').text(name).html()) + '</a>';
        }
    } else {
        return ($('<a/>').text(name).html());
    }
}

function getCallLink(id, callNumber) {
    var call = towbook.get(vm.calls, id);
    if (call) {
        var marker = tbmap.findCall(call.id);
        if (marker) {
            return '<a href="#" class="map-link" ' +
                'onmouseover="doMouseOut = true; highlightCall(' + id + ')" ' +
                'onmouseout="if (doMouseOut) unHighlightCall(' + id + ')" ' +
                'onclick= "doMouseOut = false; showCall(' + id + ')"> Call #' + callNumber + '</a>';
        } else {
            return '<a href="#" class="no-map-link" ' +
                'onmouseover="doMouseOut = true; highlightCall(' + id + ')" ' +
                'onmouseout="if (doMouseOut) unHighlightCall(' + id + ')" ' +
                'onclick= "doMouseOut = false; showCall(' + id + ')"> Call #' + callNumber + '</a>';
        }
    } else {
        return 'Call #' + callNumber;
    }
}

function isScheduled(call) {
  if (call.status && call.status.id == 0 && call.arrivalETA) {
    var currentDate = new Date();
    var etaDate = towbook.parseDateString(call.arrivalETA + 'Z'); // new Date(c.arrivalETA);
    var diff = (etaDate.getTime() - currentDate.getTime()) / 3600000; // get difference in hours
    if (diff > 6)
      return true;
  }

  return false;
}

// Determine if a jQuery object has a vertical scroll bar visible
(function ($) {
    $.fn.hasVerticalScrollBar = function () {
        return this.get(0).scrollHeight > this.get(0).clientHeight;
    }
})(jQuery);

// Determine if a jQuery object has a horizontal scroll bar visible
(function ($) {
    $.fn.hasHorizontalScrollBar = function () {
        return this.get(0).scrollWidth > this.get(0).clientWidth;
    }
})(jQuery);
