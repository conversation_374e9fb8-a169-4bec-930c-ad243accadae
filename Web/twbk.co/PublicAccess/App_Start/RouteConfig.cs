using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.AspNetCore.Routing;

namespace PublicAccess
{
    public class RouteConfig
    {
        public static void RegisterRoutes(RouteCollection routes)
        {
            /*routes.IgnoreRoute("{resource}.axd/{*pathInfo}");
            routes.IgnoreRoute("{resource}.css/{*pathInfo}");
            routes.IgnoreRoute("{resource}.js/{*pathInfo}");
            routes.IgnoreRoute("{resource}.woff/{*pathInfo}");
            routes.IgnoreRoute("{resource}.woff2/{*pathInfo}");

            #region Driver Replay
            routes.MapRoute(
                name: "DriverReplay",
                url: "dr/{id}",
                defaults: new { controller = "DriverReplay", action = "Index" }
            );
            #endregion

            #region Quote Routes
            routes.MapRoute(
                name: "ShortQuotes",
                url: "q/{id}/{action}",
                defaults: new { controller = "Quotes", action = "Index", id = UrlParameter.Optional }
            );
            #endregion

            #region Signature Routes
            routes.MapRoute(
                name: "ShortSignatures",
                url: "sig/{id}/{action}",
                defaults: new { controller = "Signatures", action = "Index", id = UrlParameter.Optional }
            );
            #endregion

            #region Invoice routes
            routes.MapRoute(
                name: "ShortInvoices",
                url: "i/{id}/{action}",
                defaults: new { controller = "Invoices", action = "Index", id = UrlParameter.Optional }
            );
            #endregion

            #region Statement Routes
            routes.MapRoute(
                name: "ShortStatements",
                url: "s/{id}/{action}",
                defaults: new { controller = "Statements", action = "Index", id = UrlParameter.Optional }
            );
            #endregion

            #region Generic Call/Payment Routes
            routes.MapRoute(
                name: "CallLinkPayment",
                url: "cu/{id}",
                defaults: new { controller = "Payments", action = "CallPaymentLink", id = UrlParameter.Optional }
            );
            #endregion

            #region Generic Call/Payment Routes
            routes.MapRoute(
                name: "StatementLinkPayment",
                url: "su/{id}",
                defaults: new { controller = "Payments", action = "StatementPaymentLink", id = UrlParameter.Optional }
            );
            #endregion

            #region Payment Routes
            routes.MapRoute(
                name: "ShortPaymentsPOST",
                url: "p/{id}",
                defaults: new { controller = "Payments", action = "Post" },
                constraints: new
                {
                    httpMethod = new HttpMethodConstraint(new string[] { "POST" }),
                }
            );

            routes.MapRoute(
                name: "ShortPaymentsDefault",
                url: "p/{id}/{action}",
                defaults: new { controller = "Payments", action = "Index" }
            );
            #endregion



            routes.MapRoute(
                name: "Default",
                url: "{id}/{action}",
                defaults: new { controller = "Home", action = "Index", id = UrlParameter.Optional }
            );*/

        }
    }
}
