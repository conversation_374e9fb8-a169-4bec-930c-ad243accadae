<!DOCTYPE html>
<html ng-app="app" ng-controller="LayoutCtrl">
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <title>{siteName}</title>
    <link href="/content/libs/bootstrap/css/bootstrap.min.css" rel="stylesheet" />
    <link href="/content/app/app.css" rel="stylesheet">
</head>
<body lang="en">
    <div class="navbar navbar-inverse navbar-fixed-top">
        <div class="navbar-header">
            <span class="navbar-brand">{siteName}</span>
        </div>
        <ul class="nav navbar-nav" ng-show="model.currentUser" ng-cloak>
            <li class="dropdown">
                <a href="#" class="dropdown-toggle" data-toggle="dropdown">{{model.currentUser}} <b class="caret"></b></a>
                <ul class="dropdown-menu">
                    <li><a href="{{model.logoutUrl}}">Logout</a></li>
                    <li class="divider" ng-show="model.loginWithDifferentAccountUrl"></li>
                    <li><a href="{{model.loginWithDifferentAccountUrl}}" ng-show="model.loginWithDifferentAccountUrl">Login With Different Account</a></li>
                </ul>
            </li>
        </ul>
    </div>

    <div class="container">
        <div class="page-header">
            <h1>This is a custom view</h1>
        </div>
    </div>


    <div class='container page-loggedOut' ng-cloak>
        <div class="page-header">
            <h1>
                Logout
                <small>You are now logged out</small>
            </h1>
            <div ng-show="model.redirectUrl">
                Click <a ng-href="{{model.redirectUrl}}">here</a> to return to the
                <span ng-bind="model.clientName"></span> application.
            </div>
            <iframe class="signout" ng-repeat="url in model.iFrameUrls" ng-src="{{url}}"></iframe>
        </div>
    </div>

    <script id='modelJson' type='application/json'>
        {model}
    </script>
    <script src="/content/libs/jquery/jquery-1.11.0.min.js"></script>
    <script src="/content/libs/bootstrap/js/bootstrap.min.js"></script>
    <script src="/content/libs/angular/angular.1.2.13.min.js"></script>
    <script src="/content/libs/encoder/encoder.min.js"></script>
    <script src="/content/app/app.js"></script>
</body>
</html>

