<!DOCTYPE html>
<html ng-app="app" ng-controller="LayoutCtrl">
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <title>{siteName}</title>
    <link href="/content/libs/bootstrap/css/bootstrap.min.css" rel="stylesheet" />
    <link href="/content/app/app.css" rel="stylesheet">
</head>
<body lang="en">
    <div class="navbar navbar-white navbar-fixed-top">
        <div class="navbar-header">
            <img src="/content/app/towbook.png" alt="Towbook" />
            <ul class="nav navbar-nav" style="float:right" ng-show="model.currentUser" ng-cloak>
                <li class="dropdown">
                    <a href="#" class="dropdown-toggle" data-toggle="dropdown">{{model.currentUser}} <b class="caret"></b></a>
                    <ul class="dropdown-menu">
                        <li><a href="{{model.logoutUrl}}">Logout</a></li>
                        <li class="divider" ng-show="model.loginWithDifferentAccountUrl"></li>
                        <li><a href="{{model.loginWithDifferentAccountUrl}}" ng-show="model.loginWithDifferentAccountUrl">Login With Different Account</a></li>
                    </ul>
                </li>
            </ul>
        </div>
        
    </div>

    <div class='container page-consent' ng-cloak>
        <div class="row page-header">
            <div class="col-sm-10">
                <div class="client-logo" ng-show="model.clientLogoUrl"><img ng-src="{{model.clientLogoUrl}}"></div>
                <h1>
                    {{model.clientName}}
                    <small>is requesting your permission</small>
                </h1>
            </div>
        </div>

        <div class="row">
            <div class="col-sm-8">
                <div ng-show="model.errorMessage" class="alert alert-danger">
                    <strong>Error:</strong>
                    {{model.errorMessage}}
                </div>
                <form name="form" method="post" action="{{model.consentUrl}}" class="consent-form">
                    <anti-forgery-token token="model.antiForgery"></anti-forgery-token>


                    <div class="panel panel-default consent-buttons" ng-show="model.identityScopes">
                        <div class="panel-heading">
                            <span class="glyphicon glyphicon-user"></span>
                            Company Information
                        </div>
                        <ul class="list-group">
                            <li ng-repeat="scope in model.identityScopes" class="list-group-item">
                                <label>
                                    <input class="consent-scopecheck" type="checkbox" name="scopes" id="scopes_{{scope.name}}" value="{{scope.name}}" ng-model="scope.selected" ng-disabled="scope.required" />
                                    <strong>{{scope.displayName}}</strong>
                                    <span ng-show="scope.emphasize" class="glyphicon glyphicon-exclamation-sign"></span>
                                </label>
                                <div class="consent-description" ng-show="scope.description">
                                    <label for="scopes_{{scope.name}}">{{scope.description}}</label>
                                </div>
                            </li>
                        </ul>
                    </div>

                    <div class="panel panel-default" ng-show="model.resourceScopes">
                        <div class="panel-heading">
                            <span class="glyphicon glyphicon-tasks"></span>
                            Application Access
                        </div>
                        <ul class="list-group">
                            <li ng-repeat="scope in model.resourceScopes" class="list-group-item">
                                <label>
                                    <input class="consent-scopecheck" type="checkbox" name="scopes" value="{{scope.name}}" ng-model="scope.selected" ng-disabled="scope.required" />
                                    <strong>{{scope.displayName}}</strong>
                                    <span ng-show="scope.emphasize" class="glyphicon glyphicon-exclamation-sign"></span>
                                </label>
                                <span ng-show="scope.required"><em>(required)</em></span>
                                <div class="consent-description" ng-show="scope.description">
                                    <label for="scopes_{{scope.name}}">{{scope.description}}</label>
                                </div>
                            </li>
                        </ul>
                    </div>

                    <input class="consent-scopecheck" type="hidden" name="RememberConsent" value="true" ng-model="model.rememberConsent" />
                    
                    <div class="consent-buttons">
                        <button name="button" value="yes" class="btn btn-primary" autofocus>Yes, Allow</button>
                        <button name="button" value="no" class="btn">No, Do Not Allow</button>
                        <a class="pull-right btn btn-default" target="_blank" ng-show="model.clientUrl" ng-href="{{model.clientUrl}}">
                            <span class="glyphicon glyphicon-info-sign"></span>
                            <strong>{{model.clientName}}</strong>
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

        <script id='modelJson' type='application/json'>
            {model}
        </script>
        <script src="/content/libs/jquery/jquery-1.11.0.min.js"></script>
        <script src="/content/libs/bootstrap/js/bootstrap.min.js"></script>
        <script src="/content/libs/angular/angular.1.2.13.min.js"></script>
        <script src="/content/libs/encoder/encoder.min.js"></script>
        <script src="/content/app/app.js"></script>
</body>
</html>
