using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Threading.Tasks;
using Amazon;
using Amazon.S3;
using Amazon.S3.Model;
using Dapper;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.Extensions.Configuration;

namespace AgeroPhotos.Pages
{
    public class POModel : PageModel
    {

        private const string BucketName = "storage.towbook.net";
        private const string AccessKeyId = "********************";
        private const string SecretAccessKey = "zq6dHt8iJAftEerw49NKccK04jcoN+lsiwu9gppJ";


        private readonly IConfiguration _configuration;


        public List<PhotoModel> Photos { get; set; }
        public POModel(IConfiguration configuration)
        {
            _configuration = configuration;
        }


        public string PONumber { get; set; }
        public class JobDetails
        {

            public string VendorId { get; set; }
            public string CompanyName { get; set; }

            public string Make { get; set; }
            public string Model { get; set; }
            public string ModelYear { get; set; }

            public string Vehicle
            {
                get => $"{ModelYear} {Make} {Model}";
            }

            public string Color { get; set; }

            public string VIN { get; set; }
            public string PlateNumber { get; set; }
            public DateTime CreateDate { get; set; }
            public DateTime CompletionTime { get; set; }

            public string CustomerName { get; set; }

            public string ServiceType { get; set; }
            public string JobType { get; set; }
        }

        public JobDetails Details { get; set; }

        public class PhotoModel
        {
            public int DispatchEntryPhotoId { get; set; }
            public DateTime CreateDate { get; set; }
            public int DispatchEntryId { get; set; }
            public int CompanyId { get; set; }

            public decimal Latitude { get; set; }
            public decimal Longitude { get; set; }

            public string Url { get; set; }

        }

        public void OnGet()
        {
            this.Photos = new List<PhotoModel>();

            using (var c = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
            {
                var results = c.QueryMultiple("AgeroPhotoLookupByPO",
                       new
                       {
                           poNumber = Request.Query["number"].ToString()
                       },
                       commandType: System.Data.CommandType.StoredProcedure);

                var details = results.Read<JobDetails>();

                this.Details = details.FirstOrDefault();

                var rows = results.Read<PhotoModel>();

                if (rows.Any())
                {
                    this.Photos.AddRange(rows);
                }
                foreach(var photo in Photos)
                {
                    photo.Url = GetPresignedUrl($"/Storage/dispatchEntries/Photos/{photo.CompanyId}/{photo.DispatchEntryId}/{photo.DispatchEntryPhotoId}.jpg",
                         "image/jpg", 30, HttpVerb.GET);
                }

                PONumber = Request.Query["number"].ToString();
            }
        }



        private static string GetPresignedUrl(string path, string contentType, int expiresInMinutes,
            HttpVerb verb)
        {
            if (path.ToLowerInvariant().StartsWith("/storage"))
                path = path.Substring(9);

            if (path.StartsWith("/"))
                path = path.TrimStart('/');

            for (int retries = 1; retries <= 3; retries++)
            {
                try
                {
                    using (var client = new AmazonS3Client(AccessKeyId, SecretAccessKey, 
                        new AmazonS3Config() { RegionEndpoint = RegionEndpoint.USEast1 }))
                    {
                        var request1 = new GetPreSignedUrlRequest()
                        {
                            BucketName = BucketName,
                            Key = path,
                            Expires = DateTime.Now.AddMinutes(expiresInMinutes),
                        };

                        if (verb == HttpVerb.PUT)
                        {
                            request1.ContentType = contentType;
                            request1.Verb = verb;
                        }

                        string url = client.GetPreSignedURL(request1);

                        return url;
                    }
                }
                catch (Exception e)
                {
                    if (retries == 3)
                        throw new Exception("CloudStorage: pre-signed url request of " + path + " failed. Attempted " + retries + " times.",
                            e);
                }
            }

            throw new Exception("Couldn't get pre-signed URL for " + path);
        }
    }
}
