@page
@model AgeroPhotos.Pages.LoginModel
@{
}

<h2>@ViewData["Title"]</h2>
<div class="row">
    <div class="col-md-3">
        <form method="post">
            <p>Please enter your credentials.</p>
            <hr>
            <div asp-validation-summary="All" class="text-danger"></div>
            <div class="form-group">
                <label asp-for="Input.Username"></label>
                <input asp-for="Input.Username" class="form-control">
                <span asp-validation-for="Input.Username" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="Input.Password"></label>
                <input asp-for="Input.Password" class="form-control">
                <span asp-validation-for="Input.Password" class="text-danger"></span>
            </div>
            <div class="form-group">
                <button type="submit" class="btn btn-default">Log in</button>
            </div>
        </form>
    </div>
</div>

@section Scripts {
    @await Html.PartialAsync("_ValidationScriptsPartial")
}