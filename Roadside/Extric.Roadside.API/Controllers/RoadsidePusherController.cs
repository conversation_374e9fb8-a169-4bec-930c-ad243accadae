using System.Net;
using System.Net.Http;
using PusherServer;
using Extric.Roadside;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using System.Threading.Tasks;

namespace Extric.Towbook.API.Integration.Roadside
{
    [Route("roadside/Pusher")]
    public class RoadsidePusherController : ControllerBase
    {
        public class PusherAuth
        {
            /// <summary>
            /// The name of the channel the user is requesting an authentication token for.
            /// </summary>
            public string channel_name { get; set; }

            /// <summary>
            /// The socket_id (retrieved via pusher library) to request an auth token for.
            /// </summary>
            public string socket_id { get; set; }
        }

        private const string ApplicationId = "1905796";
        private const string ApplicationKey = "27c7d1f0230812b3d81b";
        private const string ApplicationSecret = "1a00a746ab476cd0f71a";
        private const string Cluster = "us2";

        /// <summary>
        /// Used with <PERSON><PERSON><PERSON> to authenticate for private channels.
        /// </summary>
        /// <param name="auth">Object containing channel_name and socket_id to try authenticating against.</param>
        /// <returns>
        /// If sucessful, returns a text/json object to use with <PERSON>ush<PERSON> library. 
        /// If it fails, a 403 status code will be returned.
        /// </returns>
        //rl.Add(routes.MapHttpRoute(
        //    name: "Integration_Roadside_Post",
        //    routeTemplate: root + "{controller}/{id}",
        //    defaults: new { id = RouteParameter.Optional, action = "Post" },
        //    constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "POST" }) }));
        [HttpPost]
        [Route("")]
        [AllowAnonymous]
        public async Task<HttpResponseMessage> PostAsync(PusherAuth auth)
        {
            var provider = new Pusher(ApplicationId,
                ApplicationKey,
                ApplicationSecret, new PusherOptions()
                {
                    Cluster = Cluster
                });

            if (auth.channel_name.StartsWith("private-gps"))
            {
                string companyId = "";
                int result = 0;

                companyId = auth.channel_name.Replace("private-gps-", "").Split('-')[0];
                int.TryParse(companyId, out result);
                if (result > 0)
                {
                    var x = new HttpResponseMessage()
                    {
                        Content = new StringContent(provider.Authenticate(auth.channel_name, auth.socket_id).ToJson())
                    };

                    x.Content.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("text/json");
                    return x;
                }
            }

            if (auth.channel_name.StartsWith("private-roadside-"))
            {
                string strHash = "";
                int dispatchId = 0;
                int dispatchUserId = 0;

                strHash = auth.channel_name.Replace("private-roadside-", "").Split('-')[0];

                var ids = RoadsideHelper.DecodeUrlHash(strHash);

                if (ids.Length > 0)
                    dispatchId = ids[0];
                if (ids.Length > 1)
                    dispatchUserId = ids[1];

                if (dispatchId > 0 && dispatchUserId > 0)
                {
                    var rd = await RoadsideDispatch.GetByIdAsync(dispatchId);
                    var user = await RoadsideDispatchUser.GetByDispatchUserIdAsync(dispatchUserId);
                    if (rd != null && user != null && rd.Id == user.RoadsideDispatchId)
                    {
                        var x = new HttpResponseMessage()
                        {
                            Content = new StringContent(provider.Authenticate(auth.channel_name, auth.socket_id).ToJson())
                        };

                        x.Content.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("text/json");
                        return x;
                    }
                }
            }

            return new HttpResponseMessage()
            {
                StatusCode = HttpStatusCode.Forbidden,
                Content = new StringContent("Invalid channel name, or you don't have access to it")
            };
        }
    }
}
