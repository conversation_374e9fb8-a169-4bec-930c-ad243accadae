using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using Extric.Roadside;
using Extric.Roadside.API.Models;
using Extric.Roadside.Surveys;
using Extric.Towbook.Accounts;
using Extric.Towbook.Utility;
using Extric.Towbook.WebShared;
using Microsoft.AspNetCore.Mvc;

namespace Extric.Towbook.API.Integration.Roadside
{
    [Route("roadside/settings")]
    public class RoadsideSettingsController : ControllerBase
    {
        public class RoadsideCompanySettings
        {
            public Collection<RoadsideSettingModel> Settings { get; set; }
            public Collection<SurveyQuestionModel> Questions { get; set; }
        }

        //rl.Add(routes.MapHttpRoute(
        //    name: "Integration_Roadside_Settings",
        //    routeTemplate: root + "settings",
        //    defaults: new { id = RouteParameter.Optional, action = "Get" },
        //    constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "GET" }) }));
        [HttpGet]
        [Route("")]
        public async Task<RoadsideCompanySettings> Get([FromQuery] int? accountId = null)
        {
            if (WebGlobal.CurrentUser == null ||
                WebGlobal.CurrentUser.Type != Towbook.User.TypeEnum.Manager ||
                !WebGlobal.CurrentUser.Company.HasFeature(Generated.Features.Roadside))
            {
                throw new Extric.Towbook.Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.Forbidden)
                {
                    Content = new StringContent("You don't have access to this feature or your user account doesn't have access due to a companyId restriction.")
                });
            }

            var companyId = WebGlobal.CurrentUser.CompanyId;

            if (!WebGlobal.CurrentUser.HasAccessToCompany(companyId))
            {
                throw new Extric.Towbook.Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound)
                {
                    Content = new StringContent("The specified companyId either doesn't exist or you don't have access to it.")
                });
            }

            if (accountId != null)
            {
                var acc = await Account.GetByIdAsync(accountId.Value);
                if (acc == null || !(await WebGlobal.GetCompaniesAsync()).Select(s => s.Id).Contains(acc.CompanyId))
                {
                    throw new Extric.Towbook.Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.Forbidden)
                    {
                        Content = new StringContent($"You don't have access to account {accountId} the account doesn't exist.")
                    });
                }

                Collection<RoadsideSettingModel> ret = new Collection<RoadsideSettingModel>();

                var rsm = RoadsideSettingModel.Map(RoadsideSetting.GetByCompanyId(companyId, acc.Id));

                if (!WebGlobal.CurrentUser.Company.HasFeature(Generated.Features.Roadside))
                    rsm.SocialMediaIntegration = null;

                ret.Add(rsm);

                return new RoadsideCompanySettings()
                {
                    Settings = ret.ToCollection(),
                    Questions = SurveyQuestion.GetAllByCompanyId(companyId).Where(w => w.AccountId == acc.Id).Select(s => SurveyQuestionModel.Map(s)).ToCollection(),
                };
            }

            var items = JobProgressTextAlertItem.GetByCompanyId(companyId, true).ToCollection();

            // No items means a company has never saved the roadside settings since introduction of custom text alerts.
            // Most likely new customer or we need to be backwark compatible.  Provide defaults.
            if (items.Count == 0)
            {
                if (!WebGlobal.CurrentUser.Company.HasFeature(Generated.Features.Roadside_CustomerSurveysOnly))
                {
                    items = JobProgressTextAlertItem.GetDefaults(companyId, null);
                }
            }
            else
            {
                // Always add completed alert item if that type was never added or deleted as an item
                // This is to default the company settings to on for completed/survey links
                if (!items.Where(w => w.StatusTypeId == JobProgressStatusType.Completed.Id).Any())
                {
                    items.Add(new JobProgressTextAlertItem()
                    {
                        Id = -1,
                        CompanyId = companyId,
                        StatusTypeId = JobProgressStatusType.Completed.Id,
                        Message = JobProgressStatusType.Completed.DefaultMessage,
                        CreateDate = DateTime.Now,
                        IsDeleted = false
                    });
                }

                items = items.Where(w => !w.IsDeleted).ToCollection();

            }

            var settings = RoadsideSetting.GetByCompanyId(companyId).Select(s => RoadsideSettingModel.Map(s, items)).ToCollection();
            var questions = SurveyQuestion.GetAllByCompanyId(companyId).Select(s => SurveyQuestionModel.Map(s)).ToCollection();

            if (!WebGlobal.CurrentUser.Company.HasFeature(Generated.Features.Roadside))
            {
                // roadside.io is expecting null if feature is off
                foreach (var s in settings)
                    s.SocialMediaIntegration = null;
            }

            

            return new RoadsideCompanySettings()
            {
                Settings = settings,
                Questions = questions
            };
        }

        [HttpPost]
        [Route("")]
        public HttpResponseMessage Post(RoadsideCompanySettings model)
        {
            if (WebGlobal.CurrentUser == null ||
                WebGlobal.CurrentUser.Type != Towbook.User.TypeEnum.Manager ||
                !WebGlobal.CurrentUser.Company.HasFeature(Generated.Features.Roadside))
            {
                throw new Extric.Towbook.Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.Forbidden)
                {
                    Content = new StringContent("You don't have access to this feature or your user account doesn't have access due to a companyId restriction.")
                });
            }

            if (model != null)
            {
                if (model.Settings != null &&
                    model.Settings.Select(s => s.CompanyId).Distinct()
                        .Where(w => w != null && !WebGlobal.CurrentUser.HasAccessToCompany(w.Value)).Any())
                {
                    throw new Extric.Towbook.Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound)
                    {
                        Content = new StringContent("The specified companyId either doesn't exist or you don't have access to it.")
                    });
                }

                if (model.Questions != null &&
                    model.Questions.Select(s => s.CompanyId).Distinct()
                        .Where(w => w != null && !WebGlobal.CurrentUser.HasAccessToCompany(w.Value)).Any())
                {
                    throw new Extric.Towbook.Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound)
                    {
                        Content = new StringContent("The specified companyId either doesn't exist or you don't have access to it.")
                    });
                }
            }


            if (model.Settings != null)
            {
                // Delete all other possible settings by company (keep new one)
                foreach (var x in RoadsideSetting.GetByCompanyId(WebGlobal.CurrentUser.Company.Id).Where(w => w.AccountId == null && w.IsDeleted == false))
                {
                    x.Delete(WebGlobal.CurrentUser.Id);
                }

                foreach (var x in model.Settings)
                {
                    var o = RoadsideSetting.GetById(x.Id);

                    if (o?.Id > 0 && !WebGlobal.CurrentUser.HasAccessToCompany(o?.CompanyId ?? 0))
                        continue;

                    o = RoadsideSettingModel.Map(x, o);

                    if (o.IsDeleted)
                    {
                        // save as new to record who changed the settings
                        o.Id = 0;
                        o.IsDeleted = false;
                        o.DeletedTime = null;
                        o.DeletedUserId = null;
                    }

                    o.OwnerUserId = WebGlobal.CurrentUser.Id;
                    o.Save();


                    // Save text alert items
                    if (x.TextAlertItems != null)
                    {
                        var saved = JobProgressTextAlertItem.GetByCompanyId(WebGlobal.CurrentUser.CompanyId);
                        Collection<JobProgressTextAlertItem> newItems = new Collection<JobProgressTextAlertItem>();

                        foreach (var item in x.TextAlertItems)
                        {
                            // ignore "no change" conditions.  Keep in data store as is...continue.
                            if (item.Id > 0 && saved.Where(w => w.Id == item.Id && w.Message == item.Message).Any())
                            {
                                if (item.Enabled)
                                {
                                    saved = saved.Where(w => w.Id != item.Id);
                                }
                                continue;
                            }

                            var newItem = new JobProgressTextAlertItem()
                            {
                                Id = 0,
                                Message = item.Message,
                                StatusTypeId = item.TypeId,
                                CompanyId = WebGlobal.CurrentUser.CompanyId
                            };

                            if (!item.Enabled)
                            {
                                newItem.IsDeleted = true;
                                newItem.DeletedByUserId = WebGlobal.CurrentUser.Id;
                                newItem.DeletedDate = DateTime.Now;
                            }

                            newItems.Add(newItem);
                        }

                        // delete all remaining original
                        foreach (var d in saved)
                            d.Delete(WebGlobal.CurrentUser);

                        // create new items
                        foreach (var n in newItems)
                            n.Save(WebGlobal.CurrentUser);
                    }
                }
            }

            if (model.Questions != null)
            {
                foreach (var x in model.Questions)
                {
                    var o = SurveyQuestion.GetById(x.Id);

                    if (o?.Id > 0 && !WebGlobal.CurrentUser.HasAccessToCompany(o?.CompanyId ?? 0))
                        continue;

                    o = SurveyQuestionModel.Map(x, o);
                    o.CompanyId = WebGlobal.CurrentUser.CompanyId;
                    o.OwnerUserId = WebGlobal.CurrentUser.Id;
                    if (x.Deleted == true)
                        o.Delete(WebGlobal.CurrentUser.Id);
                    else
                    {
                        if (!string.IsNullOrWhiteSpace(o.Question))
                            o.Save();
                    }
                }
            }

            return new HttpResponseMessage(HttpStatusCode.OK);
        }

        
    }
}
