
/***************************************
SCHEMA
****************************************/

IF NOT EXISTS (SELECT 1 FROM sys.schemas WHERE name = 'Roadside')
BEGIN
    EXEC( 'CREATE SCHEMA Roadside' );
END


/***************************************
TABLES
****************************************/

IF OBJECT_ID('[Roadside].[Dispatches]') IS NULL 
CREATE TABLE [Roadside].[Dispatches] (
    RoadsideDispatchId int IDENTITY(1,1) NOT NULL,
    DispatchEntryId int NOT NULL,
    CompanyId int NOT NULL,
    CreateDate  DateTime NOT NULL DEFAULT(getdate()),
    OwnerUserId int NOT NULL,
    Deleted bit DEFAULT(0)

    CONSTRAINT [PK_RoadsideDispatchId] PRIMARY KEY CLUSTERED ([RoadsideDispatchId] ASC)
    CONSTRAINT [FK_RoadsideDispatches_DispatchEntryId] FOREIGN KEY ([DispatchEntryId]) REFERENCES [dbo].[DispatchEntries] ([DispatchEntryId]),
    CONSTRAINT [FK_RoadsideDispatches_CompanyId] FOREIGN KEY ([CompanyId]) REFERENCES [dbo].[Companies] ([CompanyId]),
    CONSTRAINT [FK_RoadsideDispatches_OwnerUserId] FOREIGN KEY ([OwnerUserId]) REFERENCES [dbo].[Users] ([UserId])
);
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='Dispatches' AND COLUMN_NAME = 'MovingAverage' AND TABLE_SCHEMA='Roadside')
    ALTER TABLE Roadside.Dispatches ADD MovingAverage DECIMAL(5,1)

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='Dispatches' AND COLUMN_NAME = 'RatingCount' AND TABLE_SCHEMA='Roadside')
    ALTER TABLE Roadside.Dispatches ADD RatingCount INT




IF OBJECT_ID('[Roadside].[DispatchUsers]') IS NULL 
CREATE TABLE [Roadside].[DispatchUsers]  (
    RoadsideDispatchUserId int IDENTITY(1,1) NOT NULL,
    RoadsideDispatchId int NOT NULL,
    DispatchEntryContactId int,
    Url varchar(50),
    MobileNumber varchar(32),
    Name varchar(100),
    Email varchar(100),
    CreateDate DateTime NOT NULL DEFAULT(getdate()),
    OwnerUserId int NULL,
    Deleted bit DEFAULT(0)

    CONSTRAINT [PK_RoadsideDispatchUserId] PRIMARY KEY CLUSTERED ([RoadsideDispatchUserId] ASC)
    CONSTRAINT [FK_RoadsideDispatchUsers_RoadsideDispatchId] FOREIGN KEY ([RoadsideDispatchId]) REFERENCES [roadside].[Dispatches] ([RoadsideDispatchId]),
);
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchUsers' AND COLUMN_NAME = 'LastActivity' AND TABLE_SCHEMA='Roadside')
    ALTER TABLE Roadside.DispatchUsers ADD LastActivity DateTime NULL

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchUsers' AND COLUMN_NAME = 'IsActive' AND TABLE_SCHEMA='Roadside')
    ALTER TABLE Roadside.DispatchUsers ADD IsActive bit NULL

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchUsers' AND COLUMN_NAME = 'PickupConfirmed' AND TABLE_SCHEMA='Roadside')
    ALTER TABLE Roadside.DispatchUsers ADD PickupConfirmed Bit Default 0

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchUsers' AND COLUMN_NAME = 'DestinationConfirmed' AND TABLE_SCHEMA='Roadside')
    ALTER TABLE Roadside.DispatchUsers ADD DestinationConfirmed Bit Default 0


IF OBJECT_ID('[Roadside].[DispatchUserLocation]') IS NULL 
CREATE TABLE [Roadside].[DispatchUserLocation]  (
    RoadsideDispatchUserLocationId int IDENTITY(1,1) NOT NULL,
    RoadsideDispatchId int NOT NULL,
    RoadsideDispatchUserId int NOT NULL,
    Type int NOT NULL,
    Latitude Decimal(9,6) NOT NULL,
    Longitude Decimal (9,6) NOT NULL,
    Timestamp DateTime NOT NULL DEFAULT(getdate())

    CONSTRAINT [PK_RoadsideDispatchUserLocationId] PRIMARY KEY CLUSTERED ([RoadsideDispatchUserLocationId] ASC)
    CONSTRAINT [FK_RoadsideDispatchUserLocation_RoadsideDispatchId] FOREIGN KEY ([RoadsideDispatchId]) REFERENCES [roadside].[Dispatches] ([RoadsideDispatchId]),
    CONSTRAINT [FK_RoadsideDispatchUserLocation_RoadsideDispatchUserId] FOREIGN KEY ([RoadsideDispatchUserId]) REFERENCES [roadside].[DispatchUsers] ([RoadsideDispatchUserId]),
);
GO


IF OBJECT_ID('[Roadside].[SurveyQuestionTypes]') IS NULL 
BEGIN
    CREATE TABLE [Roadside].[SurveyQuestionTypes] (
        SurveyQuestionTypeId int IDENTITY(1,1) NOT NULL,
        Name varchar(100) NOT NULL
    )

    SET IDENTITY_INSERT Roadside.SurveyQuestionTypes ON 
    Insert into Roadside.SurveyQuestionTypes (SurveyQuestionTypeId, Name) values (1, 'Rating')
    Insert into Roadside.SurveyQuestionTypes (SurveyQuestionTypeId, Name) values (2, 'Boolean')
    SET IDENTITY_INSERT Roadside.SurveyQuestionTypes OFF
END


IF OBJECT_ID('[Roadside].[DispatchMessages]') IS NULL 
CREATE TABLE [Roadside].[DispatchMessages] (
    RoadsideDispatchMessageId int IDENTITY(1,1) NOT NULL,
    RoadsideDispatchId int NOT NULL,
    RoadsideDispatchUserId int NOT NULL,
    Message varchar(300),
    CallStatus int NULL,
    CreateDate DateTime NOT NULL DEFAULT(getdate()),
    ReadDate DateTime,
    LastActivityDate DateTime

    CONSTRAINT [PK_RoadsideDispatchMessageId] PRIMARY KEY CLUSTERED ([RoadsideDispatchMessageId] ASC)
    CONSTRAINT [FK_RoadsideDispatchMesages_RoadsideDispatchId] FOREIGN KEY ([RoadsideDispatchId]) REFERENCES [roadside].[Dispatches] ([RoadsideDispatchId]),
    CONSTRAINT [FK_RoadsideDispatchMessage_RoadsideDispatchUserId] FOREIGN KEY ([RoadsideDispatchUserId]) REFERENCES [roadside].[DispatchUsers] ([RoadsideDispatchUserId])
);
GO

IF(SELECT (COLUMNPROPERTY(OBJECT_ID('roadside.DispatchMessages', 'U'), 'CallStatus', 'AllowsNull'))) = 1
BEGIN
    Alter Table roadside.DispatchMessages Alter Column CallStatus Int NULL
END
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='DispatchMessages' AND COLUMN_NAME = 'JobProgressTextAlertItemId' AND TABLE_SCHEMA='Roadside')
    ALTER TABLE Roadside.DispatchMessages ADD JobProgressTextAlertItemId INT NULL



IF (NOT EXISTS(SELECT 1 FROM sys.indexes WHERE name='IX_RoadsideDispatchMessages_ByCallStatus'))
BEGIN			
	CREATE NONCLUSTERED INDEX IX_RoadsideDispatchMessages_ByCallStatus ON [Roadside].[DispatchMessages] (RoadsideDispatchId, CallStatus)
END


IF OBJECT_ID('[Roadside].[SurveyQuestions]') IS NULL 
CREATE TABLE [Roadside].[SurveyQuestions] (
    SurveyQuestionId int IDENTITY(1,1) NOT NULL,
    CompanyId int NOT NULL,
    Question varchar(500),
    AllowFeedbackResponse bit DEFAULT(0),
    Type int DEFAULT(0),
    AccountId int,
    ReasonId int,    
    CreateDate DateTime NOT NULL DEFAULT(getdate()),
    OwnerUserId int,    
    Deleted bit DEFAULT(0),
    DeletedByUserId int,
    DeletedDate DateTime,
    DeletedIpAddress varchar(50)

    CONSTRAINT [PK_SurveyQuestionId] PRIMARY KEY CLUSTERED ([SurveyQuestionId] ASC)
    CONSTRAINT [FK_SurveyQuestions_CompanyId] FOREIGN KEY ([CompanyId]) REFERENCES [dbo].[Companies] ([CompanyId]),
    CONSTRAINT [FK_SurveyQuestions_AccountId] FOREIGN KEY ([AccountId]) REFERENCES [dbo].[Accounts] ([AccountId]),
    CONSTRAINT [FK_SurveyQuestions_ReasonId] FOREIGN KEY ([ReasonId]) REFERENCES [dbo].[DispatchReasons] ([DispatchReasonId]),
    CONSTRAINT [FK_SurveyQuestions_OwnerUserId] FOREIGN KEY ([OwnerUserId]) REFERENCES [dbo].[Users] ([UserId]),
    CONSTRAINT [FK_SurveyQuestions_DeletedByUserId] FOREIGN KEY ([DeletedByUserId]) REFERENCES [dbo].[Users] ([UserId])
);
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='SurveyQuestions' AND COLUMN_NAME = 'IsDefault' AND TABLE_SCHEMA='Roadside')
    ALTER TABLE Roadside.SurveyQuestions ADD IsDefault bit NOT NULL DEFAULT(0)

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='SurveyQuestions' AND COLUMN_NAME = 'DisplayOrder' AND TABLE_SCHEMA='Roadside')
    ALTER TABLE Roadside.SurveyQuestions add DisplayOrder int NULL


IF OBJECT_ID('[Roadside].[SurveyResponses]') IS NULL 
CREATE TABLE [Roadside].[SurveyResponses] (
    SurveyResponseId int IDENTITY(1,1) NOT NULL,
    RoadsideDispatchId int NOT NULL,
    RoadsideDispatchUserId int NOT NULL,
    CreateDate DateTime NOT NULL DEFAULT(getdate()),
    IpAddress varchar(50)

    CONSTRAINT [PK_SurveyResponseId] PRIMARY KEY CLUSTERED ([SurveyResponseId] ASC)
    CONSTRAINT [FK_SurveyResponses_RoadsideDispatchId] FOREIGN KEY ([RoadsideDispatchId]) REFERENCES [roadside].[Dispatches] ([RoadsideDispatchId]),
    CONSTRAINT [FK_SurveyResponses_RoadsideDispatchUserId] FOREIGN KEY ([RoadsideDispatchUserId]) REFERENCES [roadside].[DispatchUsers] ([RoadsideDispatchUserId])
);
GO

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='SurveyResponses' AND COLUMN_NAME = 'Average' AND TABLE_SCHEMA='Roadside')
    ALTER TABLE Roadside.SurveyResponses ADD Average DECIMAL(5,1) 

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='SurveyResponses' AND COLUMN_NAME = 'Deleted' AND TABLE_SCHEMA='Roadside')
    ALTER TABLE Roadside.SurveyResponses ADD Deleted BIT DEFAULT(0)

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='SurveyResponses' AND COLUMN_NAME = 'DeletedAverage' AND TABLE_SCHEMA='Roadside')
    ALTER TABLE Roadside.SurveyResponses ADD DeletedAverage DECIMAL(5,1)

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='SurveyResponses' AND COLUMN_NAME = 'DeletedByUserId' AND TABLE_SCHEMA='Roadside')
    ALTER TABLE Roadside.SurveyResponses ADD DeletedByUserId INT

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='SurveyResponses' AND COLUMN_NAME = 'DeletedDate' AND TABLE_SCHEMA='Roadside')
    ALTER TABLE Roadside.SurveyResponses ADD DeletedDate DATETIME




IF OBJECT_ID('[Roadside].[SurveyResponseAnswers]') IS NULL 
CREATE TABLE [Roadside].[SurveyResponseAnswers] (
    SurveyResponseAnswerId int IDENTITY(1,1) NOT NULL,    
    SurveyResponseId int NOT NULL,
    SurveyQuestionId int NOT NULL,
    Rating int NOT NULL,
    Value varchar(100)

    CONSTRAINT [PK_SurveyResponseAnswerId] PRIMARY KEY CLUSTERED ([SurveyResponseAnswerId] ASC)
    CONSTRAINT [FK_SurveyResponses_SurveyResponseId] FOREIGN KEY ([SurveyResponseId]) REFERENCES [roadside].[SurveyResponses] ([SurveyResponseId]),
    CONSTRAINT [FK_SurveyResponses_SurveyQuestionId] FOREIGN KEY ([SurveyQuestionId]) REFERENCES [roadside].[SurveyQuestions] ([SurveyQuestionId])
);
GO

IF(SELECT (COLUMNPROPERTY(OBJECT_ID('roadside.SurveyResponseAnswers', 'U'), 'Rating', 'AllowsNull'))) = 1
BEGIN
    Alter Table roadside.SurveyResponseAnswers Alter Column Rating Int NULL
END
GO


IF OBJECT_ID('[Roadside].[SurveyResponseFeedback]') IS NULL 
CREATE TABLE [Roadside].[SurveyResponseFeedback] (
    SurveyResponseFeedbackId int IDENTITY(1,1) NOT NULL,        
    SurveyResponseId int NOT NULL,    
    SurveyQuestionId int NOT NULL,
    Response varchar(2000)

    CONSTRAINT [PK_SurveyResponseFeedbackId] PRIMARY KEY CLUSTERED ([SurveyResponseFeedbackId] ASC)
    CONSTRAINT [FK_SurveyResponseFeedback_SurveyResponseId] FOREIGN KEY ([SurveyResponseId]) REFERENCES [roadside].[SurveyResponses] ([SurveyResponseId]),
    CONSTRAINT [FK_SurveyResponseFeedback_SurveyQuestionId] FOREIGN KEY ([SurveyQuestionId]) REFERENCES [roadside].[SurveyQuestions] ([SurveyQuestionId])
);
GO


IF OBJECT_ID('[Roadside].[Settings]') IS NULL 
CREATE TABLE [Roadside].[Settings] (
    SettingId int IDENTITY (1, 1) NOT NULL,
    CompanyId int NOT NULL DEFAULT(0),
    AccountId int NULL,
    ShowCallETA bit NOT NULL DEFAULT(0),
    ShowDriverName bit NOT NULL DEFAULT(0),
    ShowInvoice bit NOT NULL DEFAULT(0),
    OwnerUserId int,
    CreateDate datetime NOT NULL DEFAULT(GETDATE()),
    IsDeleted bit NOT NULL DEFAULT(0),
    DeletedTime datetime,
    DeletedUserId int

    CONSTRAINT [PK_RoadsideSettings] PRIMARY KEY CLUSTERED (SettingId ASC),
    CONSTRAINT [FK_RoadsideSettings_Users] FOREIGN KEY(OwnerUserId) REFERENCES dbo.Users (UserId),
    CONSTRAINT [FK_RoadsideSettings_UserDeleter] FOREIGN KEY(DeletedUserId) REFERENCES dbo.Users (UserId),
    CONSTRAINT [FK_RoadsideSettings_Accounts] FOREIGN KEY(AccountId) REFERENCES dbo.Accounts (AccountId),
    CONSTRAINT [FK_RoadsideSettings_Companies] FOREIGN KEY(CompanyId) REFERENCES dbo.Companies (CompanyId)
);
GO
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='Settings' AND COLUMN_NAME = 'ShowCompanyName' AND TABLE_SCHEMA='Roadside')
    ALTER TABLE Roadside.Settings add ShowCompanyName bit NOT NULL DEFAULT(0)
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='Settings' AND COLUMN_NAME = 'ShowDriverLocation' AND TABLE_SCHEMA='Roadside')
    ALTER TABLE Roadside.Settings add ShowDriverLocation bit NOT NULL DEFAULT(0)
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='Settings' AND COLUMN_NAME = 'ShowCallOption' AND TABLE_SCHEMA='Roadside')
    ALTER TABLE Roadside.Settings add ShowCallOption int NOT NULL DEFAULT(0)
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='Settings' AND COLUMN_NAME = 'ShowDriverFirstNameOnly' AND TABLE_SCHEMA='Roadside')
    ALTER TABLE Roadside.Settings add ShowDriverFirstNameOnly bit NOT NULL DEFAULT(0)

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='Settings' AND COLUMN_NAME = 'FacebookReviewLink' AND TABLE_SCHEMA='Roadside')
    ALTER TABLE Roadside.Settings add FacebookReviewLink VARCHAR(255)
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='Settings' AND COLUMN_NAME = 'GoogleReviewLink' AND TABLE_SCHEMA='Roadside')
    ALTER TABLE Roadside.Settings add GoogleReviewLink VARCHAR(255)
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='Settings' AND COLUMN_NAME = 'YelpReviewLink' AND TABLE_SCHEMA='Roadside')
    ALTER TABLE Roadside.Settings add YelpReviewLink VARCHAR(255)


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='Settings' AND COLUMN_NAME = 'EnableMotorClubTextAlerts' AND TABLE_SCHEMA='Roadside')
    ALTER TABLE Roadside.Settings add EnableMotorClubTextAlerts bit default(0)

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='Settings' AND COLUMN_NAME = 'EnableMotorClubAutoInvite' AND TABLE_SCHEMA='Roadside')
    ALTER TABLE Roadside.Settings add EnableMotorClubAutoInvite bit default(0)


IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='Settings' AND COLUMN_NAME = 'NonMotorClubTextAlertPreferenceType' AND TABLE_SCHEMA='Roadside')
    ALTER TABLE Roadside.Settings add NonMotorClubTextAlertPreferenceType int

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='Settings' AND COLUMN_NAME = 'EnableNonMotorClubAutoInvite' AND TABLE_SCHEMA='Roadside')
    ALTER TABLE Roadside.Settings add EnableNonMotorClubAutoInvite bit default(0)

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='Settings' AND COLUMN_NAME = 'CompanySignatureName' AND TABLE_SCHEMA='Roadside')
    ALTER TABLE Roadside.Settings add CompanySignatureName VARCHAR(30)


/***************************************
PROCEDURES
****************************************/

/* Roadside Dispatches */

IF OBJECT_ID('Roadside.DispatchGetById') IS NULL EXEC('CREATE PROCEDURE Roadside.DispatchGetById AS SET NOCOUNT ON;')
GO

ALTER PROCEDURE Roadside.DispatchGetById (
    @RoadsideDispatchId int
) AS
    SELECT TOP 1 * from Roadside.Dispatches WHERE RoadsideDispatchId=@RoadsideDispatchId and Deleted=0
GO


IF OBJECT_ID('Roadside.DispatchGetByDispatchEntryId') IS NULL EXEC('CREATE PROCEDURE Roadside.DispatchGetByDispatchEntryId AS SET NOCOUNT ON;')
GO

ALTER PROCEDURE Roadside.DispatchGetByDispatchEntryId (
    @DispatchEntryId int
) AS
    SELECT TOP 1 * from Roadside.Dispatches WHERE DispatchEntryId=@DispatchEntryId and Deleted=0
GO


IF OBJECT_ID('Roadside.DispatchInsert') IS NULL EXEC('CREATE PROCEDURE Roadside.DispatchInsert  AS SET NOCOUNT ON;')
GO
ALTER PROCEDURE Roadside.DispatchInsert
(
    @DispatchEntryId int,
    @CompanyId int,
    @OwnerUserId int
)
AS
    INSERT INTO Roadside.Dispatches (DispatchEntryId, CompanyId, OwnerUserId) 
        VALUES( @DispatchEntryId, @CompanyId, @OwnerUserId)

    SELECT @@IDENTITY AS Id
GO


IF OBJECT_ID('Roadside.DispatchDeleteById') IS NULL EXEC('CREATE PROCEDURE Roadside.DispatchDeleteById AS SET NOCOUNT ON;')
GO

ALTER PROCEDURE Roadside.DispatchDeleteById (
    @RoadsideDispatchId int
) AS
    UPDATE Roadside.Dispatches SET Deleted=1 WHERE RoadsideDispatchId=@RoadsideDispatchId
GO


/* Roadside Dispatch Users */

IF OBJECT_ID('Roadside.DispatchUserGetByDispatchUserId') IS NULL EXEC('CREATE PROCEDURE Roadside.DispatchUserGetByDispatchUserId AS SET NOCOUNT ON;')
GO

ALTER PROCEDURE Roadside.DispatchUserGetByDispatchUserId (
    @RoadsideDispatchUserId int
) AS
    SELECT TOP 1 * from Roadside.DispatchUsers WHERE RoadsideDispatchUserId=@RoadsideDispatchUserId and Deleted=0
GO


IF OBJECT_ID('Roadside.DispatchUsersGetByDispatchId') IS NULL EXEC('CREATE PROCEDURE Roadside.DispatchUsersGetByDispatchId AS SET NOCOUNT ON;')
GO

ALTER PROCEDURE Roadside.DispatchUsersGetByDispatchId (
    @RoadsideDispatchId int
) AS
    SELECT * from Roadside.DispatchUsers WHERE RoadsideDispatchId=@RoadsideDispatchId
GO


IF OBJECT_ID('Roadside.DispatchUserDeleteByDispatchUserId') IS NULL EXEC('CREATE PROCEDURE Roadside.DispatchUserDeleteByDispatchUserId AS SET NOCOUNT ON;')
GO

ALTER PROCEDURE Roadside.DispatchUserDeleteByDispatchUserId (
    @RoadsideDispatchUserId int
) AS
    UPDATE Roadside.DispatchUsers SET Deleted=1 WHERE RoadsideDispatchUserId=@RoadsideDispatchUserId
GO


IF OBJECT_ID('Roadside.DispatchUserInsert') IS NULL EXEC('CREATE PROCEDURE Roadside.DispatchUserInsert  AS SET NOCOUNT ON;')
GO
ALTER PROCEDURE Roadside.DispatchUserInsert
(
    @RoadsideDispatchId int,
    @DispatchEntryContactId int,
    @MobileNumber varchar(32),
    @Name varchar(100),
    @Email varchar(100),
    @OwnerUserId int
)
AS
    INSERT INTO Roadside.DispatchUsers (RoadsideDispatchId, DispatchEntryContactId, MobileNumber, Name, Email, OwnerUserId) 
        VALUES( @RoadsideDispatchId, @DispatchEntryContactId, @MobileNumber, @Name, @Email, @OwnerUserId)

    SELECT @@IDENTITY AS Id
GO


/* Roadside Dispatch Messages */

IF OBJECT_ID('Roadside.DispatchMessageGetByStatusId') IS NULL EXEC('CREATE PROCEDURE Roadside.DispatchMessageGetByStatusId AS SET NOCOUNT ON;')
GO

ALTER PROCEDURE Roadside.DispatchMessageGetByStatusId (
    @RoadsideDispatchUserId int,
    @CallStatus int
) AS
    SELECT TOP 1 * from Roadside.DispatchMessages WHERE RoadsideDispatchUserId=@RoadsideDispatchUserId AND CallStatus=@CallStatus
GO


IF OBJECT_ID('Roadside.DispatchMessagesGetByDispatchUserId') IS NULL EXEC('CREATE PROCEDURE Roadside.DispatchMessagesGetByDispatchUserId AS SET NOCOUNT ON;')
GO

ALTER PROCEDURE Roadside.DispatchMessagesGetByDispatchUserId (
    @RoadsideDispatchUserId int
) AS
    SELECT * from Roadside.DispatchMessages WHERE RoadsideDispatchUserId=@RoadsideDispatchUserId
GO


IF OBJECT_ID('Roadside.DispatchMessageInsert') IS NULL EXEC('CREATE PROCEDURE Roadside.DispatchMessageInsert  AS SET NOCOUNT ON;')
GO
ALTER PROCEDURE Roadside.DispatchMessageInsert
(
    @RoadsideDispatchId int,
    @RoadsideDispatchUserId int,
    @Message varchar(300),
    @CallStatus int,
    @JobProgressTextAlertItemId int
)
AS
    INSERT INTO Roadside.DispatchMessages (RoadsideDispatchId, RoadsideDispatchUserId, Message, CallStatus, JobProgressTextAlertItemId) 
        VALUES( @RoadsideDispatchId, @RoadsideDispatchUserId, @Message, @CallStatus, @JobProgressTextAlertItemId)

    SELECT @@IDENTITY AS Id
GO


/* Roadside Surveys */

IF OBJECT_ID('Roadside.SurveyQuestionsGetByCompanyId') IS NULL EXEC('CREATE PROCEDURE Roadside.SurveyQuestionsGetByCompanyId AS SET NOCOUNT ON;')
GO

ALTER PROCEDURE Roadside.SurveyQuestionsGetByCompanyId (
    @CompanyId int
) AS
    SELECT * from Roadside.SurveyQuestions WHERE (CompanyId=1 OR CompanyId=@CompanyId) and Deleted=0
GO


IF OBJECT_ID('Roadside.SurveyResponseInsert') IS NULL EXEC('CREATE PROCEDURE Roadside.SurveyResponseInsert  AS SET NOCOUNT ON;')
GO
ALTER PROCEDURE Roadside.SurveyResponseInsert
(
    @RoadsideDispatchId int,
    @RoadsideDispatchUserId int,
    @CreateDate datetime,
    @IpAddress varchar(100)
)
AS
    INSERT INTO Roadside.SurveyResponses (RoadsideDispatchId, RoadsideDispatchUserId, CreateDate, IpAddress) 
        VALUES( @RoadsideDispatchId, @RoadsideDispatchUserId, @CreateDate, @IpAddress)

    SELECT @@IDENTITY AS Id
GO


IF OBJECT_ID('Roadside.SurveyResponseGetByDispatchUserId') IS NULL EXEC('CREATE PROCEDURE Roadside.SurveyResponseGetByDispatchUserId AS SET NOCOUNT ON;')
GO

ALTER PROCEDURE Roadside.SurveyResponseGetByDispatchUserId (
    @RoadsideDispatchUserId int
) AS
    SELECT * from Roadside.SurveyResponses WHERE RoadsideDispatchUserId=@RoadsideDispatchUserId
GO


IF OBJECT_ID('Roadside.SurveyResponseAnswerInsert') IS NULL EXEC('CREATE PROCEDURE Roadside.SurveyResponseAnswerInsert  AS SET NOCOUNT ON;')
GO
ALTER PROCEDURE Roadside.SurveyResponseAnswerInsert
(
    @SurveyResponseId int,
    @SurveyQuestionId int,
    @Rating int,
    @Value varchar(100)
)
AS
    INSERT INTO Roadside.SurveyResponseAnswers (SurveyResponseId, SurveyQuestionId, Rating, Value) 
        VALUES( @SurveyResponseId, @SurveyQuestionId, @Rating, @Value)

    SELECT @@IDENTITY AS Id
GO


IF OBJECT_ID('Roadside.SurveyResponseFeedbackInsert') IS NULL EXEC('CREATE PROCEDURE Roadside.SurveyResponseFeedbackInsert  AS SET NOCOUNT ON;')
GO
ALTER PROCEDURE Roadside.SurveyResponseFeedbackInsert
(
    @SurveyResponseId int,
    @SurveyQuestionId int,
    @Response varchar(2000)
)
AS
    INSERT INTO Roadside.SurveyResponseFeedback (SurveyResponseId, SurveyQuestionId, Response) 
        VALUES( @SurveyResponseId, @SurveyQuestionId, @Response)

    SELECT @@IDENTITY AS Id
GO


/***************************************
POPULATE DEFAULTS
****************************************/

IF (SELECT COUNT(1) FROM Roadside.SurveyQuestions where SurveyQuestionId=1) = 0
BEGIN
    SET IDENTITY_INSERT Roadside.SurveyQuestions ON 
    insert into roadside.SurveyQuestions (SurveyQuestionId, CompanyId, Question, AllowFeedbackResponse, Type, OwnerUserId) values (1, 1, 'How would you rate the overall experience?', 0, 0, 1)
    SET IDENTITY_INSERT Roadside.SurveyQuestions OFF
END
GO

IF (SELECT COUNT(1) FROM Roadside.SurveyQuestions where SurveyQuestionId=2) = 0
BEGIN
    SET IDENTITY_INSERT Roadside.SurveyQuestions ON 
    insert into roadside.SurveyQuestions (SurveyQuestionId, CompanyId, Question, AllowFeedbackResponse, Type, OwnerUserId) values (2, 1, 'Driver professionalism', 0, 0, 1)
    SET IDENTITY_INSERT Roadside.SurveyQuestions OFF
END
GO

IF (SELECT COUNT(1) FROM Roadside.SurveyQuestions where SurveyQuestionId=3) = 0
BEGIN
    SET IDENTITY_INSERT Roadside.SurveyQuestions ON 
    insert into roadside.SurveyQuestions (SurveyQuestionId, CompanyId, Question, AllowFeedbackResponse, Type, OwnerUserId) values (3, 1, 'Did we satisfy your expectations?', 1, 1, 1)
    SET IDENTITY_INSERT Roadside.SurveyQuestions OFF
END
GO

UPDATE Roadside.SurveyQuestions SET IsDefault = 1, CompanyId=1, Deleted=0, DisplayOrder=NULL WHERE SurveyQuestionId BETWEEN 1 AND 3
UPDATE Roadside.SurveyQuestions SET DisplayOrder = 1 WHERE SurveyQuestionId=1



/***************************************
Survey Response Connections
****************************************/

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='SurveyResponseWebComponentConnections')
BEGIN 
	CREATE TABLE [Roadside].[SurveyResponseWebComponentConnections] (
		Id 							INT IDENTITY(1,1) NOT NULL,
		CompanyId 					INT NOT NULL,
		WebKey				        VARCHAR(64),
		EnableWebComponent	 	    BIT DEFAULT(1),
		CreateDate					DATETIME NOT NULL,
		IsDeleted					BIT DEFAULT(0),
		DeletedByUserId				INT NULL,
		DeletedDate					DATETIME NULL
		
		CONSTRAINT [PK_SurveyResponseWebComponentConnections] PRIMARY KEY CLUSTERED (Id ASC)
	)
	
	CREATE NONCLUSTERED INDEX IX_WebComponentConnections_ByCompany ON Roadside.SurveyResponseWebComponentConnections (CompanyId)
	CREATE NONCLUSTERED INDEX IX_WebComponentConnections_ByWebKey ON Roadside.SurveyResponseWebComponentConnections (WebKey)

    GRANT SELECT ON Roadside.SurveyResponseWebComponentConnections TO public
    GRANT INSERT ON Roadside.SurveyResponseWebComponentConnections TO public
    GRANT UPDATE ON Roadside.SurveyResponseWebComponentConnections TO public
END
GO


/***************************************
Survey Response Share items (websites, facebook, etc)
****************************************/

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='SurveyResponseWebComponentItems')
BEGIN 
	CREATE TABLE Roadside.SurveyResponseWebComponentItems (
		Id INT IDENTITY(1,1) NOT NULL,
		CompanyId INT NOT NULL,
		SurveyResponseId INT NOT NULL,
		CreateDate DateTime NOT NULL,
		OwnerUserId INT NOT NULL,
		Deleted BIT NULL,
		DeletedByUserId INT NULL,
		DeletedDate DateTime NULL
		
		CONSTRAINT [PK_SurveyResponseWebComponentItems] PRIMARY KEY CLUSTERED (Id ASC),
        CONSTRAINT [FK_SurveyResponseWebComponentItems_Responses] FOREIGN KEY(SurveyResponseId) REFERENCES roadside.SurveyResponses (SurveyResponseId),
	)	
	
    CREATE NONCLUSTERED INDEX IX_SurveyResponseWebComponentItems_ByCompany ON Roadside.SurveyResponseWebComponentItems (CompanyId)

    GRANT SELECT ON Roadside.SurveyResponseWebComponentItems TO public
    GRANT INSERT ON Roadside.SurveyResponseWebComponentItems TO public
    GRANT UPDATE ON Roadside.SurveyResponseWebComponentItems TO public
END
GO	



/***************************************
Demo web integration setup
****************************************/
IF (SELECT COUNT(1) FROM Roadside.SurveyResponseWebComponentConnections where Id=1) = 0
BEGIN
    SET IDENTITY_INSERT Roadside.SurveyResponseWebComponentConnections ON 
    insert into roadside.SurveyResponseWebComponentConnections (Id, CompanyId, WebKey, EnableWebComponent, CreateDate) values (1, 1, 'demo-web-integration', 1, GetDate())
    SET IDENTITY_INSERT Roadside.SurveyResponseWebComponentConnections OFF
END
GO



/***************************************
Job Progress Text Alert Items
****************************************/
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='JobProgressTextAlertItems' AND TABLE_SCHEMA='Roadside')
BEGIN 
	CREATE TABLE Roadside.JobProgressTextAlertItems (
		ItemId INT IDENTITY(1,1) NOT NULL,
		CompanyId INT NOT NULL,
        AccountId INT,
		StatusTypeId INT NOT NULL,
		Message VARCHAR(320) NULL,
		CreateDate DateTime NOT NULL,
		OwnerUserId INT NOT NULL,
		IsDeleted BIT DEFAULT(0),
		DeletedByUserId INT NULL,
		DeletedDate DateTime NULL
		
		CONSTRAINT [PK_JobProgressTextAlertItems] PRIMARY KEY CLUSTERED (ItemId ASC),
        CONSTRAINT [FK_JobProgressTextAlertItems_Companies] FOREIGN KEY(CompanyId) REFERENCES dbo.Companies (CompanyId),
        CONSTRAINT [FK_JobProgressTextAlertItems_Accounts] FOREIGN KEY(AccountId) REFERENCES dbo.Accounts (AccountId),
        CONSTRAINT [FK_JobProgressTextAlertItems_Users] FOREIGN KEY(OwnerUserId) REFERENCES dbo.Users (UserId)
	)	
	
    CREATE NONCLUSTERED INDEX IX_JobProgressTextAlertItems_ByCompany ON Roadside.JobProgressTextAlertItems (CompanyId)

    GRANT SELECT ON Roadside.JobProgressTextAlertItems TO public
    GRANT INSERT ON Roadside.JobProgressTextAlertItems TO public
    GRANT UPDATE ON Roadside.JobProgressTextAlertItems TO public
END
GO



/***************************************
Company Ratings
****************************************/
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='CompanyRatings' AND TABLE_SCHEMA='Roadside')
BEGIN 
	CREATE TABLE Roadside.CompanyRatings (
		CompanyRatingId INT IDENTITY(1,1) NOT NULL,
		CompanyId INT NOT NULL,
        Rating DECIMAL(5,1),
        Count INT NOT NULL,
        Last10 DECIMAL(5,1),
        Last10Count INT NOT NULL,
        Past30Days DECIMAL(5,1),
        Past30DayCount INT NOT NULL,
        Past60Days DECIMAL(5,1),
        Past60DayCount INT NOT NULL,
        Past90Days DECIMAL(5,1),
        Past90DayCount INT NOT NULL,
        Past120Days DECIMAL(5,1),
        Past120DayCount INT NOT NULL,
		LastActivity DateTime NOT NULL
       
		CONSTRAINT [PK_CompanyRatingId] PRIMARY KEY CLUSTERED (CompanyRatingId ASC),
        CONSTRAINT [FK_CompanyRatings_Companies] FOREIGN KEY(CompanyId) REFERENCES dbo.Companies (CompanyId),
	)	
	
    CREATE NONCLUSTERED INDEX IX_CompanyRatings_ByCompany ON Roadside.CompanyRatings (CompanyId)

    GRANT SELECT ON Roadside.CompanyRatings TO public
    GRANT INSERT ON Roadside.CompanyRatings TO public
    GRANT UPDATE ON Roadside.CompanyRatings TO public
END
GO

/***************************************
Company Driver Ratings
****************************************/
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='CompanyDriverRatings' AND TABLE_SCHEMA='Roadside')
BEGIN 
	CREATE TABLE Roadside.CompanyDriverRatings (
		DriverRatingId INT IDENTITY(1,1) NOT NULL,
        DriverId INT NOT NULL,
        CompanyId INT NOT NULL,
        Rating DECIMAL(5,1),
        Count INT NOT NULL,
        Last10 DECIMAL(5,1),
        Last10Count INT NOT NULL,
        Past30Days DECIMAL(5,1),
        Past30DayCount INT NOT NULL,
        Past60Days DECIMAL(5,1),
        Past60DayCount INT NOT NULL,
        Past90Days DECIMAL(5,1),
        Past90DayCount INT NOT NULL,
        Past120Days DECIMAL(5,1),
        Past120DayCount INT NOT NULL,
		LastActivity DateTime NOT NULL
       
		CONSTRAINT [PK_DriverRatingId] PRIMARY KEY CLUSTERED (DriverRatingId ASC),
        CONSTRAINT [FK_CompanyDriverRatings_Drivers] FOREIGN KEY(DriverId) REFERENCES dbo.Drivers (DriverId),
        CONSTRAINT [FK_CompanyDriverRatings_Companies] FOREIGN KEY(CompanyId) REFERENCES dbo.Companies (CompanyId),
	)	
	
    CREATE NONCLUSTERED INDEX IX_CompanyDriverRatings_ByCompanyAndDriver ON Roadside.CompanyDriverRatings (CompanyId, DriverId)

    GRANT SELECT ON Roadside.CompanyDriverRatings TO public
    GRANT INSERT ON Roadside.CompanyDriverRatings TO public
    GRANT UPDATE ON Roadside.CompanyDriverRatings TO public
END
GO




/***************************************
Surveys
****************************************/
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='Surveys' AND TABLE_SCHEMA='Roadside')
BEGIN 
	CREATE TABLE Roadside.Surveys (
		SurveyId INT IDENTITY(1,1) NOT NULL,
        SurveyResponseId INT NOT NULL,
        RoadsideDispatchId INT NOT NULL,
		CompanyId INT NOT NULL,
        CompanyName VARCHAR(100),
        AccountId INT NOT NULL,
        AccountName VARCHAR(50),        
        DispatchEntryId INT NOT NULL,
        CallNumber INT NOT NULL,
        PurchaseOrderNumber VARCHAR(50),
        InvoiceNumber VARCHAR(50),
        DriverId INT,
        DriverName VARCHAR(50),
        TruckId INT,
        TruckName VARCHAR(50),
        ReasonId INT,
        Reason VARCHAR(50),
        RoadsideUserId INT NOT NULL,
        DispatchContactId INT,
        ContactName VARCHAR(100),
        ContactPhone VARCHAR(32),
        ContactEmail VARCHAR(100),
        SurveySentDate DATETIME NOT NULL,
        SurveySubmittedDate DATETIME NOT NULL,
        Rating DECIMAL(5,1),
        Review VARCHAR(2000),
        AnswersJson VARCHAR(MAX)
		
		CONSTRAINT [PK_Surveys] PRIMARY KEY CLUSTERED (SurveyResponseId ASC),
        CONSTRAINT [FK_Surveys_SurveyResponses] FOREIGN KEY(SurveyResponseId) REFERENCES Roadside.SurveyResponses (SurveyResponseId),
        CONSTRAINT [FK_Surveys_RoadsideDispatches] FOREIGN KEY(RoadsideDispatchId) REFERENCES Roadside.Dispatches (RoadsideDispatchId),
        CONSTRAINT [FK_Surveys_Companies] FOREIGN KEY(DispatchEntryId) REFERENCES dbo.DispatchEntries (DispatchEntryId),
        CONSTRAINT [FK_Surveys_DispatchEntries] FOREIGN KEY(CompanyId) REFERENCES dbo.Companies (CompanyId),
        CONSTRAINT [FK_Surveys_Accounts] FOREIGN KEY(AccountId) REFERENCES dbo.Accounts (AccountId),
        CONSTRAINT [FK_Surveys_Drivers] FOREIGN KEY(DriverId) REFERENCES dbo.Drivers (DriverId),
        CONSTRAINT [FK_Surveys_RoadsideUsers] FOREIGN KEY(RoadsideUserId) REFERENCES Roadside.DispatchUsers (RoadsideDispatchUserId)
	)	
	
    CREATE NONCLUSTERED INDEX IX_Surveys_ByCompany ON Roadside.Surveys (CompanyId)
    CREATE NONCLUSTERED INDEX IX_Surveys_ByRoadsideUser ON Roadside.Surveys (RoadsideUserId)
    CREATE NONCLUSTERED INDEX IX_Surveys_ByCompanyAndDate ON Roadside.Surveys (CompanyId, SurveySubmittedDate)

END
GO

GRANT SELECT ON Roadside.Surveys TO public
GRANT INSERT ON Roadside.Surveys TO public
GRANT DELETE ON Roadside.Surveys TO public

IF OBJECT_ID('Roadside.SurveyDeleteBySurveyResponseId') IS NULL EXEC('CREATE PROCEDURE Roadside.SurveyDeleteBySurveyResponseId AS SET NOCOUNT ON;')
GO

ALTER PROCEDURE Roadside.SurveyDeleteBySurveyResponseId (
	@SurveyResponseId int
) AS
	DELETE FROM Roadside.Surveys WHERE SurveyResponseId=@SurveyResponseId
GO