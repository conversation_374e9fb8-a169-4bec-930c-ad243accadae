using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Extric.Roadside.API.Models
{
    public class RoadsideUserModel
    {
        public int Id { get; set; }
        public int DispatchId { get; set; }
        public int? CallContactId { get; set; }
        public string Name { get; set; }
        public string MobileNumber { get; set; }
        public string Email { get; set; }
        public bool PickupConfirmed { get; set; }
        public bool DestinationConfirmed { get; set; }
        public string Url { get; set; }

        public static RoadsideUserModel Map(RoadsideDispatchUser user, RoadsideUserModel model = null)
        {
            if (model == null)
                model = new RoadsideUserModel();
            
            model.Id = user.Id;
            model.DispatchId = user.RoadsideDispatchId;
            model.Name = user.Name;
            model.MobileNumber = user.MobileNumber;
            model.Email = user.Email;
            model.CallContactId = user.DispatchEntryContactId;
            model.PickupConfirmed = user.PickupConfirmed;
            model.DestinationConfirmed = user.DestinationConfirmed;
            model.Url = user.Url;

            return model;
        }
    }
}
