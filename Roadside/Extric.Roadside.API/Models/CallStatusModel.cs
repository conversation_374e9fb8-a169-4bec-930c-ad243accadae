using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Extric.Towbook.Dispatch;

namespace Extric.Roadside.API.Models
{
    public class CallStatusModel
    {
        public int Id { get; set; }
        public string Status { get; set; }

        public static CallStatusModel Map(Status status, CallStatusModel model)
        {
            if (model == null)
                model = new CallStatusModel();

            model.Id = status.Id;
            model.Status = status.Name;

            return model;
        }
    }
}
