using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Extric.Towbook.Dispatch;

namespace Extric.Roadside.API.Models
{
    public class RoadsideVehicleModel
    {
        public string Make { get; set; }
        public string Model { get; set; }
        public int Year { get; set; }
        public string Color { get; set; }
        public string Vin { get; set; }
        public string LicensePlate { get; set; }

        //CAN BE DELETED, kept if async functions need to be rollbacked
        public static RoadsideVehicleModel Map(EntryAsset asset, RoadsideVehicleModel model)
        {
            if (model == null)
                model = new RoadsideVehicleModel();

            model.Make = asset.Make;
            model.Model = asset.Model;
            model.Year = asset.Year;
            model.Vin = asset.Vin;
            model.LicensePlate = asset.LicenseNumber;

            var color = Towbook.Vehicle.Color.GetById(asset.ColorId);
            if (color != null)
                model.Color = color.Name;

            return model;
        }
        public static async Task<RoadsideVehicleModel> MapAsync(EntryAsset asset, RoadsideVehicleModel model)
        {
            if (model == null)
                model = new RoadsideVehicleModel();

            model.Make = asset.Make;
            model.Model = asset.Model;
            model.Year = asset.Year;
            model.Vin = asset.Vin;
            model.LicensePlate = asset.LicenseNumber;

            var color = await Towbook.Vehicle.Color.GetByIdAsync(asset.ColorId);
            if(color != null)
                model.Color = color.Name;

            return model;
        }
    }
}
