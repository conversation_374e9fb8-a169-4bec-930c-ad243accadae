using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Extric.Roadside.API.Models
{
    public class SurveyResponseWebComponentRatingModel
    {
        public decimal Score { get; set; }
        public int Count { get; set; }
        public DateTime LastActivity { get; set; }
    }

    public class SurveyResponseWebComponentModel
    {
        public int CompanyId { get; set; }
        public string CompanyName { get; set; }
        public SurveyResponseWebComponentRatingModel CompanyRating { get; set; }
        public Collection<SurveyResponseWebComponentItemModel> Reviews { get; set; }

        public SurveyResponseWebComponentModel()
        {
            Reviews = new Collection<SurveyResponseWebComponentItemModel>();
        }
    }
}
