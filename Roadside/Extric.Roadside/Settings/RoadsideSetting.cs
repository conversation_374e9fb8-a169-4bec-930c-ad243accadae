using Extric.Roadside.Settings;
using Extric.Towbook;
using Extric.Towbook.Accounts;
using Extric.Towbook.Integration;
using Extric.Towbook.Utility;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Extric.Roadside
{
    [Table("Roadside.Settings")]
    public class RoadsideSetting
    {
        [Key("SettingId")]
        public int Id { get; set; }
        public int CompanyId { get; set; }
        public int? AccountId { get; set; }
        public bool ShowCallETA { get; set; } = true;
        public bool ShowDriverName { get; set; } = true;
        public bool ShowDriverFirstNameOnly { get; set; }
        public bool ShowInvoice { get; set; }
        public bool ShowCompanyName { get; set; } = true;
        public bool ShowDriverLocation { get; set; } = true;
        public int ShowCallOption { get; set; }
        public int OwnerUserId { get; set; }
        public DateTime CreateDate { get; set; }
        public bool IsDeleted { get; set; } = false;
        public DateTime? DeletedTime { get; set; }
        public int? DeletedUserId { get; set; }

        public string GoogleReviewLink { get; set; }
        public string YelpReviewLink { get; set; }
        public string FacebookReviewLink { get; set; }

        public bool? EnableMotorClubTextAlerts { get; set; }
        public bool? EnableMotorClubAutoInvite { get; set; }
        public TextAlertPreferenceType? NonMotorClubTextAlertPreferenceType { get; set; }
        public bool? EnableNonMotorClubAutoInvite { get; set; }
        public string CompanySignatureName { get; set; }

        public RoadsideSetting()
        {
            EnableMotorClubTextAlerts = false;
            EnableMotorClubAutoInvite = false;
            EnableNonMotorClubAutoInvite = false;

            NonMotorClubTextAlertPreferenceType = TextAlertPreferenceType.Roadside_io;
        }

        public static RoadsideSetting GetById(int id)
        {
            return SqlMapper.Query<RoadsideSetting>(    
                "SELECT * FROM Roadside.Settings WHERE SettingId = @Id",
                new { Id = id }).FirstOrDefault()
                ?? new RoadsideSetting();
        }

        public static IEnumerable<RoadsideSetting> GetByCompanyId(int companyId)
        {
            var ret = SqlMapper.Query<RoadsideSetting>(
                "SELECT * FROM Roadside.Settings " +
                "WHERE (CompanyId = 1 OR CompanyId = @CompanyId) AND IsDeleted = 0",
                new { CompanyId = companyId }).ToCollection();

            if (ret.Count() == 0)
                ret.Add(new RoadsideSetting());

            return ret;
        }

        public static RoadsideSetting GetByCompanyId(int companyId, int? accountId = null)
        {
            var settings =  SqlMapper.Query<RoadsideSetting>(
                "SELECT * FROM Roadside.Settings " +
                "WHERE (CompanyId = 1 OR CompanyId = @CompanyId) AND IsDeleted = 0",
                new { CompanyId = companyId });

            return settings.FirstOrDefault(s => s.CompanyId == companyId && s.AccountId == accountId)
                ?? settings.FirstOrDefault(s => s.CompanyId == companyId && s.AccountId == null)
                ?? settings.FirstOrDefault(s => s.CompanyId == 1)
                ?? new RoadsideSetting();
        }

        public static async Task<RoadsideSetting> GetByCompanyIdAsync(int companyId, int? accountId = null)
        {
            var settings = await SqlMapper.QueryAsync<RoadsideSetting>(
                "SELECT * FROM Roadside.Settings " +
                "WHERE (CompanyId = 1 OR CompanyId = @CompanyId) AND IsDeleted = 0",
                new { CompanyId = companyId });

            return settings.FirstOrDefault(s => s.CompanyId == companyId && s.AccountId == accountId)
                ?? settings.FirstOrDefault(s => s.CompanyId == companyId && s.AccountId == null)
                ?? settings.FirstOrDefault(s => s.CompanyId == 1)
                ?? new RoadsideSetting();
        }

        public void Save()
        {
            if (Id < 1)
            {
                CreateDate = DateTime.Now;
                Id = (int)SqlMapper.Insert(this);
            }
            else
            {
                SqlMapper.Update(this);
            }
        }

        public void Delete(int userId)
        {
            if (Id < 1)
                return;

            DeletedTime = DateTime.Now;
            DeletedUserId = userId;
            IsDeleted = true;

            SqlMapper.Update(this);
        }

        public static bool IsAutoInviteEnabled(RoadsideSetting setting, Towbook.Dispatch.Entry entry)
        {
            if (setting == null || entry == null)
                return false;

            if (entry.Account != null)
            {
                var av = AccountKeyValue.GetByAccount(entry.Account.CompanyId, entry.Account.Id, Provider.Towbook.ProviderId, "AlwaysSendSurvey").FirstOrDefault();
                if (av != null && av.Value == "1" && entry.Status?.Id == Towbook.Dispatch.Status.Completed.Id)
                {
                    return true;
                }
            }

            // Get text message items for company
            var items = JobProgressTextAlertItem.GetByCompanyId(entry.CompanyId, true).ToCollection();

            bool autoInviteEnabled = (entry.Account?.Type == Towbook.Accounts.AccountType.MotorClub ?
                        setting?.EnableMotorClubAutoInvite.GetValueOrDefault() :
                        setting?.EnableNonMotorClubAutoInvite.GetValueOrDefault()) ?? false;

            if (entry.Account?.MasterAccountId == MasterAccountTypes.AaaNortheast ||
                entry.Account?.MasterAccountId == MasterAccountTypes.AaaAca)
                autoInviteEnabled = true;

            // No items means a company has never saved the roadside settings since introduction of custom text alerts.
            // Most likely new customer or we need to be backwark compatible.  Provide defaults.
            if (items.Count == 0)
            {
                if (!entry.Company.HasFeature(Towbook.Generated.Features.Roadside_CustomerSurveysOnly))
                {
                    if (autoInviteEnabled)
                        items = JobProgressTextAlertItem.GetDefaults(entry.CompanyId, null);
                }
            }
            else
            {
                items = items.Where(w => !w.IsDeleted).ToCollection();
            }


            // check for a text message to send based on settings
            var msgItem = JobProgressTextAlertItem.TranslateEntryStatusToMessageItem(setting, entry, items);
            if (msgItem == null)
                return false;

            if (!autoInviteEnabled)
            {
                var av = Towbook.Integration.AccountKeyValue.GetByAccount(entry.CompanyId, entry.AccountId, Towbook.Integration.Provider.Towbook.ProviderId, "AutoSendRoadsideInvite").FirstOrDefault();
                if (av != null)
                {
                    if (av.Value == "1")
                        autoInviteEnabled = true;
                    else if (av.Value == "0")
                        autoInviteEnabled = false;
                }
            }

            return autoInviteEnabled;
        }
    }
}
