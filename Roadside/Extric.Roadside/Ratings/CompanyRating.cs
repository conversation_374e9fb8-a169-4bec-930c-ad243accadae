using Extric.Towbook;
using Extric.Towbook.Utility;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Extric.Roadside
{
    [Table("Roadside.CompanyRatings")]
    public class CompanyRating
    {
        [Key("CompanyRatingId")]
        public int Id { get; set; }
        public int CompanyId { get; set; }
        public DateTime LastActivity { get; set; }

        public decimal Rating { get; set; }
        public int Count { get; set; }

        public decimal Last10 { get; set; }
        public decimal Past30Days { get; set; }
        public decimal Past60Days { get; set; }
        public decimal Past90Days { get; set; }
        public decimal Past120Days { get; set; }

        public int Last10Count { get; set; }
        public int Past30DayCount { get; set; }
        public int Past60DayCount { get; set; }
        public int Past90DayCount { get; set; }
        public int Past120DayCount { get; set; }


        public static CompanyRating GetByCompanyId(int companyId)
        {
            return SqlMapper.Query<CompanyRating>(
                "SELECT * FROM Roadside.CompanyRatings WHERE CompanyId = @Id",
                new { Id = companyId }).FirstOrDefault();
        }

        public static Collection<CompanyRating> GetByCompanyIds(int[] companyIds)
        {
            return SqlMapper.Query<CompanyRating>(
                "SELECT * FROM Roadside.CompanyRatings WHERE CompanyId IN @Ids",
                new { Ids = companyIds }).ToCollection();
        }

        public void Save()
        {
            LastActivity = DateTime.Now;

            if (Id < 1)
            {
                Id = (int)SqlMapper.Insert(this);
            }
            else
            {
                SqlMapper.Update(this);
            }
        }

        public static CompanyRating UpdateStats(int companyId)
        {
            var responses = Surveys.SurveyResponse.GetByCompanyIds(new int[] { companyId }.ToArray())
                .Where(w => w.AverageRating.GetValueOrDefault() > 0)
                .OrderByDescending(o => o.CreateDate);

            var companyRating = GetByCompanyId(companyId) ??
                                    new CompanyRating() { CompanyId = companyId };

            if (responses != null)
            {
                var sum = responses.Sum(z => z.AverageRating.Value);
                var count = responses.Count();

                if (count > 0)
                {
                    companyRating.Rating = Decimal.Round(sum / count, 1, MidpointRounding.AwayFromZero);
                    companyRating.Count = count;

                    // Last 10
                    var last10dispatches = responses.Take(10).ToCollection();
                    count = last10dispatches.Count;
                    if (count > 0)
                    {
                        companyRating.Last10 = Decimal.Round(last10dispatches.Sum(z => z.AverageRating.Value) / count, 1, MidpointRounding.AwayFromZero);
                        companyRating.Last10Count = count;
                    }

                    // last 30
                    var last30Dispatches = responses.Where(x => x.CreateDate > DateTime.Now.AddDays(-30)).ToCollection();
                    count = last30Dispatches.Count;
                    if (count > 0)
                    {
                        companyRating.Past30Days = Decimal.Round(last30Dispatches.Sum(z => z.AverageRating.Value) / count, 1, MidpointRounding.AwayFromZero);
                        companyRating.Past30DayCount = count;
                    }

                    // last 60
                    var last60Dispatches = responses.Where(x => x.CreateDate > DateTime.Now.AddDays(-60)).ToCollection();
                    count = last30Dispatches.Count;
                    if (count > 0)
                    {
                        companyRating.Past60Days = Decimal.Round(last30Dispatches.Sum(z => z.AverageRating.Value) / count, 1, MidpointRounding.AwayFromZero);
                        companyRating.Past60DayCount = count;
                    }

                    // last 90
                    var last90Dispatches = responses.Where(x => x.CreateDate > DateTime.Now.AddDays(-90)).ToCollection();
                    count = last90Dispatches.Count;
                    if (count > 0)
                    {
                        companyRating.Past90Days = Decimal.Round(last90Dispatches.Sum(z => z.AverageRating.Value) / last90Dispatches.Count, 1, MidpointRounding.AwayFromZero);
                        companyRating.Past90DayCount = last90Dispatches.Count;
                    }

                    // last 120
                    var last120Dispatches = responses.Where(x => x.CreateDate > DateTime.Now.AddDays(-120)).ToCollection();
                    count = last120Dispatches.Count;
                    if (count > 0) {
                        companyRating.Past120Days = Decimal.Round(last120Dispatches.Sum(z => z.AverageRating.Value) / count, 1, MidpointRounding.AwayFromZero);
                        companyRating.Past120DayCount = count;
                    }

                    // save the company stats
                    companyRating.Save();

                    return companyRating;
                }
            }

            return null;
        }
    }
}
