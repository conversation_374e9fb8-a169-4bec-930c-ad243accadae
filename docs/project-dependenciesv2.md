# Introduction

This document focuses on mapping out all the projects and folders that are found in the Solution Explorer, 
their .NET version alongside all their dependencies.

## HTTP Folder

### Ajax (net8.0)

- Extric.Towbook.API
- Extric.Towbook.Integration.MotorClubs
- Extric.Towbook.TWS
- Integrations\Data\Extric.Towbook.Integrations.AutoData
- Integrations\GPS\Extric.Towbook.Integrations.Fleetmatics
- Integrations\MotorClubs\Urgently

### App (NetFramework 4.8)

- Extric.Towbook.API.Models
- Extric.Towbook.Generated.Features
- Extric.Towbook.Integration.MotorClubs
- Extric.Towbook.Integrations.Email
- Extric.Towbook.Integrations.Faxing
- Extric.Towbook.Storage
- Extric.Towbook.WebShared
- Extric.Towbook.WebWrapper
- Extric.Towbook
- Integrations\Data\Extric.Towbook.Integrations.AutoData
- Integrations\MotorClubs\Aaa
- Integrations\MotorClubs\Agero
- Integrations\MotorClubs\Allstate
- Integrations\MotorClubs\Fleetnet
- Integrations\MotorClubs\Geico
- Integrations\MotorClubs\Swoop
- Integrations\Square\Square
- Roadside\Extric.Roadside

### Extric.Towbook.Agent.API (net8.0)

- Extric.Towbook.WebShared.Net5

### Extric.Towbook.API (net8.0)

- Extric.Towbook.Agent.API
- Extric.Towbook.API.Integration.Internal.Providers.Braintree
- Extric.Towbook.API.Models
- Extric.Towbook.DirectBilling.API
- Extric.Towbook.PermitRequests.API
- Integrations\Data\Extric.Towbook.API.Integration.Data.Providers.AutoData
- Integrations\Gateway\AutoRescueApi
- Integrations\Gateway\DispatchGateway
- Integrations\GPS\Extric.Towbook.API.Integration.GPS.Providers.Azuga
- Integrations\GPS\Extric.Towbook.API.Integration.GPS.Providers.DriverLocate
- Integrations\GPS\Extric.Towbook.API.Integration.GPS.Providers.FleetComplete
- Integrations\GPS\Extric.Towbook.API.Integration.GPS.Providers.Fleetmatics
- Integrations\GPS\Extric.Towbook.API.Integration.GPS.Providers.GeoTab
- Integrations\GPS\Extric.Towbook.API.Integration.GPS.Providers.Networkfleet
- Integrations\GPS\Extric.Towbook.API.Integration.GPS.Providers.Samsara
- Integrations\GPS\Extric.Towbook.API.Integration.GPS.Providers.TomTom
- Integrations\GPS\Extric.Towbook.API.Integration.GPS.Providers.USFleetTracking
- Integrations\GPS\Extric.Towbook.Integrations.TomTom
- Integrations\MotorClubs\AaaAceReceiverApi
- Integrations\MotorClubs\AaaNeReceiverApi
- Integrations\MotorClubs\AaaReceiverApi
- Integrations\MotorClubs\AgeroReceiverApi
- Integrations\MotorClubs\Agero
- Integrations\MotorClubs\AllstateReceiverApi
- Integrations\MotorClubs\Allstate
- Integrations\MotorClubs\Extric.Towbook.Integration.MotorClubs.StackThree
- Integrations\MotorClubs\FleetnetReceiverApi
- Integrations\MotorClubs\Fleetnet
- Integrations\MotorClubs\GeicoReceiverApi
- Integrations\MotorClubs\Geico
- Integrations\MotorClubs\GerberReceiverApi
- Integrations\MotorClubs\Gerber
- Integrations\MotorClubs\HonkReceiverApi
- Integrations\MotorClubs\NacReceiverApi
- Integrations\MotorClubs\NsdReceiverApi
- Integrations\MotorClubs\OON\OonAgeroClient
- Integrations\MotorClubs\OON\OonAgeroReceiverApi
- Integrations\MotorClubs\QuestReceiverApi
- Integrations\MotorClubs\RoadsideProtectReceiverApi
- Integrations\MotorClubs\RoadsideProtect
- Integrations\MotorClubs\StackThreeReceiverApi
- Integrations\MotorClubs\SwoopReceiverApi
- Integrations\MotorClubs\Swoop
- Integrations\MotorClubs\SykesReceiverApi
- Integrations\MotorClubs\Sykes
- Integrations\MotorClubs\TowbookApi
- Integrations\MotorClubs\TrxReceiverApi
- Integrations\MotorClubs\Trx
- Integrations\MotorClubs\UrgentlyReceiverApi
- Integrations\MotorClubs\Urgently
- Integrations\Square\SquareApi
- Integrations\Square\Square
- Integrations\vNext\Extric.Towbook.API.Integrations.Accounting.Providers.QuickBooks
- Integrations\vNext\Extric.Towbook.Integrations.Quickbooks
- Services\SquareService
- Roadside\Extric.Roadside

### Extric.Towbook.API.Models (netstandard2.0)

- Extric.Towbook.Integrations.Email
- Extric.Towbook.Integrations.Faxing
- Roadside\Extric.Roadside

### Extric.Towbook.DirectBilling.API (net8.0)

- Extric.Towbook.Integration.MotorClubs
- Extric.Towbook.WebShared.Net5

### Extric.Towbook.InternalVoiceApi (net8.0)

- Extric.Towbook.WebShared.Net5

### Extric.Towbook.PermitRequests.API (net8.0)

- Extric.Towbook.WebShared.Net5

### Extric.Towbook.TWS.API.Public (net8.0)

- TWS

### Extric.Towbook.VoiceDispatchApi (net8.0)

- Extric.Towbook.Integration.MotorClubs
- Extric.Towbook.WebShared.Net5

### Extric.Towbook.Web.LocateMvc (net8.0)

- Extric.Towbook.WebShared.Net5

### Extric.Towbook.Web.PublicAddress (net8.0)

- Extric.Towbook.WebShared.Net5
- Integrations\Square\Square

### Extric.Towbook.Web.PublicRequestCallMvc (net8.0)

- Extric.Towbook.API

### NodeApp (React? Wrapped with .NET)

### TWS (net8.0)

- Extric.Towbook.TWS
- Extric.Towbook.WebShared.Net5

## Integrations

### AutoDataDirect

#### Extric.Towbook.API.Integration.Data.Providers.AutoData (net8.0)

- Extric.Towbook.WebShared.Net5
- Extric.Towbook.Integrations.AutoData

#### Extric.Towbook.Integrations.AutoData (netstandard2.0)

- Extric.Towbook
- Also lots of comments?

#### Extric.Towbook.Integrations.AutoData.Tests (net4.8)

- Extric.Towbook.Integrations.AutoData

### BrainTree

#### Extric.TowBook.API.Integration.Internal.Providers.BrainTree (net8.0)

- Extric.Towbook.WebShared.Net5

### Fax and Email

#### Extric.Towbook.Integrations.Email (netstandard2.0)

- Extric.Towbook

####  Extric.Towbook.Integrations.Faxing (netstandard2.0)

- Extric.Towbook

### Gateway

#### AutoRescueApi (net8.0)

- Extric.Towbook.Integration.MotorClubs
- Extric.Towbook.WebShared.Net5
- MotorClubs/Urgently
- DispatchGateway

#### DispatchGateway (netstandard2.0)

- Extric.Towbook

### GPS

#### Azuga

##### Extric.Towbook.API.Integration.GPS.Providers.Azuga (net8.0)

- Extric.Towbook.WebShared.Net5
- Extric.Towbook.Integrations.Gps.Azuga

##### Extric.Towbook.Integrations.Gps.Azuga (netstandard2.0)

- Extric.Towbook

#### DriverLocate

##### Extric.Towbook.Integrations.Gps.Providers.DriverLocate (net8.0)

- Extric.Towbook.WebShared.Net5
- Extric.Towbook.Integrations.DriverLocate

##### Extric.Towbook.Integrations.DriverLocate (netstandard2.0)

- Extric.Towbook

##### Extric.Towbook.Integrations.DriverLocate.Tests (net8.0)

- Extric.Towbook.Integrations.DriverLocate

#### FleetComplete

##### Extric.Towbook.Integrations.GPS.Providers.FleetComplete (net8.0)

- Extric.Towbook.WebShared.Net5
- Extric.Towbook.Integrations.FleetComplete

##### Extric.Towbook.Integrations.FleetComplete (netstandard2.0)

- Extric.Towbook

#### Fleetmatics

##### Extric.Towbook.API.Integration.GPS.Providers.Fleetmatics (net8.0)

- Extric.Towbook.WebShared.Net5
- Extric.Towbook.Integrations.Fleetmatics

##### Extric.Towbook.Integrations.Fleetmatics (net8.0)

- Extric.Towbook.WebShared.Net5

#### GeoTab

##### Extric.Towbook.API.Integration.GPS.Providers.GeoTab (net8.0)

- Extric.Towbook.WebShared.Net5
- Extric.Towbook.Integrations.GeoTab

##### Extric.Towbook.Integrations.GeoTab (netstandard2.0)

- Extric.Towbook

##### Extric.Towbook.Integrations.GeoTab.Tests (net8.0)

- Extric.Towbook.Integrations.GeoTab

#### Networkfleet

##### Extric.Towbook.API.Integration.GPS.Providers.NetworkFleet (net8.0)

- Extric.Towbook.WebShared.Net5
- Extric.Towbook.API.Integrations.Networkfleet

##### Extric.Towbook.API.Integrations.Networkfleet (netstandard2.0)

- Extric.Towbook

##### Extric.Towbook.API.Integrations.Networkfleet.Tests (net8.0)

- Extric.Towbook.API.Integrations.Networkfleet

#### Samsara

##### Extric.Towbook.API.Integration.GPS.Providers.Samsara (net8.0)

- Extric.Towbook.WebShared.Net5
- Extric.Towbook.Integrations.Samsara

##### Extric.Towbook.Integrations.Samsara (netstandard2.0)

#### TomTom

##### Extric.Towbook.API.Integration.GPS.Providers.TomTom (net8.0)

- Extric.Towbook.WebShared.Net5
- Extric.Towbook.Integrations.TomTom

##### Extric.Towbook.Integrations.TomTom (netstandard2.0)

- Extric.Towbook

#### USFleetTracking

##### Extric.Towbook.API.Integration.GPS.Providers.USFleetTracking (net8.0)

- Extric.Towbook.WebShared.Net5
- Extric.Towbook.Integrations.USFleetTracking

##### Extric.Towbook.API.Integrations.USFleetTracking (netstandard2.0)

- Extric.Towbook

##### Extric.Towbook.API.Integrations.USFleetTracking.Tests (net8.0)

- Extric.Towbook
- Extric.Towbook.API.Integrations.USFleetTracking

### MotorClubs

#### Honk

##### Honk (netstandard2.0)

- Extric.Towbook

##### HonkReceiverApi (net8.0)

- Extric.Towbook.Integration.MotorClubs
- Extric.Towbook.Integrations.Email
- Extric.Towbook.WebShared.Net5
- Honk

#### Libraries

##### Aaa (netstandard2.0)

- Extric.Towbook
- Services\AaaNationalSalesforceListener\ForceDotNetJwtCompanion

##### Agero (netstandard2.0)

- Extric.Towbook

##### Allstate (netstandard2.0)

- Extric.Towbook

##### Fleetnet (netstandard2.0)

- Extric.Towbook

##### ForceDotNetJwtCompanion (netstandard2.0)

##### Gerber (netstandard2.0)

- Extric.Towbook

##### Issc (netstandard2.0)

- Extric.Towbook.Integration.MotorClubs
- Extric.Towbook

##### Nac (netstandard2.0)

Comments

- Extric.Towbook

##### Nsd (netstandard2.0)

- Extric.Towbook

##### Quest (netstandard2.0)

- Extric.Towbook

##### StackThree (netstandard2.0)

- Extric.Towbook

##### Swoop (netstandard2.0)

- Extric.Towbook

##### Sykes (netstandard2.0)

- Extric.Towbook

##### Trx (netstandard2.0)

- Extric.Towbook

##### Urgently (netstandard2.0)

- Extric.Towbook

#### OutOfNetwork

##### OonAgeroClient (netstandard2.0)

- Extric.Towbook

##### OonAgeroReceiverApi (net8.0)

- Extric.Towbook.Integration.MotorClubs
- Extric.Towbook.WebShared.Net5
- OongeroClient

#### AaaAceReceiverApi (net8.0)

- Extric.Towbook.Integration.MotorClubs
- Extric.Towbook.WebShared.Net5
- Aaa
- Swoop

#### AaaNortheastReceiverApi (net8.0)

- Extric.Towbook.Integration.MotorClubs
- Extric.Towbook.WebShared.Net5
- Aaa

#### AaaReceiverApi (net8.0)

- Extric.Towbook.Integration.MotorClubs
- Extric.Towbook.WebShared.Net5
- Aaa

#### AgeroReceiverApi (net8.0)

- Extric.Towbook.Integration.MotorClubs
- Extric.Towbook.WebShared.Net5

#### AllstateReceiverApi (net8.0)

- Extric.Towbook.Integration.MotorClubs
- Extric.Towbook.Integrations.Email
- Extric.Towbook.WebShared.Net5
- Allstate

#### Extric.Towbook.Integration.MotorClubs.Test (net8.0) - Lots of comments

- Extric.Towbook.Integration.MotorClubs

#### FleetnetReceiverApi (net8.0)

- Extric.Towbook.Integration.MotorClubs
- Extric.Towbook.WebShared.Net5
- Fleetnet

#### GerberReceiverApi (net8.0)

- Extric.Towbook.Integration.MotorClubs
- Extric.Towbook.WebShared.Net5
- Gerber

#### IsscReceiverApi (net8.0)

- Extric.Towbook.Integration.MotorClubs
- Extric.Towbook.WebShared.Net5
- Geico\Issc.csproj (?)

#### NacReceiverApi (net8.0)

- Extric.Towbook.Integration.MotorClubs
- Extric.Towbook.WebShared.Net5
- Allstate
- Nac

#### NsdReceiverApi (net8.0)

- Extric.Towbook.Integration.MotorClubs
- Extric.Towbook.WebShared.Net5
- Nsd

#### QuestReceiverApi (net8.0)

- Extric.Towbook.Integration.MotorClubs
- Extric.Towbook.Integration.Email
- Extric.Towbook.WebShared.Net5
- Quest

#### RoadsideProtect (netstandard2.0)

- Extric.Towbook

#### StackThreeReceiverApi (net8.0)

- Extric.Towbook.Integration.MotorClubs
- Extric.Towbook.WebShared.Net5
- Extric.Towbook.Integration.MotorClubs.StackThree
- Swoop

#### SwoopReceiverApi (net8.0)

- Extric.Towbook.Integration.MotorClubs
- Extric.Towbook.WebShared.Net5
- Swoop

#### SykesReceiverApi (net8.0)

- Extric.Towbook.Integration.MotorClubs
- Extric.Towbook.WebShared.Net5
- Sykes

#### SykesTests (net8.0)

- Sykes

#### TowbookReceiverApi (net8.0)

- Extric.Towbook.Integration.MotorClubs
- Extric.Towbook.WebShared.Net5

#### TrxReceiverApi (net8.0)

- Extric.Towbook.Integration.MotorClubs
- Extric.Towbook.WebShared.Net5
- Swoop

#### UrgentlyReceiverApi (net8.0)

- Extric.Towbook.Integration.MotorClubs
- Extric.Towbook.Integrations.Email
- Extric.Towbook.WebShared.Net5
- Urgently

### Phone

#### Extric.Towbook.Integrations.Phone.Grasshopper (standard2.0)

- Extric.Towbook

#### Extric.Towbook.Integrations.Phone.Vonage (standard2.0)

- Extric.Towbook

### Quickbooks - EMPTY

- Also, this is just a virtual folder. No physical equivalent.

### SendGrid

#### Extric.Towbook.API.Integrations.SendGrid (net8.0)

Commented csproj.

- Extric.Towbook.WebShared.Net5

#### Extric.Towbook.API.Integrations.SendGrid.Tests (net8.0)

- Extric.Towbook.API.Integration.SendGrid

### Square

#### Extric.Towbook.API.Integrations.Square (net8.0)

- Extric.Towbook.WebShared.Net5
- MotorClubs\Quest
- Extric.Towbook.Square

#### SquareApiTest (net8.0)

- SquareApi\Extric.Towbook.API.Integration.Square

### vNext

#### Extric.Towbook.API.Integration.Accounting.Providers.QuickBooks (net8.0)

- Extric.Towbook.WebShared.Net5
- Extric.Towbook.Integrations.Quickbooks

#### Extric.Towbook.API.Integrations.Quickbooks (netstandard2.0)

- Extric.Towbook

### IntercomReceiverApi - PROJECT (net8.0)

- Extric.Towbook.Integration.MotorClubs
- Extric.Towbook.WebShared.Net5

## Roadside

### Extric.Roadside (netstandard2.0)

- Extric.Towbook

### Extric.Roadside.API (net8.0)

- Extric.Towbook.WebShared.Net5
- Extric.Roadside

## Solution Items

Pure sql files.

## System Background Services

### CacheSyncService (net8.0)

- Extric.Towbook.API

### CallIndexerService (net8.0)

- Extric.Towbook

### EventNotificationService (net8.0)

Some comments

- Roadside\Extric.Roadside

### Extric.Towbook.AutomatedMessagesService (net8.0)

- Extric.Towbook.Integrations.Email
- Integrations\Square\Square

### Extric.Towbook.NotificationService (net8.0)

- Extric.Towbook

### Extric.Towbook.ServerAgent (net8.0)

- Extric.Towbook.Integration.MotorClubs

### FleetnetConnectivityService (net8.0)

- Extric.Towbook.Integration.MotorClubs

### GpsCollectorService (net8.0)

- Integrations\GPS\Extric.Towbook.Integrations.DriverLocate
- Integrations\GPS\Extric.Towbook.Integrations.FleetComplete
- Integrations\GPS\Extric.Towbook.Integrations.Fleetmatics
- Integrations\GPS\Extric.Towbook.Integrations.GeoTab
- Integrations\GPS\Extric.Towbook.Integrations.Gps.Azuga
- Integrations\GPS\Extric.Towbook.Integrations.Samsara
- Integrations\GPS\Extric.Towbook.Integrations.TomTom
- Integrations\GPS\Extric.Towbook.Integrations.USFleetTracking

### MotorClubBillingService (net8.0)

- Extric.Towbook.Integration.MotorClubs
- Integrations\MotorClubs\Agero
- Integrations\MotorClubs\Fleetnet

### MotorClubDispatchingService (net8.0)

- Extric.Towbook.Integration.MotorClubs
- Extric.Towbook.Integration.Email
- Integrations\Gateway\DispatchGateway
- Integrations\MotorClubs\AaaReceiverApi
- Integrations\MotorClubs\Aaa
- Integrations\MotorClubs\Agero
- Integrations\MotorClubs\Allstate
- Integrations\MotorClubs\Extric.Towbook.Integration.MotorClubs.StackThree
- Integrations\MotorClubs\Fleetnet
- Integrations\MotorClubs\Geico\Issc
- Integrations\MotorClubs\Gerber
- Integrations\MotorClubs\Honk
- Integrations\MotorClubs\Nac
- Integrations\MotorClubs\Nsd
- Integrations\MotorClubs\OON\OonAgeroClient
- Integrations\MotorClubs\Quest
- Integrations\MotorClubs\RoadsideProtect
- Integrations\MotorClubs\Swoop
- Integrations\MotorClubs\Sykes
- Integrations\MotorClubs\Trx
- Integrations\MotorClubs\Urgently
- Roadside\Extric.Roadside

### MotorClubGpsService (net8.0)

- Extric.Towbook.Integration.MotorClubs
- Integrations\MotorClubs\Agero
- Integrations\MotorClubs\Allstate
- Integrations\MotorClubs\Extric.Towbook.Integration.MotorClubs.StackThree
- Integrations\MotorClubs\Fleetnet
- Integrations\MotorClubs\Geico\Issc
- Integrations\MotorClubs\Gerber
- Integrations\MotorClubs\Honk
- Integrations\MotorClubs\Nac
- Integrations\MotorClubs\Nsd
- Integrations\MotorClubs\Quest
- Integrations\MotorClubs\RoadsideProtect
- Integrations\MotorClubs\Swoop
- Integrations\MotorClubs\Sykes
- Integrations\MotorClubs\Urgently

### MotorClubMetricsService (net8.0)

- Extric.Towbook.Integration.MotorClubs

### MotorClubPaymentImportService (net8.0)

- Extric.Towbook.Integration.MotorClubs

### MotorClubPaymentImportServiceTests (net8.0)

- Extric.Towbook.Integration.MotorClubs

### MotorClubProcessingService (net8.0)

- Extric.Towbook.Integration.MotorClubs

### ProcessQueueService (net8.0)

- Extric.Towbook.WebShared.Net5
- Integrations\vNext\Extric.Towbook.Integrations.Quickbooks

### QueueProxyService (net8.0)

- Extric.Towbook.API
- Extric.Towbook-Integration.MotorClubs

### SendIntegrationService (net8.0)

- Extric.Towbook

### SquareService (net8.0)

- Extric.Towbook.Integration.MotorClubs
- Integrations\Square\Square

### StickeringProcessingService (net8.0)

- Extric.Towbook.Integrations.Email

### SupportDataSyncService (net8.0)

- Integrations\GPS\Extric.Towbook.Integrations.DriverLocate
- Integrations\GPS\Extric.Towbook.Integrations.GeoTab
- Integrations\GPS\Extric.Towbook.Integrations.TomTom
- Integrations\GPS\Extric.Towbook.Integrations.USFleetTracking

### TomTomService (net8.0)

- Extric.Towbook.API
- Extric.Towbook.Integration.MotorClubs

### VendorProcessingService (net8.0)

- Extric.Towbook.Integration.MotorClubs
- Extric.Towbook.Integrations.Email

## Tests

### Automation

Pure Postman files for testing of dispatching and callers.

### Dummy

#### Extric.Towbook.Dummy (net8.0)

- Integrations\MotorClubs\Urgently
- Integrations\MotorClubs\Swoop

## Root

### Extric.Towbook (netstandard2.0)

- Extric.Towbook.Generated.Features
- Extric.Towbook.Storage
- Glav.CacheAdapter

### Extric.Towbook.API.Tests (net8.0)

- Extric.Towbook.API

### Extric.Towbook.API.Utils (net8.0-windows)

- Extric.Towbook

### Extric.Towbook.DynamicFeatureBuilder (net8.0)

### Extric.Towbook.Generated.Features (netstandard2.0)

### Extric.Towbook.Integration.MotorClubs (netstandard2.0)

- Integrations\MotorClubs\Agero
- Integrations\MotorClubs\Allstate
- Integrations\MotorClubs\Fleetnet
- Integrations\MotorClubs\Trx

### Extric.Towbook.MotorClubs (net8.0)

- Ajax
- Extric.Towbook.Integrations.Phone.Grasshopper
- Extric.Towbook.Integrations.Phone.Vonage
- Services\GpsCollectorService
- Services\MotorClubBillingService
- Services\MotorClubDispatchingService
- Services\MotorClubPaymentImportService
- Services\VendorProcessingService

### Extric.Towbook.Square (netstandard2.0)

- Extric.Towbook

### Extric.Towbook.Storage (netstandard2.0)

### Extric.Towbook.Tests (net8.0)

- Extric.Towbook.WebShared.Net5

### Extric.Towbook.TWS (netstandard2.0)

- Extric.Towbook

### Extric.Towbook.WebShared (net8.0)

- Extric.Towbook

### Extric.Towbook.WebShared.Net5 (net8.0)

- Extric.Towbook

### Extric.Towbook.WebWrapper (netstandard2.0)

### Glav.CacheAdapter (netstandard2.0)

- Extric.Towbook.WebWrapper

### RoadsideProtectReceiverApi (net8.0)

- Extric.Towbook.Integration.MotorClubs
- Extric.Towbook.WebShared.Net5
- RoadsideProtect

### SafeclearSyncTool (net8.0)

- Extric.Towbook