# Diagrams

## HTTP Folder Component Diagram

Basic Simple Component Structure:
````
@startuml

package "HTTP" {
  [Ajax] << net8.0 >>
  [App] << NetFramework 4.8 >>
  [Extric.Towbook.Agent.API] << net8.0 >>
  [Extric.Towbook.API] << net8.0 >>
  [Extric.Towbook.API.Models] << netstandard2.0 >>
  [Extric.Towbook.DirectBilling.API] << net8.0 >>
  [Extric.Towbook.InternalVoiceApi] << net8.0 >>
  [Extric.Towbook.PermitRequests.API] << net8.0 >>
  [Extric.Towbook.TWS.API.Public] << net8.0 >>
  [Extric.Towbook.VoiceDispatchApi] << net8.0 >>
  [Extric.Towbook.Web.LocateMvc] << net8.0 >>
  [Extric.Towbook.Web.PublicAddress] << net8.0 >>
  [Extric.Towbook.Web.PublicRequestCallMvc] << net8.0 >>
  [NodeApp] << React? >>
  [TWS] << net8.0 >>
}

@enduml
````

## Integrations Folder Component Diagram

````
@startuml
package "Integrations" {
  folder "AutoDataDirect" {
    [Extric.Towbook.API.Integration.Data.Providers.AutoData] << net8.0 >>
    [Extric.Towbook.Integrations.AutoData] << netstandard2.0 >>
    [Extric.Towbook.Integrations.AutoData.Tests] << net4.8 >> 
  }

  folder "BrainTree" {
    [Extric.TowBook.API.Integration.Internal.Providers.BrainTree] << net8.0 >>
  }

  folder "Fax and Email" {
    [Extric.Towbook.Integrations.Email] << netstandard2.0 >>
    [Extric.Towbook.Integrations.Faxing] << netstandard2.0 >>
  }

  folder "Gateway" {
    [AutoRescueApi] << net8.0 >>
    [DispatchGateway] << netstandard2.0 >> 
  }

  folder "GPS" {
    folder "Azuga" {
      [Extric.Towbook.API.Integration.GPS.Providers.Azuga] << net8.0 >>
      [Extric.Towbook.Integrations.Gps.Azuga] << netstandard2.0 >>
    }
    folder "DriverLocate" {
      [Extric.Towbook.Web.LocateMvc] << net8.0 >>
      [Extric.Towbook.Web.PublicAddress] << net8.0 >>
      [Extric.Towbook.Web.PublicRequestCallMvc] << net8.0 >>
    }
    folder "FleetComplete" {
      [Extric.Towbook.Integrations.GPS.Providers.FleetComplete] << net8.0 >>
      [Extric.Towbook.Integrations.FleetComplete] << netstandard2.0 >>
    }
    folder "Fleetmatics" {
      [Extric.Towbook.API.Integration.GPS.Providers.Fleetmatics] << net8.0 >>
      [Extric.Towbook.Integrations.Fleetmatics] << net8.0 >>
    }
    folder "GeoTab" {
      [Extric.Towbook.API.Integration.GPS.Providers.GeoTab] << net8.0 >>
      [Extric.Towbook.Integrations.GeoTab] << netstandard2.0 >>
      [Extric.Towbook.Integrations.GeoTab.Tests] << net8.0 >>
    }
    folder "NetworkFleet" {
      [Extric.Towbook.API.Integration.GPS.Providers.NetworkFleet] << net8.0 >>
      [Extric.Towbook.API.Integrations.Networkfleet] << netstandard2.0 >>
      [Extric.Towbook.API.Integrations.Networkfleet.Tests] << net8.0 >>
    }
    folder "Samsara" {
      [Extric.Towbook.API.Integration.GPS.Providers.Samsara] << net8.0 >>
      [Extric.Towbook.Integrations.Samsara] << netstandard2.0 >>
    }
    folder "TomTom" {
      [Extric.Towbook.API.Integration.GPS.Providers.TomTom] << net8.0 >>
      [Extric.Towbook.Integrations.TomTom] << netstandard2.0 >>
    }
    folder "USFleetTracking" {
      [Extric.Towbook.API.Integration.GPS.Providers.USFleetTracking] << net8.0 >>
      [Extric.Towbook.API.Integrations.USFleetTracking] << netstandard2.0 >>
      [Extric.Towbook.API.Integrations.USFleetTracking.Tests] << net8.0 >>
    }
  }

  folder "MotorClubs" {
    folder "HonkFolder" as "Honk" {
      [HonkComponent] << netstandard2.0 >>
      [HonkReceiverApi] << net8.0 >>
    }
    folder "Libraries" {
      [Aaa] << netstandard2.0 >>
      [Agero] << netstandard2.0 >>
      [Allstate] << netstandard2.0 >>
      [Fleetnet] << netstandard2.0 >>
      [ForceDotNetJwtCompanion] << netstandard2.0 >>
      [Gerber] << netstandard2.0 >>
      [Issc] << netstandard2.0 >>
      [Nac] << netstandard2.0 >>
      [Nsd] << netstandard2.0 >>
      [Quest] << netstandard2.0 >>
      [StackThree] << netstandard2.0 >>
      [Swoop] << netstandard2.0 >>
      [Sykes] << netstandard2.0 >>
      [Trx] << netstandard2.0 >>
      [Urgently] << netstandard2.0 >>
    }
    folder "OutOfNetwork" {
      [OonAgeroClient] << netstandard2.0 >>
      [OonAgeroReceiverApi] << net8.0 >>
    }   
    [AaaAceReceiverApi] << net8.0 >>
    [AaaNortheastReceiverApi] << net8.0 >>
    [AaaReceiverApi] << net8.0 >>
    [AgeroReceiverApi] << net8.0 >>
    [AllstateReceiverApi] << net8.0 >>
    [Extric.Towbook.Integration.MotorClubs.Test] << net8.0 >>
    [FleetnetReceiverApi] << net8.0 >>
    [GerberReceiverApi] << net8.0 >>
    [IsscReceiverApi] << net8.0 >>
    [NacReceiverApi] << net8.0 >>
    [NsdReceiverApi] << net8.0 >>
    [QuestReceiverApi] << net8.0 >>
    [RoadsideProtect] << netstandard2.0 >>
    [StackThreeReceiverApi] << net8.0 >>
    [SwoopReceiverApi] << net8.0 >>
    [SykesReceiverApi] << net8.0 >>
    [SykesTests] << net8.0 >>
    [TowbookReceiverApi] << net8.0 >>
    [TrxReceiverApi] << net8.0 >>
    [UrgentlyReceiverApi] << net8.0 >>
    [UrgentlyReceiverApi] << net8.0 >>
  }

  folder "Phone" {
    [Extric.Towbook.Integrations.Phone.Grasshopper] << netstandard2.0 >>
    [Extric.Towbook.Integrations.Phone.Vonage] << netstandard2.0 >>
  }

  folder "Quickbooks" {
  }

  folder "SendGrid" {
    [Extric.Towbook.API.Integrations.SendGrid] << net8.0 >>
    [Extric.Towbook.API.Integrations.SendGrid.Tests] << net8.0 >>
  }

  folder "Square" {
    [Extric.Towbook.API.Integrations.Square] << net8.0 >>
    [SquareApiTest] << net8.0 >>
  }

  folder "vNext" {
    [Extric.Towbook.API.Integration.Accounting.Providers.QuickBooks] << net8.0 >>
    [Extric.Towbook.API.Integrations.Quickbooks] << netstandard2.0 >>
  }

  [IntercomReceiverApi] << net8.0 >>
}
@enduml
````

## Roadside folder component diagram

````
@startuml
package "Roadside" {
  [Extric.Roadside] << netstandard2.0 >>
  [Extric.Roadside.API] << net8.0 >>
}
@enduml
````

## Solution items folder component diagram

## System Background Services folder component diagram

````
@startuml

package "System Background Services" {
  [CacheSyncService] << net8.0 >>
  [CallIndexerService] << net8.0 >>
  [EventNotificationService] << net8.0 >>
  [Extric.Towbook.AutomatedMessagesService] << net8.0 >>
  [Extric.Towbook.NotificationService] << net8.0 >>
  [Extric.Towbook.ServerAgent] << net8.0 >>
  [FleetnetConnectivityService] << net8.0 >>
  [GpsCollectorService] << net8.0 >>
  [MotorClubBillingService] << net8.0 >>
  [MotorClubDispatchingService] << net8.0 >>
  [MotorClubGpsService] << net8.0 >>
  [MotorClubMetricsService] << net8.0 >>
  [MotorClubPaymentImportService] << net8.0 >>
  [MotorClubPaymentImportServiceTests] << net8.0 >>
  [MotorClubProcessingService] << net8.0 >>
  [ProcessQueueService] << net8.0 >>
  [QueueProxyService] << net8.0 >>
  [SendIntegrationService] << net8.0 >>
  [SquareService] << net8.0 >>
  [StickeringProcessingService] << net8.0 >>
  [SupportDataSyncService] << net8.0 >>
  [VendorProcessingService] << net8.0 >>
}

@enduml
````

## Tests folder component diagram
````
@startuml

package "Tests" {
  folder "Dummy" {
    [Extric.Towbook.Dummy] << net8.0 >>
  }
}

@enduml
````

## Root folder component diagram

````
@startuml

package "Root" {
  [Extric.Towbook] << netstandard2.0 >>
  [Extric.Towbook.API.Tests] << net8.0 >>
  [Extric.Towbook.API.Utils] << net8.0-windows >>
  [Extric.Towbook.DynamicFeatureBuilder] << net8.0 >>
  [Extric.Towbook.Generated.Features] << netstandard2.0 >>
  [Extric.Towbook.Integration.MotorClubs] << netstandard2.0 >>
  [Extric.Towbook.MotorClubs] << net8.0 >>
  [Extric.Towbook.Square] << netstandard2.0 >>
  [Extric.Towbook.Storage] << netstandard2.0 >>
  [Extric.Towbook.Tests] << net8.0 >>
  [Extric.Towbook.TWS] << netstandard2.0 >>
  [Extric.Towbook.WebShared] << net8.0 >>
  [Extric.Towbook.WebShared.Net5] << net8.0 >>
  [Extric.Towbook.WebWrapper] << netstandard2.0 >>
  [Glav.CacheAdapter] << netstandard2.0 >>
  [RoadsideProtectReceiverApi] << net8.0 >>
  [SafeclearSyncTool] << net8.0 >>
}

@enduml
````