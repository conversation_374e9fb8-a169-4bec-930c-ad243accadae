# Introduction

This is a small document that describes the Extric.Towbook.Dummy project. This 
is physically at `/Tests/Dummy/Extric.Towbook.Dummy`. Great care was taken in 
order to make the Solution Explorer's view match the physical plane.

## Patterns and Folder Structure

The project is in .NET 8, (.NET Core), and it ports all controllers that were 
at the `Extric.Towbook.API` level. However, they are not class-based, they were 
ported to **Minimal APIs**. The structure is simple: `/MotorClubs/Controllers/...`. 
At this level we have files all following the same pattern: through the usage 
of an extension method, all the endpoints are declared in a pretty declarative 
manner and then they all get injected into the DI container at the `Program.cs` 
level through one simple call to them.

````csharp
var builder = WebApplication.CreateBuilder(args);

// Add services to DI container

var app = builder.Build();

// Configure the HTTP request pipeline

app.AddAaaAceDummyEndpoints();
app.AddAllstateDummyEndpoints();
app.AddHonkDummyEndpoints();

app.Run();
````

## Program.cs

The structure has also made use of things such as `GlobalUsings`, a concept introduced 
in .NET 6, in order to keep classes lean and clean. The `Program.cs` file also remains 
clean because it uses the *Top Level Statements* feature of .NET so that code is 
more focused and all the overhead of a class, its body and others are abstracted 
away and the developers can simply focus on the actual code that is important.