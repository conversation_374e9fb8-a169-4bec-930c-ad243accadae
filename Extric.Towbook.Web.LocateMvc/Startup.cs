using Extric.Towbook.Configuration;
using Extric.Towbook.WebShared;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using NLog;

namespace Extric.Towbook.Web.LocateMvc
{
    public class Startup
    {
        private static readonly Logger logger = LogManager.GetCurrentClassLogger();

        public Startup(IConfiguration configuration)
        {
            Configuration = configuration;
        }

        public IConfiguration Configuration { get; }

        public void ConfigureServices(IServiceCollection services)
        {
            logger.Info("Startup configuring services...");
            services.AddRazorPages();
            services.AddHttpContextAccessor();
            services.ConfigureCore();
        }

        public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
        {
            if (env.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
            }
            else
            {
                app.UseExceptionHandler("/Home/Error");
            }
            app.UseStaticFiles();

            app.UseRouting();

            app.UseAuthorization();

            app.UseEndpoints(endpoints =>
            {
                endpoints.MapControllers();
            });

            HttpContext.Configure(app.ApplicationServices.GetRequiredService<IHttpContextAccessor>(), env, app.ApplicationServices);
            HttpContextFactory.Instance = new HttpContextNet5();
            logger.Info("Startup confgured");
        }
    }
}
