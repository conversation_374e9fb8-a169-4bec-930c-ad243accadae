using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Threading.Tasks;
using Extric.Towbook.Accounts;
using Extric.Towbook.PermitRequests.API.Models;
using Extric.Towbook.Stripe;
using Extric.Towbook.Utility;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Stripe;


namespace Extric.Towbook.PermitRequests.API.Controllers
{
    [Route("permits/requests/guestpass")]
    [AllowAnonymous]
    public class RequestAccountGuestPassController : ControllerBase
    {
        //rl.Add(routes.MapHttpRoute(
        //    name: "Guest_Pass_Get",
        //    routeTemplate: root + "requests/guestpass/{hash}/pass/{id}",
        //    defaults: new { action = "Get", controller = "requestAccountGuestPass" },
        //    constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "Get" }) }));
        [Route("{hash}/pass/{id}")]
        [HttpGet]
        public VisitorPassModel Get(string hash, string id)
        {
            var ids = HelperUtility.DecodeUrlHash(id);
            var hashs = HelperUtility.DecodeUrlHash(hash);
            if (ids.Length != 2 || hashs.Length != 2 || ids[1] != hashs[1])
            {
                throw new Extric.Towbook.Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.Forbidden)
                {
                    Content = new StringContent("Invalid parameters provided. You don't have permission.")
                });
            }

            int permitId = ids[0];
            int accountId = ids[1];
            int requestId = hashs[0];

            var pp = ParkingPermit.GetById(permitId);
            if (pp == null || pp.AccountId != accountId)
            {
                throw new Extric.Towbook.Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.Forbidden)
                {
                    Content = new StringContent("Visitor pass doesn't exist.")
                });
            }

            int? userId = null;
            var request = ParkingPermitRequest.GetByPermitId(pp.Id);
            if (request != null)
                userId = request.UserId;

            return VisitorPassModel.Map(pp, requestId, userId);
        }

        [Route("{hash}/create")]
        [HttpPost]
        public async Task<object> Create(string hash, PaymentIntentVisitorPassCreateModel pivpcr)
        {
            var ids = HelperUtility.DecodeUrlHash(hash);

            if (ids.Length != 2)
            {
                throw new Extric.Towbook.Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.Forbidden)
                {
                    Content = new StringContent("Invalid parameters provided. You don't have permission.")
                });
            }

            int linkId = ids[0];
            int accountId = ids[1];

            var account = await Accounts.Account.GetByIdAsync(accountId);
            if (account == null)
            {
                throw new Extric.Towbook.Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound)
                {
                    Content = new StringContent("AccountId is invalid or there is no account associated by that Id.")
                });
            }

            var link = ParkingPermitPublicLink.GetById(linkId);
            if (link == null || link.AccountId != account.Id)
            {
                throw new Extric.Towbook.Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound)
                {
                    Content = new StringContent("Property not found by linkId or you don't have permission.")
                });
            }

            var metaData = new Dictionary<string, string>();

            metaData.Add("AtProperty", account.Company);
            metaData.Add("AccountId", account.Id.ToString());
            metaData.Add("CompanyId", account.CompanyId.ToString());

            var company = await Company.Company.GetByIdAsync(account.CompanyId);
            if (company != null)
                metaData.Add("CompanyName", company.ShortName);

            if (!string.IsNullOrEmpty(Web.HttpContext.Current.Request.Path + Web.HttpContext.Current.Request.QueryString.ToUriComponent()))
                metaData.Add("Referrer", Web.HttpContext.Current.Request.Path + Web.HttpContext.Current.Request.QueryString.ToUriComponent());

            if (!string.IsNullOrEmpty(pivpcr.TenantName))
                metaData.Add("TenantName", pivpcr.TenantName);

            if (!string.IsNullOrEmpty(pivpcr.UnitNumber))
                metaData.Add("UnitNumber", pivpcr.UnitNumber);

            var paymentIntent = await StripeUtility.CreateStripeTransactionAndPaymentIntentAsync(account.CompanyId, account.Id, pivpcr.Amount, pivpcr.NoOfFeeItems, request: null, metaData, "automatic");

            return new { clientSecret = paymentIntent?.ClientSecret };
        }

        //rl.Add(routes.MapHttpRoute(
        //    name: "Guest_Pass_Post",
        //    routeTemplate: root + "requests/guestpass/{hash}",
        //    defaults: new { action = "Post", controller = "requestAccountGuestPass" },
        //    constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "Post" }) }));
        [Route("{hash}")]
        [Route("{hash}/post")]
        [HttpPost]
        public async Task<HttpResponseMessage> Post(string hash, VisitorPassModel model)
        {
            var pHash = ParkingPermitPublicLink.GenerateUrlHash(model.LinkId, model.AccountId, true);
            if (pHash != hash)
            {
                throw new Extric.Towbook.Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.Forbidden)
                {
                    Content = new StringContent("Invalid parameters provided. You don't have permission.")
                });
            }

            var account = await Accounts.Account.GetByIdAsync(model.AccountId);
            if (account == null)
            {
                throw new Extric.Towbook.Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound)
                {
                    Content = new StringContent("AccountId is invalid or there is no account associated by that Id.")
                });
            }

            var link = ParkingPermitPublicLink.GetById(model.LinkId);
            if (link == null || link.AccountId != account.Id)
            {
                throw new Extric.Towbook.Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound)
                {
                    Content = new StringContent("Property not found by linkId or you don't have permission.")
                });
            }

            StripeTransaction stripeTransaction = null;

            // attempt auth before creating any objects in the datastore.  Throw errors early.
            var order = new ParkingPermitApprovalSessionOrder();
            if (model.Order != null && model.Order.PreAuthTotal > 0)
            {
                order = ParkingPermitOrderModel.Map(model.Order);

                #region Configure meta data
                var metaData = new Dictionary<string, string>();

                metaData.Add("Name", model.Name);

                if (!string.IsNullOrWhiteSpace(model.Phone))
                    metaData.Add("Phone", model.Phone);

                if (!string.IsNullOrWhiteSpace(model.Email))
                    metaData.Add("Email", model.Email);

                string ccDetails = null;
                if (!string.IsNullOrWhiteSpace(order.Last4))
                    ccDetails = $"{order.Last4}";

                if (!string.IsNullOrWhiteSpace(order.Brand))
                    ccDetails += $" ({order.Brand})";

                if (ccDetails != null)
                    metaData.Add("CardInfo", ccDetails);

                metaData.Add("AtProperty", account.Company);

                if (model.Vehicle != null) { }
                string vehicle = $"{(model.Vehicle.Year != null ? model.Vehicle.Year.Value.ToString() : "")}" +
                    $" {model.Vehicle.Make} {model.Vehicle.Model} {model.Vehicle.Color}" +
                    $" {model.Vehicle.LicensePlate} {model.Vehicle.LicenseState}";

                metaData.Add("VisitorPass", vehicle);

                if (!string.IsNullOrEmpty(Web.HttpContext.Current.Request.Path + Web.HttpContext.Current.Request.QueryString.ToUriComponent()))
                    metaData.Add("Referrer", Web.HttpContext.Current.Request.Path + Web.HttpContext.Current.Request.QueryString.ToUriComponent());

                stripeTransaction = StripeTransaction.GetByPaymentIntent(StripeUtility.GetPaymentIntentFromClientSecret(order.PaymentIntentClientSecret));
                
                try
                {
                    // capture immediately
                    var capture = StripeUtility.ChargeAmount(
                                            account.CompanyId,
                                            account.Company,
                                            order.PaymentMethodId,
                                            order.PaymentIntentClientSecret,
                                            order.PreAuthTotal.Value,
                                            "Visitor Pass Fee",
                                            metaData);

                    if (capture != null)
                    {
                        // no preauth, capture only
                        order.PreAuth = string.Empty;
                        order.PreAuthTotal = (int?)null;
                        order.PreAuthFee = capture.PreAuthFee;
                        order.ApprovedTotal = capture.Charge?.Amount;
                        order.ApprovedAuth = capture.Charge?.Id;
                        order.ApprovedFee = capture.ApprovedFee;
                        order.ApplicationFeeId = capture.Charge?.ApplicationFee?.Id;
                        order.StripeTransferId = capture.Transfer?.Id;
                        order.StripePaymentId = capture.Transfer?.DestinationPaymentId;
                        order.PaymentIntentId = capture.Intent?.Id;
                        order.PaymentIntentClientSecret = capture.Intent?.ClientSecret;
                    }
                }
                catch (StripeException e)
                {
                    var ex = new StripeApiException()
                    {
                        AccountId = account.Id,
                        CompanyId = account.CompanyId,
                        RequestId = model.RequestId,
                        PreAuthTotal = order?.PreAuthTotal,
                        SessionOrderId = order?.Id,
                        StripeChargeReference = order?.PreAuth,
                        StripeIntentClientSecret = order?.PaymentIntentClientSecret,
                        StripeTokenReference = order?.Token,
                        UserId = (int?)null,
                        HttpStatusCode = e.HttpStatusCode,
                        Type = e.StripeError?.Type,
                        Code = e.StripeError?.Code,
                        DeclineCode = e.StripeError?.DeclineCode,
                        StripeMessage = e.StripeError.Message,
                        Param = e.StripeError.Param
                    };

                    // Log activity
                    await HelperUtility.LogErrorAsync(new Dictionary<string, object>
                    {
                        ["type"] = "Visitor Pass Charge Error",
                        ["order"] = order.ToJson(),
                        ["userId"] = model.userId,
                        ["account"] = account.Company,
                        ["accountId"] = account.Id,
                        ["referenceId"] = stripeTransaction?.Id
                    }, account.CompanyId, e);

                    var msg = StripeApiException.HandleExceptionMessage(ex);

                    if(stripeTransaction != null &&
                        !stripeTransaction.Deleted &&
                        ex.Type == "invalid_request_error" &&
                        ex.StripeMessage == "The parameter application_fee_amount cannot be updated on a PaymentIntent after a capture has already been made.")
                    {
                        // delete the transaction
                        stripeTransaction.CaptureAmount = (int?)order?.ApprovedTotal;
                        stripeTransaction.RequestId = model.RequestId;
                        stripeTransaction.PermitUserId = model.userId;
                        
                        if(order?.Id > 0)
                            stripeTransaction.AccountParkingPermitApprovalSessionOrderId = order?.Id;

                        stripeTransaction.Deleted = true;
                        stripeTransaction.Save();
                    }

                    return new HttpResponseMessage(HttpStatusCode.InternalServerError) { Content = new StringContent(msg) };
                }
            }
            #endregion


            var lp = model.Vehicle?.LicensePlate;
            if (!string.IsNullOrWhiteSpace(lp))
                lp = lp.Replace(" ", "");

            ParkingPermit pp = new ParkingPermit()
            {
                AccountId = model.AccountId,
                ParkingPermitListId = (int)ParkingPermitListBuiltInTypes.Visitor,
                StatusId = PermitStatus.Valid.Id,
                LicensePlate = lp.Truncate(10),
                LicensePlateState = (model.Vehicle?.LicenseState ?? string.Empty).Truncate(50),
                VehicleModel = (model.Vehicle?.Model ?? string.Empty).Truncate(50),
                VehicleMake = (model.Vehicle?.Make ?? string.Empty).Truncate(50),
                VehicleColor = (model.Vehicle?.Color ?? string.Empty).Truncate(50),
                VehicleYear = model.Vehicle?.Year,
                VIN = (model.Vehicle?.VIN ?? string.Empty).Truncate(17),
                FullName = model.Name.Truncate(50),
                CellPhone = Core.FormatPhone(model.Phone, true).Truncate(32),
                Email = model.Email.Truncate(100),
                Notes = $"Visiting: {model.TenantRoll?.TenantName}".Truncate(100),
                UnitNumber = (model.TenantRoll?.UnitNumber ?? string.Empty).Truncate(10),
                CreateDate = DateTime.Now
            };

            // set the expiration date
            var s = ParkingPermitSetting.GetByAccountId(account.CompanyId, account.Id);
            pp.ExpirationDate = ParkingPermit.GetExpirationDate(pp, s);
            await pp.Save();


            // generate a request for the sake of connecting to a future user profile
            ParkingPermitRequest request = new ParkingPermitRequest()
            {
                AccountId = pp.AccountId,
                CellPhone = Core.FormatPhone(pp.CellPhone, true).Truncate(32),
                Email = pp.Email.Truncate(100),
                UnitNumber = pp.UnitNumber.Truncate(10),
                FullName = model.Name.Truncate(50),
                LastActivity = DateTime.Now.ToLocalTime(),
                UserTypeId = Role.Guest.Id
            };

            await request.Save(false);

            // save url and hash (with new id)
            request.Url = HelperUtility.GenerateUrlHash(request.Id, request.AccountId);
            request.Code = HelperUtility.GenerateUrlHash(request.Id, request.AccountId, true);

            // update the invite
            await request.Save(false);

            // cancel the invite from showing on the list.
            await request.Delete();

            // generate as approved
            ParkingPermitApprovalSession session = new ParkingPermitApprovalSession()
            {
                RequestId = request.Id,
                CompanyId = account.CompanyId,
                AccountId = pp.AccountId,
                SessionStatusId = (int)ApprovalSessionStatuses.Approved,
                CreateDate = DateTime.Now.ToLocalTime(),
                ApprovalDate = DateTime.Now.ToLocalTime(),
                ApprovedByUserId = 1,
                SessionOrderId = order != null && order.Id > 0 ? order.Id : (int?)null,
                Deleted = true,
            };

            await session.Save(false);

            // Create and save session order information
            if (order != null)
            {
                // Saving order info
                order.ApprovalSessionId = session.Id;
                order.Save();

                session.SessionOrderId = order.Id;
                await session.Save(false);


                // update StripeTransaction
                
                if(stripeTransaction != null)
                {
                    stripeTransaction.CaptureAmount = (int?)order.ApprovedTotal;
                    stripeTransaction.RequestId = request?.Id;
                    stripeTransaction.PermitUserId = request?.UserId;
                    stripeTransaction.UnitNumber = request?.UnitNumber;
                    
                    if (order?.Id > 0)
                        stripeTransaction.AccountParkingPermitApprovalSessionOrderId = order.Id;
                    
                    stripeTransaction.CaptureAmount = (int?)order?.ApprovedTotal;

                    if (string.IsNullOrEmpty(stripeTransaction.TenantName))
                        stripeTransaction.TenantName = pp.FullName;
                    
                    if (string.IsNullOrEmpty(stripeTransaction.UnitNumber))
                        stripeTransaction.UnitNumber = pp.UnitNumber;

                    stripeTransaction.Deleted = true;
                    stripeTransaction.Save();
                }

            }

            ParkingPermitApprovalSessionItem item = new ParkingPermitApprovalSessionItem()
            {
                ApprovalSessionId = session.Id,
                ParkingPermitId = pp.Id,
                ParkingPermitListId = (int)ParkingPermitListBuiltInTypes.Visitor,
                Approved = true,
                LicensePlate = pp.LicensePlate.Truncate(10),
                LicensePlateState = pp.LicensePlateState.Truncate(50),
                VehicleModel = pp.VehicleModel.Truncate(50),
                VehicleMake = pp.VehicleMake.Truncate(50),
                VehicleYear = pp.VehicleYear,
                VehicleColor = pp.VehicleColor.Truncate(50),
                VIN = pp.VIN,
            };
            item.Save();

            // welcome message to the applicant
            await HelperUtility.SendWelcomeMessageToGuestPassApplicant(pp, request);

            // prepare return data
            model.RequestId = request.Id;
            model.PermitId = pp.Id;


            HelperUtility.LogInfo(new Dictionary<object, object>
            {
                ["type"] = "Visitor Pass Submission",
                ["permit"] = pp.Id,
                ["session"] = session.Id,
                ["request"] = request.Id,
                ["account"] = account.Company,
                ["accountId"] = account.Id,
                ["order"] = model,
                ["vehicle"] = model.Vehicle,
                ["referenceId"] = stripeTransaction?.Id
            }, account.CompanyId);

            return new HttpResponseMessage(HttpStatusCode.OK)
            {
                Content = new StringContent(HelperUtility.GenerateGuestPassUrlLink(request.Id, request.AccountId, pp.Id, false))
            };
        }

        [Route("{hash}/pass/{id}")]
        [HttpDelete]
        public async Task<HttpResponseMessage> Delete(string hash, int id)
        {
            return await new RequestAccountResidentPassController().Delete(hash, id);
        }

        [Route("{hash}")]
        [Route("{hash}/renew")]
        [HttpPut]
        public async Task<HttpResponseMessage> Renew(string hash, ResidentPassModel model)
        {
            return await new RequestAccountResidentPassController().Renew(hash, model);
        }

        //rl.Add(routes.MapHttpRoute(
        //    name: "Guest_Pass_download",
        //    routeTemplate: root + "requests/guestpass/{hash}/pass/{id}/pdf",
        //    defaults: new { action = "pdf", controller = "requestAccountGuestPass" },
        //    constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "Get" }) }));
        [Route("{hash}/pass/{id}/pdf")]
        [HttpGet]
        public async Task<IActionResult> Pdf(string hash, string id)
        {
            var ids = HelperUtility.DecodeUrlHash(id);
            var hashs = HelperUtility.DecodeUrlHash(hash);
            if (ids.Length != 2 || hashs.Length != 2 || ids[1] != hashs[1])
            {
                throw new Extric.Towbook.Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.Forbidden)
                {
                    Content = new StringContent("Invalid parameters provided. You don't have permission.")
                });
            }

            int permitId = ids[0];
            int accountId = ids[1];
            int requestId = hashs[0];

            var account = await Accounts.Account.GetByIdAsync(accountId);
            if (account == null)
            {
                throw new Extric.Towbook.Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound)
                {
                    Content = new StringContent("AccountId is invalid or there is no account associated by that Id.")
                });
            }

            var items = ParkingPermitApprovalSessionItem.GetByRequestId(requestId);
            int? sessionId = null;
            if (items != null)
            {
                sessionId = items.Select(s => s.ApprovalSessionId).Distinct().LastOrDefault();
            }

            if (sessionId == null)
            {
                throw new Extric.Towbook.Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.NotFound)
                {
                    Content = new StringContent("The vehicle permit is not available for download at this time. Please try again later.")
                });
            }

            // Get html contents
            var token = Extric.Towbook.Core.ProtectId(account.Id, sessionId.Value);

            var statusCode = 0;
            var url = $"https://app.towbook.com/publicAccess/ParkingPermit.aspx?requestId={requestId}&token={token}";
            var html = HelperUtility.GetResponseFromUrl(url, out statusCode);

            if (statusCode != 200)
            {
                throw new Extric.Towbook.Web.HttpResponseException(new HttpResponseMessage(HttpStatusCode.InternalServerError)
                {
                    Content = new StringContent("Error retrieving Permit pdf download. HttpStatusCode:" + statusCode + ", Response:" + html)
                });
            }

            // Generate PDF
            var ms = await HelperUtility.GeneratePdf(html, account);
            ms.Position = 0;

            return File(ms.ToArray(), "application/pdf", $"ParkingPermits.pdf");
        }

    }
}
