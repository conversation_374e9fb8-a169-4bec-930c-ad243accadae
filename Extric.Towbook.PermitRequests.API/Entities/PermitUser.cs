using Extric.Towbook.Utility;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;

namespace Extric.Towbook.PermitRequests.API
{
    [Table("PPIO.PermitUsers")]
    public class PermitUser
    {
        [Key("UserId")]
        public int Id { get; set; }
        public string Name { get; set; }
        public string Phone { get; set; }
        public string Email { get; set; }
        public int UserTypeId { get; set; }
        public string UserName { get; set; }
        public string Password { get; set; }

        public DateTime CreateDate { get; set; }
        public bool Deleted { get; set; }

        [Write(false)]
        public string Token { get; set; }

        [Write(false)]
        public string UserType
        {
            get
            {
                return Role.GetNameById(this.UserTypeId);
            }
        }

        public static PermitUser GetById(int id)
        {
            var user = SqlMapper.Query<PermitUser>(
                "SELECT * FROM PPIO.PermitUsers WHERE UserId=@Id",
                new { Id = id })
                .FirstOrDefault();

            if (user != null)
                user.Password = null;

            return user;
        }

        public void Save()
        {
            if (Id < 1)
            {
                CreateDate = DateTime.Now;

                Id = Convert.ToInt32(SqlMapper.QuerySP<dynamic>("PPIO.PermitUserInsert", 
                    new
                    {
                        UserTypeId,
                        UserName,
                        Password,
                        Name,
                        Phone,
                        Email
                    }).FirstOrDefault().Id);
            }
            else
            {
                SqlMapper.ExecuteSP("PPIO.PermitUserUpdate",
                    new
                    {
                        UserId = Id,
                        UserName,
                        Password,
                        Name,
                        Phone,
                        Email
                    });
            }
        }

        internal static PermitUser FindByPhoneAndEmail(string phoneNumber, string email)
        {
            if (string.IsNullOrWhiteSpace(phoneNumber) && string.IsNullOrWhiteSpace(email))
                return null;

            var builder = new SqlBuilder();
            var selector = builder.AddTemplate(@"(
                    SELECT TOP 1 * FROM PPIO.PermitUsers /**where**/
                )");

            if (!string.IsNullOrWhiteSpace(phoneNumber))
                builder.Where("Phone=@Phone", new { Phone = phoneNumber });

            if (!string.IsNullOrWhiteSpace(email))
                builder.Where("Email=@Email", new { Email = email });

            return SqlMapper.Query<PermitUser>(selector.RawSql, selector.Parameters).FirstOrDefault();
        }

        internal static PermitUser Login(string username, string password)
        {
            if (string.IsNullOrWhiteSpace(username) || string.IsNullOrWhiteSpace(password))
                return null;

            var user = SqlMapper.QuerySP<PermitUser>("PPIO.PermitUserLogin", new
            {
                @Username = username,
                @Password = password
            }).FirstOrDefault();

            if(user != null)
                user.Password = null;

            return user;
        }
    }
}