using Extric.Towbook.Accounts;
using Extric.Towbook.Utility;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;

namespace Extric.Towbook.PermitRequests.API
{
    public class ParkingPermitResponseModel
    {
        public int RequestId { get; set; }
        public string CellPhone { get; set; }
        public string Email { get; set; }
        public string FullName { get; set; }
        public string Address { get; set; }
        public string City { get; set; }
        public string State { get; set; }
        public string Zip { get; set; }

        public string CustomPermitNumber { get; set; }
        public string UnitNumber { get; set; }

        public ParkingPermitAccountModel Account { get; set; }
        public Collection<ParkingPermitVehicleModel> Vehicles { get; set; }
        public Collection<ParkingPermitOrderModel> Orders { get; set; }

        public string Disclaimer { get; set; }

        public DateTime? CompletedDate { get; set; }
        
        public ParkingPermitResponseModel()
        {
            Vehicles = new Collection<ParkingPermitVehicleModel>();
            Orders = new Collection<ParkingPermitOrderModel>();
        }


        public static ParkingPermitResponseModel Map(ParkingPermitRequest request, Account account = null, string disclaimer = null)
        {
            var model = new ParkingPermitResponseModel();

            if (request == null)
                request = new ParkingPermitRequest();

            model.RequestId = request.Id;
            model.CellPhone = Core.FormatPhone(request.CellPhone);
            model.Email = request.Email;
            model.FullName = Core.FormatName(request.FullName);
            model.CustomPermitNumber = request.CustomPermitNumber;
            model.UnitNumber = request.UnitNumber;

            Collection<FeeModel> fees = new Collection<FeeModel>();

            if (account != null)
            {
                model.Account = new ParkingPermitAccountModel();
                model.Account.Name = account.Company;
                model.Account.Address = account.Address;
                model.Account.City = account.City;
                model.Account.State = account.State;
                model.Account.Zip = account.Zip;
                model.Account.Phone = Core.FormatPhone(account.Phone);

                // needed for pusher subscribes
                model.Account.CompanyId = account.CompanyId;

                var email = account.Email;
                var emails = account.Email.Split(new[] { ';', ',' }, StringSplitOptions.RemoveEmptyEntries);
                if (emails.Count() > 1)
                    email = emails[0];

                model.Account.Email = email;

                var setting = ParkingPermitSetting.GetByAccountId(account.CompanyId, account.Id);
                var cSetting = ParkingPermitSetting.GetByCompanyId(account.CompanyId);

                if (setting == null)
                    setting = cSetting ?? new ParkingPermitSetting();

                // company Overrides
                if (setting.TaxRateId == null && cSetting.TaxRateId != null)
                    setting.TaxRateId = cSetting.TaxRateId;

                model.Account.Settings = ParkingPermitAccountSettingModel.Map(setting);
                
                var aFees = ParkingPermitFee.GetByAccountId(account.CompanyId, account.Id);
                foreach (var aFee in aFees)
                {
                    var fee = new FeeModel();
                    fee.Type = aFee.PermitListId == 5 ? FeeType.Guest : FeeType.Residential;
                    fee.Number = aFee.FeeNumber;
                    fee.Amount = aFee.Amount;

                    fees.Add(fee);
                }

                model.Account.ResidentFees = fees.Where(w => w.Type == FeeType.Residential).Take(model.Account.Settings.NumberOfParkingSpaces).ToCollection();
                model.Account.GuestFees = fees.Where(w => w.Type == FeeType.Guest).Take(model.Account.Settings.NumberOfGuestPasses).ToCollection();

            }

            model.Disclaimer = disclaimer;

            model.CompletedDate = request.DeletedDate;

            return model;
        }

        public static Collection<ParkingPermitVehicleModel> Map(IEnumerable<ParkingPermitApprovalSessionItem> items, Account account, IEnumerable<ParkingPermit> permits = null)
        {
            var ret = new Collection<ParkingPermitVehicleModel>();
            var residents = items.Where(w => w.ParkingPermitListId == 1);
            var guests = items.Where(w => w.ParkingPermitListId == 5);
            var ppvm = new ParkingPermitVehicleModel();

            var count = 1;
            foreach (var r in residents)
            {
                var p = permits.Where(s => r.ParkingPermitId.GetValueOrDefault() == s.Id).FirstOrDefault();

                if (p != null && p.Deleted)
                    continue;

                ppvm = Map(r, account, p);
                ppvm.ListId = 1;
                ppvm.FeeNumber =  count++;
                ret.Add(ppvm);
            }

            count = 1;
            foreach (var g in guests)
            {
                var p = permits.Where(s => g.ParkingPermitId.GetValueOrDefault() == s.Id).FirstOrDefault();

                if (p != null && p.Deleted)
                    continue;

                ppvm = Map(g, account, p);
                ppvm.ListId = 5;
                ppvm.FeeNumber = count++;
                ret.Add(ppvm);
            }

            return ret;
        }

        private static ParkingPermitVehicleModel Map(ParkingPermitApprovalSessionItem item, Account account, ParkingPermit permit = null)
        {
            int? permitStatusId = permit?.StatusId;
            string permitStatus = permit != null ? PermitStatus.GetById(permit.StatusId.GetValueOrDefault()).Name : string.Empty;

            return new ParkingPermitVehicleModel()
            {
                ItemId = item.Id,
                PermitId = item.ParkingPermitId,
                Approved = item.Approved != null && item.Approved.Value == true,
                ListId = item.ParkingPermitListId,
                FeeNumber = null,
                Year = item.VehicleYear,
                Make = item.VehicleMake,
                Model = item.VehicleModel,
                Color = item.VehicleColor,
                VIN = item.VIN,
                LicensePlate = item.LicensePlate,
                LicenseState = item.LicensePlateState,
                StateRegistrationNumber = item.StateRegistrationNumber,
                StateRegistrationExpiration = item.StateRegistrationExpiration,
                PropertyId = account.Id,
                PropertyName = account.Company,
                PermitStatusId = permitStatusId,
                PermitStatus = permitStatus,
                ExpirationDate = permit.ExpirationDate

            };
        }

        internal static void GetVehicleAvailableActions(int companyId, int accountId, Collection<ParkingPermitVehicleModel> vehicles)
        {
            var setting = ParkingPermitSetting.GetByCompanyId(companyId, accountId) ?? new ParkingPermitSetting();
            
            var noOfValidGuestPasses = vehicles.Count(c =>
                        c.ListId == 5 /* guest pass */
                        && new int[] { PermitStatus.Valid.Id, PermitStatus.Extended.Id }.Contains(c.PermitStatusId.GetValueOrDefault()));

            foreach (var v in vehicles)
            {
                Collection<string> actions = new Collection<string>();

                actions.Add("view");
                actions.Add("print");

                if (v.Approved == true)
                {
                    if(new int[] { PermitStatus.Valid.Id, PermitStatus.Submitted.Id, PermitStatus.Extended.Id  }.Contains(v.PermitStatusId.GetValueOrDefault()))
                        actions.Add("edit");

                    if (v.ListId == 5 /* guest pass*/ )
                    {
                        if(v.PermitStatusId.GetValueOrDefault() != PermitStatus.Expired.Id && 
                            !setting.AllowGuestPassUpdate.GetValueOrDefault())
                            actions.Remove("edit");

                        if (new int[] { PermitStatus.Valid.Id, PermitStatus.Extended.Id }.Contains(v.PermitStatusId.GetValueOrDefault()))
                            actions.Add("cancel");
                    }
                }

                if (v.PermitStatusId.GetValueOrDefault() == PermitStatus.Expired.Id)
                {
                    actions.Add("renew");

                    // Don't allow renewing of an expired permit if the max number of ongoing/valid guest passes has been reached
                    if (setting.GuestPermitsPerResident > 0 && noOfValidGuestPasses == setting.GuestPermitsPerResident)
                    {
                        actions.Remove("renew");
                    }
                }

                v.AvailableActions = actions.ToArray();
            }
        }
    }
    
}
