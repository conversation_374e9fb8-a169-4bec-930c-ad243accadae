<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Microsoft.Bcl.AsyncInterfaces" version="7.0.0" targetFramework="net48" />
  <package id="Newtonsoft.Json" version="13.0.1" targetFramework="net48" />
  <package id="Pipelines.Sockets.Unofficial" version="2.2.0" targetFramework="net48" />
  <package id="protobuf-net" version="3.0.73" targetFramework="net48" />
  <package id="protobuf-net.Core" version="3.0.73" targetFramework="net48" />
  <package id="ServerAppFabric.Client" version="1.1.2106" targetFramework="net45" />
  <package id="StackExchange.Redis" version="2.2.79" targetFramework="net48" />
  <package id="System.Buffers" version="4.5.1" targetFramework="net48" />
  <package id="System.Collections.Immutable" version="7.0.0" targetFramework="net48" />
  <package id="System.Diagnostics.PerformanceCounter" version="5.0.0" targetFramework="net48" />
  <package id="System.IO.Compression" version="4.3.0" targetFramework="net48" />
  <package id="System.IO.Pipelines" version="9.0.0" targetFramework="net48" />
  <package id="System.Memory" version="4.5.5" targetFramework="net48" />
  <package id="System.Numerics.Vectors" version="4.5.0" targetFramework="net48" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="6.0.0" targetFramework="net48" />
  <package id="System.Threading.Channels" version="5.0.0" targetFramework="net48" />
  <package id="System.Threading.Tasks.Extensions" version="4.5.4" targetFramework="net48" />
</packages>