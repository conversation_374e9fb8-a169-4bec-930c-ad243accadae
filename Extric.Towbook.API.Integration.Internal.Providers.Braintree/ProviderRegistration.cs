using Microsoft.AspNetCore.Routing;

namespace Extric.Towbook.API.Integration.Internal.Providers.BraintreeProvider
{
    public class ProviderRegistration
    {
        public ProviderRegistration()
        {
        }

        public static void RegisterRoutes(RouteCollection routes)
        {
            /* string root = "integration/internal/braintree/";
            List<Route> rl = new List<Route>();

            rl.Add(routes.MapHttpRoute(
                name: "Integration_Braintree_Get",
                routeTemplate: root + "{controller}/{id}/{action}",
                defaults: new { id = RouteParameter.Optional, action = "Get" },
                constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "PUT" }) }));

            rl.Add(routes.MapHttpRoute(
                name: "Integration_Braintree_Put",
                routeTemplate: root + "{controller}/{id}/{action}",
                defaults: new { id = RouteParameter.Optional, action = "Put" },
                constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "PUT" }) }));


            rl.Add(routes.MapHttpRoute(
                name: "Integration_Braintree_Post",
                routeTemplate: root + "{controller}/{id}",
                defaults: new { id = RouteParameter.Optional, action = "Post" },
                constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "POST" }) }));


            rl.Add(routes.MapHttpRoute(
                name: "Integration_Braintree_Delete",
                routeTemplate: root + "{controller}/{id}",
                defaults: new { id = RouteParameter.Optional, action = "Delete" },
                constraints: new { httpMethod = new HttpMethodConstraint(new string[] { "DELETE" }) }));



            rl.Add(routes.MapHttpRoute(
                name: "Integration_Braintree",
                routeTemplate: root + "{controller}/{id}",
                defaults: new { action = "Get", id = RouteParameter.Optional }));

            foreach (var r in rl)
            {
                if (r.DataTokens == null)
                    r.DataTokens = new RouteValueDictionary();

                r.DataTokens["Namespaces"] = new string[] { "Extric.Towbook.API.Integration.Internal.Providers.BraintreeProvider" };
            } */

        }
    }
}
