using Extric.Towbook.Accounts;
using Extric.Towbook.Dispatch;
using Extric.Towbook.WebShared;
using System;
using System.Net;
using System.Net.Http;
using System.Reflection;
using System.Text;
using Twilio.TwiML;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;


namespace Extric.Towbook.VoiceDispatchApi.Controllers
{
    /// <summary>
    /// Handles the outbound call speech, telling the receiving phone number why we're calling, what actions can be taken, etc.
    /// </summary>
    /// 
    [Route("VoiceDispatch/AnswerCall")]
    public class AnswerCallController : ControllerBase
    {
        public sealed class InboundCallModel
        {
            public string Called { get; set; }
            public string ToState { get; set; }
            public string CallerCountry { get; set; }
            public string Direction { get; set; }
            public string CallerState { get; set; }
            public string ToZip { get; set; }
            public string CallSid { get; set; }
            public string To { get; set; }
            public string CallerZip { get; set; }
            public string ToCountry { get; set; }
            public string ApiVersion { get; set; }
            public string CalledZip { get; set; }
            public string CalledCity { get; set; }
            public string CallStatus { get; set; }
            public string From { get; set; }
            public string AccountSid { get; set; }
            public string CalledCountry { get; set; }
            public string CallerCity { get; set; }
            public string Caller { get; set; }
            public string FromCountry { get; set; }
            public string ToCity { get; set; }
            public string FromCity { get; set; }
            public string CalledState { get; set; }
            public string FromZip { get; set; }
            public string FromState { get; set; }
        }

        [HttpPost("{companyId}/{voiceId}")]
        public async Task<HttpResponseMessage> PostBodyAsync(int companyId, string voiceId, [FromBody] InboundCallModel d) => await Post(companyId, voiceId, d);

        [ApiExplorerSettings(IgnoreApi = true)]
        [HttpPost("{companyId}/{voiceId}")]
        [FormContentType]
        public async Task<HttpResponseMessage> PostFormAsync(int companyId, string voiceId, [FromForm] InboundCallModel d) => await Post(companyId, voiceId, d);


        internal async Task<HttpResponseMessage> Post(int companyId, 
            string voiceId, 
            InboundCallModel post)
        {
            Guid voiceGuid = Guid.Parse(voiceId);

            var vd = VoiceDispatch.GetByPublicId(voiceGuid);
            if (vd == null)
                return new HttpResponseMessage(System.Net.HttpStatusCode.NotFound);

            var response = new VoiceResponse();
            if (vd.Type == 1)
            {
                var cr = await CallRequest.GetByIdAsync((int)vd.ReferenceId);
                if (cr == null)
                    return new HttpResponseMessage(System.Net.HttpStatusCode.NotFound);

                if (cr.Status != CallRequestStatus.None)
                    return new HttpResponseMessage(System.Net.HttpStatusCode.NotFound);

                var a = await Account.GetByIdAsync(cr.AccountId);
                string accountName = a.Company;
                if (a.MasterAccountId > 0 && a.MasterAccountId != 15)
                    accountName = (await MasterAccount.GetByIdAsync(a.MasterAccountId))?.Name ?? accountName;

                int? delayInSeconds = null;

                // todo: grab this from a companyKeyValue - voice delay. 
                if (a.CompanyId == 49039 || 
                    a.CompanyId == 17609)
                    delayInSeconds = 5; // so that it gets by the "this call may be recorded"

                response.Append(new Twilio.TwiML.Voice.Gather(new System.Collections.Generic.List<Twilio.TwiML.Voice.Gather.InputEnum>()
                { Twilio.TwiML.Voice.Gather.InputEnum.Dtmf },
                    new Uri($"{WebGlobal.GetDomain()}/voicedispatch/handleResponse/{companyId}/{voiceGuid.ToString("N")}"), numDigits: 1)
                    .Pause(delayInSeconds)
                    .Say($"New dispatch from {accountName}. {cr.Reason} for {cr.Vehicle}located at {cr.StartingLocation}. Press 1 to accept, 2 to refuse.")); ;
            }
            else if (vd.Type == 2)
            {
                // call dispatched. 
                var en = await Entry.GetByIdAsync((int)vd.ReferenceId);
                if (en == null)
                    return new HttpResponseMessage(System.Net.HttpStatusCode.NotFound);

                if (en.Status.Id > Status.Dispatched.Id)
                {
                    response.Say("The call we are calling you about has already been responded to, you can hang up now. Thank you.");
                }
                else
                {
                    string accountName = en.Account?.Company;
                    if (en.Account != null && en.Account.MasterAccountId > 0 && en.Account.MasterAccountId != 15)
                        accountName = (await MasterAccount.GetByIdAsync(en.Account.MasterAccountId))?.Name ?? accountName;

                    response.Append(new Twilio.TwiML.Voice.Gather(new System.Collections.Generic.List<Twilio.TwiML.Voice.Gather.InputEnum>()
                    {
                        Twilio.TwiML.Voice.Gather.InputEnum.Dtmf },
                            new Uri($"{WebGlobal.GetDomain()}/voicedispatch/handleResponse/{companyId}/{voiceGuid.ToString("N")}"), numDigits: 1)
                            .Say
                            ($"You have been assigned a {en.Reason?.Name ?? ""} job, located at {en.TowSource}. Press 1 to accept, 2 to refuse."));
                    }
            }
            else if (vd.Type == 3)
            {
                // call cancelled
            }
            else if (vd.Type == 4)
            {
                // call reassigned to someone else.
            }
            return new HttpResponseMessage()
            {
                Content = new StringContent(response.ToString(), Encoding.UTF8, "application/xml")
            };
        }
    }
}
