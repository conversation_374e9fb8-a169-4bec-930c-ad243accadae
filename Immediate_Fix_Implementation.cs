// IMMEDIATE FIX IMPLEMENTATION
// File: Extric.Towbook.API/Controllers/Dispatch/CallsController.cs
// Replace line 269 with the following implementation

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Extric.Towbook.API.Controllers.Dispatch
{
    public partial class CallsController
    {
        // Add these static fields at the class level
        private static readonly SemaphoreSlim _finishMapSemaphore = new SemaphoreSlim(20, 20);
        private static readonly SemaphoreSlim _databaseSemaphore = new SemaphoreSlim(10, 10);

        // OPTION 1: IMMEDIATE FIX - Replace line 269 with this
        private async Task<Collection<CallModel>> ProcessCallsWithConcurrencyControl(Collection<CallModel> result)
        {
            var tasks = result.Select(async call =>
            {
                await _finishMapSemaphore.WaitAsync();
                try
                {
                    return await call.FinishMapAsync().ConfigureAwait(false);
                }
                finally
                {
                    _finishMapSemaphore.Release();
                }
            });

            var processedCalls = await Task.WhenAll(tasks);
            return processedCalls.ToCollection();
        }

        // OPTION 2: BATCH PROCESSING APPROACH
        private async Task<Collection<CallModel>> ProcessCallsInBatches(Collection<CallModel> result)
        {
            const int batchSize = 25;
            var processedResults = new List<CallModel>();

            // Process in batches to prevent overwhelming the database
            for (int i = 0; i < result.Count; i += batchSize)
            {
                var batch = result.Skip(i).Take(batchSize);
                var batchTasks = batch.Select(async call =>
                {
                    await _finishMapSemaphore.WaitAsync();
                    try
                    {
                        return await call.FinishMapAsync().ConfigureAwait(false);
                    }
                    finally
                    {
                        _finishMapSemaphore.Release();
                    }
                });

                var batchResults = await Task.WhenAll(batchTasks);
                processedResults.AddRange(batchResults);

                // Small delay between batches to allow connection pool recovery
                if (i + batchSize < result.Count)
                {
                    await Task.Delay(50); // 50ms delay
                }
            }

            return processedResults.ToCollection();
        }

        // OPTION 3: STREAMING APPROACH WITH BACKPRESSURE
        private async Task<Collection<CallModel>> ProcessCallsWithStreaming(Collection<CallModel> result)
        {
            var processedCalls = new List<CallModel>();
            var semaphore = new SemaphoreSlim(Environment.ProcessorCount * 2);

            await foreach (var call in ProcessCallsAsyncEnumerable(result, semaphore))
            {
                processedCalls.Add(call);
            }

            return processedCalls.ToCollection();
        }

        private async IAsyncEnumerable<CallModel> ProcessCallsAsyncEnumerable(
            IEnumerable<CallModel> calls, 
            SemaphoreSlim semaphore)
        {
            var tasks = new List<Task<CallModel>>();
            const int maxConcurrent = 20;

            foreach (var call in calls)
            {
                tasks.Add(ProcessSingleCallAsync(call, semaphore));

                // Yield results as they complete
                if (tasks.Count >= maxConcurrent)
                {
                    var completed = await Task.WhenAny(tasks);
                    tasks.Remove(completed);
                    yield return await completed;
                }
            }

            // Process remaining tasks
            while (tasks.Count > 0)
            {
                var completed = await Task.WhenAny(tasks);
                tasks.Remove(completed);
                yield return await completed;
            }
        }

        private async Task<CallModel> ProcessSingleCallAsync(CallModel call, SemaphoreSlim semaphore)
        {
            await semaphore.WaitAsync();
            try
            {
                return await call.FinishMapAsync().ConfigureAwait(false);
            }
            finally
            {
                semaphore.Release();
            }
        }
    }
}

// =============================================================================
// ENHANCED DRIVERKEY IMPLEMENTATION WITH CACHE STAMPEDE PROTECTION
// File: Extric.Towbook/Integration/DriverKey.cs
// =============================================================================

namespace Extric.Towbook.Integration
{
    public class DriverKey : IKey
    {
        private const string CacheKeyAll = "ik_driver_all";
        private static readonly SemaphoreSlim _cacheLock = new SemaphoreSlim(1, 1);
        private static readonly object _lockObject = new object();
        private static volatile bool _isLoading = false;

        // ENHANCED GetAllAsync with cache stampede protection
        public static async Task<Collection<DriverKey>> GetAllAsync()
        {
            // First, try to get from cache without locking
            var cached = AppServices.Cache.Get<DriverKeyCollection>(CacheKeyAll);
            if (cached != null)
            {
                return cached.Collection;
            }

            // Check if another thread is already loading
            lock (_lockObject)
            {
                if (_isLoading)
                {
                    // Another thread is loading, wait and try cache again
                    // This prevents multiple threads from hitting the database
                }
                else
                {
                    _isLoading = true;
                }
            }

            if (!_isLoading)
            {
                // Wait for the other thread to complete and try cache again
                await Task.Delay(100); // Small delay
                cached = AppServices.Cache.Get<DriverKeyCollection>(CacheKeyAll);
                if (cached != null)
                {
                    return cached.Collection;
                }
            }

            try
            {
                await _cacheLock.WaitAsync();
                try
                {
                    // Double-check pattern after acquiring lock
                    cached = AppServices.Cache.Get<DriverKeyCollection>(CacheKeyAll);
                    if (cached != null)
                    {
                        return cached.Collection;
                    }

                    // Load from database with timeout protection
                    using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(30));
                    var queryResult = await SqlMapper.QuerySpAsync<dynamic>(
                        "Integration.ProviderDriverKeysGetAll", 
                        cancellationToken: cts.Token);

                    var result = new DriverKeyCollection(Map(queryResult));
                    
                    // Cache the result
                    AppServices.Cache.Set(CacheKeyAll, result, TimeSpan.FromDays(30));
                    
                    return result.Collection;
                }
                finally
                {
                    _cacheLock.Release();
                }
            }
            finally
            {
                lock (_lockObject)
                {
                    _isLoading = false;
                }
            }
        }

        // BULK LOADING METHOD for better performance
        public static async Task<Dictionary<int, Collection<DriverKey>>> GetByProviderIdsBulkAsync(int[] providerIds)
        {
            var allKeys = await GetAllAsync();
            return providerIds.ToDictionary(
                providerId => providerId,
                providerId => allKeys.Where(k => k.ProviderId == providerId).ToCollection()
            );
        }
    }
}

// =============================================================================
// ENHANCED SQLMAPPER WITH CIRCUIT BREAKER AND MONITORING
// File: Extric.Towbook/Utility/SqlMapperExtensions.cs
// =============================================================================

namespace Extric.Towbook.Utility
{
    public static partial class SqlMapper
    {
        private static readonly SemaphoreSlim _connectionSemaphore = new SemaphoreSlim(50, 50);
        private static readonly Counter _queryCounter = Metrics.CreateCounter(
            "database_queries_total", "Total database queries", new[] { "operation", "status" });
        private static readonly Histogram _queryDuration = Metrics.CreateHistogram(
            "database_query_duration_seconds", "Database query duration");

        public static async Task<IEnumerable<T>> QuerySpAsync<T>(
            string sql, 
            object param = null, 
            IDbTransaction transaction = null, 
            int? commandTimeout = null,
            CancellationToken cancellationToken = default)
        {
            await _connectionSemaphore.WaitAsync(cancellationToken);
            
            using var timer = _queryDuration.NewTimer();
            var operation = sql.Split('.').LastOrDefault() ?? sql;
            
            try
            {
                using var cnn = new SqlConnection(Core.ConnectionString);
                await cnn.OpenAsync(cancellationToken);
                
                var data = await Dapper.SqlMapper.QueryAsync<T>(
                    cnn, sql, param, transaction, commandTimeout, CommandType.StoredProcedure);
                
                _queryCounter.WithLabels(operation, "success").Inc();
                return data.ToList();
            }
            catch (Exception ex)
            {
                _queryCounter.WithLabels(operation, "error").Inc();
                
                // Enhanced error information for debugging
                var connectionString = Core.ConnectionString;
                var maskedConnectionString = connectionString?.Length > 50 
                    ? connectionString.Substring(0, 50) + "..." 
                    : connectionString;
                
                throw new Exception($"Database connection failed for stored procedure '{sql}'. " +
                    $"Connection string: '{maskedConnectionString}'. " +
                    $"Active connections: {50 - _connectionSemaphore.CurrentCount}. " +
                    $"Original error: {ex.Message}", ex);
            }
            finally
            {
                _connectionSemaphore.Release();
            }
        }
    }
}

// =============================================================================
// CONFIGURATION CHANGES
// File: appsettings.json
// =============================================================================

/*
{
  "ConnectionStrings": {
    "Database": "Server=localhost;Database=TowbookDev;Integrated Security=True;TrustServerCertificate=True;MultipleActiveResultSets=true;Max Pool Size=200;Min Pool Size=20;Connection Timeout=30;Command Timeout=60;Pooling=true;Connection Lifetime=300;"
  },
  "DatabaseSettings": {
    "MaxConcurrentConnections": 50,
    "QueryTimeoutSeconds": 60,
    "ConnectionPoolSize": 200,
    "EnableConnectionPooling": true
  },
  "PerformanceSettings": {
    "MaxConcurrentFinishMapOperations": 20,
    "BatchSize": 25,
    "EnableAsyncStreaming": true
  }
}
*/
